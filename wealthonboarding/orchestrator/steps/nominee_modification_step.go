package steps

import (
	"context"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/samber/lo"

	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/release"
)

type NomineeModificationStep struct {
	releaseEvaluator release.IEvaluator
	userClient       userPb.UsersClient
	currentStep      woPb.OnboardingStep
	nextStep         woPb.OnboardingStep
}

func NewNomineeModificationStep(releaseEvaluator release.IEvaluator, userClient userPb.UsersClient) *NomineeModificationStep {
	return &NomineeModificationStep{
		releaseEvaluator: releaseEvaluator,
		userClient:       userClient,
		currentStep:      woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS,
		nextStep:         woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
	}
}

func (n *NomineeModificationStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	personalDetails := details.GetMetadata().GetPersonalDetails()
	nomineeDeclarationDetails := personalDetails.GetNomineeDeclarationDetails()
	if nomineeDeclarationDetails.GetChoice() == commontypes.BooleanEnum_FALSE {
		// nominee opted out, skip step
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, n.currentStep, n.nextStep), nil
	}

	isNomineeModificationEnabled, updateAppDeeplink, err := n.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS).WithActorId(details.GetActorId()))
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, n.currentStep, n.nextStep), err
	}
	if updateAppDeeplink != nil {
		updateAppDeeplink = GetUpdateAppDeeplink(updateAppDeeplink)
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, n.currentStep, n.nextStep)
		res.Deeplink = updateAppDeeplink
		return res, nil
	}
	if !isNomineeModificationEnabled {
		logger.Info(ctx, "nominee modification step is not enabled for user")
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED, n.currentStep, n.nextStep), nil
	}

	wealthNominees := nomineeDeclarationDetails.GetWealthAccountNominees()
	// fetch all nominees for the actor
	nomineesRes, err := n.userClient.GetNominees(ctx, &userPb.GetNomineesRequest{ActorId: details.GetActorId()})
	if rpcErr := epifigrpc.RPCError(nomineesRes, err); rpcErr != nil && !nomineesRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to get wealthNominees for validation", zap.Error(rpcErr))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, n.currentStep, n.nextStep), rpcErr
	}

	var isNomineeDetailsValid bool
	for _, nominee := range wealthNominees {
		matchedNominee, found := lo.Find(nomineesRes.GetNominees(), func(userNominee *typesv2.Nominee) bool {
			return userNominee.GetId() == nominee.GetNomineeId()
		})
		if found && validateNomineeDetails(ctx, matchedNominee) {
			isNomineeDetailsValid = true
			break
		}
	}
	if isNomineeDetailsValid {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, n.currentStep, n.nextStep), nil
	}

	// additional nominee details missing, build nominee deeplink
	missingDataScreenDeeplink := GetMissingDataDeeplinkForNominee()
	res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, n.currentStep, n.nextStep)
	res.Deeplink = missingDataScreenDeeplink
	return res, nil
}

func validateNomineeDetails(ctx context.Context, nomineeDetails *typesv2.Nominee) bool {
	if nomineeDetails == nil {
		logger.Info(ctx, "nomineeDetails is nil for nominee details")
		return false
	}
	if nomineeDetails.GetRelationship() == typesv2.RelationType_RELATION_TYPE_UNSPECIFIED {
		logger.Info(ctx, "relationship type cannot be unspecified for nominee details")
		return false
	}
	if nomineeDetails.GetNomineeDocument().GetDocumentNumber() == "" ||
		nomineeDetails.GetNomineeDocument().GetDocumentType() == typesv2.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED {
		logger.Info(ctx, "Document is missing for nominee details")
		return false
	}
	if nomineeDetails.GetName() == "" {
		logger.Info(ctx, "name is missing for nominee details")
		return false
	}
	if nomineeDetails.GetDob() == nil {
		logger.Info(ctx, "DOB is missing for nominee details")
		return false
	}
	contact := nomineeDetails.GetContactInfo()
	if contact == nil {
		logger.Info(ctx, "contactInfo is missing for nominee details")
		return false
	}
	if contact.GetEmailId() == "" {
		logger.Info(ctx, "emailId is missing for nominee details")
		return false
	}
	if contact.GetPhoneNumber() == nil {
		logger.Info(ctx, "PhoneNumber is missing for nominee details")
		return false
	}
	if contact.GetPhoneNumber().GetNationalNumber() == 0 {
		logger.Info(ctx, "PhoneNumber NationalNumber is missing or zero for nominee details")
		return false
	}
	return true
}
