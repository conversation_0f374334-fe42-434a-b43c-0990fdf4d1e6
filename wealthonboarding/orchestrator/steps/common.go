package steps

import (
	"github.com/epifi/gamma/api/frontend/deeplink"
	clientstate1 "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/frontend"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/regex"
)

//nolint:goconst
func GetUpdateAppDeeplink(updateAppDeeplink *deeplink.Deeplink) *deeplink.Deeplink {
	updateAppDeeplink.GetWealthOnboardingStatusScreenOptions().Title = "Update Fi app to start investing"
	updateAppDeeplink.GetWealthOnboardingStatusScreenOptions().Description = "For a smoother and better investment experience on Fi, please update the app"
	updateAppDeeplink.GetWealthOnboardingStatusScreenOptions().Cta = &deeplink.Cta{
		Text: "Update App",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_UPDATE_APP_SCREEN,
		},
	}
	return updateAppDeeplink
}

func GetMissingDataDeeplinkForNominee() *deeplink.Deeplink {
	var missingDataListItem []*deeplink.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus
	collectNomineeScreen := &deeplink.Deeplink{
		Screen: deeplink.Screen_COLLECT_NOMINEE_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CollectNomineeDetailsScreenOptions{
			CollectNomineeDetailsScreenOptions: &deeplink.CollectNomineeDetailsScreenOptions{
				Title:       "Select a nominee",
				MaxNominees: 1,
				OptOutText:  "I want to continue without a nominee",
				Flow:        deeplink.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING,
				FlowData: &deeplink.DataCollectionFlowData{
					Data: &deeplink.DataCollectionFlowData_WealthFlow{
						WealthFlow: clientstate1.WealthFlow_WEALTH_FLOW_INVESTMENT,
					},
				},
				AddNomineeDeeplink: GetAddNomineeDeeplink(),
			},
		},
	}
	collectNomineeListItem := &deeplink.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
		Status:              deeplink.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
		DisplayText:         "Nominee details",
		MissingDataDeeplink: collectNomineeScreen,
		FreshInputText:      "ADD",
	}
	missingDataListItem = append(missingDataListItem, collectNomineeListItem)
	missingDataScreen := &deeplink.Deeplink{
		Screen: deeplink.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
		ScreenOptions: &deeplink.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
			WealthOnboardingCaptureMissingDataScreenOptions: &deeplink.WealthOnboardingCaptureMissingDataScreenOptions{
				MissingData: missingDataListItem,
			},
		},
	}
	return missingDataScreen
}

// GetAddNomineeDeeplink returns deeplink for add nominee
func GetAddNomineeDeeplink() *deeplink.Deeplink {
	nomineeDocuments := []*frontend.NomineeScreenOptions_NomineeDocument{
		{
			DocumentType:             typesv2.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN.String(),
			DocumentName:             "PAN",
			DocumentInputPlaceHolder: "Enter PAN",
			MaxInputLength:           10,
			Validator: []*typesv2.TextFieldValidator{
				{
					Validator: &typesv2.TextFieldValidator_RegexValidator{
						RegexValidator: &typesv2.RegexValidator{
							Regex:             regex.PersonalPan,
							ValidationMessage: "Please enter valid PAN",
						},
					},
				},
			},
		},
		{
			DocumentType:             typesv2.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_AADHAAR.String(),
			DocumentName:             "Aadhaar",
			DocumentInputPlaceHolder: "Enter last 4 digits of Aadhaar",
			MaxInputLength:           4,
			Validator: []*typesv2.TextFieldValidator{
				{
					Validator: &typesv2.TextFieldValidator_RegexValidator{
						RegexValidator: &typesv2.RegexValidator{
							Regex:             regex.Aadhaar4Digit,
							ValidationMessage: "Please enter valid Aadhaar",
						},
					},
				},
			},
		},
		{
			DocumentType:             typesv2.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE.String(),
			DocumentName:             "Driving License",
			DocumentInputPlaceHolder: "Enter Driving License",
			MaxInputLength:           20,
			Validator: []*typesv2.TextFieldValidator{
				{
					Validator: &typesv2.TextFieldValidator_RegexValidator{
						RegexValidator: &typesv2.RegexValidator{
							Regex:             regex.DrivingLicense,
							ValidationMessage: "Please enter valid Driving License",
						},
					},
				},
			},
		},
	}
	screenOptions := &frontend.NomineeScreenOptions{
		NomineeDocuments: nomineeDocuments,
		Entrypoint:       typesv2.NomineeEntryPoint_NOMINEE_ENTRY_POINT_WEALTH_ONBOARDING.String(),
	}
	return deeplinkv3.GetDeeplinkV3WithoutError(
		deeplink.Screen_ADD_NOMINEE_DETAILS_SCREEN,
		screenOptions,
	)
}
