package steps

import (
	"context"
	"testing"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	rpcPb "github.com/epifi/be-common/api/rpc"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	livenessMocks "github.com/epifi/gamma/api/auth/liveness/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	clientstate1 "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	types "github.com/epifi/gamma/api/typesv2"
	wVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/release"
	daoMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	helperMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	manualReviewMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/manual_review"
	ocrMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/ocr"
	releaseEvaluatorMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
)

func TestCollectMissingDataStep_Perform(t *testing.T) {
	ctr := gomock.NewController(t)

	mockCkycHelper := helperMocks.NewMockICkycHelper(ctr)
	mockOcrHelper := ocrMocks.NewMockIOcrHelper(ctr)
	mockManualReview := manualReviewMocks.NewMockIManualReview(ctr)
	mockEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	mockStepDetailsDao := daoMocks.NewMockOnboardingStepDetailsDao(ctr)
	mockLivenessClient := livenessMocks.NewMockLivenessClient(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	mockActorId := "random_actor"
	mockOdId := "random_onboarding_details"
	mockManualReviewId := "random_manual_review_id"
	type args struct {
		ctx         context.Context
		od          *woPb.OnboardingDetails
		disableCKYC bool
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *StepExecutionResponse
		wantErr    bool
	}{
		{
			name: "verified kra user with no data missing",
			args: args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					Id:      "",
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra:           false,
						CustomerProvidedData: &woPb.CustomerProvidedData{NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE}},
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				disableCKYC: false,
			},
			setupMocks: func() {
				mockEvaluator.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(mockActorId)).
					Return(false, nil, nil).Times(1)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_CollectMissingInfoStepMetadata{
							CollectMissingInfoStepMetadata: &woPb.CollectMissingInfoStepMetadata{
								CustomerProvidedData: &woPb.CustomerProvidedData{NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE}},
							},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "non individual response from ckyc",
			args: args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					Id:      mockOdId,
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra: true,
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				disableCKYC: false,
			},
			setupMocks: func() {
				mockCkycHelper.EXPECT().FetchPopulateValidateCkycDownloadData(context.Background(), &woPb.OnboardingDetails{
					Id:      mockOdId,
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra: true,
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				}).Return(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ENTITY_TYPE_NON_INDIVIDUAL, nil).Times(1)
				brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: mockOdId,
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
					SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ENTITY_TYPE_NON_INDIVIDUAL,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_CollectMissingInfoStepMetadata{
							CollectMissingInfoStepMetadata: &woPb.CollectMissingInfoStepMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
			},
			wantErr: false,
		},
		{
			name: "verified kra user with gender missing",
			args: args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					Id:      "",
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						KraData: &woPb.KraData{
							StatusData: &woPb.KraStatusData{
								PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
									AppStatusDelta: wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_GENDER_NOT_AVAILABLE,
								},
							},
						},
						IsFreshKra: false,
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				disableCKYC: false,
			},
			setupMocks: func() {},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
					SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_PARTIAL_DATA_UPLOAD_NEEDED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_CollectMissingInfoStepMetadata{
							CollectMissingInfoStepMetadata: &woPb.CollectMissingInfoStepMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
			},
			wantErr: false,
		},
		{
			name: "fetch populate with low ocr confidence and no liveness video",
			args: args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					Id:      mockOdId,
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						PersonalDetails: &woPb.PersonalDetails{},
						CkycData: &woPb.CkycData{
							DownloadData: &woPb.CkycDownloadData{
								PersonalDetails: &woPb.CkycPersonalDetails{
									DocumentsList: []*types.DocumentProof{
										{
											ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
											Id:        "",
											Photo:     []*commontypes.Image{{}},
											Expiry:    nil,
											S3Paths:   []string{""},
										},
									},
								},
							},
						},
						IsFreshKra: true,
						PoaWithOcr: &woPb.OcrDocumentProof{
							Doc: &types.DocumentProof{
								ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
								Id:        "",
							},
						},
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				disableCKYC: false,
			},
			setupMocks: func() {
				mockEvaluator.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(mockActorId)).
					Return(false, nil, nil).Times(1)
				mockEvaluator.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS).WithActorId(mockActorId)).
					Return(false, nil, nil).Times(1)
				mockCkycHelper.EXPECT().FetchPopulateValidateCkycDownloadData(context.Background(), &woPb.OnboardingDetails{
					Id:      mockOdId,
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						PersonalDetails: &woPb.PersonalDetails{},
						PoaWithOcr: &woPb.OcrDocumentProof{
							Doc: &types.DocumentProof{
								ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
								Id:        "",
								Photo:     nil,
								Expiry:    nil,
								S3Paths:   nil,
							},
						},
						CkycData: &woPb.CkycData{
							SearchData: nil,
							DownloadData: &woPb.CkycDownloadData{
								PersonalDetails: &woPb.CkycPersonalDetails{
									DocumentsList: []*types.DocumentProof{
										{
											ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
											Id:        "",
											Photo:     []*commontypes.Image{{}},
											Expiry:    nil,
											S3Paths:   []string{""},
										},
									},
								},
								IdentityDetails: nil,
							},
						},
						IsFreshKra: true,
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				}).Return(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED, nil).Times(1)
				mockManualReview.EXPECT().AddToReview(context.Background(), &woPb.ReviewPayload{
					Payload: &woPb.ReviewPayload_RedactionReview{
						RedactionReview: &woPb.ReviewPayload_OcrReview{
							ActorId: mockActorId,
							ProcessedDocument: &woPb.OcrDocumentProof{
								Doc: &types.DocumentProof{
									ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
								},
							},
							EditedDocument: nil,
						},
					},
				}, woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION).
					Return(mockManualReviewId, nil).Times(1)
				mockOcrHelper.EXPECT().PopulatePoaWithOcr(context.Background(), gomock.Any()).Return(woErr.ErrOCRLowConfidence)
				mockOcrHelper.EXPECT().FetchPanWithOcr(context.Background(), gomock.Any(), gomock.Any()).Return(nil, woErr.ErrOCRLowConfidence)
				mockLivenessClient.EXPECT().GetLivenessAttempts(context.Background(), &livenessPb.GetLivenessAttemptsRequest{
					ActorId: mockActorId,
					Limit:   maxLvAttemptLimit,
				}).Return(&livenessPb.GetLivenessAttemptsResponse{Status: rpcPb.StatusOk()}, nil).Times(1)
				brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: mockOdId,
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_USER_INPUT_NEEDED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_CollectMissingInfoStepMetadata{
							CollectMissingInfoStepMetadata: &woPb.CollectMissingInfoStepMetadata{
								CkycData: &woPb.CkycData{DownloadData: &woPb.CkycDownloadData{PersonalDetails: &woPb.CkycPersonalDetails{
									DocumentsList: []*types.DocumentProof{
										{
											ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
											Id:        "",
											Photo:     []*commontypes.Image{{}},
											Expiry:    nil,
											S3Paths:   []string{""},
										},
									},
								}}},
							},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
						WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions{
							BottomInfoText:         commontypes.GetTextFromStringFontColourFontStyle(missingDataBottomInfoText, "#8D8D8D", commontypes.FontStyle_BODY_4),
							IsSignDocketCtaVisible: true,
							MissingData: []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
								{
									MissingDataDeeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_COLLECT_GENDER_SCREEN,
										ScreenOptions: &deeplinkPb.Deeplink_CollectGenderScreenOptions{
											CollectGenderScreenOptions: &deeplinkPb.CollectGenderScreenOptions{
												GenderOptions: []*deeplinkPb.CollectGenderScreenOptions_GenderOption{
													{
														Gender:      types.Gender_MALE,
														DisplayText: "Male",
													}, {
														Gender:      types.Gender_MALE,
														DisplayText: "Female",
													}, {
														Gender:      types.Gender_TRANSGENDER,
														DisplayText: "Transgender",
													}, {
														Gender:      types.Gender_OTHER,
														DisplayText: "Other",
													},
												},
												Title: "Select your gender",
											},
										},
									},
									Status:      deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
									DisplayText: "Gender",
								},
								{
									MissingDataDeeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_COLLECT_MARITAL_STATUS_SCREEN,
										ScreenOptions: &deeplinkPb.Deeplink_CollectMaritalStatusScreenOptions{
											CollectMaritalStatusScreenOptions: &deeplinkPb.CollectMaritalStatusScreenOptions{
												MaritalStatusOptions: []*deeplinkPb.CollectMaritalStatusScreenOptions_MaritalStatusOption{
													{
														MaritalStatus: types.MaritalStatus_MARRIED,
														DisplayText:   "Married",
													}, {
														MaritalStatus: types.MaritalStatus_UNMARRIED,
														DisplayText:   "Unmarried",
													},
												},
												Title: "What is your marital status?",
											},
										},
									},
									Status:      deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
									DisplayText: "Marital Status",
								},
								{
									MissingDataDeeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_COLLECT_INCOME_SLAB_SCREEN,
										ScreenOptions: &deeplinkPb.Deeplink_CollectIncomeSlabScreenOptions{
											CollectIncomeSlabScreenOptions: &deeplinkPb.CollectIncomeSlabScreenOptions{
												IncomeSlabOptions: []*deeplinkPb.CollectIncomeSlabScreenOptions_IncomeSlabOption{
													{
														IncomeSlab:  types.IncomeSlab_INCOME_SLAB_BELOW_1_LAC,
														DisplayText: "Less than 1 Lakh",
													}, {
														IncomeSlab:  types.IncomeSlab_INCOME_SLAB_1_TO_5_LAC,
														DisplayText: "1 - 5 Lakhs",
													}, {
														IncomeSlab:  types.IncomeSlab_INCOME_SLAB_5_TO_10_LAC,
														DisplayText: "5 - 10 Lakhs",
													}, {
														IncomeSlab:  types.IncomeSlab_INCOME_SLAB_10_TO_25_LAC,
														DisplayText: "10 - 25 Lakhs",
													}, {
														IncomeSlab:  types.IncomeSlab_INCOME_SLAB_ABOVE_25_LAC,
														DisplayText: "25 Lakhs+",
													}, {
														IncomeSlab:  types.IncomeSlab_INCOME_SLAB_ABOVE_1_CRORE,
														DisplayText: "1 Crore+",
													},
												},
												Title: "What is your annual income?",
											},
										},
									},
									Status:      deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
									DisplayText: "Income Slab",
								},
								{
									MissingDataDeeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_COLLECT_SIGNATURE_SCREEN,
										ScreenOptions: &deeplinkPb.Deeplink_CollectSignatureScreenOptions{
											CollectSignatureScreenOptions: &deeplinkPb.CollectSignatureScreenOptions{
												Title:             "Add your signature",
												CameraCaptureHint: "Make sure the signature is in the frame and is clear to read",
											},
										},
									},
									Status:         deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
									DisplayText:    "Signature",
									FreshInputText: "SIGN",
								},
								{
									MissingDataDeeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_COLLECT_PAN_SCREEN,
										ScreenOptions: &deeplinkPb.Deeplink_CollectPanScreenOptions{
											CollectPanScreenOptions: &deeplinkPb.CollectPanScreenOptions{
												Title: "Add a PAN Card Image",
											},
										},
									},
									Status:         deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
									DisplayText:    "PAN Card Image",
									FreshInputText: "UPLOAD",
								},
								{
									MissingDataDeeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_COLLECT_NOMINEE_DETAILS_SCREEN,
										ScreenOptions: &deeplinkPb.Deeplink_CollectNomineeDetailsScreenOptions{
											CollectNomineeDetailsScreenOptions: &deeplinkPb.CollectNomineeDetailsScreenOptions{
												Title:       "Select a nominee",
												MaxNominees: 1,
												OptOutText:  "I want to continue without a nominee",
												Flow:        deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING,
												FlowData: &deeplinkPb.DataCollectionFlowData{
													Data: &deeplinkPb.DataCollectionFlowData_WealthFlow{
														WealthFlow: clientstate1.WealthFlow_WEALTH_FLOW_INVESTMENT,
													},
												},
												AddNomineeDeeplink: GetAddNomineeDeeplink(),
											},
										},
									},
									Status:         deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
									DisplayText:    "Nominee details",
									FreshInputText: "ADD",
								},
								{
									Status:      deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED,
									DisplayText: "Video Verification",
								},
								{
									MissingDataDeeplink: nil,
									Status:              deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED,
									DisplayText:         "Advisory agreement",
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "ckyc disabled without digilocker data",
			args: args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					Id:      mockOdId,
					ActorId: mockActorId,
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra:      true,
						PersonalDetails: &woPb.PersonalDetails{},
						PanDetails: &types.DocumentProof{
							Id: "pan123",
						},
					},
					Status:         0,
					CurrentStep:    0,
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				disableCKYC: true,
			},
			setupMocks: func() {
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: mockOdId,
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_CollectMissingInfoStepMetadata{
							CollectMissingInfoStepMetadata: &woPb.CollectMissingInfoStepMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conf.DisableCKYC = tt.args.disableCKYC
			s := &CollectMissingDataStep{
				currentStep:                  woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				manualReviewStep:             woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
				confirmPepAndCitizenshipStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				ckycHelper:                   mockCkycHelper,
				livenessStep:                 woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS,
				review:                       mockManualReview,
				conf:                         conf,
				lvClient:                     mockLivenessClient,
				stepDetailsDao:               mockStepDetailsDao,
				releaseEvaluator:             mockEvaluator,
				ocrHelper:                    mockOcrHelper,
				eventBroker:                  brokerMock,
			}
			tt.setupMocks()
			got, err := s.Perform(tt.args.ctx, tt.args.od)
			if (err != nil) != tt.wantErr {
				t.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isEqualStepExecutionResponse(got, tt.want, t) {
				t.Errorf("Perform() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isEqualStepExecutionResponse(got *StepExecutionResponse, want *StepExecutionResponse, t *testing.T) bool {
	if got.CurrentStepDetails.GetCreatedAt() != nil {
		want.CurrentStepDetails.CreatedAt = got.CurrentStepDetails.CreatedAt
	}
	if got.CurrentStepDetails.GetCompletedAt() != nil {
		want.CurrentStepDetails.CompletedAt = got.CurrentStepDetails.CompletedAt
	}
	if got.NextOnboardingStep != want.NextOnboardingStep {
		return false
	}
	if diff := cmp.Diff(want.Deeplink, got.Deeplink, protocmp.Transform()); diff != "" {
		t.Errorf("mismatch (-want +got):\n%s", diff)
	}
	if got.OnboardingStatus != want.OnboardingStatus {
		return false
	}
	if diff := cmp.Diff(want.CurrentStepDetails, got.CurrentStepDetails, protocmp.Transform()); diff != "" {
		t.Errorf("mismatch (-want +got):\n%s", diff)
	}
	if len(got.StepsToInvalidate) != len(want.StepsToInvalidate) {
		return false
	}
	for idx := range got.StepsToInvalidate {
		if got.StepsToInvalidate[idx] != want.StepsToInvalidate[idx] {
			return false
		}
	}
	return true
}
