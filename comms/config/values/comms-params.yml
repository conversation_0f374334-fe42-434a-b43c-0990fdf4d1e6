Flags:
  TrimDebugMessageFromStatus: true

InAppTargetedCommsConfig:
  ReturnEmptyDynamicElementList: false

EmailProvider: "AWS_SES"

SecureLoggingV2:
  EnableSecureLog: true
  DefaultFileLoggingParams:
    LogPath: "/var/log/comms/secure.log"
    MaxSizeInMBs: 100
    MaxBackups: 10

SmsTemplates:
  ONBOARDING_OTP:
    VERSION_V1:
      Template: "<#> Your OTP code is {#otp#}. We welcome you to Fi! We're excited that you're about to see what financial freedom looks like. {#android_client_signature#} -Epifi"
  WAITLIST_OTP:
    VERSION_V1:
      Template: "Your OTP code is {#otp#}. We appreciate your interest in Fi's Waitlist -Epifi"
  DEBIT_CARD_BLOCK:
    VERSION_V1:
      Template: "We've blocked your card {#debit_card_last_four#} for security reasons. All card-related payments will stop now! Call us on {#phone_number#}, and we'll sort things out together. -Federal"
  DEBIT_CARD_NEW_CARD_ISSUANCE_SOFT_PIN:
    VERSION_V1:
      Template: "{#first_name#}, a new Debit Card will reach you soon. Once you have it, please log on to your Fi app & activate it. -Federal"
  DEBIT_CARD_NEW_CARD_ISSUANCE_SOFT_PIN_RE:
    VERSION_V1:
      Template: "Yikes! Looks like your card isn't active yet. No worries, you must've been busy. Log on to the Fi app now, navigate to the card settings & enable it. -Federal"
  DEBIT_CARD_ON:
    VERSION_V1:
      Template: "Hola {#first_name#}, looks like you're ready to use the Debit Card {#debit_card_last_four#}. You're all set to start transacting with it. Have a great day! -Federal"
  DEBIT_CARD_OFF:
    VERSION_V1:
      Template: "Roger that! We've turned OFF the ability to do any transactions on your Debit Card {#debit_card_last_four#}. -Federal"
  DEBIT_CARD_ON_OFF_FAILURE:
    VERSION_V1:
      Template: "It appears that we're unable to process your card ON/OFF request. We request that you try again in some time. -Federal"
  DEBIT_CARD_INTERNATIONAL_ON:
    VERSION_V1:
      Template: "We pay heed to your needs, {#first_name#}. You can now actively use your Debit Card {#debit_card_last_four#} for International Transactions. Happy Trails! -Federal"
  DEBIT_CARD_INTERNATIONAL_OFF:
    VERSION_V1:
      Template: "Looks like you want to disable International Transactions on your Debit Card {#debit_card_last_four#}. Consider it done. -Federal"
  DEBIT_CARD_INTERNATIONAL_ON_OFF_FAILURE:
    VERSION_V1:
      Template: "Oops! Apparently, we couldn't process your request to turn the International Transactions ON/OFF. Do try again in some time. -Federal"
  DEBIT_CARD_ECOM_ON:
    VERSION_V1:
      Template: "Your wish is our command, {#first_name#}! We've successfully enabled your Debit Card {#debit_card_last_four#} for online transactions (ECOM). -Federal"
  DEBIT_CARD_ECOM_OFF:
    VERSION_V1:
      Template: "Hi {#first_name#}, we heard you loud & clear. As per your request, we've disabled online transactions (ECOM) on your Debit Card {#debit_card_last_four#}. -Federal"
  DEBIT_CARD_ACTIVATE:
    VERSION_V1:
      Template: "Hello {#first_name#}, we have activated your card. Cheers! -Federal"
  DEBIT_CARD_BLOCK_FAILURE:
    VERSION_V1:
      Template: "Oops! We couldn't process your card blocking request. But there's no need to panic yet, {#first_name#}. Call us on {#phone_number#}, and we'll help. -Federal"
  DEBIT_CARD_DISPATCH:
    VERSION_V1:
      Template: "{#first_name#}, your Debit Card {#debit_card_last_four#} is on its way to you through {#logistics_partner#} on {#date_time#}.  Here's a trackable Ref No. {#tracking_number#}. -Federal"
  DEBIT_CARD_INCORRECT_PIN_RETRIES:
    VERSION_V1:
      Template: "ALERT! We've noticed multiple incorrect PIN entries on your ATM card {#debit_card_last_four#}. If this isn't by you, call us immediately on {#phone_number#}. -Federal"
  DEBIT_CARD_FREEZE:
    VERSION_V1:
      Template: "{#first_name#}, your Debit Card {#debit_card_last_four#} is frozen now. All outgoing transactions are temporarily suspended. You can unfreeze it anytime on our app. -Federal"
  DEBIT_CARD_UNFREEZE:
    VERSION_V1:
      Template: "Hi {#first_name#}, looks like you've unfrozen your card. Great! Your Debit Card {#debit_card_last_four#}, and its virtual avatar, are now fully functional again. -Federal"
  DEBIT_CARD_CHANGE_ATM_PIN:
    VERSION_V1:
      Template: "ATM PIN = Changed. {#first_name#}, you're ready to roll. Use your new PIN for all Debit Card transactions. Oh and remember, never share your PIN. -Federal"
  UPI_REGISTRATION:
    VERSION_V1:
      Template: "{#first_name#}, you're now registered on UPI. When you sign-up on our app, we create a fresh UPI ID for you. Questions? Call us on {#phone_number#}. -Federal"
  CASH_WITHDRAWAL_ATM:
    VERSION_V1:
      Template: "Withdrawn: {#withdraw_amount#} | Balance: {#balance_amount#}. This transaction occurred on {#date#} at {#time#}. If it wasn't done by you, ping us on the Fi app. -Federal"
  NEFT_DEBIT:
    VERSION_V1:
      Template: "{#first_name#}, you've sent {#txn_amount#} to {#reciever_name#}. Mode:{#payment_mode#} | {#date_time#} | Ref:{#reference_no#}. Balance: {#balance_amount#} -Federal"
  NEFT_CREDIT:
    VERSION_V1:
      Template: "{#first_name#}, {#sender_name#} has sent {#txn_amount#} to you. Mode:{#payment_mode#} | {#date_time#} | Ref No.{#reference_no#}. Balance: Rs.{#balance_amount#} -Federal"
  NEFT_CREDIT_OTHER_BANK:
    VERSION_V1:
      Template: "You've received {#txn_amount#} in your account {#account_no_last_four#} on {#date_time#}. Mode:{#payment_mode#} | Balance: {#balance_amount#} -Federal"
  NEFT_CREDIT_CONFIRMATION:
    VERSION_V1:
      Template: "{#first_name#}, {#reciever_name#} has received {#txn_amount#} from your account. Mode: {#payment_mode#} | {#date_time#} | Ref No. {#reference_no#} -Federal"
  POS_DEBIT:
    VERSION_V1:
      Template: "You've spent {#txn_amount#} at {#reciever_name#} on {#date_time#}. Balance: {#balance_amount#}. If it wasn't done by you, ping us on the Fi app. -Federal"
  POS_REVERSAL_CREDIT:
    VERSION_V1:
      Template: "Woohoo! The failed transaction is reversed. Your {#txn_amount#} has returned to the account {#account_no_last_four#}. Date: {#date_time#}. Balance: {#balance_amount#} -Federal"
  UNSUCCESSFUL_ATM_REVERSAL_CREDIT:
    VERSION_V1:
      Template: "Woohoo! The failed ATM transaction is reversed. Your {#txn_amount#} is back safe & sound in your account. Date: {#date_time#}. Balance: {#balance_amount#} -Federal"
  RTGS_CREDIT_CONFIRMATION:
    VERSION_V1:
      Template: "{#first_name#}, your RTGS transaction was successful. {#reciever_name#} received {#txn_amount#} on {#date_time#}. Ref No.{#reference_no#}. -Federal"
  RTGS_DEBIT:
    VERSION_V1:
      Template: "{#first_name#}, you've sent {#txn_amount#} to {#reciever_name#}. Mode: {#payment_mode#} | {#date_time#} | Ref:{#reference_no#}. Balance: {#balance_amount#} -Federal"
  CREDIT_CASH_DEPOSIT_MACHINE:
    VERSION_V1:
      Template: "We've added Rs.{#txn_amount#} to your account, {#first_name#}. Deposit location:{#location#} Balance: {#balance_amount#} -Federal"
  UPI_CREDIT:
    VERSION_V1:
      Template: "Hi {#first_name#}, {#txn_amount#} has been credited to your account no. {#account_no_last_four#}, on {#date_time#} by {#sender_name#} via UPI {#sender_upi_id#} -Federal"
    VERSION_V2:
      Template: "You received {#txn_amount#} in your Account {#account_no_last_four#}. Sent by {#sender_name#} | Date: {#date_time#} Mode: UPI {#sender_upi_id#}-Federal Bank"
  UPI_DEBIT:
    VERSION_V1:
      Template: "Hi {#first_name#}, {#txn_amount#} has been debited from your account no. {#account_no_last_four#} on {#date_time#}, towards {#sender_pi#}. For any concerns, call us at {#phone_number#} -Federal"
    VERSION_V2:
      Template: "{#txn_amount#} sent from your Account {#account_no_last_four#} Mode: UPI | To: {#sender_pi#} Date: {#date_time#} Not done by you? Call {#phone_number#} -Federal Bank"
  COLLECT_REQUEST:
    VERSION_V1:
      Template: "Hi {#first_name#}, {#sender_name#} has requested money from you on your Fi app. If you approve, {#txn_amount#} will be debited from your account -Federal"
  FAILED_TRANSACTION:
    VERSION_V1:
      Template: "Sorry, your UPI transaction for {#txn_amount#} has failed. Don't worry. Your money is safe. Just retry -Federal"
  GENERIC_PI_CREDIT:
    VERSION_V1:
      Template: "Hi {#first_name#}! Your account no. {#account_no_last_four#} has been credited with {#txn_amount#} on {#date_time#} -Federal"
    VERSION_V2:
      Template: "{#first_name#}, you've received {#txn_amount#} in your Account {#account_no_last_four#}. Woohoo! It was sent by {#sender_pi#} on {#date_time#}. -Federal Bank"
  GENERIC_PI_DEBIT:
    VERSION_V1:
      Template: "Hi {#first_name#}, {#txn_amount#} has been debited from your account no. {#account_no_last_four#} on {#date_time#}, towards {#sender_pi#}. For any concerns, call us at {#phone_number#} -Federal"
    VERSION_V2:
      Template: "{#txn_amount#} sent from your account {#account_no_last_four#} Sent to {#sender_pi#} on {#date_time#}. If this transaction wasn't done by you, call {#phone_number#}-Federal Bank"
  GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS:
    VERSION_V1:
      Template: "{#txn_amount#} sent from your account {#account_no_last_four#} Sent on {#date_time#}. If this transaction wasn't done by you, call {#phone_number#}-Federal Bank"
  FD_OPEN:
    VERSION_V1:
      Template: "Hi, {#fd_name#} created. Amount {#fd_amt#} | Interest rate: {#fd_int#} | Duration: {#fd_duration#} days i.e. till {#fd_maturity_date#} | Maturity amount: {#fd_maturity_amt#} | Renewal: {#fd_renewal_instruction#} -Federal Bank"
  SD_OPEN:
    VERSION_V1:
      Template: "Congrats! {#sd_name#} created. Amount {#sd_amt#} | Interest rate: {#sd_int#} | Duration: {#sd_duration#} days i.e. till {#sd_maturity_date#} | Maturity amount {#sd_maturity_amt#} -Federal Bank"
    VERSION_V2:
      Template: "Congrats! Smart Deposit {#sd_name#} created. Amount {#sd_amt#} | Interest rate: {#sd_int#} | Duration: {#sd_duration#} days i.e. till {#sd_maturity_date#} -Federal Bank"
  FD_SD_X_DAYS_BEFORE_MATURITY:
    VERSION_V1:
      Template: "Your {#deposit_inst#} no: {#deposit_no_last_four#} matures on {#deposit_maturity_date#}. Opened on {#deposit_open_date#}, for a duration of {#deposit_duration#}. Renewal: {#deposit_renewal_instruction#} | Renewal period: {#deposit_renewal_period#} - Federal Bank"
  SD_X_DAYS_BEFORE_MATURITY:
    VERSION_V1:
      Template: "Hi! Your Smart Deposit of {#deposit_amount#} named '{#deposit_name#}' will mature on {#deposit_maturity_date#}. For details, open the Fi app. - Federal Bank"
  ADD_FUNDS_SD:
    VERSION_V1:
      Template: "Hi, {#sd_add_amt#} has been added to your Smart Deposit account no {#sd_account_no_last_four#}. You can check it in your Fi app -Federal Bank"
  FD_SD_CLOSURE:
    VERSION_V1:
      Template: "Hello. Your {#deposit_inst#}, account no: {#deposit_no_last_four#}, has matured on {#deposit_maturity_date#}. So, we've added {#deposit_maturity_amt#} to your Savings Account no: {#account_no_last_four#}.-Federal Bank"
  INTEREST_PAID_IN_SB:
    VERSION_V1:
      Template: "Great news! We've added {#interest_amt#} as interest to your account {#last_four_acct_no#}. Date: {#date_time#}| Balance {#balance#}. Check Fi app for details. -Federal Bank"
  MOBILE_NUMBER_ADD:
    VERSION_V1:
      Template: "We've updated your mobile no\nRequest accepted on {#date#} at {#time#} | Customer ID {#customer_id#}. -Federal Bank"
  MOBILE_NUMBER_MODIFY:
    VERSION_V1:
      Template: "We've changed your registered mobile no. from {#old_mobile_no#} to {#new_mobile_no#}. Request accepted on {#date#} at {#time#} | Customer ID {#customer_id#}. -Federal Bank"
  CARD_CONTROL_ON:
    VERSION_V1:
      Template: "Hi {#first_name#}! We've successfully enabled your Debit Card {#debit_card_last_four#} for {#card_mode#} transactions. -Federal Bank"
  CARD_CONTROL_OFF:
    VERSION_V1:
      Template: "Hi {#first_name#}, we heard you loud & clear. As per your request, we've disabled {#card_mode#} transactions on your Debit Card {#debit_card_last_four#}. -Federal Bank"
  VKYC_APPROVED:
    VERSION_V1:
      Template: "Congrats {#first_name#}, you now hold a full Federal Bank Savings Account through Fi! Your KYC documents have been successfully verified. Curious about the benefits? Open the Fi app & find out. -Fi"
  WAITLIST_ACCESS:
    VERSION_V1:
      Template: "Congrats, {#first_name#}. We have sent you an exclusive early access link to our app.That's right!! You're on Fi's CBO shortlist.Check your email for more. -Fi"
    VERSION_V2:
      Template: "Congrats, {#first_name#}. We've sent you an exclusive early access to our app. That's right! You're on Fi's CBO shortlist. Check https://bit.ly/3mcliu8 -Fi"
  VKYC_WITH_LIMIT:
    VERSION_V1:
      Template: "Congrats {#first_name#}! Your request for a full KYC Federal Bank Savings Account through Fi has been approved. Your verified account has no balance limits & lifetime validity."
  FINITE_CODE:
    VERSION_V1:
      Template: "{#first_name#}, we've sent you something special! It's a Fi.Nite code: {#finite_code#}. Enter it and gain early access to the Fi app: https://bit.ly/3mcliu8 - Fi"
  CBO_FINITE_CODE:
    VERSION_V1:
      Template: "You've made it to the CBO shortlist! We are giving you early access to the Fi app: https://bit.ly/3mcliu8. Use the finite code {#finite_code#} to get started. -Fi"
  VKYC_SIX_WEEKS_BEFORE:
    VERSION_V1:
      Template: "Want the perks of a full KYC Federal Bank Savings Account through Fi? You're just one step away from enjoying many benefits! Open Fi & complete a 3-minute verification video call -Fi"
  VKYC_FOUR_WEEKS_BEFORE:
    VERSION_V1:
      Template: "Hi {#first_name#}, quick question. Could you finish a verification video call with us before {#account_freeze_date#}? It only takes 3 minutes! Do it today; open Fi. -Fi"
  VKYC_TEN_DAYS_BEFORE:
    VERSION_V1:
      Template: "Uh-oh, your account gets frozen on {#account_freeze_date#}! To avoid this, finish a verification video call with us. P.S: Account reactivation involves bank visits! -Fi"
  PAN_REMINDER:
    VERSION_V1:
      Template: "We're hoping this PANS out. But we need a few details. Keep your Aadhaar and PAN card ready so you can open your Federal Bank Savings Account on Fi in minutes. -Fi"
  EKYC_REMINDER:
    VERSION_V1:
      Template: "Let's meet on the Aadhaar side. Keep your Aadhaar details ready so you can open a Fi account in no time. Need help? Call Fi Care at 080-******** -Fi"
  NAME_MISMATCH_UPDATE:
    VERSION_V1:
      Template: "Hi {#first_name#}! We've got your name right and fixed it on our app. You can take your savings to the next level by opening a Fi account. We're waiting. -Fi"
  LIVENESS_REMINDER:
    VERSION_V1:
      Template: "We need a quick video & no you don't need to change. This is to complete your KYC and start your Fi account. Need help? Call Fi Care at 080-******** -Fi"
  CBO_FINITE_CODE_REMINDER:
    VERSION_V1:
      Template: "Congrats! You're on Fi's CBO shortlist.\nHere's your Fi.Nite code: {#finite_code#}.\nEnter it and gain early access to the Fi app: https://bit.ly/3mcliu8 -Fi"
  NON_CBO_REMINDER_SMS:
    VERSION_V1:
      Template: "{#name#}, your Fi account is waiting!\nHere's your Fi.Nite code: {#finite_code#}.\nEnter it and gain early access to the Fi app: https://bit.ly/3mcliu8\n- Fi"
  KYC_VALIDATION_FAILURE:
    VERSION_V1:
      Template: "Uh oh, we couldn't verify your KYC details the last time. Let's start over! Update the Fi app so you can continue opening your Fi account! -Fi"
  DEBIT_CARD_DELIVERY:
    VERSION_V1:
      Template: "Hi {#first_name#}, Your Fi-Federal Debit Card is on its way to you through {#carrier_partner#}. Here's a trackable Ref No. {#awb#}. - Fi"
  COMMUNITY_LOGIN_OTP:
    VERSION_V1:
      Template: "We appreciate your interest in Fi Club. Here's your OTP: {#otp#} - Fi"
  CARD_OUT_FOR_DELIVERY:
    VERSION_V1:
      Template: "Hey {#first_name#}, we've got good news. Our courier partner is heading out to deliver your Fi VISA Debit Card.\nP.S. Have fun unboxing your new Card.\n- Fi"
  CARD_DELIVERY_DELAY:
    VERSION_V1:
      Template: "{#first_name#}, we're sorry. There's an unforeseen delay in your Card's shipment. But rest assured, we'll send an update with tracking details soon.\n- Fi"
  CARD_DISPATCH_TIMELINE_INFO:
    VERSION_V1:
      Template: "Woohoo! A brand-new Fi VISA Platinum Debit Card will reach your doorstep in 15 days. We'll send you the tracking details for this shipment in 4-6 days. \n- Fi"
  ONBOARDING_KYC_COMPLETE:
    VERSION_V1:
      Template: "Your KYC is complete! Log in to the app and open your Federal Bank Savings Account on Fi. We're looking forward to seeing you on the (Fi)ne side of banking.\n-Fi"
  FIT_SMART_DEPOSIT_ADD_FUNDS:
    VERSION_V1:
      Template: "Hi, {#deposit_amt#} added in Smart Deposit no. {#sd_acc_no_last_four#} from account {#savings_acc_no_last_four#}. FIT rule: {#rule_name#} was executed {#count#} times on {#execution_date#} - Federal Bank (Fi)"
  CASH_WITHDRAWAL_ATM_FALLBACK:
    VERSION_V1:
      Template: "Withdrawn: {#withdraw_amount#} | This transaction occurred on {#date#} at {#time#}. If it wasn't done by you, ping us on the Fi app. -Federal Bank"
  POS_DEBIT_FALLBACK:
    VERSION_V1:
      Template: "You've spent {#txn_amount#} at {#time#} on {#date#}{#var1#}. If it wasn't done by you, ping us on the Fi app. -Federal Bank"
  NEFT_DEBIT_FALLBACK:
    VERSION_V1:
      Template: "{#first_name#}, you've sent {#txn_amount#} to {#reciever_name#}. Mode: {#payment_mode#} | {#date_time#} | Ref:{#reference_no#}{#var1#} -Federal Bank"
  NEFT_CREDIT_FALLBACK:
    VERSION_V1:
      Template: "{#first_name#}, {#sender_name#} has sent {#txn_amount#} to you. Mode:{#payment_mode#} | {#date_time#} | Ref No. {#reference_no#}{#var1#}{#var2#} -Federal Bank"
  RTGS_DEBIT_FALLBACK:
    VERSION_V1:
      Template: "{#first_name#}, you've sent {#txn_amount#} to {#reciever_name#}. Mode: {#payment_mode#} | {#date_time#} | Ref:{#reference_no#}{#var1#} -Federal Bank"
  INTEREST_PAID_IN_SB_FALLBACK:
    VERSION_V1:
      Template: "Great news! We've added {#interest_amt#} as interest to your account {#last_four_acct_no#}. Date: {#date_time#} | Check Fi app for details. -Federal Bank"
  MANDATE_RECEIVED:
    VERSION_V1:
      Template: "UPI auto-payment request received: {#PayeeName#} is trying to collect {#MandateAmount#}. To authorise this, open the Fi app.
-Fi"
  MANDATE_APPROVED:
    VERSION_V1:
      Template: "Auto-payment approved! Starting from {#mandateStartDate#}, we'll send {#mandateAmount#} from your Fi account to {#PayeeName#} on a {#MandateFrequency#} basis.
- Fi"
  MANDATE_DECLINED:
    VERSION_V1:
      Template: "Auto-payment declined! You've opted to reject sending a {#MandateFrequency#} amount of {#MandateAmount#} to {#PayeeName#} on {#MandateExecutionDate#}.
- Fi"
  MANDATE_CREATED:
    VERSION_V1:
      Template: "You're all set, {#PayerName#}! We've registered a {#MandateFrequency#} auto-payment towards {#PayeeName#} for {#MandateAmount#}. For more, open the Fi app.
- Fi"
  MANDATE_EXECUTION_SUCCESSFUL:
    VERSION_V1:
      Template: "We've successfully completed an auto-payment of {#MandateAmount#} to {#PayeeName#} on {#MandateExecutionDate#}.
- Fi"
  MANDATE_EXECUTION_FAILED:
    VERSION_V1:
      Template: "Yikes! Your auto-payment of {#MandateAmount#} to {#PayeeName#} has failed on {#MandateExecutionDate#}. If you need help, please contact us through the in-app chat.
- Fi"
  MANDATE_REVOKED:
    VERSION_V1:
      Template: "Auto-payment cancelled! We've successfully revoked your UPI AutoPay towards {#PayeeName#} for {#MandateAmount#}.
-Fi"
  MANDATE_MODIFIED:
    VERSION_V1:
      Template: "Auto-payment modified! We've successfully revised your UPI AutoPay towards {#PayeeName#} for {#MandateAmount#}.
-Fi"
  MANUAL_LIVENESS_PASSED:
    VERSION_V1:
      Template: "Hey {#first_name#}! Your video verification is done and your Fi account is all ready to be set up! Log in and get started. -Fi"
  TRANSACTION_REVERSED:
    VERSION_V1:
      Template: "This is a Payment Reversal update! We're sorry that your last transaction failed. However, we've safely returned the {#amount#} back into your account. \n-Fi"
  SI_CREATED:
    VERSION_V1:
      Template: "Your auto-payment rule has been created. {#amount#} will be sent {#frequency#} to {#payee_name#}. Check or edit your auto-payment rules on the app - Fi"
  SI_DECLINED:
    VERSION_V1:
      Template: "Your auto-payment rule could not be set up. {#amount#} will not be sent to {#payee_name#}. Try again on the app after some time? - Fi"
  SI_EXECUTION_SUCCESSFUL:
    VERSION_V1:
      Template: "Auto-payment successful! You sent {#amount#} to {#payee_name#} on {#execution_date#}, as per auto-payment rules set by you. Check your app for details. - Fi"
  SI_EXECUTION_FAILED:
    VERSION_V1:
      Template: "Your auto-payment failed. {#amount#} could not be sent to {#payee_name#} as per your auto-payment rule. Check your app for details - Fi"
  MANDATE_AUTHORISED:
    VERSION_V1:
      Template: "{#payeename#} has sent an AutoPay request for {#amount#}. As the amount is over ₹5000, please authorise the transaction via the Fi app."
  MANDATE_ACCEPTANCE:
    VERSION_V1:
      Template: "{#payername#} wants to send you {#amount#}. They've set up a UPI auto-payment to do so. To accept this transaction, log in to the Fi app.
- Fi"
  MANDATE_PAUSED:
    VERSION_V1:
      Template: "{#payername#}, your {#payeename#} UPI auto-payment has been paused temporarily. For any queries, please contact us via the in-app chat.
- Fi"
  MANDATE_UNPAUSED:
    VERSION_V1:
      Template: "We've successfully resumed your UPI auto-payment towards {#payeename#}. If you didn't authorise this, please notify us through the in-app chat.
- Fi"
  MUTUAL_FUND_WITHDRAWAL_OTP:
    VERSION_V1:
      Template: "{#otp#} is the OTP to confirm your withdrawal from {#mutual_fund#}. Amount: {#amount#}. 82275JXpmM1 -Fi "
  MUTUAL_FUND_ONE_TIME_BUY_OTP:
    VERSION_V1:
      Template: "{#otp#} is the OTP to confirm your lump sum investment in {#mutual_fund#}. Amount: INR {#amount#}. 82275JXpmM1 - Fi"
  MUTUAL_FUND_REGISTER_SIP_OTP:
    VERSION_V1:
      Template: "{#otp#} is the OTP to confirm your {#frequency#} investment in {#mutual_fund#}. Amount: INR {#amount#}. 82275JXpmM1 - Fi"
  OB_VKYC_REMINDER_ONE:
    VERSION_V1:
      Template: "Take a few minutes to complete your Video KYC and your Fi Account will be ready for you! Get upto Rs. {#rewardAmount#} once you sign-up: https://bit.ly/3mcliu8 -Fi"
  OB_VKYC_REMINDER_TWO:
    VERSION_V1:
      Template: "Thanks for registering on Fi. You are minutes away from joining 1 million other active users with a Fi account! Sign up now: https://bit.ly/3mcliu8 -Fi "
  OB_SCREENER_VERIFICATION_REMINDER_ONE:
    VERSION_V1:
      Template: "You're less than 250 seconds away from earning a Rs. 250 joining bonus! To sign up, simply tell us where you work. Tap here: https://invite.fi.money/ZjEQ/OB
- Fi"
  OB_SCREENER_VERIFICATION_REMINDER_TWO:
    VERSION_V1:
      Template: "You're 5 minutes away from your Fi zero balance savings account!! Your money's insured upto Rs. 5L. Plus, we uphold high standards of security & privacy when handling your data. Wrap up your account verification now: https://invite.fi.money/ZjEQ/OB
-Fi "
  ECS_RETURN_CHARGES:
    VERSION_V1:
      Template: "Hi, ECS request declined due to insufficient funds. {#amount#} will get deducted as ECS return charges from your account. For more, visit fi.money/fees\n- Federal Bank"
  ATM_DECLINE_FEES:
    VERSION_V1:
      Template: "Hi, your attempt to withdraw cash from an account with insufficient funds will be charged an ATM Decline Fee of {#amount#}. For more info, visit fi.money/fees\n- Federal Bank"
  DUPLICATE_CARD_FEE:
    VERSION_V1:
      Template: "Hi, a Card Replacement fee  {#amount#} is deducted from your Fi account. For all card-related fees, visit fi.money/fees\n- Federal Bank"
  ATM_WITHDRAWAL_COMPLAINT_PENALTY:
    VERSION_V1:
      Template: "As per RBI guidelines, we have delayed your failed ATM transaction's reimbursement claim. To make up for this, we have added {#amount#} to your Fi account.\n- Federal Bank"
  INTERNATIONAL_ATM_CHARGES:
    VERSION_V1:
      Template: "Hi, an International ATM Fee of {#amount#} has been deducted from your account for transacting in an ATM outside India. For more, visit : fi.money/fees\n- Federal Bank"
  OTHER_BANK_ATM_USAGE_CHARGES:
    VERSION_V1:
      Template: "Hi, an ATM Usage Fee of {#amount#} is deducted from your account as you've exceeded your free usage limits at Other Bank ATMs. For more info, visit: fi.money/fees\n- Federal Bank"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_FORTY_FIVE_DAYS:
    VERSION_V1:
      Template: "Your Fi account with a balance of Rs. {#amount#} will be restricted in {#remaining_day} days. To avoid this, complete your KYC video call now. Tap here: {#link} -Fi"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_THIRTY_DAYS:
    VERSION_V1:
      Template: "Your Federal Bank Savings Account through Fi with a balance of Rs. {#amount#} will be permanently closed in {#remaining_day} days. To avoid this, complete your KYC video call now. Tap here: {#link} -Fi"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_ELEVEN_DAYS:
    VERSION_V1:
      Template: "Alert! Your Federal Bank Savings Account through Fi with a balance of Rs. {#amount#} will be permanently closed in {#remaining_day} days. To avoid this, complete your KYC video call now. Tap here: {#link} -Fi"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_FIVE_DAYS:
    VERSION_V1:
      Template: "Alert! Your Federal Bank Savings Account through Fi with a balance of Rs. {#amount#} will be permanently closed in {#remaining_day} days. To avoid this, complete your KYC video call now. Tap here: {#link} -Fi"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_THREE_DAYS:
    VERSION_V1:
      Template: "Alert! Your Federal Bank Savings Account through Fi with a balance of Rs. {#amount#} will be permanently closed in {#remaining_day} days. To avoid this, complete your KYC video call now. Tap here: {#link} -Fi"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_ONE_DAY:
    VERSION_V1:
      Template: "Your Federal Bank Savings Account will permanently close today! Please transfer any remaining funds to another account. In case you can't do it, share your details with us at https://fi.money/min-kyc-closed-account -Fi"
  TOD_CHARGES_DEBIT:
    VERSION_V1:
      Template: "Your account has been charged with a temporary fee of Rs. {#amount#}. This amount will be refunded to you in the next 48 hrs. Sorry about the inconvenience. -Fi "
  ONBOARDING_ACCOUNT_BLOCK_DAY_ZERO:
    VERSION_V1:
      Template: "{#username#}, Your Federal Bank Account has been frozen because of security reasons. Please respond to the email sent from {#email#} on your registered email ID to re-activate your account"
  ONBOARDING_ACCOUNT_UNBLOCK:
    VERSION_V1:
      Template: "{#username#}, Your Federal Bank account has been reactivated. You can now use your Fi app with your registered mobile number. We apologize for the inconvenience caused"
  WEALTH_ACCOUNT_NOMINEE_DECLARATION_OTP:
    VERSION_V1:
      Template: "<#>Your OTP code is {#otp#}. Regarding the Nomination Declaration for all upcoming mutual fund investments on Fi, you have: {#choice#}. 82275JXpmM1 -Fi"
  ONBOARDING_DOB_AND_PAN_DROP_OFF:
    VERSION_V1:
      Template: "Hey {#var#}, you are 50% done with your account setup on Fi. Share your identity details to complete your account setup now- {#link#} -Fi"
  CREDIT_REPORT_DOWNLOAD_OTP_VERIFICATION:
    VERSION_V1:
      Template: "<#> Your OTP to view credit score on Fi is {#otp#}. Do not share this with anyone for your security. {#hash#} -Fi"
  CREDIT_CARD_CROSS_BORDER_TRANSACTION_SUCCESS:
    VERSION_V1:
      Template: "You've made a transaction of {#amount#} from {#beneficiary#}. Note: Currency exchange rate {#currency_exchange_rate#} & markup fee: {#mark_up_fee#}
- Federal Bank"
  CREDIT_CARD_ATM_TRANSACTION_FAILURE:
    VERSION_V1:
      Template: "Uh-oh! It looks like your ATM transaction for {#amount#} was unsuccessful. Don't worry; your money is safe. Please, retry later.
- Federal Bank"
  CREDIT_CARD_ATM_TRANSACTION_SUCCESS:
    VERSION_V1:
      Template: "{#first_name#}, your ATM transaction of {#amount#} was successful! Account balance: {#available_account_balance#}. If you didn't do this, call {#helpline_number#}.
- Federal Bank"
  CREDIT_CARD_FAILED_TRANSACTION_REVERSAL_SUCCESS:
    VERSION_V1:
      Template: "Your failed Credit Card transaction has been reversed! {#amount#} returned to your account {#account_number#}. Date: {#reversal_date#}. Balance: {#available_account_balance#}
- Federal Bank"
    VERSION_V2:
      Template: "Your failed Credit Card transaction for the card ending with {#cc_ending_digits#} has been reversed! {#amount#} returned. Date: {#reversal_date#}. Balance: {#available_account_balance#}
- Federal Bank"
  CREDIT_CARD_TRANSACTION_DECLINED:
    VERSION_V1:
      Template: "Transaction declined! {#beneficiary#} has not received {#amount#} on {#txn_date#}. Your money is safe. Please retry the payment later.
- Federal Bank"
    VERSION_V2:
      Template: "Transaction declined on the Credit Card ending with {#cc_ending_digits#}.{#beneficiary#}. has not received {#amount#} on {#txn_date#}. Your money is safe. Please retry later.
- Federal Bank"
  CREDIT_CARD_TRANSACTION_SUCCESS:
    VERSION_V1:
      Template: "Transaction successful! {#amount#} spent at {#beneficiary#} on {#txn_date#}. If you didn't do this, call {#helpline_number#}.
- Federal Bank"
    VERSION_V2:
      Template: "Transaction successful on Credit Card ending with {#cc_ending_digits#}.{#amount#} spent at {#beneficiary#}. on {#txn_date#} at {#txn_time#}. If you didn't do this, call {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_INTERNATIONAL_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Template: "Hey there {#first_name#}, international transactions on your {#card_type#} Credit Card {#last_four_digits#} are now disabled.
- Federal Bank"
  CREDIT_CARD_INTERNATIONAL_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Template: "Hi {#first_name#}, we have enabled international transactions on your {#card_type#} Credit Card {#last_four_digits#}. Bon voyage!
- Federal Bank"
  CREDIT_CARD_POS_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Template: "Hey {#first_name#}, as requested, we have disabled POS transactions on your {#card_type#} Credit Card {#last_four_digits#}.
- Federal Bank"
  CREDIT_CARD_POS_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Template: "Good going, {#first_name#}! POS transactions on your {#card_type#} Credit Card {#last_four_digits#} are enabled. - Federal Bank"
  CREDIT_CARD_CONTACTLESS_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Template: "{#first_name#}, contactless transactions on your {#card_type#} Credit Card {#last_four_digits#} are now disabled, as requested.
- Federal Bank"
  CREDIT_CARD_CONTACTLESS_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Template: "{#first_name#}, we have successfully enabled your {#card_type#} Credit Card {#last_four_digits#} for contactless transactions. Now, you can tap & pay.
- Federal Bank"
  CREDIT_CARD_ONLINE_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Template: "{#first_name#}, we heard you loud & clear. Online transactions on your {#card_type#} Credit Card {#last_four_digits#} are now disabled, as requested.
- Federal Bank"
  CREDIT_CARD_ONLINE_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Template: "Congrats, {#first_name#}. We've successfully enabled your {#card_type#} Credit Card {#last_four_digits#} for online payments. Commence the shopping spree!
- Federal Bank"
  CREDIT_CARD_EMI_CREATED:
    VERSION_V1:
      Template: "Hey {#first_name#}, Congrats on successfully converting your Credit Card transaction to an EMI! Please check the Fi app for details.
-Federal Bank"
  CREDIT_CARD_EMI_CLOSED:
    VERSION_V1:
      Template: "Well done! You've fully paid off the Credit Card EMI for your purchase at {#merchant_name#}. Congrats on successfully closing this EMI on time.
- Federal Bank"
  CREDIT_CARD_EMI_PRE_CLOSED:
    VERSION_V1:
      Template: "We have received your Credit Card EMI foreclosure request on {#current_timestamp#} for EMI {#merchant_name#}. Amount due: {#due_amount#}. Foreclosure fee: {#pre_closure_fee#}. For details, check the Fi app.
- Federal Bank"
  CREDIT_CARD_EMI_CANCELLED:
    VERSION_V1:
      Template: "Congrats, Credit Card EMI foreclosed as per your request! You no longer need to make payments for the EMI started for your purchase at {#merchant_name#}.
- Federal Bank"
  CREDIT_CARD_REVISED_LIMIT_SMS:
    VERSION_V1:
      Template: "Hi {#first_name#}, your Fi-Federal co-branded Credit Card's limit has been adjusted to {#limit#}, in line with Federal Bank's policy. Regular usage & timely bill payments may help you increase your limit going forward -Federal Bank"
  CATEGORY_SPENDS_EXCEEDED_REMINDER_SMS:
    VERSION_V1:
      Template: "ALERT!\n\nYou have spent Rs. {#amount#} on {#category#} already by {#date#}\n\nCheck your Fi App for a detailed analysis of your spends here {#deeplink#} -Fi"
    VERSION_V2:
      Template: "ALERT!\n\nYour customised reminder that you have spent Rs. {#configured_amount#} on {#category#}\n\nYou have already spent Rs. {#amount#} so far.\n\nCheck your Fi App for a detailed analysis of your spends here {#deeplink#} -Fi"
  AMOUNT_SPENDS_EXCEEDED_REMINDER_SMS:
    VERSION_V1:
      Template: "ALERT!\n\nThis is a reminder that you have already spent Rs.{#amount#}\n\nFor a detailed analysis of your spends, check your Fi app {#deeplink#} -Fi"
    VERSION_V2:
      Template: "ALERT!\n\nYour customised reminder that you have already spent Rs.{#amount#}\n\nFor a detailed analysis of your spends, check your Fi app {#deeplink#} -Fi"
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER_SMS:
    VERSION_V1:
      Template: "ALERT! Your Credit Card bill for Rs.{#amount#} is due on {#date#}. Please pay your bill on time to avoid late fees -Fi"
    VERSION_V2:
      Template: "ALERT!\n\nYour customised reminder that your Credit Card bill for Rs.{#amount#} is due on {#date#}\n\nPlease pay your bill on time to avoid late fees.\n\nCheck your bill on the Fi App here {#deeplink#} -Fi"
  CREDIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS:
    VERSION_V1:
      Template: "ALERT! You've exceeded the maximum PIN attempts for transactions on your Credit Card {#last_four_digits#}. If you didn't do this, report it to {#helpline_number#}.
- Federal Bank"
  CREDIT_CARD_PAYMENT_NOT_DONE_REMINDER_WITH_INTEREST_CHARGE:
    VERSION_V1:
      Template: "You may be charged late fees + additional interest! Please pay your {#credit_card_type#} Credit Card bill as soon as possible.
- Federal Bank"
  CREDIT_CARD_PAYMENT_NOT_DONE_REMINDER:
    VERSION_V1:
      Template: "{#first_name#}, your bill for Credit Card no {#masked_card_number#} is still unpaid. Please, complete the payment to avoid late fee charges.
- Federal Bank"
  CREDIT_CARD_BILL_SUCCESSFUL_REPAYMENT:
    VERSION_V1:
      Template: "Well done, {#first_name#}! You have successfully repaid the bill for your {#credit_card_type#} Credit Card no: {#masked_card_number#}.
- Federal Bank"
  CREDIT_CARD_BILL_REPAYMENT_DUE:
    VERSION_V1:
      Template: "Hi {#first_name#}, here's a gentle reminder: Your Credit Card bill is due in {#day#} days. To avoid late charges, pay {#link#}.
- Federal Bank"
  CREDIT_CARD_STATEMENT_GENERATION:
    VERSION_V1:
      Template: "{#first_name#}, we've generated the statement for your Credit Card no: {#card_number#}. It will soon arrive in your registered email's inbox.
- Federal Bank"
  CREDIT_CARD_COMMUNICATE_TRANSACTION_CHARGES:
    VERSION_V1:
      Template: "As per your recent Credit Card transactions, a cash withdrawal fee of {#fee#} will get added to your Credit Card statement.
- Federal Bank"
  CREDIT_CARD_REPLACEMENT:
    VERSION_V1:
      Template: "A new Credit Card will reach you soon! Once it arrives, use it for daily spends & earn Fi-Coins. To start: Scan the QR code within the card kit.
- Federal Bank"
  CREDIT_CARD_LIMIT_CHANGE_FAILURE:
    VERSION_V1:
      Template: "{#first_name#}, there's an issue with changing your Credit Card's limit. It's a temporary hiccup - please, try again in some time.
- Federal Bank"
  CREDIT_CARD_CARD_USAGE_CHANGE_FAILURE:
    VERSION_V1:
      Template: "Uh-oh! We were unable to process your Credit Card settings. Could you please try again in some time? Thanks in advance.
- Federal Bank"
  CREDIT_CARD_CONTACTLESS_PURCHASE_LIMIT_CHANGED:
    VERSION_V1:
      Template: "{#name#}, your Credit Card's purchase limit for contactless payments has been modified. If you didn't do this, call {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_ONLINE_PURCHASE_LIMIT_CHANGED:
    VERSION_V1:
      Template: "{#name#}, your Credit Card's purchase limit for online transactions has been modified. If you didn't do this, call {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_POS_PURCHASE_LIMIT_CHANGED:
    VERSION_V1:
      Template: "Your Credit Card's purchase limit for POS transactions has successfully been modified. If you didn't do this, report it via: {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_UNFREEZING_FAILURE:
    VERSION_V1:
      Template: "{#name#}, your attempt to unfreeze your {#card_type#} Credit Card {#last_four_digits#} was unsuccessful. Need help? Please call Fi Care: {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_FREEZING_FAILURE:
    VERSION_V1:
      Template: "{#name#}, your attempt to freeze your {#card_type#} Credit Card {#last_four_digits#} was unsuccessful. Don't worry; call Fi Care at {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_UNFREEZING_SUCCESS:
    VERSION_V1:
      Template: "Hi {#name#}, it looks like you've unfrozen your card. Great! Your {#card_type#} Credit Card {#last_four_digits#} is now fully functional again.
- Federal Bank"
  CREDIT_CARD_FREEZING_SUCCESS:
    VERSION_V1:
      Template: "Your {#card_type#} Credit Card {#last_four_digits#} is frozen. All outgoing transactions are now temporarily suspended. You can unfreeze it anytime on Fi/ FedMobile app.
- Federal Bank"
  CREDIT_CARD_PIN_CHANGE_SUCCESS:
    VERSION_V1:
      Template: "Hey {#name#}, the PIN for your Credit Card has successfully changed! If not done by you, please contact Fi Care @ {#helpline_number#}.
- Federal Bank"
  CREDIT_CARD_ACTIVATION_FAILURE:
    VERSION_V1:
      Template: "Your Credit Card did not get activated! Retry later by scanning the QR code within the card kit! Need help? Please contact Fi Care: {#helpline_number#}
- Federal Bank"
  CREDIT_CARD_PHYSICAL_CARD_ACTIVATION_SUCCESS:
    VERSION_V1:
      Template: "Activated! Your physical Credit Card is now ready to rock & roll. Its got a ton of offers linked to it; use it for daily spends & earn Fi-Coins.
- Federal Bank"
  CREDIT_CARD_DIGITAL_CARD_ACTIVATION_SUCCESS:
    VERSION_V1:
      Template: "Well done! Your digital Credit Card is active. Now, you can use it for your online spends, earn Fi-Coins & explore all the offers linked to it.
- Federal Bank"
  CREDIT_CARD_ACTIVATION_INFORMATION:
    VERSION_V1:
      Template: "Unboxed your Credit Card? Great! Now activate & use it for your daily spends to earn Fi-Coins. To start: Scan the QR code within the card kit.
- Federal Bank"
  CREDIT_CARD_SHIPMENT_DELAY:
    VERSION_V1:
      Template: "{#name#}, your Credit Card's shipment may take longer than expected. Tracking ID: {#tracking_id#}. Link to track: {#tracking_link#}
- Federal Bank"
  CREDIT_CARD_DISPTACH_DELAY:
    VERSION_V1:
      Template: "There was an unforeseen dispatch-related delay for your Credit Card. Don't worry; we'll send an update & shipment tracking details soon.
- Federal Bank"
  CREDIT_CARD_DISPTACHED_WITH_TRACKING_NUMBER:
    VERSION_V1:
      Template: "Hi {#name#}, your {#card_type#} Credit Card is on its way to you through {#tracking_name#}. For tracking details, open the Fi app.
- Federal Bank"
  CREDIT_CARD_ISSUED_WITH_CREDIT_LIMIT:
    VERSION_V1:
      Template: "Congrats! You've been issued a brand-new Credit Card with a {#card_limit#} credit limit. It comes with loads of benefits - explore it on the Fi app.
- Federal Bank"
  CREDIT_CARD_ISSUED:
    VERSION_V1:
      Template: "Congrats! A spanking new {#card_type#} Credit Card will reach your doorstep in 15 days. We'll send the tracking details soon!
- Federal Bank"
  CREDIT_CARD_INCOMPLETE_APPLICATION_PROCESS:
    VERSION_V1:
      Template: "You've unlocked a {#credit_limit#} credit limit! Finish signing up in minutes & get a brand-new Credit Card. Tap to complete: {#link#} - Federal Bank"
  CREDIT_CARD_COMPLETE_VIDEO_KYC_FOR_CREDIT_CARD:
    VERSION_V1:
      Template: "Hey {#name#}! Want a {#card_limit#} limit Credit Card issued by us? Just complete your KYC via a 3-min video call: {#link#}
- Federal Bank"
  CREDIT_CARD_OTP_FOR_CHANGING_CARD_PIN:
    VERSION_V1:
      Template: "{#otp#} is the OTP you'll need to set/reset your Credit Card's PIN. Valid for ten minutes only - Federal Bank"
  CREDIT_CARD_REWARD_POINTS_CREDITED:
    VERSION_V1:
      Template: "Congrats {#name#}! You earned {#coins#} Fi-Coins for your Credit Card spends. Head to the Fi app, redeem them & get rewards.
- Federal Bank"
  CREDIT_CARD_LIMIT_REACHING_THRESHOLD:
    VERSION_V1:
      Template: "Heads up! You have reached {#threshold_percentage#}% of your monthly credit limit. Any further transactions over {#available_limit#} would lead to over-limit charges.
- Federal Bank"
  CREDIT_CARD_TRANSACTION_DECLINED_WITH_REASON:
    VERSION_V1:
      Template: "Transaction declined on the Credit Card ending with {#cc_ending_digits#}. {#beneficiary#} has not received {#amount#} on {#txn_date#} . Reason: {#decline_reason#}
- Federal Bank"
    VERSION_V2:
      Template: "Hi, Transaction declined for Credit Card ending {#cc_ending_digits#}. {#beneficiary#} has not received {#amount#} on {#txn_date#} . Reason: {#decline_reason#}. {#action_item#}.
    - Federal Bank"
    VERSION_V3:
      Template: "{#txn_failure_reason#} {#txn_failure_fix#}
- Federal Bank"
  CREDIT_CARD_JOINING_FEES:
    VERSION_V1:
      Template: "Fee update! We've added a joining fee of {#amount#} to your Credit Card ending with {#cc_ending_digits#} on {#txn_date#} at {#txn_time#}.
- Federal Bank"
  CREDIT_CARD_UNPAID_DUE_FEES:
    VERSION_V1:
      Template: "Fee update! We've levied an interest of {#amount#} to your Credit Card ending with {#cc_ending_digits#} on {#txn_date#} at {#txn_time#} for unpaid dues.
- Federal Bank"
  CREDIT_CARD_GENERIC_CREDIT:
    VERSION_V1:
      Template: "Update! We've received {#amount#} towards your Credit Card ending with {#cc_ending_digits#} on {#txn_date#} at {#txn_time#}.
- Federal Bank"
  UPI_PIN_SET_RESET:
    VERSION_V1:
      Template: "Your UPI PIN has been successfully generated via Fi. If you didn't do this, inform Fi Care immediately at 080********. P.S Do not share UPI PIN/Card info/OTP/CVV with anyone! -Fi"
  PL_LOAN_AGREEMENT_OTP:
    VERSION_V1:
      Template: "Your OTP to acknowledge & e-sign the personal loan KFS and agreement on Fi is {#var#}. For your security, do not share this with anyone.
-Fi"
  LOAN_CONTACTABILITY_ALTERNATE_PHONE_NUMBER_SMS:
    VERSION_V1:
      Template: "{#var#} is your OTP. It helps us verify your alternate phone number, needed to process your loan application. Do not share this OTP with anyone.
      -Fi"
  CIBIL_REPORT_SMS:
    VERSION_V1:
      Template: "Alert! Your free CIBIL report has been successfully fetched via Fi. Click here {#var#} to view report. -Fi"
  CREDIT_CARD_NOT_ACTIVATED_SMS:
    VERSION_V1:
      Template: "{#first_name#}, your Credit Card ending with {#cc_last_four_digits#} has been inactive for {#total_inactive_days#} days. It will be permanently deactivated. To avoid this, use your card and get exciting rewards.
- Federal Bank"
  CREDIT_CARD_CLOSURE_CONFIRMATION_SMS:
    VERSION_V1:
      Template: "Hi {#first_name#}, as requested, we've closed your Credit Card ending with {#cc_last_four_digits#}. Have feedback to share? Don't hesitate to tell <NAME_EMAIL>
- Federal Bank"
  SECURED_CREDIT_CARD_SUCCESSFUL_FD_CREATION:
    VERSION_V1:
      Template: "Fixed Deposit {#deposit_identifier#} opened while applying for Credit Card. Amt: {#deposit_amount#} | Int rate: {#interest_rate#} | Duration: {#deposit_duration#} days i.e. till {#maturity_date#}.
- Federal Bank"
  SECURED_CREDIT_CARD_FD_LIEN_MARKING_INTIMATION:
    VERSION_V1:
      Template: "Your Fixed Deposit {#deposit_identifier#} will be linked as security for your Credit Card. For the card to remain active, this FD cannot be closed.
- Federal Bank"
  SECURED_CREDIT_CARD_FD_CLOSURE_CONFIRMATION:
    VERSION_V1:
      Template: "Fixed Deposit {#deposit_identifier#}, linked to your Credit Card, was closed on {#closure_date#}. We've added {#maturity_amount#} to your Savings Account no: {#account_number#}.
- Federal Bank"
  SECURED_CREDIT_CARD_FD_CLOSURE_WARNING:
    VERSION_V1:
      Template: "{#first_name#}, please do pay the bill for your Credit Card ending with {#cc_last_four_digits#} . Failing to do so will result in card cancellation & FD closure.
- Federal Bank"
  CREDIT_CARD_WEB_ELIGIBILITY_CHECK_LOGIN_OTP:
    VERSION_V1:
      Template: "Your OTP to verify your phone number & find your credit score on Fi is {#otp#}. For your security, do not share this with anyone. -Fi"
  CREDIT_CARD_WEB_ELIGIBILITY_APPROVED_SMS:
    VERSION_V1:
      Template: "Hey {#name#}, congratulations! You are one step closer to getting your hands on the Amplifi Fi-Federal Credit Card. Download the Fi app now {#download_link#} -Fi"
  CREDIT_CARD_WEB_ELIGIBILITY_REJECTED_SMS:
    VERSION_V1:
      Template: "It looks like you are not yet eligible for the AmpliFi Fi-Federal Credit Card. Do not worry Fi has lots more to offer you. Download the app & browse the features. {#download_link#} -Fi"
  RISK_ACCOUNT_FREEZE_SMS:
    VERSION_V1:
      Template: "Due to suspicious activity, your Federal Bank a/c linked to the Fi app is frozen. To unfreeze it, check your inbox for an email from {#fi_contact_email#} - Fi"
  DEBIT_CARD_FOREX_MARKUP_REFUND_RECEIVED:
    VERSION_V1:
      Template: "Forex fee refund Rs.{#refund_amt#} sent to your a/c {#acc_number#}. Enjoy unlimited Zero Forex on international spends via Fi's Infinite or Salary plans. -Fi"
  DEBIT_CARD_UNABLE_TO_PROCESS_TRANSACTION:
    VERSION_V1:
      Template: "Hi! Your Fi-Federal Debit Card transaction was not processed. Check card control settings. {#download_link#} -Fi"
  DEBIT_CARD_INCORRECT_PIN:
    VERSION_V1:
      Template: "Hi! Your Fi-Federal Debit Card transaction couldn't be processed. Wrong PIN entered. Try again. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined due to invalid PIN. Please try again with the correct PIN -Fi"
  DEBIT_CARD_UNABLE_TO_AUTHORIZE_TRANSACTION:
    VERSION_V1:
      Template: "Hi! We could not authorise the transaction on your Fi-Federal Debit Card. Check the app for details. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} could not be processed. Please try again & reach out to in-app help chat on Fi app of the issue persists -Fi"
  DEBIT_CARD_CARD_EXPIRED:
    VERSION_V1:
      Template: "Hi! Your Fi-Federal Debit Card has expired. Use your Fi app to apply for a new one. {#download_link#} -Fi"
  DEBIT_CARD_ECOM_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Template: "Hi! Your Fi-Federal Debit Card is not enabled for e-commerce transactions. Enable ecommerce transactions on the Fi App. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed as online transactions are not enabled for your card. Activate Online transactions for your card on the Fi app -Fi"
  DEBIT_CARD_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED:
    VERSION_V1:
      Template: "Hi! You have exceeded the daily max limit on your Fi-Federal Debit Card. Try again tomorrow. {#download_link#} -Fi"
  DEBIT_CARD_POS_NOT_SUPPORTED:
    VERSION_V1:
      Template: "Hi! Your Fi-Federal Debit Card is not supported at point of sale. Please try paying with UPI. {#download_link#} -Fi"
  DEBIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS:
    VERSION_V1:
      Template: "Hi! you entered the wrong PIN too many times on your Fi-Federal Debit Card blocked. Contact us to unblock. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your Fi-Federal Debit Card ending with {#last_four_digit#} has been blocked for entering incorrect PIN too many times. You can try using your card after 48 hours -Fi"
  DEBIT_CARD_DUPLICATE_TRANSACTION:
    VERSION_V1:
      Template: "Hi! Your transaction of Rs. {#amount#} was made twice on your Fi-Federal Debit Card. Check again!  {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! You've already made a similar payment using your Debit Card. Re-check this transaction to avoid duplicate payments. -Fi"
  DEBIT_CARD_TRANSACTION_DECLINED:
    VERSION_V1:
      Template: "Hi!, your transaction for Rs. {#amount#} on your Fi-Federal Debit Card was declined. Check card controls on Fi app. {#download_link#} -Fi"
  DEBIT_CARD_TRANSACTION_TYPE_NOT_SUPPORTED:
    VERSION_V1:
      Template: "Hi! Your Fi-Federal Debit Card does not support this transaction. Check card controls or contact customer care. {#download_link#} -Fi"
  DEBIT_CARD_INVALID_TRANSACTION:
    VERSION_V1:
      Template: "Hi! Your transaction of Rs. {#amount#} was on your Fi-Federal Debit Card was invalid. Check card controls and try again {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined due to wrong card details. Please try again -Fi"
  DEBIT_CARD_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Template: "Hi! Your transaction on your Fi-Federal Debit Card was declined. Check your Card Settings & enable International Transactions. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! A recent payment using Fi-Federal Debit Card ending with {#last_four_digit#} has failed because internation usage is not enabled on your card. Enable it on the Fi app & try again -Fi"
  DEBIT_CARD_CONTACTLESS_CARD_USAGE_NOT_ENABLED:
    VERSION_V1:
      Template: "Hi! Contactless payments on your Fi-Federal Debit Card are not enabled. Enable it on your Fi app. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! A Contactless transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as Contactless payments are currently disabled for your card. Enable it now on the Fi app & try again -Fi"
  DEBIT_CARD_INSUFFICIENT_FUNDS_FOR_TRANSACTION:
    VERSION_V1:
      Template: "Alert: It looks like your account has insufficient funds. Add money to your account to complete your payment. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to insufficient funds in your savings account. Add funds & try again -Fi"
  DEBIT_CARD_DAILY_WITHDRAWAL_LIMIT_REACHED:
    VERSION_V1:
      Template: "Hey there! It looks like you have reached your ATM withdrawal limit. Increase your daily limit under card settings on the Fi app. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your recent attempt to withdraw cash from an ATM using your Fi-Federal Debit Card ending in {#last_four_digit#} on {#txn_date#} at {#txn_time#} was unsuccessful. Reason - You have exceeded the daily ATM withdrawal limit set on the Fi app. Change it on the Fi app & try again -Fi"
  DEBIT_CARD_LOW_FUNDS_FOR_TRANSACTION:
    VERSION_V1:
      Template: "Hey! Your account does not have enough funds to complete the payment. Add funds now! {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to insufficient funds in your savings account. Add funds & try again -Fi"
  DEBIT_CARD_INVALID_EXPIRY_DATE:
    VERSION_V1:
      Template: "Hi there! You seem to have entered the incorrect expiry date. Recheck the details on the Fi app or your Debit Card to complete the payment. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as you have entered the wrong expiry date. Please try again later -Fi"
  DEBIT_CARD_NFC_NOT_ENABLED:
    VERSION_V1:
      Template: "Hey there! Your contactless payment was not completed since you have not enabled NFC on your device. Enable NFC & try again. {#download_link#} -Fi"
  DEBIT_CARD_PRM_DECLINED:
    VERSION_V1:
      Template: "Hi there! Your transaction has been declined and flagged as risky. Re-check your payment to continue. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your recent attempt to withdraw cash from an ATM in {#country#} using Fi-Federal Debit Card ending with {#last_four_digit#} on {#txn_date#} at {#txn_time#} was unsuccessful. Reason - You have exceeded the daily ATM withdrawal limits applicable on your card in <Country>. Please try again tomorrow - {#download_link#} -Fi"
  DEBIT_CARD_CVV_ERROR:
    VERSION_V1:
      Template: "Alert: The CVV you entered is incorrect. Add the correct details to complete your payment. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined due to incorrect CVV. Please try again with correct CVV number -Fi"
  DEBIT_CARD_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED:
    VERSION_V1:
      Template: "Hey! You have reached your daily contactless payment limit. Try again tomorrow & contact us if the issue persists. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! A contactless transaction at {#merchant#} using your Fi-Federal Debit Card ending with {#last_four_digit#} was declined as you have reached the daily contactless payment limit set for your card. Increase the limits for the card to try paying again -Fi"
  DEBIT_CARD_CARD_OFF_FOR_TRANSACTIONS:
    VERSION_V1:
      Template: "Hey there, please enable transactions under 'card controls' in card settings of the Fi app to use your Debit Card. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your payment using Fi-Federal Debit Card ending with {#last_four_digit#} has failed as transactions are currently disabled for your card. Enable them now by changing settings on the Fi app -Fi"
  DEBIT_CARD_HOST_DOWN:
    VERSION_V1:
      Template: "Hey! Your payment did not go through due to technical errors. Please try again & contact customer care if the issue persists. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! A recent payment at {#merchant#} using your Debit Card ending {#last_four_digit#} failed due to technical issues. Please retry. Reach out to in-app chat on the Fi app if the issue persists - {#download_link#} -Fi"
  DEBIT_CARD_DOMESTIC_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Template: "Hey there! Please enable domestic transactions under card settings on the Fi app. {#download_link#} -Fi"
    VERSION_V2:
      Template: "Alert! Your recent payment using Fi-Federal Debit Card ending with {#last_four_digit#} has failed. Reason - You card is not enabled for Card Swipe/ ATM / Online transactions withint India. Enable it now- {#download_link#} -Fi"
  CALL_RECORDING_POST_RISK_USE_CASE:
    VERSION_V1:
      Template: "Please check your registered email for a <NAME_EMAIL> and reply on the same. If other issues, fill out u3.mnge.co/vRYWWWM and we will assist you -Fi"
  LAMF_LIEN_MARK_SUCCESS:
    VERSION_V1:
      Template: "{#investor_name#}, your mutual funds have been pledged successfully. Click here to continue your loan journey {#continue_link#}. -Fi"
  LAMF_LOAN_DISBURSED:
    VERSION_V1:
      Template: "{#investor_name#}, we are thrilled to inform you that your loan disbursal has been successfully processed. Click here to track the status of your loan. {#status_link#} -Fi"
  LAMF_ALL_EMI_PAID_LOAN_CLOSURE_INITIATED:
    VERSION_V1:
      Template: "{#investor_name#}, congratulations! All your EMI payments have been paid off, and the loan closure process has been initiated. -Fi"
  LAMF_REPAYMENT_AUTO_RECOVERED_BY_BAJAJ:
    VERSION_V1:
      Template: "{#investor_name#}, Rs.{#emi_amount#} has been auto-debited from your Savings Account ending {#account_last_four_digits#} and settled against your EMI. -Fi"
  LAMF_REPAYMENT_AUTO_RECOVERED_BY_FI:
    VERSION_V1:
      Template: "{#investor_name#}, Rs.{#emi_amount#} has been auto-debited from your Savings Account ending {#account_last_four_digits#} and settled against your EMI. -Fi"
  LAMF_REPAYMENT_UPCOMING_EMI:
    VERSION_V1:
      Template: "{#investor_name#}, your EMI payment of Rs.{#emi_amount#} is due on {#due_date#}. Please ensure you maintain a sufficient account balance. -Fi"
  LAMF_REPAYMENT_UPCOMING_EMI_LOW_BALANCE:
    VERSION_V1:
      Template: "{#investor_name#}, your EMI payment of Rs.{#emi_amount#} is due on {#due_date#}. Your Savings Account balance is low, add funds to avoid payment bounce charges.
-Fi"
  LAMF_REPAYMENT_EMANDATE_BOUNCE:
    VERSION_V1:
      Template: "{#investor_name#},
There's an insufficient balance in Savings Account {#account_last_four_digits#} and your EMI has bounced. Pay your overdue amount to prevent an impact on your credit score. -Fi"
  LAMF_REPAYMENT_PREPAYMENT_SUCCESS:
    VERSION_V1:
      Template: "{#investor_name#},
Your pre-payment has been successfully initiated from your loan account & will be reflected in your account statement in 4 working days. -Fi"
  CC_FI_LITE_PAN_DOB_DROP_OFF_2_HRS:
    VERSION_V1:
      Template: "Alert! Your application for AmpliFi Fi-Federal Credit card is still pending. Complete it now on the Fi app - {#Link#} -Fi"
  CC_FI_LITE_PAN_DOB_DROP_OFF_120_HRS:
    VERSION_V1:
      Template: "Your credit card application is still pending. Complete your 100% digital application now - {#Link#} - Fi"
  CC_FI_LITE_EKYC_DROP_OFF:
    VERSION_V1:
      Template: "Alert! Your application for AmpliFi Fi-Federal Credit card is still pending. Complete it now on the Fi app - {#Link#} -Fi"
  CC_VKYC_DROP_OFF:
    VERSION_V1:
      Template: "{#Name#}, Could not start KYC verification call for your Fi-Federal Cobranded Credit Card? Our agents are available from 9AM to 10PM everyday. Start your call now - {#Link#} -Fi"
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_START_APPLICATION_SMS:
    VERSION_V1:
      Template: "{#name#}, Congrats! You are eligible for the Amplifi Fi-Federal Credit Card. Continue your application on the Fi app now! {#application_download_url#} -Fi"
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_COMPLETE_APPLICATION_SMS:
    VERSION_V1:
      Template: "{#name#}, You are one step closer to getting your hands on the AmpliFi Fi-Federal Credit Card! Just install the Fi app & share your details to get the card. {#application_download_url#} -Fi"
  FI_STORE_ORDER_DELIVERY_STATUS_UPDATE_SMS:
    VERSION_V1:
      Template: "Tap on this link, {#order_tracking_link#}, to track the status of your Fi Store order (ID: {#order_id#}) -Fi"
  RISK_OUTCALL_FORM_LOGIN_OTP:
    VERSION_V1:
      Template: "Your OTP to login and view the questionnaire sent by Fi is {#otp#}. Please don't share this with anyone else. -Fi"
  SALARY_PROGRAM_B2B_USER_WHITELISTED_SMS:
    VERSION_V1:
      Template: "Account update:  Fi has partnered with your company! Unlock salary benefits like Instant Salary, ₹20L health cover & 2% cashback on spends. Download Fi app today : https://bit.ly/downloadFi"
  NON_RESIDENT_ONBOARDING_OTP:
    VERSION_V1:
      Template: "Your OTP {#var#}"
  DEBIT_CARD_INTERNATIONAL_ATM_WITHDRAWAL_LIMIT_SMS:
    VERSION_V1:
      Template: "{#name#}, the daily ATM limit of your Fi-Federal Debit Card in {#country#} is Rs.{#withdrawal_amount_limit#}. There is 0% forex markup fees on ATM withdrawals or when you swipe your card -Fi"
    #todo(naveed) add proper template here for all envs.
  STOCKGUARDIAN_LOAN_APPLICATION_ESIGN_SMS:
    VERSION_V1:
      Template: "Your OTP code for Esign is {#otp#} -Fi"
  CX_USER_CALL_DROPPED_SMS:
    VERSION_V1:
      Template: "Hi! We've enhanced our chat services for fast issue reporting & quick resolutions. To connect with us, tap on:{#var#} No app access, install it from {#var#} You can also email <NAME_EMAIL> for assistance. -Fi"
  DEBIT_CARD_CAF_NOT_FOUND:
    VERSION_V1:
      Template: "Alert! Your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as your card has not been activated yet. To pay using your card, activate it now- {#download_link#} -Fi"
  DEBIT_CARD_ATM_USAGE_NOT_ENABLED:
    VERSION_V1:
      Template: "Alert! Your recent attempt to withdraw cash from an ATM using your Fi-Federal Debit Card ending in {#last_four_digit#} on {#txn_date#} at {#txn_time#} was unsuccessful as ATM usage is currently disabled for your card. Enable it now- {#download_link#} -Fi"
  DEBIT_CARD_HOST_NOT_AVAILABLE:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_LOST_OR_STOLEN_CARD:
    VERSION_V1:
      Template: "Alert: Your Debit Card ending with {#last_four_digit#} has been marked as lost/stolen. Reach out to in-app chat on the Fi app for further assistance {#download_link#} -Fi"
  DEBIT_CARD_AMOUNT_OVER_DAILY_MAX:
    VERSION_V1:
      Template: "Alert! Your recent attempt to withdraw cash from an ATM using your Fi-Federal Debit Card ending in {#last_four_digit#} on {#txn_date#} at {#txn_time#} was unsuccessful. Reason - You've exceeded the daily ATM withdrawal limit applicable for your Fi-Federal Debit Card. Try again tomorrow or change the limit now -Fi"
  DEBIT_CARD_INELIGIBLE_ACCOUNT:
    VERSION_V1:
      Template: "Alert! Your transaction using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as your card does not support the transaction -Fi"
  DEBIT_CARD_ELA:
    VERSION_V1:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as you have exceeded daily transaction limit for this type of transaction. To complete your payment, please modify the limits now-{#download_link#} -Fi"
  DEBIT_CARD_POS_USAGE_NOT_ENABLED:
    VERSION_V1:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as POS usage is currently disabled for your card. Enable now-{#download_link#} -Fi"
  DEBIT_CARD_UNAUTHORIZED_USAGE:
    VERSION_V1:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined due to unauthorized transaction type or location -Fi"
  DEBIT_CARD_SI_HUB_DECLINE:
    VERSION_V1:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined due to system issues. Please try again later -Fi"
  DEBIT_CARD_AMOUNT_OVER_WITHDRAWAL_LIMIT_POS:
    VERSION_V1:
      Template: "Alert! Your transaction at {#merchant#} for {#txn_amount#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined as you have exceeded the max daily limit of POS(Card Swipe) transactions set on the Fi app. Change it now -{#download_link#} -Fi"
  DEBIT_CARD_CAF_STATUS_DECLINE:
    VERSION_V1:
      Template: "Sorry, it seems like you are trying to use your old Fi-Federal Debit Card for a payment. Please use your new Debit Card for payment & try again -Fi"
  DEBIT_CARD_FALLBACK_DECLINE:
    VERSION_V1:
      Template: "{#customer_name#}, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} was unsuccessful due to issues with card swipe. Please try again -Fi"
  DEBIT_CARD_MESSAGE_EDIT_ERROR:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_DEST_NOT_AVAILABLE:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} was declined due to system issues. Please try again -Fi"
  DEBIT_CARD_ATC_CHECK_FAILURE:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_TOKEN_IN_APP_FLAG_OFF:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_NO_IDF_ERROR:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_SYSTEM_ERROR:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_ARQC_FAILURE:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_APPROVED_NO_BALANCES:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_INVALID_TXN_DATE:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_TO_BE_CAPTURED_IN_CAF:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed as it was flagged as unusual transaction. Please recheck your payment details and try again -Fi"
  DEBIT_CARD_BAD_CARD_STATUS:
    VERSION_V1:
      Template: "Alert! Your payment using Fi-Federal Debit Card ending with {#last_four_digit#} has failed as transactions are currently disabled for your card. Reach out to in-app chat help on the Fi app to know more -Fi"
  DEBIT_CARD_RESERVED_B24_CODE:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_HSM_PARAM_ERROR:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_MAX_CREDIT_PER_REFUND:
    VERSION_V1:
      Template: "Sorry, your transaction at {#merchant#} using Fi-Federal Debit Card ending with {#last_four_digit#} has failed due to technical issues. Please try again -Fi"
  DEBIT_CARD_USAGE_LIMIT_EXCEEDED:
    VERSION_V1:
      Template: "Transaction Declined! Reason - You've exceeded the maximum transaction limit for this type of transaction. Please try again later -Fi"
  DEBIT_CARD_TOKEN_NFC_FLAG_OFF:
    VERSION_V1:
      Template: "Transaction Declined! Reason - You've exceeded the maximum transaction limit for this type of transaction. Please try again later -Fi"
  RISKOPS_CF_SMS:
    VERSION_V1:
      Template: "Your savings account is under {#freeze_type#} Freeze due to {#freeze_reason#}. Check your email or the Fi app for steps to remove the freeze. -Fi"
  DEBIT_CARD_FOREX_MARKUP_TXN:
    VERSION_V1:
      Template: "{#name#}, You were charged a {#amount#} forex fee on your international purchase on {#date#} at {#time#}. Since you're eligible for a 0% forex fee benefit, you can expect the forex fee benefit to be refunded within the next 15 days -Fi"
  RISK_UNIFIED_LEA_DEBIT_FREEZE:
    VERSION_V1:
      Template: "{#reminderText#} Your savings account is under Debit Freeze. Please check your registered email ID for next steps to remove the restriction. -Fi"
  RISK_UNIFIED_LEA_CREDIT_FREEZE:
    VERSION_V1:
      Template: "{#reminderText#} Your savings account is under Credit Freeze. Please check your registered email ID for next steps to remove the freeze. -Fi"
  RISK_UNIFIED_LEA_TOTAL_FREEZE:
    VERSION_V1:
      Template: "{#reminderText#} Your savings account is under Total Freeze. Please check your registered email ID for next steps to remove the freeze. -Fi"
  RISK_UNIFIED_LEA_LIEN:
    VERSION_V1:
      Template: "{#reminderText#} ₹{#amount#} was blocked in your account due to unusual activity. Please check your registered email ID for next steps to remove the freeze. -Fi"
  RISK_CREDIT_FREEZE_APPLIED_SMS:
    VERSION_V1:
      Template: "{#reminder#}Your savings account is under {#freeze_type#} Freeze due to {#freeze_reason#}. Check your email or the Fi app for steps to remove the freeze. -Fi"
  CHEQUE_CREDIT_PROCESSING_FINT:
    VERSION_V1:
      Template: "Your cheque of {#var#} for credit to your account has been received. The amount will be credited within 24 hours. In case of failure, you will be notified - Federal Bank"
  CHEQUE_CREDIT_PROCESSING_FAILED_FINT:
    VERSION_V1:
      Template: "Your cheque of {#var#} has been returned. Please contact the drawer of the cheque for details - Federal Bank."
  CREDIT_CARD_BLOCK_KYC_EXPIRY:
    VERSION_V1:
      Template: "Urgent! Payments using your Credit Card ending in {#card_last_four_digit#} are blocked due to KYC issues. Tap to update your KYC & enable payments: {#complete_kyc_link_url#} -Fi"
  CREDIT_CARD_UNBLOCK_KYC_COMPLETED:
    VERSION_V1:
      Template: "KYC complete. Your Credit Card ending in {#card_last_four_digit#} will be unblocked within 48 hours, and you can start making payments with it. -Fi"

# Adding here since this should not be environment specific
WhitelistedEmailDomains: [ "fi.money", "fi.care" ,"stockguardian.in"]

SupportPhone: "080-********"

WhatsappTemplates:
  WHATSAPP_TYPE_CARD_PRINTED:
    VERSION_V1:
      TemplateId: "card_printed_30oct"
  WHATSAPP_TYPE_PHYSICAL_CARD_ORDER_SUCCESS:
    VERSION_V1:
      TemplateId: "carddelivery_ordered_wa_v2"
  WHATSAPP_TYPE_CARD_DISPATCHED:
    VERSION_V1:
      TemplateId: "card_status_15nov"
    VERSION_V2:
      TemplateId: "carddelivery_dispatched_wa_v2"
  WHATSAPP_TYPE_CARD_OUT_FOR_DELIVERY:
    VERSION_V1:
      TemplateId: "card_out_for_delivery"
    VERSION_V2:
      TemplateId: "carddelivery_outfordelivery_wa_v2"
  WHATSAPP_TYPE_CARD_DELIVERED:
    VERSION_V1:
      TemplateId: "carddelivery_delivered_wa_v1"
    VERSION_V2:
      TemplateId: "carddelivery_tracking_order_activate_v1"
  WHATSAPP_TYPE_CARD_RETURNED_TO_ORIGIN:
    VERSION_V1:
      TemplateId: "carddelivery_returned_wa_v1"
  FIRST_DEFAULT_OPT_IN:
    VERSION_V1:
      Template: "Hi from all of us at Fi!\n\nWe’ll use Whatsapp to send you details regarding early access to the app and relevant details about your account (no good morning messages, don’t worry 😇)\n\nIf you choose to stop receiving them, reply with STOP."
      TemplateId: "epifi_text_2"
    VERSION_V2:
      TemplateId: "epifi_text_20"
    VERSION_V3:
      TemplateId: "epifi_text_31"
  OPT_OUT:
    VERSION_V1:
      Template: "We hear you 📢 (you want a break from us)\n\nOn the upside, if you want to resume receiving updates, just message START (we’ll do a little jig when that happens 💃 )"
      TemplateId: "epifi_text_3"
  OPT_BACK_IN:
    VERSION_V1:
      Template: "Glad to have you back 😊 \n\nJust a reminder - you’ll receive only important account-related updates from us (we dislike spam as much as you do)."
      TemplateId: "epifi_text_4"
  GENERIC_REPLY:
    VERSION_V1:
      Template: "Thanks for reaching out to Fi If you face any issues, reach out to our super-friendly Fi support at 080-******** or chat with us via the Fi app"
      TemplateId: "epifi_text_8"
  FI_EARLY_ACCESS_CBO:
    VERSION_V1:
      Template: "Woohoo! You’re on Fi’s Chief Broke Officer shortlist. This might help jog your memory on the role - https://fi.money/cbo\nWhat this means is you get early access to the Fi app 🙌🏼  You can download it here - https://bit.ly/3s4NHDJ\n\nWhat next? Use Fi actively. And give us feedback on how to improve the <NAME_EMAIL>.\nThis increases your chances of becoming a CBO 📈"
      TemplateId:
  FI_EARLY_ACCESS_NON_CBO:
    VERSION_V1:
      Template: "We’ve given early access to Fi only to a select few  🔢\n\nAnd you’re one of them ✋\n\nYou can download the app here - https://bit.ly/3uGsUIl\n\nIf you run into any issues or want to give us feedback, write to <NAME_EMAIL>.\n\nWelcome to Fi!"
      TemplateId:
  FI_NORMAL_ACCESS_WITHOUT_PLAYSTORE:
    VERSION_V1:
      Template: "You signed up for early access to Fi. And here it is!\nSign in at www.fi.money to get your fi.nite code and play store link to download the app.\nWhat next? Use Fi actively and send in your <NAME_EMAIL> ✍️"
      TemplateId: "epifi_text_11"
  FI_NORMAL_ACCESS_WITH_PLAYSTORE:
    VERSION_V1:
      Template: "You signed up for early access to Fi. And here it is!\nYou can download the app here - https://play.google.com/store/apps/details?id=com.epifi.paisa\nWhat next? Use Fi actively and send in your <NAME_EMAIL>  ✍️"
      TemplateId: "epifi_text_13"
  FI_CBO_ACCESS_WITHOUT_PLAYSTORE:
    VERSION_V1:
      Template: "You’d applied for the post of Chief Broke Officer (CBO). And you’re now on the official shortlist 🙌🏽\nSign in at www.fi.money to get your fi.nite code and play store link to download the app.\nWhat next? Use Fi actively 📲  If you have any questions or suggestions, write to <NAME_EMAIL>"
      TemplateId: "epifi_text_12"
  FI_CBO_ACCESS_WITH_PLAYSTORE:
    VERSION_V1:
      Template: "You’d applied for the post of Chief Broke Officer (CBO). And you’re now on the official shortlist 🙌🏽\nThis means you get early access to the Fi app. You can download it here - https://play.google.com/store/apps/details?id=com.epifi.paisa\nWhat next? Use Fi actively 📲  If you have any questions or suggestions, write to <NAME_EMAIL>"
      TemplateId: "epifi_text_14"
  VKYC_FIRST_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_first_reminder"
  VKYC_SECOND_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_second_reminder"
  VKYC_THIRD_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_third_reminder"
  FI_EARLY_ACCESS_FINITE_CODE:
    VERSION_V1:
      TemplateId: "epifi_text_34"
  FI_CBO_ACCESS_FINITE_CODE:
    VERSION_V1:
      TemplateId: "epifi_text_35"
  EARLY_ACCESS_FINITE_CODE:
    VERSION_V1:
      TemplateId: "epifi_text_39"
  EARLY_ACCESS_CBO_FINITE_CODE:
    VERSION_V1:
      TemplateId: "epifi_text_40"
  FI_BOT_WELCOME:
    VERSION_V1:
      Template: "Hi 👋🏼\n\nI’m Fi Bot and I’d like to tell you about your application status for Fi bank.\n\nTo get started, you can type:\n\n“Status” to see your waitlist status\n“Help” to see details on Fi help\n“Stop” to stop these messages\nI can only answer the above (for now)"
  FI_BOT_STATUS_CBO_ADMIT:
    VERSION_V1:
      Template: "Congratulations! You've made it to the CBO shortlist! \nThis means you get early access to the Fi app 🙌 \n\n1. Download the *' Fi Money - Digital Banking'* app from Google's Play Store: https://play.google.com/store/apps/details?id=com.epifi.paisa&referrer=utm_source%3DWA \n2. Use this *fi.nite code* {#finite_code#} to securely open your account in less than 5 minutes⚡︎ \n\nIf you feel like sharing suggestions — or need some questions answered about this banking app created in partnership with VISA & Federal Bank — write to <NAME_EMAIL> 📝 \n\nIn case you forgot, 21 CBO finalists stand to win an extra month's salary of up to ₹1 lakh! To win and become a CBO, use Fi actively; make smart financial choices, and keep sending us your feedback."
  FI_BOT_STATUS_WAITLIST_ADMIT:
    VERSION_V1:
      Template: "You've gained access to the Fi app 🙌 \n\n1. Download the *'Fi Money - Digital Banking'* app from Google's Play Store: https://play.google.com/store/apps/details?id=com.epifi.paisa&referrer=utm_source%3DWA \n2. Use this *fi.nite code* {#finite_code#} to securely open your account in less than 5 minutes⚡︎ \n\nIf you feel like sharing suggestions, feedback — or need some questions answered about this banking app: Write to <NAME_EMAIL> 📝"
  FI_BOT_STATUS_WAITLIST_UNDER_CONSIDERATION:
    VERSION_V1:
      Template: "You're currently part of our Early Access queue — but not for long!\n\nWe'll try to bump you up in our early access list & send you your exclusive Fi.nite code soon 😇 \n\nPS: We are available on Android right now. Our app's iOS version is currently in beta-testing for a closed group. It might take us a couple of months to launch on the App Store."
  FI_BOT_STATUS_CBO_UNDER_CONSIDERATION:
    VERSION_V1:
      Template: "You're part of our CBO queue! \nThe good news is you may not have to wait for long ✅\n\nWe're reviewing your application. We'll try to bump you up in our early access list & send you your exclusive Fi.nite code soon 😇 \n\nPS: We are available on Android right now. Our app's iOS version is currently in beta-testing for a closed group. It might take us a couple of months to launch on the App Store. Being an iOS user doesn't affect your chances of becoming a CBO at all."
  FI_BOT_STATUS_UNREGISTERED:
    VERSION_V1:
      Template: "Uh-oh! We've noticed that this isn't a registered number within our Early Access list. No worries, you can visit www.fi.money to sign up now ✅ \n\nGot questions? Write to <NAME_EMAIL>, and we'll get back to you as soon as we can!"
  FI_BOT_STATUS_ONBOARDING_STARTED:
    VERSION_V1:
      Template: "Thank you for choosing Fi, {#first_name#}!\nComplete your KYC and open your Federal Bank Savings Account on Fi in minutes!\nWe look forward to seeing you on the (Fi)ne side of banking. 💃🏻\nGot any questions? Write to <NAME_EMAIL>, and we’ll get back to you as soon as we can!\nIf you need more assistance, you can call us at {#phone_number#}."
  FI_BOT_STATUS_ONBOARDING_COMPLETED:
    VERSION_V1:
      Template: "We're thrilled that you've chosen to bank on Fi ✌️\n \nFi only uses Whatsapp to send you relevant personal finance updates and alerts about your account.\n \nGot any questions? Write to <NAME_EMAIL>, and we'll get back to you as soon as we can!\n \nIf you need more assistance, you can call us at {#phone_number#}."
  FI_BOT_STATUS_DIRECT_ADMIT:
    VERSION_V1:
      Template: "You've gained access to the Fi app 🙌 \n\n1. Download the *'Fi Money - Digital Banking'* app from Google's Play Store: https://play.google.com/store/apps/details?id=com.epifi.paisa&referrer=utm_source%3DWA \n2. Use this *fi.nite code* {#finite_code#} to securely open your account in less than 5 minutes⚡︎ \n\nIf you feel like sharing suggestions, feedback — or need some questions answered about this banking app: Write to <NAME_EMAIL> 📝"
  FI_BOT_GENERIC_RESPONSE_WAITLIST_USER:
    VERSION_V1:
      Template: "Hmmm 🤔 \nSorry, we didn't quite understand that {#first_name#}!\nConsider replying with one of the *bold* words below to continue:\n\n*Status* - Learn about your current Early Access status. \n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Help* - To seek assistance on anything related to Fi. \n\nP.S: We're working on improving your Whatsapp experience with Fi.\nThank you for your patience!"
  FI_BOT_GENERIC_RESPONSE_ONBOARDING_STARTED:
    VERSION_V1:
      Template: "Hmmm 🤔 \nSorry, we didn't quite understand that {#first_name#}!\nConsider replying with one of the *bold* words below to continue:\n\n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Status* - Learn about your current Early Access status.\n*Help* - To seek assistance on anything related to Fi. \n\nP.S: We're working on improving your Whatsapp experience with Fi.\nThank you for your patience!"
  FI_BOT_GENERIC_RESPONSE_ONBOARDED_USER:
    VERSION_V1:
      Template: "Hmmm 🤔 \nSorry, we didn't quite understand that {#first_name#}!\nConsider replying with one of the *bold* words below to continue:\n\n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Status* - Learn about your current Early Access status.\n*Help* - To seek assistance on anything related to Fi. \n\nP.S: We're working on improving your Whatsapp experience with Fi.\nThank you for your patience!"
  FI_BOT_GENERIC_RESPONSE_UNREGISTERED_USER:
    VERSION_V1:
      Template: "Hmmm 🤔 \nSorry, we didn't quite understand that!\nConsider replying with one of the *bold* words below to continue:\n\n*Status* - Learn about your current Early Access status. \n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Help* - To seek assistance on anything related to Fi. \n\nP.S: We're working on improving your Whatsapp experience with Fi.\nThank you for your patience!"
  FI_BOT_HELP:
    VERSION_V1:
      Template: "Have you got questions about our banking app?\nMaybe a few suggestions to help us improve your in-app experience?\nOr do you need some help? \n\nWhatever the reason, please feel free to write to <NAME_EMAIL> 📩 \nIf you'd rather speak than type, give us a call: {#phone_number#}"
  FI_BOT_HELLO_UNREGISTERED:
    VERSION_V1:
      Template: "Hello 👋 \nWelcome to Fi on WhatsApp!\n \nWe're here to provide information related to Fi that you may need.\nTo receive an update, reply with one of the *bold* words seen below.\n\n*Status* - Learn about your current Early Access status.\n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Help* - To seek assistance on anything related to Fi. \n\nIn case you don't want to receive any updates, please reply with *Stop*."
  FI_BOT_HELLO_WAITLIST:
    VERSION_V1:
      Template: "Hello {#first_name#}👋 \nWelcome to Fi on WhatsApp!\n \nWe're here to provide information related to Fi that you may need.\nTo receive an update, reply with one of the *bold* words seen below.\n\n*Status* - Learn about your current Early Access status.\n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Help* - To seek assistance on anything related to Fi. \n\nIn case you don't want to receive any updates, please reply with *Stop*."
  FI_BOT_HELLO_ONBOARDING_STARTED:
    VERSION_V1:
      Template: "Hello {#first_name#}👋 \nWelcome to Fi on WhatsApp! \n\nWe're here to provide information related to Fi that you may need. \nTo receive an update, reply with one of the *bold* words seen below. \n\n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Status* - Learn about your current Early Access status.\n*Help* - To seek assistance on anything related to Fi.\n\nIn case you don't want to receive any updates, please reply with *Stop*."
  FI_BOT_HELLO_ONBOARDING_COMPLETED:
    VERSION_V1:
      Template: "Hello {#first_name#}👋 \nWelcome to Fi on WhatsApp! \n\nWe're here to provide information related to Fi that you may need. \nTo receive an update, reply with one of the *bold* words seen below. \n\n*Features* - Understand how Fi's innovative features help you. \n*Benefits* - For a breakdown of your account-related benefits. \n*Status* - Learn about your current Early Access status.\n*Help* - To seek assistance on anything related to Fi.\n\nIn case you don't want to receive any updates, please reply with *Stop*."
  FI_BOT_BENEFITS:
    VERSION_V1:
      Template: "Fi is India’s smartest bank account, and it comes loaded with benefits.\nHere are a few important ones provided by a Federal Bank Savings Account on Fi:\n\n• No minimum balance \n• Zero account maintenance fees \n• Free VISA Platinum Debit Card \n• Zero Forex on Debit Cards \n• Upto 5.1%: Interest rate for Smart Deposits \n• We have no hidden charges 🔎\n\nCurious to learn more? \nFor details on the few things we charge for, please go here:\nhttps://fi.money/fees "
  FI_BOT_FEATURES:
    VERSION_V1:
      Template: "Fi is a banking app that comes with a zero balance savings account. It has many features that help you know your money, grow your money and organise your funds.\n\n*Ask Fi* ☝️• Make sense of your money instantly. Like, if you want to know what you've spent on Swiggy? Ask Fi, your financial assistant. \n\n*FIT rules* 📝 • Want to tie a habit to a financial goal? Or set a reminder to help you save more? FIT rules make saving fun. \n\n*Stash* 🎁 • Have a goal? Stash away for it with our Smart and Fixed deposits. Save for a gadget, a sabbatical, or for retirement. \n\n*Rewards* 🏆 • Want to save more? We're here to help you build good habits and believe that those who save money deserve rewards. \n\nFYI: There are no hidden fees or charges linked to a Fi account!\n\nWant to know more about us? Visit www.fi.money"
  VKYC_NEW_USER_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_new_user_reminder"
    VERSION_V2:
      TemplateId: "vkyc_day0script_v3"
  VKYC_12M_VALIDITY_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_12m_validity_reminder"
  INVEST_IN_MF_REMINDER:
    VERSION_V1:
      TemplateId: "mf_addfunds100plus_1b"
  OB_DOB_AND_PAN_DROP_OFF:
    VERSION_V1:
      TemplateId: "wa_onboarding_pandropoff_v8"
    VERSION_V2:
      TemplateId: "wa_onboarding_pandropoff_v13"
  WEB_ONBOARDING_COMPLETED:
    VERSION_V1:
      TemplateId: "b2bonboardingflow_v3"
  OB_VKYC_DROP_OFF_FIRST_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_duringonb_offer_v3"
  OB_VKYC_DROP_OFF_SECOND_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_duringonb_offer_v4"
  CATEGORY_SPENDS_EXCEEDED_REMINDER:
    VERSION_V1:
      TemplateId: "category_reminder_14thapr"
    VERSION_V2:
      TemplateId: "category_expenses_reminder_22ndmay"
  AMOUNT_SPENDS_EXCEEDED_REMINDER:
    VERSION_V1:
      TemplateId: "spends_reminder_14thapr"
    VERSION_V2:
      TemplateId: "spends_expenses_reminder_22ndmay"
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER:
    VERSION_V1:
      TemplateId: "cc_reminder_14thapr_new"
    VERSION_V2:
      TemplateId: "cc_reminder_22ndmay_new"
  EPAN_DOWNLOADED_FIRST_VKYC_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_epan_dropoff_day0_v1"
  EPAN_DOWNLOADED_SECOND_VKYC_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_epan_dropoff_day3_v1"
  EPAN_DOWNLOADED_THIRD_VKYC_REMINDER:
    VERSION_V1:
      TemplateId: "vkyc_epan_dropoff_day3_v2"
  JUMP_YEARLY_ACCOUNT_STATEMENT:
    VERSION_V1:
      TemplateId: "ump_annual_statement_2023_with_cta"
      TemplateCategory: "MEDIA_TEMPLATE"
  WA_SAVINGS_ACCOUNT_SUMMARY:
    VERSION_V1:
      TemplateId: "accountopened_v2"
  CC_BILL_REPAYMENT_REMINDER_ON_STATEMENT_GEN_DAY:
    VERSION_V1:
      TemplateId: "creditcard_billgen_v1"
  CC_BILL_REPAYMENT_REMINDER_PAYMENT_DUE_TOMORROW:
    VERSION_V1:
      TemplateId: "creditcard_ddminus1_v1"
  NEW_CC_BILL_REPAYMENT_REMINDER_ON_DUE_DAY:
    VERSION_V1:
      TemplateId: "creditcard_ddminus0_v1"
  CC_BILL_REPAYMENT_REMINDER_PAYMENT_DUE_YESTERDAY:
    VERSION_V1:
      TemplateId: "creditcard_ddplus1_v1"
  CC_BILL_REPAYMENT_REMINDER_AFTER_X_DUE_DAYS:
    VERSION_V1:
      TemplateId: "creditcard_ddplus2to6_v1"
  CC_BILL_REPAYMENT_REMINDER_CREDIT_SCORE_IS_FALLING:
    VERSION_V1:
      TemplateId: "creditcard_ddplus7plus_v1"
  LAMF_LOW_BALANCE_UPCOMING_EMI_REMINDER:
    VERSION_V1:
      TemplateId: "lamf_2"
  LAMF_EMANDATE_BOUNCE_ALERT:
    VERSION_V1:
      TemplateId: "lamf_3"
  LAMF_EMI_OVERDUE_REMINDER:
    VERSION_V1:
      TemplateId: "lamf_5"
  WHATSAPP_TYPE_CC_PAN_DROP_OFF:
    VERSION_V1:
      TemplateId: "lite_amplifi_pandropoff_wa_v1"
  WHATSAPP_TYPE_CC_EKYC_DROP_OFF:
    VERSION_V1:
      TemplateId: "lite_amplifi_kycdropoff_wa_v1"
  WHATSAPP_TYPE_LIVENESS_DROP_OFF:
    VERSION_V1:
      TemplateId: "lite_amplifi_livenessdropoff_wa_v1"
  WHATSAPP_TYPE_VKYC_2HR_DROP_OFF:
    VERSION_V1:
      TemplateId: "simplifi_wa_4dec_dropoff_v1"
  WHATSAPP_TYPE_CC_ELIGIBLE_WEB_FLOW_COMPLETE_APPLICATION:
    VERSION_V1:
      TemplateId: "cc_wa_web_19dec"
  WHATSAPP_TYPE_CC_ELIGIBLE_WEB_FLOW_APPLICATION_PENDING:
    VERSION_V1:
      TemplateId: "cc_wa2_web_19dec"
  WHATSAPP_TYPE_CC_FI_LITE_CARD_CREATION_SUCCESS:
    VERSION_V1:
      TemplateId: "simplifi_cardcreation"
  WHATSAPP_TYPE_RISK_OUTCALL:
    VERSION_V1:
      TemplateId: "investigations_03may"
  WHATSAPP_TYPE_DEBIT_CARD_INSUFFICIENT_FUNDS_FOR_TRANSACTION:
    VERSION_V1:
      TemplateId: "dc_txnfailure_insufficient_funds_v2"
  WHATSAPP_TYPE_DEBIT_CARD_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      TemplateId: "dc_txnfailure_int_usageoff_v4"
  WHATSAPP_TYPE_DEBIT_CARD_CONTACTLESS_CARD_USAGE_NOT_ENABLED:
    VERSION_V1:
      TemplateId: "dc_txnfailure_int_contactlessusageoff_v3"
  WHATSAPP_TYPE_DEBIT_CARD_POS_NOT_ENABLED:
    VERSION_V1:
      TemplateId: "dc_posusageoff_int_v3"
  WHATSAPP_TYPE_DEBIT_CARD_ATM_USAGE_NOT_ENABLED:
    VERSION_V1:
      TemplateId: "dc_txnfailure_atmusageoff_international_v3"
  WHATSAPP_TYPE_DEBIT_CARD_INTL_DAILY_ALLOWED_WITHDRAWAL_LIMIT_REACHED:
    VERSION_V1:
      TemplateId: "dc_txnfailure_int_withdrawalexceed_user_v2"
  WHATSAPP_TYPE_DEBIT_CARD_INTL_DAILY_MAX_WITHDRAWAL_LIMIT_REACHED:
    VERSION_V1:
      TemplateId: "dc_txnfailure_int_withdrawalexceed_country_v3"
  WHATSAPP_TYPE_RISKOPS_CF:
    VERSION_V1:
      TemplateId: "credit_freeze_tm"
  WHATSAPP_TYPE_RISK_UNIFIED_LEA_DEBIT_FREEZE:
    VERSION_V1:
      TemplateId: "debit_freeze_15nov"
  WHATSAPP_TYPE_RISK_UNIFIED_LEA_CREDIT_FREEZE:
    VERSION_V1:
      TemplateId: "credit_freeze_15nov"
  WHATSAPP_TYPE_RISK_UNIFIED_LEA_TOTAL_FREEZE:
    VERSION_V1:
      TemplateId: "total_freeze_15nov"
  WHATSAPP_TYPE_RISK_UNIFIED_LEA_LIEN:
    VERSION_V1:
      TemplateId: "lien_applied_15nov"
  WHATSAPP_TYPE_RISK_CREDIT_FREEZE:
    VERSION_V1:
      TemplateId: "webform_credit_freeze_15nov"
  WHATSAPP_TYPE_CX_TICKET_RESOLUTION_CSAT:
    VERSION_V1:
      TemplateId: "customer_feedback_new"

RateLimiterConfig:
  ResourceMap:
    sms_status_api:
      Rate: 15
      Period: 1s
    send_message_notification_campaign_name_onb_acblocked_d0_pn:
      Rate: 5
      Period: 1m
    send_message_notification_campaign_name_low_balance_add_money_alert:
      Rate: 1
      Period: 24h
  Namespace: "comms"

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/comms/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

Tracing:
  Enable: false

Notifications:
  DefaultExpiryDuration: 48h
  PriorityToSectionMapping:
    NOTIFICATION_PRIORITY_UNSPECIFIED: "NOTIFICATION_SECTION_GENERAL"
    NOTIFICATION_PRIORITY_CRITICAL: "NOTIFICATION_SECTION_IMPORTANT"
    NOTIFICATION_PRIORITY_HIGH: "NOTIFICATION_SECTION_IMPORTANT"
    NOTIFICATION_PRIORITY_MEDIUM: "NOTIFICATION_SECTION_GENERAL"
    NOTIFICATION_PRIORITY_LOW: "NOTIFICATION_SECTION_GENERAL"
  SubStatusEligibilityForShowingNotificationOnHomeMap:
    NOTIFICATION_SUB_STATUS_UNSPECIFIED: true
    NOTIFICATION_SUB_STATUS_VIEWED_ON_HOME: true
    NOTIFICATION_SUB_STATUS_CLICKED_ON_HOME: false
    NOTIFICATION_SUB_STATUS_DISMISSED_ON_HOME: false
    NOTIFICATION_SUB_STATUS_VIEWED: false
    NOTIFICATION_SUB_STATUS_CLICKED: false
    NOTIFICATION_SUB_STATUS_DISMISSED: false
  SubStatusToReadOnHomeMap:
    NOTIFICATION_SUB_STATUS_UNSPECIFIED: false
    NOTIFICATION_SUB_STATUS_VIEWED_ON_HOME: false
    NOTIFICATION_SUB_STATUS_CLICKED_ON_HOME: true
    NOTIFICATION_SUB_STATUS_DISMISSED_ON_HOME: true
    NOTIFICATION_SUB_STATUS_VIEWED: true
    NOTIFICATION_SUB_STATUS_CLICKED: true
    NOTIFICATION_SUB_STATUS_DISMISSED: true
  SectionToLabelMap:
    NOTIFICATION_SECTION_IMPORTANT: "Important"
    NOTIFICATION_SECTION_GENERAL: "General"
  IsV2Enabled: false
  PlatformToV2ControlledReleaseConfigMap:
    ANDROID:
      IsRestrictedReleaseEnabled: false
    IOS:
      IsRestrictedReleaseEnabled: false
  WhitelistedClientMapForSendingNotificationsPreDeviceRegistration:
    user: true
    kyc: true
    auth: true
    insights: true
    analyser: true

CommsNotificationCacheConfig:
  NotificationsV2MapKeyPrefix: "comms_notifications_in_app_v2"
  V2NotificationFieldPrefix: "notifications"
  V2CountFieldPrefix: "count"

CommsValidatorConfig:
  EnableAccessRevokeCheck: false
  IsRateLimitValidatorCheckEnabled: true

CommsRetryLogConfig:
  IsRetryLoggingEnabled: false

CommsEmailConfig:
  WhiteListedEmailIds: [ ]
  IsEmailIdWhiteListCheckEnabled: false
  WhiteListedEmailIdsTTL: "0s"

QuestSdk:
  Disable: true # make this false in respective env yaml files to enable quest
