Application:
  Environment: "uat"
  Name: "salaryestimation"
  ServerName: "central-growth"

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "connectedaccount"
  StatementTimeout: 5m
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "uat/rds/postgres"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Flags:
  EnableStatefulSalaryEstimation: true
