// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "trimdebugmessagefromstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.E<PERSON><PERSON>("invalid path %q for primitive field \"TrimDebugMessageFromStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrimDebugMessageFromStatus, nil
	case "enablestatefulsalaryestimation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableStatefulSalaryEstimation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableStatefulSalaryEstimation, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
