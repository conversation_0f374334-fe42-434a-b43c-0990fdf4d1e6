package salaryestimation

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/enums"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/frontend/deeplink"
	salaryestimationpb "github.com/epifi/gamma/api/salaryestimation"
	datasharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	salaryEstimationScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	salaryEstimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	salaryestimationPkg "github.com/epifi/gamma/pkg/salaryestimation"
	seEvents "github.com/epifi/gamma/salaryestimation/events"
)

func (s *Service) getNextActionForFreshVisit(ctx context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     req.GetActorId(),
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil {
		if perpetualConsentRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "perpetual consent not found", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			var nextAction *deeplink.Deeplink
			nextAction, err = s.getNextActionForDataSharingViaApp(ctx, req)
			if err != nil {
				return nil, errors.Wrap(err, "error getting next action for data sharing via app")
			}
			logger.Info(ctx, "next action for data sharing via app", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
			return nextAction, nil
		}
		return nil, errors.Wrapf(err, "error getting consent type %s", consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2)
	}
	logger.Info(ctx, "perpetual consent found", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	nextAction, err := s.getNextActionAfterDirectDataSharing(ctx, req, perpetualConsentRes.GetConsentId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating analysis with server-to-server data sharing")
	}
	logger.Info(ctx, "next action after direct data sharing", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
	return nextAction, nil
}

func (s *Service) getNextActionForDataSharingViaApp(ctx context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	latestOneTimeDataSharingConsent, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     req.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	var consentInvalid bool
	if err = epifigrpc.RPCError(latestOneTimeDataSharingConsent, err); err != nil {
		if latestOneTimeDataSharingConsent.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "latest one time data sharing consent not found", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			consentInvalid = true
		} else {
			return nil, errors.Wrap(err, "error fetching latest one time data sharing consent")
		}
	}
	if time.Now().After(latestOneTimeDataSharingConsent.GetExpiresAt().AsTime()) {
		logger.Info(ctx, "latest one time data sharing consent found but it's expired", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		consentInvalid = true
	}
	var consentCheckboxes []*widget.CheckboxItem
	if consentInvalid {
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	// TODO(Brijesh): Fix logic to record correct AA partner consent and unify logic
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())
	dataPullFailureDeeplink, err := s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &connected_account.StartConnectionFlowRequest{
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &connected_account.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      req.GetClient().String(),
					ClientReqId: req.GetClientReqId(),
					Source:      req.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	aaDataPullStatus, err := salaryestimationPkg.GetAADataPullStatus(ctx, s.connectedAccountClient, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return s.getVerifyIncomeHomeScreen(ctx, req, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId()), nil
		}
		return nil, errors.Wrap(err, "error getting accounts to be analysed")
	}
	switch {
	case aaDataPullStatus.IsDataPullInProgress():
		return s.getLatestAnalysisStatusScreenForDataPullInProgress(ctx, req)
	case aaDataPullStatus.IsDataPullFailed():
		return s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	case aaDataPullStatus.IsDataPullSuccess():
		accountBlocks, gaErr := getAccountBlocks(aaDataPullStatus.GetSuccessAccount())
		if gaErr != nil {
			return nil, errors.Wrap(gaErr, "error getting account blocks")
		}
		return s.getSalaryAccountSelectionScreen(ctx, req, accountBlocks, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId()), nil
	default:
		return s.getLatestAnalysisStatusScreenForDataPullInProgress(ctx, req)
	}
}

func getCheckboxForOneTimeDataSharingConsent() *widget.CheckboxItem {
	var oneTimeConsentPlainText = "I agree to download the above bank statement from Epifi Wealth Pvt. Ltd. and share it with Epifi Tech and its lending partners to verify my source of funds"
	return &widget.CheckboxItem{
		Id: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP.String(),
		DisplayText: common.GetPlainStringText(oneTimeConsentPlainText).WithFontColor(colors.ColorMonochromeSlate).
			WithFontStyle(common.FontStyle_BODY_4_PARA),
	}
}

func getCheckboxForEpifiWealthAndFinvuTnc() *widget.CheckboxItem {
	epifiWealthTnCUrl := "https://fi.money/wealth/TnC"
	finvuTncUrl := "https://finvu.in/terms"
	tncText := "I agree to the T&C of <a style='color: #00B899; text-decoration: none;' href=\"" + epifiWealthTnCUrl + "\">EpiFi Wealth</a> and " +
		"<a style='color: #00B899; text-decoration: none;' href=\"" + finvuTncUrl + "\">Finvu</a>."
	return &widget.CheckboxItem{
		Id:          consent.ConsentType_CONNECTED_ACCOUNTS_FI_WEALTH_AND_FINVU_TNC.String(),
		DisplayText: common.GetHtmlText(tncText).WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(common.FontStyle_BODY_XS),
	}
}

func getTncComponent(consentCheckboxes []*widget.CheckboxItem) *salaryEstimationScreenOptions.TermsAndConditionsComponent {
	if len(consentCheckboxes) == 0 {
		return nil
	}
	return &salaryEstimationScreenOptions.TermsAndConditionsComponent{
		Title: ui.NewITC().WithTexts(common.GetPlainStringText("Agree to terms & conditions").
			WithFontStyle(common.FontStyle_HEADLINE_S).WithFontColor(colors.ColorOnDarkLowEmphasis)),
		CheckboxItems: consentCheckboxes,
		BgColor:       widget.GetBlockBackgroundColour(colors.ColorOnDarkHighEmphasis),
	}
}

var moreVerificationMethodsFooter = ui.NewITC().
	WithTexts(common.GetPlainStringText("More verification methods (ITR, Salary Slip etc.) coming soon").
		WithFontStyle(common.FontStyle_BODY_XS).WithFontColor(colors.ColorMonochromeAsh))

func (s *Service) getSalaryAccountSelectionScreen(
	ctx context.Context,
	req *salaryestimationpb.ComputeSalaryRequest,
	bankAccountBlocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock,
	consentCheckboxes []*widget.CheckboxItem,
	caFlowId string,
) *deeplink.Deeplink {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(req.GetActorId(), seEvents.AccountSelectionScreenLoadedEventName))
	})
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
			BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
			VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
				VisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-gold-bg.png").
					WithProperties(&common.VisualElementProperties{Width: 125, Height: 125}),
				TitleText: common.GetPlainStringText("Confirm if all your sources of income are listed here").
					WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight),
				SubtitleText: common.GetPlainStringText("This is required to verify your salary").
					WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead),
				BackgroundColor: colors.ColorSnow,
			},
			BankAccountBlocks: bankAccountBlocks,
			PartnerLogo: ui.NewITC().
				WithLeftVisualElement(common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/powered-by-epifi-wealth.png").
					WithProperties(&common.VisualElementProperties{Width: 106, Height: 16})),
			Consents: getTncComponent(consentCheckboxes),
			PrimaryCta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Proceed",
				// No deeplink is sent as the next action is computed dynamically by calling ComputeSalary with account IDs
			},
			SecondaryCta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_SECONDARY,
				Text:         "Connect account",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&connectedaccount.BenefitsScreenOptions{
						CaFlowName: enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION.String(),
						CaFlowId:   caFlowId,
					}),
				},
			},
			FooterLabel: moreVerificationMethodsFooter,
			Client:      req.GetClient().String(),
			ClientReqId: req.GetClientReqId(),
			Source:      req.GetSource().String(),
		}),
	}
}

func (s *Service) getVerifyIncomeHomeScreen(
	ctx context.Context,
	req *salaryestimationpb.ComputeSalaryRequest,
	consentCheckboxes []*widget.CheckboxItem,
	caFlowId string,
) *deeplink.Deeplink {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(req.GetActorId(), seEvents.NewAccountConnectionScreenLoadedEventName))
	})
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.VerifyIncomeHomeScreenOptions{
			BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
			VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
				VisualElement: common.
					GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/green-chain-link.png").
					WithProperties(&common.VisualElementProperties{Width: 150, Height: 100}),
				TitleText: common.GetPlainStringText("Connect your account to verify salary").
					WithFontColor(colors.ColorOnLightHighEmphasis).WithFontStyle(common.FontStyle_HEADLINE_XL),
				SubtitleText: common.GetPlainStringText("Our partner needs your salary proof").
					WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(common.FontStyle_BODY_S),
				BackgroundColor: colors.ColorSnow,
			},
			AdditionalInfos: []*ui.IconTextComponent{
				ui.NewITC().
					WithLeftVisualElement(
						common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-bank-building.png").
							WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
					).
					WithLeftImagePadding(12).
					WithTexts(common.GetPlainStringText("Choose your salary account").
						WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor("#383838")),
				ui.NewITC().
					WithLeftVisualElement(
						common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/bank-statement-paper.png").
							WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
					).
					WithLeftImagePadding(12).
					WithTexts(common.GetPlainStringText("Fetch your account statement").
						WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor("#383838")),
			},
			Consents: getTncComponent(consentCheckboxes),
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Connect salary account",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&connectedaccount.BenefitsScreenOptions{
						CaFlowName: enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION.String(),
						CaFlowId:   caFlowId,
					}),
				},
			},
			FooterLabel: moreVerificationMethodsFooter,
			Client:      req.GetClient().String(),
			ClientReqId: req.GetClientReqId(),
			Source:      req.GetSource().String(),
		}),
	}
}

func (s *Service) getNextActionAfterDirectDataSharing(
	ctx context.Context,
	req *salaryestimationpb.ComputeSalaryRequest,
	consentId string,
) (*deeplink.Deeplink, error) {
	accounts, err := salaryestimationPkg.GetAccountsToBeAnalysedForActor(ctx, s.connectedAccountClient, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			consentCheckboxes := []*widget.CheckboxItem{getCheckboxForEpifiWealthAndFinvuTnc()}
			dataPullFailureDeeplink, dlErr := s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
			if dlErr != nil {
				return nil, errors.Wrap(dlErr, "error getting data pull failure deeplink")
			}
			caConnectionFlowRes, startErr := s.connectedAccountClient.StartConnectionFlow(ctx, &connected_account.StartConnectionFlowRequest{
				ClientReqId: req.GetClientReqId(),
				ActorId:     req.GetActorId(),
				CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
				CaFlowParams: &connected_account.CAFlowParams{
					DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
						ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
							Client:      req.GetClient().String(),
							ClientReqId: req.GetClientReqId(),
							Source:      req.GetSource().String(),
						}),
					},
					DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
				},
			})
			if err = epifigrpc.RPCError(caConnectionFlowRes, startErr); err != nil {
				return nil, errors.Wrap(err, "error starting connection flow")
			}
			return s.getVerifyIncomeHomeScreen(ctx, req, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId()), nil
		}
		return nil, errors.Wrap(err, "error getting accounts to be analysed")
	}
	accountIds := lo.Map(accounts, func(ad *beCaExtPb.AccountDetails, i int) string {
		return ad.GetAccountId()
	})
	downloadDataRes, err := s.dataSharingClient.DownloadData(ctx, &datasharing.DownloadDataRequest{
		Client:          datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientOwnership: common.Owner_OWNER_EPIFI_TECH,
		ClientReqId:     req.GetClientReqId(),
		ActorId:         req.GetActorId(),
		DataType:        datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          accountIds,
					OldestTransactionTs: timestamp.New(s.conf.OldestAATransactionUpdatedAt()),
					LatestTransactionTs: timestamp.Now(),
					ConsentId:           consentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(downloadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error downloading data")
	}
	uploadDataRes, err := s.dataSharingClient.UploadData(ctx, &datasharing.UploadDataRequest{
		DataSharingRecord: downloadDataRes.GetDataSharingRecord(),
	})
	if err = epifigrpc.RPCError(uploadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error uploading data")
	}
	return uploadDataRes.GetNextAction(), nil
}

func (s *Service) getLatestAnalysisStatusScreenForDataPullInProgress(
	ctx context.Context,
	req *salaryestimationpb.ComputeSalaryRequest,
) (*deeplink.Deeplink, error) {
	commonScreenOptions := &salaryEstimationScreenOptions.IncomeAnalysisStatusScreen{
		BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
		VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
			BackgroundColor: colors.ColorSnow,
		},
		Client:      req.GetClient().String(),
		ClientReqId: req.GetClientReqId(),
		Source:      req.GetSource().String(),
	}
	commonScreenOptions.VtsHeaderComponent.VisualElement = common.
		GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/blue-hourglass.png").
		WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
	commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Reviewing your account to verify income").
		WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight)
	commonScreenOptions.VtsHeaderComponent.SubtitleText = common.GetPlainStringText("This usually takes a few minutes").
		WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead)
	commonScreenOptions.AdditionalInformationBlock = &salaryEstimationScreenOptions.AdditionInformationBlock{
		BgColor: widget.GetBlockBackgroundColour("#FBF3E6"),
		LeftIcon: common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/shield-grey-outline.png").
			WithProperties(&common.VisualElementProperties{Width: 32, Height: 32}),
		Title: ui.NewITC().WithTexts(common.GetPlainStringText("In progress: Income review").
			WithFontStyle(common.FontStyle_SUBTITLE_2).WithFontColor(colors.ColorNight)),
		Subtitle: ui.NewITC().WithTexts(common.GetPlainStringText("Our best teams are on this. We'll notify you once its done").
			WithFontStyle(common.FontStyle_BODY_XS).WithFontColor(colors.ColorOnDarkMediumEmphasis)),
	}
	nextAction, err := s.getClientNextActionForInProgressAnalysis(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "error getting next action for in progress analysis")
	}
	commonScreenOptions.Cta = &deeplink.Cta{
		Type:     deeplink.Cta_DONE,
		Text:     "Ok, got it!",
		Deeplink: nextAction,
	}
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(commonScreenOptions),
	}, nil
}
