//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	caPb "github.com/epifi/gamma/api/connected_account"
	caAnalytics "github.com/epifi/gamma/api/connected_account/analytics"
	caDataAnalytics "github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/salaryestimation"
	genConf "github.com/epifi/gamma/salaryestimation/config/genconf"
	"github.com/epifi/gamma/salaryestimation/dao"
)

func InitialiseSalaryEstimationService(
	conf *genConf.Config,
	db cmdtypes.FeatureEngineeringPGDB,
	caAnalyticsClient caAnalytics.AnalyticsClient,
	caDataAnalyticsClient caDataAnalytics.DataAnalyticsClient,
	consentClient consent.ConsentClient,
	dataSharingClient datasharing.DataSharingClient,
	connectedAccountClient caPb.ConnectedAccountClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
	eventBroker events.Broker,
) *salaryestimation.Service {
	wire.Build(
		salaryestimation.NewService,
		dao.SalaryEstimationAttemptPgDbDaoWireSet,
	)
	return &salaryestimation.Service{}
}
