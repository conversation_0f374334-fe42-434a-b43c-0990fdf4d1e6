// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/salaryestimation"
	"github.com/epifi/gamma/salaryestimation/config/genconf"
	"github.com/epifi/gamma/salaryestimation/dao"
)

// Injectors from wire.go:

func InitialiseSalaryEstimationService(conf *genconf.Config, db types.FeatureEngineeringPGDB, caAnalyticsClient analytics.AnalyticsClient, caDataAnalyticsClient data_analytics.DataAnalyticsClient, consentClient consent.ConsentClient, dataSharingClient datasharing.DataSharingClient, connectedAccountClient connected_account.ConnectedAccountClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, eventBroker events.Broker) *salaryestimation.Service {
	salaryEstimationAttemptDao := dao.NewSalaryEstimationAttemptPgDbDao(db)
	service := salaryestimation.NewService(conf, caAnalyticsClient, caDataAnalyticsClient, consentClient, dataSharingClient, connectedAccountClient, preApprovedLoanClient, eventBroker, salaryEstimationAttemptDao)
	return service
}
