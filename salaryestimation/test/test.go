package test

import (
	"log"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/salaryestimation/config"
	"github.com/epifi/gamma/salaryestimation/config/genconf"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer() (*config.Config, *genconf.Config, func()) {
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	// Init dynamic config
	genConf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	return conf, genConf, func() {
		_ = logger.Log.Sync()
	}
}
