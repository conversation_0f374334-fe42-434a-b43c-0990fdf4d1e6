package consumer_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	authPb "github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payIncidentManagerPb "github.com/epifi/gamma/api/pay/payincidentmanager"
	mockPayIncidentManager "github.com/epifi/gamma/api/pay/payincidentmanager/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	vgHeaderPb "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	mockVgUpi "github.com/epifi/gamma/api/vendorgateway/openbanking/upi/mocks"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	daoMocks "github.com/epifi/gamma/order/dao/mocks"
	mock_payment "github.com/epifi/gamma/order/internal/payment/mocks"
	mocksUpiProcessor "github.com/epifi/gamma/order/internal/upi/mocks"
	"github.com/epifi/gamma/order/payment/consumer"
	"github.com/epifi/gamma/order/test/mocks"
	"github.com/epifi/gamma/pkg/pay"
)

type PaymentConsumerTestSuite struct {
	db             *gormv2.DB
	conf           *config.Config
	dao            dao.TransactionDao
	consumerServer paymentPb.ConsumerServer
	dbName         string
}

func NewPaymentConsumerTestSuite(db *gormv2.DB, conf *config.Config, dao dao.TransactionDao, consumerServer paymentPb.ConsumerServer, dbName string) PaymentConsumerTestSuite {
	return PaymentConsumerTestSuite{db: db, conf: conf, dao: dao, consumerServer: consumerServer, dbName: dbName}
}

type VgGetPaymentStatusRequestMatcher struct {
	want *vgPaymentPb.GetStatusRequest
}

func NewVgGetPaymentStatusRequestMatcher(want *vgPaymentPb.GetStatusRequest) VgGetPaymentStatusRequestMatcher {
	return VgGetPaymentStatusRequestMatcher{want: want}
}

func (v VgGetPaymentStatusRequestMatcher) Matches(x interface{}) bool {
	got := &vgPaymentPb.GetStatusRequest{}
	if err := copier.Copy(got, x); err != nil {
		return false
	}
	v.want.RequestId = got.RequestId
	v.want.Auth = got.Auth
	return reflect.DeepEqual(v.want, got)
}

func (v VgGetPaymentStatusRequestMatcher) String() string {
	return fmt.Sprintf("%v", v.want)
}

var (
	pts PaymentConsumerTestSuite
)

func TestService_ProcessPayment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	testActorId := "test-actor-id-1"
	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPublisher := mocks.NewMockPublisher(ctr)
	mockAuth := authMocks.NewMockAuthClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockPaymentHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockPaymentHealthProcessor.EXPECT().UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockPayIncidentManagerClient := mockPayIncidentManager.NewMockPayIncidentManagerClient(ctr)
	mockPayIncidentManagerClient.EXPECT().ProcessIncidentCreation(gomock.Any(), gomock.Any()).Return(&payIncidentManagerPb.ProcessIncidentCreationResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()

	pts.consumerServer = consumer.NewService(pts.dao, mockVgPaymentClient, mockAuth, mockPublisher, nil, brokerMock, pts.conf, mockPiClient, nil, mockUpiProcessor, nil, mockPaymentHealthProcessor, nil, nil, mockPayIncidentManagerClient, nil)
	defer func() {
		ctr.Finish()
		pts.consumerServer = nil
	}()
	t.Run("record not found", func(t *testing.T) {

		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: false,
			},
			TransactionId: "random-transaction",
			ActorId:       testActorId,
		}

		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_PERMANENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("IN_PROGRESS to SUCCESS", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		payReq := &vgPaymentPb.GetStatusRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			Auth: &vgHeaderPb.Auth{
				DeviceId: fixturesTransaction3ReqInfo.DeviceId,
			},
			Protocol:          paymentPb.PaymentProtocol_IMPS,
			OriginalRequestId: fixturesTransaction3ReqInfo.ReqId,
		}

		mockVgPaymentClient.EXPECT().
			GetStatus(context.Background(), NewVgGetPaymentStatusRequestMatcher(payReq)).
			Return(&vgPaymentPb.GetStatusResponse{
				Status: rpc.StatusOk(),
			}, nil)

		mockPublisher.EXPECT().
			Publish(context.Background(), &orderPb.ProcessOrderRequest{
				OrderId: fixturesOrder3Id,
			}).Return("random-msg-id", nil)

		mockAuth.EXPECT().GetDeviceAuth(context.Background(), &authPb.GetDeviceAuthRequest{
			ActorId: testActorId,
		}).Return(&authPb.GetDeviceAuthResponse{
			UserProfileId: "user-profile-id-1",
			DeviceToken:   "device-token-1",
			Status:        rpc.StatusOk(),
			Device:        nil,
		}, nil)

		mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
			Id: "pi-1",
		}).Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						IfscCode: "FDRL10001",
					},
				},
			},
		}, nil)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: false,
			},
			TransactionId: fixturesTransaction3Id,
			ActorId:       testActorId,
		}
		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)

		fecthedTransaction, err := pts.dao.GetById(context.Background(), fixturesTransaction3Id)
		assert.Nil(t, err)
		assert.NotNil(t, fecthedTransaction)
	})

	t.Run("IN_PROGRESS to FAILED", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		payReq := &vgPaymentPb.GetStatusRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			Auth: &vgHeaderPb.Auth{
				DeviceId: fixturesTransaction3ReqInfo.DeviceId,
			},
			Protocol:          paymentPb.PaymentProtocol_IMPS,
			OriginalRequestId: fixturesTransaction3ReqInfo.ReqId,
		}

		mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
			Id: "pi-1",
		}).Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						IfscCode: "FDRL10001",
					},
				},
			},
		}, nil)

		mockVgPaymentClient.EXPECT().
			GetStatus(context.Background(), NewVgGetPaymentStatusRequestMatcher(payReq)).
			Return(&vgPaymentPb.GetStatusResponse{
				Status: rpc.NewStatus(uint32(vgPaymentPb.GetStatusResponse_FAILED), "failed", ""),
			}, nil)

		mockPublisher.EXPECT().
			Publish(context.Background(), &orderPb.ProcessOrderRequest{
				OrderId: fixturesOrder3Id,
			}).Return("random-msg-id", nil)

		mockAuth.EXPECT().GetDeviceAuth(context.Background(), &authPb.GetDeviceAuthRequest{
			ActorId: testActorId,
		}).Return(&authPb.GetDeviceAuthResponse{
			UserProfileId: "user-profile-id-1",
			DeviceToken:   "device-token-1",
			Status:        rpc.StatusOk(),
			Device:        nil,
		}, nil)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: false,
			},
			TransactionId: fixturesTransaction3Id,
			ActorId:       testActorId,
		}
		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_PERMANENT_FAILURE, res.ResponseHeader.Status)

		fecthedTransaction, err := pts.dao.GetById(context.Background(), fixturesTransaction3Id)
		assert.Nil(t, err)
		assert.NotNil(t, fecthedTransaction)
	})

	t.Run("failure in processing IN_PROGRESS due to vg RPC error", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		payReq := &vgPaymentPb.GetStatusRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			Auth: &vgHeaderPb.Auth{
				DeviceId: fixturesTransaction3ReqInfo.DeviceId,
			},
			Protocol:          paymentPb.PaymentProtocol_IMPS,
			OriginalRequestId: fixturesTransaction3ReqInfo.ReqId,
		}

		mockVgPaymentClient.EXPECT().
			GetStatus(context.Background(), NewVgGetPaymentStatusRequestMatcher(payReq)).
			Return(nil, errors.New("vg RPC failure"))

		mockAuth.EXPECT().GetDeviceAuth(context.Background(), &authPb.GetDeviceAuthRequest{
			ActorId: testActorId,
		}).Return(&authPb.GetDeviceAuthResponse{
			UserProfileId: "user-profile-id-1",
			DeviceToken:   "device-token-1",
			Status:        rpc.StatusOk(),
			Device:        nil,
		}, nil)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: false,
			},
			TransactionId: fixturesTransaction3Id,
			ActorId:       testActorId,
		}

		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)

		fecthedTransaction, err := pts.dao.GetById(context.Background(), fixturesTransaction3Id)
		assert.Nil(t, err)
		assert.NotNil(t, fecthedTransaction)
	})

	t.Run("process already completed transaction", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: false,
			},
			TransactionId: fixturesTransaction6Id,
		}
		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_PERMANENT_FAILURE, res.ResponseHeader.Status)

		fecthedTransaction, err := pts.dao.GetById(context.Background(), fixturesTransaction6Id)
		assert.Nil(t, err)
		assert.NotNil(t, fecthedTransaction)
	})

	t.Run("publish failure", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		payReq := &vgPaymentPb.GetStatusRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			Auth: &vgHeaderPb.Auth{
				DeviceId: fixturesTransaction3ReqInfo.DeviceId,
			},
			Protocol:          paymentPb.PaymentProtocol_IMPS,
			OriginalRequestId: fixturesTransaction3ReqInfo.ReqId,
		}

		mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
			Id: "pi-1",
		}).Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						IfscCode: "FDRL10001",
					},
				},
			},
		}, nil)

		mockVgPaymentClient.EXPECT().
			GetStatus(context.Background(), NewVgGetPaymentStatusRequestMatcher(payReq)).
			Return(&vgPaymentPb.GetStatusResponse{
				Status: rpc.NewStatus(uint32(vgPaymentPb.GetStatusResponse_FAILED), "failed", ""),
			}, nil)

		mockPublisher.EXPECT().
			Publish(context.Background(), &orderPb.ProcessOrderRequest{
				OrderId: fixturesOrder3Id,
			}).Return("", errors.New("publish failure"))

		mockAuth.EXPECT().GetDeviceAuth(context.Background(), &authPb.GetDeviceAuthRequest{
			ActorId: testActorId,
		}).Return(&authPb.GetDeviceAuthResponse{
			UserProfileId: "user-profile-id-1",
			DeviceToken:   "device-token-1",
			Status:        rpc.StatusOk(),
			Device:        nil,
		}, nil)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: false,
			},
			TransactionId: fixturesTransaction3Id,
			ActorId:       testActorId,
		}
		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)

		fecthedTransaction, err := pts.dao.GetById(context.Background(), fixturesTransaction3Id)
		assert.Nil(t, err)
		assert.NotNil(t, fecthedTransaction)
	})

	t.Run("move to manual intervention on last attempt", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

		payReq := &vgPaymentPb.GetStatusRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			Auth: &vgHeaderPb.Auth{
				DeviceId: fixturesTransaction3ReqInfo.DeviceId,
			},
			Protocol:          paymentPb.PaymentProtocol_IMPS,
			OriginalRequestId: fixturesTransaction3ReqInfo.ReqId,
		}

		mockVgPaymentClient.EXPECT().
			GetStatus(context.Background(), NewVgGetPaymentStatusRequestMatcher(payReq)).
			Return(&vgPaymentPb.GetStatusResponse{
				Status: rpc.ExtendedStatusInProgress(),
			}, nil)

		mockPublisher.EXPECT().
			Publish(context.Background(), &orderPb.ProcessOrderRequest{
				OrderId: fixturesOrder3Id,
			}).Return("", errors.New("publish failure"))

		mockAuth.EXPECT().GetDeviceAuth(context.Background(), &authPb.GetDeviceAuthRequest{
			ActorId: testActorId,
		}).Return(&authPb.GetDeviceAuthResponse{
			UserProfileId: "user-profile-id-1",
			DeviceToken:   "device-token-1",
			Status:        rpc.StatusOk(),
			Device:        nil,
		}, nil)

		mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
			Id: "pi-1",
		}).Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						IfscCode: "FDRL10001",
					},
				},
			},
		}, nil)

		req := &paymentPb.ProcessPaymentRequest{
			RequestHeader: &queuePb.ConsumerRequestHeader{
				IsLastAttempt: true,
			},
			TransactionId: fixturesTransaction3Id,
			ActorId:       testActorId,
		}
		res, err := pts.consumerServer.ProcessPayment(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_PERMANENT_FAILURE, res.ResponseHeader.Status)

		fecthedTransaction, err := pts.dao.GetById(context.Background(), fixturesTransaction3Id)
		assert.Nil(t, err)
		assert.NotNil(t, fecthedTransaction)
	})
}

func TestService_ProcessPayment1(t *testing.T) {
	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockDao := daoMocks.NewMockTransactionDao(ctr)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockPublisher := mocks.NewMockPublisher(ctr)
	mockAuth := authMocks.NewMockAuthClient(ctr)
	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPiClient := mockPi.NewMockPiClient(ctr)
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockPaymentHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockPaymentHealthProcessor.EXPECT().UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockPayIncidentManagerClient := mockPayIncidentManager.NewMockPayIncidentManagerClient(ctr)

	pts.consumerServer = consumer.NewService(mockDao, mockVgPaymentClient, mockAuth, mockPublisher, mockVgUPIClient, brokerMock, pts.conf, mockPiClient, mockOrderClient, mockUpiProcessor, nil, mockPaymentHealthProcessor, nil, nil, mockPayIncidentManagerClient, nil)

	defer func() {
		ctr.Finish()
		pts.consumerServer = nil
	}()
	impsNotFoundMaxRetryDurationForFederal := pts.conf.PaymentEnquiryParams().NotFoundMaxRetryDurationVendorMap[commonvgpb.Vendor_FEDERAL_BANK.String()].IMPS
	upiNotFoundMaxRetryDurationForFederal := pts.conf.PaymentEnquiryParams().NotFoundMaxRetryDurationVendorMap[commonvgpb.Vendor_FEDERAL_BANK.String()].UPI
	txnCreationTimeForImpsNotFoundRetry := time.Now().Add(-1 * impsNotFoundMaxRetryDurationForFederal).Add(time.Minute)
	txnCreationTimeForImpsNotFoundFailure := time.Now().Add(-1 * impsNotFoundMaxRetryDurationForFederal).Add(-1 * time.Minute)
	txnCreationTimeForUPINotFoundRetry := time.Now().Add(-1 * upiNotFoundMaxRetryDurationForFederal).Add(time.Minute)
	txnCreationTimeForUPINotFoundFailure := time.Now().Add(-1 * upiNotFoundMaxRetryDurationForFederal).Add(-1 * time.Minute)

	inProgressToSuccessConfNeft := pts.conf.PaymentEnquiryParams().InProgressToSuccessMap[commonvgpb.Vendor_FEDERAL_BANK.String()].PaymentProtocolToDurationMap[paymentPb.PaymentProtocol_NEFT.String()]
	txnCreationTimeForNeftInprogress := time.Now().Add(-1 * inProgressToSuccessConfNeft).Add(-1 * time.Minute)
	txnCreationTimeForNeftInprogress1 := time.Now().Add(-1 * inProgressToSuccessConfNeft).Add(5 * time.Minute)

	type mockGetTxnWithReqInfo struct {
		txnId   string
		txn     *paymentPb.Transaction
		reqInfo *paymentPb.PaymentRequestInformation
		err     error
	}
	type mockUpdateTxn struct {
		enable  bool
		txn     *paymentPb.Transaction
		mask    []paymentPb.TransactionFieldMask
		reqInfo *paymentPb.PaymentRequestInformation
		err     error
	}
	type mockCheckUPITxnStatus struct {
		enable bool
		req    *vgUPIPb.ReqCheckTxnStatusRequest
		res    *vgUPIPb.ReqCheckTxnStatusResponse
		err    error
	}
	type mockGetFundTransferStatus struct {
		enable bool
		req    *vgPaymentPb.GetStatusRequest
		res    *vgPaymentPb.GetStatusResponse
		err    error
	}
	type mockGetDeviceAuth struct {
		enable bool
		req    *authPb.GetDeviceAuthRequest
		resp   *authPb.GetDeviceAuthResponse
		err    error
	}
	type mockGetOrderId struct {
		enable  bool
		txnId   string
		orderId string
		err     error
	}
	type mockPublish struct {
		enable bool
		req    interface{}
		msgId  string
		err    error
	}
	type mockGetPIsByIds struct {
		enable bool
		req    *piPb.GetPIsByIdsRequest
		res    *piPb.GetPIsByIdsResponse
		err    error
	}

	type mockGetPiById struct {
		enable bool
		req    *piPb.GetPiByIdRequest
		res    *piPb.GetPiByIdResponse
		err    error
	}

	tests := []struct {
		name                      string
		mockGetTxnWithReqInfo     mockGetTxnWithReqInfo
		mockUpdateTxn             mockUpdateTxn
		mockCheckUPITxnStatus     mockCheckUPITxnStatus
		mockGetOrderId            mockGetOrderId
		mockPublish               mockPublish
		mockGetPIsByIds           mockGetPIsByIds
		mockGetFundTransferStatus mockGetFundTransferStatus
		mockGetDeviceAuth         mockGetDeviceAuth
		mockGetPiById             []mockGetPiById
		req                       *paymentPb.ProcessPaymentRequest
		want                      *paymentPb.ProcessPaymentResponse
		wantErr                   bool
	}{
		{
			name: "update txn state to SUCCESS based on UPI check txn status",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},

			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},

			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.StatusOk(),
					Ref: []*vgUPIPb.ReqCheckTxnStatusResponse_Ref{
						{
							Type:     vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER,
							RespCode: "00",
						},
						{
							Type:     vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE,
							RespCode: "00",
						},
					},

					RawStatusCode:        "00",
					RawStatusDescription: "SUCCESS",
					OriginalTransactionDate: &timestamp.Timestamp{
						Seconds: 1,
						Nanos:   1,
					},
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to FAILED based on UPI check txn status",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.NewStatus(uint32(vgUPIPb.ReqCheckTxnStatusResponse_FAILED), "failed", ""),
					Ref: []*vgUPIPb.ReqCheckTxnStatusResponse_Ref{
						{
							Type:     vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER,
							RespCode: "00",
						},
						{
							Type:     vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE,
							RespCode: "00",
						},
					},
					OriginalTransactionDate: &timestamp.Timestamp{
						Seconds: 1,
						Nanos:   1,
					},
					RawStatusCode: "H02",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "transient error in case RPC error while trying status check",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},
			mockUpdateTxn: mockUpdateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					Utr:             "txn-rrn",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
								SystemErrorDescription: "RPC error",
								Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
							},
						},
					},
				},
				mask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res:    nil,
				err:    errors.New("RPC error"),
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "unknown protocol permanent failure",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "update RRN if empty",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},
			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.StatusOk(),

					Ref: []*vgUPIPb.ReqCheckTxnStatusResponse_Ref{
						{
							Type:     vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER,
							RespCode: "00",
						},
						{
							Type:     vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE,
							RespCode: "00",
						},
					},
					CustRefId: "txn-rrn",
					OriginalTransactionDate: &timestamp.Timestamp{
						Seconds: 1,
						Nanos:   1,
					},
					RawStatusCode: "00",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to UNKNOWN in case of record not found from IMPS enquiry",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				ActorId:       "actor-1",
				CustomerId:    "customer-id",
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForImpsNotFoundRetry),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
				},
				err: nil,
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req:    &authPb.GetDeviceAuthRequest{ActorId: "actor-1"},
				resp: &authPb.GetDeviceAuthResponse{
					Status:        rpc.StatusOk(),
					UserProfileId: "user-profile",
					DeviceToken:   "device-token",
					Device:        &commontypes.Device{DeviceId: "device-id"},
				},
				err: nil,
			},
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &vgPaymentPb.GetStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &vgHeaderPb.Auth{
						DeviceId:      "device-id",
						UserProfileId: "user-profile",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
					},
					Protocol:          paymentPb.PaymentProtocol_IMPS,
					OriginalRequestId: "txn-req-id-1",
				},
				res: &vgPaymentPb.GetStatusResponse{
					Status:          rpc.StatusRecordNotFound(),
					RawResponseCode: "OBE0067",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to FAILED in case of record not found from IMPS enquiry",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				ActorId:       "actor-1",
				CustomerId:    "customer-id",
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForImpsNotFoundFailure),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req:    &authPb.GetDeviceAuthRequest{ActorId: "actor-1"},
				resp: &authPb.GetDeviceAuthResponse{
					Status:        rpc.StatusOk(),
					UserProfileId: "user-profile",
					DeviceToken:   "device-token",
					Device:        &commontypes.Device{DeviceId: "device-id"},
				},
				err: nil,
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &vgPaymentPb.GetStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &vgHeaderPb.Auth{
						DeviceId:      "device-id",
						UserProfileId: "user-profile",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
					},
					Protocol:          paymentPb.PaymentProtocol_IMPS,
					OriginalRequestId: "txn-req-id-1",
				},
				res: &vgPaymentPb.GetStatusResponse{
					Status:          rpc.StatusRecordNotFound(),
					RawResponseCode: "OBE0067",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to FAILED in case of record not found from UPI check txn status",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForUPINotFoundFailure),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status:        rpc.StatusRecordNotFound(),
					RawStatusCode: "U48",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to FAILED in case of record not found from UPI check txn status",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForUPINotFoundRetry),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},
			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status:        rpc.StatusRecordNotFound(),
					RawStatusCode: "U48",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to SUCCESS in case of in-progress from NEFT enquiry after x mins",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				ActorId:       "actor-1",
				CustomerId:    "customer-id",
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForNeftInprogress),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req:    &authPb.GetDeviceAuthRequest{ActorId: "actor-1"},
				resp: &authPb.GetDeviceAuthResponse{
					Status:        rpc.StatusOk(),
					UserProfileId: "user-profile",
					DeviceToken:   "device-token",
					Device:        &commontypes.Device{DeviceId: "device-id"},
				},
				err: nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &vgPaymentPb.GetStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &vgHeaderPb.Auth{
						DeviceId:      "device-id",
						UserProfileId: "user-profile",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
					},
					Protocol:          paymentPb.PaymentProtocol_NEFT,
					OriginalRequestId: "txn-req-id-1",
				},
				res: &vgPaymentPb.GetStatusResponse{
					Status:          rpc.ExtendedStatusInProgress(),
					RawResponseCode: "F23",
					StatusCode:      "FI304",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to IN-PROGRESS in case of in-progress from NEFT enquiry before x mins",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				ActorId:       "actor-1",
				CustomerId:    "customer-id",
				TransactionId: "txn-1",
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForNeftInprogress1),
					OrderId:         "order-id-1",
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req:    &authPb.GetDeviceAuthRequest{ActorId: "actor-1"},
				resp: &authPb.GetDeviceAuthResponse{
					Status:        rpc.StatusOk(),
					UserProfileId: "user-profile",
					DeviceToken:   "device-token",
					Device:        &commontypes.Device{DeviceId: "device-id"},
				},
				err: nil,
			},
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &vgPaymentPb.GetStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &vgHeaderPb.Auth{
						DeviceId:      "device-id",
						UserProfileId: "user-profile",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
					},
					Protocol:          paymentPb.PaymentProtocol_NEFT,
					OriginalRequestId: "txn-req-id-1",
				},
				res: &vgPaymentPb.GetStatusResponse{
					Status:          rpc.ExtendedStatusInProgress(),
					RawResponseCode: "F23",
					StatusCode:      "FI304",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "update txn state to MANUAL_INTERVENTION in case of Unknown from UPI check txn status in Last Attempt",
			req: &paymentPb.ProcessPaymentRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: true},
				TransactionId: "txn-1",
			},
			mockUpdateTxn: mockUpdateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					Id:     "txn-1",
					Status: paymentPb.TransactionStatus_MANUAL_INTERVENTION,
				},
				mask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
				err:  nil,
			},
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForUPINotFoundFailure),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-to",
						"pi-from",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "test@fede",
								},
							},
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Type:                 piPb.PaymentInstrumentType_UPI,
						},
					},
				},
			},

			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockCheckUPITxnStatus: mockCheckUPITxnStatus{
				enable: true,
				req:    &vgUPIPb.ReqCheckTxnStatusRequest{},
				res: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status:        rpc.StatusUnknown(),
					RawStatusCode: "UNKNOWN",
				},
				err: nil,
			},

			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			want: &paymentPb.ProcessPaymentResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			mockDao.EXPECT().
				GetByIdWithPaymentReqInfo(context.Background(), tt.mockGetTxnWithReqInfo.txnId).
				Return(tt.mockGetTxnWithReqInfo.txn, tt.mockGetTxnWithReqInfo.reqInfo, tt.mockGetTxnWithReqInfo.err)
			if tt.mockGetPIsByIds.enable {
				mockPiClient.EXPECT().GetPIsByIds(context.Background(), tt.mockGetPIsByIds.req).
					Return(tt.mockGetPIsByIds.res, tt.mockGetPIsByIds.err)
			}
			if tt.mockUpdateTxn.enable {
				mockDao.EXPECT().
					Update(context.Background(), pay.NewTxnUpdateArgMatcher(tt.mockUpdateTxn.txn), tt.mockUpdateTxn.reqInfo, tt.mockUpdateTxn.mask).
					Return(tt.mockUpdateTxn.err)
			}
			if tt.mockCheckUPITxnStatus.enable {
				mockVgUPIClient.EXPECT().
					ReqCheckTxnStatus(gomock.Any(), gomock.AssignableToTypeOf(tt.mockCheckUPITxnStatus.req)).
					Return(tt.mockCheckUPITxnStatus.res, tt.mockCheckUPITxnStatus.err)
			}
			if tt.mockGetDeviceAuth.enable {
				mockAuth.EXPECT().
					GetDeviceAuth(context.Background(), tt.mockGetDeviceAuth.req).
					Return(tt.mockGetDeviceAuth.resp, tt.mockGetDeviceAuth.err)
			}
			if tt.mockGetFundTransferStatus.enable {
				mockVgPaymentClient.EXPECT().GetStatus(context.Background(), NewVgGetPaymentStatusRequestMatcher(tt.mockGetFundTransferStatus.req)).
					Return(tt.mockGetFundTransferStatus.res, tt.mockGetFundTransferStatus.err)
			}
			if tt.mockGetOrderId.enable {
				mockDao.EXPECT().
					GetOrderId(context.Background(), tt.mockGetOrderId.txnId).
					Return(tt.mockGetOrderId.orderId, tt.mockGetOrderId.err)
			}
			if tt.mockPublish.enable {
				mockPublisher.EXPECT().
					Publish(context.Background(), tt.mockPublish.req).
					Return(tt.mockPublish.msgId, tt.mockPublish.err)
			}

			for _, piById := range tt.mockGetPiById {
				if piById.enable {
					mockPiClient.EXPECT().GetPiById(gomock.Any(), piById.req).Return(
						piById.res, piById.err)
				}
			}
			got, err := pts.consumerServer.ProcessPayment(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessPayment() got = %v, want %v", got, tt.want)
			}

		})
	}
}

func TestService_ProcessDeemedPayments(t *testing.T) {
	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockDao := daoMocks.NewMockTransactionDao(ctr)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockPublisher := mocks.NewMockPublisher(ctr)
	mockAuth := authMocks.NewMockAuthClient(ctr)
	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPiClient := mockPi.NewMockPiClient(ctr)
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockPaymentHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockPaymentHealthProcessor.EXPECT().UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockPayIncidentManagerClient := mockPayIncidentManager.NewMockPayIncidentManagerClient(ctr)

	pts.consumerServer = consumer.NewService(mockDao, mockVgPaymentClient, mockAuth, mockPublisher, mockVgUPIClient, brokerMock, pts.conf, mockPiClient, mockOrderClient, mockUpiProcessor, nil, mockPaymentHealthProcessor, nil, nil, mockPayIncidentManagerClient, nil)
	defer func() {
		ctr.Finish()
		pts.consumerServer = nil
	}()

	txnCreationTimeForUPINotFoundFailure := time.Now().Add(-1 * pts.conf.PaymentEnquiryParams().NotFoundMaxRetryDurationVendorMap[commonvgpb.Vendor_FEDERAL_BANK.String()].UPI).Add(-1 * time.Minute)

	type mockGetTxnWithReqInfo struct {
		txnId   string
		txn     *paymentPb.Transaction
		reqInfo *paymentPb.PaymentRequestInformation
		err     error
	}
	type mockGetPiById struct {
		enable bool
		req    *piPb.GetPiByIdRequest
		res    *piPb.GetPiByIdResponse
		err    error
	}
	type mockGetOrderId struct {
		enable  bool
		txnId   string
		orderId string
		err     error
	}
	type mockPublish struct {
		enable bool
		req    interface{}
		msgId  string
		err    error
	}

	tests := []struct {
		name                  string
		mockGetTxnWithReqInfo mockGetTxnWithReqInfo
		mockGetPiById         []mockGetPiById
		mockGetOrderId        mockGetOrderId
		mockPublish           mockPublish
		req                   *paymentPb.ProcessDeemedPaymentsRequest
		want                  *paymentPb.ProcessDeemedPaymentsResponse
		wantErr               bool
	}{
		{
			name: "update txn state to SUCCESS in case of Last Attempt for UPI-DEEMED txn via ProcessDeemedPayments",
			mockGetTxnWithReqInfo: mockGetTxnWithReqInfo{
				txnId: "txn-1",
				txn: &paymentPb.Transaction{
					Id:              "txn-1",
					PiFrom:          "pi-from",
					PiTo:            "pi-to",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					ReqId:           "txn-req-id-1",
					Utr:             "txn-rrn",
					CreatedAt:       timestampPb.New(txnCreationTimeForUPINotFoundFailure),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								State: paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED,
								Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
							},
						},
					},
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "txn-req-id-1",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						MerchantRefId: "txn-ref-id",
						RefUrl:        "https://epifi.com",
					},
				},
				err: nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-from",
					},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									IfscCode: "FDRL10001",
								},
							},
						},
					},
				},
			},
			mockGetOrderId: mockGetOrderId{
				enable:  true,
				txnId:   "txn-1",
				orderId: "order-1",
				err:     nil,
			},
			mockPublish: mockPublish{
				enable: true,
				req:    &orderPb.ProcessOrderRequest{OrderId: "order-1"},
				msgId:  "",
				err:    nil,
			},
			req: &paymentPb.ProcessDeemedPaymentsRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: true},
				TransactionId: "txn-1",
			},
			want: &paymentPb.ProcessDeemedPaymentsResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDao.EXPECT().GetByIdWithPaymentReqInfo(context.Background(), tt.mockGetTxnWithReqInfo.txnId).Return(
				tt.mockGetTxnWithReqInfo.txn, tt.mockGetTxnWithReqInfo.reqInfo, tt.mockGetTxnWithReqInfo.err)
			if tt.mockGetOrderId.enable {
				mockDao.EXPECT().
					GetOrderId(context.Background(), tt.mockGetOrderId.txnId).
					Return(tt.mockGetOrderId.orderId, tt.mockGetOrderId.err)
			}
			if tt.mockPublish.enable {
				mockPublisher.EXPECT().
					Publish(context.Background(), tt.mockPublish.req).
					Return(tt.mockPublish.msgId, tt.mockPublish.err)
			}
			for _, piById := range tt.mockGetPiById {
				if piById.enable {
					mockPiClient.EXPECT().GetPiById(gomock.Any(), piById.req).Return(piById.res, piById.err)
				}
			}
			got, err := pts.consumerServer.ProcessDeemedPayments(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessDeemedPayments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.ResponseHeader, tt.want.ResponseHeader) {
				t.Errorf("ProcessDeemedPayments() got = %v, want %v", got.ResponseHeader, tt.want.ResponseHeader)
			}
		})
	}
}
