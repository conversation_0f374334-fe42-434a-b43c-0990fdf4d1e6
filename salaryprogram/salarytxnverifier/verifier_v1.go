package salarytxnverifier

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	actorPb "github.com/epifi/gamma/api/actor"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	empPb "github.com/epifi/gamma/api/employment"
	employmentPb "github.com/epifi/gamma/api/employment"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	namecheckVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	employernamematchVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/salaryprogram/config"
	"github.com/epifi/gamma/salaryprogram/dao"
	"github.com/epifi/gamma/salaryprogram/employerverifier"
	"github.com/epifi/gamma/salaryprogram/minreqsalaryamount"
)

type SalaryTxnVerifierV1 struct {
	txnCatClient                  categorizerPb.TxnCategorizerClient
	actorClient                   actorPb.ActorClient
	piClient                      piPb.PiClient
	empClient                     empPb.EmploymentClient
	minReqSalaryAmountGetter      minreqsalaryamount.IMinReqSalaryAmountGetter
	employerNameMatchVgClient     employernamematchVgPb.EmployerNameMatchClient
	vgPaymentClient               vgPaymentPb.PaymentClient
	employerVerifier              employerverifier.ISalaryEmployerVerifier
	vgEmploymentClient            vgEmploymentPb.EmploymentClient
	usersClient                   usersPb.UsersClient
	conf                          *config.Config
	employerNameCategoriserClient employernamecategoriser.EmployerNameCategoriserClient
	salaryTxnVerReqDao            dao.ISalaryTxnVerificationRequestDao
}

func NewSalaryTxnVerifierV1(
	txnCatClient categorizerPb.TxnCategorizerClient, actorClient actorPb.ActorClient, piClient piPb.PiClient, empClient empPb.EmploymentClient,
	minReqSalaryAmountGetter minreqsalaryamount.IMinReqSalaryAmountGetter, employerNameMatchVgClient employernamematchVgPb.EmployerNameMatchClient,
	txnRemitterNameVgClient vgPaymentPb.PaymentClient, employerVerifier employerverifier.ISalaryEmployerVerifier, vgEmploymentClient vgEmploymentPb.EmploymentClient,
	usersClient usersPb.UsersClient, cfg *config.Config, employerNameCatergoriserClient employernamecategoriser.EmployerNameCategoriserClient,
	salaryTxnVerReqDao dao.ISalaryTxnVerificationRequestDao) *SalaryTxnVerifierV1 {
	return &SalaryTxnVerifierV1{
		txnCatClient:                  txnCatClient,
		actorClient:                   actorClient,
		piClient:                      piClient,
		empClient:                     empClient,
		minReqSalaryAmountGetter:      minReqSalaryAmountGetter,
		employerNameMatchVgClient:     employerNameMatchVgClient,
		vgPaymentClient:               txnRemitterNameVgClient,
		employerVerifier:              employerVerifier,
		vgEmploymentClient:            vgEmploymentClient,
		usersClient:                   usersClient,
		conf:                          cfg,
		employerNameCategoriserClient: employerNameCatergoriserClient,
		salaryTxnVerReqDao:            salaryTxnVerReqDao,
	}
}

type txnRemitterName struct {
	field beSalaryPb.SalaryAutoVerifierMeta_FieldUsedForTxnRemitterName
	name  string
}

// nolint:funlen
func (s *SalaryTxnVerifierV1) IsSalaryTxn(
	ctx context.Context,
	salaryTxnVerifierParams SalaryTxnVerifierParams) (bool, *VerificationMetadata, error) {

	order := salaryTxnVerifierParams.OrderWithTxns.GetOrder()
	txns := salaryTxnVerifierParams.OrderWithTxns.GetTransactions()
	beneficiaryActorId := order.GetToActorId()

	if _, ok := s.conf.BlacklistedUsersFromSalaryProgram[beneficiaryActorId]; ok {
		return false, &VerificationMetadata{
			VerificationFailureReason: UserBlacklistedFromSalaryProgram,
		}, nil
	}

	currentEmployerInfo, err := s.getCurrentEmployerOfTheUser(ctx, beneficiaryActorId)
	switch {
	// if no current employer exists for then salary verification can't be performed so returning false.
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no current employer exists for the user", zap.String(logger.ACTOR_ID_V2, beneficiaryActorId))
		return false, &VerificationMetadata{
			VerificationFailureReason: NoEmployerExistForUser,
		}, nil
	case err != nil:
		return false, nil, fmt.Errorf("error fetching current employer of the user, err : %w", err)
	}
	currentEmployerId := currentEmployerInfo.GetEmployerId()

	// only a single txn will exist in salary orders.
	if len(txns) != 1 {
		return false, &VerificationMetadata{
			VerificationFailureReason: NoOfTxnIsNotOneInOrder,
			SalaryTxnEmployerId:       currentEmployerId,
		}, nil
	}
	txn := txns[0]

	validationsToRun := salaryTxnVerifierParams.ValidationsToRun
	// we will run all validations if the list is empty
	runAllValidations := true
	if len(validationsToRun) != 0 {
		runAllValidations = false
	}

	// check if given order workflow is possible for a salary txn
	if !s.isGivenWorkflowPossibleForSalaryTxn(runAllValidations, validationsToRun, order.GetWorkflow()) {
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_WORKFLOW
		return false, getVerificationMetadata(OrderWorkFlowNotAllowed, currentEmployerId, validationsToRun, validationRan), nil
	}

	// check if given order provenance is possible for a salary txn
	if !s.isGivenProvenancePossibleForSalaryTxn(runAllValidations, validationsToRun, order.GetProvenance()) {
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PROVENANCE
		return false, getVerificationMetadata(OrderProvenanceNotAllowed, currentEmployerId, validationsToRun, validationRan), nil
	}

	// deposit txn cannot be salary txn
	if s.isDepositTxn(runAllValidations, validationsToRun, order.GetTags()) {
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_NOT_DEPOSIT_TXN
		return false, getVerificationMetadata(TxnIsDepositTxn, currentEmployerId, validationsToRun, validationRan), nil
	}

	// check if the txn amount is greater than the min required salary txn amount value.
	isAmtGreaterOrEqual, err := s.isAmountGreaterThanOrEqualToMinRequiredSalaryTxnAmount(ctx, beneficiaryActorId, order.GetAmount(),
		runAllValidations, salaryTxnVerifierParams)
	if err != nil {
		return false, nil, fmt.Errorf("error checking if txn amount is greater than min required salary txn amount, err : %w", err)
	}
	if !isAmtGreaterOrEqual {
		logger.Info(ctx, "txn amount is less than the min req salary amount, hence not a salary txn", zap.String(logger.ORDER_ID, order.GetId()), zap.String("piFrom", txn.GetPiFrom()))
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MIN_AMOUNT
		return false, getVerificationMetadata(TxnAmountIsLessThanMinReqSalaryAmount, currentEmployerId, validationsToRun, validationRan), nil
	}

	// only orders in terminal success state can be verified for salary txn
	// for salary orders terminal success state is PAID
	isOrderStatusPaid := isOrderStatusPaid(order, runAllValidations, validationsToRun)
	if !isOrderStatusPaid {
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TERMINAL_STATE
		return false, getVerificationMetadata(OrderStatusNotPaid, currentEmployerId, validationsToRun, validationRan), nil
	}

	// check if given payment protocol possible for a salary txn
	if !s.isGivenPaymentProtocolPossibleForSalaryTxn(runAllValidations, validationsToRun, txn.GetPaymentProtocol()) {
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PAYMENT_PROTOCOL
		return false, getVerificationMetadata(PaymentProtocolNotAllowed, currentEmployerId, validationsToRun, validationRan), nil
	}

	// if the txn is refund related, then it can't be salary.
	isNonIncomeTxn, err := s.isGivenTxnNotCategorizedAsIncome(ctx, beneficiaryActorId, txn, runAllValidations, validationsToRun)
	if err != nil {
		return false, nil, fmt.Errorf("error checking if the txn was categorized as refund, err : %w", err)
	}
	if isNonIncomeTxn {
		logger.Info(ctx, "txn is categorized as refund, hence not a salary txn", zap.String(logger.ORDER_ID, order.GetId()))
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TXN_CATEGORY_NON_INCOME
		return false, getVerificationMetadata(TxnIsCategorisedAsNonIncome, currentEmployerId, validationsToRun, validationRan), nil
	}

	// validate if beneficiary actor of the txn a Fi User
	isFiUser, fiUserCheckErr := s.isFiUser(ctx, beneficiaryActorId, runAllValidations, validationsToRun)
	if fiUserCheckErr != nil {
		return false, nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId rpc call failed")
	}
	if !isFiUser {
		logger.Info(ctx, "not a fi user", zap.String(logger.ORDER_ID, order.GetId()))
		validationRan := beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_FI_USER
		return false, getVerificationMetadata(NotFiUser, currentEmployerId, validationsToRun, validationRan),
			fmt.Errorf("beneficiary actor is not a fi user")
	}

	// if the B2B employer's pi id is in the set of verified pi ids, it is considered as a salary transaction
	if currentEmployerInfo.GetSalaryProgramChannel() == employmentPb.EmployerSalaryProgramChannel_B2B && lo.Contains(s.conf.BypassSalaryVerificationChecksForPIs, txn.GetPiFrom()) {
		logger.Info(ctx, "PI ID is in PI list for which salary verification is bypassed. Hence a salary transaction", zap.String(logger.PI_FROM, txn.GetPiFrom()), zap.String(logger.ORDER_ID, order.GetId()))
		return true, &VerificationMetadata{
			SalaryTxnEmployerId: currentEmployerInfo.GetEmployerId(),
			AdditionalMeta: &beSalaryPb.SalaryAutoVerifierMeta{
				VerificationSuccessMeta: &beSalaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
					EmployerMatchedWithTxnRemitter: beSalaryPb.SalaryAutoVerifierMeta_BYPASS_PAYMENT_INSTRUMENT_ID_MATCH,
					RemitterToEmployerMatchLogic:   beSalaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_ID_MATCH,
				},
			},
		}, nil
	}

	// checks if the txn remitter matches user's current employer or any employer in employer DB
	remitterMatchedWithEmployer, verificationMetadataForRemitterMatching, err := s.checkIfTxnRemitterMatchesAnyEmployer(ctx, txn, currentEmployerInfo,
		beneficiaryActorId, order.GetFromActorId(), runAllValidations, validationsToRun)
	if err != nil {
		return false, nil, err
	}
	if !remitterMatchedWithEmployer {
		logger.Info(ctx, "remitter did not matched with user's current employer or any employer in employer DB, hence not a salary txn", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.ACTOR_ID_V2, beneficiaryActorId))
		return false, verificationMetadataForRemitterMatching, nil
	}
	return true, verificationMetadataForRemitterMatching, nil
}

func getVerificationMetadata(verificationFailureReason VerificationFailureReason, salaryTxnEmployerId string, validationsToRun []beSalaryPb.SalaryTxnValidation, validationRan beSalaryPb.SalaryTxnValidation) *VerificationMetadata {
	verificationMetadata := &VerificationMetadata{
		VerificationFailureReason: verificationFailureReason,
		SalaryTxnEmployerId:       salaryTxnEmployerId,
	}
	if len(validationsToRun) == 0 || lo.Contains(validationsToRun, validationRan) {
		verificationMetadata.FailedSalaryTxnValidation = validationRan
	}
	return verificationMetadata
}

// checkIfTxnRemitterMatchesAnyEmployer verifies if the txn remitter matches user's current employer or matches with any employer in the db.
// following checks runs in the sequence
//  1. txn remitter pi match with the pi associated with user's employer, return true
//  2. txn remitter names match with user's employer name:
//     if employer blacklisted - do uan lookup check
//     else return true
//  3. txn remitter names match with any employer in db:
//     if employer blacklisted - do uan lookup check
//     else return true
//
// returns false if the match is unsuccessful.
// nolint:funlen

func isOrderStatusPaid(order *orderPb.Order, runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation) bool {
	if runAllValidations || lo.Contains(validationTypes,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TERMINAL_STATE) {
		return order.GetStatus() == orderPb.OrderStatus_PAID
	}
	return true
}

func (s *SalaryTxnVerifierV1) isFiUser(ctx context.Context, beneficiaryActorId string, runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation) (bool, error) {
	if runAllValidations || lo.Contains(validationTypes,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_FI_USER) {
		entityDetailsRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: beneficiaryActorId})
		if err != nil || !entityDetailsRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "actorClient.GetEntityDetailsByActorId rpc call failed", zap.String(logger.ACTOR_ID_V2, beneficiaryActorId), zap.Any(logger.RPC_STATUS, entityDetailsRes.GetStatus()), zap.Error(err))
			return false, fmt.Errorf("actorClient.GetEntityDetailsByActorId rpc call failed")
		}
		return entityDetailsRes.GetType() == types.ActorType_USER, nil
	}
	return true, nil
}

// nolint: funlen
func (s *SalaryTxnVerifierV1) checkIfTxnRemitterMatchesAnyEmployer(ctx context.Context, txn *paymentPb.Transaction, userDeclaredEmpInfo *empPb.EmployerInfo,
	beneficiaryActorId, fromActorId string, runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation) (bool, *VerificationMetadata, error) {
	if !runAllValidations && !(lo.Contains(validationTypes, beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_USER_DECLARED_EMPLOYER) ||
		lo.Contains(validationTypes, beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_ANY_EMPLOYER_IN_DB)) {
		return true, nil, nil
	}
	txnRemitterPi := txn.GetPiFrom()
	var filteredTxnRemitterNames []*txnRemitterName
	var filteredTxnRemitterNamesErr error
	var isUserNewToSalaryProgramErr error
	isUserNewToSalaryProgram := false

	if runAllValidations ||
		lo.Contains(validationTypes, beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_USER_DECLARED_EMPLOYER) {
		// todo: do blacklist check before checking employer PI mapping check
		empPiMappingRes, err := s.empClient.GetEmployerPiMappingByPiId(ctx, &empPb.GetEmployerPiMappingByPiIdRequest{
			PiId: txnRemitterPi,
		})
		if err != nil || !empPiMappingRes.GetStatus().IsSuccess() && !empPiMappingRes.GetStatus().IsRecordNotFound() {
			return false, nil, fmt.Errorf("empClient.GetEmployerPiMappingByPiId rpc call failed, err : %w", err)
		}
		// if employer pi mapping is present for txn remitter pi then return true if employer id associated to txn remitter pi matches with user's employer id
		if !empPiMappingRes.GetStatus().IsRecordNotFound() && userDeclaredEmpInfo.GetEmployerId() == empPiMappingRes.GetEmployerPiMapping().GetEmployerId() {
			return true, &VerificationMetadata{
				SalaryTxnEmployerId: userDeclaredEmpInfo.GetEmployerId(),
				AdditionalMeta: &beSalaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &beSalaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						EmployerMatchedWithTxnRemitter: beSalaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   beSalaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_ID_MATCH,
					},
				},
			}, nil
		}
	}

	// getTxnRemitterNames fetches remitter names from different fields in the system for the txn
	txnRemitterNames, err := s.getTxnRemitterNames(ctx, txn, fromActorId)
	if err != nil {
		return false, nil, err
	}

	filteredTxnRemitterNames, filteredTxnRemitterNamesErr = s.getFilteredRemitterNames(ctx, txnRemitterNames)
	if filteredTxnRemitterNamesErr != nil {
		return false, nil, filteredTxnRemitterNamesErr
	}

	if len(filteredTxnRemitterNames) == 0 {
		return false, &VerificationMetadata{
			VerificationFailureReason: NoTxnRemitterNamesLeftAfterFiltering,
		}, nil
	}

	// Skipping new user check for B2B channel employers as these are our verified list of employers, and
	// we wouldn't want the employees (of these employers) to get stuck with ops.
	// We'd want the activation to happen automatically right from the first transaction itself.
	if userDeclaredEmpInfo.GetSalaryProgramChannel() != employmentPb.EmployerSalaryProgramChannel_B2B {
		isUserNewToSalaryProgram, isUserNewToSalaryProgramErr = s.isUserNewToSalaryProgram(ctx, beneficiaryActorId)
		if isUserNewToSalaryProgramErr != nil {
			return false, nil, fmt.Errorf("error checking whether user is new to salary, err: %w", isUserNewToSalaryProgramErr)
		}
	}
	var (
		dsNameMatchRpcReqResList []*DsNameMatchRpcReqRes
	)

	/* --------------------- CHECK IF THE REMITTER NAMES MATCHES WITH THE USER DECLARE EMPLOYER ----------------------------------- */

	for _, remitterName := range filteredTxnRemitterNames {
		txnRemitterNameMatchesEmployerName, dsNameMatchRpcReqResponses, checkErr := s.checkIfTxnRemitterNameMatchesEmployerName(ctx, remitterName, userDeclaredEmpInfo.GetPossibleRemitterNames())
		if checkErr != nil {
			return false, nil, fmt.Errorf("error while checking if txn remitter name matches the user's employer name, err: %w", checkErr)
		}
		dsNameMatchRpcReqResList = append(dsNameMatchRpcReqResList, dsNameMatchRpcReqResponses...)

		if txnRemitterNameMatchesEmployerName {
			if userDeclaredEmpInfo.GetSalaryProgramEligibilityStatus() == employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE {
				if isUserNewToSalaryProgram && remitterName.field == beSalaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME {
					return false, &VerificationMetadata{
						RaiseSalaryTxnVerificationRequestToOps: true,
						VerificationFailureReason:              UserNewToSalaryAndRemitterPiVerifiedNameMatchedWithUserEmployerRaisingToOps,
						MarshalledDsNameMatchRpcReqResList:     getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
						FailedSalaryTxnValidation:              beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_USER_DECLARED_EMPLOYER,
					}, nil
				} else {
					return true, &VerificationMetadata{
						SalaryTxnEmployerId:                userDeclaredEmpInfo.GetEmployerId(),
						MarshalledDsNameMatchRpcReqResList: getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
						AdditionalMeta: &beSalaryPb.SalaryAutoVerifierMeta{
							VerificationSuccessMeta: &beSalaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
								FieldUsedForTxnRemitterName:    remitterName.field,
								EmployerMatchedWithTxnRemitter: beSalaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
								RemitterToEmployerMatchLogic:   beSalaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
							},
						},
					}, nil
				}
			}
			isUanCheckPassed, uanCheckFailureReason, uanCheckErr := s.performEmployerUANCheck(ctx, beneficiaryActorId, userDeclaredEmpInfo.GetNameBySource())
			if uanCheckErr != nil {
				return false, nil, fmt.Errorf("error performing employer UAN check, err: %w", uanCheckErr)
			}
			if isUanCheckPassed {
				return true, &VerificationMetadata{
					SalaryTxnEmployerId:                userDeclaredEmpInfo.GetEmployerId(),
					MarshalledDsNameMatchRpcReqResList: getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
					AdditionalMeta: &beSalaryPb.SalaryAutoVerifierMeta{
						VerificationSuccessMeta: &beSalaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
							FieldUsedForTxnRemitterName:    remitterName.field,
							EmployerMatchedWithTxnRemitter: beSalaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
							RemitterToEmployerMatchLogic:   beSalaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
						},
					},
				}, nil
			}
			return false, &VerificationMetadata{
				VerificationFailureReason:          uanCheckFailureReason,
				MarshalledDsNameMatchRpcReqResList: getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
				FailedSalaryTxnValidation:          beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_USER_DECLARED_EMPLOYER,
			}, nil
		}
	}

	/* --------------------- CHECK IF THE REMITTER NAMES MATCHES WITH ANY EMPLOYER IN THE DB ----------------------------------- */
	for _, remitterName := range filteredTxnRemitterNames {
		if isUserNewToSalaryProgram && remitterName.field != beSalaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME {
			continue
		}
		txnRemitterMatchesEmployerInDb, matchedEmpInfo, dsNameMatchRpcReqResponses, checkErr := s.checkIfTxnRemitterMatchesEmployerInDB(ctx, remitterName)
		if checkErr != nil {
			return false, nil, fmt.Errorf("error while checking if txn remitter matches employer in Db, err: %w", checkErr)
		}
		dsNameMatchRpcReqResList = append(dsNameMatchRpcReqResList, dsNameMatchRpcReqResponses...)

		if txnRemitterMatchesEmployerInDb {
			logger.Info(ctx, "remitter matched with employer in db")
			if matchedEmpInfo.GetSalaryProgramEligibilityStatus() == employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE {
				if isUserNewToSalaryProgram {
					return false, &VerificationMetadata{
						RaiseSalaryTxnVerificationRequestToOps: true,
						RemitterMatchesWithOtherEmployerInDb:   true,
						MarshalledDsNameMatchRpcReqResList:     getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
						VerificationFailureReason:              UserNewToSalaryAndRemitterPiVerifiedNameMatchedWithAnyEmployerInDbRaisingToOps,
						FailedSalaryTxnValidation:              beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_ANY_EMPLOYER_IN_DB,
					}, nil
				} else {
					return true, &VerificationMetadata{
						SalaryTxnEmployerId:                  matchedEmpInfo.GetEmployerId(),
						RemitterMatchesWithOtherEmployerInDb: true,
						MarshalledDsNameMatchRpcReqResList:   getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
						AdditionalMeta: &beSalaryPb.SalaryAutoVerifierMeta{
							VerificationSuccessMeta: &beSalaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
								FieldUsedForTxnRemitterName:    remitterName.field,
								EmployerMatchedWithTxnRemitter: beSalaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
								RemitterToEmployerMatchLogic:   beSalaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
							},
						},
					}, nil
				}
			}
			isUanCheckPassed, uanCheckFailureReason, uanCheckErr := s.performEmployerUANCheck(ctx, beneficiaryActorId, matchedEmpInfo.GetNameBySource())
			if uanCheckErr != nil {
				return false, nil, fmt.Errorf("error performing employer UAN check, err: %w", uanCheckErr)
			}
			if isUanCheckPassed {
				return true, &VerificationMetadata{
					SalaryTxnEmployerId:                  matchedEmpInfo.GetEmployerId(),
					MarshalledDsNameMatchRpcReqResList:   getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
					RemitterMatchesWithOtherEmployerInDb: true,
					AdditionalMeta: &beSalaryPb.SalaryAutoVerifierMeta{
						VerificationSuccessMeta: &beSalaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
							FieldUsedForTxnRemitterName:    remitterName.field,
							EmployerMatchedWithTxnRemitter: beSalaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
							RemitterToEmployerMatchLogic:   beSalaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
						},
					},
				}, nil
			}
			return false, &VerificationMetadata{
				VerificationFailureReason:          uanCheckFailureReason,
				MarshalledDsNameMatchRpcReqResList: getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
				FailedSalaryTxnValidation:          beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_ANY_EMPLOYER_IN_DB,
			}, nil
		}
	}
	return false, &VerificationMetadata{
		VerificationFailureReason:          RemitterNotMatchedCurrentEmployerOrAnyEmployerInDb,
		MarshalledDsNameMatchRpcReqResList: getMarshalledDsNameMatchRpcReqResList(ctx, beneficiaryActorId, dsNameMatchRpcReqResList),
	}, nil
}

func (s *SalaryTxnVerifierV1) getFilteredRemitterNames(ctx context.Context, txnRemitterNames []*txnRemitterName) ([]*txnRemitterName, error) {
	var filteredTxnRemitterNames []*txnRemitterName
	for _, remitterName := range txnRemitterNames {
		if !s.isValidRemitterName(remitterName.name) {
			continue
		}
		isMerchant, err := s.isRemitterNameCategorisedAsMerchant(ctx, remitterName.name)
		if err != nil {
			return nil, fmt.Errorf("error while checking if txn remitter name is categorised as merchant, err %w", err)
		}
		if !isMerchant {
			continue
		}
		filteredTxnRemitterNames = append(filteredTxnRemitterNames, remitterName)
	}
	return filteredTxnRemitterNames, nil
}

func (s *SalaryTxnVerifierV1) isRemitterNameCategorisedAsMerchant(ctx context.Context, remitterName string) (bool, error) {
	const minNameLenAllowedToCategorise = 3
	if len(remitterName) < minNameLenAllowedToCategorise {
		return false, nil
	}

	employerNameSearchRequest := &employernamecategoriser.EmployerNameCategoriserRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
		Name:      remitterName,
		RequestId: uuid.NewString(),
	}

	employerNameCategoriserResponse, err := s.employerNameCategoriserClient.EmployerNameCategoriser(ctx, employerNameSearchRequest)
	if rpcErr := epifigrpc.RPCError(employerNameCategoriserResponse, err); rpcErr != nil {
		return false, fmt.Errorf("employerNameCategoriserClient.EmployerNameCategoriser rpc call failed, err %w", rpcErr)
	}

	if employerNameCategoriserResponse.GetDecision() == 0 {
		return false, nil
	}
	return true, nil
}

func (s *SalaryTxnVerifierV1) checkIfTxnRemitterNameMatchesEmployerName(ctx context.Context, txnRemitterName *txnRemitterName, possibleRemitterNamesForEmployer []string) (bool, []*DsNameMatchRpcReqRes, error) {
	// check if txnRemitterName matches with any of the possible remitter names of employer
	var (
		nameMatched              = atomic.NewBool(false)
		mutex                    sync.Mutex
		dsNameMatchRpcReqResList []*DsNameMatchRpcReqRes
	)
	nameMatchErrGrp, gctx := errgroup.WithContext(ctx)
	for _, empRemitterName := range possibleRemitterNamesForEmployer {
		// adding this check to prevent failures in name-match rpc due empty remitter names returned from employer rpc
		// todo (utkarsh) : check with onboarding team to fix the employer data to sanitize the employer data at their end
		if empRemitterName == "" {
			logger.WarnWithCtx(ctx, "ignoring empty remitter name")
			continue
		}
		// local variable for use in goroutine
		empRemName := empRemitterName
		nameMatchErrGrp.Go(func() error {
			req := &employernamematchVgPb.EmployerNameMatchRequest{
				Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
				Name_1: txnRemitterName.name,
				Name_2: empRemName,
			}
			nameCheckRes, rpcErr := s.employerNameMatchVgClient.EmployerNameMatch(gctx, req)
			if rpcErr != nil || !nameCheckRes.GetStatus().IsSuccess() {
				logger.Error(gctx, "nameCheckVgClient.EmployerNameMatch rpc call failed", zap.Any(logger.RPC_STATUS, nameCheckRes.GetStatus()), zap.Error(rpcErr))
				return fmt.Errorf("nameCheckVgClient.EmployerNameMatch rpc call failed")
			}

			if txnRemitterName.field == beSalaryPb.SalaryAutoVerifierMeta_NAME_FROM_FEDERAL_REMITTER_API {
				logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "successfully called DS name match rpc in salary auto verifier", zap.Any(logger.REQUEST, req), zap.Any(logger.RESPONSE, nameCheckRes))
			} else {
				mutex.Lock()
				dsNameMatchRpcReqResList = append(dsNameMatchRpcReqResList, &DsNameMatchRpcReqRes{
					Req: req,
					Res: nameCheckRes,
				})
				mutex.Unlock()
			}

			if nameCheckRes.GetDecision() == namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS {
				nameMatched.Store(true)
			}
			return nil
		})
	}
	if err := nameMatchErrGrp.Wait(); err != nil {
		return false, nil, fmt.Errorf("error while doing a namematch, err : %w", err)
	}
	return nameMatched.Load(), dsNameMatchRpcReqResList, nil
}

func (s *SalaryTxnVerifierV1) isDepositTxn(runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation, orderTags []orderPb.OrderTag) bool {
	if runAllValidations ||
		lo.Contains(validationTypes, beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_NOT_DEPOSIT_TXN) {
		return lo.Contains(orderTags, orderPb.OrderTag_DEPOSIT)
	}
	return false
}

func (s *SalaryTxnVerifierV1) isValidRemitterName(remitterName string) bool {
	// return false if:
	// remitter name is length is less than 4
	// remitter name is coming as others
	if len(remitterName) < 4 || strings.EqualFold(remitterName, "others") {
		return false
	}
	return true
}

func (s *SalaryTxnVerifierV1) checkIfTxnRemitterMatchesEmployerInDB(ctx context.Context, txnRemitterName *txnRemitterName) (bool, *employmentPb.EmployerInfo, []*DsNameMatchRpcReqRes, error) {

	// replacing all "/" in remitter name as the search rpc does not allow this character which might be present in remitter name
	txnRemitterName.name = strings.ReplaceAll(txnRemitterName.name, "/", "")

	var dsNameMatchRpcReqResList []*DsNameMatchRpcReqRes

	searchResultRes, err := s.empClient.SearchCompanyV2(ctx, &empPb.SearchCompanyRequestV2{
		SearchString: txnRemitterName.name,
		Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
	})
	if rpcErr := epifigrpc.RPCError(searchResultRes, err); rpcErr != nil {
		logger.Error(ctx, "error in SearchCompanyV2 rpc call", zap.Error(rpcErr))
		return false, nil, nil, fmt.Errorf("error in SearchCompanyV2 rpc call, err: %w", rpcErr)
	}
	matchedEmployers := searchResultRes.GetCompanies()[:integer.Min(3, len(searchResultRes.GetCompanies()))]

	for _, matchedEmployer := range matchedEmployers {
		matched, dsNameMatchRpcReqResponses, checkErr := s.checkIfTxnRemitterNameMatchesEmployerName(ctx, txnRemitterName, []string{matchedEmployer.GetNameBySource()})
		if checkErr != nil {
			return false, nil, nil, fmt.Errorf("error while checking if txn remitter name matches the user's employer name, err: %w", checkErr)
		}
		dsNameMatchRpcReqResList = append(dsNameMatchRpcReqResList, dsNameMatchRpcReqResponses...)

		if matched {
			employerInfo, fetchEmpErr := s.getEmployerDetails(ctx, matchedEmployer.GetEmployerId())
			if fetchEmpErr != nil {
				return false, nil, nil, fmt.Errorf("error while fetching employer info for id: %s, err: %v", matchedEmployer.GetEmployerId(), fetchEmpErr)
			}
			return true, employerInfo, dsNameMatchRpcReqResList, nil
		}
	}
	return false, nil, dsNameMatchRpcReqResList, nil
}

func (s *SalaryTxnVerifierV1) performEmployerUANCheck(ctx context.Context, beneficiaryActorId, employerName string) (bool, VerificationFailureReason, error) {
	if !s.conf.SalaryTxnVerifierConfig.PerformUanLookupCheckForSoftBlacklistedEmployers {
		// soft blacklisted employers shouldn't be considered as eligible without uan name check
		return false, EmployerBlacklistedAndUanCheckNotEnabled, nil
	}
	userResp, userErr := s.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: beneficiaryActorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, userErr); rpcErr != nil {
		return false, "", fmt.Errorf("error fetching user by actor id, err: %w", rpcErr)
	}
	user := userResp.GetUser()

	uanRes, uanErr := s.vgEmploymentClient.UANLookupByPan(ctx, &vgEmploymentPb.UANLookupByPanRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_KARZA,
		},
		Pan: user.GetProfile().GetPAN(),
	})
	if uanErr != nil || !uanRes.GetStatus().IsSuccess() && !uanRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "vgEmploymentClient.UANLookupByPan call failed", zap.Error(uanErr), zap.String(logger.RPC_STATUS, uanRes.GetStatus().String()))
		return false, "", ErrUanLookupApiFailed
	}
	if uanRes.GetStatus().IsRecordNotFound() {
		logger.WarnWithCtx(ctx, "employer details not found from karza uan lookup api", zap.String(logger.ACTOR_ID_V2, beneficiaryActorId))
		return false, EmployerBlacklistedAndUanCheckApiReturnedNotFound, nil
	}

	empNameFromUan := uanRes.GetResult().GetSummary().GetUanLookup().GetCurrentEmployer()
	if empNameFromUan == "" {
		logger.Info(ctx, "empty current employer name received from karza uan lookup api", zap.String(logger.ACTOR_ID_V2, beneficiaryActorId))
		return false, EmployerBlacklistedAndUanCheckApiReturnedEmptyEmployerName, nil
	}
	// check if the internal employer name and UAN name matches using VG name matcher
	nameCheckRes, nameCheckErr := s.employerNameMatchVgClient.EmployerNameMatch(ctx, &employernamematchVgPb.EmployerNameMatchRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
		Name_1: employerName,
		Name_2: empNameFromUan,
	})
	if rpcErr := epifigrpc.RPCError(nameCheckRes, nameCheckErr); rpcErr != nil {
		return false, "", fmt.Errorf("vgEmployerNameMatchClient.EmployerNameMatch rpc call failed, err: %w", rpcErr)
	}
	if nameCheckRes.GetDecision() != namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS {
		logger.Info(ctx, "user employer name and employer name fetched from karza uan api doesn't match", zap.String("userEmpName", employerName), zap.String("empNameFromUan", empNameFromUan))
		return false, EmployerBlacklistedAndUanEmployerNotMatchCurrentEmployer, nil
	}
	return true, "", nil
}

func (s *SalaryTxnVerifierV1) getCurrentEmployerOfTheUser(ctx context.Context, actorId string) (*empPb.EmployerInfo, error) {
	employerRes, err := s.empClient.GetEmployerOfUser(ctx, &empPb.GetEmployerOfUserRequest{ActorId: actorId})
	if err != nil || !employerRes.GetStatus().IsRecordNotFound() && !employerRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "empClient.GetEmployerOfUser rpc call failed", zap.Any(logger.RPC_STATUS, employerRes.GetStatus()), zap.Error(err))
		return nil, fmt.Errorf("empClient.GetEmployerOfUser rpc call failed")
	}
	if employerRes.GetStatus().IsRecordNotFound() {
		return nil, epifierrors.ErrRecordNotFound
	}
	// we only need the current employer only if it's a verified one
	employerInfo := employerRes.GetEmployerInfo()
	if employerInfo == nil || !employerInfo.GetIsVerified() {
		logger.Info(ctx, "current employer of user is not a verified one", zap.String(logger.EMPLOYER_ID, employerInfo.GetEmployerId()))
		return nil, epifierrors.ErrRecordNotFound
	}

	return employerInfo, nil
}

// getTxnRemitterNames fetches remitter names from different fields in the system, following remitter names are returned
// 1. remitter name from vg if remitter api is enable for the protocol
// 2. FromPI verified name and FromPI account name
// 3. FromActor name (it's stored by parsing the transparticular)
// nolint: funlen
func (s *SalaryTxnVerifierV1) getTxnRemitterNames(ctx context.Context, txn *paymentPb.Transaction, fromActorId string) ([]*txnRemitterName, error) {
	var (
		remitterNameFromVg   string
		remitterVerifiedName string
		remitterAccName      string
		remitterActorName    string
	)
	var txnRemitterNames []*txnRemitterName
	errGroup, gctx := errgroup.WithContext(ctx)
	errGroup.Go(func() error {
		// taking the txn creation time at federal as txn time to avoid any inconsistency b/w epifi and federal since the api call federal
		txnTime := txn.GetDedupeId().GetTxnTime().AsTime()
		if s.doesPaymentProtocolRequiresRemitterInfoFetchFromVg(txn.GetPaymentProtocol()) && time.Since(txnTime) < s.conf.SalaryTxnVerifierConfig.MaxAllowedDurationSinceTxnTimeToFetchRemitterInfoFromVg {
			// if flag enabled, fetch remitter name from vg
			if s.conf.SalaryTxnVerifierConfig.FetchTxnRemitterInfoFromVg {
				txnRemitterName, err := s.getTxnRemitterNameFromVg(gctx, txn)
				if err != nil {
					return err
				}
				if txnRemitterName == "" {
					logger.Info(gctx, "got empty remitter name from vg", zap.String(logger.TXN_ID, txn.GetId()))
					return nil
				}
				remitterNameFromVg = txnRemitterName
			}
		}
		return nil
	})
	errGroup.Go(func() error {
		// get remitter details from payment instrument
		piRes, err := s.piClient.GetPiById(gctx, &piPb.GetPiByIdRequest{Id: txn.GetPiFrom()})
		if err != nil || !piRes.GetStatus().IsSuccess() {
			logger.Error(gctx, "piClient.GetPiById rpc call failed", zap.String(logger.PI_ID, txn.GetPiFrom()), zap.Any(logger.RPC_STATUS, piRes.GetStatus()), zap.Error(err))
			return fmt.Errorf("piClient.GetPiById rpc call failed")
		}
		remitterVerifiedName = piRes.GetPaymentInstrument().GetVerifiedName()
		remitterAccName = piRes.GetPaymentInstrument().GetAccount().GetName()
		return nil
	})
	errGroup.Go(func() error {
		// checking if name in from actor is matching with user’s declared employer, if yes then allow
		fromActorRes, err := s.actorClient.GetActorById(gctx, &actorPb.GetActorByIdRequest{
			Id: fromActorId,
		})
		if rpcErr := epifigrpc.RPCError(fromActorRes, err); rpcErr != nil {
			logger.Error(gctx, "error in actorClient.GetActorById rpc call", zap.Error(rpcErr), zap.String("fromActorId", fromActorId))
			return fmt.Errorf("error in actorClient.GetActorById rpc call")
		}
		remitterActorName = fromActorRes.GetActor().GetName()
		return nil
	})
	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return nil, errGrpErr
	}

	// doing this outside the go routine as we need it in a given order to verify it in unit tests
	if remitterNameFromVg != "" {
		txnRemitterNames = append(txnRemitterNames,
			&txnRemitterName{
				field: beSalaryPb.SalaryAutoVerifierMeta_NAME_FROM_FEDERAL_REMITTER_API,
				name:  remitterNameFromVg,
			},
		)
	}
	if remitterVerifiedName != "" {
		txnRemitterNames = append(txnRemitterNames,
			&txnRemitterName{
				field: beSalaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
				name:  remitterVerifiedName,
			},
		)
	}
	if remitterAccName != "" {
		txnRemitterNames = append(txnRemitterNames,
			&txnRemitterName{
				field: beSalaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_ACCOUNT_NAME,
				name:  remitterAccName,
			},
		)
	}
	if remitterActorName != "" {
		txnRemitterNames = append(txnRemitterNames,
			&txnRemitterName{
				field: beSalaryPb.SalaryAutoVerifierMeta_TXN_REMITTER_ACTOR_NAME,
				name:  remitterActorName,
			},
		)
	}

	return txnRemitterNames, nil
}

// fetch remitter name from vg federal api, currently supported only for INTRA_BANK and IMPS protocol.
func (s *SalaryTxnVerifierV1) getTxnRemitterNameFromVg(ctx context.Context, txn *paymentPb.Transaction) (string, error) {
	var cbsTranId, utr string
	switch txn.GetPaymentProtocol() {
	case paymentPb.PaymentProtocol_INTRA_BANK:
		cbsTranId = txn.GetDedupeId().GetCbsId()
	case paymentPb.PaymentProtocol_IMPS:
		utr = txn.GetUtr()
	default:
		return "", fmt.Errorf("payment protocol not supported for fetching remitter info from vg")
	}
	remitterInfoRes, err := s.vgPaymentClient.GetRemitterDetailsV1(ctx, &vgPaymentPb.GetRemitterDetailsV1Request{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		CbsTranId:       cbsTranId,
		Utr:             utr,
		TxnDatetime:     txn.GetDedupeId().GetTxnTime(),
		PaymentProtocol: txn.GetPaymentProtocol(),
	})
	if err != nil || !remitterInfoRes.GetStatus().IsSuccess() && !remitterInfoRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching remitter info from vg", zap.String(logger.TXN_ID, txn.GetId()), zap.Any(logger.RPC_STATUS, remitterInfoRes.GetStatus()), zap.Error(err))
		return "", fmt.Errorf("error fetching remitter info from vg")
	}
	if remitterInfoRes.GetStatus().IsRecordNotFound() {
		logger.WarnWithCtx(ctx, "remitter info not found for the txn", zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.PAYMENT_PROTOCOL, txn.GetPaymentProtocol().String()))
		return "", ErrRemitterInfoNotFound
	}
	return remitterInfoRes.GetRemitterAccountInfo().GetRemitterName(), nil
}

func (s *SalaryTxnVerifierV1) isGivenWorkflowPossibleForSalaryTxn(runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation,
	workflow orderPb.OrderWorkflow) bool {
	// order workflow would be NO_OP(which is deprecated) or OFF_APP_UPI for salary txns
	if runAllValidations || lo.Contains(validationTypes,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_WORKFLOW) {
		return workflow == orderPb.OrderWorkflow_NO_OP || workflow == orderPb.OrderWorkflow_OFF_APP_UPI
	}
	return true
}

func (s *SalaryTxnVerifierV1) isGivenProvenancePossibleForSalaryTxn(runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation,
	provenance orderPb.OrderProvenance) bool {
	if runAllValidations || lo.Contains(validationTypes,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PROVENANCE) {
		return provenance != orderPb.OrderProvenance_ATM
	}
	return true
}

func (s *SalaryTxnVerifierV1) isAmountGreaterThanOrEqualToMinRequiredSalaryTxnAmount(ctx context.Context, beneficiaryActorId string,
	amount *moneyPb.Money, runAllValidations bool, salaryTxnVerifierParams SalaryTxnVerifierParams) (bool, error) {
	if runAllValidations || lo.Contains(salaryTxnVerifierParams.ValidationsToRun,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MIN_AMOUNT) {
		minReqSalaryAmount, err := s.minReqSalaryAmountGetter.GetMinReqAmount(ctx, beneficiaryActorId)
		if err != nil {
			return false, err
		}
		// Adding discount on minimum salary required for salary verification.
		if salaryTxnVerifierParams.DiscountOnMinimumSalaryThreshold > 0 && salaryTxnVerifierParams.DiscountOnMinimumSalaryThreshold <= 100 {
			minReqSalaryAmount.Units = (int64)((100 - salaryTxnVerifierParams.DiscountOnMinimumSalaryThreshold) * minReqSalaryAmount.GetUnits() / 100)
		}
		// If after discount, minimum req salary amount goes below a threshold, we reassign the value of minimum salary
		// required for reverification
		if minReqSalaryAmount.GetUnits() < salaryTxnVerifierParams.MinRequiredSalaryAmountForReverification {
			minReqSalaryAmount.Units = salaryTxnVerifierParams.MinRequiredSalaryAmountForReverification
		}
		return pkgMoney.Compare(amount, minReqSalaryAmount) >= 0, nil
	}
	return true, nil
}

func (s *SalaryTxnVerifierV1) isGivenPaymentProtocolPossibleForSalaryTxn(runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation,
	txnPaymentProtocol paymentPb.PaymentProtocol) bool {
	if runAllValidations || lo.Contains(validationTypes,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PAYMENT_PROTOCOL) {
		return lo.Contains(s.conf.SalaryTxnVerifierConfig.AllowedTxnPaymentProtocols, txnPaymentProtocol)
	}
	return true
}

// for following protocols currently remitter details are not stored in internal DB so need to fetch from vg.
func (s *SalaryTxnVerifierV1) doesPaymentProtocolRequiresRemitterInfoFetchFromVg(protocol paymentPb.PaymentProtocol) bool {
	return lo.Contains(s.conf.SalaryTxnVerifierConfig.TxnProtocolsRequireRemitterInfoFromVg, protocol)
}

func (s *SalaryTxnVerifierV1) isGivenTxnNotCategorizedAsIncome(ctx context.Context, beneficiaryActorId string,
	txn *paymentPb.Transaction, runAllValidations bool, validationTypes []beSalaryPb.SalaryTxnValidation) (bool, error) {
	if runAllValidations || lo.Contains(validationTypes,
		beSalaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TXN_CATEGORY_NON_INCOME) {

		txnCategories, err := s.getCategoriesForTxn(ctx, beneficiaryActorId, txn.GetId())
		if err != nil {
			return false, fmt.Errorf("error fetching txn category details, err : %w", err)
		}

		// set on ontology ids linked to the txn
		txnCategoryOntologyIdsSet := make(map[string]struct{}, len(txnCategories.GetOntologies()))
		for _, ontology := range txnCategories.GetOntologies() {
			txnCategoryOntologyIdsSet[ontology.GetOntologyId()] = struct{}{}
		}

		// check if the txn was tagged with a non income related ontology id.
		for _, nonIncomeOntologyId := range s.conf.NonIncomeRelatedTxnCategoryOntologyIds {
			if _, ok := txnCategoryOntologyIdsSet[nonIncomeOntologyId]; ok {
				return true, nil
			}
		}
		return false, nil
	}
	return false, nil
}

func (s *SalaryTxnVerifierV1) getCategoriesForTxn(ctx context.Context, beneficiaryActorId, txnId string) (*categorizerPb.TransactionCategories, error) {
	txnCategoriesRes, err := s.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
		ActorId:      beneficiaryActorId,
		Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: txnId},
		Provenance:   categorizerPb.Provenance_DS,
		DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
	})
	if err != nil || !txnCategoriesRes.GetStatus().IsSuccess() && !txnCategoriesRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "txnCatClient.GetTxnCategoryDetails rpc call failed", zap.Any(logger.RPC_STATUS, txnCategoriesRes.GetStatus()), zap.Error(err))
		return nil, fmt.Errorf("txnCatClient.GetTxnCategoryDetails rpc call failed")
	}
	if txnCategoriesRes.GetStatus().IsRecordNotFound() {
		return nil, ErrTxnCategoryNotFound
	}
	return txnCategoriesRes.GetTxnCategories(), nil
}

func (s *SalaryTxnVerifierV1) isUserNewToSalaryProgram(ctx context.Context, actorId string) (bool, error) {
	// returning false for QA env to directly verify a request instead of sending it to sherlock
	if cfg.IsQaEnv(s.conf.Application.Environment) {
		return false, nil
	}

	// fetching verified salary txn verification requests with page size 2 and sort order ASC
	salaryTxnVerReqs, _, err := s.salaryTxnVerReqDao.GetSalaryTxnVerificationRequestsPaginated(ctx, "", []string{actorId}, nil, beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, beSalaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, 2, beSalaryPb.SortOrder_ASC, nil, nil, nil)
	if err != nil {
		return false, fmt.Errorf("salaryTxnVerReqDao.GetSalaryTxnVerificationRequestsPaginated dao call failed, err: %w", err)
	}
	// user is new to salary if there is only 1 or less verified reqs
	if len(salaryTxnVerReqs) < 2 {
		return true, nil
	}
	firstVerifiedSalaryTxnTimestamp := salaryTxnVerReqs[0].GetTxnTimestamp()

	// fetching verified salary txn verification requests with page size 1 and sort order DESC
	salaryTxnVerReqs, _, err = s.salaryTxnVerReqDao.GetSalaryTxnVerificationRequestsPaginated(ctx, "", []string{actorId}, nil, beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, beSalaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, 1, beSalaryPb.SortOrder_DESC, nil, nil, nil)
	if err != nil {
		return false, fmt.Errorf("salaryTxnVerReqDao.GetSalaryTxnVerificationRequestsPaginated dao 2nd call failed, err: %w", err)
	}
	latestVerifiedSalaryTxnTimestamp := salaryTxnVerReqs[0].GetTxnTimestamp()

	if latestVerifiedSalaryTxnTimestamp.AsTime().Sub(firstVerifiedSalaryTxnTimestamp.AsTime()) > s.conf.MinReqDurationToCompleteASalaryCycle {
		return false, nil
	}

	return true, nil
}

func getMarshalledDsNameMatchRpcReqResList(ctx context.Context, beneficiaryActorId string, dsNameMatchRpcReqResList []*DsNameMatchRpcReqRes) string {
	marshalledRes, marshalErr := json.Marshal(dsNameMatchRpcReqResList)
	if marshalErr != nil {
		logger.Error(ctx, "error marshalling dsNameMatchRpcReqResList", zap.String(logger.ACTOR_ID_V2, beneficiaryActorId), zap.Error(marshalErr))
	}
	return string(marshalledRes)
}

func (s *SalaryTxnVerifierV1) getEmployerDetails(ctx context.Context, employerId string) (*employmentPb.EmployerInfo, error) {
	employerRes, employerResErr := s.empClient.GetEmployer(ctx, &employmentPb.GetEmployerRequest{
		Identifier: &employmentPb.GetEmployerRequest_EmployerId{
			EmployerId: employerId,
		},
	})
	if err := epifigrpc.RPCError(employerRes, employerResErr); err != nil {
		return nil, fmt.Errorf("error getting employer details, empId: %s, err: %v", employerId, err)
	}
	return employerRes.GetEmployerInfo(), nil
}
