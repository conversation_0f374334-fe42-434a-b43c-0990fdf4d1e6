package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifigrpc"

	actorPb "github.com/epifi/gamma/api/actor"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	usersPb "github.com/epifi/gamma/api/user"

	"go.uber.org/zap"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/scripts/delete_salary_user_data/config"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	phoneNumber = flag.Uint64("phoneNumber", 9090909090,
		"10 digit phone number i.e. without country code, to delete salary user data")
	userClient  usersPb.UsersClient
	actorClient actorPb.ActorClient
)

// nolint: funlen
func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	userClient = userPb.NewUsersClient(authConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient = actorPb.NewActorClient(actorConn)

	ctx, cancelFunc := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelFunc()

	ph := &commontypes.PhoneNumber{
		CountryCode:    91,
		NationalNumber: *phoneNumber,
	}

	userRes, err := userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_PhoneNumber{
			PhoneNumber: ph,
		},
	})

	if err = epifigrpc.RPCError(userRes, err); err != nil {
		logger.PanicWithCtx(ctx, "userClient.GetUser call failed", zap.Error(err))
		return
	}

	actorRes, err2 := actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userRes.GetUser().GetId(),
	})

	if te := epifigrpc.RPCError(actorRes, err2); te != nil {
		logger.PanicWithCtx(ctx, "Error in fetching the actor id from the user data ", zap.Error(err))
		return
	}

	actorId := actorRes.GetActor().GetId()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}
	logger.InfoNoCtx("config", zap.Any("db-conf", conf.SalaryProgramDb))
	epifiSalaryDB1, err := storagev2.NewPostgresDBWithConfig(conf.SalaryProgramDb, false)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}

	epifiSalaryDB := gormctxv2.FromContextOrDefault(context.Background(), epifiSalaryDB1)

	var result []string

	err = epifiSalaryDB.Transaction(func(tx *gormv2.DB) error {
		if err = epifiSalaryDB.Raw("delete from salary_program_activation_history where salary_program_registration_id IN (select id from salary_program_registrations where actor_id = ?)", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_program_activation_history, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_program_activation_history")

		if err = epifiSalaryDB.Raw("delete from salary_txn_verification_requests where actor_id=?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_txn_verification_requests, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_txn_verification_requests")

		if err = epifiSalaryDB.Raw("delete from aa_salary_txn_verification_requests where actor_id=?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from aa_salary_txn_verification_requests, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from aa_salary_txn_verification_requests")

		if err = epifiSalaryDB.Raw("delete from salary_program_registration_stage_details where salary_program_registration_id IN (select id from salary_program_registrations where actor_id = ?)", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_program_registration_stage_details, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_program_registration_stage_details")

		if err = epifiSalaryDB.Raw("delete from salary_program_registrations where actor_id= ?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_program_registrations, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_program_registrations")

		if err = epifiSalaryDB.Raw("delete from salary_estimations where actor_id= ?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_estimations, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_estimations")

		if err = epifiSalaryDB.Raw("delete from salary_lite_mandate_execution_requests where actor_id= ?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_lite_mandate_execution_requests, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_lite_mandate_execution_requests")

		if err = epifiSalaryDB.Raw("delete from salary_lite_mandate_requests where actor_id= ?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from salary_lite_mandate_requests, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from salary_lite_mandate_requests")

		if err = epifiSalaryDB.Raw("delete from raise_salary_verification_by_ops_eligibility_info where actor_id= ?", actorId).Scan(result).Error; err != nil {
			return fmt.Errorf("error in deleting enteries from raise_salary_verification_by_ops_eligibility_info, err: %w", err)
		}
		logger.InfoNoCtx("entries deleted successfully from raise_salary_verification_by_ops_eligibility_info")

		return nil
	})
	if err != nil {
		logger.Panic("failed to delete entries", zap.Error(err))
	}
	logger.InfoNoCtx("entries deleted successfully for salary user")
}
