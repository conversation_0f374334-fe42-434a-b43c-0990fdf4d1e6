Application:
  Environment: "staging"

Aws:
  Region: "ap-south-1"

BrokerFirmAccountDetailsForForeignRemittance:
  OutwardRemittanceAccount: "OutwardFirmAccountId"

USStocksAlpacaDb:
  DbType: "PGDB"
  AppName: "usstocks"
  StatementTimeout: 1s
  Username: "usstocks_alpaca_dev_user"
  Password: ""
  Name: "usstocks_alpaca"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "staging/rds/epifimetis/usstocks_alpaca_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiWealthDb:
  DbType: "CRDB"
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

StocksDb:
  DBType: "PGDB"
  AppName: "stocks"
  StatementTimeout: 5m
  Username: "stocks_dev_user"
  Password: ""
  Name: "stocks"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "staging/rds/epifimetis/stocks_dev_user"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true


StocksRefreshCatalogPublisher:
  QueueName: "staging-stocks-catalog-refresh-queue"

SecuritiesHistoricalPricePublisher:
  QueueName: "staging-securities-historical-price-queue"

AddNewSecuritiesPublisher:
  QueueName: "staging-securities-catalog-addition-queue"

CaNewDataFetchPublisher:
  QueueName: "staging-insights-ca-data-new-data-fetch-event-queue"

Secrets:
  Ids:
    SlackBotOauthToken: "staging/ift/slack-bot-oauth-token"

PgdbMigrationConf:
  UsePgdb: true
  PgdbConnAlias: "usstocks_alpaca_pgdb"

USStocksRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 0
