package main

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
)

type jobPublishCaNewDataFetchEvent struct {
	caNewDataFetchEventPublisher queue.Publisher
}

func (j *jobPublishCaNewDataFetchEvent) PerformJob(ctx context.Context, req *JobRequest) error {
	actorId := req.Args1
	if actorId == "" {
		return fmt.Errorf("actor_id is required as Args1")
	}
	accountId := req.Args2
	if accountId == "" {
		return fmt.Errorf("account_id is required as Args2")
	}

	msgId, pubErr := j.caNewDataFetchEventPublisher.Publish(ctx, &caExtPb.AccountDataSyncEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
		ActorId:           actorId,
		AccountId:         accountId,
		EventTimestamp:    timestamppb.Now(),
		DataSyncType:      caExtPb.AccountDataSyncType_ACCOUNT_DATA_SYNC_TYPE_FULL_IMPORT,
	})
	if pubErr != nil {
		return fmt.Errorf("error publishing ca new data fetch event: %v", pubErr)
	}
	logger.Info(ctx, "successfully published ca new data fetch event", zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	return nil
}
