package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/metadata"

	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	sqsPkg "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	investmentAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	networthpb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog/scheduler"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	fgPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/api/usstocks/account"
	usStocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	mfVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	vgAnalyticsPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	"github.com/epifi/gamma/investment/mutualfund/dao/impl"
	"github.com/epifi/gamma/scripts/wealth_dragon/config"
	orderDaoImpl "github.com/epifi/gamma/usstocks/order/dao/impl"
)

var (
	inputJob = flag.String("JobName", "", "job name, refer to jobNames for accepted values")
	jobArgs1 = flag.String("Args1", "", "input args for the job (refer to job requirements)")
	jobArgs2 = flag.String("Args2", "", "input args for the job (refer to job requirements)")
)

func main() {
	_ = os.Setenv("DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP", "TRUE")
	flag.Parse()
	logger.Init(cfg.DevelopmentEnv)
	defer func() { _ = logger.Log.Sync() }()

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		defer os.Exit(1)
		return
	}
	clock := idgen.NewClock()
	idGen := idgen.NewDomainIdGenerator(clock)

	ussAlpacaDB, err := storagev2.NewGormDB(conf.USStocksAlpacaDb)
	if err != nil {
		logger.ErrorNoCtx("Failed to initialize USS Alpaca DB", zap.Error(err))
		defer os.Exit(1)
		return
	}

	epifiWealthDB, err := storagev2.NewGormDB(conf.EpifiWealthDb)
	if err != nil {
		logger.ErrorNoCtx("Failed to initialize Epifi Wealth DB", zap.Error(err))
		defer os.Exit(1)
		return
	}

	mfDao := impl.NewMutualFundCrdb(epifiWealthDB, nil)

	WalletOrderDao := orderDaoImpl.NewWalletOrderPgdb(conf.PgdbMigrationConf, ussAlpacaDB, idGen, 10)

	stocksDb, err1 := storagev2.NewGormDB(conf.StocksDb)
	if err1 != nil {
		logger.ErrorNoCtx("Failed to initialize Stocks DB", zap.Error(err1))
		defer os.Exit(1)
		return
	}

	awsConf, err2 := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	if err2 != nil {
		logger.ErrorNoCtx("Failed to initialize aws config", zap.Error(err1))
		defer os.Exit(1)
		return
	}

	sqsClient := sqsPkg.InitSQSClient(awsConf)
	stocksRefreshCatalogPublisher := initPublisher(ctx, sqsClient, conf.StocksRefreshCatalogPublisher)
	securitiesHistoricalPricePublisher := initPublisher(ctx, sqsClient, conf.SecuritiesHistoricalPricePublisher)
	addNewSecuritiesPublisher := initPublisher(ctx, sqsClient, conf.AddNewSecuritiesPublisher)
	caNewDataFetchEventPublisher := initPublisher(ctx, sqsClient, conf.CaNewDataFetchPublisher)

	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	defer epifigrpc.CloseConn(payConn)
	iftFileGenClient := fgPb.NewFileGeneratorClient(payConn)

	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	netWorthClient := networthpb.NewNetWorthClient(investmentConn)
	mfExternalOrdersClient := mfExternalPb.NewMFExternalOrdersClient(investmentConn)
	catalogSchClient := scheduler.NewSchedulerClient(investmentConn)

	analyserConn := epifigrpc.NewConnByService(cfg.ANALYSER_SERVICE)
	defer epifigrpc.CloseConn(analyserConn)
	investmentAnalyticsClient := investmentAnalyserPb.NewInvestmentAnalyticsClient(analyserConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	mfAnalyticsVg := vgAnalyticsPb.NewMFAnalyticsClient(vgConn)
	mfVGClient := mfVgPb.NewMutualFundClient(vgConn)
	vgStocksClient := vgStocksPb.NewStocksClient(vgConn)

	vgNotificationConn := epifigrpc.NewConnByService(cfg.VENDOR_NOTIFI_SERVICE)
	defer epifigrpc.CloseConn(vgNotificationConn)
	moengageClient := moengageVnPb.NewMoengageClient(vgNotificationConn)

	ussConn := epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE)
	defer epifigrpc.CloseConn(ussConn)
	orderMangerClient := usStocksOrderPb.NewOrderManagerClient(ussConn)
	accountManagerClient := account.NewAccountManagerClient(ussConn)

	redisClient := storage.NewRedisClientFromConfig(conf.USStocksRedisOptions, true)
	ussRedisStore := cache.NewRedisCacheStorage(redisClient)

	// ---- ADD YOUR JOB PROCESSOR DETAILS HERE ----
	var jobProcessors = map[string]*JobConfig{
		"GENERATE_TCS_REPORTING_FILES": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 15},
			isProdAllowed: true,
			processor: &jobGenerateTCSReportingFiles{
				iftFileGenClient: iftFileGenClient,
			},
		},
		"VERIFY_PORTFOLIO_VALUE": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 30},
			isProdAllowed: true,
			processor: &jobVerifyPortfolioValue{
				netWorthClient:            netWorthClient,
				investmentAnalyticsClient: investmentAnalyticsClient,
				mfExternalOrdersClient:    mfExternalOrdersClient,
				mfAnalyticsVg:             mfAnalyticsVg,
			},
		},
		"MF_CATALOG_CATEGORY_AVG": {
			validTill:     &date.Date{Year: 2025, Month: 12, Day: 30},
			isProdAllowed: true,
			processor: &jobMfCatalogCategoryAvg{
				catalogSchClient: catalogSchClient,
			},
		},
		"VERIFY_MOENGAGE_CONTENT_API": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 24},
			isProdAllowed: true,
			processor: &jobVerifyContentApi{
				moengageClient: moengageClient,
			},
		},
		"USS_STOCK_ID_MIGRATION": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 30},
			isProdAllowed: true,
			processor: &jobUssStockIdMigration{
				ussAlpacaDB: ussAlpacaDB,
			},
		},
		"MF_OBSOLETE_FUNDS_BACKFILL": {
			validTill:     &date.Date{Year: 2030, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobMfObsoleteFundsBackfill{
				mfVGClient: mfVGClient,
				mfDao:      mfDao,
			},
		},
		"CANCEL_USS_ORDERS": {
			validTill:     &date.Date{Year: 2025, Month: 05, Day: 13},
			isProdAllowed: true,
			processor: &jobCancelOrders{
				orderManagerClient: orderMangerClient,
			},
		},
		"STORE_USS_HISTORICAL_PRICES": {
			validTill:     &date.Date{Year: 2025, Month: 06, Day: 30},
			isProdAllowed: true,
			processor: &storeUssHistoricalPricesInRedis{
				vgStocksClient: vgStocksClient,
				ussRedisStore:  ussRedisStore,
			},
		},
		"REFRESH_SECURITY_DETAILS": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobRefreshSecuritiesCatalog{
				stocksDb:                      stocksDb,
				stocksRefreshCatalogPublisher: stocksRefreshCatalogPublisher,
			},
		},
		"SECURITY_HISTORICAL_PRICE": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobSecurityHistoricalPrice{
				stocksDb:                           stocksDb,
				securitiesHistoricalPricePublisher: securitiesHistoricalPricePublisher,
			},
		},
		"ADD_NEW_SECURITIES": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobAddNewSecurities{
				addNewSecuritiesPublisher: addNewSecuritiesPublisher,
			},
		},
		"UPDATE_SECURITIES_LISTINGS_ISIN": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobUpdateSecuritiesListingsISIN{
				StocksDb: stocksDb,
			},
		},
		"UPDATE_INVESTOR_ADDRESS": {
			validTill:     &date.Date{Year: 2025, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &updateInvestorAddressJob{
				accountManagerSvc: accountManagerClient,
			},
		},
		"USS_WALLET_FUND_TRANSFER_JOURNAL": {
			validTill:     &date.Date{Year: 2025, Month: 06, Day: 10},
			isProdAllowed: true,
			processor: &jobUssWalletFundTransferJournal{
				walletOrderDao: WalletOrderDao,
				vgStocksClient: vgStocksClient,
				config:         conf,
			},
		},
		"PUBLISH_CA_NEW_DATA_FETCH_EVENT": {
			validTill:     &date.Date{Year: 2025, Month: 07, Day: 01},
			isProdAllowed: true,
			processor: &jobPublishCaNewDataFetchEvent{
				caNewDataFetchEventPublisher: caNewDataFetchEventPublisher,
			},
		},
	}

	cleanedInputJob := *inputJob
	fmt.Printf("\n JOB: '%v' \n", *inputJob)

	jobDetails := jobProcessors[cleanedInputJob]
	if jobDetails == nil {
		fmt.Printf("\n JOB PROCESSOR DETAILS NOT FOUND FOR JOB: '%v' \n", cleanedInputJob)
		defer os.Exit(1)
		return
	}

	if err = jobDetails.validateJobDetails(); err != nil {
		fmt.Printf("\n JOB VALIDATION ERROR: '%v' \n", err)
		defer os.Exit(1)
		return
	}

	fmt.Println("---------------------------------- JOB START ----------------------------------")
	jobProcessor := jobDetails.processor
	if err = jobProcessor.PerformJob(ctx, &JobRequest{
		Job:   cleanedInputJob,
		Args1: *jobArgs1,
		Args2: *jobArgs2,
	}); err != nil {
		logger.Error(ctx, fmt.Sprintf("error in job: %v", cleanedInputJob), zap.Error(err))
		defer os.Exit(1)
		return
	}
	fmt.Println("---------------------------------- JOB END ------------------------------------")
}

func initPublisher(ctx context.Context, sqsClient *sqs.Client, pubConf *cfg.SqsPublisher) queue.Publisher {
	pub, err := sqsPkg.NewPublisherWithConfig(ctx, pubConf, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialize publisher", zap.Error(err))
	}
	return pub
}
