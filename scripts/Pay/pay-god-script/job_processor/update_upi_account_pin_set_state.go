package job_processor

import (
	"context"
	"encoding/json"
	"fmt"

	"gorm.io/gorm"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	upiEnums "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
)

type UpdateUpiAccountPinSetStateJob struct {
	accountId       string
	fromPinSetState upiEnums.UpiPinSetStatus
	toPinSetState   upiEnums.UpiPinSetStatus
	db              *gorm.DB
}

type updateUpiAccountPinSetStateArgs struct {
	AccountId       string `json:"account_id"`
	FromPinSetState string `json:"from_pin_set_state"`
	ToPinSetState   string `json:"to_pin_set_state"`
}

func NewUpdateUpiAccountPinSetStateJob(conf *config.Config) (*UpdateUpiAccountPinSetStateJob, func(), error) {
	// Get database connection using storagev2
	dbConn, err := storagev2.NewGormDB(conf.EpifiDb)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to connect to database: %v", err)
	}
	job := &UpdateUpiAccountPinSetStateJob{
		db: dbConn,
	}

	return job, func() {
		sqlDB, err := dbConn.DB()
		if err == nil && sqlDB != nil {
			_ = sqlDB.Close()
		}
	}, nil
}

// Args1: JSON {"account_id": "<string>", "from_pin_set_state": <string>, "to_pin_set_state": <string>}
func (j *UpdateUpiAccountPinSetStateJob) ParseAndStoreArgs(input *JobRequest) error {
	var args updateUpiAccountPinSetStateArgs
	if err := json.Unmarshal([]byte(input.Args1), &args); err != nil {
		return fmt.Errorf("failed to parse Args1 as JSON: %w", err)
	}
	if args.AccountId == "" || args.FromPinSetState == "" || args.ToPinSetState == "" {
		return fmt.Errorf("account_id, from_pin_set_state, to_pin_set_state fields are required")
	}

	// Convert string to enum for FromPinSetState
	fromEnumVal, ok := upiEnums.UpiPinSetStatus_value[args.FromPinSetState]
	if !ok {
		return fmt.Errorf("invalid from_pin_set_state: %s", args.FromPinSetState)
	}
	// Convert string to enum for ToPinSetState
	toEnumVal, ok := upiEnums.UpiPinSetStatus_value[args.ToPinSetState]
	if !ok {
		return fmt.Errorf("invalid to_pin_set_state: %s", args.ToPinSetState)
	}

	// if fromEnumVal != int32(upiEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET_DEVICE_CHANGE) ||
	// toEnumVal != int32(upiEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET) {
	// return fmt.Errorf("unsupported from_pin_set_state or to_pin_set_state: %s, %s", args.FromPinSetState, args.ToPinSetState)
	// }

	j.accountId = args.AccountId
	j.fromPinSetState = upiEnums.UpiPinSetStatus(fromEnumVal)
	j.toPinSetState = upiEnums.UpiPinSetStatus(toEnumVal)
	return nil
}

func (j *UpdateUpiAccountPinSetStateJob) DoJob(ctx context.Context) error {
	// Update only the specific upi_account with the given accountId
	query := `UPDATE upi_accounts SET pin_set_status = ?, updated_at = NOW() WHERE id = ? AND pin_set_status = ?`
	result := j.db.Exec(query, j.toPinSetState, j.accountId, j.fromPinSetState)
	if result.Error != nil {
		return fmt.Errorf("failed to execute update: %v", result.Error)
	}
	return nil
}
