package main

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	OperStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	beSavingsPb "github.com/epifi/gamma/api/savings"

	"go.uber.org/zap"
)

type SyncAccountStatus struct {
	actorClient             actorPb.ActorClient
	savingsClient           beSavingsPb.SavingsClient
	operationalStatusClient OperStatusPb.OperationalStatusServiceClient
	accountBalanceClient    accountBalancePb.BalanceClient
}

const s3FilePathForAccounts = "account_status_report_%s.csv"

func (m *SyncAccountStatus) GetQueryStorageInfo() []*QueryStorageInfo {
	args, query := getAccountsQuery()
	s3FilePath := fmt.Sprintf(s3FilePathForAccounts, time.Now().Format("01-02-2006"))
	var queryStorageInfos []*QueryStorageInfo
	queryStorageInfos = append(queryStorageInfos, &QueryStorageInfo{
		queryArgs:   args,
		prestoQuery: query,
		s3File:      s3FilePath,
	})

	return queryStorageInfos
}

func getAccountsQuery() ([]interface{}, string) {

	args := []interface{}{operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_CLOSED.String(), 90}
	query := "SELECT aos.account_identifier FROM epifi.account_operational_statuses aos WHERE aos.operational_status != ?  AND date_add('day', -?, CURRENT_DATE) >= date_trunc('day', aos.updated_at)"
	return args, query
}

//nolint:funlen,gocritic
func (m *SyncAccountStatus) DoJob(savingsAccountIds []string, _ int) error {
	const (
		sleepDuration = time.Second
		batchSize     = 2
	)
	var failedSavingsAccountIds []string

	for idx, savingsAccountId := range savingsAccountIds {
		ctx := context.Background()
		if idx%50 == 0 {
			logger.InfoNoCtx(fmt.Sprintf("index checkpoint for savings_account_ids (idx modulo 50), idx: %d", idx))
		}

		// Sleep based on batch size to avoid overwhelming services
		if (idx+1)%batchSize == 0 {
			time.Sleep(sleepDuration)
		}
		// Get status by savings account id
		accStatusResp, accStatusErr := m.operationalStatusClient.GetOperationalStatus(ctx, &OperStatusPb.GetOperationalStatusRequest{
			DataFreshness: OperStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
			AccountIdentifier: &OperStatusPb.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: savingsAccountId,
			},
		})
		if rpcErr := epifigrpc.RPCError(accStatusResp, accStatusErr); rpcErr != nil {
			logger.Error(ctx, "error fetching account status", zap.Error(rpcErr))
			failedSavingsAccountIds = append(failedSavingsAccountIds, savingsAccountId)
			continue
		}

	}

	fmt.Println("--------------------")
	logger.InfoNoCtx(fmt.Sprintf("Process failed for savings account ids: %v ", failedSavingsAccountIds))

	return nil
}

func (m *SyncAccountStatus) GetS3FileName() []string {
	var fileNames []string

	s3InputPathWithDate := fmt.Sprintf(s3FilePathForAccounts, time.Now().Format("01-02-2006"))
	fileNames = append(fileNames, s3InputPathWithDate)
	return fileNames
}
