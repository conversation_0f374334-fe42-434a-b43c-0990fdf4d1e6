// nolint:funlen,dupl
package main

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"github.com/rudderlabs/analytics-go"
	"go.uber.org/zap"

	"github.com/slack-go/slack"

	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	sqsPkg "github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/cx/watson"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	dao2 "github.com/epifi/gamma/kyc/vkyc/dao"
	"github.com/epifi/gamma/scripts/presto_result_s3_uploader/config"
	"github.com/epifi/gamma/vendormapping/dao"
)

var inputJob = flag.String("JobName", "", "job name, refer to jobNames for accepted values")

// Args are specific for each job, go to the specific job file to see Args definition
// For celestial_manual_intervention_report, args map to CelestialManualInterventionArgs
var args = flag.String("Args", "", "additional arguments required for the report")

type JobProcessors interface {
	GetQueryStorageInfo() []*QueryStorageInfo
	GetS3FileName() []string
	DoJob([]string, int) error
}

func main() {
	flag.Parse()

	conf, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}

	logger.Init(conf.Application.Environment)
	ctx := context.Background()

	awsSession, err := session.NewSession(conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		fmt.Printf("error to initialize AWS session : %v\n", zap.Error(err))
		defer os.Exit(1)
		return
	}
	s3Client := s3.NewClient(awsSession, conf.S3Conf.Bucket)
	if err != nil {
		logger.ErrorNoCtx("failed to create client", zap.Error(err))
		defer os.Exit(1)
		return
	}

	var db *sql.DB

	celestialQueryArgs := &CelestialManualInterventionArgs{}
	if *inputJob == "celestial_manual_intervention_report" {
		if err = json.Unmarshal([]byte(*args), &celestialQueryArgs); err != nil {
			logger.ErrorNoCtx("failed to unmarshal args into celestial manual intervention alerts args", zap.Error(err))
			defer os.Exit(1)
			return
		}
		configDetails, ok := workflowToConfigDetails[strings.ToLower(celestialQueryArgs.WorkflowType)]
		if !ok {
			logger.ErrorNoCtx("failed to get config details for workflow type", zap.String("workflowType", celestialQueryArgs.WorkflowType))
			defer os.Exit(1)
			return
		}

		if configDetails.DbOwnership == "" {
			logger.ErrorNoCtx("failed to get ownership for workflow type", zap.String("workflowType", celestialQueryArgs.WorkflowType))
			defer os.Exit(1)
			return
		}
		ownership := configDetails.DbOwnership
		prestoConf, isConfigPresent := conf.S3UploaderPrestoCelestialConfig[ownership]
		if !isConfigPresent {
			logger.ErrorNoCtx("failed to get presto config", zap.String("ownership", ownership))
			defer os.Exit(1)
			return
		}

		db, err = storagev2.NewPrestoDBFromConfig(prestoConf, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	nonTerminalCasesReportArgs := &CelestialNonTerminalCasesReportArgs{}
	if *inputJob == "celestial_non_terminal_cases_report" {
		if err = json.Unmarshal([]byte(*args), &nonTerminalCasesReportArgs); err != nil {
			logger.ErrorNoCtx("failed to unmarshal args into celestial non terminal cases args", zap.Error(err))
			defer os.Exit(1)
			return
		}
		configDetails, ok := workflowToConfigDetails[strings.ToLower(nonTerminalCasesReportArgs.WorkflowType)]
		if !ok {
			logger.ErrorNoCtx("failed to get config details for workflow type", zap.String("workflowType", nonTerminalCasesReportArgs.WorkflowType))
			defer os.Exit(1)
			return
		}

		if configDetails.DbOwnership == "" {
			logger.ErrorNoCtx("failed to get ownership for workflow type", zap.String("workflowType", nonTerminalCasesReportArgs.WorkflowType))
			defer os.Exit(1)
			return
		}
		ownership := configDetails.DbOwnership
		prestoConf, isConfigPresent := conf.S3UploaderPrestoCelestialConfig[ownership]
		if !isConfigPresent {
			logger.ErrorNoCtx("failed to get presto config", zap.String("ownership", ownership))
			defer os.Exit(1)
			return
		}

		db, err = storagev2.NewPrestoDBFromConfig(prestoConf, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "vkyc_stuck_in_review_state_federal_mail" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "minkyc_account_closure" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoFederalConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "nr_vkyc_stuck_in_review_state_federal_mail" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoNRFederalConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "re_kyc_due_mail" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoBankCustomerConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "unfreeze_rekyc_completed_federal_mail" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoFederalConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "unfreeze_revkyc_completed_federal_mail" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "unfreeze_pan_update_completed_federal_mail" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoFederalConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "afu_stuck_report" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoFederalConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "onboarding_manual_intervention_report" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "liveness_stuck_report" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	if *inputJob == "sync_account_status" {
		db, err = storagev2.NewPrestoDBFromConfig(conf.S3UploaderPrestoConfig, conf.Application.Environment)
		if err != nil {
			logger.ErrorNoCtx("failed to create connection for presto db", zap.Error(err))
			defer os.Exit(1)
			return
		}
		defer func(db *sql.DB) {
			dbErr := db.Close()
			if dbErr != nil {
				logger.Error(ctx, "failed to close db connection", zap.Error(dbErr))
				defer os.Exit(1)
				return
			}
		}(db)
	}

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	kycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	defer epifigrpc.CloseConn(kycConn)
	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	bankcustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bankcustConn)
	usersClient := user.NewUsersClient(userConn)
	bcClient := bankcust.NewBankCustomerServiceClient(bankcustConn)
	compClient := compliance.NewComplianceClient(bankcustConn)
	commsClient := comms.NewCommsClient(commsConn)
	actorClient := actor.NewActorClient(actorConn)
	savingsClient := savings.NewSavingsClient(savingsConn)
	vkycClient := vkycPb.NewVKYCClient(kycConn)
	customerVgClient := customer.NewCustomerClient(vgConn)
	slackClient := slack.New(conf.SlackConfig.OAuthToken, slack.OptionDebug(false))
	onbClient := onbPb.NewOnboardingClient(userConn)

	accountService := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accountService)

	operationalStatusClient := operStatusPb.NewOperationalStatusServiceClient(accountService)

	fedVkycUpdPublisher := initPublisher(conf)
	if err != nil {
		logger.ErrorNoCtx("failed to load postgres db", zap.Error(err))
		defer os.Exit(1)
		return
	}
	dbConn, err := storagev2.NewPostgresDBWithConfig(conf.VendormappingPgdb, false)
	if err != nil {
		logger.ErrorNoCtx("failed to load postgres db", zap.Error(err))
		defer os.Exit(1)
		return
	}
	epifiDb, _ := storagev2.NewCRDBWithConfig(conf.EpifiDb, false)

	vendorMappingDao := dao.NewVendorMappingDao(dbConn, nil)
	accountBalanceClient := accountBalancePb.NewBalanceClient(accountService)
	authClient := authPb.NewAuthClient(authConn)

	vkycCustInfoDao := dao2.NewVKYCKarzaCustomerInfo(epifiDb)
	vkycSummaryInfoDao := dao2.NewVKYCSummaryDao(epifiDb)
	vkycKarzaCallHistoryDao := dao2.NewVKYCKarzaCallHistoryDao(epifiDb)

	// Setup rudderstack client
	broker, err := initRudderClient(conf)
	if err != nil {
		logger.ErrorNoCtx("failed to initialize rudder client")
		panic(err)
	}

	cxConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	watsonClient := watson.NewWatsonClient(cxConn)

	var jobProcessor = map[string]JobProcessors{
		"vkyc_stuck_in_review_state_federal_mail": &VkycStuckInReviewStateFederalMail{
			vkycCustInfoDao:         vkycCustInfoDao,
			vkycSummaryInfoDao:      vkycSummaryInfoDao,
			commsClient:             commsClient,
			conf:                    conf,
			usersClient:             usersClient,
			actorClient:             actorClient,
			customerVgClient:        customerVgClient,
			fedVkycUpdPublisher:     fedVkycUpdPublisher,
			vkycClient:              vkycClient,
			bcClient:                bcClient,
			savingsClient:           savingsClient,
			slack:                   slackClient,
			vkycKarzaCallHistoryDao: vkycKarzaCallHistoryDao,
			watsonClient:            watsonClient,
			eventBroker:             broker,
		},
		"minkyc_account_closure": &MinKycAccountClosure{
			actorClient:         actorClient,
			userClient:          usersClient,
			vendorMappingDao:    vendorMappingDao,
			authClient:          authClient,
			savingsClient:       savingsClient,
			bcClient:            bcClient,
			fedVkycUpdPublisher: fedVkycUpdPublisher,
			balClient:           accountBalanceClient,
		},
		"dob_discrepancy_mail_scheduler": &DobDiscrepancyMailScheduler{
			conf:             conf,
			usersClient:      usersClient,
			actorClient:      actorClient,
			savingsClient:    savingsClient,
			vendorMappingDao: vendorMappingDao,
			commsClient:      commsClient,
		},
		"nr_vkyc_stuck_in_review_state_federal_mail": &NrVkycStuckInReviewStateFederalMail{
			commsClient: commsClient,
			conf:        conf,
			slack:       slackClient,
		},
		"re_kyc_due_mail": &RekycDueMail{
			commsClient: commsClient,
			conf:        conf,
			slack:       slackClient,
			bcClient:    bcClient,
			usersClient: usersClient,
			compClient:  compClient,
		},
		"unfreeze_rekyc_completed_federal_mail": &UnfreezeRekycCompletedFederalMail{
			commsClient:             commsClient,
			conf:                    conf,
			bcClient:                bcClient,
			compClient:              compClient,
			operationalStatusClient: operationalStatusClient,
			savingsClient:           savingsClient,
			watsonClient:            watsonClient,
			eventBroker:             broker,
			slack:                   slackClient,
		},
		"unfreeze_revkyc_completed_federal_mail": &UnfreezeRevkycCompletedFederalMail{
			commsClient:             commsClient,
			conf:                    conf,
			bcClient:                bcClient,
			operationalStatusClient: operationalStatusClient,
			savingsClient:           savingsClient,
			slack:                   slackClient,
			vkycSummaryInfoDao:      vkycSummaryInfoDao,
		},
		"unfreeze_pan_update_completed_federal_mail": &UnfreezePanUpdateCompletedFederalMail{
			commsClient:             commsClient,
			conf:                    conf,
			bcClient:                bcClient,
			operationalStatusClient: operationalStatusClient,
			savingsClient:           savingsClient,
			UserClient:              usersClient,
		},
		"afu_stuck_report": &AFUStuckReport{
			slack: slackClient,
		},
		"onboarding_manual_intervention_report": &OnboardingStageManualInterventionReport{
			slack:     slackClient,
			bcClient:  bcClient,
			onbClient: onbClient,
		},
		"liveness_stuck_report": &LivenssStuckReport{
			slack:     slackClient,
			bcClient:  bcClient,
			onbClient: onbClient,
		},
		"celestial_manual_intervention_report": &CelestialManualInterventionReport{
			args:  celestialQueryArgs,
			slack: slackClient,
			conf:  conf,
		},
		"celestial_non_terminal_cases_report": &CelestialNonTerminalCasesReport{
			args:  nonTerminalCasesReportArgs,
			slack: slackClient,
			conf:  conf,
		},
		"sync_account_status": &SyncAccountStatus{
			actorClient:             actorClient,
			savingsClient:           savingsClient,
			operationalStatusClient: operationalStatusClient,
			accountBalanceClient:    accountBalanceClient,
		},
	}

	job := jobProcessor[*inputJob]
	if job == nil {
		fmt.Printf("\n JOB PROCESSOR NOT FOUND FOR JOB: '%v' \n", job)
		defer os.Exit(1)
		return
	}
	queryStorageInfos := job.GetQueryStorageInfo()
	exitCode := 0

	for _, queryStorageInfo := range queryStorageInfos {
		prestoResult, queryErr := queryPresto(ctx, db, queryStorageInfo.prestoQuery, queryStorageInfo.queryArgs)
		if queryErr != nil {
			logger.Error(ctx, "error in running query in presto client", zap.Error(queryErr))
			exitCode = 1
			break
		}
		fmt.Println(prestoResult)
		if err = uploadToS3(prestoResult, s3Client, queryStorageInfo.s3File); err != nil {
			exitCode = 1
			break
		}
	}

	if exitCode == 1 {
		defer os.Exit(1)
		return
	}

	fileNames := job.GetS3FileName()
	for index, fileName := range fileNames {
		prestoResults, fileErr := readFromS3(s3Client, fileName)
		if fileErr != nil {
			exitCode = 1
			break
		}
		if err = job.DoJob(prestoResults, index); err != nil {
			exitCode = 1
			break
		}
	}
	if exitCode == 1 {
		defer os.Exit(1)
		return
	}
}

func queryPresto(ctx context.Context, prestoDb *sql.DB, query string, args []interface{}) ([]string, error) {
	logPrestoQuery(ctx, query, args)
	rows, err := prestoDb.QueryContext(ctx,
		query, args...)
	if err != nil {
		return nil, fmt.Errorf("error in querying from presto: %w", err)
	}
	if rows.Err() != nil {
		return nil, fmt.Errorf("encountered error while getting rows from presto: %w", rows.Err())
	}

	var prestoResults []string
	for rows.Next() {
		var prestoResult string
		err = rows.Scan(&prestoResult)
		if err != nil {
			logger.ErrorNoCtx("Error in typecast string", zap.Error(err))
			continue
		}
		prestoResults = append(prestoResults, prestoResult)
	}

	return prestoResults, err
}

func logPrestoQuery(ctx context.Context, query string, args []any) {
	var builder strings.Builder
	argIndex := 0

	for i := 0; i < len(query); i++ {
		if query[i] == '?' {
			if argIndex >= len(args) {
				logger.Error(ctx, "not enough args")
				return
			}

			arg := args[argIndex]
			switch v := arg.(type) {
			case string:
				builder.WriteString("'" + strings.ReplaceAll(v, "'", "''") + "'")
			case bool:
				builder.WriteString(fmt.Sprintf("%t", v))
			default:
				builder.WriteString(fmt.Sprintf("%v", v))
			}
			argIndex++
		} else {
			builder.WriteByte(query[i])
		}
	}

	if argIndex != len(args) {
		logger.Error(ctx, "too many args provided")
		return
	}
	logger.Info(ctx, "presto_sql: "+builder.String())
}

func uploadToS3(snowflakeResults []string, s3Client s3.S3Client, outputPath string) error {
	buf := new(bytes.Buffer)
	wr := csv.NewWriter(buf)
	err := wr.Write(snowflakeResults)
	if err != nil {
		logger.ErrorNoCtx("error while writing snowflake output to csv write", zap.Error(err))
		return err
	}
	wr.Flush()
	logger.InfoNoCtx(fmt.Sprintf("filename: %v row count: %v", outputPath, len(snowflakeResults)))
	err = s3Client.Write(outputPath, buf.Bytes(), string(types.ObjectCannedACLBucketOwnerFullControl))
	if err != nil {
		logger.ErrorNoCtx("error while uploading files to s3")
		return errors.Wrap(err, "error while uploading files to s3")
	}
	return nil
}

func initPublisher(conf *config.Config) queue.Publisher {
	awsSession, err := session.NewSession(conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		fmt.Printf("error to initialize AWS session : %v\n", zap.Error(err))
		defer os.Exit(1)
		return nil
	}
	var sqsClient = sqsPkg.InitSQSClient(awsSession)
	pub, err := sqsPkg.NewPublisherWithConfig(conf.FederalVkycUpdatePublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialize publisher", zap.Error(err))
	}
	return pub
}

// read csv file from the given path
func readFromS3(s3Client s3.S3Client, fileName string) ([]string, error) {
	csvData, err := s3Client.Read(fileName)
	if err != nil {
		logger.ErrorNoCtx("Failed to read CSV data", zap.Error(err))
		return []string{}, err
	}
	reader := csv.NewReader(bytes.NewReader(csvData))
	snowflakeResults, readErr := reader.ReadAll()
	if readErr != nil {
		logger.ErrorNoCtx("Failed to read typecast csv file", zap.Error(readErr))
		return []string{}, readErr
	}
	if len(snowflakeResults) == 0 {
		return []string{}, nil
	}
	logger.InfoNoCtx(fmt.Sprintf("filename: %v result count: %v", fileName, len(snowflakeResults[0])))
	return snowflakeResults[0], nil
}

func initRudderClient(conf *config.Config) (events.Broker, error) {
	// load rudder-stack broker for sending events
	client, err := analytics.NewWithConfig(
		conf.Secrets.Ids["RudderWriteKey"], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second,
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
		})
	if err != nil {
		return nil, err
	}
	broker := events.NewRudderStackBroker(client)
	return broker, nil
}
