//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsSqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/rudderlabs/analytics-go"
	"github.com/slack-go/slack"

	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	wire "github.com/google/wire"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/faas"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	cardControlPb "github.com/epifi/gamma/api/card/control"
	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	tokenizerProxyPb "github.com/epifi/gamma/api/card/tokenizer_proxy"
	commsPb "github.com/epifi/gamma/api/comms"
	clePb "github.com/epifi/gamma/api/credit_limit_estimator"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	kycPb "github.com/epifi/gamma/api/kyc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pan"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tokenizer"
	usersPb "github.com/epifi/gamma/api/user"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	vgLendingPb "github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	obPayment "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	panPb "github.com/epifi/gamma/api/vendorgateway/pan"
	cardWireTypes "github.com/epifi/gamma/card/wire/types"
	accountingDao "github.com/epifi/gamma/firefly/accounting/dao"
	ffBillingDao "github.com/epifi/gamma/firefly/billing/dao"
	ffComms "github.com/epifi/gamma/firefly/comms"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	ffWireTypes "github.com/epifi/gamma/firefly/wire/types"
	"github.com/epifi/gamma/scripts/cc_firefly/config"
	"github.com/epifi/gamma/scripts/cc_firefly/helper"
	"github.com/epifi/gamma/scripts/cc_firefly/job"
	types2 "github.com/epifi/gamma/scripts/cc_firefly/wire/types"
	wireTypes "github.com/epifi/gamma/scripts/cc_firefly/wire/types"
	"github.com/epifi/gamma/user/onboarding/dao"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"

	epifitemporalfaas "github.com/epifi/be-common/pkg/epifitemporal/faas"

	ffPb "github.com/epifi/gamma/api/firefly"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	simulatorLendingCreditCardPb "github.com/epifi/gamma/api/simulator/lending/creditcard"
	dcDao "github.com/epifi/gamma/card/dao"
	daoImpl "github.com/epifi/gamma/firefly/dao"

	docsPb "github.com/epifi/gamma/api/docs"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"

	authDaoImpl "github.com/epifi/gamma/auth/orchestrator/dao"

	ccLmsPb "github.com/epifi/gamma/api/firefly/lms"
	ccVgV2Pb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	currencyInsightsVgPb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	accountVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	cpVgClientPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
)

func InitialiseJobRegistry(
	ctx context.Context,
	db *gorm.DB,
	dcDb dcDao.DebitCardPGDB,
	s3Client s3.S3Client,
	rudderClient analytics.Client,
	conf *config.Config,
	fireflyClient ffPb.FireflyClient,
	client *slack.Client,
	commsClient commsPb.CommsClient,
	userClient usersPb.UsersClient,
	ccVgClient ccVgPb.CreditCardClient,
	accountingClient ffAccountsPb.AccountingClient,
	doOnceManager onceV2.DoOnce,
	piClient piPb.PiClient,
	epifiDb types.EpifiCRDB,
	ccDb types.CreditCardPGDB,
	billingClient billing.BillingClient,
	actorClient actorPb.ActorClient,
	paymentClient paymentPb.PaymentClient,
	savingsClient savingsPb.SavingsClient,
	dcDocsS3Client helper.DcDocsS3Client,
	orderClient orderPb.OrderServiceClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	orderUpdateEventPublisher job.OrderUpdateEventPublisher,
	rewardsClient rewardsPb.RewardsGeneratorClient,
	ccPinotClient ffPinotPb.TxnAggregatesClient,
	ccStageEventUpdatePublisher job.StageUpdateEventPublisher,
	ccDocsS3Client job.CcDocsS3Client,
	ccWorkflowReportingConfig *config.CcWorkflowReportingJobConfig,
	debitCardDataS3Client helper.DcDataS3Client,
	cryptor helper.PgpCryptor,
	panClient pan.PanClient,
	operationalServiceClient operationalStatusPb.OperationalStatusServiceClient,
	CelestialClient celestialPb.CelestialClient,
	cardProvisioningClient cardProvPb.CardProvisioningClient,
	TieringClient tieringPb.TieringClient,
	payClient payPb.PayClient,
	cardControlClient cardControlPb.CardControlClient,
	depositClient depositPb.DepositClient,
	obPaymentClient obPayment.PaymentClient,
	creditLineClient vgLendingPb.CreditLineClient,
	cleClient clePb.CreditLimitEstimatorClient,
	palClient palPb.PreApprovedLoanClient,
	profileClient profilePb.ProfileClient,
	simulatorLendingCreditCard simulatorLendingCreditCardPb.CreditCardClient,
	awsConf aws.Config,
	cryptorStore *cryptormap.InMemoryCryptorStore,
	lmsClient ccLmsPb.LoanManagementSystemClient,
	rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient,
	docsClient docsPb.DocsClient,
	eKycVgClient ekycPb.EKYCClient,
	panVgClient panPb.PANClient,
	kycClient kycPb.KycClient,
	onboardingClient onboardingPb.OnboardingClient,
	authClient auth.AuthClient,
	vgSavingsClient vgSavingsPb.SavingsClient,
	onbDao dao.OnboardingDao,
	ffTemporalClient helper.FFTemporalClient,
	authReqDao authDaoImpl.AuthRequestsDao,
	authReqStageDao authDaoImpl.AuthRequestStagesDao,
	authTemporalClient job.AuthTemporalClient,
	epifiDbV2 job.EpifiDbV2,
	tokenizerClient tokenizer.TokenizerClient,
	fireflyRedisStore ffWireTypes.FireflyRedisStore,
	manualGiveawayEventPublisher wireTypes.RewardsManualGiveawayEventPublisher,
	dcTxnExecutor job.DcIdempotentTxnExecutor,
	dbHandle *dynamodb.Client,
	segmentClient segmentPb.SegmentationServiceClient,
	debitCardTemporalClient job.DebitCardTemporalClient,
	redisClient cardWireTypes.CardsRedisStore,
	tokenizerProxyClient tokenizerProxyPb.TokenizerProxyClient,
	currencyInsightsVgClient currencyInsightsVgPb.ServiceClient,
	cpVgClient cpVgClientPb.CardProvisioningClient,
	opsVgClient accountVgPb.AccountsClient,
	ccVgClientV2 ccVgV2Pb.CreditCardClient,
) (*job.Registry, error) {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		daoImpl.CreditCardOfferEligibilityCriteriaWireSet,
		daoImpl.CardRequestDaoWithInstrumentationWireSet,
		daoImpl.CardRequestStageWireSet,
		daoImpl.CreditCardWireSet,
		daoImpl.CreditCardOfferProxyWireSet,
		dcDao.CardDaoWireSet,
		types2.CardDaoCacheConfigProvider,
		types2.DcCacheStorageProvider,
		dcDao.PhysicalCardDispatchRequestWireSet,
		dcDao.CardTrackingRequestWireSet,
		ffBillingDao.CreditCardBillDaoWireSet,
		accountingDao.CreditAccountWireSet,
		accountingDao.CreditCardTransactionWireSet,
		types2.CcCacheStorageProvider,
		types2.CardRequestCacheConfigProvider,
		types2.CreditCardOfferCacheConfigProvider,
		FireflyFaasExecutorProvider,
		helper.NewHelper,
		job.NewProcessFiEligibleBaseJob,
		job.NewGenerateCcQrJob,
		job.NewInitiateCardRequestJob,
		job.NewPhysicalCardReconJob,
		job.NewCardTrackingCardRequestBackfillJob,
		job.NewCcCommsTestJob,
		job.NewDCExpiryUpdateJob,
		job.NewDcTxnDataFetchJob,
		job.NewDcTxnPublishJob,
		job.NewDcFiPlusForexRefundNotificationReplayJob,
		job.NewCcStatementJob,
		job.NewTriggerWelcomeOfferJob,
		job.NewMaskCardNumberJob,
		job.NewDcTxnDataFetchWithCountryCodeJob,
		ffComms.AsyncProcessorWireSet,
		ffHelper.NewCommsDataHelper,
		job.NewProcessCardForexTxnRefundJob,
		job.NewCcWorkflowReportingJob,
		job.NewDisableOffersJob,
		job.NewAddFiRejectedUsersJob,
		job.NewExtendOfferExpiryJob,
		job.NewProcessCardDeclineDataJob,
		job.NewUpdateCardRequestsJob,
		job.NewCreditCardClosureIntimationJob,
		job.NewUploadFileToS3BucketJob,
		job.NewGetPanAadhaarLinkStatusJob,
		job.NewDcVendorIdToAccNumberMapJob,
		job.NewForexRefundReconJob,
		job.NewCbsIdPopulationForTxnIdJob,
		job.NewDcCardCreationRetryJob,
		job.NewStatementAlertingJob,
		job.NewReconUnsentRefunds,
		job.NewDcPhysicalCardDispatchReconJob,
		job.NewDcContactlessSwitchOffJob,
		job.NewProcessedRefundsRecon,
		job.NewForexRefundAlertingJob,
		job.NewUpdatePaymentInstrumentJob,
		job.NewCcCardRequestRewardJob,
		job.NewDcPhysicalCardDispatchFailureAlertJob,
		job.NewTriggerStatementGenerationWorkflowJob,
		job.NewAddCCTransactionJob,
		job.NewTriggerReconcileWorkflowsJob,
		job.NewStatementDueDateMismatchIssueJob,
		job.NewUpdateCardRequestAndStageStatusJob,
		job.NewManualStatementGenerationJob,
		job.NewDecryptVgFilesJob,
		job.NewFetchWelcomeOfferRewardIdJob,
		job.NewOfferInvalidationJob,
		job.NewBillEraserReconJob,
		job.NewCcSecuredDepositIdJob,
		job.NewUpdateAtmLimitJob,
		job.NewUpdateRewardInfoInBillJob,
		job.NewFiliteResetJob,
		job.NewDedupeCheckJob,
		job.NewTerminateAuthWorkflowsJob,
		job.NewCcOfferAddJob,
		job.NewInvalidOnboardingDetectionJob,
		job.NewUserOnboardingDataPopulationJob,
		job.NewCcUpdateCustomerDetailsAtM2PJob,
		job.NewBlockAndReissueNewCCJob,
		job.NewRegisterCustomerForCCJob,
		job.NewUpdateCardDetailsAtBankJob,
		job.NewDcRenewCardJob,
		job.NewDcBlockCardJob,
		job.NewImitateBlockAndReissueNewCCJob,
		job.NewActivateCreditCardJob,
		job.NewGenerateStatementJob,
		job.NewProcessCardTransaction,
		job.NewDeleteDcDynamoDataJob,
		job.NewChangeDcPinValidationParamsJob,
		job.NewGetActorCcShippingAddressJob,
		job.NewTriggerWelcomeRewardsJob,
		job.NewDcPhysicalCardChargesReversalJob,
		job.NewCCReconJob,
		job.NewRotateTokenizerKeysJob,
		job.NewTriggerFeeWaiverJob,
		job.NewTriggerProcessCcTxnWfJob,
		job.NewBypassCcOnboardingJob,
		job.NewDeleteCardRequestJob,
		job.NewSendInAppNotificationJob,
		job.NewSendDpdEventsJob,
		job.NewInitiateAmcReportGeneration,
		job.NewCorrectForexRefundDbJob,
		job.NewDeleteAmcCardRequestJob,
		job.NewVisaApiTestJob,
		job.NewCollectDCIssuanceApiTestJob,
		job.NewInvestigateAwbMismatchJob,
		job.NewDcOrderPhysicalCardWithChargesJob,
		job.NewFetchDueJob,
		job.NewCreatVendorRepaymentRecordJob,
		job.NewCcSmsScriptJob,
		job.NewCorrectForexTcsClashJob,
		job.NewProcessKycExpiryForCcJob,
		job.NewEnquireDcChargesCollectionStsJob,
		job.NewPublishCcDelStateUpdateEventJob,
		wire.NewSet(events.NewRudderStackBroker,
			wire.Bind(new(events.Broker),
				new(*events.RudderStackBroker))),
		job.NewRegistry,
	)
	return &job.Registry{}, nil
}

func FireflyFaasExecutorProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) (faas.FaaSExecutor, error) {
	return epifitemporalfaas.NewFaaSExecutor(ctx, awsSqs.NewFromConfig(awsConf), namespace.Firefly, conf.ProcrastinatorWorkflowPublisher)
}
