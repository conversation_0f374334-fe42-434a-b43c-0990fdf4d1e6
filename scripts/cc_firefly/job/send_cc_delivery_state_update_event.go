package job

import (
	"context"
	"encoding/json"
	"fmt"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"google.golang.org/grpc/metadata"

	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
)

type PublishCcDelStateUpdateEventJob struct {
	ccVgClient ccVgPb.CreditCardClient
}

func NewPublishCcDelStateUpdateEventJob(ccVgClient ccVgPb.CreditCardClient) *PublishCcDelStateUpdateEventJob {
	return &PublishCcDelStateUpdateEventJob{
		ccVgClient: ccVgClient,
	}
}

// Sample Args: {/"StateUpdateTo/":/"DELIVERED/",/"VendorUserId/":/"ext_user_id/",/"CarrierName/":/"INDIAPOST/",/"TrackingUrl/":/"tracking_url/",/"ActorId/":/"actor_id_1/"}
type PublishCcDelStateUpdateEventJobArgs struct {
	StateUpdateTo string `json:"StateUpdateTo"`
	VendorUserId  string `json:"VendorUserId"`
	ActorId       string `json:"ActorId"`
	CarrierName   string `json:"CarrierName"`
	TrackingUrl   string `json:"TrackingUrl"`
}

func (c *PublishCcDelStateUpdateEventJob) Run(ctx context.Context, argString string) error {
	args := &PublishCcDelStateUpdateEventJobArgs{}
	err := json.Unmarshal([]byte(argString), args)
	if err != nil {
		return fmt.Errorf("unmarshal json fail: %w", err)
	}
	actorCtx := epificontext.WithTraceId(ctx, metadata.MD{})
	actorCtx = epificontext.CtxWithActorId(actorCtx, args.ActorId)

	vgResp, err := c.ccVgClient.UpdateCreditCardDeliveryState(actorCtx, &ccVgPb.UpdateCreditCardDeliveryStateRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_SAVEN,
		},
		Carrier:       args.CarrierName,
		DeliveryState: ccVgPb.DeliveryState(ccVgPb.DeliveryState_value[args.StateUpdateTo]),
		TrackingUrl:   args.TrackingUrl,
		UserId:        args.VendorUserId,
	})
	if te := epifigrpc.RPCError(vgResp, err); te != nil {
		return fmt.Errorf("update delivery state fail: %w", te)
	}
	logger.Info(ctx, "update delivery state success")
	return nil
}
