// nolint:dupl,unused,deadcode,gosec,funlen,depguard
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	awsConf "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	stsv2 "github.com/aws/aws-sdk-go-v2/service/sts"
	awsSess "github.com/aws/aws-sdk-go/aws/session"
	"github.com/opensearch-project/opensearch-go"

	// nolint: depguard
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/jonboulle/clockwork"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	gormv2 "gorm.io/gorm"
	"gorm.io/plugin/dbresolver"

	sgApplicantPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/applicant"
	sgMatrixPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	osPkg "github.com/epifi/gamma/pkg/opensearch"
	"github.com/epifi/gamma/testing/integration/app/inhousevkyc"
	vkyccallConf "github.com/epifi/gamma/vkyccall/config"
	vkyccallGenconf "github.com/epifi/gamma/vkyccall/config/genconf"

	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/developer/devcache"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	sqsPkg "github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	vkycCallDao "github.com/epifi/gamma/vkyccall/dao"

	actorDao "github.com/epifi/gamma/actor/dao"
	dao3 "github.com/epifi/gamma/alfred/dao"
	"github.com/epifi/gamma/aml/dao/impl"
	balancePb "github.com/epifi/gamma/api/accounts/balance"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/employment"
	feSaClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	"github.com/epifi/gamma/api/frontend/genie"
	kycPb "github.com/epifi/gamma/api/kyc"
	kycAgent "github.com/epifi/gamma/api/kyc/agent"
	"github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/pan"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/risk/redlist"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	userLocation "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/ckyc"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	vgLivenessPb "github.com/epifi/gamma/api/vendorgateway/liveness"
	vgLoc "github.com/epifi/gamma/api/vendorgateway/location"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgAuthPb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	panPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/api/vendorgateway/phonenetwork"
	vgVKYC "github.com/epifi/gamma/api/vendorgateway/vkyc"
	"github.com/epifi/gamma/api/vendornotification/openbanking/kyctypechange/federal"
	livenessGenConf "github.com/epifi/gamma/auth/config/genconf"
	authDao "github.com/epifi/gamma/auth/dao"
	complianceDao "github.com/epifi/gamma/bankcust/compliance/dao"
	"github.com/epifi/gamma/bankcust/config/genconf"
	dao5 "github.com/epifi/gamma/bankcust/dao"
	dao6 "github.com/epifi/gamma/kyc/vkyc/dao"
	livCfg "github.com/epifi/gamma/liveness/config"
	livDao "github.com/epifi/gamma/liveness/dao"
	epanDao "github.com/epifi/gamma/pan/dao"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/scripts/onboarding_god_script/config"
	dao9 "github.com/epifi/gamma/user/contact/dao"
	"github.com/epifi/gamma/user/dao"
	dao8 "github.com/epifi/gamma/user/location/dao"
	dao2 "github.com/epifi/gamma/user/onboarding/dao"
	dao7 "github.com/epifi/gamma/user/shipping_preference/dao"
	tempuserdao "github.com/epifi/gamma/user/temp_user_dao"
	ipDao "github.com/epifi/gamma/vendordata/ip/dao"
)

var (
	inputJob = flag.String("JobName", "", "job name, refer to jobNames for accepted values")
	jobArgs1 = flag.String("Args1", "", "input args for the job (refer to job requirements)")
	jobArgs2 = flag.String("Args2", "", "input args for the job (refer to job requirements)")
)

type JobType uint32

const (
	slackChannelId                 = "C03A99M9VK2"
	JobUnspecified         JobType = 0
	JobAccountCreationBulk JobType = 1
	JobSample              JobType = 2
	JobUNNameCheck         JobType = 3
	JobDedupeCheck         JobType = 4

	// JobUNNameCheckSMS sends SMS to users who were stuck on UN Name Check
	JobUNNameCheckSMS JobType = 8

	JobFetchAddressForIdentifiersRisk JobType = 9
	JobBulkProcessLiveness            JobType = 12
	JobFixLivenessVideoURL            JobType = 13
	JobSearchCompanyName              JobType = 14
	JobGetAccountNumber               JobType = 15
	JobFetchPANName                   JobType = 17
	JobUnblockAddressStuckUser        JobType = 18
	JobUnblockUsersStuckInAFU         JobType = 19
	JobBulkPassLiveness               JobType = 20
	JobBulkRetryLiveness              JobType = 21
	JobUANCheck                       JobType = 24
	JobRunPanNameCheck                JobType = 25
	JobSkipAddMoney                   JobType = 27
	JobVKYCCallPrompt                 JobType = 29
	JobScreenerPOC                    JobType = 31
	JobKarzaEmailIdFraudCheck         JobType = 32
	JobSeonFraudPOC                   JobType = 33
	JobGetStuckUsers                  JobType = 34
	JobBackFillProfilePhoto           JobType = 36
	JobVKYCUtil                       JobType = 37
	JobBackFillAccessRevokeDetails    JobType = 38
	JobBackFillConsentsActorID        JobType = 39
	JobRedactESData                   JobType = 42
	JobBackfillKYCAddress             JobType = 44
	JobBackfillDeviceRegLocations     JobType = 46
	// JobPostAccountClosureFundTransferEmail job sends email to all the user shared by federal whose account closed and fund transfer by the federal.
	JobPostAccountClosureFundTransferEmail JobType = 47
	JobReadESData                          JobType = 48
	JobCategoriseDomains                   JobType = 51
	JobBankCustomerBackfill                JobType = 52
	JobFetchBAV                            JobType = 53
	JobPostpaidPOC                         JobType = 54
	JobAddUserGroupMappingVKYC             JobType = 55
	JobBackFillDomainDetailsType           JobType = 56
	JobDeleteOldReviewCases                JobType = 57
	JobUpdateFatherName                    JobType = 58
	JobBackfillData                        JobType = 59
	JobResetAddressStages                  JobType = 60
	AdHocUpdateUserPhone                   JobType = 61
	JobFirehoseFromActor                   JobType = 62
	JobUpdateRedListPhoneNumber            JobType = 63
	JobBackFillSavingsAccConstraints       JobType = 64
	JobBackfillMaxMindDataDump             JobType = 65
	JobBackFillPinCodeFromLatLong          JobType = 66
	JobLatLongToLocationToken              JobType = 67
	JobCreateActor                         JobType = 68
	JobBackFillPostalCode                  JobType = 69
	JobSuccessfulBavForTickets             JobType = 70
	JobFetchAccountDetails                 JobType = 71
	JobActorFromFirehose                   JobType = 72
	JobVkycFedCallback                     JobType = 73
	JobUserIdFromActorId                   JobType = 74
	JobAppVersionCheckFalsePass            JobType = 75
	JobFetchPayuAffluenceScores            JobType = 76
	JobPinCodeSeedBackfill                 JobType = 77
	JobBackfillOnboardingDetails           JobType = 78
	JobRunHunterApi                        JobType = 79
	JobClosePIs                            JobType = 80
	JobGetUserDetail                       JobType = 81
	JobDeleteQueueElements                 JobType = 82
	JobBackfillUserProfileImage            JobType = 83
	JobBackfillContactProperties           JobType = 84
	JobBackfillGenderUserProfile           JobType = 85
	JobDeleteUsers                         JobType = 86
	JobGetOnbDetails                       JobType = 87
	JobDeleteOnboardedUser                 JobType = 88
	JobBackfillGoodUserAssociates          JobType = 89
	JobDeleteAdhaarLinkageData             JobType = 90
	JobMaintainAdhaarPanStatus             JobType = 91
	JobBackfillGmailPanNameMatchScore      JobType = 92
	JobGRPCAPICaller                       JobType = 93
	JobBackFillGeoIpData                   JobType = 94
	JobUpdateKycStatus                     JobType = 95
	JobSyncOnbLending                      JobType = 96
	JobReadPersistentQueue                 JobType = 97
	JobBackFillSavingsAccount              JobType = 98
	JoUpdateServiceRequest                 JobType = 99
	JobFetchWebUsersDetails                JobType = 100
	JobBackfillUserGroupMappings           JobType = 101
	JobBackFillSavingsOperStatus           JobType = 102
	JobUpdateKycLevel                      JobType = 103
	JobUpdateChequebookStatus              JobType = 104
	JobFailCifCreation                     JobType = 105
	JobFailSavingAccountState              JobType = 106
	JobUNNameBlacklistCheck                JobType = 107
	JobUpdateSavingAccountSku              JobType = 108
	JobCurrentOnboardingStage              JobType = 109
	JobDowngradeKYCLevel                   JobType = 110
	JobVKYCAgentAuditorCallback            JobType = 111
	JobUpdateVkycApproved                  JobType = 112
	JobTriggerNameDobValidation            JobType = 113
	JobBackfillBankCustReStructured        JobType = 114
	JobCreateAccountPi                     JobType = 115
	JobUpdatePeriodicKycEntity             JobType = 116
	JobFailCifCreationRpc                  JobType = 117
	JobPermanentDeviceDeactivation         JobType = 118
	JobKYCCompliancesTableDump             JobType = 119
	JobEkycYobDobReset                     JobType = 120
	JobModifyAcquisitionInfo               JobType = 121
	JobGetOtpBkycAgent                     JobType = 122
	JobGetParentsName                      JobType = 123
	JobSyncPeriodicKYCDue                  JobType = 124
	JobBackfillUserActorIds                JobType = 125
	JobUpdatePgDbKyc                       JobType = 126
	JobREKYCUtility                        JobType = 127
	JobGetPeriodicKYCDetails               JobType = 128
	JobUndeleteActor                       JobType = 129
	JobDeleteDeviceRegistrationCache       JobType = 130
	JobUploadEpanToSFTP                    JobType = 131
	JobMailSaClosureRequests               JobType = 132
	JobAccessS3File                        JobType = 133
	JobGetBankCustomer                     JobType = 134
	JobFailEPANAttempt                     JobType = 135
	JobBackfillUserActorIdsVerify          JobType = 136
	JobSignOutUser                         JobType = 137
	JobGetAccessRevokeDetails              JobType = 138
	JobGetCkycNumber                       JobType = 139
	JobUpdateOccupationAtVendor            JobType = 140
	JobActorIdFromCustId                   JobType = 141
	JobReRegisterUserForVKYC               JobType = 142
	JobDeleteUserDetails                   JobType = 143
	JobInsertDeviceRegistrations           JobType = 144
	JobPerformBiometricKYC                 JobType = 145
	JobResetEKYCDetailsInREKYC             JobType = 146
	JobGetPanNumber                        JobType = 147
	JobUpdateIncomeAtVendor                JobType = 148
	JobDevRegStatusAtVendor                JobType = 149
	JobDevRegCleanup                       JobType = 150
	JobCompareLivenessVideoCompression     JobType = 151
	JobUpdateAfuToProgress                 JobType = 152
	JobUpdateAfuToFailure                  JobType = 153
	JobDeleteDirtyAddressForIdentifiers    JobType = 154
	JobEvaluateKycMismatch                 JobType = 155
	JobCacheActions                        JobType = 156
	JobSetIsAllowedForAmbAccountFlag       JobType = 157
	JobValidateGetMinUserValue             JobType = 158 // delete after 24 oct 2024
	JobFixAddressData                      JobType = 159
	JobUpdateAccessRevokeStatus                    = 160
	JobGetPanName                          JobType = 161
	JobUserKycInfo                         JobType = 162
	JobUpdateAuditorLocationCheckStatus    JobType = 163
	JobDedupeDataVerificationDetail        JobType = 164
	JobGetNROnboardingLogs                 JobType = 165
	AdHocUpdateKycName                     JobType = 166
	JobGetDobReconciliationActors          JobType = 167
	JobInHouseVKYCTesting                  JobType = 168
	JobOnbUserDropOffAlerts                JobType = 169
	JobAccessS3Folder                      JobType = 170
	JobDeleteEmploymentData                JobType = 171
	JobMatrixTesting                       JobType = 172
	JobGetCancelledCheque                  JobType = 173
	JobDownloadFailedLivenessVideos        JobType = 174
	JobAdHocUpdateUserEmail                JobType = 175
	JobAccountClosureBalTransferEnquiry    JobType = 176
)

var (
	jobNames = map[string]JobType{
		// Args1: comma seperated actor ids
		"ACCOUNT_CREATION_BULK":                    JobAccountCreationBulk,
		"RESET_ADDRESS_STAGES":                     JobResetAddressStages,
		"SAMPLE":                                   JobSample,
		"PASS_UNNAME_CHECK_BULK":                   JobUNNameCheck,
		"REDO_DEDUPE_CHECK_BULK":                   JobDedupeCheck,
		"SMS_UN_NAME_CHECK":                        JobUNNameCheckSMS,
		"FETCH_ADDRESS_FOR_IDENTIFIERS":            JobFetchAddressForIdentifiersRisk,
		"PROCESS_LIVENESS_BULK":                    JobBulkProcessLiveness,
		"FIX_LIVENESS_VIDEO_URL":                   JobFixLivenessVideoURL,
		"SEARCH_COMPANY_NAME":                      JobSearchCompanyName,
		"FETCH_ACCOUNT_NUMBER":                     JobGetAccountNumber,
		"FETCH_PAN_NAME":                           JobFetchPANName,
		"UNBLOCK_ADDRESS_STUCK_USER":               JobUnblockAddressStuckUser,
		"UNBLOCK_USERS_STUCK_IN_AFU":               JobUnblockUsersStuckInAFU,
		"BULK_PASS_LIVENESS":                       JobBulkPassLiveness,
		"BULK_RETRY_LIVENESS":                      JobBulkRetryLiveness,
		"UAN_CHECK":                                JobUANCheck,
		"PAN_NAME_CHECK_BULK":                      JobRunPanNameCheck,
		"SKIP_ADD_MONEY":                           JobSkipAddMoney,
		"VKYC_CALL_PROMPT":                         JobVKYCCallPrompt,
		"SCREENER_POC":                             JobScreenerPOC,
		"KARZA_EMAIL_ID_FRAUD_CHECK":               JobKarzaEmailIdFraudCheck,
		"SEON_FRAUD_POC":                           JobSeonFraudPOC,
		"GET_STUCK_USERS":                          JobGetStuckUsers,
		"BACK_FILL_USER_PROFILE_PHOTO":             JobBackFillProfilePhoto,
		"VKYC_UTILITY":                             JobVKYCUtil,
		"BACK_FILL_ACCESS_REVOKE_DETAILS":          JobBackFillAccessRevokeDetails,
		"BACK_FILL_CONSENTS_ACTOR_ID":              JobBackFillConsentsActorID,
		"REDACT_ES_DATA":                           JobRedactESData,
		"READ_ES_DATA":                             JobReadESData,
		"BACKFILL_KYC_ADDRESS":                     JobBackfillKYCAddress,
		"BACKFILL_DEVICE_REG_LOCATIONS":            JobBackfillDeviceRegLocations,
		"POST_ACCOUNT_CLOSURE_FUND_TRANSFER_EMAIL": JobPostAccountClosureFundTransferEmail,
		"CATEGORISE_DOMAINS":                       JobCategoriseDomains,
		"BANK_CUSTOMER_BACKFILL":                   JobBankCustomerBackfill,
		"FETCH_BAV":                                JobFetchBAV,
		"POSTPAID_POC":                             JobPostpaidPOC,
		"ADD_USER_GROUP_MAPPING_VKYC":              JobAddUserGroupMappingVKYC,
		"BACKFILL_DOMAIN_DETAILS_TYPE":             JobBackFillDomainDetailsType,
		"DELETE_OLD_REVIEW_CASES":                  JobDeleteOldReviewCases,
		"UPDATE_FATHER_NAME":                       JobUpdateFatherName,
		"JOB_BACKFILL_DATA":                        JobBackfillData,
		"UPDATE_USER_PHONE":                        AdHocUpdateUserPhone,
		"FIREHOSE_FROM_ACTOR":                      JobFirehoseFromActor,
		"UPDATE_REDLIST_PHONE_NUMBER":              JobUpdateRedListPhoneNumber,
		"BACKFILL_SAVINGS_ACC_CONSTRAINTS":         JobBackFillSavingsAccConstraints,
		"BACKFILL_PIN_CODE_FOR_LATLONG":            JobBackFillPinCodeFromLatLong,
		"BACKFILL_MAX_MIND_IP_ADDR_DUMP":           JobBackfillMaxMindDataDump,
		"ADHOC_CREATE_ACTOR":                       JobCreateActor,
		"BACKFILL_POSTAL_CODE":                     JobBackFillPostalCode,
		"SUCCESSFUL_TICKET_BAV":                    JobSuccessfulBavForTickets,
		"BACKFILL_LOCATION_TOKEN":                  JobLatLongToLocationToken,
		"JOB_FETCH_ACCOUNT_DETAILS":                JobFetchAccountDetails,
		"ACTOR_FROM_FIREHOSE":                      JobActorFromFirehose,
		"VKYC_FED_CALL_BACK":                       JobVkycFedCallback,
		"USER_ID_FROM_ACTOR_ID":                    JobUserIdFromActorId,
		"APP_VERSION_CHECK_FALSE_PASS":             JobAppVersionCheckFalsePass,
		"JOB_FETCH_PAYU_AFFLUENCE_SCORES":          JobFetchPayuAffluenceScores,
		"PINCODE_SEED_BACKFILL":                    JobPinCodeSeedBackfill,
		"RUN_HUNTER_API":                           JobRunHunterApi,
		"BACKFILL_ONBOARDING_DETAILS":              JobBackfillOnboardingDetails,
		"GRPC_API_CALLER":                          JobGRPCAPICaller,
		"CLOSE_PI":                                 JobClosePIs,
		"GET_USER_DETAIL":                          JobGetUserDetail,
		"DELETE_QUEUE_ELEMENTS":                    JobDeleteQueueElements,
		"BACKFILL_USER_PROFILE_IMAGE":              JobBackfillUserProfileImage,
		"JOB_BACKFILL_CONTACT_PROPERTIES":          JobBackfillContactProperties,
		"BACKFILL_GENDER_USER_PROFILE":             JobBackfillGenderUserProfile,
		"JOB_DELETE_USERS":                         JobDeleteUsers,
		"JOB_GET_ONB_DETAILS":                      JobGetOnbDetails,
		"JOB_DELETE_ONBOARDED_USER":                JobDeleteOnboardedUser,
		"BACKFILL_GOOD_USER_ASSOCIATE":             JobBackfillGoodUserAssociates,
		"JOB_DELETE_ADHAAR_LINKAGE_DATA":           JobDeleteAdhaarLinkageData,
		"MAINTAIN_ADHAAR_PAN_STATUS":               JobMaintainAdhaarPanStatus,
		"JOB_BACKFILL_GMAIL_PANNAMEMATCH_SCORE":    JobBackfillGmailPanNameMatchScore,
		"BACKFILL_GEO_IP_DATA":                     JobBackFillGeoIpData,
		"JOB_UPDATE_KYC_STATUS":                    JobUpdateKycStatus,
		"JOB_SYNC_ONB_LENDING":                     JobSyncOnbLending,
		"JOB_READ_PERSISTENT_QUEUE":                JobReadPersistentQueue,
		"JOB_BACKFILL_SAVINGS_ACCOUNT":             JobBackFillSavingsAccount,
		"JOB_UPDATE_SERVICE_REQUEST":               JoUpdateServiceRequest,
		"JOB_FETCH_WEB_USERS_DETAILS":              JobFetchWebUsersDetails,
		"JOB_BACKFILL_USER_GROUP_MAPPINGS":         JobBackfillUserGroupMappings,
		"JOB_SAV_OPER_BACKFILL":                    JobBackFillSavingsOperStatus,
		"UPDATE_KYC_LEVEL":                         JobUpdateKycLevel,
		"JOB_UPDATE_CHEQUEBOOK_STATUS":             JobUpdateChequebookStatus,
		"JOB_FAIL_CIF_CREATION":                    JobFailCifCreation,
		"JOB_FAIL_SAVING_ACCOUNT_STATE":            JobFailSavingAccountState,
		"JOB_UN_NAME_BLACKLIST_CHECK":              JobUNNameBlacklistCheck,
		"JOB_UPDATE_SAVING_ACCOUNT_SKU":            JobUpdateSavingAccountSku,
		"JOB_GET_CURRENT_ONBOARDING_STAGE":         JobCurrentOnboardingStage,
		"JOB_DOWNGRADE_KYC_LEVEL":                  JobDowngradeKYCLevel,
		"JOB_VKYC_AGENT_AUDITOR_CALLBACK":          JobVKYCAgentAuditorCallback,
		"JOB_UPDATE_VKYC_APPROVED":                 JobUpdateVkycApproved,
		"JOB_TRIGGER_NAMEDOBVALIDATION":            JobTriggerNameDobValidation,
		"JOB_BCKFILL_BANK_CUST_RE_STRUCTURED":      JobBackfillBankCustReStructured,
		"JOB_CREATE_ACCOUNT_PI":                    JobCreateAccountPi,
		"JOB_UPDATE_PERIODIC_KYC_ENTITY":           JobUpdatePeriodicKycEntity,
		"JOB_FAIL_CIF_CREATION_RPC":                JobFailCifCreationRpc,
		"JOB_PERMANENT_DEVICE_DEACTIVATION":        JobPermanentDeviceDeactivation,
		"JOB_KYC_COMPLIANCE_DUMP":                  JobKYCCompliancesTableDump,
		"JOB_EKYC_YOB_DOB_RESET":                   JobEkycYobDobReset,
		"MODIFY_ACQUISITION_INFO":                  JobModifyAcquisitionInfo,
		"JOB_GET_BKYC_AGENT_OTP":                   JobGetOtpBkycAgent,
		"JOB_GET_PARENTS_NAME":                     JobGetParentsName,
		"JOB_SYNC_PERIODIC_KYC_DUE":                JobSyncPeriodicKYCDue,
		"BACKFILL_USER_DETAILS_ACTOR_IDS":          JobBackfillUserActorIds,
		"UPDATE_PGDB_KYC":                          JobUpdatePgDbKyc,
		"JOB_REKYC_UTILITY":                        JobREKYCUtility,
		"JOB_GET_PERIODIC_KYC_DETAILS":             JobGetPeriodicKYCDetails,
		"JOB_UNDELETE_ACTOR":                       JobUndeleteActor,
		"JOB_DELETE_DEVICE_REGISTRATION_CACHE":     JobDeleteDeviceRegistrationCache,
		"JOB_UPLOAD_EPAN_TO_SFTP":                  JobUploadEpanToSFTP,
		"JOB_MAIL_SA_CLOSURE_REQUESTS":             JobMailSaClosureRequests,
		"JOB_ACCESS_S3_FILE":                       JobAccessS3File,
		"JOB_ACCESS_S3_FOLDER":                     JobAccessS3Folder,
		"JOB_GET_BANKCUSTOMER":                     JobGetBankCustomer,
		"JOB_FAIL_EPAN_ATTEMPT":                    JobFailEPANAttempt,
		"VERIFY_USERS":                             JobBackfillUserActorIdsVerify,
		"SIGN_OUT_USER":                            JobSignOutUser,
		"ACCESS_REBOKE_DETAILS":                    JobGetAccessRevokeDetails,
		"GET_CKYC_NUMBER":                          JobGetCkycNumber,
		"UPDATE_OCCUPATION_AT_VENDOR":              JobUpdateOccupationAtVendor,
		"JOB_ACTOR_ID_FROM_CUST_ID":                JobActorIdFromCustId,
		"RE_REGISTER_USER_FOR_VKYC":                JobReRegisterUserForVKYC,
		"DELETE_USER_DETAILS":                      JobDeleteUserDetails,
		"INSERT_DEVICE_REGISTRATIONS":              JobInsertDeviceRegistrations,
		"PERFORM_BKYC":                             JobPerformBiometricKYC,
		"JOB_RESET_EKYC_DETAILS_IN_REKYC":          JobResetEKYCDetailsInREKYC,
		"GET_PAN_NUMBER":                           JobGetPanNumber,
		"UPDATE_INCOME_AT_VENDOR":                  JobUpdateIncomeAtVendor,
		"DEVREG_STATUS":                            JobDevRegStatusAtVendor,
		"DEVREG_CLEANUP":                           JobDevRegCleanup,
		"COMPARE_LIVENESS_VIDEO_COMPRESSION":       JobCompareLivenessVideoCompression,
		"UPDATE_AFU_TO_PROGRESS":                   JobUpdateAfuToProgress,
		"UPDATE_AFU_TO_FAILURE":                    JobUpdateAfuToFailure,
		"JOB_DELETE_DIRTY_LOCATION_RECORDS":        JobDeleteDirtyAddressForIdentifiers,
		"JOB_FIX_ADDRESS_DATA":                     JobFixAddressData,
		"JOB_EVALUATE_KYC_MISMATCH":                JobEvaluateKycMismatch,
		"CACHE_ACTIONS":                            JobCacheActions,
		"JOB_SET_IS_ALLOWED_FOR_AMB_ACCOUNT_FLAG":  JobSetIsAllowedForAmbAccountFlag,
		"JOB_VALIDATE_MIN_USER_VALUE":              JobValidateGetMinUserValue,
		"JOB_UPDATE_ACCESS_REVOKE_STATUS":          JobUpdateAccessRevokeStatus,
		"JOB_GET_PAN_NAME":                         JobGetPanName,
		"JOB_USER_KYC_INFO":                        JobUserKycInfo,
		"JOB_UPDATE_AUDITOR_LOCATION_CHECK_STATUS": JobUpdateAuditorLocationCheckStatus,
		"JOB_DEDUPE_DATA_VERIFICATION_DETAIL":      JobDedupeDataVerificationDetail,
		"GET_NR_ONBOARDING_LOGS":                   JobGetNROnboardingLogs,
		"ADHOC_UPDATE_KYC_NAME":                    AdHocUpdateKycName,
		"GET_DOB_RECONCILIATION_ACTORS":            JobGetDobReconciliationActors,
		"JOB_IN_HOUSE_VKYC_TESTING":                JobInHouseVKYCTesting,
		"JOB_ONB_USER_DROP_OFF_ALERTS":             JobOnbUserDropOffAlerts,
		"JOB_DELETE_EMPLOYMENT_DATA":               JobDeleteEmploymentData,
		"JOB_MATRIX_TESTING":                       JobMatrixTesting,
		"GET_CANCELLED_CHEQUE":                     JobGetCancelledCheque,
		"DOWNLOAD_FAILED_LIVENESS_VIDEOS":          JobDownloadFailedLivenessVideos,
		"UPDATE_USER_EMAIL":                        JobAdHocUpdateUserEmail,
		"ACCOUNT_CLOSURE_BAL_TRANSFER_ENQUIRY":     JobAccountClosureBalTransferEnquiry,
	}
)

// JobRequest data that is sent to each job for processing
type JobRequest struct {
	Job   JobType
	Args1 string
	Args2 string
}

type JobProcessor interface {
	DoJob(context.Context, *JobRequest) error
}

//nolint:funlen
func main() {
	_ = os.Setenv("DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP", "TRUE")

	flag.Parse()
	_, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(cfg.DevelopmentEnv)
	defer func() { _ = logger.Log.Sync() }()

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		defer os.Exit(1)
		return
	}
	dbV2, err1 := storageV2.NewGormDB(conf.EpifiDb)
	if err1 != nil {
		logger.ErrorNoCtx("Failed to load DB", zap.Error(err1))
		defer os.Exit(1)
		return
	}
	var simDB *gormv2.DB
	if conf.SimulatorDb != nil {
		simDB, err1 = storageV2.NewGormDB(conf.SimulatorDb)
		if err1 != nil {
			logger.ErrorNoCtx("Failed to load DB", zap.Error(err1))
			defer os.Exit(1)
			return
		}
	}
	storageV2.InitDefaultCRDBTransactionExecutor(dbV2)

	dbActor, err2 := storageV2.NewGormDB(conf.ActorPgdb)
	if err2 != nil {
		logger.ErrorNoCtx("Failed to load DB", zap.Error(err2))
		defer os.Exit(1)
		return
	}
	var (
		pdbV2        *gormv2.DB
		errPdb       error
		nudgepdbV2   *gormv2.DB
		bankCustPgdb *gormv2.DB
		kycPgdb      *gormv2.DB
	)
	if conf.EpifiPgdb != nil {
		var dbConf *cfg.DB
		switch conf.UseVendorDataPgdb {
		case true:
			dbConf = conf.VendorDataPgdb
		case false:
			dbConf = conf.EpifiPgdb
		}
		pdbV2, errPdb = storageV2.NewGormDB(dbConf)
		if errPdb != nil {
			logger.ErrorNoCtx("failed to load postgress db", zap.Error(errPdb))
			defer os.Exit(1)
			return
		}
	} else {
		logger.InfoNoCtx("skipping pg db setup as missing config")
	}
	if conf.BankCustomerPgdb != nil {
		bankCustPgdb, err = storageV2.NewGormDB(conf.BankCustomerPgdb)
		if err != nil {
			logger.ErrorNoCtx("failed to load bankcust postgress db", zap.Error(errPdb))
			defer os.Exit(1)
			return
		}
	} else {
		logger.InfoNoCtx("skipping pg db setup as missing config")
	}
	kycPgdb, err = storageV2.NewGormDB(conf.KycPgdb)
	if err != nil {
		logger.ErrorNoCtx("failed to load kyc postgress db", zap.Error(errPdb))
		defer os.Exit(1)
		return
	}

	verifyPgdb, err := storageV2.NewGormDB(conf.VerifiPGDB)
	if err != nil {
		logger.ErrorNoCtx("failed to load Verifi PGDB", zap.Error(err))
		defer os.Exit(1)
		return
	}

	timeClient := datetime.NewDefaultTime()
	onbDao := dao2.NewOnboardingCrdb(dbV2, timeClient)
	vkycNotificationKycDao := dao6.NewVKYCNotificationCRDB(dbV2)
	cf := changefeed.NewChangefeed(dbV2)
	idg := idgen.NewDomainIdGenerator(idgen.NewClock())
	epifiCrdbtxnExecutor := storageV2.NewCRDBIdempotentTxnExecutor(dbV2)
	bcDao := dao5.NewBankCustomerDaoCrdb(dbV2, idg, tempuserdao.NewTempUserDaoCrdb(dbV2, &NoTestCache{}), epifiCrdbtxnExecutor)
	userRedisClient := storage.NewRedisClient(conf.UserRedis.Options, conf.UserRedis.IsSecureRedis, false)
	userRedisCacheStorage := cache.NewRedisCacheStorage(userRedisClient)
	bankCustGenConf := &genconf.BankCustomersCacheConfig{}
	_ = bankCustGenConf.SetCacheTTL("24h", false, nil)
	_ = bankCustGenConf.SetIsCachingEnabled(true, false, nil)
	bcCacheDao := dao5.NewBankCustomerCache(
		bankCustGenConf,
		userRedisCacheStorage,
		bcDao,
	)
	userDao := dao.NewUserGormDao(dbV2, cf)
	actorDaoPgdb := actorDao.NewActorDaoPgdb(dbActor, idg, datetime.NewDefaultTime(), idgen.NewAttributedIdGen[*actorDao.ActorAttribute](idgen.NewClock()), nil, false)
	shippingPrefDao := dao7.NewShippingPreferenceVendorRequestCrdb(dbV2, idg)
	userLocDao := dao8.NewLocationDaoCrdb(dbV2)
	contactPropertiesPGDBDao := dao9.NewContactPropertiesPGDB(dbV2.Clauses(dbresolver.Use("user_properties_pgdb")))
	devRegDao := authDao.NewDeviceRegistrationDaoCrdb(dbV2)
	afuDao := authDao.NewAfuCrdbDao(dbV2, nil)
	authDevRegRedisClient := storage.NewRedisClientFromConfig(conf.AuthDevRegRedisOptions, conf.Tracing.Enable)
	devRegcacheStorage := cache.NewRedisCacheStorage(authDevRegRedisClient)
	otpDao := authDao.NewAuthDao(dbV2)
	deviceIntegrityResultDao := authDao.NewDeviceIntegrityResultDaoCrdb(dbV2)
	alfredDao := dao3.NewServiceRequestsDao(dbV2)
	ownershipDBMap := map[common.Ownership]*cfg.DB{
		common.Ownership_EPIFI_TECH: conf.EpifiDb,
	}
	// nolint: dogsled
	epifiDBResourceProvider, _, _, _ := storageV2.NewDBResourceProviderV2(ownershipDBMap, false, nil)
	amlScreeningAttemptDao := impl.NewCrdbScreeningAttemptsDao(epifiDBResourceProvider)
	kycComplianceDao := complianceDao.NewKYCComplianceDaoPGDB(bankCustPgdb)
	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	omegleConn := epifigrpc.NewConnByService(cfg.OMEGLE_SERVICE)
	defer epifigrpc.CloseConn(omegleConn)
	usersClient := user.NewUsersClient(userConn)
	onbClient := onbPb.NewOnboardingClient(userConn)
	userLocationClient := userLocation.NewLocationClient(userConn)
	userGroupClient := group.NewGroupClient(userConn)
	redListClient := redlist.NewRedListClient(userConn)
	obsClient := obfuscator.NewObfuscatorClient(userConn)
	userIntelClient := userintel.NewUserIntelServiceClient(userConn)
	bcConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bcConn)
	bcClient := bankcust.NewBankCustomerServiceClient(bcConn)
	productClient := product.NewProductClient(userConn)
	compClient := compliancePb.NewComplianceClient(userConn)
	omegleClient := omegle.NewOmegleClient(omegleConn)

	savConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savConn)
	savClient := savings.NewSavingsClient(savConn)
	extacctClient := extacct.NewExternalAccountsClient(savConn)
	operationalStatusClient := operStatusPb.NewOperationalStatusServiceClient(savConn)

	accConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accConn)
	operStatusClient := operStatusPb.NewOperationalStatusServiceClient(accConn)
	accountBalanceClient := balancePb.NewBalanceClient(accConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)

	kycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	defer epifigrpc.CloseConn(kycConn)
	kycClient := kycPb.NewKycClient(kycConn)
	vkycClient := vkyc.NewVKYCClient(kycConn)
	kycAgentClient := kycAgent.NewKycAgentServiceClient(kycConn)
	panClient := pan.NewPanClient(kycConn)
	docClient := docs.NewDocExtractionClient(kycConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	beCommsClient := comms.NewCommsClient(commsConn)

	cxConn := epifigrpc.NewConnByService(cfg.CX_SERVICE)
	ticketClient := ticket.NewTicketClient(cxConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	beNameMatchClient := namecheck.NewUNNameCheckClient(vgConn)
	ckycClient := ckyc.NewCKycClient(vgConn)
	phoneNetworkClient := phonenetwork.NewPhoneNetworkClient(vgConn)
	vgLocationClient := vgLoc.NewLocationClient(vgConn)
	vgDepositClient := vgDepositPb.NewDepositClient(vgConn)
	vgCustomerClient := vgCustomerPb.NewCustomerClient(vgConn)
	vgVkycClient := vgVKYC.NewVkycClient(vgConn)
	vgPanClient := panPb.NewPANClient(vgConn)
	vgAuthClient := vgAuthPb.NewVendorAuthClient(vgConn)
	vgAccountsClient := accounts.NewAccountsClient(vgConn)
	vgEkycClient := ekyc.NewEKYCClient(vgConn)

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	authClient := auth.NewAuthClient(authConn)
	authLocClient := location.NewLocationClient(authConn)
	authDevCacheClient := devcache.NewDevCacheClient(authConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	defer epifigrpc.CloseConn(celestialConn)
	celestialClient := celestial.NewCelestialClient(celestialConn)

	livenessConn := epifigrpc.NewConnByService(cfg.LIVENESS_SERVICE)
	defer epifigrpc.CloseConn(livenessConn)
	livenessClient := liveness.NewLivenessClient(livenessConn)

	employmentConn := epifigrpc.NewConnByService(cfg.EMPLOYMENT_SERVICE)
	defer epifigrpc.CloseConn(employmentConn)
	employmentClient := employment.NewEmploymentClient(employmentConn)

	vgEmploymentClient := vgEmploymentPb.NewEmploymentClient(vgConn)

	depositConn := epifigrpc.NewConnByService(cfg.DEPOSIT_SERVICE)
	defer epifigrpc.CloseConn(depositConn)
	depositClient := deposit.NewDepositClient(depositConn)

	feConn := epifigrpc.NewConnByService(cfg.FRONTEND_SERVICE)
	defer epifigrpc.CloseConn(feConn)

	aurorConn := epifigrpc.NewConnByServer(cfg.AUROR_SERVER)
	defer epifigrpc.CloseConn(aurorConn)

	genieClient := genie.NewGenieClient(feConn)
	vmConn := epifigrpc.NewConnByService(cfg.VENDORMAPPING_SERVICE)
	defer epifigrpc.CloseConn(vmConn)
	vendorMappingClient := vendormappingPb.NewVendorMappingServiceClient(vmConn)

	saClosureFeClient := feSaClosurePb.NewSavingsAccountClosureClient(feConn)

	livAttDao := livDao.NewLivenessDao(dbV2, nil, &livCfg.Config{
		Flags: &livCfg.Flags{
			DisableLivenessAttemptChangeFeed: true,
		},
	})

	cleanedInputJob := *inputJob
	fmt.Printf("\n JOB: '%v' \n", *inputJob)
	job := jobNames[cleanedInputJob]
	if job == JobUnspecified {
		fmt.Printf("\n INVALID INPUT JOB: '%v' \n", cleanedInputJob)
		defer os.Exit(1)
		return
	}
	currRedactor, host := getRedactorAndHost(conf)
	esClient, err := client(host, "", "")
	if err != nil {
		logger.ErrorNoCtx("new es impl error", zap.Error(err))
		defer os.Exit(1)
		return
	}
	openSearchClient := initOpenSearchClient(conf)
	vkycCustInfoDao := dao6.NewVKYCKarzaCustomerInfo(dbV2)
	vkycSummaryDao := dao6.NewVKYCSummaryDao(dbV2)
	epanAttemptsDao := epanDao.NewEPANAttemptCRDBDao(dbV2)
	vnConn := epifigrpc.NewConnByService(cfg.VENDOR_NOTIFI_SERVICE)
	vnClient := federal.NewKycTypeChangeClient(vnConn)
	defer epifigrpc.CloseConn(vnConn)

	piConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	piClient := piPb.NewPiClient(piConn)
	defer epifigrpc.CloseConn(piConn)

	geoIpNetDao := ipDao.NewGeoIpNetworkDaoPGDB(pdbV2)
	geoIpLocDao := ipDao.NewGeoIpLocationDaoPGDB(pdbV2)
	pQueueDao := persistentqueue.NewQueueDaoCRDB(dbV2)

	sgApiGwConn := epifigrpc.NewConnByService(cfg.SG_APIGATEWAY_SERVICE)
	sgOmegleClient := omegle.NewOmegleClient(sgApiGwConn)

	sess, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic("failed to create new aws session", zap.Error(err))
	}
	var s3Client s3.S3Client
	// s3Client is needed only in prod as of now
	if cfg.IsProdEnv(conf.Application.Environment) {
		s3Client = s3.NewClient(sess, conf.S3Conf.Bucket)
	}
	onboardingS3Client := s3.NewClient(sess, conf.S3Conf.BucketNames["OnboardingBucket"])
	snowflakeS3Client := s3.NewClient(sess, conf.S3Conf.SnowFlakeBucket)
	livenessS3Client := s3.NewClient(sess, conf.S3Conf.LivenessBucket)

	var sqsClient = sqsPkg.InitSQSClient(sess)
	fedVkycUpdPublisher := initPublisher(sqsClient, conf.FederalVkycUpdatePublisher)
	fileGenerationPublisher := initPublisher(sqsClient, conf.AmlFileGenerationPublisher)
	savingsAccountPIPublisher := initPublisher(sqsClient, conf.SavingsAccountPIPublisher)

	sgApplicationServiceConn := epifigrpc.NewConnByService(cfg.SG_APPLICATION_SERVICE)
	sgApplicantServiceClient := sgApplicantPb.NewApplicantServiceClient(sgApplicationServiceConn)
	sgMatrixConn := epifigrpc.NewConnByService(cfg.SG_MATRIX_SERVICE)
	sgMatrixClient := sgMatrixPb.NewMatrixClient(sgMatrixConn)

	livenessGenConf := &livenessGenConf.Config{}
	livenesConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	vgLivenessClient := vgLivenessPb.NewLivenessClient(livenesConn)
	vkycCallRedisClient := storage.NewRedisClientFromConfig(conf.VkycCallRedisOption, conf.Tracing.Enable)
	redisCacheStorage := cache.NewRedisCacheStorage(vkycCallRedisClient)
	vkyccallGenConf := &vkyccallGenconf.Config{}
	err = vkyccallGenConf.SetDefaultDataTTL(time.Hour*168, false, nil)
	if err != nil {
		logger.ErrorNoCtx("failed to set vkyc call DefaultDataTTL config", zap.Error(err))
	}
	err = vkyccallGenConf.Set(&vkyccallConf.Config{
		CustomerQueue: &vkyccallConf.CustomerQueueConfig{
			UserToCallDetailsKeyPrefix: "VCIP:usertocalldetails",
			CallIdToActorIdKeyPrefix:   "VCIP:callidtoactorid",
		},
	}, false, []string{"", ""})

	if err != nil {
		logger.ErrorNoCtx("failed to set vkyc call config", zap.Error(err))
	}
	vkycCallRedisDao := vkycCallDao.NewVkycCallRedisDao(redisCacheStorage, vkyccallGenConf, clockwork.NewRealClock())
	inhouseVkycDeps := inhousevkyc.NewInHouseVKYCDep(map[string]*gormv2.DB{
		"EpifiDb":     dbV2,
		"SimulatorDb": simDB,
	}, omegleClient, employmentClient, usersClient, sgOmegleClient)
	// ---- ADD YOUR JOB PROCESSOR HERE ----
	var jobProcessors = map[JobType]JobProcessor{
		JobAccountCreationBulk: &jobAccountCreationBulk{
			savClient:   savClient,
			actorClient: actorClient,
			userClient:  usersClient,
			onbClient:   onbClient,
			bcClient:    bcClient,
		},
		JobResetAddressStages: &ResetShippingAddressStages{
			userClient:         usersClient,
			onboardingDao:      onbDao,
			shippingPrefReqDao: shippingPrefDao,
		},
		JobSample: &jobSample{},
		JobUNNameCheck: &jobUNNameCheckBulk{
			onbClient: onbClient,
		},
		JobDedupeCheck: &RedoDedupeCheck{
			onbClient: onbClient,
		},
		JobUNNameCheckSMS: &jobUNNameCheckSMS{
			commsClient: beCommsClient,
			onbClient:   onbClient,
			actorClient: actorClient,
		},
		JobFetchAddressForIdentifiersRisk: &JobFetchAddressForIdentifiers{
			userLocationClient: userLocationClient,
			livClient:          livenessClient,
			onbClient:          onbClient,
		},
		JobBulkProcessLiveness: &JobProcessLivenessBulk{
			kycClient:      kycClient,
			livenessClient: livenessClient,
			onbClient:      onbClient,
		},
		JobFixLivenessVideoURL: &FixLivenessVideoURL{
			livDao: livAttDao,
		},
		JobSearchCompanyName: &SearchCompanyName{
			employmentClient: employmentClient,
		},
		JobGetAccountNumber: &FetchAccountNumber{
			savingsClient: savClient,
			bcClient:      bcClient,
		},
		JobFetchPANName: &FetchPANName{
			actorClient: actorClient,
			userClient:  usersClient,
		},
		JobUnblockAddressStuckUser: &UnblockAddressStuckUser{
			onbClient: onbClient,
			kycClient: kycClient,
		},
		JobUnblockUsersStuckInAFU: &UnblockUsersStuckInAFU{
			authClient: authClient,
		},
		JobBulkPassLiveness: &JobPassLivenessBulk{
			livenessClient: livenessClient,
			onbClient:      onbClient,
			pQueueDao:      pQueueDao,
		},
		JobBulkRetryLiveness: &JobRetryLivenessBulk{
			kycClient:      kycClient,
			livenessClient: livenessClient,
			onbClient:      onbClient,
		},
		JobUANCheck: &UanPresenceCheck{
			vgEmploymentClient: vgEmploymentClient,
		},
		JobRunPanNameCheck: &PanNameCheck{
			panClient:   panClient,
			onbClient:   onbClient,
			userClient:  usersClient,
			actorClient: actorClient,
			db:          dbV2,
		},
		JobSkipAddMoney: &SkipAddMoney{
			dbConn:    pdbV2,
			onbClient: onbClient,
		},
		JobVKYCCallPrompt: &VKYCCallPrompt{
			onboardingDao: onbDao,
			userDao:       userDao,
			actorDao:      actorDaoPgdb,
			dbConn:        dbV2,
			pgDB:          pdbV2,
			commsClient:   beCommsClient,
		},
		JobScreenerPOC: &ScreenerPOC{
			userClient:  usersClient,
			actorClient: actorClient,
			dbConn:      pdbV2,
		},
		JobKarzaEmailIdFraudCheck: &KarzaEmailIdFraudCheck{
			actorClient: actorClient,
			userClient:  usersClient,
			dbConn:      pdbV2,
			conf:        conf,
		},
		JobSeonFraudPOC: &SeonFraudPOC{
			actorClient: actorClient,
			userClient:  usersClient,
			dbConn:      pdbV2,
		},
		JobGetStuckUsers: &GetStuckUsers{
			db:            dbV2,
			onboardingDao: onbDao,
			onbClient:     onbClient,
			userClient:    usersClient,
		},
		JobBackFillProfilePhoto: &BackFillProfilePhoto{
			// db:         db,
			userClient: usersClient,
		},
		JobVKYCUtil: &VKYCUtility{
			dbConnV2:         dbV2,
			conf:             conf,
			vnClient:         vnClient,
			vkycCustInfoDao:  vkycCustInfoDao,
			userClient:       usersClient,
			actorClient:      actorClient,
			userCacheStorage: userRedisCacheStorage,
			bcClient:         bcClient,
			omegleClient:     omegleClient,
			onbClient:        onbClient,
		},
		JobBackFillAccessRevokeDetails: &BackFillAccessRevokeDetails{
			// dbConn: db,
		},
		JobBackFillConsentsActorID: &ConsentActorIDBackfill{
			db: dbV2,
		},
		JobRedactESData: &RedactESData{
			Conf:     conf,
			Redactor: currRedactor,
			Host:     host,
			ESClient: esClient,
		},
		JobBackfillKYCAddress: &BackfillKYCAddresses{
			// dbConn:      db,
			userDao:     userDao,
			actorClient: actorClient,
		},
		JobBackfillDeviceRegLocations: &BackfillDeviceRegLocations{
			onbDao:             onbDao,
			authClient:         authClient,
			userLocationClient: userLocationClient,
		},
		JobPostAccountClosureFundTransferEmail: &PostAccountClosureFundTransferEmail{
			conf:                conf,
			commsClient:         beCommsClient,
			savingsClient:       savClient,
			userClient:          usersClient,
			vkycNotificationDao: vkycNotificationKycDao,
			actorClient:         actorClient,
		},
		JobReadESData: &ReadESData{
			Conf:     conf,
			Redactor: currRedactor,
			Host:     host,
			ESClient: esClient,
			s3Client: s3Client,
		},
		JobCategoriseDomains: &CategoriseDomains{
			emplClient: employmentClient,
		},
		JobBankCustomerBackfill: &BankCustomerBackFill{
			userDao:     userDao,
			dbConn:      dbV2,
			actorClient: actorClient,
			userClient:  usersClient,
			bcClient:    bcClient,
			s3Client:    onboardingS3Client,
			onbClient:   onbClient,
		},
		JobFetchBAV: &JobFetchBankAccountVerification{
			db:              dbV2,
			savClient:       savClient,
			actorClient:     actorClient,
			exacctClient:    extacctClient,
			commsClient:     beCommsClient,
			authClient:      authClient,
			userClient:      usersClient,
			vgDepositClient: vgDepositClient,
			config:          conf,
			bcClient:        bcClient,
		},
		JobPostpaidPOC: &PostpaidPOC{
			dbConn:             pdbV2,
			actorClient:        actorClient,
			phoneNetworkClient: phoneNetworkClient,
		},
		JobAddUserGroupMappingVKYC: &AddUserGroupMappingVKYC{
			actorClient:     actorClient,
			usersClient:     usersClient,
			userGroupClient: userGroupClient,
		},
		JobBackFillDomainDetailsType: &BackFillDomainDetailsType{
			db: dbV2,
		},
		JobDeleteOldReviewCases: &DeleteOldReviewCases{
			dbConn: dbV2,
		},
		JobUpdateFatherName: &UpdateFatherName{
			usersClient: usersClient,
		},
		JobBackfillData: &BackfillData{
			dbConn:    dbV2,
			onbClient: onbClient,
			livAttDao: livAttDao,
		},
		AdHocUpdateUserPhone: &AdHocJobUpdateUserPhone{
			userClient: usersClient,
		},
		JobFirehoseFromActor: &FirehoseFromActor{
			dbConn: pdbV2,
		},
		JobUpdateRedListPhoneNumber: &UpdatePhoneNumberRedList{
			rlClient: redListClient,
			s3Client: onboardingS3Client,
		},
		JobBackFillSavingsAccConstraints: &BackFillSavingsAccountConstraints{
			db:            dbV2,
			savingsClient: savClient,
		},
		JobBackFillPinCodeFromLatLong: &BackFillPinCodeForLatLong{
			db:            dbV2,
			s3Client:      onboardingS3Client,
			authLocClient: authLocClient,
			locClient:     vgLocationClient,
			userLocClient: userLocationClient,
			usrLocDao:     userLocDao,
		},
		JobBackfillMaxMindDataDump: &BackFillMaxMindDataDump{
			db:        dbV2,
			s3Client:  onboardingS3Client,
			obsClient: obsClient,
		},
		JobCreateActor: &CreateActorJob{
			actorClient: actorClient,
		},
		JobBackFillPostalCode: &BackFillPostalCode{
			db:       dbV2,
			s3Client: onboardingS3Client,
		},
		JobSuccessfulBavForTickets: &SuccessfulBavForTickets{
			config:       conf,
			savClient:    savClient,
			actorClient:  actorClient,
			exacctClient: extacctClient,
			commsClient:  beCommsClient,
			ticketClient: ticketClient,
		},
		JobLatLongToLocationToken: &GenerateLocationToken{
			authLocClient: authLocClient,
			s3Client:      onboardingS3Client,
		},
		JobFetchAccountDetails: &FetchAccountDetails{
			dbConn:          dbV2,
			depositClient:   depositClient,
			authClient:      authClient,
			actorClient:     actorClient,
			userClient:      usersClient,
			vgDepositClient: vgDepositClient,
			savingsClient:   savClient,
			bcClient:        bcClient,
		},
		JobActorFromFirehose: &ActorFromFirehose{
			dbConn:              pdbV2,
			config:              conf,
			vendorMappingClient: vendorMappingClient,
		},
		JobVkycFedCallback: &VkycFedCallback{
			vnClient: vnClient,
		},
		JobUserIdFromActorId: &JobActorIdToUserId{
			actorClient: actorClient,
			s3Client:    onboardingS3Client,
		},
		JobAppVersionCheckFalsePass: &AppVersionCheckFalsePass{
			dbConn: dbV2,
			onbDao: onbDao,
		},
		JobFetchPayuAffluenceScores: &FetchPayuScores{
			dbConn:          pdbV2,
			userIntelClient: userIntelClient,
			s3Client:        onboardingS3Client,
		},
		JobPinCodeSeedBackfill: &PinCodeSeedBackfill{
			db:         dbV2,
			userLocDao: userLocDao,
		},
		JobRunHunterApi: &RunHunterApi{
			savingsClient:   savClient,
			userIntelClient: userIntelClient,
			actorClient:     actorClient,
			s3Client:        onboardingS3Client,
		},
		JobBackfillOnboardingDetails: &BackfillOnboardingDetails{
			dbConn:    dbV2,
			onbClient: onbClient,
			bcClient:  bcClient,
			onbDao:    onbDao,
		},
		JobGRPCAPICaller: &GRPCAPICaller{
			UserClient:              usersClient,
			OnbClient:               onbClient,
			LocClient:               userLocationClient,
			SavClient:               savClient,
			actorClient:             actorClient,
			kycClient:               kycClient,
			ckycClient:              ckycClient,
			vgNameMatchClient:       beNameMatchClient,
			vgCustClient:            vgCustomerClient,
			authClient:              authClient,
			livClient:               livenessClient,
			vendorAuthClient:        vgAuthClient,
			operationalStatusClient: operationalStatusClient,
			vgAccountsClient:        vgAccountsClient,
			vgEkycClient:            vgEkycClient,
			productClient:           productClient,
			genieClient:             genieClient,
			vkycClient:              vkycClient,
			panClient:               panClient,
			bcClient:                bcClient,
			vgCustomerClient:        vgCustomerClient,
			vgVkycClient:            vgVkycClient,
			vgPanClient:             vgPanClient,
			vgLocClient:             vgLocationClient,
			omegleClient:            omegleClient,
			kycAgentClient:          kycAgentClient,
		},
		JobClosePIs: &ClosePI{
			piClient: piClient,
		},
		JobGetUserDetail: &GetUserDetail{
			usersClient:    usersClient,
			vkycSummaryDao: vkycSummaryDao,
		},
		JobDeleteQueueElements: &DeleteQueueElements{
			dbConn: dbV2,
		},
		JobBackfillUserProfileImage: &BackfillImage{
			db:   dbV2,
			conf: conf,
		},
		JobBackfillContactProperties: &BackfillCustomerProperties{
			actorClient:           actorClient,
			onboardingClient:      onbClient,
			db:                    dbV2,
			contactPropertiesPGDB: contactPropertiesPGDBDao,
			config:                conf,
		},
		JobBackfillGenderUserProfile: &BackFillGender{
			bcClient:         bcClient,
			vgCustomerClient: vgCustomerClient,
			userClient:       usersClient,
			db:               dbV2,
			actorClient:      actorClient,
			authClient:       authClient,
			kycCLient:        kycClient,
			config:           conf,
		},
		JobDeleteUsers: &DeleteUsers{
			onbClient: onbClient,
		},
		JobGetOnbDetails: &OnbDetails{
			db:   dbV2,
			pgdb: pdbV2,
		},
		JobDeleteOnboardedUser: &DeleteOnboardedUser{
			authClient:         authClient,
			userClient:         usersClient,
			actorClient:        actorClient,
			bankCustomerClient: bcClient,
			employmentClient:   employmentClient,
			bcDao:              bcDao,
			onbDao:             onbDao,
			devRegDao:          devRegDao,
		},
		JobBackfillGoodUserAssociates: &BackfillGoodUserAssociates{
			pgdb:     nudgepdbV2,
			s3Client: onboardingS3Client,
			config:   conf,
		},
		JobDeleteAdhaarLinkageData: &DeleteAdhaarLinkageData{
			db: dbV2,
		},
		JobMaintainAdhaarPanStatus: &MaintainAdhaarPanStatus{
			db:              dbV2,
			userIntelClient: userIntelClient,
			bcClient:        bcClient,
			idgen:           idg,
			s3Client:        onboardingS3Client,
		},
		JobBackfillGmailPanNameMatchScore: &BackfillGmailPanNameMatchScore{
			db:        dbV2,
			OnbClient: onbClient,
			config:    conf,
			s3Client:  onboardingS3Client,
		},
		JobBackFillGeoIpData: &BackFillGeoIpData{
			s3Client: onboardingS3Client,
			locDao:   geoIpLocDao,
			netDao:   geoIpNetDao,
		},
		JobUpdateKycStatus: &UpdateKycStatus{
			s3Client:            snowflakeS3Client,
			bcClient:            bcClient,
			pgdb:                pdbV2,
			userClient:          usersClient,
			vgClient:            vgCustomerClient,
			fedVkycUpdPublisher: fedVkycUpdPublisher,
		},
		JobSyncOnbLending: &SyncOnbLending{
			onbClient: onbClient,
			onbDao:    onbDao,
		},
		JobReadPersistentQueue: &ReadPersistentQueue{
			pQueueDao: pQueueDao,
		},
		JobBackFillSavingsAccount: &SavingsAccountBackFill{
			dbConn:        dbV2,
			actorClient:   actorClient,
			savingsClient: savClient,
			config:        conf,
		},
		JoUpdateServiceRequest: &UpdateServiceRequest{
			conf: conf,
			db:   dbV2,
		},
		JobFetchWebUsersDetails: &FetchWebUsersDetails{
			empClient:   employmentClient,
			usersClient: usersClient,
			dbConn:      pdbV2,
			s3Client:    onboardingS3Client,
		},
		JobBackFillSavingsOperStatus: &OperStatusBackFill{
			dbConn:        dbV2,
			savingsClient: savClient,
			opsClient:     operStatusClient,
			config:        conf,
		},
		JobUpdateKycLevel: &UpdateKycLevel{
			bcClient:  bcClient,
			onbClient: onbClient,
		},
		JobUpdateChequebookStatus: &UpdateChequebookStatus{
			alfredDao:       alfredDao,
			celestialClient: celestialClient,
		},
		JobFailCifCreation: &FailCifCreationStatus{
			db: dbV2,
		},
		JobFailSavingAccountState: &FailSavingAccountState{
			savingsClient: savClient,
		},
		JobUNNameBlacklistCheck: &UNNameBlacklistCheck{
			s3Client:                onboardingS3Client,
			db:                      conf.EpifiDb,
			dbConn:                  dbV2,
			actorDao:                actorDaoPgdb,
			FileGenerationPublisher: fileGenerationPublisher,
			userDao:                 userDao,
			amlScreeningAttemptDao:  amlScreeningAttemptDao,
		},
		JobUpdateSavingAccountSku: &UpdateSavingAccountSku{
			savingsClient: savClient,
			bcClient:      bcClient,
		},
		JobDowngradeKYCLevel: &DowngradeKYCLevel{
			savingsClient: savClient,
			bcClient:      bcClient,
			userClient:    usersClient,
			vgClient:      vgCustomerClient,
			vkycClient:    vkycClient,
			s3Client:      onboardingS3Client,
			bankCustDao:   bcDao,
		},
		JobCurrentOnboardingStage: &CurrentOnbaordingStage{
			onbDao: onbDao,
		},
		JobVKYCAgentAuditorCallback: &VKYCAgentAuditorCallback{
			vkycClient: vkycClient,
		},
		JobUpdateVkycApproved: &UpdateVkycApproved{
			fedVkycUpdPublisher: fedVkycUpdPublisher,
			bcClient:            bcClient,
			db:                  dbV2,
		},
		JobTriggerNameDobValidation: &TriggerNameDobValidation{
			userClient:   usersClient,
			vgEkycClient: vgEkycClient,
		},
		JobBackfillBankCustReStructured: &BankCustReStructuredBackFill{
			dbConn:            dbV2,
			bankCustDao:       bcDao,
			bankCustomerCache: bcCacheDao,
		},
		JobCreateAccountPi: &createAccountPi{
			savingsClient:                   savClient,
			createSavingsAccountPIPublisher: savingsAccountPIPublisher,
		},
		JobUpdatePeriodicKycEntity: &UpdatePeriodicKycEntity{
			s3Client:         onboardingS3Client,
			kycComplianceDao: kycComplianceDao,
			idgen:            idg,
			bcDao:            bcDao,
			bcDB:             bankCustPgdb,
		},
		JobFailCifCreationRpc: &FailCifCreationStatusRpc{
			bcClient: bcClient,
		},
		JobPermanentDeviceDeactivation: &DeactivateDevice{
			authClient: authClient,
		},
		JobKYCCompliancesTableDump: &JobKYCCompDump{
			bcDB: bankCustPgdb,
		},
		JobEkycYobDobReset: &EkycYobDobReset{
			kycClient: kycClient,
		},
		JobModifyAcquisitionInfo: &ModifyAcquisitionInfo{
			userClient: usersClient,
		},
		JobGetOtpBkycAgent: &GetOtpBkycAgent{
			otpDao:         otpDao,
			kycAgentClient: kycAgentClient,
		},
		JobGetParentsName: &GetParentsName{
			usersClient: usersClient,
		},
		JobSyncPeriodicKYCDue: &SyncPeriodicKYCDue{
			s3Client: onboardingS3Client,
			pgdb:     pdbV2,
			bcClient: bcClient,
		},
		JobBackfillUserActorIds: &UserFillDetails{
			dbConnCRDB: dbV2,
			dbConnPGDB: dbActor,
			dao:        userDao,
		},
		JobBackfillUserActorIdsVerify: &UserCheckDetails{
			dbConnCRDB: dbV2,
		},
		JobUpdatePgDbKyc: &UpdateKycDb{
			kycDb: kycPgdb,
		},
		JobREKYCUtility: &REKYCUtility{
			dbConnV2:         dbV2,
			conf:             conf,
			userCacheStorage: userRedisCacheStorage,
			complianceDao:    kycComplianceDao,
			idgen:            idg,
			bcDao:            bcDao,
			pgdb:             bankCustPgdb,
		},
		JobGetPeriodicKYCDetails: &GetPeriodicKYCDetails{
			compClient: compClient,
			bcClient:   bcClient,
		},
		JobUndeleteActor: &UndeleteActor{
			actorDb: dbActor,
		},
		JobDeleteDeviceRegistrationCache: &DeleteDeviceRegistrationCache{
			cacheStorage: devRegcacheStorage,
			devRegDao:    devRegDao,
		},
		JobUploadEpanToSFTP: &UploadEpanToSFTP{
			vkycClient: vkycClient,
		},
		JobMailSaClosureRequests: &JobMailSavingsAccountClosureRequests{
			saClosureFeClient:    saClosureFeClient,
			savingsClient:        savClient,
			beCommsClient:        beCommsClient,
			exacctClient:         extacctClient,
			config:               conf,
			accountBalanceClient: accountBalanceClient,
		},
		JobAccessS3File: &AccessS3File{
			commsClient: beCommsClient,
			s3Clients:   getAllS3Clients(sess, conf.S3Conf),
			conf:        conf,
		},
		JobAccessS3Folder: &AccessS3Folder{
			conf:      conf,
			s3Clients: getAllS3Clients(sess, conf.S3Conf),
		},
		JobGetBankCustomer: &GetBankCustomer{
			bcClient: bcClient,
		},
		JobFailEPANAttempt: &FailEPANAttempt{
			epanAttemptsDao: epanAttemptsDao,
		},
		JobSignOutUser: &SignOutUser{
			authClient: authClient,
			env:        conf.Application.Environment,
		},
		JobGetAccessRevokeDetails: &GetAccessRevokeDetails{
			bcClient:    bcClient,
			usersClient: usersClient,
		},
		JobGetCkycNumber: &GetCkycNumber{
			kycClient: kycClient,
		},
		JobUpdateOccupationAtVendor: &UpdateOccupationAtVendor{
			bcClient:  bcClient,
			empClient: employmentClient,
			idGen:     idg,
		},
		JobActorIdFromCustId: &JobCustIdToActorId{
			bcClient: bcClient,
			s3Client: onboardingS3Client,
			bcDao:    bcDao,
		},
		JobReRegisterUserForVKYC: &ReRegisterUserForVKYC{
			vkycClient: vkycClient,
		},
		JobDeleteUserDetails: &DeleteUserDetails{
			userClient: usersClient,
		},
		JobInsertDeviceRegistrations: &InsertDeviceRegistrations{
			devRegDao: devRegDao,
		},
		JobPerformBiometricKYC: &PerformBiometricKYC{
			genieClient: genieClient,
			authClient:  authClient,
			userClient:  usersClient,
			simulatorDB: simDB,
		},
		JobResetEKYCDetailsInREKYC: &ResetEKYCDetailsInRekyc{
			kycComplianceDao: kycComplianceDao,
		},
		JobGetPanNumber: &GetPanNumber{
			usersClient: usersClient,
		},
		JobUpdateIncomeAtVendor: &UpdateIncomeAtVendor{
			bcClient:  bcClient,
			empClient: employmentClient,
			idGen:     idg,
			s3Client:  onboardingS3Client,
			pgdb:      pdbV2,
		},
		JobDevRegStatusAtVendor: &DevRegStatusAtVendor{
			usersClient:  usersClient,
			authClient:   authClient,
			vgCustClient: vgCustomerClient,
			bcClient:     bcClient,
		},
		JobDevRegCleanup: &DevRegCleanup{
			dbConn:       dbV2,
			config:       conf,
			cacheStorage: devRegcacheStorage,
			txnExecutor:  epifiCrdbtxnExecutor,
		},
		JobCompareLivenessVideoCompression: &CompareLivenessVideoCompression{
			dbOnboardingDetails: dbV2,
			daoLivenessAttempt:  *livAttDao,
			s3Client:            livenessS3Client,
			vgLivClient:         vgLivenessClient,
			conf:                livenessGenConf,
			commsClient:         beCommsClient,
			onbConf:             conf,
		},
		JobUpdateAfuToProgress: &UpdateAfuToInProgress{
			db: dbV2,
		},
		JobUpdateAfuToFailure: &UpdateAfuToFailure{
			db:     dbV2,
			afuDao: afuDao,
		},
		JobDeleteDirtyAddressForIdentifiers: &DeleteLocation{
			db:       dbV2,
			s3Client: onboardingS3Client,
		},
		JobEvaluateKycMismatch: &EvaluateKYCMismatch{
			docClient:    docClient,
			omegleClient: omegleClient,
			onbClient:    onbClient,
		},
		JobCacheActions: &cacheActions{
			authDevCacheClient: authDevCacheClient,
		},
		JobSetIsAllowedForAmbAccountFlag: &SetIsAllowedForAmbAccountFlag{
			onboardingDao: onbDao,
			userClient:    usersClient,
		},
		JobValidateGetMinUserValue: &GetMinUserDetail{
			usersClient: usersClient,
		},
		JobFixAddressData: &FixAddressData{
			dbConn:         dbV2,
			s3Client:       onboardingS3Client,
			vgLocClient:    vgLocationClient,
			locationClient: authLocClient,
			config:         conf,
		},
		JobUpdateAccessRevokeStatus: &UpdateAccessRevokeStatus{
			usersClient: usersClient,
		},
		JobGetPanName: &GetPanName{
			usersClient:      usersClient,
			onboardingClient: onbClient,
		},
		JobUserKycInfo: &UserKycInfo{
			onbDao:    onbDao,
			kycClient: kycClient,
		},
		JobUpdateAuditorLocationCheckStatus: &UpdateAuditorLocationCheckStatus{
			vkycCallDao: vkycCallRedisDao,
			conf:        vkyccallGenConf,
		},
		JobDedupeDataVerificationDetail: &DedupeDataVerificationDetail{
			userDao: userDao,
		},
		JobGetNROnboardingLogs: &GetNROnboardingLogs{
			onboardingClient:         onbClient,
			actorDao:                 actorDaoPgdb,
			devRegDao:                devRegDao,
			deviceIntegrityResultDao: deviceIntegrityResultDao,
			AuthDao:                  otpDao,
			usersClient:              usersClient,
			commsClient:              beCommsClient,
		},
		AdHocUpdateKycName: &AdHocJobUpdateKycName{userClient: usersClient},
		JobGetDobReconciliationActors: &GetDobReconciliationActors{
			bcClient:    bcClient,
			idGen:       idg,
			s3Client:    onboardingS3Client,
			usersClient: usersClient,
			kycClient:   kycClient,
			commsClient: beCommsClient,
			userDao:     userDao,
		},
		JobInHouseVKYCTesting: &InHouseVKYCTesting{
			dbConns: map[string]*gormv2.DB{
				"EpifiDb":     dbV2,
				"SimulatorDb": simDB,
			},
			conf:             conf,
			omegleClient:     omegleClient,
			employmentClient: employmentClient,
			commsClient:      beCommsClient,
			userClient:       usersClient,
			sgOmegleClient:   sgOmegleClient,
			s3Client:         onboardingS3Client,
			userCacheStorage: userRedisCacheStorage,
			onbDao:           onbDao,
		},
		JobOnbUserDropOffAlerts: &OnbUserDropOffAlerts{
			OpenSearchClient: openSearchClient,
			OnbClient:        onbClient,
			UsersClient:      usersClient,
			conf:             conf,
			CommsClient:      beCommsClient,
			verifyPGDB:       verifyPgdb,
		},
		JobDeleteEmploymentData: &DeleteEmploymentDataJob{
			employmentClient: employmentClient,
		},
		JobMatrixTesting: &MatrixTesting{
			sgApplicantServiceClient: sgApplicantServiceClient,
			sgMatrixClient:           sgMatrixClient,
			userClient:               usersClient,
			conf:                     conf,
			commsClient:              beCommsClient,
			inhouseVkycDeps:          inhouseVkycDeps,
		},
		JobGetCancelledCheque: &GetCancelledCheque{
			bcClient:    bcClient,
			s3Client:    onboardingS3Client,
			usersClient: usersClient,
		},
		JobDownloadFailedLivenessVideos: &DownloadFailedLivenessVideos{
			livAttDao: livAttDao,
			s3Client:  livenessS3Client,
		},
		JobAdHocUpdateUserEmail: &AdHocJobUpdateUserEmail{userClient: usersClient},
		JobAccountClosureBalTransferEnquiry: &AccountClosureBalTransferEnquiry{
			config:           conf,
			db:               dbV2,
			savClient:        savClient,
			exacctClient:     extacctClient,
			accountsVgClient: vgAccountsClient,
			bankCustClient:   bcClient,
			userClient:       usersClient,
		},
	}

	jobProcessor := jobProcessors[job]
	if jobProcessor == nil {
		fmt.Printf("\n JOB PROCESSOR NOT FOUND FOR JOB: '%v' \n", job)
		defer os.Exit(1)
		return
	}

	fmt.Println("---------------------------------- JOB START ----------------------------------")
	if err = jobProcessor.DoJob(ctx, &JobRequest{
		Job:   job,
		Args1: *jobArgs1,
		Args2: *jobArgs2,
	}); err != nil {
		logger.Error(ctx, fmt.Sprintf("error in job: %v", job), zap.Error(err))
		defer os.Exit(1)
		return
	}
	fmt.Println("---------------------------------- JOB END ------------------------------------")
}

func getAllS3Clients(sess *awsSess.Session, conf *config.S3Conf) map[string]*s3.Client {
	var bucketMap = make(map[string]*s3.Client)
	for key := range conf.BucketNames {
		bucketMap[key] = s3.NewClient(sess, conf.BucketNames[key])
	}
	return bucketMap
}

func getRedactorAndHost(conf *config.Config) (ESRedactor, string) {
	var (
		username string
		password string
		host     string
	)
	host = conf.ESHost
	logger.InfoNoCtx("", zap.String("host", host))
	// initialize esclient
	esRedactor, err := NewESRedactorImpl(host, username, password)
	if err != nil {
		logger.Fatal("error in initializing ES Redactor", zap.Error(err))
	}
	return esRedactor, host
}

func initPublisher(sqsClient *sqs.SQS, pubConf *cfg.SqsPublisher) queue.Publisher {
	pub, err := sqsPkg.NewPublisherWithConfig(pubConf, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialize publisher", zap.Error(err))
	}
	return pub
}

func initOpenSearchClient(conf *config.Config) *opensearch.Client {
	awsConfig, cfgErr := awsConf.LoadDefaultConfig(context.Background(), awsConf.WithRegion(conf.Aws.Region))
	if cfgErr != nil {
		logger.Panic("error in loading aws config", zap.Error(cfgErr))
	}
	client := stsv2.NewFromConfig(awsConfig)
	creds := stscreds.NewAssumeRoleProvider(client, conf.ESRoleArn)
	awsConfig.Credentials = creds

	signer, errSigner := osPkg.NewAWSSigner(awsConfig)
	if errSigner != nil {
		logger.Panic("err in getting AWS signer", zap.Error(errSigner))
	}

	esClient, errESClient := osPkg.NewESClientWithRoleAuth(conf.ESHost, signer)
	if errESClient != nil {
		logger.Panic("error in creating es client", zap.Error(errESClient))
	}

	return esClient
}
