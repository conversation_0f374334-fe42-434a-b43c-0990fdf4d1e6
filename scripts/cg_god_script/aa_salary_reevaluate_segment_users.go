package main

import (
	"context"
	"fmt"
	"time"

	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	spAaPb "github.com/epifi/gamma/api/salaryprogram/aa"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/salaryprogram/aasalary"
	"github.com/epifi/gamma/salaryprogram/dao"
	salaryTypes "github.com/epifi/gamma/salaryprogram/wire/types"
)

const (
	defaultCriteriaName = "CRITERIA_NAME_C_TWO"
)

type AaSalaryReevaluateSegmentUsers struct {
	segmentClient                      segmentPb.SegmentationServiceClient
	salaryProgramClient                salaryprogramPb.SalaryProgramClient
	salaryEstimationsDao               dao.ISalaryEstimationsDao
	salaryRegistrationDao              dao.IRegistrationDao
	aaSalTxnVerificationReqDao         dao.IAASalaryTxnVerificationRequestsDao
	salaryProgramActivationHistoryDao  dao.ISalaryProgramActivationHistoryDao
	aaSalaryCriteriaDao                dao.IAaSalaryCriteriaDao
	txnExecutor                        storagev2.TxnExecutor
	salaryProgramStatusUpdatePublisher salaryTypes.SalaryProgramStatusUpdateEventPublisher
}

type reevaluateMetric struct {
	progressMap                       map[string][]string
	usersInSegmentCount               int
	latestSalaryEstimatedCount        int
	latestSalaryEstimatedErrCount     int
	aaSalaryTxnVerificationCount      int
	aaSalaryTxnVerificationErrCount   int
	latestSalaryCommitedCount         int
	aaSalaryActivationHistoryErrCount int
	calculatedSalaryBandCount         int
	calculatedSalaryBandErrCount      int
	salaryProgramRegistrationCount    int
	salaryProgramRegistrationErrCount int
	reevaluatedUsersFailedCount       int
	skippedUsersCount                 int
	reevaluatedActors                 []string
	skippedUsers                      []string
}

func (a *AaSalaryReevaluateSegmentUsers) DoJob(ctx context.Context, request *JobRequest) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	segmentId := request.Args1
	metric := &reevaluateMetric{
		progressMap: make(map[string][]string),
	}
	var failedActorIds []string
	getMembersRes, err := a.segmentClient.GetMembers(ctx, &segmentPb.GetMembersRequest{
		SegmentId: segmentId,
		LatestBy:  timestampPb.Now(),
	})
	if rpcErr := epifigrpc.RPCError(getMembersRes, err); rpcErr != nil {
		return fmt.Errorf("segmentClient.GetMembers rpc call failed for segment : %v, err :%v", segmentId, rpcErr)
	}

	metric.usersInSegmentCount = len(getMembersRes.GetActorIds())

	for _, actorId := range getMembersRes.GetActorIds() {
		latestSalaryEstimated, latestSalaryEstimatedErr := a.salaryEstimationsDao.GetLatestForActor(ctx, actorId)
		if latestSalaryEstimatedErr != nil {
			failedActorIds = append(failedActorIds, actorId)
			metric.latestSalaryEstimatedErrCount++
			metric.reevaluatedUsersFailedCount++
			fmt.Printf("error fetching latest salary estimation for actor: %v, err: %v", actorId, latestSalaryEstimatedErr)
			continue
		}
		metric.latestSalaryEstimatedCount++

		aaSalaryTxnVerificationResponse, aaSalaryTxnVerificationErr := a.aaSalTxnVerificationReqDao.GetLatestByActorIdAndStatusList(ctx, actorId, []spAaPb.AASalaryTxnVerificationRequestStatus{
			spAaPb.AASalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS, spAaPb.AASalaryTxnVerificationRequestStatus_REQUEST_STATUS_COMPLETED,
		})
		if aaSalaryTxnVerificationErr != nil {
			failedActorIds = append(failedActorIds, actorId)
			metric.aaSalaryTxnVerificationErrCount++
			metric.reevaluatedUsersFailedCount++
			fmt.Printf("error fetching latest aa salary txn verification request for actor: %v, err: %v", actorId, aaSalaryTxnVerificationErr)
			continue
		}
		metric.aaSalaryTxnVerificationCount++

		latestCommitedSalary := aaSalaryTxnVerificationResponse.GetSalaryAmountCommitted()
		metric.latestSalaryCommitedCount++

		calculatedSalaryBand, aaSalaryCriteria, calculatedSalaryBandErr := a.calculateAaSalaryBand(ctx, latestSalaryEstimated.GetLastEstimatedAmount(), latestCommitedSalary)
		if calculatedSalaryBandErr != nil {
			failedActorIds = append(failedActorIds, actorId)
			metric.calculatedSalaryBandErrCount++
			metric.reevaluatedUsersFailedCount++
			fmt.Printf("error calculating salary band for actor: %v, err: %v", actorId, calculatedSalaryBandErr)
			continue
		}
		metric.calculatedSalaryBandCount++

		if calculatedSalaryBand == aaSalaryTxnVerificationResponse.GetSalaryBand() {
			metric.skippedUsers = append(metric.skippedUsers, actorId)
			metric.skippedUsersCount++
			continue
		}

		txnErr := a.txnExecutor.RunTxn(ctx, func(ctx context.Context) error {
			updateRequest := aaSalaryTxnVerificationResponse
			updateRequest.VerificationStatus = spAaPb.AASalaryTxnVerificationRequestStatus_REQUEST_STATUS_INVALID
			updateRequest.UpdatedAt = timestampPb.Now()
			updateErr := a.aaSalTxnVerificationReqDao.Update(ctx, updateRequest, []spAaPb.AASalaryTxnVerificationRequestFieldMask{
				spAaPb.AASalaryTxnVerificationRequestFieldMask_VERIFICATION_STATUS,
			})
			if updateErr != nil {
				return fmt.Errorf("error updating aa salary txn verification request for actor: %v, err: %w", actorId, updateErr)
			}

			createRequest := &spAaPb.AASalaryTxnVerificationRequest{
				ActorId:                         aaSalaryTxnVerificationResponse.GetActorId(),
				TransactionDetails:              nil,
				RequestSource:                   spAaPb.AASalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_ACTIVATION_SCRIPT,
				VerificationStatus:              spAaPb.AASalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
				VerifiedBy:                      aaSalaryTxnVerificationResponse.GetVerifiedBy(),
				AaSalaryCriteriaRefId:           aaSalaryCriteria.GetId(),
				SalaryEstimationsRefId:          aaSalaryTxnVerificationResponse.GetSalaryEstimationsRefId(),
				SalaryAmountCommitted:           aaSalaryTxnVerificationResponse.GetSalaryAmountCommitted(),
				SalaryBand:                      calculatedSalaryBand,
				SalaryProgramRegistrationsRefId: aaSalaryTxnVerificationResponse.GetSalaryProgramRegistrationsRefId(),
			}
			_, createErr := a.aaSalTxnVerificationReqDao.Create(ctx, createRequest)
			if createErr != nil {
				return fmt.Errorf("error creating aa salary txn verification request for actor: %v, err: %w", actorId, createErr)
			}
			return nil
		})
		if txnErr != nil {
			failedActorIds = append(failedActorIds, actorId)
			metric.reevaluatedUsersFailedCount++
			fmt.Printf("error updating aa salary txn verification request for actor: %v, err: %v", actorId, txnErr)
			continue
		}

		metric.progressMap[actorId] = append(metric.progressMap[actorId], "Reevaluated")
	}

	fmt.Println("\nReevaluate Summary")
	fmt.Println("---------------------------------------------------------")
	fmt.Printf("Total Users in Segment: %v\n", metric.usersInSegmentCount)
	fmt.Printf("Latest Salary Estimated Count: %v\n", metric.latestSalaryEstimatedCount)
	fmt.Printf("Latest Salary Estimated Error Count: %v\n", metric.latestSalaryEstimatedErrCount)
	fmt.Printf("AA Salary Txn Verification Count: %v\n", metric.aaSalaryTxnVerificationCount)
	fmt.Printf("AA Salary Txn Verification Error Count: %v\n", metric.aaSalaryTxnVerificationErrCount)
	fmt.Printf("Latest Salary Commited Count: %v\n", metric.latestSalaryCommitedCount)
	fmt.Printf("Calculated Salary Band Count: %v\n", metric.calculatedSalaryBandCount)
	fmt.Printf("Calculated Salary Band Error Count: %v\n", metric.calculatedSalaryBandErrCount)
	fmt.Printf("Salary Program Registration Count: %v\n", metric.salaryProgramRegistrationCount)
	fmt.Printf("Salary Program Registration Error Count: %v\n", metric.salaryProgramRegistrationErrCount)
	fmt.Printf("Reevaluated Users Failed Count: %v\n", metric.reevaluatedUsersFailedCount)
	fmt.Printf("Skipped Users Count: %v\n", metric.skippedUsersCount)
	fmt.Println("---------------------------------------------------------")

	fmt.Println("ProgressMap:")
	for actorId, progress := range metric.progressMap {
		fmt.Printf("%s : ", actorId)
		for _, p := range progress {
			fmt.Printf("%30s ", p)
		}
		fmt.Println()
	}

	println("Reevaluated Actors:", len(metric.reevaluatedActors))
	for _, actorId := range metric.reevaluatedActors {
		println(actorId)
	}
	return nil
}

func (a *AaSalaryReevaluateSegmentUsers) calculateAaSalaryBand(ctx context.Context, salaryEstimated, amountTransferred *moneyPb.Money) (salaryEnumsPb.SalaryBand, *spAaPb.AaSalaryCriteria, error) {

	criteria, criteriaErr := a.aaSalaryCriteriaDao.GetCriteriaByName(ctx, defaultCriteriaName)
	if criteriaErr != nil {
		return salaryEnumsPb.SalaryBand_SALARY_BAND_UNSPECIFIED, nil, fmt.Errorf("error getting criteria %v by name. error %w", defaultCriteriaName, criteriaErr)
	}
	salaryBand, _, calSalaryBandErr := aasalary.CalculateSalaryBand(salaryEstimated, amountTransferred, criteria.GetDetails())
	return salaryBand, criteria, calSalaryBandErr
}
