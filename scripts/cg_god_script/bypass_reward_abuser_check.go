package main

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/tiering/helper"
)

type BypassRewardAbuserCheck struct {
	redisClient *redis.Client
}

func (a *BypassRewardAbuserCheck) DoJob(ctx context.Context, request *JobRequest) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()
	// request will take comma seperated actor ids in arg1
	// tier to bypass in arg2
	// type of operation in arg 3 - ADD or DEL - does adding and deleting of actor id, tier pair to the redis store to bypass abuser check.

	const (
		addOperation    = "ADD"
		deleteOperation = "DEL"
		expiryDuration  = 24 * time.Hour
	)

	actorIdList := getActorIdsAfterCleaning(request.Args1, ",")
	tierVal, ok := tieringExternalPb.Tier_value[request.Args2]
	if !ok {
		return fmt.Errorf("tier value not found, %s", request.Args2)
	}

	tier := tieringExternalPb.Tier(tierVal)
	for _, actorId := range actorIdList {
		key := helper.GetAbuserCheckBypassKey(actorId, tier)

		switch request.Args3 {
		case addOperation:
			result, setErr := a.redisClient.Set(ctx, key, true, expiryDuration).Result()
			if setErr != nil {
				return fmt.Errorf("error setting key in redis client: %w", setErr)
			}

			logger.Info(ctx, "successfully set key in redis client", zap.String("key", key), zap.String("result", result))
		case deleteOperation:
			result, delErr := a.redisClient.Del(ctx, key).Result()
			if delErr != nil {
				return fmt.Errorf("error deleting key in redis client: %w", delErr)
			}

			logger.Info(ctx, "successfully deleted key in redis client", zap.String("key", key), zap.Int64("result", result))
		default:
			return fmt.Errorf("invalid arugument, should be either %s or %s, got %s", addOperation, deleteOperation, request.Args2)
		}

		exists, existsErr := a.redisClient.Exists(ctx, key).Result()
		if existsErr != nil {
			return fmt.Errorf("error checking existence of key in redis client: %w", existsErr)
		}

		logger.Info(ctx, "successfully checked existence of key", zap.String("key", key), zap.Int64("exists", exists))
	}
	return nil
}
