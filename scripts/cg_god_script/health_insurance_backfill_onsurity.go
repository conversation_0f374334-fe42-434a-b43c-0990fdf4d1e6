package main

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/salaryprogram/healthinsurance/dao"
	"github.com/epifi/gamma/salaryprogram/healthinsurance/insurancevendor"
	"github.com/epifi/gamma/scripts/cg_god_script/config"
)

const (
	phoneNumber        = "phone_number"
	email              = "email_id"
	dateLayoutDDMMYYYY = "02/01/2006"
	empty              = "-"
	phoneNumberLength  = 10
)

type HealthInsuranceBackfillOnsurity struct {
	conf                 *config.Config
	policyDetailsDao     dao.IPolicyDetailsDao
	policyIssuanceReqDao dao.IPolicyIssuanceRequestDao
	userClient           user.UsersClient
	txnExecutor          storagev2.TxnExecutor
	s3Client             s3.S3Client
}

type userDetails struct {
	vendorPolicyId string
	phoneNumber    string
	emailId        string
	planName       string
	activationDate string
}

type autoUpgradeMetricOnsurity struct {
	progressMap                  map[int][]string
	activationDateErr            int
	phoneNumberAndEmailIdMissing int
	userFetchErr                 int
	getPolicyTypeErr             int
	vendorPolicyIdMissing        int
	policyIssuanceReqErr         int
	policyIssuanceErr            int
	policyIssued                 int
}

// nolint:funlen
func (a *HealthInsuranceBackfillOnsurity) DoJob(ctx context.Context, request *JobRequest) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	infilePath := request.Args1
	userData, getUserDataErr := a.getUserDetails(ctx, infilePath)
	if getUserDataErr != nil {
		return fmt.Errorf("error in Getting User Details : %s", getUserDataErr.Error())
	}

	metric := &autoUpgradeMetricOnsurity{
		progressMap: make(map[int][]string),
	}

	for row, user := range userData {
		vendorPolicyId := user.vendorPolicyId
		if vendorPolicyId == empty {
			metric.progressMap[row] = append(metric.progressMap[row], "vendor policy id is missing")
			metric.vendorPolicyIdMissing++
			logger.Error(ctx, "vendorPolicyId is missing", zap.String("row", string(int32(row))))
			continue
		}

		activationDate, err := time.Parse(dateLayoutDDMMYYYY, user.activationDate)
		if err != nil {
			metric.progressMap[row] = append(metric.progressMap[row], "activation date parsing error")
			metric.activationDateErr++
			logger.Error(ctx, "error in getting activation Date, Error:", zap.Error(err), zap.String("row", string(int32(row))))
			continue
		}
		policyActiveFrom := timestampPb.New(activationDate)

		if user.emailId != empty && len(user.phoneNumber) == 1 {
			metric.progressMap[row] = append(metric.progressMap[row], "email and phoneNumber is missing")
			metric.phoneNumberAndEmailIdMissing++
			logger.Error(ctx, "emailId and phoneNumber is missing", zap.String("row", string(int32(row))))
			continue
		}

		filter := make(map[string]string)
		if user.phoneNumber != empty && len(user.phoneNumber) == phoneNumberLength {
			_, err := strconv.Atoi(user.phoneNumber)
			if err != nil {
				logger.Error(ctx, "error in converting phone number, Error:", zap.Error(err), zap.String("row", string(int32(row))))
			} else {
				filter[phoneNumber] = user.phoneNumber
			}
		}
		if user.emailId != empty {
			filter[email] = user.emailId
		}

		userResponse, userResponseErr := a.fetchUserData(ctx, filter)
		if userResponseErr != nil {
			metric.progressMap[row] = append(metric.progressMap[row], "error in fetching user data")
			metric.userFetchErr++
			logger.Error(ctx, "error in fetching user data:", zap.Error(userResponseErr), zap.String("row", string(int32(row))))
			continue
		}

		policyType, getPolicyTypeErr := insurancevendor.GetPlanNameToHealthInsurancePolicyType()[strings.Join(strings.Split(strings.ToUpper(user.planName), " "), "_")]

		if !getPolicyTypeErr {
			metric.getPolicyTypeErr++
			metric.progressMap[row] = append(metric.progressMap[row], "error in policy type")
			logger.Error(ctx, "error in getting policyType", zap.String("row", string(int32(row))))
			continue
		}

		request := &healthinsurancePb.IssueNewPolicyRequest{
			PolicyVendor: commonvgpb.Vendor_ONSURITY,
			ActorId:      userResponse.GetUser().GetActorId(),
			PolicyType:   policyType,
		}

		vendorRequestId := "EPI_" + idgen.RandAlphaNumericString(15)

		txnErr := a.txnExecutor.RunTxn(ctx, func(ctx context.Context) error {
			if request.GetPolicyVendor() == commonvgpb.Vendor_ONSURITY {
				newPolicyIssuanceReq, newPolicyIssuanceReqErr := a.policyIssuanceReqDao.Create(ctx, &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
					ActorId:          request.GetActorId(),
					PolicyVendor:     commonvgpb.Vendor_ONSURITY,
					PolicyType:       request.GetPolicyType(),
					VendorRequestId:  vendorRequestId,
					RequestStatus:    healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL,
					RequestExpiresAt: timestampPb.New(time.Now().Add(10 * time.Minute)),
					CreatedAt:        timestampPb.New(time.Now()),
					UpdatedAt:        timestampPb.New(time.Now()),
				})

				if newPolicyIssuanceReqErr != nil {
					metric.progressMap[row] = append(metric.progressMap[row], "error in policy issuance request")
					metric.policyIssuanceReqErr++
					logger.Error(ctx, "error creating a new onsurity policy issuance request, err : %v\n", zap.Error(newPolicyIssuanceReqErr), zap.String("row", string(int32(row))))
					return fmt.Errorf("%w", newPolicyIssuanceReqErr)
				}

				if _, createErr := a.policyDetailsDao.Create(ctx, &healthinsurancePb.HealthInsurancePolicyDetails{
					ActorId:                 request.GetActorId(),
					PolicyVendor:            request.GetPolicyVendor(),
					VendorPolicyId:          vendorPolicyId,
					PolicyIssuanceRequestId: newPolicyIssuanceReq.GetId(),
					PolicyRequestSource:     healthinsurancePb.PolicyRequestSource_POLICY_REQUEST_SOURCE_BACKFILLED_AFTER_DASHBOARD_ACTIVATION,
					PolicyActiveFrom:        policyActiveFrom,
					PolicyMetadata: &healthinsurancePb.PolicyMetadata{
						VendorPolicyId:   vendorPolicyId,
						PolicyActiveFrom: policyActiveFrom,
					},
					CreatedAt: timestampPb.New(time.Now()),
					UpdatedAt: timestampPb.New(time.Now()),
				}); createErr != nil {
					metric.progressMap[row] = append(metric.progressMap[row], "added data successfully in dao")
					metric.policyIssuanceErr++
					return fmt.Errorf("error creating a new onsurity policy, err : %v", createErr.Error())
				}
				metric.policyIssued++
			}
			return nil
		})
		if txnErr != nil {
			return fmt.Errorf("failed to create policy details: %w", txnErr)
		}
		logger.Info(ctx, "policy issue db entries created successfully", zap.String("ActorId", request.GetActorId()))
	}

	fmt.Println("Summary")
	fmt.Println("---------------------------------------------------------")
	fmt.Printf("%40s %d\n", "UsersFetchErr", metric.userFetchErr)
	fmt.Printf("%40s %d\n", "PhoneNumberAndEmailIdMissing", metric.phoneNumberAndEmailIdMissing)
	fmt.Printf("%40s %d\n", "PolicyIssuanceReqErr", metric.policyIssuanceReqErr)
	fmt.Printf("%40s %d\n", "PolicyIssuanceErr", metric.policyIssuanceErr)
	fmt.Printf("%40s %d\n", "ActivationDateErr", metric.activationDateErr)
	fmt.Printf("%40s %d\n", "PolicyIssued", metric.policyIssued)
	fmt.Printf("%40s %d\n", "VendorPolicyIdMissing", metric.vendorPolicyIdMissing)
	fmt.Printf("%40s %d\n", "GetPolicyTypeErr", metric.getPolicyTypeErr)
	fmt.Println("---------------------------------------------------------")
	fmt.Println("ProgressMap:")
	for row, progress := range metric.progressMap {
		fmt.Printf("%d : ", row+1)
		for _, p := range progress {
			fmt.Printf("%30s ", p)
		}
		fmt.Println()
	}

	return nil
}

func (a *HealthInsuranceBackfillOnsurity) fetchUserData(ctx context.Context, filter map[string]string) (*user.GetUserResponse, error) {
	var (
		userResponse *user.GetUserResponse
		err          error
	)
	userReq := &user.GetUserRequest{}
	for key, value := range filter {
		switch key {
		case phoneNumber:
			nationalNumber, _ := strconv.Atoi(value)
			userPhoneNumber := &common.PhoneNumber{
				CountryCode:    91,
				NationalNumber: uint64(nationalNumber),
			}
			userReq.Identifier = &user.GetUserRequest_PhoneNumber{PhoneNumber: userPhoneNumber}
			userResponse, err = a.userClient.GetUser(ctx, userReq)
			if te := epifigrpc.RPCError(userResponse, err); te != nil {
				logger.Error(ctx, "Cannot get user by phone number:", zap.Error(err))
				return nil, te
			}
			return userResponse, nil
		case email:
			userReq.Identifier = &user.GetUserRequest_EmailId{EmailId: value}
			userResponse, err = a.userClient.GetUser(ctx, userReq)
			if te := epifigrpc.RPCError(userResponse, err); te != nil {
				logger.Error(ctx, "Cannot get user by email id:", zap.Error(err))
				return nil, te
			}
			return userResponse, nil
		default:
			return nil, fmt.Errorf("unknown parameter: %s", key)
		}
	}
	return nil, fmt.Errorf("error in fetching data")
}

func (a *HealthInsuranceBackfillOnsurity) getUserDetails(ctx context.Context, inputPath string) ([]userDetails, error) {
	csvRecordsFile, err := a.s3Client.Read(inputPath)
	if err != nil {
		return nil, fmt.Errorf("Read api of s3 client failed %w", err)
	}
	rows, err := csv.NewReader(bytes.NewReader(csvRecordsFile)).ReadAll()
	if err != nil {
		return nil, fmt.Errorf("Error Getting Sheet %w", err)
	}

	logger.Info(ctx, "Creating list with user details")
	var userData []userDetails
	for j, row := range rows {
		var currentUser userDetails
		if j != 0 {
			currentUser.vendorPolicyId = row[0]
			currentUser.phoneNumber = row[3]
			currentUser.emailId = row[4]
			currentUser.planName = row[6]
			currentUser.activationDate = row[9]
			userData = append(userData, currentUser)
		}
	}
	return userData, nil
}
