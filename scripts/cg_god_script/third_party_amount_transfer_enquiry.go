package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	bankcustPb "github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
)

type ThirdPartyAccountTransferEnquiry struct {
	accountsVgClient accounts.AccountsClient
	extAcctClient    extacct.ExternalAccountsClient
	savingsClient    savingsPb.SavingsClient
	bankCustClient   bankcustPb.BankCustomerServiceClient
}

func (t *ThirdPartyAccountTransferEnquiry) DoJob(ctx context.Context, req *JobRequest) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	// ThirdPartyAccountCollection job takes
	// 1. list of comma seperated actor ids as parameter
	// 2. request type for ThirdPartyAccountCollection RPC - REQUEST_TYPE_ENQ / REQUEST_TYPE_ADD
	actorIds := getActorIdsAfterCleaning(req.Args1, ",")
	requestType := accounts.ThirdPartyAccountCollectionRequest_RequestType(accounts.ThirdPartyAccountCollectionRequest_RequestType_value[req.Args2])

	var stringSummary []string
	stringSummary = append(stringSummary, "No., ActorId, PennyDropId, Status, UTR, ErrorCode")

	for idx, actorId := range actorIds {
		savingsAccResp, savingsAccErr := t.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
				ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
					ActorId:                actorId,
					AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
					PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
		if savingsAccErr != nil || savingsAccResp == nil {
			logger.Error(ctx, "error getting savings account for actor id", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(savingsAccErr))
			continue
		}

		bankCustResp, bankCustErr := t.bankCustClient.GetBankCustomer(ctx, &bankcustPb.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(bankCustResp, bankCustErr); rpcErr != nil {
			logger.Error(ctx, "error getting bank customer for actorId", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			continue
		}

		getExtAccResp, getExtAccErr := t.extAcctClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(getExtAccResp, getExtAccErr); rpcErr != nil {
			logger.Error(ctx, "error getting bank accounts for actor id", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			continue
		}

		successfulPennyDropId := ""
		for _, bav := range getExtAccResp.GetBankAccountVerifications() {
			if bav.GetOverallStatus() != extacct.OverallStatus_OVERALL_STATUS_SUCCESS {
				continue
			}

			successfulPennyDropId = bav.GetVendorReqId()
		}

		if successfulPennyDropId == "" {
			logger.Error(ctx, "No successful penny drop found for actor id", zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Any("bav_len", len(getExtAccResp.GetBankAccountVerifications())),
				zap.Any("bank_acc_len", len(getExtAccResp.GetBankAccounts())),
			)
			continue
		}

		if len(getExtAccResp.GetBankAccounts()) == 0 {
			logger.Error(ctx, "no external bank accounts found for actor id: %s", zap.String(logger.ACTOR_ID_V2, actorId))
			continue
		}

		tpacResp, tpacErr := t.accountsVgClient.ThirdPartyAccountCollection(ctx, &accounts.ThirdPartyAccountCollectionRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			SbAccount:       savingsAccResp.GetAccount().GetAccountNo(),
			CustomerId:      bankCustResp.GetBankCustomer().GetVendorCustomerId(),
			MobileNumber:    savingsAccResp.GetAccount().GetPhoneNumber().ToString(),
			PennyDropId:     successfulPennyDropId,
			TpAccountNumber: getExtAccResp.GetBankAccounts()[0].GetAccountNumber(),
			TpIfsc:          getExtAccResp.GetBankAccounts()[0].GetIfsc(),
			RequestType:     requestType,
		})
		if rpcErr := epifigrpc.RPCError(tpacResp, tpacErr); rpcErr != nil {
			logger.Error(ctx, "error in third party account collection enquiry request", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr),
				zap.String("penny_drop_id", successfulPennyDropId))
		}

		fmt.Printf("==============================\n")
		fmt.Printf("%s TPAC Response: \n", actorId)
		fmt.Printf("\tStatus: %s\n", tpacResp.GetStatus().String())
		fmt.Printf("\tMessage: %s\n", tpacResp.GetMessage())
		fmt.Printf("\tUTR: %s\n", tpacResp.GetUtr())
		fmt.Printf("\tError code: %s\n", tpacResp.GetErrorCode().String())
		fmt.Printf("\tSuccessful Penny drop id: %s\n", successfulPennyDropId)
		fmt.Printf("==============================\n\n")

		stringSummary = append(stringSummary, fmt.Sprintf("%d, %s, %s, %s, %s, %s", idx+1, actorId, successfulPennyDropId, tpacResp.GetStatus().String(), tpacResp.GetUtr(), tpacResp.GetErrorCode().String()))
	}

	fmt.Printf("Summary:\n")
	for _, s := range stringSummary {
		println(s)
	}

	return nil
}
