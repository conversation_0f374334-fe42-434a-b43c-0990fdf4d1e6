package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/tiering/helper"
	helper2 "github.com/epifi/gamma/tiering/helper"
)

type PublishSavingsTierUpdateEvent struct {
	savingsTierUpdateEventPublisher queue.Publisher
	tieringClient                   tieringPb.TieringClient
}

// parseActorIds parses actor IDs from FilePath (CSV) or Args1 (comma-separated)
// Note : csv column name should be Actor_id
func parseActorIds(ctx context.Context, request *JobRequest) ([]string, error) {
	var actorIdList []string
	switch {
	case request.FilePath != "":
		file, err := os.Open(request.FilePath)
		if err != nil {
			logger.Error(ctx, "failed to open CSV file", zap.Error(err), zap.String("file_path", request.FilePath))
			return nil, fmt.Errorf("failed to open CSV file: %w", err)
		}
		defer func() {
			if err := file.Close(); err != nil {
				logger.Error(ctx, "error closing file", zap.Error(err), zap.String("file_path", request.FilePath))
				fmt.Printf("warning: error closing file: %v\n", err)
			}
		}()
		var rows []struct {
			ActorId string `csv:"Actor_id"`
		}
		if err := gocsv.UnmarshalFile(file, &rows); err != nil {
			logger.Error(ctx, "failed to parse CSV file", zap.Error(err), zap.String("file_path", request.FilePath))
			return nil, fmt.Errorf("failed to parse CSV file: %w", err)
		}
		for _, row := range rows {
			actorId := strings.TrimSpace(row.ActorId)
			if actorId != "" {
				actorIdList = append(actorIdList, actorId)
			}
		}
	default:
		actorIdList = getActorIdsAfterCleaning(request.Args1, ",")
	}
	if len(actorIdList) == 0 {
		logger.Error(ctx, "no actor id found", zap.String("Arg1", request.Args1), zap.String("file_path", request.FilePath))
		return nil, fmt.Errorf("actorIdList is empty (provide via CSV file or Args1 comma-separated)")
	}
	return actorIdList, nil
}

func (a *PublishSavingsTierUpdateEvent) DoJob(ctx context.Context, request *JobRequest) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	ctx = epificontext.WithTraceId(ctx, nil)

	actorIdList, err := parseActorIds(ctx, request)
	if err != nil {
		return err
	}

	const (
		batchSize           = 100
		delayBetweenBatches = 1 * time.Second
	)

	var failedActors []string

	for idx, actorId := range actorIdList {
		actorCtx := epificontext.CtxWithActorId(ctx, actorId)
		if idx%batchSize == 0 && idx != 0 {
			time.Sleep(delayBetweenBatches)
		}

		tieringPitchResp, tieringPitchErr := a.tieringClient.GetTieringPitchV2(actorCtx, &tieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(tieringPitchResp, tieringPitchErr); rpcErr != nil {
			logger.Error(actorCtx, "failed to get tiering pitch", zap.Error(rpcErr))
			failedActors = append(failedActors, actorId)
			continue
		}

		latestMovement := helper.GetLatestMovementDetails(tieringPitchResp)
		var movementType external.TierMovementType
		if latestMovement != nil && latestMovement.GetFromTier() == external.Tier_TIER_UNSPECIFIED {
			movementType = external.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE
		} else {
			var err error
			movementType, err = helper2.GetMovementTypeFromStartAndEndTiersExt(latestMovement.GetFromTier(), tieringPitchResp.GetCurrentTier())
			if err != nil {
				logger.Error(actorCtx, "failed to get movement type from start and end tiers", zap.Error(err))
				failedActors = append(failedActors, actorId)
				continue
			}
		}

		_, publishErr := a.savingsTierUpdateEventPublisher.Publish(actorCtx, &external.TierUpdateEvent{
			EventId:           uuid.New().String(),
			ActorId:           actorId,
			FromTier:          latestMovement.GetFromTier(),
			ToTier:            tieringPitchResp.GetCurrentTier(),
			MovementType:      movementType,
			EventTimestamp:    timestamppb.Now(),
			MovementTimestamp: latestMovement.GetMovementTimestamp(),
		})
		if publishErr != nil {
			logger.Error(actorCtx, "failed to publish savings tier update event", zap.Error(publishErr))
			failedActors = append(failedActors, actorId)
			continue
		}
	}

	fmt.Println("---------------------------------------------------------")
	println("Failed Actors:", len(failedActors))
	for _, actorId := range failedActors {
		println(actorId)
	}
	fmt.Println("---------------------------------------------------------")

	return nil
}
