Application:
  Environment: "development"
  Name: "pal"
  Namespace: ""

EpifiDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FeatureEngineeringDb:
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalPgDb:
  DbType: "PGDB"
  Host: "localhost"
  Port: 5432
  Name: "loans_federal"
  EnableDebug: false
  SSLMode: "disable"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FederalDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "federal"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AuthDb:
  AppName: "auth"
  StatementTimeout: 5s
  Name: "auth"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  DbType: "PGDB"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_federal"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_liquiloans"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_idfc"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_fiftyfin"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  MONEYVIEW_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_ABFL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_LENDEN:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    Host: "localhost"
    Port: 5432
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  CONNECTED_ACCOUNT_WEALTH:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "connected_account"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

DbConfigMap:
  FEDERAL_BANK:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DbType: CRDB
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "loans_fiftyfin_crdb"
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

EpifiPgdb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 2m
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"
  S3:
    EligibleUsersBucketName: "epifi-preapprovedloan"
    PreApprovedLoanBucketName: "epifi-preapprovedloan"

RudderStack:
  IntervalInSec: 1
  BatchSize: 1000
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"
    EpifiExperianPgpKeySet: "{\"public\":\"\",\"private\":\"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\",\"pass_phrase\":\"qwerty1234\",\"vendor_public_key\":\"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\"}"

PgdbMigrationFlag: true

RawBucketScienapticBreDataFilePath: "development/data/vendor/bre_scienaptic/bre_data/%s/%s-.csv"

RawDataDevBucketName: "epifi-raw-dev"

PalTemporalNamespace: "pre-approved-loan"

ITRFileConf:
  ITRS3Bucket: "itr-intimation"
  S3PrefixPath: "ITR_INT_PDF/"
  OutputS3Bucket: "epifi-preapprovedloan"
  OutputS3PfxPath: "itr-intimation/ITR-Docs/"

RedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: ""
    DB: 0

CredgenicsNACHPaymentsPostingConf:
  PreApprovedLoanBucketName: "epifi-preapprovedloan"
