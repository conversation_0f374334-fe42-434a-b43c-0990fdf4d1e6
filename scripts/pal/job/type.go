package job

type Type int32

const (
	Unspecified                   Type = 0
	ProcessFiEligibleBase         Type = 1
	ProcessEligibleBaseFromVendor Type = 2
	DeleteRiskyUsers              Type = 3
	RetryFetchDetailsV2           Type = 4
	GetStatement                  Type = 5
	PopulateLecFromVendorFile     Type = 6
	PopulateLecFromVendor         Type = 7
	SendEmiComms                  Type = 8
	CreateNewOffer                Type = 9
	ExtendExpiryFedOffers         Type = 10
	ProcessLlEligibleBase         Type = 11
	CreditReportFlattenAdhoc      Type = 12
	UpdateInstallmentInfo         Type = 13
	FetchLlRejectedBase           Type = 14
	TriggerLlActivity             Type = 15
	SetLoanProgramInLoanOffers    Type = 16
	CreditReportFlatten           Type = 17
	BackfillCkycVideoPQ           Type = 18
	LoanAccountRecon              Type = 19
	RepaymentCapture              Type = 20
	LoanActivityBackFill          Type = 21
	CryptS3Data                   Type = 22
	GenerateVendorBureauFile      Type = 23
	DisableOffers                 Type = 24
	LoanSchemeBackfill            Type = 25
	SyncLoanAccount               Type = 26
	DeactivateAllOffers           Type = 27
	DeactivateOfferIfLoanTaken    Type = 28
	CleanUpPayouts                Type = 29
	ResolveLOEC                   Type = 30
	SiCollection                  Type = 31
	BackfillLoanAccountId         Type = 32
	ResolveEMIDefaults            Type = 33
	EsEsignTrigger                Type = 34
	CreateIdfcCugOffers           Type = 35
	DeleteLoanAccount             Type = 36
	GetAddress                    Type = 37
	FixLoanApplicant              Type = 38
	ResetChargesAmount            Type = 39
	BackfillUTR                   Type = 40
	BureauReportingSubvention     Type = 41
	UpdateVendorReqIdIdfc         Type = 42
	BrePiping                     Type = 43
	SyncLoanAccountLL             Type = 44
	DeleteDuplicateLoanLii        Type = 45
	DeactivateFederalOffer        Type = 46
	ProcessScrubData              Type = 47
	ProcessEncryptedScrubData     Type = 48
	// ProcessPrepaymentAtVendor is used to recon a given transaction as a pre-payment at loan vendor
	ProcessPrepaymentAtVendor           Type = 49
	MvPanDedupe                         Type = 50
	FetchRepaymentSchedule              Type = 51
	FetchLoanStatus                     Type = 52
	LamfResetFailedOfferGenLse          Type = 53
	BackfillLoanActivitiesUTR           Type = 54
	DeleteUserLoanData                  Type = 55
	CollectionsSyncLead                 Type = 56
	NormalizeCibil                      Type = 57
	BfCibilCustIdMismatch               Type = 58
	BackFillCibilReports                Type = 59
	ReconLoanPreClosure                 Type = 60
	LamfManualResolution                Type = 61
	CollectionsAllocationSync           Type = 62
	ProcessBillzyPayments               Type = 63
	LmsDataDifference                   Type = 64
	FillFederalDpdUserData              Type = 65
	UploadITRFileToSFTP                 Type = 66
	LamfCreateInterestAccountPi         Type = 67
	CreditReportDataRecovery            Type = 68
	AutoPay                             Type = 69
	PostNachPaymentsToCredgenics        Type = 70
	SoftDeleteCreditReportData          Type = 71
	HardDeleteFlattenedCreditReportData Type = 72
	SoftDeleteOldCreditReportData       Type = 73
	OneTime                             Type = 74
	UpdateCreditReportDownloadData      Type = 75
	ApplyOfferDiscountAfterUserDropoff  Type = 76
	HardDeleteFlattenedCibilReportData  Type = 77
	SoftDeleteOldCibilReportData        Type = 78
	DeactivateAllLoanApplication        Type = 79
	PurgeCkycData                       Type = 80
	AbflRegulatoryData                  Type = 81
	LoanAccountCreationLAMF             Type = 82
	StoreExperianConsents               Type = 83
	UpdateLrLseStatus                   Type = 84
	SyncAllActiveLeadsCollections       Type = 85
	LiquiloansRepostHoldPayments        Type = 86
	UpdateLoanOfferConstraints          Type = 87
	InitiateAnalysis                    Type = 88
	UploadAbflPwaLoanApplicationStatus  Type = 89
	BackfillRejectedBreData             Type = 90
	EpfoDataFetch                       Type = 91
	FedLentraCugTesting                 Type = 92
	RetryDedupe                         Type = 93
	LoanFunnelTracking                  Type = 94
	UpdateFederalLoanRequestVendorId    Type = 95
	LoansOnboarding                     Type = 96
	LiquiloansGetSoa                    Type = 97
)
