//nolint:all
package eligibility_flow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	beAuthPb "github.com/epifi/gamma/api/auth"

	"context"
	"fmt"
	"time"

	"github.com/lib/pq"
	pkgErrors "github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	creditreportpb "github.com/epifi/gamma/api/frontend/credit_report"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePalPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	bePalPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

const (
	maxAuthPollCount                 = 30
	maxLoanApplicationPollCount      = 30
	maxCreditReportDownloadPollCount = 30
)

type PlJobSuite struct {
	fePalClient             fePalPb.PreApprovedLoanClient
	creditReportClient      creditreportpb.CreditReportClient
	loansDbResourceProvider *storage.DBResourceProvider[*gorm.DB]
	beAuthClient            beAuthPb.AuthClient
}

func NewPlJobSuite(
	fePalClient fePalPb.PreApprovedLoanClient,
	creditReportClient creditreportpb.CreditReportClient,
	loansDbResourceProvider *storage.DBResourceProvider[*gorm.DB],
	beAuthClient beAuthPb.AuthClient,
) *PlJobSuite {
	return &PlJobSuite{
		fePalClient:             fePalClient,
		creditReportClient:      creditReportClient,
		loansDbResourceProvider: loansDbResourceProvider,
		beAuthClient:            beAuthClient,
	}
}

func (ts *PlJobSuite) getPlLandingInfo(ctx context.Context, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader) (*fePalPb.GetLandingInfoResponse, error) {
	// get landing page info
	landingInfoRes, err := ts.fePalClient.GetLandingInfo(ctx, &fePalPb.GetLandingInfoRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
		LoanType:   palEnumFePb.LoanType_LOAN_TYPE_PERSONAL,
	})
	if err != nil {
		return nil, fmt.Errorf("GetLandingInfo error: %v", err)
	}
	if landingInfoRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", landingInfoRes.GetRespHeader().GetStatus().GetCode())
	}
	return landingInfoRes, nil
}

func (ts *PlJobSuite) getOfferDetails(ctx context.Context, request *fePalPb.GetOfferDetailsRequest) (*fePalPb.GetOfferDetailsResponse, error) {
	offerDetailsResponse, err := ts.fePalClient.GetOfferDetails(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("GetOfferDetails error: %v", err)
	}
	if offerDetailsResponse.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", offerDetailsResponse.GetRespHeader().GetStatus().GetCode())
	}
	return offerDetailsResponse, nil
}

func (ts *PlJobSuite) getApplicationDetails(ctx context.Context, request *fePalPb.GetApplicationDetailsRequest) (*fePalPb.GetApplicationDetailsResponse, error) {
	applicationDetailsResponse, err := ts.fePalClient.GetApplicationDetails(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("GetApplicationDetails error: %v", err)
	}
	if applicationDetailsResponse.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", applicationDetailsResponse.GetRespHeader().GetStatus().GetCode())
	}
	return applicationDetailsResponse, nil
}

func (ts *PlJobSuite) applyForLoan(ctx context.Context, request *fePalPb.ApplyForLoanRequest) (*fePalPb.ApplyForLoanResponse, error) {
	applyForLoanRes, err := ts.fePalClient.ApplyForLoan(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("ApplyForLoan error: %v", err)
	}
	if applyForLoanRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", applyForLoanRes.GetRespHeader().GetStatus().GetCode())
	}
	return applyForLoanRes, nil
}

func (ts *PlJobSuite) getApplicationStatus(ctx context.Context, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, loanRequestId string) (*fePalPb.GetApplicationStatusResponse, error) {
	applicationStatusRes, err := ts.fePalClient.GetApplicationStatus(ctx, &fePalPb.GetApplicationStatusRequest{
		Req:           userAuth,
		LoanHeader:    loanHeader,
		LoanRequestId: loanRequestId,
	})
	if err != nil {
		return nil, fmt.Errorf("GetApplicationStatus error: %v", err)
	}
	if applicationStatusRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", applicationStatusRes.GetRespHeader().GetStatus().GetCode())
	}
	return applicationStatusRes, nil
}

func (ts *PlJobSuite) pollLoansStatusScreen(ctx context.Context, lrId string, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, expectedScreen deeplink.Screen) (*deeplink.Deeplink, error) {
	var res *fePalPb.GetApplicationStatusResponse
	for attempt := 1; attempt <= maxLoanApplicationPollCount; attempt++ {
		var err error
		res, err = ts.fePalClient.GetApplicationStatus(ctx, &fePalPb.GetApplicationStatusRequest{
			Req:           userAuth,
			LoanHeader:    loanHeader,
			LoanRequestId: lrId,
		})
		if err != nil {
			return nil, fmt.Errorf("GetApplicationStatus error on attempt %d: %v", attempt, err)
		}
		if res.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
			return nil, fmt.Errorf("unexpected status code on attempt %d: %v", attempt, res.GetRespHeader().GetStatus().GetCode())
		}
		if res.GetDeeplink().GetScreen() == expectedScreen {
			return res.GetDeeplink(), nil
		}
		time.Sleep(2 * time.Second)
	}
	return nil, fmt.Errorf("max polling attempts (%d) reached without getting expected screen", maxLoanApplicationPollCount)
}

func (ts *PlJobSuite) pollCreditReportDownloadStatus(ctx context.Context, clientReqId string, userAuth *header.RequestHeader, expectedScreen deeplink.Screen) error {
	for attempt := 1; attempt <= maxCreditReportDownloadPollCount; attempt++ {
		creditReportNextAction, err := ts.creditReportClient.GetReportDownloadNextAction(ctx, &creditreportpb.GetReportDownloadNextActionRequest{
			Req:         userAuth,
			ClientReqId: clientReqId,
		})
		if err != nil {
			return fmt.Errorf("GetReportDownloadNextAction error on attempt %d: %v", attempt, err)
		}
		if creditReportNextAction.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
			return fmt.Errorf("unexpected status code on attempt %d: %v", attempt, creditReportNextAction.GetRespHeader().GetStatus().GetCode())
		}

		// here we can get either consent screen (if consent is not present or expired), or polling screen again
		// if we are getting the consent screen, call the consent rpc to register the user consent to move forward
		if creditReportNextAction.GetNextAction().GetScreen() == deeplink.Screen_CREDIT_REPORT_CONSENT_V2 {
			res, err := ts.creditReportClient.RecordDownloadConsent(ctx, &creditreportpb.RecordDownloadConsentRequest{
				Req:           userAuth,
				RequestId:     clientReqId,
				ConsentAction: creditreportpb.ConsentAction_CONSENT_ACTION_ACCEPTED,
			})
			if err != nil {
				return fmt.Errorf("RecordDownloadConsent error on attempt %d: %v", attempt, err)
			}
			if res.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
				return fmt.Errorf("unexpected status code on attempt %d: %v", attempt, res.GetRespHeader().GetStatus().GetCode())
			}
			continue
		}
		if creditReportNextAction.GetNextAction().GetScreen() == expectedScreen {
			return nil
		}
		time.Sleep(2 * time.Second)
	}
	return fmt.Errorf("max polling attempts (%d) reached without getting expected screen", maxCreditReportDownloadPollCount)
}

func (ts *PlJobSuite) getLoanDashboard(ctx context.Context, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader) (*fePalPb.GetDashboardResponse, error) {
	dashboardRes, err := ts.fePalClient.GetDashboard(ctx, &fePalPb.GetDashboardRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
	})
	if err != nil {
		return nil, fmt.Errorf("GetDashboard error: %v", err)
	}
	if dashboardRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", dashboardRes.GetRespHeader().GetStatus().GetCode())
	}
	return dashboardRes, nil
}

func (ts *PlJobSuite) getLoanDetails(ctx context.Context, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, loanId string) (*fePalPb.GetLoanDetailsResponse, error) {
	loanDetailsRes, err := ts.fePalClient.GetLoanDetails(ctx, &fePalPb.GetLoanDetailsRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
		LoanId:     loanId,
	})
	if err != nil {
		return nil, fmt.Errorf("GetLoanDetails error: %v", err)
	}
	if loanDetailsRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", loanDetailsRes.GetRespHeader().GetStatus().GetCode())
	}
	return loanDetailsRes, nil
}

func (ts *PlJobSuite) addPanAndDOBdata(reqId string, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, dob *date.Date, pan string) (*deeplink.Deeplink, error) {
	addPanAndDOBdataReq := fePalPb.AddPanAndDobDataRequest{
		Req:           auth,
		LoanHeader:    loanHeader,
		LoanRequestId: reqId,
		Pan:           pan,
		Dob:           fmt.Sprintf(`%v/%v/%v`, dob.GetDay(), dob.GetMonth(), dob.GetYear()),
	}

	addPanAndDOBres, err := ts.fePalClient.AddPanAndDobData(context.Background(), &addPanAndDOBdataReq)
	if err != nil {
		return nil, fmt.Errorf("AddPanAndDobData error: %v", err)
	}
	return addPanAndDOBres.GetDeeplink(), nil
}

func (ts *PlJobSuite) getMandateData(ctx context.Context, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, reqId string) error {
	getMandateViewDataRequest := fePalPb.GetMandateViewDataRequest{
		Req:           auth,
		LoanHeader:    loanHeader,
		LoanRequestId: reqId,
	}

	_, err := ts.fePalClient.GetMandateViewData(ctx, &getMandateViewDataRequest)
	return err
}

func (ts *PlJobSuite) generateOTp(ctx context.Context, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, reqId string) (*fePalPb.GenerateConfirmationCodeResponse, error) {
	generateConfrmationCode := fePalPb.GenerateConfirmationCodeRequest{
		Req:           auth,
		LoanRequestId: reqId,
		LoanHeader:    loanHeader,
	}
	resp, err := ts.fePalClient.GenerateConfirmationCode(ctx, &generateConfrmationCode)
	if err != nil {
		return nil, fmt.Errorf("GenerateConfirmationCode error: %v", err)
	}
	return resp, nil
}

func (ts *PlJobSuite) verifyDetails(ctx context.Context, auth *header.RequestHeader, reqId string, detailsType preapprovedloans.DetailsType, value string) (*deeplink.Deeplink, error) {
	var verifyDetailsReq fePalPb.VerifyDetailsRequest
	if detailsType == preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION {
		var empType types.EmploymentType
		if value == "1" {
			empType = types.EmploymentType_EMPLOYMENT_TYPE_SALARIED
		} else {
			empType = types.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED
		}
		verifyDetailsReq = fePalPb.VerifyDetailsRequest{
			Req:            auth,
			LoanReqId:      reqId,
			DetailsType:    detailsType,
			EmploymentType: empType,
		}
	} else {
		verifyDetailsReq = fePalPb.VerifyDetailsRequest{
			Req:         auth,
			LoanReqId:   reqId,
			DetailsType: detailsType,
			Value:       value,
		}
	}
	verifyDetailsResp, err := ts.fePalClient.VerifyDetails(ctx, &verifyDetailsReq)
	if err != nil {
		return nil, fmt.Errorf("VerifyDetails error: %v", err)
	}
	return verifyDetailsResp.GetDeeplink(), nil
}

func (ts *PlJobSuite) addAddressDetailsLl(ctx context.Context, userAuth *header.RequestHeader, lrId string, loanHeader *palEnumFePb.LoanHeader) (*deeplink.Deeplink, error) {
	addConfRes, err := ts.fePalClient.AddAddressDetails(ctx, &fePalPb.AddAddressDetailsRequest{
		Req:           userAuth,
		LoanRequestId: lrId,
		Address: &types.PostalAddress{
			PostalCode:         "560037",
			AdministrativeArea: "administrative area 1",
			Locality:           "locality 1",
			AddressLines:       []string{"line 1", "line 2"},
		},
		LoanHeader: loanHeader,
	})
	if err != nil {
		return nil, fmt.Errorf("AddAddressDetails error: %v", err)
	}
	if addConfRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", addConfRes.GetRespHeader().GetStatus().GetCode())
	}
	return addConfRes.GetDeeplink(), nil
}

func (ts *PlJobSuite) addEmploymentDetailsLl(ctx context.Context, userAuth *header.RequestHeader, lrId string, loanHeader *palEnumFePb.LoanHeader) (*deeplink.Deeplink, error) {
	// validate the add employment details call
	empConfRes, err := ts.fePalClient.AddEmploymentDetails(ctx, &fePalPb.AddEmploymentDetailsRequest{
		Req:           userAuth,
		LoanRequestId: lrId,
		EmploymentDetailsList: []*fePalPb.AddEmploymentDetailsRequest_EmploymentDetails{
			{
				DetailsType: fePalPb.DetailsFormField_OCCUPATION_TYPE,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_EmploymentValue{EmploymentValue: types.EmploymentType_EMPLOYMENT_TYPE_SALARIED},
			},
			{
				DetailsType: fePalPb.DetailsFormField_OCCUPATION_NAME,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_StringValue{StringValue: "epifi technologies"},
			},
			{
				DetailsType: fePalPb.DetailsFormField_MONTHLY_INCOME,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_MoneyValue{MoneyValue: &types.Money{
					CurrencyCode: "INR",
					Units:        98065,
					Decimals:     0,
				}},
			},
			{
				DetailsType: fePalPb.DetailsFormField_OFFICE_ADDRESS,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_AddressValue{AddressValue: &types.PostalAddress{
					PostalCode:         "560037",
					AdministrativeArea: "administrative area 1",
					Locality:           "locality 1",
					AddressLines:       []string{"line 1", "line 2"},
				}},
			},
			{
				DetailsType: fePalPb.DetailsFormField_WORK_EMAIL_ADDRESS,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_StringValue{StringValue: "<EMAIL>"},
			},
		},
		LoanHeader: loanHeader,
	})
	if err != nil {
		return nil, fmt.Errorf("AddEmploymentDetails error: %v", err)
	}
	if empConfRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", empConfRes.GetRespHeader().GetStatus().GetCode())
	}
	return empConfRes.GetDeeplink(), nil
}

func (ts *PlJobSuite) getActorIdUsingAuthHeader(auth *header.RequestHeader) (string, error) {
	validateAuthRes, err := ts.beAuthClient.ValidateToken(context.Background(), &beAuthPb.ValidateTokenRequest{
		Token:     auth.Auth.GetAccessToken(),
		TokenType: beAuthPb.TokenType_ACCESS_TOKEN,
	})
	if err != nil {
		return "", fmt.Errorf("ValidateToken error: %v", err)
	}
	if validateAuthRes.GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return "", fmt.Errorf("unexpected status code: %v", validateAuthRes.GetStatus().GetCode())
	}
	if validateAuthRes.GetActorId() == "" {
		return "", fmt.Errorf("empty actor ID in response")
	}
	return validateAuthRes.GetActorId(), nil
}

// deleteLoansData clears all the loans data in db as currently repeat loans are not supported. so even is loan account is in closed
// state, user can't take another loan. As we are using the same user to run the integration tests across different flows, we need
// to clear the loans data after each run.
func (ts *PlJobSuite) deleteLoansData(ctx context.Context, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, vendor bePalPb.Vendor) error {
	ownership := helper.GetPalOwnership(vendor)
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	db, err := GetConnFromContextOrProvider(ctxWithOwnership, ts.loansDbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider: %v", err)
	}

	actorId, err := ts.getActorIdUsingAuthHeader(userAuth)
	if err != nil {
		return fmt.Errorf("error getting actor ID: %v", err)
	}

	lp := bePalPb.LoanProgram_name[int32(loanHeader.GetLoanProgram())]
	var loanAccountIds []string

	if err := db.Raw("SELECT loan_account_id FROM loan_accounts WHERE actor_id = ? and loan_program=?", actorId, lp).Scan(pq.Array(&loanAccountIds)); err.Error != nil {
		return fmt.Errorf("failed to fetch loan account id with given actor id: %w", err.Error)
	}

	if err = db.Exec("DELETE FROM loan_installment_info WHERE account_id IN (?)", loanAccountIds).Error; err != nil {
		return fmt.Errorf("error deleting loan_installment_info relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_activities WHERE loan_account_id IN (?)", loanAccountIds).Error; err != nil {
		return fmt.Errorf("error deleting loan_activities relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_requests WHERE actor_id = ? and loan_program = ?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_requests relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_accounts WHERE actor_id = ? and loan_program=?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_accounts relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_step_executions WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting loan_step_executions relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_offers WHERE actor_id = ? and loan_program = ?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_offers relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_offer_eligibility_criteria WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting loan_offer_eligibility_criteria relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_applicants WHERE actor_id = ? and loan_program = ?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_applicants relation: %w", err)
	}

	return nil
}

func GetConnFromContextOrProvider(ctx context.Context, dbResourceProvider *storage.DBResourceProvider[*gorm.DB]) (*gorm.DB, error) {
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	// default to federal bank
	if ow == commontypes.Ownership_EPIFI_TECH {
		ow = commontypes.Ownership_FEDERAL_BANK
	}
	dbConn, err := dbResourceProvider.GetResourceForOwnership(ow)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "err in GetResourceForOwnership")
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}

func (ts *PlJobSuite) InitiateESign(ctx context.Context, request *fePalPb.InitiateESignRequest) (*fePalPb.InitiateESignResponse, error) {
	initoateEsignRes, err := ts.fePalClient.InitiateESign(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("InitiateESign error: %v", err)
	}
	if initoateEsignRes.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return nil, fmt.Errorf("unexpected status code: %v", initoateEsignRes.GetRespHeader().GetStatus().GetCode())
	}
	return initoateEsignRes, nil
}
