package eligibility_flow

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/consent"
	ackPb "github.com/epifi/gamma/api/frontend/consent/ack"
	creditreportpb "github.com/epifi/gamma/api/frontend/credit_report"
	feDL "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePalPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2"
	consentDL "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent"
	plScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/epifigrpc/interceptors/frontend"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
)

// TestStage defines the stage until which the test should run
type TestStage string

const (
	StageAll               TestStage = "all"
	StageApplicationReview TestStage = "application_review"
	StageCreditReport      TestStage = "credit_report"
)

// FieldType represents the type of form field
type FieldType string

const (
	FieldTypeString FieldType = "STRING"
	FieldTypeInt    FieldType = "INT"
	FieldTypeDate   FieldType = "DATE"
	FieldTypeSelect FieldType = "SELECT"
	FieldTypeMoney  FieldType = "MONEY"
)

// nolint:dupl
func A2LUserCreation(ctx context.Context, dep *OnbDep, aUser *UserData, palTs *PlJobSuite) (*header.RequestHeader, error) {
	reqH, respAddOAuth, err := userWithAccessToken(ctx, dep, aUser)
	if err != nil {
		return nil, fmt.Errorf("error creating user with access token: %v", err)
	}
	if respAddOAuth == nil {
		return nil, fmt.Errorf("addOAuth response should not be nil")
	}
	if respAddOAuth.GetNextAction() == nil {
		return nil, fmt.Errorf("next action in AddOAuth response should not be nil")
	}

	nextAction := respAddOAuth.GetNextAction()

	if nextAction.GetScreen() != feDL.Screen_ONBOARDING_INTENT_SELECTION {
		return nil, fmt.Errorf("expected screen ONBOARDING_INTENT_SELECTION, got %v", nextAction.GetScreen())
	}

	resp, err := dep.SignupClient.SetOnboardingIntent(ctx, &signup.SetOnboardingIntentRequest{
		Req:              reqH,
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS.String(),
	})

	if err != nil {
		return nil, fmt.Errorf("SetOnboardingIntent API error: %v", err)
	}

	if err := fepkg.FeRPCError(resp, err); err != nil {
		return nil, err
	}

	nextAction, err = NextAction(ctx, dep.SignupClient, reqH)
	if err != nil {
		return nil, err
	}
	if nextAction.GetScreen() != feDL.Screen_CONSENT_V2 {
		return nil, fmt.Errorf("expected screen CONSENT_V2, got %v", nextAction.GetScreen())
	}

	var consentTypeList []consent.ConsentType
	consentTypeList = append(consentTypeList, consent.ConsentType_FI_TNC, consent.ConsentType_FED_TNC, consent.ConsentType_FI_PRIVACY_POLICY, consent.ConsentType_FI_WEALTH_TNC)

	if err := RecordFiConsent(ctx, dep.ConsentClient, reqH, consentTypeList); err != nil {
		return nil, err
	}

	nextAction, err = NextAction(ctx, dep.SignupClient, reqH)
	if err != nil {
		return nil, err
	}
	if nextAction.GetScreen() != feDL.Screen_PHONE_PERMISSION_SCREEN {
		return nil, fmt.Errorf("expected screen PHONE_PERMISSION_SCREEN, got %v", nextAction.GetScreen())
	}

	var ackId string
	var ackTypeStr string

	if waOpts := nextAction.GetWaConsentScreenOption(); waOpts != nil {
		if waOpts.GetCta() != nil && waOpts.GetCta().GetDeeplink() != nil {
			ctaDeeplink := waOpts.GetCta().GetDeeplink()
			deeplinkStr := ctaDeeplink.String()

			ackIdPattern := `ack_id:"([^"]+)"`
			ackIdRegex := regexp.MustCompile(ackIdPattern)
			matches := ackIdRegex.FindStringSubmatch(deeplinkStr)
			if len(matches) > 1 {
				ackId = matches[1]
			}

			ackTypePattern := `ack_type:"([^"]+)"`
			ackTypeRegex := regexp.MustCompile(ackTypePattern)
			matches = ackTypeRegex.FindStringSubmatch(deeplinkStr)
			if len(matches) > 1 {
				ackTypeStr = matches[1]
			}
		}
	}

	if ackId == "" {
		ackId = "phone_permission_ack"
		ackTypeStr = consentDL.AckType_ACK_TYPE_PERMISSION_GRANTED.String()
	}

	waConsentResp, err := dep.ConsentClient.RecordWAConsent(ctx, &consent.RecordWAConsentRequest{
		Req:          reqH,
		ConsentGiven: commontypes.BooleanEnum_TRUE,
	})

	if err != nil {
		return nil, fmt.Errorf("RecordWAConsent API error: %v", err)
	}

	if err := fepkg.FeRPCError(waConsentResp, err); err != nil {
		return nil, err
	}

	if ackId != "" {
		feConn := epifigrpc.NewConnByService(cfg.FRONTEND_SERVICE, frontend.DeeplinkBackwardCompatibilityClientInterceptor)
		consentAckClient := ackPb.NewAckClient(feConn)

		ackResp, err := consentAckClient.ProcessUserAck(ctx, &ackPb.ProcessUserAckRequest{
			Req:     reqH,
			AckId:   ackId,
			AckType: ackTypeStr,
		})

		if err != nil {
			return nil, fmt.Errorf("ProcessUserAck API error: %v", err)
		}

		if err := fepkg.FeRPCError(ackResp, err); err != nil {
			return nil, err
		}
	}

	nextAction, err = NextAction(ctx, dep.SignupClient, reqH)
	if err != nil {
		return nil, err
	}
	if nextAction.GetScreen() != feDL.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN {
		return nil, fmt.Errorf("expected screen PRE_APPROVED_LOAN_LANDING_SCREEN, got %v", nextAction.GetScreen())
	}

	landingInfoResp, landingErr := dep.PALClient.GetLandingInfo(ctx, &fePalPb.GetLandingInfoRequest{
		Req:        reqH,
		LoanHeader: &palEnums.LoanHeader{},
	})

	if landingErr != nil {
		return nil, fmt.Errorf("GetLandingInfo API error: %v", landingErr)
	} else if landingInfoResp.GetDeeplink() != nil {
		nextAction = landingInfoResp.GetDeeplink()
	}

	acqToLendResp, err := dep.PALClient.GetAcqToLendLandingScreen(ctx, &fePalPb.GetAcqToLendLandingScreenRequest{
		Req: reqH,
		LoanHeader: &palEnums.LoanHeader{
			LoanProgram: palEnums.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
			Vendor:      palEnums.Vendor_EPIFI_TECH,
		},
	})

	if err != nil {
		return nil, fmt.Errorf("GetAcqToLendLandingScreen API error: %v", err)
	} else if acqToLendResp.GetDeeplink() != nil {
		nextAction = acqToLendResp.GetDeeplink()
	}

	if nextAction.GetScreen() != feDL.Screen_PL_ACQ_TO_LEND_LANDING_SCREEN {
		return nil, fmt.Errorf("expected screen PL_ACQ_TO_LEND_LANDING_SCREEN, got %v", nextAction.GetScreen())
	}

	return reqH, nil
}

// FormFieldValue represents a value for a form field
type FormFieldValue struct {
	FieldId    string
	FieldType  FieldType
	FieldValue *fePalPb.CollectFormDataRequest_FormFieldValue
}

// FormFieldMap stores all possible form field values
type FormFieldMap struct {
	values map[string]*FormFieldValue
}

// GetRelatedFields returns additional fields that should be submitted together with the given field
func (m *FormFieldMap) GetRelatedFields(fieldId string) []string {
	switch fieldId {
	case "EMPLOYMENT_TYPE":
		return []string{"EMPLOYER_NAME", "MONTHLY_INCOME"}
	default:
		return nil
	}
}

// GetFormFieldValues returns form field values for the given field IDs
func (m *FormFieldMap) GetFormFieldValues(fieldIds []string) ([]*fePalPb.CollectFormDataRequest_FormFieldValue, error) {
	var result []*fePalPb.CollectFormDataRequest_FormFieldValue
	var missingFields []string

	// First check if we have all required fields
	for _, fieldId := range fieldIds {
		if _, ok := m.values[fieldId]; !ok {
			missingFields = append(missingFields, fieldId)
		}
		// Check for related fields that should be submitted together
		for _, relatedField := range m.GetRelatedFields(fieldId) {
			if _, ok := m.values[relatedField]; !ok {
				missingFields = append(missingFields, relatedField)
			}
		}
	}

	if len(missingFields) > 0 {
		return nil, fmt.Errorf("missing values for fields: %v", missingFields)
	}

	// Add all required fields to result
	for _, fieldId := range fieldIds {
		if value, ok := m.values[fieldId]; ok {
			result = append(result, &fePalPb.CollectFormDataRequest_FormFieldValue{
				FieldId:    fieldId,
				FieldValue: value.FieldValue.FieldValue,
			})
			// Add related fields that should be submitted together
			for _, relatedField := range m.GetRelatedFields(fieldId) {
				if relatedValue, ok := m.values[relatedField]; ok {
					result = append(result, &fePalPb.CollectFormDataRequest_FormFieldValue{
						FieldId:    relatedField,
						FieldValue: relatedValue.FieldValue.FieldValue,
					})
				}
			}
		}
	}

	return result, nil
}

// GetFieldIdsFromFormOptions extracts field IDs from form options
func GetFieldIdsFromFormOptions(formOptions *plScreenOptionsPb.LoansFormEntryScreenOptions) []string {
	var fieldIds []string
	for _, field := range formOptions.GetFormFields() {
		if field.GetFieldType() != "" {
			fieldIds = append(fieldIds, field.GetFieldType())
		}
	}
	return fieldIds
}

// NewFormFieldMap creates a new FormFieldMap with predefined values
func NewFormFieldMap(panNumber string) *FormFieldMap {
	return &FormFieldMap{
		values: map[string]*FormFieldValue{
			"NAME": {
				FieldId:   "NAME",
				FieldType: FieldTypeString,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_StringValue{
						StringValue: "Test User",
					},
				},
			},
			"DOB": {
				FieldId:   "DOB",
				FieldType: FieldTypeDate,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_DateValue{
						DateValue: &typesv2.Date{
							Day:   15,
							Month: 8,
							Year:  2001,
						},
					},
				},
			},
			"PAN": {
				FieldId:   "PAN",
				FieldType: FieldTypeString,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_StringValue{
						StringValue: panNumber,
					},
				},
			},
			"GENDER": {
				FieldId:   "GENDER",
				FieldType: FieldTypeSelect,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
						SelectedOptionId: "MALE",
					},
				},
			},
			"MARITAL_STATUS": {
				FieldId:   "MARITAL_STATUS",
				FieldType: FieldTypeSelect,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
						SelectedOptionId: "MARRIED",
					},
				},
			},
			"PINCODE": {
				FieldId:   "PINCODE",
				FieldType: FieldTypeInt,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_IntValue{
						IntValue: 422003,
					},
				},
			},
			"ADDRESS_TYPE": {
				FieldId:   "ADDRESS_TYPE",
				FieldType: FieldTypeSelect,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
						SelectedOptionId: "RESIDENCE_TYPE_OWNED",
					},
				},
			},
			"LOAN_PURPOSE": {
				FieldId:   "LOAN_PURPOSE",
				FieldType: FieldTypeSelect,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
						SelectedOptionId: "LOAN_PURPOSE_INVESTMENT",
					},
				},
			},
			"DESIRED_LOAN_AMOUNT": {
				FieldId:   "DESIRED_LOAN_AMOUNT",
				FieldType: FieldTypeMoney,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_MoneyValue{
						MoneyValue: &typesv2.Money{
							CurrencyCode: "INR",
							Units:        50000,
						},
					},
				},
			},
			"EMPLOYMENT_TYPE": {
				FieldId:   "EMPLOYMENT_TYPE",
				FieldType: FieldTypeSelect,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
						SelectedOptionId: "EMPLOYMENT_TYPE_SALARIED",
					},
				},
			},
			"EMPLOYER_NAME": {
				FieldId:   "EMPLOYER_NAME",
				FieldType: FieldTypeSelect,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
						SelectedOptionId: "EPIFI TECHNOLOGIES PRIVATE LIMITED",
					},
				},
			},
			"WORK_EMAIL": {
				FieldId:   "WORK_EMAIL",
				FieldType: FieldTypeString,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_StringValue{
						StringValue: "<EMAIL>",
					},
				},
			},
			"MONTHLY_INCOME": {
				FieldId:   "MONTHLY_INCOME",
				FieldType: FieldTypeMoney,
				FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue{
					FieldValue: &fePalPb.CollectFormDataRequest_FormFieldValue_MoneyValue{
						MoneyValue: &typesv2.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
					},
				},
			},
		},
	}
}

// processLoanFormJourney handles the form submission and polling flow
func processLoanFormJourney(ctx context.Context, palTs *PlJobSuite, reqH *header.RequestHeader, panNumber string, stage TestStage) error {
	loanHeader := &palEnums.LoanHeader{
		LoanProgram: palEnums.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
		Vendor:      palEnums.Vendor_EPIFI_TECH,
	}

	// Create form field map with all possible values
	formFieldMap := NewFormFieldMap(panNumber)

	checkEligibilityResp, checkErr := palTs.fePalClient.CheckLoanEligibility(ctx, &fePalPb.CheckLoanEligibilityRequest{
		Req:        reqH,
		LoanHeader: loanHeader,
	})

	if checkErr != nil {
		return fmt.Errorf("CheckLoanEligibility API error: %v", checkErr)
	}

	if checkEligibilityResp.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
		return fmt.Errorf("CheckLoanEligibility failed with status: %v", checkEligibilityResp.GetRespHeader().GetStatus().GetShortMessage())
	}

	var loanRequestId string
	if checkEligibilityResp.GetDeeplink() != nil &&
		checkEligibilityResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions() != nil {
		loanRequestId = checkEligibilityResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId()
	} else {
		return fmt.Errorf("no loan request ID found in CheckLoanEligibility response")
	}

	var nextAction = checkEligibilityResp.GetDeeplink()
	var response *fePalPb.GetApplicationStatusResponse
	var err error

	for {
		_, nextAction, err = pollApplicationStatus(ctx, palTs, reqH, loanRequestId, loanHeader, nextAction, []feDL.Screen{feDL.Screen_LOANS_FORM_ENTRY_SCREEN, feDL.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN})
		if err != nil {
			return err
		}

		if nextAction.GetScreen() != feDL.Screen_LOANS_FORM_ENTRY_SCREEN {
			break
		}

		// Get form options from the screen
		formOptions := &plScreenOptionsPb.LoansFormEntryScreenOptions{}
		if err := nextAction.GetScreenOptionsV2().UnmarshalTo(formOptions); err != nil {
			return fmt.Errorf("failed to unmarshal form entry screen options: %v", err)
		}

		// Get field IDs from form options
		fieldIds := GetFieldIdsFromFormOptions(formOptions)
		fmt.Printf("Processing form with fields: %v\n", fieldIds)

		// Get form field values for the current screen
		formFieldValues, err := formFieldMap.GetFormFieldValues(fieldIds)
		if err != nil {
			return fmt.Errorf("failed to get form field values: %v", err)
		}

		// Submit form data
		formDataResp, formErr := palTs.fePalClient.CollectFormData(ctx, &fePalPb.CollectFormDataRequest{
			Req:             reqH,
			LoanHeader:      loanHeader,
			LoanRequestId:   loanRequestId,
			FormFieldValues: formFieldValues,
		})

		if formErr != nil {
			return fmt.Errorf("CollectFormData API error: %v", formErr)
		}

		if formDataResp.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
			return fmt.Errorf("CollectFormData failed with status: %v", formDataResp.GetRespHeader().GetStatus().GetShortMessage())
		}

		if formDataResp.GetNextAction() != nil {
			nextAction = formDataResp.GetNextAction()
		}

		time.Sleep(2 * time.Second) // Add a small delay between form submissions
	}

	if nextAction.GetScreen() == feDL.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN {
		if stage == StageApplicationReview {
			return nil
		}

		submitReviewRes, err := palTs.submitReviewLoanDetails(ctx, &fePalPb.SubmitReviewLoanDetailsRequest{
			Req:           reqH,
			LoanHeader:    loanHeader,
			LoanRequestId: loanRequestId,
		})
		if err != nil {
			return err
		}

		if submitReviewRes == nil {
			return fmt.Errorf("SubmitReviewLoanDetails response is nil")
		}

		if submitReviewRes.GetRespHeader().GetStatus().GetShortMessage() != "Success" {
			return fmt.Errorf("unexpected status message: %v", submitReviewRes.GetRespHeader().GetStatus().GetShortMessage())
		}

		response, _, err = pollApplicationStatus(ctx, palTs, reqH, submitReviewRes.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId(), submitReviewRes.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanHeader(), submitReviewRes.GetDeeplink(), []feDL.Screen{feDL.Screen_CREDIT_REPORT_CONSENT_V2})
		if err != nil {
			return err
		}

		if stage == StageCreditReport {
			return nil
		}

		res, err := palTs.creditReportClient.RecordDownloadConsent(ctx, &creditreportpb.RecordDownloadConsentRequest{
			Req:           reqH,
			RequestId:     response.GetDeeplink().GetCreditReportConsentV2ScreenOptions().GetClientRequestId(),
			ConsentAction: creditreportpb.ConsentAction_CONSENT_ACTION_ACCEPTED,
		})
		if err != nil {
			return err
		}

		if res.GetRespHeader().GetStatus().GetCode() != rpc.StatusOk().GetCode() {
			return fmt.Errorf("unexpected status code: %v", res.GetRespHeader().GetStatus().GetCode())
		}

		if err := palTs.pollCreditReportDownloadStatus(ctx, response.GetDeeplink().GetCreditReportConsentV2ScreenOptions().GetClientRequestId(),
			reqH, feDL.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN); err != nil {
			return err
		}

		response, _, err = pollApplicationStatus(ctx, palTs, reqH, response.GetLoanRequestId(), loanHeader, submitReviewRes.GetDeeplink(), []feDL.Screen{feDL.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN})
		if err != nil {
			return err
		}

		if response.GetDeeplink() == nil || response.GetDeeplink().GetScreen() != feDL.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN {
			return fmt.Errorf("error occurred while processing the loan application: %v", response.GetRespHeader().GetStatus().GetShortMessage())
		}
		fmt.Println("Loan application completed successfully!")
		return nil

	}

	return nil
}

// pollApplicationStatus polls the application status and returns the next action
func pollApplicationStatus(ctx context.Context, palTs *PlJobSuite, reqH *header.RequestHeader, loanRequestId string, loanHeader *palEnums.LoanHeader, currentAction *feDL.Deeplink, expectedScreens []feDL.Screen) (*fePalPb.GetApplicationStatusResponse, *feDL.Deeplink, error) {
	appStatusResp := &fePalPb.GetApplicationStatusResponse{}
	appStatusErr := error(nil)
	if currentAction != nil && currentAction.GetScreen() == feDL.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN {

		maxAttempts := 30
		pollInterval := 2 * time.Second
		for attempt := 0; attempt < maxAttempts; attempt++ {
			appStatusResp, appStatusErr = palTs.fePalClient.GetApplicationStatus(ctx, &fePalPb.GetApplicationStatusRequest{
				Req:                reqH,
				LoanHeader:         loanHeader,
				LoanRequestId:      loanRequestId,
				ScreenName:         feDL.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
				RetryAttemptNumber: 1,
			})

			if appStatusErr != nil {
				return nil, nil, fmt.Errorf("GetApplicationStatus API error on attempt %d: %v", attempt+1, appStatusErr)
			}
			if appStatusResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions() != nil &&
				appStatusResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId() != "" &&
				appStatusResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId() != loanRequestId {

				// Log the change
				fmt.Printf("Loan request ID changed from %s to %s",
					loanRequestId,
					appStatusResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId())

				// Update the ID for subsequent requests
				loanRequestId = appStatusResp.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId()

				// Update the response's loan request ID field to ensure it gets propagated
				appStatusResp.LoanRequestId = loanRequestId
			}
			if appStatusResp.GetDeeplink() != nil {
				// Check if we've reached one of the expected screens
				screen := appStatusResp.GetDeeplink().GetScreen()
				for _, expectedScreen := range expectedScreens {
					if screen == expectedScreen {
						return appStatusResp, appStatusResp.GetDeeplink(), nil
					}
				}

			}

			time.Sleep(pollInterval)
		}
		// If we've exhausted all attempts and haven't reached an expected screen, return an error
		return nil, nil, fmt.Errorf("timed out waiting for expected screens %v after %d attempts. Last screen: %v", expectedScreens, maxAttempts, appStatusResp.GetDeeplink().GetScreen())
	}
	return appStatusResp, currentAction, nil
}

func (ts *PlJobSuite) submitReviewLoanDetails(ctx context.Context, req *fePalPb.SubmitReviewLoanDetailsRequest) (*fePalPb.SubmitReviewLoanDetailsResponse, error) {
	resp, err := ts.fePalClient.SubmitReviewLoanDetails(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("error while submitting review loan details: %v", err)
	}
	if resp == nil {
		return nil, fmt.Errorf("SubmitReviewLoanDetails response is nil")
	}
	return resp, nil
}

func A2LFlowJorney(ctx context.Context, dep *OnbDep, aUser *UserData, palTs *PlJobSuite, vendor palPb.Vendor, stage TestStage) error {
	// Use A2LUserCreation to get to the landing screen and get the request header
	reqH, err := A2LUserCreation(ctx, dep, aUser, palTs)
	if err != nil {
		return fmt.Errorf("TestA2LIntent failed: %v", err)
	}
	panNumber := "**********"
	switch vendor {
	case palPb.Vendor_MONEYVIEW:
		panNumber = "**********"
	case palPb.Vendor_FEDERAL:
		panNumber = "**********"
	case palPb.Vendor_ABFL:
		panNumber = "**********"
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		panNumber = "**********"
	case palPb.Vendor_LENDEN:
		panNumber = "**********"
	}
	// Now proceed with the loan form journey using the request header and stage
	return processLoanFormJourney(ctx, palTs, reqH, panNumber, stage)
}
