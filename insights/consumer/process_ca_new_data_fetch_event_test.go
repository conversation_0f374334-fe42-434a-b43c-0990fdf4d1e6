package consumer

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	caEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	caxPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/insights/consumer"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	catalogMocks "github.com/epifi/gamma/api/securities/catalog/mocks"
	aaHelper "github.com/epifi/gamma/insights/networth/assets/helper/aa"
	mockAAHelper "github.com/epifi/gamma/insights/networth/assets/helper/aa/mocks"
	mockNetWorthDao "github.com/epifi/gamma/insights/networth/dao/mocks"
)

func TestService_ProcessCaNewDataFetchEvent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAaBalanceFetcher := mockAAHelper.NewMockIAaBalanceFetcher(ctrl)
	mockAssetHistoryDao := mockNetWorthDao.NewMockAssetHistoryDao(ctrl)
	mockCatalogClient := catalogMocks.NewMockSecuritiesCatalogClient(ctrl)

	service := &Service{
		aaBalanceFetcher: mockAaBalanceFetcher,
		assetHistoryDao:  mockAssetHistoryDao,
		catalogClient:    mockCatalogClient,
	}

	type args struct {
		ctx   context.Context
		event *caxPb.AccountDataSyncEvent
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *consumer.ProcessCaNewDataFetchEventResponse
		wantErr    bool
	}{
		{
			name: "success case - EQUITIES instrument type",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor123",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
				},
			},
			setupMocks: func() {
				assetIdUnitsPairs := []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018", // TCS ISIN
						Units:   100,
					},
					{
						AssetId: "INE467B01029", // Infosys ISIN
						Units:   50,
					},
				}

				mockAaBalanceFetcher.EXPECT().
					GetAaAccountHoldings(gomock.Any(), "actor123", enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(assetIdUnitsPairs, nil)

				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "TCS_NSE",
						},
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							SecurityListingExternalId: "TCS_BSE",
						},
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE467B01029",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "INFY_NSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)

				expectedAssetHistory := &modelPb.AssetHistory{
					AssetType: enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
					Data: &modelPb.AssetData{
						AssetData: &modelPb.AssetData_IndianStocksData{
							IndianStocksData: &modelPb.IndianStocksData{
								IndianStocks: []*modelPb.IndianStock{
									{
										Units:             100.0,
										SecurityListingId: "TCS_BSE", // BSE should overwrite NSE
									},
									{
										Units:             50.0,
										SecurityListingId: "INFY_NSE",
									},
								},
							},
						},
					},
					ActorId: "actor123",
				}

				mockAssetHistoryDao.EXPECT().
					CreateOrUpdateMultipleHistories(gomock.Any(), gomock.Any()).
					Do(func(ctx context.Context, histories []*modelPb.AssetHistory) {
						if len(histories) != 1 {
							t.Errorf("Expected 1 history, got %d", len(histories))
						}
						history := histories[0]
						if history.AssetType != expectedAssetHistory.AssetType {
							t.Errorf("Expected asset type %v, got %v", expectedAssetHistory.AssetType, history.AssetType)
						}
						if history.ActorId != expectedAssetHistory.ActorId {
							t.Errorf("Expected actor ID %v, got %v", expectedAssetHistory.ActorId, history.ActorId)
						}
						// Check that HistoryDate is set
						if history.HistoryDate == nil {
							t.Error("HistoryDate should not be nil")
						}
						// Check Indian stocks data
						if history.Data.GetIndianStocksData() == nil {
							t.Error("IndianStocksData should not be nil")
						}
						stocks := history.Data.GetIndianStocksData().GetIndianStocks()
						if len(stocks) != 2 {
							t.Errorf("Expected 2 stocks, got %d", len(stocks))
						}
					}).
					Return([]*modelPb.AssetHistory{expectedAssetHistory}, nil)
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: success,
			},
			wantErr: false,
		},
		{
			name: "filtered out instrument type - should return success without processing",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor123",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT, // Not supported
				},
			},
			setupMocks: func() {
				// No mocks needed as it should return early
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: success,
			},
			wantErr: false,
		},
		{
			name: "no holdings found - should return success",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor123",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
				},
			},
			setupMocks: func() {
				mockAaBalanceFetcher.EXPECT().
					GetAaAccountHoldings(gomock.Any(), "actor123", enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: success,
			},
			wantErr: false,
		},
		{
			name: "error getting holdings - should return transient failure",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor123",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
				},
			},
			setupMocks: func() {
				mockAaBalanceFetcher.EXPECT().
					GetAaAccountHoldings(gomock.Any(), "actor123", enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(nil, errors.New("database error"))
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: transientFailure,
			},
			wantErr: false,
		},
		{
			name: "error getting asset data - should return transient failure",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor123",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
				},
			},
			setupMocks: func() {
				assetIdUnitsPairs := []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018",
						Units:   100,
					},
				}

				mockAaBalanceFetcher.EXPECT().
					GetAaAccountHoldings(gomock.Any(), "actor123", enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(assetIdUnitsPairs, nil)

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("catalog service error"))
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: transientFailure,
			},
			wantErr: false,
		},
		{
			name: "error creating asset history - should return transient failure",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor123",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
				},
			},
			setupMocks: func() {
				assetIdUnitsPairs := []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018",
						Units:   100,
					},
				}

				mockAaBalanceFetcher.EXPECT().
					GetAaAccountHoldings(gomock.Any(), "actor123", enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(assetIdUnitsPairs, nil)

				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "TCS_NSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)

				mockAssetHistoryDao.EXPECT().
					CreateOrUpdateMultipleHistories(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error"))
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: transientFailure,
			},
			wantErr: false,
		},
		{
			name: "ETF instrument type - should process successfully",
			args: args{
				ctx: context.Background(),
				event: &caxPb.AccountDataSyncEvent{
					ActorId:           "actor456",
					AccInstrumentType: caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
				},
			},
			setupMocks: func() {
				assetIdUnitsPairs := []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INF204KB17I5", // Sample ETF ISIN
						Units:   25,
					},
				}

				mockAaBalanceFetcher.EXPECT().
					GetAaAccountHoldings(gomock.Any(), "actor456", enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(assetIdUnitsPairs, nil)

				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INF204KB17I5",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "NIFTYBEES_NSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)

				mockAssetHistoryDao.EXPECT().
					CreateOrUpdateMultipleHistories(gomock.Any(), gomock.Any()).
					Return([]*modelPb.AssetHistory{}, nil)
			},
			want: &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: success,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			got, err := service.ProcessCaNewDataFetchEvent(tt.args.ctx, tt.args.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessCaNewDataFetchEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessCaNewDataFetchEvent() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getAssetData(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCatalogClient := catalogMocks.NewMockSecuritiesCatalogClient(ctrl)

	service := &Service{
		catalogClient: mockCatalogClient,
	}

	type args struct {
		ctx       context.Context
		assetType enumsPb.AssetType
		unitPairs []*aaHelper.AssetIdUnitsPair
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *modelPb.AssetData
		wantErr    bool
	}{
		{
			name: "success case - INDIAN_SECURITIES",
			args: args{
				ctx:       context.Background(),
				assetType: enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018",
						Units:   100,
					},
				},
			},
			setupMocks: func() {
				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "TCS_NSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)
			},
			want: &modelPb.AssetData{
				AssetData: &modelPb.AssetData_IndianStocksData{
					IndianStocksData: &modelPb.IndianStocksData{
						IndianStocks: []*modelPb.IndianStock{
							{
								Units:             100.0,
								SecurityListingId: "TCS_NSE",
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "unsupported asset type - should return error",
			args: args{
				ctx:       context.Background(),
				assetType: enumsPb.AssetType_ASSET_TYPE_NPS, // Not implemented in getAssetData
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "SAMPLE_NPS_ID",
						Units:   1000,
					},
				},
			},
			setupMocks: func() {
				// No mocks needed as it should return error before calling anything
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error getting indian stocks",
			args: args{
				ctx:       context.Background(),
				assetType: enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018",
						Units:   100,
					},
				},
			},
			setupMocks: func() {
				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("catalog service error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			got, err := service.getAssetData(tt.args.ctx, tt.args.assetType, tt.args.unitPairs)
			if (err != nil) != tt.wantErr {
				t.Errorf("getAssetData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getAssetData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getIndianStocksFromUnitPairs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCatalogClient := catalogMocks.NewMockSecuritiesCatalogClient(ctrl)

	service := &Service{
		catalogClient: mockCatalogClient,
	}

	type args struct {
		ctx       context.Context
		unitPairs []*aaHelper.AssetIdUnitsPair
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       []*modelPb.IndianStock
		wantErr    bool
	}{
		{
			name: "success case - BSE overrides NSE",
			args: args{
				ctx: context.Background(),
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018", // TCS
						Units:   100,
					},
				},
			},
			setupMocks: func() {
				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "TCS_NSE",
						},
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							SecurityListingExternalId: "TCS_BSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)
			},
			want: []*modelPb.IndianStock{
				{
					Units:             100.0,
					SecurityListingId: "TCS_BSE", // BSE should override NSE
				},
			},
			wantErr: false,
		},
		{
			name: "success case - NSE only (no BSE)",
			args: args{
				ctx: context.Background(),
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE467B01029", // Infosys
						Units:   50,
					},
				},
			},
			setupMocks: func() {
				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE467B01029",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "INFY_NSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)
			},
			want: []*modelPb.IndianStock{
				{
					Units:             50.0,
					SecurityListingId: "INFY_NSE",
				},
			},
			wantErr: false,
		},
		{
			name: "empty security listing IDs - should return empty stocks",
			args: args{
				ctx: context.Background(),
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "UNKNOWN_ISIN",
						Units:   25,
					},
				},
			},
			setupMocks: func() {
				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status:                 &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)
			},
			want:    []*modelPb.IndianStock{},
			wantErr: false,
		},
		{
			name: "catalog service error",
			args: args{
				ctx: context.Background(),
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018",
						Units:   100,
					},
				},
			},
			setupMocks: func() {
				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("catalog service error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "multiple stocks with mixed exchanges",
			args: args{
				ctx: context.Background(),
				unitPairs: []*aaHelper.AssetIdUnitsPair{
					{
						AssetId: "INE002A01018", // TCS
						Units:   100,
					},
					{
						AssetId: "INE467B01029", // Infosys
						Units:   50,
					},
					{
						AssetId: "INE009A01021", // Bharti Airtel
						Units:   75,
					},
				},
			},
			setupMocks: func() {
				catalogResp := &catalogPb.GetSecListingIdsByISINsResponse{
					Status: &rpc.Status{Code: uint32(rpc.StatusOk().GetCode())},
					SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
						// TCS - both NSE and BSE
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "TCS_NSE",
						},
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE002A01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							SecurityListingExternalId: "TCS_BSE",
						},
						// Infosys - only NSE
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE467B01029",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
							SecurityListingExternalId: "INFY_NSE",
						},
						// Bharti Airtel - only BSE
						{
							IsinExchangePair: &catalogPb.ISINExchangePair{
								Isin:     "INE009A01021",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							SecurityListingExternalId: "BHARTIARTL_BSE",
						},
					},
				}

				mockCatalogClient.EXPECT().
					GetSecListingIdsByISINs(gomock.Any(), gomock.Any()).
					Return(catalogResp, nil)
			},
			want: []*modelPb.IndianStock{
				{
					Units:             100.0,
					SecurityListingId: "TCS_BSE", // BSE overrides NSE
				},
				{
					Units:             50.0,
					SecurityListingId: "INFY_NSE",
				},
				{
					Units:             75.0,
					SecurityListingId: "BHARTIARTL_BSE",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			got, err := service.getIndianStocksFromUnitPairs(tt.args.ctx, tt.args.unitPairs)
			if (err != nil) != tt.wantErr {
				t.Errorf("getIndianStocksFromUnitPairs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getIndianStocksFromUnitPairs() = %v, want %v", got, tt.want)
			}
		})
	}
}
