package impl

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/insights/config"
	"github.com/epifi/gamma/insights/networth/dao"
	"github.com/epifi/gamma/insights/test"
)

var (
	invDeclarationTS         *investmentDeclarationTestSuite
	newInvestmentDeclaration = &modelPb.InvestmentDeclaration{
		ActorId:        "actor-2",
		InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
		InvestedAmount: money.AmountINR(20).GetPb(),
		InvestedAt:     timestamp.New(time.Date(2022, 2, 21, 2, 4, 5, 0, datetime.IST)),
		InterestRate:   3.7,
		DeclarationDetails: &modelPb.OtherDeclarationDetails{
			Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
				FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
					CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_AT_MATURITY,
				}}},
	}
	newInvestmentDeclaration2 = &modelPb.InvestmentDeclaration{
		ActorId:        "actor-3",
		InstrumentType: types.InvestmentInstrumentType_ART_AND_ARTEFACTS,
		InvestedAmount: money.AmountINR(20).GetPb(),
		InvestedAt:     timestamp.New(time.Date(2022, 2, 21, 2, 4, 5, 0, datetime.IST)),
		InterestRate:   3.7,
		DeclarationDetails: &modelPb.OtherDeclarationDetails{
			Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
				FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
					CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_AT_MATURITY,
				}}},
		Source: modelPb.Source_SOURCE_MAGIC_IMPORT,
	}
	alreadyExistsDeclarations = []*modelPb.InvestmentDeclaration{
		{
			Id:             "037b37d3-07e2-463a-a456-df7304f2a354",
			ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a355",
			ActorId:        "actor-1",
			InstrumentType: types.InvestmentInstrumentType_MUTUAL_FUNDS,
			InvestedAmount: money.AmountINR(100).GetPb(),
			InvestedAt:     timestamp.New(time.Date(2022, 11, 21, 2, 4, 5, 0, datetime.IST)),
			InterestRate:   7.5,
			DeclarationDetails: &modelPb.OtherDeclarationDetails{
				Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
					FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
						CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_DAILY,
					}}},
		},
		{
			Id:             "137b37d3-07e2-463a-a456-df7304f2a351",
			ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a356",
			ActorId:        "actor-1",
			InstrumentType: types.InvestmentInstrumentType_SMART_DEPOSIT,
			InvestedAmount: money.AmountINR(23).GetPb(),
			InvestedAt:     timestamp.New(time.Date(2022, 11, 21, 2, 4, 5, 0, datetime.IST)),
			InterestRate:   7.34,
			DeclarationDetails: &modelPb.OtherDeclarationDetails{
				Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
					FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
						CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY,
					}}},
		},
		{
			Id:             "199bc000-5c0d-11ed-9b6a-0242ac120077",
			ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a357",
			ActorId:        "actor-1",
			InstrumentType: types.InvestmentInstrumentType_RECURRING_DEPOSIT,
			InvestedAmount: money.AmountINR(324).GetPb(),
			InvestedAt:     timestamp.New(time.Date(2022, 12, 21, 2, 4, 5, 0, datetime.IST)),
			InterestRate:   35.34,
			DeclarationDetails: &modelPb.OtherDeclarationDetails{
				Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
					FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
						CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY,
					}}},
		},
		{
			Id:             "199bc000-5c0d-11ed-9b6a-0242ac120004",
			ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a358",
			ConsentId:      "037b37d3-07e2-463a-a456-df7304f2a555",
			ActorId:        "actor-1",
			InstrumentType: types.InvestmentInstrumentType_RECURRING_DEPOSIT,
			InvestedAmount: money.AmountINR(34).GetPb(),
			InvestedAt:     timestamp.New(time.Date(2022, 12, 21, 2, 4, 5, 0, datetime.IST)),
			InterestRate:   35,
			DeclarationDetails: &modelPb.OtherDeclarationDetails{
				Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
					FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
						CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY,
					}}},
		},
		{
			Id:             "199bc000-5c0d-11ed-9b6a-0242ac120005",
			ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a359",
			ActorId:        "actor-1",
			InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
			InvestedAmount: money.AmountINR(34).GetPb(),
			InvestedAt:     timestamp.New(time.Date(2022, 12, 21, 2, 4, 5, 0, datetime.IST)),
			InterestRate:   3.345,
			DeclarationDetails: &modelPb.OtherDeclarationDetails{
				Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
					FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
						CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY,
					}}},
		},
		{
			Id:             "199bc000-5c0d-11ed-9b6a-0242ac120006",
			ActorId:        "actor-2",
			InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
			InvestedAmount: money.AmountINR(43).GetPb(),
			InvestedAt:     timestamp.New(time.Date(2022, 12, 21, 2, 4, 5, 0, datetime.IST)),
			InterestRate:   3.345,
			DeclarationDetails: &modelPb.OtherDeclarationDetails{
				Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
					FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
						CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY,
					}}},
		},
	}
)

type investmentDeclarationTestSuite struct {
	db   *gormv2.DB
	dao  dao.InvestmentDeclarationDao
	conf *config.Config
}

func newInvestmentDeclarationTestSuite(db *gormv2.DB, dao dao.InvestmentDeclarationDao, conf *config.Config) *investmentDeclarationTestSuite {
	return &investmentDeclarationTestSuite{
		db:   db,
		dao:  dao,
		conf: conf,
	}
}

func TestInvestmentDeclarationPGDB_Create(t *testing.T) {
	type args struct {
		ctx                   context.Context
		investmentDeclaration *modelPb.InvestmentDeclaration
	}
	tests := []struct {
		name    string
		args    args
		want    *modelPb.InvestmentDeclaration
		wantErr bool
	}{
		{
			name: "successful create",
			args: args{
				ctx:                   context.Background(),
				investmentDeclaration: newInvestmentDeclaration,
			},
			want:    newInvestmentDeclaration,
			wantErr: false,
		},
		{
			name: "successful create with source",
			args: args{
				ctx:                   context.Background(),
				investmentDeclaration: newInvestmentDeclaration2,
			},
			want:    newInvestmentDeclaration2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)
			got, err := invDeclarationTS.dao.Create(tt.args.ctx, tt.args.investmentDeclaration)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&modelPb.InvestmentDeclaration{}, "id", "external_id", "created_at", "updated_at"),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestInvestmentDeclarationPGDB_GetByActorAndFilters(t *testing.T) {
	type args struct {
		ctx       context.Context
		actorId   string
		pageToken *pagination.PageToken
		pageSize  uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.InvestmentDeclaration
		want1   *rpc.PageContextResponse
		want2   []*modelPb.InvestmentDeclaration
		want3   *rpc.PageContextResponse
		wantErr bool
	}{
		{
			name: "single page resp",
			args: args{
				ctx:       context.Background(),
				pageToken: nil,
				pageSize:  5,
				actorId:   alreadyExistsDeclarations[0].GetActorId(),
			},
			want: []*modelPb.InvestmentDeclaration{
				alreadyExistsDeclarations[0],
				alreadyExistsDeclarations[1],
				alreadyExistsDeclarations[2],
				alreadyExistsDeclarations[3],
				alreadyExistsDeclarations[4],
			},
			want1: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "multiple page resp",
			args: args{
				ctx:       context.Background(),
				pageToken: nil,
				pageSize:  3,
				actorId:   alreadyExistsDeclarations[0].GetActorId(),
			},
			want: []*modelPb.InvestmentDeclaration{
				alreadyExistsDeclarations[0],
				alreadyExistsDeclarations[1],
				alreadyExistsDeclarations[2],
			},
			want1: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				HasAfter:    true,
				AfterToken:  "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY5MjkwODIzMCwibmFub3MiOjgzMzAwMDAwMH0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
			want2: []*modelPb.InvestmentDeclaration{
				alreadyExistsDeclarations[3],
				alreadyExistsDeclarations[4],
			},
			want3: &rpc.PageContextResponse{
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY5MjkwODMwMiwibmFub3MiOjU5ODAwMDAwMH0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjp0cnVlfQ==",
				HasBefore:   true,
				HasAfter:    false,
				AfterToken:  "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)
			got, got1, err := invDeclarationTS.dao.GetByActorAndFilters(tt.args.ctx, tt.args.actorId, tt.args.pageToken, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorAndFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&modelPb.InvestmentDeclaration{}, "created_at", "updated_at"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByActorAndFilters() got = %v,\n want %v \n diff = %v", got, tt.want, diff)
			}
			if diff := cmp.Diff(got1, tt.want1, opts...); diff != "" {
				t.Errorf("GetByActorAndFilters() got1 = %v,\n want1 %v", got1, tt.want1)
			}
			if got1.GetHasAfter() {
				pt, ptErr := pagination.GetPageToken(&rpc.PageContextRequest{
					Token:    &rpc.PageContextRequest_AfterToken{AfterToken: got1.GetAfterToken()},
					PageSize: tt.args.pageSize,
				})
				if ptErr != nil {
					t.Errorf("error in getting page token: %v", ptErr)
					return
				}
				got2, got3, err := invDeclarationTS.dao.GetByActorAndFilters(tt.args.ctx, tt.args.actorId, pt, tt.args.pageSize)
				if err != nil {
					t.Errorf("error in GetByActorAndFilters: %v", err)
					return
				}
				if diff := cmp.Diff(got2, tt.want2, opts...); diff != "" {
					t.Errorf("GetByActorAndFilters() got2 = %v,\n want2 %v\n diff %s", got2, tt.want2, diff)
				}
				if diff := cmp.Diff(got3, tt.want3, opts...); diff != "" {
					t.Errorf("GetByActorAndFilters() got3 = %v,\n want3 %v\n diff %s", got3, tt.want3, diff)
				}
			}
		})
	}
}

func TestInvestmentDeclarationPGDB_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *modelPb.InvestmentDeclaration
		wantErr bool
		err     error
	}{
		{
			name: "successful get",
			args: args{
				ctx: context.Background(),
				id:  alreadyExistsDeclarations[0].GetId(),
			},
			want:    alreadyExistsDeclarations[0],
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				id:  "d09e5b74-9abf-11ec-b909-0242ac120001",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)
			got, err := invDeclarationTS.dao.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&modelPb.InvestmentDeclaration{}, "created_at", "updated_at"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetById() got = %v,\n want %v \n diff %v", got, tt.want, diff)
			}
			if err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() gotErr = %v,\n wantErr %v", err, tt.err)
				}
			}
		})
	}
}

func TestInvestmentDeclarationPGDB_UpdateByActorIdExternalId(t *testing.T) {
	type args struct {
		ctx                   context.Context
		investmentDeclaration *modelPb.InvestmentDeclaration
		fieldMasks            []modelPb.InvestmentDeclarationFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *modelPb.InvestmentDeclaration
		wantErr bool
	}{
		{
			name: "successful update",
			args: args{
				ctx: context.Background(),
				investmentDeclaration: func() *modelPb.InvestmentDeclaration {
					return &modelPb.InvestmentDeclaration{
						ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a360",
						ActorId:        "actor-2",
						InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
						InvestedAmount: money.AmountINR(20).GetPb(),
						InvestedAt:     timestamp.New(time.Date(2022, 2, 21, 2, 4, 5, 0, datetime.IST)),
						InterestRate:   3.7,
						DeclarationDetails: &modelPb.OtherDeclarationDetails{
							Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
								FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
									CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_AT_MATURITY,
								}}},
					}
				}(),
				fieldMasks: []modelPb.InvestmentDeclarationFieldMask{
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_DECLARATION_DETAILS,
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE,
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT,
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT,
				},
			},
			want: &modelPb.InvestmentDeclaration{
				ActorId:        "actor-2",
				InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
				InvestedAmount: money.AmountINR(20).GetPb(),
				InvestedAt:     timestamp.New(time.Date(2022, 2, 21, 2, 4, 5, 0, datetime.IST)),
				InterestRate:   3.7,
				DeclarationDetails: &modelPb.OtherDeclarationDetails{
					Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
						FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
							CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_AT_MATURITY,
						}}},
				ExternalId: "037b37d3-07e2-463a-a456-df7304f2a360",
			},
			wantErr: false,
		},
		{
			name: "invalid actorId or externalId, record not found error",
			args: args{
				ctx: context.Background(),
				investmentDeclaration: &modelPb.InvestmentDeclaration{
					ActorId:        "actor-55",
					ExternalId:     "037b37d3-07e2-463a-a456-df7304f2a360",
					InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
					InvestedAmount: money.AmountINR(20).GetPb(),
					InvestedAt:     timestamp.New(time.Date(2022, 2, 21, 2, 4, 5, 0, datetime.IST)),
					InterestRate:   3.7,
				},
				fieldMasks: []modelPb.InvestmentDeclarationFieldMask{
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE,
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT,
					modelPb.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)
			got, err := invDeclarationTS.dao.UpdateByActorIdExternalId(tt.args.ctx, tt.args.investmentDeclaration, tt.args.fieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&modelPb.InvestmentDeclaration{}, "created_at", "updated_at", "id"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetById() got = %v,\n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}

func TestInvestmentDeclarationPGDB_DeleteByActorIdExternalId(t *testing.T) {
	tests := []struct {
		name       string
		externalId string
		actorId    string
		wantErr    bool
	}{
		{
			name:       "Successfully delete declaration",
			externalId: "037b37d3-07e2-463a-a456-df7304f2a360",
			actorId:    "actor-2",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)
			if err := invDeclarationTS.dao.DeleteByActorIdExternalId(context.Background(), tt.actorId, tt.externalId); (err != nil) != tt.wantErr {
				t.Fatalf("DeleteByExternalId() error = %v, wantErr %v", err, tt.wantErr)
			}
			_, err := invDeclarationTS.dao.GetByActorIdExternalId(context.Background(), tt.actorId, tt.externalId)
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				t.Errorf("DeleteByExternalIdP() expected record not found error after deleting the declaration")
			}
		})
	}
}

func TestInvestmentDeclarationPGDB_GetByActorIdExternalId(t *testing.T) {
	tests := []struct {
		name       string
		externalId string
		actorId    string
		want       *modelPb.InvestmentDeclaration
		wantErr    bool
	}{
		{
			name:       "Successfully get declaration by external id",
			externalId: "037b37d3-07e2-463a-a456-df7304f2a360",
			actorId:    "actor-2",
			want: &modelPb.InvestmentDeclaration{
				Id:             "199bc000-5c0d-11ed-9b6a-0242ac120006",
				ActorId:        "actor-2",
				InstrumentType: types.InvestmentInstrumentType_US_STOCKS,
				InvestedAmount: money.AmountINR(43).GetPb(),
				InvestedAt:     timestamp.New(time.Date(2022, 12, 20, 20, 0, 0, 0, time.UTC)),
				InterestRate:   3.345,
				DeclarationDetails: &modelPb.OtherDeclarationDetails{
					Details: &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
						FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
							CompoundingFrequency: modelPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY,
						}}},
				ExternalId: "037b37d3-07e2-463a-a456-df7304f2a360",
			},
		},
		{
			name:       "Invalid external id should cause not found error",
			externalId: "037b37d3-07e2-463a-a456-df7304f2a900",
			actorId:    "actor-id",
			wantErr:    true,
		},
		{
			name:       "Empty external id should lead to error",
			externalId: "037b37d3-07e2-463a-a456-df7304f2a900",
			actorId:    "actor-id",
			wantErr:    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)
			got, err := invDeclarationTS.dao.GetByActorIdExternalId(context.Background(), tt.actorId, tt.externalId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByExternalId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&modelPb.InvestmentDeclaration{}, "created_at", "updated_at"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByExternalId() got = %v,\n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}

func TestInvestmentDeclarationPGDB_DeleteByActorId(t *testing.T) {
	tests := []struct {
		name    string
		actorId string
		wantErr bool
		errType error
		setup   func(t *testing.T)
	}{
		{
			name:    "Empty actor id should return error",
			actorId: "",
			wantErr: true,
			errType: fmt.Errorf("actor id cannot be empty"),
		},
		{
			name:    "Successfully delete all declarations for actor",
			actorId: "actor-2",
			wantErr: false,
			setup: func(t *testing.T) {
				// Verify test data exists before deletion
				declarations, _, err := invDeclarationTS.dao.GetByActorAndFilters(
					context.Background(),
					"actor-2",
					nil,
					1000,
				)
				if err != nil {
					t.Fatalf("Setup failed: could not fetch initial declarations: %v", err)
				}
				if len(declarations) == 0 {
					t.Fatalf("Setup failed: no declarations found for actor-2 before deletion")
				}
			},
		},
		{
			name:    "Non-existent actor id should return ErrRecordNotFound",
			actorId: "non-existent-actor",
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
		{
			name:    "Delete already deleted records should return ErrRecordNotFound",
			actorId: "actor-2",
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			// Reset database state
			pkgTest.TruncateAndPopulateRdsFixtures(t, invDeclarationTS.db, invDeclarationTS.conf.InsightsDb.GetName(), test.InsightsTables)

			// Run setup if provided
			if tt.setup != nil {
				tt.setup(t)
			}

			if tt.name == "Delete already deleted records should return ErrRecordNotFound" {
				if err := invDeclarationTS.dao.DeleteByActorId(context.Background(), tt.actorId); err != nil {
					t.Fatalf("Failed to setup test: %v", err)
				}
			}

			// Execute deletion
			err := invDeclarationTS.dao.DeleteByActorId(context.Background(), tt.actorId)

			// Check error condition
			if (err != nil) != tt.wantErr {
				t.Fatalf("DeleteByActorId() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.wantErr && err != nil && tt.errType != nil {
				if err.Error() != tt.errType.Error() {
					t.Errorf("DeleteByActorId() error = %v, wantErrType %v", err, tt.errType)
				}
				return
			}

			// For successful deletion cases, verify the records are actually deleted
			if !tt.wantErr {
				// Wait a short time to ensure deletion is complete
				time.Sleep(100 * time.Millisecond)

				declarations, _, err := invDeclarationTS.dao.GetByActorAndFilters(
					context.Background(),
					tt.actorId,
					nil,
					1000,
				)

				if err != nil {
					if !errors.Is(err, epifierrors.ErrRecordNotFound) {
						t.Errorf("Expected ErrRecordNotFound after deletion, got: %v", err)
					}
				} else if len(declarations) > 0 {
					t.Errorf("Expected no declarations after deletion, found %d declarations", len(declarations))
					for _, d := range declarations {
						t.Logf("Remaining declaration: ID=%s, ActorID=%s", d.Id, d.ActorId)
					}
				}
			}
		})
	}
}
