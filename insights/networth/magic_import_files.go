package networth

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/api/rpc"
	filePb "github.com/epifi/be-common/api/typesv2/common/file"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	usstocksScreenOpts "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/screen_options_v2"
	"github.com/epifi/gamma/insights/networth/events"
	"github.com/epifi/gamma/insights/networth/llm"
	magicimport "github.com/epifi/gamma/insights/networth/magic_import"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (s *Service) MagicImportFiles(ctx context.Context, req *networthPb.MagicImportFilesRequest) (*networthPb.MagicImportFilesResponse, error) {
	if err := s.validateReq(req); err != nil {
		var status *rpc.Status
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Info(ctx, "validation failed for MagicImportFilesRequest", zap.Error(err))
			status = rpc.StatusInvalidArgumentWithDebugMsg(err.Error())
		case errors.Is(err, epifierrors.ErrPermissionDenied):
			logger.Info(ctx, "validation failed for MagicImportFilesRequest", zap.Error(err))
			status = rpc.StatusPermissionDeniedWithDebugMsg(err.Error())
		default:
			logger.Error(ctx, "validation failed for MagicImportFilesRequest", zap.Error(err))
			status = rpc.StatusInternalWithDebugMsg(err.Error())
		}
		return &networthPb.MagicImportFilesResponse{
			Status: status,
		}, nil
	}
	var (
		response string
		err      error
	)
	response, err = s.llmClient.SendPromptWithFiles(ctx, &llm.SendPromptWithFilesRequest{
		Files:         req.GetFiles(),
		SystemContext: magicimport.GetMagicImportPrompt(),
		Model:         llm.Gemini_V1_5_FLASH,
	})
	if err != nil {
		logger.Error(ctx, "error in SendPromptWithImages", zap.Error(err))
		return &networthPb.MagicImportFilesResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewMagicImportFilesEvent(req.GetActorId(), response))
	})
	logger.Debug(ctx, "Cleaned Magic import response from LLM", zap.String("response", response))
	aiResp := &magicimport.MagicImportPromptResponse{}
	err = json.Unmarshal([]byte(response), &aiResp)
	if err != nil {
		logger.Error(ctx, "unmarshalling response failed", zap.Error(err))
		// todo(saiteja): fix this later, for now we are returning the response as is
	}

	return &networthPb.MagicImportFilesResponse{
		Status: rpc.StatusOk(),
		NextAction: &deeplink.Deeplink{
			Screen: deeplink.Screen_MAGIC_IMPORT_ASSETS_LIST_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&usstocksScreenOpts.MagicImportAssetsListScreenOptions{
				Title: response,
			}),
		},
	}, nil
}

func (s *Service) validateReq(req *networthPb.MagicImportFilesRequest) error {
	if len(req.GetFiles()) == 0 {
		return fmt.Errorf("no files in request %w", epifierrors.ErrInvalidArgument)
	}
	if req.GetActorId() == "" {
		return fmt.Errorf("actor id is empty %w", epifierrors.ErrInvalidArgument)
	}
	if len(req.GetFiles()) > 5 {
		return fmt.Errorf("maximum 5 files allowed, got %d files %w", len(req.GetFiles()), epifierrors.ErrPermissionDenied)
	}
	for _, file := range req.GetFiles() {
		if file.GetBase64Data() == "" || file.GetType() == filePb.FileType_FILE_TYPE_UNSPECIFIED {
			return fmt.Errorf("file base64 data or type is empty %w", epifierrors.ErrInvalidArgument)
		}
	}
	return nil
}
