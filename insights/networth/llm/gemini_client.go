package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"
	"golang.org/x/oauth2/google"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/mask"

	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"

	"github.com/epifi/be-common/pkg/logger"

	commontypes "github.com/epifi/be-common/api/typesv2/common/file"

	"github.com/epifi/gamma/insights/networth/llm/config"
)

var GeminiClientWireSet = wire.NewSet(NewGeminiClient, wire.Bind(new(LLMClient), new(*GeminiClient)), httpcontentredactor.GetInstance)

const (
	Gemini_V1_5_FLASH = "gemini-1.5-flash-002"
	Gemini_V2_5_FLASH = "gemini-2.5-flash-preview-05-20"
)

var (
	// https://cloud.google.com/vertex-ai/generative-ai/docs/learn/locations
	// Refer above docs to check locations and their allowed models
	modelToRegionMap = map[string]string{
		Gemini_V1_5_FLASH: "asia-south1",
		Gemini_V2_5_FLASH: "us-central1",
	}
)

type GeminiClient struct {
	geminiConf *config.GeminiConf
	redactor   *httpcontentredactor.HTTPContentRedactor
}

func NewGeminiClient(geminiConf *config.GeminiConf, redactor *httpcontentredactor.HTTPContentRedactor) *GeminiClient {
	return &GeminiClient{
		geminiConf: geminiConf,
		redactor:   redactor,
	}
}

var _ LLMClient = (*GeminiClient)(nil)

// todo: solve this in a proper way if required later (add in pkg/vendorapi)
func (s *GeminiClient) SendPromptWithFiles(ctx context.Context, req *SendPromptWithFilesRequest) (string, error) {
	// todo: optimize this to fetch when required
	accessToken, err := authenticateGCloudAndReturnAuthToken(ctx, s.geminiConf.CredentialsJson)
	if err != nil {
		return "", err
	}
	logger.Debug(ctx, "GCloud access token for GEMINI API", zap.String(logger.PAYLOAD, accessToken))
	var parts []interface{}

	// Add system message
	parts = append(parts, map[string]string{
		"text": req.GetSystemContext(),
	})

	// Add each image
	for _, file := range req.GetFiles() {
		mimeType, ok := getMimeType(file.GetType())
		if !ok {
			logger.Error(ctx, "unsupported file type for Gemini", zap.String("file_type", file.GetType().String()))
			continue
		}
		parts = append(parts, map[string]interface{}{
			"inline_data": map[string]string{
				"mime_type": mimeType,
				"data":      file.GetBase64Data(),
			},
		})
	}

	reqBody := map[string]interface{}{
		"contents": []map[string]interface{}{
			{
				"role":  "user",
				"parts": parts,
			},
		},
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error(ctx, "failed to marshal request body", zap.Error(err))
		return "", fmt.Errorf("failed to marshal request body: %w", err)
	}
	url, err := s.getUrl(req)
	if err != nil {
		return "", fmt.Errorf("failed to generate url: %w", err)
	}
	redactedRequest, redactErr := s.redactor.Redact(ctx, body, httpcontentredactor.ContentTypeJSON, map[string]mask.MaskingStrategy{
		"data": mask.MaskToStaticValue,
		"text": mask.MaskToStaticValue,
	})
	if redactErr != nil {
		logger.Error(ctx, "failed to redact request body", zap.Error(redactErr))
	}
	logger.Info(ctx, "Redacted raw request", zap.String(logger.URL, url), zap.String(logger.PAYLOAD, string(redactedRequest)))
	var (
		response  string
		handleErr error
	)
	for numAttempts := uint(0); numAttempts < 3; numAttempts++ {
		response, handleErr = s.handle(ctx, body, url, accessToken)
		if errors.Is(handleErr, epifierrors.ErrVgUnmarshal) {
			logger.Info(ctx, "vg unmarshal failed, retrying")
			time.Sleep(200 * time.Millisecond) // todo: have a custom retry config with millisecond accuracy
			continue
		}
		break
	}
	if handleErr != nil {
		logger.Error(ctx, "error in handling request", zap.Error(handleErr))
		return "", handleErr
	}
	return response, nil
}

func (s *GeminiClient) getUrl(req *SendPromptWithFilesRequest) (string, error) {
	region, ok := modelToRegionMap[req.GetModel()]
	if !ok {
		return "", fmt.Errorf("no region set for model %v", req.GetModel())
	}

	return fmt.Sprintf("https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/%v:generateContent", region, s.geminiConf.ProjectId, region, req.GetModel()), nil
}

func (s *GeminiClient) handle(ctx context.Context, body []byte, url string, accessToken string) (string, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		logger.Error(ctx, "error creating request for Gemini", zap.Error(err))
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request to Gemini: %w", err)
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	if resp.StatusCode != http.StatusOK {
		responseBody, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return "", fmt.Errorf("failed to read response body: %w", err)
		}
		logger.Error(ctx, "Raw response with non ok status code",
			zap.String(logger.STATUS, resp.Status),
			zap.String(logger.URL, url),
			zap.String(logger.PAYLOAD, string(responseBody)))
		return "", fmt.Errorf("request failed: %s", resp.Status)
	}

	var parsed struct {
		Candidates []struct {
			Content struct {
				Parts []struct {
					Text string `json:"text"`
				} `json:"parts"`
			} `json:"content"`
		} `json:"candidates"`
	}

	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}
	logger.Debug(ctx, "Raw response", zap.String(logger.PAYLOAD, string(responseBody)), zap.String(logger.URL, url))
	redactedResponse, err := s.redactor.Redact(ctx, responseBody, httpcontentredactor.ContentTypeJSON, map[string]mask.MaskingStrategy{
		"invested_value": mask.MaskToStaticValue,
	})
	if err != nil {
		logger.Error(ctx, "failed to redact response body", zap.Error(err))
	}
	logger.Info(ctx, "Redacted raw response", zap.String(logger.PAYLOAD, string(redactedResponse)), zap.String(logger.URL, url))

	if err = json.Unmarshal(responseBody, &parsed); err != nil {
		logger.Error(ctx, "failed to decode responseBody", zap.Error(err))
		return "", fmt.Errorf("failed to decode responseBody: %w %w", err, epifierrors.ErrVgUnmarshal)
	}

	if len(parsed.Candidates) == 0 || len(parsed.Candidates[0].Content.Parts) == 0 {
		logger.Error(ctx, "empty response from gemini", zap.Error(epifierrors.ErrMissingVals))
		return "", epifierrors.ErrMissingVals
	}
	response := parsed.Candidates[0].Content.Parts[0].Text
	/* LLMs generally give response in the below format, so cleaning them to give callers an expected JSON string
		```json
	     {
	        ....
		 }
		```
	*/
	response = strings.TrimPrefix(response, "\n")
	response = strings.TrimPrefix(response, "```json")
	response = strings.TrimPrefix(response, "\n")

	response = strings.TrimSuffix(response, "\n")
	response = strings.TrimSuffix(response, "```")
	response = strings.TrimSuffix(response, "\n")
	return response, nil
}

func getMimeType(fileType commontypes.FileType) (string, bool) {
	fileTypeToMimeType := map[commontypes.FileType]string{
		commontypes.FileType_FILE_TYPE_JPEG: "image/jpeg",
		commontypes.FileType_FILE_TYPE_JPG:  "image/jpg",
		commontypes.FileType_FILE_TYPE_PDF:  "application/pdf",
		commontypes.FileType_FILE_TYPE_TIFF: "image/tiff",
	}
	mimeType, exists := fileTypeToMimeType[fileType]
	return mimeType, exists
}

func authenticateGCloudAndReturnAuthToken(ctx context.Context, credentialsJson string) (string, error) {
	creds, err := google.CredentialsFromJSON(ctx, []byte(credentialsJson), "https://www.googleapis.com/auth/cloud-platform")
	if err != nil {
		logger.Error(ctx, "error while authenticating gcloud credentials", zap.Error(err))
		return "", err
	}

	// Create an OAuth2 token source
	tokenSource := creds.TokenSource

	// Obtain the access token
	token, err := tokenSource.Token()
	if err != nil {
		logger.Error(ctx, "Failed to obtain access token", zap.Error(err))
		return "", err
	}

	return token.AccessToken, nil
}
