package utils

import (
	"context"
	"fmt"
	"sort"

	mfUtils "github.com/epifi/gamma/pkg/networth"

	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
)

type AssetMoverTileInfo struct {
	Name             string
	ReturnPercentage float64
	ReturnAmount     *moneyPb.Money
}

type GetTopGainerAndTopLoserRequest struct {
	AnalyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable
	AnalysisVariables   []analyserVariablePb.AnalysisVariableName
	TopGainers          int
	TopLosers           int
}

func GetTopGainerAndTopLoser(ctx context.Context, req *GetTopGainerAndTopLoserRequest) ([]*AssetMoverTileInfo, []*AssetMoverTileInfo, error) {
	var assetMovers, topGainers, topLosers []*AssetMoverTileInfo
	for _, analyserVariable := range req.AnalysisVariables {
		// TODO(amrit): move switch case to strategy
		switch analyserVariable {
		case analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS:
			mfSchemeAnalytics, ok := req.AnalyserVariableMap[analyserVariable]
			if !ok {
				return nil, nil, fmt.Errorf("MF scheme analytics not found in analysis variable map while building top movers section")
			}

			for _, scheme := range mfSchemeAnalytics.GetMfSecretsSchemeAnalytics().GetSchemeAnalytics() {
				dailyReturnPercentage := mfUtils.CalculateNavChangePercentage(ctx, scheme.GetSchemeDetail())
				assetMovers = append(assetMovers, &AssetMoverTileInfo{
					Name:             scheme.GetSchemeDetail().GetNameData().GetDisplayName(),
					ReturnPercentage: dailyReturnPercentage,
					ReturnAmount:     money.Multiply(scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails().GetCurrentValue(), decimal.NewFromFloat(dailyReturnPercentage/(100+dailyReturnPercentage))),
				})
			}
		case analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION:
			mfWeeklyVariable, ok := req.AnalyserVariableMap[analyserVariable]
			if !ok {
				return nil, nil, fmt.Errorf("MF weekly distribution not found in analysis variable map while building top movers section")
			}
			weeklyChangeMap := mfUtils.WeeklyChangeMapCreation(mfWeeklyVariable.GetMfWeeklyDistribution().GetDayChangeResponse().GetAssetsValueChange())

			for _, scheme := range mfWeeklyVariable.GetMfWeeklyDistribution().GetDayChangeResponse().GetAssetsValueChange() {
				percentageChange := mfUtils.CalculateWeeklyPercentageChange(weeklyChangeMap[scheme.GetAssetId()])
				assetMovers = append(assetMovers, &AssetMoverTileInfo{
					Name:             mfWeeklyVariable.GetMfWeeklyDistribution().GetSecurityMetadataMap()[scheme.GetAssetId()].GetSecurityName(),
					ReturnPercentage: percentageChange,
					ReturnAmount:     money.ParseFloat(scheme.GetChange(), money.RupeeCurrencyCode),
				})
			}
		}
	}

	sort.Slice(assetMovers, func(i, j int) bool {
		a, _ := money.ToDecimal(assetMovers[i].ReturnAmount).Float64()
		b, _ := money.ToDecimal(assetMovers[j].ReturnAmount).Float64()
		return a > b
	})

	for i := 0; i < min(req.TopGainers, len(assetMovers)); i++ {
		if assetMovers[i].ReturnPercentage > 0 {
			topGainers = append(topGainers, assetMovers[i])
		}
	}

	for i := len(assetMovers) - 1; i >= max(0, len(assetMovers)-req.TopLosers); i-- {
		if assetMovers[i].ReturnPercentage < 0 {
			topLosers = append(topLosers, assetMovers[i])
		}
	}
	return topGainers, topLosers, nil
}
