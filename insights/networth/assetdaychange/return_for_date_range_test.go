package assetdaychange

import (
	"fmt"
	"math"
	"testing"
	"time"

	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
)

// floatEquals checks if two floats are equal within a small tolerance.
func floatEquals(a, b float64) bool {
	const epsilon = 1e-6
	return math.Abs(a-b) < epsilon
}

// Note: All types (Lot, InvestmentReturnForDateRangeRequest, InvestmentReturnForDateRangeResponse, calculateInvestmentReturnForDateRange)
// are defined in return_for_date_range.go in the same package. No import is needed. The logic and tests here are generic for any date range, not just weekly returns.
func TestCalculateInvestmentReturnForDateRange(t *testing.T) {
	date := func(yyyy, mm, dd int) time.Time {
		return time.Date(yyyy, time.Month(mm), dd, 0, 0, 0, 0, time.UTC)
	}

	makeMoney := func(amount float64) *money.Money {
		return &money.Money{
			CurrencyCode: "INR",
			Units:        int64(amount),
			Nanos:        int32((amount - float64(int64(amount))) * 1e9),
		}
	}

	makeTransaction := func(date time.Time, units float64, nav float64) *mfExternalPb.MutualFundExternalOrder {
		orderType := mfExternalPb.ExternalOrderType_BUY
		if units < 0 {
			orderType = mfExternalPb.ExternalOrderType_SELL
		}
		dateStr := date.Format("20060102")
		unitsStr := fmt.Sprintf("%.0f", units)
		return &mfExternalPb.MutualFundExternalOrder{
			Id:                "id-" + dateStr + "-" + unitsStr,
			ActorId:           "actorId-" + dateStr + "-" + unitsStr,
			IsinNumber:        "isin-" + dateStr + "-" + unitsStr,
			Amc:               1, // Use a valid enum value for Amc
			FolioId:           "folio-" + dateStr + "-" + unitsStr,
			ExternalOrderType: orderType,
			TransactionDate:   timestamppb.New(date),
			TransactionUnits:  units,
			PurchasePrice:     makeMoney(nav),
		}
	}

	tests := []struct {
		name    string
		input   *InvestmentReturnForDateRangeRequest
		want    *InvestmentReturnForDateRangeResponse
		wantErr bool
	}{
		{
			name: "Case 1: No Transactions in Date Range",
			// Calculation:
			// ReturnAmount = (N3 - N0) * U0 = (12 - 10) * 100 = 200
			// InitialPrice = U0 * N0 = 100 * 10 = 1000
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV:   makeMoney(10),
				FinalNAV:     makeMoney(12),
				FinalUnits:   100,
				Transactions: nil,
			},
			want: &InvestmentReturnForDateRangeResponse{
				ReturnAmount: 200,
				InitialPrice: 1000,
			},
			wantErr: false,
		},
		{
			name: "Case 2: Only Sell Transactions (some units left)",
			// Calculation:
			// ReturnAmount = (N3 - N0) * U3 = (12 - 10) * 80 = 160
			// InitialPrice = U3 * N0 = 80 * 10 = 800
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV: makeMoney(10),
				FinalNAV:   makeMoney(12),
				FinalUnits: 80,
				Transactions: []*mfExternalPb.MutualFundExternalOrder{
					makeTransaction(date(2024, 6, 2), -5, 10.5),
					makeTransaction(date(2024, 6, 3), -5, 10.7),
					makeTransaction(date(2024, 6, 4), -5, 10.8),
					makeTransaction(date(2024, 6, 5), -5, 11),
				},
			},
			want: &InvestmentReturnForDateRangeResponse{
				ReturnAmount: 160,
				InitialPrice: 800,
			},
			wantErr: false,
		},
		{
			name: "Case 3: All Holdings Sold (final units = 0)",
			// Calculation:
			// All units sold, so should error
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV: makeMoney(10),
				FinalNAV:   makeMoney(12),
				FinalUnits: 0,
				Transactions: []*mfExternalPb.MutualFundExternalOrder{
					makeTransaction(date(2024, 6, 2), -20, 10.5),
					makeTransaction(date(2024, 6, 3), -20, 10.7),
					makeTransaction(date(2024, 6, 4), -20, 10.8),
					makeTransaction(date(2024, 6, 5), -20, 11),
					makeTransaction(date(2024, 6, 6), -20, 11.1),
				},
			},
			want:    nil, // Should error
			wantErr: true,
		},
		{
			name: "Case 4.1: Fresh Buy with Existing Holdings",
			// Calculation:
			// For each buy: units * (N3 - buy.NAV)
			// (10 * (12-10.5)) + (10 * (12-10.7)) + (10 * (12-10.8)) + (10 * (12-11)) + (10 * (12-11.1)) = 15 + 13 + 12 + 10 + 9 = 59
			// InitialPrice = sum(buy.Units * buy.NAV) + U0 * N0 = 10*10.5 + 10*10.7 + 10*10.8 + 10*11 + 10*11.1 + 100*10 = 105 + 107 + 108 + 110 + 111 + 1000 = 1541
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV: makeMoney(10),
				FinalNAV:   makeMoney(12),
				FinalUnits: 150,
				Transactions: []*mfExternalPb.MutualFundExternalOrder{
					makeTransaction(date(2024, 6, 2), 10, 10.5),
					makeTransaction(date(2024, 6, 3), 10, 10.7),
					makeTransaction(date(2024, 6, 4), 10, 10.8),
					makeTransaction(date(2024, 6, 5), 10, 11),
					makeTransaction(date(2024, 6, 6), 10, 11.1),
				},
			},
			want: &InvestmentReturnForDateRangeResponse{
				ReturnAmount: 259,
				InitialPrice: 1541,
			},
			wantErr: false,
		},
		{
			name: "Case 4.2: Pure Fresh Buy (no prior holdings)",
			// Calculation:
			// For each buy: units * (N3 - buy.NAV)
			// (10 * (12-10.5)) + (10 * (12-10.7)) + (10 * (12-10.8)) + (10 * (12-11)) + (10 * (12-11.1)) = 15 + 13 + 12 + 10 + 9 = 59
			// InitialPrice = sum(buy.Units * buy.NAV) = 10*10.5 + 10*10.7 + 10*10.8 + 10*11 + 10*11.1 = 105 + 107 + 108 + 110 + 111 = 541
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV: makeMoney(10),
				FinalNAV:   makeMoney(12),
				FinalUnits: 50,
				Transactions: []*mfExternalPb.MutualFundExternalOrder{
					makeTransaction(date(2024, 6, 2), 10, 10.5),
					makeTransaction(date(2024, 6, 3), 10, 10.7),
					makeTransaction(date(2024, 6, 4), 10, 10.8),
					makeTransaction(date(2024, 6, 5), 10, 11),
					makeTransaction(date(2024, 6, 6), 10, 11.1),
				},
			},
			want: &InvestmentReturnForDateRangeResponse{
				ReturnAmount: 59,
				InitialPrice: 541,
			},
			wantErr: false,
		},
		{
			name: "Case 5.1: Partial Sell (U2 < U0)",
			// Calculation:
			// Initial holdings: 100 units at NAV 10
			// Buys: 10 units at NAV 11 (2024-06-02), 5 units at NAV 12 (2024-06-03)
			// Sells: 10 units (2024-06-04, NAV 13), 5 units (2024-06-05, NAV 14), 5 units (2024-06-06, NAV 15), 5 units (2024-06-07, NAV 16)
			// Final holdings: 90 units
			// FIFO: Sell 10 from initial, then 5 from initial, then 5 from buy1, then 5 from buy2
			// Remaining: 85 from initial, 5 from buy1
			// InitialPrice = (85*10) + (5*11) = 850 + 55 = 905
			// ReturnAmount = (85*(17-10)) + (5*(17-11)) = (85*7)+(5*6) = 595+30=625
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV: makeMoney(10),
				FinalNAV:   makeMoney(17),
				FinalUnits: 90,
				Transactions: []*mfExternalPb.MutualFundExternalOrder{
					makeTransaction(date(2024, 6, 2), -5, 11),
					makeTransaction(date(2024, 6, 3), 5, 12),
					makeTransaction(date(2024, 6, 4), -10, 13),
					makeTransaction(date(2024, 6, 5), 10, 14),
					makeTransaction(date(2024, 6, 6), -5, 15),
					makeTransaction(date(2024, 6, 7), -5, 16),
				},
			},
			want: &InvestmentReturnForDateRangeResponse{
				ReturnAmount: 580,
				InitialPrice: 950,
			},
			wantErr: false,
		},
		{
			name: "Case 5.2: Sell Exceeds Initial Holdings (FIFO)",
			// Calculation:
			// Initial holdings: 100 units at NAV 10
			// Buys: 10 units at NAV 11 (2024-06-02), 5 units at NAV 12 (2024-06-03)
			// Sells: 80 units (2024-06-04, NAV 13), 20 units (2024-06-05, NAV 14), 10 units (2024-06-06, NAV 15)
			// Final holdings: 5 units
			// FIFO: Sell 100 from initial (80+20), then 5 from buy1, then 5 from buy2 (for the last 10 sell)
			// Remaining: 5 from buy2
			// InitialPrice = 5*12 = 60
			// ReturnAmount = 5*(17-12) = 5*5 = 25
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV: makeMoney(10),
				FinalNAV:   makeMoney(17),
				FinalUnits: 10,
				Transactions: []*mfExternalPb.MutualFundExternalOrder{
					makeTransaction(date(2024, 6, 2), 10, 11),
					makeTransaction(date(2024, 6, 4), -75, 13),
					makeTransaction(date(2024, 6, 4), 5, 12),
					makeTransaction(date(2024, 6, 5), -20, 14),
					makeTransaction(date(2024, 6, 6), -10, 15),
				},
			},
			want: &InvestmentReturnForDateRangeResponse{
				ReturnAmount: 55,
				InitialPrice: 115,
			},
			wantErr: false,
		},
		{
			name: "Error: InitialNAV is nil",
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV:   nil,
				FinalNAV:     makeMoney(12),
				FinalUnits:   100,
				Transactions: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error: FinalNAV is nil",
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV:   makeMoney(10),
				FinalNAV:     nil,
				FinalUnits:   100,
				Transactions: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error: FinalUnits is zero",
			input: &InvestmentReturnForDateRangeRequest{
				InitialNAV:   makeMoney(10),
				FinalNAV:     makeMoney(12),
				FinalUnits:   0,
				Transactions: nil,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := calculateInvestmentReturnForDateRange(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("error = %v, wantErr %v", err, tt.wantErr)
			}
			if (got == nil) != (tt.want == nil) {
				t.Errorf("got = %v, want = %v", got, tt.want)
				return
			}
			if got != nil && tt.want != nil {
				if !floatEquals(got.ReturnAmount, tt.want.ReturnAmount) {
					t.Errorf("ReturnAmount = %v, want %v", got.ReturnAmount, tt.want.ReturnAmount)
				}
				if !floatEquals(got.InitialPrice, tt.want.InitialPrice) {
					t.Errorf("InitialPrice = %v, want %v", got.InitialPrice, tt.want.InitialPrice)
				}
			}
		})
	}
}
