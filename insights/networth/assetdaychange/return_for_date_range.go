package assetdaychange

import (
	"fmt"
	"math"

	"google.golang.org/genproto/googleapis/type/money"

	moneyPkg "github.com/epifi/be-common/pkg/money"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"

	"github.com/epifi/be-common/pkg/epifierrors"
)

// Lot represents a holding lot for FIFO (First-In-First-Out) calculations.
type Lot struct {
	Units float64
	NAV   float64
}

// InvestmentReturnForDateRangeRequest holds all data needed for investment return calculation over a date range.
type InvestmentReturnForDateRangeRequest struct {
	InitialNAV   *money.Money
	FinalNAV     *money.Money
	FinalUnits   float64
	Transactions []*mfExternalPb.MutualFundExternalOrder // all transactions in the period, sorted by date
}

// InvestmentReturnForDateRangeResponse holds the result of the investment return calculation for a date range.
type InvestmentReturnForDateRangeResponse struct {
	ReturnAmount float64 // Total return amount over the date range
	InitialPrice float64 // Initial investment value at the start of the date range
}

// calculateInvestmentReturnForDateRange computes the investment return for an actor in a scheme over a specified date range.
// It supports cases with no transactions, only sells, only buys, or a mix, and applies FIFO logic for sells.
func calculateInvestmentReturnForDateRange(req *InvestmentReturnForDateRangeRequest) (*InvestmentReturnForDateRangeResponse, error) {
	// Validation check
	if req.FinalNAV == nil || req.InitialNAV == nil {
		return nil, fmt.Errorf("error calculating return for date range: %w", epifierrors.ErrInvalidArgument)
	}

	// Case-3: All Holdings Sold (final units = 0) -- gracefully handle this case
	if req.FinalUnits == 0 {
		return nil, fmt.Errorf("all holdings sold, err: %w", epifierrors.ErrRecordNotFound)
	}

	finalNavs := moneyPkg.ToFloat(req.FinalNAV)
	initialNavs := moneyPkg.ToFloat(req.InitialNAV)

	initialUnits, netSoldUnits, buyTxns := computeInitialUnitsBuysAndSells(req.Transactions, req.FinalUnits)

	// Case-1: No Transactions in the date range
	// Case-2: Only Sell Transactions (but some units still held)
	// Case-4: Only buys (fresh buy with or without prior holdings, no sells)
	// Case-5: If there are both buys and sells, or only sells but not all sold, use FIFO logic
	// code below will handle all the mentioned cases
	var lots []Lot
	if initialUnits > 0 {
		lots = append(lots, Lot{initialUnits, initialNavs})
	}
	for _, buyTxn := range buyTxns {
		lots = append(lots, Lot{buyTxn.GetTransactionUnits(), moneyPkg.ToFloat(buyTxn.GetPurchasePrice())})
	}
	// FIFO: units sold are taken from the oldest units bought
	lots = applyFIFO(lots, netSoldUnits)

	// Calculate the return and initial price for the remaining lots
	returnAmount := 0.0
	initialPrice := 0.0
	for _, lot := range lots {
		if lot.Units > 0 {
			// For each remaining unit, add its return and its original price
			returnAmount += lot.Units * (finalNavs - lot.NAV)
			initialPrice += lot.Units * lot.NAV
		}
	}
	// Compose the response with the total return and initial price
	return &InvestmentReturnForDateRangeResponse{
		ReturnAmount: returnAmount,
		InitialPrice: initialPrice,
	}, nil
}

// applyFIFO applies FIFO logic to sell units from lots and returns the updated lots.
// Used for calculating remaining lots after sales in the date range.
func applyFIFO(lots []Lot, unitsToSell float64) []Lot {
	for i := range lots {
		if unitsToSell <= 0 {
			break
		}
		sell := math.Min(lots[i].Units, unitsToSell)
		lots[i].Units -= sell
		unitsToSell -= sell
	}
	return lots
}

// computeInitialUnitsBuysAndSells computes the initial units at the start of the period, total units sold, and collects all buy transactions.
// It works by reversing out buys and sells from the final units to infer the initial state.
func computeInitialUnitsBuysAndSells(transactions []*mfExternalPb.MutualFundExternalOrder, finalUnits float64) (float64, float64, []*mfExternalPb.MutualFundExternalOrder) {
	initialUnits := finalUnits
	var buyTxns []*mfExternalPb.MutualFundExternalOrder
	var sellUnits float64
	for _, txn := range transactions {
		if txn.GetTransactionUnits() < 0 {
			// This is a sell transaction
			sellUnits -= txn.GetTransactionUnits() // subtracting as units are already negative
			initialUnits -= txn.GetTransactionUnits()
		}
		if txn.GetTransactionUnits() > 0 {
			// This is a buy transaction
			buyTxns = append(buyTxns, txn)
			initialUnits -= txn.GetTransactionUnits()
		}
	}

	return initialUnits, sellUnits, buyTxns
}
