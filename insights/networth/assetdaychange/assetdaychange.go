package assetdaychange

//go:generate mockgen -source=assetdaychange.go -destination=./mocks/mock_assetdaychange.go -package=mocks

import (
	"context"

	"github.com/google/wire"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
)

// WireSet combines all wire sets for value calculators and the factory
var WireSet = wire.NewSet(
	MutualFundCalculatorWireSet,
	IndianStocksCalculatorWireSet,
	NPSCalculatorWireSet,
	FactoryWireSet,
	wire.Bind(new(DayChangeCalculator), new(*MutualFundCalculator)),
)

type CalculateAssetDayChangeValueRequest struct {
	// Mandatory fields required to calculate asset day change value for any asset type
	ActorId     string
	InitialDate *timestampPb.Timestamp
	FinalDate   *timestampPb.Timestamp

	// Optional fields: these fields will only be used if the asset data is coming from asset histories table
	// Asset Examples where this might not be used: Mutual Funds
	OwnershipToInitialDateDataMap map[commontypes.Ownership]*modelPb.AssetData
	OwnershipToFinalDateDataMap   map[commontypes.Ownership]*modelPb.AssetData
}

type DayChangeCalculator interface {
	// CalculateAssetDayChangeValue calculates asset values for given dates
	// For example in dynamic assets like stocks, nps etc., units history will be given in the request and price history will be fetched from corresponding catalogs
	// Once both are available based on the asset level logic value at given dates will be returned, units*price in the case of dynamic assets
	CalculateAssetDayChangeValue(context.Context, *CalculateAssetDayChangeValueRequest) (*networthPb.AssetTypeDayChangeResponse, error)
}
