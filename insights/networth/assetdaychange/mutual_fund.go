package assetdaychange

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/google/wire"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	mfAnalyserVariablePb "github.com/epifi/gamma/api/analyser/variables/mutualfund"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
)

var MutualFundCalculatorWireSet = wire.NewSet(
	NewMutualFundCalculator,
)

// MutualFundCalculator implements DayChangeCalculator for mutual funds
type MutualFundCalculator struct {
	catalogClient           catalogPb.CatalogManagerClient
	mfExternalOrdersClient  mfExternalPb.MFExternalOrdersClient
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
}

// NewMutualFundCalculator creates a new instance of MutualFundCalculator
func NewMutualFundCalculator(
	catalogClient catalogPb.CatalogManagerClient,
	mfExternalOrdersClient mfExternalPb.MFExternalOrdersClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
) *MutualFundCalculator {
	return &MutualFundCalculator{
		catalogClient:           catalogClient,
		mfExternalOrdersClient:  mfExternalOrdersClient,
		variableGeneratorClient: variableGeneratorClient,
	}
}

func (m *MutualFundCalculator) CalculateAssetDayChangeValue(ctx context.Context, req *CalculateAssetDayChangeValueRequest) (*networthPb.AssetTypeDayChangeResponse, error) {
	actorId := req.ActorId
	initialDate := req.InitialDate
	finalDate := req.FinalDate

	// Step 1: Fetch the latest mutual fund holding details for the actor as of the final date
	mfDetails, err := m.getMfSchemeAnalyticsDetails(ctx, actorId)
	if err != nil {
		return nil, err
	}

	// Step 2: Fetch all mutual fund transactions for the actor within the specified date range
	mfTxnResp, mfTxnErr := m.mfExternalOrdersClient.GetMFTransactionsByDateRange(ctx, &mfExternalPb.GetMFTransactionsByDateRangeRequest{
		ActorId:  actorId,
		FromDate: datetime.TimeToDateInLoc(initialDate.AsTime(), datetime.IST),
		ToDate:   datetime.TimeToDateInLoc(finalDate.AsTime(), datetime.IST),
	})
	if rpcErr := epifigrpc.RPCError(mfTxnResp, mfTxnErr); rpcErr != nil && !mfTxnResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching mutual fund transactions", zap.Error(rpcErr))
		return nil, fmt.Errorf("error fetching mutual fund transactions: %w", rpcErr)
	}

	// Step 3: Collect all mutual fund IDs present in the actor's holdings
	var allMfIds []string
	for _, mf := range mfDetails {
		allMfIds = append(allMfIds, mf.GetEnrichedAnalytics().GetAnalytics().GetMutualFundId())
	}

	// Step 4: Fetch the NAVs for all mutual funds as of the initial date (T-7)
	initialNavValues, navErr := m.getInitialDateNavs(ctx, allMfIds, initialDate)
	if navErr != nil {
		return nil, navErr
	}

	// Step 5: Compute the asset day change response using the gathered holdings, transactions, and NAVs
	return m.computeMFReturns(mfTxnResp.GetMutualFundExternalOrders(), mfDetails, initialNavValues)
}

func (m *MutualFundCalculator) getMfSchemeAnalyticsDetails(ctx context.Context, actorId string) ([]*mfAnalyserVariablePb.SchemeAnalytics, error) {
	getAnalysisVariableResp, getAnalysisVariableErr := m.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
	})
	if rpcErr := epifigrpc.RPCError(getAnalysisVariableResp, getAnalysisVariableErr); rpcErr != nil {
		return nil, fmt.Errorf("error fetching analysis variable generator: %w", rpcErr)
	}
	variable, ok := getAnalysisVariableResp.GetVariableEnumMap()[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		return nil, fmt.Errorf("error when fetch analyser variable mf scheme analytics: %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}
	mfDetails := variable.GetMfSecretsSchemeAnalytics().GetSchemeAnalytics()
	return mfDetails, nil
}

func (m *MutualFundCalculator) getInitialDateNavs(ctx context.Context, mfIds []string, initialDate *timestamppb.Timestamp) (map[string]*money.Money, error) {
	navReqs := make([]*catalogPb.NavRequestByMutualFundId, 0, len(mfIds))

	// converting initialDate to StartOfDay (00:00:00) in IST timezone as data is stored in that format
	initialDateIST := timestamppb.New(datetime.StartOfDay(initialDate.AsTime().In(datetime.IST)))
	for _, mfId := range mfIds {
		navReqs = append(navReqs, &catalogPb.NavRequestByMutualFundId{
			MutualFundId: mfId,
			Dates:        []*timestamppb.Timestamp{initialDateIST},
		})
	}
	resp, err := m.catalogClient.GetNavHistoryByMutualFundId(ctx, &catalogPb.GetNavHistoryByMutualFundIdRequest{
		NavRequests: navReqs,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return nil, fmt.Errorf("error getting NAVs for dates, err: %w", rpcErr)
	}

	navsForMfIds := make(map[string]*money.Money)

	for _, nav := range resp.GetNavHistories() {
		for _, navDetail := range nav.GetNavDetails() {
			navsForMfIds[nav.GetMutualFundId()] = navDetail.GetNav()
		}
	}

	return navsForMfIds, nil
}

// computeMFReturns computes the AssetTypeDayChangeResponse for mutual funds
func (m *MutualFundCalculator) computeMFReturns(mfTxns []*mfExternalPb.MutualFundExternalOrder, mfDetails []*mfAnalyserVariablePb.SchemeAnalytics,
	initialNavMap map[string]*money.Money) (*networthPb.AssetTypeDayChangeResponse, error) {
	var (
		totalInitialValue = 0.0
		totalFinalValue   = 0.0
		assetsValueChange []*networthPb.AssetValueChange
	)

	// Step 1: Group all transactions by their mutual fund ID for efficient lookup
	mfTxnsById := make(map[string][]*mfExternalPb.MutualFundExternalOrder)
	for _, txn := range mfTxns {
		mfId := txn.GetMutualFundId()
		mfTxnsById[mfId] = append(mfTxnsById[mfId], txn)
	}

	// Step 2: For each scheme in the actor's holdings, compute the value change
	for _, scheme := range mfDetails {
		mfId := scheme.GetEnrichedAnalytics().GetAnalytics().GetMutualFundId()

		_, isTxnAvailable := mfTxnsById[mfId]
		if !isTxnAvailable {
			mfTxnsById[mfId] = nil // nil represents no transactions for this mutual fund
			logger.InfoNoCtx("no transactions found for mutual fund", zap.String("mfId:", mfId))
		}
		// Compute the value change for this scheme (returns nil if asset is excluded)
		assetValue, err := m.computeSingleScheme(scheme, mfTxnsById[mfId], initialNavMap[mfId])
		if err != nil {
			// If any error occurs, abort and return the error
			return nil, err
		}
		if assetValue == nil {
			// Skip excluded assets (e.g., all holdings sold)
			continue
		}
		// Aggregate the per-asset value changes
		assetsValueChange = append(assetsValueChange, assetValue)
		totalInitialValue += moneyPkg.ToFloat(assetValue.GetInitialDateValue())
		totalFinalValue += moneyPkg.ToFloat(assetValue.GetFinalDateValue())
	}

	// Step 3: Build and return the aggregated response for all schemes
	return &networthPb.AssetTypeDayChangeResponse{
		InitialDateTotalValue: moneyPkg.ParseFloat(totalInitialValue, moneyPkg.RupeeCurrencyCode),
		FinalDateTotalValue:   moneyPkg.ParseFloat(totalFinalValue, moneyPkg.RupeeCurrencyCode),
		TotalChange:           totalFinalValue - totalInitialValue,
		AssetsValueChange:     assetsValueChange,
	}, nil
}

func (m *MutualFundCalculator) computeSingleScheme(scheme *mfAnalyserVariablePb.SchemeAnalytics, mfTxnsById []*mfExternalPb.MutualFundExternalOrder,
	initialNav *money.Money) (*networthPb.AssetValueChange, error) {
	// Step 1: Extract relevant scheme and date details
	mfId := scheme.GetEnrichedAnalytics().GetAnalytics().GetMutualFundId()
	finalNavValue := scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails().GetNavValue()
	finalUnits := scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails().GetUnits()

	// Step 2: Call the core date range return calculation logic
	report, calcReportErr := calculateInvestmentReturnForDateRange(&InvestmentReturnForDateRangeRequest{
		InitialNAV:   initialNav,
		FinalNAV:     finalNavValue,
		FinalUnits:   finalUnits,
		Transactions: mfTxnsById,
	})

	// If the calculation returns an error that is not ErrRecordNotFound, propagate the error
	if calcReportErr != nil && !errors.Is(calcReportErr, epifierrors.ErrRecordNotFound) {
		return nil, calcReportErr
	}

	// Step 3: If the calculation failed due to all holdings sold (ErrRecordNotFound), skip this asset by returning nil (caller will continue to next asset)
	if errors.Is(calcReportErr, epifierrors.ErrRecordNotFound) {
		return nil, nil
	}

	// Step 4: Build and return the per-asset value change result
	initialValue := report.InitialPrice
	finalValue := report.InitialPrice + report.ReturnAmount

	return &networthPb.AssetValueChange{
		AssetId:          mfId,
		InitialDateValue: moneyPkg.ParseFloat(initialValue, moneyPkg.RupeeCurrencyCode),
		FinalDateValue:   moneyPkg.ParseFloat(finalValue, moneyPkg.RupeeCurrencyCode),
		Change:           report.ReturnAmount,
	}, nil
}
