package assetdaychange

import (
	"context"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/insights/config/genconf"
)

var IndianStocksCalculatorWireSet = wire.NewSet(
	NewIndianStocksCalculator,
)

// IndianStocksCalculator implements DayChangeCalculator for Indian securities/stocks
type IndianStocksCalculator struct {
	gconf                   *genconf.Config
	securitiesCatalogClient catalogPb.SecuritiesCatalogClient
}

// NewIndianStocksCalculator creates a new instance of IndianStocksCalculator
func NewIndianStocksCalculator(
	gconf *genconf.Config,
	securitiesCatalogClient catalogPb.SecuritiesCatalogClient,
) *IndianStocksCalculator {
	return &IndianStocksCalculator{
		gconf:                   gconf,
		securitiesCatalogClient: securitiesCatalogClient,
	}
}

func (s *IndianStocksCalculator) getUnitPriceMapForDate(ctx context.Context, actorId string, securityListingIds []string, dateInTs *timestampPb.Timestamp, unitsMap map[string]float64) (map[string]*AssetUnitPricePair, error) {
	// Doing -1 day since in securities terminology today price is yesterday EOD price i.e. price at D-x = EOD price at D-x-1
	priceDate := datetime.TimestampToDateInLoc(timestampPb.New(dateInTs.AsTime().Add(-time.Hour*24)), datetime.IST)
	priceResp, getPriceErr := s.securitiesCatalogClient.GetPriceByDateAndSecListingIDs(ctx, &catalogPb.GetPriceByDateAndSecListingIDsRequest{
		SecurityListingIds: securityListingIds,
		PriceDate:          priceDate,
	})
	if rpcErr := epifigrpc.RPCError(priceResp, getPriceErr); rpcErr != nil {
		return nil, errors.Wrapf(rpcErr, "error getting prices for date %v", dateInTs)
	}

	// Debug logging for debug actors
	if isPresent := s.gconf.NetworthParams().DebugActorIdsForDailyReport().Get(actorId); isPresent {
		logger.Info(ctx, "debugging logs for indian stocks unit price map",
			zap.String("actorId", actorId),
			zap.Int("securityListingIdsCount", len(securityListingIds)),
			zap.Any("securityListingIds", securityListingIds),
			zap.String("priceDate", priceDate.String()),
			zap.Int("priceResponseCount", len(priceResp.GetPrices())),
			zap.Any("priceResponse", priceResp.GetPrices()),
		)
	}

	unitPriceMap := make(map[string]*AssetUnitPricePair)
	for securityListingId, price := range priceResp.GetPrices() {
		units, ok := unitsMap[securityListingId]
		if !ok {
			// if units data is not available for this id then it implies that user doesn't hold those stocks at that time
			continue
		}
		unitPriceMap[securityListingId] = &AssetUnitPricePair{
			Units: units,
			Price: price,
		}
	}

	// Debug logging for final unit price map for debug actors
	if isPresent := s.gconf.NetworthParams().DebugActorIdsForDailyReport().Get(actorId); isPresent {
		logger.Info(ctx, "debugging logs for indian stocks final unit price map",
			zap.String("actorId", actorId),
			zap.Int("unitPriceMapCount", len(unitPriceMap)),
			zap.Any("unitPriceMap", unitPriceMap),
		)
	}

	return unitPriceMap, nil
}

func (s *IndianStocksCalculator) CalculateAssetDayChangeValue(ctx context.Context, req *CalculateAssetDayChangeValueRequest) (*networthPb.AssetTypeDayChangeResponse, error) {
	initialDateUnitsMap := make(map[string]float64)
	finalDateUnitsMap := make(map[string]float64)
	allSecurityListingIdsMap := make(map[string]bool)

	// Collect initial date units and all security IDs
	for _, assetData := range req.OwnershipToInitialDateDataMap {
		for _, stock := range assetData.GetIndianStocksData().GetIndianStocks() {
			securityListingId := stock.GetSecurityListingId()
			initialDateUnitsMap[securityListingId] = stock.GetUnits()
			allSecurityListingIdsMap[securityListingId] = true
		}
	}

	// Collect final date units and all security IDs
	for _, assetData := range req.OwnershipToFinalDateDataMap {
		for _, stock := range assetData.GetIndianStocksData().GetIndianStocks() {
			securityListingId := stock.GetSecurityListingId()
			finalDateUnitsMap[securityListingId] = stock.GetUnits()
			allSecurityListingIdsMap[securityListingId] = true
		}
	}

	allSecurityListingIds := make([]string, 0)
	for securityListingId := range allSecurityListingIdsMap {
		allSecurityListingIds = append(allSecurityListingIds, securityListingId)
	}

	g := errgroup.New()
	var initialDateUnitPriceMap map[string]*AssetUnitPricePair
	g.Go(func() error {
		var err error
		initialDateUnitPriceMap, err = s.getUnitPriceMapForDate(ctx, req.ActorId, allSecurityListingIds, req.InitialDate, initialDateUnitsMap)
		if err != nil {
			return errors.Wrap(err, "error processing initial date prices for indian stocks")
		}
		return nil
	})

	var finalDateUnitPriceMap map[string]*AssetUnitPricePair
	g.Go(func() error {
		var err error
		finalDateUnitPriceMap, err = s.getUnitPriceMapForDate(ctx, req.ActorId, allSecurityListingIds, req.FinalDate, finalDateUnitsMap)
		if err != nil {
			return errors.Wrap(err, "error processing final date prices for indian stocks")
		}
		return nil
	})
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Use common function to craft response
	assetsDayChangeResp, getAssetsDayChangeErr := getAssetsDayChangeResponse(initialDateUnitPriceMap, finalDateUnitPriceMap)
	if getAssetsDayChangeErr != nil {
		return nil, errors.Wrap(getAssetsDayChangeErr, "error in getting assets day change response for indian stocks")
	}
	return assetsDayChangeResp, nil
}
