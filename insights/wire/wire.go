//go:build wireinject
// +build wireinject

package wire

import (
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kms"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/insights/networth/assetdaychange"
	epfHelper "github.com/epifi/gamma/insights/networth/assets/helper/epf"
	"github.com/epifi/gamma/insights/networth/assets/helper/mutualfund"
	"github.com/epifi/gamma/insights/networth/history"
	"github.com/epifi/gamma/insights/networth/llm"
	llmConf "github.com/epifi/gamma/insights/networth/llm/config"

	"github.com/google/wire"
	"gorm.io/gorm"

	kms2 "github.com/epifi/be-common/pkg/aws/v2/kms"
	pkgKms "github.com/epifi/be-common/pkg/aws/v2/kms"
	"github.com/epifi/be-common/pkg/cfg"
	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	smc "github.com/epifi/be-common/pkg/secrets/aws"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"

	"github.com/epifi/be-common/pkg/cache"

	creditScoreParamFetcher "github.com/epifi/gamma/analyser/creditscore/params_fetcher"
	analyserTypes "github.com/epifi/gamma/analyser/wire/types"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	investAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/auth"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/connected_account"
	credit_report "github.com/epifi/gamma/api/creditreportv2"
	empDao "github.com/epifi/gamma/api/employment"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	insights2 "github.com/epifi/gamma/api/insights"
	accessinfoPb "github.com/epifi/gamma/api/insights/accessinfo"
	emailparserPb "github.com/epifi/gamma/api/insights/emailparser"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	investmentAggregatorPb "github.com/epifi/gamma/api/investment/aggregator"
	mfExternal "github.com/epifi/gamma/api/investment/mutualfund/external"
	merchantPb "github.com/epifi/gamma/api/merchant"
	npsPb "github.com/epifi/gamma/api/nps/catalog"
	payPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/rewards"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/employment"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/insights"
	"github.com/epifi/gamma/insights/accessinfo"
	accessinfoConsumer "github.com/epifi/gamma/insights/accessinfo/consumer"
	accessinfoDao "github.com/epifi/gamma/insights/accessinfo/dao"
	"github.com/epifi/gamma/insights/config"
	"github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/gamma/insights/consumer"
	insightsDao "github.com/epifi/gamma/insights/dao"
	"github.com/epifi/gamma/insights/developer"
	"github.com/epifi/gamma/insights/developer/processor"
	"github.com/epifi/gamma/insights/emailparser"
	emailParserConsumer "github.com/epifi/gamma/insights/emailparser/consumer"
	"github.com/epifi/gamma/insights/emailparser/dao"
	"github.com/epifi/gamma/insights/epf"
	epfConsumer "github.com/epifi/gamma/insights/epf/consumer"
	epfDao "github.com/epifi/gamma/insights/epf/dao"
	"github.com/epifi/gamma/insights/helper"
	"github.com/epifi/gamma/insights/helper/evaluator"
	realtimeHelper "github.com/epifi/gamma/insights/helper/realtime"
	kubair "github.com/epifi/gamma/insights/kubair"
	kubairComponentBuilder "github.com/epifi/gamma/insights/kubair/component_builder"
	kubairProcessor "github.com/epifi/gamma/insights/kubair/processor"
	"github.com/epifi/gamma/insights/networth"
	"github.com/epifi/gamma/insights/networth/assets"
	aaHelper "github.com/epifi/gamma/insights/networth/assets/helper/aa"
	investmentHelper "github.com/epifi/gamma/insights/networth/assets/helper/investment"
	"github.com/epifi/gamma/insights/networth/dao/impl"
	investmentDeclaration "github.com/epifi/gamma/insights/networth/investment_declaration"
	investmentCalculator "github.com/epifi/gamma/insights/networth/investment_declaration/calculator"
	"github.com/epifi/gamma/insights/networth/liabilities"
	creditReportHelper "github.com/epifi/gamma/insights/networth/liabilities/helper/creditreport"
	"github.com/epifi/gamma/insights/realtime"
	"github.com/epifi/gamma/insights/realtime/datagenerator"
	"github.com/epifi/gamma/insights/realtime/framework"
	"github.com/epifi/gamma/insights/realtime/framework/validator"
	"github.com/epifi/gamma/insights/realtime/generator"
	"github.com/epifi/gamma/insights/story"
	storyDao "github.com/epifi/gamma/insights/story/dao"
	"github.com/epifi/gamma/insights/story/storiesgenerator"
	storyValGenFactory "github.com/epifi/gamma/insights/story/story_value_generator/factory"
	userDeclaration "github.com/epifi/gamma/insights/user_declaration"
	userDeclarationDao "github.com/epifi/gamma/insights/user_declaration/dao"
	"github.com/epifi/gamma/insights/utils"
	"github.com/epifi/gamma/insights/wire/types"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	"github.com/epifi/gamma/pkg/dmf/txnaggregates"
	zincSearch "github.com/epifi/gamma/pkg/zinc/search"
	mfCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
)

func NewKmsProvider(awsConfig aws.Config) *kms.Client {
	return kms2.InitKMSClient(awsConfig)
}

func InitializeService(gconf *genconf.Config, actorInsightDB cmdTypes.ActorInsightsPGDB, actorClient actorPb.ActorClient, userGrpClient usergroupPb.GroupClient,
	userClient user.UsersClient, txnAggClient txnAggregatesPb.TxnAggregatesClient, client connected_account.ConnectedAccountClient,
	savingsClient savings.SavingsClient, merchantClient merchantPb.MerchantServiceClient, piClient paymentinstrument.PiClient,
	categorizerClient categorizer.TxnCategorizerClient, fireflyClient firefly.FireflyClient) *insights.Service {
	wire.Build(
		insights.NewService,
		insightsDao.WireInsightFrameworkDaoSet,
		insightsDao.WireInsightSegmentDaoSet,
		insightsDao.WireActorInsightDaoSet,
		insightsDao.WireContentTemplateDaoSet,
		insightsDao.WireInsightEngagementDaoSet,
		insightsDao.WireGenerationScriptRunDaoSet,
		insightsDao.WireInsightFeedbackDaoSet,
		insightsDao.WireActorInsightGenerationLogDaoSet,
		realtime.InsightProcessorWireSet,
		realtime.GeneratorSelectorWireSet,
		realtime.GeneratorToFrameworkNamesMappingWireSet,
		utils.GenerateRandomNumberWireSet,
		datafetcher.ActorAccountsWireSet,
		helper.CategoryHelperWireSet,
		framework.ValueGeneratorFactoryWireSet,
		generator.InsightGeneratorFactoryWireSet,
		txnaggregates.TxnAggregatesWireSet,
		utils.CurrentTimeGeneratorWireSet,
		helper.RelevanceScoreWireSet,
		helper.GlobalVarValuesWireSet,
		evaluator.ExpressionEvaluatorWireSet,
		validator.FrameworkValidatorWireSet,
		storagev2.DefaultTxnExecutorWireSet,
		actorInsightsGormDbProvider,
		realtimeHelper.WireFrameworkValueSelectorSet,
		datagenerator.WireCategoryAggregateDataGenSet,
		datafetcher.IMerchantsWireSet,
	)
	return &insights.Service{}
}

func InitializeInsightsDevEntityService(db cmdTypes.InsightsPGDB, actorInsightDB cmdTypes.ActorInsightsPGDB) *developer.InsightsDevService {
	wire.Build(
		developer.NewInsightsDevService,
		developer.NewDevFactory,
		processor.NewMailSyncLog,
		dao.WireMailSyncLogDaoSet,
		processor.NewMerchantQuery,
		dao.WireMerchantQueryDaoSet,
		processor.NewMessageProcessingState,
		dao.WireMessageDaoSet,
		processor.NewMerchant,
		dao.WireMerchantDaoSet,
		processor.NewInsightFramework,
		insightsDao.WireInsightFrameworkDaoSet,
		processor.NewInsightSegment,
		insightsDao.WireInsightSegmentDaoSet,
		processor.NewInsightContextMapping,
		insightsDao.WireInsightScreenMappingDaoSet,
		processor.NewGenerationScriptRun,
		insightsDao.WireGenerationScriptRunDaoSet,
		processor.NewContentTemplate,
		insightsDao.WireContentTemplateDaoSet,
		processor.NewInsightEngagement,
		insightsDao.WireInsightEngagementDaoSet,
		processor.NewEpfPassbookRequest,
		epfDao.EPFPassbookRequestDaoWireSet,
	)
	return &developer.InsightsDevService{}
}

func InitializeActorInsightConsumerService(
	actorInsightDB cmdTypes.ActorInsightsPGDB,
	insightsClient insights2.InsightsClient,
	commsClient types.InsightsCommsClientWithInterceptors,
	caClient connected_account.ConnectedAccountClient,
	dbConnProvider *usecase.DBResourceProvider[*gorm.DB],
	catalogClient catalogPb.SecuritiesCatalogClient,
) *consumer.Service {
	wire.Build(
		types.CommsClientProvider,
		consumer.NewService,
		insightsDao.WireInsightFrameworkDaoSet,
		insightsDao.WireInsightSegmentDaoSet,
		aaHelper.WireAaBalanceFetcherSet,
		insightsDao.WireActorInsightDaoSet,
		insightsDao.WireGenerationScriptRunDaoSet,
		impl.AssetHistoryWireSet,
		actorInsightsGormDbProvider,
		storagev2.DefaultTxnExecutorWireSet,
		insightsDao.WireInsightEngagementDaoSet,
		utils.CurrentTimeGeneratorWireSet,
	)
	return &consumer.Service{}
}

func InitializeEmailparserService(conf *config.Config, db cmdTypes.InsightsPGDB, authClient authPb.AuthClient, accessInfoClient accessinfoPb.AccessInfoClient,
	spendsPublisher types.GmailUserSpendsPublisher) *emailparser.Service {
	wire.Build(
		emailparser.NewService,
		dao.WireUserSpendingDaoSet,
		dao.WireMailSyncLogDaoSet,
		dao.WireMessageDaoSet,
		dao.WireGmailQueryExecResultsDaoSet,
		emailParserConsumer.WireMailServiceSet,
		dao.WireMerchantDaoSet,
		dao.WireMerchantQueryDaoSet,
		getHttpClientGmail,
		gmailBatchGetParamsProvider,
		insights.GetOAuthCredentialConfig,
		oauthCredProvider,
		getGmailListApiClient,
	)
	return &emailparser.Service{}
}

func InitializeEmailparserConsumerService(conf *config.Config, db cmdTypes.InsightsPGDB, accessInfoClient accessinfoPb.AccessInfoClient,
	publisher types.MailDataParserPublisher, spendsPublisher types.GmailUserSpendsPublisher, actorClient actor.ActorClient,
	userClient user.UsersClient, awsConf aws.Config) *emailParserConsumer.Service {
	wire.Build(
		NewKmsProvider,
		emailParserConsumer.NewService,
		dao.WireMessageDaoSet,
		dao.WireUserSpendingDaoSet,
		emailParserConsumer.WireMailServiceSet,
		emailParserConsumer.WireMailDataFetcherSet,
		emailParserConsumer.WireMailDataParserSet,
		dao.WireMailSyncLogDaoSet,
		dao.WireMerchantQueryDaoSet,
		dao.WireMerchantDaoSet,
		storagev2.IdempotentTxnExecutorWireSet,
		insightsGormDbProvider,
		gmailBatchGetParamsProvider,
		emailparserParamsProvider,
		getMailDataCryptor,
		mailFetchConcurrencyParamsProvider,
		emailIdRegexProvider,
		mailFetchDateRangeProvider,
		getGmailListApiClient,
		getHttpClientGmail,
		getHttpClientForEmailParser,
		insights.GetOAuthCredentialConfig,
		oauthCredProvider,
		storagev2.DefaultTxnExecutorWireSet,
	)
	return &emailParserConsumer.Service{}
}

func InitializeMockEmailparserConsumerService(conf *config.Config, db cmdTypes.InsightsPGDB, accessInfoClient accessinfoPb.AccessInfoClient,
	publisher types.MailDataParserPublisher, spendsPublisher types.GmailUserSpendsPublisher, actorClient actor.ActorClient,
	userClient user.UsersClient, awsConf aws.Config) *emailParserConsumer.Service {
	wire.Build(
		NewKmsProvider,
		emailParserConsumer.NewService,
		dao.WireMessageDaoSet,
		dao.WireUserSpendingDaoSet,
		emailParserConsumer.WireMockMailServiceSet,
		emailParserConsumer.WireMockMailDataFetcherSet,
		emailParserConsumer.WireMockMailDataParserSet,
		dao.WireMailSyncLogDaoSet,
		dao.WireMerchantQueryDaoSet,
		dao.WireMerchantDaoSet,
		storagev2.IdempotentTxnExecutorWireSet,
		insightsGormDbProvider,
		getMailDataCryptor,
		emailIdRegexProvider,
		mailFetchDateRangeProvider,
	)
	return &emailParserConsumer.Service{}
}

func InitializeAccessInfoService(conf *config.Config, db cmdTypes.InsightsPGDB, userMailAccessPublisher types.UserEmailAccessPublisher,
	awsConf aws.Config, authCleint auth.AuthClient,
	periodicPublisher types.PeriodicEmaiSyncPublisher, onbClient onboardingPb.OnboardingClient, epClient emailparserPb.EmailParserClient) *accessinfo.Service {
	wire.Build(
		NewKmsProvider,
		smc.NewAwsSecretsManagerClient,
		accessinfo.NewService,
		accessinfoDao.WireAccessInfoDaoSet,
		accessinfo.WireOAuthConfigSet,
		accessinfo.WireMailDataPublisherSet,
		pkgKms.NewKMSSymmetricCryptor,
		accessinfoDao.WireWlOnbActorMapDaoSet,
		oauthRevokeTokenUrlProvider,
		addEmailAccBannerConfigProvider,
		getOAuthClient,
		getAccessInfoCryptor,
		accessInfoKmsKeyIdProvider,
		insights.GetOAuthCredentialConfig,
		oauthCredProvider,
	)
	return &accessinfo.Service{}
}

func InitializeMockAccessInfoService(conf *config.Config, db cmdTypes.InsightsPGDB, userMailAccessPublisher types.UserEmailAccessPublisher,
	awsConf aws.Config, authCleint auth.AuthClient,
	periodicPublisher types.PeriodicEmaiSyncPublisher, onbClient onboardingPb.OnboardingClient, epClient emailparserPb.EmailParserClient) *accessinfo.Service {
	wire.Build(
		NewKmsProvider,
		smc.NewAwsSecretsManagerClient,
		accessinfo.NewService,
		accessinfoDao.WireAccessInfoDaoSet,
		accessinfo.WireMockOAuthConfigSet,
		accessinfo.WireMailDataPublisherSet,
		pkgKms.NewKMSSymmetricCryptor,
		accessinfoDao.WireWlOnbActorMapDaoSet,
		addEmailAccBannerConfigProvider,
		getAccessInfoCryptor,
		accessInfoKmsKeyIdProvider,
	)
	return &accessinfo.Service{}
}

func InitializeAccessInfoConsumerService(db cmdTypes.InsightsPGDB) *accessinfoConsumer.Service {
	wire.Build(accessinfoConsumer.NewService, accessinfoDao.WireAccessInfoDaoSet)
	return &accessinfoConsumer.Service{}
}

func InitializeStoryService(conf *genconf.Config, actorInsightDB cmdTypes.ActorInsightsPGDB,
	client rewards.RewardsGeneratorClient, usersClient user.UsersClient, txnAggClient txnAggregatesPb.TxnAggregatesClient,
	actorClient actor.ActorClient, savingsClient savings.SavingsClient, connectedAccClient connected_account.ConnectedAccountClient,
	merchantClient merchantPb.MerchantServiceClient, piClient paymentinstrument.PiClient,
	creditReportManagerClient credit_report.CreditReportManagerClient, categorizerClient categorizer.TxnCategorizerClient,
	groupClient usergroupPb.GroupClient, fireflyClient firefly.FireflyClient, accountBalanceClient accountBalancePb.BalanceClient,
	ffTxnAggregatesClient ffpinotpb.TxnAggregatesClient, ffAccountingClient ffAccPb.AccountingClient, rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient) *story.Service {
	wire.Build(
		story.NewService,
		storyDao.WireStoryDaoSet,
		storyDao.WireStoryGroupDaoSet,
		storyDao.WireStoryGroupMappingDaoSet,
		storyDao.WireStoryTemplateDaoSet,
		storyDao.WireStoryGroupEngagementDaoSet,
		storiesgenerator.WireStoriesGeneratorSet,
		storyValGenFactory.WireStoryValueGeneratorFactorySet,
		evaluator.ExpressionEvaluatorWireSet,
		datafetcher.ActorAccountsWireSet,
		txnaggregates.TxnAggregatesWireSet,
		helper.CategoryHelperWireSet,
		utils.GenerateRandomNumberWireSet,
		creditScoreParamFetcher.CreditScoreParamsFetcherWireset,
		datafetcher.IMerchantsWireSet,
		datetime.WireDefaultTimeSet,
	)
	return &story.Service{}
}

func IDbResourceProviderProvider(dbConnProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) storagev2.IDbResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func InitialiseNetWorthService(
	cfg *genconf.Config,
	insightsDb cmdTypes.InsightsPGDB,
	savingsClient savings.SavingsClient,
	connectedAccountClient connected_account.ConnectedAccountClient,
	creditReportManagerClient credit_report.CreditReportManagerClient,
	userClient user.UsersClient,
	investAnalyticsClient investAnalyserPb.InvestmentAnalyticsClient,
	investmentAggregatorClient investmentAggregatorPb.InvestmentAggregatorClient,
	epfClient epfPb.EpfClient,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	client onboardingPb.OnboardingClient,
	mfExternalClient mfExternal.MFExternalOrdersClient,
	netWorthClient networthPb.NetWorthClient,
	iftClient payPb.InternationalFundTransferClient,
	zincSearchClient zincSearch.SearchClient,
	preapprovedloanClient preapprovedloan.PreApprovedLoanClient,
	empClient employmentPb.EmploymentClient,
	dbConnProvider *usecase.DBResourceProvider[*gorm.DB],
	catalogClient catalogPb.SecuritiesCatalogClient,
	npsCatalogClient npsPb.NpsCatalogClient,
	eventBroker events.Broker,
	mfCatalogClient mfCatalogPb.CatalogManagerClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
) *networth.Service {
	wire.Build(
		insightsGormDbProvider,
		networthParamsProvider,
		idgen.NewClock,
		idgen.WireSet,
		impl.InvestmentDeclarationWireSet,
		impl.NetWorthRefreshSessionWireSet,
		investmentDeclaration.WireInvestmentDeclarationProcessorSet,
		investmentCalculator.WireInvestmentCalculatorFactorySet,
		datetime.WireDefaultTimeSet,
		aaHelper.WireAaBalanceFetcherSet,
		epfHelper.WireEpfAggregatorHelperSet,
		mutualfund.WireMfAggregatorHelperSet,
		creditReportHelper.WireCreditReportHelperSet,
		investmentHelper.WireInvestmentAggregatorHelperSet,
		creditScoreParamFetcher.CreditScoreParamsFetcherWireset,
		assets.WireAssertValueAggregatorFactorySet,
		liabilities.WireLiabilityValueAggregatorFactorySet,
		impl.PMSProviderZincWireSet,
		impl.AIFZincWireSet,
		history.NetworthWireset,
		assetdaychange.WireSet,
		networth.NewService,
		storagev2.DefaultTxnExecutorWireSet,
		llm.GeminiClientWireSet,
		GeminiConfProvider,
	)

	return &networth.Service{}
}

func GeminiConfProvider(cfg *genconf.Config) *llmConf.GeminiConf {
	return cfg.GeminiConf()
}

func UANAccountsCacheConfigProvider(cfg *genconf.Config) *genconf.UANAccountsCacheConfig {
	return cfg.UANAccountsCacheConfig()
}

func InitializeEpfService(conf *genconf.Config,
	insightsDb cmdTypes.InsightsPGDB,
	employmentClient employment.EmploymentClient,
	userClient user.UsersClient,
	nameCheckClient ncPb.UNNameCheckClient,
	epfPassbookImportEventPublisher types.EpfPassbookImportEventPublisher,
	eventBroker events.Broker,
	analyserRedisStore analyserTypes.AnalyserRedisStore,
	employerClient empDao.EmploymentClient) *epf.Service {
	wire.Build(
		insightsGormDbProvider,
		analyserTypes.AnalyserRedisStoreRedisClientProvider,
		UANAccountsCacheConfigProvider,
		storagev2.TxnExecutorWireSet,
		epfDao.EPFPassbookRequestDaoWireSet,
		epfDao.UANAccountDaoCacheWireSet,
		epfDao.EPFImportSessionDaoWireSet,
		epfDao.EpfPassbookEmployeeDetailsDaoWireSet,
		epfDao.EpfPassbookEstDetailsDaoWireSet,
		epfDao.EpfPassbookTransactionDaoWireSet,
		epfDao.EpfPassbookOverallPfBalanceDaoWireSet,
		epf.EpfHelperWireset,
		cache.RedisStorageWireSet,
		epfDao.EmployerPfHistoryDetailsDaoWireSet,
		datetime.WireDefaultTimeSet,
		epf.NewService,
		epfDao.EpfSmsDataDaoWireSet,
	)
	return &epf.Service{}
}

func InitializeKubairService(dynConf *genconf.Config, networthClient networthPb.NetWorthClient, connectedAccountClient connected_account.ConnectedAccountClient) *kubair.Service {
	wire.Build(
		kubairProcessor.INetWorthProcessorWireSet,
		kubairProcessor.INpsProcessorWireSet,
		kubairComponentBuilder.AssetBottomComponentFactoryWireSet,
		aaHelper.WireAaBalanceFetcherSet,
		kubairComponentBuilder.ComponentBuilderFactoryWireSet,
		kubair.NewService,
	)
	return &kubair.Service{}
}

func networthParamsProvider(cfg *genconf.Config) *genconf.NetworthParams {
	return cfg.NetworthParams()
}

func addEmailAccBannerConfigProvider(conf *config.Config) *config.AddGmailAccountBannerParams {
	return conf.AddGmailAccountBannerParams
}

func oauthRevokeTokenUrlProvider(conf *config.Config) accessinfo.OAuthRevokeTokenUrl {
	return accessinfo.OAuthRevokeTokenUrl(conf.GoogleOAuthParams.RevokeTokenUrl)
}

func accessInfoKmsKeyIdProvider(conf *config.Config) accessinfo.AccessInfoKmsKeyId {
	return accessinfo.AccessInfoKmsKeyId(conf.Application.GmailDataEncrKeyKMSId)
}

func actorInsightsGormDbProvider(db cmdTypes.ActorInsightsPGDB) *gorm.DB {
	return db
}

func insightsGormDbProvider(db cmdTypes.InsightsPGDB) *gorm.DB {
	return db
}

func oauthCredentialProvider(conf *config.Config) insights.OAuthCredential {
	return insights.OAuthCredential(conf.Secrets.Ids[config.GoogleOAuthCredentials])
}

func gmailBatchGetParamsProvider(conf *config.Config) *config.GmailBatchGetApiParams {
	return conf.GmailBatchGetApiParams
}

func gmailListParamsProvider(conf *config.Config) *config.GmailListApiParams {
	return conf.GmailListApiParams
}

func emailparserParamsProvider(conf *config.Config) *config.EmailParserParams {
	return conf.EmailParserParams
}

func mailFetchConcurrencyParamsProvider(conf *config.Config) *config.MailFetchConcurrencyParams {
	return conf.MailFetchConcurrencyParams
}

func getGmailListApiClient(conf *config.Config) emailParserConsumer.GmailListApiClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.GmailListApiParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.GmailListApiParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.GmailListApiParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.GmailListApiParams.TimeoutInMillis) * time.Millisecond,
	}
	return emailParserConsumer.GmailListApiClient(httpClient)
}

func getAccessInfoCryptor(conf *config.Config, secretManagetClient *secretsmanager.Client) accessinfo.AccessInfoCryptor {
	env := conf.Application.Environment
	if env == cfg.DevelopmentEnv || env == cfg.TestEnv {
		return accessinfo.NewMockAccessInfoCryptorImpl()
	} else {
		secretsManager := smc.NewAwsSecretManager(secretManagetClient)
		return accessinfo.NewAccessInfoCryptorImpl(conf.Application.GmailDataEncrKeyKMSId, secretsManager, insights.NewAESCryptor())
	}
}

func getMailDataCryptor(conf *config.Config, kmsClient *kms.Client) emailParserConsumer.MailDataCryptor {
	env := conf.Application.Environment
	if env == cfg.DevelopmentEnv || env == cfg.TestEnv {
		return emailParserConsumer.NewMockMailDataCryptorImpl()
	} else {
		mailDataEncrKey := conf.Secrets.Ids[config.MailDataEncryptionKey]
		return emailParserConsumer.NewMailDataCryptorImpl(emailParserConsumer.MailDataEncryptionKey(mailDataEncrKey),
			emailParserConsumer.GmailDataEncrKeyKMSId(conf.Application.GmailDataEncrKeyKMSId),
			kms2.NewKMSSymmetricCryptor(kmsClient), insights.NewAESCryptor())
	}
}

func getHttpClientGmail(conf *config.Config) *emailParserConsumer.GmailClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.GmailBatchGetApiParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.GmailBatchGetApiParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.GmailBatchGetApiParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.GmailBatchGetApiParams.TimeoutInMillis) * time.Millisecond,
	}
	return &emailParserConsumer.GmailClient{Client: httpClient}
}

func getHttpClientForEmailParser(conf *config.Config) *emailParserConsumer.EmailParserClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.EmailParserParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.EmailParserParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.EmailParserParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.EmailParserParams.TimeoutInMillis) * time.Millisecond,
	}
	return &emailParserConsumer.EmailParserClient{Client: httpClient}
}

func getOAuthClient(conf *config.Config) accessinfo.OAuthClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.GoogleOAuthParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.GoogleOAuthParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.GoogleOAuthParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.GoogleOAuthParams.TimeoutInMillis) * time.Millisecond,
	}
	return accessinfo.OAuthClient(httpClient)
}

func mailFetchDateRangeProvider(conf *config.Config) emailParserConsumer.MailFetchDateRange {
	return emailParserConsumer.MailFetchDateRange(conf.Application.MailFetchDateRangeInMonths)
}

func emailIdRegexProvider(conf *config.Config) emailParserConsumer.EmailIdRegexString {
	return emailParserConsumer.EmailIdRegexString(conf.Application.EmailIdRegex)
}

func oauthCredProvider(conf *config.Config) insights.OAuthCredential {
	return insights.OAuthCredential(conf.Secrets.Ids[config.GoogleOAuthCredentials])
}

func InitializeEpfPassbookConsumerService(conf *genconf.Config, insightsPgdb dbTypes.InsightsPGDB, analyserRedisStore analyserTypes.AnalyserRedisStore) *epfConsumer.EpfPassbookConsumerService {
	wire.Build(
		epfConsumer.NewEpfPassbookConsumerService,
		epfDao.UANAccountDaoCacheWireSet,
		insightsGormDbProvider,
		analyserTypes.AnalyserRedisStoreRedisClientProvider,
		UANAccountsCacheConfigProvider,
		storagev2.TxnExecutorWireSet,
		epfDao.EpfPassbookEmployeeDetailsDaoWireSet,
		epfDao.EpfPassbookOverallPfBalanceDaoWireSet,
		epfDao.EpfPassbookEstDetailsDaoWireSet,
		epfDao.EpfPassbookTransactionDaoWireSet,
		cache.RedisStorageWireSet,
	)
	return &epfConsumer.EpfPassbookConsumerService{}
}

func InitializeUserDeclarationService(db cmdTypes.InsightsPGDB) *userDeclaration.Service {
	wire.Build(
		userDeclaration.NewService,
		userDeclarationDao.UserDeclarationDaoWireSet,
	)
	return &userDeclaration.Service{}
}
