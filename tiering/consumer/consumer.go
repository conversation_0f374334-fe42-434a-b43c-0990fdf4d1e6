package consumer

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/celestial/workflow/event"
	"github.com/epifi/be-common/api/celestial/workflow/stage"
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	usStocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/investment"
	events2 "github.com/epifi/gamma/api/investment/aggregator/events"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pay/savings_account/consumer"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	salaryprogramEventsPb "github.com/epifi/gamma/api/salaryprogram/events"
	savingsPb "github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringCoPb "github.com/epifi/gamma/api/tiering/consumer"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	userpb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/pay"
	tieringPkg "github.com/epifi/gamma/pkg/tiering"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/data"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
	tieringEvents "github.com/epifi/gamma/tiering/events"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/orchestrator"
	"github.com/epifi/gamma/tiering/release"
	tierOptions "github.com/epifi/gamma/tiering/tier_options"
	"github.com/epifi/gamma/tiering/tiermappings"
	"github.com/epifi/gamma/tiering/timeline"
)

var (
	balanceUpdateEventThreshold = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        5000,
	}
	balanceBelowThresholdMarketingEventMaxValue = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        1000,
	}
)

type TieringConsumer struct {
	tieringCoPb.UnimplementedTieringConsumerServer
	conf               *genconf.Config
	orchestrator       orchestrator.Orchestrator
	releaseManager     release.Manager
	eventBroker        events.Broker
	actorTierInfoDao   dao.ActorTierInfoDao
	timelineManager    timeline.TierTimeline
	tierOptionsManager tierOptions.Manager
	dataProcessor      data.TieringDataProcessor
	piClient           paymentinstrument.PiClient
	savingsClient      savingsPb.SavingsClient
	accPiClient        accountPIPb.AccountPIRelationClient
}

func NewTieringConsumer(
	conf *genconf.Config,
	orchestrator orchestrator.Orchestrator,
	releaseManager release.Manager,
	eventBroker events.Broker,
	actorTierInfoDao dao.ActorTierInfoDao,
	timelineManager timeline.TierTimeline,
	tierOptionsManager tierOptions.Manager,
	dataProcessor data.TieringDataProcessor,
	piClient paymentinstrument.PiClient,
	savingsClient savingsPb.SavingsClient,
	accPiClient accountPIPb.AccountPIRelationClient,
) *TieringConsumer {
	return &TieringConsumer{
		conf:               conf,
		orchestrator:       orchestrator,
		releaseManager:     releaseManager,
		eventBroker:        eventBroker,
		actorTierInfoDao:   actorTierInfoDao,
		timelineManager:    timelineManager,
		tierOptionsManager: tierOptionsManager,
		dataProcessor:      dataProcessor,
		piClient:           piClient,
		savingsClient:      savingsClient,
		accPiClient:        accPiClient,
	}
}

var (
	permanentFailureStatus = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
	transientFailureStatus = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
	successStatus          = queuePb.MessageConsumptionStatus_SUCCESS
)

func (t *TieringConsumer) ProcessTierReEvaluationEvent(ctx context.Context, req *tieringCoPb.ProcessTierReEvaluationEventRequest) (*tieringCoPb.ProcessTierReEvaluationEventResponse, error) {
	if req.GetActorEvaluationDetails().GetActorId() == "" {
		logger.Error(ctx, "actor id is empty in request")
		return &tieringCoPb.ProcessTierReEvaluationEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}

	actorId := req.GetActorEvaluationDetails().GetActorId()

	logger.Info(ctx, "received tier re-eval event", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, req.GetActorEvaluationDetails().GetFromTier()), zap.Any(logger.TO_TIER, req.GetActorEvaluationDetails().GetToTier()),
		zap.Any("computedAt", req.GetActorEvaluationDetails().GetComputedAt()))

	toSkipCurPacket, toSkipCurPacketErr := toSkipCurrentPacket(req.GetActorEvaluationDetails())
	if toSkipCurPacketErr != nil {
		logger.Error(ctx, "error checking if current packet can be skipped or not", zap.Error(toSkipCurPacketErr))
		return &tieringCoPb.ProcessTierReEvaluationEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}
	if toSkipCurPacket {
		logger.Info(ctx, "dropping packet", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Any(logger.FROM_TIER, req.GetActorEvaluationDetails().GetFromTier()), zap.Any(logger.TO_TIER, req.GetActorEvaluationDetails().GetToTier()),
			zap.Any("computedAt", req.GetActorEvaluationDetails().GetComputedAt()))
		return &tieringCoPb.ProcessTierReEvaluationEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	fromTier, toTier, err := t.orchestrator.OrchestrateTierMovement(ctx, actorId, tieringEnumPb.Provenance_PROVENANCE_AUTOMATIC, &tieringPb.TierMovementReason{})
	if err != nil {
		if helper.IsIgnorableOrchestratorError(err) {
			logger.Error(ctx, "got ignorable error in tier re-eval event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringCoPb.ProcessTierReEvaluationEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus}}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in tier re-eval event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessTierReEvaluationEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus}}, nil
	}
	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement for tier re-eval event", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, fromTier), zap.Any(logger.TO_TIER, toTier), zap.Any(logger.MOVEMENT_TYPE, movementType))
	return &tieringCoPb.ProcessTierReEvaluationEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

// toSkipCurrentPacket checks whether to skip the current packet or not based on the following conditions
// 1. If movement is an upgrade movement and movement is happening from UNSPECIFIED to TEN(first tier movement for non-tiered users)
func toSkipCurrentPacket(evaluationDetails *tieringCoPb.ActorEvaluationDetails) (bool, error) {
	extTier, _ := tiermappings.GetExternalTierFromInternalTier(evaluationDetails.GetToTier())
	movementType, getMmtTypeErr := helper.GetMovementTypeFromStartAndEndTiers(evaluationDetails.GetFromTier(), evaluationDetails.GetToTier())
	if getMmtTypeErr != nil {
		return false, errors.Wrap(getMmtTypeErr, "error getting movement type from fromTier and toTier")
	}
	if movementType == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE &&
		evaluationDetails.GetFromTier() == tieringEnumPb.Tier_TIER_UNSPECIFIED &&
		extTier.IsBaseTier() {
		return true, nil
	}
	return false, nil
}

func (t *TieringConsumer) ProcessKycUpdateEvent(ctx context.Context, req *userpb.KycEvent) (*tieringCoPb.ProcessKycUpdateEventResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "actor id is empty in request")
		return &tieringCoPb.ProcessKycUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}

	actorId := req.GetActorId()

	logger.Info(ctx, "received kyc update event", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actionTimestamp", req.GetActionTimestamp()))

	fromTier, toTier, err := t.orchestrator.OrchestrateTierMovement(ctx, actorId, tieringEnumPb.Provenance_PROVENANCE_KYC_CONSUMER, &tieringPb.TierMovementReason{})
	if err != nil {
		if helper.IsIgnorableOrchestratorError(err) {
			logger.Error(ctx, "got ignorable error in kyc update event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringCoPb.ProcessKycUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus}}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in kyc update event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessKycUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus}}, nil
	}
	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement for kyc update event", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, fromTier), zap.Any(logger.TO_TIER, toTier), zap.Any(logger.MOVEMENT_TYPE, movementType))
	return &tieringCoPb.ProcessKycUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

func (t *TieringConsumer) ProcessSalaryUpdateEvent(ctx context.Context, req *salaryprogramEventsPb.SalaryProgramStatusUpdateEvent) (*tieringCoPb.ProcessSalaryUpdateEventResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "actor id is empty in request")
		return &tieringCoPb.ProcessSalaryUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}

	if !(req.GetSalaryProgramStatus() == salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_ACTIVE ||
		req.GetSalaryProgramStatus() == salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_INACTIVE) {
		logger.Error(ctx, "received invalid salary program status, not processing further",
			zap.Any("salaryProgramStatus", req.GetSalaryProgramStatus()))
		return &tieringCoPb.ProcessSalaryUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	actorId := req.GetActorId()

	logger.Info(ctx, "received salary update event", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("salaryProgramStatus", req.GetSalaryProgramStatus()))
	fromTier, toTier, err := t.orchestrator.OrchestrateTierMovement(ctx, actorId, tieringEnumPb.Provenance_PROVENANCE_SALARY_CONSUMER, &tieringPb.TierMovementReason{})
	if err != nil {
		if helper.IsIgnorableOrchestratorError(err) {
			logger.Error(ctx, "got ignorable error in salary update event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringCoPb.ProcessSalaryUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus}}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement salary update event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessSalaryUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus}}, nil
	}
	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement for salary update event", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, fromTier), zap.Any(logger.TO_TIER, toTier), zap.Any(logger.MOVEMENT_TYPE, movementType))
	return &tieringCoPb.ProcessSalaryUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

func (t *TieringConsumer) ProcessBalanceUpdateEvent(ctx context.Context, req *consumer.BalanceUpdate) (*tieringCoPb.ProcessBalanceUpdateEventResponse, error) {
	actorId := req.GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actor id is empty in request")
		return &tieringCoPb.ProcessBalanceUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}
	// Check if we need to proceed with the packet further
	toTriggerEvent, triggerCheckErr := t.toTriggerEvent(req)
	if triggerCheckErr != nil {
		logger.Error(ctx, "error checking event can be triggered or not", zap.Error(triggerCheckErr))
		return &tieringCoPb.ProcessBalanceUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}
	if !toTriggerEvent {
		logger.Debug(ctx, "not triggering since trigger not met", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessBalanceUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}
	if triggerEventErr := t.triggerEvent(ctx, actorId, req.GetAvailableBalance()); triggerEventErr != nil {
		headerStatus := queue.GetStatusFromErr(triggerEventErr)
		logger.Error(ctx, "error triggering event", zap.Error(triggerEventErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessBalanceUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: headerStatus},
		}, nil
	}
	logger.Debug(ctx, "successfully processed tiering balance update event", zap.Any(logger.ACTOR_ID_V2, actorId))
	return &tieringCoPb.ProcessBalanceUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

// toTriggerEvent checks whether to proceed with triggering event based on certain pre-defined criteria
func (t *TieringConsumer) toTriggerEvent(req *consumer.BalanceUpdate) (bool, error) {
	// 1. Should be a credit txn
	// 2. Only needs to be trigger if balance >= Threshold
	cmpVal, cmpErr := moneyPkg.CompareV2(req.GetAvailableBalance(), balanceUpdateEventThreshold)
	if cmpErr != nil {
		return false, errors.Wrap(cmpErr, "error comparing available balance and threshold amount")
	}
	return moneyPkg.IsPositive(req.GetDiffBalance()) && cmpVal >= 0, nil
}

// triggerEvent gathers data needed and triggers event
func (t *TieringConsumer) triggerEvent(ctx context.Context, actorId string, balanceAmount *moneyPb.Money) error {
	collectedData, gatherDataErr := t.gatherDataForTriggeringEvent(ctx, actorId)
	if gatherDataErr != nil {
		return fmt.Errorf("error gathering data for triggering event: %v: %w", gatherDataErr, epifierrors.ErrTransient)
	}
	nextClosestTier, nextClosestTierErr := t.determineNextClosestTier(collectedData.GetCurrentTier(), balanceAmount, collectedData.GetTierOptionsMap())
	if nextClosestTierErr != nil {
		return fmt.Errorf("error determining next closest tier user can upgrade: %v: %w", nextClosestTierErr, epifierrors.ErrPermanent)
	}
	balanceBucket := tieringPkg.DetermineBalanceBucket(balanceAmount.GetUnits())
	event := tieringEvents.NewMetTieringCriteria(
		actorId,
		time.Now(),
		nextClosestTier.String(),
		collectedData.GetCurrentTier().String(),
		collectedData.GetIsUserInCoolOff(),
		balanceBucket,
	)
	logger.Debug(ctx, "publishing tiering criteria met event", zap.Any("event", event))
	t.eventBroker.AddToBatch(
		epificontext.WithEventAttributes(ctx),
		event,
	)
	return nil
}

// determineNextClosestTier determines next closest tier the actor can upgrade based on current balance
// if user already has enough balance for some tier return that tier
func (t *TieringConsumer) determineNextClosestTier(currentTier tieringExtPb.Tier,
	balanceAmount *moneyPb.Money, tierOptionsMap tierOptions.TierOptionsMap) (tieringExtPb.Tier, error) {
	currentIntTier, conversionErr := tiermappings.GetInternalTierFromExternalTier(currentTier)
	if conversionErr != nil {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, errors.Wrap(conversionErr, "error converting external tier to internal tier")
	}
	for _, tier := range tiermappings.GetAllActiveInternalTiers() {
		// if tier is a salary related tier then skip since it won't have min balance as criteria
		if tiermappings.IsSalaryRelatedTier(tier) {
			continue
		}
		tierOpts, ok := tierOptionsMap[tier]
		if !ok {
			return tieringExtPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("error fetching tier options for tier: %s", tier.String())
		}
		// Ignore if current tier is already higher or same
		if currentIntTier.Number() >= tier.Number() {
			continue
		}
		minBalance, getMinBalanceErr := helper.GetMinBalanceFromTierOptions(tierOpts)
		if getMinBalanceErr != nil {
			if errors.Is(getMinBalanceErr, tieringErrors.ErrTierHasNoMinBalanceCriteria) {
				continue
			}
			return tieringExtPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("error fetching min balance for %s", tier.String())
		}
		cmpVal, cmpErr := moneyPkg.CompareV2(balanceAmount, minBalance)
		if cmpErr != nil {
			return tieringExtPb.Tier_TIER_UNSPECIFIED, errors.Wrap(cmpErr, "error comparing current balance amount and tier min balance")
		}
		if cmpVal >= 0 {
			extTier, extToIntConvErr := tiermappings.GetExternalTierFromInternalTier(tier)
			if extToIntConvErr != nil {
				return tieringExtPb.Tier_TIER_UNSPECIFIED, errors.Wrap(extToIntConvErr, "error converting internal tier to external tier")
			}
			return extTier, nil
		}
	}
	return tieringExtPb.Tier_TIER_UNSPECIFIED, nil
}

// gatherDataForTriggeringEvent gathers data needed for triggerint the criteria event
func (t *TieringConsumer) gatherDataForTriggeringEvent(ctx context.Context, actorId string) (*BalanceEventCollectedData, error) {
	gatherDataErrGrp, gCtx := errgroup.WithContext(ctx)
	var (
		curExtTier      tieringExtPb.Tier
		isUserInCoolOff bool
		tierOptionsMap  tierOptions.TierOptionsMap
	)
	gatherDataErrGrp.Go(func() error {
		currentTier, getCurTierErr := t.dataProcessor.GetCurrentTierDefaultToBaseTier(gCtx, actorId)
		if getCurTierErr != nil {
			return errors.Wrap(getCurTierErr, "error getting current tier for actor")
		}
		var conversionErr error
		curExtTier, conversionErr = tiermappings.GetExternalTierFromInternalTier(currentTier)
		if conversionErr != nil {
			return errors.Wrap(conversionErr, "error converting internal tier to external tier")
		}
		return nil
	})
	gatherDataErrGrp.Go(func() error {
		var coolOffCheckErr error
		isUserInCoolOff, coolOffCheckErr = t.isUserInCoolOff(gCtx, actorId)
		if coolOffCheckErr != nil {
			return errors.Wrap(coolOffCheckErr, "error checking if user is in cool off or not")
		}
		return nil
	})
	gatherDataErrGrp.Go(func() error {
		var getTierOptsMapErr error
		tierOptionsMap, getTierOptsMapErr = t.tierOptionsManager.GetTierOptionsMap(gCtx, actorId)
		if getTierOptsMapErr != nil {
			return errors.Wrap(getTierOptsMapErr, "error getting tier options map")
		}
		return nil
	})
	if gatherDataErr := gatherDataErrGrp.Wait(); gatherDataErr != nil {
		return nil, gatherDataErr
	}
	return &BalanceEventCollectedData{
		CurrentTier:     curExtTier,
		IsUserInCoolOff: isUserInCoolOff,
		TierOptionsMap:  tierOptionsMap,
	}, nil
}

func (t *TieringConsumer) isUserInCoolOff(ctx context.Context, actorId string) (bool, error) {
	coolOffErrGrp, gCtx := errgroup.WithContext(ctx)
	var isUserInCoolOff, willUserBeInCoolOff bool
	coolOffErrGrp.Go(func() error {
		var coolOffCheckErr error
		isUserInCoolOff, coolOffCheckErr = t.timelineManager.IsUserInCooloff(gCtx, actorId)
		if coolOffCheckErr != nil {
			return errors.Wrap(coolOffCheckErr, "error checking if the user is in cool off or not")
		}
		return nil
	})
	coolOffErrGrp.Go(func() error {
		var coolOffCheckErr error
		willUserBeInCoolOff, coolOffCheckErr = t.timelineManager.WillUserBeInCooloff(gCtx, actorId)
		if coolOffCheckErr != nil {
			return errors.Wrap(coolOffCheckErr, "error checking if the user is in cool off or not")
		}
		return nil
	})
	if coolOffCheckErr := coolOffErrGrp.Wait(); coolOffCheckErr != nil {
		return false, coolOffCheckErr
	}
	return isUserInCoolOff || willUserBeInCoolOff, nil
}

func (t *TieringConsumer) ProcessBalanceUpdateMarketingEvent(ctx context.Context, req *consumer.BalanceUpdate) (*tieringCoPb.ProcessBalanceUpdateMarketingEventResponse, error) {
	actorId := req.GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actor id is empty in request")
		return &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}

	// Check is packet passes eligibility criteria to trigger event
	shouldEventTrigger, shouldEventTriggerErr := t.shouldBalanceBelowThresholdMarketingEventTrigger(req)
	if shouldEventTriggerErr != nil {
		logger.Error(ctx, "error checking if balanceUpdateMarketingEvent can be triggered or not", zap.Error(shouldEventTriggerErr))
		return &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}
	if !shouldEventTrigger {
		logger.Debug(ctx, "not triggering balanceUpdateMarketingEvent since criteria not met", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}
	if triggerBalanceUpdateMarketingEventErr := t.triggerBalanceUpdateMarketingEvent(ctx, actorId, req.GetAvailableBalance()); triggerBalanceUpdateMarketingEventErr != nil {
		logger.Error(ctx, "error triggering triggerBalanceUpdateMarketingEvent", zap.Error(triggerBalanceUpdateMarketingEventErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queue.GetStatusFromErr(triggerBalanceUpdateMarketingEventErr)},
		}, nil
	}
	return &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

func (t *TieringConsumer) shouldBalanceBelowThresholdMarketingEventTrigger(req *consumer.BalanceUpdate) (bool, error) {
	// Balance below threshold marketing event is fulfilled when:
	//	 - Should be a Debit Txn
	// 	 - Final balance has gone less than highest bucket(bucket with maximum balance or configurable)

	cmpVal, cmpErr := moneyPkg.CompareV2(req.GetAvailableBalance(), balanceBelowThresholdMarketingEventMaxValue)
	if cmpErr != nil {
		return false, errors.Wrap(cmpErr, "error comparing available balance and balance threshold amount")
	}
	return moneyPkg.IsNegative(req.GetDiffBalance()) && cmpVal <= 0, nil
}

func (t *TieringConsumer) triggerBalanceUpdateMarketingEvent(ctx context.Context, actorId string, balanceAmount *moneyPb.Money) error {
	upperLimitForBalanceBucket := t.getUpperLimitForBalanceBucket(balanceAmount.GetUnits())
	if upperLimitForBalanceBucket == int64(-1) {
		return fmt.Errorf("invalid BalanceUpdateMarketingEvent encountered as balance more than %v: %w", balanceBelowThresholdMarketingEventMaxValue.GetUnits(), epifierrors.ErrPermanent)
	}
	event := tieringEvents.NewBalanceBelowThreshold(
		actorId,
		time.Now(),
		upperLimitForBalanceBucket,
	)
	logger.Debug(ctx, "publishing BalanceUpdateMarketingEvent event", zap.Any("event", event))
	t.eventBroker.AddToBatch(
		epificontext.WithEventAttributes(ctx),
		event,
	)
	return nil
}

func (t *TieringConsumer) getUpperLimitForBalanceBucket(balance int64) int64 {
	switch {
	case balance <= 0:
		return 0
	case balance <= 100:
		return 100
	case balance <= 500:
		return 500
	case balance <= 1000:
		return 1000
	default:
		// this should not be reachable ideally, as all the packets where balance is more than 1000 will be discarded
		return -1
	}
}

func (t *TieringConsumer) ProcessAddFundsOrderEvent(ctx context.Context, orderUpdateEvent *orderPb.OrderUpdate) (*tieringCoPb.ProcessAddFundsOrderEventResponse, error) {
	isTpapAddFundsOrder, isTpapAddFundsOrderErr := pay.IsTpapAddFundsOrder(ctx, t.piClient, t.savingsClient, t.accPiClient, orderUpdateEvent.GetOrderWithTransactions())
	if isTpapAddFundsOrderErr != nil {
		logger.Error(ctx, "error checking if order is tpap add funds order or not", zap.Error(isTpapAddFundsOrderErr))
		return &tieringCoPb.ProcessAddFundsOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
		}, nil
	}

	isAddFundsOrder := orderUpdateEvent.GetOrderWithTransactions().GetOrder().IsAddFundsOrder()
	if !isAddFundsOrder && !isTpapAddFundsOrder {
		return &tieringCoPb.ProcessAddFundsOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	if orderUpdateEvent.GetOrderWithTransactions().GetOrder().GetUiEntryPoint() == orderPb.UIEntryPoint_AA_SALARY {
		logger.Debug(ctx, "order is aa salary add fund, skipping processing", zap.String(logger.ACTOR_ID_V2, orderUpdateEvent.GetOrderWithTransactions().GetOrder().GetToActorId()))
		return &tieringCoPb.ProcessAddFundsOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	actorId := orderUpdateEvent.GetOrderWithTransactions().GetOrder().GetToActorId()
	orderId := orderUpdateEvent.GetOrderWithTransactions().GetOrder().GetId()
	if isAddFundsOrder && !orderUpdateEvent.GetOrderWithTransactions().GetOrder().GetStatus().IsTerminalForAddFunds() {
		logger.Debug(ctx, "add funds order is not in terminal state", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, orderId))
		return &tieringCoPb.ProcessAddFundsOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	if isTpapAddFundsOrder && orderUpdateEvent.GetOrderWithTransactions().GetOrder().GetStatus() != orderPb.OrderStatus_PAID {
		logger.Debug(ctx, "tpap add funds order is not in paid state", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, orderId))
		return &tieringCoPb.ProcessAddFundsOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	// adding a sleep to process tier orchestration to avoid race condition
	// we have a sync flow in app where we poll for order status and then process tier orchestration
	// we need to avoid a race condition between the polling in the sync flow and the async flow in this consumer
	time.Sleep(3 * time.Second)
	logger.Info(ctx, "received add funds order event", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, orderId))

	fromTier, toTier, orchestrateErr := t.orchestrator.OrchestrateTierMovement(ctx, actorId, tieringEnumPb.Provenance_PROVENANCE_ADD_FUNDS_CONSUMER, &tieringPb.TierMovementReason{})
	if orchestrateErr != nil {
		if helper.IsIgnorableOrchestratorError(orchestrateErr) {
			logger.Info(ctx, "got ignorable error in add funds order event", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringCoPb.ProcessAddFundsOrderEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in add funds order event", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessAddFundsOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
		}, nil
	}
	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement for add funds order event", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, fromTier), zap.Any(logger.TO_TIER, toTier), zap.Any(logger.MOVEMENT_TYPE, movementType))
	return &tieringCoPb.ProcessAddFundsOrderEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

func (t *TieringConsumer) ProcessInvestmentEvent(ctx context.Context, in *events2.InvestmentEvent) (*tieringCoPb.ProcessInvestmentEventResponse, error) {
	actorId := in.GetActorId()
	logger.Debug(ctx, "received investment event", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("eventType", in.GetEventType()), zap.Any("instrumentType", in.GetInstrumentType()))

	if !lo.Contains([]investment.InvestmentInstrumentType{investment.InvestmentInstrumentType_SMART_DEPOSIT, investment.InvestmentInstrumentType_FIXED_DEPOSIT},
		in.GetInstrumentType()) {
		return &tieringCoPb.ProcessInvestmentEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	if in.GetEventType() != events2.EventType_EventType_ANY_INVESTMENT_DONE {
		return &tieringCoPb.ProcessInvestmentEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	// todo[obed]: remove after testing
	logger.Info(ctx, "received deposit investment event", zap.String(logger.ACTOR_ID_V2, actorId))

	fromTier, toTier, orchestrateErr := t.orchestrator.OrchestrateTierMovement(ctx, actorId, tieringEnumPb.Provenance_PROVENANCE_INVESTMENTS_EVENT_CONSUMER, &tieringPb.TierMovementReason{})
	if orchestrateErr != nil {
		if helper.IsIgnorableOrchestratorError(orchestrateErr) {
			logger.Info(ctx, "got ignorable error in investment event", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringCoPb.ProcessInvestmentEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in investment event", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessInvestmentEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
		}, nil
	}

	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement for investment event",
		zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.FROM_TIER, fromTier),
		zap.Any(logger.TO_TIER, toTier), zap.Any(logger.MOVEMENT_TYPE, movementType))

	return &tieringCoPb.ProcessInvestmentEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}

func (t *TieringConsumer) ProcessUsStocksWalletOrderEvent(ctx context.Context, in *event.WorkflowUpdate) (*tieringCoPb.ProcessUsStocksWalletOrderEventResponse, error) {
	actorId := in.GetWorkflowRequest().GetActorId()
	logger.Debug(ctx, "received us stocks wallet order event", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any("workflowType", in.GetWorkflowRequest().GetTypeEnum()),
		zap.Any("stage", in.GetWorkflowRequest().GetStageEnum()),
		zap.Any("status", in.GetWorkflowRequest().GetStatus()))

	if celestial.GetWorkflowTypeFromTypeEnum(in.GetWorkflowRequest().GetTypeEnum()) != usStocksNs.AddFundsToWallet ||
		epifitemporal.Stage(in.GetWorkflowRequest().GetStageEnum().GetStageValue()) != usStocksNs.TrackWalletFundTransferStage ||
		in.GetWorkflowRequest().GetStatus() != stage.Status_SUCCESSFUL {
		return &tieringCoPb.ProcessUsStocksWalletOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
		}, nil
	}

	// todo[obed]: remove after testing
	logger.Info(ctx, "received us stocks wallet order", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String(logger.WORKFLOW_REQ_ID, in.GetWorkflowRequest().GetId()))

	fromTier, toTier, orchestrateErr := t.orchestrator.OrchestrateTierMovement(ctx, actorId,
		tieringEnumPb.Provenance_PROVENANCE_US_STOCKS_WALLET_ORDER_CONSUMER, &tieringPb.TierMovementReason{})
	if orchestrateErr != nil {
		// adding a special handling in usstocks consumer to retry tier orchestration for certain cases
		// reason: there is a delay in entry to be created in wallet_orders table and the TrackWalletFundTransferStage completion
		if errors.Is(orchestrateErr, tieringErrors.ErrEligibleMovementNotMatured) ||
			errors.Is(orchestrateErr, tieringErrors.ErrNotMovableButEligibilityCreated) ||
			errors.Is(orchestrateErr, tieringErrors.ErrSameTierMovementNotAllowed) {
			logger.Info(ctx, "got ignorable error, tier update did not happen, retrying on the next attempt", zap.Error(orchestrateErr))
			return &tieringCoPb.ProcessUsStocksWalletOrderEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
			}, nil
		}

		if helper.IsIgnorableOrchestratorError(orchestrateErr) {
			logger.Info(ctx, "got ignorable error in us stocks wallet order event", zap.Error(orchestrateErr),
				zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringCoPb.ProcessUsStocksWalletOrderEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in us stocks wallet order event", zap.Error(orchestrateErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringCoPb.ProcessUsStocksWalletOrderEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
		}, nil
	}

	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement for us stocks wallet order event",
		zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.FROM_TIER, fromTier),
		zap.Any(logger.TO_TIER, toTier), zap.Any(logger.MOVEMENT_TYPE, movementType))

	return &tieringCoPb.ProcessUsStocksWalletOrderEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}
