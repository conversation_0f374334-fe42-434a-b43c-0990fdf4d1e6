// nolint: goimports
//
//go:generate mockgen -source=./blocker.go -destination=../../test/mocks/call/blocker/mock_blocker.go -package=mocks
package blocker

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"k8s.io/apimachinery/pkg/util/waitgroup"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	cacheStorage "github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/comms"
	callRoutingPb "github.com/epifi/gamma/api/cx/call_routing"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/firefly"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/gamma/api/preapprovedloan"
	riskProfilePb "github.com/epifi/gamma/api/risk/profile"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	genConf "github.com/epifi/gamma/cx/config/genconf"
	cxEvents "github.com/epifi/gamma/cx/events"
	"github.com/epifi/gamma/cx/metrics"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/frontend/cx"
	"github.com/epifi/gamma/pkg/obfuscator"
)

var (
	UnRegisteredUserErr             = errors.New("user not registered")
	AppAccessBlockedErr             = errors.New("app access is blocked")
	UserReportedIssueErr            = errors.New("user reported issue")
	NonStandardTierUserErr          = errors.New("user is not in basic tier")
	CallBlockingDisabledErr         = errors.New("call blocking is disabled")
	TriagedUserWithActiveLoanErr    = errors.New("user is triaged and has active loan")
	TriagedUserWithKycDue           = errors.New("user is triaged and has kyc due")
	TriagedUserWithActiveCreditCard = errors.New("user is triaged and has active credit card")
	ActiveLoanUserErr               = errors.New("user has active loan")
	ActiveCreditCardUserErr         = errors.New("user has active credit card")
	UserSentToRiskIvrFlowErr        = errors.New("user sent to risk ivr flow")
)

type Blocker interface {
	// DeterminePreRecordedMsgForCaller determines the prerecorded message to be played to the caller.
	// It performs various checks related to the user who is calling,
	// and based on the business requirement returns the prerecorded message.
	// Only for the cases this method returns a RecordingIdentifier we will play the prerecorded message
	// and drop the call for user, otherwise in case of any failure, user's call will be connected to agent
	DeterminePreRecordedMsgForCaller(ctx context.Context, phoneNumber *commontypes.PhoneNumber, monitorUcid string) (callRoutingPb.RecordingIdentifier, error)
}

type BlockerImpl struct {
	usersClient           userPb.UsersClient
	cacheStorage          cacheStorage.CacheStorage
	cxGenConf             *genConf.Config
	riskProfileClient     riskProfilePb.ProfileClient
	commsClient           comms.CommsClient
	tieringClient         beTieringPb.TieringClient
	ticketClient          ticketPb.TicketClient
	eventBroker           events.Broker
	releaseEvaluator      release.IEvaluator
	redisClient           *redis.Client
	preApprovedLoanClient preapprovedloan.PreApprovedLoanClient
	compClient            compliancePb.ComplianceClient
	ffClient              firefly.FireflyClient
}

func NewBlocker(usersClient userPb.UsersClient, cacheStorage cacheStorage.CacheStorage, cxGenConf *genConf.Config,
	riskProfileClient riskProfilePb.ProfileClient, commsClient comms.CommsClient, tieringClient beTieringPb.TieringClient,
	ticketClient ticketPb.TicketClient, eventBroker events.Broker, releaseEvaluator release.IEvaluator,
	redisClient *redis.Client, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient,
	compClient compliancePb.ComplianceClient, ffClient firefly.FireflyClient) *BlockerImpl {
	return &BlockerImpl{usersClient: usersClient, cacheStorage: cacheStorage, cxGenConf: cxGenConf,
		riskProfileClient: riskProfileClient, commsClient: commsClient, tieringClient: tieringClient,
		ticketClient: ticketClient, eventBroker: eventBroker, releaseEvaluator: releaseEvaluator,
		redisClient: redisClient, preApprovedLoanClient: preApprovedLoanClient, compClient: compClient, ffClient: ffClient}
}

var _ Blocker = &BlockerImpl{}

func (b *BlockerImpl) DeterminePreRecordedMsgForCaller(ctx context.Context, phoneNumber *commontypes.PhoneNumber,
	monitorUcid string) (callRoutingPb.RecordingIdentifier, error) {
	startTime := time.Now()
	status := metrics.SuccessStr
	defer func() {
		processingDuration := time.Since(startTime)
		metrics.RecordCallBlockerTimestamp(status, processingDuration)
	}()
	if monitorUcid == "" {
		logger.Error(ctx, "monitor ucid is empty")
	}

	// acquiring lock on monitor ucid to avoid duplicate events / sending duplicate comms for the same call
	isLockAcquired, err := b.acquireLock(ctx, monitorUcid)
	if err != nil {
		logger.Error(ctx, "error while acquiring lock for call blocker", zap.Error(err))
	}
	if !isLockAcquired {
		logger.Info(ctx, "duplicate event received for call blocker metrics won't be piped for this call",
			zap.String(logger.PHONE_NUMBER, obfuscator.HashedPhoneNum(phoneNumber)))
	}

	userResp, userErr := b.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		if isLockAcquired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent("", monitorUcid,
				cxEvents.CallBlockerEventNoStr, cxEvents.UserRegistrationStatusNotFound))
			metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, cxEvents.UserRegistrationStatusNotFound)

		}
		if userResp.GetStatus().IsRecordNotFound() {
			if isLockAcquired {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCxCallBlockerUserRegistrationEvent("", monitorUcid, cxEvents.CallBlockerEventNoStr))
			}
			return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, UnRegisteredUserErr
		}

		if isLockAcquired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCxCallBlockerUserRegistrationEvent("", monitorUcid, cxEvents.CallBlockerEventFailureStr))
		}
		logger.Error(ctx, "error while fetching user from phone number", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
		status = metrics.FailureStr
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED,
			errors.Wrap(te, fmt.Sprintf("error while fetching actor from phone number : %s", obfuscator.HashedPhoneNum(phoneNumber)))
	}

	// check if actor is part of release group for this feature
	isCallBlockingEnabled, releaseErr := b.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_FEATURE_CX_CALL_BLOCKER).WithActorId(userResp.GetUser().GetActorId()))
	if releaseErr != nil {
		logger.Error(ctx, "error while checking if call blocking enabled for actor", zap.Error(releaseErr),
			zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, CallBlockingDisabledErr
	}
	if !isCallBlockingEnabled {
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, CallBlockingDisabledErr
	}

	actorId := userResp.GetUser().GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)

	b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerInitializationEvent(actorId, monitorUcid, isLockAcquired))

	// if a user's app access is revoked, connect them to the agent
	if userResp.GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus() == userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED {
		if isLockAcquired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserAppAccessEvent(actorId, monitorUcid,
				cxEvents.CallBlockerEventYesStr, userResp.GetUser().GetAccessRevokeDetails().GetReason().String()))
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid, cxEvents.CallBlockerEventNoStr, cxEvents.NoAppAccess))
			metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, cxEvents.NoAppAccess)
		}
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, AppAccessBlockedErr
	}
	if isLockAcquired {
		b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserAppAccessEvent(actorId, monitorUcid,
			cxEvents.CallBlockerEventNoStr, userResp.GetUser().GetAccessRevokeDetails().GetReason().String()))
	}

	isRiskIvrEnabled, err := b.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_CX_CALL_RISK_IVR_FLOW).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error while checking for risk ivr flow enabled for user group", zap.Error(err), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	// check for user's risk blocks and whether we should play call recording for credit freeze case
	riskUserProfileResp, riskUserProfileErr := b.riskProfileClient.GetDetailedUserProfile(ctx, &riskProfilePb.GetDetailedUserProfileRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(riskUserProfileResp, riskUserProfileErr); te != nil {
		if isLockAcquired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserInCreditFreezeEvent(actorId, monitorUcid, cxEvents.CallBlockerEventFailureStr, cxEvents.CallBlockerEventFailureStr))
		}
		logger.Error(ctx, "error while fetching risk related profile for actor", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
	}

	// ShouldPlayCallRecording helps us determine the users who Fi blocks internally based on risk criteria
	if riskUserProfileResp.ShouldPlayCallRecording() {
		logger.Info(ctx, "actor is eligible for high risk pre-recorded message", zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
		// If the user is blocked by risk, we will not play the prerecorded message and will start the risk IVR flow, if the flow is enabled
		if isRiskIvrEnabled {
			if isLockAcquired {
				metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventYesStr, cxEvents.UserSentToRiskIvrFlow)
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid, cxEvents.CallBlockerEventYesStr, cxEvents.UserSentToRiskIvrFlow))
			}
			return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_HIGH_RISK_SCORE, UserSentToRiskIvrFlowErr
		}

		// otherwise, play the normal recording for high-risk users
		if isLockAcquired {
			commsErr := b.sendCommsForHighRiskUseCase(ctx, userResp.GetUser().GetId(), monitorUcid)
			if commsErr != nil {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserInCreditFreezeEvent(actorId, monitorUcid,
					cxEvents.CallBlockerEventYesStr, cxEvents.CallBlockerEventFailureStr))
				logger.Error(ctx, "error while sending comms to user", zap.Error(commsErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
			} else {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserInCreditFreezeEvent(actorId, monitorUcid,
					cxEvents.CallBlockerEventYesStr, cxEvents.CallBlockerEventYesStr))
			}
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
				cxEvents.CallBlockerEventYesStr, cxEvents.UserInCreditFreeze))
			metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventYesStr, cxEvents.UserInCreditFreeze)
		}
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_HIGH_RISK_SCORE, nil
	}

	isHighPriorityProductUser := b.isHighPriorityProductUser(ctx, actorId, monitorUcid, isLockAcquired)
	// as per product and ops alignment, high-priority product users
	// should be connected to the agent, hence not blocking them
	if isHighPriorityProductUser {
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, nil
	}

	// check if user has already reported the issue via issue reporting flow
	cachedData, err := b.cacheStorage.Get(ctx, fmt.Sprintf(b.cxGenConf.CustomerAuth().AuthFactorCacheKey(), actorId))
	if err != nil {
		if isLockAcquired {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserHasAlreadyReportedIssueViaApp(actorId, monitorUcid, cxEvents.CallBlockerEventNoStr))
			} else {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserHasAlreadyReportedIssueViaApp(actorId, monitorUcid, cxEvents.CallBlockerEventFailureStr))
			}
		}
		logger.Error(ctx, "error while fetching cached auth factor", zap.Error(err), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
	}
	if cachedData != "" {
		authDetails := &caPb.CachedAuthDetails{}
		unmarshalErr := protojson.Unmarshal([]byte(cachedData), authDetails)
		if unmarshalErr != nil {
			if isLockAcquired {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserHasAlreadyReportedIssueViaApp(actorId, monitorUcid, cxEvents.CallBlockerEventFailureStr))
			}
			logger.Error(ctx, "error while unmarshalling cached auth details", zap.Error(unmarshalErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
		}
		if authDetails.GetAuthFactorStatus() == caPb.AuthFactorStatus_VERIFICATION_SUCCESS {
			if isLockAcquired {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserHasAlreadyReportedIssueViaApp(actorId, monitorUcid, cxEvents.CallBlockerEventYesStr))
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
					cxEvents.CallBlockerEventNoStr, cxEvents.UserAlreadyReportedIssue))
				metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, cxEvents.UserAlreadyReportedIssue)
			}
			return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, UserReportedIssueErr
		}
	}

	tieringResp, tieringErr := b.tieringClient.GetTieringPitchV2(ctx, &beTieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if rErr := epifigrpc.RPCError(tieringResp, tieringErr); rErr != nil {
		if isLockAcquired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserTierEvent(actorId, monitorUcid, cxEvents.CallBlockerEventFailureStr))
		}
		logger.Error(ctx, "error while fetching tiering pitch", zap.Error(rErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
	} else {
		if isLockAcquired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserTierEvent(actorId, monitorUcid, tieringResp.GetCurrentTier().String()))
		}
	}
	if lo.Contains(b.cxGenConf.CallConfig().CallBlockerConfig().TriagedTierList().ToStringArray(), tieringResp.GetCurrentTier().String()) {

		isActiveUser, triageErr := b.isActiveProductUser(ctx, actorId, monitorUcid, isLockAcquired)
		if triageErr != nil {
			return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, errors.Wrap(triageErr, "error while processing triage tier")
		}
		if isActiveUser {
			return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, nil
		} else {
			// all the calls which reach this stage will not be connected to the agent, hence sending the call drop off sms here
			if isLockAcquired {
				commsErr := b.sendCommsDropOffCase(ctx, userResp.GetUser().GetId(), monitorUcid, userResp.GetUser().GetProfile().GetKycName().GetFirstName())
				if commsErr != nil {
					b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerCallDropOffCommsEvent(actorId, monitorUcid, cxEvents.CallBlockerEventFailureStr))
					logger.Error(ctx, "error while sending comms to user", zap.Error(commsErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
				} else {
					b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerCallDropOffCommsEvent(actorId, monitorUcid, cxEvents.CallBlockerEventYesStr))
				}
			}
			return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_REDIRECT_TO_ISSUE_REPORTING_FLOW, nil
		}
	}

	// if the user is not part of blocked tier (i.e., Standard tier, Plus, etc.) let them through to the agent.
	if !lo.Contains(b.cxGenConf.CallConfig().CallBlockerConfig().BlockTierList().ToStringArray(), tieringResp.GetCurrentTier().String()) {
		if isLockAcquired {
			metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, cxEvents.NonStandardTierUser)
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
				cxEvents.CallBlockerEventNoStr, cxEvents.NonStandardTierUser))
		}
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED, NonStandardTierUserErr
	}

	// all the calls which reach this stage will not be connected to the agent, hence sending the call drop off sms here
	if isLockAcquired {
		commsErr := b.sendCommsDropOffCase(ctx, userResp.GetUser().GetId(), monitorUcid, userResp.GetUser().GetProfile().GetKycName().GetFirstName())
		if commsErr != nil {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerCallDropOffCommsEvent(actorId, monitorUcid, cxEvents.CallBlockerEventFailureStr))
			logger.Error(ctx, "error while sending comms to user", zap.Error(commsErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
		} else {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerCallDropOffCommsEvent(actorId, monitorUcid, cxEvents.CallBlockerEventYesStr))
		}
	}
	isRiskBlocked, blockType := riskProfilePb.IsUserBlockedByRiskForCX(riskUserProfileResp)
	// apart from a credit freeze, if there are other risk blocks on a user account,
	// we create a ticket to track the call-back request for that user, and play the prerecorded message
	if isRiskBlocked {
		if isLockAcquired {
			createTicketResp, createTicketErr := b.ticketClient.CreateTicketAsync(ctx, &ticketPb.CreateTicketAsyncRequest{
				Ticket: b.getDropOffTicketDetails(userResp.GetUser(), monitorUcid),
			})
			if rErr := epifigrpc.RPCError(createTicketResp, createTicketErr); rErr != nil {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserRiskBlockCallDropOffEvent(actorId, monitorUcid,
					cxEvents.CallBlockerEventYesStr, cxEvents.CallBlockerEventFailureStr, blockType.String()))
				logger.Error(ctx, "error while creating ticket", zap.Error(rErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
			} else {
				b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserRiskBlockCallDropOffEvent(actorId, monitorUcid,
					cxEvents.CallBlockerEventYesStr, cxEvents.CallBlockerEventYesStr, blockType.String()))
			}
			metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventYesStr, cxEvents.UserInRiskBlock)
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
				cxEvents.CallBlockerEventYesStr, cxEvents.UserInRiskBlock))
		}
		return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_CALLBACK_REQUEST, nil
	}
	if isLockAcquired {
		b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
			cxEvents.CallBlockerEventYesStr, cxEvents.UserInStandardTier))
		metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventYesStr, cxEvents.UserInStandardTier)
		b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerUserRiskBlockCallDropOffEvent(actorId, monitorUcid,
			cxEvents.CallBlockerEventNoStr, cxEvents.CallBlockerEventNoStr, blockType.String()))
	}

	return callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_REDIRECT_TO_ISSUE_REPORTING_FLOW, nil
}

func (b *BlockerImpl) sendCommsForHighRiskUseCase(ctx context.Context, userId, monitorUcid string) error {
	smsMessage := &comms.SMSMessage{
		SmsOption: &comms.SmsOption{
			Option: &comms.SmsOption_CallRecordingPostRiskUseCaseSmsOption{
				CallRecordingPostRiskUseCaseSmsOption: &comms.CallRecordingPostRiskUseCaseSmsOption{
					SmsType: comms.SmsType_CALL_RECORDING_POST_RISK_USE_CASE,
					Option: &comms.CallRecordingPostRiskUseCaseSmsOption_CallRecordingPostRiskUseCaseSmsOptionV1{
						CallRecordingPostRiskUseCaseSmsOptionV1: &comms.CallRecordingPostRiskUseCaseSmsOptionV1{
							TemplateVersion: comms.TemplateVersion_VERSION_V1,
						},
					},
				},
			},
		},
	}
	err := b.sendCommsToUser(ctx, []*comms.Communication{
		{
			Medium: comms.Medium_SMS,
			Message: &comms.Communication_Sms{
				Sms: smsMessage,
			},
		},
	}, userId, monitorUcid)
	if err != nil {
		return errors.Wrap(err, "error in sending risk use case")
	}
	return nil
}

func (b *BlockerImpl) sendCommsDropOffCase(ctx context.Context, userId, monitorUcid, userName string) error {
	smsMessage := &comms.SMSMessage{
		SmsOption: &comms.SmsOption{
			Option: &comms.SmsOption_CxUserCallDroppedSmsOption{
				CxUserCallDroppedSmsOption: &comms.CxUserCallDroppedSmsOption{
					SmsType: comms.SmsType_CX_USER_CALL_DROPPED_SMS,
					Option: &comms.CxUserCallDroppedSmsOption_CxUserCallDroppedSmsOptionV1{
						CxUserCallDroppedSmsOptionV1: &comms.CxUserCallDroppedSmsOptionV1{
							TemplateVersion:   comms.TemplateVersion_VERSION_V1,
							ContactUsFlowLink: b.cxGenConf.CallConfig().CallBlockerConfig().ContactUsFlowSmsLink(),
							AppDownloadLink:   b.cxGenConf.CallConfig().CallBlockerConfig().FiAppDownloadLink(),
						},
					},
				},
			},
		},
	}

	notificationTitle := fmt.Sprintf(b.cxGenConf.CallConfig().CallBlockerConfig().CallDropOffNotificationTitle(), userName)
	// handling a case for a worst-case scenario where username is not available (not expected)
	if userName == "" {
		notificationTitle = "Hey"
	}
	notificationMsg := &fcmPb.Notification{
		NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
		NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
			SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
				CommonTemplateFields: &fcmPb.CommonTemplateFields{
					Title:    notificationTitle,
					Body:     b.cxGenConf.CallConfig().CallBlockerConfig().CallDropOffNotificationBody(),
					Deeplink: cx.GetContactUsDeeplink(),
				},
			},
		},
	}
	err := b.sendCommsToUser(ctx, []*comms.Communication{
		{
			Medium: comms.Medium_SMS,
			Message: &comms.Communication_Sms{
				Sms: smsMessage,
			},
			ExternalReferenceId: monitorUcid,
		},
		{
			Medium: comms.Medium_NOTIFICATION,
			Message: &comms.Communication_Notification{
				Notification: &comms.NotificationMessage{
					Notification: notificationMsg,
				},
			},
			ExternalReferenceId: monitorUcid,
		},
	}, userId, monitorUcid)
	if err != nil {
		return errors.Wrap(err, "error in sending drop off sms")
	}
	return nil
}

func (b *BlockerImpl) sendCommsToUser(ctx context.Context, commsList []*comms.Communication, userId, monitorUcid string) error {
	resp, err := b.commsClient.SendMessageBatch(ctx, &comms.SendMessageBatchRequest{
		Type: comms.QoS_GUARANTEED,
		UserIdentifier: &comms.SendMessageBatchRequest_UserId{
			UserId: userId,
		},
		CommunicationList: commsList,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return errors.Wrap(te, "error while sending comms to user")
	}
	// comms sent
	logger.Info(ctx, "comms sent to user", zap.String("messageIdList", strings.Join(resp.GetMessageIdList(), ",")),
		zap.String(logger.USER_ID, userId), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
	return nil
}

func (b *BlockerImpl) getDropOffTicketDetails(user *userPb.User, ucid string) *ticketPb.Ticket {
	return &ticketPb.Ticket{
		GroupId:     int64(callBackTicketGroupId),
		Priority:    ticketPb.Priority_PRIORITY_MEDIUM,
		Subject:     callBackTicketTitle,
		Description: callBackTicketDescription,
		Status:      ticketPb.Status_STATUS_OPEN,
		Email:       user.GetProfile().GetEmail(),
		CustomFields: &ticketPb.CustomFields{
			EntityId: ucid,
		},
		Tags: []string{callBackTicketTag},
	}
}

func (b *BlockerImpl) acquireLock(ctx context.Context, phoneNumber string) (bool, error) {
	lockKey := fmt.Sprintf("cx_call_blocker_lock:%s", phoneNumber)
	result, err := b.redisClient.SetNX(ctx, lockKey, "1", 5*time.Second).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

func (b *BlockerImpl) isActiveProductUser(ctx context.Context, actorId, monitorUcid string, isLockingRequired bool) (bool, error) {
	productCheckCount := 1
	defaultTimeout := 3 * time.Second
	errChan := make(chan error, productCheckCount)
	var wg waitgroup.SafeWaitGroup

	_ = wg.Add(productCheckCount)

	goroutine.Run(ctx, defaultTimeout, func(gCtx context.Context) {
		defer wg.Done()
		compResp, compErr := b.compClient.GetPeriodicKYCDetail(gCtx, &compliancePb.GetPeriodicKYCDetailRequest{
			ActorId:     actorId,
			FetchLatest: false,
		})
		if rErr := epifigrpc.RPCError(compResp, compErr); rErr != nil {
			errChan <- errors.Wrap(rErr, "error while fetching kyc compliance details")
			return
		}
		if compResp.GetPeriodicKYCDetail().GetKYCComplianceStatus() == compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE {
			errChan <- TriagedUserWithKycDue
			return
		}
		errChan <- nil
	})

	wg.Wait()
	close(errChan)

	processingErr := errors.New("error while processing triage tier")
	failureCnt := 0
	for err := range errChan {
		if err != nil {
			// if we encounter one of the following error users call shouldn't be blocked as they are an active product user
			if errors.Is(err, TriagedUserWithActiveLoanErr) || errors.Is(err, TriagedUserWithActiveCreditCard) || errors.Is(err, TriagedUserWithKycDue) {
				reason := getReasonFromErr(err)
				if isLockingRequired {
					metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, reason)
					b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
						cxEvents.CallBlockerEventNoStr, reason))
				}
				return true, nil
			} else {
				failureCnt++
				processingErr = errors.Wrap(processingErr, err.Error())
			}
		}
	}
	if failureCnt > 0 {
		if isLockingRequired {
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
				cxEvents.CallBlockerEventFailureStr, ""))
		}
		return false, processingErr
	}
	if isLockingRequired {
		metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventYesStr, cxEvents.UserInTriagedTier)
		b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
			cxEvents.CallBlockerEventYesStr, cxEvents.UserInTriagedTier))
	}
	return false, nil
}

func (b *BlockerImpl) isHighPriorityProductUser(ctx context.Context, actorId, monitorUcid string, isLockAcquired bool) bool {
	// todo: make each product related check part of a factory pattern
	productCheckCount := 2
	defaultTimeout := 3 * time.Second
	boolChan := make(chan bool, productCheckCount)
	var wg waitgroup.SafeWaitGroup

	_ = wg.Add(productCheckCount)

	goroutine.Run(ctx, defaultTimeout, func(gCtx context.Context) {
		defer wg.Done()
		isActiveLoanUser, err := b.isActiveLoanHolder(gCtx, actorId, monitorUcid, isLockAcquired)
		if err != nil {
			logger.Error(gCtx, "error while checking loan status", zap.Error(err))
		}
		boolChan <- isActiveLoanUser
	})

	goroutine.Run(ctx, defaultTimeout, func(gCtx context.Context) {
		defer wg.Done()
		isCreditCardUser, err := b.isCreditCardHolder(gCtx, actorId, monitorUcid, isLockAcquired)
		if err != nil {
			logger.Error(gCtx, "error while checking cc status", zap.Error(err))
		}
		boolChan <- isCreditCardUser
	})

	wg.Wait()
	close(boolChan)
	for c := range boolChan {
		// returning true if any of the product checks are true
		if c {
			return true
		}
	}
	return false
}

func (b *BlockerImpl) isCreditCardHolder(ctx context.Context, actorId, monitorUcid string, isLockAcquired bool) (bool, error) {
	ccResp, ccErr := b.ffClient.IsCreditCardUser(ctx, &firefly.IsCreditCardUserRequest{ActorId: actorId})
	if rErr := epifigrpc.RPCError(ccResp, ccErr); rErr != nil {
		return false, errors.Wrap(rErr, "error while fetching credit card status")
	}

	if ccResp.GetIsCreditCardUser() && isLockAcquired {
		metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, cxEvents.UserHasActiveCreditCard)
		b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
			cxEvents.CallBlockerEventNoStr, cxEvents.UserHasActiveCreditCard))
	}
	return ccResp.GetIsCreditCardUser(), nil
}

func (b *BlockerImpl) isActiveLoanHolder(ctx context.Context, actorId, monitorUcid string, isLockAcquired bool) (bool, error) {
	loanStatusResp, loanStatusErr := b.preApprovedLoanClient.GetLoansUserStatus(ctx,
		&preapprovedloan.GetLoansUserStatusRequest{ActorId: actorId})
	if rErr := epifigrpc.RPCError(loanStatusResp, loanStatusErr); rErr != nil {
		return false, errors.Wrap(rErr, "error while fetching loan status")
	}

	if loanStatusResp.GetLoansUserStatus() == preapprovedloan.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT {
		if isLockAcquired {
			metrics.RecordCallBlockerTerminalStatus(cxEvents.CallBlockerEventNoStr, cxEvents.UserHasActiveLoan)
			b.eventBroker.AddToBatch(ctx, cxEvents.NewCallBlockerTerminalEvent(actorId, monitorUcid,
				cxEvents.CallBlockerEventNoStr, cxEvents.UserHasActiveLoan))
		}
		return true, nil
	}
	return false, nil
}

func getReasonFromErr(err error) string {
	switch {
	case errors.Is(err, ActiveLoanUserErr):
		return cxEvents.UserHasActiveLoan
	case errors.Is(err, ActiveCreditCardUserErr):
		return cxEvents.UserHasActiveCreditCard
	case errors.Is(err, TriagedUserWithActiveCreditCard):
		return cxEvents.UserInTriagedTierWithActiveCreditCard
	case errors.Is(err, TriagedUserWithActiveLoanErr):
		return cxEvents.UserInTriagedTierWithActiveLoan
	case errors.Is(err, TriagedUserWithKycDue):
		return cxEvents.UserInTriagedTierWithKycDue
	default:
		return cxEvents.CallBlockerEventFailureStr
	}
}
