package dao

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer(true)
	IncidentDTS = IncidentDaoTestSuite{
		conf:        conf,
		db:          db,
		IncidentDao: NewIncidentDao(db),
	}
	incidentTicketDetailDTS = IncidentTicketDetailDaoTestSuite{
		conf:                    conf,
		db:                      db,
		IncidentTicketDetailDao: NewIncidentTicketDetailDao(db),
	}
	IncidentCommsDetailDTS = IncidentCommsDetailDaoTestSuite{
		conf: conf, db: db,
		IncidentCommsDetailDao: NewIncidentCommsDetailDao(db),
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
