package risk_ops

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"

	riskOpsPb "github.com/epifi/gamma/api/cx/risk_ops"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

// GetTransactionBlocks retrieves transaction blocks for a given actor or alert ID
// This is a frontend-facing RPC that calls the case_management service's GetTransactionBlocks method
func (s *Service) GetTransactionBlocks(ctx context.Context, req *riskOpsPb.GetTransactionBlocksRequest) (*riskOpsPb.GetTransactionBlocksResponse, error) {
	// Determine identifier type (actor_id or alert_id)
	var cmRequest *caseManagementPb.GetTransactionBlocksRequest
	cxLogger.Debug(ctx, "GetTransactionBlocks Started", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("Alert", req.GetAlertId()))

	// Based on which identifier is provided, create the appropriate case_management request
	switch {
	case req.GetActorId() != "":
		cmRequest = &caseManagementPb.GetTransactionBlocksRequest{
			Identifier: &caseManagementPb.GetTransactionBlocksRequest_ActorId{
				ActorId: req.GetActorId(),
			},
		}
	case req.GetAlertId() != "":
		cmRequest = &caseManagementPb.GetTransactionBlocksRequest{
			Identifier: &caseManagementPb.GetTransactionBlocksRequest_AlertId{
				AlertId: req.GetAlertId(),
			},
		}
	default:
		cxLogger.Error(ctx, "no identifier provided for GetTransactionBlocks request")
		return &riskOpsPb.GetTransactionBlocksResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("either actor_id or alert_id must be provided"),
		}, nil
	}

	// Set the block type filter if provided
	if req.GetBlockType() != riskOpsPb.TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED {
		cmRequest.BlockType = caseManagementPb.TransactionBlockType(int32(req.GetBlockType()))
	}

	// Set the limit
	cmRequest.Limit = req.GetLimit()

	// Call the case_management service
	cmResp, err := s.caseManagementClient.GetTransactionBlocks(ctx, cmRequest)
	if err = epifigrpc.RPCError(cmResp, err); err != nil {
		if cmResp != nil && cmResp.GetStatus().IsRecordNotFound() {
			return &riskOpsPb.GetTransactionBlocksResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error in GetTransactionBlocks from case_management", zap.Error(err))
		return &riskOpsPb.GetTransactionBlocksResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// Convert transaction blocks from case_management format to risk_ops format
	transactionBlocks := make([]*riskOpsPb.TransactionBlock, 0, len(cmResp.GetTransactionBlocks()))
	for _, block := range cmResp.GetTransactionBlocks() {
		transactionBlocks = append(transactionBlocks, convertCaseManagementBlockToRiskOpsBlock(block))
	}

	return &riskOpsPb.GetTransactionBlocksResponse{
		Status:            rpcPb.StatusOk(),
		TransactionBlocks: transactionBlocks,
	}, nil
}

// convertCaseManagementBlockToRiskOpsBlock converts a transaction block from case_management format to risk_ops format
func convertCaseManagementBlockToRiskOpsBlock(cmBlock *caseManagementPb.TransactionBlock) *riskOpsPb.TransactionBlock {
	if cmBlock == nil {
		return nil
	}

	return &riskOpsPb.TransactionBlock{
		Id:               cmBlock.GetId(),
		ActorId:          cmBlock.GetActorId(),
		AlertId:          cmBlock.GetAlertId(),
		BlockType:        riskOpsPb.TransactionBlockType(int32(cmBlock.GetBlockType())),
		AggregatedCredit: cmBlock.AggregatedCredit,
		AggregatedDebit:  cmBlock.AggregatedDebit,
		TransactionIds:   cmBlock.TransactionIds,
		Duration:         cmBlock.Duration,
		CreatedAt:        cmBlock.GetCreatedAt(),
	}
}
