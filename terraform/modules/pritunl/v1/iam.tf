resource "aws_iam_role" "pritunl-role" {
  name = "pritunl_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Sid    = ""
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      },
    ]
  })
}

resource "aws_iam_role_policy_attachment" "pritunl-role-attach" {
  role       = aws_iam_role.pritunl-role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "ssm-s3-logs" {
  count      = var.env == "prod" ? 1 : 0
  role       = aws_iam_role.pritunl-role.name
  policy_arn = "arn:aws:iam::854002675954:policy/ssm_s3_logging_policy"
}

resource "aws_iam_instance_profile" "pritunl_profile" {
  name = "pritunl_profile"
  role = aws_iam_role.pritunl-role.name
}
