package order

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	usStocksPb "github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/catalog"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	usstocksOrderMgPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/usstocks/order/dao"
)

func isBuyOrSellOrder(activityType orderPb.AccountActivityType) bool {
	return activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_SELL || activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY ||
		activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_REWARD || activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_FOR_SIP
}

func isWalletAddOrWithdrawFundsOrder(activityType orderPb.AccountActivityType) bool {
	return activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_ADD_FUNDS ||
		activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_WITHDRAW_FUNDS ||
		activityType == orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_ADD_FUNDS_FOR_SIP
}

func (s *Service) GetAccountActivities(ctx context.Context, req *usstocksOrderMgPb.GetAccountActivitiesRequest) (*usstocksOrderMgPb.GetAccountActivitiesResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "Unable to fetch pageToken from request", zap.Error(err))
		return &orderPb.GetAccountActivitiesResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	ordersMap := make(map[string]*usstocksOrderMgPb.Order, 0)
	walletOrdersMap := make(map[string]*usstocksOrderMgPb.WalletOrder, 0)
	symbolToStockMap := make(map[string]*catalog.Stock, 0)

	// notes: dao return only field which are required to render for ui
	// if we need more field to be populated corresponding value need to updated in dao
	filterOptions := []storagev2.FilterOption{}

	if len(req.GetSymbols()) > 0 {
		filterOptions = append(filterOptions, dao.WithSymbols(req.GetSymbols()))
	}

	if len(req.GetActivityType()) > 0 {
		filterOptions = append(filterOptions, dao.WithAccountActivitiesActivityType(req.GetActivityType()))
	}

	if req.GetCreatedAfter().IsValid() {
		filterOptions = append(filterOptions, dao.WithCreatedAtAfter(req.GetCreatedAfter().AsTime()))
	}

	if req.GetCreatedBefore().IsValid() {
		filterOptions = append(filterOptions, dao.WithCreatedAtBefore(req.GetCreatedBefore().AsTime()))
	}

	if len(req.GetOrderStates()) != 0 {
		orderStates := req.GetOrderStates()
		if lo.Contains(req.GetActivityType(), orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND) {
			// Since the activity will have its order state set to 'unspecified'
			orderStates = append(orderStates, usStocksPb.OrderState_ORDER_STATE_UNSPECIFIED)
		}
		filterOptions = append(filterOptions, dao.WithAccountActivitiesOrderStateIn(orderStates))
	}

	accountActivities, pageContext, err := s.accountActivityDao.GetAccountActivities(ctx, req.GetActorId(), pageToken, req.GetPageContext().GetPageSize(), filterOptions...)
	if err != nil {
		logger.Error(ctx, "error while getting account activities", zap.Error(err))
		return &usstocksOrderMgPb.GetAccountActivitiesResponse{Status: rpc.StatusInternal()}, nil
	}

	// populate map if mask exist
	if lo.Contains(req.GetFieldMask(), usstocksOrderMgPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_ORDER_MAP) {
		var err2 error
		ordersMap, err2 = s.getOrderMappingForActivity(ctx, accountActivities)
		if err2 != nil {
			logger.Error(ctx, "error while getting order map", zap.Error(err2))
			return &usstocksOrderMgPb.GetAccountActivitiesResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	// populate symbol stock mapping
	if lo.Contains(req.GetFieldMask(), usstocksOrderMgPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_STOCKS_MAP) {
		var err2 error
		symbolToStockMap, err2 = s.getStocksMappingForActivity(ctx, accountActivities)
		if err2 != nil {
			logger.Error(ctx, "error while getting stocks map", zap.Error(err2))
			return &usstocksOrderMgPb.GetAccountActivitiesResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	// populate map if mask exist
	if lo.Contains(req.GetFieldMask(), usstocksOrderMgPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_WALLET_ORDER_MAP) {
		var err2 error
		walletOrdersMap, err2 = s.getWalletOrderMappingForActivity(ctx, accountActivities)
		if err2 != nil {
			logger.Error(ctx, "error while getting order map", zap.Error(err2))
			return &usstocksOrderMgPb.GetAccountActivitiesResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	return &usstocksOrderMgPb.GetAccountActivitiesResponse{
		Status:                      rpc.StatusOk(),
		AccountActivities:           accountActivities,
		PageContext:                 pageContext,
		SymbolToStock:               symbolToStockMap,
		OrderMappedToActivity:       ordersMap,
		WalletOrderMappedToActivity: walletOrdersMap,
	}, nil
}

// nolint:dupl
func (s *Service) getOrderMappingForActivity(ctx context.Context, accountActivities []*orderPb.AccountActivity) (map[string]*usstocksOrderMgPb.Order, error) {
	// get list of orders
	var err error
	ordersMap := make(map[string]*usstocksOrderMgPb.Order, 0)
	orderIds := make([]string, 0)
	for _, activity := range accountActivities {
		if isBuyOrSellOrder(activity.GetType()) {
			orderIds = append(orderIds, activity.GetOrderId())
		}
	}
	orders := make([]*orderPb.Order, 0)
	if len(orderIds) > 0 {
		// get list of order for given order list
		orders, err = s.orderDao.GetByIds(ctx, orderIds)
		if err != nil {
			logger.Error(ctx, "error while getting orders", zap.Error(err))
			return nil, err
		}
	}
	for _, order := range orders {
		ordersMap[order.GetId()] = order
	}
	for _, activity := range accountActivities {
		// check if we have order data for sell/Buy activity
		if isBuyOrSellOrder(activity.GetType()) && ordersMap[activity.GetOrderId()] == nil {
			logger.Error(ctx, "error while getting order details for activity", zap.String(logger.ORDER_ID, activity.GetOrderId()))
			return nil, fmt.Errorf("error while getting order details for activity")
		}
	}
	return ordersMap, nil
}

// nolint:dupl
func (s *Service) getWalletOrderMappingForActivity(ctx context.Context, accountActivities []*orderPb.AccountActivity) (map[string]*usstocksOrderMgPb.WalletOrder, error) {
	// get list of orders
	var err error
	ordersMap := make(map[string]*usstocksOrderMgPb.WalletOrder, 0)
	orderIds := make([]string, 0)
	for _, activity := range accountActivities {
		if isWalletAddOrWithdrawFundsOrder(activity.GetType()) {
			orderIds = append(orderIds, activity.GetOrderId())
		}
	}
	orders := make([]*orderPb.WalletOrder, 0)
	if len(orderIds) > 0 {
		// get list of order for given order list
		orders, err = s.walletOrderDao.GetByIds(ctx, orderIds)
		if err != nil {
			logger.Error(ctx, "error while getting wallet orders", zap.Error(err))
			return nil, err
		}
	}
	for _, order := range orders {
		ordersMap[order.GetId()] = order
	}
	for _, activity := range accountActivities {
		// check if we have order data for add/withdraw funds activity
		if isWalletAddOrWithdrawFundsOrder(activity.GetType()) && ordersMap[activity.GetOrderId()] == nil {
			logger.Error(ctx, "data not found for wallet order activity", zap.String(logger.ORDER_ID, activity.GetOrderId()))
			return nil, fmt.Errorf("data not found for at least one wallet order activity")
		}
	}
	return ordersMap, nil
}

func (s *Service) getStocksMappingForActivity(ctx context.Context, accountActivities []*orderPb.AccountActivity) (map[string]*catalog.Stock, error) {
	// get possible stock and order exist for activity
	symbolToStockMap := make(map[string]*catalog.Stock, 0)
	symbolToExchangeMap := make(map[catalog.Exchange][]string, 0)
	for _, activity := range accountActivities {
		if activity.GetSymbol() != "" {
			// map symbol to exchange
			symbolToStockMap[activity.GetSymbol()] = nil
		}
	}

	// group according to exchange
	for symbol := range symbolToStockMap {
		exchange, ok := s.conf.SymbolToExchangeMap()[symbol]
		if !ok {
			logger.Error(ctx, "error while getting exchange for symbol", zap.String(logger.SYMBOL_ID, symbol))
			return nil, fmt.Errorf("error finding exchange")
		}
		if symbolToExchangeMap[exchange] == nil {
			symbolToExchangeMap[exchange] = make([]string, 0)
		}
		symbolToExchangeMap[exchange] = append(symbolToExchangeMap[exchange], symbol)
	}

	// map and fill stock data according to exhange
	// it is bulk rpc
	// since there are limited exchange no need of parallelize call
	for exchange, symbols := range symbolToExchangeMap {
		// get stock for given exchange and symbols
		stocks, err := s.stocksDao.GetByListOfSymbolInExchange(ctx, symbols, exchange, nil)
		if err != nil {
			logger.Error(ctx, "error while getting stock details for activity", zap.Strings(logger.SYMBOL_IDS, symbols))
			return nil, err
		}
		for _, stock := range stocks {
			symbolToStockMap[stock.GetSymbol()] = stock
		}
	}

	// validate if we dont have any stock mapped
	for symbol, stock := range symbolToStockMap {
		if stock == nil {
			logger.Error(ctx, "unable to get stock details for symbol", zap.String(logger.SYMBOL_ID, symbol))
			return nil, fmt.Errorf("unable to get stock details for symbol")
		}
	}
	return symbolToStockMap, nil
}
