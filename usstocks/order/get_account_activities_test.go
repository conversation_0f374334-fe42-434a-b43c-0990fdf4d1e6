package order_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	usStocksPb "github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/catalog"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

func TestService_GetAccountActivities(t *testing.T) {
	orderSvc, md := getServiceWithMocks(t)
	mockOrderDao := md.orderDao
	stocksDao := md.stocksDao
	mockAccountActivityDao := md.accountActivityDao

	// both belong to same exchange
	symbol1 := "MSFT"
	symbol2 := "AAPL"

	type args struct {
		ctx context.Context
		req *orderPb.GetAccountActivitiesRequest
	}

	actorId := "actor-id"
	executedAt := timestampPb.Now()
	createdAt := timestampPb.Now()
	sampleActivity1 := &orderPb.AccountActivity{
		Id:              "id-3",
		VendorAccountId: "vendor-account-id",
		Type:            orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND_SHORT_TERM_CAPITAL_GAIN,
		ActorId:         actorId,
		Symbol:          symbol1,
		NetAmount: &money.Money{
			CurrencyCode: moneyPb.USDCurrencyCode,
			Units:        51,
		},
		ExecutedAt:       executedAt,
		CreatedAt:        createdAt,
		UpdatedAt:        createdAt,
		VendorActivityId: "activity-id-3",
		OrderState:       usStocksPb.OrderState_ORDER_STATE_UNSPECIFIED,
		Vendor:           commonvgpb.Vendor_ALPACA,
	}

	sampleActivity2 := &orderPb.AccountActivity{
		Id:              "id-1",
		VendorAccountId: "vendor-account-id",
		Type:            orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND,
		ActorId:         actorId,
		Symbol:          symbol1,
		NetAmount: &money.Money{
			CurrencyCode: moneyPb.USDCurrencyCode,
			Units:        50,
		},
		ExecutedAt:       executedAt,
		CreatedAt:        createdAt,
		UpdatedAt:        createdAt,
		VendorActivityId: "activity-id-1",
		OrderState:       usStocksPb.OrderState_ORDER_STATE_UNSPECIFIED,
		Vendor:           commonvgpb.Vendor_ALPACA,
	}

	sampleActivity3 := &orderPb.AccountActivity{
		Id:              "id-2",
		VendorAccountId: "vendor-account-id",
		Type:            orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND_LONG_TERM_CAPITAL_GAIN,
		ActorId:         actorId,
		Symbol:          symbol2,
		NetAmount: &money.Money{
			CurrencyCode: moneyPb.USDCurrencyCode,
			Units:        90,
		},
		ExecutedAt:       executedAt,
		CreatedAt:        createdAt,
		UpdatedAt:        createdAt,
		VendorActivityId: "activity-id-2",
		OrderState:       usStocksPb.OrderState_ORDER_STATE_UNSPECIFIED,
		Vendor:           commonvgpb.Vendor_ALPACA,
	}

	sampleOrderId := "usttocks-order-id"

	sampleOrderActivity := &orderPb.AccountActivity{
		Id:              "id-3",
		VendorAccountId: "vendor-account-id",
		Type:            orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY,
		ActorId:         actorId,
		Symbol:          symbol2,
		NetAmount: &money.Money{
			CurrencyCode: moneyPb.USDCurrencyCode,
			Units:        90,
		},
		ExecutedAt:       executedAt,
		CreatedAt:        createdAt,
		UpdatedAt:        createdAt,
		VendorActivityId: "activity-id-2",
		OrderState:       usStocksPb.OrderState_ORDER_SUCCESS,
		OrderId:          sampleOrderId,
		Vendor:           commonvgpb.Vendor_ALPACA,
	}

	sampleOrder := &orderPb.Order{
		Id:            sampleOrderId,
		VendorOrderId: "vendor-order-id",
		ActorId:       actorId,
		Symbol:        symbol1,
		Side:          usStocksPb.OrderSide_BUY,
		State:         usStocksPb.OrderState_ORDER_SUCCESS,
	}

	sampleActivitiesWithOrder := []*orderPb.AccountActivity{
		sampleActivity1,
		sampleActivity2,
		sampleActivity3,
		sampleOrderActivity,
	}

	sampleActivities := []*orderPb.AccountActivity{
		sampleActivity1,
		sampleActivity2,
		sampleActivity3,
	}

	sampleStock1 := &catalogPb.Stock{
		Id:     "catalog-id-1",
		Symbol: symbol1,
	}

	sampleStock2 := &catalogPb.Stock{
		Id:     "catalog-id-2",
		Symbol: symbol2,
	}

	sampleSymbolToStocks := make(map[string]*catalogPb.Stock, 0)
	sampleSymbolToStocks[symbol1] = sampleStock1
	sampleSymbolToStocks[symbol2] = sampleStock2

	sampleOrderMappedToActivity := make(map[string]*orderPb.Order, 0)
	sampleOrderMappedToActivity[sampleOrderId] = sampleOrder

	sampleReq := &orderPb.GetAccountActivitiesRequest{
		ActorId:   actorId,
		FieldMask: []orderPb.GetAccountActivitiesRequest_FieldMask{orderPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_ORDER_MAP, orderPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_STOCKS_MAP},
	}
	sampleReqOnlyStocksMap := &orderPb.GetAccountActivitiesRequest{
		ActorId:   actorId,
		FieldMask: []orderPb.GetAccountActivitiesRequest_FieldMask{orderPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_STOCKS_MAP},
	}
	sampleReqOnlyOrderMap := &orderPb.GetAccountActivitiesRequest{
		ActorId:   actorId,
		FieldMask: []orderPb.GetAccountActivitiesRequest_FieldMask{orderPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_ORDER_MAP},
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *orderPb.GetAccountActivitiesResponse
		wantErr        bool
	}{
		{
			name: "getting account activities with orders and stocks",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				stocksDao.EXPECT().GetByListOfSymbolInExchange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, symbols []string, exchange catalog.Exchange, fieldmasks []catalog.StockFieldMask) ([]*catalogPb.Stock, error) {
						if len(symbols) != 2 {
							return nil, fmt.Errorf("invalid paramer")
						}
						symbolMapping := make(map[string]bool)
						for _, symbol := range symbols {
							symbolMapping[symbol] = true
						}
						for _, symbol := range []string{symbol1, symbol2} {
							if symbolMapping[symbol] == false {
								return nil, fmt.Errorf("invalid argument")
							}
						}
						return []*catalog.Stock{sampleStock2, sampleStock1}, nil
					})

				mockAccountActivityDao.EXPECT().GetAccountActivities(gomock.Any(), sampleReq.GetActorId(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					sampleActivitiesWithOrder,
					nil, nil,
				)
				mockOrderDao.EXPECT().GetByIds(gomock.Any(), []string{sampleOrderId}).Return([]*orderPb.Order{sampleOrder}, nil)
			},
			want: &orderPb.GetAccountActivitiesResponse{
				Status:                rpc.StatusOk(),
				AccountActivities:     sampleActivitiesWithOrder,
				SymbolToStock:         sampleSymbolToStocks,
				OrderMappedToActivity: sampleOrderMappedToActivity,
			},
		},
		{
			name: "getting account activities only",
			args: args{
				ctx: context.Background(),
				req: &orderPb.GetAccountActivitiesRequest{
					ActorId: actorId,
				},
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().GetAccountActivities(gomock.Any(), sampleReq.GetActorId(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					sampleActivities,
					nil, nil,
				)
			},
			want: &orderPb.GetAccountActivitiesResponse{
				Status:            rpc.StatusOk(),
				AccountActivities: sampleActivities,
			},
		},
		{
			name: "getting account activities and stocks",
			args: args{
				ctx: context.Background(),
				req: sampleReqOnlyStocksMap,
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().GetAccountActivities(gomock.Any(), sampleReq.GetActorId(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					sampleActivitiesWithOrder,
					nil, nil,
				)
				stocksDao.EXPECT().GetByListOfSymbolInExchange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, symbols []string, exchange catalog.Exchange, fieldmasks []catalog.StockFieldMask) ([]*catalogPb.Stock, error) {
						if len(symbols) != 2 {
							return nil, fmt.Errorf("invalid paramer")
						}
						symbolMapping := make(map[string]bool)
						for _, symbol := range symbols {
							symbolMapping[symbol] = true
						}
						for _, symbol := range []string{symbol1, symbol2} {
							if symbolMapping[symbol] == false {
								return nil, fmt.Errorf("invalid argument")
							}
						}
						return []*catalog.Stock{sampleStock2, sampleStock1}, nil
					})
			},
			want: &orderPb.GetAccountActivitiesResponse{
				Status:            rpc.StatusOk(),
				AccountActivities: sampleActivitiesWithOrder,
				SymbolToStock:     sampleSymbolToStocks,
			},
		},
		{
			name: "while getting activity and order",
			args: args{
				ctx: context.Background(),
				req: sampleReqOnlyOrderMap,
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().GetAccountActivities(gomock.Any(), sampleReq.GetActorId(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					sampleActivitiesWithOrder,
					nil,
					nil,
				)
				mockOrderDao.EXPECT().GetByIds(gomock.Any(), []string{sampleOrderId}).Return([]*orderPb.Order{sampleOrder}, nil)
			},
			want: &orderPb.GetAccountActivitiesResponse{
				Status:                rpc.StatusOk(),
				AccountActivities:     sampleActivitiesWithOrder,
				SymbolToStock:         nil,
				OrderMappedToActivity: sampleOrderMappedToActivity,
			},
		},
		{
			name: "error while get account activities dao call",
			args: args{
				ctx: context.Background(),
				req: &orderPb.GetAccountActivitiesRequest{
					ActorId: actorId,
				},
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().GetAccountActivities(gomock.Any(), sampleReq.GetActorId(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					sampleActivities,
					nil, epifierrors.ErrRecordNotFound,
				)
			},
			want: &orderPb.GetAccountActivitiesResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := orderSvc.GetAccountActivities(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountActivities() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(orderPb.Order{}, "CreatedAt", "UpdatedAt"),
				cmpopts.IgnoreFields(orderPb.AccountActivity{}, "CreatedAt", "UpdatedAt"),
				cmpopts.IgnoreFields(catalog.Stock{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetAccountActivities() got = %v,\n want %v, diff= %v", got, tt.want, diff)
			}
		})
	}

}
