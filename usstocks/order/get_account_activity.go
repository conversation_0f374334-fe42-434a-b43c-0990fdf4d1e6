package order

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/usstocks/catalog"
	usstocksOrderMgPb "github.com/epifi/gamma/api/usstocks/order"
)

// This RPC is only used for trade order activity
func (s *Service) GetAccountActivity(ctx context.Context, req *usstocksOrderMgPb.GetAccountActivityRequest) (*usstocksOrderMgPb.GetAccountActivityResponse, error) {
	var (
		activity *usstocksOrderMgPb.AccountActivity
		err      error
	)
	if req.GetIdentifier() == nil && req.GetActivityId() == "" {
		return &usstocksOrderMgPb.GetAccountActivityResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
	if req.GetIdentifier() == nil && req.GetActivityId() != "" {
		activity, err = s.accountActivityDao.GetById(ctx, req.GetActivityId())
	}
	switch req.GetIdentifier().(type) {
	case *usstocksOrderMgPb.GetAccountActivityRequest_AccountActivityId:
		activity, err = s.accountActivityDao.GetById(ctx, req.GetAccountActivityId())
	case *usstocksOrderMgPb.GetAccountActivityRequest_OrderId:
		activity, err = s.accountActivityDao.GetByOrderId(ctx, req.GetOrderId())
	}

	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &usstocksOrderMgPb.GetAccountActivityResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error while getting activity",
			zap.Error(err),
			zap.String("Activity Id: ", req.GetActivityId()),
			zap.String("Account Activity Id: ", req.GetAccountActivityId()),
			zap.String("Stock Order Id: ", req.GetOrderId()),
		)
		return &usstocksOrderMgPb.GetAccountActivityResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// get exchange for corresponding symbol
	exchange, ok := s.conf.SymbolToExchangeMap()[activity.GetSymbol()]
	if !ok {
		logger.Error(ctx, "error mapping exchange to symbol", zap.String(logger.SYMBOL_ID, activity.GetSymbol()))
		return &usstocksOrderMgPb.GetAccountActivityResponse{Status: rpc.StatusInternalWithDebugMsg("error mapping exchange to symbol")}, nil
	}

	// get stock data for corresponding
	stock, err := s.stocksDao.GetBySymbolAndExchangeId(ctx, activity.GetSymbol(), exchange, []catalog.StockFieldMask{catalog.StockFieldMask_STOCK_COMPANY_INFO, catalog.StockFieldMask_STOCK_STOCK_BASIC_DETAILS})
	if err != nil {
		logger.Error(ctx, "error while getting stock", zap.Error(err))
		return &usstocksOrderMgPb.GetAccountActivityResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &usstocksOrderMgPb.GetAccountActivityResponse{
		Status:   rpc.StatusOk(),
		Activity: activity,
		Stock:    stock,
	}, nil
}
