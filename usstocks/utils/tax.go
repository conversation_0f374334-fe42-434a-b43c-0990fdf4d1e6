package utils

import (
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/api/usstocks/tax"
)

func GetSellType(buyTs *timestamppb.Timestamp, sellTs *timestamppb.Timestamp, ltcgDurationInYears int) (tax.SellType, error) {
	if buyTs == nil {
		return 0, fmt.Errorf("buyTs cannot be nil")
	}
	if sellTs == nil {
		return 0, fmt.Errorf("sellTs cannot be nil")
	}
	buyTime := buyTs.AsTime().In(datetime.IST)
	sellTime := sellTs.AsTime().In(datetime.IST)
	if buyTime.After(sellTime) {
		return 0, fmt.Errorf("buyTime cannot be after sellTime")
	}
	if datetime.AddNMonths(&buyTime, 12*ltcgDurationInYears).After(sellTime) {
		return tax.SellType_SELL_TYPE_SHORT_TERM, nil
	} else {
		return tax.SellType_SELL_TYPE_LONG_TERM, nil
	}
}

// GetLtcgDurationForStockType returns duration in years for a txn to be eligible for LTCG
// e.g. us stocks has 2 yrs, us etf has 2 years according to new rule as of 1st April 2025
func GetLtcgDurationForStockType(stockType catalog.StockType) (int, error) {
	switch stockType {
	case catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY:
		return 2, nil
	case catalog.StockType_STOCK_TYPE_ETF:
		return 2, nil
	default:
		return 0, fmt.Errorf("unhandled stock type %T", stockType)
	}
}

// GetFYForTime returns start year of FY the time belongs to
func GetFYForTime(ts *timestamppb.Timestamp) int {
	t := ts.AsTime().In(datetime.IST)
	if t.In(datetime.IST).Month() >= 4 {
		return t.Year()
	}
	return t.Year() - 1
}

func GetSecurityType(stockType catalog.StockType) (tax.SecurityType, error) {
	switch stockType {
	case catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY:
		return tax.SecurityType_SECURITY_TYPE_EQUITY, nil
	case catalog.StockType_STOCK_TYPE_ETF:
		return tax.SecurityType_SECURITY_TYPE_ETF, nil
	default:
		return 0, fmt.Errorf("unhandled stock type : %s", stockType.String())
	}
}

func GetTimeRangeString(rangeType tax.TimeRangeType, computedTill time.Time) (string, error) {
	switch rangeType {
	case tax.TimeRangeType_TIME_RANGE_TYPE_FINANCIAL_YEAR:
		currentYear := computedTill.In(datetime.IST).Year()
		prevYear := currentYear - 1
		return fmt.Sprintf("FY_%d_%d", prevYear, currentYear), nil
	default:
		return "", fmt.Errorf("unhandled time range type %v", rangeType)
	}
}

var DocumentTypeToDocNameParams = map[tax.UsStockDocumentType]*DocNameParams{
	tax.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_CAPITAL_GAIN: {DisplayName: "Capital_Gain", S3PathPrefix: "capital_gains", FileExtension: "xlsx", EmailAttachmentType: "application/vnd.ms-excel"},
	tax.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_FORM_67:      {DisplayName: "Form_67", S3PathPrefix: "form_67", FileExtension: "xlsx", EmailAttachmentType: "application/vnd.ms-excel"},
	tax.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_SCHEDULE_FA:  {DisplayName: "Schedule_FA", S3PathPrefix: "schedule_fa", FileExtension: "xlsx", EmailAttachmentType: "application/vnd.ms-excel"},
	tax.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_SCHEDULE_FSI: {DisplayName: "Schedule_FSI", S3PathPrefix: "schedule_fsi", FileExtension: "xlsx", EmailAttachmentType: "application/vnd.ms-excel"},
	tax.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_SCHEDULE_TR:  {DisplayName: "Schedule_TR", S3PathPrefix: "schedule_tr", FileExtension: "xlsx", EmailAttachmentType: "application/vnd.ms-excel"},
	tax.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_FORM_1042_S:  {DisplayName: "Form_1042S", S3PathPrefix: "form_1042_s", FileExtension: "pdf", EmailAttachmentType: "application/pdf"},
}

type DocNameParams struct {
	DisplayName         string
	S3PathPrefix        string
	FileExtension       string
	EmailAttachmentType string
}
