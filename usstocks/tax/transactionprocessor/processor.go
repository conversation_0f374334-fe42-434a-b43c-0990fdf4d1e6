//go:generate mockgen -source=processor.go -destination=./mocks/mock_processor.go package=mocks
package transactionprocessor

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	"github.com/google/wire"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/utils"
)

var ITransactionProcessorWireSet = wire.NewSet(NewTransactionProcessor, wire.Bind(new(ITransactionProcessor), new(*TransactionProcessor)))

const (
	StockModelSymbolFieldName            = "symbol"
	StockModelStockTypeFieldName         = "stock_type"
	StockModelStockBasicDetailsFieldName = "stock_basic_details"
)

type DocumentParamsInfo struct {
	BuySellPairs *BuySellPairs
	DividendInfo *DividendInfo
}

type ITransactionProcessor interface {
	GetBuySellPairsMap(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (map[string][]*BuySellPair, error)
	GetDividendInfo(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (*DividendInfo, error)
	GetNetLtcgAndStcg(ctx context.Context, pairs []*BuySellPair) (*LtcgStcgInfo, error)
	GetBuySellPairForScheduleFA(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (map[string][]*BuySellPair, error)
}

type TransactionProcessor struct {
	gconf                   *genconf.Config
	ciiProvider             utils.CostInflationIndexProvider
	sbiExchangeRateProvider utils.SBIExchangeRateProvider
	ussCatalogMgrClient     catalog.CatalogManagerClient
}

func NewTransactionProcessor(gconf *genconf.Config, ciiProvider utils.CostInflationIndexProvider,
	sbiExchangeRateProvider utils.SBIExchangeRateProvider, ussCatalogMgrClient catalog.CatalogManagerClient) *TransactionProcessor {
	return &TransactionProcessor{
		gconf:                   gconf,
		ciiProvider:             ciiProvider,
		sbiExchangeRateProvider: sbiExchangeRateProvider,
		ussCatalogMgrClient:     ussCatalogMgrClient,
	}
}

// GetBuySellPairsMap returns a map of stockSymbol to all its buy-sell pairs
// nolint:dupl
func (p *TransactionProcessor) GetBuySellPairsMap(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (map[string][]*BuySellPair, error) {
	// creating copy of transactions as this method will be updating the values in txns, and
	// we don't want this values to be updated out of scope of GetBuySellPairsMap method
	deepCopyTxns := p.createDeepCopyOfTxns(txns)
	filteredTxns, symbolToStockDetailsMap, stockDetailsFetchErr := p.filterTransactionsAndGetStockDetails(ctx, deepCopyTxns)
	if stockDetailsFetchErr != nil {
		return nil, fmt.Errorf("failed to filter transactions and get stock symbol details : %w", stockDetailsFetchErr)
	}

	buySellPairsMap := make(map[string][]*BuySellPair)
	buyTxnsMap := make(map[string][]*tax.TransactionWrapper)

	for _, txn := range filteredTxns {
		symbol := txn.GetSymbol()
		switch txn.GetTransactionType() {
		case tax.TransactionType_TRANSACTION_TYPE_BUY:
			if txn.GetQuantity().IsZero() {
				return nil, fmt.Errorf("buy transaction cannot have 0 units for symbol %s, executed at: %v", symbol, txn.GetExecutedAt().AsTime())
			}
			buyTxnsMap[symbol] = append(buyTxnsMap[symbol], txn)
		case tax.TransactionType_TRANSACTION_TYPE_SELL:
			if txn.GetQuantity().IsZero() {
				return nil, fmt.Errorf("sell transaction cannot have 0 units for symbol %s, executed at: %v", symbol, txn.GetExecutedAt().AsTime())
			}
			if _, found := buyTxnsMap[symbol]; !found {
				return nil, fmt.Errorf("no buy found in buyTxnMap for Symbol : %s", symbol)
			}
			buySellPairs, err := p.getBuySellPairs(buyTxnsMap, txn, symbolToStockDetailsMap, fromTime, toTime)
			if err != nil {
				return nil, fmt.Errorf("failed to get buy-sell pairs : %w", err)
			}
			buySellPairsMap[symbol] = append(buySellPairsMap[symbol], buySellPairs...)
		}
	}
	return buySellPairsMap, nil
}

func (p *TransactionProcessor) filterTransactionsAndGetStockDetails(ctx context.Context, txns []*tax.TransactionWrapper) ([]*tax.TransactionWrapper, map[string]*catalog.Stock, error) {
	// filtering out all txns other than buy and sell
	txns = lo.Filter(txns, func(txn *tax.TransactionWrapper, index int) bool {
		return txn.GetTransactionType() == tax.TransactionType_TRANSACTION_TYPE_BUY || txn.GetTransactionType() == tax.TransactionType_TRANSACTION_TYPE_SELL
	})
	// sort all transactions primarily on executed at and incase of a tie, the buy txn comes before sell
	sort.Slice(txns, func(i, j int) bool {
		cmp := txns[i].GetExecutedAt().AsTime().Compare(txns[j].GetExecutedAt().AsTime())
		if cmp == 0 { // both are executed at same time
			return txns[i].GetTransactionType() == tax.TransactionType_TRANSACTION_TYPE_BUY
		}
		return cmp < 0 // true if i executed before j
	})

	var stockSymbols []string
	for _, txn := range txns {
		if txn.GetSymbol() == "" {
			errMsg := fmt.Sprintf("transaction symbol is empty %s", txn.GetTransactionType())
			if len(txn.GetParentActivities()) > 0 {
				switch txn.GetParentActivities()[0].GetActivity().(type) {
				case *stocks.AccountActivity_TradeActivity:
					errMsg = fmt.Sprintf("%s tradeActivityId : %s", errMsg, txn.GetParentActivities()[0].GetTradeActivity().GetId())
				case *stocks.AccountActivity_NonTradeActivity:
					errMsg = fmt.Sprintf("%s tradeActivityId : %s", errMsg, txn.GetParentActivities()[0].GetNonTradeActivity().GetId())
				default:
					return nil, nil, fmt.Errorf("stock symbol empty and activity is neither trade nor non-trade")
				}
			}
			return nil, nil, errors.New(errMsg)
		}
		stockSymbols = append(stockSymbols, txn.GetSymbol())
	}
	symbolToStockDetailsMap, stockDetailsFetchErr := p.getStocksDetails(ctx, stockSymbols)
	if stockDetailsFetchErr != nil {
		return nil, nil, fmt.Errorf("failed to get stock symbol details : %w", stockDetailsFetchErr)
	}
	return txns, symbolToStockDetailsMap, nil
}

// getBuySellPairs returns all buy-sell pairs from a sell txn and a list of buy txns using FIFO strategy
// NOTE: this also updates the []buyTxns after extracting the pairs
func (p *TransactionProcessor) getBuySellPairs(buyTxnMap map[string][]*tax.TransactionWrapper, sellTxn *tax.TransactionWrapper,
	symbolToStockDetailsMap map[string]*catalog.Stock, fromTime time.Time, toTime time.Time) ([]*BuySellPair, error) {
	var buySellPairs []*BuySellPair
	symbol := sellTxn.GetSymbol()
	buyTxns := buyTxnMap[symbol]
	for !sellTxn.GetQuantity().IsZero() {
		if len(buyTxns) == 0 {
			return nil, fmt.Errorf("no buy txns to match the sell txn : %s", symbol)
		}
		buySellPair, err := p.getBuySellPair(buyTxns[0], sellTxn, symbolToStockDetailsMap)
		if err != nil {
			return nil, fmt.Errorf("failed to get buy-sell pair for %s : %w", symbol, err)
		}
		// sellTime of current pair should be in range [fromTime, toTime]
		if !(buySellPair.SellTime.Before(fromTime) || buySellPair.SellTime.After(toTime)) {
			buySellPairs = append(buySellPairs, buySellPair)
		}
		if buyTxns[0].GetQuantity().IsZero() {
			buyTxns = buyTxns[1:]
		}
	}
	// update the map with the new buys post fifo change
	buyTxnMap[symbol] = buyTxns
	return buySellPairs, nil
}

// nolint: funlen
func (p *TransactionProcessor) getBuySellPair(buyTxn *tax.TransactionWrapper, sellTxn *tax.TransactionWrapper,
	symbolToStockDetailsMap map[string]*catalog.Stock) (*BuySellPair, error) {
	minQty := decimal.Min(buyTxn.GetQuantity(), sellTxn.GetQuantity())
	if minQty.IsZero() {
		return nil, fmt.Errorf("min of buy and sell qty cannot be 0")
	}

	ratioBuyAmount, ratioBuyExpense := p.getRatioAmountAndExpense(buyTxn, minQty)
	ratioSellAmount, ratioSellExpense := p.getRatioAmountAndExpense(sellTxn, minQty)

	totalBuyValue, err := money.Sum(ratioBuyAmount.GetPb(), ratioBuyExpense.GetPb())
	if err != nil {
		return nil, fmt.Errorf("failed to compute total buy value : %w", err)
	}
	totalSellValue, err := money.Subtract(ratioSellAmount.GetPb(), ratioSellExpense.GetPb())
	if err != nil {
		return nil, fmt.Errorf("failed to compute total sell value : %w", err)
	}
	indexedBuyValueInUsd := totalBuyValue

	stockDetails, found := symbolToStockDetailsMap[buyTxn.GetSymbol()]
	if !found {
		return nil, fmt.Errorf("failed to find stock details for buy symbol in details map: %s", buyTxn.GetSymbol())
	}
	ltcgDurationInYears, err := utils.GetLtcgDurationForStockType(stockDetails.GetStockType())
	if err != nil {
		return nil, fmt.Errorf("failed to get ltcg duration for stockType: %w", err)
	}
	sellType, err := utils.GetSellType(buyTxn.GetExecutedAt(), sellTxn.GetExecutedAt(), ltcgDurationInYears)
	if err != nil {
		return nil, fmt.Errorf("failed to check if buy-sell pair is ltcg or not : %w", err)
	}
	if sellType == tax.SellType_SELL_TYPE_LONG_TERM && sellTxn.GetExecutedAt().AsTime().Before(p.gconf.IndexationBenefitNotApplicableFrom()) {
		ciiFactor, ciiFactorErr := p.getCiiFactor(buyTxn.GetExecutedAt(), sellTxn.GetExecutedAt())
		if ciiFactorErr != nil {
			return nil, fmt.Errorf("failed to get cii factor : %w", ciiFactorErr)
		}
		indexedBuyValueInUsd = money.Multiply(totalBuyValue, ciiFactor)
	}

	capitalGainInUsd, err := money.Subtract(totalSellValue, indexedBuyValueInUsd)
	if err != nil {
		return nil, fmt.Errorf("failed to subtract indexedBuyValue from sellAmount : %w", err)
	}

	sellTime := sellTxn.GetExecutedAt().AsTime().In(datetime.IST)
	prevMonthToSell := datetime.AddNMonths(&sellTime, -1)
	conversionRate, err := p.sbiExchangeRateProvider.GetMonthEndBuyRate(*prevMonthToSell)
	if err != nil {
		return nil, fmt.Errorf("failed to get last month sbi exchange rate : %w", err)
	}

	// update current buy and sell transactions since a buy-sell pair is extracted from these
	updateErr := p.updateTxnAfterBuySellPairExtraction(buyTxn, minQty, ratioBuyAmount, ratioBuyExpense)
	if updateErr != nil {
		return nil, fmt.Errorf("failed to update buy txn detail post buy-sell pair extraction : %w", updateErr)
	}
	updateErr = p.updateTxnAfterBuySellPairExtraction(sellTxn, minQty, ratioSellAmount, ratioSellExpense)
	if updateErr != nil {
		return nil, fmt.Errorf("failed to update sell txn detail post buy-sell pair extraction : %w", updateErr)
	}

	return &BuySellPair{
		Symbol:               buyTxn.GetSymbol(),
		StockType:            stockDetails.GetStockType(),
		SellType:             sellType,
		DisplayName:          stockDetails.GetStockBasicDetails().GetName().GetStandardName(),
		Quantity:             minQty,
		BuyTime:              buyTxn.GetExecutedAt().AsTime(),
		SellTime:             sellTxn.GetExecutedAt().AsTime(),
		BuyValueInUsd:        totalBuyValue,
		SellValueInUsd:       totalSellValue,
		IndexedBuyValueInUsd: indexedBuyValueInUsd,
		CapitalGainInUsd:     capitalGainInUsd,
		ConversionRate:       conversionRate.GetPb(),
	}, nil
}

// getCiiFactor returns the ratio of cii of sell year and cii of buy year
func (p *TransactionProcessor) getCiiFactor(buyTime *timestamppb.Timestamp, sellTime *timestamppb.Timestamp) (decimal.Decimal, error) {
	ciiOfBuyYear, ciiErr := p.ciiProvider.GetCII(utils.GetFYForTime(buyTime))
	if ciiErr != nil {
		return decimal.Decimal{}, fmt.Errorf("failed to get cii of buy year : %s", buyTime)
	}
	ciiOfSellYear, ciiErr := p.ciiProvider.GetCII(utils.GetFYForTime(sellTime))
	if ciiErr != nil {
		return decimal.Decimal{}, fmt.Errorf("failed to get cii of sell year : %s", buyTime)
	}
	return decimal.NewFromFloat(float64(ciiOfSellYear) / float64(ciiOfBuyYear)), nil
}

func (p *TransactionProcessor) getRatioAmountAndExpense(txn *tax.TransactionWrapper, qty decimal.Decimal) (*money.Money, *money.Money) {
	ratioAmount := p.getRatioAmount(txn.GetAmount(), txn.GetQuantity(), qty)
	ratioExpense := p.getRatioAmount(txn.GetExpense(), txn.GetQuantity(), qty)
	return ratioAmount, ratioExpense
}

// getRatioAmount returns amt * units / totalUnits
func (p *TransactionProcessor) getRatioAmount(amt *moneyPb.Money, totalUnits decimal.Decimal, units decimal.Decimal) *money.Money {
	ratio := units.Div(totalUnits)
	return money.NewMoney(money.Multiply(amt, ratio))
}

func (p *TransactionProcessor) updateTxnAfterBuySellPairExtraction(txn *tax.TransactionWrapper, qty decimal.Decimal, usedAmount *money.Money, usedExpense *money.Money) error {
	var err error
	txn.Quantity = txn.GetQuantity().Sub(qty)
	txn.Amount, err = money.Subtract(txn.Amount, usedAmount.GetPb())
	if err != nil {
		return fmt.Errorf("failed to sub usedAmount from txn amount : %w", err)
	}
	txn.Expense, err = money.Subtract(txn.Expense, usedExpense.GetPb())
	if err != nil {
		return fmt.Errorf("failed to sub usedExpense from txn amount : %w", err)
	}
	return nil
}

// nolint:funlen
func (p *TransactionProcessor) GetDividendInfo(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (*DividendInfo, error) {
	var dividendTxns []*DividendTxn
	taxPaidInUsd := money.ZeroUSD()
	taxPaidInInr := money.ZeroINR().GetPb()
	totalDividendIncomeUsd := money.ZeroUSD()
	totalDividendIncomeInr := money.ZeroINR().GetPb()

	var stockSymbols []string
	for _, txn := range txns {
		stockSymbols = append(stockSymbols, txn.GetSymbol())
	}

	symbolToStockDetailsMap, stockDetailsFetchErr := p.getStocksDetails(ctx, stockSymbols)
	if stockDetailsFetchErr != nil {
		return nil, fmt.Errorf("failed to get stock symbol details : %w", stockDetailsFetchErr)
	}
	for _, txn := range txns {
		if txn.GetTransactionType() != tax.TransactionType_TRANSACTION_TYPE_DIVIDEND && txn.GetTransactionType() != tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD {
			continue
		}
		executedAt := txn.GetExecutedAt().AsTime().In(datetime.IST)
		if executedAt.Before(fromTime) || executedAt.After(toTime) {
			continue
		}
		lastMonthDate := datetime.AddNMonths(&executedAt, -1)
		conversionRate, err := p.sbiExchangeRateProvider.GetMonthEndBuyRate(*lastMonthDate)
		if err != nil {
			return nil, fmt.Errorf("failed to get sbi exchange rate : %w", err)
		}
		amountInInr := money.ConvertUSDToINR(txn.GetAmount(), conversionRate.GetPb())
		stockDetails, stockDetailsFound := symbolToStockDetailsMap[txn.GetSymbol()]
		if !stockDetailsFound {
			return nil, fmt.Errorf("failed to get stock details in map for stock : %s", txn.GetSymbol())
		}
		securityType, err := utils.GetSecurityType(stockDetails.GetStockType())
		if err != nil {
			return nil, fmt.Errorf("failed to get security type : %w", err)
		}
		dividendTxns = append(dividendTxns, &DividendTxn{
			ExecutedAt:      txn.GetExecutedAt().AsTime().In(datetime.IST),
			SecuritySymbol:  txn.GetSymbol(),
			SecurityType:    securityType,
			SecurityName:    stockDetails.GetStockBasicDetails().GetName().GetStandardName(),
			AmountInUsd:     txn.GetAmount(),
			AmountInInr:     amountInInr,
			TransactionType: txn.GetTransactionType(),
		})
		if txn.GetTransactionType() == tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD {
			taxPaidInUsd, err = money.Sum(taxPaidInUsd, txn.GetAmount())
			if err != nil {
				return nil, fmt.Errorf("failed to sum taxPaidInUsd with txn amount : %w", err)
			}
			taxPaidInInr, err = money.Sum(taxPaidInInr, amountInInr)
			if err != nil {
				return nil, fmt.Errorf("failed to sum taxPaidInInr with amountInInr : %w", err)
			}
		} else {
			totalDividendIncomeUsd, err = money.Sum(totalDividendIncomeUsd, txn.GetAmount())
			if err != nil {
				return nil, fmt.Errorf("failed to sum totalDividendIncomeUsd with txn amount : %w", err)
			}
			totalDividendIncomeInr, err = money.Sum(totalDividendIncomeInr, amountInInr)
			if err != nil {
				return nil, fmt.Errorf("failed to sum totalDividendIncomeInr with amountInInr : %w", err)
			}
		}
	}
	return &DividendInfo{
		DividendTxns: dividendTxns,
		Summary: &DividendSummary{
			TotalDividendIncomeInUsd: totalDividendIncomeUsd,
			TotalDividendIncomeInInr: totalDividendIncomeInr,
			TotalTaxPaidInUsd:        AbsoluteMoney(taxPaidInUsd),
			TotalTaxPaidInInr:        AbsoluteMoney(taxPaidInInr),
		},
	}, nil
}

func (p *TransactionProcessor) getStocksDetails(ctx context.Context, stockSymbols []string) (map[string]*catalog.Stock, error) {
	stockSymbols = lo.Uniq(stockSymbols)
	if len(stockSymbols) == 0 {
		return nil, nil
	}
	getStocksResp, getStocksErr := p.ussCatalogMgrClient.GetStocks(ctx, &catalog.GetStocksRequest{
		Identifiers: &catalog.GetStocksRequest_Symbols{
			Symbols: &catalog.RepeatedStrings{
				Ids: stockSymbols,
			},
		},
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{
				StockModelSymbolFieldName,
				StockModelStockTypeFieldName,
				StockModelStockBasicDetailsFieldName,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(getStocksResp, getStocksErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get stocks by symbols : %w for %v", rpcErr, stockSymbols)
	}
	return getStocksResp.GetStocks(), nil
}

func (p *TransactionProcessor) GetNetLtcgAndStcg(_ context.Context, pairs []*BuySellPair) (*LtcgStcgInfo, error) {
	var sumErr error
	stcgInUsd := money.ZeroUSD()
	ltcgInUsd := money.ZeroUSD()
	stcgInInr := money.ZeroINR().GetPb()
	ltcgInInr := money.ZeroINR().GetPb()

	for _, pair := range pairs {
		if pair == nil {
			return nil, fmt.Errorf("pair cannot be nil")
		}
		switch pair.SellType {
		case tax.SellType_SELL_TYPE_LONG_TERM:
			ltcgInUsd, sumErr = money.Sum(ltcgInUsd, pair.CapitalGainInUsd)
			if sumErr != nil {
				return nil, fmt.Errorf("failure in dollar sum in ltcg calc : %w", sumErr)
			}
			ltcgInInr, sumErr = money.Sum(ltcgInInr, money.ConvertUSDToINR(pair.CapitalGainInUsd, pair.ConversionRate))
			if sumErr != nil {
				return nil, fmt.Errorf("failure in inr sum in ltcg calc : %w", sumErr)
			}
		case tax.SellType_SELL_TYPE_SHORT_TERM:
			stcgInUsd, sumErr = money.Sum(stcgInUsd, pair.CapitalGainInUsd)
			if sumErr != nil {
				return nil, fmt.Errorf("failed to money sum in stcg calc : %w", sumErr)
			}
			stcgInInr, sumErr = money.Sum(stcgInInr, money.ConvertUSDToINR(pair.CapitalGainInUsd, pair.ConversionRate))
			if sumErr != nil {
				return nil, fmt.Errorf("failure in inr sum in stcg calc : %w", sumErr)
			}
		default:
			return nil, fmt.Errorf("unhandled sell type %T", pair.SellType)
		}
	}
	return &LtcgStcgInfo{
		LtcgInUsd: ltcgInUsd,
		StcgInUsd: stcgInUsd,
		LtcgInInr: ltcgInInr,
		StcgInInr: stcgInInr,
	}, nil
}

func AbsoluteMoney(m *moneyPb.Money) *moneyPb.Money {
	if money.IsNegative(m) {
		m = money.Negate(m)
	}
	return m
}
