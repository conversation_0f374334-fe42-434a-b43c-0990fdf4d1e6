package generator

import (
	"context"
	"fmt"
	"strings"

	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	ussTaxDocumentParamsPb "github.com/epifi/gamma/api/usstocks/tax/documentparams"
)

const Sheet1 = "Sheet1"

type CapitalGainsGeneratorImpl DocumentGenerator

type CapitalGainsGenerator struct {
}

func NewCapitalGainsGenerator() *CapitalGainsGenerator {
	return &CapitalGainsGenerator{}
}

// GenerateExcel creates an excel using the capital gains params in the
// defined format https://docs.google.com/document/d/1dj4Nk82RAnwQn-Ukrq6MnWf_Qnt0GBWzo2EIf0SsldE/edit#bookmark=id.3hf33qv8xynk
func (c *CapitalGainsGenerator) GenerateExcel(ctx context.Context, params *ussTaxDocumentParamsPb.DocumentParams) ([]byte, error) {
	capitalGainsParams := params.GetCapitalGainParams()
	if capitalGainsParams == nil {
		return nil, fmt.Errorf("capital gains params cannot be nil, got params of type %T", params.GetParams())
	}

	rows := c.createAccountDetailsSection(capitalGainsParams.GetAccountDetails()).
		AppendEmptyRows(2).
		AppendSection(c.createStcgSection(capitalGainsParams.GetShortTermCapitalGainsSummary())).
		AppendEmptyRows(2).
		AppendSection(c.createLtcgSection(capitalGainsParams.GetLongTermCapitalGainsSummary())).
		AppendEmptyRows(1).
		AppendSection(c.createCapitalGainsSummarySection(capitalGainsParams.GetCapitalGainsSummary())).
		AppendEmptyRows(2).
		AppendSection(c.createDividendsSection(capitalGainsParams.GetDividendsSummary()))

	disclaimer, err := GetDisclaimerSection(ussTaxPb.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_CAPITAL_GAIN)
	if err != nil {
		return nil, fmt.Errorf("failed to get disclaimer section: %w", err)
	}
	// append disclaimer at end
	rows.AppendEmptyRows(4).AppendSection(disclaimer)

	buf, err := rows.CreateExcel(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create capital gains excel file: %w", err)
	}

	return buf, nil
}

func (c *CapitalGainsGenerator) createAccountDetailsSection(accountDetails *ussTaxDocumentParamsPb.AccountDetails) *Section {
	return NewSection().AppendRow(NewStringCellsWitStyle(Bold, "Account Details")).
		AppendRow(NewStringCellsWitStyle(Bold, "Broker Account Number", accountDetails.GetBrokerAccountNumber())).
		AppendRow(NewStringCellsWitStyle(Bold, "Broker", accountDetails.GetBroker())).
		AppendRow(NewStringCellsWitStyle(Bold, "Statement Duration", fmt.Sprintf("%s to %s", dateToString(accountDetails.GetStatementFrom()), dateToString(accountDetails.GetStatementTill()))))
}

func (c *CapitalGainsGenerator) createStcgSection(txns []*ussTaxDocumentParamsPb.BuySellInfo) *Section {
	rows := NewSection()
	rows.AppendRow(NewStringCellsWitStyle(Bold, "Short Term Capital Gain Summary")).
		AppendRow(NewStringCellsWitStyle(Bold, "Sale Date", "Security ISIN/ Symbol", "Security Type", "Security Name", "Units Sold",
			"Average Sale Rate (US $)", "Sale Value (US $)", "Buy Date", "Buy Rate (US $)", "Buy Value (US $)",
			"Short Term Capital Gains / (Loss) (US $)", "Conversion Rate", "Short Term Capital Gains / (Loss) (INR)",
			"Country of Incorporation of Company / Security", "Comments"))
	for _, buysell := range txns {
		comments := strings.Join(buysell.GetComments(), "\n")
		rows.AppendRow(NewStringCells(
			dateToString(buysell.GetSaleDate()),
			buysell.GetSymbol(),
			securityTypeToString(buysell.GetSecurityType()),
			buysell.GetSecurityName(),
			buysell.GetUnitsSold().GetValue(),
			moneyToString(buysell.GetAverageSaleRateInUsd()),
			moneyToString(buysell.GetSaleValueInUsd()),
			dateToString(buysell.GetBuyDate()),
			moneyToString(buysell.GetBuyRateInUsd()),
			moneyToString(buysell.GetBuyValueInUsd()),
			moneyToString(buysell.GetCapitalGainInUsd()),
			moneyToString(buysell.GetConversionRate()),
			moneyToString(buysell.GetCapitalGainInInr()),
			buysell.GetCountryOfIncorporationOfSecurity(),
			comments))
	}
	return rows
}

func (c *CapitalGainsGenerator) createLtcgSection(txns []*ussTaxDocumentParamsPb.BuySellInfo) *Section {
	rows := NewSection()
	rows.AppendRow(NewStringCellsWitStyle(Bold, "Long Term Capital Gain Summary")).
		AppendRow(NewStringCellsWitStyle(Bold, "Sale Date", "Security ISIN/ Symbol", "Security Type", "Security Name", "Units Sold",
			"Average Sale Rate (US $)", "Sale Value (US $)", "Buy Date", "Buy Rate (US $)", "Buy Value (US $)", "Period of Holding",
			"Long Term Capital Gains / (Loss) (US $)", "Conversion Rate", "Long Term Capital Gains / (Loss) (INR)",
			"Country of Incorporation of Company / Security", "Comments"))

	for _, buysell := range txns {
		comments := strings.Join(buysell.GetComments(), "\n")
		rows.AppendRow(NewStringCells(
			dateToString(buysell.GetSaleDate()),
			buysell.GetSymbol(),
			securityTypeToString(buysell.GetSecurityType()),
			buysell.GetSecurityName(),
			fmt.Sprint(buysell.GetUnitsSold()),
			moneyToString(buysell.GetAverageSaleRateInUsd()),
			moneyToString(buysell.GetSaleValueInUsd()),
			dateToString(buysell.GetBuyDate()),
			moneyToString(buysell.GetBuyRateInUsd()),
			moneyToString(buysell.GetBuyValueInUsd()),
			holdingDurationToString(buysell.GetPeriodOfHolding()),
			moneyToString(buysell.GetCapitalGainInUsd()),
			moneyToString(buysell.GetConversionRate()),
			moneyToString(buysell.GetCapitalGainInInr()),
			buysell.GetCountryOfIncorporationOfSecurity(),
			comments))
	}
	return rows
}

func (c *CapitalGainsGenerator) createCapitalGainsSummarySection(cgSummary *ussTaxDocumentParamsPb.CapitalGainsSummary) *Section {
	rows := NewSection()
	rows.AppendRow(NewStringCellsWitStyle(Bold, "Capital Gain Summary")).
		AppendRow([]*Cell{NewStringCellWithStyle(Bold, "Short Term Capital Gains / (Loss)"), NewStringCell(moneyToString(cgSummary.GetStcgInInr()))}).
		AppendRow([]*Cell{NewStringCellWithStyle(Bold, "Long Term Capital Gains / (Loss)"), NewStringCell(moneyToString(cgSummary.GetLtcgInInr()))}).
		AppendRow(NewStringCellsWitStyle(Bold, "Net Capital Gains/ Loss after set off - Short Term")).
		AppendRow(NewStringCellsWitStyle(Bold, "Net Capital Gains/ Loss after set off - Long Term"))
	return rows
}

func (c *CapitalGainsGenerator) createDividendsSection(divSummary *ussTaxDocumentParamsPb.DividendsSummary) *Section {
	rows := NewSection()
	rows.AppendRow(NewStringCellsWitStyle(Bold, "Dividends")).
		AppendRow(NewStringCellsWitStyle(Bold, "Received Date", "Security ISIN/ Symbol", "Security Type", "Security Name", "Amount ($)", "Amount (INR)", "Comments"))

	for _, div := range divSummary.GetDividendTransactions() {
		comments := strings.Join(div.GetComments(), "\n")

		rows.AppendRow(NewStringCells(
			dateToString(div.GetDate()),
			div.GetSecuritySymbol(),
			securityTypeToString(div.GetSecurityType()),
			div.GetSecurityName(),
			moneyToString(div.GetAmount().GetUsd()),
			moneyToString(div.GetAmount().GetInr()),
			comments))
	}

	rows.AppendRow(append(NewStringCells("", "", ""), NewStringCellWithStyle(Bold, "Total Dividend Income"), NewStringCell(moneyToString(divSummary.GetTotalDividendIncome().GetUsd())), NewStringCell(moneyToString(divSummary.GetTotalDividendIncome().GetInr()))))

	return rows
}

func moneyToString(m *moneyPb.Money) string {
	return money.ToDecimal(m).Truncate(10).String()
}

func dateToString(d *datePb.Date) string {
	return datetime.DateToString(d, "2006-01-02", datetime.IST)
}

func holdingDurationToString(h *ussTaxDocumentParamsPb.HoldingDuration) string {
	if h.GetYears() > 0 {
		return fmt.Sprintf("%d years, %d days", h.GetYears(), h.GetDays())
	}
	return fmt.Sprintf("%d days", h.GetDays())
}

func securityTypeToString(securityType ussTaxPb.SecurityType) string {
	switch securityType {
	case ussTaxPb.SecurityType_SECURITY_TYPE_EQUITY:
		return "Equity"
	case ussTaxPb.SecurityType_SECURITY_TYPE_ETF:
		return "ETF"
	case ussTaxPb.SecurityType_SECURITY_TYPE_RIGHTS:
		return "Rights"
	default:
		return "Unknown"
	}
}
