package generator

import (
	"bytes"
	"context"
	"fmt"
	"testing"

	"github.com/go-test/deep"
	"github.com/xuri/excelize/v2"
	datePb "google.golang.org/genproto/googleapis/type/date"
	decimalPb "google.golang.org/genproto/googleapis/type/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	ussTaxDocumentParamsPb "github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/api/usstocks/tax/moneywrapper"
)

func TestCapitalGainsGenerator_Generate(t *testing.T) {
	tests := []struct {
		name    string
		params  *ussTaxDocumentParamsPb.DocumentParams
		want    []byte
		wantErr bool
	}{
		{
			name: "Successfully create capital gains excel",
			params: &ussTaxDocumentParamsPb.DocumentParams{
				Params: &ussTaxDocumentParamsPb.DocumentParams_CapitalGainParams{CapitalGainParams: &ussTaxDocumentParamsPb.CapitalGainsParams{
					AccountDetails: &ussTaxDocumentParamsPb.AccountDetails{
						BrokerAccountNumber: "**********",
						Broker:              "Alpaca",
						StatementFrom:       &datePb.Date{Year: 2023, Month: 04, Day: 01},
						StatementTill:       &datePb.Date{Year: 2024, Month: 03, Day: 31},
					},
					ShortTermCapitalGainsSummary: []*ussTaxDocumentParamsPb.BuySellInfo{
						{
							SellType:                         ussTaxPb.SellType_SELL_TYPE_SHORT_TERM,
							SaleDate:                         &datePb.Date{Year: 2023, Month: 11, Day: 05},
							Symbol:                           "APPL",
							SecurityType:                     ussTaxPb.SecurityType_SECURITY_TYPE_EQUITY,
							SecurityName:                     "Apple",
							UnitsSold:                        &decimalPb.Decimal{Value: "2"},
							AverageSaleRateInUsd:             &moneyPb.Money{CurrencyCode: "USD", Units: 50, Nanos: *********},
							SaleValueInUsd:                   &moneyPb.Money{CurrencyCode: "USD", Units: 100, Nanos: *********},
							BuyDate:                          &datePb.Date{Year: 2023, Month: 10, Day: 10},
							BuyRateInUsd:                     &moneyPb.Money{CurrencyCode: "USD", Units: 40},
							BuyValueInUsd:                    &moneyPb.Money{CurrencyCode: "USD", Units: 80},
							PeriodOfHolding:                  &ussTaxDocumentParamsPb.HoldingDuration{Days: 26},
							CapitalGainInUsd:                 &moneyPb.Money{CurrencyCode: "USD", Units: 20, Nanos: *********},
							ConversionRate:                   &moneyPb.Money{CurrencyCode: "INR", Units: 80},
							CapitalGainInInr:                 &moneyPb.Money{CurrencyCode: "INR", Units: 1609, Nanos: *********},
							CountryOfIncorporationOfSecurity: "USA",
							Comments:                         []string{"short term sell", "split on 2023/11/02"},
						},
						{
							SellType:                         ussTaxPb.SellType_SELL_TYPE_SHORT_TERM,
							SaleDate:                         &datePb.Date{Year: 2023, Month: 11, Day: 15},
							Symbol:                           "ASML",
							SecurityType:                     ussTaxPb.SecurityType_SECURITY_TYPE_EQUITY,
							SecurityName:                     "ASML",
							UnitsSold:                        &decimalPb.Decimal{Value: "1.77"},
							AverageSaleRateInUsd:             &moneyPb.Money{CurrencyCode: "USD", Units: 85},
							SaleValueInUsd:                   &moneyPb.Money{CurrencyCode: "USD", Units: 150, Nanos: *********},
							BuyDate:                          &datePb.Date{Year: 2023, Month: 11, Day: 4},
							BuyRateInUsd:                     &moneyPb.Money{CurrencyCode: "USD", Units: 90},
							BuyValueInUsd:                    &moneyPb.Money{CurrencyCode: "USD", Units: 159, Nanos: *********},
							PeriodOfHolding:                  &ussTaxDocumentParamsPb.HoldingDuration{Days: 11},
							CapitalGainInUsd:                 &moneyPb.Money{CurrencyCode: "USD", Units: -9, Nanos: -*********},
							ConversionRate:                   &moneyPb.Money{CurrencyCode: "INR", Units: 80},
							CapitalGainInInr:                 &moneyPb.Money{CurrencyCode: "INR", Units: -720, Nanos: -*********},
							CountryOfIncorporationOfSecurity: "Netherland",
						},
					},
					LongTermCapitalGainsSummary: []*ussTaxDocumentParamsPb.BuySellInfo{
						{
							SellType:                         ussTaxPb.SellType_SELL_TYPE_SHORT_TERM,
							SaleDate:                         &datePb.Date{Year: 2021, Month: 11, Day: 05},
							Symbol:                           "APPL",
							SecurityType:                     ussTaxPb.SecurityType_SECURITY_TYPE_EQUITY,
							SecurityName:                     "Apple",
							UnitsSold:                        &decimalPb.Decimal{Value: "2"},
							AverageSaleRateInUsd:             &moneyPb.Money{CurrencyCode: "USD", Units: 50, Nanos: *********},
							SaleValueInUsd:                   &moneyPb.Money{CurrencyCode: "USD", Units: 100, Nanos: *********},
							BuyDate:                          &datePb.Date{Year: 2023, Month: 10, Day: 10},
							BuyRateInUsd:                     &moneyPb.Money{CurrencyCode: "USD", Units: 40},
							BuyValueInUsd:                    &moneyPb.Money{CurrencyCode: "USD", Units: 80},
							PeriodOfHolding:                  &ussTaxDocumentParamsPb.HoldingDuration{Years: 2, Days: 10},
							CapitalGainInUsd:                 &moneyPb.Money{CurrencyCode: "USD", Units: 20, Nanos: *********},
							ConversionRate:                   &moneyPb.Money{CurrencyCode: "INR", Units: 80},
							CapitalGainInInr:                 &moneyPb.Money{CurrencyCode: "INR", Units: 1609, Nanos: *********},
							CountryOfIncorporationOfSecurity: "USA",
						},
					},
					CapitalGainsSummary: &ussTaxDocumentParamsPb.CapitalGainsSummary{
						StcgInInr: &moneyPb.Money{CurrencyCode: "INR", Units: 889, Nanos: *********},
						LtcgInInr: &moneyPb.Money{CurrencyCode: "INR", Units: 1609, Nanos: *********},
					},
					DividendsSummary: &ussTaxDocumentParamsPb.DividendsSummary{
						DividendTransactions: []*ussTaxDocumentParamsPb.DividendInterestTxn{
							{
								Date:           &datePb.Date{Year: 2023, Month: 05, Day: 05},
								SecuritySymbol: "APPL",
								SecurityType:   ussTaxPb.SecurityType_SECURITY_TYPE_EQUITY,
								SecurityName:   "Apple",
								Amount: &moneywrapper.UsdInrWrapper{
									Usd: &moneyPb.Money{CurrencyCode: "USD", Units: 1, Nanos: *********},
									Inr: &moneyPb.Money{CurrencyCode: "INR", Units: 86},
								},
								Comments: []string{"Q1 dividend"},
							},
							{
								Date:           &datePb.Date{Year: 2023, Month: 05, Day: 05},
								SecuritySymbol: "APPL",
								SecurityType:   ussTaxPb.SecurityType_SECURITY_TYPE_EQUITY,
								SecurityName:   "Apple",
								Amount: &moneywrapper.UsdInrWrapper{
									Usd: &moneyPb.Money{CurrencyCode: "USD", Units: 0, Nanos: -*********},
									Inr: &moneyPb.Money{CurrencyCode: "INR", Units: -8},
								},
								Comments: []string{"Q1 dividend withholding"},
							},
						},
						TotalDividendIncome: &moneywrapper.UsdInrWrapper{
							Usd: &moneyPb.Money{CurrencyCode: "USD", Units: 1, Nanos: *********},
							Inr: &moneyPb.Money{CurrencyCode: "INR", Units: -8},
						},
					},
				},
				},
			},
		},
		{
			name: "Invalid params type should lead to error",
			params: &ussTaxDocumentParamsPb.DocumentParams{
				Params: &ussTaxDocumentParamsPb.DocumentParams_Form_67Params{Form_67Params: &ussTaxDocumentParamsPb.Form67Params{}},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			c := NewCapitalGainsGenerator()
			got, err := c.GenerateExcel(context.Background(), tt.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateExcel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}

			compareExcelBufferWithFile(t, got, "capital_gains_sample.xlsx")
		})
	}
}

func compareExcelBufferWithFile(t *testing.T, gotBuf []byte, filePath string) {
	gotExcel, err := excelize.OpenReader(bytes.NewReader(gotBuf))
	if err != nil {
		t.Fatalf("failed to create reader for excel buffer: %v", err)
	}
	gotRows, err := getExcelAsMatrix(gotExcel)
	if err != nil {
		t.Fatalf("failed to get got excel as matrix: %v", err)
	}

	wantExcel, err := excelize.OpenFile(filePath)
	if err != nil {
		t.Fatalf("failed to open excel file: %v", err)
	}

	wantRows, err := getExcelAsMatrix(wantExcel)
	if err != nil {
		t.Fatalf("failed to get want excel as matrix: %v", err)
	}

	if diff := deep.Equal(gotRows, wantRows); len(diff) != 0 {
		t.Fatalf("mismatch in excel rows: %v", diff)
	}
}

func getExcelAsMatrix(f *excelize.File) ([][]string, error) {
	rows, err := f.Rows(Sheet1)
	if err != nil {
		return nil, fmt.Errorf("failed to get rows of sheet1: %w", err)
	}
	var rowsStr [][]string
	for rows.Next() {
		cols, err := rows.Columns()
		if err != nil {
			return nil, fmt.Errorf("failed to read got columns: %w", err)
		}
		rowsStr = append(rowsStr, cols)
	}
	return rowsStr, nil
}
