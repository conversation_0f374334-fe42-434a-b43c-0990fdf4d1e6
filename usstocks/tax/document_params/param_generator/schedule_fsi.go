package param_generator

import (
	"context"
	"fmt"

	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/api/usstocks/tax/moneywrapper"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
)

type ScheduleFsiDocParamsGenerator struct {
	*BaseDataProvider
}

func NewScheduleFsiDocParamsGenerator(transactionProcessor transactionprocessor.ITransactionProcessor) *ScheduleFsiDocParamsGenerator {
	return &ScheduleFsiDocParamsGenerator{
		BaseDataProvider: NewBaseDataProvider(transactionProcessor),
	}
}

func (g *ScheduleFsiDocParamsGenerator) GetDocumentParams(ctx context.Context, req *ParamsGenerateReq) (*documentparams.DocumentParams, error) {
	if err := g.validateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failure : %w", err)
	}

	usaIncomeSummary := g.getUsaIncomeSummary(req.BuySellPairs, req.DividendInfo)

	return &documentparams.DocumentParams{
		Params: &documentparams.DocumentParams_ScheduleFsiParams{
			ScheduleFsiParams: &documentparams.ScheduleFSIParams{
				IncomesFromOutsideSourcesDetails: []*documentparams.IncomeFromForeignSourcesDetails{
					usaIncomeSummary,
				},
			},
		},
	}, nil
}

func (g *ScheduleFsiDocParamsGenerator) getUsaIncomeSummary(pairs *transactionprocessor.BuySellPairs, dividendInfo *transactionprocessor.DividendInfo) *documentparams.IncomeFromForeignSourcesDetails {
	stcgInUsd := moneyPb.ZeroUSD()
	ltcgInUsd := moneyPb.ZeroUSD()
	stcgInInr := moneyPb.ZeroINR().GetPb()
	ltcgInInr := moneyPb.ZeroINR().GetPb()

	if pairs.NetLtcgStcgInfo != nil && pairs.NetLtcgStcgInfo.StcgInUsd != nil {
		stcgInUsd = pairs.NetLtcgStcgInfo.StcgInUsd
	}
	if pairs.NetLtcgStcgInfo != nil && pairs.NetLtcgStcgInfo.LtcgInUsd != nil {
		ltcgInUsd = pairs.NetLtcgStcgInfo.LtcgInUsd
	}
	if pairs.NetLtcgStcgInfo != nil && pairs.NetLtcgStcgInfo.StcgInInr != nil {
		stcgInInr = pairs.NetLtcgStcgInfo.StcgInInr
	}
	if pairs.NetLtcgStcgInfo != nil && pairs.NetLtcgStcgInfo.LtcgInInr != nil {
		ltcgInInr = pairs.NetLtcgStcgInfo.LtcgInInr
	}

	return &documentparams.IncomeFromForeignSourcesDetails{
		CountryName: CountryUSA,
		StcgAmount: &moneywrapper.UsdInrWrapper{
			Usd: stcgInUsd,
			Inr: stcgInInr,
		},
		LtcgAmount: &moneywrapper.UsdInrWrapper{
			Usd: ltcgInUsd,
			Inr: ltcgInInr,
		},
		GainFromDividends: &moneywrapper.UsdInrWrapper{
			Usd: dividendInfo.Summary.TotalDividendIncomeInUsd,
			Inr: dividendInfo.Summary.TotalDividendIncomeInInr,
		},
		GainFromInterests: &moneywrapper.UsdInrWrapper{
			Usd: moneyPb.ZeroUSD(),
			Inr: moneyPb.ZeroINR().GetPb(),
		},
		TaxPaidOnDividends: &moneywrapper.UsdInrWrapper{
			Usd: dividendInfo.Summary.TotalTaxPaidInUsd,
			Inr: dividendInfo.Summary.TotalTaxPaidInInr,
		},
		TaxPaidOnInterests: &moneywrapper.UsdInrWrapper{
			Usd: moneyPb.ZeroUSD(),
			Inr: moneyPb.ZeroINR().GetPb(),
		},
	}
}

func (g *ScheduleFsiDocParamsGenerator) validateRequest(req *ParamsGenerateReq) error {
	switch {
	case req == nil:
		return fmt.Errorf("param_gen_req cannot be nil")
	case req.DividendInfo == nil:
		return fmt.Errorf("dividend_info cannot be nil")
	case req.BuySellPairs == nil:
		return fmt.Errorf("buy_sell_pairs cannot be nil")
	case req.BuySellPairs.NetLtcgStcgInfo == nil:
		return fmt.Errorf("buy_sell_pairs.net_ltcg_stcg cannot be nil")
	case req.DividendInfo.Summary == nil:
		return fmt.Errorf("dividend_info.summary cannot be nil")
	}
	return nil
}
