package param_generator

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	cacheMock "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/api/usstocks/tax/moneywrapper"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
	mocks "github.com/epifi/gamma/usstocks/tax/transactionprocessor/mocks"
	mocks2 "github.com/epifi/gamma/usstocks/utils/mocks"
)

var (
	dailyClosingPrice31Dec = &tax.DailyClosingPrice{
		ClosingPrice:     &moneyPb.Money{CurrencyCode: "USD", Units: 150},
		ClosingPriceTime: timestamppb.New(time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST)),
	}
	dailyClosingPrice30Dec = &tax.DailyClosingPrice{
		ClosingPrice:     &moneyPb.Money{CurrencyCode: "USD", Units: 135},
		ClosingPriceTime: timestamppb.New(time.Date(2024, 12, 30, 0, 0, 0, 0, datetime.IST)),
	}
	dailyClosingPrice15Dec = &tax.DailyClosingPrice{
		ClosingPrice:     &moneyPb.Money{CurrencyCode: "USD", Units: 125},
		ClosingPriceTime: timestamppb.New(time.Date(2024, 12, 15, 0, 0, 0, 0, datetime.IST)),
	}
	dailyClosingPrice10Sept = &tax.DailyClosingPrice{
		ClosingPrice:     &moneyPb.Money{CurrencyCode: "USD", Units: 180},
		ClosingPriceTime: timestamppb.New(time.Date(2024, 9, 10, 0, 0, 0, 0, datetime.IST)),
	}
	dailyClosingPrice5May = &tax.DailyClosingPrice{
		ClosingPrice:     &moneyPb.Money{CurrencyCode: "USD", Units: 175},
		ClosingPriceTime: timestamppb.New(time.Date(2024, 5, 5, 0, 0, 0, 0, datetime.IST)),
	}
)

type fields struct {
	transactionProcessor    *mocks.MockITransactionProcessor
	sbiExchangeRateProvider *mocks2.MockSBIExchangeRateProvider
	usStocksCache           *cacheMock.MockCacheStorage
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		transactionProcessor:    mocks.NewMockITransactionProcessor(ctrl),
		sbiExchangeRateProvider: mocks2.NewMockSBIExchangeRateProvider(ctrl),
		usStocksCache:           cacheMock.NewMockCacheStorage(ctrl),
	}
}

func TestScheduleFaDocParamsGenerator_getClosingInvestmentBalanceINR(t *testing.T) {
	t.Parallel()
	logger.Init(cfg.TestEnv)
	type args struct {
		req *foreignSecurityReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *moneyPb.Money
		wantErr    bool
	}{
		{
			name: "stock sold, sell value not nil",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						SellValueInUsd: &moneyPb.Money{CurrencyCode: "USD", Units: 100},
					},
				},
			},
			setupMocks: func(f *fields) {},
			wantErr:    false,
		},
		{
			name: "stock not sold, closing price available, error getting exchange rate",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Quantity: decimal.NewFromInt32(10),
						Symbol:   "AAPL",
					},
					stockEntityDetails: &tax.StockEntityDetails{
						Symbol:             "AAPL",
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice31Dec, dailyClosingPrice30Dec, dailyClosingPrice15Dec},
					},
					toTime: time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {
				exchangeRateTime := time.Date(2024, 11, 30, 0, 0, 0, 0, datetime.IST)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(exchangeRateTime).Return(nil, fmt.Errorf("failed to get exchange rate"))
			},
			wantErr: true,
		},
		{
			name: "stock not sold, no closing price available",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Quantity: decimal.NewFromInt32(10),
						Symbol:   "AAPL",
					},
					stockEntityDetails: &tax.StockEntityDetails{
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice30Dec, dailyClosingPrice15Dec},
					},
					toTime: time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {},
			wantErr:    false,
		},
		{
			name: "stock not sold, closing price available",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Quantity: decimal.NewFromInt32(10),
						Symbol:   "AAPL",
					},
					stockEntityDetails: &tax.StockEntityDetails{
						Symbol:             "AAPL",
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice31Dec, dailyClosingPrice30Dec, dailyClosingPrice15Dec},
					},
					toTime: time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {
				exchangeRateTime := time.Date(2024, 11, 30, 0, 0, 0, 0, datetime.IST)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(exchangeRateTime).Return(money.NewMoney(money.FromPaisa(80*100)), nil)
			},
			want: &moneyPb.Money{CurrencyCode: "INR", Units: 120000},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			g := &ScheduleFaDocParamsGenerator{
				transactionProcessor:    f.transactionProcessor,
				sbiExchangeRateProvider: f.sbiExchangeRateProvider,
				usStocksCache:           f.usStocksCache,
			}
			tt.setupMocks(f)
			got, err := g.getClosingInvestmentBalanceINR(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("getClosingInvestmentBalanceINR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("getClosingInvestmentBalanceINR() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func TestScheduleFaDocParamsGenerator_getPeakValueOfInvestment(t *testing.T) {
	t.Parallel()
	logger.Init(cfg.TestEnv)
	type args struct {
		req *foreignSecurityReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *moneywrapper.UsdInrWrapper
		wantErr    bool
	}{
		{
			name: "stock not sold, peak price available, error getting exchange rate",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Symbol:   "AAPL",
						Quantity: decimal.NewFromInt32(10),
						BuyTime:  time.Date(2024, 1, 15, 0, 0, 0, 0, datetime.IST),
					},
					stockEntityDetails: &tax.StockEntityDetails{
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice31Dec},
					},
					fromTime: time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
					toTime:   time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {
				exchangeRateTime := time.Date(2024, 11, 30, 0, 0, 0, 0, datetime.IST).In(time.Local)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(exchangeRateTime).Return(nil, fmt.Errorf("failed to get exchange rate"))
			},
			wantErr: true,
		},
		{
			name: "valid buy sell pair, no closing price available within range",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Symbol:         "AAPL",
						Quantity:       decimal.NewFromInt32(10),
						SellValueInUsd: &moneyPb.Money{CurrencyCode: "USD", Units: 2000},
						BuyTime:        time.Date(2024, 4, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:       time.Date(2024, 6, 1, 0, 0, 0, 0, datetime.IST),
					},
					stockEntityDetails: &tax.StockEntityDetails{
						Symbol:             "AAPL",
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice15Dec, dailyClosingPrice30Dec, dailyClosingPrice15Dec},
					},
					fromTime: time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
					toTime:   time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {},
			wantErr:    true,
		},
		{
			name: "stock not sold, buy before range, peak within range",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Symbol:   "AAPL",
						Quantity: decimal.NewFromInt32(10),
						BuyTime:  time.Date(2023, 12, 15, 0, 0, 0, 0, datetime.IST),
					},
					stockEntityDetails: &tax.StockEntityDetails{
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice15Dec, dailyClosingPrice30Dec, dailyClosingPrice31Dec},
					},
					fromTime: time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
					toTime:   time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {
				exchangeRateTime := time.Date(2024, 11, 30, 0, 0, 0, 0, datetime.IST).In(time.Local)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(exchangeRateTime).Return(money.NewMoney(money.FromPaisa(80*100)), nil)
			},
			want: &moneywrapper.UsdInrWrapper{
				Usd: &moneyPb.Money{CurrencyCode: "USD", Units: 1500},
				Inr: &moneyPb.Money{CurrencyCode: "INR", Units: 120000},
			},
			wantErr: false,
		},
		{
			name: "success, peak value found for buy sell pair",
			args: args{
				req: &foreignSecurityReq{
					buySellPair: &transactionprocessor.BuySellPair{
						Symbol:         "AAPL",
						Quantity:       decimal.NewFromInt32(10),
						SellValueInUsd: &moneyPb.Money{CurrencyCode: "USD", Units: 2000},
						BuyTime:        time.Date(2024, 1, 15, 0, 0, 0, 0, datetime.IST),
						SellTime:       time.Date(2024, 10, 15, 0, 0, 0, 0, datetime.IST),
					},
					stockEntityDetails: &tax.StockEntityDetails{
						DailyClosingPrices: []*tax.DailyClosingPrice{dailyClosingPrice15Dec, dailyClosingPrice30Dec, dailyClosingPrice31Dec, dailyClosingPrice10Sept, dailyClosingPrice5May},
					},
					fromTime: time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
					toTime:   time.Date(2024, 12, 31, 0, 0, 0, 0, datetime.IST),
				},
			},
			setupMocks: func(f *fields) {
				exchangeRateTime := time.Date(2024, 8, 10, 0, 0, 0, 0, datetime.IST).In(time.Local)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(exchangeRateTime).Return(money.NewMoney(money.FromPaisa(80*100)), nil)
			},
			want: &moneywrapper.UsdInrWrapper{
				Usd: &moneyPb.Money{CurrencyCode: "USD", Units: 1800},
				Inr: &moneyPb.Money{CurrencyCode: "INR", Units: 144000},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			g := &ScheduleFaDocParamsGenerator{
				transactionProcessor:    f.transactionProcessor,
				sbiExchangeRateProvider: f.sbiExchangeRateProvider,
				usStocksCache:           f.usStocksCache,
			}
			got, err := g.getPeakValueOfInvestment(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("getPeakValueOfInvestment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("getPeakValueOfInvestment() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
