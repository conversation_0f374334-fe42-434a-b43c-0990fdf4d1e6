package config

//go:generate conf_gen github.com/epifi/gamma/usstocks/config Config

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	zincCfg "github.com/epifi/be-common/pkg/zinc/cfg"

	"github.com/epifi/gamma/api/usstocks"
	accountPb "github.com/epifi/gamma/api/usstocks/account"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	releaseCfg "github.com/epifi/gamma/pkg/feature/release/config"
	usstocksPkg "github.com/epifi/gamma/pkg/usstocks"
)

var (
	RudderWriteKey = "RudderWriteKey"
	once           sync.Once
	config         *Config
	_, b, _, _     = runtime.Caller(0)
)

func Load() (*Config, error) {
	var err error
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, configPath, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.US_STOCKS_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dynamic config: %w", err)
	}

	err = readAndSetEnv(conf)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read and set env")
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, conf.USStocksAlpacaDb)
	if err != nil {
		return nil, err
	}

	conf.CatalogSearchConfig.MappingFilePath = filepath.Clean(filepath.Join(*configPath, conf.CatalogSearchConfig.MappingFilePath))

	conf.StockUniverse, err = getStockUniverse(filepath.Clean(filepath.Join(*configPath, conf.StockUniverseFilePath)))
	if err != nil {
		return nil, errors.Wrap(err, "error while getting stock universe")
	}

	conf.SymbolToExchangeMap = make(map[string]catalogPb.Exchange, 0)
	for _, stock := range conf.StockUniverse {
		conf.SymbolToExchangeMap[stock.Symbol] = stock.Exchange
	}

	conf.MarketCalendar, err = getStockMarketCalendar(filepath.Clean(filepath.Join(*configPath, conf.MarketCalendarFilePath)))
	if err != nil {
		return nil, fmt.Errorf("error gettting stock market calendar: %w", err)
	}

	conf.ETFUniverse, err = getETFUniverse(filepath.Clean(filepath.Join(*configPath, conf.ETFUniverseFilePath)))
	if err != nil {
		return nil, errors.Wrap(err, "error while getting etf universe")
	}
	conf.SuitabilityQuestionsMap, err = getSuitabilityQuestionConfig(filepath.Clean(filepath.Join(*configPath, conf.SuitabilityQuestionConfigFilePath)))
	if err != nil {
		return nil, errors.Wrap(err, "error while getting SuitabilityQuestion config")
	}

	return conf, nil
}

func getSuitabilityQuestionConfig(configPath string) (map[accountPb.SuitabilityQuestionType]*accountPb.SuitabilityQuestions, error) {
	fp, err := os.Open(filepath.Clean(configPath))
	if err != nil {
		return nil, fmt.Errorf("error opening SuitabilityConfig file: %w", err)
	}
	defer func() {
		if err = fp.Close(); err != nil {
			logger.ErrorNoCtx("error closing SuitabilityConfig file", zap.Error(err))
		}
	}()

	suitabilityConfigBytes, err := io.ReadAll(fp)
	if err != nil {
		return nil, fmt.Errorf("error reading eligibility config data: %w", err)
	}
	var jsonMessages []*json.RawMessage
	err = json.Unmarshal(suitabilityConfigBytes, &jsonMessages)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling bytes list of raw json messages: %w", err)
	}
	suitabilityQuestionConfig := make([]*accountPb.SuitabilityQuestions, 0)
	for _, jsonMessage := range jsonMessages {
		suitabilityQuestion := &accountPb.SuitabilityQuestions{}
		err = protojson.Unmarshal(*jsonMessage, suitabilityQuestion)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling bytes to raw json messages: %w", err)
		}
		suitabilityQuestionConfig = append(suitabilityQuestionConfig, suitabilityQuestion)
	}
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling eligibility config data: %w", err)
	}
	suitabilityQuestionMap := make(map[accountPb.SuitabilityQuestionType]*accountPb.SuitabilityQuestions)
	for _, suitabilityQuestion := range suitabilityQuestionConfig {
		suitabilityQuestionMap[suitabilityQuestion.GetType()] = suitabilityQuestion
	}
	return suitabilityQuestionMap, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}
		c.Server.Ports.GrpcSecurePort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.USStocksAlpacaDb.Host = val
	}

	return nil
}

func getStockUniverse(configPath string) ([]*StockConfig, error) {
	fp, err := os.Open(filepath.Clean(configPath))
	if err != nil {
		return nil, err
	}
	defer func() {
		if err = fp.Close(); err != nil {
			logger.ErrorNoCtx("Failed to close file here", zap.Error(err))
		}
	}()

	rows, err := csv.NewReader(fp).ReadAll()
	if err != nil {
		return nil, err
	}

	// Parsing CSV
	// The first row is expected to contain headers
	// For each row,
	// First column is expected to be ticker / symbol of stock
	// Second column is expected to be the exchange the stock is listed in

	var stockUniverse []*StockConfig
	for _, row := range rows[1:] {
		if len(row) < 6 {
			return nil, fmt.Errorf("less cols in row than expected")
		}

		exchange, err := GetExchangeEnumFromCode(strings.TrimSpace(row[1]))
		if err != nil {
			return nil, err
		}

		stockType, err := GetStockTypeEnumFromCode(strings.TrimSpace(row[4]))
		if err != nil {
			return nil, err
		}

		stockConfig := &StockConfig{
			Symbol:              strings.TrimSpace(row[0]),
			Exchange:            exchange,
			LogoURL:             row[2],
			CompanyShortName:    row[3],
			CompanyStandardName: row[5],
			StockType:           stockType,
			Id:                  row[6],
		}
		stockUniverse = append(stockUniverse, stockConfig)
	}
	return stockUniverse, nil
}

func getETFUniverse(configPath string) ([]*ETFConfig, error) {
	fp, err := os.Open(filepath.Clean(configPath))
	if err != nil {
		return nil, err
	}
	defer func() {
		if err = fp.Close(); err != nil {
			logger.ErrorNoCtx("Failed to close file here", zap.Error(err))
		}
	}()

	rows, err := csv.NewReader(fp).ReadAll()
	if err != nil {
		return nil, err
	}

	// Parsing CSV
	// The first row is expected to contain headers
	// For each row,
	// First column is expected to be MStarID
	// Second column is expected to be ticker / symbol of stock
	// Third column is expected to be the exchange id

	var etfUniverse []*ETFConfig
	for _, row := range rows[1:] {
		if len(row) < 3 {
			return nil, fmt.Errorf("less cols in row than expected")
		}

		etfConfig := &ETFConfig{
			MStarID:    strings.TrimSpace(row[0]),
			Ticker:     row[1],
			ExchangeID: GetExchangeEnumFromIdForETF(strings.TrimSpace(row[2])),
		}
		etfUniverse = append(etfUniverse, etfConfig)
	}
	return etfUniverse, nil
}

func GetExchangeEnumFromIdForETF(exchangeId string) catalogPb.Exchange {
	switch exchangeId {
	case "EX$$$$XNAS":
		return catalogPb.Exchange_EXCHANGE_NAS
	case "EX$$$$ARCX":
		return catalogPb.Exchange_EXCHANGE_ARCA
	case "EX$$$$BATS":
		return catalogPb.Exchange_EXCHANGE_BATS
	default:
		return catalogPb.Exchange_EXCHANGE_UNSPECIFIED
	}
}

func GetStockTypeEnumFromCode(stockType string) (catalogPb.StockType, error) {
	switch stockType {
	case "ETF":
		return catalogPb.StockType_STOCK_TYPE_ETF, nil
	case "COMPANY":
		return catalogPb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY, nil
	default:
		return 0, fmt.Errorf("no StockType enum found for %s", stockType)
	}
}

func GetExchangeEnumFromCode(exchangeCode string) (catalogPb.Exchange, error) {
	switch exchangeCode {
	case "NYS", "NYSE":
		return catalogPb.Exchange_EXCHANGE_NYSE, nil
	case "NAS", "NASDAQ":
		return catalogPb.Exchange_EXCHANGE_NAS, nil
	case "ASE", "AMEX":
		return catalogPb.Exchange_EXCHANGE_ASE, nil
	case "ARCX", "ARCA":
		return catalogPb.Exchange_EXCHANGE_ARCA, nil
	case "BATS":
		return catalogPb.Exchange_EXCHANGE_BATS, nil
	case "PINX":
		return catalogPb.Exchange_EXCHANGE_PINX, nil
	case "OTC":
		return catalogPb.Exchange_EXCHANGE_OTC, nil

	default:
		return 0, fmt.Errorf("no exchange enum found for %s", exchangeCode)
	}
}

func GetExchangeCodeFromEnum(exchangeEnum catalogPb.Exchange) (string, error) {
	switch exchangeEnum {
	case catalogPb.Exchange_EXCHANGE_NYSE:
		return "NYS", nil
	case catalogPb.Exchange_EXCHANGE_NAS:
		return "NAS", nil
	case catalogPb.Exchange_EXCHANGE_ASE:
		return "ASE", nil
	case catalogPb.Exchange_EXCHANGE_ARCA:
		return "ARCX", nil
	case catalogPb.Exchange_EXCHANGE_BATS:
		return "BATS", nil
	case catalogPb.Exchange_EXCHANGE_PINX:
		return "PINX", nil
	case catalogPb.Exchange_EXCHANGE_OTC:
		return "OTC", nil

	default:
		return "", fmt.Errorf("no exchange code found for %s", exchangeEnum.String())
	}
}

// nolint:funlen
func getStockMarketCalendar(configPath string) ([]*MarketDay, error) {
	fp, err := os.Open(filepath.Clean(configPath))
	if err != nil {
		return nil, fmt.Errorf("error opening market calendar file: %w", err)
	}
	defer func() {
		if err = fp.Close(); err != nil {
			logger.ErrorNoCtx("error closing market calendar file", zap.Error(err))
		}
	}()

	calendarDataBytes, err := io.ReadAll(fp)
	if err != nil {
		return nil, fmt.Errorf("error reading market calendar data: %w", err)
	}

	type marketTimings struct {
		Date string `json:"date"`

		// Time at which regular trading session opens in HH:MM format
		Open string `json:"open"`

		// Time at which regular trading session closes in HH:MM format
		Close string `json:"close"`

		// Time at which pre-market trading session opens in HHMM format
		SessionOpen string `json:"session_open"`

		// Time at which after-market trading session closes in HHMM format
		SessionClose string `json:"session_close"`
	}
	var marketTimingsCalendar []*marketTimings
	err = json.Unmarshal(calendarDataBytes, &marketTimingsCalendar)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling market calendar data: %w", err)
	}

	var marketCalendar []*MarketDay
	for _, dayTimings := range marketTimingsCalendar {
		marketDate := datetime.DateFromString(dayTimings.Date)
		openHour, openMin, parseErr := datetime.GetHourAndMinute(dayTimings.Open)
		if parseErr != nil {
			return nil, fmt.Errorf("error getting hour and minute from open time: %w", parseErr)
		}
		closeHour, closeMin, parseErr := datetime.GetHourAndMinute(dayTimings.Close)
		if parseErr != nil {
			return nil, fmt.Errorf("error getting hour and minute from close time: %w", parseErr)
		}
		if len(dayTimings.SessionOpen) < 4 {
			return nil, fmt.Errorf("unexpected format for pre-market trading open time")
		}
		preMarketOpenHour, preMarketOpenMin, parseErr := datetime.GetHourAndMinute(dayTimings.SessionOpen[:2] + ":" + dayTimings.SessionOpen[2:])
		if parseErr != nil {
			return nil, fmt.Errorf("error getting hour and minute from pre-market open time: %w", parseErr)
		}
		if len(dayTimings.SessionClose) < 4 {
			return nil, fmt.Errorf("unexpected format for after-market trading close time")
		}
		afterMarketCloseHour, afterMarketCloseMin, parseErr := datetime.GetHourAndMinute(dayTimings.SessionClose[:2] + ":" + dayTimings.SessionClose[2:])
		if parseErr != nil {
			return nil, fmt.Errorf("error getting hour and minute from after-market close time: %w", parseErr)
		}
		marketTimingsForDay := &MarketTimings{
			RegularTradingOpenAt:      convertToMarketTime(marketDate, openHour, openMin),
			RegularTradingCloseAt:     convertToMarketTime(marketDate, closeHour, closeMin),
			PreMarketTradingOpenAt:    convertToMarketTime(marketDate, preMarketOpenHour, preMarketOpenMin),
			AfterMarketTradingCloseAt: convertToMarketTime(marketDate, afterMarketCloseHour, afterMarketCloseMin),
		}
		marketCalendar = append(marketCalendar, &MarketDay{Date: convertToMarketTime(marketDate, 0, 0), MarketTimings: marketTimingsForDay})
	}
	sort.SliceStable(marketCalendar, func(i, j int) bool {
		return marketCalendar[i].Date.Before(marketCalendar[j].Date)
	})
	return marketCalendar, nil
}

// GetMarketDaysForTimeRange returns the list of market days
// in the range [start time's date in datetime.EST5EDT, end time's date in datetime.EST5EDT] (both inclusive)
// as long as start >= Year 2015 and end <= Year 2029
// If start time is before 2015 or end time is after 2029, error is thrown
// List returned will never be empty
func GetMarketDaysForTimeRange(start, end time.Time) ([]*MarketDay, error) {
	start = start.In(datetime.EST5EDT)
	end = end.In(datetime.EST5EDT)
	if start.IsZero() && end.IsZero() {
		return config.MarketCalendar, nil
	}
	var lo, hi int
	if !start.IsZero() {
		startDay := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, datetime.EST5EDT)
		lo = sort.Search(len(config.MarketCalendar), func(i int) bool {
			// considers date only for returning market days; timestamp is not considered
			// eg: for startTime := 20 June, 11:00 in EST5EDT, returns 20 June onwards if it is a market day
			return config.MarketCalendar[i].Date.Equal(startDay) || config.MarketCalendar[i].Date.After(startDay)
		})
		if lo == len(config.MarketCalendar) {
			return nil, fmt.Errorf("no market day found on or before: %s", start.String())
		}
		if end.IsZero() {
			return config.MarketCalendar[lo:], nil
		}
	}
	if !end.IsZero() {
		endDay := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, datetime.EST5EDT)
		hi = sort.Search(len(config.MarketCalendar), func(i int) bool {
			// considers date only for returning market days; timestamp is not considered
			// eg: for endTime := 20 June, 11:00 in EST5EDT, returns till 20 June if it is a market day
			return config.MarketCalendar[i].Date.After(endDay)
		})
		if hi == 0 {
			return nil, fmt.Errorf("no market day found after: %s", end.String())
		}
		if start.IsZero() {
			return config.MarketCalendar[:hi], nil
		}
	}
	return config.MarketCalendar[lo:hi], nil
}

func convertToMarketTime(date *date.Date, hour, min int) time.Time {
	return time.Date(int(date.GetYear()), time.Month(date.GetMonth()), int(date.GetDay()), hour, min, 0, 0, datetime.EST5EDT)
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	Application                                    *Application
	Server                                         *Server
	Flags                                          *Flags `dynamic:"true"`
	Logging                                        *cfg.Logging
	USStocksAlpacaDb                               *cfg.DB
	AWS                                            *cfg.AWS
	RudderStack                                    *cfg.RudderStackBroker
	Secrets                                        *cfg.Secrets
	Tracing                                        *cfg.Tracing
	ProcessUSStockCatalogUpdateSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	ProcessUSEtfCatalogUpdateSubscriber            *cfg.SqsSubscriber `dynamic:"true"`
	USStockCatalogRefreshPublisher                 *cfg.SqsPublisher
	USStockCatalogRefreshSubscriber                *cfg.SqsSubscriber `dynamic:"true"`
	ProcessOrderUpdateEventSubscriber              *cfg.SqsSubscriber `dynamic:"true"`
	FetchReverseFeedFileSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	AmlActionEventSubscriber                       *cfg.SqsSubscriber `dynamic:"true"`
	CelestialWorkflowUpdateSubscriber              *cfg.SqsSubscriber `dynamic:"true"`
	ProcessAccountActivitySyncSubscriber           *cfg.SqsSubscriber `dynamic:"true"`
	USStocksTaxDocumentGenerationRequestSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	ProcessAccountActivitySyncPublisher            *cfg.SqsPublisher
	CatalogS3Conf                                  *CatalogS3Conf
	AccountManagerConfig                           *AccountManagerConfig `dynamic:"true"`
	VendorActorId                                  map[usstocks.Vendor]string
	MaxPageSize                                    uint32 `dynamic:"true"`
	// page size of aggregate remittance transaction dao
	AggRemittanceTxnsMaxPageSize uint32
	// PriceUpdatesConnectionInfo defines details required to connect with vendor for price updates
	// It also defines retry strategy for connection reattempts
	PriceUpdatesConnectionInfo *PriceUpdatesConnectionInfo `dynamic:"true"`
	// OrderUpdateEventsConnectionInfo defines details required to connect with vendor for order update Events
	// It also defines retry strategy for connection reattempts
	OrderUpdateEventsConnectionInfo *OrderUpdateEventsConnectionInfo `dynamic:"true"`
	// AccountUpdateEventsConnectionInfo defines details required to connect with vendor for account update Events
	// It also defines retry strategy for connection reattempts
	AccountUpdateEventsConnectionInfo *AccountUpdateEventsConnectionInfo `dynamic:"true"`
	// Represent logic for calculation of tax
	TaxCalculationLogic *TaxCalculationLogic
	// liveness s3 bucket name, needed to send s3 path for face match api
	LivenessS3BucketName string
	FaceMatchThreshold   float32
	CatalogSearchConfig  *CatalogSearchConfig `dynamic:"true"`
	Zinc                 *zincCfg.Zinc

	AccountActivitySyncConfig *AccountActivitySyncConfig `dynamic:"true"`
	// rate limit config for sending defined number of updates for a symbol to BE service
	// since price of stock changes at every trade, there will be multiple updates received within a second
	// getting updates for all the trades is not required for our use-case
	// config is defined per symbol level
	// eg: 1 update per second per symbol
	PriceUpdateRateLimiterConfig *cfg.RateLimitConfig `dynamic:"true"`

	// Help in deciding whether to use sell Lock or not
	ShouldUseSellLock bool `dynamic:"true"`

	CommsNotification *Notifications `dynamic:"true"`

	// Path to file containing all the stock symbols we are interested in
	StockUniverseFilePath string `dynamic:"true"`

	// Path to file containing all the ETFs tickers we are interested in
	ETFUniverseFilePath string `dynamic:"true"`

	// Path to file containing all the ETFs tickers we are interested in
	SuitabilityQuestionConfigFilePath string `dynamic:"true"`

	// list of stock symbols, exchanges and other static info to identify a stock that is supported
	// populated when config is loaded (via reading a CSV file)
	StockUniverse []*StockConfig

	// this map help in mapping symbol to corresponding exchange
	SymbolToExchangeMap map[string]catalogPb.Exchange

	// TODO(satyam): remove this hardcoded logic when we have moved to serving deeplinks from workflow
	IsValidConnectedAccount bool `dynamic:"true"`

	// contains from_email_id and from_email_name details by which mail would send to users for daily trade confirmation and monthly account statement
	CommsEmail *Email `dynamic:"true"`

	// refer to batch size of users for sending email to multiple users
	EmailBatchSize int `dynamic:"true"`

	// Path to file containing market calendar data
	MarketCalendarFilePath string

	// represent Pool Account for inward remittance
	// not making dynamic since it is financial data and less chance to change
	InwardPoolAccountNumber string

	// represent sol id for Pool Account for inward remittance
	InwardsRemittancePoolAccountSolId string

	// Market days sorted from 2015 onwards till 2029
	// data from before 2015 is not stored because historical price data is only needed for 5 years (at max) from today
	MarketCalendar       []*MarketDay
	USStocksRedisOptions *cfg.RedisOptions

	// Configurations for historical stock prices
	HSPConfig []*HSPConfigForPeriod

	SkipPanValidation bool `dynamic:"true"`

	// represent instant purchase amount
	InstantPurchaseAmount int64 `dynamic:"true"`

	// represents max batch size for
	// 1. bulk inserting account activities in DB while generating the inward fund transfer file contents
	// 2. bulk updating account activities in DB while acknowledging inward fund transfer file
	MaxBatchSizeToCreateOrUpdateAccountActivities int `dynamic:"true"`

	UseVendorAPIForExchangeStatus bool `dynamic:"true"`

	UsStocksSendMailToUsersPublisher  *cfg.SqsPublisher
	UsStocksSendMailToUsersSubscriber *cfg.SqsSubscriber `dynamic:"true"`

	NonFinancialEventSqsPublisher *cfg.SqsPublisher
	MorningStarS3Bucket           *MorningStarS3Bucket `dynamic:"true"`
	IsETFEnabled                  bool                 `dynamic:"true"`
	ETFUniverse                   []*ETFConfig
	SuitabilityQuestionsMap       map[accountPb.SuitabilityQuestionType]*accountPb.SuitabilityQuestions
	// details of Fi's accounts present at US Stocks broker for performing foreign remittances
	BrokerFirmAccountDetailsForForeignRemittance        *usstocksPkg.BrokerFirmAccountDetailsForForeignRemittance `dynamic:"true"`
	USStocksIFTRemittanceFileProcessingEventsSubscriber *cfg.SqsSubscriber                                        `dynamic:"true"`
	USStocksIncomeUpdateEventsSubscriber                *cfg.SqsSubscriber                                        `dynamic:"true"`
	// AddFundsPreReqChecksData holds data required to perform pre-requisite checks for a add funds order
	AddFundsPreReqChecksData *AddFundsPreReqChecksData `dynamic:"true"`
	// DayTradesLimit defines max number of day trade orders a user a can place
	// eg: 4 day trades in 5 days
	// 5th order will be rejected
	DayTradesLimit uint32          `dynamic:"true"`
	WalletOrderEta *WalletOrderEta `dynamic:"true"`
	// defines details required to connect with vendor for journal update Events
	JournalUpdateEventsConnectionInfo *SSEUpdateEventsConnectionInfo `dynamic:"true"`
	// defines details required to connect with vendor for fundTransfer update Events
	FundTransferUpdateEventsConnectionInfo *SSEUpdateEventsConnectionInfo `dynamic:"true"`
	// IgnoreCanceledTxnsForInwardRemittance set then cancel state will be ignore in details.csv
	IgnoreCanceledTxnsForInwardRemittance bool `dynamic:"true"`
	// pgdb migration related configs
	PgdbMigrationConf *cfg.PgdbConn `dynamic:"true"`
	// enable of sync ingestion in zinc
	EnableZincIngestionInSync          bool                                `dynamic:"true"`
	GetStocksPriceSnapshotsBatchConfig *GetStocksPriceSnapshotsBatchConfig `dynamic:"true"`
	// rewards related config details
	USStocksRewardDetails *USStocksRewardDetails `dynamic:"true"`
	// config for us market indices
	MarketIndexConfig map[string]*MarketIndexConfig `dynamic:"true"`
	// HomeUsStocksCollection controls whether or not to show a us stocks collection list in home
	// If yes, it also specifies which collection to show
	HomeUsStocksCollection *HomeUsStocksCollection `dynamic:"true"`
	// sbi buy exchange rate map for a given month
	// key follows the structure MM-YYYY
	SBIMonthlyBuyExchangeRate map[string]float64 `dynamic:"true"`
	// VendorAccountActivitiesBucketName is the s3 bucket where raw vendor activities are written as csv
	VendorAccountActivitiesBucketName string `iam:"s3-readwrite"`

	// List of actor IDs whose savings account can not be credited
	// and thus should not be allowed to withdraw.
	CreditFrozenActorIds []string `dynamic:"true"`

	MinAndroidVersionToSupportTabbedCard int `dynamic:"true"`
	MinIOSVersionToSupportTabbedCard     int `dynamic:"true"`
	// financial year to cost inflation index value map
	// key follows pattern YYYY-YYYY
	CostInflationIndexMap map[string]int `dynamic:"true"`
	// Brokerage stores configuration related to commission that will be charged from user's wallet on trade orders
	Brokerage                   *usstocksPkg.Brokerage `dynamic:"true"`
	TaxDocumentGenerationParams *TaxDocumentGenerationParams
	// FeatureReleaseConfig handles controlled rollout of different features
	FeatureReleaseConfig *releaseCfg.FeatureReleaseConfig `dynamic:"true"`

	// Slack channel to alert US stocks ops
	UsStocksOpsAlertSlackChannelId string `dynamic:"true"`

	// s3 bucket to store us stocks tax related documents
	TaxDocumentsBucketName string `iam:"s3-readwrite"`

	// Android had an issue due to which UI was breaking in case of empty response for tabbed section dynamic element
	// more context: https://epifi.slack.com/archives/C063D6AHV1N/p1716881401216549
	// // MinIOSVersionWithSupportForEmptyTabbedSection is the app version after which the issue was fixed on Android
	MinAndroidVersionWithSupportForEmptyTabbedSection int `dynamic:"true"`

	// IOS had an issue due to which UI was breaking in case of empty response for tabbed section dynamic element
	// MinIOSVersionWithSupportForEmptyTabbedSection is the app version after which the issue was fixed on IOS
	MinIOSVersionWithSupportForEmptyTabbedSection int `dynamic:"true"`
	// AuthFactorUpdateCoolOffPeriod determines the time duration till which add funds and withdrawals will be blocked post
	// an auth factor update for the user
	AuthFactorUpdateCoolOffPeriod time.Duration `dynamic:"true"`

	// minimum android version which contains fix for direct to add fund flow client polling in android
	MinAndroidAppVersionToDirectToAddFundRetryBugFix uint32 `dynamic:"true"`

	// flag to enable/disable new db based stock universe approach
	IsNewStockUniverseEnabled bool `dynamic:"true"`

	PortfolioConfig *PortfolioConfig `dynamic:"true"`
	// from this time, indexation benefit will not be applicable on long term orders
	IndexationBenefitNotApplicableFrom time.Time `dynamic:"true"`
}

type PortfolioConfig struct {
	FilteredSymbols       []string `dynamic:"true"`
	EnableSymbolFiltering bool     `dynamic:"true"`
}

type HomeUsStocksCollection struct {
	IsHomeUsStocksCollectionReleased bool   `dynamic:"true"`
	CollectionId                     string `dynamic:"true"`
}

type USStocksRewardDetails struct {
	// Note:  If Option1StockName and/or Option2StockName is changed, BgLottieUrl should also be changed to have animation for corresponding stocks only

	// name of the first stock option for rewards
	Option1StockName string `dynamic:"true"`
	// name of the second stock option for rewards
	Option2StockName string `dynamic:"true"`
	// amount value of stock that will be offered as reward to user
	Amount *moneyPb.Money
	// lottie url consisting flip animation for Option1StockName & Option2StockName
	BgLottieUrl string `dynamic:"true"`
}

// GetStocksPriceSnapshotsBatchConfig configures batch request to vg GetStocksPriceSnapshots rpc
type GetStocksPriceSnapshotsBatchConfig struct {
	// Max number of symbols to get price snapshots for in a single API call to market data provider
	// Note: Batch size affects the number of API calls required to get prices of all stocks.
	// and can indirectly impact the time taken to execute full-catalog stock price refreshes.
	BatchSize int `dynamic:"true"`

	// Approximate wait time between two API calls
	JitterSeconds int `dynamic:"true"`
}

type MorningStarS3Bucket struct {
	BucketName string `dynamic:"true" iam:"s3-readwrite"`
}

type PriceUpdatesConnectionInfo struct {
	// If set to true, worker to get price updates will be started
	// Can be disabled for faster server start-up time and lesser dependency during development and remote-debugging.
	// These prices are also watched upon and acted on.
	// E.g., stock prices are updated in cache and prices are streamed to subscribed clients.
	Enable bool `dynamic:"true"`
	// In case of connection failure, VGConnectionRetryInterval is the interval after which VG connection will be reattempted
	VGConnectionRetryInterval time.Duration `dynamic:"true"`
	// VGConnectionMaxRetries defines max allowed retry attempts
	VGConnectionMaxRetries int `dynamic:"true"`
}

type OrderUpdateEventsConnectionInfo struct {
	// if set to true, worker to get order update events will be started
	// Can be disabled for faster server start-up time and lesser dependency during development and remote-debugging.
	Enable bool `dynamic:"true"`
	// In case of connection failure, VGConnectionRetryInterval is the interval after which VG connection will be reattempted
	VGConnectionRetryInterval time.Duration `dynamic:"true"`
	// VGConnectionMaxRetries defines max allowed retry attempts
	VGConnectionMaxRetries int `dynamic:"true"`
}

type SSEUpdateEventsConnectionInfo struct {
	// if set to true, worker will start receiving journal update events
	// Can be disabled for faster server start-up time and lesser dependency during development and remote-debugging.
	Enable bool `dynamic:"true"`
	// In case of connection failure, VGConnectionRetryInterval is the interval after which VG connection will be reattempted
	VGConnectionRetryInterval time.Duration `dynamic:"true"`
	// VGConnectionMaxRetries defines max allowed retry attempts
	VGConnectionMaxRetries int `dynamic:"true"`
}

type AccountUpdateEventsConnectionInfo struct {
	// if set to true, worker to get account update events will be started
	// Can be disabled for faster server start-up time and lesser dependency during development and remote-debugging.
	Enable bool `dynamic:"true"`
	// In case of connection failure, VGConnectionRetryInterval is the interval after which VG connection will be reattempted
	VGConnectionRetryInterval time.Duration `dynamic:"true"`
	// VGConnectionMaxRetries defines max allowed retry attempts
	VGConnectionMaxRetries int `dynamic:"true"`
}

type AddFundsPreReqChecksData struct {
	Enabled bool `dynamic:"true"`
	// Maximum allowed amount for one order in USD
	// eg: $1200
	MaxAllowedAmountForAnOrder *moneyPb.Money
	// Minimum allowed amount for one order in USD
	// eg: $10
	MinAllowedAmountForAnOrder *moneyPb.Money
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool

	// Contract with morningstar is ending for companies, disabling all their data usages as per contract
	// Company short name, logo and standard name are populated from stock universe CSV
	DisableMorningStarDataUsages bool `dynamic:"true"`

	// if enabled, inward remittance transactions are aggregated per user to reduce tax charged
	// if disabled, inward remittances transactions are not aggregated (trades for a sell-order are still aggregated)
	EnableAggregatedInwardRemittance bool `dynamic:"true"`

	// If true, custom account activities corresponding to dividends need to be created in DB
	// This might be required if account activities are not being synced from vendor within our DB
	CreateCustomDividendActivities bool `dynamic:"true"`

	// If true, TCS charges are calculated by partner bank. Else we calculate it internally based on RBI regulations
	UseBankApiForTcsCalculation bool `dynamic:"true"`

	// If true, catalog data is synced from primary DB (e.g., Postgres) to search-oriented DB (e.g., Zinc) for
	// search queries over catalog data.
	// Catalog search feature requires additional dependency on Zinc.
	// Hence, it can be disabled for faster server start-up time and lesser dependency
	// during development and remote-debugging.
	// Whenever working on search feature, please enable it.
	EnableCatalogSearchSync bool `dynamic:"true"`

	// If true, a user on whose savings account we are planning to freeze credit will not be able to
	// transfer funds from their wallet to their partner bank account.
	// This should be turned ON only after we have a complete SOP on how to handle
	// accounts being planned to be frozen after a withdrawal has been initiated (but not completed) by investor.
	EnableImpendingSavingsAccountFreezeValidation bool `dynamic:"true"`

	// If true, transaction remarks for crediting bank account in INR
	// from US stock withdrawals are enriched with details like exchange rate and USD amount
	// for helping partner bank with their CBWT reporting.
	// Also applies to GST debits from the user's bank account.
	EnableModifiedTransactionRemarkForInwardRemittance bool `dynamic:"true"`

	// If true, the inward remittance GST reporting workflow will be initiated.
	// This will start the batch workflow for GST reporting to the partner bank, corresponding to the given TTUM.
	EnableInwardRemittanceGstReportingFlowUsingApi bool `dynamic:"true"`

	// flag to enable separate ttum file for inward fund transfer
	// this will create two different ttum file for user transaction and tax transaction
	EnableSeparateTTUMFileChanges bool `dynamic:"true"`
}

type CatalogS3Conf struct {
	BucketName string `iam:"s3-readwrite"`
}

type AccountManagerConfig struct {
	KycDocumentsBucketName              string `iam:"s3-readwrite"`
	W8BenFormVersion                    string `dynamic:"true"`
	MaxRetriesToInitiateAccountCreation int
}

type TaxCalculationLogic struct {
	TCSCalculationLogic *TCSCalculationLogic
}

// Note: Struct should be modified to support multiple slabs if needed in future
type TCSCalculationLogic struct {
	// The maximum INR amount above which user is charged TCS
	TCSSlabAmount *moneyPb.Money

	// Percentage of INR amount to be charged as TCS
	TCSPercentage float32
}

type CatalogSearchConfig struct {
	IndexName                   string
	MappingFilePath             string
	CatalogFetchDefaultPageSize int32 `dynamic:"true"`
}

type Notifications struct {
	NotificationIconURL string              `dynamic:"true"`
	BuyOrderCancelled   *NotificationParams `dynamic:"true"`
	SellOrderCancelled  *NotificationParams `dynamic:"true"`
}

type NotificationParams struct {
	Title string `dynamic:"true"`
	Body  string `dynamic:"true"`
}

type StockConfig struct {
	Id       string
	Symbol   string
	Exchange catalogPb.Exchange

	// Note: If a stock doesn't have a logo in CSV file, this will be blank
	LogoURL string

	// Note: If a stock doesn't custom company name in CSV file, this will be blank
	CompanyShortName string

	// Note: If a stock doesn't company standard name in CSV file, this will be blank
	CompanyStandardName string

	StockType catalogPb.StockType
}

type ETFConfig struct {
	// MStarID is a unique id provided by morningstar for a symbol/ticker and exchange
	MStarID string
	// Ticker is a symbol for identify any ETF, eg: VOO, AAXJ
	Ticker string
	// ExchangeID is a unique id for each exchange provided by morningstar, eg: EX$$$$XNAS, EX$$$$ARCX for NASDAQ and ARCA/ARCX exchange
	ExchangeID catalogPb.Exchange
}

type AccountActivitySyncConfig struct {
	IsEnabled bool `dynamic:"true"`

	// Total number of account activities to fetch from stockbroker's API for syncing
	PageSize uint32 `dynamic:"true"`

	// The average duration to query stockbroker with, to check if there are any upcoming account activities to sync
	Duration time.Duration `dynamic:"true"`

	// The timestamp of the first account activity with stockbroker
	// This is used as a fallback when activities are being synced from the beginning in an empty table
	FirstActivityTs int64 `dynamic:"true"`

	// List of Epifi (firm) account IDs
	FirmAccountIDs []string
}

type Email struct {
	FromEmailId *EmailDetails `dynamic:"true"`
	// keep alpaca email id in bcc for all daily trade confirmation and monthly account statement
	// If new vendor is introduced, another field needs to be added and switch case to be added to use vendor specific bcc email Id
	// TODO(satyam.j): use a map `bccMap[Vendor]*EmailDetails` for handling new vendor addition
	AlpacaEmail *EmailDetails `dynamic:"true"`
}

type EmailDetails struct {
	EmailId   string `dynamic:"true"`
	EmailName string `dynamic:"true"`
}

// MarketTimings represents market open and closing times in datetime.EST5EDT
// More on trading day: https://en.wikipedia.org/wiki/Trading_day
// More on extended trading hours: https://en.wikipedia.org/wiki/Extended-hours_trading
type MarketTimings struct {
	RegularTradingOpenAt  time.Time
	RegularTradingCloseAt time.Time

	// Time at which pre-market trading session opens
	PreMarketTradingOpenAt time.Time

	// Time at which after-market trading session closes
	AfterMarketTradingCloseAt time.Time
}

// MarketDay represents a day when stock market was open
type MarketDay struct {
	// Note: Date represents midnight datetime.EST5EDT of a market day
	// It will have non-zero values for year, month and day only when represented in datetime.EST5EDT
	Date time.Time

	// market timings for Date
	MarketTimings *MarketTimings
}

type HSPConfigForPeriod struct {
	// e.g. 1D, 1W, 1M, 1Y, 5Y
	PeriodName string

	// e.g. 1Min, 5Min, 30Min, 1Day, 1Week
	AggregationTimeFrame string

	// same as AggregationTimeFrame but in time.Duration
	AggregationTimeFrameDuration time.Duration

	// O-based indexing to decide display order of time-periods
	DisplayRank int

	// Flag to decide whether to remove stock prices outside regular market hours
	// Stock prices aggregated at a daily level will have timestamp as 00:00:00 in EST5EDT, hence we shouldn't remove those
	// This flag helps to keep these daily data points from being removed
	RemoveNonRegularMarketHourPrices bool

	// If set to true, stock prices are expected to be updated real-time from price-update streaming API by end-user devices
	ShouldUpdateRealtime bool
}

type WalletOrderEta struct {
	// sell orders may take around 5 days for amount to hit user's bank account
	// WithdrawFundsPaymentSettlement defines no of days for which user has to wait to expect money to hit his/her savings account after order is fulfilled by vendor
	WithdrawFundsPaymentSettlement int `dynamic:"true"`
	// send money to broker may take a day after order is fulfilled by broker
	// AddFundsPaymentSettlement defines no of days for which money is expected to be sent to broker
	AddFundsPaymentSettlement int `dynamic:"true"`
}

type MarketIndexConfig struct {
	DisplayName string
	// Most liquid etf symbol which tracks the market index
	ProxyETF string
	// symbols of stocks which constitutes the index
	ConstituentSymbols []string `dynamic:"true"`
}

type TaxDocumentGenerationParams struct {
	// map of corporate actions to be overridden for a stock
	// key is the stock symbol
	// value is the collection of overridden corporate actions for the stock
	CorporateActionsOverride map[string]*StockCorporateActions
}

type StockCorporateActions struct {
	// map of corporate actions on a stock in a day
	// key is the date as string Eg 2023-05-07
	// value is the collection of corporate actions for the date
	DateToCorporateActionsMap map[string]*vgStocksPb.CorporateActions
}
