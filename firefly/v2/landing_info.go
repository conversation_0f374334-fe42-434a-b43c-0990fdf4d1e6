package v2

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	firefly2 "github.com/epifi/gamma/pkg/firefly"

	"go.uber.org/zap"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
)

func (s *Service) GetLandingInfo(ctx context.Context, req *ffBeV2Pb.GetLandingInfoRequest) (*ffBeV2Pb.GetLandingInfoResponse, error) {
	var (
		res = &ffBeV2Pb.GetLandingInfoResponse{}
	)

	savedCards, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId())
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error while fetching credit cards for actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if len(savedCards) > 0 {
		for _, card := range savedCards {
			if card.GetState() == ccEnumsV2Pb.CardState_CARD_STATE_CREATED {
				res.NextAction = &dlPb.Deeplink{
					Screen: dlPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN_V2,
				}
				res.Status = rpc.StatusOk()
				return res, nil
			}
		}

		res.Status = rpc.StatusOk()
		res.NextAction = s.getIntroScreenV2Deeplink(savedCards[0].GetState(), ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED)
		return res, nil
	}

	onboardingRequests, err := s.cardRequestDao.GetByActorIdAndRequestType(ctx, req.GetActorId(), ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			res.NextAction = s.getIntroScreenV2Deeplink(ccEnumsV2Pb.CardState_CARD_STATE_UNSPECIFIED, ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED)
			res.Status = rpc.StatusOk()
			return res, nil
		}
		logger.Error(ctx, "error while fetching cc onboarding requests for actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	latestOnboardingRequest := onboardingRequests[0]
	switch latestOnboardingRequest.GetStatus() {
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		res.NextAction = &dlPb.Deeplink{
			Screen: dlPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN_V2,
		}
		res.Status = rpc.StatusOk()
		return res, nil
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED, ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		res.NextAction = s.getIntroScreenV2Deeplink(ccEnumsV2Pb.CardState_CARD_STATE_UNSPECIFIED, latestOnboardingRequest.GetStatus())
		res.Status = rpc.StatusOk()
		return res, nil
	default:
		logger.Error(ctx, "unexpected onboarding request status",
			zap.String(logger.STATUS, latestOnboardingRequest.GetStatus().String()),
			zap.String(logger.REQUEST_ID, latestOnboardingRequest.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
}

func (s *Service) getIntroScreenV2Deeplink(cardState ccEnumsV2Pb.CardState, onbReqStatus ccEnumsV2Pb.CardRequestStatus) *dlPb.Deeplink {
	metadata := &firefly.CreditCardMetadata{
		Metadata: &firefly.CreditCardMetadata_IntroScreenV2Metadata{
			IntroScreenV2Metadata: &firefly.IntroScreenV2Metadata{
				CardState:               cardState.String(),
				OnboardingRequestStatus: onbReqStatus.String(),
			},
		},
	}

	screenOption := &firefly.CcIntroScreenV2ScreenOptions{
		LoaderAnimation: common.GetVisualElementLottieFromUrlHeightAndWidth(s.conf.IntroScreenV2LoaderAnimation(), s.conf.IntroScreenV2LoaderAnimationHeight(), s.conf.IntroScreenV2LoaderAnimationWidth()),
		BgImage:         common.GetVisualElementImageFromUrl(s.conf.IntroScreenV2BgImage()),
		Metadata:        firefly2.GetCardMetadataString(metadata),
	}

	return &dlPb.Deeplink{
		Screen:          dlPb.Screen_CC_INTRO_SCREEN_V2,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(screenOption),
	}
}
