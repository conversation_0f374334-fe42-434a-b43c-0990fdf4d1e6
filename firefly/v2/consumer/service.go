package consumer

import (
	"context"
	"fmt"

	queuePb "github.com/epifi/be-common/api/queue"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffConsumerV2Pb "github.com/epifi/gamma/api/firefly/v2/consumer"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/firefly/v2/dao"
)

var (
	// this map is used to map the card request status to current card request stage status in cc onboarding event
	// we get the event only for stage transition, i.e. when current stage move to success and next stage is initiated
	onbCardReqStatusToOnbCurrentStageStatusMap = map[ccEnumsV2Pb.CardRequestStatus]ccEnumsV2Pb.CardRequestStageState{
		ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS: ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS,
		ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:     ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS,
		ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:      ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_FAILED,
	}
)

type Service struct {
	cardRequestDao dao.CardRequestDao
	creditCardDao  dao.CreditCardDao
}

func NewService(cardRequestDao dao.CardRequestDao, creditCardDao dao.CreditCardDao) *Service {
	return &Service{
		cardRequestDao: cardRequestDao,
		creditCardDao:  creditCardDao,
	}
}

func (s *Service) ProcessCreditCardOnboardingStateUpdateEvent(ctx context.Context, req *ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventRequest) (*ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventResponse, error) {
	if err := s.validateCcOnbStageUpdateEventReq(req); err != nil {
		logger.Error(ctx, "invalid credit card onboarding stage update event request", zap.Error(err))
		return transientFailureResponse(), nil
	}

	// Get latest onboarding request
	latestCcOnbRequest, err := s.getLatestOnboardingRequestForExternalUserId(ctx, req.GetExternalUserId())
	if err != nil {
		logger.Error(ctx, "error fetching latest credit card onboarding request", zap.Error(err))
		return transientFailureResponse(), nil
	}
	// check if current stage in the event was already initiated,
	// if not that indicates that, some previous event has yet not been processed,
	// in which case we'll throw and wait for the previous event to be processed first,
	// current event should get processed via retry logic.
	if err = s.currentOnbStageStateValidation(latestCcOnbRequest, req.GetStageTransitionInfo().GetCurrentStage()); err != nil {
		logger.Error(ctx, "current credit card onboarding stage validation failed", zap.Error(err),
			zap.String(logger.REQUEST_ID, latestCcOnbRequest.GetId()))
		return transientFailureResponse(), nil
	}

	switch latestCcOnbRequest.GetStatus() {
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		logger.Info(ctx, "credit card onboarding request is already in success state")
		err = s.handleCcOnboardingReqTerminalStatus(ctx, latestCcOnbRequest)
		if err != nil {
			logger.Error(ctx, "error handling credit card onboarding request terminal status", zap.Error(err))
			return transientFailureResponse(), nil
		}
		return successResponse(), nil

	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		logger.Info(ctx, "credit card onboarding request is already in failed state")
		return successResponse(), nil

	default:
		// do nothing
	}
	s.handleStageTransition(latestCcOnbRequest, req)

	updateMasks := []ccEnumsV2Pb.CardRequestFieldMask{ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STAGE_DETAILS}
	if latestCcOnbRequest.GetStatus() != req.GetWorkflowStatus() {
		latestCcOnbRequest.Status = req.GetWorkflowStatus()
		updateMasks = append(updateMasks, ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS)
	}
	err = s.cardRequestDao.Update(ctx, latestCcOnbRequest, updateMasks)
	if err != nil {
		logger.Error(ctx, "error updating card request", zap.Error(err))
		return transientFailureResponse(), nil
	}

	// if onboarding is success then proceed with creating a card for user
	if latestCcOnbRequest.GetStatus() == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
		createErr := s.createCreditCard(ctx, latestCcOnbRequest)
		if createErr != nil {
			logger.Error(ctx, "error creating credit card", zap.Error(createErr))
			return transientFailureResponse(), nil
		}
	}
	return successResponse(), nil
}

// validateCcOnbStageUpdateEventReq validates the credit card onboarding stage update event request.
func (s *Service) validateCcOnbStageUpdateEventReq(req *ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventRequest) error {
	if req.GetWorkflowStatus() == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED {
		return errors.New(fmt.Sprintf("invalid workflow status: %s", req.GetWorkflowStatus().String()))
	}
	if req.GetStageTransitionInfo().GetCurrentStage() == ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_UNSPECIFIED {
		return errors.New(fmt.Sprintf("invalid current stage: %s", req.GetStageTransitionInfo().GetCurrentStage().String()))
	}
	if req.GetStageTransitionInfo().GetNextStage() == ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_UNSPECIFIED {
		return errors.New(fmt.Sprintf("invalid next stage: %s", req.GetStageTransitionInfo().GetNextStage().String()))
	}
	return nil
}

func (s *Service) currentOnbStageStateValidation(cardRequest *ffBeV2Pb.CardRequest, stageName ccEnumsV2Pb.CardRequestStage) error {
	if cardRequest.GetStageDetails().GetStages() == nil {
		return errors.New("no stages found in card request")
	}
	_, ok := cardRequest.GetStageDetails().GetStages()[stageName.String()]
	if !ok {
		return errors.New(fmt.Sprintf("stage %s not found in card request", stageName.String()))
	}
	return nil
}

// getLatestOnboardingRequestForExternalUserId fetches the latest onboarding request for the given user
func (s *Service) getLatestOnboardingRequestForExternalUserId(ctx context.Context, externalUserId string) (*ffBeV2Pb.CardRequest, error) {
	ccOnboardingRequests, err := s.cardRequestDao.GetByExternalUserIdAndRequestType(ctx, externalUserId, ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching credit card onboarding request")
	}
	return ccOnboardingRequests[0], nil
}

// getOrCreateStageInfo gets existing stage info or creates a new one from card request,
// it does not perform DB update in case of stage creation, it's callers responsibility to handle any updates if required
func (s *Service) getOrCreateStageInfo(cardRequest *ffBeV2Pb.CardRequest, stage ccEnumsV2Pb.CardRequestStage) *ffBeV2Pb.StageInfo {
	if stage == ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_UNSPECIFIED {
		return nil
	}
	stageInfo, exists := cardRequest.GetStageDetails().GetStages()[stage.String()]
	if !exists {
		logger.Info(context.Background(), "current stage not found in card request",
			zap.String("stage", stage.String()))
		return &ffBeV2Pb.StageInfo{
			State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
			LastUpdatedAt: timestamp.Now(),
			StartedAt:     timestamp.Now(),
		}
	}
	return stageInfo
}

// handleStageTransition manages the transition to the next stage
func (s *Service) handleStageTransition(cardRequest *ffBeV2Pb.CardRequest, req *ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventRequest) {
	currentStageInfo := s.getOrCreateStageInfo(cardRequest, req.GetStageTransitionInfo().GetCurrentStage())
	nextStageInfo := s.getOrCreateStageInfo(cardRequest, req.GetStageTransitionInfo().GetNextStage())

	// update the current stage status, as per the card request status from the event
	if currentStageInfo != nil {
		currentStageInfo.State = onbCardReqStatusToOnbCurrentStageStatusMap[req.GetWorkflowStatus()]
		currentStageInfo.LastUpdatedAt = timestamp.Now()
		cardRequest.StageDetails.Stages[req.GetStageTransitionInfo().GetCurrentStage().String()] = currentStageInfo
	}

	// if next stage is not empty and card request status is in terminal state i.e. next stage should be either (completed or rejected),
	// we'll mark the next stage status as success since onboarding request has moved to terminal state and there will be no action at vendor's on the onboarding request
	if nextStageInfo != nil {
		nextStageInfo.State = ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED
		if req.GetWorkflowStatus() == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED ||
			req.GetWorkflowStatus() == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
			nextStageInfo.State = ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS
		}
		nextStageInfo.LastUpdatedAt = timestamp.Now()
		cardRequest.StageDetails.Stages[req.GetStageTransitionInfo().GetNextStage().String()] = nextStageInfo
	}
}

// handleCcOnboardingReqTerminalStatus handles the success status of a credit card onboarding request.
// If the onboarding request is successful, it proceeds to create a credit card for the user.
// If a valid credit card already exists, it does not create a new one.
func (s *Service) handleCcOnboardingReqTerminalStatus(ctx context.Context, req *ffBeV2Pb.CardRequest) error {
	// check if card is already created
	savedCards, err := s.creditCardDao.GetByExternalUserId(ctx, req.GetExternalUserId())
	if err != nil {
		return errors.Wrap(err, "error fetching credit cards")
	}
	for _, cc := range savedCards {
		if cc.GetState() == ccEnumsV2Pb.CardState_CARD_STATE_CREATED {
			return nil
		}
	}
	// proceed with creating an entry for credit card in db if no valid credit card is found
	err = s.createCreditCard(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

// createCreditCard creates a new credit card entry in the database based on the onboarding card request.
// It returns the created credit card or an error if the creation fails.
func (s *Service) createCreditCard(ctx context.Context, onboardingCardRequest *ffBeV2Pb.CardRequest) error {
	createdCreditCard, err := s.creditCardDao.Create(ctx, &ffBeV2Pb.CreditCard{
		ActorId: onboardingCardRequest.GetActorId(),
		// TODO(CB): populate it from vg response when we start receiving it.
		CardNetworkType: ccEnumsV2Pb.CardNetworkType_CARD_NETWORK_TYPE_VISA,
		ExternalUserId:  onboardingCardRequest.GetExternalUserId(),
		State:           ccEnumsV2Pb.CardState_CARD_STATE_CREATED,
		// TODO(cb): get confirmation on this from product.
		Vendor:      commonvgpb.Vendor_SAVEN,
		CardProgram: onboardingCardRequest.GetRequestDetails().GetOnboardingRequestDetails().GetCardProgram(),
	})
	if err != nil {
		return errors.Wrap(err, "error creating credit card")
	}
	logger.Info(ctx, "credit card created successfully", zap.String(logger.CARD_ID, createdCreditCard.GetId()))
	return nil
}

func transientFailureResponse() *ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventResponse {
	return &ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
		},
	}
}

func successResponse() *ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventResponse {
	return &ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}
}
