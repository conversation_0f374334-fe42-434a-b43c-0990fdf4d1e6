//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/idgen"

	"github.com/epifi/gamma/firefly/config/genconf"

	"github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	usersPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	ccVgPb2 "github.com/epifi/gamma/api/vendorgateway/creditcard"
	fireflyV2 "github.com/epifi/gamma/firefly/v2"
	consumerV2 "github.com/epifi/gamma/firefly/v2/consumer"
	fireflyV2Dao "github.com/epifi/gamma/firefly/v2/dao"
	types2 "github.com/epifi/gamma/firefly/v2/wire/types"
)

func InitialiseFireflyV2Svc(conf *genconf.Config, db types2.CreditCardFederalPGDB, usersClient usersPb.UsersClient,
	ccVgClient ccVgPb2.CreditCardClient, authClient auth.AuthClient, bankCustClient bankCustPb.BankCustomerServiceClient,
	onbClient onbPb.OnboardingClient, ccOnboardingStateUpdateEventPublisher types2.CcOnboardingStateUpdateEventPublisher) *fireflyV2.Service {
	wire.Build(
		fireflyV2.NewService,
		idgen.NewClock,
		idgen.WireSet,
		fireflyV2Dao.CreditCardWireSet,
		fireflyV2Dao.CreditCardOffersWireSet,
		fireflyV2Dao.CardRequestWireSet,
	)
	return &fireflyV2.Service{}
}

func InitialiseConsumerV2Service(db types2.CreditCardFederalPGDB) *consumerV2.Service {
	wire.Build(
		consumerV2.NewService,
		idgen.NewClock,
		idgen.WireSet,
		fireflyV2Dao.CardRequestWireSet,
		fireflyV2Dao.CreditCardWireSet,
	)
	return &consumerV2.Service{}
}
