// nolint:funlen,govet,gocritic,ineffassign
package firefly

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/cfg"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	temporalClient "go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/ratelimiter"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/biometrics"
	authOrchPb "github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/card"
	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	consentPb "github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/accounting/enums"
	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	"github.com/epifi/gamma/api/firefly/billing"
	ffRePb "github.com/epifi/gamma/api/firefly/card_recommendation"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	ffWfPb "github.com/epifi/gamma/api/firefly/workflow"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	payPb "github.com/epifi/gamma/api/pay"
	paymentInstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGrpPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	ffActHelper "github.com/epifi/gamma/firefly/activity"
	ffConf "github.com/epifi/gamma/firefly/config"
	ffGenConf "github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/cryptor"
	"github.com/epifi/gamma/firefly/dao"
	ffEvents "github.com/epifi/gamma/firefly/events"
	"github.com/epifi/gamma/firefly/helper"
	types2 "github.com/epifi/gamma/firefly/wire/types"
	featureCfg "github.com/epifi/gamma/pkg/feature"
	"github.com/epifi/gamma/pkg/feature/release"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	copyCardDetailsExpiry                  = 30
	viewCardDetailsExpiry                  = 30
	ReissueCardApi1                        = "cc_reissue_card_api_1"
	ReissueCardApi2                        = "cc_reissue_card_api_2"
	ReissueCardApi3                        = "cc_reissue_card_api_3"
	CreditCardPidKey                       = "pid"
	ExternalVendorIdentifierKey            = "af_sub1"
	PaisabazaarPidVal                      = "paisabazaar_int"
	cardStateInvalidDisplayTitle           = "Your card is blocked"
	cardStateInvalidDisplayDesc            = "This action can not be completed since your card is blocked. If you've requested a new card, its details will be available soon."
	alertCircleIcon                        = "https://epifi-icons.pointz.in/credit_card_images/alert-circle.png"
	annualMembershipFeesBeneficiaryName    = "AmpliFi Renewal Fee"
	annualMembershipFeesIdentifier         = "membershipfees"
	annualMembershipFeesGSTBeneficiaryName = "GST on AmpliFi Renewal Fee"
	annualMembershipFeesGSTIdentifier      = "membershipfeesservicetax"
)

var (
	// TODO(team) : Add this mapping once we have merged the workflow related changes
	cardRequestWorkflowToWorkflowTypeMap = map[ffEnumsPb.CardRequestWorkFlow]workflowPb.Type{
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_FREEZE_UNFREEZE:     workflowPb.Type_FREEZE_UNFREEZE_CARD,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD:        workflowPb.Type_PROCESS_REISSUE_CARD,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_LIMIT_CHANGE:        workflowPb.Type_SET_CARD_LIMITS,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CONTROL_CHANGE:      workflowPb.Type_SET_CARD_USAGE,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_VIEW_CARD_DETAILS:   workflowPb.Type_PROCESS_VIEW_CARD_DETAILS,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ACTIVATION:     workflowPb.Type_ACTIVATE_CREDIT_CARD,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_ISSUE_PHYSICAL_CARD: workflowPb.Type_ISSUE_PHYSICAL_CREDIT_CARD,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PROCESS_DISPUTE:     workflowPb.Type_PROCESS_DISPUTE,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET:           workflowPb.Type_RESET_CARD_PIN,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_EXPORT_STATEMENT:   workflowPb.Type_EXPORT_CREDIT_CARD_STATEMENT,
	}
	maxRetryAttempts       int32 = 10
	minimumValidOfferValue       = 5000.0

	contextToStageMap = map[ffEnumsPb.SignalContext][]ffEnumsPb.CardRequestStageName{
		ffEnumsPb.SignalContext_SIGNAL_CONTEXT_USER_CONTINUE_FD_SUCCESS:                    {ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_FD_CREATION, ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PAN_AADHAAR_VALIDATION},
		ffEnumsPb.SignalContext_SIGNAL_CONTEXT_RECORDED_USER_ACTION_ON_LIMIT_CHANGE_SCREEN: {ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_LIMIT_CHECK},
	}

	userIntentShownCardRequestStageStatus = []ffEnumsPb.CardRequestStageStatus{
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS,
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS,
	}

	userIntentShownCardRequestStatus = []ffEnumsPb.CardRequestStatus{
		ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
		ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
	}
	// map to store the valid card states for a particular workflow.
	validCardStatesForWorkflows = map[ffEnumsPb.CardRequestWorkFlow][]ffEnumsPb.CardState{
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET: {
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD: {
			ffEnumsPb.CardState_CARD_STATE_CREATED,
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_BLOCKED,
			ffEnumsPb.CardState_CARD_STATE_SUSPENDED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CONTROL_CHANGE: {
			ffEnumsPb.CardState_CARD_STATE_CREATED,
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_LIMIT_CHANGE: {
			ffEnumsPb.CardState_CARD_STATE_CREATED,
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_VIEW_CARD_DETAILS: {
			ffEnumsPb.CardState_CARD_STATE_CREATED,
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_FREEZE_UNFREEZE: {
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_SUSPENDED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ACTIVATION: {
			ffEnumsPb.CardState_CARD_STATE_CREATED,
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PAYMENT: {
			ffEnumsPb.CardState_CARD_STATE_CREATED,
			ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_SUSPENDED,
			ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
			ffEnumsPb.CardState_CARD_STATE_BLOCKED,
		},
	}

	cardProgramTypeToRewardCardTypeMap = map[types.CardProgramType]string{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED:    "UNSPECIFIED",
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:        "SIMPLIFI_CREDIT_CARD_ID",
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:      "AMPLIFI_CREDIT_CARD_ID",
		types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED: "MAGNIFI_CREDIT_CARD_ID",
	}

	validCreditCardStates = []ffEnumsPb.CardState{
		ffEnumsPb.CardState_CARD_STATE_CREATED,
		ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
		ffEnumsPb.CardState_CARD_STATE_SUSPENDED,
		ffEnumsPb.CardState_CARD_STATE_BLOCKED,
	}
)

type Service struct {
	ffPb.UnimplementedFireflyServer
	cardRequestDao             dao.CardRequestDao
	cardRequestStageDao        dao.CardRequestStageDao
	creditCardDao              dao.CreditCardDao
	ccOffersDao                dao.CreditCardOffersDao
	ccOfferEligibilityCriteria dao.CreditCardOfferEligibilityCriteriaDao
	celestialClient            celestialPb.CelestialClient
	userClient                 userPb.UsersClient
	actorClient                actorPb.ActorClient
	signalWorkflowPublisher    queue.Publisher
	conf                       *ffConf.Config
	rpcHelper                  *helper.RpcHelper
	txnExecutor                storagev2.TxnExecutor
	cryptorMap                 *cryptor.FireflyCryptorMap
	ffAccountingClient         ffAccPb.AccountingClient
	ccVgClient                 creditcard.CreditCardClient
	rewardsClient              rewardsPb.RewardsGeneratorClient
	rewardsAggregatesClient    rewardspinotpb.RewardsAggregatesClient
	limitEstimatorClient       limitEstimatorPb.CreditLimitEstimatorClient
	cCVgPciClient              CreditCardClientWithInterceptorsToVendorGatewayPCIServer
	releaseEvaluator           release.IEvaluator
	eventBroker                events.Broker
	biometricClient            biometrics.BiometricsServiceClient
	genConf                    *ffGenConf.Config
	temporalClient             temporalClient.Client
	rateLimitClient            ratelimiter.RateLimiter
	doOnce                     onceV2.DoOnce
	redemptionClient           redemptionPb.OfferRedemptionServiceClient
	recommendationEngineClient ffRePb.CardRecommendationServiceClient
	fireflyRedisClient         *redis.Client
	consentClient              consentPb.ConsentClient
	cardProvisioningClient     cardProvisioningPb.CardProvisioningClient
	segmentationServiceClient  segment.SegmentationServiceClient
	piClient                   paymentInstrumentPb.PiClient
	ffTxnAggregatesClient      ffpinotpb.TxnAggregatesClient
	billingClient              billing.BillingClient
	inAppTargetedCommsClient   tcPb.InAppTargetedCommsClient
	fireflyV2Client            ffBeV2Pb.FireflyV2Client
	userGrpClient              userGrpPb.GroupClient
}

type CreditCardClientWithInterceptorsToVendorGatewayPCIServer creditcard.CreditCardClient

func NewService(cardRequestDao dao.CardRequestDao, cardRequestStageDao dao.CardRequestStageDao, creditCardDao dao.CreditCardDao, ccOffersDao dao.CreditCardOffersDao,
	ccOfferEligibilityCriteria dao.CreditCardOfferEligibilityCriteriaDao,
	celestialClient celestialPb.CelestialClient, ccVgClient creditcard.CreditCardClient, userClient userPb.UsersClient, signalWorkflowPublisher types2.SignalWorkflowPublisher, conf *ffConf.Config, ffAccountingClient ffAccPb.AccountingClient,
	actorClient actorPb.ActorClient, rpcHelper *helper.RpcHelper, txnExecutor storagev2.TxnExecutor, cryptorMap *cryptor.FireflyCryptorMap,
	rewardsClient rewardsPb.RewardsGeneratorClient, limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	cCVgPciClient CreditCardClientWithInterceptorsToVendorGatewayPCIServer, eventBroker events.Broker, releaseEvaluator release.IEvaluator, biometricClient biometrics.BiometricsServiceClient,
	genConf *ffGenConf.Config, temporalClient temporalClient.Client, rateLimitClient ratelimiter.RateLimiter, doOnce onceV2.DoOnce, redemptionClient redemptionPb.OfferRedemptionServiceClient,
	recommendationEngineClient ffRePb.CardRecommendationServiceClient, fireflyRedisClient *redis.Client, consentClient consentPb.ConsentClient, cardProvisioningClient cardProvisioningPb.CardProvisioningClient, segmentationServiceClient segment.SegmentationServiceClient,
	piClient paymentInstrumentPb.PiClient, rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient, ffTxnAggregatesClient ffpinotpb.TxnAggregatesClient, billingClient billing.BillingClient,
	inAppTargetedCommsClient tcPb.InAppTargetedCommsClient, fireflyV2Client ffBeV2Pb.FireflyV2Client, userGrpClient userGrpPb.GroupClient) *Service {
	return &Service{
		cardRequestDao:             cardRequestDao,
		cardRequestStageDao:        cardRequestStageDao,
		creditCardDao:              creditCardDao,
		ccOffersDao:                ccOffersDao,
		ccOfferEligibilityCriteria: ccOfferEligibilityCriteria,
		celestialClient:            celestialClient,
		userClient:                 userClient,
		actorClient:                actorClient,
		signalWorkflowPublisher:    signalWorkflowPublisher,
		conf:                       conf,
		rpcHelper:                  rpcHelper,
		txnExecutor:                txnExecutor,
		cryptorMap:                 cryptorMap,
		ffAccountingClient:         ffAccountingClient,
		ccVgClient:                 ccVgClient,
		rewardsClient:              rewardsClient,
		rewardsAggregatesClient:    rewardsAggregatesClient,
		limitEstimatorClient:       limitEstimatorClient,
		cCVgPciClient:              cCVgPciClient,
		releaseEvaluator:           releaseEvaluator,
		eventBroker:                eventBroker,
		biometricClient:            biometricClient,
		genConf:                    genConf,
		temporalClient:             temporalClient,
		rateLimitClient:            rateLimitClient,
		doOnce:                     doOnce,
		redemptionClient:           redemptionClient,
		recommendationEngineClient: recommendationEngineClient,
		fireflyRedisClient:         fireflyRedisClient,
		consentClient:              consentClient,
		cardProvisioningClient:     cardProvisioningClient,
		segmentationServiceClient:  segmentationServiceClient,
		piClient:                   piClient,
		ffTxnAggregatesClient:      ffTxnAggregatesClient,
		billingClient:              billingClient,
		inAppTargetedCommsClient:   inAppTargetedCommsClient,
		fireflyV2Client:            fireflyV2Client,
		userGrpClient:              userGrpClient,
	}
}

func (s *Service) GetPaymentsDomainOrderData(ctx context.Context, req *payPb.GetPaymentsDomainOrderDataRequest) (*payPb.GetPaymentsDomainOrderDataResponse, error) {
	var (
		actorId     = req.GetActorId()
		clientReqId = req.GetClientReqId()
		res         = &payPb.GetPaymentsDomainOrderDataResponse{}
	)

	cardRequest, err := s.getAndValidateCardRequestDetails(ctx, actorId, clientReqId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no card request found",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in validating card request details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	piData, err := s.getPaymentInstrumentDetails(ctx, cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSecuredCardOnboardingDetails().GetPiId())
	if err != nil {
		logger.Error(ctx, "error in fetching pi details",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return nil, err
	}

	res = &payPb.GetPaymentsDomainOrderDataResponse{
		Status: rpc.StatusOk(),
		DomainOrderData: &payPb.DomainOrderData{
			NextAction: helper.GetCreditCardPollingScreen(cardRequest.GetId()),
			AccountsEligibleForPaymentFulfillment: []*payPb.AccountDetails{
				{
					AccountNumber: piData.GetAccount().GetActualAccountNumber(),
					IfscCode:      piData.GetAccount().GetIfscCode(),
					Name:          piData.GetName(),
				},
			},
			Amount:                cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSecuredCardOnboardingDetails().GetDepositAmount(),
			ClientRequestIdExpiry: timestamppb.New(cardRequest.GetCreatedAt().AsTime().Add(5 * time.Hour)),
			ToPi:                  s.conf.CardProgramToBeneficiaryDetails[ffPkg.GetCardProgramStringFromCardProgram(cardRequest.GetRequestDetails().GetCardProgram())].ActorId,
			ToActor:               s.conf.CardProgramToBeneficiaryDetails[ffPkg.GetCardProgramStringFromCardProgram(cardRequest.GetRequestDetails().GetCardProgram())].PaymentInstrumentId,
		},
	}
	return res, nil
}

func (s *Service) getPaymentInstrumentDetails(ctx context.Context, paymentInstrumentId string) (*paymentInstrumentPb.PaymentInstrument, error) {
	piData, err := s.piClient.GetPiById(ctx, &paymentInstrumentPb.GetPiByIdRequest{
		Id: paymentInstrumentId,
	})
	if te := epifigrpc.RPCError(piData, err); te != nil {
		return nil, errors.Wrap(te, fmt.Sprintf("error in fetching pi by id : [%s]", paymentInstrumentId))
	}
	return piData.GetPaymentInstrument(), nil
}

func (s *Service) getAndValidateCardRequestDetails(ctx context.Context, actorId, clientReqId string) (*ffPb.CardRequest, error) {
	crRes, err := s.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             actorId,
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	switch {
	case err != nil:
		return nil, errors.Wrap(err, "error in fetching card request and stages")
	case crRes.GetStatus().IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case !crRes.GetStatus().IsSuccess():
		return nil, errors.New(fmt.Sprintf("response [%s] received from GetCardRequestAndCardRequestStage", crRes.GetStatus().String()))
	}
	isClientRequestIdValid := false
	for _, crs := range crRes.GetCardRequestStages() {
		if crs.GetExternalRequestId() == clientReqId {
			isClientRequestIdValid = true
			break
		}
	}
	if !isClientRequestIdValid {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no active onboarding request with the given client req id")
	}
	return crRes.GetCardRequest(), nil
}

func (s *Service) GetRequestStatus(ctx context.Context, req *ffPb.GetRequestStatusRequest) (*ffPb.GetRequestStatusResponse, error) {
	var (
		res = &ffPb.GetRequestStatusResponse{}
	)
	cardRequest, err := s.cardRequestDao.GetById(ctx, req.GetCardRequestId(),
		[]ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_WORKFLOW,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
	case err != nil:
		logger.Error(ctx, "error in fetching card request", zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	default:
		res.RequestStatus = cardRequest.GetStatus()
		res.NextAction = cardRequest.GetNextAction()
		res.Workflow = cardRequest.GetWorkflow()
		res.Status = rpc.StatusOk()
		res.CardRequestDetails = cardRequest.GetRequestDetails()
	}
	return res, nil
}

func (s *Service) handleSetPinStuckCase(ctx context.Context, cardRequest *ffPb.CardRequest) error {
	if cardRequest.GetNextAction().GetScreen() != deeplinkPb.Screen_CREDIT_CARD_SET_CARD_PIN {
		return nil
	}

	appPlatform := epificontext.AppPlatformFromContext(ctx)
	if appPlatform != commontypes.Platform_IOS {
		return nil
	}

	// Threshold is 6 hours post screen was updated. If threshold is not passed, move on
	threshold := cardRequest.GetUpdatedAt().AsTime().Add(time.Minute * 15)
	if datetime.IsBefore(timestamppb.Now(), timestamppb.New(threshold)) {
		return nil
	}

	crs, err := s.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, cardRequest.GetId(), ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_SET_CARD_PIN, nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "set pin card request stage not found", zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
	case err != nil:
		return errors.Wrap(err, "error fetching pin set stage")
	}

	// if set pin stage exists, pin set was attempted at some point. move on
	if crs != nil {
		return nil
	}

	var title, desc string
	if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ACTIVATION {
		title = "Unable to activate card"
		desc = "Unfortunately, we’ve run into some issues and cannot activate your card. \n\nWe’re working hard to fix this, but it might take a few days. \n\nWe'll inform you when you can try again, and don't worry, your progress will be saved."
	} else if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET {
		title = "Unable to reset PIN"
		desc = "Unfortunately, we’ve run into some issues and cannot reset your PIN. \n\nWe’re working hard to fix this, but it might take a few days. \n\nWe'll inform you when you can try again, and don't worry, your progress will be saved."
	}

	cardRequest.NextAction = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplinkPb.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/alert_circle.png",
				Details: []*deeplinkPb.InfoItem{
					{
						Title: title,
						Desc:  desc,
					},
				},
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "BACK TO HOME",
					Deeplink:     helper.GetCreditCardDashboardDeeplink(),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
			},
		},
	}

	return nil
}

// StartCardOnboarding rpc checks if user has already started onboarding or not, if user has
// already started onboarding we will check if card request is in created state if not we will fail the rpc.
// If card request is not present or in CREATED state we will initiate onboarding workflow and send next action to the
// client.
func (s *Service) StartCardOnboarding(ctx context.Context, req *ffPb.StartCardOnboardingRequest) (*ffPb.StartCardOnboardingResponse, error) {
	var (
		res                         = &ffPb.StartCardOnboardingResponse{}
		cardRequest                 *ffPb.CardRequest
		createErr                   error
		skipBillGenDateCaptureStage bool
		creditCardRequestHeader     = &types.CreditCardRequestHeader{}
	)
	cardRequests, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, req.GetActorId(),
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING, []ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound),
		len(cardRequests) != 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		logger.Info(ctx, "no card request for card onboarding/failed card request for onboarding")

		if req.GetCardProgram() != nil {
			creditCardRequestHeader.CardProgram = ffPkg.GetCardProgramStringFromCardProgram(req.GetCardProgram())
		}

		eligibilityRes, eligibleErr := s.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{
			ActorId:                 req.GetActorId(),
			Vendor:                  ffEnumsPb.Vendor_FEDERAL,
			ShouldCallVendor:        true,
			CreditCardRequestHeader: creditCardRequestHeader,
		})
		switch {
		case eligibleErr != nil:
			logger.Error(ctx, "error in FetchCreditCardEligibility RPC", zap.Error(eligibleErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		case eligibilityRes.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "card exists for the user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			if len(cardRequests) != 0 {
				res.NextAction = cardRequests[0].GetNextAction()
				res.Status = rpc.StatusOk()
				res.CardRequestId = cardRequests[0].GetId()
				return res, nil
			}
			res.Status = rpc.StatusInternal()
			return res, nil
		case !eligibilityRes.GetStatus().IsSuccess():
			logger.Error(ctx, fmt.Sprintf("[%v] status from FetchCreditCardEligibility RPC", eligibilityRes.GetStatus()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		case !eligibilityRes.GetIsUserCcEligible():
			logger.Error(ctx, "user is not eligible for a cc", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusOk()
			res.NextAction = helper.GetOfferExpiredScreenDeeplink()
			res.OfferId = eligibilityRes.GetOfferId()
			return res, nil
		default:
			skipBillGenDateCaptureStage = true
			offerDetails, offerErr := s.ccOffersDao.GetById(ctx, eligibilityRes.GetOfferId())
			switch {
			case errors.Is(epifierrors.ErrRecordNotFound, offerErr):
				logger.Error(ctx, "no offer found with given offer id", zap.String(logger.OFFER_ID, eligibilityRes.GetOfferId()))
				res.Status = rpc.StatusInternal()
				return res, nil
			case offerErr != nil:
				logger.Error(ctx, "error fetching offer from db", zap.String(logger.OFFER_ID, eligibilityRes.GetOfferId()), zap.Error(offerErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			cardProgram := offerDetails.GetCardProgram()
			offerId := eligibilityRes.GetOfferId()
			if req.GetCardProgram() != nil {
				cardProgram = req.GetCardProgram()
				offerWithCardProgram, fetchErr := s.ccOffersDao.GetAllActiveOffersByActorId(ctx, req.GetActorId())
				if fetchErr != nil {
					logger.Error(ctx, "error fetching offer for actor, vendor and card program", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
				offerWithCardProgram = filterCreditCardOfferByVendorAndCardProgram(offerWithCardProgram, ffEnumsPb.Vendor_VENDOR_UNSPECIFIED, ffPkg.GetCardProgramStringFromCardProgram(cardProgram))
				offerId = offerWithCardProgram[0].GetId()
			}

			extVendorId, idErr := s.getExternalVendorId(ctx, req.GetActorId())
			if idErr != nil {
				logger.Error(ctx, "error in getExternalVendorId", zap.Error(idErr))
			}
			cardRequest, createErr = s.cardRequestDao.Create(ctx, &ffPb.CardRequest{
				ActorId:         req.GetActorId(),
				OrchestrationId: uuid.New().String(),
				Vendor:          commonvgpb.Vendor_M2P,
				Workflow:        ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
				Status:          ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
				Provenance:      req.GetProvenance(),
				RequestDetails: &ffPb.CardRequestDetails{
					Data: &ffPb.CardRequestDetails_CardOnboardingDetails{
						CardOnboardingDetails: &ffPb.CardOnboardingDetails{
							CardLimit:                                eligibilityRes.GetAvailableLimit(),
							OfferId:                                  offerId,
							SkipBillGenDateCaptureStage:              skipBillGenDateCaptureStage,
							SkipBillGenDateCaptureOnActivationScreen: true,
							CardProgram:                              cardProgram,
						},
					},
					CardProgram: cardProgram,
				},
				ExternalVendorId: extVendorId,
			})
			if createErr != nil {
				logger.Error(ctx, "error in creating card onboarding request", zap.Error(createErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			cardRequest.NextAction = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.PanAadhaarLinkVerificationDisplayMessage)
			if updateErr := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
			}); updateErr != nil {
				logger.Error(ctx, "error in updating next action in card request", zap.Error(updateErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
		}
	case err != nil:
		logger.Error(ctx, "error in fetching card onboarding workflow request", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Info(ctx, "existing card requests for onboarding present for user")
		cardRequest = cardRequests[0]
		res.NextAction = cardRequest.GetNextAction()
	}
	payload := &ffWfPb.CreditCardOnboardingPayload{
		CardReqId:                   cardRequest.GetId(),
		SkipBillGenDateCaptureStage: skipBillGenDateCaptureStage,
	}
	bytePayload, err := protojson.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "error in creation payload for cc onboarding workflow", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	initiateWorkflowRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		ActorId: req.GetActorId(),
		Version: workflowPb.Version_V1,
		Type:    workflowPb.Type_PERFORM_CARD_ONBOARDING,
		Payload: bytePayload,
		ClientReqId: &celestialPb.ClientReqId{
			Id:     cardRequest.GetOrchestrationId(),
			Client: workflowPb.Client_FIREFLY,
		},
		Ownership: commontypes.Ownership_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil && !initiateWorkflowRes.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "error in initiating workflow", zap.Error(te), zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, cardRequest.GetOrchestrationId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.CardRequestId = cardRequest.GetId()
	res.NextAction = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.PanAadhaarLinkVerificationDisplayMessage)
	res.Status = rpc.StatusOk()
	res.OfferId = cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
	res.CardProgram = cardRequest.GetRequestDetails().GetCardProgram()
	return res, nil
}

func (s *Service) FetchCardUsage(ctx context.Context, req *ffPb.FetchCardUsageRequest) (*ffPb.FetchCardUsageResponse, error) {
	res := &ffPb.FetchCardUsageResponse{}
	var (
		currentCard       *ffPb.CreditCard
		err               error
		getCCByActorIdRes []*ffPb.CreditCard
		ccFieldMask       = []ffEnumsPb.CreditCardFieldMask{
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CONTROLS_DATA,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
		}
	)
	if req.GetCreditCardId() != "" {
		currentCard, err = s.creditCardDao.GetById(ctx, req.GetCreditCardId(), ccFieldMask)
		if err != nil {
			logger.Error(ctx, "Error while fetching credit card details", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		getCCByActorIdRes, err = s.creditCardDao.GetByActorId(ctx, req.GetActorId(), ccFieldMask)
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "No credit card found for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		} else if err != nil {
			logger.Error(ctx, "Error while fetching credit card details", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		currentCard = getCCByActorIdRes[0]
	}
	res.CreditCardId = currentCard.GetId()

	atmEnabled := currentCard.GetControlDetails().GetAtm()
	ecomEnabled := currentCard.GetControlDetails().GetEcom()
	internationalEnabled := currentCard.GetControlDetails().GetInternational()
	posEnabled := currentCard.GetControlDetails().GetPos()
	contactlessEnabled := currentCard.GetControlDetails().GetContactless()
	accountDetails, err := s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{GetBy: &ffAccPb.GetAccountRequest_AccountId{
		AccountId: currentCard.GetAccountId(),
	}})
	if te := epifigrpc.RPCError(accountDetails, err); te != nil {
		logger.Error(ctx, "error in fetching account details for the user", zap.Error(te))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	prefs, err := s.ccVgClient.FetchPreference(ctx, &creditcard.FetchPreferenceRequest{
		Header:   &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
		EntityId: accountDetails.GetAccount().GetReferenceId(),
	})
	if te := epifigrpc.RPCError(accountDetails, err); te != nil {
		logger.Error(ctx, "error in fetchPreference API call, falling back on DB")
	} else {
		atmEnabled = prefs.GetResult().GetAtm()
		ecomEnabled = prefs.GetResult().GetEcom()
		internationalEnabled = prefs.GetResult().GetInternational()
		posEnabled = prefs.GetResult().GetPos()
		contactlessEnabled = prefs.GetResult().GetContactless()
	}
	res.CardState = currentCard.GetCardState()
	res.CardUsages = append(res.CardUsages, &ffPb.FetchCardUsageResponse_CardUsage{
		CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
		IsEnabled:       atmEnabled,
	})
	res.CardUsages = append(res.CardUsages, &ffPb.FetchCardUsageResponse_CardUsage{
		CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
		IsEnabled:       ecomEnabled,
	})
	res.CardUsages = append(res.CardUsages, &ffPb.FetchCardUsageResponse_CardUsage{
		CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
		IsEnabled:       posEnabled,
	})
	res.CardUsages = append(res.CardUsages, &ffPb.FetchCardUsageResponse_CardUsage{
		CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS,
		IsEnabled:       contactlessEnabled,
	})
	res.CardUsages = append(res.CardUsages, &ffPb.FetchCardUsageResponse_CardUsage{
		CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL,
		IsEnabled:       internationalEnabled,
	})

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) FetchCardLimits(ctx context.Context, req *ffPb.FetchCardLimitsRequest) (*ffPb.FetchCardLimitsResponse, error) {
	res := &ffPb.FetchCardLimitsResponse{}
	var (
		currentCard *ffPb.CreditCard
		err         error
		ccFieldMask = []ffEnumsPb.CreditCardFieldMask{
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_LIMITS,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CONTROLS_DATA,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
		}
	)

	if req.GetCreditCardId() != "" {
		currentCard, err = s.creditCardDao.GetById(ctx, req.GetCreditCardId(), ccFieldMask)
		if err != nil {
			logger.Error(ctx, "Error while fetching credit card details", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		var getCCByActorIdRes []*ffPb.CreditCard
		getCCByActorIdRes, err = s.creditCardDao.GetByActorId(ctx, req.GetActorId(), ccFieldMask)
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "No credit card found for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		} else if err != nil {
			logger.Error(ctx, "Error while fetching credit card details", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		currentCard = getCCByActorIdRes[0]
	}
	res.CreditCardId = currentCard.GetId()

	accResp, accErr := s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
		GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: currentCard.GetAccountId()},
	})
	if te := epifigrpc.RPCError(accResp, accErr); te != nil {
		logger.Error(ctx, "error fetching account info: ", zap.Error(te))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// map to keep a track of all the reasons for which some of or all of the limits have been disabled for
	// a particular location type
	domesticlimitDisablementTypes := make(map[ffEnumsPb.CardLimitDisablementType]bool)
	internationallimitDisablementTypes := make(map[ffEnumsPb.CardLimitDisablementType]bool)

	for _, cardLimit := range currentCard.GetCardLimits().GetLimits() {
		ctrDisablementType := ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_UNSPECIFIED
		limitDisabled := false
		if currentCard.GetCardState() == ffEnumsPb.CardState_CARD_STATE_SUSPENDED {
			ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CARD_FROZEN
			limitDisabled = true
		}
		if currentCard.GetCardState() == ffEnumsPb.CardState_CARD_STATE_BLOCKED && !limitDisabled {
			ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CARD_BLOCKED
			limitDisabled = true
		}
		if !currentCard.GetControlDetails().GetInternational() && cardLimit.GetLocationType() == ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL && !limitDisabled {
			ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_INTERNATIONAL_DISABLED
			limitDisabled = true
		}
		switch cardLimit.GetControlType() {
		case ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM:
			if !currentCard.GetControlDetails().GetEcom() {
				if !limitDisabled {
					ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED
					limitDisabled = true
				}
			}
		case ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS:
			if !currentCard.GetControlDetails().GetPos() {
				if !limitDisabled {
					ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED
					limitDisabled = true
				}
			}
		case ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM:
			if !currentCard.GetControlDetails().GetAtm() {
				if !limitDisabled {
					ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED
					limitDisabled = true
				}
			}
		case ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL:
			if !currentCard.GetControlDetails().GetInternational() {
				if !limitDisabled {
					ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED
					limitDisabled = true
				}
			}
		case ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS:
			if !currentCard.GetControlDetails().GetContactless() {
				if !limitDisabled {
					ctrDisablementType = ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED
					limitDisabled = true
				}
			}
		default:
		}
		if cardLimit.GetLocationType() == ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC {
			domesticlimitDisablementTypes[ctrDisablementType] = true
			maxDailyLimitValue := s.getMaxLimitValueForControlType(cardLimit, accResp.GetAccount())
			dailyLimitValue := s.getDailyLimitValueForControlType(cardLimit, maxDailyLimitValue)
			res.ControlLimits = append(res.ControlLimits, &ffPb.FetchCardLimitsResponse_ControlLimits{
				CardControlType:    cardLimit.GetControlType(),
				DailyLimitValue:    dailyLimitValue,
				MinDailyLimitValue: cardLimit.GetMinDailyLimitValue(),
				MaxDailyLimitValue: maxDailyLimitValue,
				LimitDisabled:      limitDisabled,
			})
		} else {
			internationallimitDisablementTypes[ctrDisablementType] = true
		}
		if res.GetCardLimits() == nil {
			res.CardLimits = make(map[string]*ffPb.FetchCardLimitsResponse_CardLimitDetails)
		}
		limitRes, ok := res.GetCardLimits()[cardLimit.GetLocationType().String()]
		if !ok {
			maxDailyLimitValue := s.getMaxLimitValueForControlType(cardLimit, accResp.GetAccount())
			dailyLimitValue := s.getDailyLimitValueForControlType(cardLimit, maxDailyLimitValue)
			res.GetCardLimits()[cardLimit.GetLocationType().String()] = &ffPb.FetchCardLimitsResponse_CardLimitDetails{
				ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
					{
						CardControlType:    cardLimit.GetControlType(),
						DailyLimitValue:    dailyLimitValue,
						MinDailyLimitValue: cardLimit.GetMinDailyLimitValue(),
						MaxDailyLimitValue: maxDailyLimitValue,
						LimitDisabled:      limitDisabled,
					},
				},
			}
		} else {
			limitRes.ControlLimits = append(limitRes.GetControlLimits(), &ffPb.FetchCardLimitsResponse_ControlLimits{
				CardControlType:    cardLimit.GetControlType(),
				DailyLimitValue:    cardLimit.GetDailyLimitValue(),
				MinDailyLimitValue: cardLimit.GetMinDailyLimitValue(),
				MaxDailyLimitValue: s.getMaxLimitValueForControlType(cardLimit, accResp.GetAccount()),
				LimitDisabled:      limitDisabled,
			})
			res.CardLimits[cardLimit.GetLocationType().String()] = limitRes
		}
	}
	limitDisablementTypePrecedence := []ffEnumsPb.CardLimitDisablementType{
		ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CARD_BLOCKED,
		ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CARD_FROZEN,
		ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_INTERNATIONAL_DISABLED,
		ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED,
	}
	for locType, control := range res.GetCardLimits() {
		switch locType {
		case ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC.String():
			for _, disablementType := range limitDisablementTypePrecedence {
				if domesticlimitDisablementTypes[disablementType] {
					control.CardLimitDisablementType = disablementType
					break
				}
			}
		case ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL.String():
			for _, disablementType := range limitDisablementTypePrecedence {
				if internationallimitDisablementTypes[disablementType] {
					control.CardLimitDisablementType = disablementType
					break
				}
			}
		}
	}
	res.CardState = currentCard.GetCardState()
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getDailyLimitValueForControlType(cardLimit *ffPb.CardLimits_LimitsForControlTypePerLocation, maxDailyLimitValue *money.Money) *money.Money {
	if cardLimit.GetControlType() != ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM {
		return cardLimit.GetDailyLimitValue()
	}
	dailyLimitValue := cardLimit.GetDailyLimitValue()
	compareValue, err := moneyPkg.CompareV2(dailyLimitValue, maxDailyLimitValue)
	// if there's an error, instead of breaking we will use the max limit value
	if err != nil {
		return maxDailyLimitValue
	}
	if compareValue > 0 {
		return maxDailyLimitValue
	}
	return dailyLimitValue
}

func (s *Service) getMaxLimitValueForControlType(cardLimit *ffPb.CardLimits_LimitsForControlTypePerLocation, ccAccount *ffAccPb.CreditAccount) *money.Money {
	switch {
	case cardLimit.GetControlType() == ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM && ffPkg.IsCreditCardProgramSecured(ccAccount.GetCardProgram()):
		ccLimit := moneyPkg.ParseFloat(float64(ccAccount.GetTotalLimit()), moneyPkg.RupeeCurrencyCode)
		return moneyPkg.Multiply(ccLimit, decimal.NewFromFloat32(s.conf.SimplifiConfig.ControlConfig.AtmMaxLimitMultiplier))
	default:
		return cardLimit.GetMaxDailyLimitValue()
	}
}

// GetLandingInfo checks the current onboarding request status for the user and returns next action present in the
// card onboarding request, if onboarding request does not exist for the user we will redirect user to the first
// card onboarding screen.
func (s *Service) GetLandingInfo(ctx context.Context, req *ffPb.GetLandingInfoRequest) (*ffPb.GetLandingInfoResponse, error) {
	var (
		res                     = &ffPb.GetLandingInfoResponse{}
		creditCardRequestHeader = req.GetCreditCardRequestHeader()
		isFireflyV2Enabled      = s.isFireflyV2Enabled(ctx, req.GetActorId())
	)
	if creditCardRequestHeader == nil {
		creditCardRequestHeader = &types.CreditCardRequestHeader{}
	}

	showHorizontalLayout, err := s.isFeatureEnabled(ctx, req.GetActorId(), types.Feature_FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT)
	if err != nil {
		// not throwing error from here as we will not block get landing info
		logger.Error(ctx, "error in checking if Horizontal Layout Intro Screen feature is enabled",
			zap.Error(err))
	}

	cardRequests, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, req.GetActorId(),
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
		[]ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CARD_ID,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID,
		},
	)

	if len(cardRequests) > 0 {
		res.CardProgramType = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram().GetCardProgramType()
		res.CardProgram = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()
	}

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		if isFireflyV2Enabled {
			res.Status = rpc.StatusOk()
			res.IsCcSdkFlowEnabledForUser = true
			return res, nil
		}
		if s.genConf.DisableCreditCardOnboarding() && s.genConf.SkipEligibilityCheckForDisabledCCOnboarding() {
			res.Status = rpc.StatusOk()
			res.NextAction = helper.GetOnboardingDisabledScreenDeeplink(s.genConf.DisabledOnboardingScreenOptions())
			return res, nil
		}
		eligibilityResp, eligibleErr := s.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{
			ActorId:                 req.GetActorId(),
			Vendor:                  ffEnumsPb.Vendor_FEDERAL,
			CreditCardRequestHeader: creditCardRequestHeader,
		})
		switch {
		case eligibleErr != nil:
			logger.Error(ctx, "error in fetching card eligibility for the user", zap.Error(eligibleErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		case eligibilityResp.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "card exists for the user without onboarding card request", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		case !eligibilityResp.GetStatus().IsSuccess():
			logger.Error(ctx, fmt.Sprintf("[%v] status received from FetchCreditCardEligibility RPC", eligibilityResp.GetStatus()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil

		case eligibilityResp.GetIsUserFiRejected():
			logger.Info(ctx, "fi rejected user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusOk()
			res.NextAction = s.getUserFeedbackScreenWithFallback(ctx, req.GetActorId(), helper.GetWaitlistIntroDeeplinkV2(eligibilityResp.GetCardProgram()), eligibilityResp.GetCardProgram())
			return res, nil

		case eligibilityResp.GetIsUserWebApproved():
			logger.Info(ctx, "user has been approved in the web eligibility check based on their credit report", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusOk()
			res.NextAction = helper.GetWebApprovedDeeplink(eligibilityResp.GetCardProgram())
			return res, nil
		case !eligibilityResp.GetIsUserCcEligible():
			res.NextAction, err = s.getNextActionForIneligibleUser(ctx, req.GetActorId(), helper.GetWaitlistIntroDeeplinkV2(eligibilityResp.GetCardProgram()), eligibilityResp.GetCardProgram())
			if err != nil {
				logger.Error(ctx, "failed to get next action for ineligible user", zap.Error(err))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.OfferId = eligibilityResp.GetOfferId()

		default:
			if s.genConf.DisableCreditCardOnboarding() {
				res.Status = rpc.StatusOk()
				res.OfferId = eligibilityResp.GetOfferId()
				res.CardProgramType = eligibilityResp.GetCardProgramType()
				res.CardProgram = eligibilityResp.GetCardProgram()
				res.NextAction = s.getUserFeedbackScreenWithFallback(ctx, req.GetActorId(), helper.GetOnboardingPausedDeeplink(), eligibilityResp.GetCardProgram())
				return res, nil
			}
			logger.Info(ctx, "User landed on credit card offers screen", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			creditCardRequestHeader.CardProgram = ffPkg.GetCardProgramStringFromCardProgram(eligibilityResp.GetCardProgram())
			res.NextAction, err = helper.GetOnboardingNextActionForUser(ctx, nil, eligibilityResp.GetCardProgram(), eligibilityResp.GetAvailableLimit(), s.genConf, showHorizontalLayout, creditCardRequestHeader)
			if err != nil {
				logger.Error(ctx, "failed to get next action for the user", zap.Error(err))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.OfferId = eligibilityResp.GetOfferId()
			res.CardProgramType = eligibilityResp.GetCardProgramType()
			res.CardProgram = eligibilityResp.GetCardProgram()
		}

	case len(cardRequests) != 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		// When the user have a credit card
		latestCardRequest := cardRequests[0]

		// NOTE: This is specific handling added to enable CC V2 flow for users who have closed their credit card but are part of CC Internal group
		// Ideally we are supposed to let users through CC V2 flow if users have had CC onboarded in older flow but now have closed their card.
		isCcUser, ccUserCheckErr := s.IsCreditCardUser(ctx, &ffPb.IsCreditCardUserRequest{
			ActorId: req.GetActorId(),
		})
		if rpcErr := epifigrpc.RPCError(isCcUser, ccUserCheckErr); rpcErr != nil {
			logger.Error(ctx, "error in checking if user is CC user", zap.Error(rpcErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		if !isCcUser.GetIsCreditCardUser() && isFireflyV2Enabled {
			res.IsCcSdkFlowEnabledForUser = true
			res.Status = rpc.StatusOk()
			return res, nil
		}

		nextAction, shouldRedirectToSdkFlow, getNextActionErr := s.getNextActionForUserWithCreditCard(ctx, req.GetActorId(), isFireflyV2Enabled)
		if getNextActionErr != nil {
			logger.Error(ctx, "unable to get next action for CC onboarded user", zap.Error(getNextActionErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if shouldRedirectToSdkFlow {
			res.IsCcSdkFlowEnabledForUser = true
			res.Status = rpc.StatusOk()
			return res, nil
		}

		res.NextAction = nextAction
		res.OfferId = latestCardRequest.GetRequestDetails().GetCardOnboardingDetails().GetOfferId()

	case len(cardRequests) != 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
		len(cardRequests) != 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_MANUAL_INTERVENTION:
		if isFireflyV2Enabled {
			res.Status = rpc.StatusOk()
			res.IsCcSdkFlowEnabledForUser = true
			return res, nil
		}
		if s.genConf.DisableCreditCardOnboarding() && s.genConf.SkipEligibilityCheckForDisabledCCOnboarding() {
			res.Status = rpc.StatusOk()
			res.NextAction = helper.GetOnboardingDisabledScreenDeeplink(s.genConf.DisabledOnboardingScreenOptions())
			return res, nil
		}
		eligibilityResp, eligibleErr := s.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{
			ActorId:                 req.GetActorId(),
			Vendor:                  ffEnumsPb.Vendor_FEDERAL,
			CreditCardRequestHeader: creditCardRequestHeader,
		})
		switch {
		case eligibilityResp.GetStatus().IsAlreadyExists():
			logger.Error(ctx, "card already exists for the user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			dl, _, dlErr := s.getNextActionForUserWithCreditCard(ctx, req.GetActorId(), false)
			if dlErr != nil {
				logger.Error(ctx, "error getting onboarded user deeplink", zap.Error(dlErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.NextAction = dl
			res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
			res.CardProgram = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()
			res.CardProgramType = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram().GetCardProgramType()
			res.Status = rpc.StatusOk()
			return res, nil
		case epifigrpc.RPCError(eligibilityResp, eligibleErr) != nil:
			logger.Error(ctx, "error in FetchCreditCardEligibility RPC resp", zap.Error(epifigrpc.RPCError(eligibilityResp, eligibleErr)), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil

		case eligibilityResp.GetIsUserFiRejected():
			logger.Info(ctx, "fi rejected user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.NextAction = s.getUserFeedbackScreenWithFallback(ctx, req.GetActorId(), helper.GetRetryableFailureDeeplink(helper.GetOfferExpiredScreenDeeplink(), cardRequests[0].GetRequestDetails().GetCardProgram()), cardRequests[0].GetRequestDetails().GetCardProgram())
			res.Status = rpc.StatusOk()
			return res, nil

		case !eligibilityResp.GetIsUserCcEligible():
			res.NextAction, err = s.getNextActionForIneligibleUser(ctx, req.GetActorId(), helper.GetRetryableFailureDeeplink(helper.GetOfferExpiredScreenDeeplink(), cardRequests[0].GetRequestDetails().GetCardProgram()), cardRequests[0].GetRequestDetails().GetCardProgram())
			if err != nil {
				logger.Error(ctx, "failed to get next action for ineligible user", zap.Error(err))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()

		default:
			creditCardRequestHeader.CardProgram = ffPkg.GetCardProgramStringFromCardProgram(eligibilityResp.GetCardProgram())
			if s.genConf.DisableCreditCardOnboarding() {
				res.Status = rpc.StatusOk()
				res.NextAction = s.getUserFeedbackScreenWithFallback(ctx, req.GetActorId(), helper.GetOnboardingPausedDeeplink(), eligibilityResp.GetCardProgram())
				res.OfferId = eligibilityResp.GetOfferId()
				res.CardProgramType = eligibilityResp.GetCardProgramType()
				res.CardProgram = eligibilityResp.GetCardProgram()
				return res, nil
			}
			if cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
				logger.Info(ctx, "user landed on credit card offers screen", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
				res.NextAction, err = helper.GetOnboardingNextActionForUser(ctx, nil, eligibilityResp.GetCardProgram(), eligibilityResp.GetAvailableLimit(), s.genConf, showHorizontalLayout, creditCardRequestHeader)
				if err != nil {
					logger.Error(ctx, "failed to get next action for the user", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
			} else {
				redirectAction, err := helper.GetOnboardingNextActionForUser(ctx, nil, eligibilityResp.GetCardProgram(),
					eligibilityResp.GetAvailableLimit(), s.genConf, showHorizontalLayout, creditCardRequestHeader)
				if err != nil {
					logger.Error(ctx, "failed to get next action for the user", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
				res.NextAction = helper.GetRetryableFailureDeeplink(redirectAction, eligibilityResp.GetCardProgram())
			}
			res.OfferId = eligibilityResp.GetOfferId()
			res.CardProgramType = eligibilityResp.GetCardProgramType()
			res.CardProgram = eligibilityResp.GetCardProgram()
		}

	case len(cardRequests) != 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE:
		if isFireflyV2Enabled {
			res.Status = rpc.StatusOk()
			res.IsCcSdkFlowEnabledForUser = true
			return res, nil
		}
		if s.genConf.DisableCreditCardOnboarding() && s.genConf.SkipEligibilityCheckForDisabledCCOnboarding() {
			res.Status = rpc.StatusOk()
			res.NextAction = helper.GetOnboardingDisabledScreenDeeplink(s.genConf.DisabledOnboardingScreenOptions())
			return res, nil
		}
		res.NextAction = s.getUserFeedbackScreenWithFallback(ctx, req.GetActorId(), helper.GetNonRetryableFailureDeeplink(), cardRequests[0].GetRequestDetails().GetCardProgram())
		res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()

	case len(cardRequests) != 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		eligibilityResp, eligibilityErr := s.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{
			ActorId: req.GetActorId(), Vendor: ffEnumsPb.Vendor_FEDERAL,
		})
		switch {
		case eligibilityErr != nil:
			logger.Error(ctx, "error in fetching card eligibility for the user", zap.Error(eligibilityErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil

		case eligibilityResp.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "card already exists for the user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			dl, _, dlErr := s.getNextActionForUserWithCreditCard(ctx, req.GetActorId(), false)
			if dlErr != nil {
				logger.Error(ctx, "error getting onboarded user deeplink", zap.Error(dlErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.NextAction = dl
			res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
			res.CardProgram = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()
			res.CardProgramType = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram().GetCardProgramType()

		case !eligibilityResp.GetStatus().IsSuccess():
			logger.Error(ctx, fmt.Sprintf("[%v] status received from FetchCreditCardEligibility RPC", eligibilityResp.GetStatus()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil

		case eligibilityResp.GetIsUserFiRejected():
			logger.Info(ctx, "fi rejected user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.NextAction = helper.GetOnboardingDisabledScreenDeeplink(s.genConf.DisabledOnboardingScreenOptions())
			res.Status = rpc.StatusOk()
			return res, nil

		case !eligibilityResp.GetIsUserCcEligible():
			res.NextAction = helper.GetOnboardingDisabledScreenDeeplink(s.genConf.DisabledOnboardingScreenOptions())
			res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()

		default:
			// fetch offer
			if s.genConf.DisableCreditCardOnboarding() {
				res.Status = rpc.StatusOk()
				res.NextAction = helper.GetOnboardingDisabledScreenDeeplink(s.genConf.DisabledOnboardingScreenOptions())
				return res, nil
			}
			offerResp, fetchOfferErr := s.ccOffersDao.GetById(ctx, cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId())
			switch {
			case fetchOfferErr != nil:
				logger.Error(ctx, "error fetching cc offer from db", zap.Error(fetchOfferErr))
				res.Status = rpc.StatusInternal()
				return res, nil

			case datetime.IsAfter(timestamppb.Now(), offerResp.GetValidTill()): // offer with given offer id is expired
				shouldFailRequest, err := s.handleOfferExpiry(ctx, cardRequests[0])
				if err != nil {
					logger.Error(ctx, "error in handling offer expiry", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
				if !shouldFailRequest {
					res.Status = rpc.StatusOk()
					res.NextAction = cardRequests[0].GetNextAction()
					res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
					res.CardProgram = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()
					res.CardProgramType = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram().GetCardProgramType()
					return res, nil
				}
				// mark current card request as failed
				cardRequests[0].Status = ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED
				updErr := s.cardRequestDao.Update(ctx, cardRequests[0], []ffEnumsPb.CardRequestFieldMask{
					ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
				})
				if updErr != nil {
					logger.Error(ctx, "error updating card request state to failed", zap.Error(updErr))
					res.Status = rpc.StatusInternal()
					return res, nil
				}

				// return deeplink of restart application screen
				cardProgram := eligibilityResp.GetCardProgram()
				creditCardRequestHeader.CardProgram = ffPkg.GetCardProgramStringFromCardProgram(cardProgram)
				redirectAction, err := helper.GetOnboardingNextActionForUser(ctx, nil, cardProgram,
					eligibilityResp.GetAvailableLimit(), s.genConf, showHorizontalLayout, creditCardRequestHeader)
				if err != nil {
					logger.Error(ctx, "failed to get next action for the user", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
				res.NextAction = helper.GetRetryableFailureDeeplink(redirectAction, cardProgram)
				res.OfferId = eligibilityResp.GetOfferId()
				res.CardProgram = cardProgram
				res.CardProgramType = cardProgram.GetCardProgramType()
			default:
				res.NextAction = cardRequests[0].GetNextAction()
				res.OfferId = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
				res.CardProgram = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()
				res.CardProgramType = cardRequests[0].GetRequestDetails().GetCardOnboardingDetails().GetCardProgram().GetCardProgramType()
			}
		}

	case err != nil:
		logger.Error(ctx, "error in fetching onboarding request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		latestCardRequest := cardRequests[0]
		if s.genConf.DisableCreditCardOnboarding() {
			res.Status = rpc.StatusOk()
			res.NextAction = s.getUserFeedbackScreenWithFallback(ctx, req.GetActorId(), helper.GetOnboardingPausedDeeplink(), cardRequests[0].GetRequestDetails().GetCardProgram())
			res.OfferId = latestCardRequest.GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
			res.CardProgramType = latestCardRequest.GetRequestDetails().GetCardProgram().GetCardProgramType()
			res.CardProgram = latestCardRequest.GetRequestDetails().GetCardProgram()
			return res, nil
		}
		res.NextAction = latestCardRequest.GetNextAction()
		res.OfferId = latestCardRequest.GetRequestDetails().GetCardOnboardingDetails().GetOfferId()
		res.CardProgramType = latestCardRequest.GetRequestDetails().GetCardProgram().GetCardProgramType()
		res.CardProgram = latestCardRequest.GetRequestDetails().GetCardProgram()
	}
	res.Status = rpc.StatusOk()
	if res.GetCardProgram() == nil && res.OfferId != "" {
		offer, err := s.ccOffersDao.GetById(ctx, res.GetOfferId())
		if err == nil {
			res.CardProgram = offer.GetCardProgram()
		} else {
			logger.Error(ctx, "error fetching offer: ", zap.Error(err))
		}
	}
	return res, nil
}

// handleOfferExpiry method to check if user has a new offer in case of older offer getting expired
// we will return a boolean to check if request has to be failed
func (s *Service) handleOfferExpiry(ctx context.Context, cardRequest *ffPb.CardRequest) (bool, error) {
	var (
		cardRequestFieldMask []ffEnumsPb.CardRequestFieldMask
	)
	newOffer, err := s.ccOffersDao.GetActiveOfferByActorIdAndVendorAndCardProgram(ctx, cardRequest.GetActorId(),
		ffEnumsPb.Vendor_FEDERAL, ffPkg.GetCardProgramStringFromCardProgram(cardRequest.GetRequestDetails().GetCardProgram()))
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "user does not have any active offer for the given card program")
		return true, nil
	case err != nil:
		return false, fmt.Errorf("error in fetching offer %w", err)
	default:
	}
	cardCreationRequestStage, err := s.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, cardRequest.GetId(),
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CARD_CREATION, nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "user has not reached card creation stage")
	case err != nil:
		return false, fmt.Errorf("error in fetching card creation stage")
	case cardCreationRequestStage.GetStatus() == ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED:
		logger.Info(ctx, "user is currently on card creation stage")
	case cardCreationRequestStage.GetStatus() != ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS:
		return true, nil
	default:
		logger.Info(ctx, "card already created for user")
		return false, nil
	}

	cardRequest.GetRequestDetails().GetCardOnboardingDetails().CardLimit = newOffer.GetOfferConstraints().GetLimit()
	cardRequest.GetRequestDetails().GetCardOnboardingDetails().OfferId = newOffer.GetId()
	if cardRequest.GetNextAction().GetScreen() == deeplinkPb.Screen_FIREFLY_CARD_ACTIVATION_SCREEN {
		// if user is still at activation screen we will update the limit details on that screen
		screenOptions := cardRequest.GetNextAction().GetFireflyCardActivationScreenOptions()
		limitText := fmt.Sprintf("%s", moneyPkg.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true))
		screenOptions.GetCardInfoItems()[0].SubTitle = &commontypes.Text{
			Text:      limitText,
			FontColor: screenOptions.GetCardInfoItems()[0].GetSubTitle().GetFontColor(),
			BgColor:   screenOptions.GetCardInfoItems()[0].GetSubTitle().GetBgColor(),
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: limitText,
			},
			FontStyle:        screenOptions.GetCardInfoItems()[0].GetSubTitle().GetFontStyle(),
			FontColorOpacity: screenOptions.GetCardInfoItems()[0].GetSubTitle().GetFontColorOpacity(),
			StringFormatters: screenOptions.GetCardInfoItems()[0].GetSubTitle().GetStringFormatters(),
			Alignment:        screenOptions.GetCardInfoItems()[0].GetSubTitle().GetAlignment(),
		}
		cardRequestFieldMask = append(cardRequestFieldMask, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION)
	}
	cardRequestFieldMask = append(cardRequestFieldMask, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS)
	err = s.cardRequestDao.Update(ctx, cardRequest, cardRequestFieldMask)
	if err != nil {
		return false, fmt.Errorf("error in updating card request %w", err)
	}
	return false, nil
}

func (s *Service) getNextActionForUserWithCreditCard(ctx context.Context, actorId string, isFireflyV2Enabled bool) (*deeplinkPb.Deeplink, bool, error) {
	cardId := ""
	creditCards, ccErr := s.creditCardDao.GetByActorId(ctx, actorId, []ffEnumsPb.CreditCardFieldMask{
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
	})
	if ccErr != nil {
		return nil, false, ccErr
	}
	if creditCards[0].GetCardState() == ffEnumsPb.CardState_CARD_STATE_CLOSED && isFireflyV2Enabled {
		return nil, true, nil
	}

	cardId = creditCards[0].GetId()

	creditAccountResp, err := s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
		GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: creditCards[0].GetAccountId()},
	})
	if te := epifigrpc.RPCError(creditAccountResp, err); te != nil {
		return nil, false, te
	}

	dl, dlErr := s.getNextActionForSuccessfullyOnboardedUser(ctx, actorId, cardId, creditAccountResp.GetAccount().GetCardProgram())
	return dl, false, dlErr
}

func (s *Service) getPrimaryRecommendation(ctx context.Context, actorId string, creditCardHeader *types.CreditCardRequestHeader) (*types.CardProgram, error) {
	// TODO(chandresh/priyansh) : Change this once we have support for aysnc rules run
	if creditCardHeader.GetAttributes() == nil {
		fetchRecommendationsResp, fetchErr := s.recommendationEngineClient.FetchCcRecommendationInfoForUser(ctx, &ffRePb.FetchCcRecommendationInfoForUserRequest{
			ActorId: actorId,
		})
		switch {
		case fetchErr != nil:
			return nil, fmt.Errorf("error while calling FetchCcRecommendationInfoForUser: %w", fetchErr)
		case fetchRecommendationsResp.GetStatus().IsRecordNotFound():
			logger.Debug(ctx, "record not found for cc recommendation")
		case !fetchRecommendationsResp.GetStatus().IsSuccess():
			return nil, fmt.Errorf("non succes status returned from FetchCcRecommendationInfoForUser, status: %s", fetchRecommendationsResp.GetStatus().String())
		default:
			// assign card program from primary recommendation
			return fetchRecommendationsResp.GetCardRecommendations()[0].GetCardProgram(), nil
		}
	}
	evalRecommendationResp, reErr := s.recommendationEngineClient.EvaluateCardRecommendationsForUser(ctx, &ffRePb.EvaluateCardRecommendationsForUserRequest{
		ActorId:                 actorId,
		CreditCardRequestHeader: creditCardHeader,
	})
	switch {
	case reErr != nil:
		return nil, fmt.Errorf("error in EvaluateCardRecommendationsForUser: %w", reErr)
	case evalRecommendationResp.GetStatus().IsFailedPrecondition(): // no active offer for user
		return nil, epifierrors.ErrRecordNotFound
	case !evalRecommendationResp.GetStatus().IsSuccess():
		return nil, fmt.Errorf("non success status returned from EvaluateCardRecommendationsForUser, status: %s", evalRecommendationResp.GetStatus().String())
	default:
		// do nothing
	}
	// assign card program from primary recommendation
	return evalRecommendationResp.GetCardRecommendations()[0].GetCardProgram(), nil
}

func (s *Service) isRealtimeEligibilityEnabled(ctx context.Context, actorId string) (bool, error) {
	// check if feature is enabled for the actor id, user group and stickiness constraint or not
	releaseConstraint := release.NewCommonConstraintData(types.Feature_FEATURE_CC_REALTIME_ELIGIBILITY).WithActorId(actorId)
	isEnabled, err := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		return false, fmt.Errorf("error in feature release evaluation %w", err)
	}
	return isEnabled, nil
}

func (s *Service) isBiometricValidationEnabled(ctx context.Context, actorId string) (bool, error) {
	// check if feature is enabled for the actor id, user group and stickiness constraint or not
	releaseConstraint := release.NewCommonConstraintData(types.Feature_FEATURE_BIOMETRIC_REVALIDATION).WithActorId(actorId)
	isEnabled, err := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		return false, fmt.Errorf("error in feature release evaluation %w", err)
	}
	return isEnabled, nil
}

func (s *Service) getNextActionForIneligibleUser(ctx context.Context, actorId string, fallbackScreen *deeplinkPb.Deeplink, cardProgram *types.CardProgram) (*deeplinkPb.Deeplink, error) {
	var nextAction *deeplinkPb.Deeplink

	// if user is not eligible then we will trigger eligibility check for the user
	isRealTimeEligibilityEnabled, err := s.isRealtimeEligibilityEnabled(ctx, actorId)
	if err != nil {
		// not throwing error from here as we will not block landing info call for real time
		// eligibility feature check and show feedback screen to the user
		logger.Error(ctx, "error in checking if realtime eligibility feature is enabled",
			zap.Error(err))
	}
	if isRealTimeEligibilityEnabled {
		nextAction, err = s.getNextActionFromEligibilityCheck(ctx, actorId)
		if err != nil {
			return nil, errors.Wrap(err, "error in checking card eligibility")
		}
	} else {
		nextAction = s.getUserFeedbackScreenWithFallback(ctx, actorId, fallbackScreen, cardProgram)
	}

	return nextAction, nil
}

// nolint: unparam
func (s *Service) getNextActionForSuccessfullyOnboardedUser(ctx context.Context, actorId, cardId string, cardProgram *types.CardProgram) (*deeplinkPb.Deeplink, error) {
	var nextAction *deeplinkPb.Deeplink
	// check if feature biometric revalidation is enabled or not
	isBiometricValidationEnabled, err := s.isBiometricValidationEnabled(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in checking if biometric validation feature is enabled", zap.Error(err))
	}

	if isBiometricValidationEnabled {
		// check if user has invalidated biometric details
		biometricsResp, biometricErr := s.biometricClient.GetBiometricsDetails(ctx, &biometrics.GetBiometricsDetailsRequest{ActorId: actorId})
		if te := epifigrpc.RPCError(biometricsResp, biometricErr); te != nil {
			// in case of failures, do not throw any errors
			// redirect to the CC dashboard as biometric validation is the best effort basis approach
			logger.Error(ctx, "error fetching biometric details", zap.Error(te))
			nextAction = helper.GetCreditCardDashboardDeeplinkWithRewardsCardTypeId(cardProgramTypeToRewardCardTypeMap[cardProgram.GetCardProgramType()])
			return nextAction, nil
		}

		switch biometricsResp.GetBiometricStatus() {
		case biometrics.BiometricStatus_BIOMETRIC_STATUS_REVOKED:
			// if biometrics status is revoked, start biometric revalidation
			nextAction = helper.GetCreditCardDashboardDeeplinkWithRewardsCardTypeId(cardId)
		default:
			// if biometrics status is success, redirect user to the CC dashboard
			nextAction = helper.GetCreditCardDashboardDeeplinkWithRewardsCardTypeId(cardProgramTypeToRewardCardTypeMap[cardProgram.GetCardProgramType()])
		}

	} else {
		// if biometric feature is not enabled, redirect user to the CC dashboard
		nextAction = helper.GetCreditCardDashboardDeeplinkWithRewardsCardTypeId(cardProgramTypeToRewardCardTypeMap[cardProgram.GetCardProgramType()])
	}

	return nextAction, nil
}

func (s *Service) getNextActionFromEligibilityCheck(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	cardRequests, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, actorId,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
		[]ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
		})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return helper.GetCreditCardRealTimeEligibilityCheckDeeplink()
	case err != nil:
		return nil, fmt.Errorf("error in fetching card requests for care eligibility check %w", err)
	case len(cardRequests) > 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		return cardRequests[0].GetNextAction(), nil
	case len(cardRequests) > 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE:
		return s.getUserFeedbackScreenWithFallback(ctx, actorId, cardRequests[0].GetNextAction(), cardRequests[0].GetRequestDetails().GetCardProgram()), nil
	default:
		return helper.GetCreditCardRealTimeEligibilityCheckDeeplink()
	}
}

func isValidAmount(amt *money.Money) bool {
	if amt == nil {
		return false
	}
	if !moneyPkg.IsPositive(amt) {
		return false
	}
	moneyFloat, _ := moneyPkg.ToDecimal(amt).Float64()
	return moneyFloat >= minimumValidOfferValue
}

func (s *Service) FetchCreditCardEligibility(ctx context.Context, req *ffPb.FetchCreditCardEligibilityRequest) (*ffPb.FetchCreditCardEligibilityResponse, error) {
	var (
		primaryRecommendedCardProgram *types.CardProgram
		res                           = &ffPb.FetchCreditCardEligibilityResponse{}
	)

	if req.GetVendor() == ffEnumsPb.Vendor_VENDOR_UNSPECIFIED {
		req.Vendor = ffEnumsPb.Vendor_FEDERAL
	}

	isCreditCardUserResp, err := s.IsCreditCardUser(ctx, &ffPb.IsCreditCardUserRequest{
		ActorId: req.GetActorId(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching credit card entry for the user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.IsUserCcEligible = false
	case !isCreditCardUserResp.GetIsCreditCardUser():
		if req.GetCreditCardRequestHeader().GetCardProgram() != "" {
			primaryRecommendedCardProgram = ffPkg.GetCardProgramFromCardProgramString(req.GetCreditCardRequestHeader().GetCardProgram())
		} else {
			isChoiceFrameworkEnabled, constraintCheckErr := s.isFeatureEnabled(ctx, req.GetActorId(), types.Feature_FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK)
			if constraintCheckErr != nil {
				// we will log the error here, consider choice framework disabled in this case and continue with the old flow
				logger.Error(ctx, "error evaluating feature release constraints", zap.Error(constraintCheckErr))
			}
			switch {
			case isChoiceFrameworkEnabled:
				cardProgram, reFetchErr := s.getPrimaryRecommendation(ctx, req.GetActorId(), req.GetCreditCardRequestHeader())
				switch {
				case errors.Is(reFetchErr, epifierrors.ErrRecordNotFound): // no offer for user
					res.IsUserCcEligible = false
					res.Status = rpc.StatusOk()
					return res, nil
				case reFetchErr != nil:
					logger.Error(ctx, "error in fetching cc offer for primary recommendation", zap.Error(reFetchErr))
					res.IsUserCcEligible = false
					res.Status = rpc.StatusInternal()
					return res, nil
				default:
					// do nothing
				}
				primaryRecommendedCardProgram = cardProgram
			default:
				isUserEligibleForSecuredCard, offerId, limit, cardProgram, err := s.isUserEligibleForSecuredCard(ctx, req.GetActorId(), req.GetVendor())
				switch {
				case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
					logger.Info(ctx, "no record found for cc offer, moving ahead with eligibility check")
				case err != nil:
					logger.Info(ctx, "error while fetching active offer for actor", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				case !isUserEligibleForSecuredCard:
					logger.Info(ctx, "no record found for cc offer, moving ahead with eligibility check")
				default:
					res.Status = rpc.StatusOk()
					res.OfferId = offerId
					res.CardProgramType = cardProgram.GetCardProgramType()
					res.AvailableLimit = limit
					res.IsUserCcEligible = true
					res.CardProgram = cardProgram
					return res, nil
				}
			}
		}

		if primaryRecommendedCardProgram.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
			// fetch offer from db
			ccOffers, offerFetchErr := s.ccOffersDao.GetAllActiveOffersByActorId(ctx, req.GetActorId())
			if offerFetchErr != nil {
				logger.Error(ctx, "error fetching offer by card program and actor", zap.Error(offerFetchErr))
				res.IsUserCcEligible = false
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			ccOffer := filterCreditCardOfferByVendorAndCardProgram(ccOffers, ffEnumsPb.Vendor_VENDOR_UNSPECIFIED, ffPkg.GetCardProgramStringFromCardProgram(primaryRecommendedCardProgram))[0]
			res.Status = rpc.StatusOk()
			res.OfferId = ccOffer.GetId()
			res.CardProgramType = ccOffer.GetCardProgram().GetCardProgramType()
			res.AvailableLimit = ccOffer.GetOfferConstraints().GetLimit()
			res.IsUserCcEligible = true
			res.CardProgram = ccOffer.GetCardProgram()
			return res, nil
		}

		// Check if user is risky
		// if yes, return user is not eligible
		isUserFiRejected, err := s.isUserFiRejected(ctx, req.GetActorId(), req.GetVendor())
		switch {
		case err != nil:
			logger.Error(ctx, "unable to check if user is rejected", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		case isUserFiRejected:
			logger.Info(ctx, "user is fi rejected for cc", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.IsUserCcEligible = false
			res.IsUserWebApproved = false
			res.IsUserFiRejected = true
			res.Status = rpc.StatusOk()
			return res, nil
		}

		ccLimitRes, limitErr := s.limitEstimatorClient.GetCreditCardConservativeLimit(ctx, &limitEstimatorPb.GetCreditCardConservativeLimitRequest{
			ActorId:          req.GetActorId(),
			Vendor:           ffEnumsPb.Vendor_FEDERAL,
			ShouldCallVendor: req.GetShouldCallVendor(),
			CardProgram:      primaryRecommendedCardProgram,
		})
		switch {
		case limitErr != nil:
			logger.Error(ctx, "error in fetching cc limit details", zap.Error(limitErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.IsUserCcEligible = false
			res.Status = rpc.StatusInternal()
			return res, nil
		case ccLimitRes.GetStatus().IsRecordNotFound():
			res.IsUserCcEligible = false
			// Fetching web eligibility of the user. This will check the user's eligibility based on in-house checks
			// on the credit report of the user
			webEligibilityCardRequests, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, req.GetActorId(), ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK, nil)
			if err != nil || len(webEligibilityCardRequests) == 0 {
				res.IsUserWebApproved = false
				res.Status = rpc.StatusOk()
				return res, nil
			}
			webEligCardReq := webEligibilityCardRequests[0]
			if webEligCardReq.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
				res.EstimatedLimit = webEligCardReq.GetRequestDetails().GetData().(*ffPb.CardRequestDetails_RealTimeProfileValidationCheckDetails).RealTimeProfileValidationCheckDetails.GetEstimatedCreditLimit()
				res.IsUserWebApproved = true
			}
		case ccLimitRes.GetStatus().IsResourceExhausted():
			res.IsUserCcEligible = false

		case !ccLimitRes.GetStatus().IsSuccess():
			logger.Error(ctx, fmt.Sprintf("[%v] response received from GetCreditCardConservativeLimit", ccLimitRes.GetStatus()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.IsUserCcEligible = false
			res.Status = rpc.StatusInternal()
			return res, nil

		default:
			if ccLimitRes.GetCardProgram().GetCardProgramSource() == types.CardProgramSource_CARD_PROGRAM_SOURCE_FI_BRE_APPROVED ||
				isValidAmount(ccLimitRes.GetConservativeLimit()) {
				res.IsUserCcEligible = true
			}
			res.OfferId = ccLimitRes.GetOfferId()
			res.AvailableLimit = ccLimitRes.GetConservativeLimit()
			res.CardProgramType = ccLimitRes.GetCardProgram().GetCardProgramType()
			res.CardProgram = ccLimitRes.GetCardProgram()
		}
	default:
		ccRes, getCcErr := s.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{
			GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: req.GetActorId()},
			SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{
				ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
			},
		})
		if getCcErr != nil {
			logger.Error(ctx, "error in fetching credit card", zap.Error(getCcErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.IsUserCcEligible = false
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		accResp, accErr := s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
			GetBy: &ffAccPb.GetAccountRequest_AccountId{
				AccountId: ccRes.GetCreditCard().GetAccountId(),
			},
		})
		if te := epifigrpc.RPCError(accResp, accErr); te != nil {
			logger.Error(ctx, "error fetching account info: ", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.IsUserCcEligible = true
		res.Status = rpc.StatusAlreadyExists()
		res.CardProgramType = accResp.GetAccount().GetCardProgram().GetCardProgramType()
		res.CardProgram = accResp.GetAccount().GetCardProgram()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

// CollectCardDeliveryAddress rpc store the address type for card delivery address in card onboarding request and sends
// signal for onboarding workflow.
func (s *Service) CollectCardDeliveryAddress(ctx context.Context, req *ffPb.CollectCardDeliveryAddressRequest) (*ffPb.CollectCardDeliveryAddressResponse, error) {
	var (
		res = &ffPb.CollectCardDeliveryAddressResponse{}
	)
	cardRequest, err := s.cardRequestDao.GetById(ctx, req.GetCardRequestId(), []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_WORKFLOW,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching card request", zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	cardRequest.GetRequestDetails().AddressType = req.GetAddressType()
	// store address in on-boarding and reissue card request, since address is collected at the start of the flow and there is a possibility
	// of the address stored in user db going stale by the end of the flow
	if req.GetAddressType() == types.AddressType_SHIPPING && (cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING || cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD) {
		userRes, rpcErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: cardRequest.GetActorId()},
		})
		if te := epifigrpc.RPCError(userRes, rpcErr); te != nil {
			logger.Error(ctx, "error fetching user", zap.Error(te), zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		addressRes, rpcErr := s.userClient.GetAllAddresses(ctx, &userPb.GetAllAddressesRequest{
			UserId: userRes.GetUser().GetId(),
		})
		if te := epifigrpc.RPCError(addressRes, rpcErr); te != nil {
			logger.Error(ctx, "error getting all addresses", zap.Error(te), zap.String(logger.USER_ID, userRes.GetUser().GetId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		var shippingAddress *postaladdress.PostalAddress
		addresses := addressRes.GetAddresses()[types.AddressType_SHIPPING.String()]
		if len(addresses.GetAddresses()) > 0 {
			shippingAddress = addresses.GetAddresses()[0]
		}
		// save address type and shipping address based on the workflow
		if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING && cardRequest.GetRequestDetails().GetCardOnboardingDetails() != nil {
			cardRequest.GetRequestDetails().GetCardOnboardingDetails().AddressType = req.GetAddressType()
			cardRequest.GetRequestDetails().GetCardOnboardingDetails().ShippingAddress = shippingAddress
		}
		if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD && cardRequest.GetRequestDetails().GetReissueRequestDetails() != nil {
			cardRequest.GetRequestDetails().GetReissueRequestDetails().AddressType = req.GetAddressType()
			cardRequest.GetRequestDetails().GetReissueRequestDetails().ShippingAddress = shippingAddress
		}
	}

	nextAction := helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.CardDeliveryAddressSubmissionPollingMessage)
	cardRequest.NextAction = nextAction
	var (
		currentScreens []deeplinkPb.Screen
	)
	if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD {
		currentScreens = []deeplinkPb.Screen{deeplinkPb.Screen_CREDIT_CARD_NEW_CARD_REQUEST_ADDRESS_SELECTION}
	} else {
		currentScreens = []deeplinkPb.Screen{deeplinkPb.Screen_CREDIT_CARD_ADDRESS_SELECTION_SCREEN, deeplinkPb.Screen_CREDIT_CARD_ADDRESS_SELECTION_V2_SCREEN}
	}
	err = s.cardRequestDao.UpdateWithNextActionDirtyWriteCheck(ctx, currentScreens, cardRequest, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION})
	switch {
	case errors.Is(err, epifierrors.ErrDirtyWrite):
		logger.Error(ctx, "case of dirty write detected", zap.Error(err))
	case err != nil:
		logger.Error(ctx, "error in updating address type for card delivery", zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	payload := &ffPb.AddressTypeSelectedSignalPayload{}
	payloadMarshal, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "error in marshalling payload", zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if sigErr := s.sendSignal(ctx, cardRequest.GetOrchestrationId(), string(ffNs.CreditCardAddressTypeSelectedSignal), payloadMarshal); sigErr != nil {
		logger.Error(ctx, "error sending the address type selected signal", zap.Error(sigErr), zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()), zap.String(logger.CLIENT_REQUEST_ID, cardRequest.GetOrchestrationId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED &&
		cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE {
		goroutine.Run(ctx, 10*time.Second, func(goCtx context.Context) {
			s.eventBroker.AddToBatch(goCtx, ffEvents.NewAppsFlyerEvent(cardRequest.GetActorId(), ffEvents.EventMagnifiStageChange, cardRequest.GetId(), "", cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramVendor().String(), "", ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE.String(), cardRequest.GetRequestDetails().GetCardProgram()))
		})
	}

	res.NextAction = nextAction
	res.Status = rpc.StatusOk()
	return res, nil
}

// CreateCard RPC is used to trigger the CreateCard activity. On trigger, the card creation activity will be executed
// and the card will be created on the vendor's end as well as our end
func (s *Service) CreateCard(ctx context.Context, req *ffPb.CreateCardRequest) (*ffPb.CreateCardResponse, error) {
	res := &ffPb.CreateCardResponse{}
	cardRequest, err := s.cardRequestDao.GetById(ctx, req.GetCardReqeustId(), []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID,
	})
	switch {
	case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "card request not found for the cardRequestId", zap.Error(err), zap.String(logger.CARD_REQUEST_ID, req.GetCardReqeustId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching card request from DB", zap.Error(err), zap.String(logger.CARD_REQUEST_ID, req.GetCardReqeustId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		// continue the normal flow
	}

	// if the card request is already successful, we would return the updated next action
	if cardRequest.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
		return &ffPb.CreateCardResponse{
			Status:     rpc.StatusOk(),
			NextAction: cardRequest.GetNextAction(),
		}, nil
	}

	// skip all checks for Fi lite and send signal
	// TODO(akk) - clean this up
	if cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE {
		payload := &ffPb.CreateCardSignalPayload{}
		payloadMarshal, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(payload)
		if err != nil {
			logger.Error(ctx, "error in marshalling payload for CreateCardSignal", zap.Error(err), zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if sigErr := s.sendSignal(ctx, cardRequest.GetOrchestrationId(), string(ffNs.CreditCardOnboardingCreateCardSignal), payloadMarshal); sigErr != nil {
			logger.Error(ctx, "error in sending signal to CreateCard activity", zap.Error(sigErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED {
			goroutine.Run(ctx, 10*time.Second, func(goCtx context.Context) {
				s.eventBroker.AddToBatch(goCtx, ffEvents.NewAppsFlyerEvent(cardRequest.GetActorId(), ffEvents.EventAmplifiStageChange, cardRequest.GetId(), "", cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramVendor().String(), "", ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CONSENT_COLLECTION.String(), cardRequest.GetRequestDetails().GetCardProgram()))
			})
		}

		var ccPollingDl *deeplinkPb.Deeplink
		switch {
		case cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
			ccPollingDl = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.PostCardCreationPollingDisplayMessage)
		case cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE:
			ccPollingDl = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), "Great! Few more steps to go")
		default:
			ccPollingDl = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.RewardGenerationDisplayMessage)
		}

		cardRequest.RequestDetails.AddressType = req.GetAddressType()
		var defaultBillPaymentInfo *ffConf.CreditCardBillPaymentInfo
		for _, bpi := range s.conf.CreditCardBillPaymentInfo {
			if bpi.Default {
				defaultBillPaymentInfo = bpi
			}
		}
		// save default bill gen date for fi lite onboarding
		cardRequest.RequestDetails.BillGenDate = defaultBillPaymentInfo.BillGenDate
		cardRequest.RequestDetails.PaymentDueDate = defaultBillPaymentInfo.PaymentDueDate
		cardRequest.NextAction = ccPollingDl
		updErr := s.cardRequestDao.UpdateWithNextActionDirtyWriteCheck(ctx, []deeplinkPb.Screen{deeplinkPb.Screen_FIREFLY_CARD_ACTIVATION_SCREEN, deeplinkPb.Screen_CC_AMPLI_FI_SCREEN}, cardRequest, []ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		})
		switch {
		case errors.Is(updErr, epifierrors.ErrDirtyWrite):
			logger.Error(ctx, "dirty write detected")
		case updErr != nil:
			logger.Error(ctx, "error in updating next action in card request", zap.Error(updErr))
			return &ffPb.CreateCardResponse{
				Status: rpc.StatusInternalWithDebugMsg(updErr.Error()),
			}, nil
		}

		return &ffPb.CreateCardResponse{
			Status:      rpc.StatusOk(),
			NextAction:  ccPollingDl,
			CardProgram: cardRequest.GetRequestDetails().GetCardProgram(),
		}, nil
	}

	eligibilityRes, eligibleErr := s.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{
		ActorId:          cardRequest.GetActorId(),
		Vendor:           ffEnumsPb.Vendor_FEDERAL,
		ShouldCallVendor: true,
		CreditCardRequestHeader: &types.CreditCardRequestHeader{
			CardProgram: ffPkg.GetCardProgramStringFromCardProgram(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()),
		},
	})
	switch {
	case eligibleErr != nil:
		logger.Error(ctx, "error in FetchCreditCardEligibility call", zap.Error(err), zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	case eligibilityRes.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "card already exists for the user", zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))
		res.Status = rpc.StatusOk()
		res.NextAction = cardRequest.GetNextAction()
		return res, nil
	case !eligibilityRes.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("[%v] response from FetchCreditCardEligibility RPC", eligibilityRes.GetStatus()))
		res.Status = rpc.StatusInternal()
		return res, nil
	case !eligibilityRes.GetIsUserCcEligible():
		logger.Info(ctx, "user no longer eligible for cc", zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))

		cardRequest.Status = ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED
		cardRequest.NextAction = helper.GetOfferExpiredScreenDeeplink()
		updErr := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		})
		if updErr != nil {
			logger.Error(ctx, "error in updating card request", zap.Error(updErr), zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
			res.Status = rpc.StatusInternalWithDebugMsg(updErr.Error())
			return res, nil
		}

		res.Status = rpc.StatusOk()
		res.NextAction = helper.GetOfferExpiredScreenDeeplink()
		return res, nil
	default:
		logger.Info(ctx, "user still eligible for cc", zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))

	}

	if isBillGenCaptureSkipped(cardRequest) {
		if cardRequest.GetRequestDetails() == nil {
			cardRequest.RequestDetails = &ffPb.CardRequestDetails{}
		}

		// to handle empty bill gen date
		// use default bill gen date in such cases
		for _, billDate := range s.conf.CreditCardBillPaymentInfo {
			if billDate.BillGenDate == req.GetSelectedBillGenDate() {
				cardRequest.RequestDetails.BillGenDate = req.GetSelectedBillGenDate()
				cardRequest.RequestDetails.PaymentDueDate = req.GetSelectedPaymentDueDate()
				break
			} else if billDate.Default {
				cardRequest.RequestDetails.BillGenDate = billDate.BillGenDate
				cardRequest.RequestDetails.PaymentDueDate = billDate.PaymentDueDate
			}
		}

		updErr := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		})

		if updErr != nil {
			logger.Error(ctx, "error in updating  bill gen date and payment due date", zap.Error(updErr), zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
			res.Status = rpc.StatusInternalWithDebugMsg(updErr.Error())
			return res, nil
		}
	}

	// for a secured card, we need to update the status to IN_PROGRESS.
	if cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
		depositAccountId := cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSecuredCardOnboardingDetails().GetDepositAccountId()
		depositAccount, depositErr := s.rpcHelper.GetDepositAccount(ctx, &depositPb.GetByIdRequest{
			Id: depositAccountId,
		})
		if te := epifigrpc.RPCError(depositAccount, depositErr); te != nil {
			logger.Error(ctx, "error fetching deposit account: ", zap.String(logger.DEPOSIT_ACCOUNT_ID, depositAccountId), zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		cardRequestStage, cardReqStageErr := s.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, cardRequest.GetId(), ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CARD_CREATION, []ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS,
			ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID})
		switch {
		case errors.Is(cardReqStageErr, epifierrors.ErrRecordNotFound):
			logger.Info(ctx, "no card req stage found for user")
			res.Status = rpc.StatusInternal()
			return res, nil
		case cardReqStageErr != nil:
			logger.Error(ctx, "error fetching card request stage for the user: ", zap.Error(cardReqStageErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		if !helper.IsFdStatusValid(depositAccount.GetAccount()) {
			logger.Error(ctx, "deposit account is in invalid state,updating card request next action, status and stage status", zap.String(logger.STATE, depositAccount.GetAccount().GetState().String()))
			nextAction := helper.GetNextActionByDepositStatus(depositAccount.GetAccount(), cardRequest)
			cardRequest.NextAction = nextAction
			cardRequest.Status = ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED
			cardRequestStage.Status = ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED

			updErr := s.updateCardRequestAndStageWithDirtyWriteCheck(ctx, cardRequest, cardRequestStage,
				[]ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
				[]ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS},
				[]deeplinkPb.Screen{deeplinkPb.Screen_FIREFLY_CARD_ACTIVATION_SCREEN})
			switch {
			case errors.Is(updErr, epifierrors.ErrDirtyWrite):
				logger.Error(ctx, "dirty write detected")
				res.Status = rpc.StatusOk()
				res.NextAction = nextAction
				return res, nil
			case updErr != nil:
				logger.Error(ctx, "error in updating next action in card request", zap.Error(updErr))
				return &ffPb.CreateCardResponse{
					Status: rpc.StatusInternalWithDebugMsg(updErr.Error()),
				}, nil
			}
			res.NextAction = nextAction
			res.Status = rpc.StatusOk()
			return res, nil
		}

		cardRequestStage.Status = ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS
		updErr := s.cardRequestStageDao.Update(ctx, cardRequestStage, []ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS})
		if updErr != nil {
			logger.Error(ctx, "error in updating card req stage", zap.Error(updErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	payload := &ffPb.CreateCardSignalPayload{}
	payloadMarshal, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "error in marshalling payload for CreateCardSignal", zap.Error(err), zap.String(logger.ACTOR_ID_V2, cardRequest.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if sigErr := s.sendSignal(ctx, cardRequest.GetOrchestrationId(), string(ffNs.CreditCardOnboardingCreateCardSignal), payloadMarshal); sigErr != nil {
		logger.Error(ctx, "error in sending signal to CreateCard activity", zap.Error(sigErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var ccPollingDl *deeplinkPb.Deeplink
	switch {
	case eligibilityRes.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
		ccPollingDl = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.PostCardCreationPollingDisplayMessage)
	case eligibilityRes.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		ccPollingDl = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.CardCreationDisplayMessage)
	default:
		ccPollingDl = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.RewardGenerationDisplayMessage)
	}

	cardRequest.NextAction = ccPollingDl
	updErr := s.cardRequestDao.UpdateWithNextActionDirtyWriteCheck(ctx, []deeplinkPb.Screen{deeplinkPb.Screen_FIREFLY_CARD_ACTIVATION_SCREEN}, cardRequest, []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	switch {
	case errors.Is(updErr, epifierrors.ErrDirtyWrite):
		logger.Error(ctx, "dirty write detected")
	case updErr != nil:
		logger.Error(ctx, "error in updating next action in card request", zap.Error(updErr))
		return &ffPb.CreateCardResponse{
			Status: rpc.StatusInternalWithDebugMsg(updErr.Error()),
		}, nil
	}

	return &ffPb.CreateCardResponse{
		Status:      rpc.StatusOk(),
		NextAction:  ccPollingDl,
		CardProgram: cardRequest.GetRequestDetails().GetCardProgram(),
	}, nil
}

func isBillGenCaptureSkipped(cardRequest *ffPb.CardRequest) bool {
	return cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureStage()
}

func (s *Service) GetBillingDatesInfo(ctx context.Context, req *ffPb.GetBillingDatesInfoRequest) (*ffPb.GetBillingDatesInfoResponse, error) {
	billGenDays := make([]*ffPb.CreditCardBillingDates, 0)
	billGenDaysCfgList := s.conf.CreditCardBillPaymentInfo
	for _, info := range billGenDaysCfgList {
		billGenDays = append(billGenDays, &ffPb.CreditCardBillingDates{
			BillGenDate:    info.BillGenDate,
			PaymentDueDate: info.PaymentDueDate,
			Default:        info.Default,
		})
	}
	return &ffPb.GetBillingDatesInfoResponse{
		Status: rpc.StatusOk(),
		Dates:  billGenDays,
	}, nil
}

func (s *Service) CheckDoOnceActivityStatus(ctx context.Context, req *ffPb.CheckDoOnceActivityStatusRequest) (*ffPb.CheckDoOnceActivityStatusResponse, error) {
	res := &ffPb.CheckDoOnceActivityStatusResponse{}
	isDone, err := s.doOnce.IsDone(ctx, req.GetDoOnceTaskId())
	if err != nil {
		logger.Error(ctx, "error in checking done status of activity", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.IsTaskDone = isDone
	return res, nil
}

// SetCardBillingDates updates the bill gen date and payment due date in the card_requests table
// It will then signal the card onboarding workflow to continue
func (s *Service) SetCardBillingDates(ctx context.Context, req *ffPb.SetCardBillingDatesRequest) (*ffPb.SetCardBillingDatesResponse, error) {
	var (
		res = &ffPb.SetCardBillingDatesResponse{}
	)
	cardRequest, err := s.cardRequestDao.GetById(ctx, req.GetCardRequestId(), []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
	})

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching card request", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	if cardRequest.GetRequestDetails() == nil {
		cardRequest.RequestDetails = &ffPb.CardRequestDetails{}
	}

	cardRequest.RequestDetails.BillGenDate = req.GetBillingDates().GetBillGenDate()
	cardRequest.RequestDetails.PaymentDueDate = req.GetBillingDates().GetPaymentDueDate()
	cardRequest.NextAction = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.AuthInitialisationPollingMessage)
	updErr := s.cardRequestDao.UpdateWithNextActionDirtyWriteCheck(ctx, []deeplinkPb.Screen{deeplinkPb.Screen_CREDIT_CARD_BILL_DATES_SELECTION_SCREEN}, cardRequest, []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	switch {
	case errors.Is(updErr, epifierrors.ErrDirtyWrite):
		logger.Error(ctx, "dirty write detected")
	case updErr != nil:
		logger.Error(ctx, "error in updating  bill gen date and payment due date", zap.Error(updErr))
		res.Status = rpc.StatusInternalWithDebugMsg(updErr.Error())
		return res, nil
	}

	payload := &ffPb.BillingDetailsSubmittedPayload{}
	payloadMarshal, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "error in marshalling CreditCardBillingDateSelectedSignal payload", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
	}
	if sigErr := s.sendSignal(ctx, cardRequest.GetOrchestrationId(), string(ffNs.CreditCardBillingDateSelectedSignal), payloadMarshal); sigErr != nil {
		logger.Error(ctx, "error in sending signal to card onboarding workflow", zap.Error(sigErr))
		res.Status = rpc.StatusInternalWithDebugMsg(sigErr.Error())
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.NextAction = helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.AuthInitialisationPollingMessage)
	return res, nil
}

func (s *Service) sendSignal(ctx context.Context, orchId string, signalName string, payload []byte) error {
	_, err := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     orchId,
				Client: workflowPb.Client_FIREFLY,
			},
		},
		SignalId:         signalName,
		Payload:          payload,
		Ownership:        commontypes.Ownership_EPIFI_TECH,
		QualityOfService: celestialPb.QoS_BEST_EFFORT,
	})
	if err != nil {
		return errors.Wrap(err, "error publishing pin set signal to workflow")
	}
	return nil
}

func (s *Service) GetCreditCard(ctx context.Context, req *ffPb.GetCreditCardRequest) (*ffPb.GetCreditCardResponse, error) {
	var (
		res         = &ffPb.GetCreditCardResponse{}
		creditCard  *ffPb.CreditCard
		creditCards []*ffPb.CreditCard
		err         error
	)
	switch req.GetBy.(type) {
	case *ffPb.GetCreditCardRequest_CreditCardId:
		creditCard, err = s.creditCardDao.GetById(ctx, req.GetCreditCardId(), req.GetSelectFieldMasks())
	case *ffPb.GetCreditCardRequest_VendorIdentifier:
		creditCard, err = s.creditCardDao.GetByVendorIdentifier(ctx, req.GetVendorIdentifier(), req.GetSelectFieldMasks())
	case *ffPb.GetCreditCardRequest_ActorId:
		creditCards, err = s.creditCardDao.GetByActorId(ctx, req.GetActorId(), req.GetSelectFieldMasks())
		if len(creditCards) != 0 {
			creditCard = creditCards[0]
		}
	}
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error in fetching credit card from db", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.CreditCard = creditCard
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) InitiateCardReq(ctx context.Context, req *ffPb.InitiateCardReqRequest) (*ffPb.InitiateCardReqResponse, error) {
	var (
		res                   = &ffPb.InitiateCardReqResponse{}
		cardRequest           *ffPb.CardRequest
		createNewRequest      bool
		savedCard             *ffPb.CreditCard
		savedCards            []*ffPb.CreditCard
		creditAccountResponse *ffAccPb.GetAccountResponse
		err                   error
	)

	if req.GetCardId() != "" {
		savedCard, err = s.creditCardDao.GetById(ctx, req.GetCardId(), []ffEnumsPb.CreditCardFieldMask{
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_VENDOR,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_LIMITS,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CONTROLS_DATA,
			ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
		})
	} else {
		savedCards, err = s.creditCardDao.GetByActorId(ctx, req.GetActorId(), nil)
		if len(savedCards) != 0 {
			savedCard = savedCards[0]
		}
	}

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "card not found", zap.String(logger.CARD_ID, req.GetCardId()), zap.String(logger.WORKFLOW,
			req.GetCardRequestWorkFlow().String()))
	case err != nil:
		logger.Error(ctx, "error in fetching card", zap.String(logger.CARD_ID,
			req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "fetched card successfully", zap.String(logger.CARD_ID,
			req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()))
	}

	actorId := savedCard.GetActorId()
	if len(actorId) == 0 {
		actorId = req.GetActorId()
	}

	if isCardStateValid := isCardStateValidForWorkflowType(savedCard.GetCardState(), req.GetCardRequestWorkFlow()); !isCardStateValid {
		logger.Error(ctx, fmt.Sprintf("card state : %s invalid for workflow type : %s", savedCard.GetCardState().String(), req.GetCardRequestWorkFlow().String()))
		res.Status = rpc.StatusOk()
		res.NextAction = helper.GetCreditCardBottomViewDeeplink(savedCard.GetId(), alertCircleIcon, cardStateInvalidDisplayTitle, cardStateInvalidDisplayDesc, &deeplinkPb.Cta{
			Text:     "Ok, got it",
			Deeplink: nil,
		})
		return res, nil
	}

	// for record not found case for savedCard
	// recordNotFound is expected in web eligibility flows
	if savedCard.GetAccountId() != "" {
		creditAccountResponse, err = s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
			GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: savedCard.GetAccountId()},
		})
		if te := epifigrpc.RPCError(creditAccountResponse, err); te != nil {
			logger.Error(ctx, "error in fetching credit account", zap.String(logger.ACCOUNT_ID, savedCard.GetAccountId()), zap.Error(te))
		}
	}

	cardRequest, _, err = s.validateAndFetchExistingRequest(ctx, savedCard, req.GetCardRequestWorkFlow(), actorId, req.GetClientRequestId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Debug(ctx, "no card request for workflow", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()))
		createNewRequest = true
		// applicable only in case of duplicate client request id.
	case errors.Is(err, epifierrors.ErrAlreadyExists):
		logger.Error(ctx, "a card request already exists for the given id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		res.Status = rpc.StatusAlreadyExists()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching card request", zap.Error(err), zap.String(logger.CARD_ID,
			req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// if there are pending card requests, we will handle them on workflow basis, for card reissue, activation
	// and onboarding flows we will proceed with the existing request and return the next action from card request
	// if it's not in created state.
	// For other flows like view card details, freeze/unfreeze, controls and limit changes we will fail the existing
	// request and create new request for the workflow
	if cardRequest != nil {
		switch cardRequest.GetWorkflow() {
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ACTIVATION,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_WELCOME_OFFER:
			if cardRequest.GetStatus() != ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED {
				logger.Info(ctx, "existing request present for workflow..returning next action", zap.String(logger.CARD_REQUEST_ID,
					cardRequest.GetId()), zap.String(logger.WORKFLOW, cardRequest.GetWorkflow().String()))
				res.NextAction = cardRequest.GetNextAction()
				res.Status = rpc.StatusOk()
				return res, nil
			}
			createNewRequest = false
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_VIEW_CARD_DETAILS,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CONTROL_CHANGE,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_LIMIT_CHANGE,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_FREEZE_UNFREEZE,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET,
			ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_BIOMETRIC_REVALIDATION:
			var (
				cardRequestStage          *ffPb.CardRequestStage
				cardRequestFieldMask      []ffEnumsPb.CardRequestFieldMask
				cardRequestStageFieldMask []ffEnumsPb.CardRequestStageFieldMask
			)
			logger.Info(ctx, "failing existing request", zap.String(logger.WORKFLOW, cardRequest.GetWorkflow().String()),
				zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
			cardRequest.Status = ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED
			cardRequestFieldMask = append(cardRequestFieldMask, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS)

			cardRequestStages, err := s.cardRequestStageDao.GetByCardRequestId(ctx, cardRequest.GetId(),
				[]ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS,
					ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_SUB_STATUS})
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				err = s.cardRequestDao.Update(ctx, cardRequest,
					[]ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS})
				if err != nil {
					logger.Error(ctx, "error in updating card request to failed", zap.String(logger.CARD_REQUEST_ID,
						cardRequest.GetId()), zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
			case err != nil:
				logger.Error(ctx, "error in fetching card request stages", zap.String(logger.CARD_REQUEST_ID,
					cardRequest.GetId()), zap.Error(err))
				res.Status = rpc.StatusInternal()
				return res, nil
			default:
				cardRequestStage = cardRequestStages[0]
				cardRequestStage.Status = ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED
				cardRequestStage.SubStatus = ffEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_DECLINED_BY_USER
				cardRequestStageFieldMask = append(cardRequestStageFieldMask,
					ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS,
					ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_SUB_STATUS)
				err = s.updateCardRequestAndStage(ctx, cardRequest, cardRequestStage, cardRequestFieldMask, cardRequestStageFieldMask)
				if err != nil {
					logger.Error(ctx, "error in updating card request and card request stage", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
			}
			createNewRequest = true
		// Create new request only if dispute is allowed. If not, return
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PROCESS_DISPUTE:
			createNewRequest, err = s.isDisputeAllowed(ctx, req, savedCard)
			switch {
			case errors.Is(err, epifierrors.ErrAlreadyExists):
				logger.Error(ctx, "dispute already exists for txn id and actor id", zap.String(logger.TXN_ID, req.GetDisputeData().GetTxnId()), zap.String(logger.ACTOR_ID_V2, savedCard.GetActorId()))
				res.Status = rpc.StatusAlreadyExists()
				return res, nil
			case err != nil:
				logger.Error(ctx, "error initiating dispute request", zap.Error(err), zap.String(logger.TXN_ID, req.GetDisputeData().GetTxnId()), zap.String(logger.ACTOR_ID_V2, savedCard.GetActorId()))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PAYMENT:
			createNewRequest = true
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_EXPORT_STATEMENT:
			if cardRequest.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS &&
				req.GetExportStatementData().GetBillId() == cardRequest.GetRequestDetails().GetExportStatementDetails().GetBillId() {
				logger.Info(ctx, "existing request present for workflow..returning success response", zap.String(logger.CARD_REQUEST_ID,
					cardRequest.GetId()), zap.String(logger.WORKFLOW, cardRequest.GetWorkflow().String()))
				res.Status = rpc.StatusOk()
				return res, nil
			}
		}
	} else {
		createNewRequest = true
	}

	if createNewRequest {
		// rate limit check in case user is opting for reissue card
		switch req.GetCardRequestWorkFlow() {
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD:
			isRateLimitReached, err := s.isRateLimitReachedForReissueCard(ctx, actorId)
			if err != nil {
				logger.Error(ctx, "error while fetching rate limit for cc reissue card api", zap.Error(err), zap.String(logger.CARD_ID, req.GetCardId()))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			if isRateLimitReached {
				logger.Info(ctx, "user has breached limit for reissue card")
				res.Status = rpc.StatusPermissionDenied()
				return res, nil
			}
		default:
		}

		cardRequest, err = s.createCardRequest(ctx, req, savedCard, actorId, creditAccountResponse.GetAccount())
		if err != nil {
			logger.Error(ctx, "error in creating card request", zap.String(logger.CARD_ID,
				req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	// for view card details workflow, we can skip initiating workflow if biometric is set for the user
	// we just need a card request with nextAction for view card details deeplink
	// and an auth stage with status skipped
	if req.GetCardRequestWorkFlow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_VIEW_CARD_DETAILS &&
		req.GetDeviceUnlockMechanism() == types.DeviceUnlockMechanism_DEVICE_UNLOCK_MECHANISM_BIOMETRIC &&
		req.GetDeviceUnlockMechanismStrength() == types.DeviceUnlockMechanismStrength_DEVICE_UNLOCK_MECHANISM_STRENGTH_HIGH &&
		app.IsFeatureEnabledFromCtx(ctx, s.conf.SkipWorkflowInitiationForViewCardDetails) {

		nextAction, err := s.handleViewCardDetailsWorkflowRequest(ctx, cardRequest)
		if err != nil {
			logger.Error(ctx, "error in handleViewCardDetailsWorkflowRequest", zap.String(logger.CARD_ID,
				req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		res.CardRequestId = cardRequest.GetId()
		res.NextAction = nextAction
		res.Status = rpc.StatusOk()
		return res, nil
	}

	nextAction := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_FIREFLY_GET_REQUEST_STATUS,
		ScreenOptions: &deeplinkPb.Deeplink_FireflyGetRequestStatusScreenOptions{FireflyGetRequestStatusScreenOptions: &deeplinkPb.FireflyGetRequestStatusScreenOptions{
			CardRequestId: cardRequest.GetId(),
		}},
	}
	cardRequest.NextAction = nextAction
	err = s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION})
	if err != nil {
		logger.Error(ctx, "error in updating next action for card request", zap.Error(err), zap.String(logger.CARD_REQUEST_ID,
			cardRequest.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	err = s.initiateWorkflow(ctx, cardRequest, savedCard, req)
	if err != nil {
		logger.Error(ctx, "error in initiating workflow", zap.Error(err), zap.String(logger.CARD_ID,
			req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkFlow().String()),
			zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.CardRequestId = cardRequest.GetId()
	res.Status = rpc.StatusOk()
	res.NextAction = cardRequest.GetNextAction()
	return res, nil
}

func (s *Service) handleViewCardDetailsWorkflowRequest(ctx context.Context, cardRequest *ffPb.CardRequest) (*deeplinkPb.Deeplink, error) {
	cardRequest.NextAction = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CREDIT_CARD_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CreditCardDetailsScreenOptions{
			CreditCardDetailsScreenOptions: &deeplinkPb.CreditCardDetailsScreenOptions{
				CardRequestId: cardRequest.GetId(),
			},
		},
	}

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION})
		if err != nil {
			return errors.Wrap(err, "failed updating cardRequest")
		}

		_, err = s.cardRequestStageDao.Create(ctx, &ffPb.CardRequestStage{
			CardRequestId:   cardRequest.GetId(),
			OrchestrationId: cardRequest.GetOrchestrationId(),
			Stage:           ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_AUTH,
			Workflow:        cardRequest.GetWorkflow(),
			Status:          ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SKIPPED,
			SubStatus:       ffEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_SKIPPED,
		})
		if err != nil {
			return errors.Wrap(err, "failed creating cardRequestStage")
		}

		return nil
	})

	if txnErr != nil {
		return nil, errors.Wrap(txnErr, "error updating card request and stage in txn")
	}

	return cardRequest.GetNextAction(), nil
}

func (s *Service) isRateLimitReachedForReissueCard(ctx context.Context, actorId string) (bool, error) {
	if !s.genConf.Flags().EnableReissueCardRateLimiter() {
		return false, nil
	}
	// checks day based limit for renew card
	isAllowed1, err := s.rateLimitClient.Acquire(ctx, ReissueCardApi1, actorId)
	if err != nil || !isAllowed1 {
		if err != nil {
			return true, fmt.Errorf("failed to check user level rate limit for renew card %w", err)
		}
		return true, nil
	}

	// checks week based limit for renew card
	isAllowed2, err := s.rateLimitClient.Acquire(ctx, ReissueCardApi2, actorId)
	if err != nil || !isAllowed2 {
		if err != nil {
			return true, fmt.Errorf("failed to check user level rate limit for renew card %w", err)
		}
		return true, nil
	}

	// checks month based limit for renew card
	isAllowed3, err := s.rateLimitClient.Acquire(ctx, ReissueCardApi3, actorId)
	if err != nil || !isAllowed3 {
		if err != nil {
			return true, fmt.Errorf("failed to check user level rate limit for renew card %w", err)
		}
		return true, nil
	}

	return false, nil
}

func (s *Service) isDisputeAllowed(ctx context.Context, req *ffPb.InitiateCardReqRequest, savedCard *ffPb.CreditCard) (bool, error) {
	disputeRes, rpcErr := s.ffAccountingClient.IsDisputeAllowed(ctx, &ffAccPb.IsDisputeAllowedRequest{
		TxnId:   req.GetDisputeData().GetTxnId(),
		ActorId: savedCard.GetActorId(),
	})
	if err := epifigrpc.RPCError(disputeRes, rpcErr); err != nil {
		if disputeRes.GetStatus().IsAlreadyExists() {
			return false, errors.Wrap(epifierrors.ErrAlreadyExists, "dispute already exists")
		}
		return false, errors.Wrap(err, "error checking if dispute allowed")
	}
	return true, nil
}

// createCardRequest for creating card request from the request params
// TODO(team) : Use factory pattern here
func (s *Service) createCardRequest(ctx context.Context, req *ffPb.InitiateCardReqRequest, savedCard *ffPb.CreditCard, actorId string, account *ffAccPb.CreditAccount) (*ffPb.CardRequest, error) {
	var (
		cardRequestDetails *ffPb.CardRequestDetails
		err                error
	)
	switch req.GetCardRequestWorkFlow() {
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_ReissueRequestDetails{
				ReissueRequestDetails: &ffPb.ReissueRequestDetails{
					BlockCardReason: req.GetReissueCardData().GetBlockCardReason(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CONTROL_CHANGE:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_ControlChangeRequestDetails{
				ControlChangeRequestDetails: &ffPb.ControlChangeRequestDetails{
					International:          req.GetControlsChangeData().GetInternational(),
					Contactless:            req.GetControlsChangeData().GetContactless(),
					Atm:                    req.GetControlsChangeData().GetAtm(),
					Pos:                    req.GetControlsChangeData().GetPos(),
					Ecom:                   req.GetControlsChangeData().GetEcom(),
					ChangedSettingsDetails: getChangedStateDetails(req, savedCard.GetControlDetails()),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_LIMIT_CHANGE:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_LimitChangeRequestDetails{
				LimitChangeRequestDetails: &ffPb.LimitChangeRequestDetails{
					CardUsageLocationType: req.GetLimitsChangeData().GetCardUsageLocationType(),
					IsValueIncrease:       req.GetLimitsChangeData().GetIsValueIncrease(),
					ControlType:           req.GetLimitsChangeData().GetControlType(),
					UpdatedLimitValue:     req.GetLimitsChangeData().GetUpdatedLimitValue(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PROCESS_DISPUTE:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_DisputeRequestDetails{
				DisputeRequestDetails: &ffPb.DisputeRequestDetails{
					TxnId:       req.GetDisputeData().GetTxnId(),
					Amount:      req.GetDisputeData().GetAmount(),
					Reason:      req.GetDisputeData().GetReason(),
					Description: req.GetDisputeData().GetDescription(),
					DisputeType: req.GetDisputeData().GetDisputeType(),
					Url:         req.GetDisputeData().GetUrl(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PAYMENT:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_PaymentRequestDetails{
				PaymentRequestDetails: &ffPb.PaymentRequestDetails{
					Amount:             req.GetPaymentData().GetAmount(),
					CardId:             req.GetPaymentData().GetCardId(),
					DiscountAmount:     req.GetPaymentData().GetDiscountAmount(),
					AccountId:          req.GetPaymentData().GetAccountId(),
					PaymentAccountType: req.GetPaymentData().GetPaymentAccountType(),
					DerivedAccountId:   req.GetPaymentData().GetDerivedAccountId(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_EXPORT_STATEMENT:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_ExportStatementDetails{
				ExportStatementDetails: &ffPb.ExportStatementDetails{
					BillId: req.GetExportStatementData().GetBillId(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ACTIVATION:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_CardActivationDetails{
				CardActivationDetails: &ffPb.CardActivationDetails{
					IsQrCodeVerified: req.GetCardActivationData().GetIsQrCodeVerified(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK:
		initiateCardReqData := req.GetRealTimeProfileValidationCheckData()
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_RealTimeProfileValidationCheckDetails{
				RealTimeProfileValidationCheckDetails: &ffPb.RealTimeProfileValidationCheckDetails{
					EmailId:           initiateCardReqData.GetEmailId(),
					AuthClientReqId:   initiateCardReqData.GetAuthClientReqId(),
					Name:              initiateCardReqData.GetName(),
					CardProgramType:   initiateCardReqData.GetCardProgramType(),
					CardProgramVendor: initiateCardReqData.GetCardProgramVendor(),
				},
			},
		}
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_AUTOMATED_REPAYMENT:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_AutoPaymentRequestDetails{
				AutoPaymentRequestDetails: &ffPb.AutoRepaymentRequestDetails{
					Amount:             req.GetAutoRepaymentData().GetAmount(),
					PaymentClientReqId: uuid.NewString(),
					PaymentProvenance:  req.GetAutoRepaymentData().GetPaymentProvenance(),
				}},
		}

	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_USER_COMMUNICATION:
		cardRequestDetails = &ffPb.CardRequestDetails{
			Data: &ffPb.CardRequestDetails_UserCommunicationDetails{
				UserCommunicationDetails: &ffPb.UserCommunicationDetails{
					CommunicationType:               req.GetUserCommunicationData().GetCommunicationType(),
					ReferenceTime:                   req.GetUserCommunicationData().GetReferenceTime(),
					InitiatorWfClientReqId:          req.GetUserCommunicationData().GetInitiatorWfClientReqId(),
					SkipTriggerTimeWindowValidation: req.GetUserCommunicationData().GetSkipTriggerTimeWindowValidation(),
				},
			},
		}
	}

	if cardRequestDetails == nil {
		cardRequestDetails = &ffPb.CardRequestDetails{}
	}
	cardRequestDetails.DeviceUnlockMechanism = req.GetDeviceUnlockMechanism()
	cardRequestDetails.DeviceUnlockMechanismStrength = req.GetDeviceUnlockMechanismStrength()
	cardRequestDetails.CardProgram = account.GetCardProgram()
	orchId := req.GetClientRequestId()
	if len(orchId) == 0 {
		orchId = uuid.NewString()
	}
	cardRequest := &ffPb.CardRequest{
		CardId:          req.GetCardId(),
		ActorId:         actorId,
		OrchestrationId: orchId,
		Vendor:          commonvgpb.Vendor_M2P,
		RequestDetails:  cardRequestDetails,
		Workflow:        req.GetCardRequestWorkFlow(),
		Status:          ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
		Provenance:      req.GetProvenance(),
	}
	cardRequest, err = s.cardRequestDao.Create(ctx, cardRequest)
	if err != nil {
		return nil, fmt.Errorf("error in creating card request %w", err)
	}
	return cardRequest, nil
}

func getChangedStateDetails(req *ffPb.InitiateCardReqRequest, existingControlDetails *ffPb.CardControlDetails) []*ffPb.ControlChangeRequestDetails_ChangedSettingsDetail {
	changedStateDetails := make([]*ffPb.ControlChangeRequestDetails_ChangedSettingsDetail, 0)

	if getBoolValueFromBoolEnum(req.GetControlsChangeData().GetAtm()) != existingControlDetails.GetAtm() {
		changedStateDetails = append(changedStateDetails, &ffPb.ControlChangeRequestDetails_ChangedSettingsDetail{
			CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
			NewControlValue: getBoolValueFromBoolEnum(req.GetControlsChangeData().GetAtm()),
		})
	}

	if getBoolValueFromBoolEnum(req.GetControlsChangeData().GetPos()) != existingControlDetails.GetPos() {
		changedStateDetails = append(changedStateDetails, &ffPb.ControlChangeRequestDetails_ChangedSettingsDetail{
			CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
			NewControlValue: getBoolValueFromBoolEnum(req.GetControlsChangeData().GetPos()),
		})
	}

	if getBoolValueFromBoolEnum(req.GetControlsChangeData().GetEcom()) != existingControlDetails.GetEcom() {
		changedStateDetails = append(changedStateDetails, &ffPb.ControlChangeRequestDetails_ChangedSettingsDetail{
			CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
			NewControlValue: getBoolValueFromBoolEnum(req.GetControlsChangeData().GetEcom()),
		})
	}

	if getBoolValueFromBoolEnum(req.GetControlsChangeData().GetInternational()) != existingControlDetails.GetInternational() {
		changedStateDetails = append(changedStateDetails, &ffPb.ControlChangeRequestDetails_ChangedSettingsDetail{
			CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL,
			NewControlValue: getBoolValueFromBoolEnum(req.GetControlsChangeData().GetInternational()),
		})
	}

	if getBoolValueFromBoolEnum(req.GetControlsChangeData().GetContactless()) != existingControlDetails.GetContactless() {
		changedStateDetails = append(changedStateDetails, &ffPb.ControlChangeRequestDetails_ChangedSettingsDetail{
			CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS,
			NewControlValue: getBoolValueFromBoolEnum(req.GetControlsChangeData().GetContactless()),
		})
	}

	return changedStateDetails
}

// validateAndFetchExistingRequest checks if a pending request exists for a given workflow, if it does then we will
// return that card request, also returns card requests for ad hoc use
func (s *Service) validateAndFetchExistingRequest(ctx context.Context, savedCard *ffPb.CreditCard,
	workflow ffEnumsPb.CardRequestWorkFlow, actorId, clientReqId string) (*ffPb.CardRequest, []*ffPb.CardRequest, error) {
	var cardRequests []*ffPb.CardRequest
	var err error
	switch {
	case clientReqId != "":
		cardRequests = make([]*ffPb.CardRequest, 0)
		_, fetchErr := s.cardRequestDao.GetByOrchestrationId(ctx, clientReqId, nil)
		switch {
		case errors.Is(fetchErr, epifierrors.ErrRecordNotFound):
			return nil, nil, epifierrors.ErrRecordNotFound
		case fetchErr != nil:
			return nil, nil, fetchErr
		default:
			return nil, nil, epifierrors.ErrAlreadyExists
		}
	case savedCard.GetId() != "":
		cardRequests, err = s.cardRequestDao.GetByCardIdAndWorkflow(ctx, savedCard.GetId(), workflow,
			[]ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_WORKFLOW,
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_UPDATED_AT})
	case actorId != "":
		cardRequests, err = s.cardRequestDao.GetByActorIdAndWorkflow(ctx, actorId, workflow,
			[]ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_WORKFLOW,
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
				ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS, ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_UPDATED_AT})
	default:
		return nil, nil, errors.Wrap(err, "error in fetching card requests")
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, nil, epifierrors.ErrRecordNotFound
	case err != nil:
		return nil, nil, err
	default:
		for _, cardReq := range cardRequests {
			switch cardReq.GetStatus() {
			case ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS, ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
				switch {
				case workflow == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_WELCOME_OFFER:
					return cardReq, nil, nil
				}
				return nil, cardRequests, nil
			default:
				return cardReq, cardRequests, nil
			}
		}
	}
	return nil, nil, nil
}

func (s *Service) initiateWorkflow(ctx context.Context, cardRequest *ffPb.CardRequest, savedCard *ffPb.CreditCard, req *ffPb.InitiateCardReqRequest) error {
	workflowTypeEnum, err := getWorkflowTypeEnum(cardRequest.GetWorkflow(), req)
	if err != nil {
		return err
	}

	payload, err := s.getPayloadForWorkflow(ctx, workflowTypeEnum, cardRequest, savedCard, cardRequest.GetWorkflow())
	if err != nil {
		return errors.Wrap(err, "failed to get payload for the workflow")
	}

	initiateWfRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		ActorId: cardRequest.GetActorId(),
		Version: workflowPb.Version_V0,
		Type:    celestialPkg.GetProtoEnumFromTypeEnum(workflowTypeEnum),
		ClientReqId: &celestialPb.ClientReqId{
			Id:     cardRequest.GetOrchestrationId(),
			Client: workflowPb.Client_FIREFLY,
		},
		Ownership: commontypes.Ownership_EPIFI_TECH,
		Payload:   payload,

		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: cardRequest.GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    workflowTypeEnum,
			Payload: payload,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     cardRequest.GetOrchestrationId(),
				Client: workflowPb.Client_FIREFLY,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	switch {
	case err != nil:
		return fmt.Errorf("error in initiating workflow %w", err)
	case initiateWfRes.GetStatus().IsAlreadyExists():
		return nil
	case !initiateWfRes.GetStatus().IsSuccess():
		return fmt.Errorf("non success state in initiating workflow %s", initiateWfRes.GetStatus().String())
	default:
		return nil
	}
}

func getWorkflowTypeEnum(cardRequestWorkflowType ffEnumsPb.CardRequestWorkFlow, req *ffPb.InitiateCardReqRequest) (*workflowPb.TypeEnum, error) {
	workflowType, ok := cardRequestWorkflowToWorkflowTypeMap[cardRequestWorkflowType]
	if ok {
		return celestialPkg.GetTypeEnumFromProtoEnum(workflowType), nil
	}
	switch cardRequestWorkflowType {
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PAYMENT:
		if req.GetPaymentData().GetPaymentAccountType() == ffEnumsPb.PaymentAccountType_PAYMENT_ACCOUNT_TYPE_THIRD_PARTY_ACCOUNT {
			return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.PerformCreditCardPaymentFromTpap), nil
		}
		return celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_PERFORM_CREDIT_CARD_PAYMENT), nil
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_BIOMETRIC_REVALIDATION:
		return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.PerformBiometricRevalidation), nil
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_WELCOME_OFFER:
		return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.ProcessWelcomeOffer), nil
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK:
		return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.RealtimeCardEligibilityCheck), nil
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK:
		return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.RealTimeProfileValidationCheck), nil
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_AUTOMATED_REPAYMENT:
		return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.ProcessAutoRepayment), nil
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_USER_COMMUNICATION:
		return celestialPkg.GetTypeEnumFromWorkflowType(ffNs.PerformUserCommunication), nil
	}
	return nil, fmt.Errorf("failed to fetch workflow for card request workflow %s", cardRequestWorkflowType.String())
}

func (s *Service) getPayloadForWorkflow(ctx context.Context, workflowType *workflowPb.TypeEnum, cardRequest *ffPb.CardRequest, savedCard *ffPb.CreditCard, cardRequestWorkflowType ffEnumsPb.CardRequestWorkFlow) ([]byte, error) {
	switch celestialPkg.GetProtoEnumFromTypeEnum(workflowType) {
	case workflowPb.Type_SET_CARD_LIMITS:
		isAuthRequired := false
		for _, limit := range savedCard.GetCardLimits().GetLimits() {
			if limit.GetControlType() == cardRequest.GetRequestDetails().GetLimitChangeRequestDetails().GetControlType() && limit.GetLocationType() == cardRequest.GetRequestDetails().GetLimitChangeRequestDetails().GetCardUsageLocationType() &&
				limit.GetDailyLimitValue().GetUnits() < cardRequest.GetRequestDetails().GetLimitChangeRequestDetails().GetUpdatedLimitValue().GetUnits() {
				isAuthRequired = true
			}
		}

		payload, err := protojson.Marshal(&ffWfPb.SetCardLimitsPayload{
			IsAuthRequired: isAuthRequired,
			CreditCardId:   cardRequest.GetCardId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "payload marshal failed")
		}
		return payload, nil

	case workflowPb.Type_SET_CARD_USAGE:
		isAuthRequired := false
		if (getBoolValueFromBoolEnum(cardRequest.GetRequestDetails().GetControlChangeRequestDetails().GetAtm()) && !savedCard.GetControlDetails().GetAtm()) ||
			(getBoolValueFromBoolEnum(cardRequest.GetRequestDetails().GetControlChangeRequestDetails().GetEcom()) && !savedCard.GetControlDetails().GetEcom()) ||
			(getBoolValueFromBoolEnum(cardRequest.GetRequestDetails().GetControlChangeRequestDetails().GetPos()) && !savedCard.GetControlDetails().GetPos()) ||
			(getBoolValueFromBoolEnum(cardRequest.GetRequestDetails().GetControlChangeRequestDetails().GetContactless()) && !savedCard.GetControlDetails().GetContactless()) ||
			(getBoolValueFromBoolEnum(cardRequest.GetRequestDetails().GetControlChangeRequestDetails().GetInternational()) && !savedCard.GetControlDetails().GetInternational()) {
			isAuthRequired = true
		}

		payload, err := protojson.Marshal(&ffWfPb.SetCardUsagePayload{
			IsAuthRequired: isAuthRequired,
			CreditCardId:   cardRequest.GetCardId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "payload marshal failed")
		}
		return payload, nil

	case workflowPb.Type_ISSUE_PHYSICAL_CREDIT_CARD:
		payload, err := protojson.Marshal(&ffWfPb.IssuePhysicalCreditCardPayload{
			CardId:    cardRequest.GetCardId(),
			CardReqId: cardRequest.GetId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "payload marshal failed")
		}
		return payload, nil
	case workflowPb.Type_RESET_CARD_PIN:
		payload, err := protojson.Marshal(&ffWfPb.ResetCardPinPayload{
			CreditCardId:  cardRequest.GetCardId(),
			CardRequestId: cardRequest.GetId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "payload marshal failed")
		}
		return payload, nil
	case workflowPb.Type_PERFORM_CREDIT_CARD_PAYMENT:
		payload, err := protojson.Marshal(&ffWfPb.CreditCardPaymentPayload{
			OrchestrationId: cardRequest.GetOrchestrationId(),
			CardRequestId:   cardRequest.GetId(),
			ActorId:         cardRequest.GetRequestDetails().GetPaymentRequestDetails().GetActorId(),
			Amount:          cardRequest.GetRequestDetails().GetPaymentRequestDetails().GetAmount(),
			CardId:          cardRequest.GetRequestDetails().GetPaymentRequestDetails().GetCardId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "payload marshal failed")
		}
		return payload, nil
	case workflowPb.Type_EXPORT_CREDIT_CARD_STATEMENT:
		payload, err := protojson.Marshal(&ffWfPb.ExportStatementPayload{
			CardRequestId: cardRequest.GetId(),
			BillId:        cardRequest.GetRequestDetails().GetExportStatementDetails().GetBillId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "payload marshal failed")
		}
		return payload, nil
	default:
		switch cardRequestWorkflowType {
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_WELCOME_OFFER:
			onbCardReq, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, savedCard.GetActorId(), ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING, nil)
			if err != nil {
				return nil, errors.Wrap(err, "failed to fetch onboarding card request")
			}

			payload, err := protojson.Marshal(&ffWfPb.ProcessWelcomeOfferPayload{
				RewardId:       onbCardReq[0].GetRequestDetails().GetSelectedRewardId(),
				RewardOptionId: onbCardReq[0].GetRequestDetails().GetSelectedRewardOptionId(),
				CardRequestId:  cardRequest.GetId(),
			})
			if err != nil {
				return nil, errors.Wrap(err, "payload marshal failed")
			}
			return payload, nil

		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK:
			payload, err := protojson.Marshal(&ffWfPb.RealtimeCardEligibilityCheckRequest{
				ActorId:       cardRequest.GetActorId(),
				CardRequestId: cardRequest.GetId(),
			})
			if err != nil {
				return nil, errors.Wrap(err, "payload marshal failed")
			}
			return payload, nil
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK:
			payload, err := protojson.Marshal(&ffWfPb.RealtimeProfileValidationCheck{
				ActorId:         cardRequest.GetActorId(),
				CardRequestId:   cardRequest.GetId(),
				AuthClientReqId: cardRequest.GetRequestDetails().GetRealTimeProfileValidationCheckDetails().GetAuthClientReqId(),
				EmailId:         cardRequest.GetRequestDetails().GetRealTimeProfileValidationCheckDetails().GetEmailId(),
			})
			if err != nil {
				return nil, errors.Wrap(err, "payload marshal failed")
			}
			return payload, nil
		case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_USER_COMMUNICATION:
			payload, err := protojson.Marshal(&ffWfPb.PerformUserCommunicationPayload{
				ActorId: cardRequest.GetActorId(),
			})
			if err != nil {
				return nil, errors.Wrap(err, "payload marshal failed")
			}
			return payload, nil
		}
		return nil, nil
	}
}

func getBoolValueFromBoolEnum(boolEnum commontypes.BooleanEnum) bool {
	switch boolEnum {
	case commontypes.BooleanEnum_TRUE:
		return true
	case commontypes.BooleanEnum_FALSE:
		return false
	default:
		return false
	}
}

func (s *Service) CollectAuthFlow(ctx context.Context, req *ffPb.CollectAuthFlowRequest) (*ffPb.CollectAuthFlowResponse, error) {
	var (
		res = &ffPb.CollectAuthFlowResponse{}
		err error
	)
	cardRequest, err := s.cardRequestDao.GetById(ctx, req.GetCardRequestId(), []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching card request", zap.String(logger.CARD_ID, req.GetCardRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	authFlow := getAuthFlow(req.GetCardAuthFlow())
	if authFlow == authOrchPb.FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED {
		logger.Error(ctx, "selected auth flow is not mapped", zap.String(logger.CARD_ID, req.GetCardRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	payload := &ffActPb.CreditCardAuthOptionSelectedSignalData{
		AuthFlow:    authFlow,
		Preferences: s.getAuthFlowPreferences(cardRequest),
	}
	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "error in marshalling payload", zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if sigErr := s.sendSignal(ctx, cardRequest.GetOrchestrationId(), string(ffNs.CreditCardAuthOptionSelectedSignal), marshalledPayload); sigErr != nil {
		logger.Error(ctx, "error sending the auth selection signal", zap.Error(sigErr), zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	ccPollingDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_FIREFLY_GET_REQUEST_STATUS,
		ScreenOptions: &deeplinkPb.Deeplink_FireflyGetRequestStatusScreenOptions{FireflyGetRequestStatusScreenOptions: &deeplinkPb.FireflyGetRequestStatusScreenOptions{
			CardRequestId: cardRequest.GetId(),
		}},
	}
	cardRequest.NextAction = ccPollingDl
	cardRequest.GetRequestDetails().CardAuthFlow = req.GetCardAuthFlow()
	if updErr := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
	}); updErr != nil {
		logger.Error(ctx, "error in updating next action AND request details in card request", zap.Error(updErr), zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.NextAction = ccPollingDl
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getAuthFlowPreferences(cardRequest *ffPb.CardRequest) *authOrchPb.Preferences {
	var preferences *authOrchPb.Preferences
	switch cardRequest.GetWorkflow() {
	case ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET:
		preferences = &authOrchPb.Preferences{
			SmsOtpPreferences: &authOrchPb.SmsOtpPreferences{
				GenerateOtpFlow: authPb.GenerateOTPFlow_GENERATE_OTP_FLOW_CREDIT_CARD_RESET_PIN,
			},
		}
	default:
	}
	return preferences
}

// TODO(akk) - uniqueness constraint add for card request stage
func (s *Service) fetchOrCreateCardRequestStage(ctx context.Context, cardReqId string, orchId string, stage ffEnumsPb.CardRequestStageName, status ffEnumsPb.CardRequestStageStatus) (*ffPb.CardRequestStage, error) {
	cardRequestStage, err := s.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, cardReqId, stage, []ffEnumsPb.CardRequestStageFieldMask{})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound), s.isStageTerminated(cardRequestStage):
		createRes, createErr := s.createCardRequestStage(ctx, cardReqId, orchId, stage, status)
		if createErr != nil {
			return nil, errors.Wrap(err, "error creating card request stage")
		}
		cardRequestStage = createRes
	case err != nil:
		return nil, errors.Wrap(err, "error fetching card request stage")
	}
	return cardRequestStage, nil
}

func (s *Service) createCardRequestStage(ctx context.Context, cardReqId string, orchId string, stage ffEnumsPb.CardRequestStageName, status ffEnumsPb.CardRequestStageStatus) (*ffPb.CardRequestStage, error) {
	crStage := &ffPb.CardRequestStage{
		CardRequestId:   cardReqId,
		OrchestrationId: orchId,
		Stage:           stage,
		Status:          status,
	}
	crs, err := s.cardRequestStageDao.Create(ctx, crStage)
	if err != nil {
		return nil, err
	}

	return crs, nil
}

func (s *Service) updateCardRequestAndStage(ctx context.Context, cardRequest *ffPb.CardRequest,
	cardRequestStage *ffPb.CardRequestStage, cardRequestFieldMask []ffEnumsPb.CardRequestFieldMask,
	cardRequestStageFieldMask []ffEnumsPb.CardRequestStageFieldMask) error {
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err := s.cardRequestStageDao.Update(txnCtx, cardRequestStage, cardRequestStageFieldMask)
		if err != nil {
			return errors.Wrap(err, "error updating card request stage")
		}
		err = s.cardRequestDao.Update(txnCtx, cardRequest, cardRequestFieldMask)
		if err != nil {
			return errors.Wrap(err, "error updating card request")
		}
		return nil
	})

	if txnErr != nil {
		return errors.Wrap(txnErr, "error updating card request and stage in txn")
	}

	return nil
}

func (s *Service) updateCardRequestAndStageWithDirtyWriteCheck(ctx context.Context, cardRequest *ffPb.CardRequest,
	cardRequestStage *ffPb.CardRequestStage, cardRequestFieldMask []ffEnumsPb.CardRequestFieldMask,
	cardRequestStageFieldMask []ffEnumsPb.CardRequestStageFieldMask, expectedCurrentScreens []deeplinkPb.Screen) error {
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err := s.cardRequestStageDao.Update(txnCtx, cardRequestStage, cardRequestStageFieldMask)
		if err != nil {
			return errors.Wrap(err, "error updating card request stage")
		}
		err = s.cardRequestDao.UpdateWithNextActionDirtyWriteCheck(txnCtx, expectedCurrentScreens, cardRequest, cardRequestFieldMask)
		if err != nil {
			return errors.Wrap(err, "error updating card request")
		}
		return nil
	})

	if txnErr != nil {
		return errors.Wrap(txnErr, "error updating card request and stage in txn")
	}

	return nil
}

func (s *Service) isStageTerminated(cardReqStage *ffPb.CardRequestStage) bool {
	switch cardReqStage.GetStatus() {
	case ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS,
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED,
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION:
		return true
	default:
		return false
	}
}

func getAuthFlow(cardAuthFlow ffEnumsPb.CardAuthFlow) authOrchPb.FlowName {
	switch cardAuthFlow {
	case ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_OTP_AND_LIVENESS:
		return authOrchPb.FlowName_AUTH_REQUEST_FLOW_LIVENESS_OTP
	case ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_OTP_AND_UPI_PIN_VALIDATION:
		return authOrchPb.FlowName_AUTH_REQUEST_FLOW_NPCI_PIN_OTP
	case ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_LIVENESS:
		return authOrchPb.FlowName_AUTH_REQUEST_FLOW_LIVENESS_SUMMARY
	case ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_UPI_PIN_VALIDATION:
		return authOrchPb.FlowName_AUTH_REQUEST_FLOW_NPCI_SECURE_PIN
	default:
		return authOrchPb.FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED
	}
}

func (s *Service) GetCardDetails(ctx context.Context, req *ffPb.GetCardDetailsRequest) (*ffPb.GetCardDetailsResponse, error) {
	var (
		res                  = &ffPb.GetCardDetailsResponse{}
		clearCardDetailsResp = &creditcard.GetUnmaskedCardDetailsResponse{}
		cvvRes               = &creditcard.GenerateCVVResponse{}
		cardListErr          error
		cvvErr               error
	)
	cardRequest, err := s.cardRequestDao.GetById(ctx, req.GetCardRequestId(), []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CARD_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_VENDOR,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching card request to send signal to view card details workflow", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	// just verifying if the auth stage of the workflow has been successful before exposing any card details.
	// avoiding checking the card request status to success to avoid race conditions between the user and the
	// ProcessViewCardDetails workflow
	authCardReqStage, err := s.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, cardRequest.GetId(), ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_AUTH, []ffEnumsPb.CardRequestStageFieldMask{
		ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching auth card request stage from the card request id", zap.Error(err), zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if !lo.Contains([]ffEnumsPb.CardRequestStageStatus{
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS,
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SKIPPED,
	}, authCardReqStage.GetStatus()) {
		logger.Error(ctx, "card details auth activity not successful")
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}

	latestCard, err := s.creditCardDao.GetById(ctx, cardRequest.GetCardId(), []ffEnumsPb.CreditCardFieldMask{
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_VENDOR_IDENTIFIER,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching card", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Errorf("error in fetch credit card record %w", err).Error())
		return res, nil
	}
	cardAccount, err := s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
		GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: latestCard.GetAccountId()}})
	if te := epifigrpc.RPCError(cardAccount, err); te != nil {
		logger.Error(ctx, "error in fetching card account", zap.Error(te))
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Errorf("error in fetch credit card account record %w", te).Error())
		return res, nil
	}

	// vg call to fetch clear card details for the user
	clearCardDetailsResp, cardListErr = s.getClearCardDetails(ctx, cardRequest.GetVendor(), cardAccount.GetAccount().GetReferenceId())
	if cardListErr != nil {
		logger.Error(ctx, "error in fetching clear card details", zap.Error(cardListErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	activeCard, err := s.getMatchingCard(clearCardDetailsResp.GetCardDetails(), latestCard.GetVendorIdentifier())
	if err != nil {
		logger.Error(ctx, "error in determining active card for the user", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if activeCard.GetCardStatus() == ffEnumsPb.CardStatus_CLOSED {
		logger.Error(ctx, "card is closed", zap.String(logger.CARD_ID, latestCard.GetId()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}

	// vg call to fetch cvv for a card
	cvvRes, cvvErr = s.fetchCvvData(ctx, latestCard, cardAccount.GetAccount(), clearCardDetailsResp.GetDateOfBirth(), cardRequest, activeCard.GetExpiryToken())
	if cvvErr != nil {
		logger.Error(ctx, "error in fetching cvv", zap.Error(cvvErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res = &ffPb.GetCardDetailsResponse{
		Status:                rpc.StatusOk(),
		ExpiryDate:            activeCard.GetExpiryDate(),
		ClearCardNumber:       activeCard.GetCardNumber(),
		CardNetworkType:       activeCard.GetNetworkType(),
		Cvv:                   cvvRes.GetCvv(),
		Name:                  clearCardDetailsResp.GetName(),
		ViewCardDetailsExpiry: viewCardDetailsExpiry,
		CopyCardDetailsExpiry: copyCardDetailsExpiry,
		CardProgram:           cardAccount.GetAccount().GetCardProgram(),
		ExpiryToken:           activeCard.GetExpiryToken(),
	}

	if cardRequest.GetStatus() != ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
		cardRequest.Status = ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS
		err = s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS})
		if err != nil {
			logger.Error(ctx, "failed updating card request status")
			// not throwing any error as this update is not that critical
		}
	}

	return res, nil
}

func (s *Service) getCustomerDetails(ctx context.Context, actorId string) (*userPb.User, error) {
	actorDetails, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if epifigrpc.RPCError(actorDetails, err) != nil {
		return nil, errors.Wrap(epifigrpc.RPCError(actorDetails, err), "error fetching actor details for the user")
	}

	userDetails, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{Id: actorDetails.GetActor().GetEntityId()},
	})
	if te := epifigrpc.RPCError(userDetails, err); te != nil {
		return nil, errors.Wrap(te, "error fetching customer details for the user")
	}
	return userDetails.GetUser(), err
}

func (s *Service) getClearCardDetails(ctx context.Context, vendor commonvgpb.Vendor, referenceId string) (*creditcard.GetUnmaskedCardDetailsResponse, error) {
	var (
		ccVgClient creditcard.CreditCardClient
	)
	if s.conf.EnableViewCardDetailsViaVgPciServer {
		ccVgClient = s.cCVgPciClient
	} else {
		ccVgClient = s.ccVgClient
	}
	res, err := ccVgClient.GetUnmaskedCardDetails(ctx, &creditcard.GetUnmaskedCardDetailsRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: vendor},
		CustomerId: referenceId,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching clear card details for the user")
	}
	return res, nil
}

func (s *Service) fetchCvvData(ctx context.Context, card *ffPb.CreditCard, cardAccount *ffAccPb.CreditAccount, dob *date.Date, cardRequest *ffPb.CardRequest, expiryToken string) (*creditcard.GenerateCVVResponse, error) {
	var (
		ccVgClient creditcard.CreditCardClient
	)
	if s.conf.EnableViewCardDetailsViaVgPciServer {
		ccVgClient = s.cCVgPciClient
	} else {
		ccVgClient = s.ccVgClient
	}
	res, err := ccVgClient.GenerateCVV(ctx, &creditcard.GenerateCVVRqeuest{
		Header:      &commonvgpb.RequestHeader{Vendor: cardRequest.GetVendor()},
		CustomerId:  cardAccount.GetReferenceId(),
		KitNumber:   card.GetVendorIdentifier(),
		ExpiryDate:  card.GetBasicInfo().GetExpiryDate(),
		ExpiryToken: expiryToken,
		Dob:         dob,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching cvv response from vendor")
	}
	return res, nil
}

func (s *Service) getMatchingCard(cards []*creditcard.CardDetails, vendorRefNo string) (*creditcard.CardDetails, error) {
	for _, card := range cards {
		if card.GetKitNumber() == vendorRefNo {
			return card, nil
		}
	}
	return nil, errors.New("no active card for the given user")
}

func (s *Service) GetCreditCardPaymentStatus(ctx context.Context, req *ffPb.GetCreditCardPaymentStatusRequest) (*ffPb.GetCreditCardPaymentStatusResponse, error) {
	var (
		res = &ffPb.GetCreditCardPaymentStatusResponse{}
	)

	fundTransferDeeplink, err := s.getFundTransferStatusNextAction(ctx, req.GetPaymentReqId(), req.GetAttemptNumber())
	if err != nil {
		logger.Error(ctx, "error in calling pay", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Deeplink = fundTransferDeeplink
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getFundTransferStatusNextAction(ctx context.Context, paymentReqId string, attemptNumber int32) (*deeplinkPb.Deeplink, error) {
	fundTransferStatusDeeplink, paymentStatus, err := s.rpcHelper.GetFundTransferStatusNextAction(ctx, paymentReqId)
	if err != nil {
		return nil, errors.Wrap(err, "Error fetching fund transfer status")
	}
	switch paymentStatus {
	// Sometimes we are getting unknown and internal status for an ongoing payment status. Hence we need to keep
	// continuing the poll instead of terminating the poll from client
	case ffEnumsPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED, ffEnumsPb.PaymentStatus_PAYMENT_STATUS_IN_PROGRESS,
		ffEnumsPb.PaymentStatus_PAYMENT_STATUS_INTERNAL, ffEnumsPb.PaymentStatus_PAYMENT_STATUS_UNKNOWN,
		ffEnumsPb.PaymentStatus_PAYMENT_STATUS_RECORD_NOT_FOUND:
		if 1+attemptNumber < maxRetryAttempts {
			// Incrementing attempt number
			return helper.GetCreditCardPaymentStatusPollingScreen(paymentReqId, "", 1+attemptNumber), nil
		}
		return helper.GetPaymentStatusNextAction(paymentStatus), nil
	case ffEnumsPb.PaymentStatus_PAYMENT_STATUS_SUCCESS, ffEnumsPb.PaymentStatus_PAYMENT_STATUS_FAILED:
		return fundTransferStatusDeeplink, nil
	default:
		return nil, errors.Wrap(err, "Unknown payment status received.")
	}
}

func (s *Service) SignalWorkFlowByExternalId(ctx context.Context, req *ffPb.SignalWorkFlowByExternalIdRequest) (*ffPb.SignalWorkFlowByExternalIdResponse, error) {
	var (
		res                   = &ffPb.SignalWorkFlowByExternalIdResponse{}
		err                   error
		signalWorkflowRequest = &celestialPb.SignalWorkflowRequest{}
	)
	cardRequestStage, err := s.cardRequestStageDao.GetByExternalRequestIdAndStage(ctx, req.GetExternalId(), req.GetStageName(), []ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID})
	if err != nil {
		logger.Error(ctx, "Error in fetching card request stage", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	switch req.GetSignalWorkFlowPayload().(type) {
	case *ffPb.SignalWorkFlowByExternalIdRequest_VendorRepaymentSignalWorkflowData:
		payload, err := protojson.Marshal(&ffActPb.CreditCardVendorRepaymentUpdateSignalData{PaymentStatus: req.GetVendorRepaymentSignalWorkflowData().GetPaymentStatus()})
		if err != nil {
			logger.Error(ctx, "Error in marshalling signal payload", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		signalWorkflowRequest = &celestialPb.SignalWorkflowRequest{
			Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     cardRequestStage.GetOrchestrationId(),
					Client: workflowPb.Client_FIREFLY,
				},
			},
			SignalId:  req.GetSignalName(),
			Payload:   payload,
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
	default:
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	signalResp, err := s.celestialClient.SignalWorkflow(ctx, signalWorkflowRequest)
	if te := epifigrpc.RPCError(signalResp, err); te != nil {
		logger.Error(ctx, "Error in sending signal to request", zap.Error(te))
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetAllCards RPC to fetch all cards for a user given an actor Id . Required to populate the agent dashboard
func (s *Service) GetAllCards(ctx context.Context, req *ffPb.GetAllCardsRequest) (*ffPb.GetAllCardsResponse, error) {
	res := &ffPb.GetAllCardsResponse{}
	cards, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId(), nil)
	if err != nil {
		logger.Error(ctx, "error in fetching cards using actor id", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	return &ffPb.GetAllCardsResponse{
		Status:      rpc.StatusOk(),
		CreditCards: cards,
	}, nil
}

func (s *Service) GetCardRequestByActorIdAndWorkflow(ctx context.Context, req *ffPb.GetCardRequestByActorIdAndWorkflowRequest) (*ffPb.GetCardRequestByActorIdAndWorkflowResponse, error) {
	res := &ffPb.GetCardRequestByActorIdAndWorkflowResponse{}
	cardReq, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, req.GetActorId(), req.GetCardRequestWorkFlow(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching card request from DB", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	return &ffPb.GetCardRequestByActorIdAndWorkflowResponse{
		Status:      rpc.StatusOk(),
		CardRequest: cardReq[0],
	}, nil
}

func (s *Service) GetCardRequestAndCardRequestStage(ctx context.Context, req *ffPb.GetCardRequestAndCardRequestStageRequest) (*ffPb.GetCardRequestAndCardRequestStageResponse, error) {
	res := &ffPb.GetCardRequestAndCardRequestStageResponse{}

	cardRequest, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, req.GetActorId(), req.GetCardRequestWorkflow(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching card request from DB", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	default:
	}

	cardRequestStages, err := s.cardRequestStageDao.GetByCardRequestId(ctx, cardRequest[0].GetId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no record found while fetching card request stage from DB", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching card request stage from DB", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	default:
	}

	res.CardRequest = cardRequest[0]
	res.CardRequestStages = cardRequestStages
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) TriggerRealtimeCardEligibilityCheck(ctx context.Context, req *ffPb.TriggerRealtimeCardEligibilityCheckRequest) (*ffPb.TriggerRealtimeCardEligibilityCheckResponse, error) {
	var (
		res = &ffPb.TriggerRealtimeCardEligibilityCheckResponse{}
	)
	nextAction, err := s.checkAndTriggerCardEligibility(ctx, req)
	if err != nil {
		logger.Error(ctx, "error in triggering card eligibility check", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.NextAction = nextAction
	res.Status = rpc.StatusOk()
	return res, nil
}

// checkAndTriggerCardEligibility checks if there is any existing request for card eligibility check, if it does not
// exist then it creates a new request, otherwise we will return the corresponding next action
func (s *Service) checkAndTriggerCardEligibility(ctx context.Context, req *ffPb.TriggerRealtimeCardEligibilityCheckRequest) (*deeplinkPb.Deeplink, error) {
	var (
		cardRequest      *ffPb.CardRequest
		actorId          = req.GetActorId()
		screenIdentifier = req.GetScreenIdentifier()
	)

	cardRequests, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, actorId,
		ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK, nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no record found for card eligibility check")
		realtimeElgDetails := &ffPb.CardRequestDetails{
			CardProgram: req.GetCardProgram(),
			Data: &ffPb.CardRequestDetails_RealtimeEligibilityDetails{
				RealtimeEligibilityDetails: &ffPb.RealtimeEligibilityDetails{IsFiSavingsAccountHolder: true},
			},
		}
		if screenIdentifier == int32(deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API) {
			realtimeElgDetails.Data = &ffPb.CardRequestDetails_RealtimeEligibilityDetails{
				RealtimeEligibilityDetails: &ffPb.RealtimeEligibilityDetails{
					IsFiSavingsAccountHolder: false,
					CardProgram:              req.GetCardProgram(),
				},
			}
		}

		extVendorId, idErr := s.getExternalVendorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error in getExternalVendorId", zap.Error(idErr))
		}
		cardRequest, err = s.cardRequestDao.Create(ctx, &ffPb.CardRequest{
			ActorId:          actorId,
			OrchestrationId:  uuid.New().String(),
			Vendor:           commonvgpb.Vendor_M2P,
			Workflow:         ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
			Status:           ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
			Provenance:       ffEnumsPb.Provenance_PROVENANCE_APP,
			RequestDetails:   realtimeElgDetails,
			ExternalVendorId: extVendorId,
		})
		if err != nil {
			return nil, fmt.Errorf("error in creating card eligibility check card request %w", err)
		}
	case err != nil:
		return nil, fmt.Errorf("error in fetching card requests for card eligibility check %w", err)
	case len(cardRequests) > 0 && cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED:
		cardRequest = cardRequests[0]
	case len(cardRequests) > 0 && (cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS ||
		cardRequests[0].GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED):
		realtimeElgDetails := &ffPb.CardRequestDetails{
			CardProgram: req.GetCardProgram(),
			Data: &ffPb.CardRequestDetails_RealtimeEligibilityDetails{
				RealtimeEligibilityDetails: &ffPb.RealtimeEligibilityDetails{IsFiSavingsAccountHolder: true},
			},
		}
		if screenIdentifier == int32(deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API) {
			realtimeElgDetails.Data = &ffPb.CardRequestDetails_RealtimeEligibilityDetails{
				RealtimeEligibilityDetails: &ffPb.RealtimeEligibilityDetails{
					IsFiSavingsAccountHolder: false,
					CardProgram:              req.GetCardProgram(),
				},
			}
		}
		cardRequest, err = s.cardRequestDao.Create(ctx, &ffPb.CardRequest{
			ActorId:         actorId,
			OrchestrationId: uuid.New().String(),
			Vendor:          commonvgpb.Vendor_M2P,
			Workflow:        ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
			Status:          ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
			Provenance:      ffEnumsPb.Provenance_PROVENANCE_APP,
			RequestDetails:  realtimeElgDetails,
		})
		if err != nil {
			return nil, fmt.Errorf("error in creating card eligibility check card request %w", err)
		}
	default:
		return cardRequests[0].GetNextAction(), nil
	}

	// TODO(priyansh) : Update with correct polling message
	nextAction := helper.GetCreditCardPollingScreenWithDisplayMessage(cardRequest.GetId(), ffActHelper.EligibilityCheckPollingDisplayMessage)
	cardRequest.NextAction = nextAction
	err = s.cardRequestDao.Update(ctx, cardRequest,
		[]ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION})
	if err != nil {
		logger.Error(ctx, "error in updating next action for card request", zap.Error(err),
			zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()))
		return nil, fmt.Errorf("error in updating next action for card request %w", err)
	}

	switch screenIdentifier {
	case int32(deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API):
		initiateWorkflowRes, rpcErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
			Params: &celestialPb.WorkflowCreationRequestParams{
				ActorId: cardRequest.GetActorId(),
				Version: workflowPb.Version_V0,
				Type:    celestialPkg.GetTypeEnumFromWorkflowType(ffNs.RealtimeCardEligibilityCheckV2),
				Payload: []byte{},
				ClientReqId: &workflowPb.ClientReqId{
					Id:     cardRequest.GetOrchestrationId(),
					Client: workflowPb.Client_FIREFLY,
				},
				Ownership:        commontypes.Ownership_EPIFI_TECH,
				QualityOfService: celestialPb.QoS_BEST_EFFORT,
			},
		})
		if te := epifigrpc.RPCError(initiateWorkflowRes, rpcErr); te != nil && !initiateWorkflowRes.GetStatus().IsAlreadyExists() {
			return nil, errors.Wrap(te, "error initiating workflow")
		}
		return nextAction, nil
	default:
		err = s.initiateWorkflow(ctx, cardRequest, nil, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("error in initiating workflow for card eligibility check %w", err)
	}
	return nextAction, nil
}
func (s *Service) getExternalVendorId(ctx context.Context, actorId string) (string, error) {
	userResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if grpcErr := epifigrpc.RPCError(userResp, err); grpcErr != nil {
		logger.Error(ctx, "error in getting user", zap.Error(grpcErr))
		return "", grpcErr
	}

	campaign, ok := userResp.GetUser().GetAcquisitionInfo().GetAttributionDetails().GetAppsflyerAttributionData().AsMap()[CreditCardPidKey].(string)
	if campaign != PaisabazaarPidVal {
		return "", nil
	}

	extUserId, ok := userResp.GetUser().GetAcquisitionInfo().GetAttributionDetails().GetAppsflyerAttributionData().AsMap()[ExternalVendorIdentifierKey].(string)
	if !ok {
		return "", nil
	}

	return extUserId, nil
}

// RefreshCardDetails is a rpc responsible for refreshing card details, currently only refreshing card state only we can add more parameters in the
// definition to refresh them
func (s *Service) RefreshCardDetails(ctx context.Context, req *ffPb.RefreshCardDetailsRequest) (*ffPb.RefreshCardDetailsResponse, error) {
	kitNumberToCardDetailsMap := make(map[string]*creditcard.CardDetails, 0)
	res := &ffPb.RefreshCardDetailsResponse{}
	// fetching all cards for the actor from DB
	allCardsForActor, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no record found while fetching credit card details from DB", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching credit card details from DB", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	default:
	}

	accountId := allCardsForActor[0].GetAccountId()
	getCardListRes, err := s.getAllCardsForActorFromVendor(ctx, accountId)

	if err != nil {
		return &ffPb.RefreshCardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// putting values inside map by kit number from response from vendor
	for _, card := range getCardListRes.GetCardDetails() {
		kitNumberToCardDetailsMap[card.KitNumber] = card
	}

	err = s.refreshCards(ctx, kitNumberToCardDetailsMap, allCardsForActor)
	if err != nil {
		logger.Error(ctx, "error updating credit card state ", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &ffPb.RefreshCardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getAllCardsForActorFromVendor(ctx context.Context, accountId string) (*creditcard.GetCardListResponse, error) {
	// fetching account details for actor to get reference id
	account, err := s.ffAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
		GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: accountId},
	})
	if rpcError := epifigrpc.RPCError(account, err); rpcError != nil {
		logger.Error(ctx, "error Fetching account details from DB for user", zap.Error(rpcError), zap.String(logger.ACCOUNT_ID, accountId))
		return nil, rpcError
	}

	// getting cards data from vendor for actor
	getCardListRes, err := s.ccVgClient.GetCardList(ctx, &creditcard.GetCardListRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
		CustomerId: account.GetAccount().GetReferenceId(),
	})

	if getCardListRpcErr := epifigrpc.RPCError(getCardListRes, err); getCardListRpcErr != nil {
		logger.Error(ctx, "Error Fetching credit card(s) details from vendor", zap.Error(getCardListRpcErr), zap.String(logger.REFERENCE_ID, account.GetAccount().GetReferenceId()))
		return nil, getCardListRpcErr
	}

	return getCardListRes, nil
}

func (s *Service) refreshCards(ctx context.Context, kitNumberToCardDetailsMap map[string]*creditcard.CardDetails, allCards []*ffPb.CreditCard) error {
	// refreshing card states if different in vendor response
	for _, card := range allCards {
		currentState := card.GetCardState()
		if ccFromVg, ok := kitNumberToCardDetailsMap[card.GetVendorIdentifier()]; ok {
			needRefresh := false
			switch ccFromVg.GetCardStatus() {
			case ffEnumsPb.CardStatus_BLOCKED,
				ffEnumsPb.CardStatus_REPLACED:
				if card.GetCardState() != ffEnumsPb.CardState_CARD_STATE_BLOCKED {
					card.CardState = ffEnumsPb.CardState_CARD_STATE_BLOCKED
					needRefresh = true
				}
			case ffEnumsPb.CardStatus_CLOSED:
				if card.GetCardState() != ffEnumsPb.CardState_CARD_STATE_CLOSED {
					card.CardState = ffEnumsPb.CardState_CARD_STATE_CLOSED
					needRefresh = true
				}
			case ffEnumsPb.CardStatus_LOCKED:
				if card.GetCardState() != ffEnumsPb.CardState_CARD_STATE_SUSPENDED {
					card.CardState = ffEnumsPb.CardState_CARD_STATE_SUSPENDED
					needRefresh = true
				}
			default:
				logger.Debug(ctx, "credit card status is ALLOCATED", zap.String(logger.CARD_ID, card.GetId()))
			}

			if !needRefresh {
				continue
			}

			err := s.creditCardDao.Update(ctx, card, []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE})
			if err != nil {
				return err
			}
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(ctx, ffEvents.NewCardStatusUpdateEvent(card.GetActorId(), card.GetId(),
					card.GetAccountId(), currentState.String(), card.GetCardState().String()))
			})
		}
	}
	return nil
}

func (s *Service) isUserEligibleForSecuredCard(ctx context.Context, actorId string, vendor ffEnumsPb.Vendor) (bool, string, *money.Money, *types.CardProgram, error) {
	ccOffer, err := s.ccOffersDao.GetActiveOfferByActorIdAndVendorAndCardProgram(ctx, actorId, vendor, "")
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return false, "", nil, nil, epifierrors.ErrRecordNotFound
	case err != nil:
		return false, "", nil, nil, err
	default:
		if ccOffer.GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
			logger.Info(ctx, "found a secured card program offer, skipping real time eligibility check for actor: ", zap.String(logger.ACTOR_ID_V2, actorId))
			return true, ccOffer.GetId(), ccOffer.GetOfferConstraints().GetLimit(), ccOffer.GetCardProgram(), nil
		}
	}
	return false, ccOffer.GetId(), ccOffer.GetOfferConstraints().GetLimit(), ccOffer.GetCardProgram(), nil
}

func (s *Service) GetUserCreditCardIntent(ctx context.Context, req *ffPb.GetUserCreditCardIntentRequest) (*ffPb.GetUserCreditCardIntentResponse, error) {
	var (
		res = &ffPb.GetUserCreditCardIntentResponse{}
		err error
	)

	cardRequests, err := s.cardRequestDao.GetByActorIdAndWorkflow(ctx, req.GetActorId(), ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID})

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no card request found for user")
		res.Status = rpc.StatusOk()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching card request for the user: ", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil

	}

	latestCardRequest := cardRequests[0]
	// if the latest card request is not in progress, it is assumed that the user did not show intent
	// since the workflow is yet to start or has failed
	if !lo.Contains(userIntentShownCardRequestStatus, latestCardRequest.GetStatus()) {
		res.Status = rpc.StatusOk()
		return res, nil
	}

	cardRequestStage, err := s.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, latestCardRequest.GetId(), ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CARD_CREATION, []ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no card req stage found for user")
		res.Status = rpc.StatusOk()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching card request stage for the user: ", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil

	}

	if lo.Contains(userIntentShownCardRequestStageStatus, cardRequestStage.GetStatus()) {
		res.Status = rpc.StatusOk()
		res.HasUserShownIntent = true
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getUserFeedbackScreenWithFallback(ctx context.Context, actorId string, fallbackScreen *deeplinkPb.Deeplink, cardProgram *types.CardProgram) *deeplinkPb.Deeplink {
	isUserFeedbackEnabled, featureErr := s.isFeatureEnabled(ctx, actorId, types.Feature_FEATURE_CC_USER_INELIGIBLE_FEEDBACK)
	if !isUserFeedbackEnabled || featureErr != nil {
		return fallbackScreen
	}
	// when user feedback screen is enabled, we will directly pass it without wrapping
	if ffPkg.IsCreditCardProgramSecured(cardProgram) {
		userIneligibleScreen, screenErr := helper.GetSecuredProgramCreditCardUserIneligibleToOnboardScreen(ctx)
		if screenErr != nil {
			logger.Error(ctx, "error fetching user ineligible screen: ", zap.Error(screenErr))
			return fallbackScreen
		}
		return userIneligibleScreen
	}
	userIneligibleScreen, screenErr := helper.GetCreditCardUserIneligibleToOnboardScreen(ctx)
	if screenErr != nil {
		logger.Error(ctx, "error fetching user ineligible screen: ", zap.Error(screenErr))
		return fallbackScreen
	}
	return userIneligibleScreen
}

func (s *Service) UpdateCreditCard(ctx context.Context, req *ffPb.UpdateCreditCardRequest) (*ffPb.UpdateCreditCardResponse, error) {
	res := &ffPb.UpdateCreditCardResponse{}
	if err := s.creditCardDao.Update(ctx, req.GetCreditCard(), req.GetCreditCardFieldMask()); err != nil {
		logger.Error(ctx, "error in updating credit card", zap.Error(err), zap.String(logger.CARD_ID, req.GetCreditCard().GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

// IsCreditCardUser checks whether a user has a credit card, first by checking
// the Redis cache, and if not found, then by querying the database.
func (s *Service) IsCreditCardUser(ctx context.Context, req *ffPb.IsCreditCardUserRequest) (*ffPb.IsCreditCardUserResponse, error) {
	res := &ffPb.IsCreditCardUserResponse{}

	// Check Redis cache first
	hasCreditCard, redisErr := s.getCreditCardStatusFromCache(ctx, fmt.Sprintf("%s-%s", s.conf.RedisCachePrefix, req.GetActorId()))
	if redisErr == nil {
		res.Status = rpc.StatusOk()
		res.IsCreditCardUser = hasCreditCard
		return res, nil
	}
	// Continue with DB call
	if redisErr != nil && redisErr != redis.Nil {
		logger.Error(ctx, "error fetching credit card details from Redis", zap.Error(redisErr))
	} else {
		logger.Debug(ctx, "no hit of credit card details in Redis", zap.Error(redisErr))
	}

	// Query the database for credit card details
	creditCards, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId(), []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// TODO(Chandresh): Remove the check once v2 flow is enabled.
		if s.isFireflyV2Enabled(ctx, req.GetActorId()) {
			getCardsFromV2Resp, getCardsFromV2Err := s.fireflyV2Client.GetCreditCards(ctx, &ffBeV2Pb.GetCreditCardsRequest{
				Identifier: &ffBeV2Pb.GetCreditCardsRequest_ActorId{
					ActorId: req.GetActorId(),
				},
				StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED},
			})
			if te := epifigrpc.RPCError(getCardsFromV2Resp, getCardsFromV2Err); te != nil && !rpc.StatusFromError(te).IsRecordNotFound() {
				logger.Error(ctx, "error while fetching card for actor from V2 flow", zap.Error(err))
				res.Status = rpc.StatusFromError(te)
				return res, nil
			}
			if len(getCardsFromV2Resp.GetCreditCards()) > 0 {
				res.IsCreditCardUser = true
			}
		}
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error fetching credit card details from DB", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
	default:
		// Update response based on database result
		if len(creditCards) > 0 && lo.Contains(validCreditCardStates, creditCards[0].GetCardState()) {
			res.IsCreditCardUser = true
		}
		res.Status = rpc.StatusOk()
	}

	// Set the boolean value in Redis cache
	setErr := s.setCreditCardStatusInCache(ctx, fmt.Sprintf("%s-%s", s.conf.RedisCachePrefix, req.GetActorId()), res.GetIsCreditCardUser())
	if setErr != nil {
		logger.Error(ctx, "error setting credit card details in Redis", zap.Error(setErr))
	}

	return res, nil
}

// setCreditCardStatusInCache sets the credit card status in the Redis cache.
func (s *Service) setCreditCardStatusInCache(ctx context.Context, key string, value bool) error {
	// TTL is set to 24 hours for the key
	err := s.fireflyRedisClient.Set(ctx, key, value, time.Hour*24).Err()
	if err != nil {
		return err
	}
	return nil
}

// getCreditCardStatusFromCache retrieves the credit card status from the Redis cache.
func (s *Service) getCreditCardStatusFromCache(ctx context.Context, key string) (bool, error) {
	result, err := s.fireflyRedisClient.Get(ctx, key).Result()
	if err != nil {
		return false, err
	}

	value, err := strconv.ParseBool(result)
	if err != nil {
		return false, err
	}

	return value, nil
}

func (s *Service) GetCardRequestByExternalVendorId(ctx context.Context, req *ffPb.GetCardRequestByExternalVendorIdRequest) (*ffPb.GetCardRequestByExternalVendorIdResponse, error) {
	extId := req.GetExternalVendorId()
	if extId == "" {
		return &ffPb.GetCardRequestByExternalVendorIdResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unique user id can not be empty"),
		}, nil
	}

	daoResp, err := s.cardRequestDao.GetByExternalVendorId(ctx, extId, []ffEnumsPb.CardRequestFieldMask{
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_WORKFLOW,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID,
	})
	if err != nil {
		logger.Error(ctx, "error in GetByExternalVendorId", zap.Error(err))
		if errors.Is(epifierrors.ErrRecordNotFound, err) {
			return &ffPb.GetCardRequestByExternalVendorIdResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &ffPb.GetCardRequestByExternalVendorIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &ffPb.GetCardRequestByExternalVendorIdResponse{
		CardRequests: daoResp,
		Status:       rpc.StatusOk(),
	}, nil
}

// nolint: dupl
func (s *Service) ValidateCardPresence(ctx context.Context, req *ffPb.ValidateCardPresenceRequest) (*ffPb.ValidateCardPresenceResponse, error) {
	res := &ffPb.ValidateCardPresenceResponse{}
	switch req.GetCardType() {
	case types.CardType_DEBIT:
		cardForActor, err := s.getLatestDebitCard(ctx, req.GetActorId())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Info(ctx, "user does not have a DC", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusOk()
			res.PresenceStatus = commontypes.BooleanEnum_FALSE
			return res, nil
		case err != nil:
			logger.Error(ctx, "error in fetching latest dc", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		cardNumber := cardForActor.GetBasicInfo().GetMaskedCardNumber()
		if len(cardNumber) < 4 {
			logger.Error(ctx, "invalid card number for the user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		last4Digits := cardNumber[len(cardNumber)-4:]
		if last4Digits != req.GetLastFourDigits() {
			res.PresenceStatus = commontypes.BooleanEnum_FALSE
		} else {
			res.PresenceStatus = commontypes.BooleanEnum_TRUE
		}
	case types.CardType_CREDIT:
		ccForUser, err := s.getLatestCreditCard(ctx, req.GetActorId())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Info(ctx, "user does not have a CC", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusOk()
			res.PresenceStatus = commontypes.BooleanEnum_FALSE
			return res, nil
		case err != nil:
			logger.Error(ctx, "error in fetching latest cc", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		cardNumber := ccForUser.GetBasicInfo().GetMaskedCardNumber()
		if len(cardNumber) < 4 {
			logger.Error(ctx, "invalid cc number for the user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		last4Digits := cardNumber[len(cardNumber)-4:]
		if last4Digits != req.GetLastFourDigits() {
			res.PresenceStatus = commontypes.BooleanEnum_FALSE
		} else {
			res.PresenceStatus = commontypes.BooleanEnum_TRUE
		}
	default:
		logger.Error(ctx, "no card type present in request", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

// getLatestDebitCard fetches the latest debit card for the user.
func (s *Service) getLatestDebitCard(ctx context.Context, actorId string) (*card.Card, error) {
	latestDcRes, err := s.cardProvisioningClient.GetLatestCardForActorIds(ctx, &cardProvisioningPb.GetLatestCardForActorIdsRequest{
		ActorIds: []string{actorId},
	})
	switch {
	case err != nil:
		return nil, errors.Wrap(err, "error in fetching latest dc for actor id")
	case latestDcRes.GetStatus().IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case !latestDcRes.GetStatus().IsSuccess():
		return nil, errors.New(fmt.Sprintf("[%v] status received from GetLatestCardForActorIds", latestDcRes.GetStatus()))
	default:
		cardForActor, ok := latestDcRes.GetCards()[actorId]
		if !ok {
			return nil, epifierrors.ErrRecordNotFound
		}
		return cardForActor, nil
	}
}

// getLatestCreditCard fetches the latest credit card for the user.
func (s *Service) getLatestCreditCard(ctx context.Context, actorId string) (*ffPb.CreditCard, error) {
	ccForUser, err := s.creditCardDao.GetByActorId(ctx, actorId, nil)
	if err != nil {
		return nil, errors.Wrap(err, "error in fetching cc for user")
	}
	return ccForUser[0], nil
}

func isCardStateValidForWorkflowType(cardState ffEnumsPb.CardState, wfType ffEnumsPb.CardRequestWorkFlow) bool {
	validStatesForWf, ok := validCardStatesForWorkflows[wfType]
	if !ok {
		// in case there are no valid states for a wf type, we will return true since we are
		// following a whitelisting approach
		return true
	}
	return lo.Contains(validStatesForWf, cardState)
}

/*
The TriggerUnsecuredCCRenewalFeeReversal Rpc is responsible for initiating the reversal process for renewal
fees associated with unsecured credit cards. Here's a breakdown of what it does:

Check User Eligibility: It first checks whether the user is eligible for fee reversal by calling the isUserEligibleForFeeReversal function.
This function verifies if the user is not in a "DPD" (Days Past Due) status.

Fetch Credit Account Information: If the user is eligible, it fetches information about the user's credit account to ensure they have an unsecured credit card.

Fetch Credit Card Information: It then retrieves details about the user's credit card to validate its state (whether it's closed or active).

Fetch Latest Renewal Fee Transaction: The function retrieves the latest renewal fee transaction associated with the user's credit account.

Fetch Card Request for Reversal: It fetches any existing card request for the reversal of renewal fees.
If there's already an ongoing card request for fee reversal, it logs and returns. if not then follow next steps

Create a New Card Request for Reversal: If no existing card request is found, it creates a new card request for the fee reversal process.

Call Vendor Gateway API: call the vendor gateway API for triggering the reversal process.

Return Success Response: Finally, if all steps are completed successfully,
it returns a success response indicating that the fee reversal process has been initiated.

Overall, this function orchestrates the various steps involved in initiating the reversal of renewal fees for unsecured credit cards,
including eligibility checks, data retrieval, and request creation.
*/
// nolint:dupl
func (s *Service) TriggerUnsecuredCCRenewalFeeReversal(ctx context.Context, req *ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest) (*ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse, error) {
	var (
		res                        = &ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse{}
		shouldCreateNewCardRequest bool
		cardRequest                = &ffPb.CardRequest{}
		vendorExtTxnId             string
	)

	// Check if the user is eligible for fee reversal
	isUserEligibleForFeeReversal, err := s.isUserEligibleForFeeReversal(ctx, req)
	switch {
	case err != nil:
		// Log and return response indicating user is not eligible
		logger.Error(ctx, "failed to check user's eligibility for fee reversal", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	case !isUserEligibleForFeeReversal:
		// Log and return response indicating user is not eligible
		logger.Info(ctx, "user is not eligible for fee reversal", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusFailedPreconditionWithDebugMsg("User is Dpd")
		return res, nil
	default:
		// User is eligible, proceed with the process
	}

	// Fetch credit account information for the actor
	creditAccounts, rpcErr := s.ffAccountingClient.GetAccounts(ctx, &ffAccPb.GetAccountsRequest{GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: req.GetActorId()}})
	if err = epifigrpc.RPCError(creditAccounts, rpcErr); err != nil {
		logger.Error(ctx, "error getting credit accounts", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	// Select the first credit account
	creditAccount := creditAccounts.GetAccounts()[0]

	// Check if the credit card program is unsecured
	if !ffPkg.IsCreditCardProgramUnsecured(creditAccount.GetCardProgram()) {
		logger.Info(ctx, "user does not have an unsecured credit card", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusFailedPreconditionWithDebugMsg("user does not have an unsecured credit card")
		return res, nil
	}

	// Fetch credit card information for the actor
	creditCards, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// Handle case where credit card is not found
		logger.Error(ctx, "credit card not found for actor id: ", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusFailedPreconditionWithDebugMsg("credit card not found for actor id")
		return res, nil
	case err != nil:
		// Handle error while fetching credit card
		logger.Error(ctx, "error fetching credit card for actor id: ", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("error fetching credit card for actor id")
		return res, nil
	}
	// Select the first credit card
	creditCard := creditCards[0]

	// Check if the credit card is closed
	if creditCard.GetCardState() == ffEnumsPb.CardState_CARD_STATE_CLOSED {
		logger.Info(ctx, "credit card has been closed, cannot reverse the fee", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusFailedPreconditionWithDebugMsg("credit card has been closed, cannot reverse the fee")
		return res, nil
	}

	// Fetch the latest renewal fee transaction
	latestRenewalFeeTxn, err := s.fetchLatestRenewalFeeTransaction(ctx, creditAccount.GetId())
	if err != nil {
		// Log and return error response if failed to fetch renewal fee transaction
		logger.Error(ctx, "error fetching latest renewal fee transaction", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	switch {
	case latestRenewalFeeTxn != nil:
		vendorExtTxnId = latestRenewalFeeTxn.GetVendorExtTxnId()
	default:
		logger.Error(ctx, "no renewal fee related txn found", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternalWithDebugMsg("no renewal fee related txn found")
		return res, nil
	}

	// Fetch card request for renewal fee reversal
	cardRequest, err = s.cardRequestDao.GetByOrchestrationId(ctx, vendorExtTxnId, nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) || cardRequest.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		// Log if card request not found, will create a new one
		logger.Info(ctx, "no card request found for reversal of renewal fees, creating a new one", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		shouldCreateNewCardRequest = true
	case err != nil:
		// Handle error while fetching card request
		logger.Error(ctx, "error fetching card request for renewal fee reversal", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	case cardRequest.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS &&
		cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetRenewalFeeWaiverType() ==
			ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL && cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetIsFeeWaiverTriggered():
		logger.Info(ctx, "already in progress card request for reversal of renewal fees exists", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusAlreadyExistsWithDebugMsg("already in progress card request for reversal of renewal fees exists, please wait for reversal transaction to be reflected")
		return res, nil
	case cardRequest.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS &&
		cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetRenewalFeeWaiverType() ==
			ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_THRIVE_VOUCHERS:
		cardRequest.RequestDetails = &ffPb.CardRequestDetails{
			Reason: req.GetDescription(),
			Data: &ffPb.CardRequestDetails_RenewalFeeWaiverRequestDetails{
				RenewalFeeWaiverRequestDetails: &ffPb.RenewalFeeWaiverRequestDetails{
					RenewalFeeWaiverType: ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL,
				},
			},
			CardProgram: creditAccount.GetCardProgram(),
		}
		updateErr := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS})
		if updateErr != nil {
			logger.Error(ctx, "error marking fee waiver trigger flag false on vg api failure", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(updateErr.Error())
			return res, nil
		}
	case cardRequest.GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		logger.Info(ctx, "already processed waiver of renewal fees", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusAlreadyExistsWithDebugMsg("already processed waiver of renewal fees")
		return res, nil
	default:
	}

	if shouldCreateNewCardRequest {
		// Create a new card request for renewal fee reversal
		cardRequest, err = s.cardRequestDao.Create(ctx, &ffPb.CardRequest{
			OrchestrationId: vendorExtTxnId,
			CardId:          creditCard.GetId(),
			ActorId:         creditAccount.GetActorId(),
			Vendor:          commonvgpb.Vendor_M2P,
			RequestDetails: &ffPb.CardRequestDetails{
				Reason: req.GetDescription(),
				Data: &ffPb.CardRequestDetails_RenewalFeeWaiverRequestDetails{
					RenewalFeeWaiverRequestDetails: &ffPb.RenewalFeeWaiverRequestDetails{
						RenewalFeeWaiverType: ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL,
					},
				},
				CardProgram: creditAccount.GetCardProgram(),
			},
			Workflow:   ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_CARD_FEE_WAIVER,
			Status:     ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
			Provenance: req.GetProvenance(),
		})
		if err != nil {
			// Handle error in creating card request
			logger.Error(ctx, "error in creating card request for fee reversal", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg("error in creating card request for fee reversal")
			return res, nil
		}
	}

	if !cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetIsFeeWaiverTriggered() {
		vgRes, vgErr := s.ccVgClient.FeeReversal(ctx, &creditcard.FeeReversalRequest{
			Header:      &commonvgpb.RequestHeader{Vendor: cardRequest.GetVendor()},
			ExtTxnId:    strings.TrimSuffix(vendorExtTxnId, "_FEES"),
			EntityId:    creditAccount.GetReferenceId(),
			Description: req.GetDescription(),
			Amount:      latestRenewalFeeTxn.GetAmount(),
		})
		if err = epifigrpc.RPCError(vgRes, vgErr); err != nil {
			logger.Error(ctx, "error calling vg api", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return res, nil
		}

		cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().IsFeeWaiverTriggered = true
		cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().IsFeeReversalTriggered = true
		cardRequest.GetRequestDetails().GetRenewalFeeWaiverRequestDetails().IsFeeServiceTaxReversalTriggered = true
		updateErr := s.cardRequestDao.Update(ctx, cardRequest, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS})
		if updateErr != nil {
			logger.Error(ctx, "error marking fee reversal trigger flag true on vg api success", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(updateErr.Error())
			return res, nil
		}
	}

	// Return success response
	res.Status = rpc.StatusOk()
	return res, nil
}

// isUserEligibleForFeeReversal checks if the user is eligible for renewal fee reversal.
func (s *Service) isUserEligibleForFeeReversal(ctx context.Context, req *ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest) (bool, error) {
	// Fetch configuration for unsecured credit card renewal fee reversal
	unsecuredCCRenewalFeeReversalConfig := s.genConf.UnsecuredCCRenewalFeeReversalConfig()

	// Check if actor ID is present in the request
	if req.GetActorId() == "" {
		logger.Error(ctx, "actor id is not present in request")
		return false, errors.New("actor id is not present in request")
	}

	// Check if actor is a member of the ineligible user segment
	userSegmentRes, err := s.segmentationServiceClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    req.GetActorId(),
		SegmentIds: []string{unsecuredCCRenewalFeeReversalConfig.IneligibleUsersSegmentId()},
	})
	switch {
	case err != nil:
		// Handle error while checking with Segmentation Service
		return false, errors.Wrap(err, "error checking actor's segment ID with Segmentation Service")
	case !userSegmentRes.GetStatus().IsSuccess():
		// Handle failure while invoking segment service for segment validation
		return false, errors.New("failed invoking segment service for validation of segmentId")
	default:
		// do nothing
	}

	// Extract membership information from segment response
	membership, ok := userSegmentRes.GetSegmentMembershipMap()[unsecuredCCRenewalFeeReversalConfig.IneligibleUsersSegmentId()]
	switch {
	case !ok:
		// Handle missing segment ID
		return false, errors.New("the segment ID does not exist")
	case membership == nil:
		// Handle missing membership for the segment
		return false, errors.New("the membership does not exist for this segment")
	case membership.GetIsActorMember():
		// Handle case where user is a DPD user
		return false, nil
	}
	// User is eligible for renewal fee reversal
	return true, nil
}

// fetchLatestRenewalFeeTransaction fetches the latest renewal fee transaction for a given account.
func (s *Service) fetchLatestRenewalFeeTransaction(ctx context.Context, accountId string) (*ffAccPb.CardTransaction, error) {
	// Fetch fee transactions for the credit account
	feeTransactions, rpcErr := s.ffAccountingClient.GetTransactions(ctx, &ffAccPb.GetTransactionsRequest{
		GetBy: &ffAccPb.GetTransactionsRequest_AccountIdAndCategories{
			AccountIdAndCategories: &ffAccPb.AccountIdAndCategories{
				AccountId: accountId,
				TransactionCategories: []enums.TransactionCategory{
					enums.TransactionCategory_TRANSACTION_CATEGORY_FEES,
				},
			},
		},
	})
	if err := epifigrpc.RPCError(feeTransactions, rpcErr); err != nil {
		return nil, errors.Wrap(err, "error getting fee transactions")
	}

	// Process each fee transaction to find latest renewal fee txn
	for _, feeTransaction := range feeTransactions.GetTransactions() {
		if feeTransaction.GetBeneficiaryInfo().GetBeneficiaryName() == annualMembershipFeesBeneficiaryName ||
			(strings.ToLower(regexp.MustCompile("[^a-zA-Z]+").ReplaceAllString(feeTransaction.GetDescription(), "")) == annualMembershipFeesIdentifier &&
				strings.HasSuffix(feeTransaction.GetVendorExtTxnId(), "_FEES")) {
			return feeTransaction, nil
		}
	}

	return nil, nil
}

// fetchLatestRenewalFeeServiceTaxTransaction fetches the latest renewal fee service tax transaction for a given account.
// nolint:dupl
func (s *Service) fetchLatestRenewalFeeServiceTaxTransaction(ctx context.Context, accountId string) (*ffAccPb.CardTransaction, error) {
	// Fetch fee transactions for the credit account
	feeTransactions, rpcErr := s.ffAccountingClient.GetTransactions(ctx, &ffAccPb.GetTransactionsRequest{
		GetBy: &ffAccPb.GetTransactionsRequest_AccountIdAndCategories{
			AccountIdAndCategories: &ffAccPb.AccountIdAndCategories{
				AccountId: accountId,
				TransactionCategories: []enums.TransactionCategory{
					enums.TransactionCategory_TRANSACTION_CATEGORY_SERVICE_TAX,
				},
			},
		},
	})
	if err := epifigrpc.RPCError(feeTransactions, rpcErr); err != nil {
		return nil, errors.Wrap(err, "error getting fee transactions")
	}

	// Process each fee transaction to find latest renewal fee service tax txn
	for _, feeTransaction := range feeTransactions.GetTransactions() {
		if feeTransaction.GetBeneficiaryInfo().GetBeneficiaryName() == annualMembershipFeesGSTBeneficiaryName ||
			(strings.ToLower(regexp.MustCompile("[^a-zA-Z]+").ReplaceAllString(feeTransaction.GetDescription(), "")) == annualMembershipFeesGSTIdentifier &&
				strings.HasSuffix(feeTransaction.GetVendorExtTxnId(), "_FEES_ST")) {
			return feeTransaction, nil
		}
	}
	return nil, nil
}

func (s *Service) isFireflyV2Enabled(ctx context.Context, actorId string) bool {
	return pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.genConf.IsFireflyV2Enabled().PlatformVersionCheck()) &&
		featureCfg.IsFeatureEnabledForUser(ctx, actorId, &cfg.FeatureReleaseConfig{
			IsFeatureRestricted: s.genConf.IsFireflyV2Enabled().UserGroupCheck().EnableUserGroupCheck(),
			AllowedUserGroups:   s.genConf.IsFireflyV2Enabled().UserGroupCheck().AllowedUserGrp(),
		}, s.userGrpClient, s.userClient, s.actorClient)
}
