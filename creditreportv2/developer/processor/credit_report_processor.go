//nolint:goimports
package processor

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/proto/json"
	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/gamma/api/creditreportv2"
	devpb "github.com/epifi/gamma/api/creditreportv2/developer"
	dbstatepb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/creditreportv2/dao"
)

type CreditReportProcessor struct {
	crDAO dao.CreditReportDao
}

func NewCreditReportProcessor(crDAO dao.CreditReportDao) *CreditReportProcessor {
	return &CreditReportProcessor{crDAO: crDAO}
}

func (p *CreditReportProcessor) FetchParamList(ctx context.Context, entity devpb.CreditReportEntity) ([]*dbstatepb.ParameterMeta, error) {
	params := []*dbstatepb.ParameterMeta{
		{
			Name:            CreditReportId,
			Label:           CreditReportIdLabel,
			Type:            dbstatepb.ParameterDataType_STRING,
			ParameterOption: dbstatepb.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            dbstatepb.ParameterDataType_STRING,
			ParameterOption: dbstatepb.ParameterOption_OPTIONAL,
		},
		{
			Name:            Limit,
			Label:           LimitLabel,
			Type:            dbstatepb.ParameterDataType_INTEGER,
			ParameterOption: dbstatepb.ParameterOption_OPTIONAL,
		},
	}
	return params, nil
}

func (p *CreditReportProcessor) FetchData(ctx context.Context, entity devpb.CreditReportEntity, filters []*dbstatepb.Filter) (string, error) {
	var id, actorID string
	limit := 1 // default limit
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case CreditReportId:
			id = filter.GetStringValue()
		case ActorId:
			actorID = filter.GetStringValue()
		case Limit:
			limit = int(filter.GetIntegerValue())
		default:
			return "", fmt.Errorf("unknown param type: %s", filter.GetParameterName())
		}
	}

	if id != "" {
		creditReport, err := p.crDAO.GetById(ctx, id)
		if err != nil {
			return "", errors.Wrap(err, "error in fetching credit report using credit report id")
		}
		if creditReport != nil {
			maskCreditReport(creditReport)
		}

		e, err := json.Marshal(creditReport)
		if err != nil {
			return "", errors.Wrap(err, "error in marshalling credit report to json")
		}
		return string(e), nil
	}
	if actorID != "" {
		creditReports, err := p.crDAO.GetReportsByActorId(ctx, actorID, limit)
		if err != nil {
			return "", errors.Wrap(err, "error in fetching credit reports using actor id")
		}
		for _, report := range creditReports {
			if report != nil {
				maskCreditReport(report)
			}
		}

		e, err := json.Marshal(creditReports)
		if err != nil {
			return "", errors.Wrap(err, "error in marshalling credit reports to json")
		}
		return string(e), nil
	}

	return "", fmt.Errorf("no valid filters provided")
}

//nolint:gosec
func maskCreditReport(report *creditreportv2.CreditReport) {
	if report == nil {
		return
	}

	report.CreditReportDataRaw = nil
	if report.CreditReportData != nil {
		crData := report.CreditReportData

		if crData.CreditScore != 0 {
			crData.CreditScore = int32(mask.GetMaskedInt(crData.GetCreditScore()))
		}

		if crData.GetPan() != "" {
			crData.Pan = mask.GetMaskedString(mask.MaskAllChars, crData.GetPan())
		}

		if originalDob := crData.GetDob(); originalDob != nil {
			crData.Dob = &date.Date{
				Year:  originalDob.GetYear(),                                 // Keep original year
				Month: int32(mask.GetMaskedInt(int(originalDob.GetMonth()))), // Mask month
				Day:   int32(mask.GetMaskedInt(int(originalDob.GetDay()))),   // Mask day
			}
		}

		if crData.Address != nil {
			for _, addr := range crData.Address {
				if addr != nil {
					if addr.GetFirstLineOfAddressNonNormalized() != "" {
						addr.FirstLineOfAddressNonNormalized = mask.GetMaskedString(mask.MaskAllChars, addr.GetFirstLineOfAddressNonNormalized())
					}
					if addr.GetSecondLineOfAddressNonNormalized() != "" {
						addr.SecondLineOfAddressNonNormalized = mask.GetMaskedString(mask.MaskAllChars, addr.GetSecondLineOfAddressNonNormalized())
					}
					if addr.GetThirdLineOfAddressNonNormalized() != "" {
						addr.ThirdLineOfAddressNonNormalized = mask.GetMaskedString(mask.MaskAllChars, addr.GetThirdLineOfAddressNonNormalized())
					}
					if addr.GetFifthLineOfAddressNonNormalized() != "" {
						addr.FifthLineOfAddressNonNormalized = mask.GetMaskedString(mask.MaskAllChars, addr.GetFifthLineOfAddressNonNormalized())
					}
				}
			}
		}
	}
}
