package rule_subscriptions

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/google/uuid"
	"github.com/google/wire"
	copier "github.com/jinzhu/copier/035"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	execpb "github.com/epifi/gamma/api/rms/command_processor"
	pb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/rms/orchestrator/event"
	"github.com/epifi/gamma/pkg/rms"
	"github.com/epifi/gamma/rms/dao"
	"github.com/epifi/gamma/rms/dao/model"
)

var RuleSubscriptionsDaoWireSet = wire.NewSet(NewRuleSubscriptionPgDb, wire.Bind(new(dao.RuleSubscriptionsDao), new(*RuleSubscriptionsPgDb)))

const MaxRetriesForDuplicateKeyQueries = 5

type RuleSubscriptionsPgDb struct {
	db                                          *gormv2.DB
	maxPageSize                                 uint32
	maxPageSizeToFetchSubscriptionsForExecution uint32
	cutoffParams                                *rms.CutoffParams
}

func NewRuleSubscriptionPgDb(db *gorm.DB, cutoffParams *rms.CutoffParams) *RuleSubscriptionsPgDb {
	return &RuleSubscriptionsPgDb{
		db:          db,
		maxPageSize: 50,
		maxPageSizeToFetchSubscriptionsForExecution: 2000,
		cutoffParams: cutoffParams,
	}
}

var _ = RuleSubscriptionsPgDb{}

func (a *RuleSubscriptionsPgDb) Create(ctx context.Context, subscription *pb.RuleSubscription) (*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	ruleSubscription, err := subscriptionProtoToModel(ctx, subscription)
	if err != nil || ruleSubscription == nil {
		logger.Info(ctx, "Failed to create subscription", zap.String(logger.ACTOR_ID_V2, subscription.ActorId),
			zap.String(logger.RULE_ID, subscription.RuleId), zap.Error(err))
		return nil, err
	}

	err = db.Transaction(func(tx *gormv2.DB) error {
		err = addNewSubscription(ctx, tx, ruleSubscription)
		if err != nil {
			return err
		}

		err = a.createSubscriptionForPreEmptiveNotificationsRule(ctx, tx, ruleSubscription.ActorId)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		logger.Error(ctx, "Error while creating user rule subscription", zap.Error(err))
		return nil, err
	}
	return subscriptionModelToProto(ruleSubscription), nil
}

func (a *RuleSubscriptionsPgDb) createSubscriptionForPreEmptiveNotificationsRule(ctx context.Context, tx *gormv2.DB, actorId string) error {
	var rule *model.Rule
	if err := tx.Model(&model.Rule{}).Where("rule_type_for_special_handling = ?", pb.RuleTypeForSpecialHandling_RULE_TYPE_PRE_EMPTIVE_NOTIFICATIONS).Take(&rule).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			logger.Info(ctx, "pre-emptive rule not present, skipping subscription for pre-emptive notification rule", zap.Error(err))
			return nil
		}
	}

	var existingSubsCount int64
	if err := tx.Model(&model.SubscriptionRuntimeInfo{}).Where("rule_id = ? and actor_id = ?", rule.ID, actorId).Count(&existingSubsCount).Error; err != nil {
		logger.Error(ctx, "error in getting existing subscriptions count for pre-emptive notification rule", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return err
	}

	if existingSubsCount != 0 {
		logger.Debug(ctx, "subscription for pre-emptive rule already exists, skipping new creation")
		return nil
	}

	err := addNewSubscription(ctx, tx, &model.RuleSubscription{
		RuleId:                rule.ID,
		ActorId:               actorId,
		State:                 pb.RuleSubscriptionState_ACTIVE,
		VersionState:          pb.SubscriptionVersionState_CURRENT,
		StateChangeReason:     pb.SubscriptionStateChangeReason_NEW_SUBSCRIPTION,
		StateChangeProvenance: pb.SubscriptionStateChangeProvenance_INTERNAL,
		ExecutionState:        pb.SubscriptionExecutionState_SUBSCRIPTION_EXECUTION_ALLOWED,
	})
	if err != nil {
		return err
	}
	return nil
}

func addNewSubscription(ctx context.Context, tx *gormv2.DB, subscription *model.RuleSubscription) error {
	subscription.VersionState = pb.SubscriptionVersionState_CURRENT

	if err := tx.Create(subscription).Error; err != nil {
		logger.Error(ctx, "Error while creating user rule subscription", zap.Error(err), zap.Any("Subscription", subscription))
		return err
	}

	// if clientRequestId is empty string, then pass nil to the query to insert null
	var clientRequestId *string
	if subscription.ClientRequestId != "" {
		clientRequestId = &subscription.ClientRequestId
	}

	if err := tx.Exec("INSERT INTO subscription_runtime_infos (id, rule_id, actor_id, state, aggregation_id, "+
		"aggregation_type, rule_param_values, client_request_id) (SELECT ?, ?, ?, ?, "+
		"CASE WHEN subscriptions_aggregation_type = ? then id::text "+
		"else ( select tag_id from rule_tags join rule_tag_mappings on tag_id = rule_tags.id where rule_id = rules.id and is_display_tag = true limit 1 ) "+
		"end as aggregation_id, subscriptions_aggregation_type as aggregation_type, ?, ? from rules "+
		"where id = ?)", subscription.ID, subscription.RuleId, subscription.ActorId, subscription.State,
		pb.SubscriptionsAggregationType_AGGREGATE_ON_RULE, subscription.RuleParamValues,
		clientRequestId, subscription.RuleId).Error; err != nil {
		logger.Error(ctx, "Error while creating subscription runtime info", zap.Error(err), zap.Any("Subscription", subscription))
		return err
	}
	return nil
}

func (a *RuleSubscriptionsPgDb) Update(ctx context.Context, subscriptionId string, subscription *pb.RuleSubscription,
	updatedFields []pb.RuleSubscriptionFieldMask) (*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "Update", time.Now())
	if _, err := uuid.Parse(subscriptionId); err != nil || subscription == nil {
		return nil, fmt.Errorf("Cannot update rule subscription, either invalid subscriptionId or request has nil reference %v, error %w ", subscriptionId, err)
	}

	if subscription.StateChangeProvenance == pb.SubscriptionStateChangeProvenance_STATE_CHANGE_PROVENANCE_UNSPECIFIED ||
		subscription.StateChangeReason == pb.SubscriptionStateChangeReason_UNSPECIFIED {
		return nil, fmt.Errorf("state change provenance and reason are mandatory for updating subscription, subId: %s", subscriptionId)
	}

	ruleSubscription, err := subscriptionProtoToModel(ctx, subscription)
	if err != nil {
		logger.Error(ctx, "Unable to create actor rule subscription", zap.String(logger.SUBSCRIPTION_ID, subscriptionId),
			zap.String(logger.RULE_ID, subscription.RuleId), zap.Error(err))
		return nil, err
	}

	currentVersion, err := a.getCurrentSubscriptionVersion(ctx, subscriptionId)
	if err != nil {
		return nil, err
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	var updatedSubVersion *model.RuleSubscription
	err = dao.RunTxnWithRetriesOnDuplicateRowError(ctx, db, func(tx *gormv2.DB) error {
		expiryTime := storage.PgNow()
		if _, err = a.expireCurrentSubscriptions(ctx, []string{subscriptionId}, expiryTime, tx); err != nil {
			logger.Error(ctx, "Failed to expire existing subscription version", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, currentVersion.ID))
			return err
		}

		newVersion := &model.RuleSubscription{}
		if err = copier.Copy(newVersion, currentVersion); err != nil {
			logger.Error(ctx, "Failed to clone subscription from older version", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, currentVersion.ID))
			return err
		}
		newVersion.VersionId = ""
		newVersion.VersionState = pb.SubscriptionVersionState_CURRENT
		newVersion.VersionValidFrom = &expiryTime
		newVersion.StateChangeReason = subscription.StateChangeReason
		newVersion.StateChangeProvenance = subscription.StateChangeProvenance
		newVersion.CreatedAt = nil
		newVersion.UpdatedAt = nil

		var updatedSubRuntime *model.SubscriptionRuntimeInfo
		updatedSubVersion, updatedSubRuntime = constructUpdatedVersion(ctx, newVersion, ruleSubscription, updatedFields)
		updateCutoffParamUpdatedTime(currentVersion, updatedSubVersion, updatedFields, a.cutoffParams.ParamMap)

		if err = tx.Create(updatedSubVersion).Error; err != nil {
			logger.Error(ctx, "Error in creating new subscription version", zap.String(logger.SUBSCRIPTION_ID, subscriptionId), zap.Error(err))
			return err
		}

		if err = tx.Model(&model.SubscriptionRuntimeInfo{Id: subscriptionId}).Updates(updatedSubRuntime).Error; err != nil {
			logger.Error(ctx, "Error in updating subscription runtime info", zap.String(logger.SUBSCRIPTION_ID, subscriptionId), zap.Error(err))
			return err
		}
		return nil
	}, MaxRetriesForDuplicateKeyQueries)
	if err != nil {
		logger.Error(ctx, "Error in updating subscription", zap.String(logger.SUBSCRIPTION_ID, subscriptionId), zap.Error(err))
		return nil, err
	}
	return subscriptionModelToProto(updatedSubVersion), nil
}

func (a *RuleSubscriptionsPgDb) UpdateStateBulk(ctx context.Context, subscriptionIds []string,
	targetState pb.RuleSubscriptionState,
	changeReason pb.SubscriptionStateChangeReason, provenance pb.SubscriptionStateChangeProvenance) error {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "UpdateStateBulk", time.Now())
	requestTime := storage.PgNow()
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	err := dao.RunTxnWithRetriesOnDuplicateRowError(ctx, db, func(tx *gormv2.DB) error {
		versionIds, err := a.expireCurrentSubscriptions(ctx, subscriptionIds, requestTime, tx)
		if err != nil {
			logger.Error(ctx, "Failed to expire subscriptions for rule", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds),
				zap.String(logger.TARGET_STATE, targetState.String()), zap.Error(err))
			return err
		}

		if err = tx.Exec("insert into rule_subscriptions (id, rule_id, state, state_change_reason, "+
			"state_change_provenance, actor_id, valid_from, valid_till, rule_param_values, created_at, updated_at, "+
			"subscription_expiry_data, cutoff_param_updated_at, version_valid_from, version_state, execution_state) select id, rule_id, ?, ?, ?, "+
			"actor_id, valid_from, valid_till, rule_param_values, now(), now(), subscription_expiry_data, cutoff_param_updated_at, now(), ?, execution_state "+
			"FROM rule_subscriptions WHERE version_id in (?)", targetState, changeReason, provenance,
			pb.SubscriptionVersionState_CURRENT.String(), versionIds).Error; err != nil {
			logger.Error(ctx, "Failed to add new subscription versions", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds), zap.Error(err))
			return err
		}

		if err = tx.Model(&model.SubscriptionRuntimeInfo{}).Where("id in (?)", subscriptionIds).
			Updates(&model.SubscriptionRuntimeInfo{State: targetState}).Error; err != nil {
			logger.Error(ctx, "Failed to update bulk subscription state to subscription runtime info", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds), zap.Error(err))
			return err
		}
		return nil
	}, MaxRetriesForDuplicateKeyQueries)
	if err != nil {
		logger.Error(ctx, "error in bulk updating subscriptions state", zap.Error(err), zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds))
		return err
	}
	logger.Info(ctx, "Updated all subscriptions", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds))
	return nil
}

// cutoff params are those params which affects execution of subscription (configured in yml files)
// eg: player name, deposit amount, state of subscription (Active/Inactive)
// SD Name is not a cutoff param as changing SD name is purely related to action
// editing of cutoff params in cases where rewards are associated will be required to check for some deadline related stuffs
// During update of a subscription, user may chose to update cutoff params,
// if such update is requested, we update cutoffUpdatedTime of the subscription
func updateCutoffParamUpdatedTime(existingVersion *model.RuleSubscription, newVersion *model.RuleSubscription,
	fieldsToBeUpdated []pb.RuleSubscriptionFieldMask, cutoffParams map[string]bool) {
	var paramValuesUpdated bool
	var stateUpdated bool
	for _, field := range fieldsToBeUpdated {
		if field == pb.RuleSubscriptionFieldMask_RULE_PARAM_VALUES {
			paramValuesUpdated = true
		} else if field == pb.RuleSubscriptionFieldMask_STATE {
			stateUpdated = true
		}
	}

	if !(paramValuesUpdated || stateUpdated) {
		return
	}

	cutoffUpdateTime := storage.PgNow()

	if stateUpdated && newVersion.State != existingVersion.State {
		newVersion.CutoffParamUpdatedAt = &cutoffUpdateTime
		return
	}

	for key, val := range newVersion.RuleParamValues.GetRuleParamValues() {
		if cutoffParams[strings.ToLower(key)] && !proto.Equal(existingVersion.RuleParamValues.RuleParamValues[key], val) {
			newVersion.CutoffParamUpdatedAt = &cutoffUpdateTime
			break
		}
	}
}

func (a *RuleSubscriptionsPgDb) GetById(ctx context.Context, id string) (*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetById", time.Now())
	subscription, err := a.getCurrentSubscriptionVersion(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch actor rule subscription: %w", err)
	}
	return subscriptionModelToProto(subscription), nil
}

func (a *RuleSubscriptionsPgDb) GetByIds(ctx context.Context, ids []string) (map[string]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetByIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	subscriptions := make([]*model.RuleSubscription, 0)
	if err := db.Model(&model.RuleSubscription{}).Where("id in (?) AND version_state = ?", ids, pb.SubscriptionVersionState_CURRENT.String()).
		Find(&subscriptions).Error; err != nil {
		logger.Error(ctx, "Error while getting current rule subscription  ", zap.Strings(logger.SUBSCRIPTION_ID, ids), zap.Error(err))
		return nil, err
	}

	subscriptionsProto := make(map[string]*pb.RuleSubscription)
	for _, subscription := range subscriptions {
		subscriptionsProto[subscription.ID] = subscriptionModelToProto(subscription)
	}
	return subscriptionsProto, nil
}

func (a *RuleSubscriptionsPgDb) GetAllVersionsByIds(ctx context.Context, ids []string, startTime *time.Time,
	endTime *time.Time) (map[string][]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetAllVersionsByIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	versions := make([]*model.RuleSubscription, 0)

	db = db.Model(&model.RuleSubscription{}).Where("id in (?)", ids)

	if startTime != nil {
		db.Where("version_valid_till is null or version_valid_till > ?", startTime)
	}

	if endTime != nil {
		db.Where("version_valid_from < ?", endTime)
	}

	if err := db.Find(&versions).Error; err != nil {
		logger.Error(ctx, "Error while getting current rule subscription for actor ", zap.Strings(logger.SUBSCRIPTION_ID, ids))
		return nil, err
	}

	allVersionsMap := make(map[string][]*pb.RuleSubscription)
	for _, version := range versions {
		versionProto := subscriptionModelToProto(version)
		if _, ok := allVersionsMap[versionProto.GetId()]; !ok {
			allVersionsMap[versionProto.GetId()] = make([]*pb.RuleSubscription, 0)
		}
		allVersionsMap[versionProto.GetId()] = append(allVersionsMap[versionProto.GetId()], versionProto)
	}
	return allVersionsMap, nil
}

func (a *RuleSubscriptionsPgDb) GetAllSubscriptionVersionsForActor(ctx context.Context, eventTypes []event.EventType,
	actorId string,
	startTime time.Time, endTime time.Time) (map[string][]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetAllSubscriptionVersionsForActor", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	var versions []*model.RuleSubscription
	/*
		if we break the query `(version_valid_from < ? AND (version_valid_till is null OR version_valid_till > ?) `in two parts
		1. version_valid_from < ?
		> means all the versions that started before end time
		2. (version_valid_till is null OR version_valid_till > ?)
		> means all the versions which are current OR ended after start_time

		This would serve overlapping versions
		i) started before start_time and ended between start_time and end_time
		ii) started before start_time and ended after end_time (or still `CURRENT`)
		iii) started after start_time and ended before end_time
		iv) started after start_time and ended after end_time
	*/
	if err := db.Model(&model.RuleSubscription{}).
		Where("actor_id = ? AND rule_id in (select id from rules where event_type in (?) and state = 'RULE_STATE_ACTIVE' and deleted_at is null) "+
			"and ( (version_valid_till is null and version_valid_from < ? ) or ( version_valid_from, version_valid_till ) OVERLAPS (? :: timestamp, ? :: timestamp) )",
			actorId, eventTypes, endTime, startTime, endTime).Find(&versions).Error; err != nil {
		logger.Error(ctx, "Error while getting current rule subscription for actor ", zap.Any(logger.EVENT_TYPE, eventTypes))
		return nil, err
	}

	// subscription Id vs versions
	allVersionsMap := make(map[string][]*pb.RuleSubscription)
	for _, version := range versions {
		versionProto := subscriptionModelToProto(version)
		if _, ok := allVersionsMap[versionProto.GetId()]; !ok {
			allVersionsMap[versionProto.GetId()] = make([]*pb.RuleSubscription, 0)
		}
		allVersionsMap[versionProto.GetId()] = append(allVersionsMap[versionProto.GetId()], versionProto)
	}
	return allVersionsMap, nil
}

func (a *RuleSubscriptionsPgDb) Delete(ctx context.Context, subscriptionId string) error {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "Delete", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	err := db.Transaction(func(tx *gormv2.DB) error {
		if err := tx.Delete(&model.RuleSubscription{}).Where(&model.RuleSubscription{
			ID:           subscriptionId,
			VersionState: pb.SubscriptionVersionState_CURRENT,
		}).Error; err != nil {
			logger.Error(ctx, "Error in deleting subscription ", zap.String(logger.SUBSCRIPTION_ID, subscriptionId), zap.Error(err))
			return err
		}

		if err := tx.Delete(&model.SubscriptionRuntimeInfo{}).Where(&model.SubscriptionRuntimeInfo{
			Id: subscriptionId,
		}).Error; err != nil {
			logger.Error(ctx, "Error in deleting subscription runtime info ", zap.String(logger.SUBSCRIPTION_ID, subscriptionId), zap.Error(err))
			return err
		}
		return nil
	})
	if err != nil {
		logger.Error(ctx, "Error in deleting subscription", zap.Error(err))
		return err
	}
	return nil
}

type SubscriptionsByCreatedTime []*model.RuleSubscription

func (rs SubscriptionsByCreatedTime) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs SubscriptionsByCreatedTime) GetTimestamp(index int) time.Time     { return *rs[index].CreatedAt }
func (rs SubscriptionsByCreatedTime) Size() int                            { return len(rs) }

type SubscriptionsByUpdatedTime []*model.RuleSubscription

func (rs SubscriptionsByUpdatedTime) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs SubscriptionsByUpdatedTime) GetTimestamp(index int) time.Time     { return *rs[index].UpdatedAt }
func (rs SubscriptionsByUpdatedTime) Size() int                            { return len(rs) }

func (a *RuleSubscriptionsPgDb) GetSubscriptionsByActorId(ctx context.Context, actorId string, ruleIds []string,
	createdAfter, createdBefore, updatedAfter, updatedBefore *timestamppb.Timestamp, states []pb.RuleSubscriptionState,
	mutualFundId, smartDepositId, usStockId *string,
	paginate bool, pageToken *pagination.PageToken, pageSize uint32) ([]*pb.RuleSubscription, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetSubscriptionsByActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if pageSize > uint32(a.maxPageSize) || pageSize == 0 {
		pageSize = uint32(a.maxPageSize)
	}

	subsRuntimeInfoQuery := db.Model(&model.SubscriptionRuntimeInfo{}).
		Where("actor_id = ?", actorId)

	if len(ruleIds) > 0 {
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where("rule_id in (?)", ruleIds)
	}
	switch {
	case mutualFundId != nil: // index: subscription_runtime_infos_mutual_fund_id_idx
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where("rule_param_values->'ruleParamValues'->'mutualFundVal'->'mutualFundVal'->>'mfId"+
			"' = ?",
			*mutualFundId)
	case smartDepositId != nil: // index: subscription_runtime_infos_smart_deposit_id_idx
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where(
			"rule_param_values->'ruleParamValues'->'depositAccountId'->'sdValue'->>'accountId"+
				"' = ?",
			*smartDepositId)
	case usStockId != nil:
		// TODO(Brijesh): Check if an index is needed for faster querying
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where("rule_param_values->'ruleParamValues'->'usStockValue'->'usStockValue'->>'stockId' = ?", *usStockId)
	}

	if len(states) > 0 {
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where("state in (?)", states)
	}

	subscriptions, pageCtxResp, err := fetchWithTimeFilterAndPagination(ctx, db, subsRuntimeInfoQuery, createdAfter, createdBefore,
		updatedAfter,
		updatedBefore,
		paginate, pageToken, pageSize)
	if err != nil {
		logger.Error(ctx, "Failed to fetch subscription", zap.Error(err))
		return nil, nil, err
	}
	var subscriptionsProto []*pb.RuleSubscription
	for _, subscription := range subscriptions {
		subscriptionsProto = append(subscriptionsProto, subscriptionModelToProto(subscription))
	}

	return subscriptionsProto, pageCtxResp, nil
}

// nolint: funlen
func fetchWithTimeFilterAndPagination(ctx context.Context, db, subsRuntimeInfoQuery *gormv2.DB,
	createdAfter, createdBefore, updatedAfter, updatedBefore *timestamppb.Timestamp,
	paginate bool, pageToken *pagination.PageToken, pageSize uint32) ([]*model.RuleSubscription,
	*rpc.PageContextResponse, error) {
	var after, before *timestamppb.Timestamp
	timeColumn := dao.CreatedAtCol
	if createdAfter != nil || createdBefore != nil {
		after = createdAfter
		before = createdBefore
	} else if updatedAfter != nil || updatedBefore != nil {
		after = updatedAfter
		before = updatedBefore
		timeColumn = dao.UpdatedAtCol
	}
	if after != nil {
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where(timeColumn+" >= ?", after.AsTime())
	}
	if before != nil {
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where(timeColumn+" <= ?", before.AsTime())
	}
	if paginate {
		if pageToken != nil {
			if pageToken.IsReverse {
				subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where(timeColumn+" >= ?", pageToken.Timestamp.AsTime()).
					Order(timeColumn)
			} else {
				subsRuntimeInfoQuery = subsRuntimeInfoQuery.Where(timeColumn+" <= ?", pageToken.Timestamp.AsTime()).
					Order(timeColumn + " desc")
			}
			// fetch pageSize + 1 extra row to compute next page availability.
			subsRuntimeInfoQuery = subsRuntimeInfoQuery.Offset(int(pageToken.Offset))
		} else {
			subsRuntimeInfoQuery = subsRuntimeInfoQuery.Order(timeColumn + " desc")
		}
		subsRuntimeInfoQuery = subsRuntimeInfoQuery.Limit(int(pageSize + 1))
	}
	var subsIds []string
	res := subsRuntimeInfoQuery.Select("id").Find(&subsIds)
	if res.Error != nil {
		logger.Error(ctx, "Failed to get subscriptions", zap.Error(res.Error))
		return nil, nil, res.Error
	}

	var subscriptions []*model.RuleSubscription
	if len(subsIds) != 0 {
		// TODO(sakthi) Find efficient query to get the data in single query without loosing efficient index usage.
		// Alternative implementation attempted here fails to use index for mutual fund/SD filters
		// -> https://github.com/epiFi/gamma/blob/aba83e0262d8538a44d9138e86871a9731b60685/rms/dao/rule_subscriptions.go#L419
		db = db.Model(model.RuleSubscription{}).Where("id in (?) and version_state = ? order by array_position(?, id)", subsIds,
			pb.SubscriptionVersionState_CURRENT.String(), pq.StringArray(subsIds))
		res = db.Find(&subscriptions)
		if res.Error != nil {
			logger.Error(ctx, "Failed to get subscriptions", zap.Error(res.Error))
			return nil, nil, res.Error
		}
	}
	var pageCtxResp *rpc.PageContextResponse
	if paginate {
		rows, resp, err := pagination.NewPageCtxResp(pageToken, int(pageSize),
			typeCastAsRows(subscriptions, timeColumn))
		if err != nil {
			logger.Error(ctx, "Failed to create new page context", zap.Error(err))
			return nil, nil, err
		}
		subscriptions = typeCastAsSubscriptions(rows, timeColumn)
		pageCtxResp = resp
	}
	return subscriptions, pageCtxResp, nil
}

func typeCastAsRows(subscriptions []*model.RuleSubscription, timeColumn string) pagination.Rows {
	if timeColumn == dao.CreatedAtCol {
		return SubscriptionsByCreatedTime(subscriptions)
	}
	return SubscriptionsByUpdatedTime(subscriptions)
}

func typeCastAsSubscriptions(subscriptions pagination.Rows, timeColumn string) []*model.RuleSubscription {
	if timeColumn == dao.CreatedAtCol {
		return subscriptions.(SubscriptionsByCreatedTime)
	}
	return subscriptions.(SubscriptionsByUpdatedTime)
}

func (a *RuleSubscriptionsPgDb) GetActiveSubscriptionsByActorId(ctx context.Context, actorId string, ruleIds []string,
	client event.RMSClient) ([]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetActiveSubscriptionsByActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	var subscriptionsProto []*pb.RuleSubscription
	var subscriptions []*model.RuleSubscription

	db = db.Model(&model.RuleSubscription{}).
		Where("actor_id = ? AND state = ? AND version_state = ?", actorId, pb.RuleSubscriptionState_ACTIVE.String(),
			pb.SubscriptionVersionState_CURRENT.String())
	if len(ruleIds) > 0 {
		db = db.Where(" rule_id in (?) ", ruleIds)
	} else if client != event.RMSClient_RMS_CLIENT_UNSPECIFIED {
		db = db.Where("rule_id in (select id from rules where client = ?)", client)
	}

	res := db.Find(&subscriptions)
	if res.Error != nil {
		logger.Error(ctx, "Failed to get active subscriptions", zap.Error(res.Error), zap.Strings(logger.RULE_ID, ruleIds), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, res.Error
	}
	if len(subscriptions) == 0 {
		return nil, fmt.Errorf("record not found for actor: %s, %w", actorId, epifierrors.ErrRecordNotFound)
	}
	for _, subscription := range subscriptions {
		subscriptionsProto = append(subscriptionsProto, subscriptionModelToProto(subscription))
	}
	return subscriptionsProto, nil
}

func (a *RuleSubscriptionsPgDb) GetSubsCount(ctx context.Context, actorId string, ruleIds []string,
	client event.RMSClient, categories []pb.RuleCategory, states []pb.RuleSubscriptionState) (map[string]int64,
	map[string]*pb.StateWiseSubsCount, map[string]*pb.StringToInt64Map, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetSubsCount", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	type SubscriptionCount struct {
		State    string
		Count    int64
		RuleId   string
		Category string
	}

	var countByState []*SubscriptionCount
	subsQuery := db.Model(&model.SubscriptionRuntimeInfo{}).Select("rule_id", "subscription_runtime_infos.state", "category", "count(*)").
		Where("actor_id = ?", actorId).Joins(" join rules on rules.id = rule_id")

	if len(ruleIds) > 0 {
		subsQuery = subsQuery.Where(" rule_id in (?) ", ruleIds)
	}

	if client != event.RMSClient_RMS_CLIENT_UNSPECIFIED {
		subsQuery = subsQuery.Where("client = ?", client)
	}

	if len(categories) > 0 {
		subsQuery = subsQuery.Where("category in (?)", categories)
	}

	if len(states) > 0 {
		subsQuery = subsQuery.Where("subscription_runtime_infos.state in (?)", states)
	}

	if err := subsQuery.Group("rule_id, subscription_runtime_infos.state, category").Find(&countByState).Error; err != nil {
		logger.Error(ctx, "Failed to get subscriptions count", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil, nil, err
	}

	subscriptionCountMap := make(map[string]int64)
	// key is the rule_id, value is map of subscription_state and subs_count for corresponding rule_id and subscription state
	subsCountPerRule := make(map[string]*pb.StateWiseSubsCount)
	subsCountPerCategory := make(map[string]*pb.StringToInt64Map)
	for _, c := range countByState {
		if _, present := subsCountPerRule[c.RuleId]; !present {
			subsCountPerRule[c.RuleId] = &pb.StateWiseSubsCount{SubsCountPerState: make(map[string]int64)}
		}

		if _, p := subsCountPerCategory[c.Category]; !p {
			subsCountPerCategory[c.Category] = &pb.StringToInt64Map{Map: make(map[string]int64)}
		}

		subsCountPerCategory[c.Category].Map[c.State] += c.Count
		// adding count to corresponding subscriptionState
		subscriptionCountMap[c.State] += c.Count
		// adding count to corresponding ruleId and corresponding subscription state
		subsCountPerRule[c.RuleId].SubsCountPerState[c.State] += c.Count
	}
	return subscriptionCountMap, subsCountPerRule, subsCountPerCategory, nil
}

func (a *RuleSubscriptionsPgDb) GetActiveSubscriptionsByRules(ctx context.Context,
	ruleIds []string) ([]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetActiveSubscriptionsByRules", time.Now())
	if len(ruleIds) == 0 {
		return nil, fmt.Errorf("no rule Id specified, cannot fetch subscriptions for all rules %v ", ruleIds)
	}
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	var subscriptionsProto []*pb.RuleSubscription
	var subscriptions []*model.RuleSubscription

	res := db.Model(&model.RuleSubscription{}).
		Where("rule_id in (?) AND state = ? AND version_state = ?", ruleIds, pb.RuleSubscriptionState_ACTIVE.String(),
			pb.SubscriptionVersionState_CURRENT.String()).Find(&subscriptions)
	if res.Error != nil {
		logger.Error(ctx, "Failed to get active subscriptions", zap.Error(res.Error), zap.Strings(logger.RULE_ID, ruleIds))
		return nil, res.Error
	}
	for _, subscription := range subscriptions {
		subscriptionsProto = append(subscriptionsProto, subscriptionModelToProto(subscription))
	}
	return subscriptionsProto, nil
}

// returns map of Subscription Count for each rule:
// Key of the map will be rule_id
// Value will contain
// 1. total number of ACTIVE subscriptions by a given actor for a rule
// 2. total number of ACTIVE subscriptions across all actors. Multiple subscriptions by the same actor are also counted.
func (a *RuleSubscriptionsPgDb) GetActiveSubscriptionCountForRules(ctx context.Context, ruleIds []string,
	actorId string) (map[string]*pb.SubscriptionCount, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetActiveSubscriptionCountForRules", time.Now())
	subsCountMap, err := a.getActiveSubsCountAcrossActors(ctx, ruleIds)
	if err != nil {
		logger.Error(ctx, "Failed to get subscription count across actors", zap.Error(err), zap.Any(logger.RULE_IDS, ruleIds))
		return nil, err
	}
	err = a.populateActiveSubsCountPerActor(ctx, subsCountMap, ruleIds, actorId)
	if err != nil {
		logger.Error(ctx, "Failed to get subscription count for specific actor", zap.Error(err), zap.Any(logger.RULE_IDS, ruleIds), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	return subsCountMap, nil
}

func (a *RuleSubscriptionsPgDb) getActiveSubsCountAcrossActors(ctx context.Context,
	ruleIds []string) (map[string]*pb.SubscriptionCount, error) {
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	// count with group by ruleId across actors
	rows, err := db.Model(model.RuleSubscription{}).Where("rule_id in (?) AND state = ? AND version_state = ?", ruleIds,
		pb.RuleSubscriptionState_ACTIVE.String(), pb.SubscriptionVersionState_CURRENT).Select("rule_id, count(id) as subs_count").Group("rule_id").Rows()
	if err != nil {
		return nil, err
	}
	if rows.Err() != nil {
		logger.Error(ctx, "Failed to get subscription count across actors for rules", zap.Error(rows.Err()), zap.Any(logger.RULE_IDS, ruleIds))
		return nil, rows.Err()
	}
	defer func() {
		err := rows.Close()
		if err != nil {
			logger.Error(ctx, "Error in closing rows", zap.Error(err))
		}
	}()
	subsCountMap := make(map[string]*pb.SubscriptionCount)
	for rows.Next() {
		var ruleId string
		var count uint32
		err := rows.Scan(&ruleId, &count)
		if err != nil {
			logger.Error(ctx, "Failed to get subscription count across actors for rules", zap.Error(err), zap.Any(logger.RULE_IDS, ruleIds))
			return nil, err
		}
		subsCountMap[ruleId] = &pb.SubscriptionCount{
			CountAcrossActors: count,
		}
	}
	return subsCountMap, nil
}

func (a *RuleSubscriptionsPgDb) populateActiveSubsCountPerActor(ctx context.Context,
	subsCountMap map[string]*pb.SubscriptionCount, ruleIds []string, actorId string) error {
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	// count with group by ruleId for given actor
	rows, err := db.Model(model.RuleSubscription{}).Where("actor_id = ? and rule_id in (?) AND state = ? AND version_state = ?", actorId, ruleIds,
		pb.RuleSubscriptionState_ACTIVE.String(), pb.SubscriptionVersionState_CURRENT).Select("rule_id, count(id) as subs_count").Group("rule_id").Rows()
	if err != nil {
		return err
	}
	if rows.Err() != nil {
		logger.Error(ctx, "Failed to get subscription count per actor for rules", zap.Error(rows.Err()), zap.Any(logger.RULE_IDS, ruleIds), zap.String(logger.ACTOR_ID_V2, actorId))
		return rows.Err()
	}
	defer func() {
		err := rows.Close()
		if err != nil {
			logger.Error(ctx, "Error in closing rows", zap.Error(err))
		}
	}()
	for rows.Next() {
		var ruleId string
		var count uint32
		err := rows.Scan(&ruleId, &count)
		if err != nil {
			logger.Error(ctx, "Failed to get subscription count per actor for rules", zap.Error(err), zap.Any(logger.RULE_IDS, ruleIds), zap.String(logger.ACTOR_ID_V2, actorId))
			return err
		}
		subsCount, ok := subsCountMap[ruleId]
		if !ok {
			subsCount = &pb.SubscriptionCount{}
			subsCountMap[ruleId] = subsCount
		}
		subsCount.CountPerActor = count
	}

	return nil
}

// DISCLAIMER: Invoking this function may performs a bulk update operation (which might lead to increase in dead tuples and slowness)
// USE THIS FUNCTION CAREFULLY, only at the places where it is actually required
func (a *RuleSubscriptionsPgDb) UpdateAllSubscriptionsForRules(ctx context.Context, ruleIds []string,
	subscription *pb.RuleSubscription,
	updatedFields []pb.RuleSubscriptionFieldMask, stateFilter []pb.RuleSubscriptionState,
	stateChangeReasonFilter []pb.SubscriptionStateChangeReason) error {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "UpdateAllSubscriptionsForRules", time.Now())

	ruleSubscription, err := subscriptionProtoToModel(ctx, subscription)
	if err != nil {
		logger.Error(ctx, "Unable to create actor rule subscription for update subscriptions", zap.Strings(logger.RULE_IDS, ruleIds), zap.Error(err))
		return err
	}

	if len(updatedFields) != 1 || updatedFields[0] != pb.RuleSubscriptionFieldMask_STATE {
		return fmt.Errorf("only updation for subscription state is supported by UpdateAllSubscriptionsForRule ")
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	err = dao.RunTxnWithRetriesOnDuplicateRowError(ctx, db, func(tx *gormv2.DB) error {
		var versionIds, subIds []string
		versionIds, subIds, err = a.expireCurrentSubscriptionsForRule(ctx, ruleIds, ruleSubscription.State, stateFilter, stateChangeReasonFilter, tx)
		if err != nil {
			logger.Error(ctx, "Failed to expire subscriptions for rule", zap.Strings(logger.RULE_ID, ruleIds), zap.Error(err))
			return err
		}
		if len(versionIds) > 0 {
			if err = tx.Exec("insert into rule_subscriptions (id, rule_id, state, state_change_reason, state_change_provenance, "+
				"actor_id, valid_from, valid_till, rule_param_values, created_at, updated_at, subscription_expiry_data, cutoff_param_updated_at, "+
				"version_valid_from, version_state, execution_state) select id, rule_id, ?, ?, ?, actor_id, valid_from, valid_till, rule_param_values, "+
				"now(), now(), subscription_expiry_data, cutoff_param_updated_at, now(), ?, execution_state FROM rule_subscriptions WHERE version_id in (?)",
				ruleSubscription.State, ruleSubscription.StateChangeReason, ruleSubscription.StateChangeProvenance,
				pb.SubscriptionVersionState_CURRENT.String(), versionIds).Error; err != nil {
				logger.Error(ctx, "Failed to add new subscription version for rule", zap.Strings(logger.RULE_IDS, ruleIds), zap.Error(err))
				return err
			}
		}

		if len(subIds) > 0 {
			if err = tx.Model(&model.SubscriptionRuntimeInfo{}).Where("id in (?)", subIds).
				Updates(&model.SubscriptionRuntimeInfo{State: ruleSubscription.State}).Error; err != nil {
				logger.Error(ctx, "Error in updating subscription runtime info", zap.Error(err))
				return err
			}
		}
		return nil
	}, MaxRetriesForDuplicateKeyQueries)
	if err != nil {
		logger.Error(ctx, "failed to update subscriptions for rule", zap.Error(err), zap.Strings(logger.RULE_ID, ruleIds))
		return err
	}

	logger.Info(ctx, "Updated all subscriptions for rule", zap.Strings(logger.RULE_IDS, ruleIds))
	return nil
}

func constructUpdatedVersion(ctx context.Context, version *model.RuleSubscription, subscription *model.RuleSubscription,
	updatedFields []pb.RuleSubscriptionFieldMask) (*model.RuleSubscription, *model.SubscriptionRuntimeInfo) {
	updatedSubRuntime := &model.SubscriptionRuntimeInfo{}

	for _, field := range updatedFields {
		switch field {
		case pb.RuleSubscriptionFieldMask_VALID_TILL:
			version.ValidTill = subscription.ValidTill
		case pb.RuleSubscriptionFieldMask_RULE_PARAM_VALUES:
			version.RuleParamValues = subscription.RuleParamValues
			updatedSubRuntime.RuleParamValues = subscription.RuleParamValues
		case pb.RuleSubscriptionFieldMask_SUBSCRIPTION_EXPIRY_DATA:
			version.SubscriptionExpiryData = subscription.SubscriptionExpiryData
		case pb.RuleSubscriptionFieldMask_STATE:
			version.State = subscription.State
			updatedSubRuntime.State = subscription.State
		case pb.RuleSubscriptionFieldMask_RULE_SUBSCRIPTION_FIELD_MASK_UNSPECIFIED:
			logger.Error(ctx, "Rule subscription field mask unspecified")
		}
	}
	return version, updatedSubRuntime
}

func (a *RuleSubscriptionsPgDb) getCurrentSubscriptionVersion(ctx context.Context,
	subscriptionId string) (*model.RuleSubscription, error) {
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	subscription := &model.RuleSubscription{}
	if err := db.Model(subscription).Where(&model.RuleSubscription{
		ID:           subscriptionId,
		VersionState: pb.SubscriptionVersionState_CURRENT,
	}).Take(subscription).Error; err != nil {
		logger.Error(ctx, "Error while getting current rule subscription for actor ", zap.String("Actor", subscription.ActorId), zap.Error(err))
		return nil, err
	}
	return subscription, nil
}

func (a *RuleSubscriptionsPgDb) expireCurrentSubscriptions(ctx context.Context, subscriptionIds []string,
	expiryTime time.Time, db *gormv2.DB) ([]string, error) {
	currentSubscription := &model.RuleSubscription{}
	currentSubscription.VersionValidTill = &expiryTime
	currentSubscription.VersionState = pb.SubscriptionVersionState_EXPIRED

	var versionIds []string
	if err := db.Model(&model.RuleSubscription{}).
		Where("id in (?) AND version_state = ? ", subscriptionIds, pb.SubscriptionVersionState_CURRENT.String()).
		Select("version_id").Find(&versionIds).
		Select([]string{model.VersionValidTillColumnName(),
			model.VersionStateColumnName()}).Updates(currentSubscription).Error; err != nil {
		logger.Error(ctx, "Failed to expire existing subscription version", zap.Error(err), zap.Any("subscriptionIds", subscriptionIds))
		return nil, err
	}
	return versionIds, nil
}

/*
For a given rule id, marks the version of the subscriptions (across actors) as EXPIRED.
Note: if a subscription version is marked CLOSED, subscription is not affected.
as, for every subscription there will be one CURRENT version, a new row will be inserted everytime after expiration
*/
func (a *RuleSubscriptionsPgDb) expireCurrentSubscriptionsForRule(ctx context.Context, ruleIds []string,
	targetState pb.RuleSubscriptionState, stateFilter []pb.RuleSubscriptionState, stateChangeReasonFilter []pb.SubscriptionStateChangeReason,
	txn *gormv2.DB) ([]string, []string, error) {
	currTime := storage.PgNow()
	currentSubscription := &model.RuleSubscription{}
	currentSubscription.VersionValidTill = &currTime
	currentSubscription.VersionState = pb.SubscriptionVersionState_EXPIRED

	// subscriptions which are already in terminal state or target state should not be  expired
	notApplicableStates := []pb.RuleSubscriptionState{pb.RuleSubscriptionState_CLOSED}
	if targetState != pb.RuleSubscriptionState_CLOSED {
		notApplicableStates = append(notApplicableStates, targetState)
	}

	type SubIdAndVerId struct {
		VersionId string
		Id        string
	}

	var rows []*SubIdAndVerId
	txn = txn.Model(&model.RuleSubscription{}).Select("id, version_id").
		Where("rule_id in (?) AND state not in (?) AND version_state = ? ", ruleIds, notApplicableStates, pb.SubscriptionVersionState_CURRENT.String())

	if len(stateFilter) > 0 {
		txn = txn.Where("state in (?)", stateFilter)
	}

	if len(stateChangeReasonFilter) > 0 {
		txn = txn.Where("state_change_reason in (?)", stateChangeReasonFilter)
	}

	if err := txn.Scan(&rows).
		Select([]string{model.VersionValidTillColumnName(), model.VersionStateColumnName()}).
		Updates(currentSubscription).Error; err != nil {
		logger.Error(ctx, "Failed to update subscription versions", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
		return nil, nil, err
	}

	var versionIds, subIds []string
	subIdsMap := make(map[string]bool)
	for _, r := range rows {
		versionIds = append(versionIds, r.VersionId)
		if !subIdsMap[r.Id] {
			subIdsMap[r.Id] = true
			subIds = append(subIds, r.Id)
		}
	}

	return versionIds, subIds, nil
}

func subscriptionProtoToModel(ctx context.Context, subscription *pb.RuleSubscription) (*model.RuleSubscription, error) {
	ruleSubscription := &model.RuleSubscription{
		ID:                     subscription.GetId(),
		RuleId:                 subscription.GetRuleId(),
		ActorId:                subscription.GetActorId(),
		State:                  subscription.GetState(),
		RuleParamValues:        subscription.GetRuleParamValues(),
		SubscriptionExpiryData: subscription.GetSubscriptionExpiryData(),
		StateChangeReason:      subscription.GetStateChangeReason(),
		StateChangeProvenance:  subscription.GetStateChangeProvenance(),
		ExecutionState:         subscription.GetExecutionState(),
		ClientRequestId:        subscription.GetClientRequestId(),
	}

	if subscription.GetValidFrom() != nil {
		validFrom, err := ptypes.Timestamp(subscription.GetValidFrom())
		if err != nil {
			logger.Error(ctx, "timestamp format not valid, unable to convert to proto timestamp", zap.Any("timestamp", subscription.ValidFrom), zap.Error(err))
			return nil, err
		}
		ruleSubscription.ValidFrom = &validFrom
	}

	if subscription.GetValidTill() != nil {
		validTill, err := ptypes.Timestamp(subscription.GetValidTill())
		if err != nil {
			logger.Error(ctx, "timestamp format not valid, unable to convert to proto timestamp", zap.Any("timestamp", subscription.ValidTill), zap.Error(err))
			return nil, err
		}
		ruleSubscription.ValidTill = &validTill
	}
	return ruleSubscription, nil
}

func subscriptionModelToProto(subscription *model.RuleSubscription) *pb.RuleSubscription {
	var validTill *timestamppb.Timestamp
	if subscription.ValidTill != nil {
		validTill = timestamppb.New(*subscription.ValidTill)
	}

	var cutoffParamUpdatedTime *timestamppb.Timestamp
	if subscription.CutoffParamUpdatedAt != nil {
		cutoffParamUpdatedTime = timestamppb.New(*subscription.CutoffParamUpdatedAt)
	}

	var validFromTime *timestamppb.Timestamp
	if subscription.ValidFrom != nil {
		validFromTime = timestamppb.New(*subscription.ValidFrom)
	}

	var versionValidFromTime *timestamppb.Timestamp
	if subscription.ValidFrom != nil {
		versionValidFromTime = timestamppb.New(*subscription.VersionValidFrom)
	}

	return &pb.RuleSubscription{
		ActorId:                subscription.ActorId,
		RuleParamValues:        subscription.RuleParamValues,
		RuleId:                 subscription.RuleId,
		State:                  subscription.State,
		Id:                     subscription.ID,
		ValidFrom:              validFromTime,
		ValidTill:              validTill,
		SubscriptionExpiryData: subscription.SubscriptionExpiryData,
		CutoffParamUpdatedAt:   cutoffParamUpdatedTime,
		VersionState:           subscription.VersionState,
		VersionId:              subscription.VersionId,
		VersionValidFrom:       versionValidFromTime,
		StateChangeReason:      subscription.StateChangeReason,
		StateChangeProvenance:  subscription.StateChangeProvenance,
		ExecutionState:         subscription.ExecutionState,
		ClientRequestId:        subscription.ClientRequestId,
	}
}

type SubscriptionActiveDuration struct {
	StartTime     *time.Time
	EndTime       *time.Time
	endTimeSetNil bool
}

func (a *RuleSubscriptionsPgDb) GetActiveSubscriptionsForTimeInterval(ctx context.Context, ruleIds []string, actorId string,
	startTime time.Time, endTime time.Time, paramFilters []*pb.ParamFilter) ([]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetActiveSubscriptionsForTimeInterval", time.Now())
	if len(ruleIds) == 0 {
		return nil, fmt.Errorf("no rule Id specified, cannot fetch subscriptions for all rules %v ", ruleIds)
	}
	var paramFiltersQuery string
	if len(paramFilters) > 0 {
		var err error
		paramFiltersQuery, err = getParamFiltersQuery(paramFilters)
		if err != nil {
			return nil, err
		}
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	var rows *sql.Rows
	var err error
	if actorId == "" {
		rawQuery := `select * from rule_subscriptions where rule_id in (?) and execution_state in ('SUBSCRIPTION_EXECUTION_ALLOWED', 'SUBSCRIPTION_EXECUTION_NOT_SPECIFIED') and
					((version_valid_till is null and version_valid_from < ? ) or ( version_valid_from, version_valid_till ) OVERLAPS (? :: timestamp, ? :: timestamp)) `

		if paramFiltersQuery != "" {
			rawQuery = fmt.Sprintf("%s AND (%s)", rawQuery, paramFiltersQuery)
		}
		logger.Debug(ctx, "getting subscription version without actor query", zap.String("query", rawQuery), zap.Strings(logger.RULE_IDS, ruleIds),
			zap.Time("startTime", startTime), zap.Time("endTime", endTime))
		rows, err = db.Raw(rawQuery, ruleIds, endTime, startTime, endTime).Rows()
	} else {
		rawQuery := "select * from rule_subscriptions where rule_id in (?) and actor_id = ? and execution_state in ('SUBSCRIPTION_EXECUTION_ALLOWED', 'SUBSCRIPTION_EXECUTION_NOT_SPECIFIED') and " +
			"( (version_valid_till is null and version_valid_from < ? ) or ( version_valid_from, version_valid_till ) OVERLAPS (? :: timestamp, ? :: timestamp) )"

		if paramFiltersQuery != "" {
			rawQuery = fmt.Sprintf("%s AND (%s)", rawQuery, paramFiltersQuery)
		}
		logger.Debug(ctx, "getting subscription version query", zap.String("query", rawQuery), zap.Strings(logger.RULE_IDS, ruleIds),
			zap.Time("startTime", startTime), zap.Time("endTime", endTime), zap.String(logger.ACTOR_ID_V2, actorId))

		rows, err = db.Raw(rawQuery, ruleIds, actorId, endTime, startTime, endTime).Rows()
	}

	if err != nil {
		logger.Error(ctx, "Failed to get active subscriptions", zap.Strings(logger.RULE_IDS, ruleIds), zap.Error(err))
		return nil, err
	}

	if rows.Err() != nil {
		logger.Error(ctx, "Failed to get active subscriptions, gorm row error", zap.Strings(logger.RULE_IDS, ruleIds), zap.Error(rows.Err()))
		return nil, rows.Err()
	}

	defer func() {
		err = rows.Close()
		if err != nil {
			logger.Error(ctx, "Failed to close subscription rows", zap.Any(logger.RULE_IDS, ruleIds), zap.Error(err))
		}
	}()

	subs, err := getActiveSubscriptionByInterval(ctx, rows, db, startTime, endTime)
	if err != nil {
		return nil, err
	}
	logger.Debug(ctx, "active subs for interval", zap.Any("activeSubs", subs))
	return subs, nil
}

// nolint: dupl
func getParamFiltersQuery(paramFilters []*pb.ParamFilter) (string, error) {
	var paramFiltersQuery string
	for i, p := range paramFilters {
		if i > 0 {
			paramFiltersQuery = fmt.Sprintf("%s OR ", paramFiltersQuery)
		}
		switch p.GetParamType() {
		case pb.RuleParamType_CRICKET_TEAM:
			paramFiltersQuery = fmt.Sprintf("%s rule_param_values->'ruleParamValues'->'configuredCricketTeam'->'teamVal'->>'id' = '%s' OR  rule_param_values->'ruleParamValues'->'configuredCricketPlayer'->'playerVal'->'team'->>'id' = '%s' ", paramFiltersQuery, p.GetKey(), p.GetKey())
		case pb.RuleParamType_CRICKET_PLAYER:
			paramFiltersQuery = fmt.Sprintf("%s rule_param_values->'ruleParamValues'->'configuredCricketPlayer'->'playerVal'->>'id' = '%s'", paramFiltersQuery, p.GetKey())
		default:
			return "", fmt.Errorf("filtering subscriptions through param is not supported for specified param type %s", p.GetParamType().String())
		}
	}
	return paramFiltersQuery, nil
}

func (a *RuleSubscriptionsPgDb) GetClientRulesExecutionInitiatedVersionsForActor(ctx context.Context,
	actorId string) (map[string]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetClientRulesExecutionInitiatedVersionsForActor", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	var versions []*model.RuleSubscription
	if err := db.Raw("select * from rule_subscriptions where version_id in (select distinct(subscription_version_id) from rule_executions where state in (?)) and actor_id = ?",
		[]string{execpb.RuleExecutionState_CONDITION_EVALUATION_AT_CLIENT_PENDING.String()}, actorId).Scan(&versions).Error; err != nil {
		logger.Error(ctx, "Failed to get subscription versions for pending client rule executions", zap.Error(err))
		return nil, err
	}
	versionsMap := make(map[string]*pb.RuleSubscription)
	for _, version := range versions {
		versionsMap[version.VersionId] = subscriptionModelToProto(version)
	}
	return versionsMap, nil
}

// adding nolint as the size of the function increased due to detailed explanation
// nolint:funlen
func getActiveSubscriptionByInterval(ctx context.Context, rows *sql.Rows, db *gormv2.DB, startTime time.Time,
	endTime time.Time) ([]*pb.RuleSubscription, error) {
	var subscriptionsProto []*pb.RuleSubscription

	subscriptionActiveDurationMap := make(map[string]*SubscriptionActiveDuration)
	inactiveSubscriptionMap := make(map[string]bool)
	latestVersionMap := make(map[string]*model.RuleSubscription)

	// iterating all selected subscription versions
	// since query selects all overlapping versions, there can be case where user activated after startTime
	// for checking this we need to determine start and end time of versions,
	// if subscription was active for complete duration (start_time, end_time)
	for rows.Next() {
		version := &model.RuleSubscription{}
		err := db.ScanRows(rows, version)
		if err != nil {
			logger.Error(ctx, "Failed to scan selected row to subscription", zap.Error(err))
			return nil, err
		}

		// if state of subscription for any version within interval is other than ACTIVE, do not process
		// in cases of multiple versions, there can be case when first few iterated versions were active and suppose 3rd version is INACTIVE
		// first 2 versions will pass this if check and make entries into latestVersionMap & subscriptionActiveDurationMap
		// these should be removed from map on encountering INACTIVE version
		if inactiveSubscriptionMap[version.ID] || version.State != pb.RuleSubscriptionState_ACTIVE {
			if !inactiveSubscriptionMap[version.ID] {
				inactiveSubscriptionMap[version.ID] = true
				delete(subscriptionActiveDurationMap, version.ID)
				delete(latestVersionMap, version.ID)
			}
			continue
		}

		// If already a version of subscription was encountered before
		if _, ok := subscriptionActiveDurationMap[version.ID]; ok {
			// if already encountered version's start time is AFTER current version's start time,
			// overriding the start time
			if subscriptionActiveDurationMap[version.ID].StartTime.After(*version.VersionValidFrom) {
				subscriptionActiveDurationMap[version.ID].StartTime = version.VersionValidFrom
			}

			// if already encountered version's end time is before current version's end time,
			// overriding the end time
			// if version's endTime is set to nil, it is considered as never ending
			// In order to determine subscription's valid till is set to nil, or its not set yet (this case also it will be nil)
			// 'endTimeSetNil' is used for same
			if !subscriptionActiveDurationMap[version.ID].endTimeSetNil &&
				(version.VersionValidTill == nil || subscriptionActiveDurationMap[version.ID].EndTime.Before(*version.VersionValidTill)) {
				subscriptionActiveDurationMap[version.ID].EndTime = version.VersionValidTill
				latestVersionMap[version.ID] = version
				if version.VersionValidTill == nil {
					subscriptionActiveDurationMap[version.ID].endTimeSetNil = true
				}
			}
		} else {
			// adding new entries to map
			subscriptionActiveDurationMap[version.ID] = &SubscriptionActiveDuration{
				StartTime: version.VersionValidFrom,
				EndTime:   version.VersionValidTill,
			}
			if version.VersionValidTill == nil {
				subscriptionActiveDurationMap[version.ID].endTimeSetNil = true
			}
			latestVersionMap[version.ID] = version
		}
	}

	// iterating over latestVersionMap
	// latestVersionMap contains latest subscription for mentioned duration
	for _, subscription := range latestVersionMap {
		// NOT processing subscriptions which were not active for complete duration (start_time - end_time)
		if subscriptionActiveDurationMap[subscription.ID].StartTime.After(startTime) ||
			(subscriptionActiveDurationMap[subscription.ID].EndTime != nil &&
				subscriptionActiveDurationMap[subscription.ID].EndTime.Before(endTime)) {
			logger.Debug(ctx, "not processing subscription as it was not active for complete duration", zap.String(logger.SUBSCRIPTION_ID, subscription.ID),
				zap.Time(logger.START_TIME, startTime), zap.Time(logger.END_TIME, endTime), zap.Any("subscriptionActiveDuration", subscriptionActiveDurationMap[subscription.ID]))
			continue
		}

		if subscription.CutoffParamUpdatedAt.After(startTime) && subscription.CutoffParamUpdatedAt.Before(endTime) {
			logger.Debug(ctx, "not processing subscription as subscription's cutoff param was updated within time interval", zap.String(logger.SUBSCRIPTION_ID, subscription.ID),
				zap.Time(logger.START_TIME, startTime), zap.Time(logger.END_TIME, endTime), zap.Time("cutoffParamUpdateTime", *subscription.CutoffParamUpdatedAt))
			continue
		}

		subscriptionsProto = append(subscriptionsProto, subscriptionModelToProto(subscription))
	}
	return subscriptionsProto, nil
}

// nolint: funlen
func (a *RuleSubscriptionsPgDb) GetActiveSubscriptionsAtParticularTime(ctx context.Context, ruleIds []string, actorId string,
	particularTime time.Time, paramFilters []*pb.ParamFilter, pageToken *pagination.PageToken, pageSize uint32) ([]*pb.RuleSubscription, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetActiveSubscriptionsAtParticularTime", time.Now())
	if len(ruleIds) == 0 {
		return nil, nil, fmt.Errorf("no rule Id specified, cannot fetch subscriptions for all rules %v ", ruleIds)
	}
	var paramFiltersQuery string
	if len(paramFilters) > 0 {
		var err error
		paramFiltersQuery, err = getParamFiltersQuery(paramFilters)
		if err != nil {
			logger.Error(ctx, "unable to create paramFiltersQuery", zap.Error(err))
			return nil, nil, err
		}
	}
	if pageSize > a.maxPageSizeToFetchSubscriptionsForExecution || pageSize == 0 {
		pageSize = a.maxPageSizeToFetchSubscriptionsForExecution
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	var allActiveSubscriptions SubscriptionsByCreatedTime
	// if start time is equal to end time, then we need not find the overlapping interval for time
	// since at a particular moment, only one subscription for a particular actor will be active for a rule
	// hence the query simplifies to fetching only that subscription which is active at a given moment
	var rawQuery = `select * from rule_subscriptions where rule_id in (?)`
	paginationQuery := dao.GetPaginationOnColumnRawQuery(dao.CreatedAtCol, pageToken, pageSize)
	if actorId != "" {
		rawQuery = fmt.Sprintf("%s and actor_id = ?", rawQuery)
	}

	// adding where clause to filter active subscriptions for the mentioned time range and execution state as SUBSCRIPTION_EXECUTION_ALLOWED or SUBSCRIPTION_EXECUTION_NOT_SPECIFIED
	rawQuery = fmt.Sprintf("%s and state='ACTIVE' and version_valid_from <= ? and (version_valid_till is null or version_valid_till > ?) and execution_state in ('SUBSCRIPTION_EXECUTION_ALLOWED', 'SUBSCRIPTION_EXECUTION_NOT_SPECIFIED') ", rawQuery)
	if paramFiltersQuery != "" {
		rawQuery = fmt.Sprintf("%s and (%s)", rawQuery, paramFiltersQuery)
	}
	if actorId != "" {
		rawQuery = fmt.Sprintf("%s %s", rawQuery, paginationQuery)
		logger.Debug(ctx, "GetActiveSubscriptionsAtParticularTime without actor query", zap.String("query", rawQuery), zap.Strings(logger.RULE_IDS, ruleIds),
			zap.Time("particularTime", particularTime), zap.Any("pageToken", pageToken), zap.Int("pageSize", int(pageSize)))
		db = db.Raw(rawQuery, ruleIds, actorId, particularTime, particularTime)
	} else {
		rawQuery = fmt.Sprintf("%s %s", rawQuery, paginationQuery)
		logger.Debug(ctx, "GetActiveSubscriptionsAtParticularTime query", zap.String("query", rawQuery),
			zap.Strings(logger.RULE_IDS, ruleIds), zap.Time("particularTime", particularTime), zap.Any("pageToken", pageToken),
			zap.Int("pageSize", int(pageSize)), zap.String(logger.ACTOR_ID_V2, actorId))
		db = db.Raw(rawQuery, ruleIds, particularTime, particularTime)
	}
	if err := db.Scan(&allActiveSubscriptions).Error; err != nil {
		logger.Error(ctx, "failed to get active subscriptions", zap.Error(err))
		return nil, nil, err
	}

	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), SubscriptionsByCreatedTime(allActiveSubscriptions))
	if err != nil {
		logger.Error(ctx, "Failed to create new page context", zap.Error(err))
		return nil, nil, err
	}

	allActiveSubscriptions = rows.(SubscriptionsByCreatedTime)
	var subscriptionsProto = make([]*pb.RuleSubscription, len(allActiveSubscriptions))
	for i, subscription := range allActiveSubscriptions {
		subscriptionsProto[i] = subscriptionModelToProto(subscription)
	}
	logger.Debug(ctx, "getActiveSubscriptionsAtParticularTime", zap.Any("subscriptionsProto", subscriptionsProto))
	return subscriptionsProto, pageCtxResp, nil
}

func (a *RuleSubscriptionsPgDb) UpdateExecutionStateBulk(ctx context.Context, subscriptionIds []string, targetExecutionState pb.SubscriptionExecutionState,
	changeReason pb.SubscriptionStateChangeReason, provenance pb.SubscriptionStateChangeProvenance) error {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "UpdateExecutionStateBulk", time.Now())
	requestTime := storage.PgNow()
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	err := dao.RunTxnWithRetriesOnDuplicateRowError(ctx, db, func(tx *gormv2.DB) error {
		versionIds, err := a.expireCurrentSubscriptions(ctx, subscriptionIds, requestTime, tx)
		if err != nil {
			logger.Error(ctx, "Failed to expire subscriptions for rule", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds),
				zap.String(logger.TARGET_STATE, targetExecutionState.String()), zap.Error(err))
			return err
		}

		if err = tx.Exec("insert into rule_subscriptions (id, rule_id, state, state_change_reason, "+
			"state_change_provenance, actor_id, valid_from, valid_till, rule_param_values, created_at, updated_at, "+
			"subscription_expiry_data, cutoff_param_updated_at, version_valid_from, version_state, execution_state) select id, rule_id, state, ?, ?, "+
			"actor_id, valid_from, valid_till, rule_param_values, now(), now(), subscription_expiry_data, cutoff_param_updated_at, now(), ?, ? "+
			"FROM rule_subscriptions WHERE version_id in (?)", changeReason, provenance,
			pb.SubscriptionVersionState_CURRENT.String(), targetExecutionState, versionIds).Error; err != nil {
			logger.Error(ctx, "failed to add new subscription versions while updating execution state", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds), zap.Error(err))
			return err
		}

		return nil
	}, MaxRetriesForDuplicateKeyQueries)
	if err != nil {
		logger.Error(ctx, "error in bulk updating subscriptions execution state", zap.Error(err), zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds))
		return err
	}
	logger.Info(ctx, "updated all subscriptions", zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds))
	return nil
}

func (a *RuleSubscriptionsPgDb) GetSubscriptionsForExecutionStates(ctx context.Context, actorId string,
	executionStates []pb.SubscriptionExecutionState, fieldMask *fieldmaskpb.FieldMask) ([]*pb.RuleSubscription, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetSubscriptionsForExecutionStates", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	if len(executionStates) == 0 || actorId == "" {
		logger.Error(ctx, "execution states or actor id cannot be nil", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, fmt.Errorf("execution states cannot be nil")
	}

	var subscriptions []*model.RuleSubscription
	db = db.Model(&model.RuleSubscription{})

	// actor id is mandatory until pagination is implemented
	// TODO: make actor id optional when pagination is implemented
	// current version should be checked for requested execution state
	db = db.Where("actor_id = ? and version_state = ?", actorId, pb.SubscriptionVersionState_CURRENT)

	db = db.Where("execution_state in (?)", executionStates)
	if fieldMask != nil {
		db = db.Select(fieldMask.Paths)
	}
	if err := db.Scan(&subscriptions).Error; err != nil {
		logger.Error(ctx, "failed to fetch subscriptions", zap.Error(err))
		return nil, err
	}

	var subscriptionsProto []*pb.RuleSubscription
	for _, subscription := range subscriptions {
		subscriptionsProto = append(subscriptionsProto, subscriptionModelToProto(subscription))
	}

	return subscriptionsProto, nil
}

func (a *RuleSubscriptionsPgDb) GetCountForFilters(ctx context.Context, eventTime *timestamppb.Timestamp,
	eventType event.EventType, actorId string, executionStates []pb.SubscriptionExecutionState, state pb.RuleSubscriptionState) (int64, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetCountForFilters", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if actorId == "" {
		return 0, fmt.Errorf("actorId is mandatory for checking existing subs")
	}

	db = db.Model(&model.RuleSubscription{}).Where("actor_id = ? ", actorId)

	if state != pb.RuleSubscriptionState_RULE_SUBSCRIPTION_STATE_UNSPECIFIED {
		db = db.Where("state = ?", state)
	}

	if len(executionStates) > 0 {
		db = db.Where("execution_state in (?)", executionStates)
	}

	// filtering active subModels for actor valid during the event time
	if eventTime != nil {
		db = db.Where("version_valid_from <= ? and (version_valid_till is null or version_valid_till > ?)", eventTime.AsTime(), eventTime.AsTime())
	} else {
		db = db.Where("version_state = ?", pb.SubscriptionVersionState_CURRENT)
	}

	if eventType != event.EventType_EVENT_TAG_UNSPECIFIED {
		// filtering only relevant rules according to requested event type
		db = db.Where("rule_id in (select id from rules where event_type = ?)", eventType)
	}

	var totalSubs int64
	if err := db.Count(&totalSubs).Error; err != nil {
		return 0, errors.Wrap(err, "error in getting active subModels count for event")
	}

	return totalSubs, nil
}

func (a *RuleSubscriptionsPgDb) GetUniqueActorsForSubscriptions(ctx context.Context, ruleSubscriptionStates []pb.RuleSubscriptionState,
	ruleTypeForSpecialHandling []pb.RuleTypeForSpecialHandling, ruleIds []string) ([]string, error) {
	defer metric_util.TrackDuration("rms/dao/rule_subscriptions", "RuleSubscriptionsPgDb", "GetUniqueActorsForSubscriptions", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if len(ruleTypeForSpecialHandling) == 0 && len(ruleIds) == 0 {
		return nil, fmt.Errorf("rule type and rule Ids both cannot be nil for getting unique actors")
	}

	subQuery := db.Model(&model.Rule{}).Select("id")
	if len(ruleIds) > 0 {
		subQuery = subQuery.Where("id in (?)", ruleIds)
	}
	if len(ruleTypeForSpecialHandling) > 0 {
		subQuery = subQuery.Where("rule_type_for_special_handling IN (?) and state IN (?)", ruleTypeForSpecialHandling, []pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE})
	}
	db = db.Model(&model.SubscriptionRuntimeInfo{})
	db = db.Distinct("actor_id").Where("state in (?)", ruleSubscriptionStates).Where("rule_id IN (?)", subQuery)
	actorIds := make([]string, 0)
	if err := db.Pluck("actor_id", &actorIds).Error; err != nil {
		logger.Error(ctx, "Failed to fetch actorIds", zap.Error(err))
		return nil, err
	}
	return actorIds, nil
}
