// nolint:dupl
package datacollector

import (
	"context"

	"github.com/samber/lo"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/logger"

	eventPb "github.com/epifi/gamma/api/event"
	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
	journeyPb "github.com/epifi/gamma/api/nudge/journey"
	journeyDataCollectorPb "github.com/epifi/gamma/api/nudge/journey/datacollector"
	"github.com/epifi/gamma/nudge/datacollector/model"
)

func (c *ConsumerService) ProcessRudderEvent(ctx context.Context, rudderEvent *eventPb.RudderEvent) (*nudgeDataCollectorPb.ConsumerResponse, error) {
	logger.Debug(ctx, "ProcessRudderEvent called", zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()), zap.String(logger.STATUS, rudderEvent.GetEvent()))
	rudderCollectedData := &model.RudderCollectedData{
		RudderEvent: rudderEvent,
	}
	protoCollectedData, err := rudderCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting rudder collected data model to proto",
			zap.Error(err), zap.Any("rudderEvent", rudderEvent),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	// Only for whitelisted events, publish the rudder event to journey's entry event queue
	// This is to ensure that the journey's entry event queue is not piled up with unnecessary events.
	if lo.Contains(c.dynConf.AllowedRudderEventsForJourneys().ToStringArray(), rudderEvent.GetEvent()) {
		msgId, err := c.journeyEntryEventSqsPublisher.Publish(ctx, &journeyDataCollectorPb.CollectedData{
			Id:            rudderEvent.GetMessageId(),
			ActorId:       rudderEvent.GetUserId(),
			ActionTime:    protoCollectedData.GetActionTime(),
			EventDataType: journeyPb.JourneyEntryEvent_RUDDER_EVENT,
			EventData:     &journeyDataCollectorPb.CollectedData_RudderEvent{RudderEvent: rudderEvent},
		})
		if err != nil {
			logger.Error(ctx, "error while publishing rudder collected data to journey entry event queue",
				zap.String(logger.EVENT_ID, rudderCollectedData.GetId()),
				zap.String(logger.EVENT_NAME, rudderEvent.GetEvent()),
				zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()),
				zap.Error(err),
			)
			return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
		logger.Debug(ctx, "Pushed rudder event to journey entry queue",
			zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()),
			zap.String(logger.STATUS, rudderEvent.GetEvent()),
			zap.String(logger.QUEUE_MESSAGE_ID, msgId))

	}
	// Only for whitelisted events, publish the rudder event to nudge's entry event queue
	// This is to ensure that the nudge's entry event queue is not piled up with unnecessary events.
	if lo.Contains(c.dynConf.AllowedRudderEventsForNudgeEntryEvents().ToStringArray(), rudderEvent.GetEvent()) {
		msgId, err := c.entryEvaluatorSqsPublisher.Publish(ctx, protoCollectedData)
		if err != nil {
			logger.Error(ctx, "error while publishing rudder collected data to nudge entry event queue",
				zap.String(logger.EVENT_ID, rudderCollectedData.GetId()),
				zap.String(logger.EVENT_NAME, rudderEvent.GetEvent()),
				zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()),
				zap.Error(err),
			)
			return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
		logger.Debug(ctx, "Pushed rudder event to entry evaluator queue",
			zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()),
			zap.String(logger.STATUS, rudderEvent.GetEvent()),
			zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	}

	queueMessageId, err := c.exitEvaluatorPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing rudder collected data",
			zap.String(logger.EVENT_ID, rudderCollectedData.GetId()),
			zap.String(logger.EVENT_NAME, rudderEvent.GetEvent()),
			zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()),
			zap.Error(err),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Info(ctx, "Pushed rudder event",
		zap.String(logger.EVENT_NAME, rudderEvent.GetEvent()), zap.String(logger.QUEUE_MESSAGE_ID, queueMessageId),
		zap.String(logger.ACTOR_ID_V2, rudderEvent.GetUserId()))

	return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS}}, nil
}
