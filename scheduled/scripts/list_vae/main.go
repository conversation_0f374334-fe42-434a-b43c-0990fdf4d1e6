package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"

	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/upi"
)

const (
	RemarksListPspKeys = "LIST PSP KEYS"
)

func main() {
	// Get environment
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	vgUpiClient := vgUpiPb.NewUPIClient(vgConn)

	txnHeader := &vgUpiPb.TransactionHeader{}
	err = upi.PopulateTransactionHeader(txnHeader, commonvgpb.Vendor_FEDERAL_BANK, RemarksListPspKeys)
	if err != nil {
		logger.Panic("error in populating txn header", zap.Error(err))
	}

	ctx := context.Background()
	traceId := uuid.New().String()
	logger.InfoNoCtx("traceId which is populated for list_vae job", zap.String("traceID", traceId))

	ctx = epificontext.WithTraceId(ctx, metadata.MD{"trace_id": []string{traceId}})

	// main job
	resp, err := vgUpiClient.ListVae(ctx, &vgUpiPb.ListVaeRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		TxnHeader: txnHeader,
		PageSize:  1000,
	})
	if err != nil || !resp.GetStatus().IsSuccess() {
		logger.Panic("error in list vae response in cron job", zap.Error(err))
	}

	logger.Info(ctx, "list_vae job complete")
}
