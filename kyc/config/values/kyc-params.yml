Flags:
  TrimDebugMessageFromStatus: false
  SkipCKYCDownload: false
  SendPassportBackAsImageToKarza: true

VKYCNotification:
  CallScheduled:
    IsEnabled: true
    Title: "Your video KYC call is scheduled successfully"
    Body: "Please keep your original PAN card ready prior to the call"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  ReScheduleCall:
    IsEnabled: true
    Title: "Please choose a different time for KYC call"
    Body: "Your assigned RM is unavailable at your scheduled slot! Please pick another slot for a video KYC call"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  DocumentsUnderReview:
    IsEnabled: true
    Title: "KYC documents under review"
    Body: "Your documents have been submitted to an auditor for verification. This may take up to 48 hours."
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  DocumentsApproved:
    IsEnabled: true
    Title: "KYC documents approved"
    Body: "You are now ready to use your full KYC bank account"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  StartLiveCall:
    IsEnabled: true
    Title: "Join your video KYC call now"
    Body: "Keep your original PAN card ready before you join the call"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  ScheduledCallReminder:
    IsEnabled: true
    Title: "Your Video KYC call begins in"
    Body: "Keep your original PAN card ready prior to the call"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  BookCall:
    IsEnabled: true
    Title: "Book your live video KYC call"
    Body: "Please complete KYC before %s, from the comfort of your home in 3 minutes."
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  ScheduledCallMissed:
    IsEnabled: true
    Title: "Please choose a different time for KYC call"
    Body: "You were unavailable at your scheduled slot! Please pick another slot for a Video KYC call."
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  CallFailed:
    IsEnabled: true
    Title: "The bank RM couldn’t verify your document"
    Body: "It's no cause for concern! Before you start your video call, make sure you’re in a well-lit and quiet place."
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  CallFailedByAuditor:
    IsEnabled: false
    Title: "We were unable to verify your KYC"
    Body: "As a result, your Federal savings account will continue to have a deposit limit of ₹50,000. You can head to a Federal bank to complete KYC"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"
  VKYCAvailable:
    IsEnabled: true
    Title: "Join your Video KYC call now"
    Body: "Keep your original PAN card ready before you join the call"
    IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    Type: "SYSTEM_TRAY"

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/kyc/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

Values:
  PurgeJobBatchSize: 200
  CKYCFMThreshold: 80
  MinDLIssueDays: 7
  BKYCDataValidity: "72h" # 3 days.
  EKYCDataValidtyAfterAccountCreation: "72h" # 3 days
  MinimumAgeForKYC: 21

EKYCDowntime:
  EKYCDowntimeFrom: 2022-02-20 18:00:00.000
  EKYCDowntimeTo: 2022-02-20 19:30:00.000
  EKYCDowntimeMessage: "UIDAI is having scheduled maintenance activity from 20th Feb 06:00PM to 20th Feb 07:30PM. Please try again after 07:30PM"

VKYC:
  LocationMatchThresholdInKM: 5000 # 5000 KM in non prod to unblock testing
  EnableReAttemptVKYCHomeBottomSheet: false
  EnableReAttemptVKYCNudge: true
  ReAttemptVKYCRolloutPercentage: 100
  ReAttemptVkycStartHour: 8
  ReAttemptVkycEndHour: 24
  MaxRetryForAgentRejected: 1
  UnsupportedWebviewDeviceModels: [ "Redmi 8A Dual", "redmi8adual", "SM-G985F", "SM-N975F", "SM-N770F", "V2045", ".*pixel.*", "SM-E225F" ]
  UnsupportedCustomerDeviceModels: [ "SM-A505F", "STK-L22", ".*iPhone SE.*"]
  AgentWaitingOverlay:
    Disable: true
  EnableVKYCFlowV2:
    MinAndroidVersion: 442
    MinIOSVersion: 0
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableVKYCFlowV2ForHome:
    MinAndroidVersion: 425
    MinIOSVersion: 588
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableVKYCBenefitScreenV2: true
  EnableCallScheduleForVKYCV2Flow: false
  VKYCFaqCategoryId: "82000263662"
  EnableVKYCScheduleFlow:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: true
  EnableConfirmDetailsState:
    MinAndroidVersion: 99999
    MinIOSVersion: 99999
    FallbackToEnableFeature: true
    DisableFeature: true
  EnableNRConfirmDetailsState:
    MinAndroidVersion: 0
    MinIOSVersion: 545
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableVKYCDemandManagement:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: true
  FederalSFTPEPANUploadPath: "/data/"
  FederalSFTPEPANUploadPrefix: "ePAN_vKYC_"
  EnableUploadEpanFile: true
  RetryEPANDuration: 24h
  ExpectedUserWaitingTimeInNRVKYC: 2m
  EnablePanCapturePreOnboarding: true
  EnableEpanInstructionScreen:
    MinAndroidVersion: 346
    MinIOSVersion: 502
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableEPANInNRFlow:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - VKYC_PAN_IMAGE_CAPTURE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - VKYC_CALL_QUALITY_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
  EnableCallQualityCheckStage:
    MinAndroidVersion: 343
    MinIOSVersion: 99999
    FallbackToEnableFeature: true
    DisableFeature: false
  VKYCCallQuality:
    CheckDurationInMillis: 10000
    SampleDurationInMillis: 1000
    NoiseLevelThresholdAndroid: 2000
    ImageLuminanceThresholdAndroid: 100
    NoiseLevelThresholdIOS: -15
    ImageLuminanceThresholdIOS: 110
  EnableApprovedNudgeDismissal:
    MinAndroidVersion: 351
    MinIOSVersion: 504
    FallbackToEnableFeature: true
    DisableFeature: false

QuestSdk:
  Disable: false

Tracing:
  Enable: false

ExtractedDocumentsExpiryConfig:
  NRKYCDataValidity: "168h" # 7 days

UqudoPassportTamperingConfig:
  EnableTamperCheck: false
