package stateprocessor

import (
	"context"
	"fmt"
	"strconv"

	"github.com/samber/lo"

	types "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	userPb "github.com/epifi/gamma/api/user"

	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/kyc"
	formPkg "github.com/epifi/gamma/api/typesv2/form"
	"github.com/epifi/gamma/kyc/vkyc/common"
	"github.com/epifi/gamma/kyc/vkyc/metrics"
	addressPkg "github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	pkgEmployment "github.com/epifi/gamma/pkg/employment"

	userPkg "github.com/epifi/gamma/pkg/user"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	empPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/deeplink"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/form"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	genConf "github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/gamma/kyc/vkyc/pkg"
)

type ConfirmDetailsState struct {
	vkycClient       vkycPb.VKYCClient
	empFeClient      empPb.EmploymentFeClient
	genConf          *genConf.Config
	onbClient        onbPb.OnboardingClient
	userAttrFetcher  userPkg.UserAttributesFetcher
	kycClient        kyc.KycClient
	employmentClient empPb.EmploymentClient
	userClient       userPb.UsersClient
}

var _ StateProcType = &ConfirmDetailsState{}

func NewConfirmDetailsState(genConf *genConf.Config, vkycClient vkycPb.VKYCClient, empFeClient empPb.EmploymentFeClient,
	onbClient onbPb.OnboardingClient, userAttrFetcher userPkg.UserAttributesFetcher, kycClient kyc.KycClient, employmentClient empPb.EmploymentClient, userClient userPb.UsersClient) *ConfirmDetailsState {
	return &ConfirmDetailsState{
		vkycClient:       vkycClient,
		genConf:          genConf,
		empFeClient:      empFeClient,
		onbClient:        onbClient,
		userAttrFetcher:  userAttrFetcher,
		kycClient:        kycClient,
		employmentClient: employmentClient,
		userClient:       userClient,
	}
}

type userDetails struct {
	vkycType              vkycPb.VKYCType
	isEmpEditable         bool
	empUpdateDL           *deeplink.Deeplink
	titleString           string
	flow                  form.UpdateUserDetailsFlow
	incomeLabel           string
	infoBoxText           string
	commsAddressFieldId   formPkg.FieldIdentifier
	employmentInfoFieldId formPkg.FieldIdentifier
}

//nolint:funlen
func (s *ConfirmDetailsState) StateProc(ctx context.Context, req *StateProcRequest) (*StateProcResponse, error) {
	if req.GetReq().GetClientLastState() == vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS {
		return nil, SkipStateError
	}
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.VKYC().EnableConfirmDetailsState()) {
		return nil, SkipStateError
	}
	metrics.RecordVKYCStateActivity(ctx, vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS, metrics.StateStarted, req.GetReq().GetEntryPoint())
	logger.Info(ctx, "confirm vkyc details flow is enabled")

	var (
		actorId = req.GetReq().GetActorId()
		details = &userDetails{}
	)

	if common.IsVKYCFlowV2Enabled(ctx, req.GetReq().GetEntryPoint(), s.genConf) {
		logger.Info(ctx, "user landed on confirm details step", zap.String(logger.FEEDBACK_ENGINE_APP_FLOW, req.GetReq().GetEntryPoint().String()))
		dl, err := s.getVkycStepDl(ctx, actorId, req.GetReq().GetEntryPoint())
		if err != nil {
			return nil, err
		}
		return &StateProcResponse{
			NextAction: dl,
		}, nil
	}

	details.flow = form.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_CONFIRM_VKYC_DETAILS
	details.infoBoxText = infoBoxTitle
	details.incomeLabel = "MONTHLY INCOME"

	if req.GetReq().GetEntryPoint() == vkycPb.EntryPoint_ENTRY_POINT_FEDERAL_LOANS {
		details.isEmpEditable = false
		details.titleString = "Confirm your details to proceed with your loan application"
		details.commsAddressFieldId = formPkg.FieldIdentifier_FIELD_IDENTIFIER_LOANS_COMMUNICATION_ADDRESS
		details.employmentInfoFieldId = formPkg.FieldIdentifier_FIELD_IDENTIFIER_LOANS_EMPLOYMENT_INFO

		return &StateProcResponse{
			NextAction: getConfirmVKYCDetailsDL(details, req.GetReq().GetEntryPoint()),
		}, nil
	}
	isNriUserRes, err := s.userAttrFetcher.IsNonResidentUser(ctx, &userPkg.IsNonResidentUserRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching whether user is non-resident in ConfirmDetailsState", zap.Error(err))
		return nil, err
	}
	details.commsAddressFieldId = formPkg.FieldIdentifier_FIELD_IDENTIFIER_COMMUNICATION_ADDRESS
	details.employmentInfoFieldId = formPkg.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_INFO

	if isNriUserRes.GetIsNonResidentUser() {
		// for IOS, there where one issue due to which added this backward compatibility check.
		// https://epifi.slack.com/archives/C0187R5ML1E/p1728108378641349
		if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.VKYC().EnableNRConfirmDetailsState()) {
			return nil, SkipStateError
		}
		details.flow = form.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_CONFIRM_NR_VKYC_DETAILS
		details.infoBoxText = nrInfoBoxTitle
		details.incomeLabel = "ANNUAL INCOME"
	}

	if err := s.appendEmploymentDetails(ctx, actorId, details); err != nil {
		return nil, err
	}
	if err := s.appendProduct(ctx, actorId, details); err != nil {
		return nil, err
	}

	logger.Info(ctx, "landed on confirm user detail screen", zap.String(logger.STATUS, strconv.FormatBool(isNriUserRes.GetIsNonResidentUser())))
	return &StateProcResponse{
		NextAction: getConfirmVKYCDetailsDL(details, req.GetReq().GetEntryPoint()),
	}, nil
}

func (s *ConfirmDetailsState) appendProduct(ctx context.Context, actorId string, details *userDetails) error {
	resp, errResp := s.onbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    actorId,
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching onboarding details", zap.Error(err))
		return err
	}
	var productType string
	switch {
	case resp.GetDetails().GetFeature().IsNonResidentUserOnboarding():
		productType = "NRE/NRO Savings Account"
	default:
		var ok bool
		productType, ok = pkg.ProductMapping[resp.GetDetails().GetFeature()]
		if !ok {
			// assume default product type as Savings Account
			productType = "SAVINGS ACCOUNT"
		}
	}
	details.titleString = fmt.Sprintf(vkycInstructionsScreenV2Title, productType)
	return nil
}

func (s *ConfirmDetailsState) appendEmploymentDetails(ctx context.Context, actorId string, details *userDetails) error {
	empDlResp, respErr := s.empFeClient.GetEmploymentDeclarationDL(ctx, &empPb.GetEmploymentDeclarationDLRequest{
		UpdateSource: empPb.UpdateSource_UPDATE_SOURCE_VKYC,
		ScreenView:   deeplink.EmploymentDeclarationOptions_BOTTOM_SHEET,
		ActorId:      actorId,
	})
	if rpcErr := epifigrpc.RPCError(empDlResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting employment deeplink from BE", zap.Error(rpcErr))
		return rpcErr
	}
	details.isEmpEditable = !empDlResp.GetIsNotEditable()
	details.empUpdateDL = empDlResp.GetDeeplink()
	return nil
}

func (s *ConfirmDetailsState) getVkycStepDl(ctx context.Context, actorId string, entryPoint vkycPb.EntryPoint) (*deeplink.Deeplink, error) {
	kycResp, err := s.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(kycResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to fetch kyc record from kyc service", zap.Error(rpcErr))
		return nil, rpcErr
	}
	kycAttemptResp, err := s.kycClient.GetKYCAttempt(ctx, &kyc.GetKYCAttemptRequest{
		Identifier: &kyc.GetKYCAttemptRequest_KycAttemptId{
			KycAttemptId: kycResp.GetKycAttemptId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(kycAttemptResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to fetch kyc attempt from kyc service", zap.Error(rpcErr))
		return nil, rpcErr
	}

	// Building Deeplink
	vkycStepScreenScreenOptionBuilder := NewVKYCStepsScreenOptionBuilder()
	vkycStepScreenScreenOptionBuilder.WithHeaderBar(GetEkycStepDropOffDeeplink(vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS.String(), entryPoint), GetVkycFQAScreenDl(s.genConf.VKYC().VKYCFaqCategoryId()))
	vkycStepScreenScreenOptionBuilder.WithTitle().WithSubtitle()
	if s.genConf.VKYC().EnableCallScheduleForVKYCV2Flow() {
		vkycStepScreenScreenOptionBuilder.WithFooterText()
	}
	vkycStepScreenScreenOptionBuilder.AppendStepCompletedCard(panStepCompleted)
	if kycAttemptResp.GetKycAttempt().GetRequestParams().GetEkycSource() == kyc.EkycSource_EKYC_SOURCE_VKYC {
		// Only showing Aadhaar step completed card if the EKYC is completed via VKYC
		vkycStepScreenScreenOptionBuilder.AppendStepCompletedCard(aadhaarStepCompleted)
	}

	vkycStepScreenScreenOptionBuilder, err = s.appendDetailConfirmationCard(ctx, entryPoint, actorId, vkycStepScreenScreenOptionBuilder, kycResp)
	if err != nil {
		logger.Error(ctx, "error in appending detail confirmation card", zap.Error(err))
		return nil, err
	}
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_VKYC_STEPS_INFO_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(vkycStepScreenScreenOptionBuilder.Build()),
	}, nil
}

func (s *ConfirmDetailsState) appendDetailConfirmationCard(ctx context.Context, entryPoint vkycPb.EntryPoint, actorId string, vkycStepScreenScreenOptionBuilder *VKYCStepsScreenOptionBuilder, kycResp *kyc.GetKYCRecordResponse) (*VKYCStepsScreenOptionBuilder, error) {
	switch entryPoint {
	case vkycPb.EntryPoint_ENTRY_POINT_FEDERAL_LOANS:
		userResp, respErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(userResp, respErr); rpcErr != nil {
			logger.Error(ctx, "error in getting user data", zap.Error(rpcErr))
			return nil, rpcErr
		}
		commsAddress, ok := userResp.GetUser().GetProfile().GetAddresses()[types.AddressType_LOAN_COMMUNICATION.String()]
		if !ok {
			logger.Error(ctx, "loans communication address not found")
			return nil, fmt.Errorf("communication address not found")
		}

		var employmentDetails *userPb.DataVerificationDetail_EmploymentDetail
		for _, verificationDetails := range userResp.GetUser().GetDataVerificationDetails().GetDataVerificationDetails() {
			if verificationDetails.GetVerificationMethod() == userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA {
				employmentDetails = verificationDetails.GetEmploymentDetail()
				// NOTE: We are not breaking here as we want the latest employment details from the list.
			}
		}
		vkycStepScreenScreenOptionBuilder.AppendDetailConfirmationCard(entryPoint, false, nil, map[string]string{
			"MONTHLY INCOME":        pkgMoney.ToDisplayString(employmentDetails.GetMonthlyIncome()),
			"OCCUPATION":            getOccupationType(employmentDetails.GetOccupationType()),
			"EMPLOYMENT TYPE":       getEmploymentTypeDisplayText(empPb.GetEmploymentType(employmentDetails.GetEmploymentType())),
			"COMMUNICATION ADDRESS": addressPkg.ConvertPostalAddressToString(commsAddress),
		}, []string{"MONTHLY INCOME", "OCCUPATION", "EMPLOYMENT TYPE", "COMMUNICATION ADDRESS"})
		return vkycStepScreenScreenOptionBuilder, nil

	default:
		empDlResp, err := s.empFeClient.GetEmploymentDeclarationDL(ctx, &empPb.GetEmploymentDeclarationDLRequest{
			UpdateSource: empPb.UpdateSource_UPDATE_SOURCE_VKYC,
			ScreenView:   deeplink.EmploymentDeclarationOptions_BOTTOM_SHEET,
			ActorId:      actorId,
		})
		if rpcErr := epifigrpc.RPCError(empDlResp, err); rpcErr != nil {
			logger.Error(ctx, "error in getting employment deeplink from BE", zap.Error(rpcErr))
			return nil, rpcErr
		}

		empInfoRes, err := s.employmentClient.GetEmploymentInfo(ctx, &empPb.GetEmploymentInfoRequest{
			ActorId: actorId,
		})
		if err = epifigrpc.RPCError(empInfoRes, err); err != nil {
			logger.Error(ctx, "error in getting employment info of user", zap.Error(err))
			return nil, fmt.Errorf("error in getting employment info of user")
		}

		// marking employment as non-editable https://monorail.pointz.in/p/fi-app/issues/detail?id=97306
		vkycStepScreenScreenOptionBuilder.AppendDetailConfirmationCard(entryPoint, !empDlResp.GetIsNotEditable(), empDlResp.GetDeeplink(), map[string]string{
			"MONTHLY INCOME":        getMonthlySalRangeFromAnnualSalary(empInfoRes.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary()),
			"OCCUPATION":            getOccupationType(empInfoRes.GetEmploymentData().GetOccupationType()),
			"EMPLOYMENT TYPE":       getEmploymentTypeDisplayText(empInfoRes.GetEmploymentData().GetEmploymentType()),
			"COMMUNICATION ADDRESS": getCommunicationAddress(kycResp.GetKycRecord()),
		}, []string{"MONTHLY INCOME", "OCCUPATION", "EMPLOYMENT TYPE", "COMMUNICATION ADDRESS"})
		return vkycStepScreenScreenOptionBuilder, nil
	}
}

func getEmploymentTypeDisplayText(employmentType empPb.EmploymentType) string {
	feEmploymentType, _ := pkgEmployment.BeEmploymentTypeToUiState[employmentType]
	displayString, ok := pkgEmployment.EmploymentTypeToDisplayText[feEmploymentType]
	if !ok {
		return "NA"
	}
	return displayString
}

func getOccupationType(occupationType empPb.OccupationType) string {
	if occupationType != empPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED &&
		!lo.Contains(pkgEmployment.OccupationDropDownExclusionList, occupationType.String()) {
		occ, ok := pkgEmployment.OccupationTypeToDisplayText[occupationType]
		if !ok {
			occ = "NA"
		}
		return occ
	}
	return "NA"
}

func getMonthlySalRangeFromAnnualSalary(annualSalary *screening.AnnualSalary) string {
	if annualSalary.GetRange() == nil || annualSalary.GetRange().GetMaxValue() == 0 {
		return fmt.Sprintf("%v", int(annualSalary.GetAbsolute()/12))
	}
	maxSal := annualSalary.GetRange().GetMaxValue()
	switch {
	case maxSal <= 100000:
		return "0 - 8K"
	case maxSal <= 500000:
		return "8K - 41K"
	case maxSal <= 1000000:
		return "41K - 83K"
	case maxSal <= 2500000:
		return "83K - 2.1Lacs"
	case maxSal <= 5000000:
		return "2.1Lacs - 4.1Lacs"
	case maxSal <= 10000000:
		return "4.1Lacs - 8.3Lacs"
	default:
		return "8.3Lacs- 20Lacs"
	}
}

func getCommunicationAddress(kycRecord *kyc.KYCRecord) string {
	address := kycRecord.GetPermanentAddress()
	if address == nil {
		address = kycRecord.GetCommunicationAddress()
	}
	return addressPkg.ConvertPostalAddressToString(address)
}
