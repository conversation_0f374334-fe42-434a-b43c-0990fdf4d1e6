package vkyc

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/frontend/deeplink"
	feedbackEnginePb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	"github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	vkycPb2 "github.com/epifi/gamma/api/kyc/vkyc/notification"
	"github.com/epifi/gamma/api/omegle/enums"
	matcherPb "github.com/epifi/gamma/api/omegle/matcher"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pan/epan"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	userLocation "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/onboarding"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/gamma/kyc/vkyc/ack/factory"
	"github.com/epifi/gamma/kyc/vkyc/ack/factory/ack_manager"
	"github.com/epifi/gamma/kyc/vkyc/common"
	"github.com/epifi/gamma/kyc/vkyc/dao"
	events2 "github.com/epifi/gamma/kyc/vkyc/events"
	"github.com/epifi/gamma/kyc/vkyc/notification"
	"github.com/epifi/gamma/kyc/vkyc/nudges"
	"github.com/epifi/gamma/kyc/vkyc/serviceprovider"
	"github.com/epifi/gamma/kyc/vkyc/serviceprovider/model"
	"github.com/epifi/gamma/kyc/vkyc/statemachine"
	"github.com/epifi/gamma/kyc/vkyc/statemachine/actions"
	"github.com/epifi/gamma/kyc/vkyc/stateprocessor"
	"github.com/epifi/gamma/kyc/vkyc/vkyc_priority"
	wireTypes "github.com/epifi/gamma/kyc/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	userPkg "github.com/epifi/gamma/pkg/user"
	"github.com/epifi/gamma/pkg/vkyc"
)

type Service struct {
	conf                        *genconf.Config
	svcProvider                 serviceprovider.ServiceProvider
	summaryDao                  dao.VKYCSummaryDao
	vkycAttemptDao              dao.VKYCAttemptDao
	vkycKarzaCallInfoDao        dao.VKYCKarzaCallInfoDao
	vkycKarzaCallHistoryDao     dao.VKYCKarzaCallHistoryDao
	vkycCustomerInfoDao         dao.VKYCKarzaCustomerInfoDao
	vkycCallScheduleDao         dao.VKYCCallScheduleDao
	vkycNotificationDao         dao.VKYCNotificationDao
	vendor                      commonvgpb.Vendor
	stateProcessor              *statemachine.StateProcessor
	eventBroker                 events.Broker
	notification                *notification.VKYCNotificationService
	doOnce                      once.DoOnce
	kycClient                   kyc.KycClient
	vkycPriority                vkyc_priority.IVKYCPriority
	locationClient              location.LocationClient
	userClient                  usersPb.UsersClient
	actorClient                 actor.ActorClient
	bcClient                    bankcust.BankCustomerServiceClient
	userLocationClient          userLocation.LocationClient
	empFeClient                 employmentPb.EmploymentFeClient
	savingsClient               savingsPb.SavingsClient
	inAppTargetedCommsClient    tcPb.InAppTargetedCommsClient
	initiatedStateProc          stateprocessor.StateProcType
	instructionsStateProc       stateprocessor.StateProcType
	ekycProc                    stateprocessor.StateProcType
	epanSelectionProc           stateprocessor.StateProcType
	epanEntryProc               stateprocessor.StateProcType
	panEvaluationProc           stateprocessor.StateProcType
	callStartProc               stateprocessor.StateProcType
	checkAgentAvailabilityProc  stateprocessor.StateProcType
	callInitiation              stateprocessor.StateProcType
	updateEmpState              stateprocessor.StateProcType
	vendorEkycNameDobValidation stateprocessor.StateProcType
	panEntry                    stateprocessor.StateProcType
	confirmVKYCDetailsProc      stateprocessor.StateProcType
	callQualityCheck            stateprocessor.StateProcType
	nrInstructionsState         stateprocessor.StateProcType
	nrEPANEntryState            stateprocessor.StateProcType
	nrEPANSelectionState        stateprocessor.StateProcType
	nrUpdateCustomerDetails     stateprocessor.StateProcType
	sgInstructionState          stateprocessor.StateProcType
	sgValidateAndStartCallState stateprocessor.StateProcType
	homePopupProc               nudges.NudgeProcType
	profileTopBannerProc        nudges.NudgeProcType
	homeTopBannerProc           nudges.NudgeProcType
	homeProfileExtensionProc    nudges.NudgeProcType
	registerUserProc            stateprocessor.StateProcType
	employmentClient            employmentPb.EmploymentClient
	updateCallInfoProc          stateprocessor.StateProcType
	registerNRUserState         stateprocessor.StateProcType
	nrValidateAndStartCallState stateprocessor.StateProcType
	profileMiddleWidgetProc     nudges.NudgeProcType
	panClient                   panPb.PanClient
	vgPanClient                 pan.PANClient
	vkycMockable                VkycMockable
	vkycCacheStorage            wireTypes.VKYCRueidisCacheStorage
	userIntelClient             userIntelPb.UserIntelServiceClient
	onbClient                   onboarding.OnboardingClient
	ackFactory                  factory.AckFactory
	userAttrFetcher             userPkg.UserAttributesFetcher
	userGroupClient             userGroupPb.GroupClient
	abReleaseEval               *release.ABEvaluator[string]
	matcherClient               matcherPb.MatcherClient
}

func NewService(conf *genconf.Config, svcProvider serviceprovider.ServiceProvider, summaryDao dao.VKYCSummaryDao, vkycAttemptDao dao.VKYCAttemptDao,
	vendor commonvgpb.Vendor, stateProcessor *statemachine.StateProcessor, EventBroker events.Broker,
	vkycKarzaCallInfoDao dao.VKYCKarzaCallInfoDao, vkycKarzaCallHistoryDao dao.VKYCKarzaCallHistoryDao,
	vkycCustomerInfoDao dao.VKYCKarzaCustomerInfoDao, vkycCallScheduleDao dao.VKYCCallScheduleDao,
	notification *notification.VKYCNotificationService, vkycNotificationDao dao.VKYCNotificationDao,
	doOnce once.DoOnce, kycClient kyc.KycClient, vkycPriority vkyc_priority.IVKYCPriority, locationClient location.LocationClient,
	userClient usersPb.UsersClient, actorClient actor.ActorClient, bcClient bankcust.BankCustomerServiceClient,
	empFeClient employmentPb.EmploymentFeClient, savingsClient savingsPb.SavingsClient,
	userLocationClient userLocation.LocationClient, inAppTargetedCommsClient tcPb.InAppTargetedCommsClient,
	initiatedStateProc *stateprocessor.InitiatedState, instructionsStateProc *stateprocessor.InstructionsState,
	ekycProc *stateprocessor.EKYCState, callStartProc *stateprocessor.ValidateAndStartCallState,
	profileMiddleWidgetProc *nudges.ProfileMiddleWidget, homePopupProc *nudges.HomePopup, profileTopBannerProc *nudges.ProfileTopBanner, homeTopBannerProc *nudges.HomeTopBanner, homeProfileExtensionProc *nudges.HomeProfileExtension,
	registerUserProc *stateprocessor.RegisterUserState, updateCallInfoProc *stateprocessor.UpdateCallInfoState, panClient panPb.PanClient,
	employmentClient employmentPb.EmploymentClient, epanSelectionProc *stateprocessor.EPANSelectionState,
	epanEntryProc *stateprocessor.EPANEntryState, panEvaluationProc *stateprocessor.PANEvaluationState, vkycCacheStorage wireTypes.VKYCRueidisCacheStorage, checkAgentAvailabilityProc *stateprocessor.CheckAgentAvailabilityState,
	userIntelClient userIntelPb.UserIntelServiceClient, callInitiated *stateprocessor.CallInitiationState, updateEmpState *stateprocessor.EmploymentUpdateState,
	panEntry *stateprocessor.PanEntry, vgPanClient pan.PANClient, onbClient onboarding.OnboardingClient,
	confirmVKYCDetailsProc *stateprocessor.ConfirmDetailsState, callQualityCheck *stateprocessor.CallQualityCheck, nrInstructionsState *stateprocessor.NRInstructionsState,
	ackFactory factory.AckFactory, registerNRUserState *stateprocessor.RegisterNRUserState, nrValidateAndStartCallState *stateprocessor.NRValidateAndStartCallState, nrEPANEntryState *stateprocessor.NREPANEntryState,
	userAttrFetcher userPkg.UserAttributesFetcher, nrEPANSelectionState *stateprocessor.NREPANSelectionState, nrUpdateCustomerDetails *stateprocessor.NRUpdateApplicantDetails, sgInstructionState *stateprocessor.SGInstructionsState, sgValidateAndStartCallState *stateprocessor.SGValidateAndStartCallState,
	userGroupClient userGroupPb.GroupClient, matcherClient matcherPb.MatcherClient) *Service {
	s := &Service{
		conf:                        conf,
		svcProvider:                 svcProvider,
		summaryDao:                  summaryDao,
		vkycAttemptDao:              vkycAttemptDao,
		vendor:                      vendor,
		stateProcessor:              stateProcessor,
		eventBroker:                 EventBroker,
		vkycKarzaCallInfoDao:        vkycKarzaCallInfoDao,
		vkycKarzaCallHistoryDao:     vkycKarzaCallHistoryDao,
		vkycCustomerInfoDao:         vkycCustomerInfoDao,
		vkycCallScheduleDao:         vkycCallScheduleDao,
		notification:                notification,
		vkycNotificationDao:         vkycNotificationDao,
		doOnce:                      doOnce,
		kycClient:                   kycClient,
		vkycPriority:                vkycPriority,
		locationClient:              locationClient,
		userClient:                  userClient,
		actorClient:                 actorClient,
		bcClient:                    bcClient,
		empFeClient:                 empFeClient,
		savingsClient:               savingsClient,
		userLocationClient:          userLocationClient,
		inAppTargetedCommsClient:    inAppTargetedCommsClient,
		initiatedStateProc:          initiatedStateProc,
		instructionsStateProc:       instructionsStateProc,
		ekycProc:                    ekycProc,
		callStartProc:               callStartProc,
		homePopupProc:               homePopupProc,
		registerUserProc:            registerUserProc,
		homeProfileExtensionProc:    homeProfileExtensionProc,
		homeTopBannerProc:           homeTopBannerProc,
		profileTopBannerProc:        profileTopBannerProc,
		updateCallInfoProc:          updateCallInfoProc,
		profileMiddleWidgetProc:     profileMiddleWidgetProc,
		panClient:                   panClient,
		employmentClient:            employmentClient,
		epanSelectionProc:           epanSelectionProc,
		epanEntryProc:               epanEntryProc,
		confirmVKYCDetailsProc:      confirmVKYCDetailsProc,
		panEvaluationProc:           panEvaluationProc,
		vkycCacheStorage:            vkycCacheStorage,
		checkAgentAvailabilityProc:  checkAgentAvailabilityProc,
		userIntelClient:             userIntelClient,
		callInitiation:              callInitiated,
		updateEmpState:              updateEmpState,
		panEntry:                    panEntry,
		vgPanClient:                 vgPanClient,
		onbClient:                   onbClient,
		callQualityCheck:            callQualityCheck,
		ackFactory:                  ackFactory,
		nrInstructionsState:         nrInstructionsState,
		registerNRUserState:         registerNRUserState,
		nrValidateAndStartCallState: nrValidateAndStartCallState,
		nrEPANEntryState:            nrEPANEntryState,
		userAttrFetcher:             userAttrFetcher,
		nrEPANSelectionState:        nrEPANSelectionState,
		nrUpdateCustomerDetails:     nrUpdateCustomerDetails,
		sgInstructionState:          sgInstructionState,
		sgValidateAndStartCallState: sgValidateAndStartCallState,
		userGroupClient:             userGroupClient,
		abReleaseEval:               getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.VKYC().ABFeatureReleaseConfig(), func(str string) string { return str }),
		matcherClient:               matcherClient,
	}
	// this was done to mock getVKYCInfo and getUIElements since have huge implementation
	s.vkycMockable = s
	return s
}

var _ vkycPb.VKYCServer = &Service{}
var _ vkycPb.VKYCFeServer = &Service{}

// VkycMockable to make getUIElement and GetVKYCInfo mockable
type VkycMockable interface {
	GetUIElements(context.Context, string, []vkycPb.UIElement) ([]*nudges.NudgeContent, error)
	GetVKYCInfo(ctx context.Context, request *vkycPb.GetVKYCInfoRequest) (*vkycPb.GetVKYCInfoResponse, error)
}

type UIElement struct {
	deeplink          *deeplink.Deeplink
	uiElement         vkycPb.UIElement
	title             string
	icon              string
	dynamicEleCtaList []*dePb.DynamicElementCta
	body              string
	bgColor           string
	isDismissible     bool
	nudgeId           string
	CtaList           []*deeplink.Cta
}

var CallFailSubStatusToPNTitleMap = map[vkycPb.VKYCKarzaCallInfoSubStatus]string{
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_INTERNET_WEAK:                    "We lost you 📶 Your VKYC was unsuccessful",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN:                      "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_NOT_AUDIBLE:                      "We couldn't hear you 🔊 Your VKYC wasn't done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_WILL_RECALL:                  "Call me maybe? 🤙🏻 Finish your VKYC ⚠️",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_MOVING_CONSTANTLY:                "Let's do the Mannequin Challenge for VKYC 🗽",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS:    "VKYC incomplete. Let us help you 😇",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_IS_FRAUDULENT:                "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_BEHAVIOUR_ISSUES:             "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER:                      "VKYC incomplete. Let us help you 😇",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_FACE_NOT_VISIBLE:                 "Are the lights off? 🏻 We couldn't see you",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_OR_TAMPERED_PAN:               "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_DATA:                          "Call me maybe? 🤙🏻 Finish your VKYC ⚠️",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED_ON_AGENT_SIDE:   "Call me maybe? 🤙🏻 Finish your VKYC ⚠️",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_PHOTO_MATCH_PER_LOW:               "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AADHAR_PHOTO_MATCH_PER_LOW:            "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PHOTO_UNABLE_TO_DETERMINE:             "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED:                "Call me maybe? 🤙🏻 Finish your VKYC ⚠️",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_NUMBER_MISMATCH:                   "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DOB_MISMATCH:                          "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FATHER_MOTHER_NAME_MISMATCH:           "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DUPLICATE_INVALID_TAMPERED_PAN:        "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_HAS_EPAN_SOFTCOPY_DIGILOCKER: "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_PEN_PAPER_ISSUE:              "You need to have a Pen & Paper 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DEAF_DUMB_BLIND:              "Something wasn't right ⚠️ VKYC not done",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FORGED_KYC_DOCUMENTS:                  "We need your OG PAN Card 🤘🏼 for VKYC",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_UNAWARE_ABOUT_VKYC:           "VKYC incomplete. Let us help you 😇",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_OTHER:                                 "Call me maybe? 🤙🏻 Finish your VKYC ⚠️",
}

var CallFailSubStatusToPNBodyMap = map[vkycPb.VKYCKarzaCallInfoSubStatus]string{
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_INTERNET_WEAK:                    "Poor network alert! Your video KYC wasn't completed. Try again with a better internet connection.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN:                      "Looks like we couldn't verify your PAN card. Restart your video KYC and ensure you have your PAN Card ready.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_NOT_AUDIBLE:                      "You were not audible! Find a quieter place and restart the call? Make sure your mic is also working fine 👍🏼",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_WILL_RECALL:                  "We could finish your video KYC verification. Mind giving us a call again?",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_MOVING_CONSTANTLY:                "Your video KYC wasn't done. We couldn't see you because you were moving too much. Restart your VKYC, and make sure you're sitting still!",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS:    "Your video KYC isn't done yet. Call us again, and follow the instructions clearly. 🙏🏻",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_IS_FRAUDULENT:                "Your documents couldn't match the records at our end. Restart your video KYC and ensure you have your original PAN Card handy.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_BEHAVIOUR_ISSUES:             "Restart your video KYC call and ensure that you do as requested by the RM. 🙏🏻",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER:                      "We couldn't complete your video KYC verification. Tap to know more.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_FACE_NOT_VISIBLE:                 "Your video KYC wasn't done, because we couldn't see your face. Make sure you're in a well-lit area before you restart your VKYC call. ",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_OR_TAMPERED_PAN:               "Your video KYC wasn't done, because your PAN Card couldn't be verified. Make sure you have the original for the VKYC call.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_DATA:                          "We could finish your video KYC verification. Mind giving us a call again?",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED_ON_AGENT_SIDE:   "Seems like you got disconnected from the VKYC call. Give us another call, we'll complete your verification right away.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_PHOTO_MATCH_PER_LOW:               "Your video KYC is incomplete because your documents don't seem right. Make sure you have your original documents before we go ahead with verification.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AADHAR_PHOTO_MATCH_PER_LOW:            "Your video KYC is incomplete because your documents don't seem right. Make sure you have your original documents before we go ahead with verification.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PHOTO_UNABLE_TO_DETERMINE:             "Your video KYC is incomplete because your photo was not clear on your official documents. Make sure your photo is clearly visible on your document and try again.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED:                "We could finish your video KYC verification. Mind giving us a call again?",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_NUMBER_MISMATCH:                   "Your video KYC is incomplete because your documents don't seem right. Make sure you have your original documents before we go ahead with verification.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DOB_MISMATCH:                          "Your video KYC is incomplete because there was a Date of Birth mismatch between your Fi Application and official document. Please update your details and try again.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FATHER_MOTHER_NAME_MISMATCH:           "Your video KYC is incomplete because there was a parent name mismatch between your Fi Application and PAN Card. Please update your parent name details and try again.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DUPLICATE_INVALID_TAMPERED_PAN:        "Your video KYC wasn't done, because your PAN Card couldn't be verified. Make sure you have the original for the VKYC call.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_HAS_EPAN_SOFTCOPY_DIGILOCKER: "Your video KYC wasn't done, because your PAN Card couldn't be verified. Make sure you have the original for the VKYC call.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_PEN_PAPER_ISSUE:              "Your video KYC is incomplete because we could not capture your signature. Make sure you have a pen and white paper before we go ahead with verification.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DEAF_DUMB_BLIND:              "We have not been able to complete your video KYC verification.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FORGED_KYC_DOCUMENTS:                  "Your video KYC is incomplete because your documents don't seem right. Make sure you have your original documents before we go ahead with verification.",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_UNAWARE_ABOUT_VKYC:           "Your video KYC isn't done yet. Call us again, and follow the instructions clearly. 🙏🏻",
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_OTHER:                                 "We could finish your video KYC verification. Mind giving us a call again?",
}

var CallFailSubStatusToCommType = map[vkycPb.VKYCKarzaCallInfoSubStatus]vkycPb2.CommType{
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_INTERNET_WEAK:                    vkycPb2.CommType_COMM_TYPE_CALL_FAILED_USER_INTERNET_WEAK,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN:                      vkycPb2.CommType_COMM_TYPE_CALL_FAILED_USER_WITHOUT_PAN,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_NOT_AUDIBLE:                      vkycPb2.CommType_COMM_TYPE_CALL_FAILED_USER_NOT_AUDIBLE,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_WILL_RECALL:                  vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_WILL_RECALL,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_MOVING_CONSTANTLY:                vkycPb2.CommType_COMM_TYPE_CALL_FAILED_USER_MOVING_CONSTANTLY,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS:    vkycPb2.CommType_COMM_TYPE_CALL_FAILED_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_IS_FRAUDULENT:                vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_IS_FRAUDULENT,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_BEHAVIOUR_ISSUES:             vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_BEHAVIOUR_ISSUES,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER:                      vkycPb2.CommType_COMM_TYPE_CALL_FAILED_LANGUAGE_BARRIER,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_FACE_NOT_VISIBLE:                 vkycPb2.CommType_COMM_TYPE_CALL_FAILED_USER_FACE_NOT_VISIBLE,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_OR_TAMPERED_PAN:               vkycPb2.CommType_COMM_TYPE_CALL_FAILED_INVALID_OR_TAMPERED_PAN,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_DATA:                          vkycPb2.CommType_COMM_TYPE_CALL_FAILED_INVALID_DATA,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED_ON_AGENT_SIDE:   vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_DISCONNECTED_ON_AGENT_SIDE,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_PHOTO_MATCH_PER_LOW:               vkycPb2.CommType_COMM_TYPE_CALL_FAILED_AADHAR_PHOTO_MATCH_PER_LOW,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AADHAR_PHOTO_MATCH_PER_LOW:            vkycPb2.CommType_COMM_TYPE_CALL_FAILED_PAN_PHOTO_MATCH_PER_LOW,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PHOTO_UNABLE_TO_DETERMINE:             vkycPb2.CommType_COMM_TYPE_CALL_FAILED_PHOTO_UNABLE_TO_DETERMINE,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED:                vkycPb2.CommType_COMM_TYPE_CALL_FAILED_PAN_OCR_CAPTURE_FAILED,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_NUMBER_MISMATCH:                   vkycPb2.CommType_COMM_TYPE_CALL_FAILED_PAN_NUMBER_MISMATCH,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DOB_MISMATCH:                          vkycPb2.CommType_COMM_TYPE_CALL_FAILED_DOB_MISMATCH,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FATHER_MOTHER_NAME_MISMATCH:           vkycPb2.CommType_COMM_TYPE_CALL_FAILED_FATHER_MOTHER_NAME_MISMATCH,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DUPLICATE_INVALID_TAMPERED_PAN:        vkycPb2.CommType_COMM_TYPE_CALL_FAILED_DUPLICATE_INVALID_TAMPERED_PAN,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_HAS_EPAN_SOFTCOPY_DIGILOCKER: vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_HAS_EPAN_SOFTCOPY_DIGILOCKER,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_PEN_PAPER_ISSUE:              vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_PEN_PAPER_ISSUE,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DEAF_DUMB_BLIND:              vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_DEAF_DUMB_BLIND,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FORGED_KYC_DOCUMENTS:                  vkycPb2.CommType_COMM_TYPE_CALL_FAILED_FORGED_KYC_DOCUMENTS,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_UNAWARE_ABOUT_VKYC:           vkycPb2.CommType_COMM_TYPE_CALL_FAILED_CUSTOMER_UNAWARE_ABOUT_VKYC,
	vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_OTHER:                                 vkycPb2.CommType_COMM_TYPE_CALL_FAILED_OTHER,
}

var (
	// map for callSubstatus<>max failure threshHold to be considered for unsupported webview
	callFailureForUnsupportedWebviewMap = map[vkycPb.VKYCKarzaCallInfoSubStatus]int8{
		// INCOMPATIBLE_CUSTOMER_DEVICE is allowed 1 time before deeming to be having unsupported webview
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE: 1,
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_NOT_AUDIBLE:             1,
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DENIED_PERMISSION:   1,
		// USER_INTERNET_WEAK is allowed 2 times before deeming to be having unsupported webview
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_INTERNET_WEAK: 2,
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_FAILED:        2,
	}

	// set kind of map denoting call substatuses which are to be considered if occurence count is breached, after which user will be shown option to copy vkyc weblink to complete vkyc from different device
	incompatibleDeviceMap = map[vkycPb.VKYCKarzaCallInfoSubStatus]int8{
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE: 1,
		vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_FAILED:                  2,
	}

	emptyLocationTokenError              = fmt.Errorf("empty location token")
	locationOutSideIndiaError            = fmt.Errorf("user is outside india")
	locationCommunicationAddressMismatch = fmt.Errorf("communication address and location mismatch")
)

const (
	// to raise alert if user is stuck in vkyc review state for more than 3 days
	stuckAlertDuration          = 3 * 24 * time.Hour
	defaultVkycNextAvailableAt  = "Uh-oh. The Video KYC is currently unavailable. Please try again after sometime"
	vkycNextAvailableAtTimeText = "Uh-oh. The Video KYC is currently unavailable. Please proceed with the call"
)

func (s *Service) RegisterUser(ctx context.Context, request *vkycPb.RegisterUserRequest) (*vkycPb.RegisterUserResponse, error) {
	// collectUserData method pollute the UserData from various sources
	// and return request back
	request, err := s.collectUserData(ctx, request)
	if err != nil {
		if rpcPb.StatusFromError(err).IsRecordNotFound() {
			return &vkycPb.RegisterUserResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg(err.Error()),
			}, nil
		}
		logger.Error(ctx, "got unexpected response while processing user data in regiser user", zap.Error(err))
		return &vkycPb.RegisterUserResponse{
			Status: rpcPb.StatusFromError(err),
		}, nil
	}

	vkycSummary, err := s.summaryDao.GetByActorId(ctx, request.GetActorId())
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error fetching vkyc summary for actor", zap.Error(err))
			goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRegisteredUserVKYCBE(request.GetActorId(), events2.Failure,
					"vkyc summary record not found"+err.Error()))
			})
			return &vkycPb.RegisterUserResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		// create summary for actor if not already present
		vkycSummary, err = s.summaryDao.Create(ctx, &vkycPb.VKYCSummary{
			ActorId:   request.GetActorId(),
			Vendor:    s.vendor,
			Status:    vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNREGISTERED,
			SubStatus: vkycPb.VKYCSummarySubStatus_VKYC_SUMMARY_SUB_STATUS_UNSPECIFIED,
		})
		// not returning error in case of duplicate error to make this flow idempotent
		if err != nil && !storagev2.IsDuplicateRowError(err) {
			logger.Error(ctx, "error creating vkyc summary for actor", zap.Error(err))
			goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRegisteredUserVKYCBE(request.GetActorId(), events2.Failure,
					"error creating vkyc summary for actor"+err.Error()))
			})
			return &vkycPb.RegisterUserResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		if storagev2.IsDuplicateRowError(err) {
			vkycSummary, err = s.summaryDao.GetByActorId(ctx, request.GetActorId())
			if err != nil {
				goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
					s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRegisteredUserVKYCBE(request.GetActorId(), events2.Failure,
						"error fetching vkyc summary for actor"+err.Error()))
				})
				logger.Error(ctx, "error fetching vkyc summary for actor", zap.Error(err))
				return &vkycPb.RegisterUserResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}
		}
		logger.Info(ctx, "vkyc new customer registration")
	}

	isUserVKYCRegistered := isUserVkycRegistered(vkycSummary)
	isIncomeOccupationIsSame, vkycCustInfo := s.checkIfIncomeOccupationIsSame(ctx, vkycSummary.GetId(), request.GetUserData())
	logger.Info(ctx, fmt.Sprintf("isUserVKYCRegistered: %v isIncomeOccupationIsSame: %v", isUserVKYCRegistered, isIncomeOccupationIsSame))
	switch {
	// return OK if vkyc is registered and there's no change in income occupation
	case isUserVKYCRegistered && isIncomeOccupationIsSame:
		return &vkycPb.RegisterUserResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	// register user for vkyc if vkyc is not registered
	case !isUserVKYCRegistered:
		if err = s.svcProvider.RegisterCustomer(ctx, vkycSummary, request.GetUserData()); err != nil {
			logger.Error(ctx, "error registering customer at vendor end for video kyc", zap.Error(err))
			goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRegisteredUserVKYCBE(request.GetActorId(), events2.Failure,
					"error registering customer at vendor end for video kyc"+err.Error()))
			})
			return &vkycPb.RegisterUserResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	// reaching this case means vkyc is registered since case 2 was failed
	// and since income occupation is not same, we need to update it at vendor's end when v3 feature is also enabled
	case !isIncomeOccupationIsSame:
		if err = s.svcProvider.UpdateCustomer(ctx, vkycCustInfo, request.GetUserData()); err != nil {
			logger.Error(ctx, "error updating customer at vendor's end for video kyc", zap.Error(err))
			return &vkycPb.RegisterUserResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		logger.Info(ctx, "updated vkyc income occupation details")
		return &vkycPb.RegisterUserResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}
	vkycSummary.Status = vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED
	vkycSummary.SubStatus = vkycPb.VKYCSummarySubStatus_VKYC_SUMMARY_SUB_STATUS_ATTEMPT_PENDING

	err = s.summaryDao.UpdateById(ctx, vkycSummary, []vkycPb.VKYCSummaryFieldMask{vkycPb.VKYCSummaryFieldMask_VKYC_SUMMARY_FIELD_MASK_STATUS, vkycPb.VKYCSummaryFieldMask_VKYC_SUMMARY_FIELD_MASK_SUB_STATUS})
	if err != nil {
		logger.Error(ctx, "unable to update summary status", zap.String("summaryID", vkycSummary.GetId()), zap.Error(err))
		goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRegisteredUserVKYCBE(request.GetActorId(), events2.Failure,
				"unable to update summary status"+err.Error()))
		})
		return &vkycPb.RegisterUserResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRegisteredUserVKYCBE(request.GetActorId(), events2.Success, ""))
	})
	if lo.Contains(vkyc.ReAttemptVKYC, request.GetActorId()) {
		logger.Info(ctx, "user successfully registered for re attempt vkyc")
	}
	return &vkycPb.RegisterUserResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// nolint:funlen
func (s *Service) collectUserData(ctx context.Context, request *vkycPb.RegisterUserRequest) (*vkycPb.RegisterUserRequest, error) {
	var (
		kycResp         *kyc.GetKYCRecordResponse
		uidReferenceKey string
		gender          types.Gender
		address         *postaladdress.PostalAddress
		err             error
	)

	userResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: request.GetActorId(),
		},
	})

	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		logger.Error(ctx, "Error while fetching user from actor id", zap.Error(rpcErr))
		return nil, rpcPb.StatusAsError(rpcPb.StatusInternalWithDebugMsg(rpcErr.Error()))
	}

	user := userResp.GetUser()
	bcResp, err := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: request.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(bcResp, err); te != nil {
		logger.Error(ctx, "Error while fetching info from bank customer", zap.Error(te))
		return nil, rpcPb.StatusAsError(rpcPb.StatusInternalWithDebugMsg(te.Error()))
	}

	cifSucceededAt := bcResp.GetBankCustomer().GetVendorCreationSucceededAt()
	if !vkyc.IsAllowVKYCForFullKyc(request.GetActorId()) && cifSucceededAt.IsValid() {
		accFreezeOn := timestamppb.New(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1 * 24 * time.Hour))
		if time.Now().After(accFreezeOn.AsTime()) {
			logger.Info(ctx, "account deemed to frozen, not registering user for vkyc")
			return nil, rpcPb.StatusAsError(rpcPb.StatusFailedPrecondition())
		}
	}
	kycResp, err = s.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId: request.GetActorId(),
	})

	if rpcErr := epifigrpc.RPCError(kycResp, err); rpcErr != nil {
		if kycResp.GetStatus().IsRecordNotFound() {
			// TODO(rishu) - monitor and check if GetKYCRecord call can be removed
			// TODO(rishu) - use handleKycRecordNotFound when image issue is resolved
			logger.Info(ctx, "KYC record found, not registering user for vkyc")
			return nil, rpcPb.StatusAsError(rpcPb.StatusRecordNotFound())
		}
		if kycResp.GetStatus().GetCode() == uint32(kyc.GetKYCRecordResponse_NAME_MISMATCH) {
			logger.Info(ctx, "Name mismatch in ekyc record not registering user")
			return nil, rpcPb.StatusAsError(rpcPb.NewStatus(uint32(kyc.GetKYCRecordResponse_NAME_MISMATCH), "", ""))
		}
		logger.Error(ctx, "error fetching kyc record from kyc service", zap.Error(rpcErr))
		return nil, rpcPb.StatusAsError(rpcPb.StatusInternalWithDebugMsg("error fetching kyc record from kyc service"))
	}
	if time.Since(getGenerationTime(kycResp).AsTime()) > s.conf.VKYC().PerformEkycAfter() {
		logger.Info(ctx, fmt.Sprintf("Kyc record created date more than 3 days created at: %v", getGenerationTime(kycResp).AsTime()))
		return nil, rpcPb.StatusAsError(rpcPb.StatusFailedPrecondition())
	}

	// return err if user is full kyc and user is NEITHER in VKYC user group and CKYC O type
	if bcResp.GetBankCustomer().GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC && !vkyc.IsUserCKYCWithOFlag(kycResp.GetCkycAttempt()) && !vkyc.IsAllowVKYCForFullKyc(request.GetActorId()) {
		logger.Info(ctx, "user already full kyc", zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return nil, rpcPb.StatusAsError(rpcPb.StatusFailedPrecondition())
	}
	uidReferenceKey = s.getEkycRrn(ctx, bcResp.GetBankCustomer(), kycResp.GetKycRecord().GetUidReferenceKey())
	logger.Info(ctx, fmt.Sprintf("logging the uid reference key %v", uidReferenceKey))
	employmentType, annualIncome, err := s.getEmploymentData(ctx, request.GetActorId(), user.GetProfile().GetSalaryRange(), request.GetEntryPoint(), user)
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, rpcPb.StatusAsError(rpcPb.StatusRecordNotFound())
	}
	registerCustomerRequest, err := s.getRegisterCustomerRequest(ctx, request.GetActorId(), user, kycResp, uidReferenceKey, gender, address, employmentType, annualIncome, request.GetEntryPoint())
	if err != nil {
		return nil, rpcPb.StatusAsError(rpcPb.StatusInternalWithDebugMsg(err.Error()))
	}
	request.UserData = registerCustomerRequest.GetUserData()
	return request, nil
}

func (s *Service) getRegisterCustomerRequest(ctx context.Context, actorId string, user *userPb.User, kycResp *kyc.GetKYCRecordResponse,
	uidReferenceKey string, gender types.Gender, postalAddress *postaladdress.PostalAddress, employmentType employmentPb.EmploymentType,
	annualSalary *screening.AnnualSalary, entryPoint vkycPb.EntryPoint) (*vkycPb.RegisterUserRequest, error) {
	var (
		currentAddress *postaladdress.PostalAddress
		address        *postaladdress.PostalAddress
		err            error
	)
	if kycResp.GetKycRecord().GetPermanentAddress() != nil {
		address = kycResp.GetKycRecord().GetPermanentAddress()
	}
	if address == nil && kycResp.GetKycRecord().GetCommunicationAddress() != nil {
		address = kycResp.GetKycRecord().GetCommunicationAddress()
	}
	if address == nil {
		logger.Error(ctx, "cannot get address from kyc record")
		address = postalAddress
	}
	// For rest for the flow current address will be the address from kyc record
	currentAddress = address

	// override current address only if entry point is loan application
	currentAddress, err = s.updateCurrentAddress(entryPoint, currentAddress, user)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in updating current address for %s", entryPoint.String()), zap.Error(err))
		return nil, err
	}
	vkycTypeResp, respErr := s.GetVKYCType(ctx, &vkycPb.GetVKYCTypeRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(vkycTypeResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting vkyc type", zap.Error(rpcErr))
		return nil, rpcErr
	}
	custId := vkycTypeResp.GetVendorMetadata().GetKarzaMetadata().GetCustomerId()

	registerCustomerRequest := &vkycPb.RegisterUserRequest{
		ActorId: actorId,
		UserData: &vkycPb.UserData{
			CustomerId:       custId,
			FatherName:       user.GetProfile().GetFatherName(),
			PanNumber:        user.GetProfile().GetPAN(),
			CurrentAddress:   currentAddress,
			PermanentAddress: address,
			Dob:              user.GetProfile().GetDateOfBirth(),
			Email:            user.GetProfile().GetEmail(),
			PhoneNumber:      user.GetProfile().GetPhoneNumber(),
			GenerationTime:   getGenerationTime(kycResp),
			Image:            getImage(kycResp),
			UidReferenceKey:  vkyc.GetVKYCUidReferenceKey(kycResp, uidReferenceKey),
			Gender:           getGenderFromKycRecord(kycResp, gender),
			Name:             vkyc.GetVKYCName(kycResp, user),
			AnnualSalary:     annualSalary,
			EmploymentType:   employmentType,
			MotherName:       user.GetProfile().GetMotherName(),
		},
	}
	return registerCustomerRequest, nil
}

// getEkycRrn will return the EKYC number that was used during customer creation.
// If the bank customer status is not active, it will return the last EKYC RRN that was generated during EKYC.
func (s *Service) getEkycRrn(ctx context.Context, bankCustomer *bankcust.BankCustomer, kycRecEkycRrn string) string {
	if bankCustomer.GetStatus() == bankcust.Status_STATUS_ACTIVE &&
		bankCustomer.GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo() != "" {
		resp, errResp := s.onbClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
			ActorId:    bankCustomer.GetActorId(),
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			CachedData: true,
		})
		if err := epifigrpc.RPCError(resp, errResp); err != nil {
			logger.Error(ctx, "error in fetching onboarding details, using ekycrrn from kyc record", zap.Error(err))
			return kycRecEkycRrn
		}
		ekycNameDobValidationState := resp.GetDetails().GetStageDetails().GetStageMapping()[onboarding.OnboardingStage_EKYC_NAME_DOB_VALIDATION.String()].GetState()
		kycNameDobValidationState := resp.GetDetails().GetStageDetails().GetStageMapping()[onboarding.OnboardingStage_KYC_NAME_DOB_VALIDATION.String()].GetState()
		if ekycNameDobValidationState == onboarding.OnboardingState_SUCCESS || kycNameDobValidationState == onboarding.OnboardingState_SUCCESS {
			return bankCustomer.GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo()
		}
		logger.Info(ctx, "using ekycrrn from kyc record as namedob stage is not successful for bank customer rrn")
	}
	return kycRecEkycRrn
}

func getGenderFromKycRecord(kycResp *kyc.GetKYCRecordResponse, gender types.Gender) types.Gender {
	if kycResp.GetKycRecord() == nil {
		return gender
	}
	return kycResp.GetKycRecord().GetGender()
}

func getImage(kycResp *kyc.GetKYCRecordResponse) *commontypes.Image {
	if kycResp.GetKycRecord() == nil {
		return dummyImage
	}
	return kycResp.GetKycRecord().GetPhoto()
}

func getGenerationTime(kycResp *kyc.GetKYCRecordResponse) *timestamp.Timestamp {
	if kycResp.GetKycRecord() == nil {
		return ptypes.TimestampNow()
	}
	return kycResp.GetKycRecord().GetCreatedAt()
}

func (s *Service) getEmploymentData(ctx context.Context, actorId string, salaryRange *userPb.SalaryRange, entryPoint vkycPb.EntryPoint, user *userPb.User) (employmentPb.EmploymentType, *screening.AnnualSalary, error) {
	if entryPoint == vkycPb.EntryPoint_ENTRY_POINT_FEDERAL_LOANS {
		// Employment and income are being stored at a different place for FEDERAL_LOANS which requires special handling
		return s.getEmploymentDataForLoanApplication(ctx, user)
	}
	employmentResp, err := s.employmentClient.GetEmploymentInfo(ctx, &employmentPb.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(employmentResp, err); te != nil {
		if rpcPb.StatusFromError(te).IsRecordNotFound() {
			return employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED, nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error fetching employment record from employment service", zap.String(logger.USER_ID,
			actorId), zap.Error(te))
		return employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED, nil, err
	}
	annualSalary := employmentResp.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary()
	// populate salary range from profile(salaryRange)
	if annualSalary.GetRange().GetMinValue() == 0 &&
		annualSalary.GetRange().GetMaxValue() == 0 &&
		annualSalary.GetAbsolute() == 0 {
		if annualSalary == nil {
			annualSalary = &screening.AnnualSalary{}
		}
		annualSalary.Range = &screening.AnnualSalaryRange{
			MinValue: salaryRange.GetMinValue(),
			MaxValue: salaryRange.GetMaxValue(),
		}
	}
	return employmentResp.GetEmploymentData().GetEmploymentType(), annualSalary, nil
}

func (s *Service) getEmploymentDataForLoanApplication(ctx context.Context, user *userPb.User) (employmentPb.EmploymentType, *screening.AnnualSalary, error) {
	var employmentDetails *userPb.DataVerificationDetail_EmploymentDetail
	for _, verificationDetails := range user.GetDataVerificationDetails().GetDataVerificationDetails() {
		if verificationDetails.GetVerificationMethod() == userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA {
			employmentDetails = verificationDetails.GetEmploymentDetail()
			// NOTE: We are not breaking here as we want the latest employment details from the list.
		}
	}
	if employmentDetails == nil {
		logger.Error(ctx, "error fetching employment details from user data verification details for loan appl")
		return employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED, nil, epifierrors.ErrRecordNotFound
	}

	return employmentPb.GetEmploymentType(employmentDetails.GetEmploymentType()), &screening.AnnualSalary{
		Absolute: float32(employmentDetails.GetMonthlyIncome().GetUnits() * 12),
	}, nil
}

func (s *Service) updateCurrentAddress(entryPoint vkycPb.EntryPoint, currentAddress *postaladdress.PostalAddress, user *userPb.User) (*postaladdress.PostalAddress, error) {
	if entryPoint != vkycPb.EntryPoint_ENTRY_POINT_FEDERAL_LOANS {
		return currentAddress, nil
	}
	// update current address for loans
	loanCommsAddress, ok := user.GetProfile().GetAddresses()[types.AddressType_LOAN_COMMUNICATION.String()]
	if !ok {
		return nil, fmt.Errorf("communication address not found for loan application")
	}

	return loanCommsAddress, nil
}

func (s *Service) StartLiveCall(_ context.Context, _ *vkycPb.StartLiveCallRequest) (*vkycPb.StartLiveCallResponse, error) {
	return &vkycPb.StartLiveCallResponse{
		Status: rpcPb.StatusUnimplemented(),
	}, nil
}

// return status code in respect of error
func getStartCallStatus(err error, userCountry string) *rpcPb.Status {
	switch {
	case errors.Is(err, emptyLocationTokenError):
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.StartLiveCallResponse_EMPTY_LOCATION_TOKEN), "Empty location token")
	case errors.Is(err, locationOutSideIndiaError):
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.StartLiveCallResponse_LOCATION_OUTSIDE_INDIA), userCountry)
	case err != nil:
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.StartLiveCallResponse_LOCATION_UPDATE_ERROR), "Error in updating location")
	}
	return rpcPb.StatusOk()
}

// validate location token received and update lat long on karza end
func (s *Service) validateAndUpdateLocation(ctx context.Context, locationToken, actorId string, actionReq *actions.ActionRequest, entryPoint vkycPb.EntryPoint) (error, string) {
	if locationToken == "" {
		logger.Info(ctx, "Empty location token received from client")
		return emptyLocationTokenError, ""
	}
	resp, err := s.userLocationClient.FetchAndStoreAddressForIdentifier(ctx, &userLocation.FetchAndStoreAddressForIdentifierRequest{
		IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
		IdentifierValue: locationToken,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Info(ctx, "Error in fetching country", zap.Error(err))
		return err, ""
	}
	userCountry := strings.TrimSpace(resp.GetAddress().GetRegionCode())
	if !strings.EqualFold(userCountry, "") && !strings.EqualFold(userCountry, constants.RegionCodeIndia) {
		logger.Info(ctx, "User location is outside india")
		return locationOutSideIndiaError, userCountry
	}

	if entryPoint == vkycPb.EntryPoint_ENTRY_POINT_FEDERAL_LOANS {
		err = s.compareCommsAndLocationAddress(ctx, actorId, locationToken)
		if err != nil {
			return err, ""
		}
	}

	// validations passed update location on karza end
	return s.updateLocationOrCif(ctx, locationToken, actionReq.Summary().GetId(), actorId, true), ""
}

// compareCommsAndLocationAddress checks if the communication address and the current location address are nearby.
// currently this is used only for federal loans flow
func (s *Service) compareCommsAndLocationAddress(ctx context.Context, actorId string, locationToken string) error {
	userResp, err := s.userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching user", zap.Error(rpcErr))
		return rpcErr
	}
	communicationAddress, ok := userResp.GetUser().GetProfile().GetAddresses()[types.AddressType_LOAN_COMMUNICATION.String()]
	if !ok {
		logger.Error(ctx, "loan communication address not found")
		return fmt.Errorf("loan communication address not found")
	}
	matchLoc, matchLocErr := s.matcherClient.MatchLocation(ctx, &matcherPb.MatchLocationRequest{
		GotData: &matcherPb.LocationMatchData{
			LocationSourceType: enums.LocationSourceType_LOCATION_SOURCE_TYPE_ADDRESS,
			LocationValue: &matcherPb.LocationMatchData_Address{
				Address: communicationAddress,
			},
		},
		ExpectedData: &matcherPb.LocationMatchData{
			LocationSourceType: enums.LocationSourceType_LOCATION_SOURCE_TYPE_LOCATION_TOKEN,
			LocationValue: &matcherPb.LocationMatchData_LocationToken{
				LocationToken: locationToken,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(matchLoc, matchLocErr); rpcErr != nil {
		logger.Error(ctx, "error in matching location", zap.Error(rpcErr))
		return rpcErr
	}

	if matchLoc.GetMatchResults().GetDistanceInKm() > float64(s.conf.VKYC().LocationMatchThresholdInKM()) {
		logger.Error(ctx, "communication address and location mismatch", zap.Float64("distance", matchLoc.GetMatchResults().GetDistanceInKm()))
		return locationCommunicationAddressMismatch
	}
	return nil
}

// frontend will add X days to the date as expiry
func getEkycDate(customerInfo *model.CustomerInfo) *timestamp.Timestamp {
	if customerInfo.GetTransactionMetadata().GetKycDate().IsValid() {
		return customerInfo.GetTransactionMetadata().GetKycDate()
	}
	// best effort as in right after ekyc vkyc registration call is made
	return customerInfo.GetCreatedAt()
}

func (s *Service) GetAvailableSlots(ctx context.Context, req *vkycPb.GetAvailableSlotsRequest) (*vkycPb.GetAvailableSlotsResponse, error) {
	slots := s.getSlots(ctx, req.GetActorId(), req.GetStartTime(), req.GetEndTime())
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events2.NewShowedVKYCSlotDetails(req.GetActorId(), events2.Success, ""))
	})
	return &vkycPb.GetAvailableSlotsResponse{
		Status:          rpcPb.StatusOk(),
		SlotDetailsList: slots,
	}, nil
}

func (s *Service) BookSlot(ctx context.Context, req *vkycPb.BookSlotRequest) (*vkycPb.BookSlotResponse, error) {
	// we are just notify user, previously we used to book slot actually
	// logic removed in PR https://github.com/epiFi/gamma/pull/67501
	s.notify(ctx, req.GetSlotDetails(), req.GetActorId())
	return &vkycPb.BookSlotResponse{
		Status:     rpcPb.StatusOk(),
		NextAction: getCallScheduleScreen(req.GetEntryPoint(), req.GetSlotDetails().GetStartTime(), req.GetSlotDetails().GetEndTime()),
	}, nil
}

func (s *Service) GetSchedule(ctx context.Context, request *vkycPb.GetScheduleRequest) (*vkycPb.GetScheduleResponse, error) {
	vkycSummary, err := s.summaryDao.GetByActorId(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "error fetching vkyc summary for actor id", zap.Error(err))
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.GetScheduleResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		return &vkycPb.GetScheduleResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	attempt, err := s.vkycAttemptDao.GetLatestAttemptByVkycSummaryId(ctx, vkycSummary.GetId())
	if err != nil {
		logger.Error(ctx, "unable to fetch latest attempt", zap.String("summaryID", vkycSummary.GetId()), zap.Error(err))
		return &vkycPb.GetScheduleResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// TODO: Check for any existing non terminal attempt

	resp, err := s.svcProvider.GetSchedule(ctx, vkycSummary.GetId(), attempt.GetId())
	if err != nil {
		logger.Error(ctx, "unable to fetch schedule for user", zap.String("attemptId", attempt.GetId()), zap.String("summaryID", vkycSummary.GetId()), zap.Error(err))
		return &vkycPb.GetScheduleResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if resp == nil || resp.SlotId == "" || resp.Weblink == "" {
		logger.Error(ctx, "error in fetching schedule for user, empty slotId or weblink",
			zap.String("attemptId", attempt.GetId()), zap.String("summaryID", vkycSummary.GetId()), zap.Error(err))
		return &vkycPb.GetScheduleResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	return &vkycPb.GetScheduleResponse{
		Status:         rpcPb.StatusOk(),
		SlotId:         resp.SlotId,
		StartTime:      resp.StartTime,
		EndTime:        resp.EndTime,
		Weblink:        resp.Weblink,
		AttemptId:      attempt.GetId(),
		ScheduleStatus: vkycPb.ScheduleStatus_SCHEDULE_STATUS_OK,
	}, nil
}

func (s *Service) GetVKYCSummary(ctx context.Context, request *vkycPb.GetVKYCSummaryRequest) (*vkycPb.GetVKYCSummaryResponse, error) {
	summary, err := s.summaryDao.GetByActorId(ctx, request.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.GetVKYCSummaryResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &vkycPb.GetVKYCSummaryResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// if customer registration is failed return not found
	if summary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNREGISTERED || summary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNSPECIFIED {
		return &vkycPb.GetVKYCSummaryResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	actionReq := actions.NewActionRequest(request.GetActorId(), s.vendor)
	actionReq.SetCreateFlow(false)
	actionReq.SetActorId(request.GetActorId())
	err = s.stateProcessor.TriggerVkycFlow(ctx, actionReq)
	if err != nil {
		logger.Error(ctx, "failed to trigger vkyc flow", zap.String("actor_id", request.GetActorId()), zap.Error(err))
		return &vkycPb.GetVKYCSummaryResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	vkycRecord, err := s.getVkycRecord(ctx, actionReq)
	if err != nil {
		return &vkycPb.GetVKYCSummaryResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &vkycPb.GetVKYCSummaryResponse{
		Status:      rpcPb.StatusOk(),
		VkycSummary: actionReq.Summary(),
		VkycRecord:  vkycRecord,
	}, nil
}

func (s *Service) getVkycRecord(ctx context.Context, actionReq *actions.ActionRequest) (*vkycPb.VKYCRecord, error) {
	rec := &vkycPb.VKYCRecord{
		VkycSummary:           actionReq.Summary(),
		VkycKarzaCustomerInfo: actionReq.CustomerInfo().VKYCKarzaCustomerInfo,
	}
	var attemptDataList []*vkycPb.VKYCRecord_VKYCAttemptData
	// Get other summary data for VKYC
	vkycAttemptList, err := s.vkycAttemptDao.GetAttemptsByVkycSummaryId(ctx, actionReq.Summary().GetId())
	if err != nil {
		logger.Error(ctx, "failed to fetch vkyc attempts by summary id", zap.Error(err))
		return nil, err
	}
	for _, attempt := range vkycAttemptList {
		var callInfoDataList []*vkycPb.VKYCRecord_VKYCAttemptData_VKYCKarzaCallInfoData
		callInfoList, err := s.vkycKarzaCallInfoDao.GetCallsByVkycAttemptId(ctx, attempt.GetId())
		if err != nil {
			logger.Error(ctx, "failed to fetch vkyc call info by attempt id", zap.Error(err))
			return nil, err
		}
		for _, callInfo := range callInfoList {
			var callSchedule *vkycPb.VKYCCallSchedule
			if callInfo.GetCallType() == vkycPb.VKYCKarzaCallInfoCallType_VKYC_KARZA_CALL_INFO_CALL_TYPE_SCHEDULED {
				callSchedule, err = s.vkycCallScheduleDao.GetByRefId(ctx, callInfo.GetId())
				if err != nil {
					logger.Error(ctx, "failed to fetch vkyc call schedule by ref id", zap.Error(err))
					return nil, err
				}
			}
			callHistoryList, err := s.vkycKarzaCallHistoryDao.GetByCallInfoId(ctx, callInfo.GetId())
			if err != nil {
				logger.Error(ctx, "failed to fetch vkyc call history list by call info id", zap.Error(err))
				return nil, err
			}
			callInfoDataList = append(callInfoDataList, &vkycPb.VKYCRecord_VKYCAttemptData_VKYCKarzaCallInfoData{
				VkycKarzaCallInfo:    callInfo,
				VkycCallSchedule:     callSchedule,
				VkycKarzaCallHistory: callHistoryList,
			})
		}
		attemptDataList = append(attemptDataList, &vkycPb.VKYCRecord_VKYCAttemptData{
			VkycAttempt: attempt, VkycKarzaCallInfoDataList: callInfoDataList})
	}
	rec.VkycAttemptDataList = attemptDataList
	custInfos, err := s.vkycCustomerInfoDao.GetAllByVkycSummaryId(ctx, actionReq.Summary().GetId())
	if err != nil {
		logger.Error(ctx, "error fetching customer info using vkyc summary id", zap.Error(err))
	}
	rec.VkycKarzaCustomerInfos = custInfos
	return rec, nil
}

func (s *Service) GetCallStatus(ctx context.Context, request *vkycPb.GetCallStatusRequest) (*vkycPb.GetCallStatusResponse, error) {
	actionReq := actions.NewActionRequest(request.GetActorId(), s.vendor)
	actionReq.SetCreateFlow(false)
	err := s.stateProcessor.TriggerVkycFlow(ctx, actionReq)
	if err != nil {
		logger.Error(ctx, "failed to perform live call flow", zap.String("actor_id", request.GetActorId()), zap.Error(err))
		return &vkycPb.GetCallStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	callInfo := actionReq.CallInfo()
	if callInfo == nil {
		callInfo, err = s.svcProvider.GetLatestCallInfo(ctx, request.GetAttemptId())
		if err != nil {
			logger.Error(ctx, "error while getting latest call info", zap.String("actor_id", request.GetActorId()), zap.Error(err))
			return &vkycPb.GetCallStatusResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		// check for record not found
		if callInfo == nil {
			logger.Error(ctx, "call not found for the attempt")
			return &vkycPb.GetCallStatusResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
	}
	if callInfo.GetInfo().GetStatus() == vkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED {
		_, ok := CallFailSubStatusToPNBodyMap[callInfo.GetInfo().GetSubStatus()]
		logger.Info(ctx, fmt.Sprintf("call fail check in map %v", ok))
		// send PN only for custom call failure sub statuses
		if ok {
			goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
				_ = s.sendCallFailNotification(epificontext.CloneCtx(ctx), callInfo, actionReq.ActorId())
			})
		} else {
			logger.Info(ctx, "not sending PN", zap.String("callInfoSubStatus", actionReq.CallInfo().GetInfo().GetSubStatus().String()))
		}
	}
	return &vkycPb.GetCallStatusResponse{
		Status: rpcPb.StatusOk(),
		CallStatus: []*vkycPb.KarzaCallStatus{
			{
				Status:        callInfo.GetInfo().GetStatus(),
				SubStatus:     callInfo.GetInfo().GetSubStatus(),
				CallStartedAt: callInfo.GetInfo().GetCreatedAt(),
			},
		},
	}, nil
}

func (s *Service) GetVKYCStatus(ctx context.Context, req *vkycPb.GetVKYCStatusRequest) (*vkycPb.GetVKYCStatusResponse, error) {
	summary, err := s.summaryDao.GetByActorId(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.GetVKYCStatusResponse{
				Status:      rpcPb.StatusRecordNotFound(),
				VkycSummary: nil,
			}, nil
		}
		return &vkycPb.GetVKYCStatusResponse{
			Status:      rpcPb.StatusInternal(),
			VkycSummary: nil,
		}, nil
	}
	// if customer registration is failed return not found
	if summary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNREGISTERED || summary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNSPECIFIED {
		return &vkycPb.GetVKYCStatusResponse{
			Status:      rpcPb.StatusRecordNotFound(),
			VkycSummary: nil,
		}, nil
	}
	actionReq := actions.NewActionRequest(req.GetActorId(), s.vendor)
	actionReq.SetCreateFlow(false)
	actionReq.SetActorId(req.GetActorId())
	err = s.stateProcessor.TriggerVkycFlow(ctx, actionReq)
	if err != nil {
		logger.Error(ctx, "failed to trigger vkyc flow", zap.String("actor_id", req.GetActorId()), zap.Error(err))
		return &vkycPb.GetVKYCStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if err := s.triggerAlertIfUserStuckInReview(ctx, actionReq.Summary()); err != nil {
		logger.Error(ctx, "error alerting vkyc stuck in review state", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
	}
	return &vkycPb.GetVKYCStatusResponse{
		Status:       rpcPb.StatusOk(),
		VkycSummary:  actionReq.Summary(),
		VkycAttempt:  actionReq.Attempt(),
		VkycCallInfo: actionReq.CallInfo().GetInfo(),
	}, nil
}

func (s *Service) CheckHoliday(ctx context.Context, request *vkycPb.CheckHolidayRequest) (*vkycPb.CheckHolidayResponse, error) {
	resp := &vkycPb.CheckHolidayResponse{
		Status: rpcPb.StatusOk(),
	}
	isHoliday, vkycNextAvailableAt, err := s.svcProvider.IsHoliday(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in checking holiday", zap.Error(err))
		resp.Status = rpcPb.StatusInternal()
		return resp, nil
	}
	// IsHoliday will be true when current time is found in inactivity intervals in db OR current time is outside business hours
	resp.IsHoliday = isHoliday || !common.IsBusinessHours(time.Now().In(datetime.IST), s.conf.VKYC().VkycStartHour(), s.conf.VKYC().VkycEndHour())
	resp.VkycNextAvailableStart = vkycNextAvailableAt
	resp.VkycNextAvailableAtDisplayText = s.getVkycNextAvailableAtText(ctx, vkycNextAvailableAt, resp.GetIsHoliday())
	logger.Debug(ctx, fmt.Sprintf("isHoliday: %v isBusinessHours: %v", isHoliday, common.IsBusinessHours(time.Now().In(datetime.IST), s.conf.VKYC().VkycStartHour(), s.conf.VKYC().VkycEndHour())))
	return resp, nil
}

func (s *Service) TriggerCallback(ctx context.Context, request *vkycPb.TriggerCallbackRequest) (*vkycPb.TriggerCallbackResponse, error) {
	resp := &vkycPb.TriggerCallbackResponse{
		Status: rpcPb.StatusOk(),
	}
	var vkycRecords []*vkycPb.VKYCRecord
	for _, actorId := range request.GetActorIds() {
		vkycSummary, err := s.summaryDao.GetByActorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error fetching vkyc summary for actor id", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
			// best effort
			continue
		}
		vkycRecords = append(vkycRecords, &vkycPb.VKYCRecord{
			VkycSummary: vkycSummary,
		})
	}
	if len(vkycRecords) == 0 {
		logger.Error(ctx, "unable to fetch vkyc summary", zap.Any("actorIds", request.GetActorIds()))
		resp.Status = rpcPb.StatusInternal()
		return resp, nil
	}
	if err := s.svcProvider.TriggerCallback(ctx, request.GetCallbackType(), vkycRecords); err != nil {
		logger.Error(ctx, "error in triggering vendor callback", zap.String("callbackType", request.GetCallbackType().String()), zap.Error(err))
		resp.Status = rpcPb.StatusInternal()
		return resp, nil
	}
	return resp, nil
}

func (s *Service) notify(ctx context.Context, slotDetails *vkycPb.SlotDetails, actorId string) {
	scheduledTime := slotDetails.GetStartTime()
	slotStartTime := slotDetails.GetStartTime().AsTime().In(datetime.IST)

	dur := slotStartTime.Sub(time.Now().In(datetime.IST)) - 10*time.Minute
	logger.Info(ctx, "vkyc reminder PN", zap.String("duration", dur.String()))
	notiMsg := getScheduleReminderNotiMsg(actorId, scheduledTime, comms.Medium_NOTIFICATION, 10)
	// not returning error immediately to try to send atleast 1 notification
	_, err1 := s.notification.SendNotification(ctx, notiMsg, dur)
	if err1 != nil {
		logger.Error(ctx, "error in sending vkyc reminder PN before 10 mins", zap.Error(err1))
	}

	dur = slotStartTime.Sub(time.Now().In(datetime.IST)) - 1*time.Minute
	logger.Info(ctx, "vkyc reminder PN", zap.String("duration", dur.String()))
	notiMsg = getScheduleReminderNotiMsg(actorId, scheduledTime, comms.Medium_NOTIFICATION, 1)
	// not returning error immediately to try to send atleast 1 notification
	_, err2 := s.notification.SendNotification(ctx, notiMsg, dur)
	if err2 != nil {
		logger.Error(ctx, "error in sending vkyc reminder PN before 1 min", zap.Error(err2))
	}

	notiMsg = getCallScheduledNotiMsg(actorId, scheduledTime, comms.Medium_EMAIL)
	_, err4 := s.notification.SendNotification(ctx, notiMsg, 0)
	if err4 != nil {
		logger.Error(ctx, "error in sending call scheduled email notification", zap.Error(err4))
	}

	dur = slotStartTime.Sub(time.Now().In(datetime.IST)) - 15*time.Minute
	logger.Info(ctx, "vkyc email reminder email", zap.String("duration", dur.String()))
	notiMsg = getScheduleReminderNotiMsg(actorId, scheduledTime, comms.Medium_EMAIL, 15)
	_, err5 := s.notification.SendNotification(ctx, notiMsg, dur)
	if err5 != nil {
		logger.Error(ctx, "error in sending reminder email for call scheduled ", zap.Error(err5))
	}
}

func (s *Service) ReRegisterVkycInfo(ctx context.Context, request *vkycPb.ReRegisterVkycInfoRequest) (*vkycPb.ReRegisterVkycInfoResponse, error) {
	var (
		actorId = request.GetActorId()
		err     error
	)

	// check if call and attempt sub status have mapping with status as failed
	if actions.CallSubStatusToStatusMap[request.GetCallInfoSubStatus()] != vkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED ||
		actions.AttemptSubStatusToStatusMap[request.GetAttemptSubStatus()] != vkycPb.VKYCAttemptStatus_VKYC_ATTEMPT_STATUS_FAILED {
		logger.Info(ctx, fmt.Sprintf("either call sub status or attempt sub status not mapped to failed status, call sub status %v: attempt sub status %v: ",
			request.GetCallInfoSubStatus(), request.GetAttemptSubStatus()))
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	switch request.GetIdentifier().(type) {
	case *vkycPb.ReRegisterVkycInfoRequest_EkycRrn:
		// fetch actor id for given ekyc rrn
		actorId, err = s.getActorIdFromEkycRrn(ctx, request.GetEkycRrn())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &vkycPb.ReRegisterVkycInfoResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			return &vkycPb.ReRegisterVkycInfoResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	}

	ctx = epificontext.CtxWithActorId(ctx, actorId)

	summary, err := s.summaryDao.GetByActorId(ctx, actorId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.ReRegisterVkycInfoResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "Error in fetching vkyc summary", zap.Error(err))
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	attempt, attemptErr := s.vkycAttemptDao.GetLatestAttemptByVkycSummaryId(ctx, summary.GetId())
	if attemptErr != nil {
		logger.Error(ctx, "Error in fetching vkyc attempt", zap.Error(err))
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	call, callErr := s.vkycKarzaCallInfoDao.GetLatestCallInfoByVkycAttemptId(ctx, attempt.GetId())
	if callErr != nil {
		logger.Error(ctx, "Error in fetching vkyc call info", zap.Error(err))
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if summary == nil || attempt == nil || call == nil {
		logger.Info(ctx, "Either call, attempt or summary missing for user")
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	if s.updateVkycEntities(ctx, summary, attempt, call,
		request.GetCallInfoSubStatus(), request.GetAttemptSubStatus()) != nil {
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	vkycStatus, vkycStatusErr := s.GetVKYCStatus(ctx, &vkycPb.GetVKYCStatusRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(vkycStatus, vkycStatusErr); !vkycStatus.GetStatus().IsRecordNotFound() && rpcErr != nil {
		logger.Error(ctx, "Error in fetching vkyc status", zap.Error(rpcErr))
		return &vkycPb.ReRegisterVkycInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &vkycPb.ReRegisterVkycInfoResponse{
		Status:       rpcPb.StatusOk(),
		VkycSummary:  vkycStatus.GetVkycSummary(),
		VkycAttempt:  vkycStatus.GetVkycAttempt(),
		VkycCallInfo: vkycStatus.GetVkycCallInfo(),
	}, nil
}

func getCallScheduledNotiMsg(actorId string, scheduledTime *timestamp.Timestamp, medium comms.Medium) *vkycPb2.VKYCComm {
	return &vkycPb2.VKYCComm{
		CommType: vkycPb2.CommType_COMM_TYPE_CALL_SCHEDULED,
		CommParams: &vkycPb2.VKYCComm_CallScheduleParams{
			CallScheduleParams: &vkycPb2.CallScheduledParams{
				ScheduledTime: scheduledTime,
			},
		},
		ActorId:            actorId,
		NotificationMedium: medium,
	}
}

func getScheduleMissedNotiMsg(req *actions.ActionRequest, scheduledTime *timestamp.Timestamp, slotId string) *vkycPb2.VKYCComm {
	return &vkycPb2.VKYCComm{
		CommType: vkycPb2.CommType_COMM_TYPE_SCHEDULED_CALL_MISSED,
		CommParams: &vkycPb2.VKYCComm_ScheduledCallMissed{
			ScheduledCallMissed: &vkycPb2.ScheduledCallMissed{
				ScheduledTime: scheduledTime,
				SlotId:        slotId,
			},
		},
		ActorId: req.ActorId(),
	}
}

func getScheduleReminderNotiMsg(actorId string, scheduledTime *timestamp.Timestamp, medium comms.Medium, notifyBefore int32) *vkycPb2.VKYCComm {
	return &vkycPb2.VKYCComm{
		CommType: vkycPb2.CommType_COMM_TYPE_SCHEDULED_CALL_REMINDER,
		CommParams: &vkycPb2.VKYCComm_ScheduledCallReminderParams{
			ScheduledCallReminderParams: &vkycPb2.ScheduledCallReminderParams{
				ScheduledTime: scheduledTime,
				NotifyBefore:  notifyBefore,
			},
		},
		ActorId:            actorId,
		NotificationMedium: medium,
	}
}

func (s *Service) ApproveVKYC(ctx context.Context, request *vkycPb.ApproveVKYCRequest) (*vkycPb.ApproveVKYCResponse, error) {
	vkycSummary, err := s.summaryDao.GetByActorId(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "error fetching vkyc summary for actor id", zap.Error(err))
		return &vkycPb.ApproveVKYCResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// do not proceed if status is not in review
	if vkycSummary.GetStatus() != vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
		logger.Error(ctx, fmt.Sprintf("cannot sync kyc status, vkyc summary status: %v", vkycSummary.GetStatus()))
		return &vkycPb.ApproveVKYCResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	callInfo := &model.CallInfo{UpdatedInfo: &vkycPb.VKYCKarzaCallInfo{}}
	callInfo.UpdatedInfo.Status = vkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_APPROVED
	callInfo.UpdatedInfo.SubStatus = vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AUDITOR_APPROVED
	actionReq := actions.NewActionRequest(vkycSummary.GetActorId(), commonvgpb.Vendor_KARZA)
	actionReq.SetCallInfo(callInfo)
	actionReq.SetCreateFlow(false)
	err = s.stateProcessor.TriggerVkycFlow(ctx, actionReq)
	if err != nil {
		logger.Error(ctx, "vkyc flow execution failed while approving vkyc", zap.Error(err))
		return &vkycPb.ApproveVKYCResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &vkycPb.ApproveVKYCResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// checks if given actor is internal User or not.
// returns time at when is vkyc is next available based on which string user is shown the text in non biz hours
func (s *Service) isVKYCEnabled(ctx context.Context, actorId string) (bool, bool, string, error) {

	checkHolidayResp, err := s.CheckHoliday(ctx, &vkycPb.CheckHolidayRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(checkHolidayResp, err); te != nil {
		logger.Error(ctx, "error in CheckHoliday from BE", zap.Any("checkHolidayResp", checkHolidayResp),
			zap.String(logger.ENTITY_ID, actorId), zap.Error(te))
		return false, false, "", errors.Wrap(te, "error in CheckHoliday from BE")
	}
	// allow live calls only when it is not a holidays
	liveFlowFlag := !checkHolidayResp.GetIsHoliday()
	logger.Info(ctx, fmt.Sprintf("vkyc enabled status live: %v scheduled: %v isHoliday: %v vkycNextAvailableAt: %v", liveFlowFlag, false, checkHolidayResp.GetIsHoliday(), checkHolidayResp.GetVkycNextAvailableAtDisplayText()))
	return true, liveFlowFlag, checkHolidayResp.GetVkycNextAvailableAtDisplayText(), nil
}

func (s *Service) GetVKYCInfo(ctx context.Context, request *vkycPb.GetVKYCInfoRequest) (*vkycPb.GetVKYCInfoResponse, error) {
	var (
		actorId       = request.GetActorId()
		isEkycExpired = false
	)
	// To check if vkyc does not exist or error in fetch vkyc summary
	vkycStatusResp, summaryErr := s.GetVKYCStatus(ctx, &vkycPb.GetVKYCStatusRequest{ActorId: actorId})
	if summaryErr != nil || vkycStatusResp == nil ||
		(!vkycStatusResp.GetStatus().IsSuccess() && !vkycStatusResp.GetStatus().IsRecordNotFound()) {
		logger.Error(ctx, "unable to fetch vkyc summary by actor id", zap.Error(summaryErr))
		return &vkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusInternal()}, nil
	}
	isVkycEnabled, isLiveFlowEnabled, vkycNextAvailableAt, err := s.isVKYCEnabled(ctx, actorId)
	if !isVkycEnabled || err != nil {
		logger.Info(ctx, "vkyc not enabled for user", zap.Error(err))
		return &vkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusOk()}, nil
	}
	custInfos, _ := s.vkycCustomerInfoDao.GetAllByVkycSummaryId(ctx, vkycStatusResp.GetVkycSummary().GetId())
	// check kyc status to check if its expired
	kycStatusResp, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{ActorId: actorId, IgnoreLiveness: true})
	switch {
	case err != nil:
		logger.Error(ctx, "error fetching kyc record", zap.Error(err))
		return &vkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	case !kycStatusResp.GetStatus().IsRecordNotFound() && !kycStatusResp.GetStatus().IsSuccess():
		logger.Error(ctx, "non-ok response", zap.String(logger.RPC_STATUS, kycStatusResp.GetStatus().String()))
		return &vkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusInternal()}, nil
	case s.checkIfPerformEkyc(ctx, kycStatusResp, vkycStatusResp.GetVkycSummary()):
		isEkycExpired = true
	}
	return &vkycPb.GetVKYCInfoResponse{
		Status:                 rpcPb.StatusOk(),
		IsLiveFlowEnabled:      isLiveFlowEnabled,
		IsEkycExpired:          isEkycExpired,
		VkycNextAvailableStart: vkycNextAvailableAt,
		EntryPoint:             vkycStatusResp.GetVkycCallInfo().GetCallMetadata().GetEntryPoint(),
		VkycRecord: &vkycPb.VKYCRecord{
			VkycSummary: vkycStatusResp.GetVkycSummary(),
			VkycAttemptDataList: []*vkycPb.VKYCRecord_VKYCAttemptData{
				{
					VkycAttempt: vkycStatusResp.GetVkycAttempt(),
					VkycKarzaCallInfoDataList: []*vkycPb.VKYCRecord_VKYCAttemptData_VKYCKarzaCallInfoData{
						{
							VkycKarzaCallInfo: vkycStatusResp.GetVkycCallInfo(),
						},
					},
				},
			},
			VkycKarzaCustomerInfos: custInfos,
		},
		KycFailureType:      kycStatusResp.GetFailureReason(),
		VkycStatusResponse:  vkycStatusResp,
		IntroScreenDeeplink: getVKYCIntroScreenV2Deeplink(ctx, request.GetEntryPoint(), s.conf, vkycNextAvailableAt),
	}, nil
}

func (s *Service) getNotiMsg(ctx context.Context, request *vkycPb.SendVKYCNotificationRequest) (*vkycPb2.VKYCComm, time.Duration, error) {
	var delayDuration = time.Duration(0)
	notiReason := request.GetNotificationReason()
	switch notiReason {
	case vkycPb.NotificationReason_NOTIFICATION_REASON_OUT_OF_BUSINESS_HOURS:
		checkHolidayResp, err := s.CheckHoliday(ctx, &vkycPb.CheckHolidayRequest{ActorId: request.GetActorId()})
		if te := epifigrpc.RPCError(checkHolidayResp, err); te != nil {
			logger.Error(ctx, "error in CheckHoliday from BE", zap.Any("checkHolidayResp", checkHolidayResp),
				zap.String(logger.ENTITY_ID, request.GetActorId()), zap.Error(te))
			return nil, delayDuration, te
		}
		vkycNextAvlAt := checkHolidayResp.GetVkycNextAvailableStart()
		// don't send notification if timestamp is not valid OR next avl time is before current time(not expected to occur)
		if !vkycNextAvlAt.IsValid() || vkycNextAvlAt.AsTime().Before(time.Now()) {
			return nil, delayDuration, nil
		}
		// adding 1 minute since if vkyc is off till 12.40, we need to send PN by 12.41
		delayDuration = time.Until(vkycNextAvlAt.AsTime().Add(1 * time.Minute))
		logger.Debug(ctx, "delayDuration for vkyc available", zap.Float64("delayDuration", delayDuration.Minutes()))
		return &vkycPb2.VKYCComm{
			CommType:           vkycPb2.CommType_COMM_TYPE_VKYC_AVAILABLE,
			ActorId:            request.GetActorId(),
			NotificationMedium: comms.Medium_NOTIFICATION,
		}, delayDuration, nil
	default:
		return nil, delayDuration, errors.Errorf("received unexpected notification reason %v", notiReason)
	}
}

func (s *Service) SendVKYCNotification(ctx context.Context, request *vkycPb.SendVKYCNotificationRequest) (*vkycPb.SendVKYCNotificationResponse, error) {
	var (
		actorId = request.GetActorId()
	)
	notiMsg, delayDuration, err := s.getNotiMsg(ctx, request)
	if err != nil {
		logger.Error(ctx, "error in sending PN", zap.Error(err))
		return &vkycPb.SendVKYCNotificationResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if notiMsg == nil {
		logger.Info(ctx, "no notification to be sent")
		return &vkycPb.SendVKYCNotificationResponse{Status: rpcPb.StatusOk()}, nil
	}
	msgId, err := s.notification.SendNotification(ctx, notiMsg, delayDuration)
	if err != nil {
		logger.Error(ctx, "error in sending PN", zap.Error(err))
		return &vkycPb.SendVKYCNotificationResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if _, err := s.vkycNotificationDao.Create(ctx, &vkycPb.VKYCNotification{
		ActorId:        actorId,
		CommsMessageId: msgId,
		NotificationMetadata: &vkycPb.NotificationMetadata{
			NotificationMedium:   comms.Medium_NOTIFICATION,
			PushNotificationType: notiMsg.GetCommType(),
		},
	}); err != nil {
		logger.Error(ctx, "error creating entry in vkyc notification", zap.Error(err))
		return &vkycPb.SendVKYCNotificationResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &vkycPb.SendVKYCNotificationResponse{Status: rpcPb.StatusOk()}, nil
}

func (s *Service) UpsertCallFailureCount(ctx context.Context, req *vkycPb.UpsertCallFailureCountRequest) (*vkycPb.UpsertCallFailureCountResponse, error) {
	if req.GetFailureReason() == vkycPb.FailureReason_FAILURE_REASON_UNSPECIFIED || req.GetActorId() == "" {
		logger.Info(ctx, "Empty reason and actor id received from client")
		return &vkycPb.UpsertCallFailureCountResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Either reason or actor id empty"),
		}, nil
	}
	redisKey := getRedisKey(req.GetActorId(), req.GetFailureReason())
	val, err := s.getValueFromRedis(ctx, redisKey)
	if err != nil {
		return &vkycPb.UpsertCallFailureCountResponse{
			Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
		}, nil
	}

	now := time.Now().In(time.UTC)
	cacheValidTill := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, time.UTC)

	err = s.vkycCacheStorage.Set(ctx, redisKey, strconv.Itoa(val+1), cacheValidTill.Sub(now))
	if err != nil {
		logger.Error(ctx, "Error in updating cache", zap.Error(err))
		return &vkycPb.UpsertCallFailureCountResponse{
			Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
		}, nil
	}

	var failureReasonCount []*vkycPb.FailureReasonCount
	for _, failureReasonVal := range vkycPb.FailureReason_name {
		failureReason := vkycPb.FailureReason(vkycPb.FailureReason_value[failureReasonVal])
		if failureReason == vkycPb.FailureReason_FAILURE_REASON_UNSPECIFIED {
			continue
		}
		redisKey = getRedisKey(req.GetActorId(), failureReason)
		val, err = s.getValueFromRedis(ctx, redisKey)
		if err != nil {
			return &vkycPb.UpsertCallFailureCountResponse{
				Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
			}, nil
		}
		failureReasonCount = append(failureReasonCount, &vkycPb.FailureReasonCount{
			Count:         int32(val),
			FailureReason: failureReason,
		})
	}
	return &vkycPb.UpsertCallFailureCountResponse{
		Status:             rpcPb.StatusOk(),
		FailureReasonCount: failureReasonCount,
	}, nil
}

func getRedisKey(actorId string, failureReason vkycPb.FailureReason) string {
	return fmt.Sprintf("%v%v%v", vkycFailureKeyPrefix, actorId, failureReason)
}

func (s *Service) getValueFromRedis(ctx context.Context, redisKey string) (int, error) {
	strVal, err := s.vkycCacheStorage.Get(ctx, redisKey)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Error in fetching redis key", zap.Error(err))
		return 0, err
	}

	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return 0, nil
	}

	val, err := strconv.Atoi(strVal)
	if err != nil {
		logger.Error(ctx, "Error in parsing string to int", zap.Error(err))
		return 0, err
	}
	return val, nil
}

func (s *Service) sendCallFailNotification(ctx context.Context, callInfo *model.CallInfo, actorId string) error {
	if !common.IsBusinessHours(time.Now().In(datetime.IST), s.conf.VKYC().VkycStartHour(), s.conf.VKYC().VkycEndHour()) {
		logger.Error(ctx, "not sending PN in non-business hours")
		return errors.New("not sending PN in non-business hours")
	}
	callSubStatus := callInfo.GetInfo().GetSubStatus()
	callInfoId := callInfo.GetInfo().GetId()
	commType, ok := CallFailSubStatusToCommType[callSubStatus]
	if !ok {
		logger.Error(ctx, "CallFailSubStatusToCommType not found", zap.String("callSubStatus", callSubStatus.String()))
		return errors.New(fmt.Sprintf("CallFailSubStatusToCommType not found, callSubStatus: %v", callSubStatus.String()))
	}
	vkycNotiResp, err := s.vkycNotificationDao.GetByActorId(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error getting vkyc notification by actorId", zap.Error(err))
		return err
	}
	for _, resp := range vkycNotiResp {
		// return if notification was already sent
		if resp.GetNotificationMetadata().GetPushNotificationType() == commType &&
			resp.GetNotificationMetadata().GetReferenceId() == callInfoId {
			logger.Info(ctx, "PN already sent", zap.String("callInfoId", callInfoId), zap.String("commType", commType.String()))
			// PN already sent not considered as error
			return nil
		}
	}
	msgId, err := s.notification.SendNotification(ctx, &vkycPb2.VKYCComm{
		CommType: commType,
		CommParams: &vkycPb2.VKYCComm_CallFailedParams{
			CallFailedParams: &vkycPb2.CallFailedParams{
				Title: CallFailSubStatusToPNTitleMap[callSubStatus],
				Body:  CallFailSubStatusToPNBodyMap[callSubStatus],
			},
		},
		ActorId:            actorId,
		NotificationMedium: comms.Medium_NOTIFICATION,
	}, 0)
	if err != nil {
		logger.Error(ctx, "error in sending PN", zap.Error(err))
		return err
	}
	if _, err := s.vkycNotificationDao.Create(ctx, &vkycPb.VKYCNotification{
		ActorId:        actorId,
		CommsMessageId: msgId,
		NotificationMetadata: &vkycPb.NotificationMetadata{
			NotificationMedium:   comms.Medium_NOTIFICATION,
			PushNotificationType: commType,
			ReferenceId:          callInfoId,
		},
	}); err != nil {
		logger.Error(ctx, "error creating entry in vkyc notification", zap.Error(err))
		return err
	}
	logger.Info(ctx, "call fail notification processed successfully")
	return nil
}

func (s *Service) triggerAlertIfUserStuckInReview(ctx context.Context, vkycSummary *vkycPb.VKYCSummary) error {
	if vkycSummary.GetStatus() != vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
		return nil
	}
	// check if user is stuck in review for more than 3(stuckAlertDuration) days
	if time.Since(vkycSummary.GetUpdatedAt().AsTime()) >= stuckAlertDuration {
		// check if alert is already logged for user stuck in review state for more than 3 days using taskString
		// taskString maintains unique concat between actor and duration as of now
		taskString := fmt.Sprintf("%v stuck in review state for more than %v days", vkycSummary.GetActorId(), stuckAlertDuration.Hours()/24)
		// alertFunc is executed only taskString is present in db
		alertFunc := func() error {
			alertString := fmt.Sprintf("stuck in review state for more than %v days", stuckAlertDuration.Hours()/24)
			logger.Info(ctx, alertString, zap.String(logger.ACTOR_ID, vkycSummary.GetActorId()))
			return nil
		}
		if err := s.doOnce.DoOnceFn(ctx, taskString, alertFunc); err != nil {
			return fmt.Errorf("error in doing task %w", err)
		}
	}
	return nil
}

// checkIfUnsupportedWebview returns true for more than callFailUnSupportedWebviewThreshold calls fail on same day excluding some call failure reasons
func (s *Service) checkIfUnsupportedWebview(ctx context.Context, actionReq *actions.ActionRequest) bool {
	callInfos, err := s.svcProvider.GetCallInfos(ctx, actionReq.Summary().GetId())
	if err != nil {
		logger.Error(ctx, "unable to fetch callinfos", zap.Error(err))
		return false
	}
	// counter map
	callFailUnsupportedWebviewCountMap := make(map[vkycPb.VKYCKarzaCallInfoSubStatus]int8)
	for _, callInfo := range callInfos {
		if checkIfConsiderCallFailureForUnsupportedWebview(callInfo) {
			callFailUnsupportedWebviewCountMap[callInfo.GetSubStatus()]++
			// returning true if callFailUnsupportedWebviewCountMap(Current counter map) >= callFailureForUnsupportedWebviewMap(original map)
			if callFailUnsupportedWebviewCountMap[callInfo.GetSubStatus()] >= callFailureForUnsupportedWebviewMap[callInfo.GetSubStatus()] {
				logger.Info(ctx, "unsupported webview due to call failure", zap.String("subStatus", callInfo.GetSubStatus().String()))
				return true
			}
		}
	}
	return false
}

// checkIfConsiderCallFailureForUnsupportedWebview returns true if call failure is to be considered(map value > 0 indicating presence in map) and happened on same day.
// NOTE: same day check is ignored for INCOMPATIBLE_CUSTOMER_DEVICE
func checkIfConsiderCallFailureForUnsupportedWebview(callInfo *vkycPb.VKYCKarzaCallInfo) bool {
	return callFailureForUnsupportedWebviewMap[callInfo.GetSubStatus()] > 0 &&
		(time.Now().Day() == callInfo.GetCreatedAt().AsTime().Day() || callInfo.GetSubStatus() == vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE)

}

func (s *Service) getVkycNextAvailableAtText(ctx context.Context, vkycNextAvailableAt *timestamp.Timestamp, isHoliday bool) string {
	if !isHoliday {
		return ""
	}
	if vkycNextAvailableAt == nil || vkycNextAvailableAt.AsTime().Before(time.Now()) {
		logger.Info(ctx, "error getting vkycNextAvailableAt on holiday",
			zap.Bool("nextNotFound", vkycNextAvailableAt == nil),
			zap.String("incorrectTime", vkycNextAvailableAt.AsTime().In(datetime.IST).String()))
		return defaultVkycNextAvailableAt
	}
	dayDisplayText := s.getDayDisplayText(vkycNextAvailableAt)
	switch {
	case strings.HasPrefix(dayDisplayText, "Today"):
		dayDisplayText = fmt.Sprintf("%v today after %v", vkycNextAvailableAtTimeText, vkycNextAvailableAt.AsTime().In(datetime.IST).Format("03:04 PM"))
	case strings.HasPrefix(dayDisplayText, "Tomorrow"):
		dayDisplayText = fmt.Sprintf("%v tomorrow after %v", vkycNextAvailableAtTimeText, vkycNextAvailableAt.AsTime().In(datetime.IST).Format("03:04 PM"))
	default:
		dayDisplayText = fmt.Sprintf("%v on %v after %v", vkycNextAvailableAtTimeText, dayDisplayText, vkycNextAvailableAt.AsTime().In(datetime.IST).Format("03:04 PM"))
	}
	return dayDisplayText
}

// get UI display text for day
func (s *Service) getDayDisplayText(slotTimeStamp *timestamppb.Timestamp) string {
	dayMonth := slotTimeStamp.AsTime().In(datetime.IST).Format("02 Jan")
	// handle for today
	if slotTimeStamp.AsTime().In(datetime.IST).Day() == timestamppb.Now().AsTime().In(datetime.IST).Day() {
		return "Today, " + dayMonth
	}
	// handle for tomorrow
	if slotTimeStamp.AsTime().In(datetime.IST).Day() == timestamppb.Now().AsTime().In(datetime.IST).Day()+1 {
		return "Tomorrow, " + dayMonth
	}
	// rest of the cases
	switch slotTimeStamp.AsTime().In(datetime.IST).Weekday() {
	case time.Monday:
		return "Monday, " + dayMonth
	case time.Tuesday:
		return "Tuesday, " + dayMonth
	case time.Wednesday:
		return "Wednesday, " + dayMonth
	case time.Thursday:
		return "Thursday, " + dayMonth
	case time.Friday:
		return "Friday, " + dayMonth
	case time.Saturday:
		return "Saturday, " + dayMonth
	case time.Sunday:
		return "Sunday, " + dayMonth
	}
	// Not returning error since getting error in this is not practically possible
	return ""
}

// checkIfPerformEkyc return true IF
// - current time is past kyc expiry duration since last ekyc
// - kyc record was not found
func (s *Service) checkIfPerformEkyc(ctx context.Context, kycStatusResp *kyc.CheckKYCStatusResponse, vkycSummary *vkycPb.VKYCSummary) bool {
	if isVkycInTerminalState(vkycSummary) {
		logger.Info(ctx, "vkyc in terminal state", zap.String("vkysSummaryStatus", vkycSummary.GetStatus().String()))
		return false
	}
	// createdAt field from KycRecord is the time at which the KYC record was stored in the KYC Vendor Data entity.
	// adding "PerformEkycAfter" duration to createdAt that indicates time after which ekyc should be performed
	performEkycAfter := kycStatusResp.GetCreatedAt().AsTime().Add(s.conf.VKYC().PerformEkycAfter())
	performEkycCheck := kycStatusResp.GetStatus().IsRecordNotFound() || kycStatusResp.GetKycStatus() == kyc.KycStatus_EXPIRED || kycStatusResp.GetKycState() != kyc.KYCState_EKYC_DATA_RECEIVED ||
		(kycStatusResp.GetCreatedAt().IsValid() && time.Now().After(performEkycAfter)) || kycStatusResp.GetStatus().IsPermissionDenied() || kycStatusResp.GetKycType() == kyc.KycType_CKYC ||
		time.Now().After(kycStatusResp.GetExpiry().AsTime())
	logger.Info(ctx, fmt.Sprintf("check if perform ekyc performEkycCheck: %v", performEkycCheck),
		zap.String(logger.RPC_STATUS, kycStatusResp.GetStatus().String()),
		zap.String("createdAt", kycStatusResp.GetCreatedAt().AsTime().String()),
		zap.Bool("isCreatedAtValid", kycStatusResp.GetCreatedAt().IsValid()),
		zap.Time("performEkycAfter", performEkycAfter),
		zap.String("kycState", kycStatusResp.GetKycState().String()),
		zap.String(logger.KYC_TYPE, kycStatusResp.GetKycType().String()))
	return performEkycCheck
}

func (s *Service) checkIfCustomerDeviceIncompatible(ctx context.Context, actionReq *actions.ActionRequest) bool {
	vkycAttemptList, err := s.vkycAttemptDao.GetAttemptsByVkycSummaryId(ctx, actionReq.Summary().GetId())
	if err != nil {
		logger.Error(ctx, "failed to fetch vkyc attempts by summary id", zap.Error(err))
	}
	// counter map
	callFailIncompatibleDeviceCountMap := make(map[vkycPb.VKYCKarzaCallInfoSubStatus]int8)
	for _, attempt := range vkycAttemptList {
		callInfoList, err := s.vkycKarzaCallInfoDao.GetCallsByVkycAttemptId(ctx, attempt.GetId())
		if err != nil {
			logger.Error(ctx, "failed to fetch vkyc call info by attempt id", zap.Error(err))
		}
		for _, callInfo := range callInfoList {
			callFailIncompatibleDeviceCountMap[callInfo.GetSubStatus()]++
			// returning true if callFailIncompatibleDeviceCountMap(Current counter map) >= incompatibleDeviceMap(original map)
			// checking if incompatibleDeviceMap[callInfo.GetSubStatus()] > 0 to validate the presence of the substatus in the map
			if incompatibleDeviceMap[callInfo.GetSubStatus()] > 0 &&
				callFailIncompatibleDeviceCountMap[callInfo.GetSubStatus()] >= incompatibleDeviceMap[callInfo.GetSubStatus()] {
				logger.Info(ctx, "showing weblink to the user", zap.String("subStatus", callInfo.GetSubStatus().String()))
				return true
			}
		}
	}
	return false
}

func isVkycInTerminalState(vkycSummary *vkycPb.VKYCSummary) bool {
	return vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW
}

func isUserVkycRegistered(vkycSummary *vkycPb.VKYCSummary) bool {
	return vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_PROGRESS ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW
}

func (s *Service) UpdateCallInfo(ctx context.Context, request *vkycPb.UpdateCallInfoRequest) (*vkycPb.UpdateCallInfoResponse, error) {
	entryPoint := goutils.Enum(request.GetEntryPointStr(), vkycPb.EntryPoint_value, vkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED)
	actionReq := actions.NewActionRequest(request.GetActorId(), s.vendor)
	actionReq.SetCreateFlow(true)
	actionReq.SetAttemptReason(getAttemptReason(ctx, request.GetActorId(), s.onbClient, s.bcClient))
	err := s.stateProcessor.TriggerVkycFlow(ctx, actionReq)
	if err != nil {
		logger.Error(ctx, "failed to perform live call flow", zap.String("actor_id", request.GetActorId()), zap.Error(err))
		goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewStartedVKYCFlowBE(request.GetActorId(), actionReq.CallInfo().GetInfo().GetId(),
				"LIVE", events2.Failure, "failed to perform live call flow"+err.Error()))
		})
		return &vkycPb.UpdateCallInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	validateError, userCountry := s.validateAndUpdateLocation(ctx, request.GetLocationToken(), request.GetActorId(), actionReq, entryPoint)
	// return error only if it is handled on client end
	if validateError != nil {
		return &vkycPb.UpdateCallInfoResponse{
			Status: updateCallStatus(validateError, userCountry),
		}, nil
	}

	summary, err := s.summaryDao.GetByActorId(ctx, request.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.UpdateCallInfoResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error fetching vkyc summary", zap.Error(err))
		return &vkycPb.UpdateCallInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	attempt, err := s.vkycAttemptDao.GetLatestAttemptByVkycSummaryId(ctx, summary.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching latest vkyc attempt", zap.Error(err))
		return &vkycPb.UpdateCallInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	callInfo, err := s.svcProvider.GetLatestCallInfo(ctx, attempt.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching latest call info", zap.String("actor_id", request.GetActorId()), zap.Error(err))
		return &vkycPb.UpdateCallInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if callInfo.GetInfo().GetCallMetadata() == nil {
		callInfo.GetInfo().CallMetadata = &vkycPb.VKYCKarzaCallInfoCallMetadata{}
	}
	// populate metadata if they've zero values
	if callInfo.GetInfo().GetCallMetadata().GetEntryPoint() == vkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED {
		callInfo.GetInfo().GetCallMetadata().EntryPoint = request.GetCallInfoMetadata().GetEntryPoint()
	}
	if callInfo.GetInfo().GetCallMetadata().GetWebviewVersion() == "" {
		callInfo.GetInfo().GetCallMetadata().WebviewVersion = request.GetCallInfoMetadata().GetWebviewVersion()
	}
	if callInfo.GetInfo().GetCallMetadata().GetBrowserVersion() == "" {
		callInfo.GetInfo().GetCallMetadata().BrowserVersion = request.GetCallInfoMetadata().GetBrowserVersion()
	}

	err = s.vkycKarzaCallInfoDao.UpdateById(ctx, callInfo.GetInfo(), []vkycPb.VKYCKarzaCallInfoFieldMask{vkycPb.VKYCKarzaCallInfoFieldMask_VKYC_KARZA_CALL_INFO_CALL_METADATA})
	if err != nil {
		logger.Error(ctx, "error updating callinfo", zap.Error(err))
		return &vkycPb.UpdateCallInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &vkycPb.UpdateCallInfoResponse{
		Status: rpcPb.StatusOk(),
		NextAction: vkyc.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
			EntryPoint:      request.GetCallInfoMetadata().GetEntryPoint().String(),
			ClientLastState: vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UPDATE_CALL_INFO.String(),
			ShowCtaLoader:   true,
		}),
	}, nil
}

func getAttemptReason(ctx context.Context, actorId string, onbClient onboarding.OnboardingClient, bcClient bankcust.BankCustomerServiceClient) vkycPb.VKYCAttemptReason {
	if lo.Contains(vkyc.LSOVKYCActors, actorId) {
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_LSO_USER
	}
	if lo.Contains(vkyc.ForceVKYCActors, actorId) {
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_FORCE_VKYC
	}
	if lo.Contains(vkyc.ReAttemptVKYC, actorId) {
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_REASON_REATTEMPT_VKYC
	}
	bcResp, respErr := bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, respErr); rpcErr != nil && !bcResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in getting bank customer", zap.Error(rpcErr))
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_UNSPECIFIED
	}
	if lo.Contains(vkyc.DowngradedUsersList, bcResp.GetBankCustomer().GetVendorCustomerId()) {
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_DOWNGRADED_TO_MIN_KYC
	}
	resp, errResp := onbClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
		ActorId:    actorId,
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching onboarding details", zap.Error(err))
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_UNSPECIFIED
	}
	switch resp.GetDetails().GetFeature() {
	case onboarding.Feature_FEATURE_CC:
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_CREDIT_CARD
	case onboarding.Feature_FEATURE_PL:
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_LENDING
	case onboarding.Feature_FEATURE_SA:
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_SAVINGS_ACCOUNT
	default:
		return vkycPb.VKYCAttemptReason_VKYC_ATTEMPT_REASON_UNSPECIFIED

	}
}

func (s *Service) AddUserToPriority(ctx context.Context, request *vkycPb.AddUserToPriorityRequest) (*vkycPb.AddUserToPriorityResponse, error) {
	vkycResp, err := s.GetVKYCSummary(ctx, &vkycPb.GetVKYCSummaryRequest{
		ActorId: request.GetActorId(),
	})
	switch {
	case err != nil:
		logger.ErrorNoCtx("error in getting vkyc record from kyc service", zap.String(logger.ACTOR_ID, request.GetActorId()), zap.Error(err))
		return &vkycPb.AddUserToPriorityResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	case !vkycResp.GetStatus().IsSuccess() && !vkycResp.GetStatus().IsRecordNotFound():
		logger.ErrorNoCtx("error in getting vkyc record from kyc service", zap.String(logger.ACTOR_ID, request.GetActorId()), zap.Error(err))
		return &vkycPb.AddUserToPriorityResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	vkycSummary := vkycResp.GetVkycRecord().GetVkycSummary()
	if isVkycInTerminalState(vkycSummary) {
		logger.Info(ctx, "not adding user to priority since user vkyc in terminal state")
		return &vkycPb.AddUserToPriorityResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("not adding user to priority since user vkyc in terminal state"),
		}, nil
	}

	actionReq := actions.NewActionRequest(vkycSummary.GetActorId(), commonvgpb.Vendor_KARZA)
	actionReq.SetIsPriorityChangeFlow(true)
	actionReq.SetCreateFlow(false)
	err = s.stateProcessor.TriggerVkycFlow(ctx, actionReq)
	if err != nil {
		logger.Error(ctx, "vkyc flow execution failed", zap.Error(err))
		return &vkycPb.AddUserToPriorityResponse{Status: rpcPb.StatusInternal()}, nil
	}
	priorityAddResp, err := s.vkycPriority.Add(ctx, request.GetForceAdd(), &vkyc_priority.VKYCPriorityUser{
		ActorId:    request.GetActorId(),
		InsertedAt: timestamppb.Now(),
	})
	if err != nil {
		logger.Error(ctx, "error adding user to vkyc priority", zap.Error(err))
		return &vkycPb.AddUserToPriorityResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if priorityAddResp.VkycPriorityUserType == vkycPb.VKYCPriorityType_VKYC_PRIORITY_TYPE_UNSPECIFIED {
		logger.Info(ctx, "user added to priority", zap.String("priorityType", priorityAddResp.VkycPriorityUserType.String()))
		return &vkycPb.AddUserToPriorityResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// IF call not in terminal status -> set vkyc summary, attempt amd call status to failed for user to be able to register with new data
	return &vkycPb.AddUserToPriorityResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// checkIfIncomeOccupationIsSame returns true if-
// annual salary absolute values is same AND
// employment type is same
func (s *Service) checkIfIncomeOccupationIsSame(ctx context.Context, vkycSummaryId string, userData *vkycPb.UserData) (bool, *vkycPb.VKYCKarzaCustomerInfo) {
	vkycKarzaCustomerInfo, err := s.vkycCustomerInfoDao.GetByVkycSummaryId(ctx, vkycSummaryId)
	if err != nil {
		return false, nil
	}
	// comparing int instead of float since we pass int values to karza
	return int(vkycKarzaCustomerInfo.GetTransactionMetadata().GetAnnualSalary().GetAbsolute()) == int(userData.GetAnnualSalary().GetAbsolute()) &&
		vkycKarzaCustomerInfo.GetTransactionMetadata().GetEmploymentType() == userData.GetEmploymentType(), vkycKarzaCustomerInfo
}

// updates location on karza end
//
//nolint:funlen
func (s *Service) updateLocationOrCif(ctx context.Context, locationToken string, summaryId string, actorId string, isLocationUpdate bool) error {
	locationResp := &location.GetCoordinatesResponse{}
	var (
		locationErr     error
		isEpanAvailable = false
		epanAttempt     *epan.EPANAttempt
		epanErr         error
	)
	if isLocationUpdate {
		locationResp, locationErr = s.locationClient.GetCoordinates(ctx, &location.GetCoordinatesRequest{
			LocationToken: locationToken,
		})
		if te := epifigrpc.RPCError(locationResp, locationErr); te != nil {
			logger.Error(ctx, "Error in fetching coordinates during vkyc", zap.Error(te))
			return te
		}
	}
	// fetch customer info to get user transaction id
	vkycKarzaCustomerInfo, vkycKarzaCustomerErr := s.vkycCustomerInfoDao.GetByVkycSummaryId(ctx, summaryId)
	if vkycKarzaCustomerErr != nil {
		logger.Error(ctx, "Error in fetching customer info during location update", zap.Error(vkycKarzaCustomerErr))
		return vkycKarzaCustomerErr
	}

	kycResp, err := s.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(kycResp, err); te != nil {
		if !rpcPb.StatusFromError(te).IsRecordNotFound() {
			logger.Error(ctx, "Error in fetching kyc record", zap.Error(te))
		}
		return te
	}

	actorResp, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		logger.Error(ctx, "Error in fetching actor record", zap.Error(te))
		return te
	}
	userResp, err := s.userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_Id{
			Id: actorResp.GetActor().GetEntityId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		logger.Error(ctx, "Error in fetching user record", zap.Error(te))
		return te
	}

	vkycTypeResp, respErr := s.GetVKYCType(ctx, &vkycPb.GetVKYCTypeRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(vkycTypeResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting vkyc type", zap.Error(rpcErr))
		return rpcErr
	}

	custId := vkycTypeResp.GetVendorMetadata().GetKarzaMetadata().GetCustomerId()

	isEpanAvailable, epanAttempt, epanErr = stateprocessor.IsEPanAvailable(ctx, actorId, s.summaryDao, s.panClient)
	if epanErr != nil {
		return epanErr
	}

	userData := &vkycPb.UserData{
		CustomerId:       custId,
		UidReferenceKey:  vkyc.GetVKYCUidReferenceKey(kycResp, vkycKarzaCustomerInfo.GetTransactionMetadata().GetEkycRrnNo()),
		Name:             vkyc.GetVKYCName(kycResp, userResp.GetUser()),
		PermanentAddress: vkyc.GetVKYCAddress(kycResp),
		CurrentAddress:   vkyc.GetVKYCAddress(kycResp),
		FatherName:       userResp.GetUser().GetProfile().GetFatherName(),
		MotherName:       userResp.GetUser().GetProfile().GetMotherName(),
	}

	err = s.svcProvider.UpdateCustomerDetails(ctx, actorId, vkycKarzaCustomerInfo, locationResp.GetLatLng(), userData, isLocationUpdate, isEpanAvailable, epanAttempt)
	if err != nil {
		logger.Error(ctx, "error in updating location", zap.Error(err))
		return err
	}
	return nil
}

// nolint: funlen
func (s *Service) GetUIElements(ctx context.Context, actorId string, nudgeTypes []vkycPb.UIElement) ([]*nudges.NudgeContent, error) {
	if actorId == "" {
		return nil, fmt.Errorf("actor id is mandatory field")
	}
	var (
		staleAccountBalance = &money.Money{}
	)
	vkycDetail := &nudges.VkycDetail{}
	cacheKey := vkyc.GetVKYCApprovedRedisKey(actorId)
	_, cacheErr := s.vkycCacheStorage.Get(ctx, cacheKey)
	switch {
	case errors.Is(cacheErr, epifierrors.ErrRecordNotFound):
		logger.Debug(ctx, fmt.Sprintf("no record exist in cache for the key %v", cacheKey))
	case cacheErr != nil:
		logger.Error(ctx, fmt.Sprintf("error while getting cached data in getUIElements for key %v", cacheKey), zap.Error(cacheErr))
	default:
		vkycDetail = &nudges.VkycDetail{
			KycLevel:     kyc.KYCLevel_FULL_KYC,
			ActorId:      actorId,
			VKYCApproved: true,
		}
		return s.getVkycNudge(ctx, nudgeTypes, vkycDetail)
	}

	bcResp, bcErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(bcResp, bcErr); te != nil {
		if !rpcPb.StatusFromError(te).IsRecordNotFound() {
			logger.Error(ctx, "error in getting the bank customer", zap.Error(te))
		}
		if rpcPb.StatusFromError(te).IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error in getting bank customer: %w", te)
	}
	// If the user is full KYC && user is not part of FORCE VKYC set of actors, early return.
	// This can be driven via user group mapping. Keeping it in a constant file for now to not bloat up DB calls.
	if bcResp.GetBankCustomer().GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC && !(vkyc.IsAllowVKYCForFullKyc(actorId)) {
		return nil, nil
	}
	vkycInfo, vkycInfoErr := s.vkycMockable.GetVKYCInfo(ctx, &vkycPb.GetVKYCInfoRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(vkycInfo, vkycInfoErr); te != nil {
		logger.Error(ctx, "unable to fetch vkyc info by actor id", zap.Error(te))
		return nil, te
	}

	// if vkyc failure reason is in terminal failure and user not allow to re-register then we won't show vkyc nudge
	if lo.Contains[vkycPb.VKYCKarzaCallInfoSubStatus](vkyc.TerminalFailureReasons, vkyc.GetLatestVKYCCallInfo(vkycInfo).GetSubStatus()) &&
		!vkyc.CheckIfReRegister(vkycInfo) {
		logger.Info(ctx, "disabling vkyc nudge since user previous call failure is terminal failure")
		return nil, epifierrors.ErrRecordNotFound
	}
	vkycLandingDLRes, vkycLandingDLErr := s.GetVKYCLandingDL(ctx, &vkycPb.GetVKYCLandingDLRequest{})
	if rpcErr := epifigrpc.RPCError(vkycLandingDLRes, vkycLandingDLErr); rpcErr != nil {
		logger.Error(ctx, "error in fetching vkyc landing deeplink", zap.Error(vkycLandingDLErr))
		return nil, fmt.Errorf("error in fetching vkyc landing deeplink")
	}
	vkycInstructionDLRes, vkycInstructionDLErr := s.GetVKYCInstructionDL(ctx, &vkycPb.GetVKYCInstructionDLRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(vkycInstructionDLRes, vkycInstructionDLErr); rpcErr != nil {
		logger.Error(ctx, "error in fetching vkyc instruction deeplink", zap.Error(vkycLandingDLErr))
		return nil, fmt.Errorf("error in fetching vkyc landing deeplink")
	}

	kycLevel := bcResp.GetBankCustomer().GetKycInfo().GetKycLevel()
	vkycUpdatedAt := vkycInfo.GetVkycStatusResponse().GetVkycSummary().GetUpdatedAt()
	cifSucceededAt := bcResp.GetBankCustomer().GetVendorCreationSucceededAt()
	// getUIElement should be called when used reached home, and customer is created
	if bcResp.GetBankCustomer().GetStatus() != bankcust.Status_STATUS_ACTIVE {
		logger.Info(ctx, "customer is not active yet")
		return nil, epifierrors.ErrRecordNotFound
	}

	// it indicate bkyc flow is in progress, hence disabling the vkyc entry point
	if kycLevel == kyc.KYCLevel_MIN_KYC &&
		bcResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetKycUpgradeInfo().GetStatus() != bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_UNSPECIFIED {
		return nil, epifierrors.ErrRecordNotFound
	}

	accFreezeOnDate := timestamppb.New(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1 * 24 * time.Hour))
	fundTransferEligibleOnLastDay := timestamppb.New(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1 * vkycConstant2 * 24 * time.Hour))
	fundTransferEligibleOnLastThirdDay := timestamppb.New(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1 * vkycConstant4 * 24 * time.Hour))
	accountCloseEligibleInLast30Days := timestamppb.New(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1 * vkycConstant31 * 24 * time.Hour))
	popupAccountFreezeThreshold := timestamppb.New(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1 * vkycConstant180 * 24 * time.Hour))
	accFreezeDaysRemaining := math.Ceil(time.Until(cifSucceededAt.AsTime().Add(vkyc.AccountClosureTimeLimit).Add(-1*24*time.Hour)).Hours() / 24)

	if !vkyc.IsAllowVKYCForFullKyc(actorId) && cifSucceededAt.IsValid() && time.Now().After(accFreezeOnDate.AsTime()) {
		logger.Info(ctx, "account deemed to frozen, not showing nudge to user")
		return nil, nil
	}

	logger.Debug(ctx, fmt.Sprintf("Cif creation succeeded at %v", cifSucceededAt.AsTime()))

	targetedCommsMappingResp, targetedCommsMappingErr := s.inAppTargetedCommsClient.GetTargetedCommsMappings(ctx, &tcPb.GetTargetedCommsMappingsRequest{
		MappingDetails: &tcPb.MappingDetails{
			MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
			MappedValue: actorId,
		},
	})

	if rpcErr := epifigrpc.RPCError(targetedCommsMappingResp, targetedCommsMappingErr); !rpcPb.StatusFromError(rpcErr).IsRecordNotFound() && rpcErr != nil {
		logger.Error(ctx, "received unexpected response from GetTargetedCommsMappings api", zap.Error(rpcErr))
		return nil, rpcErr
	}

	vkycDetail = &nudges.VkycDetail{
		VkycUpdatedAt:                      vkycUpdatedAt,
		CifSucceededAt:                     cifSucceededAt,
		AccFreezeOn:                        accFreezeOnDate,
		FundTransferEligibleOnLastDay:      fundTransferEligibleOnLastDay,
		FundTransferEligibleOnLastThirdDay: fundTransferEligibleOnLastThirdDay,
		KycLevel:                           kycLevel,
		AccountCloseInLast30Days:           accountCloseEligibleInLast30Days,
		PopupAccountFreezeThreshold:        popupAccountFreezeThreshold,
		Mappings:                           targetedCommsMappingResp.GetTargetedCommsMappingList(),
		LandingDeeplink:                    vkycLandingDLRes.GetDeeplink(),
		AccFreezeDaysRemaining:             accFreezeDaysRemaining,
		InstructionDeeplink:                vkycInstructionDLRes.GetDeeplink(),
		MaxAllowedSavingsLimitAmount:       minKycMaxSavingsBalance.GetUnits(),
		StaleBalance:                       staleAccountBalance,
		VkycInfoResp:                       vkycInfo,
		InAppTargetedCommsClient:           s.inAppTargetedCommsClient,
		ActorId:                            actorId,
		PerformEkycAfter:                   s.conf.VKYC().PerformEkycAfter(),
		VKYCApproved:                       vkycInfo.GetVkycRecord().GetVkycSummary().GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED,
	}

	return s.getVkycNudge(ctx, nudgeTypes, vkycDetail)
}

func (s *Service) getVkycNudge(ctx context.Context, nudgeTypes []vkycPb.UIElement, vkycDetail *nudges.VkycDetail) ([]*nudges.NudgeContent, error) {
	nudgeContentList := make([]*nudges.NudgeContent, 0)

	var nudgeToProcMap = map[vkycPb.UIElement]nudges.NudgeProcType{
		vkycPb.UIElement_UI_ELEMENT_PROFILE_MIDDLE_WIDGET:  s.profileMiddleWidgetProc,
		vkycPb.UIElement_UI_ELEMENT_PROFILE_TOP_BANNER:     s.profileTopBannerProc,
		vkycPb.UIElement_UI_ELEMENT_HOME_VKYC_POPUP:        s.homePopupProc,
		vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION: s.homeProfileExtensionProc,
		vkycPb.UIElement_UI_ELEMENT_HOME_TOP_BANNER:        s.homeTopBannerProc,
	}

	for _, nudgeType := range nudgeTypes {
		nudgeProc, ok := nudgeToProcMap[nudgeType]
		if !ok {
			logger.Error(ctx, fmt.Sprintf("can't find nudge processor for nudge type %v", nudgeType))
			return nil, errors.New(fmt.Sprintf("can't find nudge processor for nudge type %v", nudgeType))
		}
		resp, err := nudgeProc.NudgeProc(ctx, &nudges.NudgeProcRequest{
			Req: vkycDetail,
		})
		if err != nil {
			logger.Error(ctx, "error in getting ui elements for user", zap.Error(err))
			return nil, err
		}
		if resp.GetResp() == nil {
			continue
		}
		nudgeContentList = append(nudgeContentList, resp.GetResp())
	}
	return nudgeContentList, nil
}

func (s *Service) GetVKYCLandingDL(ctx context.Context, req *vkycPb.GetVKYCLandingDLRequest) (*vkycPb.GetVKYCLandingDLResponse, error) {
	dl, err := vkyc.BuildVKYCStatusDeeplink(&vkyc.StatusScreenOptions{
		EntryPoint: vkycPb.EntryPoint_ENTRY_POINT_VKYC_HOME,
	})
	if err != nil {
		return &vkycPb.GetVKYCLandingDLResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &vkycPb.GetVKYCLandingDLResponse{
		Status:   rpcPb.StatusOk(),
		Deeplink: dl,
	}, nil
}

func (s *Service) GetVKYCInstructionDL(_ context.Context, _ *vkycPb.GetVKYCInstructionDLRequest) (*vkycPb.GetVKYCInstructionDLResponse, error) {
	dl := vkyc.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
		EntryPoint:      vkycPb.EntryPoint_ENTRY_POINT_VKYC_HOME.String(),
		ClientLastState: vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
		ShowCtaLoader:   true,
	})

	return &vkycPb.GetVKYCInstructionDLResponse{
		Status:   rpcPb.StatusOk(),
		Deeplink: dl,
	}, nil
}

func (s *Service) FetchDynamicElements(ctx context.Context, req *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	if req.GetActorId() == "" {
		logger.Info(ctx, "actor id is mandatory to get ui elements")
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	temp, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dePb.ClientContext_HomeInfo)
	if !ok {
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	uiEles, uiEleErr := s.vkycMockable.GetUIElements(ctx, req.GetActorId(), []vkycPb.UIElement{
		vkycPb.UIElement_UI_ELEMENT_HOME_TOP_BANNER,
		vkycPb.UIElement_UI_ELEMENT_HOME_VKYC_POPUP,
	})
	if uiEleErr != nil {
		if errors.Is(uiEleErr, epifierrors.ErrRecordNotFound) {
			return &dePb.FetchDynamicElementsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "Error in getting UI Elements", zap.Error(uiEleErr))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if len(uiEles) == 0 {
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	dynEleArr := make([]*dePb.DynamicElement, 0)

	switch temp.HomeInfo.GetSection() {
	case dePb.HomeScreenAdditionalInfo_SECTION_TOP_BAR:
		for _, uiEle := range uiEles {
			if uiEle.UIElement == vkycPb.UIElement_UI_ELEMENT_HOME_TOP_BANNER {
				dynamicElement := &dePb.DynamicElement{
					OwnerService:  types.ServiceName_KYC_SERVICE,
					Id:            uiEle.NudgeId,
					UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
					StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
					Content: &dePb.ElementContent{
						Content: &dePb.ElementContent_BannerV2{
							BannerV2: &dePb.BannerElementContentV2{
								Title: &commontypes.Text{
									FontColor: homeTopBannerFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: uiEle.Title,
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
									},
								},
								BackgroundColor: &ui.BackgroundColour{
									Colour: &ui.BackgroundColour_BlockColour{
										BlockColour: lo.Ternary(uiEle.BgColor != "", uiEle.BgColor, HomeTopBannerBodyColor),
									},
								},
								Deeplink: uiEle.Deeplink,
								Image: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  uiEle.Icon,
								},
							},
						},
					},
				}
				dynEleArr = append(dynEleArr, dynamicElement)
			}
		}
	case dePb.HomeScreenAdditionalInfo_SECTION_GTM_POPUP:
		for _, uiEle := range uiEles {
			if uiEle.UIElement == vkycPb.UIElement_UI_ELEMENT_HOME_VKYC_POPUP {
				dynamicElement := &dePb.DynamicElement{
					OwnerService:  types.ServiceName_KYC_SERVICE,
					Id:            uiEle.NudgeId,
					UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
					StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_GTM_POP_UP,
					Content: &dePb.ElementContent{
						Content: &dePb.ElementContent_GtmPopUpBanner{
							GtmPopUpBanner: &dePb.GTMPopUpBanner{
								Body: &dePb.GTMPopUpBanner_BodyLayoutParagraph_{
									BodyLayoutParagraph: &dePb.GTMPopUpBanner_BodyLayoutParagraph{
										Title:       commontypes.GetTextFromStringFontColourFontStyle(uiEle.Title, "#313234", commontypes.FontStyle_HEADLINE_XL),
										BodyContent: commontypes.GetTextFromStringFontColourFontStyle(uiEle.Body, "#6A6D70", commontypes.FontStyle_SUBTITLE_M),
										PopUpVisualElement: &commontypes.VisualElement{
											Asset: &commontypes.VisualElement_Image_{
												Image: &commontypes.VisualElement_Image{
													Source: &commontypes.VisualElement_Image_Url{
														Url: uiEle.Icon,
													},
													Properties: &commontypes.VisualElementProperties{
														Width:  88,
														Height: 88,
													},
													ImageType: commontypes.ImageType_PNG,
												},
											},
										},
									},
								},
								BgColour: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{
										BlockColour: uiEle.BgColor,
									},
								},
								Ctas:                       uiEle.DynamicEleCtaList,
								DismissOnClickOutsidePopUp: true,
							},
						},
					},
				}
				dynEleArr = append(dynEleArr, dynamicElement)
			}
		}
		logger.Debug(ctx, "GTM POP UP received")
	}

	if len(dynEleArr) == 0 {
		logger.Debug(ctx, fmt.Sprintf("no dynamic element returned for %v", req.GetClientContext().GetHomeInfo().GetSection()))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	return &dePb.FetchDynamicElementsResponse{
		Status:       rpcPb.StatusOk(),
		ElementsList: dynEleArr,
	}, nil
}

// DynamicElementCallback update the lastCallbackTime of nudge in in_app_targeted_comms when user clicks on nudge
func (s *Service) DynamicElementCallback(ctx context.Context, req *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	mappingResp, mappingErr := s.inAppTargetedCommsClient.GetTargetedCommsMappings(ctx, &tcPb.GetTargetedCommsMappingsRequest{
		MappingDetails: &tcPb.MappingDetails{
			MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
			MappedValue: req.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(mappingResp, mappingErr); rpcErr != nil && !rpcPb.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "received unexpected response from the for GetTargetedCommsMappings rpc", zap.Error(rpcErr))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// get mapping from in_app_targeted_comms using nudge id when user clicks on nudge
	mapping := getCommsMappingByElementId(mappingResp.GetTargetedCommsMappingList(), req.GetElementId())
	if mapping == nil {
		// todo (Rishu Sahu) add nudge in iat
		return &dePb.DynamicElementCallbackResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}

	var updateMask []tcPb.InAppTargetedCommsMappingFieldMask
	updateMask = append(updateMask, tcPb.InAppTargetedCommsMappingFieldMask_InAppTargetedCommsMappingFieldMask_LAST_CALLBACK_TIME)
	resp, err := s.inAppTargetedCommsClient.UpdateTargetedCommsMapping(ctx, &tcPb.UpdateTargetedCommsMappingRequest{
		TargetedCommsMapping: &tcPb.InAppTargetedCommsMapping{
			Id:        mapping.GetId(),
			ElementId: req.GetElementId(),
			MappingDetails: &tcPb.MappingDetails{
				MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
				MappedValue: req.GetActorId(),
			},
			LastCallbackTime: timestamppb.New(time.Now()),
		},
		TargetedCommsFieldMask: updateMask,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "got unexpected response from UpdateTargetedCommsMapping rpc", zap.Error(rpcErr))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	logger.Debug(ctx, fmt.Sprintf("successfully updated the nudge %v", req.GetElementId()))
	return &dePb.DynamicElementCallbackResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) GetVKYCNextAction(ctx context.Context, req *vkycPb.GetVKYCNextActionRequest) (*vkycPb.GetVKYCNextActionResponse, error) {
	stateOrder, stateProcMap, err := s.getRemainingStageOrderWithProcs(ctx, req.GetActorId(), req.GetEntryPoint(), req.GetClientLastState())
	if err != nil {
		logger.Error(ctx, "error in getting remaining stage order", zap.Error(err))
		return &vkycPb.GetVKYCNextActionResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	logger.Info(ctx, fmt.Sprintf("logging the state order %v", stateOrder))
	for _, state := range stateOrder {
		var (
			stateProc stateprocessor.StateProcType
			ok        bool
		)
		stateProc, ok = stateProcMap[state]
		if !ok {
			logger.Error(ctx, fmt.Sprintf("Can not find process for state processor %v:", state))
			return &vkycPb.GetVKYCNextActionResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		stageProcRes, err := stateProc.StateProc(ctx, &stateprocessor.StateProcRequest{
			Req: req,
		})
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events2.NewVKYCProcStatusUpdateEvent(req.GetActorId(), state.String(), stageProcRes.GetNextAction().GetScreen().String(), req.GetClientLastState().String(), req.GetEntryPoint().String(), err, stageProcRes.GetAnalyticsProps()))
		if err == stateprocessor.SkipStateError {
			logger.Info(ctx, fmt.Sprintf("Skipping state for user %v: ", state))
			continue
		}
		if err != nil {
			logger.Error(ctx, "Error in vkyc stage", zap.Error(err))
			return &vkycPb.GetVKYCNextActionResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		return &vkycPb.GetVKYCNextActionResponse{
			Status:     rpcPb.StatusOk(),
			NextAction: stageProcRes.GetNextAction(),
		}, nil
	}
	// return internal since its unreachable code
	logger.Error(ctx, "Unexpected reached end of loop")
	return &vkycPb.GetVKYCNextActionResponse{
		Status: rpcPb.StatusInternal(),
	}, nil
}

// nolint: funlen
func (s *Service) getRemainingStageOrderWithProcs(ctx context.Context, actorId string, entryPoint vkycPb.EntryPoint, clientLastState vkycPb.VKYCClientState) ([]vkycPb.VKYCClientState, map[vkycPb.VKYCClientState]stateprocessor.StateProcType, error) {
	var stateToProcMap = map[vkycPb.VKYCClientState]stateprocessor.StateProcType{
		// some client might not pass stage we will treat that also initiated sate
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UNSPECIFIED:                     s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED:                       s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_SELECTION:                  s.epanSelectionProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY:                      s.epanEntryProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION:                  s.panEvaluationProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS:                    s.instructionsStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EKYC_CHECK:                      s.ekycProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CALL_INITIATION:                 s.callInitiation,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_REGISTER_USER:                   s.registerUserProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CHECK_AGENT_AVAILABILITY:        s.checkAgentAvailabilityProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UPDATE_CALL_INFO:                s.updateCallInfoProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_VALIDATE_START_CALL:             s.callStartProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EMPLOYMENT_VALIDATION:           s.updateEmpState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_VENDOR_EKYC_NAME_DOB_VALIDATION: s.vendorEkycNameDobValidation,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_ENTRY:                       s.panEntry,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS:                 s.confirmVKYCDetailsProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CALL_QUALITY_CHECK:              s.callQualityCheck,
	}
	var nrStateToProcMap = map[vkycPb.VKYCClientState]stateprocessor.StateProcType{
		// some client might not pass stage we will treat that also initiated sate
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UNSPECIFIED:              s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED:                s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS:             s.nrInstructionsState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_SELECTION:           s.nrEPANSelectionState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY:               s.nrEPANEntryState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UPDATE_APPLICANT_DETAILS: s.nrUpdateCustomerDetails,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS:          s.confirmVKYCDetailsProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_VALIDATE_START_CALL:      s.nrValidateAndStartCallState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CHECK_AGENT_AVAILABILITY: s.checkAgentAvailabilityProc,
	}
	var nrQatarStateToProcMap = map[vkycPb.VKYCClientState]stateprocessor.StateProcType{
		// some client might not pass stage we will treat that also initiated sate
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UNSPECIFIED:              s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED:                s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS:             s.nrInstructionsState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_SELECTION:           s.nrEPANSelectionState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY:               s.nrEPANEntryState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UPDATE_APPLICANT_DETAILS: s.nrUpdateCustomerDetails,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS:          s.confirmVKYCDetailsProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_VALIDATE_START_CALL:      s.nrValidateAndStartCallState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CHECK_AGENT_AVAILABILITY: s.checkAgentAvailabilityProc,
	}
	var nrForm60StateToProcMap = map[vkycPb.VKYCClientState]stateprocessor.StateProcType{
		// some client might not pass stage we will treat that also initiated sate
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UNSPECIFIED:              s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED:                s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS:             s.nrInstructionsState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS:          s.confirmVKYCDetailsProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_VALIDATE_START_CALL:      s.nrValidateAndStartCallState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CHECK_AGENT_AVAILABILITY: s.checkAgentAvailabilityProc,
	}
	var stockGuardianStateToProcMap = map[vkycPb.VKYCClientState]stateprocessor.StateProcType{
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_UNSPECIFIED:         s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED:           s.initiatedStateProc,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS:        s.sgInstructionState,
		vkycPb.VKYCClientState_VKYC_CLIENT_STATE_VALIDATE_START_CALL: s.sgValidateAndStartCallState,
	}
	var (
		stateOrder   []vkycPb.VKYCClientState
		stateProcMap map[vkycPb.VKYCClientState]stateprocessor.StateProcType
	)
	switch entryPoint {
	case vkycPb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING:
		stateOrder = stateprocessor.NRStateOrder
		stateProcMap = nrStateToProcMap
	case vkycPb.EntryPoint_ENTRY_POINT_STOCKGUARDIAN_LOANS:
		stateOrder = stateprocessor.StockguardianStateOrder
		stateProcMap = stockGuardianStateToProcMap
	case vkycPb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING_QATAR:
		stateOrder = stateprocessor.NRQatarStateOrder
		stateProcMap = nrQatarStateToProcMap
	case vkycPb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING_QATAR_FORM_60_FLOW, vkycPb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING_UAE_FORM_60_FLOW:
		stateOrder = stateprocessor.NRForm60FlowStateOrder
		stateProcMap = nrForm60StateToProcMap
	case vkycPb.EntryPoint_ENTRY_POINT_VKYC_HOME:
		if common.IsVKYCFlowV2Enabled(ctx, entryPoint, s.conf) {
			stateOrder = stateprocessor.VKYCFlowV2StageOrder
			stateProcMap = stateToProcMap
		} else {
			stateOrder = stateprocessor.StateOrder
			stateProcMap = stateToProcMap
		}
	default:
		if common.IsVKYCFlowV2Enabled(ctx, entryPoint, s.conf) {
			stateOrder = stateprocessor.VKYCFlowV2StageOrder
			stateProcMap = stateToProcMap
		} else {
			stateOrder = stateprocessor.StateOrder
			stateProcMap = stateToProcMap
		}
	}

	// fetch remaining stateOrder
	stateOrder = stateOrder[lo.IndexOf(stateOrder, clientLastState):]
	return stateOrder, stateProcMap, nil
}

// GetABEvaluatorOfFeature returns an instance of the AB evaluator to perform experiments of type ABExperiment
func getABEvaluatorOfFeature[ABExperiment any](
	actorClient actor.ActorClient, userClient usersPb.UsersClient, userGroupClient userGroupPb.GroupClient,
	abFeatureReleaseConf *releaseGenConf.ABFeatureReleaseConfig,
	strToExprFn func(str string) ABExperiment,
) *release.ABEvaluator[ABExperiment] {
	abEvaluator := release.NewABEvaluator[ABExperiment](
		abFeatureReleaseConf,
		release.NewConstraintFactoryImpl(
			release.NewAppVersionConstraint(),
			release.NewStickinessConstraint(),
			release.NewUserGroupConstraint(actorClient, userClient, userGroupClient),
		),
		strToExprFn,
	)

	return abEvaluator
}

func (s *Service) GetProfileExtension(ctx context.Context, req *vkycPb.GetProfileExtensionRequest) (*vkycPb.GetProfileExtensionResponse, error) {
	if req.GetActorId() == "" {
		return &vkycPb.GetProfileExtensionResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	uiEles, uiEleErr := s.vkycMockable.GetUIElements(ctx, req.GetActorId(), []vkycPb.UIElement{
		vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
	})
	if uiEleErr != nil {
		if errors.Is(uiEleErr, epifierrors.ErrRecordNotFound) {
			return &vkycPb.GetProfileExtensionResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "Error in getting UI Elements", zap.Error(uiEleErr))
		return &vkycPb.GetProfileExtensionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	for _, uiEle := range uiEles {
		if uiEle.UIElement == vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION {
			return &vkycPb.GetProfileExtensionResponse{
				Status:   rpcPb.StatusOk(),
				Deeplink: uiEle.Deeplink,
				Title:    uiEle.Title,
			}, nil
		}
	}
	return &vkycPb.GetProfileExtensionResponse{
		Status: rpcPb.StatusRecordNotFound(),
	}, nil
}

// if the current time is after the time obtained by adding the numberOfHoursAfterShown to the lastCallbackTime.
// this means enough time has passed since the last time the popup was shown, so the popup is eligible to be shown.
func timeConstraintEligibilityDuration(lastCallbackTime *timestamppb.Timestamp, numberOfHoursAfterShown time.Duration) bool {
	return lastCallbackTime == nil ||
		datetime.IsAfter(timestamppb.Now(), timestamppb.New(lastCallbackTime.AsTime().
			Add(numberOfHoursAfterShown)))
}

// getCommsMappingByElementId is used to get the nudge mapping based on nudge id for a actor
func getCommsMappingByElementId(mappings []*tcPb.InAppTargetedCommsMapping, nudgeId string) *tcPb.InAppTargetedCommsMapping {
	for _, rec := range mappings {
		if rec.GetElementId() == nudgeId {
			return rec
		}
	}
	return nil
}

// determines whether a "nudge" is eligible to be shown based on a time constraint.
// if the difference between the current time and the creation time is less than the number of days to be shown, plus one.
// then we show the banner
func timeConstraintEligibility(createdAt *timestamppb.Timestamp, numberOfDaysToShown int) bool {
	// Added extra one to make sure that nudge will be shown strictly
	return createdAt.IsValid() && !datetime.IsDatePastXDays(createdAt, numberOfDaysToShown+1)

}

func (s *Service) GetVKYCNudges(ctx context.Context, req *vkycPb.GetVKYCNudgesRequest) (*vkycPb.GetVKYCNudgesResponse, error) {
	if req.GetActorId() == "" {
		return &vkycPb.GetVKYCNudgesResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actorId is missing in request"),
		}, nil
	}
	uiEles, err := s.vkycMockable.GetUIElements(ctx, req.GetActorId(), req.GetNudgeType())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.GetVKYCNudgesResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &vkycPb.GetVKYCNudgesResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if len(uiEles) == 0 {
		return &vkycPb.GetVKYCNudgesResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	vkycNudges := make([]*vkycPb.VKYCNudge, 0)
	for _, uiEle := range uiEles {
		vkycNudges = append(vkycNudges, &vkycPb.VKYCNudge{
			Deeplink:  uiEle.Deeplink,
			Title:     uiEle.Title,
			IconUrl:   uiEle.Icon,
			NudgeType: uiEle.UIElement,
			Body:      uiEle.Body,
			Ctas:      uiEle.CtaList,
		})
	}
	return &vkycPb.GetVKYCNudgesResponse{
		Status:     rpcPb.StatusOk(),
		VkycNudges: vkycNudges,
	}, nil
}

func (s *Service) getActorIdFromEkycRrn(ctx context.Context, ekycRrn string) (string, error) {
	// add underscore to given ekyc rrn since while registering at karza end we removed that
	ekycRrnWithUnderScore := vkyc.GetCustomerCreationEkycRrn(ekycRrn)
	// giving preference to _ added rrn
	bankCustResp, bankCustErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_EkycRrn{
			EkycRrn: ekycRrnWithUnderScore,
		},
	})

	if rpcErr := epifigrpc.RPCError(bankCustResp, bankCustErr); !bankCustResp.GetStatus().IsRecordNotFound() && rpcErr != nil {
		logger.Error(ctx, fmt.Sprintf("Error in fetching bank customer info for ekyc rrn %v: ", ekycRrnWithUnderScore), zap.Error(rpcErr))
		return "", rpcErr
	}

	// give a try with ekyc rrn received in request
	if bankCustResp.GetStatus().IsRecordNotFound() {
		bankCustResp, bankCustErr = s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Identifier: &bankcust.GetBankCustomerRequest_EkycRrn{
				EkycRrn: ekycRrn,
			},
		})
		if rpcErr := epifigrpc.RPCError(bankCustResp, bankCustErr); !bankCustResp.GetStatus().IsRecordNotFound() && rpcErr != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetching bank customer info for ekyc rrn %v: ", ekycRrn), zap.Error(rpcErr))
			return "", rpcErr
		}
		if bankCustResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, fmt.Sprintf("can not found actor for this ekyc rrn %v: ", ekycRrn))
			return "", epifierrors.ErrRecordNotFound
		}
	}
	return bankCustResp.GetBankCustomer().GetActorId(), nil
}

// updateVKYCEntities will update call info, attempt and summary in same order
func (s *Service) updateVkycEntities(ctx context.Context, vkycSummary *vkycPb.VKYCSummary, vkycAttempt *vkycPb.VKYCAttempt, vkycCallInfo *vkycPb.VKYCKarzaCallInfo,
	callInfoSubStatus vkycPb.VKYCKarzaCallInfoSubStatus, attemptSubstatus vkycPb.VKYCAttemptSubStatus) error {

	vkycCallInfo.SubStatus = callInfoSubStatus
	vkycCallInfo.Status = vkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED

	err := s.vkycKarzaCallInfoDao.UpdateById(ctx, vkycCallInfo, []vkycPb.VKYCKarzaCallInfoFieldMask{
		vkycPb.VKYCKarzaCallInfoFieldMask_VKYC_KARZA_CALL_INFO_STATUS,
		vkycPb.VKYCKarzaCallInfoFieldMask_VKYC_KARZA_CALL_INFO_SUB_STATUS,
	})

	if err != nil {
		logger.Error(ctx, "Error in updating call info", zap.Error(err))
		return err
	}

	vkycAttempt.SubStatus = attemptSubstatus
	vkycAttempt.Status = vkycPb.VKYCAttemptStatus_VKYC_ATTEMPT_STATUS_FAILED
	err = s.vkycAttemptDao.UpdateById(ctx, vkycAttempt, []vkycPb.VKYCAttemptFieldMask{
		vkycPb.VKYCAttemptFieldMask_VKYC_ATTEMPT_FIELD_MASK_SUB_STATUS,
		vkycPb.VKYCAttemptFieldMask_VKYC_ATTEMPT_FIELD_MASK_STATUS,
	})

	if err != nil {
		logger.Error(ctx, "Error in updating vkyc attempt", zap.Error(err))
		return err
	}

	vkycSummary.SubStatus = vkycPb.VKYCSummarySubStatus_VKYC_SUMMARY_SUB_STATUS_ATTEMPT_FAILED
	vkycSummary.Status = vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_RE_REGISTER
	err = s.summaryDao.UpdateById(ctx, vkycSummary, []vkycPb.VKYCSummaryFieldMask{
		vkycPb.VKYCSummaryFieldMask_VKYC_SUMMARY_FIELD_MASK_SUB_STATUS,
		vkycPb.VKYCSummaryFieldMask_VKYC_SUMMARY_FIELD_MASK_STATUS,
	})

	if err != nil {
		logger.Error(ctx, "Error in updating vkyc summary", zap.Error(err))
	}
	return err
}

// return status code in respect of error
func updateCallStatus(err error, userCountry string) *rpcPb.Status {
	switch {
	case errors.Is(err, emptyLocationTokenError):
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.UpdateCallInfoResponse_EMPTY_LOCATION_TOKEN), "Empty location token")
	case errors.Is(err, locationOutSideIndiaError):
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.UpdateCallInfoResponse_LOCATION_OUTSIDE_INDIA), userCountry)
	case errors.Is(err, locationCommunicationAddressMismatch):
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.UpdateCallInfoResponse_LOCATION_MISMATCH_WITH_COMMUNICATION_ADDRESS), "Location and communication address mismatch")
	case err != nil:
		return rpcPb.NewStatusWithoutDebug(uint32(vkycPb.UpdateCallInfoResponse_LOCATION_UPDATE_ERROR), "Error in updating location")
	}
	return rpcPb.StatusOk()
}

func (s *Service) getSlots(ctx context.Context, actorId string, startTimestamp *timestamppb.Timestamp, endTimestamp *timestamppb.Timestamp) []*vkycPb.SlotDetails {
	startTime := startTimestamp.AsTime().In(datetime.IST)
	endTime := endTimestamp.AsTime().In(datetime.IST)
	var slots []*vkycPb.SlotDetails
	slotDuration := 15 * time.Minute
	slotStart := roundDownToNearestDivisible(startTime, 5*time.Minute)
	currentTime := slotStart

	for currentTime.Before(endTime) {
		slotEndTime := currentTime.Add(slotDuration)

		if slotEndTime.After(endTime) {
			break
		}

		if currentTime.Weekday() == slotEndTime.Weekday() {
			isHoliday, _, _ := s.svcProvider.IsHoliday(ctx, actorId, currentTime)
			if !isHoliday {
				slots = append(slots, &vkycPb.SlotDetails{
					StartTime:       timestamppb.New(currentTime),
					EndTime:         timestamppb.New(slotEndTime),
					IsSlotAvailable: true,
				})
			}
		}
		currentTime = slotEndTime
	}
	return slots
}

// Rounds down a time to the nearest value divisible by the given duration
func roundDownToNearestDivisible(t time.Time, duration time.Duration) time.Time {
	rounded := t.Round(duration)
	if rounded.Before(t) {
		rounded = rounded.Add(duration)
	}
	return rounded
}

//nolint:funlen
func (s *Service) GetVKYCType(ctx context.Context, req *vkycPb.GetVKYCTypeRequest) (*vkycPb.GetVKYCTypeResponse, error) {
	var (
		vkycType vkycPb.VKYCType
		custId   string
	)
	bcResp, respErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: req.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, respErr); rpcErr != nil && !bcResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in getting bank customer", zap.Error(rpcErr))
		return &vkycPb.GetVKYCTypeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	onbResp, respErr := s.onbClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
		ActorId:    req.GetActorId(),
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(onbResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting onboarding details", zap.Error(rpcErr))
		return &vkycPb.GetVKYCTypeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	kycResp, err := s.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{ActorId: req.GetActorId()})
	if te := epifigrpc.RPCError(kycResp, err); te != nil && kycResp.GetStatus().GetCode() != uint32(kyc.GetKYCRecordResponse_NAME_MISMATCH) {
		logger.Error(ctx, "Error in fetching kyc record", zap.Error(te))
		return &vkycPb.GetVKYCTypeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	switch {
	case onbResp.GetDetails().GetCompletedAt() != nil:
		vkycType = vkycPb.VKYCType_VKYC_TYPE_AFTER_ONBOARDING
	case bcResp.GetBankCustomer().GetStatus() == bankcust.Status_STATUS_ACTIVE:
		vkycType = vkycPb.VKYCType_VKYC_TYPE_OPTIONAL_VKYC
	default:
		vkycType = vkycPb.VKYCType_VKYC_TYPE_BEFORE_CUSTOMER_CREATION
	}

	switch {
	case lo.Contains(vkyc.ReAttemptVKYC, req.GetActorId()):
		custId = reAttemptVKYC
	case lo.Contains(vkyc.DowngradedUsersList, bcResp.GetBankCustomer().GetVendorCustomerId()),
		vkyc.IsUserCKYCWithOFlag(kycResp.GetCkycAttempt()),
		lo.Contains(vkyc.LSOVKYCActors, req.GetActorId()),
		lo.Contains(vkyc.ForceVKYCActors, req.GetActorId()):
		custId = ckycVkycCustId
	case vkycType == vkycPb.VKYCType_VKYC_TYPE_BEFORE_CUSTOMER_CREATION:
		custId = preVkyc
	default:
		custId = postVkyc
	}

	return &vkycPb.GetVKYCTypeResponse{
		Status:   rpcPb.StatusOk(),
		VkycType: vkycType,
		VendorMetadata: &vkycPb.VendorMetadata{
			Metadata: &vkycPb.VendorMetadata_KarzaMetadata{
				KarzaMetadata: &vkycPb.KarzaMetadata{
					CustomerId: custId,
				},
			},
		},
	}, nil
}

func (s *Service) UploadVKYCCallQualityData(ctx context.Context, req *vkycPb.UploadVKYCCallQualityDataRequest) (*vkycPb.UploadVKYCCallQualityDataResponse, error) {
	summary, err := s.summaryDao.GetByActorId(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &vkycPb.UploadVKYCCallQualityDataResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error fetching vkyc summary", zap.Error(err))
		return &vkycPb.UploadVKYCCallQualityDataResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	attempt, err := s.vkycAttemptDao.GetLatestAttemptByVkycSummaryId(ctx, summary.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching latest vkyc attempt", zap.Error(err))
		return &vkycPb.UploadVKYCCallQualityDataResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	callInfo, err := s.svcProvider.GetLatestCallInfo(ctx, attempt.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching latest call info", zap.Error(err))
		return &vkycPb.UploadVKYCCallQualityDataResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if callInfo.GetInfo().GetCallMetadata() == nil {
		callInfo.GetInfo().CallMetadata = &vkycPb.VKYCKarzaCallInfoCallMetadata{}
	}
	callInfo.GetInfo().GetCallMetadata().CallQualityData = req.GetCallQualityData()
	if err = s.vkycKarzaCallInfoDao.UpdateById(ctx, callInfo.GetInfo(), []vkycPb.VKYCKarzaCallInfoFieldMask{vkycPb.VKYCKarzaCallInfoFieldMask_VKYC_KARZA_CALL_INFO_CALL_METADATA}); err != nil {
		logger.Error(ctx, "error updating call quality data", zap.Error(err))
		return &vkycPb.UploadVKYCCallQualityDataResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &vkycPb.UploadVKYCCallQualityDataResponse{
		Status: rpcPb.StatusOk(),
		NextAction: vkyc.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
			ClientLastState: vkycPb.VKYCClientState_VKYC_CLIENT_STATE_CALL_QUALITY_CHECK.String(),
			EntryPoint:      req.GetEntryPoint().String(),
			ShowCtaLoader:   true,
		}),
	}, nil
}

func (s *Service) ProcessUserAck(ctx context.Context, req *vkycPb.ProcessUserAckRequest) (*vkycPb.ProcessUserAckResponse, error) {
	ackImpl, err := s.ackFactory.GetImpl(ctx, req.GetAckType())
	if err != nil {
		logger.Error(ctx, "error in fetching factory implementation", zap.Error(err), zap.Any("ACK_TYPE", req.GetAckType().String()))
		return &vkycPb.ProcessUserAckResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	nextAction, err := ackImpl.ProcessAck(ctx, &ack_manager.ProcessAckRequest{
		AckType: req.GetAckType(),
		AckId:   req.GetAckId(),
	})
	if err != nil {
		logger.Error(ctx, "error in processing the acknowledgement", zap.Error(err))
		return &vkycPb.ProcessUserAckResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &vkycPb.ProcessUserAckResponse{
		Status:     rpcPb.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) IsActorEligibleForSurvey(ctx context.Context, req *feedbackEnginePb.IsActorEligibleForSurveyRequest) (*feedbackEnginePb.IsActorEligibleForSurveyResponse, error) {
	// check if actor is eligible for Re attempt VKYC
	if !s.conf.VKYC().EnableReAttemptVKYCHomeBottomSheet() || !lo.Contains(vkyc.ReAttemptVKYC, req.GetActorId()) {
		return &feedbackEnginePb.IsActorEligibleForSurveyResponse{
			Status:                   rpcPb.StatusOk(),
			IsActorEligibleForSurvey: false,
		}, nil

	}
	vkycInfo, vkycInfoErr := s.vkycMockable.GetVKYCInfo(ctx, &vkycPb.GetVKYCInfoRequest{
		ActorId: req.GetActorId(),
	})
	if te := epifigrpc.RPCError(vkycInfo, vkycInfoErr); te != nil {
		logger.Error(ctx, "unable to fetch vkyc info by actor id", zap.Error(te))
		return nil, te
	}

	// check if vkyc is in terminal state (Auditor approved, rejected or in-review)
	if vkyc.IsVkycStatusInTerminalState(vkycInfo.GetVkycRecord().GetVkycSummary()) {
		return &feedbackEnginePb.IsActorEligibleForSurveyResponse{
			Status:                   rpcPb.StatusOk(),
			IsActorEligibleForSurvey: false,
		}, nil
	}

	// check if current time is in business hours
	if !common.IsBusinessHours(time.Now().In(datetime.IST), s.conf.VKYC().ReAttemptVkycStartHour(), s.conf.VKYC().ReAttemptVkycEndHour()) {
		return &feedbackEnginePb.IsActorEligibleForSurveyResponse{
			Status:                   rpcPb.StatusOk(),
			IsActorEligibleForSurvey: false,
		}, nil
	}

	logger.Info(ctx, "showing bottom sheet for reattempt vkyc user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))

	return &feedbackEnginePb.IsActorEligibleForSurveyResponse{
		Status:                   rpcPb.StatusOk(),
		IsActorEligibleForSurvey: true,
	}, nil
}
