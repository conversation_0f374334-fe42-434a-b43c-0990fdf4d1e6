-- Moving orders to failed state as the order was not processed due to the temporal outage

UPDATE orders
set state = 'ORDER_INITIATION_FAILED', updated_at = now()
where id in ('USSO2bsK5ETzTZ250508','USSO3ojpbfc5MG250508','USSO3SqTRqk44a250508','USSO3BEZpsg2QK250508','USSO2MfRGBPGkn250508','USSOCw49GJ2XUT250508','USSO43qMtAB9BF250508','USSO3JATq2yiCZ250508','USSO3qaio9pn4n250508','USSOVPZev6SRN6250508','USSOyYZfeFR7Bc250509','USSOidCADXGU24250509','USSOEogvzdjLpA250509','USSO3tysGMTseK250509');

update account_activities
set order_state = 'ORDER_INITIATION_FAILED', updated_at = now()
where order_id in ('USSO2bsK5ETzTZ250508','USSO3ojpbfc5MG250508','USSO3SqTRqk44a250508','USSO3BEZpsg2QK250508','USSO2MfRGBPGkn250508','USSOCw49GJ2XUT250508','USSO43qMtAB9BF250508','USSO3JATq2yiCZ250508','USSO3qaio9pn4n250508','USSOVPZev6SRN6250508','USSOyYZfeFR7Bc250509','USSOidCADXGU24250509','USSOEogvzdjLpA250509','USSO3tysGMTseK250509');

update workflow_requests
set status = 'FAILED', updated_at = now()
where id in ('WFRunE2TVGaRpSqdsym3BW7cg250508==','WFRrdnDlsQJTP62b9Trnwwm/Q250508==','WFRGmvW6XXwSLq00jRY5G5g5w250508==');
