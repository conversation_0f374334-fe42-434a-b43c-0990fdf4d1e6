-- users
UPSERT
INTO users(id, profile)
VALUES ('Test-User-1', NULL),
       ('user-2', NULL),
       ('user-3', NULL),
       ('user-4', NULL),
       ('user-5', NULL),
       ('user-6', '{"phone_number": {"country_code": 91, "national_number": 6666677777}, "email": "<EMAIL>"}'),
       ('user-7', '{"phone_number": {"country_code": 91, "national_number": **********}, "email": "<EMAIL>"}'),
	   ('fraud-user-1', NULL),
	   ('fraud-user-2', NULL);

UPSERT
INTO users(id, profile, fed_customer_info)
VALUES ('Test-User-2', '{"phone_number": {"country_code": 91, "national_number": **********}, "email": "<EMAIL>"}',
    '{"Id": "*********", "KycLevel": 1, "Name": null, "OriginalKycLevelWithVendor": 1, "QueueRetryInfo": {"queue_msg_id": "584b5a48-36a8-47a6-b2bc-1810de5e1eb4", "req_id": "NEOCIFCRWed20201014041925Fmdja"}, "Status": "CREATED"}');

-- savings_accounts
UPSERT
INTO savings_accounts(id, account_number, ifsc_code, primary_account_holder, phone_number, email_id, actor_id,
                             partner_bank, state, balance_from_partner, created_at, updated_at, created_by, updated_by,
                             queue_retry_info, account_creation_info, constraints)
VALUES ('Test-Savings-1', '*********0', 'IFSC0001', 'Test-User-1', '**********', '<EMAIL>', 'actor-1',
        'FEDERAL_BANK', 'INITIATED', '{"currency_code": "INR", "units": 100}', now(), now(), '{"actor": "MANUAL"}',
        '{"actor": "MANUAL"}', '{"attempts": 1, "request_id": "req123"}', null, null),
        ('Test-Savings-2', '*********1', 'IFSC0001', 'user-2', '**********', '<EMAIL>', 'actor-2',
        'FEDERAL_BANK', 'INITIATED', '{"currency_code": "INR", "units": 100}', now(), now(), '{"actor": "MANUAL"}',
        '{"actor": "MANUAL"}', '{"attempts": 1, "request_id": "req123"}', null , null),
        ('Test-Savings', '*********2', 'IFSC0002', 'Test-User-2', '**********', '<EMAIL>', 'actor-3',
        'FEDERAL_BANK', 'INITIATED', '{"currency_code": "INR", "units": 100}', now(), now(), '{"actor": "MANUAL"}',
        '{"actor": "MANUAL"}', '{"attempts": 1, "request_id": "req1234"}', null, null),
        ('Test-Savings-3', '*********3', 'IFSC0001', 'user-3', '**********', '<EMAIL>', 'actor-4',
        'FEDERAL_BANK', 'CREATED', '{"currency_code": "INR", "units": 100}', now(), now(), '{"actor": "MANUAL"}',
        '{"actor": "MANUAL"}', null , '{"fiCreationSucceededAt": "2021-04-11T05:14:12.941920082Z"}', '{"accessLevel": "ACCESS_LEVEL_PARTIAL_ACCESS", "restrictions": ["RESTRICTION_CREDIT_FREEZE"], "transactionLimit": {"currencyCode": "INR", "nanos": 10, "units": "10"}, "withdrawalLimit": {"currencyCode": "INR", "nanos": 10, "units": "10"}}'),
        ('Test-Savings-4', '*********4', 'IFSC0001', 'user-4', '**********', '<EMAIL>', 'actor-5',
        'FEDERAL_BANK', 'CREATED', '{"currency_code": "INR", "units": 100}', now(), now(), '{"actor": "MANUAL"}',
        '{"actor": "MANUAL"}', null , '{"fiCreationSucceededAt": "2021-04-12T05:14:12.941920082Z"}', null ),
        ('Test-Savings-5', '*********5', 'IFSC0001', 'user-5', '**********', '<EMAIL>', 'actor-6',
        'FEDERAL_BANK', 'CREATED', '{"currency_code": "INR", "units": 100}', now(), now(), '{"actor": "MANUAL"}',
        '{"actor": "MANUAL"}', null , '{"fiCreationSucceededAt": "2021-04-13T05:14:12.941920082Z"}', null );

-- actors
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-user-1', 'USER', 'Test-User-1', NULL, 'EPIFI_TECH'),
       ('actor-ex-user-1', 'EXTERNAL_USER', NULL, 'Test-External-User', 'EPIFI_TECH'),
       ('actor-ex-user-2', 'EXTERNAL_USER', NULL, 'Pakalu Papito', 'EPIFI_TECH'),
       ('actor-1', 'USER', 'user-2', null, 'EPIFI_TECH'),
       ('actor-user-3', 'USER', 'user-3', NULL, 'EPIFI_TECH'),
       ('actor-2', 'USER', 'user-4', null, 'EPIFI_TECH'),
       ('actor-3', 'USER', 'user-5', null, 'EPIFI_TECH'),
       ('actor-merchant-1', 'MERCHANT', 'merchant-1', null, 'EPIFI_TECH'),
       ('actor-ex-merchant-1', 'EXTERNAL_MERCHANT', 'merchant-entity-id-1', null, 'EPIFI_TECH'),
       ('actor-fi', 'MERCHANT', 'EPIFI', NULL, 'EPIFI_TECH'),
       ('wl-actor-1', 'WAITLISTED_USER', 'bfc05f22-2722-4057-bd10-aa0351f591bf', NULL, 'EPIFI_TECH'),
       ('actor-4', 'USER', 'user-6', null, 'EPIFI_TECH'),
		('actor-5', 'USER', NULL, 'Gutsy Gibbon', 'EPIFI_TECH'),
       ('actor-aa-1', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('actor-aa-2', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('actor-aa-3', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-0', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-1', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-2', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-3', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-4', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-5', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-6', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('primary-actor-1', 'EXTERNAL_USER', NULL, 'Jake Peralta', 'EPIFI_WEALTH'),
	   ('fraud-actor-1', 'USER', 'fraud-user-1', null, 'EPIFI_TECH'),
	   ('fraud-actor-2', 'USER', 'fraud-user-2', null, 'EPIFI_TECH'),
	   ('ift-actor-1', 'USER', 'ift-user-1', 'ift actor 1', 'EPIFI_TECH'),
	   ('ift-actor-2', 'USER', 'ift-user-2', 'ift actor 2', 'EPIFI_TECH'),
	   ('tt-actor-1', 'USER', 'tt-user-1', 'tt actor 1', 'EPIFI_TECH'),
	   ('ift-international-actor-1', 'EXTERNAL_USER', 'international-actor-1', 'us stocks', 'EPIFI_TECH'),
       ('ift-actor-5', 'USER', 'ift-user-5', 'ift actor 5', 'EPIFI_TECH'),
       ('ift-actor-6', 'USER', 'ift-user-6', 'ift actor 6', 'EPIFI_TECH');

-- Adding actor for business account
UPSERT
INTO public.actors (id, type, entity_id, name) VALUES
('actor-epifi-business-account', 'EXTERNAL_USER', null, 'Fi'),
('ACHKZ9wtGvSu++VNRdu/fRlA230401==', 'EXTERNAL_MERCHANT', null, 'EPIFI_TECH');

UPSERT
INTO blocked_actors_map(actor_id, blocked_actor_id, is_spam)
VALUES ('actor-1', 'actor-ex-user-2', true);

-- payment_instruments

-- Adding pi for business account
UPSERT
INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-epifi-business-account', 'BANK_ACCOUNT', '', '{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "FDRL000001", "name": "EPIFI TECHNOLOGIES PRIVATE LIMITED", "secure_account_number": "xxxxxxxxxx6732"}}', 'CREATED', '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');

-- Adding pi for business account
UPSERT
INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-epifi-business-account-2', 'BANK_ACCOUNT', '', '{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "FDRL000001", "name": "EPIFI TECHNOLOGIES PRIVATE LIMITED", "secure_account_number": "xxxxxxxxxx7843"}}', 'CREATED', '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-1', 'BANK_ACCOUNT', 'Kunal',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2939"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
        ('paymentinstrument-pool-account-federal', 'BANK_ACCOUNT', 'EPIFI POOL ACCOUNT',
            '{
                "account": {
                    "account_type": "SAVINGS",
                    "actual_account_number": "**************",
                    "ifsc_code": "FDRL0005555",
                    "name": "EPIFI POOL ACCOUNT",
                    "secure_account_number": "xxxxxxxxxx0111"
                }
            }',
            'CREATED',
            '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 'EXTERNAL'),
       ('pi-savings-1', 'BANK_ACCOUNT', 'Test-User-1',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "*********0",
            "ifsc_code": "IFSC0001",
            "secure_account_number": "xxxxxx7890"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
        ('credit_card_pi', 'CREDIT_CARD', 'credit-card-user',
        '{
          "credit_card": {
            "id": "random_credit_card_id"
          }
        }',
        'CREATED', '{"INBOUND_TXN": true,"OUTBOUND_TXN": true }','EXTERNAL');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-9', 'BANK_ACCOUNT', 'Raj',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2930"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-2', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2940"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
        ('pi-partial-1', 'PARTIAL_BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "2342940",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2940",
            "name":"partial"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 'EXTERNAL'),
        ('pi-partial-2', 'PARTIAL_BANK_ACCOUNT', '',
        '{
          "account": {
            "name":"merchant + mid"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 'EXTERNAL'),
        ('pi-generic-1', 'GENERIC', '',
            '{
              "account": {
                "account_type": "SAVINGS",
                "actual_account_number": "********",
                "ifsc_code": "FDRL0001001",
                "secure_account_number": "xxxxxxxxxxx0000",
                "name":"generic"
              },
              "card": null,
              "upi": null
            }',
            'CREATED',
            '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 'EXTERNAL'),
        ('pi-generic-2', 'GENERIC', '',
            '{
              "account": {
                "account_type": "SAVINGS",
                "actual_account_number": "********",
                "ifsc_code": "FDRL0001001",
                "secure_account_number": "xxxxxxxxxxx0000"
              },
              "card": null,
              "upi": null
            }',
            'CREATED',
            '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 'EXTERNAL');
UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-3', 'UPI', 'nitesh',
        '{
          "account": null,
          "card": null,
          "upi": {"vpa": "nitesh@fede"}
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
       ('pi-5', 'UPI', 'lettercase',
        '{
          "account": null,
          "card": null,
          "upi": {"vpa": "lettercase@fede"}
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
     ('pi-partial-2', 'PARTIAL_UPI', 'test',
            '{
              "account": null,
              "card": null,
              "upi": {"vpa": "test@fe", "name":"partial"}
            }',
            'CREATED',
            '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 'EXTERNAL'),
    ('pi-6', 'UPI', 'random-6',
        '{
          "account": null,
          "card": null,
          "upi": {"vpa": "random-6@fede","merchant_details": {"mcc":"random-mcc"}}
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
    ('pi-7', 'UPI', 'random-7',
        '{
          "account": null,
          "card": null,
          "upi": {"vpa": "random-7@fede"}
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
    ('pi-8', 'UPI', 'random-8',
        '{
          "account": null,
          "card": null,
          "upi": {"vpa": "random-8@fede","merchant_details": {"mcc":"random-mcc"}}
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-ex-3', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "ICIC0001",
            "secure_account_number": "xxxxxxxxxxx2941"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'EXTERNAL');
UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-4', 'DEBIT_CARD', 'epifi user',
        '{
          "account": null,
          "card": {
                    "actual_card_number": "6652********0722",
                    "card_reference_number": "15b78d61-03e8-4553-92b0-307a266fcc2e",
                    "expiry": "0221",
                    "name": "epifi user",
                    "secure_card_number": "FI_APP:PERPETUAL:649adef1-43e9-4573-9d83-91bfbde0c4b5"
            },
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL');
UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification, ownership)
VALUES ('pi-wealth-1', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2935"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'EPIFI_WEALTH');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification, ownership)
VALUES ('pi-wealth-2', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx6789"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'EPIFI_WEALTH');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification, ownership)
VALUES ('pi-wealth-3', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2569"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'EPIFI_WEALTH');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification, ownership)
VALUES ('pi-ift-user-1', 'BANK_ACCOUNT', 'Shane Warne',
		'{
			"account": {
				"account_type": "SAVINGS",
				"actual_account_number": "***************",
				"ifsc_code": "FDRL0001001",
				"secure_account_number": "xxxxxxxxxxx1122"
			},
			"card": null,
			"upi": null
		}',
		'CREATED',
		'{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'EPIFI_TECH'),
	   ('pi-ift-user-2', 'BANK_ACCOUNT', 'Mike Hussey',
		'{
			"account": {
				"account_type": "SAVINGS",
				"actual_account_number": "***************",
				"ifsc_code": "FDRL0001001",
				"secure_account_number": "xxxxxxxxxxx1121"
			},
			"card": null,
			"upi": null
		}',
		'CREATED',
		'{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'EPIFI_TECH'),
	   ('pi-tt-user-1', 'BANK_ACCOUNT', 'Andy Roberts',
		'{
			"account": {
				"account_type": "SAVINGS",
				"actual_account_number": "***************",
				"ifsc_code": "FDRL0001001",
				"secure_account_number": "xxxxxxxxxxx2121"
			},
			"card": null,
			"upi": null
		}',
		'CREATED',
		'{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'EPIFI_TECH');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification, ownership)
VALUES ('pi-ift-international-1', 'INTERNATIONAL_BANK_ACCOUNT', 'Alpaca Securities LCC',
		'{"international_bank_account": {
			"beneficiary_name": "Alpaca Securities LLC",
			"beneficiary_address": "3 East Third Ave Suite 233 San Mateo CA 94401 USA",
			"beneficiary_account_number": "1636877",
			"beneficiary_bank_name": "BMO Harris Bank",
			"beneficiary_bank_address": "BMO Harris Bank NA - BMO Harris Bank 111. W. MonroeStreet Chicago IL 60603 USA",
			"beneficiary_bank_branch_name": "W. MonroeStreet Chicago",
			"beneficiary_swift_code": "HATRUS44",
			"routing_code": "*********",
			"intermediary_bank_name": "FEDERAL BANK, KOCHI",
			"intermediary_swift_code": "FDRLINBBIBD"}
		}',
		'CREATED',
		'{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL', 'US_STOCKS_ALPACA');

--- deposit account PI
UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-deposit-1', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SMART_DEPOSIT",
            "actual_account_number": "**********",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "XXXXXX9999"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL');

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, issuer_classification)
VALUES ('pi-ift-1', 'BANK_ACCOUNT', '',
        '{
          "account": {
            "account_type": "SAVINGS",
            "actual_account_number": "***************",
            "ifsc_code": "FDRL0001001",
            "secure_account_number": "xxxxxxxxxxx2940"
          },
          "card": null,
          "upi": null
        }',
        'CREATED',
        '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
		('pi-ift-2', 'BANK_ACCOUNT', '',
			'{
			  "account": {
				"account_type": "SAVINGS",
				"actual_account_number": "***************",
				"ifsc_code": "FDRL0001001",
				"secure_account_number": "xxxxxxxxxxx2940"
			  },
			  "card": null,
			  "upi": null
			}',
			'CREATED',
			'{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL'),
		('pi-ift-3', 'BANK_ACCOUNT', '',
			'{
			  "account": {
				"account_type": "SAVINGS",
				"actual_account_number": "***************",
				"ifsc_code": "FDRL0001001",
				"secure_account_number": "xxxxxxxxxxx2940"
			  },
			  "card": null,
			  "upi": null
			}',
			'CREATED',
			'{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 'INTERNAL');

-- orders
UPSERT
INTO orders (id, from_actor_id, to_actor_id, workflow, status, order_payload, amount, provenance, created_at, expire_at, external_id, updated_at, client_req_id, wf_ref_id)
VALUES
('order-1', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '1 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-1', now() + INTERVAL '1 MINUTE', 'order-client-req-1', 'wf-req-1'),
('order-2', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello", "payment_protocol": "IMPS"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '2 MINUTE', now(), 'order-ext-2', now() + INTERVAL '2 MINUTE', null, null),
('order-3', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '3 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-3', now () + INTERVAL '3 MINUTE', null, null),
('order-4', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello", "payment_protocol":"UPI"}}', '{"currency_code": "INR", "units": 5000, "nanos": 0}', 'USER_APP', now() + INTERVAL '4 MINUTE', now() + INTERVAL '4 MINUTE', 'order-ext-4', now() + INTERVAL '4 MINUTE', null, null),
('order-5', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'PAYMENT_FAILED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '5 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-5', now() + INTERVAL '5 MINUTE', null, null),
('order-6', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'IN_SETTLEMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '6 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-6', now() + INTERVAL '6 MINUTE', null, null),
('order-7', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now()+ INTERVAL '7 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-7', now() + INTERVAL '7 MINUTE', null, null),
('order-8', 'actor-2', 'actor-1', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 3000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  3000, "nanos":  0}', 'USER_APP', now() + INTERVAL '8 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-8', now() + INTERVAL '8 MINUTE', null, null),
('order-9', 'actor-2', 'actor-1', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '9 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-9', now() + INTERVAL '9 MINUTE', null, null),
('order-10', 'actor-2', 'actor-1', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '10 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-10', now() + INTERVAL '10 MINUTE', null, null),
('order-11', 'actor-2', 'actor-1', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '11 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-11', now() + INTERVAL '11 MINUTE', null, null),
('order-12', 'actor-2', 'actor-1', 'P2P_COLLECT', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-ex-3", "pi_to": "pi-3", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '12 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-12', now() + INTERVAL '12 MINUTE', null, null),
('order-13', 'actor-1', 'actor-2', 'P2P_COLLECT_SHORT_CIRCUIT', 'COLLECT_REGISTERED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-2", "pi_to": "pi-3", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '13 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-13', now() + INTERVAL '13 MINUTE', null, null),
('order-14', 'actor-1', 'actor-2', 'P2P_COLLECT', 'COLLECT_REGISTERED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-3", "pi_to": "pi-ex-3", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '14 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-14', now() + INTERVAL '14 MINUTE', null, null),
('order-15', 'actor-1', 'actor-2', 'P2P_COLLECT_SHORT_CIRCUIT', 'COLLECT_REGISTERED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-3", "pi_to": "pi-ex-3", "remarks": "collect-short-circuit-timeline"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '15 MINUTE' , now() + INTERVAL '5 MINUTE', 'order-ext-15', now() + INTERVAL '15 MINUTE', null, null),
('order-16', 'actor-1', 'actor-2', 'P2P_COLLECT', 'COLLECT_REGISTERED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-3", "pi_to": "pi-ex-3", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '16 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-16', now() + INTERVAL '16 MINUTE', null, null),
('order-17', 'actor-3', 'actor-1', 'P2P_COLLECT', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '17 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-17', now() + INTERVAL '17 MINUTE', null, null),
('order-18', 'actor-user-1', 'actor-user-3', 'ADD_FUNDS', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now()+ INTERVAL '18 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-18', now() + INTERVAL '17 MINUTE', null, null),
('order-19', 'actor-user-1', 'actor-user-3', 'ADD_FUNDS', 'SETTLED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now()+ INTERVAL '19 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-19', now() + INTERVAL '17 MINUTE', null, null),
('order-20', 'actor-user-1', 'actor-user-3', 'P2P_COLLECT', 'SETTLED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now()+ INTERVAL '20 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-20', now() + INTERVAL '17 MINUTE', null, null),
('order-21', 'actor-epifi-business-account', 'actor-user-1', 'REWARDS_ADD_FUNDS_SD', 'CREATED', '{"b2cPayload": {"amount": {"currencyCode": "INR", "units": "100"}, "partner": "FEDERAL_BANK", "piFrom": "paymentinstrument-epifi-business-account", "piTo": "pi-savings-1", "preferredProtocol": "INTRA_BANK", "remarks": "Rewards Smart Deposit fund transfer"}, "p2pFundTransferPayload": {"payerAccountId": "Test-Savings-1", "paymentDetails": {"amount": {"currencyCode": "INR", "units": "100"}, "paymentProtocol": "INTRA_BANK", "piFrom": "pi-savings-1", "piTo": "pi-deposit-1", "remarks": "Saved rewards into Smart Deposit"}}}', '{"currency_code":  "INR", "units":  100, "nanos":  0}', 'INTERNAL', now() + INTERVAL '21 MINUTE', now() + INTERVAL '17 MINUTE', 'order-ext-21', now(), 'deposit-order-client-request-id', null),
('order-22', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello", "payment_protocol":"UPI"}}', '{"currency_code": "INR", "units": 5000, "nanos": 0}', 'USER_APP', now() + INTERVAL '22 MINUTE', now() + INTERVAL '22 MINUTE', 'order-ext-22', now() + INTERVAL '22 MINUTE', null, null),
('order-23', 'actor-4', 'actor-5', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'USER_APP', now() + INTERVAL '1 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-23', now() + INTERVAL '1 MINUTE', null, null),
('order-24', 'actor-4', 'actor-5', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-4", "pi_to": "pi-5", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '2 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-24', now() + INTERVAL '1 MINUTE', null, null),
('order-25', 'actor-4', 'actor-5', 'P2P_FUND_TRANSFER', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-4", "pi_to": "pi-5", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '3 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-25', now() + INTERVAL '1 MINUTE', null, null),
('order-26', 'actor-4', 'actor-5', 'P2P_COLLECT_SHORT_CIRCUIT', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 500}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-4", "pi_to": "pi-5", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  500, "nanos":  0}', 'USER_APP', now() + INTERVAL '4 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-26', now() + INTERVAL '1 MINUTE', null, null),
('order-27', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '1 MINUTE', now() + INTERVAL '5 MINUTE',  'order-ext-27', now() + INTERVAL '1 MINUTE', 'order-client-req-27', null),
('order-28', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code": "INR", "units": 1000, "nanos": 0}', 'USER_APP', now() + INTERVAL '1 MINUTE', now() + INTERVAL '5 MINUTE',  'order-ext-28', now() + INTERVAL '1 MINUTE', 'order-client-req-28', null),
('order-29', 'actor-1', 'actor-2', 'B2C_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2022-08-16 05:00:00.000000 +05:30', '2022-08-17 00:00:00.000000 +05:30', 'order-ext-id-sample', now() + INTERVAL '2 MINUTE', 'order-client-req-sample', 'wf-req-1'),
('order-30', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2022-08-16 05:00:00.000000 +05:30', '2022-08-17 00:00:00.000000 +05:30', 'order-ext-id-sample-2', now() + INTERVAL '2 MINUTE', 'order-client-req-sample-2', null),
('order-31', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2022-08-16 05:00:00.000000 +05:30', '2022-08-17 00:00:00.000000 +05:30', 'order-ext-id-sample-3', now() + INTERVAL '2 MINUTE', 'order-client-req-sample-3', null),
('order-32', 'actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2022-08-16 05:00:00.000000 +05:30', '2022-08-17 00:00:00.000000 +05:30', 'order-ext-id-sample-4', now() + INTERVAL '2 MINUTE', 'order-client-req-sample-4', null),
('order-33', 'ift-actor-1', 'ift-international-actor-1', 'INTERNATIONAL_FUND_TRANSFER', 'IN_PAYMENT', '{}', '{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'USER_APP',now() + INTERVAL '33 MINUTE', now() + INTERVAL '33 MINUTE', 'order-ext-id-ift-1', now() + INTERVAL '33 MINUTE', 'order-client-req-ift-1', 'ift1234'),
('order-34', 'ift-actor-1', 'ift-international-actor-1', 'INTERNATIONAL_FUND_TRANSFER', 'PAID', '{}', '{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'USER_APP', now() + INTERVAL '34 MINUTE', now() + INTERVAL '34 MINUTE', 'order-ext-id-ift-2', now() + INTERVAL '34 MINUTE', 'order-client-req-ift-2', 'ift1235'),
('order-36', 'ift-actor-2', 'ift-international-actor-1', 'INTERNATIONAL_FUND_TRANSFER', 'IN_PAYMENT', '{}', '{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'USER_APP', now() + INTERVAL '36 MINUTE', now() + INTERVAL '36 MINUTE', 'order-ext-id-ift-3', now() + INTERVAL '36 MINUTE', 'order-client-req-ift-3', 'ift1236'),
('order-37', 'ift-actor-2', 'ift-international-actor-1', 'INTERNATIONAL_FUND_TRANSFER', 'PAID', '{}', '{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'USER_APP', now() + INTERVAL '37 MINUTE', now() + INTERVAL '37 MINUTE', 'order-ext-id-ift-4', now() + INTERVAL '37 MINUTE', 'order-client-req-ift-4', 'ift1237'),
('order-38', 'tt-actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2023-04-07 05:00:00.000000', '2023-04-07 05:10:00.000000' + INTERVAL '10 MINUTE', 'order-ext-id-sample-tt-1', now() + INTERVAL '38 MINUTE', 'order-client-req-sample-tt-1', null),
('order-39', 'tt-actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'PAYMENT_FAILED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2023-04-07 05:00:00.000000', '2023-04-07 05:10:00.000000' + INTERVAL '10 MINUTE', 'order-ext-id-sample-tt-2', now() + INTERVAL '39 MINUTE', 'order-client-req-sample-tt-2', null),
('order-40', 'tt-actor-1', 'actor-2', 'P2P_FUND_TRANSFER', 'PAYMENT_REVERSED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP','2023-04-07 05:00:00.000000', '2023-04-07 05:10:00.000000' + INTERVAL '10 MINUTE', 'order-ext-id-sample-tt-3', now() + INTERVAL '40 MINUTE', 'order-client-req-sample-tt-3', null),
('order-47', 'ift-actor-5', 'ift-actor-6', 'INTERNATIONAL_FUND_TRANSFER', 'PAID', '{}', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'USER_APP','2022-08-16 05:00:00.000000 +05:30', '2022-08-17 00:00:00.000000 +05:30', 'order-ext-ref-id-46', now() + INTERVAL '2 MINUTE', 'order-client-req-id-47', 'order-wf-ref-id-47'),
('order-52', 'actor-2', 'actor-1', 'P2P_FUND_TRANSFER', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '11 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-52', now() + INTERVAL '11 MINUTE', null, null),
('order-53', 'actor-1', 'actor-2', 'ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "salary program enach execution"}}', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'USER_APP', now() + INTERVAL '53 MINUTE', now() + INTERVAL '53 MINUTE', 'order-ext-53', now() + INTERVAL '53 MINUTE', null, null),
('order-54', 'actor-1', 'actor-2', 'ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER', 'PAYMENT_FAILED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "salary program enach execution failed"}}', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'USER_APP', now() + INTERVAL '54 MINUTE', now() + INTERVAL '54 MINUTE', 'order-ext-54', now() + INTERVAL '54 MINUTE', null, null);

-- order_attempts
UPSERT
into order_attempts (id, order_id, stage, num_attempts, req_id, created_at)
VALUES
    (1, 'order-1', 'FULFILLMENT', 0, 'req-1', now() - INTERVAL '5 MINUTE'),
    (2, 'order-1', 'SETTLEMENT', 1, 'req-2', now()),
    (3, 'order-29', 'CREATION', 0, 'req-3','2022-08-16 05:00:00.000000 +05:30'),
    (4, 'order-28', 'CREATION',0, 'req-4', '2022-08-16 05:00:00.000000 +05:30'),
    (5, 'order-27', 'CREATION',0, 'req-5', '2022-08-16 05:00:00.000000 +05:30'),
    (6, 'order-30', 'PAYMENT', 0, 'req-6', '2022-08-16 05:00:00.000000 +05:30'),
     (7, 'order-31', 'PAYMENT', 0, 'req-7', '2022-08-16 05:00:00.000000 +05:30'),
     (8, 'order-32', 'PAYMENT', 0, 'req-8', '2022-08-16 05:00:00.000000 +05:30');

-- transactions
UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
                          trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at, order_ref_id)
VALUES ('transaction-1', 'pi-1', 'pi-2', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null ,'IMPS', 'XYZ', null , '{ "req_id": "req-1", "device_id": "device-1"}', now() + INTERVAL '1 MINUTE', '2020-03-24 07:30:52.940000', null, null, 'order-1'),
       ('transaction-2', 'pi-1', 'pi-2', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null, 'IMPS', 'XYZ', null , '{ "req_id": "req-2", "device_id": "device-2"}', now() + INTERVAL '2 MINUTE', '2020-03-25 07:30:52.940000', null, null, 'order-2'),
       ('transaction-3', 'pi-1', 'pi-2', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'IN_PROGRESS', null, 'IMPS', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-3", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'order-3'),
       ('transaction-4', 'pi-1', 'pi-2', null , null, 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'UNKNOWN', null, 'UPI', 'XYZ', '2020-03-27 07:30:52.940000', '{ "req_id": "req-4", "device_id": "device-4"}', now() + INTERVAL '4 MINUTE', '2020-03-27 07:30:52.940000', null, null, 'order-4'),
       ('transaction-5', 'pi-1', 'pi-2', 'FED-5', 'FED-5', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'FAILED', null, 'IMPS', 'XYZ', '2020-03-28 07:30:52.940000', '{ "req_id": "req-5", "device_id": "device-5"}', now() + INTERVAL '5 MINUTE', '2020-03-28 07:30:52.940000',null, null, 'order-5'),
       ('transaction-6', 'pi-1', 'pi-2', 'FED-6', 'FED-6', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'IMPS', 'XYZ', '2020-03-29 07:30:52.940000', '{ "req_id": "req-6"}', now() + INTERVAL '6 MINUTE', '2020-03-29 07:30:52.940000',null, null, 'order-6' ),
       ('transaction-7', 'pi-1', 'pi-2', 'FED-7', 'FED-7', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'IMPS', 'hello', '2020-03-30 07:30:52.940000', '{ "req_id": "req-7"}', now() + INTERVAL '7 MINUTE', '2020-03-30 07:30:52.940000', null, null, 'order-7' ),
       ('transaction-8', 'pi-1', 'pi-2', 'FED-8', 'FED-8', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'NEFT', 'XYZ', '2020-04-01 07:30:52.940000', '{ "req_id": "req-8", "device_id": "device-8"}', now() + INTERVAL '8 MINUTE', '2020-04-01 07:30:52.940000', null, null, 'order-8' ),
       ('transaction-9', 'pi-1', 'pi-2', 'FED-9', 'FED-9', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'FAILED', null, 'RTGS', 'XYZ', '2020-04-02 07:30:52.940000', '{ "req_id": "req-9"}', now() + INTERVAL '9 MINUTE', '2020-04-02 07:30:52.940000', null, null, 'order-9' ),
       ('transaction-10', 'pi-1', 'pi-2', 'FED-10', 'FED-10', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'UNKNOWN', null, 'IMPS', 'hello', '2020-04-03 07:30:52.940000', '{ "req_id": "req-10"}',now() + INTERVAL '10 MINUTE', '2020-04-03 07:30:52.940000', null, null, 'order-10' ),
        ('transaction-11', 'pi-3', 'pi-2', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null, 'UPI', 'upi-txn', null , '{ "req_id": "req-11", "upi_info": { "merchant_ref_id" : "ref-11"}}', now() + INTERVAL '11 MINUTE', '2020-04-04 07:30:52.940000', null, null, 'order-11' ),
        ('transaction-12', 'pi-ex-3', 'pi-3', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null, 'UPI', 'upi-collect-txn', null , '{ "req_id": "req-12", "upi_info": { "merchant_ref_id" : "ref-12"}}', now() + INTERVAL '12 MINUTE', '2020-04-05 07:30:52.940000', null, null, 'order-12' ),
        ('transaction-13', 'pi-2', 'pi-3', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null, 'UPI', 'upi-collect-short-circuit-txn', null , '{ "req_id": "req-13", "upi_info": { "merchant_ref_id" : "ref-13"}}', now() + INTERVAL '13 MINUTE', '2020-04-06 07:30:52.940000', null, null, 'order-13' ),
        ('transaction-14', 'pi-3', 'pi-ex-3', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', 'REQ_AUTH_RECEIVED', 'UPI', 'incoming-upi-collect-from-ex-payee-psp', null , '{ "req_id": "req-14", "upi_info": { "merchant_ref_id" : "ref-14"}}', now() + INTERVAL '14 MINUTE', '2020-04-07 07:30:52.940000', null, null, 'order-14' ),
        ('transaction-16', 'pi-3', 'pi-ex-3', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'IN_PROGRESS', 'REQ_AUTH_RECEIVED', 'UPI', 'incoming-upi-collect-from-ex-payee-psp', null , '{ "req_id": "req-16", "upi_info": { "merchant_ref_id" : "ref-16"}}', now() + INTERVAL '16 MINUTE', '2020-04-07 07:30:52.940000', null, null, 'order-16' ),
        ('transaction-17', 'pi-1', 'pi-2', 'FED-17', 'FED-17', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'UPI', 'hello', null, '{ "req_id": "req-17"}', now() + INTERVAL '17 MINUTE', '2020-01-30 07:30:52.940000', '2020-05-30 07:30:52.940000', null, 'order-17' ),
        ('transaction-18', 'pi-1', 'pi-2', 'FED-18', 'FED-18', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'UPI', 'hello', null, '{ "req_id": "req-18"}', now() + INTERVAL '18 MINUTE', '2020-01-30 07:30:52.940000', '2020-05-30 07:30:52.940000', null, 'order-18' ),
        ('transaction-19', 'pi-1', 'pi-2', 'FED-19', 'FED-19', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'UPI', 'hello', null, '{ "req_id": "req-19"}', now() + INTERVAL '19 MINUTE', '2020-01-30 07:30:52.940000', '2020-05-30 07:30:52.940000', null, 'order-19' ),
        ('transaction-20', 'pi-1', 'pi-2', 'FED-20', 'FED-20', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'UPI', 'hello', null, '{ "req_id": "req-20"}', now() + INTERVAL '20 MINUTE', '2020-01-30 07:30:52.940000', '2020-05-30 07:30:53.940000', null, 'order-19' ),
        ('transaction-22', 'pi-1', 'pi-2', null , null, 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'UNKNOWN', null, 'UPI', 'XYZ', '2020-03-27 07:30:52.940000', '{ "req_id": "req-22", "device_id": "device-4"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '4 MINUTE', null, null, 'order-22' ),
		('transaction-23', 'pi-4', 'pi-5', null , null, 'FEDERAL_BANK',
		'{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'UNKNOWN', null, 'UPI', 'XYZ', '2020-03-27 07:30:52.940000', '{ "req_id": "req-23"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '60 MINUTE',  null, null, 'order-23' ),
 		('transaction-24', 'pi-4', 'pi-5', null , null, 'FEDERAL_BANK',
		'{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'UNKNOWN', null, 'UPI', 'XYZ', '2020-03-27 07:30:52.940000', '{ "req_id": "req-24"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '60 MINUTE', null,  now() + INTERVAL '65 MINUTE', 'order-24' ),
		('transaction-25', 'pi-4', 'pi-5', null , null, 'FEDERAL_BANK',
 		'{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'UNKNOWN', null, 'IMPS', 'XYZ', '2020-03-27 07:30:52.940000', '{ "req_id": "req-25"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '60 MINUTE',  now() + INTERVAL '63 MINUTE',  now() + INTERVAL '65 MINUTE', 'order-25' ),
 		('transaction-26', 'pi-4', 'pi-5', 'FED-26' , 'FED-26', 'FEDERAL_BANK',
 		'{"currency_code":  "INR", "units":  500, "nanos":  0}', 'UNKNOWN', null, 'UPI', 'upi-collect-short-circuit-txn', '2020-03-27 07:30:52.940000', '{ "req_id": "req-26"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '60 MINUTE',  now() + INTERVAL '63 MINUTE', null, 'order-26' ),
	    ('transaction-27', 'pi-4', 'pi-5', 'FED-27' , 'FED-27', 'FEDERAL_BANK',
		'{"currency_code":  "INR", "units":  500, "nanos":  0}', 'CREATED', null, 'UPI', 'upi-collect-short-circuit-txn', '2020-03-27 07:30:52.940000', '{ "req_id": "req-27"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '60 MINUTE',  now() + INTERVAL '63 MINUTE', null, 'order-27' ),
      	('transaction-46', 'pi-ift-1', 'pi-ift-2', 'FED-41', 'FED-46', 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'UPI', 'first-leg-txn', '2020-04-01 07:30:52.940000', '{ "req_id": "req-46"}', now() + INTERVAL '8 MINUTE', now() + INTERVAL '8 MINUTE', now() + INTERVAL '8 MINUTE', null, 'order-47' ),
    	('transaction-47', 'pi-ift-2', 'pi-ift-3', 'FED-42' , 'FED-47', 'FEDERAL_BANK',
		'{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'SWIFT', 'second-leg-txn', '2020-04-02 07:30:52.940000', '{ "req_id": "req-47"}', now() + INTERVAL '22 MINUTE', now() + INTERVAL '22 MINUTE',   now() + INTERVAL '22 MINUTE', null, 'order-47' ),
         ('transaction-52', 'pi-3', 'pi-2', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null, 'UPI', 'upi-txn', null , '{ "req_id": "req-52", "upi_info": { "merchant_ref_id" : "ref-52", "purpose" : "41"}}', now() + INTERVAL '11 MINUTE', '2020-04-04 07:30:52.940000', null, null, 'order-52' );

UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
                          trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at, dedupe_id , order_ref_id)
VALUES ('transaction-28', 'pi-1', 'pi-2', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'IN_PROGRESS', null, 'IMPS', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-28", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'CgtyZXF1ZXN0aWQtMQ==' , 'order-28');

UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
                          trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at,partner_ref_id_debit,partner_ref_id_credit , order_ref_id)
VALUES ('transaction-29', 'pi-5', 'pi-6', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'IN_PROGRESS', null, 'IMPS', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-29", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null,'','partner-ref-id-credit-1' ,'order-29'),
       ('transaction-30', 'pi-5', 'pi-6', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'IN_PROGRESS', null, 'IMPS', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-30", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null,'partner-ref-id-debit-1','' , 'order-30');


UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
				   trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at, dedupe_id, order_ref_id)
VALUES ('transaction-33', 'pi-ift-user-1', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'SUCCESS', null, 'INTRA_BANK', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-ift-33", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'CgtyZXF1ZXN0aWQtLL==', 'order-33'),
	   ('transaction-34', 'pi-ift-user-1', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'SUCCESS', null, 'INTRA_BANK', 'XYZP', '2020-03-26 07:30:52.940000', '{ "req_id": "req-ift-34", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'CgtyZXF1ZXN0aWQtML==', 'order-34'),
	   ('transaction-35', 'paymentinstrument-pool-account-federal', 'pi-ift-international-1', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'SUCCESS', null, 'INTRA_BANK', 'XYZP', '2020-03-26 07:30:52.940000', '{ "req_id": "req-ift-34_dummy", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'dummy_CgtyZXF1ZXN0aWQtML==', 'order-34'),
	   ('transaction-36', 'pi-ift-user-2', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 10287}', 'SUCCESS', null, 'INTRA_BANK', 'XYZP', '2020-03-26 07:30:52.940000', '{ "req_id": "req-ift-36", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'CgtyZXF1ZXN0aggQtMQ==', 'order-36'),
	   ('transaction-37A', 'pi-ift-user-2', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 10287}', 'SUCCESS', null, 'INTRA_BANK', 'XYZP', '2020-03-26 07:30:52.940000', '{ "req_id": "req-ift-37A", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'BgtyZXF1ZXN0aggQtMQ==', 'order-37'),
	   ('transaction-37B', 'paymentinstrument-pool-account-federal', 'pi-ift-international-1', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 10287}', 'SUCCESS', null, 'INTRA_BANK', 'XYZPA', '2020-03-26 07:30:52.940000', '{ "req_id": "req-ift-37B", "device_id": "device-3"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'BAtyZXF1ZXN0aggQtMQ==', 'order-37');

UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
				   trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at, dedupe_id, order_ref_id)
VALUES ('transaction-38', 'pi-tt-user-1', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 5********, "units": 20287}', 'IN_PROGRESS', null, 'INTRA_BANK', 'XYZ', '2023-04-07 05:00:00.000000' + INTERVAL '4 MINUTE', '{ "req_id": "req-tt-1", "device_id": "device-3"}',
        '2023-04-07 05:00:00.000000' + INTERVAL '3 MINUTE', '2023-04-07 05:00:00.000000' + INTERVAL '5 MINUTE', null, null, 'TTgtyZXF1ZXN0aWQtLL==', 'order-38'),
	   ('transaction-39', 'pi-tt-user-1', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 0, "units": 20289}', 'FAILED', null, 'INTRA_BANK', 'XYZ', '2023-04-07 05:00:00.000000' + INTERVAL '4 MINUTE', '{ "req_id": "req-tt-2", "device_id": "device-4"}',
		'2023-04-07 05:00:00.000000' + INTERVAL '3 MINUTE', '2023-04-07 05:00:00.000000' + INTERVAL '5 MINUTE', null, null, 'TTgtyZXF1ZXN0aWQtLN==', 'order-39'),
		('transaction-40', 'pi-tt-user-1', 'paymentinstrument-pool-account-federal', null , null, 'FEDERAL_BANK',
		'{"currency_code": "INR", "nanos": 50, "units": 20}', 'REVERSED', null, 'INTRA_BANK', 'XYZ', '2023-04-07 05:00:00.000000' + INTERVAL '4 MINUTE', '{ "req_id": "req-tt-3", "device_id": "device-5"}',
		'2023-04-07 05:00:00.000000' + INTERVAL '3 MINUTE', '2023-04-07 05:00:00.000000' + INTERVAL '5 MINUTE', null, null, 'TTgtyZXF1ZXN0aWQtLM==', 'order-40');

UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
                          trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at, dedupe_id , order_ref_id)
VALUES ('transaction-61', 'pi-1', 'pi-2', null , null , 'FEDERAL_BANK',
		'{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'IN_PROGRESS', null, 'IMPS', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-61", "device_id": "device-3"}', now() - INTERVAL '3 MINUTE', now() - INTERVAL '3 MINUTE', null, null, 'ChtyZXF1ZXN0aWQtMQ==' , 'order-28');

-- account_pis
UPSERT
INTO account_pis (id, actor_id, account_id, account_type, pi_id)
VALUES ('account-pi-1', 'actor-user-1', 'Test-Savings-1', 'SAVINGS', 'pi-1'),
       ('account-pi-deposit-1', 'actor-user-1', 'deposit-account-sd-1', 'SMART_DEPOSIT', 'pi-deposit-1');

-- aa_account_pis
UPSERT
INTO aa_account_pis (id, actor_id, account_id, account_type, pi_id)
VALUES ('account-aa-pi-1', 'actor-user-11', 'Test-Savings-aa-1', 'SAVINGS', 'pi-aa-1'),
       ('account-aa-pi-4', 'actor-user-12', 'Test-Savings-aa-4', 'SAVINGS', 'pi-aa-4'),
	   ('account-aa-pi-5', 'actor-user-12', 'Test-Savings-aa-5', 'SAVINGS', 'pi-aa-5');

-- actor_pi_resolutions
UPSERT
INTO actor_pi_resolutions (id, actor_from, actor_to, pi_from, pi_to)
VALUES ('actor-ex-user-2-to-actor-user-1', 'actor-ex-user-2', 'actor-user-1', 'pi-ex-3', null),
       ('actor-user-1-to-actor-user-3', 'actor-user-1', 'actor-user-3', null, 'pi-2'),
       ('actor-user-1-to-actor-ex-user-3', 'actor-user-1', 'actor-ex-user-1', null, 'pi-2'),
       ('actor-user-1-to-actor-ex-user-3-13', 'actor-user-1', 'actor-ex-user-2', null, 'pi-3');

-- aa_consents
UPSERT
INTO aa_consents(id, status)
VALUES ('consent1', 'CONSENT_STATUS_HANDLE_READY'),
       ('consent2', 'CONSENT_STATUS_ACTIVE');

-- timelines
UPSERT
INTO timelines (id, primary_actor_id, secondary_actor_id, primary_actor_name, secondary_actor_name, secondary_actor_state, last_event_updated_at, ownership,deleted_at, updated_at)
VALUES ('timeline-actor-user-1-to-actor-2', 'actor-user-1', 'actor-2', 'Jake Peralta', 'Gina Linetti', 'NOT_VISIBLE', '2020-05-12 13:06:13.838736', 'EPIFI_TECH',null, '2022-09-20 15:06:13.838736'),
       ('timeline-actor-3-to-actor-user-1', 'actor-3', 'actor-user-1', 'Rosa Diaz', 'Jake Peralta', 'VISIBLE', '2020-05-13 15:06:13.838736', 'EPIFI_TECH',null, '2022-09-20 17:06:12.838736'),
	   ('timeline-actor-10-to-actor-user-11', 'actor-5', 'actor-aa-1', 'Rosa Diaz', 'Jake Peralta', 'VISIBLE', '2021-11-23 15:06:13.838736', 'EPIFI_WEALTH',null, '2021-09-20 17:06:13.838736'),
       ('timeline-actor-5-to-actor-user-11', 'actor-5', 'actor-2', 'Rosa Diaz', 'Jake Peralta', 'VISIBLE', '2021-11-23 15:06:13.838736', 'EPIFI_TECH','2021-12-23 15:06:13.838736', '2021-11-20 17:06:13.838736');


-- connected_accounts
UPSERT
INTO connected_accounts(actor_id, aa_ref_number, masked_account_number, fip_id, source)
VALUES ('actor-ex-user-2', '********-0000-0000-0000-********0000', '1', 'hdfcbank', 'SOURCE_AA');
UPSERT
INTO connected_accounts(actor_id, aa_ref_number, masked_account_number, fip_id, source, consent_handle)
VALUES ('actor-ex-user-2', '2111', '2', 'sbi', 'SOURCE_AA', 'consent1');

-- liveness_attempts
UPSERT
INTO liveness_attempts(actor_id, attempt_id, request_id, video_location, otp, status, vendor, vendor_request_id, otp_score, blink_liveness_status)
VALUES ('actor-ex-user-2', '********-e02f-41a1-850f-f8a4f9d6dc86', '********-e02f-41a1-850f-f8a4f9d6dc86', 's3://',
'0001', 'LIVENESS_PENDING', 'KARZA', 'abcd', 90, 'BLINK_LIVENESS_SUCCESS');

UPSERT
INTO liveness_attempts(actor_id, attempt_id, request_id, otp, status)
VALUES ('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dc91', '********-e02f-41a1-850f-f8a4f9d6dc91','0001',
'LIVENESS_OTP_RECEIVED');

UPSERT
INTO liveness_attempts(actor_id, attempt_id, request_id, video_location, otp, status, vendor, vendor_request_id, blink_liveness_status)
VALUES ('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dc88', '********-e02f-41a1-850f-f8a4f9d6dc88', 'test/1.mp4',
        '6789', 'LIVENESS_INVALID_VIDEO', 'KARZA', 'abcde', 'BLINK_LIVENESS_SUCCESS');

UPSERT
INTO liveness_attempts(actor_id, attempt_id, request_id, video_location, otp, status, vendor, vendor_request_id, blink_liveness_status)
VALUES ('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dc89', '********-e02f-41a1-850f-f8a4f9d6dc89', 'test/1.mp4',
        '7890', 'LIVENESS_INVALID_VIDEO', 'KARZA', 'abcde', 'BLINK_LIVENESS_SUCCESS');
UPSERT
INTO liveness_attempts(actor_id, attempt_id, request_id, video_location, otp, status, vendor)
VALUES ('actor-ex-user-2', '********-e02f-41a1-850f-f8a4f9d6dc87', '********-e02f-41a1-850f-f8a4f9d6dc87', 's3://', '0002', 'LIVENESS_PASSED', 'KARZA');

UPSERT
INTO liveness_attempts(actor_id, attempt_id, request_id, otp, status, blink_liveness, created_at)
VALUES ('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dca1', '********-e02f-41a1-850f-f8a4f9d6dca1','0001', 'LIVENESS_PASSED', false, '2020/1/1'),
('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dca2', '********-e02f-41a1-850f-f8a4f9d6dca2','0002', 'LIVENESS_PASSED', false, '2020/3/1'),
('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dca3', '********-e02f-41a1-850f-f8a4f9d6dca3','0003', 'LIVENESS_PASSED', false, '2021/1/1'),
('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dca4', '********-e02f-41a1-850f-f8a4f9d6dca4','0004', 'LIVENESS_PASSED', true, '2020/2/1');

-- face_match_attempts
UPSERT
INTO face_match_attempts(actor_id, attempt_id, request_id, video_location, image_location, status, vendor, vendor_request_id)
VALUES ('actor-ex-user-2', '********-e02f-41a1-850f-f8a4f9d6dc86', '********-e02f-41a1-850f-f8a4f9d6dc86', 's3://',
's3://', 'FACE_MATCH_PENDING', 'KARZA', 'abcd');

UPSERT
INTO face_match_attempts(actor_id, attempt_id, request_id, video_location, image_location, status)
VALUES ('actor-ex-user-2', '********-e02f-41a1-850f-f8a4f9d6dc87', '********-e02f-41a1-850f-f8a4f9d6dc87', 's3://',
's3://', 'FACE_MATCH_PASSED');

-- kyc_attempts
UPSERT
INTO kyc_attempts(actor_id, attempt_id, request_params, liveness_params, face_match_params)
VALUES ('actor-user-1', '********-e02f-41a1-850f-f8a4f9d6dc86', '{}', '{}', '[]');

UPSERT
INTO kyc_attempts(actor_id, attempt_id, request_params, liveness_params, face_match_params)
VALUES ('actor-ex-user-2', '********-e02f-41a1-850f-f8a4f9d6dc87', '{}',
'{"otp": "1234", "request_id": "d1672b73-c654-4756-b594-a23a119e609d", "status": "STATUS_LIVENESS_VIDEO_RECEIVED"}', '[]');

UPSERT
INTO kyc_attempts(actor_id, attempt_id, request_params, liveness_params, face_match_params)
VALUES ('actor-1', '********-e02f-41a1-850f-f8a4f9d6dc88', '{}',
'{"otp": "8282", "request_id": "d1672b73-c654-4756-b594-a23a119e609e", "status": "STATUS_LIVENESS_VIDEO_RECEIVED", "attempt_count": 1}', '[]');

UPSERT
INTO kyc_attempts(actor_id, attempt_id, request_params, liveness_params, face_match_params)
VALUES ('actor-2', '********-e02f-41a1-850f-f8a4f9d6dc89', '{}',
'{"otp": "8383", "request_id": "d1672b73-c654-4756-b594-a23a119e609f", "status": "STATUS_LIVENESS_VIDEO_RECEIVED", "attempt_count": 4}', '[]');

UPSERT
INTO kyc_attempts(actor_id, attempt_id, request_params, liveness_params, face_match_params)
VALUES ('actor-3', '********-e02f-41a1-850f-f8a4f9d6dc90', '{}',
'{"otp": "8484", "request_id": "d1672b73-c654-4756-b594-a23a119e609g", "status": "STATUS_LIVENESS_VIDEO_RECEIVED", "video_location": "s3://random4", "image": {"imageType": "JPEG", "imageUrl": "random"}}',
'[{"reference_image": {"image_url": "random0"}, "request_id": "cb7c5d02-e5c6-41e5-a044-2ccc34935c0a", "status": "STATUS_FACE_MATCH_VIDEO_NOT_RECEIVED"}, {"reference_image": {"image_url": "random1"}, "request_id": "cb7c5d02-e5c6-41e5-a044-2ccc34935c0b", "status": "STATUS_FACE_MATCH_PENDING"}]');


UPSERT
INTO kyc_attempts(actor_id, attempt_id, request_params, liveness_params, face_match_params)
VALUES ('actor-user-3', '********-e02f-41a1-850f-f8a4f9d6d100', '{}',
'{"otp": "8989", "request_id": "d1672b73-c654-4756-b594-a23a119e6088", "status": "STATUS_LIVENESS_VIDEO_NOT_RECEIVED", "attempt_count": 1}', '[]');

UPSERT
INTO "kyc_attempts" ("actor_id", "attempt_id","kyc_progress_status","kyc_type", "request_params", "liveness_params", "face_match_params")
VALUES ('5', '0f5472d6-5800-4182-abf0-4a74c05219ed', 'EKYC_DATA_RECEIVED', 'EKYC', '{}', '{}', '[]') RETURNING "kyc_attempts"."attempt_id";


UPSERT
INTO "kyc_attempts" ("actor_id", "attempt_id","kyc_progress_status","kyc_type", "request_params", "liveness_params", "face_match_params")
VALUES ('2', '013f765e-1de8-4754-aa01-9ef9a26c99e3', 'CKYC_DOWNLOAD_FOUND', 'CKYC', '{}', '{}', '[]') RETURNING "kyc_attempts"."attempt_id";



UPSERT
INTO "kyc_attempts" ("actor_id", "attempt_id","kyc_progress_status","kyc_type", "request_params", "liveness_params", "face_match_params")
VALUES ('5', '0f5472d6-5800-4182-abf0-4a74c05219ef', 'CKYC_DOWNLOAD_FOUND', 'CKYC', '{}', '{}', '[]') RETURNING "kyc_attempts"."attempt_id";

UPSERT
INTO "kyc_summaries" ("actor_id","kyc_attempt_id", "ckyc_attempt_id")
VALUES ('5','0f5472d6-5800-4182-abf0-4a74c05219ed', '0f5472d6-5800-4182-abf0-4a74c05219ef')
RETURNING "kyc_summaries"."actor_id";

UPSERT
INTO "kyc_summaries" ("actor_id","kyc_attempt_id", "ckyc_attempt_id")
VALUES ('2','013f765e-1de8-4754-aa01-9ef9a26c99e3', '013f765e-1de8-4754-aa01-9ef9a26c99e3')
RETURNING "kyc_summaries"."actor_id";

UPSERT
INTO "kyc_vendor_data" ("kyc_attempt_id","payload_type","payload","ttl_in_sec")
VALUES ('0f5472d6-5800-4182-abf0-4a74c05219ed','2',
        '{"ekyc_record":{"gender":"FEMALE", "name": { "first_name":"Jolly", "last_name":"Joseph", "honorific":"Mr"}}}', 0)
RETURNING "kyc_vendor_data"."id";

-- auth_factor_updates records as foreign key constraints for token_stores testing
UPSERT
INTO "auth_factor_updates" ("id") VALUES ('0aa51103-fde4-4c4d-9bd6-f1bdcc415d4d');

UPSERT
INTO public.psp_keys
( id, psp_org_id, "type", ki, key_value, owner)
VALUES('bfc05f22-2722-4057-bd10-aa0351f591bd','pspOrg-test1', 'type', 'ki', 'keyvalue', 'owner');

UPSERT
INTO verified_address_entries
(id, merchant_vpa, "name", url, key_code, "type", ki, key_value, created_at, updated_at)
VALUES('bfc05f22-2722-4057-bd10-aa0351f591bd', 'test-1', 'name', 'url', 'keycode', 'type', 'ki', 'keyvalue', '2020-07-12 23:43:12.767', '2020-07-12 23:43:12.767');

-- waitlist_users
UPSERT
INTO waitlist_users(user_id, prospect_id, pf_status, pf_request_id, pf_otp_token, pf_data, non_pf_data, profile, cbo_voucher_code, created_at)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591bd', 'bfc05f22-2722-4057-bd10-aa0351f591bd', 'PF_STATUS_SUCCESS', '0001', 'random_token', '{}', '{}', '{"comm_email": "<EMAIL>", "linkedin_url": "something"}', 'abcd', '2020/1/1');

UPSERT
INTO waitlist_users(user_id, prospect_id, pf_status, pf_request_id, pf_otp_token, pf_data, non_pf_data, profile, created_at)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591ca', 'bfc05f22-2722-4057-bd10-aa0351f591cd', 'PF_STATUS_UNSPECIFIED', '0001', 'random_token_11', '{}', '{}', '{"phone_number": {"country_code": 91, "national_number": **********}, "linkedin_url": "something2"}', '2021/1/1');

UPSERT
INTO waitlist_users(user_id, prospect_id, pf_status, pf_request_id, pf_otp_token, pf_data, non_pf_data, profile, created_at)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591ce', 'bfc05f22-2722-4057-bd10-aa0351f591cd', 'PF_STATUS_UNSPECIFIED', '0001', 'random_token_10', '{}', '{}', '{"phone_number": {"country_code": 91, "national_number": **********}}', '2020/2/2');

UPSERT
INTO waitlist_users(user_id, prospect_id, pf_status, pf_request_id, pf_otp_token, pf_data, non_pf_data, profile)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591cd', 'bfc05f22-2722-4057-bd10-aa0351f591bd', 'PF_STATUS_SUCCESS', '0001', 'random_token_2', '{}', '{}', '{"phone_number": {"country_code": 91, "national_number": **********}, "comm_email": "<EMAIL>"}');

UPSERT
INTO waitlist_users(user_id, waitlist_status, app_access_email_status)
VALUES ('cfc05f22-2722-4057-bd10-aa0351f591c0', 'ACCESS_STATUS_EARLY_ACCESS', 'APP_ACCESS_EMAIL_STATUS_SENT'),
('cfc05f22-2722-4057-bd10-aa0351f591c1', 'ACCESS_STATUS_EARLY_ACCESS', 'APP_ACCESS_EMAIL_STATUS_UNSPECIFIED'),
('cfc05f22-2722-4057-bd10-aa0351f591c2', 'ACCESS_STATUS_EARLY_ACCESS', 'APP_ACCESS_EMAIL_STATUS_UNSPECIFIED'),
('cfc05f22-2722-4057-bd10-aa0351f591c3', 'ACCESS_STATUS_WAITLIST', 'APP_ACCESS_EMAIL_STATUS_UNSPECIFIED');

UPSERT
INTO waitlist_users(user_id, waitlist_status)
VALUES ('cfc05f22-2722-4057-bd10-aa0351f591c4', 'ACCESS_STATUS_EARLY_ACCESS');

UPSERT
INTO waitlist_users(user_id, prospect_id, pf_status, pf_request_id, pf_otp_token, pf_data, non_pf_data, profile, waitlist_status, app_access_email_status, finite_code, created_at)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591bf', 'bfc05f22-2722-4057-bd10-aa0351f591bf', 'PF_STATUS_SUCCESS', '0001', 'random_token1', '{}', '{}', '{"phone_number": {"country_code": 91, "national_number": 9999990001}, "comm_email": "<EMAIL>", "linkedin_url": ""}', 'ACCESS_STATUS_EARLY_ACCESS', 'APP_ACCESS_EMAIL_STATUS_SENT', 'FINITECODE', '2020/1/1');

UPSERT
INTO waitlist_users(user_id, prospect_id, pf_status, pf_request_id, pf_otp_token, pf_data, non_pf_data, profile, waitlist_status, app_access_email_status, finite_code, created_at)
VALUES ('b20a0279-5eed-4b19-a7f4-2b620a0665f3', 'b20a0279-5eed-4b19-a7f4-2b620a0665f3', 'PF_STATUS_SUCCESS', '0001', 'random_token2', '{}', '{}', '{"phone_number": {"country_code": 91, "national_number": 9999990002}, "comm_email": "<EMAIL>", "linkedin_url": ""}', 'ACCESS_STATUS_EARLY_ACCESS', 'APP_ACCESS_EMAIL_STATUS_SENT', 'FINITECODE1', '2020/1/1');

---
UPSERT
INTO public.verified_address_entries
(id, merchant_vpa, "name", url, key_code, "type", ki, key_value)
VALUES('4e3deb48-2159-44c5-a3f8-08e1f9804efe','test-urn@okaxis', 'name', 'url', 'keycode', 'type', 'ki', 'MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWWA1w/3rvMYnqt0SU7hhoMI76uYe5VuDg1IMiIT6hFLRDUG7ehR2A6uVxOn1xoqZ8n47I19ul7TcSiBgI5aLV/Slwmg+TlgX5SLLSPEgX925EN/aFgesseHwhVjNuxc6');

UPSERT
INTO public.psp_keys
(id, psp_org_id, "type", ki, key_value, owner)
VALUES('ff671626-e0bf-4c0c-8768-b8262999db80' ,'400021', 'type', 'ki', 'MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWWA1w/3rvMYnqt0SU7hhoMI76uYe5VuDg1IMiIT6hFLRDUG7ehR2A6uVxOn1xoqZ8n47I19ul7TcSiBgI5aLV/Slwmg+TlgX5SLLSPEgX925EN/aFgesseHwhVjNuxc6', 'owner');

-- verified_address_entry without key_value
UPSERT
INTO public.verified_address_entries
(id, merchant_vpa, "name", url, key_code, "type", ki)
VALUES('afabe43b-df01-4025-88eb-81edb921e8e1','urn-without-key@okaxis', 'name', 'url', 'keycode', 'type', 'ki');

-- upi-account-info create vpa
UPSERT
INTO public.upi_account_infos
	(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state)
	VALUES('test-create-vpa-account-id', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'create-vpa@fede','INITIATED');
UPSERT
INTO public.upi_account_infos
	(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state)
	VALUES('test-create-vpa-account-id3', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'create-vpa@fede','VPA_CREATED');
UPSERT
INTO public.upi_account_infos
	(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state)
	VALUES('test-create-vpa-account-id4', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'create-vpa@fede','PI_CREATED');
UPSERT
INTO public.upi_account_infos
	(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state, list_account_called_at)
	VALUES('test-create-vpa-account-id2', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'create-vpa@fede','ACC_REF_LINKED', '2021-07-12 12:03:47');

UPSERT
INTO public.upi_account_infos
(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state)
VALUES('test-create-vpa-account-id10', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'ironman@okaxis','PI_ACCOUNT_CREATED');

UPSERT
INTO public.upi_account_infos
(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state)
VALUES('test-vpa-account-di-0', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'treatev@fede','PI_ACCOUNT_CREATED');


UPSERT
INTO public.upi_account_infos
(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa, state)
VALUES('SV200628Sn4NrOePSTaxSObnnaLxdA==', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'hulk@okaxis','PI_ACCOUNT_CREATED');

-- upi-account-info state update
UPSERT
INTO public.upi_account_infos
(account_id, allowed_cred, pin_set_state, is_aadhaar_banking_enabled, vpa,state)
VALUES('update-test-1', '{"CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}]}', 'PIN_NOT_SET', 'false', 'hulk@okaxis','ACC_REF_LINKED');

-- deposit_accounts
UPSERT
INTO public.deposit_accounts
(id, actor_id, account_number, name, type, scheme_code, state, principal_amount, running_balance, maturity_amount, maturity_date, interest_rate, renew_info, operative_account_number, repay_account_number, partner_bank, ifsc_code, term, created_at, updated_at, deleted_at, is_add_funds_allowed, provenance)
VALUES ('deposit-account-sd-1', 'actor-user-1', '**********', 'Bike', 'SMART_DEPOSIT', 'SMART_DEPOSIT', 'CREATED',
        '{"currency_code": "INR", "units": 100}', '{"currency_code": "INR", "units": 100}',
        '{"currency_code": "INR", "units": 110}',
        '2025-05-12 13:06:13.838736', '10',
        '{"additionalAmount": "", "amount": null, "debitAccountNumber": "", "isAutoRenewable": false, "option": "RENEW_OPTION_UNSPECIFIED"}',
        '*********0', '*********0', 'FEDERAL_BANK', 'FDRL0001001',
        '{"days": 7, "months": 0}', '2020-08-25 19:56:42.193857', '2020-08-25 19:58:21.838706', null, true, 'USER_APP'),
       ('deposit-account-sd-2', 'actor-user-1', '**********', 'Phone', 'SMART_DEPOSIT', 'SMART_DEPOSIT', 'PRECLOSED',
        '{"currency_code": "INR", "units": 100}', '{"currency_code": "INR", "units": 100}',
        '{"currency_code": "INR", "units": 110}',
        '2020-05-12 13:06:13.838736', '10',
        '{"additionalAmount": "", "amount": null, "debitAccountNumber": "", "isAutoRenewable": false, "option": "RENEW_OPTION_UNSPECIFIED"}',
        '*********0', '*********0', 'FEDERAL_BANK', 'FDRL0001001',
        '{"days": 0, "months": 12}', '2020-08-26 19:56:42.193857', '2020-08-26 19:58:21.838706', null, true, 'USER_APP'),
       ('deposit-account-sd-3', 'actor-user-1', '**********', 'New Car', 'SMART_DEPOSIT', 'SMART_DEPOSIT', 'CREATED',
        '{"currency_code": "INR", "units": 100}', '{"currency_code": "INR", "units": 100}',
        '{"currency_code": "INR", "units": 110}',
        '2025-05-12 13:06:13.838736', '10',
        '{"additionalAmount": "", "amount": null, "debitAccountNumber": "", "isAutoRenewable": true, "option": "MATURITY_ONLY"}',
        '*********0', '*********0', 'FEDERAL_BANK', 'FDRL0001001',
        '{"days": 21, "months": 12}', '2020-08-27 19:50:42.193857', '2020-08-27 19:52:21.838706', null, true, 'REWARDS_APP'),
       ('deposit-account-fd-1', 'actor-user-1', '**********', 'Car', 'FIXED_DEPOSIT', 'FD_CASH_CERTIFICATE',
        'CREATED', '{"currency_code": "INR", "units": 100}', '{"currency_code": "INR", "units": 100}',
        '{"currency_code": "INR", "units": 107}', '2020-05-12 13:06:13.838736', '7',
        '{"additionalAmount": "", "amount": null, "debitAccountNumber": "", "isAutoRenewable": true, "option": "MATURITY_ONLY"}',
        '*********0', '*********0', 'FEDERAL_BANK', 'FDRL0001001', '{"days": 21, "months": 12}',
        '2020-08-27 19:56:42.193857', '2020-08-27 19:58:21.838706', null, true, 'USER_APP');

-- deposit_requests
UPSERT
INTO public.deposit_requests (id, request_id, client_request_id, deposit_account_id, type, state, deposit_info, partner_bank, created_at, updated_at, deleted_at, actor_id, last_attempt)
VALUES
       -- Transient failure in first attempt, succeeds in second attempt leading to creation of SD 'deposit-account-sd-1'
       ('deposit-request-create-sd-1', 'request-id-1', 'client-request-id-1', NULL,
        'CREATE', 'REQUEST_FAILED',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "name": "Bike", "operativeAccountNumber": "*********0", "renewInfo": {}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 7}, "type": "SMART_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-25 17:56:42.193857', '2020-08-25 17:57:21.838706',
        '2020-08-25 17:59:21.838706', 'actor-user-1', false),
       ('deposit-request-create-sd-1-retry-1', 'request-id-1-retry', 'client-request-id-1', 'deposit-account-sd-1',
        'CREATE', 'REQUEST_SUCCESS',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "name": "Bike", "operativeAccountNumber": "*********0", "renewInfo": {}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 7}, "type": "SMART_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-25 17:58:42.193857', '2020-08-25 17:59:21.838706',
        null, 'actor-user-1', false),

       -- Succeeds in first attempt leading to creation of SD 'deposit-account-sd-2'
       ('deposit-request-create-sd-2', 'request-id-2', 'client-request-id-2', 'deposit-account-sd-2',
        'CREATE', 'REQUEST_SUCCESS',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "name": "Phone", "operativeAccountNumber": "*********0", "renewInfo": {}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"months": 12}, "type": "SMART_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-26 19:56:42.193857', '2020-08-26 19:57:21.838706',
        null, 'actor-user-1', false),

       -- Succeeds in first attempt leading to pre-closure of SD 'deposit-account-sd-2'
       ('deposit-request-close-sd-2', 'request-id-2-close', 'client-request-id-2-close',
        'deposit-account-sd-2',
        'PRECLOSE', 'REQUEST_SUCCESS',
        '{"accountId": "deposit-account-sd-2", "actorId": "actor-user-1", "type": "SMART_DEPOSIT", "repayAccountNumber":"*********0", "requestType": "PRECLOSE", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-27 20:56:42.193857', '2020-08-27 20:57:21.838706',
        null, 'actor-user-1', false),

        -- Pre close deposit request in INITIATED state.
        ('deposit-request-close-sd-1', 'request-id-1-close', 'client-request-id-1-close',
        'deposit-account-sd-1',
        'PRECLOSE', 'REQUEST_INITIATED',
        '{"accountId": "deposit-account-sd-1", "actorId": "actor-user-1", "type": "SMART_DEPOSIT", "repayAccountNumber":"*********0", "requestType": "PRECLOSE" ,"vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-27 20:56:42.193857', '2020-08-27 20:57:21.838706',
        null, 'actor-user-1', false),

       -- Succeeds in first attempt leading to creation of FD 'deposit-account-fd-1'
       ('deposit-request-create-fd-1', 'request-id-3', 'client-request-id-3', 'deposit-account-fd-1',
        'CREATE', 'REQUEST_SUCCESS',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "interestPayout": "ON_MATURITY", "name": "Car", "operativeAccountNumber": "*********0", "renewInfo": {"isAutoRenewable": true, "option": "MATURITY_ONLY"}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 21, "months": 12}, "type": "FIXED_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-28 19:56:42.193857', '2020-08-28 19:57:21.838706',
        null, 'actor-user-1', false),

       -- Transient failure in first attempt and permanent failure in second attempt
       ('deposit-request-create-fd-2', 'request-id-4', 'client-request-id-4', NULL,
        'CREATE', 'REQUEST_FAILED',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "interestPayout": "ON_MATURITY", "name": "Car 2", "operativeAccountNumber": "*********0", "renewInfo": {"isAutoRenewable": true, "option": "MATURITY_ONLY"}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 11, "months": 24}, "type": "FIXED_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-29 19:56:42.193857', '2020-08-29 19:57:21.838706',
        '2020-08-28 19:59:21.838706', 'actor-user-1', false),
       ('deposit-request-create-fd-2-retry-1', 'request-id-4-retry', 'client-request-id-4', NULL,
        'CREATE', 'REQUEST_FAILED',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "interestPayout": "ON_MATURITY", "name": "Car 2", "operativeAccountNumber": "*********0", "renewInfo": {"isAutoRenewable": true, "option": "MATURITY_ONLY"}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 11, "months": 24}, "type": "FIXED_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-29 19:58:42.193857', '2020-08-29 19:59:21.838706',
        null, 'actor-user-1', true),

       -- Deposit request of type 'CREATE' for FD in initiated state
       ('deposit-request-create-fd-3', 'request-id-5', 'client-request-id-5', NULL,
        'CREATE', 'REQUEST_INITIATED',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "interestPayout": "ON_MATURITY", "name": "Vacay", "operativeAccountNumber": "*********0", "renewInfo": {}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 21, "months": 48}, "type": "FIXED_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "USER_APP"}',
        'FEDERAL_BANK', '2020-08-30 19:56:42.193857', '2020-08-30 19:57:21.838706',
        null, 'actor-user-1', false),

       -- Deposit request of type 'CREATE' for SD in success state
       ('deposit-request-create-sd-3', 'request-id-6', 'client-request-id-6', 'deposit-account-sd-3',
        'CREATE', 'REQUEST_SUCCESS',
        '{"actorId": "actor-user-1", "amount": {"currencyCode": "INR", "units": "100"}, "interestPayout": "ON_MATURITY", "name": "New Car", "operativeAccountNumber": "*********0", "renewInfo": {"isAutoRenewable": true, "option": "MATURITY_ONLY"}, "repayAccountNumber": "*********0", "requestType": "CREATE", "term": {"days": 21, "months": 12}, "type": "SMART_DEPOSIT", "vendor": "FEDERAL_BANK", "depositAccountProvenance": "REWARDS_APP"}',
        'FEDERAL_BANK', '2020-08-28 19:56:42.193857', '2020-08-28 19:57:21.838706',
        null, 'actor-user-1', false);

-- pin_code_master
UPSERT
INTO pin_code_master
(id, pin_code, division, region, circle, taluk, district, state)
VALUES ('********-0000-0000-0000-********0001','110009', 'Division1', 'Region1', 'Circle1', 'Taluk1', 'District1', 'State1'),
        ('********-0000-0000-0000-********0002','160014', 'Division2', 'Region2', 'Circle2', 'Taluk2', 'District2', 'State2'),
        ('********-0000-0000-0000-********0003','160014', 'Division3', 'Region3', 'Circle3', 'Taluk3', 'District3', 'State3'),
        ('********-0000-0000-0000-********0004','122001', 'Division4', 'Region4', 'Circle4', 'Taluk4', 'Gurgaon', 'Haryana'),
        ('********-0000-0000-0000-********0005','122001', 'Division5', 'Region5', 'Circle5', 'Taluk5', 'Gurgaon', 'Haryana'),
    	('********-0000-0000-0000-********0006','686501','Changanacherry','Kochi','Kerala','Kottayam','Kottayam','KERALA');


-- referrals
UPSERT
INTO referrals(actor_id, attempt_id, referral_type, referral_code, referral_limit)
VALUES ('actor-ex-user-2', 'afabe43b-df01-4025-88eb-81edb921e8e1', 'REFERRAL_TYPE_WAITLIST', 'abcd2', 5),
('actor-ex-user-1', 'afabe43b-df01-4025-88eb-81edb921e8e2', 'REFERRAL_TYPE_ORIGINALS', 'abcd3', 5),
('actor-user-1', 'afabe43b-df01-4025-88eb-81edb921e8e3', 'REFERRAL_TYPE_FIRST_MOVERS', 'abcd4', 5),
('actor-user-3', 'afabe43b-df01-4025-88eb-81edb921e8e4', 'REFERRAL_TYPE_FIRST_MOVERS', 'abcd5', 5),
('actor-fi', 'afabe43b-df01-4025-88eb-81edb921e8e5', 'REFERRAL_TYPE_ORIGINALS', 'referralfi', 100000),
('actor-fi', 'afabe43b-df01-4025-88eb-81edb921e8e6', 'REFERRAL_TYPE_SEEKERS', 'referralfiseeker', 100000);

-- referral_relations
UPSERT
INTO referral_relations(referrer_actor_id, referee_actor_id) VALUES ('actor-ex-user-2', 'actor-1');
UPSERT
INTO referral_relations(referrer_actor_id, referee_actor_id) VALUES ('actor-ex-user-2', 'actor-2');

-- psp key shared by federal
UPSERT
INTO public.psp_keys
(id, psp_org_id, "type", ki, key_value, owner)
VALUES('ff671626-e0bf-4c0c-8768-b8262999db99' ,'159049', 'type', 'ki', 'MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtouua/Rm5SbqzF/O53yQa7o3B23EF8GJ8ccMuxDA6kr33XF9ovtlFwPQ9vwejItA7FI08XLJbxHd6p/x1DBclg==', 'owner');

-- cKYC State Code to State Mapping
UPSERT
INTO ckyc_state_codes
VALUES('DL', 'Delhi'),
        -- for acceptance tests:
        ('KA', 'Karnataka'),
        ('KL', 'Kerala'),
        ('XX', 'Other');

-- vendorAreaCodes
UPSERT
INTO vendor_area_codes
VALUES('FEDERAL_BANK', 'KARNATAKA', 'BANGALORE', 'KA','BGR'),
        ('FEDERAL_BANK', 'KARNATAKA', 'BIDAR', 'KA', 'BID'),
      ('FEDERAL_BANK','ANDHRA PRADESH','KURNOOL','AP','KURN'),
      ('FEDERAL_BANK','ANDHRA PRADESH','KURNOOL','APN','KURN'),
      ('FEDERAL_BANK','DADRA AND NAGAR HAV.','BASTI','DN','BAST'),
        -- for acceptance tests:
        ('FEDERAL_BANK', 'KARNATAKA', 'NORTH BANGLORE', 'KA', 'XXBGR'),
        ('FEDERAL_BANK', 'KERALA', 'PALA', 'KL', 'XXPGT');

--deviceLocation
UPSERT
INTO device_locations(id, latitude, longitude)
VALUES ('4e3deb48-2159-44c5-a3f8-08e1f9804efe',11.2,113.2),
('32651ee4-21ac-49a6-a5f0-7de2822eb8b6', 21.4944, 83.9635);

UPSERT
INTO pi_state_logs(pi_id, source, state, reason)
VALUES ('pi-1', 'SYSTEM', 'CREATED',''),
       ('pi-1', 'EPIFI_USER', 'SUSPENDED','Suspended by user');

UPSERT
INTO account_upi_pin_infos (id, account_id, user_action, status)
VALUES(1, 'Test-Savings-4', 'PIN_SET', 'SUCCESS'),
      (2, 'Test-Savings-4', 'PIN_CHANGE', 'SUCCESS'),
      (3, 'Test-Savings-5', 'PIN_CHANGE', 'SUCCESS');

UPSERT
INTO account_upi_pin_infos (id, account_id, user_action, status, created_at)
VALUES(4, 'Test-Savings-6', 'PIN_RESET', 'SUCCESS', '2021-07-31 01:01:01'),
      (5, 'Test-Savings-6', 'PIN_SET', 'SUCCESS', '2021-07-31 01:05:01'),
      (6, 'Test-Savings-6', 'PIN_RESET', 'INITIATED', '2021-07-31 01:05:02'),
      (7, 'Test-Savings-6', 'PIN_RESET', 'SUCCESS', '2021-07-31 01:05:03');

-- nominees
UPSERT
INTO nominees (id, actor_id, relationship, name, dob, address, address_type, phone_number, email_id, guardian_info,pan, document, created_at, updated_at)
VALUES ('NOM001', 'actor-001', 'BROTHER', 'Tom Riddle', '2015-01-01 00:00:00+00:00', '{"locality": "Locality", "regionCode": "Region"}', 'PERMANENT', '{"country_code": 91, "national_number": **********}', '<EMAIL>',
        '{"relationship": "DE_FACTO_GUARDIAN", "contactInfo": {"address": {"locality": "Locality", "regionCode": "Region"}, "addressType": "PERMANENT", "emailId": "<EMAIL>", "phoneNumber": {"countryCode": 91, "nationalNumber": "**********"}}, "name": "Voldemort"}', NULL,NULL,'2021-01-01 01:01:01', '2021-01-01 01:01:01'),
        ('NOM002', 'actor-001', 'SISTER', 'Rowena Ravenclaw', '2015-01-01 00:00:00+00:00', '{"locality": "Locality", "regionCode": "Region"}', 'PERMANENT', '{"country_code": 91, "national_number": **********}', '<EMAIL>',
        '{"relationship": "DE_FACTO_GUARDIAN", "contactInfo": {"address": {"locality": "Locality", "regionCode": "Region"}, "addressType": "PERMANENT", "emailId": "<EMAIL>", "phoneNumber": {"countryCode": 91, "nationalNumber": "**********"}}, "name": "Voldemort"}', NULL,NULL,'2021-01-01 01:01:02', '2021-01-01 01:01:02'),
        ('NOM003', 'actor-002', 'BROTHER', 'Severus Snape', '1990-09-30 20:50:08+00:00', '{"locality": "Locality", "regionCode": "Region"}', 'PERMANENT', '{"country_code": 91, "national_number": **********}', '<EMAIL>',
        '{}', NULL,NULL,'2021-01-01 01:01:03', '2021-01-01 01:01:03'),
	   ('NOM004', 'actor-003', 'BROTHER', 'Top Snape', '1990-09-30 20:50:08+00:00', '{"locality": "Locality", "regionCode": "Region"}', 'PERMANENT', '{"country_code": 91, "national_number": **********}', '<EMAIL>',
		'{}', NULL,'{"documentType": "NOMINEE_DOCUMENT_TYPE_PAN", "documentNumber": "**********"}','2021-01-01 01:01:03', '2021-01-01 01:01:03'),
	   ('NOM005', 'actor-004', 'BROTHER', 'Snape Top', '1990-09-30 20:50:08+00:00', '{"locality": "Locality", "regionCode": "Region"}', 'PERMANENT', '{"country_code": 91, "national_number": **********}', '<EMAIL>',
		'{}', '**********',NULL,'2021-01-01 01:01:03', '2021-01-01 01:01:03');

-- golden_tickets
UPSERT
INTO golden_tickets(referral_code, golden_ticket_code)
VALUES ('abcd2', 'abacda2'),
('abcd2', 'random'),
('abcd3', 'abAcd3F'),
('abcd4', 'abAcd4F'),
('abcd5', 'abAcd5F'),
('referralfiseeker', 'FINITECODE'),
('referralfi', 'FIORIGINAL');

-- shipping_preferences
UPSERT
INTO shipping_preferences
VALUES ('SP001', 'AC001', 'DEBIT_CARD', 'MAILING', '2021-01-01 01:01:01', '2021-01-01 01:01:01'),
('SP002', 'AC002', 'DEBIT_CARD', 'PERMANENT', '2021-01-01 01:01:01', '2021-01-01 01:01:01'),
('SP004', 'AC002', 'DEBIT_CARD', 'MAILING', '2021-01-01 01:02:01', '2021-01-01 01:02:01'),
('SP003', 'AC002', 'DEBIT_CARD', 'SHIPPING', '2021-01-01 01:03:01', '2021-01-01 01:03:01');

-- Cards
UPSERT
INTO cards (id,actor_id,state,"type",form,issuer_bank,network_type,bank_identifier,created_at,updated_at,deleted_at,controls,basic_info,group_id,card_category,savings_account_id,pin_set_otp_token) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e998','actor-2','CREATED','DEBIT','PHYSICAL','FEDERAL_BANK','VISA','','2020-10-31 23:49:39.713','2020-10-31 23:49:39.713',NULL,NULL,NULL,'e11ba678-ad75-472d-8dbe-809ef598e9ee','','Test-Savings-1',NULL);

UPSERT
INTO cards (id,actor_id,state,"type",form,issuer_bank,network_type,bank_identifier,created_at,updated_at,deleted_at,controls,basic_info,group_id,card_category,savings_account_id,pin_set_otp_token) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e990','actor-2','CREATED','DEBIT','PHYSICAL','FEDERAL_BANK','VISA','','2020-10-31 23:49:39.713','2020-10-31 23:49:39.713',NULL,NULL,NULL,'e11ba678-ad75-472d-8dbe-809ef598e9ee','','Test-Savings-1',NULL);

-- physical_card_dispatch_requests
UPSERT
INTO physical_card_dispatch_requests (id,card_id,state,request_id,num_retries,failure_response_code,failure_response_reason,created_at,updated_at,deleted_at,details,client_req_id,sub_status,current_stage) VALUES
('5546422f-c1a6-4d24-ae35-024025c2f67f', 'a5004f18-5d52-4991-82a9-2a1e3010e990','FAILED','request_id_1',2,'','','2023-09-04 00:00:00.560000 +00:00','2023-09-05 00:00:00.000000 +00:00',NULL,'{}','',NULL,NULL),
('4221ce52-5284-11ee-be56-0242ac120002', 'a5004f18-5d52-4991-82a9-2a1e3010e990','FAILED','request_id_2',2,'','','2023-09-04 00:00:00.560000 +00:00','2023-09-07 00:00:00.560000 +00:00',NULL,'{}','',NULL,NULL),
('7c188876-5284-11ee-be56-0242ac120002', 'a5004f18-5d52-4991-82a9-2a1e3010e990','SUCCESS','request_id_3',2,'','','2023-09-05 00:00:00.560000 +00:00','2023-09-08 00:00:00.560000 +00:00',NULL,'{}','',NULL,NULL);

-- card_limits
UPSERT
INTO card_limits
(card_id, card_limit_data, state, created_at, updated_at, deleted_at)
VALUES('a5004f18-5d52-4991-82a9-2a1e3010e990', '{"cardLimitDetails": [{"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "DOMESTIC", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "ATM"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "INTERNATIONAL", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "ATM"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "DOMESTIC", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "NFC"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "INTERNATIONAL", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "NFC"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "DOMESTIC", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "ECOMMERCE"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "INTERNATIONAL", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "ECOMMERCE"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "DOMESTIC", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "POS"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "locType": "INTERNATIONAL", "maxAllowedAmount": {"currencyCode": "INR", "units": "1000"}, "txnType": "POS"}]}', 'UPDATED', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554', NULL);

-- card_delivery_trackings
UPSERT
INTO card_delivery_trackings
(card_id, state, card_qr_data, created_at, updated_at, deleted_at)
VALUES('a5004f18-5d52-4991-82a9-2a1e3010e990', 'DELIVERED_BY_PARTNER', '{"masked_card_number" : "1234********5678", "phone_number": {"country_code": 91, "national_number": 9967867891}, "postal_code" : "123456", "phone_number_updated" : true }','2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554', NULL);

UPSERT
INTO card_auth_attempts
(attempt_id, card_id, card_action, state, auth_type, created_at, updated_at, deleted_at)
VALUES('4f4071df-b063-43ae-b33a-0131030666cd', 'a5004f18-5d52-4991-82a9-2a1e3010e990', 'RESET_PIN', 'SUCCESS', 'LIVENESS_PLUS_FACEMATCH', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554', NULL);

-- card_block_details
UPSERT
INTO card_block_details
(card_id, reason, provenance, state, created_at, updated_at, deleted_at)
VALUES('a5004f18-5d52-4991-82a9-2a1e3010e990', 'Card stolen', 'USER_APP', 'BLOCK_CARD_QUEUED','2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554', NULL);

UPSERT
INTO onboarding_details (id, actor_id, stage_details, vendor, user_id, completed_at)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', 'actor-user-1', '{"stage_mapping": {"ONBOARDING_COMPLETE": {"last_updated_at": {"nanos": 349113491, "seconds": 1621599197}, "state": "SUCCESS"}}}', 'FEDERAL', 'user-6','2020-10-31 23:49:39.713'),
('a5004f18-5d52-4991-82a9-2a1e3010e991', 'actor-user-1', '{"ONBOARDING_COMPLETE": {"last_updated_at": {"nanos": 349113491, "seconds": 1621599197}, "state": "SUCCESS"}}', 'FEDERAL','user-7',NULL),
('a5004f18-5d52-4991-82a9-2a1e3010e992', 'actor-user-2', '{"ONBOARDING_COMPLETE": {"last_updated_at": {"nanos": 349113491, "seconds": 1621599197}, "state": "SUCCESS"}}', 'FEDERAL', 'user-8','2020-11-01 23:49:39.713');


UPSERT
INTO contacts (id, actor_id, phone_number_hash, created_at)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', 'actor-user-1', '123456', '2020-11-01 00:08:46.554'),
('a5004f18-5d52-4991-82a9-2a1e3010e991', 'fraud-actor-1', '********', '2020-11-02 00:08:46.554'),
('a5004f18-5d52-4991-82a9-2a1e3010e992', 'fraud-actor-1', '*********', '2020-12-02 00:08:46.554'),
('a5004f18-5d52-4991-82a9-2a1e3010e993', 'fraud-actor-2', '*********', '2021-12-02 00:08:46.554');

UPSERT
INTO savings_ledger_recons (id, savings_account_id, status, partner_bank, last_transaction_timestamp)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', 'Test-Savings-1', 'DISPUTED', 'FEDERAL_BANK', '2020-11-01 00:08:46.554');

UPSERT
INTO transaction_notification_map (transaction_id)
VALUES ('transaction-1');

UPSERT
INTO disputed_transactions (transaction_id, actor_id, dispute_state, disputed_at, last_dispute_event_published_at)
VALUES ('transaction-3', 'actor-user-1', 'CREATED', '2020-11-01 00:08:46.554', '2020-11-01 00:09:46.554');

UPSERT
INTO vkyc_call_schedules (id, ref_id, start_time, end_time, agent_id, vendor, status, sub_status)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', '238d8ca5-da6f-44dd-848a-fd7430c4a5d6', '2021-01-01 00:00:00.000', '2021-01-01 00:15:00.000', 'agent-id-1', 'KARZA', 'VKYC_CALL_SCHEDULE_STATUS_UNSPECIFIED', 'VKYC_CALL_SCHEDULE_SUB_STATUS_UNSPECIFIED');

UPSERT
INTO vkyc_summaries(id, actor_id, vendor, status, sub_status)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e910', 'test-actor-id-1', 'KARZA', 'VKYC_SUMMARY_STATUS_UNSPECIFIED', 'VKYC_SUMMARY_SUB_STATUS_UNSPECIFIED');

UPSERT
INTO vkyc_karza_customer_infos(id, customer_id, vkyc_summary_id, transaction_id)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', 'customer-1', 'a5004f18-5d52-4991-82a9-2a1e3010e910', 'a5004f18-5d52-4991-82a9-2a1e3010e910');


UPSERT
INTO vkyc_summaries (id, actor_id, vendor, status, sub_status, created_at, updated_at)
VALUES ('dbf0eafe-f5b8-439d-adf4-2d220cd66043', 'actor-vkyc-1', 'KARZA', 'VKYC_SUMMARY_STATUS_UNSPECIFIED', 'VKYC_SUMMARY_SUB_STATUS_UNSPECIFIED', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554');

UPSERT
INTO vkyc_karza_customer_infos (id, customer_id,  vkyc_summary_id, transaction_id, created_at, updated_at)
VALUES ('a297661a-a569-44e1-a23d-acf38103dad9', 'customer-2', 'dbf0eafe-f5b8-439d-adf4-2d220cd66043', '58f1c3f2-fd5b-402b-a400-7477fbe917a2', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554');

UPSERT
INTO vkyc_attempts (id, vkyc_summary_id, vendor, customer_info_id, attempt_source, status, sub_status, created_at, updated_at, deleted_at)
VALUES ('5657a1df-9367-4f76-bae6-db50a5ddb786', 'dbf0eafe-f5b8-439d-adf4-2d220cd66043', 'KARZA', 'a297661a-a569-44e1-a23d-acf38103dad9', 'APP', 'VKYC_ATTEMPT_STATUS_UNSPECIFIED', 'VKYC_ATTEMPT_SUB_STATUS_UNSPECIFIED', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554', NULL);

UPSERT
INTO vkyc_attempts (id, vkyc_summary_id, vendor, customer_info_id, attempt_source, status, sub_status, created_at, updated_at, deleted_at)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591bd', 'dbf0eafe-f5b8-439d-adf4-2d220cd66043', 'KARZA', 'a297661a-a569-44e1-a23d-acf38103dad9', 'APP', 'VKYC_ATTEMPT_STATUS_UNSPECIFIED', 'VKYC_ATTEMPT_SUB_STATUS_UNSPECIFIED', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554', NULL);

UPSERT
INTO vkyc_attempts (id, vkyc_summary_id, vendor, customer_info_id, attempt_source, status, sub_status, created_at, updated_at, deleted_at)
VALUES ('0f5472d6-5800-4182-abf0-4a74c05219ed', 'dbf0eafe-f5b8-439d-adf4-2d220cd66043', 'KARZA', 'a297661a-a569-44e1-a23d-acf38103dad9', 'APP', 'VKYC_ATTEMPT_STATUS_UNSPECIFIED', 'VKYC_ATTEMPT_SUB_STATUS_UNSPECIFIED', '2020-11-01 00:08:46.555', '2020-11-01 00:08:46.555', NULL);

UPSERT
INTO vkyc_call_schedules (id, ref_id, start_time, end_time, agent_id, vendor, status, sub_status, updated_at, created_at)
VALUES ('f0973e81-e7d7-4bef-9a8f-0a18926cd9ff', 'afabe43b-df01-4025-88eb-81edb921e8e3', '2020-11-01 00:08:46.554', '2020-11-01 00:23:46.554', 'agent-id-1', 'KARZA', 'st', 'st', '2020-11-01 00:08:46.554', '2020-11-01 00:08:46.554');

UPSERT
INTO vkyc_karza_call_infos (id, vkyc_attempt_id, call_type, call_metadata, status, sub_status, created_at, updated_at)
VALUES ('afabe43b-df01-4025-88eb-81edb921e8e3', '5657a1df-9367-4f76-bae6-db50a5ddb786', 'VKYC_KARZA_CALL_INFO_CALL_TYPE_SCHEDULED', '{}', 'VKYC_KARZA_CALL_INFO_STATUS_IN_PROGRESS', 'VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_INITIATED', '2020-11-02 00:08:46.554', '2020-11-02 00:08:46.554');

UPSERT
INTO vkyc_karza_call_infos (id, vkyc_attempt_id, call_type, call_metadata, status, sub_status, created_at, updated_at)
VALUES ('238d8ca5-da6f-44dd-848a-fd7430c4a5d6', '5657a1df-9367-4f76-bae6-db50a5ddb786', 'VKYC_KARZA_CALL_INFO_CALL_TYPE_SCHEDULED', '{}', 'VKYC_KARZA_CALL_INFO_STATUS_APPROVED', 'VKYC_KARZA_CALL_INFO_SUB_STATUS_AUDITOR_APPROVED', '2020-11-01 00:09:46.554', '2020-11-01 00:09:46.554');

UPSERT
INTO vkyc_karza_call_infos (id, vkyc_attempt_id, call_type, call_metadata, status, sub_status, created_at, updated_at)
VALUES ('bfc05f22-2722-4057-bd10-aa0351f591cd', 'bfc05f22-2722-4057-bd10-aa0351f591bd', 'VKYC_KARZA_CALL_INFO_CALL_TYPE_SCHEDULED', '{}', 'VKYC_KARZA_CALL_INFO_STATUS_APPROVED', 'VKYC_KARZA_CALL_INFO_SUB_STATUS_AUDITOR_APPROVED', '2020-11-01 00:09:46.554', '2020-11-01 00:09:46.554');

UPSERT
INTO vkyc_karza_call_histories (id, call_info_id, request_id, transaction_id, metadata, event_time, vkyc_karza_call_info_sub_status, created_at, updated_at)
VALUES ('694d8ca5-da6f-44dd-849a-fd7530c4b5d8', '238d8ca5-da6f-44dd-848a-fd7430c4a5d6', 'request-id-1', 'transaction-id-vendor', '{}', '2020-11-01 00:00:00.000', 'VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_ARRIVED', '2020-11-01 00:09:46.554', '2020-11-01 00:09:46.554');

UPSERT
INTO user_group_mappings (user_email, user_group,identifier_type,identifier_value)
VALUES ('<EMAIL>', 'INTERNAL','IDENTIFIER_TYPE_EMAIL','<EMAIL>'),
('<EMAIL>', 'INTERNAL','IDENTIFIER_TYPE_EMAIL','<EMAIL>'),
('<EMAIL>', 'FNF','IDENTIFIER_TYPE_EMAIL','<EMAIL>'),
('<EMAIL>', 'CARD_QR_CODE','IDENTIFIER_TYPE_EMAIL','<EMAIL>'),
('<EMAIL>', 'REQUEST_NEW_CARD_FNF','IDENTIFIER_TYPE_EMAIL','<EMAIL>');

UPSERT
INTO shipping_preference_vendor_requests (id, vendor, preference_id, status, request_id, created_at, updated_at)
VALUES ('SPVR001', 'FEDERAL_BANK', 'SP001', 'QUEUED', 'NEOCAMDF001', '2021-01-01 01:01:01', '2021-01-01 01:01:01');

UPSERT
INTO merchants (id, logo_url, display_name, brand_name, legal_name, ds_merchant_id) VALUES ('694d8ca5-da6f-44dd-849a-fd7530c4b5d8', 'https://test-epifi.amazon.com', 'Amazon', 'AMAZON', 'AMAZON_RETAIL', 'ds1');
UPSERT
INTO merchant_pis(id, merchant_id, pi_id, old_merchant_id) VALUES ('238d8ca5-da6f-44dd-848a-fd7430c4a5d6', '694d8ca5-da6f-44dd-849a-fd7530c4b5d8', 'pi-3', '694d8ca5-da6f-44dd-849a-fd7530c4b543');
UPSERT
INTO vpa_merchant_infos(id, vpa) VALUES ('694d8ca5-da6f-44dd-849a-fd7530c4b5d8', '<EMAIL>');
UPSERT
INTO card_merchant_infos (id, pi_id, mcc, mid,created_at,updated_at,address,tid,acquirer) VALUES ('0fd3583f-018b-439a-8fa4-6586e538b55e', 'pi-partial-1', 'mcc-card-1', 'mid-card-1','2021-03-21 08:23:55.140556+00:00', '2021-03-21 08:23:55.140556+00:00', '{}','tid-card-1', 'acquirer-card-1');

--merchant data for acceptance tests
UPSERT
INTO public.actors (id, type, entity_id, name, created_at, updated_at, deleted_at) VALUES
	('AC2103210e4ZU15RTb65qM6cEC3Tiw==', 'EXTERNAL_MERCHANT', '6507c8cd-30ee-464c-b279-af1322a0d46b', NULL, '2021-03-21 08:23:55.140556+00:00', '2021-03-21 08:23:55.140556+00:00', NULL),
	('AC210321HFgdiuCaSmCUisO6dTcUKw==', 'EXTERNAL_MERCHANT', '9fec2cc3-b2d0-4cd2-b874-c03bee442367', NULL, '2021-03-21 08:23:55.318655+00:00', '2021-03-21 08:23:55.318655+00:00', NULL);

UPSERT
INTO public.merchants (id, logo_url, display_name, verified_name, brand_name, legal_name, mcc, mid, merchant_size, merchant_genre, onboarding_source, ownership_type, extra_info, created_at, updated_at, deleted_at, capabilities, ds_merchant_id) VALUES
	('6507c8cd-30ee-464c-b279-af1322a0d46b', '', 'Fi Merchant 2', NULL, 'Fi', 'Fi Private', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2021-03-21 08:23:55.09062+00:00', '2021-03-21 08:23:55.09062+00:00', NULL, '{}', '694d8ca5-da6f-44dd-849a-fd7530c4b5d5'),
	('9fec2cc3-b2d0-4cd2-b874-c03bee442367', '', 'Fi Merchant 1', NULL, 'Fi', 'Fi Private', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2021-03-21 08:23:55.248941+00:00', '2021-03-21 08:23:55.248941+00:00', NULL, '{"TIMELINE_COLLECT": false, "TIMELINE_PAY": true}', NULL);

UPSERT
INTO public.payment_instruments (id, type, verified_name, identifier, state, created_at, updated_at, deleted_at, capabilities, spam_count, issuer_classification) VALUES
	('PI210321Tm+54F5kThSQbwegdTpyAg==', 'UPI', 'Fi', '{"upi": {"merchant_details": {"brand_name": "Fi", "franchise_name": "Fi", "legal_name": "Fi Private", "mcc": "5412", "merchant_genre": "ONLINE", "merchant_store_id": "mid-1", "merchant_terminal_id": "tid-1", "merchant_type": "SMALL", "onboarding_type": "MERCHANT_ONBOARDING_TYPE_UNSPECIFIED", "ownership_type": "PRIVATE", "sub_code": "12345"}, "vpa": "merchant@okaxis"}}', 'CREATED', '2021-03-21 13:53:55.238019', '2021-03-21 13:53:55.238019', NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL'),
	('PI210321b6qxY6HZR9CH1DM3cRpJzg==', 'UPI', 'Fi', '{"upi": {"merchant_details": {"brand_name": "Fi", "franchise_name": "Fi", "legal_name": "Fi Private", "mcc": "5411", "merchant_genre": "ONLINE", "merchant_store_id": "mid-2", "merchant_terminal_id": "tid-2", "merchant_type": "SMALL", "onboarding_type": "MERCHANT_ONBOARDING_TYPE_UNSPECIFIED", "ownership_type": "PRIVATE", "sub_code": "12346"}, "vpa": "merchant1@oksbi"}}', 'CREATED', '2021-03-21 13:53:55.082309', '2021-03-21 13:53:55.082309', NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL'),
	('PI210321x2USfwTvRhy87pzxYcpL0g==', 'UPI', 'Fi', '{"upi": {"merchant_details": {"brand_name": "Fi", "franchise_name": "Fi", "legal_name": "Fi Private", "mcc": "4899", "merchant_genre": "ONLINE", "merchant_store_id": "mid-2", "merchant_terminal_id": "tid-2", "merchant_type": "SMALL", "onboarding_type": "AGGREGATOR", "ownership_type": "PRIVATE", "sub_code": "12346"}, "vpa": "merchant@oksbi"}}', 'CREATED', '2021-03-21 13:53:55.072785', '2021-03-21 13:53:55.072785', NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');

UPSERT
INTO public.merchant_pis (id, merchant_id, pi_id, created_at, updated_at, deleted_at, old_merchant_id) VALUES
	('0bf2ef89-9264-4ad9-af83-78af5d2119d0', '6507c8cd-30ee-464c-b279-af1322a0d46b', 'PI210321x2USfwTvRhy87pzxYcpL0g==', '2021-03-21 08:23:55.09453+00:00', '2021-03-21 08:23:55.09453+00:00', NULL, NULL),
	('52a9aaf1-8a5b-4226-8588-f3a0e751c3c8', '6507c8cd-30ee-464c-b279-af1322a0d46b', 'PI210321b6qxY6HZR9CH1DM3cRpJzg==', '2021-03-21 08:23:55.120501+00:00', '2021-03-21 08:23:55.120501+00:00', NULL, NULL),
	('b26ce173-4137-4a59-a9bb-6a86e2207aa1', '9fec2cc3-b2d0-4cd2-b874-c03bee442367', 'PI210321Tm+54F5kThSQbwegdTpyAg==', '2021-03-21 08:23:55.252813+00:00', '2021-03-21 08:23:55.252813+00:00', NULL, '9fec2cc3-b2d0-4cd2-b874-c03bee455367');

UPSERT
INTO public.vpa_merchant_infos (id, merchant_store_id, merchant_terminal_id, merchant_genre, brand_name, legal_name, franchise_name, ownership_type, onboarding_type, merchant_type, sub_code, created_at, updated_at, deleted_at, vpa, mcc) VALUES
	('013f765e-1de8-4754-aa01-9ef9a26c99e2', NULL, NULL, 'ONLINE', 'Fi', 'Fi Private', 'Fi', 'PRIVATE', 'MERCHANT_ONBOARDING_TYPE_UNSPECIFIED', 'MERCHANT_TYPE_UNSPECIFIED', '12346', '2021-03-21 08:23:55.08811+00:00', '2021-03-21 08:23:55.08811+00:00', NULL, 'merchant1@oksbi', '5411'),
	('0fd3583f-018b-439a-8fa4-6586e538b55e', NULL, NULL, 'ONLINE', 'Fi', 'Fi Private', 'Fi', 'PRIVATE', 'AGGREGATOR', 'MERCHANT_TYPE_UNSPECIFIED', '12346', '2021-03-21 08:23:55.080047+00:00', '2021-03-21 08:23:55.080047+00:00', NULL, 'merchant@oksbi', '4899'),
	('75553dc7-8b01-47b3-89ad-cfec2603ca85', NULL, NULL, 'ONLINE', 'Fi', 'Fi Private', 'Fi', 'PRIVATE', 'MERCHANT_ONBOARDING_TYPE_UNSPECIFIED', 'MERCHANT_TYPE_UNSPECIFIED', '12345', '2021-03-21 08:23:55.246175+00:00', '2021-03-21 08:23:55.246175+00:00', NULL, 'merchant@okaxis', '5412');

UPSERT
INTO public.vpa_merchant_infos (id, merchant_store_id, merchant_terminal_id, merchant_genre, brand_name, legal_name, franchise_name, ownership_type, onboarding_type, merchant_type, sub_code, created_at, updated_at, deleted_at, vpa, mcc, restricted_account_type_details) VALUES
	('ce95e565-0d88-450b-865b-aaeec8c72c38', NULL, NULL, 'ONLINE', 'Fi', 'Fi Private', 'Fi', 'PRIVATE', 'MERCHANT_ONBOARDING_TYPE_UNSPECIFIED', 'MERCHANT_TYPE_UNSPECIFIED', '12346', '2025-02-05 08:23:55.08811+00:00', '2025-02-05 08:23:55.08811+00:00', NULL, 'ashumerchant1@oksbi', '5411', NULL),
	('148e6276-8d0e-4bed-9324-318b8a61e2d5', NULL, NULL, 'ONLINE', 'Fi', 'Fi Private', 'Fi', 'PRIVATE', 'MERCHANT_ONBOARDING_TYPE_UNSPECIFIED', 'MERCHANT_TYPE_UNSPECIFIED', '12346', '2025-02-05 08:23:55.08811+00:00', '2025-02-05 08:23:55.08811+00:00', NULL, 'ashumerchant2@oksbi', '5411', '{"AllowAllAccountTypes": true}'),
    ('cbdd34e8-dfe3-4cdd-9eb0-a836d46c58d3', NULL, NULL, 'ONLINE', 'Fi', 'Fi Private', 'Fi', 'PRIVATE', 'MERCHANT_ONBOARDING_TYPE_UNSPECIFIED', 'MERCHANT_TYPE_UNSPECIFIED', '12346', '2025-02-05 08:23:55.08811+00:00', '2025-02-05 08:23:55.08811+00:00', NULL, 'ashumerchant3@oksbi', '5411', '{"FeatureSupportedValues": "05|06", "RestrictedAccountTypes": ["CREDIT", "PPI_WALLET"], "AllowAllAccountTypes": false}');

UPSERT
INTO vkyc_notifications(id, actor_id, comms_message_id)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', 'actor-1', '013f765e-1de8-4754-aa01-9ef9a26c99e1');

UPSERT
INTO vkyc_holidays(id, start_time, end_time, created_at, updated_at)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', '2021-04-20 01:46:21.975838+00:00', '2021-04-20 10:50:21.975838+00:00', '2021-03-21 08:23:55.140556+00:00', '2021-03-21 08:23:55.140556+00:00');

UPSERT
INTO vkyc_holidays(id, start_time, end_time, created_at, updated_at)
VALUES ('013f765e-1de8-4754-aa01-9ef9a26c99e2', '2021-04-20 22:46:21.975838+00:00', '2021-04-20 22:50:21.975838+00:00', '2021-04-19 08:23:55.140556+00:00', '2021-04-19 08:23:55.140556+00:00');

UPSERT
INTO device_registrations(actor_id,device_id,device,device_token,user_profile_id,status,created_at,updated_at,deleted_at,deleted_at_unix)
VALUES ('actor-user-1','9294EE94-94C0-478E-92BD-C936B1F3E3AA','{"device_id": "9294EE94-94C0-478E-92BD-C936B1F3E3AD", "hw_version": "unavailable", "manufacturer": "Apple", "model": "iPhone 7", "os_api_version": "13.7", "sw_version": "unavailable"}','device_token1','H','REGISTERED','2021-02-25 18:41:22','2021-02-25 18:41:22',NULL,1);

UPSERT
INTO device_registrations(actor_id,device_id,device,device_token,user_profile_id,status,created_at,updated_at,deleted_at,deleted_at_unix)
VALUES ('actor-user-1','9294EE94-94C0-478E-92BD-C936B1F3E3AB','{"device_id": "9294EE94-94C0-478E-92BD-C936B1F3E3AD", "hw_version": "unavailable", "manufacturer": "Apple", "model": "iPhone 7", "os_api_version": "13.7", "sw_version": "unavailable"}','device_token2','H','REGISTERED','2021-02-26 18:41:22','2021-02-25 18:41:22',NULL,0);

UPSERT
INTO vkyc_holidays(id, start_time, end_time, created_at, updated_at)
VALUES ('a5004f18-5d52-4991-82a9-2a1e3010e990', '2021-04-22 07:25:05+00:00', '2021-04-22 07:32:05+00:00', '2021-03-21 08:23:55.140556+00:00', '2021-03-21 08:23:55.140556+00:00');

UPSERT
INTO vkyc_holidays(id, start_time, end_time, created_at, updated_at)
VALUES ('013f765e-1de8-4754-aa01-9ef9a26c99e2', '2021-04-22 07:25:05+00:00', '2021-04-22 07:40:05+00:00', '2021-03-21 08:24:55.140556+00:00', '2021-04-19 08:23:55.140556+00:00');

UPSERT
INTO card_skus(card_sku_type, free_card_replacements, vendor_card_sku)
VALUES ('CLASSIC', 2, '{"federal":{"free_replacement_card_sub_type" : "epifi_free", "chargeable_card_sub_type" : "epifi_charge"}}');

UPSERT
INTO card_sku_overrides(actor_id, card_sku_type, free_card_replacements)
VALUES ('actor-1', 'CLASSIC', 3);

-- otp fixture
UPSERT
INTO otps(id, phone_number, otp, token, device, active, created_at, updated_at) VALUES
(1, '{"country_code": 91, "national_number": *********0}', '000001', 'token1', '{"device_id": "d1"}', true, '2021-06-11 12:00:01.000+00:00', '2021-06-11 12:00:01.000+00:00'),
(2, '{"country_code": 91, "national_number": *********0}', '000002', 'token2', '{"device_id": "d1"}', true, '2021-06-11 12:01:01.000+00:00', '2021-06-11 12:01:01.000+00:00'),
(3, '{"country_code": 91, "national_number": *********0}', '000003', 'token3', '{"device_id": "d1"}', true, '2021-06-11 12:02:01.000+00:00', '2021-06-11 12:02:01.000+00:00'),
(5, '{"country_code": 91, "national_number": *********0}', '000005', 'token5', '{"device_id": "d1"}', true, '2021-06-11 12:04:01.000+00:00', '2021-06-11 12:04:01.000+00:00'),
(6, '{"country_code": 91, "national_number": *********0}', '000006', 'token6', '{"device_id": "d1"}', true, '2021-06-11 12:05:01.000+00:00', '2021-06-11 12:05:01.000+00:00'),
(7, '{"country_code": 91, "national_number": *********1}', '000007', 'token7', '{"device_id": "d2"}', true, '2021-06-11 12:02:01.000+00:00', '2021-06-11 12:02:01.000+00:00'),
(8, '{"country_code": 91, "national_number": *********1}', '000008', 'token8', '{"device_id": "d2"}', true, '2021-06-11 12:04:01.000+00:00', '2021-06-11 12:06:01.000+00:00');


-- employment_data fixtures
UPSERT
INTO employment_data(id, actor_id, employment_type, employment_info, verification_process_status, verification_result, hold_reason, rejection_reason, created_at, updated_at) VALUES
('014f765e-1de8-4754-aa01-9ef9a26c99a1', 'actorId2', 'SELF_EMPLOYED', '{"personal_profile_info": "abcd"}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a2', 'actorId3', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_COMPLETED', 'EMPLOYMENT_VERIFICATION_RESULT_ACCEPTED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a3', 'actorId4', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": false}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a4', 'actorId5', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a5', 'actorId6', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a6', 'actorId7', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a7', 'actorId8', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a8', 'actorId9', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99a9', 'actorId10', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99aa', 'actorId11', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99bb', 'actorId20', 'SALARIED', '{"annual_salary": {"absolute":100000}}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00');


UPSERT
INTO employment_data(id, actor_id, employment_type, employment_info, verification_process_status, verification_result, hold_reason, rejection_reason, created_at, updated_at) VALUES
('014f765e-1de8-4754-aa01-9ef9a26c99b1', 'actorId12', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99b2', 'actorId13', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99ab', 'actorId14', 'SALARIED', '{"company_name": "companyName1", "vendor_id": "vendorId1", "is_epf_registered": true}', 'EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00');

-- employment verification process fixtures
UPSERT
INTO employment_verification_processes(id, actor_id, employment_data_id, process_name, verification_process_status, verification_result, hold_reason, rejection_reason, created_at, updated_at) VALUES
('014f765e-1de8-4754-aa01-9ef9a26c99d1', 'actorId12', '014f765e-1de8-4754-aa01-9ef9a26c99b1', 'PROCESS_NAME_EPFO_VERIFICATION', '{"epfoVerificationProcessStatus": "EPFO_VERIFICATION_PROCESS_STATUS_INITIATED"}', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00'),
('014f765e-1de8-4754-aa01-9ef9a26c99d2', 'actorId14', '014f765e-1de8-4754-aa01-9ef9a26c99ab', 'PROCESS_NAME_EPFO_VERIFICATION', '{"epfoVerificationProcessStatus": "EPFO_VERIFICATION_PROCESS_STATUS_INITIATED"}', 'EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED', 'HOLD_REASON_UNSPECIFIED', 'REJECTION_REASON_UNSPECIFIED', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00');

UPSERT
INTO employment_verification_checks(id, actor_id, verification_process_id, name, result, reason, created_at, updated_at) VALUES
('014f765e-1de8-4754-aa01-9ef9a26c99d1', 'actorId14', '014f765e-1de8-4754-aa01-9ef9a26c99d2', 'CHECK_NAME_EPFO_ENTERED_COMPANY_NAME_MATCH', 'CHECK_RESULT_REJECTED', 'CHECK_RESULT_REASON_EPF_DATA_NAME_MATCH_NOT_FOUND', '2024-04-20 08:23:55.09453+00:00', '2024-04-20 08:23:55.09453+00:00');

-- finite codes
UPSERT
INTO finite_codes
(id, code, actor_id, channel, type, claim_limit, claimed_count, is_active, skip_employment_check_privilege, created_at, updated_at, phone_number_referral_code)
VALUES ('finite-code-id-1', 'ABCDE12345', 'actor-user-1', 'IN_APP_REFERRAL', 'REGULAR', 10000, 0, true, false,
        '2021-07-06 06:18:55.458816', '2021-07-06 06:18:55.458816','91**********'),
       ('finite-code-exhausted-id-1', 'ABCDE12346', 'actor-user-1', 'IN_APP_REFERRAL', 'REGULAR', 10000, 10000, null,
        false, '2021-07-06 06:18:55.458816', '2021-07-06 06:18:55.458816',null),
       ('influencer-finite-code-id-1', 'ABCDE12347', null, 'INFLUENCER_REFERRAL', 'REGULAR', 5, 0, true, false,
        '2021-07-06 06:18:55.458816', '2021-07-06 06:18:55.458816',null),
       ('finite-code-id-1-referral-link', 'RBCDE12345', 'actor-5', 'IN_APP_REFERRAL', 'REGULAR', 10000, 0, true, false,
        '2021-07-06 06:18:55.458816', '2021-07-06 06:18:55.458816',null);

-- finite code claims
UPSERT
INTO finite_code_claims
(id, referrer_actor_id, referee_actor_id, finite_code_id, referee_onboarding_status, created_at, updated_at)
VALUES ('finite-code-claim-id-1', 'actor-user-1', 'actor-1', 'finite-code-id-1', 'INSTALLED', '2021-07-08 08:23:59.242619', '2021-07-08 08:23:59.232619'),
       ('finite-code-claim-id-2', 'actor-user-1', 'actor-2', 'finite-code-id-1', 'REWARD_GRANTED', '2021-07-08 08:23:59.232619', '2021-07-08 08:23:59.232619');

-- inAppReferrral unlocks
UPSERT
INTO public.inappreferral_unlocks
(id, actor_id, finite_code_type, is_unlocked, average_eod_balance, unlocked_at, created_at, updated_at)
VALUES ('inappreferral-unlock-id-1', 'actor-3', 'REGULAR', false, '{"currency_code": "INR"}', null, '2021-08-26 05:53:26.377639', '2021-08-26 05:53:26.377639'),
	   ('inappreferral-unlock-id-2', 'actor-2', 'REGULAR', true, '{"currency_code": "INR", "units": 7000}', '2021-09-26 05:53:26.377639' , '2021-08-26 05:53:26.377639', '2021-08-26 05:53:26.377639'),
	   ('inappreferral-unlock-id-4', 'actor-1', 'REGULAR', false, '{"currency_code": "INR", "units": 1000}', null , '2021-08-26 05:53:26.377639', '2021-08-26 05:53:26.377639'),
	   ('inappreferral-unlock-id-5', 'actor-user-1', 'REGULAR', false, '{"currency_code": "INR", "units": 5000}', null , '2021-08-26 05:53:26.377639', '2021-08-26 05:53:26.377639');

UPSERT
INTO actor_add_fund_options_map(actor_id, add_fund_option, created_at, updated_at) VALUES
('actor-1', '{"default_amount":{"currency_code": "INR", "units": 5000}, "min_amount":{"currency_code": "INR", "units": 0}, "max_amount":{"currency_code": "INR", "units": 2000}}', '2021-06-11 12:00:01.000+00:00', '2021-06-11 12:00:01.000+00:00'),
('actor-1', '{"default_amount":{"currency_code": "INR", "units": 10000}, "min_amount":{"currency_code": "INR", "units": 0}, "max_amount":{"currency_code": "INR", "units": 2000}}', '2021-06-13 12:00:01.000+00:00', '2021-06-13 12:00:01.000+00:00'),
('actor-1', '{"default_amount":{"currency_code": "INR", "units": 20000}, "min_amount":{"currency_code": "INR", "units": 0}, "max_amount":{"currency_code": "INR", "units": 2000}}', '2021-06-12 12:00:01.000+00:00', '2021-06-12 12:00:01.000+00:00');

UPSERT
INTO account_statement_requests(id, account_id, account_type, stage, req_type, to_date, from_date, client_req_id) VALUES
('statement-1','account-1','SAVINGS', 'INITIATED', 'USER_REQUESTED', '2021-07-06 06:18:55.458816','2021-07-05 06:18:55.458816', null),
('statement-2','account-1','SAVINGS', 'INITIATED', 'USER_REQUESTED', '2021-07-06 06:18:55.458816','2021-07-05 06:18:55.458816', 'client-req-id-1');

UPSERT
INTO oauth_signup_data(id, user_id, profile, oauth_provider, refresh_token_last_validated_at) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e998', 'user-id-2', '{"first_name" : "a", "family_name": "b", "email" : "<EMAIL>", "refresh_token":"abc"}', 'GOOGLE', '2021-06-3 12:00:00.000+00:00');
-- ('a5004f18-5d52-4991-82a9-2a1e3010e988', 'user-id-3', '{"first_name" : "a", "family_name": "b", "email" : "<EMAIL>"}', 'APPLE',),
-- ('a5004f18-5d52-4991-82a9-2a1e3010e989', 'user-id-3', '{"first_name" : "a", "family_name": "b", "email" : "<EMAIL>"}', 'GOOGLE');

UPSERT
INTO upi_request_info(id, request_id, response_code, total_attempts, updated_at) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e998', 'req-1', 'U49', 4, now() + INTERVAL '1 MINUTE'),
('a5004f18-5d52-4991-82a9-2a1e3010e998', 'req-1', 'U48', 4, now() + INTERVAL '5 MINUTE');

UPSERT
INTO pay_campaigns(id, actor_id, campaign_type, total_notifications, last_notification_sent_at) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e998', 'actor-1', 'ADD_FUNDS', 1, '2021-07-05 06:18:55.458816'),
('a5004f18-5d52-4991-82a9-2a1e3010e998', 'actor-3', 'ADD_FUNDS', 5, '2021-07-05 06:18:55.458816');

-- paused events
UPSERT
INTO paused_events VALUES
('PE001', 'ONBOARDING', '2021-01-01 01:01:01', '2100-01-01 02:02:02', '{"subTitle": "please try after few hours", "title": "Our partner bank is under maintenance"}', '2021-01-01 01:01:01', '2021-01-01 01:01:01');

UPSERT
INTO recurring_payments(id, from_actor_id, to_actor_id, type, pi_from, pi_to, amount, amount_type, start_date, end_date, recurrence_rule, maximum_allowed_txns, partner_bank, preferred_payment_protocol, state, ownership, provenance, ui_entry_point, initiated_by, created_at, updated_at, external_id, share_to_payee) VALUES
('RP-1', 'actor-3', 'actor-2', 'STANDING_INSTRUCTION', 'pi-1', 'pi-2', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'EXACT', '2021-07-06 06:18:55.458816','2021-07-05 06:18:55.458816', '{"allowed_frequency": "DAILY", "day": 10}', 10, 'FEDERAL', 'IMPS', 'CREATION_INITIATED', 'EPIFI_TECH', 'USER_APP', 'FIT', 'PAYER', now() +  INTERVAL '20 MINUTE', now(), 'external-id-1',True);

UPSERT
INTO recurring_payments_actions(id, recurring_payment_id, client_request_id, action, created_at, updated_at, state, vendor_request_id,initiated_by) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e996', 'RP-2', 'Client-req-id-1', 'EXECUTE', now() - INTERVAL '5 MINUTE', now() - INTERVAL '5 MINUTE', 'ACTION_SUCCESS', null,'PAYEE'),
('a5004f18-5d52-4991-82a9-2a1e3010e997', 'RP-2', 'Client-req-id-2', 'EXECUTE', now() - INTERVAL '3 MINUTE', now() - INTERVAL '3 MINUTE', 'ACTION_SUCCESS', null,'PAYEE'),
('a5004f18-5d52-4991-82a9-2a1e3010e998', 'RP-2', 'Client-req-id-3', 'EXECUTE', now() + INTERVAL '3 MINUTE', now() + INTERVAL '3 MINUTE', 'ACTION_SUCCESS', null,'PAYEE'),
('a5004f18-5d52-4991-82a9-2a1e3010e999', 'RP-2', 'Client-req-id-4', 'EXECUTE', now() + INTERVAL '5 MINUTE', now() + INTERVAL '5 MINUTE', 'ACTION_SUCCESS', null,'PAYEE');

UPSERT
INTO upi_mandates(id, recurring_payment_id, req_id, umn, revokeable, share_to_payee, block_fund, initiated_by, created_at, updated_at) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e998','recurring-payment-id-1', 'req-id-1', 'umn-1', True, True, True, 'PAYEE', now(), now()),
('a5004f18-5d52-4991-82a9-2a1e3010e991','recurring-payment-id-2', 'req-id-2', 'umn-2', True, True, True, 'PAYEE', now(), now());

UPSERT
INTO upi_mandate_requests(id, mandate_id, req_id, action, req_info, status, stage, expire_at, created_at, updated_at) VALUES
('a5004f18-5d52-4991-82a9-2a1e3010e998','mandate-1', 'req-id-1', 'CREATE', '{"purpose":"0"}', 'INITIATED', 'REQ_AUTH_MANDATE_RECEIVED', now() + INTERVAL '30 MINUTE', now(), now()),
('a5004f18-5d52-4991-82a9-2a1e3010e991','mandate-2', 'req-id-2', 'CREATE', '{"purpose":"0"}', 'INITIATED', 'REQ_AUTH_MANDATE_RECEIVED', now() + INTERVAL '30 MINUTE', now(), now());

-- savings account aggregations
UPSERT
INTO savings_account_aggregations (id, savings_account_id, total_credited_amount, total_debited_amount) VALUES
('savings-account-aggregation-1', 'Test-Savings-1', 999900, 99900);

-- app_instance_identifiers
UPSERT
INTO app_instance_identifiers
VALUES ('AII001', 'AC001', 'PI001', 'FB001', '2021-01-01 01:01:01', '2021-01-01 01:01:01');

-- aa data purge requests
UPSERT
INTO aa_data_purge_requests (id, account_id, txn_execution_from, txn_execution_to, data_identifiers, status)
VALUES ('df85db92-6892-4414-9969-c87d57748675', 'connected-account-1', '2022-01-28 10:00:00', '2022-01-28 11:00:00', '{"actorIds": ["actor-1"]}', 'REQUEST_CREATED'),
	   ('2328f568-46a1-4ace-862e-2464a9716da7', 'connected-account-1', '2022-01-28 11:00:00', '2022-01-28 12:00:00', '{"actorIds": ["actor-1"]}', 'ACTOR_PURGED');

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-icici-amc-business-account', 'EXTERNAL_USER', null, 'ICICI Prudential AMC Ltd', 'EPIFI_TECH');

UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-icici-amc-business-account', 'BANK_ACCOUNT', 'ICICI Prudential AMC Ltd', '{"account": {"account_type": "CURRENT", "actual_account_number": "IPRUMFEPIFI", "ifsc_code": "HDFC0000060", "name": "ICICI PRUDENTIAL MF E CMS COLLECTION AC", "secure_account_number": "xxxxxxxPIFI"}}', 'CREATED', '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');

UPSERT INTO payment_instrument_purge_audits (pi_id, payload)
VALUES ('pi-1', '{"account": {"accountType": "SAVINGS", "actualAccountNumber": "***************", "ifscCode": "FDRL0001001", "secureAccountNumber": "xxxxxxxxxxx2939"}, "capabilities": {"INBOUND_TXN": true, "OUTBOUND_TXN": true}, "id": "pi-1", "issuerClassification": "INTERNAL", "state": "CREATED", "type": "BANK_ACCOUNT", "verifiedName": "Kunal"}');

UPSERT
INTO deposit_transactions (id, deposit_account_id, txn_id, txn_type, txn_amount, txn_timestamp)
VALUES ('deposit-txn-id-1', 'deposit-account-sd-1', 'transaction-8', 'CREATION', '{"units": 1000}', '2022-03-16 00:00:00');

UPSERT INTO loan_requests (id,actor_id,offer_id, orch_id, loan_account_id, vendor_request_id, vendor, type, status, sub_status,details,created_at,updated_at,deleted_at) VALUES
	('request-id-2','act-2','off-2','orch-2','*********','req-2','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL),
	('request-id-6','act-6','off-6','orch-6','acc-6','req-6','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{"loanInfo": {"amount": {"currencyCode": "INR", "units": "100000"}, "deductions": {"advanceInterest": {"currencyCode": "INR", "units": "500"}, "gst": {"currencyCode": "INR", "units": "200"}, "processingFee": {"currencyCode": "INR", "units": "300"}, "totalDeductions": {"currencyCode": "INR", "units": "1000"}}, "disbursalAmount": {"currencyCode": "INR", "units": "99000"}, "emiAmount": {"currencyCode": "INR", "units": "8000"}, "interestRate": 14.5, "tenureInMonths": 12, "totalPayable": {"currencyCode": "INR", "units": "101000"}}, "otpInfo": {"attemptsCount": 0, "maxAttempts": 3, "otp": "123456"}, "phoneNumber": {"countryCode": 91, "nationalNumber": "**********"}}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO loan_accounts (id,actor_id,loan_account_id, loan_type, ifsc_code, loan_amount, disbursed_amount, outstanding_amount, total_payable_amount, loan_end_date, maturity_date, vendor, details, status, created_at,updated_at,deleted_at) VALUES
	('account-id-2','act-2','acc-2','LOAN_TYPE_PERSONAL','ifsc-2','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','FEDERAL','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO loan_installment_info (id, account_id, total_amount, start_date, end_date, total_installment_count, next_installment_date, details, status, deactivated_at, created_at, updated_at, deleted_at) VALUES
	('inst-info-id-2', 'acc-2', '{"currency_code": "INR", "units": 50000}', '2022-08-15', '2022-10-15', 10, '2022-09-15', '{}', 'LOAN_INSTALLMENT_INFO_STATUS_ACTIVE', NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO loan_step_executions (id, actor_id, ref_id, orch_id, flow, step_name, details, status, sub_status, staled_at, completed_at, created_at, updated_at, deleted_at) VALUES
	('step-exec-id-2', 'act-2', 'ref-2', 'orch-2', 'LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION', 'LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED', '{}', 'LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED', 'LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED',NULL, NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL),
	('step-exec-id-6', 'act-6', 'ref-6', 'orch-6', 'LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION', 'LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK', '{}', 'LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED', 'LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED',NULL, '2022-04-01 11:48:50.662941+05:30', '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO loan_activities (id, loan_account_id, details, type, created_at, updated_at, deleted_at) VALUES
	('act-id-2', 'acc-2', '{}', 'LOAN_ACTIVITY_TYPE_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO loan_payment_requests (id, actor_id, account_id, amount, type, orch_id, details, status, sub_status, created_at, updated_at, deleted_at) VALUES
	('id-2', 'act-2', 'acc-2', '{"currency_code": "INR", "units": 50000}', 'LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM', 'orch-2', '{}', 'LOAN_PAYMENT_REQUEST_STATUS_CREATED', 'LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO p2p_investors (id,actor_id,vendor_investor_id,total_investment_count,used_investment_count,vendor,status,sub_status,details,created_at,updated_at,deleted_at) VALUES
	('investor-id-2','act-2','vii-2',2,0,'VENDOR_LIQUILOANS','INVESTOR_STATUS_CREATION_PENDING','INVESTOR_SUB_STATUS_UNSPECIFIED','{"address": {"administrativeArea": "AdministrativeArea", "locality": "KA", "postalCode": "100100", "regionCode": "IN", "sublocality": "BANGALORE"}, "bankDetails": {"accountName": {"firstName": "fn", "honorific": "h", "lastName": "ln", "middleName": "mn"}, "accountNumber": "AccountNumber123123", "bankBranch": "BankBranch", "bankName": "BankName", "ifsc": "Ifsc"}, "dob": {"day": 1, "month": 1, "year": 1990}, "emailId": "<EMAIL>", "name": {"firstName": "fn", "honorific": "h", "lastName": "ln", "middleName": "mn"}, "phoneNo": {"countryCode": 91, "nationalNumber": "**********"}}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL),
	('investor-id-4','act-4','vii-4',2,0,'VENDOR_LIQUILOANS','INVESTOR_STATUS_CREATION_PENDING','INVESTOR_SUB_STATUS_UNSPECIFIED','{"address": {"administrativeArea": "AdministrativeArea", "locality": "KA", "postalCode": "100100", "regionCode": "IN", "sublocality": "BANGALORE"}, "bankDetails": {"accountName": {"firstName": "fn", "honorific": "h", "lastName": "ln", "middleName": "mn"}, "accountNumber": "AccountNumber123123", "bankBranch": "BankBranch", "bankName": "BankName", "ifsc": "Ifsc"}, "dob": {"day": 1, "month": 1, "year": 1990}, "emailId": "<EMAIL>", "name": {"firstName": "fn", "honorific": "h", "lastName": "ln", "middleName": "mn"}, "phoneNo": {"countryCode": 91, "nationalNumber": "**********"}}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT INTO p2p_investment_schemes (id,vendor_scheme_id, vendor,details,created_at,updated_at,deleted_at, name) VALUES
	('scheme-id-2','vs-id-2','VENDOR_LIQUILOANS','{"displayDetails": {"description": "desc1", "title": "title1"}, "investmentRoi": 9, "lockInTenureInDays": 90, "maxInvestableAmount": {"currencyCode": "INR", "units": "100000"}, "minInvestableAmount": {"currencyCode": "INR", "units": "10000"}, "name": "sn1", "payoutType": "pt1", "returnType": "rt1", "schemeType": "SCHEME_TYPE_XIRR", "tenureInDays": 365}','2022-04-01 18:53:02.460711+05:30','2022-04-01 18:53:02.460711+05:30',NULL, 'SCHEME_NAME_LL_DEFAULT');

-- p2p_investment_transactions
UPSERT
INTO p2p_investment_transactions
(id, investor_id, scheme_id, order_client_req_id, payment_ref_id, vendor_ref_id, type, vendor, status, sub_status, details, created_at, updated_at, deleted_at)
VALUES
('p2p_investment_transaction-1', 'investor-id-2', 'scheme-id-2', 'order-client-req-id-1', '', '', 'INVESTMENT_TRANSACTION_TYPE_INVESTMENT', 'VENDOR_LIQUILOANS', 'CREATED', 'INVESTMENT_TRANSACTION_SUB_STATUS_UNSPECIFIED', '{"amount": {"currencyCode": "INR", "units": "1000"}, "lockInTenureDays": 365, "mode": "mode-1", "orderId": "vendor-order-1", "settlementUtr": "123456", "transactionSource": "source-1"}', '2022-04-06 08:45:05.173080 +00:00', '2022-04-06 08:45:05.173080 +00:00', null),
('p2p_investment_transaction-2', 'investor-id-2', 'scheme-id-2', 'order-client-req-id-2', 'payment-ref-id-1', 'vendor-ref-id-1', 'INVESTMENT_TRANSACTION_TYPE_INVESTMENT', 'VENDOR_LIQUILOANS', 'APPROVED', 'APPROVED_INVESTMENT_PAYMENT', '{"amount": {"currencyCode": "INR", "units": "1000"}, "lockInTenureDays": 365, "mode": "mode-1", "orderId": "vendor-order-1", "settlementUtr": "123456", "transactionSource": "source-1"}', '2022-04-06 08:45:05.173080 +00:00', '2022-04-06 08:45:05.173080 +00:00', null),
('p2p_investment_transaction-3', 'investor-id-2', 'scheme-id-2', 'order-client-req-id-3', 'payment-ref-id-2', '', 'INVESTMENT_TRANSACTION_TYPE_INVESTMENT', 'VENDOR_LIQUILOANS', 'APPROVED', 'APPROVED_INVESTMENT_PAYMENT', '{"amount": {"currencyCode": "INR", "units": "1000"}, "lockInTenureDays": 365, "mode": "mode-1", "orderId": "vendor-order-1", "settlementUtr": "123456", "transactionSource": "source-1"}', '2022-04-06 08:45:05.173080 +00:00', '2022-04-06 08:45:05.173080 +00:00', null),
('p2p_investment_transaction-4', 'investor-id-2', 'scheme-id-2', 'order-client-req-id-4', 'payment-ref-id-3', '', 'INVESTMENT_TRANSACTION_TYPE_INVESTMENT', 'VENDOR_LIQUILOANS', 'SUCCESS', 'SUCCESS_INVESTMENT_COMPLETE', '{"amount": {"currencyCode": "INR", "units": "1000"}, "lockInTenureDays": 365, "mode": "mode-1", "orderId": "vendor-order-1", "settlementUtr": "123456", "transactionSource": "source-1"}', '2022-06-06 08:45:05.173080 +00:00', '2022-06-06 08:45:05.173080 +00:00', null),
('p2p_investment_transaction-5', 'investor-id-2', 'scheme-id-2', 'order-client-req-id-5', 'payment-ref-id-4', '', 'INVESTMENT_TRANSACTION_TYPE_WITHDRAWAL', 'VENDOR_LIQUILOANS', 'SUCCESS', 'SUCCESS_WITHDRAWAL_COMPLETE', '{"amount": {"currencyCode": "INR", "units": "1000"}, "lockInTenureDays": 365, "mode": "mode-1", "orderId": "vendor-order-1", "settlementUtr": "", "transactionSource": "source-1"}', '2022-04-06 08:45:05.173080 +00:00', '2022-04-06 08:45:05.173080 +00:00', null);


UPSERT
INTO p2p_dynamic_inventory
(id, investor_id, investment_transaction_id, expiry_date, details)
VALUES
	('pass-id-1', 'investor-id-2', 'p2p_investment_transaction-1', NULL, '{}'),
	('pass-id-2', 'investor-id-2', 'p2p_investment_transaction-2', NULL, '{}');

-- upi_processed_phone_numbers
UPSERT
INTO upi_processed_phone_numbers
(id, phone_number, verified_at, created_at, updated_at, deleted_at)
VALUES
('df85db92-6892-4414-9969-c87d57748675','{"country_code": 91, "national_number": *********0}','2022-04-11 17:26:09.********* +00:00','2022-04-11 17:26:09.********* +00:00','2022-04-11 17:26:09.********* +00:00',null);

-- merchant_gplace_data
UPSERT INTO merchant_pi_gplace_data
(id, pi_id, gplace_types, business_status, formatted_address, geometry, icon_url, icon_mask_base_uri, icon_background_color, place_name, place_id, created_at, updated_at, deleted_at) VALUES
('8737e484-db3b-11ec-9d64-0242ac120002', '8fd80ed4-db3b-11ec-9d64-0242ac120002', ARRAY['GPLACE_TYPE_BANK', 'GPLACE_TYPE_CAFE'], 'BUSINESS_STATUS_OPERATIONAL', 'bangalore', '{}', 'icon1.png', 'icon2.png', '#12345', 'big bazaar', 'place-id-1', '2022-04-11 17:26:09.********* +00:00', '2022-04-11 17:26:09.********* +00:00', null);

UPSERT
INTO workflow_requests
(id, actor_id, stage, status, version, type, payload, client_req_id, ownership)
VALUES
('6535e977-9107-47f3-9eb7-6aa5e072da30',NULL,'CREATION','INITIATED','V0','TYPE_UNSPECIFIED',NULL,'client-req-id-1', 'EPIFI_TECH'),
('d09e5b74-9abf-11ec-b909-0242ac120002',NULL,'PAYMENT','SUCCESSFUL','V0','TYPE_UNSPECIFIED',NULL,'client-req-id-2', 'EPIFI_TECH'),
('9ef656fc-3e12-4214-838b-f62485655e41', 'actorId', 'COLLECT', 'BLOCKED', 'V0','TYPE_UNSPECIFIED', NULL, 'CiQ4NTY4YzEyZi0xYWMzLTQ3ZjEtOTIwOC0zZmRlMjAwMGNkYTQ=', 'EPIFI_TECH'),
('9ef656fc-3e12-4214-838b-f62485655e42', 'actorId', 'COLLECT', 'BLOCKED', 'V0','TYPE_UNSPECIFIED', NULL, 'CiQ5ZWY2NTZmYy0zZTEyLTQyMTQtODM4Yi1mNjI0ODU2NTVlNDA=', 'EPIFI_TECH'),
('9ef656fc-3e12-4214-838b-f62485655e43', 'actorId', 'COLLECT', 'BLOCKED', 'V0','TYPE_UNSPECIFIED',NULL, 'CiQ5ZWY2NTZmYy0zZTEyLTQyMTQtODM4Yi1mNjI0ODU2NTVlNDQ=', 'EPIFI_TECH'),
('9ef656fc-3e12-4214-838b-f62485655e44', 'actorId', 'COLLECT', 'BLOCKED', 'V0','B2C_FUND_TRANSFER',NULL, 'ChcyZmJhMGEyNi0wYzE4LTExZWQtODYxZBAD', 'EPIFI_TECH'),
('9ef656fc-3e12-4214-838b-f62485655e45', 'actorId', 'COLLECT', 'BLOCKED', 'V0','B2C_FUND_TRANSFER',NULL, 'ChdhNTAwNGYxOC01ZDUyLTQ5OTEtODJhORAD', 'EPIFI_TECH');

UPSERT
INTO workflow_histories
(wf_req_id, stage, status, attempts)
VALUES
('d09e5b74-9abf-11ec-b909-0242ac120002','PAYMENT','SUCCESSFUL', 1),
('d09e5b74-9abf-11ec-b909-0242ac120002','FULFILLMENT','SUCCESSFUL', 1),
('9ef656fc-3e12-4214-838b-f62485655e45', 'FULFILLMENT', 'CREATED', 1);

UPSERT INTO workflow_histories (wf_req_id, stage, status, attempts, deleted_at, updated_at)
VALUES
('9ef656fc-3e12-4214-838b-f62485655e45', 'CREATION', 'CREATED', 1, '2022-01-01 00:00:00.000000 +05:30', '2022-01-01 00:00:00.000000 +05:30'),
('9ef656fc-3e12-4214-838b-f62485655e44', 'CREATION', 'CREATED', 1, '2022-01-01 00:00:00.000000 +05:30', '2022-01-01 00:00:00.000000 +05:30');


-- loan_offers table
UPSERT INTO loan_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, processing_info, valid_since, valid_till, deactivated_at, created_at, updated_at) VALUES
('loan-offer-id-1', 'actor-1', 'vendor-offer-id-1', 'FEDERAL', '{}', '{}', '2022-07-25 00:00:00.000000 +05:30', '2023-07-25 00:00:00.000000 +05:30', NULL, '2022-07-25 00:00:00.000000 +05:30', '2022-07-25 00:00:00.000000 +05:30');

-- loan_offers table
UPSERT INTO loan_offer_eligibility_criteria (id, actor_id, vendor, status, vendor_response, created_at, updated_at) VALUES
	('loan-offer-eligibility-id-1', 'actor-1', 'FEDERAL', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30'),
	('loan-offer-eligibility-id-2', 'actor-2', 'FEDERAL', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30');

-- loan_installment_payout table
UPSERT INTO loan_installment_payout (id, loan_installment_info_id, amount, due_date, payout_date, details, status) VALUES
    ('loan-installment-payout-id-1', 'inst-info-id-2', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS');

--seasons table
UPSERT INTO seasons
(id, reward_offer_group_id, display_details, season_levels, active_since, active_till, display_since, display_till)
VALUES
	('02645f7b-ccc6-4198-89c3-327c9922ffb2', 'a6298923-8c49-446b-911d-f9c781699d12', '{"season_number": 1}', '{"levels": [{"total_rewards": 100}]}','2022-01-01 00:00:00.000000 +05:30','2023-01-01 00:00:00.000000 +05:30','2022-01-01 00:00:00.000000 +05:30','2023-01-01 00:00:00.000000 +05:30'),
	('8150b693-2124-488e-9f13-e07454aa8be0', '18b949a4-d0be-40cf-bc49-1f65916840fb', '{"season_number": 1}', '{"levels": [{"total_rewards": 100}]}','2022-04-01 00:00:00.000000 +05:30','2022-11-01 00:00:00.000000 +05:30','2022-05-01 00:00:00.000000 +05:30','2022-10-01 00:00:00.000000 +05:30');

UPSERT INTO public.account_merchant_infos
(id, pi_id, mcc, address, created_at, updated_at, deleted_at)
VALUES('0fd3583f-018b-439a-8fa4-6586e538b57f', 'pi-id-2', 'mcc', '{}', '2022-06-20 23:42:12.707', '2022-06-20 23:42:12.707', NULL);

UPSERT INTO goals(id, actor_id, target_amount, target_date, name, state, provenance, created_at, updated_at, deleted_at) VALUES
('goal-id-4', 'actor-user-1', '{"currency_code": "INR", "units": 3456}', '2025-05-12 13:06:13.838736', 'goal name 1', 'GOAL_STATE_ACTIVE', 'GOAL_PROVENANCE_EXISTING_SD', '2022-06-11 00:00:00.000000 +05:30', '2022-06-11 00:00:00.000000 +05:30', NULL);

-- event_publish_outbox
UPSERT
INTO event_publish_outbox
(id, payload, queue, created_at, updated_at, deleted_at)
VALUES
	('a5004f18-5d52-4991-82a9-2a1e5010e991','{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "merchant_ref_id": "ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}','queueName1','2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
	('a5004f28-5d52-4991-82a9-2a1e5010e991','{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "merchant_ref_id": "ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}','queueName2','2022-04-11 17:26:09.********* +00:00','2022-04-11 17:26:09.********* +00:00',null),
	('a5004f38-5d52-4991-82a9-2a1e5010e991','{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "merchant_ref_id": "ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}','queueName3','2022-04-12 17:26:09.********* +00:00','2022-04-12 17:26:09.********* +00:00',null);


UPSERT INTO goal_investment_instrument_mappings(id, goal_id, investment_instrument_type, investment_instrument_state, investment_instrument_money_constraints, created_at, updated_at, deleted_at) VALUES
	('goal-iim-4', 'goal-id-4', 'INVESTMENT_INSTRUMENT_TYPE_SMART_DEPOSIT', 'INVESTMENT_INSTRUMENT_STATE_INITIATED', '{"max": {"currency_code": "INR", "units": 20000}, "min": {"currency_code": "INR", "units": 500}}', '2022-06-11 00:00:00.000000 +05:30', '2022-06-11 00:00:00.000000 +05:30', NULL);

UPSERT
INTO upi_accounts
(id, actor_id, account_ref_id, masked_account_number, account_ref_number, pin_set_status, status, account_preference, ifsc_code, account_meta_info, account_type,created_at, updated_at, deleted_at, upi_controls_info)
VALUES
('a5004f18-5d52-4991-82a9-2a1e5010e991', 'actor-id-1', 'FI12345', '123xxxx1234', '11223', 'UPI_PIN_SET_STATUS_PIN_SET','UPI_ACCOUNT_STATUS_CREATED' ,'ACCOUNT_PREFERENCE_PRIMARY', 'ifsc-1',null ,'SAVINGS','2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null, '{"UpiControls": ["UPI_CONTROL_DOMESTIC_PAYMENTS", "UPI_CONTROL_INTERNATIONAL_PAYMENTS"]}'),
('a5004f18-5d52-4991-82a9-2a1e5010e992', 'actor-id-1', '', '123xxxx1233', '12223', 'UPI_PIN_SET_STATUS_PIN_NOT_SET','UPI_ACCOUNT_STATUS_ACTIVE','ACCOUNT_PREFERENCE_UNSPECIFIED', 'ifsc-2',null ,'SAVINGS','2022-04-11 17:26:09.********* +00:00','2022-04-11 17:26:09.********* +00:00',null,'{"UpiControls": ["UPI_CONTROL_DOMESTIC_PAYMENTS"]}'),
('a5004f18-5d52-4991-82a9-2a1e5010e993', 'actor-id-3', 'FI3234', '123xxxx1234', '13223', 'UPI_PIN_SET_STATUS_MAX_RETRIES_PIN_NOT_SET','UPI_ACCOUNT_STATUS_ACTIVE' ,'ACCOUNT_PREFERENCE_PRIMARY', 'ifsc-3', null ,'SAVINGS','2022-04-12 17:26:09.********* +00:00','2022-04-12 17:26:09.********* +00:00',null, '{"UpiControls": ["UPI_CONTROL_DOMESTIC_PAYMENTS"]}');

UPSERT
INTO upi_onboarding_details
(id, account_id, vendor, vpa, client_request_id, action, status, vendor_request_id, created_at, updated_at, deleted_at)
VALUES
('a5004f18-5d52-4991-82a9-2a1e5010e991', 'account-id-1', 'FEDERAL_BANK', 'user1@vpa', 'ChdhNTAwNGYxOC01ZDUyLTQ5OTEtODJhORAD', 'UPI_ONBOARDING_ACTION_LINK', 'UPI_ONBOARDING_STATUS_CREATED', 'vendor-req-1', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
('a5004f18-5d52-4991-82a9-2a1e5010e992', 'account-id-2', 'FEDERAL_BANK', 'user2@vpa', 'ChcyZmJhMGEyNi0wYzE4LTExZWQtODYxZBAD', 'UPI_ONBOARDING_ACTION_LINK', 'UPI_ONBOARDING_STATUS_CREATED', 'vendor-req-2', '2022-04-11 17:26:09.********* +00:00','2022-04-11 17:26:09.********* +00:00',null),
('a5004f18-5d52-4991-82a9-2a1e5010e993', 'account-id-3', 'FEDERAL_BANK', 'user3@vpa', 'ChczY2FlYWUyMC0wYzE5LTExZWQtODYxZBAD', 'UPI_ONBOARDING_ACTION_DELINK', 'UPI_ONBOARDING_STATUS_CREATED', 'vendor-req-3', '2022-04-12 17:26:09.********* +00:00','2022-04-12 17:26:09.********* +00:00',null),
('a5004f18-5d52-4991-82a9-2a1e5010e994', 'account-id-1', 'FEDERAL_BANK', 'user1@vpa', 'Chc5ZWY2NTZmYy0zZTEyLTQyMTQtODM4YhAD', 'UPI_ONBOARDING_ACTION_DELINK', 'UPI_ONBOARDING_STATUS_CREATED', 'vendor-req-4', '2022-04-12 17:26:09.********* +00:00','2022-04-12 17:26:09.********* +00:00',null);

UPSERT
INTO actor_vpa_name_map
(id, actor_id, vpa_name, created_at, updated_at, deleted_at)
VALUES
('a5004f18-5d52-4991-82a9-2a1e5010e991', 'actor-1', 'abcd',  now(), now(), null),
('a5004f18-5d52-4991-82a9-2a1e5010e992', 'actor-2', 'xyz',  now(), now(), null);

-- order_metadata
UPSERT INTO order_metadata (id, order_id, metadata, metadata_type, created_at, updated_at, deleted_at)
VALUES
    ('a5004f18-5d52-4991-82a9-2a1e5010e991','order-1','{"qrData" : "101011010101010001010011"}','METADATA_TYPE_QR', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
    ('a5004f28-5d52-4991-82a9-2a1e5010e991','order-1','{"qrData" : "10101101010110101010001010011"}','METADATA_TYPE_UNSPECIFIED', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
  	('a5004f38-5d52-4991-82a9-2a1e5010e991','order-2','{"qrData" : "1010101010011101010011"}','METADATA_TYPE_UNSPECIFIED', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null);

-- risk_bank_actions
UPSERT INTO risk_bank_actions
(id, client_req_id, actor_id, account_id, account_type, vendor, action, state, request_reason, failure_reason, bank_action_reason, provenance, comms_template, is_recon, bank_action_date, created_at, updated_at)
VALUES
	('a5004f18-5d52-4991-82a9-2a1e5010e991', 'a5004f18-5d52-4991-82a9-xa1e5010e993', 'actor-id-1', 'FI12345', 'SAVINGS', 'FEDERAL_BANK', 'ACTION_FULL_FREEZE', 'STATE_SUCCESS', '{"reason" : "REQUEST_REASON_FRAUDULENT_ACCOUNT", "remarks" : "TEST"}', null, '{"status" : "STATUS_TEST", "reason" : "REASON_TEST"}', 'PROVENANCE_FI', ARRAY['COMMS_TEMPLATE_EMAIL_CF'], false, NULL,'2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30'),
	('a5004f28-5d52-4991-82a9-2a1e5010e992', 'a5004f18-5d52-4991-82a9-xa1e5010e994', 'actor-id-1', 'FI12345', 'SAVINGS', 'FEDERAL_BANK', 'ACTION_UNFREEZE', 'STATE_INITIATED', '{"reason" : "REQUEST_REASON_OTHER", "remarks" : "unfreeze for same"}', 'FAILURE_REASON_DROP_DEDUPE_ALREADY_SENT', '{"status" : "STATUS_TEST", "reason" : "REASON_TEST"}', 'PROVENANCE_FI', ARRAY['COMMS_TEMPLATE_EMAIL_CF'], true, NULL,'2022-08-30 00:00:00.000000 +05:30', '2022-08-30 00:00:00.000000 +05:30'),
	('a5004f28-5d52-4991-82a9-2a1e5010e993', 'a5004f18-5d52-4991-82a9-xa1e5010e995', 'actor-id-2', 'FI123456', 'SAVINGS', 'FEDERAL_BANK', 'ACTION_FULL_FREEZE', 'STATE_SENT_TO_BANK', '{"reason" : "REQUEST_REASON_FRAUDULENT_ACCOUNT", "remarks" : null}', null, '{"status" : "STATUS_TEST", "reason" : "REASON_TEST"}', 'PROVENANCE_FI', ARRAY['COMMS_TEMPLATE_EMAIL_CF'], false, '2022-05-29 00:00:00.000000 +05:30','2022-05-29 00:00:00.000000 +05:30', '2022-05-29 00:00:00.000000 +05:30');

UPSERT INTO risk_bank_actions
(id, client_req_id, actor_id, account_id, account_type, vendor, action, state, request_reason, failure_reason, bank_action_reason, provenance, comms_template, bank_action_date, created_at, updated_at)
VALUES
	('a5004f18-5d52-4991-82a9-2a1e5010e910', 'a5004f18-5d52-4991-82a9-xa1e5010e910', 'actor-id-3', 'FI12345', 'SAVINGS', 'FEDERAL_BANK', 'ACTION_FULL_FREEZE', 'STATE_SUCCESS', '{"reason" : "REQUEST_REASON_FRAUDULENT_ACCOUNT", "remarks" : "TEST"}', null, '{"status" : "STATUS_TEST", "reason" : "REASON_TEST"}', 'PROVENANCE_FI', ARRAY['COMMS_TEMPLATE_EMAIL_CF'], '2022-05-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30');

-- uss_accounts
UPSERT
INTO uss_accounts
(id, actor_id, account_status, vendor, vendor_account_id, external_account_id, vendor_account_info, created_at,
 updated_at, deleted_at)
VALUES ('account-id-1', 'actor-id-1', 'INITIATED', 'ACL', 'vendor-account-id-1', 'ext-account-id-1', null,
		'2022-09-27 11:32:09.566727 +00:00', '2022-09-27 11:32:09.566727 +00:00', null);

-- uss_investors
UPSERT
INTO uss_investors
(id, actor_id, pan_number, disclosures, agreements, created_at, updated_at, deleted_at)
VALUES ('investor-id-1', 'actor-1', 'pan-1', '{"employerAddress": "test-employer-address", "employerName": "test-employee", "employmentPosition": "test-position", "employmentStatus": "EMPLOYED", "immediateFamilyExposed": "DISCLOSURE_STATUS_ACCEPTED", "isAffiliatedExchangeOrFinra": "DISCLOSURE_STATUS_ACCEPTED", "isControlPerson": "DISCLOSURE_STATUS_ACCEPTED", "isPoliticallyExposed": "DISCLOSURE_STATUS_ACCEPTED"}', '{"agreements":[{"agreementType":"ALPACA_MARGIN_AGREEMENT"}]}', '2022-09-27 11:40:19.087724 +00:00', '2022-09-27 11:40:19.087724 +00:00', null);

-- auth requests
UPSERT INTO auth_requests (id, actor_id, client_request_id, flow_name, orch_id, next_action, status, provenance)
VALUES
	('********-ef65-413f-8ea5-f5e40ebb9c9e', 'actor-2', 'client-req-2', 'AUTH_REQUEST_FLOW_LIVENESS', 'orch-2', '{}', 'AUTH_REQUEST_STATUS_PENDING', 'PROVENANCE_CREDIT_CARD');

UPSERT INTO auth_requests (id, actor_id, client_request_id, flow_name, orch_id, next_action, status, provenance)
VALUES
	('********-ef65-413f-8ea5-f5e40ebb9c9f', 'actor-3', 'client-req-3', 'AUTH_REQUEST_FLOW_LIVENESS', 'orch-3', '{}', 'AUTH_REQUEST_STATUS_PENDING', 'PROVENANCE_CREDIT_CARD');

-- auth request stages
UPSERT INTO auth_request_stages (id, auth_request_id, auth_stage, auth_ref_id, status) VALUES ('2d4d3554-98e6-4130-ac22-a43d8436e9ae', '********-ef65-413f-8ea5-f5e40ebb9c9e', 'AUTH_STAGE_LIVENESS', 'orch-3', 'AUTH_REQUEST_STAGE_STATUS_PENDING');

UPSERT INTO auth_request_stages (id, auth_request_id, auth_stage, auth_ref_id, status) VALUES ('2d4d3554-98e6-4130-ac22-a43d8436e9af', '********-ef65-413f-8ea5-f5e40ebb9c9e', 'AUTH_STAGE_LIVENESS', 'orch-2', 'AUTH_REQUEST_STAGE_STATUS_PENDING');

-- upi_request_logs
UPSERT INTO upi_request_logs
    (id, actor_id, account_id, vendor, status, detailed_status, api_type, vendor_req_id, created_at, updated_at, deleted_at)
VALUES
    ('a5004f18-5d52-4991-82a9-2a1e5010e991', 'actor-id-1', 'account-id-1', 'FEDERAL_BANK', 'UPI_REQUEST_LOG_API_STATUS_PENDING', '{"detailed_status_metadata" : [{"raw_status_code" : "raw-status-code-1", "raw_status_description": "raw-status-description-1", "category": "DETAILED_STATUS_CATEGORY_UNSPECIFIED", "created_at": null}]}','UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP', 'vendor-req-id-1', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
	('a5004f28-5d52-4991-82a9-2a1e5010e991', 'actor-id-2', 'account-id-2', 'TWILIO', 'UPI_REQUEST_LOG_API_STATUS_SUCCESS', '{"detailed_status_metadata" : [{"raw_status_code" : "raw-status-code-2", "raw_status_description": "raw-status-description-2", "category": "DETAILED_STATUS_CATEGORY_UNSPECIFIED", "created_at": null}]}','UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP', 'vendor-req-id-2', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
	('a5004f38-5d52-4991-82a9-2a1e5010e991', 'actor-id-3', 'account-id-3', 'M2P', 'UPI_REQUEST_LOG_API_STATUS_FAILED', '{"detailed_status_metadata" : [{"raw_status_code" : "raw-status-code-3", "raw_status_description": "raw-status-description-3", "category": "DETAILED_STATUS_CATEGORY_BUSINESS_DECLINE", "created_at": null}]}','UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE', 'vendor-req-id-3', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
	('a5004f48-5d52-4991-82a9-2a1e5010e991', 'actor-id-4', 'account-id-4', 'M2P', 'UPI_REQUEST_LOG_API_STATUS_FAILED', '{"detailed_status_metadata" : [{"raw_status_code" : "raw-status-code-1", "raw_status_description": "raw-status-description-1", "category": "DETAILED_STATUS_CATEGORY_UNSPECIFIED", "created_at": null}, {"raw_status_code" : "raw-status-code-11", "raw_status_description": "raw-status-description-11", "category": "DETAILED_STATUS_CATEGORY_BUSINESS_DECLINE", "created_at": null}]}','UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE', 'vendor-req-id-4', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null),
	('a5004f58-5d52-4991-82a9-2a1e5010e991', 'actor-id-1', 'account-id-1', 'M2P', 'UPI_REQUEST_LOG_API_STATUS_FAILED', '{"detailed_status_metadata" : [{"raw_status_code" : "raw-status-code-1", "raw_status_description": "raw-status-description-1", "category": "DETAILED_STATUS_CATEGORY_UNSPECIFIED", "created_at": null}]}','UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE', 'vendor-req-id-5', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00',null);

UPSERT INTO credit_report_downloads
    (id, request_id, actor_id, vendor, fetch_type, otp_info, consent_info, process_status, process_sub_status, redirect_deeplink, downloaded_at, completed_at, provenance, credit_report_id, orch_id, next_action, created_at, updated_at, deleted_at)
VALUES
    ('a5004f28-5d52-4991-82a9-2a1e5010e991', 'b5004f28-5d52-4991-82a9-2a1e5010e991', 'actor2', 'EXPERIAN', 'CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', null, null, 'PROVENANCE_UNSPECIFIED', '', '', null, '2022-04-10 17:20:00 +00:00','2022-04-10 17:20:00 +00:00', null),
	('a5004f28-5d52-4991-82a9-2a1e5010e992', 'b5004f28-5d52-4991-82a9-2a1e5010e992', 'actor3', 'EXPERIAN', 'CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', null, null, 'PROVENANCE_UNSPECIFIED', '', '', '{}', '2022-04-10 17:21:00 +00:00','2022-04-10 17:21:00 +00:00', null),
	('a5004f28-5d52-4991-82a9-2a1e5010e993', 'b5004f28-5d52-4991-82a9-2a1e5010e993', 'actor3', 'EXPERIAN', 'CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', '2022-04-10 17:20:00 +00:00', '2022-04-10 17:30:00 +00:00', 'PROVENANCE_UNSPECIFIED', '', '', '{}', '2022-04-10 17:22:00 +00:00','2022-04-10 17:26:09.********* +00:00', null),
	('a5004f28-5d52-4991-82a9-2a1e5010e994', 'b5004f28-5d52-4991-82a9-2a1e5010e994', 'actor3', 'KARZA', 'CREDIT_REPORT_FETCH_TYPE_EXISTING', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', null, null, 'PROVENANCE_ANALYSER', '', '', '{}', '2022-04-10 17:30:00 +00:00','2022-04-10 17:30:00 +00:00', null),
	('a5004f28-5d52-4991-82a9-2a1e5010e995', 'b5004f28-5d52-4991-82a9-2a1e5010e995', 'actor4', 'CIBIL', 'CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', null, null, 'PROVENANCE_UNSPECIFIED', '', '', '{}', '2025-03-10 10:00:00 +00:00','2022-04-10 17:20:00 +00:00', null),
	('a5004f28-5d52-4991-82a9-2a1e5010e996', 'b5004f28-5d52-4991-82a9-2a1e5010e996', 'actor4', 'CIBIL', 'CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', null, null, 'PROVENANCE_UNSPECIFIED', '', '', '{}', '2025-03-10 10:01:00 +00:00','2022-04-10 17:21:00 +00:00', null),
	('a5004f28-5d52-4991-82a9-2a1e5010e997', 'b5004f28-5d52-4991-82a9-2a1e5010e997', 'actor4', 'EXPERIAN', 'CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED', '{}', '{}', 'CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED', 'CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED', '{}', null, null, 'PROVENANCE_UNSPECIFIED', '', '', '{}', '2025-03-10 10:00:00 +00:00','2022-04-10 17:26:09.********* +00:00', null);

UPSERT INTO credit_report_user_subscription_details
    (id, actor_id, vendor, subscription_info, created_at, updated_at, deleted_at)
VALUES
    ('a5004f28-5d52-4991-82a9-2a1a5010e991', 'actor2', 'EXPERIAN', '{}', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00', null),
    ('a5004f28-5d52-4991-82a9-2a1a5010e992', 'actor3', 'EXPERIAN', '{}', '2022-04-10 17:26:09.********* +00:00','2022-04-10 17:26:09.********* +00:00', null);

UPSERT into consents
	(id, actor, actor_id, consent_type, version, device, client_req_id, expires_at, created_at, updated_at)
VALUES
	('id1', '{}', 'actor1', 'CREDIT_REPORT_TNC', 0, '{}', 'clientReqId1', '2022-10-10 00:00:00.********0 +00:00', '2022-04-10 00:00:00.********0 +00:00', '2022-04-10 00:00:00.********0 +00:00'),
	('id2', '{}', 'actor1', 'CREDIT_REPORT_TNC', 0, '{}', 'clientReqId2', '2023-04-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:00'),
	('id3', '{}', 'actor2', 'FI_PRE_APPROVED_LOAN', 0, '{}', 'clientReqId3', '2023-04-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:00'),
	('id4', '{}', 'actor2', 'CREDIT_REPORT_TNC', 0, '{}', 'clientReqId4', null, '2022-10-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:00'),
	('id5', '{}', 'actor3', 'CREDIT_REPORT_TNC', 0, '{}', null, null, '2022-10-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:00'),
	('id6', '{}', 'actor4', 'CREDIT_REPORT_TNC', 0, '{}', null, '2023-04-20 00:00:00.********0 +00:00', '2022-10-20 00:00:00.********0 +00:000', '2022-10-20 00:00:00.********0 +00:00');

UPSERT INTO aml_screening_attempts
	(id, actor_id, client_request_id, product, vendor, customer_details, status, result, last_screening_attempted_at)
VALUES
	('a5004f28-5d52-4991-82a9-2a1a5010e991', 'actor1', 'client-id-1', 'AML_PRODUCT_MUTUAL_FUNDS', 'TSS', '{}', 'AML_SCREENING_STATUS_INITIATED', 'AML_MATCH_UNSPECIFIED', '2022-10-20 23:00:00.********0 +00:000');

UPSERT INTO aml_case_details
	(id, actor_id, review_status, vendor, products, match_details, vendor_case_id, attempt_id)
VALUES
	('a5004f28-5d52-4991-82a9-2a1a5010e991', 'actor1', 'REVIEW_STATUS_PENDING', 'TSS', '{"products":["AML_PRODUCT_MUTUAL_FUNDS"]}', '{}', '1', '1');

UPSERT INTO aml_file_generation_attempts
	(id, client_request_id, file_type, file_s3_paths, updated_at)
VALUES
	('a5004f28-5d52-4991-82a9-2a1a5010e991', 'client-id-1', 'FILE_TYPE_FL1_FL43', '{"s3Paths":["s3Path"]}', '2022-10-20 23:00:00.********0 +00:000');


-- referrals_segmented_components
UPSERT INTO referrals_segmented_components
	(id, segment_id, component, app_feature, component_details, active_since, active_till, created_at)
VALUES
	('a5004f28-5d52-4991-82a9-2a1a5010e991', '1', 'OLD_HOME_SCREEN_WIDGET', 'SAVINGS_ACCOUNT', '{
		  "old_home_screen_widget_display_details": {
		  }
		}', '2022-10-10 17:26:09.********* +00:00', '2023-04-10 17:26:09.********* +00:00', '2023-09-20 07:00:00.********0 +00:00'),
	('a5004f28-5d52-4991-82a9-2a1a5010e992', '2', 'OLD_HOME_SCREEN_WIDGET', 'SAVINGS_ACCOUNT', '{
		"old_home_screen_widget_display_details": {
		}
		}', '2022-10-10 17:26:09.********* +00:00','2023-04-10 17:26:09.********* +00:00', '2023-09-19 07:00:00.********0 +00:00'),
	('a5004f28-5d52-4991-82a9-2a1a5010e993', '1', 'INVITE_FRIENDS_SCREEN_SHARE_CODE_BUTTON', 'SAVINGS_ACCOUNT', '{
			"share_finite_code_action_details": {
			}
	}', '2022-04-10 17:26:09.********* +00:00','2023-01-10 17:26:09.********* +00:00', '2023-09-18 07:00:00.********0 +00:00');

UPSERT
INTO orders (id, from_actor_id, to_actor_id, workflow, status, order_payload, amount, provenance, created_at, expire_at, external_id, updated_at, client_req_id, wf_ref_id, tags)
VALUES
('order-41', 'actor-2', 'actor-3', 'ADD_FUNDS', 'IN_SETTLEMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "merchant_ref_id":"ref-id-1", "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '1 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-41', now() + INTERVAL '1 MINUTE', null, 'wf-req-1','{"Tags": ["DEPOSIT", "SD"]}'),
('order-42', 'actor-2', 'actor-3', 'ADD_FUNDS', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello", "payment_protocol": "IMPS"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '2 MINUTE', now(), 'order-ext-42', now() + INTERVAL '2 MINUTE', null, null,'{"Tags": ["DEPOSIT", "FD"]}'),
('order-43', 'actor-2', 'actor-3', 'ADD_FUNDS', 'IN_SETTLEMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '3 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-43', now () + INTERVAL '3 MINUTE', null, null,'{"Tags": ["MERCHANT"]}'),
('order-44', 'actor-2', 'actor-3', 'ADD_FUNDS', 'CREATED', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello", "payment_protocol":"UPI"}}', '{"currency_code":  "INR", "units":  5000, "nanos":  0}', 'USER_APP', now() + INTERVAL '4 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-44', now() + INTERVAL '4 MINUTE', null, null,'{"Tags": ["MERCHANT", "REWARD"]}'),
('order-45', 'actor-2', 'actor-3', 'ADD_FUNDS', 'IN_SETTLEMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '5 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-45', now() + INTERVAL '5 MINUTE', null, null,'{"Tags": ["MERCHANT", "REWARD","WALLET"]}'),
('order-46', 'actor-2', 'actor-3', 'ADD_FUNDS', 'IN_PAYMENT', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'USER_APP', now() + INTERVAL '6 MINUTE', now() + INTERVAL '5 MINUTE', 'order-ext-46', now() + INTERVAL '6 MINUTE', null, null,'{"Tags": ["WALLET"]}'),
('order-48', 'actor-2', 'ACHKZ9wtGvSu++VNRdu/fRlA230401==', 'NO_OP', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'ATM', now(), now(), 'order-ext-48', now(), null, null, null),
('order-49', 'actor-2', 'ACHKZ9wtGvSu++VNRdu/fRlA230401==', 'NO_OP', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'ATM', now(), now(), 'order-ext-49', now(), null, null, null),
('order-50', 'ACHKZ9wtGvSu++VNRdu/fRlA230401==', 'actor-2', 'NO_OP', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 2000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'ATM', now() + INTERVAL '50 MINUTE', now() + INTERVAL '50 MINUTE', 'order-ext-50', now() + INTERVAL '50 MINUTE', null, null, null),
('order-51', 'ACHKZ9wtGvSu++VNRdu/fRlA230401==', 'actor-2', 'NO_OP', 'PAID', '{"payment_details": {"amount": {"currency_code": "INR", "units": 1000}, "pi_from": "pi-1", "pi_to": "pi-2", "remarks": "hello"}}', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'ATM', now() + INTERVAL '51 MINUTE', now() + INTERVAL '51 MINUTE', 'order-ext-51', now() + INTERVAL '51 MINUTE', null, null, null);

-- transactions
UPSERT
INTO transactions (id, pi_from, pi_to, partner_ref_id, utr, partner_bank, amount, status, protocol_status, payment_protocol,
                          trans_remarks, partner_executed_at, payment_req_info, created_at, updated_at, debited_at, credited_at, order_ref_id)
VALUES ('transaction-41', 'pi-4', 'pi-5', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null ,'NEFT', 'XYZ', null , '{ "req_id": "req-41", "device_id": "device-41"}', now() + INTERVAL '1 MINUTE', '2020-03-24 07:30:52.940000', null, null, 'order-41'),
       ('transaction-42', 'pi-4', 'pi-5', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'CREATED', null, 'NEFT', 'XYZ', null , '{ "req_id": "req-42", "device_id": "device-42"}', now() + INTERVAL '2 MINUTE', '2020-03-25 07:30:52.940000', null, null, 'order-42'),
       ('transaction-43', 'pi-4', 'pi-5', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'IN_PROGRESS', null, 'NEFT', 'XYZ', '2020-03-26 07:30:52.940000', '{ "req_id": "req-43", "device_id": "device-43"}', now() + INTERVAL '3 MINUTE', '2020-03-26 07:30:52.940000', null, null, 'order-43'),
       ('transaction-44', 'pi-4', 'pi-5', null , null, 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'UNKNOWN', null, 'NEFT', 'XYZ', '2020-03-27 07:30:52.940000', '{ "req_id": "req-44", "device_id": "device-44"}', now() + INTERVAL '4 MINUTE', '2020-03-27 07:30:52.940000', null, null, 'order-44'),
       ('transaction-45', 'pi-4', 'pi-5', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'FAILED', null, 'NEFT', 'XYZ', '2020-03-28 07:30:52.940000', '{ "req_id": "req-45", "device_id": "device-45"}', now() + INTERVAL '5 MINUTE', '2020-03-28 07:30:52.940000',null, null, 'order-45'),
    ('transaction-48', 'pi-4', 'pi-5', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'CARD', 'XYZ', '2020-03-28 07:30:52.940000', '{ "req_id": "req-48", "device_id": "device-48"}', now() + INTERVAL '5 MINUTE', '2020-03-28 07:30:52.940000',null, null, 'order-48'),
    ('transaction-49', 'pi-4', 'pi-5', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'SUCCESS', null, 'CARD', 'XYZ', '2020-03-28 07:30:52.940000', '{ "req_id": "req-49", "device_id": "device-49"}', now() + INTERVAL '5 MINUTE', '2020-03-28 07:30:52.940000',null, null, 'order-49'),
    ('transaction-50', 'pi-5', 'pi-4', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  2000, "nanos":  0}', 'SUCCESS', null, 'CARD', 'XYZ', '2020-03-28 07:31:52.940000', '{ "req_id": "req-50", "device_id": "device-50"}', now() + INTERVAL '5 MINUTE', '2020-03-28 07:31:52.940000',null, null, 'order-50'),
    ('transaction-51', 'pi-5', 'pi-4', null , null , 'FEDERAL_BANK',
        '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'SUCCESS', null, 'CARD', 'XYZ', '2020-03-28 07:32:52.940000', '{ "req_id": "req-51", "device_id": "device-51"}', now() + INTERVAL '5 MINUTE', '2020-03-28 07:32:52.940000',null, null, 'order-51');



-- upi_number_pi_mapping
UPSERT
INTO upi_number_pi_mapping (id, pi_id, upi_number, state, expire_at, created_at, updated_at, deleted_at)
VALUES  ('57f83530-407f-4a9a-ada3-998a032a54df', 'pi-id-2', '**********', 'UPI_NUMBER_STATE_DEREGISTERED', '2020-01-01 00:00:00.000000', '2022-01-01 01:01:01.000001', '2022-01-01 01:01:01.000002', null ),
		('57f83510-407f-4a9a-ada3-998a032a54df', 'pi-id-1', '**********', 'UPI_NUMBER_STATE_ACTIVE','0001-01-01 00:00:00.000000', '2000-01-01 01:01:01.000001', '2002-01-01 01:01:01.000001', null ),
	    ('57f83520-407f-4a9a-ada3-998a032a54df', 'pi-id-1', '**********', 'UPI_NUMBER_STATE_INACTIVE','0001-01-01 00:00:00.000000', '2020-01-01 01:00:01.000001', '2022-01-01 01:01:01.000001', null );

-- actor_upi_number_resolution
UPSERT
INTO actor_upi_number_resolution (id, actor_id, upi_number, created_at, updated_at, deleted_at)
VALUES ('57f83530-407f-4a9a-ada3-998a032a54df', 'actor-id-1', '**********', '2022-11-23 05:38:56.184776 +00:00','2022-11-23 05:38:56.184776 +00:00',null),
 	   ('57f83540-407f-4a9a-ada3-998a032a54df', 'actor-id-1', '**********', '2022-11-23 05:38:56.184776 +00:00','2022-11-23 05:38:56.184776 +00:00',null),
 	   ('57f83550-407f-4a9a-ada3-998a032a54df', 'actor-id-1', '**********', '2022-11-23 05:38:56.184776 +00:00','2022-11-23 05:38:56.184776 +00:00',null);

-- account_statement_request_metadata
UPSERT
INTO account_statement_request_metadata (id, client_req_id, statement_document_url, statement_document_url_expiry_time)
VALUES ('57f83112-407f-4a9a-ada3-998a032a54de','client-req-1', 'abc.com', now() + INTERVAL '5 MINUTE');


-- international_fund_transfer_checks
UPSERT INTO international_fund_transfer_checks
    (actor_id, lrs_consumed_limit, sof_document_name, sof_document_updated_at, lrs_limit_updated_at, created_at, updated_at, deleted_at)
VALUES ('ift-actor-1', null, 'sof_123doc.pdf','2022-12-27 06:00:00.000000 +00:00', null,
    '2023-01-10 17:26:09.********* +00:00', '2023-01-10 17:26:09.********* +00:00', null),
    ('ift-actor-2', null, 'sof_123doc.pdf','2022-12-27 06:00:00.000000 +00:00', null,
    '2023-01-10 17:26:09.********* +00:00', '2023-01-10 17:26:09.********* +00:00', null),
    ('ift-actor-3', '{"currency_code":  "INR", "units":  1000, "nanos":  0}', 'sof_123doc.pdf','2022-12-27 06:00:00.000000 +00:00',
        '2022-12-27 06:00:00.000000 +00:00', '2023-01-10 17:26:09.********* +00:00', '2023-01-10 17:26:09.********* +00:00', null);

-- international_fund_transfer_users_blacklist
UPSERT INTO international_fund_transfer_users_blacklist
    (actor_id, created_at, updated_at, deleted_at)
VALUES ('ift-blacklisted-actor', '2022-12-27 06:00:00.000000 +00:00', '2022-12-27 06:00:00.000000 +00:00', null),
    ('ift-non-blacklisted-actor', '2022-12-27 06:00:00.000000 +00:00', '2022-12-27 06:00:00.000000 +00:00', '2022-12-28 06:00:00.000000 +00:00');

-- Adding actors pool accounts for outward remittance for US stocks

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-federal-pool-outward-account', 'EXTERNAL_USER', null, 'US Stocks', 'EPIFI_TECH');

-- Adding pi for US STOCKS OUTWARD REMITTANCE; to which we need to send money for buy

UPSERT INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-us-stock-outward-account', 'BANK_ACCOUNT', 'US Stocks', '{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "FDRL000001", "name": "US Stocks", "secure_account_number": "xxxxxxxxxx0044"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 0, 'EXTERNAL');

-- Adding pi for US STOCKS INWARD REMITTANCE; from which we get money for sell

UPSERT INTO payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-us-stock-inward-account', 'BANK_ACCOUNT', 'US Stocks', '{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "FDRL000001", "name": "US Stocks", "secure_account_number": "xxxxxxxxxx0051"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": true}', 0, 'EXTERNAL');

-- Adding actors pool accounts for outward remittance for US stocks
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-alpaca', 'EXTERNAL_USER', null, 'US Stocks', 'US_STOCKS_ALPACA');

-- Adding pi for US STOCKS OUTWARD REMITTANCE; to which we need to send money for buy

UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification, ownership) VALUES
('paymentinstrument-alpaca-international-account', 'INTERNATIONAL_ACCOUNT', 'US Stocks', '{ "international_account": { "name": "Alpaca Securities LLC", "address": "3 East Third Ave Suite 233 San Mateo CA 94401 USA", "actual_account_number": "1636877", "secure_account_number": "xxx6877", "bank_branch_name": "W. MonroeStreet Chicago", "bank_name": "BMO Harris Bank", "bank_address": { "regionCode": "US", "postalCode": "60603", "administrativeArea": "Illinois", "locality": "Chicago", "sublocality": "W. MonroeStreet", "addressLines": [ "BMO Harris Bank NA - BMO Harris Bank 111. W. MonroeStreet" ], "recipients": ["BMO Harris Bank"], "organization": "BMO Harris Bank" }, "swift_code": "HATRUS44", "routing_code": "*********" } }', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'INTERNAL', 'US_STOCKS_ALPACA');


UPSERT INTO investment_risk_survey (id, actor_id, survey_data, survey_status, created_at, updated_at, deleted_at_unix)
VALUES ('337d7600-962b-11ed-a1eb-0242ac120002', 'actor1', '{"questionsAndAnswers": [{"liability": {"liability": "LIABILITY_10_TO_25_LAKH"},"questionType": "RISK_SURVEY_QUESTION_TYPE_LIABILITIES"}]}', 'SURVEY_STATUS_SUBMITTED', '2023-01-29','2023-01-29', 0);

-- probable known merchants
UPSERT INTO probable_known_merchants
    (merchant_id, pi_id, ds_merchant_id)
VALUES ('694d8ca5-da6f-44dd-849a-fd7530c4b5d0', 'piId1', 'dsId1'),
    ('694d8ca5-da6f-44dd-849a-fd7530c4b5d4', 'piId2', 'dsId2');

UPSERT INTO dc_forex_txn_refunds(id, txn_id, actor_id, total_txn_amount, refund_amount, txn_time, refund_status, refund_process_identifier, txn_time_user_tier, refund_processing_mode, created_at, updated_at, deleted_at, orch_id) VALUES
                                ('forex_refund_id_1', 'txn_id_1', 'actor_id_1', '{}', '{}', '2023-04-15 00:00:00.000000 +00:00', 'REFUND_STATUS_CREATED', 'card/forex_refund_40', 'TIER_FI_INFINITE', 'REFUND_PROCESSING_MODE_BATCH', '2023-04-10 06:00:00.000000 +00:00', '2023-04-10 06:00:00.000000 +00:00', null, 'orch_id1');

UPSERT INTO dc_forex_txn_refunds(id, txn_id, actor_id, total_txn_amount, refund_amount, txn_time, refund_status, refund_process_identifier, txn_time_user_tier, refund_processing_mode, created_at, updated_at, deleted_at, orch_id) VALUES
	('forex_refund_id_2', 'txn_id_2', 'actor_id_2', '{}', '{}', '2023-04-10 00:00:00.000000 +00:00', 'REFUND_STATUS_CREATED', 'card/forex_refund_41', 'TIER_FI_INFINITE', 'REFUND_PROCESSING_MODE_BATCH', '2023-04-10 06:00:00.000000 +00:00', '2023-04-10 06:00:00.000000 +00:00', null, 'orch_id2');

UPSERT INTO referrals_notification_config_infos(id, trigger, finite_code_channel, finite_code_type, variant, segment_expression, content_info, status, created_by, active_from, active_till) VALUES
( 'f41dbdba-a30e-41ae-a336-80e05411c163', 'PERFORM_QUALIFYING_ACTION_TRIGGER', 'IN_APP_REFERRAL', 'REGULAR' , 'VARIANT_1', 'DEFAULT_SEGMENT_ID', '{}', 'CREATED', 'actor-1', '2023-04-10 06:00:00.000000 +00:00', '2025-04-10 06:00:00.000000 +00:00');


UPSERT INTO transaction_amount_breakup(id, transaction_id, amount, breakup_type, conversion_rate, conversion_rate_source, overhead_charge_percentage, created_at, updated_at, deleted_at) VALUES
    ('a5004f18-5d52-4991-82a9-2a1e5010e991', 'transaction-id-1', '{"currency_code":"USD","units":20}', 'AMOUNT_BREAKUP_TYPE_MARKUP', 80,'Conversion_Rate_Source_NPCI_QR',4, '2023-04-10 06:00:00.000000 +00:00', '2023-04-10 06:00:00.000000 +00:00', null),
    ('a5004f18-5d52-4991-82a9-2a1e5010e992', 'transaction-id-1', '{"currency_code":"GBR","units":20000}', 'AMOUNT_BREAKUP_TYPE_GST', 100,'Conversion_Rate_Source_NPCI_QR',null, '2023-04-11 06:00:00.000000 +00:00', '2023-04-11 06:00:00.000000 +00:00', null),
	('a5004f18-5d52-4991-82a9-2a1e5010e993', 'transaction-id-2', '{"currency_code":"USD","units":200}', 'AMOUNT_BREAKUP_TYPE_GST', 80,'Conversion_Rate_Source_NPCI_QR',null, '2023-04-12 06:00:00.000000 +00:00', '2023-04-12 06:00:00.000000 +00:00', null),
    ('a5004f18-5d52-4991-82a9-2a1e5010e994', 'transaction-id-2', '{"currency_code":"GBR","units":100}', 'AMOUNT_BREAKUP_TYPE_MARKUP', 100,'Conversion_Rate_Source_NPCI_QR',4, '2023-04-12 06:00:00.000000 +00:00', '2023-04-12 06:00:00.000000 +00:00', null);

UPSERT INTO employer(id, name_by_source, verification_status, created_at, updated_at, deleted_at) VALUES
    ( 'a5004f18-5d52-4991-82a9-2a1e5010e991', 'employer-name-1', 'VERIFIED',  '2022-04-10 06:00:00.000000 +00:00', '2022-04-10 06:00:00.000000 +00:00', null);

UPSERT INTO employer_pi_mapping(id, employer_id, pi_id, created_at, updated_at, deleted_at, source) VALUES
    ( 'f41dbdba-a30e-41ae-a336-80e05411c163', 'a5004f18-5d52-4991-82a9-2a1e5010e991', 'pi-id-1',  '2023-04-10 06:00:00.000000 +00:00', '2023-04-10 06:00:00.000000 +00:00', null, 'EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS'),
    ( '12fed212-8167-4b64-a212-6fb438582ace', 'a5004f18-5d52-4991-82a9-2a1e5010e991', 'pi-id-2', '2023-12-04 00:00:00.000000 +00:00', '2023-12-04 00:00:00.000000 +00:00', null, 'EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS');

UPSERT INTO
    payment_instruments (id, type, identifier, state, issuer_classification, capabilities) VALUES
	('pi-30', 'UPI_LITE', '{"upi_lite": {"pi_ref_id":"pi-ref-id", "lrn":"lrn-30"}}', 'CREATED', 'INTERNAL', '{"INBOUND_TXN": false, "OUTBOUND_TXN": true}'),
	('pi-23', 'UPI_LITE', '{"upi_lite": {"pi_ref_id":"pi-3", "lrn":"lrn-1"}}', 'CREATED', 'INTERNAL', '{"INBOUND_TXN": false, "OUTBOUND_TXN": true}');

UPSERT INTO savings_account_closure_requests(id, actor_id, savings_account_id, status, status_reason, entry_point, user_feedback, created_at, updated_at, deleted_at) VALUES
    ('a5004f18-5d52-4991-82a9-2a1e5010e901', 'actor_id_01', 'savings_account_id_01', 'SA_CLOSURE_REQUEST_STATUS_INITIATED', 'SA_CLOSURE_REQUEST_STATUS_REASON_OTHERS', 'SA_CLOSURE_REQUEST_ENTRY_POINT_PROFILE', '{}', '2023-12-06 06:00:00.000000 +00:00', '2023-12-06 06:00:00.000000 +00:00', null),
    ('a5004f18-5d52-4991-82a9-2a1e5010e902', 'actor_id_01', 'savings_account_id_01', 'SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY', 'SA_CLOSURE_REQUEST_STATUS_REASON_OTHERS', 'SA_CLOSURE_REQUEST_ENTRY_POINT_CHATBOT', '{}', '2023-12-06 07:00:00.000000 +00:00', '2023-12-06 07:00:00.000000 +00:00', null),
	('a5004f18-5d52-4991-82a9-2a1e5010e903', 'actor_id_02', 'savings_account_id_02', 'SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY', 'SA_CLOSURE_REQUEST_STATUS_REASON_OTHERS', 'SA_CLOSURE_REQUEST_ENTRY_POINT_CHATBOT', '{}', '2023-12-06 08:00:00.000000 +00:00', '2023-12-06 08:00:00.000000 +00:00', null),
	('a5004f18-5d52-4991-82a9-2a1e5010e904', 'actor_id_04', 'savings_account_id_04', 'SA_CLOSURE_REQUEST_STATUS_INITIATED', 'SA_CLOSURE_REQUEST_STATUS_REASON_OTHERS', 'SA_CLOSURE_REQUEST_ENTRY_POINT_CHATBOT', '{}', '2023-12-06 08:00:00.000000 +00:00', '2023-12-06 08:00:00.000000 +00:00', null),
	('a5004f18-5d52-4991-82a9-2a1e5010e905', 'actor_id_04', 'savings_account_id_04', 'SA_CLOSURE_REQUEST_STATUS_SUBMITTED', 'SA_CLOSURE_REQUEST_STATUS_REASON_OTHERS', 'SA_CLOSURE_REQUEST_ENTRY_POINT_CHATBOT', '{}', '2023-12-07 08:00:00.000000 +00:00', '2023-12-07 08:00:00.000000 +00:00', null);

UPSERT INTO sof_details ("id","actor_id","document_type","sof_document_url","document_info","valid_till","limit_strategies","created_at","updated_at","deleted_at") VALUES
    ('SOF7Vj+onY2QjyBZiVtRM+mLA240208==', 'actor-1', 'SOF_DOCUMENT_TYPE_FI_ACCOUNT_STATEMENT', 'url1',
        '{"fiAccountDocInfo": {"fiSavingsAccountId": "acct-1", "fromTime": "2023-05-10T18:30:00Z", "numberOfTransactions": 76, "toTime": "2024-01-01T18:30:00Z"}}', '2023-06-30 18:30:00.000000 +00:00',
        '{"SOF_LIMIT_STRATEGY_LOAN_FIFO": {"loanFifoStrategy": {"loansTxnIds": ["txn4", "txn7"], "remainingLoanAmount": {"currencyCode": "INR", "units": "14000"}}, "updatedAt": "2024-02-01T18:30:00Z"}}',
        '2024-02-08 17:37:00.000000 +00:00', '2024-02-08 17:37:00.000000 +00:00', NULL),
   ('SOF7Vj+ggY2QjyBVjprR-paLEA290208==', 'actor-1', 'SOF_DOCUMENT_TYPE_FI_ACCOUNT_STATEMENT', 'url1',
	'{"fiAccountDocInfo": {"fiSavingsAccountId": "acct-1", "fromTime": "2023-05-10T18:30:00Z", "numberOfTransactions": 76, "toTime": "2024-01-01T18:30:00Z"}}', '2023-06-30 18:30:00.000000 +00:00', '{}',
	'2024-02-10 16:37:00.000000 +00:00', '2024-02-11 17:37:00.000000 +00:00', NULL);

UPSERT INTO order_vendor_order_map (id,order_id,vendor_order_id, vendor, created_at, updated_at, domain_reference_id) VALUES ('a5004f18-5d52-4991-82a9-2a1e5010e902','order-1','vendor-order-1','FEDERAL_BANK','2024-02-29 18:30:00.000000 +00:00','2024-02-29 18:30:00.000000 +00:00','domain-reference-id-1');

UPSERT INTO recurring_payments_vendor_details (id, actor_id, recurring_payment_id, vendor_customer_id, vendor, vendor_payment_id, created_at, updated_at, deleted_at) VALUES
       ('idd-1','actor','recurring-payment-idd-1','vendor-customer-idd-1','FEDERAL_BANK','vendor-payment-idd-1','2024-07-01 18:30:00.000000 +00:00','2024-07-01 18:30:00.000000 +00:00',null),
	   ('a5004f18-5d52-4991-82a9-2a1e5010e900','actor-0','recurring-payment-0','vendor-customer-0','FEDERAL_BANK','vendor-authorisation-payment-id-0','2024-07-01 18:19:00.000000 +00:00','2024-07-01 18:19:00.000000 +00:00',null),
	   ('a5004f18-5d52-4991-82a9-2a1e5010e901','actor-1','recurring-payment-1','vendor-customer-1','FEDERAL_BANK','vendor-authorisation-payment-id-1','2024-07-01 18:20:00.000000 +00:00','2024-07-01 18:30:00.000000 +00:00',null),
	   ('a5004f18-5d52-4991-82a9-2a1e5010e902','actor-1','recurring-payment-2','vendor-customer-2','FEDERAL_BANK','vendor-authorisation-payment-id-2','2024-07-01 18:30:00.000000 +00:00','2024-07-01 18:30:00.000000 +00:00',null);
