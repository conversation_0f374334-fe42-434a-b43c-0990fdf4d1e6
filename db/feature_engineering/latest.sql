CREATE TABLE public.aa_analysed_users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    actor_id character varying(255) NOT NULL,
    analysis jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.aa_analysis_attempts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    client_req_id character varying(255) NOT NULL,
    orchestration_id character varying(255) NOT NULL,
    actor_id character varying(255) NOT NULL,
    request_params jsonb,
    status character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.caps_applicant_details (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    caps_details_id character varying NOT NULL,
    credit_report_downloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    first_name character varying,
    middle_name1 character varying,
    middle_name2 character varying,
    middle_name3 character varying,
    last_name character varying,
    gender_code character varying,
    pan character varying,
    pan_issue_date character varying,
    pan_expiration_date character varying,
    passport_number character varying,
    passport_issue_date character varying,
    passport_expiration_date character varying,
    voter_id_number character varying,
    voter_id_issue_date character varying,
    voter_id_expiration_date character varying,
    driver_license_number character varying,
    driver_license_issue_date character varying,
    driver_license_expiration_date character varying,
    ration_card_number character varying,
    ration_card_issue_date character varying,
    ration_card_expiration_date character varying,
    universal_id_number character varying,
    universal_id_issue_date character varying,
    universal_id_expiration_date character varying,
    date_of_birth_applicant character varying,
    telephone_number_applicant1_st character varying,
    telephone_extension character varying,
    telephone_type character varying,
    mobile_phone_number character varying,
    email_id character varying,
    income character varying,
    marital_status character varying,
    employment_status character varying,
    time_with_employer character varying,
    number_of_major_credit_card_held character varying,
    flat_no_plot_no_house_no_cpaad character varying,
    bldg_no_society_name_cpaad character varying,
    road_no_name_area_locality_cpaad character varying,
    city_cpaad character varying,
    landmark_cpaad character varying,
    state_cpaad character varying,
    pin_code_cpaad character varying,
    country_code_cpaad character varying,
    flat_no_plot_no_house_no_cpaaad character varying,
    bldg_no_society_name_cpaaad character varying,
    road_no_name_area_locality_cpaaad character varying,
    city_cpaaad character varying,
    landmark_cpaaad character varying,
    state_cpaaad character varying,
    pin_code_cpaaad character varying,
    country_code_cpaaad character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.caps_details (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    credit_report_downloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    subscriber_code character varying,
    subscriber_name character varying,
    date_of_request character varying,
    report_time_caps character varying,
    report_id_caps character varying,
    enquiry_reason character varying,
    finance_purpose character varying,
    amount_financed character varying,
    duration_of_agreement character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.cibil_asset (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    status character varying,
    safety_check_failure boolean,
    expiration_date character varying,
    creation_date character varying,
    asset_id character varying,
    asset_type character varying,
    reference_key character varying,
    current_version character varying,
    safety_check_passed boolean,
    frozen character varying,
    deceased_indicator boolean,
    fraud_indicator boolean,
    message_code_symbol character varying,
    message_code_description character varying,
    message_code_rank character varying,
    message_text character varying,
    message_type_symbol character varying,
    message_type_description character varying,
    message_type_rank character varying,
    message_type_abbreviation character varying,
    source_inquiry_date character varying,
    source_bureau_symbol character varying,
    source_bureau_description character varying,
    source_bureau_rank character varying,
    source_bureau_abbreviation character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    message_code_abbreviation character varying,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_borrower_addresses (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    dwelling_symbol character varying,
    dwelling_description character varying,
    dwelling_rank character varying,
    dwelling_abbreviation character varying,
    ownership_symbol character varying,
    ownership_rank character varying,
    ownership_description character varying,
    ownership_abbreviation character varying,
    address_order character varying,
    date_reported character varying,
    partition_set character varying,
    credit_address_serial_number character varying,
    credit_address_address_type character varying,
    credit_address_street_address character varying,
    credit_address_city character varying,
    credit_address_postal_code character varying,
    credit_address_region character varying,
    origin_symbol character varying,
    origin_description character varying,
    origin_rank character varying,
    origin_abbreviation character varying,
    enrich_mode character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_borrower_dobs (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    gender character varying,
    birth_date character varying,
    age character varying,
    partition_set character varying,
    birth_date_day character varying,
    birth_date_month character varying,
    birth_date_year character varying,
    birth_reference character varying,
    birth_inquiry_date character varying,
    birth_locale character varying,
    birth_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_borrower_emails (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    serial_number character varying,
    email character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_borrower_employers (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    serial_number character varying,
    income_freq_indicator character varying,
    net_gross_indicator character varying,
    date_reported character varying,
    name character varying,
    occupation_code_symbol character varying,
    occupation_code_description character varying,
    occupation_code_rank character varying,
    occupation_code_abbreviation character varying,
    partition_code character varying,
    credit_address_type character varying,
    credit_address_city character varying,
    credit_address_postal_code character varying,
    credit_address_region character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    account character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at timestamp with time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL,
    credit_address_street_address character varying
);
CREATE TABLE public.cibil_borrower_identifiers (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    id character varying,
    identifier_name character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    id_serial_number character varying,
    enrich_mode character varying,
    date_issued date,
    date_expiration date,
    date_reported date,
    partition_set character varying,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_borrower_names (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    surname character varying,
    forename character varying,
    partition_set character varying,
    name_type_symbol character varying,
    name_type_description character varying,
    name_type_rank character varying,
    name_type_abbreviation character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_borrower_telephones (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    partition_set character varying,
    enrich_mode character varying,
    serial_number character varying,
    number character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    phone_type_symbol character varying,
    phone_type_description character varying,
    phone_type_rank character varying,
    phone_type_abbreviation character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_credit_score_factors (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    bureau_code character varying,
    factor_symbol character varying,
    factor_description character varying,
    factor_rank character varying,
    factor_abbreviation character varying,
    factor_text_0 character varying,
    factor_text_1 character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_credit_scores (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    credit_score_model_symbol character varying,
    credit_score_model_description character varying,
    credit_score_model_rank character varying,
    credit_score_model_abbreviation character varying,
    risk_score character varying,
    population_rank character varying,
    score_name character varying,
    no_score_reason_symbol character varying,
    no_score_reason_description character varying,
    no_score_reason_rank character varying,
    no_score_reason_abbreviation character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_credit_statements (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    borrower_key character varying,
    statement_type_symbol character varying,
    statement_type_description character varying,
    statement_type_rank character varying,
    statement_type_abbreviation character varying,
    statement character varying,
    date_updated character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_inquiries (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    amount character varying,
    inquiry_type character varying,
    subscriber_name character varying,
    enquiry_control_number character varying,
    description character varying,
    subscriber_number character varying,
    bureau character varying,
    inquiry_date character varying,
    industry_code_symbol character varying,
    industry_code_description character varying,
    industry_code_rank character varying,
    industry_code_abbreviation character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_tradelines (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    account_type_abbreviation character varying,
    account_type_description character varying,
    account_type_symbol character varying,
    dispute_flag_symbol character varying,
    dispute_flag_description character varying,
    dispute_flag_rank character varying,
    dispute_flag_abbreviation character varying,
    dispute_remarks_symbol character varying,
    dispute_remarks_date_updated character varying,
    dispute_remarks_error_date_updated character varying,
    creditor_name character varying,
    date_closed character varying,
    branch character varying,
    high_balance character varying,
    date_opened character varying,
    date_reported character varying,
    accounts_sold_to character varying,
    bureau character varying,
    written_off_amount_total character varying,
    current_balance character varying,
    subscriber_code character varying,
    pay_status_symbol character varying,
    pay_status_description character varying,
    pay_status_rank character varying,
    pay_status_abbreviation character varying,
    date_account_status character varying,
    account_number character varying,
    account_designator_symbol character varying,
    account_designator_description character varying,
    account_designator_rank character varying,
    account_designator_abbreviation character varying,
    account_condition_symbol character varying,
    account_condition_description character varying,
    account_condition_rank character varying,
    account_condition_abbreviation character varying,
    source_reference character varying,
    source_inquiry_date character varying,
    source_locale character varying,
    source_borrower_key character varying,
    industry_code_symbol character varying,
    industry_code_description character varying,
    industry_code_rank character varying,
    industry_code_abbreviation character varying,
    third_party_name character varying,
    no_of_participants character varying,
    "position" character varying,
    settlement_amount character varying,
    written_off_principal character varying,
    verification_indicator_symbol character varying,
    verification_indicator_description character varying,
    verification_indicator_rank character varying,
    verification_indicator_abbreviation character varying,
    open_closed_symbol character varying,
    open_closed_description character varying,
    open_closed_rank character varying,
    open_closed_abbreviation character varying,
    interest_rate character varying,
    payment_frequency_symbol character varying,
    payment_frequency_description character varying,
    payment_frequency_rank character varying,
    payment_frequency_abbreviation character varying,
    serial_number character varying,
    payment_status_history_status character varying,
    payment_status_history_start_date character varying,
    payment_status_history_end_date character varying,
    emi_amount character varying,
    cash_limit character varying,
    date_last_payment character varying,
    actual_payment_amount character varying,
    term_months character varying,
    credit_limit character varying,
    amount_past_due character varying,
    collateral character varying,
    worst_pay_status_symbol character varying,
    worst_pay_status_description character varying,
    worst_pay_status_rank character varying,
    worst_pay_status_abbreviation character varying,
    granted_trade_account_type_symbol character varying,
    granted_trade_account_type_description character varying,
    granted_trade_account_type_rank character varying,
    granted_trade_account_type_abbreviation character varying,
    credit_type_symbol character varying,
    credit_type_description character varying,
    credit_type_rank character varying,
    credit_type_abbreviation character varying,
    collateral_type_symbol character varying,
    collateral_type_description character varying,
    collateral_type_rank character varying,
    collateral_type_abbreviation character varying,
    term_type_symbol character varying,
    term_type_description character varying,
    term_type_rank character varying,
    term_type_abbreviation character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    id character varying,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.cibil_tradelines_histories (
    actor_id character varying,
    credit_report_id character varying,
    credit_report_downloaded_at_ist timestamp with time zone DEFAULT now(),
    date character varying,
    status character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    deleted_at timestamp without time zone,
    tradelines_id character varying,
    generic_remark_symbol character varying,
    generic_remark_description character varying,
    generic_remark_rank character varying,
    generic_remark_abbreviation character varying,
    rating_remark_symbol character varying,
    rating_remark_description character varying,
    rating_remark_rank character varying,
    rating_remark_abbreviation character varying,
    complience_remark_symbol character varying,
    complience_remark_description character varying,
    complience_remark_rank character varying,
    complience_remark_abbreviation character varying,
    monthly_pay_status_date date,
    monthly_pay_status_status character varying,
    monthly_pay_status_changed character varying,
    monthly_pay_status_current_balance character varying,
    monthly_pay_status_high_credit character varying,
    monthly_pay_status_credit_limit character varying,
    monthly_pay_status_payment_due character varying,
    monthly_pay_status_past_due character varying,
    monthly_pay_status_actual_payment character varying,
    pid uuid DEFAULT gen_random_uuid() NOT NULL
);
CREATE TABLE public.credit_account_details (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    credit_report_dowloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    account_holder_type_code character varying,
    account_number character varying,
    account_status character varying,
    account_type character varying,
    amount_past_due character varying,
    credit_limit_amount character varying,
    currency_code character varying,
    current_balance character varying,
    date_closed character varying,
    date_of_addition character varying,
    date_of_last_payment character varying,
    date_reported character varying,
    highest_credit_or_original_loan_amount character varying,
    income character varying,
    identification_number character varying,
    open_date character varying,
    original_charge_off_amount character varying,
    payment_history_profile character varying,
    payment_rating character varying,
    portfolio_type character varying,
    repayment_tenure character varying,
    scheduled_monthly_payment_amount character varying,
    settlement_amount character varying,
    subscriber_name character varying,
    terms_duration character varying,
    value_of_collateral character varying,
    value_of_credits_last_month character varying,
    written_off_amt_principal character varying,
    written_off_amt_total character varying,
    terms_frequency character varying,
    special_comment character varying,
    date_of_first_delinquency character varying,
    suit_filed_wilful_default_written_off_status character varying,
    suit_filed_wilful_default character varying,
    credit_facility_status character varying,
    type_of_collateral character varying,
    rate_of_interest character varying,
    promotional_rate_flag character varying,
    income_indicator character varying,
    income_frequency_indicator character varying,
    default_status_date character varying,
    litigation_status_date character varying,
    written_off_status_date character varying,
    subscriber_comments character varying,
    consumer_comments character varying,
    customer_segment character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.credit_account_histories (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    credit_account_details_id character varying NOT NULL,
    credit_report_downloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    asset_classification character varying,
    days_past_due character varying,
    month character varying,
    year character varying,
    year_month character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.credit_account_holder_details (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    credit_account_details_id character varying NOT NULL,
    credit_report_downloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    first_name_non_normalized character varying,
    middle_name1_non_normalized character varying,
    middle_name2_non_normalized character varying,
    middle_name3_non_normalized character varying,
    surname_non_normalized character varying,
    alias character varying,
    gender_code character varying,
    pan_cahd character varying,
    passport_number_cahd character varying,
    voter_id_number_cahd character varying,
    date_of_birth character varying,
    first_line_of_address_non_normalized character varying,
    second_line_of_address_non_normalized character varying,
    third_line_of_address_non_normalized character varying,
    city_non_normalized character varying,
    fifth_line_of_address_non_normalized character varying,
    state_non_normalized character varying,
    zip_postal_code_non_normalized character varying,
    country_code_non_normalized character varying,
    address_indicator_non_normalized character varying,
    residence_code_non_normalized character varying,
    telephone_number character varying,
    telephone_type character varying,
    telephone_extension character varying,
    mobile_telephone_number character varying,
    fax_number character varying,
    email_id_cahpd character varying,
    pan_cahid character varying,
    pan_issue_date character varying,
    pan_expiration_date character varying,
    passport_number_cahid character varying,
    passport_issue_date character varying,
    passport_expiration_date character varying,
    voter_id_number_cahid character varying,
    voter_id_issue_date character varying,
    voter_id_expiration_date character varying,
    driving_license_number character varying,
    driving_license_issue_date character varying,
    driving_license_expiration_date character varying,
    ration_card_number character varying,
    ration_card_issue_date character varying,
    ration_card_expiration_date character varying,
    universal_id_number character varying,
    universal_id_issue_date character varying,
    universal_id_expiration_date character varying,
    email_id_cahid character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.credit_report_derived_attributes (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    attribute_key character varying,
    attribute_value character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.credit_report_summaries (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    credit_report_dowloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    bureau_score character varying,
    bureau_score_confidence_level character varying,
    bureau_pl_score bigint,
    leverage_score bigint,
    residence_stability_score bigint,
    no_hit_score bigint,
    num_credit_accounts_active character varying,
    num_credit_accounts_closed character varying,
    num_credit_accounts_default character varying,
    num_credit_accounts_total character varying,
    outstanding_balance_all character varying,
    outstanding_balance_secured character varying,
    outstanding_balance_secured_percentage character varying,
    outstanding_balance_unsecured character varying,
    outstanding_balance_unsecured_percentage character varying,
    total_caps_last180_days character varying,
    total_caps_last90_days character varying,
    total_caps_last30_days character varying,
    total_caps_last7_days character varying,
    caps_last_180_days character varying,
    caps_last_90_days character varying,
    caps_last_30_days character varying,
    caps_last_7_days character varying,
    non_credit_caps_last_180_days character varying,
    non_credit_caps_last_90_days character varying,
    non_credit_caps_last_30_days character varying,
    non_credit_caps_last_7_days character varying,
    enquiry_username character varying,
    subscriber character varying,
    subscriber_name character varying,
    version character varying,
    exact_match character varying,
    income_segment character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.credit_reports_raw (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    vendor character varying NOT NULL,
    raw_report bytea,
    consent_valid_till timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL
);
CREATE TABLE public.current_application_details (
    id character varying NOT NULL,
    credit_report_id character varying NOT NULL,
    credit_report_downloaded_at timestamp with time zone NOT NULL,
    actor_id character varying NOT NULL,
    report_id character varying,
    report_date character varying,
    report_time character varying,
    enquiry_reason character varying,
    amount_financed character varying,
    finance_purpose character varying,
    duration_of_agreement character varying,
    first_name character varying,
    middle_name1 character varying,
    middle_name2 character varying,
    middle_name3 character varying,
    last_name character varying,
    gender_code character varying,
    pan character varying,
    pan_issue_date character varying,
    pan_expiration_date character varying,
    passport_number character varying,
    passport_issue_date character varying,
    passport_expiration_date character varying,
    voter_id_number character varying,
    voter_id_issue_date character varying,
    voter_id_expiration_date character varying,
    driving_license_number character varying,
    driving_license_issue_date character varying,
    driving_license_expiration_date character varying,
    ration_card_number character varying,
    ration_card_issue_date character varying,
    ration_card_expiration_date character varying,
    universal_id_number character varying,
    universal_id_issue_date character varying,
    universal_id_expiration_date character varying,
    date_of_birth_applicant character varying,
    telephone_number_applicant1st character varying,
    telephone_extension character varying,
    telephone_type character varying,
    mobile_phone_number character varying,
    email_id character varying,
    income character varying,
    marital_status character varying,
    employment_status character varying,
    time_with_employer character varying,
    number_of_major_credit_card_held character varying,
    flat_no_plot_no_house_catad character varying,
    bldg_no_society_name_catad character varying,
    road_no_name_area_locality_catad character varying,
    city_catad character varying,
    landmark_catad character varying,
    state_catad character varying,
    pin_code_catad character varying,
    country_code_catad character varying,
    flat_no_plot_no_house_no_cataad character varying,
    bldg_no_society_name_cataad character varying,
    road_no_name_area_locality_cataad character varying,
    city_cataad character varying,
    landmark_cataad character varying,
    state_cataad character varying,
    pin_code_cataad character varying,
    country_code_cataad character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    purged_at timestamp with time zone,
    dedup_id character varying
);
CREATE TABLE public.salary_estimation_attempts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    client_req_id character varying NOT NULL,
    source character varying DEFAULT 'SOURCE_UNSPECIFIED'::character varying NOT NULL,
    actor_id character varying NOT NULL,
    client_params jsonb,
    status character varying DEFAULT 'ATTEMPT_STATUS_UNSPECIFIED'::character varying NOT NULL,
    data_sharing_info jsonb,
    analysis_info jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.aa_analysed_users
    ADD CONSTRAINT aa_analysed_users_actor_id_key UNIQUE (actor_id);
ALTER TABLE ONLY public.aa_analysed_users
    ADD CONSTRAINT aa_analysed_users_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_analysis_attempts
    ADD CONSTRAINT aa_analysis_attempts_client_req_id_key UNIQUE (client_req_id);
ALTER TABLE ONLY public.aa_analysis_attempts
    ADD CONSTRAINT aa_analysis_attempts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.caps_applicant_details
    ADD CONSTRAINT caps_applicant_details_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.caps_applicant_details
    ADD CONSTRAINT caps_applicant_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.caps_details
    ADD CONSTRAINT caps_details_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.caps_details
    ADD CONSTRAINT caps_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.cibil_asset
    ADD CONSTRAINT cibil_asset_id_key UNIQUE (credit_report_id);
ALTER TABLE ONLY public.cibil_asset
    ADD CONSTRAINT cibil_asset_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_addresses
    ADD CONSTRAINT cibil_borrower_addresses_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_dobs
    ADD CONSTRAINT cibil_borrower_dob_id_key UNIQUE (credit_report_id);
ALTER TABLE ONLY public.cibil_borrower_dobs
    ADD CONSTRAINT cibil_borrower_dobs_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_emails
    ADD CONSTRAINT cibil_borrower_emails_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_employers
    ADD CONSTRAINT cibil_borrower_employers_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_identifiers
    ADD CONSTRAINT cibil_borrower_identifiers_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_names
    ADD CONSTRAINT cibil_borrower_name_id_key UNIQUE (credit_report_id);
ALTER TABLE ONLY public.cibil_borrower_names
    ADD CONSTRAINT cibil_borrower_names_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_borrower_telephones
    ADD CONSTRAINT cibil_borrower_telephones_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_credit_score_factors
    ADD CONSTRAINT cibil_credit_score_factors_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_credit_scores
    ADD CONSTRAINT cibil_credit_score_id_key UNIQUE (credit_report_id);
ALTER TABLE ONLY public.cibil_credit_scores
    ADD CONSTRAINT cibil_credit_scores_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_credit_statements
    ADD CONSTRAINT cibil_credit_statement_id_key UNIQUE (credit_report_id);
ALTER TABLE ONLY public.cibil_credit_statements
    ADD CONSTRAINT cibil_credit_statements_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_inquiries
    ADD CONSTRAINT cibil_inquiries_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_tradelines_histories
    ADD CONSTRAINT cibil_tradelines_histories_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.cibil_tradelines
    ADD CONSTRAINT cibil_tradelines_pkey PRIMARY KEY (pid);
ALTER TABLE ONLY public.credit_account_details
    ADD CONSTRAINT credit_account_details_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.credit_account_details
    ADD CONSTRAINT credit_account_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_account_histories
    ADD CONSTRAINT credit_account_histories_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.credit_account_histories
    ADD CONSTRAINT credit_account_histories_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_account_holder_details
    ADD CONSTRAINT credit_account_holder_details_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.credit_account_holder_details
    ADD CONSTRAINT credit_account_holder_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_report_derived_attributes
    ADD CONSTRAINT credit_report_derived_attributes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_report_summaries
    ADD CONSTRAINT credit_report_summaries_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.credit_report_summaries
    ADD CONSTRAINT credit_report_summaries_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_reports_raw
    ADD CONSTRAINT credit_reports_raw_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.current_application_details
    ADD CONSTRAINT current_application_details_dedup_id_key UNIQUE (dedup_id);
ALTER TABLE ONLY public.current_application_details
    ADD CONSTRAINT current_application_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_estimation_attempts
    ADD CONSTRAINT salary_estimation_attempts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_estimation_attempts
    ADD CONSTRAINT salary_estimation_attempts_unique_client_req_id_idx UNIQUE (client_req_id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.credit_report_derived_attributes
    ADD CONSTRAINT unique_feature UNIQUE (actor_id, credit_report_id, attribute_key);
CREATE INDEX caps_applicant_details_caps_details_id_idx ON public.caps_applicant_details USING btree (caps_details_id);
CREATE INDEX caps_applicant_details_credit_report_id_idx ON public.caps_applicant_details USING btree (credit_report_id);
CREATE INDEX caps_applicant_details_deleted_at_idx ON public.caps_applicant_details USING btree (deleted_at);
CREATE INDEX caps_applicant_details_updated_at_idx ON public.caps_applicant_details USING btree (updated_at);
CREATE INDEX caps_details_credit_report_id_idx ON public.caps_details USING btree (credit_report_id);
CREATE INDEX caps_details_deleted_at_idx ON public.caps_details USING btree (deleted_at);
CREATE INDEX caps_details_updated_at_idx ON public.caps_details USING btree (updated_at);
CREATE INDEX cibil_asset_deleted_at_idx ON public.cibil_asset USING btree (deleted_at);
CREATE INDEX cibil_asset_downloaded_at_idx ON public.cibil_asset USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_borrower_addresses_credit_report_id_idx ON public.cibil_borrower_addresses USING btree (credit_report_id);
CREATE INDEX cibil_borrower_addresses_deleted_at_idx ON public.cibil_borrower_addresses USING btree (deleted_at);
CREATE INDEX cibil_borrower_addresses_downloaded_at_idx ON public.cibil_borrower_addresses USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_borrower_dobs_deleted_at_idx ON public.cibil_borrower_dobs USING btree (deleted_at);
CREATE INDEX cibil_borrower_dobs_downloaded_at_idx ON public.cibil_borrower_dobs USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_borrower_emails_credit_report_id_idx ON public.cibil_borrower_emails USING btree (credit_report_id);
CREATE INDEX cibil_borrower_emails_deleted_at_idx ON public.cibil_borrower_emails USING btree (deleted_at);
CREATE INDEX cibil_borrower_emails_downloaded_at_idx ON public.cibil_borrower_emails USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_borrower_employers_credit_report_id_idx ON public.cibil_borrower_employers USING btree (credit_report_id);
CREATE INDEX cibil_borrower_employers_deleted_at_idx ON public.cibil_borrower_employers USING btree (deleted_at);
CREATE INDEX cibil_borrower_identifiers_credit_report_id_idx ON public.cibil_borrower_identifiers USING btree (credit_report_id);
CREATE INDEX cibil_borrower_identifiers_deleted_at_idx ON public.cibil_borrower_identifiers USING btree (deleted_at);
CREATE INDEX cibil_borrower_identifiers_downloaded_at_idx ON public.cibil_borrower_identifiers USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_borrower_names_deleted_at_idx ON public.cibil_borrower_names USING btree (deleted_at);
CREATE INDEX cibil_borrower_names_downloaded_at_idx ON public.cibil_borrower_names USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_borrower_telephones_credit_report_id_idx ON public.cibil_borrower_telephones USING btree (credit_report_id);
CREATE INDEX cibil_borrower_telephones_deleted_at_idx ON public.cibil_borrower_telephones USING btree (deleted_at);
CREATE INDEX cibil_borrower_telephones_downloaded_at_idx ON public.cibil_borrower_telephones USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_credit_score_factors_credit_report_id_idx ON public.cibil_credit_score_factors USING btree (credit_report_id);
CREATE INDEX cibil_credit_score_factors_deleted_at_idx ON public.cibil_credit_score_factors USING btree (deleted_at);
CREATE INDEX cibil_credit_score_factors_downloaded_at_idx ON public.cibil_credit_score_factors USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_credit_scores_deleted_at_idx ON public.cibil_credit_scores USING btree (deleted_at);
CREATE INDEX cibil_credit_scores_downloaded_at_idx ON public.cibil_credit_scores USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_credit_statements_deleted_at_idx ON public.cibil_credit_statements USING btree (deleted_at);
CREATE INDEX cibil_credit_statements_downloaded_at_idx ON public.cibil_credit_statements USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_inquiries_credit_report_id_idx ON public.cibil_inquiries USING btree (credit_report_id);
CREATE INDEX cibil_inquiries_deleted_at_idx ON public.cibil_inquiries USING btree (deleted_at);
CREATE INDEX cibil_inquiries_downloaded_at_idx ON public.cibil_inquiries USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_tradelines_credit_report_id_idx ON public.cibil_tradelines USING btree (credit_report_id);
CREATE INDEX cibil_tradelines_deleted_at_idx ON public.cibil_tradelines USING btree (deleted_at);
CREATE INDEX cibil_tradelines_downloaded_at_idx ON public.cibil_tradelines USING btree (credit_report_downloaded_at_ist);
CREATE INDEX cibil_tradelines_histories_credit_report_id_idx ON public.cibil_tradelines_histories USING btree (credit_report_id);
CREATE INDEX cibil_tradelines_histories_deleted_at_idx ON public.cibil_tradelines_histories USING btree (deleted_at);
CREATE INDEX cibil_tradelines_histories_downloaded_at_idx ON public.cibil_tradelines_histories USING btree (credit_report_downloaded_at_ist);
CREATE INDEX credit_account_details_credit_report_id_idx ON public.credit_account_details USING btree (credit_report_id);
CREATE INDEX credit_account_details_deleted_at_idx ON public.credit_account_details USING btree (deleted_at);
CREATE INDEX credit_account_details_updated_at_idx ON public.credit_account_details USING btree (updated_at);
CREATE INDEX credit_account_histories_credit_account_details_id_idx ON public.credit_account_histories USING btree (credit_account_details_id);
CREATE INDEX credit_account_histories_credit_report_id_idx ON public.credit_account_histories USING btree (credit_report_id);
CREATE INDEX credit_account_histories_deleted_at_idx ON public.credit_account_histories USING btree (deleted_at);
CREATE INDEX credit_account_history_updated_at_idx ON public.credit_account_histories USING btree (updated_at);
CREATE INDEX credit_account_holder_details_credit_account_details_id_idx ON public.credit_account_holder_details USING btree (credit_account_details_id);
CREATE INDEX credit_account_holder_details_credit_report_id_idx ON public.credit_account_holder_details USING btree (credit_report_id);
CREATE INDEX credit_account_holder_details_deleted_at_idx ON public.credit_account_holder_details USING btree (deleted_at);
CREATE INDEX credit_account_holder_details_updated_at_idx ON public.credit_account_holder_details USING btree (updated_at);
CREATE INDEX credit_report_derived_attributes_credit_report_id_idx ON public.credit_report_derived_attributes USING btree (credit_report_id);
CREATE INDEX credit_report_derived_attributes_deleted_at_idx ON public.credit_report_derived_attributes USING btree (deleted_at);
CREATE INDEX credit_report_summaries_credit_report_id_idx ON public.credit_report_summaries USING btree (credit_report_id);
CREATE INDEX credit_report_summaries_deleted_at_idx ON public.credit_report_summaries USING btree (deleted_at);
CREATE INDEX credit_report_summaries_updated_at_idx ON public.credit_report_summaries USING btree (updated_at);
CREATE INDEX credit_reports_raw_actor_id ON public.credit_reports_raw USING btree (actor_id);
CREATE INDEX credit_reports_raw_consent_valid_till_deleted_at ON public.credit_reports_raw USING btree (consent_valid_till, deleted_at_unix);
CREATE INDEX credit_reports_raw_updated_at ON public.credit_reports_raw USING btree (updated_at);
CREATE INDEX current_application_details_credit_report_id_idx ON public.current_application_details USING btree (credit_report_id);
CREATE INDEX current_application_details_deleted_at_idx ON public.current_application_details USING btree (deleted_at);
CREATE INDEX current_application_details_updated_at_idx ON public.current_application_details USING btree (updated_at);
CREATE INDEX derived_attributes_actor_id_idx ON public.credit_report_derived_attributes USING btree (actor_id);
CREATE INDEX idx_aa_analysed_users_updated_at_desc ON public.aa_analysed_users USING btree (updated_at DESC);
CREATE INDEX idx_aa_analysis_attempts_updated_at_desc ON public.aa_analysis_attempts USING btree (updated_at DESC);
ALTER TABLE ONLY public.caps_applicant_details
    ADD CONSTRAINT caps_applicant_details_fk FOREIGN KEY (caps_details_id) REFERENCES public.caps_details(id);
ALTER TABLE ONLY public.credit_account_histories
    ADD CONSTRAINT credit_account_history_fk FOREIGN KEY (credit_account_details_id) REFERENCES public.credit_account_details(id);
ALTER TABLE ONLY public.credit_account_holder_details
    ADD CONSTRAINT credit_account_holder_details_fk FOREIGN KEY (credit_account_details_id) REFERENCES public.credit_account_details(id);
