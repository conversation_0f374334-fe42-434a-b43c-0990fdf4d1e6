--- Prod user stuck to manual intervention in loan disbursal state
UPDATE loan_step_executions
SET
	status = 'LOAN_STEP_EXECUTION_STATUS_CREATED',
	updated_at = NOW(),
	completed_at = NULL
WHERE
	id = 'PALSEQeEQRSTPTWqs8pm83wurlQ250518==' AND
	status = 'LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION';

UPDATE loan_requests
SET status = 'LOAN_REQUEST_STATUS_PENDING',
	completed_at = NULL,
	updated_at = NOW()
WHERE
	id in ('PALRKPEKDbUMRZ6xdivfAoTWuA250518==') AND
	status = 'LOAN_REQUEST_STATUS_MANUAL_INTERVENTION';
