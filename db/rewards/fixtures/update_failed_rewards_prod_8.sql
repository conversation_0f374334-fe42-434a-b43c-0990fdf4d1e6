-- SD rewards moved to permanent failure state due permanent failure at upstream service.
-- Deleting failed processing requests and updating the state of these rewards to 'PROCESSING_PENDING'.
-- We need to re-trigger the processing for this reward through a sherlock dev action after running this fixture.
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=12148

update processing_requests set is_deleted = true where id = 'c8d346d4-366f-40e9-8f2e-da1a427e472f';
update rewards set processing_ref = '', status = 'PROCESSING_PENDING' where id = 'RW210807o7YZoulBTBep0epjsS8PdQ==' AND status = 'PROCESSING_FAILED';

update processing_requests set is_deleted = true where id = '8b6d4efb-6066-40a1-a6f6-12dffef92a2d';
update rewards set processing_ref = '', status = 'PROCESSING_PENDING' where id = 'RW210807K66OWz8kQhS1ff1EsX0FFg==' AND status = 'PROCESSING_FAILED';

update processing_requests set is_deleted = true where id = 'b8911046-88eb-4a20-bcfb-f0b1db32c369';
update rewards set processing_ref = '', status = 'PROCESSING_PENDING' where id = 'RW210808bBciXt0WSLajuV4zHLQpNw==' AND status = 'PROCESSING_FAILED';
