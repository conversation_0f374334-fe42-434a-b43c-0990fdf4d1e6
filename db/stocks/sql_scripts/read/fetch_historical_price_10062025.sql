SELECT sl.symbol, sl.exchange, hp.price_date, hp.price_derived_date, hp.close_price
FROM historical_prices hp
         INNER JOIN (
    SELECT external_id, symbol, exchange
    FROM security_listings
    WHERE symbol IN ('CANBK', '532483', 'HDFCBANK', '500180')
      AND exchange IN ('EXCHANGE_INDIA_BSE', 'EXCHANGE_INDIA_NSE')
) sl ON hp.security_listing_id = sl.external_id
WHERE hp.price_date >= '2025-06-03'
  AND hp.price_date <= '2025-06-10'
ORDER BY hp.price_date DESC;