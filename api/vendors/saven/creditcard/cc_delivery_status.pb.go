// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/saven/creditcard/cc_delivery_status.proto

package creditcard

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCreditCardTrackingDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingData *TrackingData `protobuf:"bytes,1,opt,name=tracking_data,proto3" json:"tracking_data,omitempty"`
	Exception    *Exception    `protobuf:"bytes,2,opt,name=exception,proto3" json:"exception,omitempty"`
}

func (x *GetCreditCardTrackingDetailsResponse) Reset() {
	*x = GetCreditCardTrackingDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardTrackingDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardTrackingDetailsResponse) ProtoMessage() {}

func (x *GetCreditCardTrackingDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardTrackingDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardTrackingDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{0}
}

func (x *GetCreditCardTrackingDetailsResponse) GetTrackingData() *TrackingData {
	if x != nil {
		return x.TrackingData
	}
	return nil
}

func (x *GetCreditCardTrackingDetailsResponse) GetException() *Exception {
	if x != nil {
		return x.Exception
	}
	return nil
}

type TrackingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PickupDate string `protobuf:"bytes,1,opt,name=pickup_date,proto3" json:"pickup_date,omitempty"`
	// eg - DELIVERED, IN_TRANSIT, OUT_FOR_DELIVERY, SHIPPED
	CurrentStatus string `protobuf:"bytes,2,opt,name=current_status,proto3" json:"current_status,omitempty"`
	// Detailed information for each scan for the order
	Scans []*Scan `protobuf:"bytes,3,rep,name=scans,json=scan,proto3" json:"scans,omitempty"`
	// Carrier partner for delivery eg - BLUEDART, DELHIVERY, INDIAPOST
	Carrier string `protobuf:"bytes,4,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// Vendor who is responsible for printing the card eg - MCT, SESHAASAI
	CardPrintingVendor string `protobuf:"bytes,5,opt,name=card_printing_vendor,proto3" json:"card_printing_vendor,omitempty"`
	// AWB number for the shipment
	AwbNumber   string       `protobuf:"bytes,6,opt,name=awb_number,proto3" json:"awb_number,omitempty"`
	ExtraFields *ExtraFields `protobuf:"bytes,7,opt,name=extra_fields,proto3" json:"extra_fields,omitempty"`
	TrackingUrl string       `protobuf:"bytes,8,opt,name=tracking_url,proto3" json:"tracking_url,omitempty"`
}

func (x *TrackingData) Reset() {
	*x = TrackingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingData) ProtoMessage() {}

func (x *TrackingData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingData.ProtoReflect.Descriptor instead.
func (*TrackingData) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{1}
}

func (x *TrackingData) GetPickupDate() string {
	if x != nil {
		return x.PickupDate
	}
	return ""
}

func (x *TrackingData) GetCurrentStatus() string {
	if x != nil {
		return x.CurrentStatus
	}
	return ""
}

func (x *TrackingData) GetScans() []*Scan {
	if x != nil {
		return x.Scans
	}
	return nil
}

func (x *TrackingData) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *TrackingData) GetCardPrintingVendor() string {
	if x != nil {
		return x.CardPrintingVendor
	}
	return ""
}

func (x *TrackingData) GetAwbNumber() string {
	if x != nil {
		return x.AwbNumber
	}
	return ""
}

func (x *TrackingData) GetExtraFields() *ExtraFields {
	if x != nil {
		return x.ExtraFields
	}
	return nil
}

func (x *TrackingData) GetTrackingUrl() string {
	if x != nil {
		return x.TrackingUrl
	}
	return ""
}

type Exception struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusCode   string `protobuf:"bytes,1,opt,name=status_code,json=error_code,proto3" json:"status_code,omitempty"`
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,proto3" json:"error_message,omitempty"`
}

func (x *Exception) Reset() {
	*x = Exception{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Exception) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Exception) ProtoMessage() {}

func (x *Exception) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Exception.ProtoReflect.Descriptor instead.
func (*Exception) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{2}
}

func (x *Exception) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *Exception) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type Scan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScanTime     string `protobuf:"bytes,1,opt,name=scan_time,json=time,proto3" json:"scan_time,omitempty"`
	Location     string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	StatusDetail string `protobuf:"bytes,3,opt,name=status_detail,proto3" json:"status_detail,omitempty"`
}

func (x *Scan) Reset() {
	*x = Scan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scan) ProtoMessage() {}

func (x *Scan) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scan.ProtoReflect.Descriptor instead.
func (*Scan) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{3}
}

func (x *Scan) GetScanTime() string {
	if x != nil {
		return x.ScanTime
	}
	return ""
}

func (x *Scan) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Scan) GetStatusDetail() string {
	if x != nil {
		return x.StatusDetail
	}
	return ""
}

type ExtraFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpectedDeliveryDate string `protobuf:"bytes,1,opt,name=expected_delivery_date,proto3" json:"expected_delivery_date,omitempty"`
}

func (x *ExtraFields) Reset() {
	*x = ExtraFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraFields) ProtoMessage() {}

func (x *ExtraFields) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraFields.ProtoReflect.Descriptor instead.
func (*ExtraFields) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{4}
}

func (x *ExtraFields) GetExpectedDeliveryDate() string {
	if x != nil {
		return x.ExpectedDeliveryDate
	}
	return ""
}

type GetCreditCardTrackingDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this is the unique identifier for the user at vendor's end i.e. there will be a one to one mapping between this and actor_id ( user identifier across epifi systems),
	// this is referred as external_user_id in across epifi system
	InternalUserId string `protobuf:"bytes,1,opt,name=internal_user_id,proto3" json:"internal_user_id,omitempty"`
}

func (x *GetCreditCardTrackingDetailsRequest) Reset() {
	*x = GetCreditCardTrackingDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardTrackingDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardTrackingDetailsRequest) ProtoMessage() {}

func (x *GetCreditCardTrackingDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardTrackingDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardTrackingDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{5}
}

func (x *GetCreditCardTrackingDetailsRequest) GetInternalUserId() string {
	if x != nil {
		return x.InternalUserId
	}
	return ""
}

type UpdateCreditCardDeliveryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeliveryDetails *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails `protobuf:"bytes,1,opt,name=delivery_details,json=cardTrackingDetails,proto3" json:"delivery_details,omitempty"`
	UserId          string                                                 `protobuf:"bytes,2,opt,name=user_id,json=epifiUserId,proto3" json:"user_id,omitempty"`
}

func (x *UpdateCreditCardDeliveryStatusRequest) Reset() {
	*x = UpdateCreditCardDeliveryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditCardDeliveryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditCardDeliveryStatusRequest) ProtoMessage() {}

func (x *UpdateCreditCardDeliveryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditCardDeliveryStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateCreditCardDeliveryStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCreditCardDeliveryStatusRequest) GetDeliveryDetails() *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails {
	if x != nil {
		return x.DeliveryDetails
	}
	return nil
}

func (x *UpdateCreditCardDeliveryStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UpdateCreditCardDeliveryStatusRequest_DeliveryDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeliveryStatus string `protobuf:"bytes,1,opt,name=delivery_status,json=deliveryStatus,proto3" json:"delivery_status,omitempty"`
	DeliveryVendor string `protobuf:"bytes,2,opt,name=delivery_vendor,json=deliveryVendor,proto3" json:"delivery_vendor,omitempty"`
	TrackingUrl    string `protobuf:"bytes,3,opt,name=tracking_url,json=trackingUrl,proto3" json:"tracking_url,omitempty"`
}

func (x *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) Reset() {
	*x = UpdateCreditCardDeliveryStatusRequest_DeliveryDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) ProtoMessage() {}

func (x *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditCardDeliveryStatusRequest_DeliveryDetails.ProtoReflect.Descriptor instead.
func (*UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP(), []int{6, 0}
}

func (x *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) GetDeliveryStatus() string {
	if x != nil {
		return x.DeliveryStatus
	}
	return ""
}

func (x *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) GetDeliveryVendor() string {
	if x != nil {
		return x.DeliveryVendor
	}
	return ""
}

func (x *UpdateCreditCardDeliveryStatusRequest_DeliveryDetails) GetTrackingUrl() string {
	if x != nil {
		return x.TrackingUrl
	}
	return ""
}

var File_api_vendors_saven_creditcard_cc_delivery_status_proto protoreflect.FileDescriptor

var file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x73, 0x61,
	0x76, 0x65, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63,
	0x63, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x73, 0x61, 0x76, 0x65, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x63, 0x61, 0x72, 0x64, 0x22, 0xbf, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x73, 0x61, 0x76, 0x65, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x45, 0x0a, 0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x73, 0x61, 0x76, 0x65, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x65, 0x78,
	0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf2, 0x02, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x73, 0x61, 0x76, 0x65, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x04, 0x73, 0x63, 0x61, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x14, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72,
	0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x77, 0x62,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x77, 0x62, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0c, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x73, 0x61,
	0x76, 0x65, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x52, 0x0a, 0x09,
	0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x61, 0x0a, 0x04, 0x53, 0x63, 0x61, 0x6e, 0x12, 0x17, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x22, 0x45, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x12, 0x36, 0x0a, 0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x51, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xd3, 0x02,
	0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x53, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x73, 0x61, 0x76, 0x65, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x13, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x86, 0x01, 0x0a, 0x0f, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x55, 0x72, 0x6c, 0x42, 0x6a, 0x0a, 0x33, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x73, 0x61, 0x76, 0x65, 0x6e, 0x2e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x73,
	0x61, 0x76, 0x65, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescOnce sync.Once
	file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescData = file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDesc
)

func file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescGZIP() []byte {
	file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescOnce.Do(func() {
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescData)
	})
	return file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDescData
}

var file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_vendors_saven_creditcard_cc_delivery_status_proto_goTypes = []interface{}{
	(*GetCreditCardTrackingDetailsResponse)(nil),                  // 0: api.vendors.saven.creditcard.GetCreditCardTrackingDetailsResponse
	(*TrackingData)(nil),                                          // 1: api.vendors.saven.creditcard.TrackingData
	(*Exception)(nil),                                             // 2: api.vendors.saven.creditcard.Exception
	(*Scan)(nil),                                                  // 3: api.vendors.saven.creditcard.Scan
	(*ExtraFields)(nil),                                           // 4: api.vendors.saven.creditcard.ExtraFields
	(*GetCreditCardTrackingDetailsRequest)(nil),                   // 5: api.vendors.saven.creditcard.GetCreditCardTrackingDetailsRequest
	(*UpdateCreditCardDeliveryStatusRequest)(nil),                 // 6: api.vendors.saven.creditcard.UpdateCreditCardDeliveryStatusRequest
	(*UpdateCreditCardDeliveryStatusRequest_DeliveryDetails)(nil), // 7: api.vendors.saven.creditcard.UpdateCreditCardDeliveryStatusRequest.DeliveryDetails
}
var file_api_vendors_saven_creditcard_cc_delivery_status_proto_depIdxs = []int32{
	1, // 0: api.vendors.saven.creditcard.GetCreditCardTrackingDetailsResponse.tracking_data:type_name -> api.vendors.saven.creditcard.TrackingData
	2, // 1: api.vendors.saven.creditcard.GetCreditCardTrackingDetailsResponse.exception:type_name -> api.vendors.saven.creditcard.Exception
	3, // 2: api.vendors.saven.creditcard.TrackingData.scans:type_name -> api.vendors.saven.creditcard.Scan
	4, // 3: api.vendors.saven.creditcard.TrackingData.extra_fields:type_name -> api.vendors.saven.creditcard.ExtraFields
	7, // 4: api.vendors.saven.creditcard.UpdateCreditCardDeliveryStatusRequest.delivery_details:type_name -> api.vendors.saven.creditcard.UpdateCreditCardDeliveryStatusRequest.DeliveryDetails
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_vendors_saven_creditcard_cc_delivery_status_proto_init() }
func file_api_vendors_saven_creditcard_cc_delivery_status_proto_init() {
	if File_api_vendors_saven_creditcard_cc_delivery_status_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardTrackingDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Exception); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardTrackingDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditCardDeliveryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditCardDeliveryStatusRequest_DeliveryDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_saven_creditcard_cc_delivery_status_proto_goTypes,
		DependencyIndexes: file_api_vendors_saven_creditcard_cc_delivery_status_proto_depIdxs,
		MessageInfos:      file_api_vendors_saven_creditcard_cc_delivery_status_proto_msgTypes,
	}.Build()
	File_api_vendors_saven_creditcard_cc_delivery_status_proto = out.File
	file_api_vendors_saven_creditcard_cc_delivery_status_proto_rawDesc = nil
	file_api_vendors_saven_creditcard_cc_delivery_status_proto_goTypes = nil
	file_api_vendors_saven_creditcard_cc_delivery_status_proto_depIdxs = nil
}
