package accounts

import (
	"fmt"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
)

var (
	notificationEventTypeMap = map[TransactionType]paymentNotificationPb.TransactionDetails_NotificationEventType{
		TransactionType_DEBIT:  paymentNotificationPb.TransactionDetails_DEBIT,
		TransactionType_CREDIT: paymentNotificationPb.TransactionDetails_CREDIT,
	}
)

func (stmtTxn *GetAccountStatementResponse_Transaction) ConvertToNotificationTxnDetails() *paymentNotificationPb.TransactionDetails {
	if stmtTxn == nil {
		return nil
	}

	txnTm := stmtTxn.GetTransactionTimestamp().AsTime()
	return &paymentNotificationPb.TransactionDetails{
		AccountNumber:         stmtTxn.GetAccountNumber(),
		Id:                    stmtTxn.GetTransactionId(),
		Timestamp:             stmtTxn.GetTransactionTimestamp(),
		Date:                  datetime.TimeToDate(&txnTm),
		ValueDate:             stmtTxn.GetValueDate(),
		Amount:                stmtTxn.GetAmount(),
		Particular:            stmtTxn.GetTransactionDescription(),
		ReferenceNumber:       stmtTxn.GetTransactionReference(),
		Remarks:               stmtTxn.GetRemarks(),
		NotificationEventType: notificationEventTypeMap[stmtTxn.GetTransactionType()],
		AdditionalParticular:  stmtTxn.GetAdditionalParticular(),
		BatchSerialId:         stmtTxn.GetBatchSerialId(),
		InstrumentDetails:     stmtTxn.GetInstrumentDetails(),
	}
}

func (stmtTxn *GetMiniStatementResponse_Transaction) ConvertToNotificationTxnDetails(acctNo string) *paymentNotificationPb.TransactionDetails {
	if stmtTxn == nil {
		return nil
	}

	txnTm := stmtTxn.GetTransactionTimestamp().AsTime()
	return &paymentNotificationPb.TransactionDetails{
		AccountNumber:         acctNo,
		Id:                    stmtTxn.GetTransactionId(),
		Timestamp:             stmtTxn.GetTransactionTimestamp(),
		Date:                  datetime.TimeToDate(&txnTm),
		ValueDate:             stmtTxn.GetValueDate(),
		Amount:                stmtTxn.GetAmount(),
		Particular:            stmtTxn.GetTransactionDescription(),
		ReferenceNumber:       stmtTxn.GetTransactionReference(),
		Remarks:               stmtTxn.GetRemarks(),
		NotificationEventType: notificationEventTypeMap[stmtTxn.GetTransactionType()],
		AdditionalParticular:  stmtTxn.GetAdditionalParticular(),
		BatchSerialId:         stmtTxn.GetBatchSerialId(),
		InstrumentDetails:     stmtTxn.GetInstrumentDetails(),
	}
}

func (stmtTxn *TransactionV1) ConvertToNotificationTxnDetails() *paymentNotificationPb.TransactionDetails {
	if stmtTxn == nil {
		return nil
	}

	txnTm := stmtTxn.GetTransactionTimestamp().AsTime()
	return &paymentNotificationPb.TransactionDetails{
		AccountNumber:         stmtTxn.GetAccountNumber(),
		Id:                    stmtTxn.GetTransactionId(),
		Timestamp:             stmtTxn.GetTransactionTimestamp(),
		Date:                  datetime.TimeToDate(&txnTm),
		ValueDate:             stmtTxn.GetValueDate(),
		Amount:                stmtTxn.GetAmount(),
		Particular:            stmtTxn.GetTransactionDescription(),
		ReferenceNumber:       stmtTxn.GetTransactionReference(),
		Remarks:               stmtTxn.GetRemarks(),
		NotificationEventType: notificationEventTypeMap[stmtTxn.GetTransactionType()],
		AdditionalParticular:  stmtTxn.GetAdditionalParticular(),
		BatchSerialId:         stmtTxn.GetBatchSerialId(),
		InstrumentDetails:     stmtTxn.GetInstrumentDetails(),
	}
}

func (d *StatementTransactionDedupeId) GetId() (string, error) {
	id, err := idgen.EncodeProtoToStdBase64(d)
	if err != nil {
		return "", fmt.Errorf("error in base64 conversion %w", err)
	}

	return id, nil
}

func (x *ThirdPartyAccountCollectionResponse) IsBalanceTransferSuccessful() bool {
	return x.ErrorCode == ThirdPartyAccountCollectionResponse_ERROR_CODE_TRANSACTION_SUCCESSFUL && x.Utr != ""
}
