//go:generate gen_sql -types=AtlasGetAuthQuestionsResponse

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/credit_report/cibil.proto

package credit_report

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AtlasFulfillOfferResponse_Status int32

const (
	// Returned an success
	AtlasFulfillOfferResponse_OK AtlasFulfillOfferResponse_Status = 0
	// denotes that either first name or last name was missing in the request.
	AtlasFulfillOfferResponse_INVALID_ARGUMENT_INCOMPLETE_USER_NAME AtlasFulfillOfferResponse_Status = 101
	// denotes that customer already exists with different client_user_key, use that.
	AtlasFulfillOfferResponse_CONFLICT_SSN_EXISTS AtlasFulfillOfferResponse_Status = 102
	// denotes that report is not found for user, it is a permanent failure.
	AtlasFulfillOfferResponse_NOT_FOUND_NO_HIT AtlasFulfillOfferResponse_Status = 103
)

// Enum value maps for AtlasFulfillOfferResponse_Status.
var (
	AtlasFulfillOfferResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "INVALID_ARGUMENT_INCOMPLETE_USER_NAME",
		102: "CONFLICT_SSN_EXISTS",
		103: "NOT_FOUND_NO_HIT",
	}
	AtlasFulfillOfferResponse_Status_value = map[string]int32{
		"OK":                                    0,
		"INVALID_ARGUMENT_INCOMPLETE_USER_NAME": 101,
		"CONFLICT_SSN_EXISTS":                   102,
		"NOT_FOUND_NO_HIT":                      103,
	}
)

func (x AtlasFulfillOfferResponse_Status) Enum() *AtlasFulfillOfferResponse_Status {
	p := new(AtlasFulfillOfferResponse_Status)
	*p = x
	return p
}

func (x AtlasFulfillOfferResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AtlasFulfillOfferResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_credit_report_cibil_proto_enumTypes[0].Descriptor()
}

func (AtlasFulfillOfferResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_credit_report_cibil_proto_enumTypes[0]
}

func (x AtlasFulfillOfferResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AtlasFulfillOfferResponse_Status.Descriptor instead.
func (AtlasFulfillOfferResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{3, 0}
}

type AtlasPingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientKey  string                       `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	RequestKey string                       `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
}

func (x *AtlasPingRequest) Reset() {
	*x = AtlasPingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasPingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasPingRequest) ProtoMessage() {}

func (x *AtlasPingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasPingRequest.ProtoReflect.Descriptor instead.
func (*AtlasPingRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{0}
}

func (x *AtlasPingRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AtlasPingRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *AtlasPingRequest) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

type AtlasPingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ResponseKey string      `protobuf:"bytes,2,opt,name=response_key,json=responseKey,proto3" json:"response_key,omitempty"`
}

func (x *AtlasPingResponse) Reset() {
	*x = AtlasPingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasPingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasPingResponse) ProtoMessage() {}

func (x *AtlasPingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasPingResponse.ProtoReflect.Descriptor instead.
func (*AtlasPingResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{1}
}

func (x *AtlasPingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AtlasPingResponse) GetResponseKey() string {
	if x != nil {
		return x.ResponseKey
	}
	return ""
}

type AtlasFulfillOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader          `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientKey   string                                `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	RequestKey  string                                `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
	UserDetails *AtlasFulfillOfferRequest_UserDetails `protobuf:"bytes,4,opt,name=user_details,json=userDetails,proto3" json:"user_details,omitempty"`
}

func (x *AtlasFulfillOfferRequest) Reset() {
	*x = AtlasFulfillOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasFulfillOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasFulfillOfferRequest) ProtoMessage() {}

func (x *AtlasFulfillOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasFulfillOfferRequest.ProtoReflect.Descriptor instead.
func (*AtlasFulfillOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{2}
}

func (x *AtlasFulfillOfferRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AtlasFulfillOfferRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *AtlasFulfillOfferRequest) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

func (x *AtlasFulfillOfferRequest) GetUserDetails() *AtlasFulfillOfferRequest_UserDetails {
	if x != nil {
		return x.UserDetails
	}
	return nil
}

type AtlasFulfillOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ResponseKey        string              `protobuf:"bytes,2,opt,name=response_key,json=responseKey,proto3" json:"response_key,omitempty"`
	VendorStatus       AtlasResponseStatus `protobuf:"varint,3,opt,name=vendor_status,json=vendorStatus,proto3,enum=vendorgateway.credit_report.AtlasResponseStatus" json:"vendor_status,omitempty"`
	AtlasErrorResponse *AtlasErrorResponse `protobuf:"bytes,4,opt,name=atlas_error_response,json=atlasErrorResponse,proto3" json:"atlas_error_response,omitempty"`
}

func (x *AtlasFulfillOfferResponse) Reset() {
	*x = AtlasFulfillOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasFulfillOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasFulfillOfferResponse) ProtoMessage() {}

func (x *AtlasFulfillOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasFulfillOfferResponse.ProtoReflect.Descriptor instead.
func (*AtlasFulfillOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{3}
}

func (x *AtlasFulfillOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AtlasFulfillOfferResponse) GetResponseKey() string {
	if x != nil {
		return x.ResponseKey
	}
	return ""
}

func (x *AtlasFulfillOfferResponse) GetVendorStatus() AtlasResponseStatus {
	if x != nil {
		return x.VendorStatus
	}
	return AtlasResponseStatus_ATLAS_RESPONSE_STATUS_UNSPECIFIED
}

func (x *AtlasFulfillOfferResponse) GetAtlasErrorResponse() *AtlasErrorResponse {
	if x != nil {
		return x.AtlasErrorResponse
	}
	return nil
}

type AtlasGetAuthQuestionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientKey  string                       `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	RequestKey string                       `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
}

func (x *AtlasGetAuthQuestionsRequest) Reset() {
	*x = AtlasGetAuthQuestionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasGetAuthQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasGetAuthQuestionsRequest) ProtoMessage() {}

func (x *AtlasGetAuthQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasGetAuthQuestionsRequest.ProtoReflect.Descriptor instead.
func (*AtlasGetAuthQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{4}
}

func (x *AtlasGetAuthQuestionsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AtlasGetAuthQuestionsRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *AtlasGetAuthQuestionsRequest) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

type AtlasGetAuthQuestionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                   *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ResponseKey              string                 `protobuf:"bytes,2,opt,name=response_key,json=responseKey,proto3" json:"response_key,omitempty"`
	ChallengeConfigurationId string                 `protobuf:"bytes,3,opt,name=challenge_configuration_id,json=challengeConfigurationId,proto3" json:"challenge_configuration_id,omitempty"`
	AuthQuestion             *AtlasAuthQuestion     `protobuf:"bytes,4,opt,name=auth_question,json=authQuestion,proto3" json:"auth_question,omitempty"`
	QuestionType             AtlasAuthQueue         `protobuf:"varint,5,opt,name=question_type,json=questionType,proto3,enum=vendorgateway.credit_report.AtlasAuthQueue" json:"question_type,omitempty"`
	AtlasErrorResponse       *AtlasErrorResponse    `protobuf:"bytes,6,opt,name=atlas_error_response,json=atlasErrorResponse,proto3" json:"atlas_error_response,omitempty"`
	TimeOfResponse           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=time_of_response,json=timeOfResponse,proto3" json:"time_of_response,omitempty"`
}

func (x *AtlasGetAuthQuestionsResponse) Reset() {
	*x = AtlasGetAuthQuestionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasGetAuthQuestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasGetAuthQuestionsResponse) ProtoMessage() {}

func (x *AtlasGetAuthQuestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasGetAuthQuestionsResponse.ProtoReflect.Descriptor instead.
func (*AtlasGetAuthQuestionsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{5}
}

func (x *AtlasGetAuthQuestionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AtlasGetAuthQuestionsResponse) GetResponseKey() string {
	if x != nil {
		return x.ResponseKey
	}
	return ""
}

func (x *AtlasGetAuthQuestionsResponse) GetChallengeConfigurationId() string {
	if x != nil {
		return x.ChallengeConfigurationId
	}
	return ""
}

func (x *AtlasGetAuthQuestionsResponse) GetAuthQuestion() *AtlasAuthQuestion {
	if x != nil {
		return x.AuthQuestion
	}
	return nil
}

func (x *AtlasGetAuthQuestionsResponse) GetQuestionType() AtlasAuthQueue {
	if x != nil {
		return x.QuestionType
	}
	return AtlasAuthQueue_ATLAS_AUTH_QUEUE_UNSPECIFIED
}

func (x *AtlasGetAuthQuestionsResponse) GetAtlasErrorResponse() *AtlasErrorResponse {
	if x != nil {
		return x.AtlasErrorResponse
	}
	return nil
}

func (x *AtlasGetAuthQuestionsResponse) GetTimeOfResponse() *timestamppb.Timestamp {
	if x != nil {
		return x.TimeOfResponse
	}
	return nil
}

type AtlasAuthQuestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnswerChoice       *AnswerChoice `protobuf:"bytes,1,opt,name=answer_choice,json=answerChoice,proto3" json:"answer_choice,omitempty"`
	ResendEligible     bool          `protobuf:"varint,2,opt,name=resend_eligible,json=resendEligible,proto3" json:"resend_eligible,omitempty"`
	LastChanceQuestion bool          `protobuf:"varint,3,opt,name=last_chance_question,json=lastChanceQuestion,proto3" json:"last_chance_question,omitempty"`
	SkipEligible       bool          `protobuf:"varint,4,opt,name=skip_eligible,json=skipEligible,proto3" json:"skip_eligible,omitempty"`
	FullQuestionText   string        `protobuf:"bytes,5,opt,name=full_question_text,json=fullQuestionText,proto3" json:"full_question_text,omitempty"`
	Key                string        `protobuf:"bytes,6,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *AtlasAuthQuestion) Reset() {
	*x = AtlasAuthQuestion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasAuthQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasAuthQuestion) ProtoMessage() {}

func (x *AtlasAuthQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasAuthQuestion.ProtoReflect.Descriptor instead.
func (*AtlasAuthQuestion) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{6}
}

func (x *AtlasAuthQuestion) GetAnswerChoice() *AnswerChoice {
	if x != nil {
		return x.AnswerChoice
	}
	return nil
}

func (x *AtlasAuthQuestion) GetResendEligible() bool {
	if x != nil {
		return x.ResendEligible
	}
	return false
}

func (x *AtlasAuthQuestion) GetLastChanceQuestion() bool {
	if x != nil {
		return x.LastChanceQuestion
	}
	return false
}

func (x *AtlasAuthQuestion) GetSkipEligible() bool {
	if x != nil {
		return x.SkipEligible
	}
	return false
}

func (x *AtlasAuthQuestion) GetFullQuestionText() string {
	if x != nil {
		return x.FullQuestionText
	}
	return ""
}

func (x *AtlasAuthQuestion) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type AnswerChoice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayText string `protobuf:"bytes,1,opt,name=display_text,json=displayText,proto3" json:"display_text,omitempty"`
	Key         string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	ChoiceId    string `protobuf:"bytes,3,opt,name=choice_id,json=choiceId,proto3" json:"choice_id,omitempty"`
}

func (x *AnswerChoice) Reset() {
	*x = AnswerChoice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnswerChoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnswerChoice) ProtoMessage() {}

func (x *AnswerChoice) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnswerChoice.ProtoReflect.Descriptor instead.
func (*AnswerChoice) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{7}
}

func (x *AnswerChoice) GetDisplayText() string {
	if x != nil {
		return x.DisplayText
	}
	return ""
}

func (x *AnswerChoice) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AnswerChoice) GetChoiceId() string {
	if x != nil {
		return x.ChoiceId
	}
	return ""
}

type AtlasVerifyAuthAnswersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                   *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientKey                string                       `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	RequestKey               string                       `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
	ChallengeConfigurationId string                       `protobuf:"bytes,4,opt,name=challenge_configuration_id,json=challengeConfigurationId,proto3" json:"challenge_configuration_id,omitempty"`
	QuestionKey              string                       `protobuf:"bytes,5,opt,name=question_key,json=questionKey,proto3" json:"question_key,omitempty"`
	AnswerKey                string                       `protobuf:"bytes,6,opt,name=answer_key,json=answerKey,proto3" json:"answer_key,omitempty"`
	// Types that are assignable to AnswerSpecificInput:
	//	*AtlasVerifyAuthAnswersRequest_ResendOtp
	//	*AtlasVerifyAuthAnswersRequest_SkipQuestion
	//	*AtlasVerifyAuthAnswersRequest_UserInput
	AnswerSpecificInput isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput `protobuf_oneof:"answer_specific_input"`
}

func (x *AtlasVerifyAuthAnswersRequest) Reset() {
	*x = AtlasVerifyAuthAnswersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasVerifyAuthAnswersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasVerifyAuthAnswersRequest) ProtoMessage() {}

func (x *AtlasVerifyAuthAnswersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasVerifyAuthAnswersRequest.ProtoReflect.Descriptor instead.
func (*AtlasVerifyAuthAnswersRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{8}
}

func (x *AtlasVerifyAuthAnswersRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AtlasVerifyAuthAnswersRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *AtlasVerifyAuthAnswersRequest) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

func (x *AtlasVerifyAuthAnswersRequest) GetChallengeConfigurationId() string {
	if x != nil {
		return x.ChallengeConfigurationId
	}
	return ""
}

func (x *AtlasVerifyAuthAnswersRequest) GetQuestionKey() string {
	if x != nil {
		return x.QuestionKey
	}
	return ""
}

func (x *AtlasVerifyAuthAnswersRequest) GetAnswerKey() string {
	if x != nil {
		return x.AnswerKey
	}
	return ""
}

func (m *AtlasVerifyAuthAnswersRequest) GetAnswerSpecificInput() isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput {
	if m != nil {
		return m.AnswerSpecificInput
	}
	return nil
}

func (x *AtlasVerifyAuthAnswersRequest) GetResendOtp() bool {
	if x, ok := x.GetAnswerSpecificInput().(*AtlasVerifyAuthAnswersRequest_ResendOtp); ok {
		return x.ResendOtp
	}
	return false
}

func (x *AtlasVerifyAuthAnswersRequest) GetSkipQuestion() bool {
	if x, ok := x.GetAnswerSpecificInput().(*AtlasVerifyAuthAnswersRequest_SkipQuestion); ok {
		return x.SkipQuestion
	}
	return false
}

func (x *AtlasVerifyAuthAnswersRequest) GetUserInput() string {
	if x, ok := x.GetAnswerSpecificInput().(*AtlasVerifyAuthAnswersRequest_UserInput); ok {
		return x.UserInput
	}
	return ""
}

type isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput interface {
	isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput()
}

type AtlasVerifyAuthAnswersRequest_ResendOtp struct {
	ResendOtp bool `protobuf:"varint,7,opt,name=resend_otp,json=resendOtp,proto3,oneof"`
}

type AtlasVerifyAuthAnswersRequest_SkipQuestion struct {
	SkipQuestion bool `protobuf:"varint,8,opt,name=skip_question,json=skipQuestion,proto3,oneof"`
}

type AtlasVerifyAuthAnswersRequest_UserInput struct {
	UserInput string `protobuf:"bytes,9,opt,name=user_input,json=userInput,proto3,oneof"`
}

func (*AtlasVerifyAuthAnswersRequest_ResendOtp) isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput() {
}

func (*AtlasVerifyAuthAnswersRequest_SkipQuestion) isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput() {
}

func (*AtlasVerifyAuthAnswersRequest_UserInput) isAtlasVerifyAuthAnswersRequest_AnswerSpecificInput() {
}

type AtlasVerifyAuthAnswersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ResponseKey          string                 `protobuf:"bytes,2,opt,name=response_key,json=responseKey,proto3" json:"response_key,omitempty"`
	VendorResponseStatus AtlasResponseStatus    `protobuf:"varint,3,opt,name=vendor_response_status,json=vendorResponseStatus,proto3,enum=vendorgateway.credit_report.AtlasResponseStatus" json:"vendor_response_status,omitempty"`
	AtlasErrorResponse   *AtlasErrorResponse    `protobuf:"bytes,4,opt,name=atlas_error_response,json=atlasErrorResponse,proto3" json:"atlas_error_response,omitempty"`
	TimeOfResponse       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=time_of_response,json=timeOfResponse,proto3" json:"time_of_response,omitempty"`
}

func (x *AtlasVerifyAuthAnswersResponse) Reset() {
	*x = AtlasVerifyAuthAnswersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasVerifyAuthAnswersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasVerifyAuthAnswersResponse) ProtoMessage() {}

func (x *AtlasVerifyAuthAnswersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasVerifyAuthAnswersResponse.ProtoReflect.Descriptor instead.
func (*AtlasVerifyAuthAnswersResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{9}
}

func (x *AtlasVerifyAuthAnswersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AtlasVerifyAuthAnswersResponse) GetResponseKey() string {
	if x != nil {
		return x.ResponseKey
	}
	return ""
}

func (x *AtlasVerifyAuthAnswersResponse) GetVendorResponseStatus() AtlasResponseStatus {
	if x != nil {
		return x.VendorResponseStatus
	}
	return AtlasResponseStatus_ATLAS_RESPONSE_STATUS_UNSPECIFIED
}

func (x *AtlasVerifyAuthAnswersResponse) GetAtlasErrorResponse() *AtlasErrorResponse {
	if x != nil {
		return x.AtlasErrorResponse
	}
	return nil
}

func (x *AtlasVerifyAuthAnswersResponse) GetTimeOfResponse() *timestamppb.Timestamp {
	if x != nil {
		return x.TimeOfResponse
	}
	return nil
}

type AtlasGetCustomerAssetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientKey  string                       `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	RequestKey string                       `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
}

func (x *AtlasGetCustomerAssetsRequest) Reset() {
	*x = AtlasGetCustomerAssetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasGetCustomerAssetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasGetCustomerAssetsRequest) ProtoMessage() {}

func (x *AtlasGetCustomerAssetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasGetCustomerAssetsRequest.ProtoReflect.Descriptor instead.
func (*AtlasGetCustomerAssetsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{10}
}

func (x *AtlasGetCustomerAssetsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AtlasGetCustomerAssetsRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *AtlasGetCustomerAssetsRequest) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

type AtlasGetCustomerAssetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ResponseKey        string              `protobuf:"bytes,2,opt,name=response_key,json=responseKey,proto3" json:"response_key,omitempty"`
	RawReport          []byte              `protobuf:"bytes,3,opt,name=raw_report,json=rawReport,proto3" json:"raw_report,omitempty"`
	AtlasErrorResponse *AtlasErrorResponse `protobuf:"bytes,4,opt,name=atlas_error_response,json=atlasErrorResponse,proto3" json:"atlas_error_response,omitempty"`
}

func (x *AtlasGetCustomerAssetsResponse) Reset() {
	*x = AtlasGetCustomerAssetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasGetCustomerAssetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasGetCustomerAssetsResponse) ProtoMessage() {}

func (x *AtlasGetCustomerAssetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasGetCustomerAssetsResponse.ProtoReflect.Descriptor instead.
func (*AtlasGetCustomerAssetsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{11}
}

func (x *AtlasGetCustomerAssetsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AtlasGetCustomerAssetsResponse) GetResponseKey() string {
	if x != nil {
		return x.ResponseKey
	}
	return ""
}

func (x *AtlasGetCustomerAssetsResponse) GetRawReport() []byte {
	if x != nil {
		return x.RawReport
	}
	return nil
}

func (x *AtlasGetCustomerAssetsResponse) GetAtlasErrorResponse() *AtlasErrorResponse {
	if x != nil {
		return x.AtlasErrorResponse
	}
	return nil
}

type AtlasGetProductWebUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientKey  string                       `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	RequestKey string                       `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
}

func (x *AtlasGetProductWebUrlRequest) Reset() {
	*x = AtlasGetProductWebUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasGetProductWebUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasGetProductWebUrlRequest) ProtoMessage() {}

func (x *AtlasGetProductWebUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasGetProductWebUrlRequest.ProtoReflect.Descriptor instead.
func (*AtlasGetProductWebUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{12}
}

func (x *AtlasGetProductWebUrlRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AtlasGetProductWebUrlRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *AtlasGetProductWebUrlRequest) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

type AtlasGetProductWebUrlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ResponseKey        string              `protobuf:"bytes,2,opt,name=response_key,json=responseKey,proto3" json:"response_key,omitempty"`
	WebUrl             string              `protobuf:"bytes,3,opt,name=web_url,json=webUrl,proto3" json:"web_url,omitempty"`
	AtlasErrorResponse *AtlasErrorResponse `protobuf:"bytes,4,opt,name=atlas_error_response,json=atlasErrorResponse,proto3" json:"atlas_error_response,omitempty"`
}

func (x *AtlasGetProductWebUrlResponse) Reset() {
	*x = AtlasGetProductWebUrlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasGetProductWebUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasGetProductWebUrlResponse) ProtoMessage() {}

func (x *AtlasGetProductWebUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasGetProductWebUrlResponse.ProtoReflect.Descriptor instead.
func (*AtlasGetProductWebUrlResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{13}
}

func (x *AtlasGetProductWebUrlResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AtlasGetProductWebUrlResponse) GetResponseKey() string {
	if x != nil {
		return x.ResponseKey
	}
	return ""
}

func (x *AtlasGetProductWebUrlResponse) GetWebUrl() string {
	if x != nil {
		return x.WebUrl
	}
	return ""
}

func (x *AtlasGetProductWebUrlResponse) GetAtlasErrorResponse() *AtlasErrorResponse {
	if x != nil {
		return x.AtlasErrorResponse
	}
	return nil
}

type AtlasErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Response:
	//	*AtlasErrorResponse_AtlasFailureResponse
	//	*AtlasErrorResponse_AtlasServiceErrorResponse
	Response isAtlasErrorResponse_Response `protobuf_oneof:"Response"`
}

func (x *AtlasErrorResponse) Reset() {
	*x = AtlasErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasErrorResponse) ProtoMessage() {}

func (x *AtlasErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasErrorResponse.ProtoReflect.Descriptor instead.
func (*AtlasErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{14}
}

func (m *AtlasErrorResponse) GetResponse() isAtlasErrorResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *AtlasErrorResponse) GetAtlasFailureResponse() *AtlasFailureResponse {
	if x, ok := x.GetResponse().(*AtlasErrorResponse_AtlasFailureResponse); ok {
		return x.AtlasFailureResponse
	}
	return nil
}

func (x *AtlasErrorResponse) GetAtlasServiceErrorResponse() *AtlasServiceErrorResponse {
	if x, ok := x.GetResponse().(*AtlasErrorResponse_AtlasServiceErrorResponse); ok {
		return x.AtlasServiceErrorResponse
	}
	return nil
}

type isAtlasErrorResponse_Response interface {
	isAtlasErrorResponse_Response()
}

type AtlasErrorResponse_AtlasFailureResponse struct {
	AtlasFailureResponse *AtlasFailureResponse `protobuf:"bytes,1,opt,name=atlas_failure_response,json=atlasFailureResponse,proto3,oneof"`
}

type AtlasErrorResponse_AtlasServiceErrorResponse struct {
	AtlasServiceErrorResponse *AtlasServiceErrorResponse `protobuf:"bytes,2,opt,name=atlas_service_error_response,json=atlasServiceErrorResponse,proto3,oneof"`
}

func (*AtlasErrorResponse_AtlasFailureResponse) isAtlasErrorResponse_Response() {}

func (*AtlasErrorResponse_AtlasServiceErrorResponse) isAtlasErrorResponse_Response() {}

type AtlasFailureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IeTransactionId    string           `protobuf:"bytes,1,opt,name=ie_transaction_id,json=ieTransactionId,proto3" json:"ie_transaction_id,omitempty"`
	Message            string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	FailureEnum        AtlasFailureEnum `protobuf:"varint,3,opt,name=failure_enum,json=failureEnum,proto3,enum=vendorgateway.credit_report.AtlasFailureEnum" json:"failure_enum,omitempty"`
	ServiceUserId      string           `protobuf:"bytes,4,opt,name=service_user_id,json=serviceUserId,proto3" json:"service_user_id,omitempty"`
	BtxRefKey          string           `protobuf:"bytes,5,opt,name=btx_ref_key,json=btxRefKey,proto3" json:"btx_ref_key,omitempty"`
	TimePeriod         string           `protobuf:"bytes,6,opt,name=time_period,json=timePeriod,proto3" json:"time_period,omitempty"`
	SecondFailureSchId string           `protobuf:"bytes,7,opt,name=second_failure_sch_id,json=secondFailureSchId,proto3" json:"second_failure_sch_id,omitempty"`
	Severity           string           `protobuf:"bytes,8,opt,name=severity,proto3" json:"severity,omitempty"`
	FailureSchId       string           `protobuf:"bytes,9,opt,name=failure_sch_id,json=failureSchId,proto3" json:"failure_sch_id,omitempty"`
	CustomerId         string           `protobuf:"bytes,10,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ClientUserKey      string           `protobuf:"bytes,11,opt,name=client_user_key,json=clientUserKey,proto3" json:"client_user_key,omitempty"`
	ClientRefreshUrl   string           `protobuf:"bytes,12,opt,name=client_refresh_url,json=clientRefreshUrl,proto3" json:"client_refresh_url,omitempty"`
}

func (x *AtlasFailureResponse) Reset() {
	*x = AtlasFailureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasFailureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasFailureResponse) ProtoMessage() {}

func (x *AtlasFailureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasFailureResponse.ProtoReflect.Descriptor instead.
func (*AtlasFailureResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{15}
}

func (x *AtlasFailureResponse) GetIeTransactionId() string {
	if x != nil {
		return x.IeTransactionId
	}
	return ""
}

func (x *AtlasFailureResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AtlasFailureResponse) GetFailureEnum() AtlasFailureEnum {
	if x != nil {
		return x.FailureEnum
	}
	return AtlasFailureEnum_ATLAS_FAILURE_ENUM_UNSPECIFIED
}

func (x *AtlasFailureResponse) GetServiceUserId() string {
	if x != nil {
		return x.ServiceUserId
	}
	return ""
}

func (x *AtlasFailureResponse) GetBtxRefKey() string {
	if x != nil {
		return x.BtxRefKey
	}
	return ""
}

func (x *AtlasFailureResponse) GetTimePeriod() string {
	if x != nil {
		return x.TimePeriod
	}
	return ""
}

func (x *AtlasFailureResponse) GetSecondFailureSchId() string {
	if x != nil {
		return x.SecondFailureSchId
	}
	return ""
}

func (x *AtlasFailureResponse) GetSeverity() string {
	if x != nil {
		return x.Severity
	}
	return ""
}

func (x *AtlasFailureResponse) GetFailureSchId() string {
	if x != nil {
		return x.FailureSchId
	}
	return ""
}

func (x *AtlasFailureResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *AtlasFailureResponse) GetClientUserKey() string {
	if x != nil {
		return x.ClientUserKey
	}
	return ""
}

func (x *AtlasFailureResponse) GetClientRefreshUrl() string {
	if x != nil {
		return x.ClientRefreshUrl
	}
	return ""
}

type AtlasServiceErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorDetail          string           `protobuf:"bytes,1,opt,name=error_detail,json=errorDetail,proto3" json:"error_detail,omitempty"`
	ErrorStatus          AtlasErrorStatus `protobuf:"varint,2,opt,name=error_status,json=errorStatus,proto3,enum=vendorgateway.credit_report.AtlasErrorStatus" json:"error_status,omitempty"`
	ErrorMessage         string           `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	Failure              string           `protobuf:"bytes,4,opt,name=failure,proto3" json:"failure,omitempty"`
	ApplicationName      string           `protobuf:"bytes,5,opt,name=application_name,json=applicationName,proto3" json:"application_name,omitempty"`
	MethodName           string           `protobuf:"bytes,6,opt,name=method_name,json=methodName,proto3" json:"method_name,omitempty"`
	MethodArgument       string           `protobuf:"bytes,7,opt,name=method_argument,json=methodArgument,proto3" json:"method_argument,omitempty"`
	MethodResponse       string           `protobuf:"bytes,8,opt,name=method_response,json=methodResponse,proto3" json:"method_response,omitempty"`
	PrimaryEntityId      string           `protobuf:"bytes,9,opt,name=primary_entity_id,json=primaryEntityId,proto3" json:"primary_entity_id,omitempty"`
	PrimaryEntityName    string           `protobuf:"bytes,10,opt,name=primary_entity_name,json=primaryEntityName,proto3" json:"primary_entity_name,omitempty"`
	SecondaryEntityId    string           `protobuf:"bytes,11,opt,name=secondary_entity_id,json=secondaryEntityId,proto3" json:"secondary_entity_id,omitempty"`
	SecondaryEntitiyName string           `protobuf:"bytes,12,opt,name=secondary_entitiy_name,json=secondaryEntitiyName,proto3" json:"secondary_entitiy_name,omitempty"`
}

func (x *AtlasServiceErrorResponse) Reset() {
	*x = AtlasServiceErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasServiceErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasServiceErrorResponse) ProtoMessage() {}

func (x *AtlasServiceErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasServiceErrorResponse.ProtoReflect.Descriptor instead.
func (*AtlasServiceErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{16}
}

func (x *AtlasServiceErrorResponse) GetErrorDetail() string {
	if x != nil {
		return x.ErrorDetail
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetErrorStatus() AtlasErrorStatus {
	if x != nil {
		return x.ErrorStatus
	}
	return AtlasErrorStatus_ATLAS_ERROR_STATUS_UNSPECIFIED
}

func (x *AtlasServiceErrorResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetFailure() string {
	if x != nil {
		return x.Failure
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetApplicationName() string {
	if x != nil {
		return x.ApplicationName
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetMethodArgument() string {
	if x != nil {
		return x.MethodArgument
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetMethodResponse() string {
	if x != nil {
		return x.MethodResponse
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetPrimaryEntityId() string {
	if x != nil {
		return x.PrimaryEntityId
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetPrimaryEntityName() string {
	if x != nil {
		return x.PrimaryEntityName
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetSecondaryEntityId() string {
	if x != nil {
		return x.SecondaryEntityId
	}
	return ""
}

func (x *AtlasServiceErrorResponse) GetSecondaryEntitiyName() string {
	if x != nil {
		return x.SecondaryEntitiyName
	}
	return ""
}

type AtlasFulfillOfferRequest_UserDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        *common.Name        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email       string              `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Pan         string              `protobuf:"bytes,4,opt,name=pan,proto3" json:"pan,omitempty"`
	DateOfBirth *date.Date          `protobuf:"bytes,5,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
}

func (x *AtlasFulfillOfferRequest_UserDetails) Reset() {
	*x = AtlasFulfillOfferRequest_UserDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtlasFulfillOfferRequest_UserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtlasFulfillOfferRequest_UserDetails) ProtoMessage() {}

func (x *AtlasFulfillOfferRequest_UserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_credit_report_cibil_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtlasFulfillOfferRequest_UserDetails.ProtoReflect.Descriptor instead.
func (*AtlasFulfillOfferRequest_UserDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AtlasFulfillOfferRequest_UserDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *AtlasFulfillOfferRequest_UserDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AtlasFulfillOfferRequest_UserDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *AtlasFulfillOfferRequest_UserDetails) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *AtlasFulfillOfferRequest_UserDetails) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

var File_api_vendorgateway_credit_report_cibil_proto protoreflect.FileDescriptor

var file_api_vendorgateway_credit_report_cibil_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x2f, 0x63, 0x69, 0x62, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x50, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x22,
	0x5b, 0x0a, 0x11, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x22, 0xd7, 0x03, 0x0a,
	0x18, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x12,
	0x64, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xde, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12,
	0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x22, 0x89, 0x03, 0x0a, 0x19, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x55, 0x0a, 0x0d,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x14, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x41, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x10, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x5f,
	0x53, 0x53, 0x4e, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x66, 0x12, 0x14, 0x0a, 0x10,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x4f, 0x5f, 0x48, 0x49, 0x54,
	0x10, 0x67, 0x22, 0x94, 0x01, 0x0a, 0x1c, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x22, 0xf5, 0x03, 0x0a, 0x1d, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x4b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x1a, 0x63, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x53, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x41, 0x75, 0x74, 0x68,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61,
	0x73, 0x41, 0x75, 0x74, 0x68, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x14, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xa3, 0x02, 0x0a, 0x11, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x41, 0x75, 0x74, 0x68, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0d, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x0c, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x12, 0x30, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x6c, 0x61, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x6b, 0x69, 0x70, 0x45,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x75, 0x6c, 0x6c, 0x5f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x75, 0x6c, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x60, 0x0a, 0x0c, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x97, 0x03, 0x0a, 0x1d, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x75, 0x74, 0x68, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65,
	0x79, 0x12, 0x3c, 0x0a, 0x1a, 0x63, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4b, 0x65,
	0x79, 0x12, 0x1f, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x4f,
	0x74, 0x70, 0x12, 0x25, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x6b, 0x69,
	0x70, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x17, 0x0a, 0x15, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x22, 0xf9, 0x02, 0x0a, 0x1e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x41, 0x75, 0x74, 0x68, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x66,
	0x0a, 0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c,
	0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x14, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0e, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x95, 0x01, 0x0a, 0x1d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x22, 0xea, 0x01, 0x0a, 0x1e, 0x41, 0x74, 0x6c, 0x61,
	0x73, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x72, 0x61, 0x77, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x61, 0x0a, 0x14, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x1c, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x57, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x22, 0xe3, 0x01, 0x0a, 0x1d,
	0x41, 0x74, 0x6c, 0x61, 0x73, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x57,
	0x65, 0x62, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x61,
	0x0a, 0x14, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x86, 0x02, 0x0a, 0x12, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x16, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x14, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x1c, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x00, 0x52, 0x19, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0a,
	0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x83, 0x04, 0x0a, 0x14, 0x41,
	0x74, 0x6c, 0x61, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x69, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x74, 0x78, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x74, 0x78, 0x52, 0x65, 0x66,
	0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x53, 0x63, 0x68, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x53, 0x63, 0x68, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4b,
	0x65, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x55, 0x72, 0x6c,
	0x22, 0xaf, 0x04, 0x0a, 0x19, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x50, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x41,
	0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x16,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5a, 0x36, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_credit_report_cibil_proto_rawDescOnce sync.Once
	file_api_vendorgateway_credit_report_cibil_proto_rawDescData = file_api_vendorgateway_credit_report_cibil_proto_rawDesc
)

func file_api_vendorgateway_credit_report_cibil_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_credit_report_cibil_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_credit_report_cibil_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_credit_report_cibil_proto_rawDescData)
	})
	return file_api_vendorgateway_credit_report_cibil_proto_rawDescData
}

var file_api_vendorgateway_credit_report_cibil_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_vendorgateway_credit_report_cibil_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_vendorgateway_credit_report_cibil_proto_goTypes = []interface{}{
	(AtlasFulfillOfferResponse_Status)(0),        // 0: vendorgateway.credit_report.AtlasFulfillOfferResponse.Status
	(*AtlasPingRequest)(nil),                     // 1: vendorgateway.credit_report.AtlasPingRequest
	(*AtlasPingResponse)(nil),                    // 2: vendorgateway.credit_report.AtlasPingResponse
	(*AtlasFulfillOfferRequest)(nil),             // 3: vendorgateway.credit_report.AtlasFulfillOfferRequest
	(*AtlasFulfillOfferResponse)(nil),            // 4: vendorgateway.credit_report.AtlasFulfillOfferResponse
	(*AtlasGetAuthQuestionsRequest)(nil),         // 5: vendorgateway.credit_report.AtlasGetAuthQuestionsRequest
	(*AtlasGetAuthQuestionsResponse)(nil),        // 6: vendorgateway.credit_report.AtlasGetAuthQuestionsResponse
	(*AtlasAuthQuestion)(nil),                    // 7: vendorgateway.credit_report.AtlasAuthQuestion
	(*AnswerChoice)(nil),                         // 8: vendorgateway.credit_report.AnswerChoice
	(*AtlasVerifyAuthAnswersRequest)(nil),        // 9: vendorgateway.credit_report.AtlasVerifyAuthAnswersRequest
	(*AtlasVerifyAuthAnswersResponse)(nil),       // 10: vendorgateway.credit_report.AtlasVerifyAuthAnswersResponse
	(*AtlasGetCustomerAssetsRequest)(nil),        // 11: vendorgateway.credit_report.AtlasGetCustomerAssetsRequest
	(*AtlasGetCustomerAssetsResponse)(nil),       // 12: vendorgateway.credit_report.AtlasGetCustomerAssetsResponse
	(*AtlasGetProductWebUrlRequest)(nil),         // 13: vendorgateway.credit_report.AtlasGetProductWebUrlRequest
	(*AtlasGetProductWebUrlResponse)(nil),        // 14: vendorgateway.credit_report.AtlasGetProductWebUrlResponse
	(*AtlasErrorResponse)(nil),                   // 15: vendorgateway.credit_report.AtlasErrorResponse
	(*AtlasFailureResponse)(nil),                 // 16: vendorgateway.credit_report.AtlasFailureResponse
	(*AtlasServiceErrorResponse)(nil),            // 17: vendorgateway.credit_report.AtlasServiceErrorResponse
	(*AtlasFulfillOfferRequest_UserDetails)(nil), // 18: vendorgateway.credit_report.AtlasFulfillOfferRequest.UserDetails
	(*vendorgateway.RequestHeader)(nil),          // 19: vendorgateway.RequestHeader
	(*rpc.Status)(nil),                           // 20: rpc.Status
	(AtlasResponseStatus)(0),                     // 21: vendorgateway.credit_report.AtlasResponseStatus
	(AtlasAuthQueue)(0),                          // 22: vendorgateway.credit_report.AtlasAuthQueue
	(*timestamppb.Timestamp)(nil),                // 23: google.protobuf.Timestamp
	(AtlasFailureEnum)(0),                        // 24: vendorgateway.credit_report.AtlasFailureEnum
	(AtlasErrorStatus)(0),                        // 25: vendorgateway.credit_report.AtlasErrorStatus
	(*common.Name)(nil),                          // 26: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                   // 27: api.typesv2.common.PhoneNumber
	(*date.Date)(nil),                            // 28: google.type.Date
}
var file_api_vendorgateway_credit_report_cibil_proto_depIdxs = []int32{
	19, // 0: vendorgateway.credit_report.AtlasPingRequest.header:type_name -> vendorgateway.RequestHeader
	20, // 1: vendorgateway.credit_report.AtlasPingResponse.status:type_name -> rpc.Status
	19, // 2: vendorgateway.credit_report.AtlasFulfillOfferRequest.header:type_name -> vendorgateway.RequestHeader
	18, // 3: vendorgateway.credit_report.AtlasFulfillOfferRequest.user_details:type_name -> vendorgateway.credit_report.AtlasFulfillOfferRequest.UserDetails
	20, // 4: vendorgateway.credit_report.AtlasFulfillOfferResponse.status:type_name -> rpc.Status
	21, // 5: vendorgateway.credit_report.AtlasFulfillOfferResponse.vendor_status:type_name -> vendorgateway.credit_report.AtlasResponseStatus
	15, // 6: vendorgateway.credit_report.AtlasFulfillOfferResponse.atlas_error_response:type_name -> vendorgateway.credit_report.AtlasErrorResponse
	19, // 7: vendorgateway.credit_report.AtlasGetAuthQuestionsRequest.header:type_name -> vendorgateway.RequestHeader
	20, // 8: vendorgateway.credit_report.AtlasGetAuthQuestionsResponse.status:type_name -> rpc.Status
	7,  // 9: vendorgateway.credit_report.AtlasGetAuthQuestionsResponse.auth_question:type_name -> vendorgateway.credit_report.AtlasAuthQuestion
	22, // 10: vendorgateway.credit_report.AtlasGetAuthQuestionsResponse.question_type:type_name -> vendorgateway.credit_report.AtlasAuthQueue
	15, // 11: vendorgateway.credit_report.AtlasGetAuthQuestionsResponse.atlas_error_response:type_name -> vendorgateway.credit_report.AtlasErrorResponse
	23, // 12: vendorgateway.credit_report.AtlasGetAuthQuestionsResponse.time_of_response:type_name -> google.protobuf.Timestamp
	8,  // 13: vendorgateway.credit_report.AtlasAuthQuestion.answer_choice:type_name -> vendorgateway.credit_report.AnswerChoice
	19, // 14: vendorgateway.credit_report.AtlasVerifyAuthAnswersRequest.header:type_name -> vendorgateway.RequestHeader
	20, // 15: vendorgateway.credit_report.AtlasVerifyAuthAnswersResponse.status:type_name -> rpc.Status
	21, // 16: vendorgateway.credit_report.AtlasVerifyAuthAnswersResponse.vendor_response_status:type_name -> vendorgateway.credit_report.AtlasResponseStatus
	15, // 17: vendorgateway.credit_report.AtlasVerifyAuthAnswersResponse.atlas_error_response:type_name -> vendorgateway.credit_report.AtlasErrorResponse
	23, // 18: vendorgateway.credit_report.AtlasVerifyAuthAnswersResponse.time_of_response:type_name -> google.protobuf.Timestamp
	19, // 19: vendorgateway.credit_report.AtlasGetCustomerAssetsRequest.header:type_name -> vendorgateway.RequestHeader
	20, // 20: vendorgateway.credit_report.AtlasGetCustomerAssetsResponse.status:type_name -> rpc.Status
	15, // 21: vendorgateway.credit_report.AtlasGetCustomerAssetsResponse.atlas_error_response:type_name -> vendorgateway.credit_report.AtlasErrorResponse
	19, // 22: vendorgateway.credit_report.AtlasGetProductWebUrlRequest.header:type_name -> vendorgateway.RequestHeader
	20, // 23: vendorgateway.credit_report.AtlasGetProductWebUrlResponse.status:type_name -> rpc.Status
	15, // 24: vendorgateway.credit_report.AtlasGetProductWebUrlResponse.atlas_error_response:type_name -> vendorgateway.credit_report.AtlasErrorResponse
	16, // 25: vendorgateway.credit_report.AtlasErrorResponse.atlas_failure_response:type_name -> vendorgateway.credit_report.AtlasFailureResponse
	17, // 26: vendorgateway.credit_report.AtlasErrorResponse.atlas_service_error_response:type_name -> vendorgateway.credit_report.AtlasServiceErrorResponse
	24, // 27: vendorgateway.credit_report.AtlasFailureResponse.failure_enum:type_name -> vendorgateway.credit_report.AtlasFailureEnum
	25, // 28: vendorgateway.credit_report.AtlasServiceErrorResponse.error_status:type_name -> vendorgateway.credit_report.AtlasErrorStatus
	26, // 29: vendorgateway.credit_report.AtlasFulfillOfferRequest.UserDetails.name:type_name -> api.typesv2.common.Name
	27, // 30: vendorgateway.credit_report.AtlasFulfillOfferRequest.UserDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	28, // 31: vendorgateway.credit_report.AtlasFulfillOfferRequest.UserDetails.date_of_birth:type_name -> google.type.Date
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_credit_report_cibil_proto_init() }
func file_api_vendorgateway_credit_report_cibil_proto_init() {
	if File_api_vendorgateway_credit_report_cibil_proto != nil {
		return
	}
	file_api_vendorgateway_credit_report_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasPingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasPingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasFulfillOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasFulfillOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasGetAuthQuestionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasGetAuthQuestionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasAuthQuestion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnswerChoice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasVerifyAuthAnswersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasVerifyAuthAnswersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasGetCustomerAssetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasGetCustomerAssetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasGetProductWebUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasGetProductWebUrlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasFailureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasServiceErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_credit_report_cibil_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtlasFulfillOfferRequest_UserDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_vendorgateway_credit_report_cibil_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*AtlasVerifyAuthAnswersRequest_ResendOtp)(nil),
		(*AtlasVerifyAuthAnswersRequest_SkipQuestion)(nil),
		(*AtlasVerifyAuthAnswersRequest_UserInput)(nil),
	}
	file_api_vendorgateway_credit_report_cibil_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*AtlasErrorResponse_AtlasFailureResponse)(nil),
		(*AtlasErrorResponse_AtlasServiceErrorResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_credit_report_cibil_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_credit_report_cibil_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_credit_report_cibil_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_credit_report_cibil_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_credit_report_cibil_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_credit_report_cibil_proto = out.File
	file_api_vendorgateway_credit_report_cibil_proto_rawDesc = nil
	file_api_vendorgateway_credit_report_cibil_proto_goTypes = nil
	file_api_vendorgateway_credit_report_cibil_proto_depIdxs = nil
}
