// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/cx/chatbot/nugget/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	nugget "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNuggetChatbotServiceClient is a mock of NuggetChatbotServiceClient interface.
type MockNuggetChatbotServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockNuggetChatbotServiceClientMockRecorder
}

// MockNuggetChatbotServiceClientMockRecorder is the mock recorder for MockNuggetChatbotServiceClient.
type MockNuggetChatbotServiceClientMockRecorder struct {
	mock *MockNuggetChatbotServiceClient
}

// NewMockNuggetChatbotServiceClient creates a new mock instance.
func NewMockNuggetChatbotServiceClient(ctrl *gomock.Controller) *MockNuggetChatbotServiceClient {
	mock := &MockNuggetChatbotServiceClient{ctrl: ctrl}
	mock.recorder = &MockNuggetChatbotServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNuggetChatbotServiceClient) EXPECT() *MockNuggetChatbotServiceClientMockRecorder {
	return m.recorder
}

// FetchAccessToken mocks base method.
func (m *MockNuggetChatbotServiceClient) FetchAccessToken(ctx context.Context, in *nugget.FetchAccessTokenRequest, opts ...grpc.CallOption) (*nugget.FetchAccessTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchAccessToken", varargs...)
	ret0, _ := ret[0].(*nugget.FetchAccessTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAccessToken indicates an expected call of FetchAccessToken.
func (mr *MockNuggetChatbotServiceClientMockRecorder) FetchAccessToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAccessToken", reflect.TypeOf((*MockNuggetChatbotServiceClient)(nil).FetchAccessToken), varargs...)
}

// MockNuggetChatbotServiceServer is a mock of NuggetChatbotServiceServer interface.
type MockNuggetChatbotServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockNuggetChatbotServiceServerMockRecorder
}

// MockNuggetChatbotServiceServerMockRecorder is the mock recorder for MockNuggetChatbotServiceServer.
type MockNuggetChatbotServiceServerMockRecorder struct {
	mock *MockNuggetChatbotServiceServer
}

// NewMockNuggetChatbotServiceServer creates a new mock instance.
func NewMockNuggetChatbotServiceServer(ctrl *gomock.Controller) *MockNuggetChatbotServiceServer {
	mock := &MockNuggetChatbotServiceServer{ctrl: ctrl}
	mock.recorder = &MockNuggetChatbotServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNuggetChatbotServiceServer) EXPECT() *MockNuggetChatbotServiceServerMockRecorder {
	return m.recorder
}

// FetchAccessToken mocks base method.
func (m *MockNuggetChatbotServiceServer) FetchAccessToken(arg0 context.Context, arg1 *nugget.FetchAccessTokenRequest) (*nugget.FetchAccessTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAccessToken", arg0, arg1)
	ret0, _ := ret[0].(*nugget.FetchAccessTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAccessToken indicates an expected call of FetchAccessToken.
func (mr *MockNuggetChatbotServiceServerMockRecorder) FetchAccessToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAccessToken", reflect.TypeOf((*MockNuggetChatbotServiceServer)(nil).FetchAccessToken), arg0, arg1)
}

// MockUnsafeNuggetChatbotServiceServer is a mock of UnsafeNuggetChatbotServiceServer interface.
type MockUnsafeNuggetChatbotServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNuggetChatbotServiceServerMockRecorder
}

// MockUnsafeNuggetChatbotServiceServerMockRecorder is the mock recorder for MockUnsafeNuggetChatbotServiceServer.
type MockUnsafeNuggetChatbotServiceServerMockRecorder struct {
	mock *MockUnsafeNuggetChatbotServiceServer
}

// NewMockUnsafeNuggetChatbotServiceServer creates a new mock instance.
func NewMockUnsafeNuggetChatbotServiceServer(ctrl *gomock.Controller) *MockUnsafeNuggetChatbotServiceServer {
	mock := &MockUnsafeNuggetChatbotServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNuggetChatbotServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNuggetChatbotServiceServer) EXPECT() *MockUnsafeNuggetChatbotServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNuggetChatbotServiceServer mocks base method.
func (m *MockUnsafeNuggetChatbotServiceServer) mustEmbedUnimplementedNuggetChatbotServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNuggetChatbotServiceServer")
}

// mustEmbedUnimplementedNuggetChatbotServiceServer indicates an expected call of mustEmbedUnimplementedNuggetChatbotServiceServer.
func (mr *MockUnsafeNuggetChatbotServiceServerMockRecorder) mustEmbedUnimplementedNuggetChatbotServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNuggetChatbotServiceServer", reflect.TypeOf((*MockUnsafeNuggetChatbotServiceServer)(nil).mustEmbedUnimplementedNuggetChatbotServiceServer))
}
