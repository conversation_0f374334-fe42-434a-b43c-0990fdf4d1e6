// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/creditcard/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	creditcard "github.com/epifi/gamma/api/vendorgateway/creditcard"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCreditCardClient is a mock of CreditCardClient interface.
type MockCreditCardClient struct {
	ctrl     *gomock.Controller
	recorder *MockCreditCardClientMockRecorder
}

// MockCreditCardClientMockRecorder is the mock recorder for MockCreditCardClient.
type MockCreditCardClientMockRecorder struct {
	mock *MockCreditCardClient
}

// NewMockCreditCardClient creates a new mock instance.
func NewMockCreditCardClient(ctrl *gomock.Controller) *MockCreditCardClient {
	mock := &MockCreditCardClient{ctrl: ctrl}
	mock.recorder = &MockCreditCardClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCreditCardClient) EXPECT() *MockCreditCardClientMockRecorder {
	return m.recorder
}

// GenerateCreditCardSdkAuthToken mocks base method.
func (m *MockCreditCardClient) GenerateCreditCardSdkAuthToken(ctx context.Context, in *creditcard.GenerateCreditCardSdkAuthTokenRequest, opts ...grpc.CallOption) (*creditcard.GenerateCreditCardSdkAuthTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateCreditCardSdkAuthToken", varargs...)
	ret0, _ := ret[0].(*creditcard.GenerateCreditCardSdkAuthTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCreditCardSdkAuthToken indicates an expected call of GenerateCreditCardSdkAuthToken.
func (mr *MockCreditCardClientMockRecorder) GenerateCreditCardSdkAuthToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCreditCardSdkAuthToken", reflect.TypeOf((*MockCreditCardClient)(nil).GenerateCreditCardSdkAuthToken), varargs...)
}

// UpdateCreditCardDeliveryState mocks base method.
func (m *MockCreditCardClient) UpdateCreditCardDeliveryState(ctx context.Context, in *creditcard.UpdateCreditCardDeliveryStateRequest, opts ...grpc.CallOption) (*creditcard.UpdateCreditCardDeliveryStateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCreditCardDeliveryState", varargs...)
	ret0, _ := ret[0].(*creditcard.UpdateCreditCardDeliveryStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCreditCardDeliveryState indicates an expected call of UpdateCreditCardDeliveryState.
func (mr *MockCreditCardClientMockRecorder) UpdateCreditCardDeliveryState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCreditCardDeliveryState", reflect.TypeOf((*MockCreditCardClient)(nil).UpdateCreditCardDeliveryState), varargs...)
}

// MockCreditCardServer is a mock of CreditCardServer interface.
type MockCreditCardServer struct {
	ctrl     *gomock.Controller
	recorder *MockCreditCardServerMockRecorder
}

// MockCreditCardServerMockRecorder is the mock recorder for MockCreditCardServer.
type MockCreditCardServerMockRecorder struct {
	mock *MockCreditCardServer
}

// NewMockCreditCardServer creates a new mock instance.
func NewMockCreditCardServer(ctrl *gomock.Controller) *MockCreditCardServer {
	mock := &MockCreditCardServer{ctrl: ctrl}
	mock.recorder = &MockCreditCardServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCreditCardServer) EXPECT() *MockCreditCardServerMockRecorder {
	return m.recorder
}

// GenerateCreditCardSdkAuthToken mocks base method.
func (m *MockCreditCardServer) GenerateCreditCardSdkAuthToken(arg0 context.Context, arg1 *creditcard.GenerateCreditCardSdkAuthTokenRequest) (*creditcard.GenerateCreditCardSdkAuthTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateCreditCardSdkAuthToken", arg0, arg1)
	ret0, _ := ret[0].(*creditcard.GenerateCreditCardSdkAuthTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCreditCardSdkAuthToken indicates an expected call of GenerateCreditCardSdkAuthToken.
func (mr *MockCreditCardServerMockRecorder) GenerateCreditCardSdkAuthToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCreditCardSdkAuthToken", reflect.TypeOf((*MockCreditCardServer)(nil).GenerateCreditCardSdkAuthToken), arg0, arg1)
}

// UpdateCreditCardDeliveryState mocks base method.
func (m *MockCreditCardServer) UpdateCreditCardDeliveryState(arg0 context.Context, arg1 *creditcard.UpdateCreditCardDeliveryStateRequest) (*creditcard.UpdateCreditCardDeliveryStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCreditCardDeliveryState", arg0, arg1)
	ret0, _ := ret[0].(*creditcard.UpdateCreditCardDeliveryStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCreditCardDeliveryState indicates an expected call of UpdateCreditCardDeliveryState.
func (mr *MockCreditCardServerMockRecorder) UpdateCreditCardDeliveryState(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCreditCardDeliveryState", reflect.TypeOf((*MockCreditCardServer)(nil).UpdateCreditCardDeliveryState), arg0, arg1)
}

// MockUnsafeCreditCardServer is a mock of UnsafeCreditCardServer interface.
type MockUnsafeCreditCardServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCreditCardServerMockRecorder
}

// MockUnsafeCreditCardServerMockRecorder is the mock recorder for MockUnsafeCreditCardServer.
type MockUnsafeCreditCardServerMockRecorder struct {
	mock *MockUnsafeCreditCardServer
}

// NewMockUnsafeCreditCardServer creates a new mock instance.
func NewMockUnsafeCreditCardServer(ctrl *gomock.Controller) *MockUnsafeCreditCardServer {
	mock := &MockUnsafeCreditCardServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCreditCardServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCreditCardServer) EXPECT() *MockUnsafeCreditCardServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCreditCardServer mocks base method.
func (m *MockUnsafeCreditCardServer) mustEmbedUnimplementedCreditCardServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCreditCardServer")
}

// mustEmbedUnimplementedCreditCardServer indicates an expected call of mustEmbedUnimplementedCreditCardServer.
func (mr *MockUnsafeCreditCardServerMockRecorder) mustEmbedUnimplementedCreditCardServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCreditCardServer", reflect.TypeOf((*MockUnsafeCreditCardServer)(nil).mustEmbedUnimplementedCreditCardServer))
}
