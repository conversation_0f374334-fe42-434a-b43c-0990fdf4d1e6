// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/creditcard/service.proto

package creditcard

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/firefly/v2/enums"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreditCardSdkModuleName int32

const (
	CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_UNSPECIFIED CreditCardSdkModuleName = 0
	CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING  CreditCardSdkModuleName = 1
	CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_CMS         CreditCardSdkModuleName = 2
)

// Enum value maps for CreditCardSdkModuleName.
var (
	CreditCardSdkModuleName_name = map[int32]string{
		0: "CREDIT_CARD_SDK_MODULE_NAME_UNSPECIFIED",
		1: "CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING",
		2: "CREDIT_CARD_SDK_MODULE_NAME_CMS",
	}
	CreditCardSdkModuleName_value = map[string]int32{
		"CREDIT_CARD_SDK_MODULE_NAME_UNSPECIFIED": 0,
		"CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING":  1,
		"CREDIT_CARD_SDK_MODULE_NAME_CMS":         2,
	}
)

func (x CreditCardSdkModuleName) Enum() *CreditCardSdkModuleName {
	p := new(CreditCardSdkModuleName)
	*p = x
	return p
}

func (x CreditCardSdkModuleName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditCardSdkModuleName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_creditcard_service_proto_enumTypes[0].Descriptor()
}

func (CreditCardSdkModuleName) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_creditcard_service_proto_enumTypes[0]
}

func (x CreditCardSdkModuleName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditCardSdkModuleName.Descriptor instead.
func (CreditCardSdkModuleName) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{0}
}

type DeliveryState int32

const (
	DeliveryState_DELIVERY_STATE_UNSPECIFIED DeliveryState = 0
	DeliveryState_DELIVERED                  DeliveryState = 1
	DeliveryState_RETURNED_TO_ORIGIN         DeliveryState = 2
)

// Enum value maps for DeliveryState.
var (
	DeliveryState_name = map[int32]string{
		0: "DELIVERY_STATE_UNSPECIFIED",
		1: "DELIVERED",
		2: "RETURNED_TO_ORIGIN",
	}
	DeliveryState_value = map[string]int32{
		"DELIVERY_STATE_UNSPECIFIED": 0,
		"DELIVERED":                  1,
		"RETURNED_TO_ORIGIN":         2,
	}
)

func (x DeliveryState) Enum() *DeliveryState {
	p := new(DeliveryState)
	*p = x
	return p
}

func (x DeliveryState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeliveryState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_creditcard_service_proto_enumTypes[1].Descriptor()
}

func (DeliveryState) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_creditcard_service_proto_enumTypes[1]
}

func (x DeliveryState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeliveryState.Descriptor instead.
func (DeliveryState) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{1}
}

type GenerateCreditCardSdkAuthTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendor gateway APIs.
	// Denotes the vendor that is supposed to process this request.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Applicant type (ETB, NTB, PRE-APPROVED)
	ApplicantType enums.CreditCardApplicantType `protobuf:"varint,2,opt,name=applicant_type,json=applicantType,proto3,enum=api.firefly.v2.enums.CreditCardApplicantType" json:"applicant_type,omitempty"`
	// User information
	UserInfo *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	// Device information
	DeviceInfo *DeviceInfo `protobuf:"bytes,4,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
	// PAN information (conditional optional)
	// This information is required only when request is for initial onboarding auth token generation,
	// otherwise, populating this field can be omitted.
	PanInfo *PanInfo `protobuf:"bytes,5,opt,name=pan_info,json=panInfo,proto3" json:"pan_info,omitempty"`
	// Consent information (optional)
	ConsentInfo *ConsentInfo `protobuf:"bytes,6,opt,name=consent_info,json=consentInfo,proto3" json:"consent_info,omitempty"`
	// Pre-approved information (conditional optional)
	// This information is required only when the applicant type is PRE-APPROVED and request is for initial onboarding auth token generation.
	// Otherwise, populating value for this field can be omitted.
	PreApprovedInfo *PreApprovedInfo `protobuf:"bytes,7,opt,name=pre_approved_info,json=preApprovedInfo,proto3" json:"pre_approved_info,omitempty"`
}

func (x *GenerateCreditCardSdkAuthTokenRequest) Reset() {
	*x = GenerateCreditCardSdkAuthTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateCreditCardSdkAuthTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateCreditCardSdkAuthTokenRequest) ProtoMessage() {}

func (x *GenerateCreditCardSdkAuthTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateCreditCardSdkAuthTokenRequest.ProtoReflect.Descriptor instead.
func (*GenerateCreditCardSdkAuthTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetApplicantType() enums.CreditCardApplicantType {
	if x != nil {
		return x.ApplicantType
	}
	return enums.CreditCardApplicantType(0)
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetDeviceInfo() *DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetPanInfo() *PanInfo {
	if x != nil {
		return x.PanInfo
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetConsentInfo() *ConsentInfo {
	if x != nil {
		return x.ConsentInfo
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetPreApprovedInfo() *PreApprovedInfo {
	if x != nil {
		return x.PreApprovedInfo
	}
	return nil
}

type GenerateCreditCardSdkAuthTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request.
	Status         *rpc.Status                    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ModuleName     CreditCardSdkModuleName        `protobuf:"varint,2,opt,name=module_name,json=moduleName,proto3,enum=vendorgateway.creditcard.CreditCardSdkModuleName" json:"module_name,omitempty"`
	AuthToken      string                         `protobuf:"bytes,3,opt,name=auth_token,json=authToken,proto3" json:"auth_token,omitempty"`
	AdditionalInfo *TokenGenerationAdditionalInfo `protobuf:"bytes,4,opt,name=additional_info,json=additionalInfo,proto3" json:"additional_info,omitempty"`
}

func (x *GenerateCreditCardSdkAuthTokenResponse) Reset() {
	*x = GenerateCreditCardSdkAuthTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateCreditCardSdkAuthTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateCreditCardSdkAuthTokenResponse) ProtoMessage() {}

func (x *GenerateCreditCardSdkAuthTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateCreditCardSdkAuthTokenResponse.ProtoReflect.Descriptor instead.
func (*GenerateCreditCardSdkAuthTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetModuleName() CreditCardSdkModuleName {
	if x != nil {
		return x.ModuleName
	}
	return CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_UNSPECIFIED
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetAuthToken() string {
	if x != nil {
		return x.AuthToken
	}
	return ""
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetAdditionalInfo() *TokenGenerationAdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

type TokenGenerationAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowId      string                  `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	UserLocalId     string                  `protobuf:"bytes,2,opt,name=user_local_id,json=userLocalId,proto3" json:"user_local_id,omitempty"`
	ExternalUserId  string                  `protobuf:"bytes,3,opt,name=external_user_id,json=externalUserId,proto3" json:"external_user_id,omitempty"`
	WorkflowStatus  enums.CardRequestStatus `protobuf:"varint,4,opt,name=workflow_status,json=workflowStatus,proto3,enum=api.firefly.v2.enums.CardRequestStatus" json:"workflow_status,omitempty"`
	WorkflowState   enums.CardRequestStage  `protobuf:"varint,5,opt,name=workflow_state,json=workflowState,proto3,enum=api.firefly.v2.enums.CardRequestStage" json:"workflow_state,omitempty"`
	WorkflowMessage string                  `protobuf:"bytes,6,opt,name=workflow_message,json=workflowMessage,proto3" json:"workflow_message,omitempty"`
	CreatedAt       *timestamppb.Timestamp  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp  `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *TokenGenerationAdditionalInfo) Reset() {
	*x = TokenGenerationAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenGenerationAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenGenerationAdditionalInfo) ProtoMessage() {}

func (x *TokenGenerationAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenGenerationAdditionalInfo.ProtoReflect.Descriptor instead.
func (*TokenGenerationAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{2}
}

func (x *TokenGenerationAdditionalInfo) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *TokenGenerationAdditionalInfo) GetUserLocalId() string {
	if x != nil {
		return x.UserLocalId
	}
	return ""
}

func (x *TokenGenerationAdditionalInfo) GetExternalUserId() string {
	if x != nil {
		return x.ExternalUserId
	}
	return ""
}

func (x *TokenGenerationAdditionalInfo) GetWorkflowStatus() enums.CardRequestStatus {
	if x != nil {
		return x.WorkflowStatus
	}
	return enums.CardRequestStatus(0)
}

func (x *TokenGenerationAdditionalInfo) GetWorkflowState() enums.CardRequestStage {
	if x != nil {
		return x.WorkflowState
	}
	return enums.CardRequestStage(0)
}

func (x *TokenGenerationAdditionalInfo) GetWorkflowMessage() string {
	if x != nil {
		return x.WorkflowMessage
	}
	return ""
}

func (x *TokenGenerationAdditionalInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TokenGenerationAdditionalInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Device *common.Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{3}
}

func (x *DeviceInfo) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User's email address.
	EmailAddress string `protobuf:"bytes,1,opt,name=email_address,json=emailAddress,proto3" json:"email_address,omitempty"`
	// User's phone number.
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Optional:
	PhoneType string `protobuf:"bytes,3,opt,name=phone_type,json=phoneType,proto3" json:"phone_type,omitempty"`
	// User's internal user ID at vendor's end.
	InternalUserId string `protobuf:"bytes,4,opt,name=internal_user_id,json=internalUserId,proto3" json:"internal_user_id,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{4}
}

func (x *UserInfo) GetEmailAddress() string {
	if x != nil {
		return x.EmailAddress
	}
	return ""
}

func (x *UserInfo) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *UserInfo) GetPhoneType() string {
	if x != nil {
		return x.PhoneType
	}
	return ""
}

func (x *UserInfo) GetInternalUserId() string {
	if x != nil {
		return x.InternalUserId
	}
	return ""
}

type PanInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PAN number.
	PanNumber string `protobuf:"bytes,1,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// Full name of user associated with the PAN.
	UserName *common.Name `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
}

func (x *PanInfo) Reset() {
	*x = PanInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PanInfo) ProtoMessage() {}

func (x *PanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PanInfo.ProtoReflect.Descriptor instead.
func (*PanInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{5}
}

func (x *PanInfo) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *PanInfo) GetUserName() *common.Name {
	if x != nil {
		return x.UserName
	}
	return nil
}

type PreApprovedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pre-approved limit.
	PreApprovedLimit *money.Money `protobuf:"bytes,1,opt,name=pre_approved_limit,json=preApprovedLimit,proto3" json:"pre_approved_limit,omitempty"`
	// Pre-approved expiration date.
	PreApprovedExp *date.Date `protobuf:"bytes,2,opt,name=pre_approved_exp,json=preApprovedExp,proto3" json:"pre_approved_exp,omitempty"`
}

func (x *PreApprovedInfo) Reset() {
	*x = PreApprovedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreApprovedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreApprovedInfo) ProtoMessage() {}

func (x *PreApprovedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreApprovedInfo.ProtoReflect.Descriptor instead.
func (*PreApprovedInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{6}
}

func (x *PreApprovedInfo) GetPreApprovedLimit() *money.Money {
	if x != nil {
		return x.PreApprovedLimit
	}
	return nil
}

func (x *PreApprovedInfo) GetPreApprovedExp() *date.Date {
	if x != nil {
		return x.PreApprovedExp
	}
	return nil
}

type ConsentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Consents []*Consent `protobuf:"bytes,1,rep,name=consents,proto3" json:"consents,omitempty"`
}

func (x *ConsentInfo) Reset() {
	*x = ConsentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentInfo) ProtoMessage() {}

func (x *ConsentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentInfo.ProtoReflect.Descriptor instead.
func (*ConsentInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{7}
}

func (x *ConsentInfo) GetConsents() []*Consent {
	if x != nil {
		return x.Consents
	}
	return nil
}

type Consent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Indicates if the consent is recorded.
	IsConsentRecorded bool `protobuf:"varint,1,opt,name=is_consent_recorded,json=isConsentRecorded,proto3" json:"is_consent_recorded,omitempty"`
	// Type of the consent.
	ConsentType string `protobuf:"bytes,2,opt,name=consent_type,json=consentType,proto3" json:"consent_type,omitempty"`
	// Category of the consent.
	ConsentCategory string `protobuf:"bytes,3,opt,name=consent_category,json=consentCategory,proto3" json:"consent_category,omitempty"`
}

func (x *Consent) Reset() {
	*x = Consent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Consent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Consent) ProtoMessage() {}

func (x *Consent) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Consent.ProtoReflect.Descriptor instead.
func (*Consent) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{8}
}

func (x *Consent) GetIsConsentRecorded() bool {
	if x != nil {
		return x.IsConsentRecorded
	}
	return false
}

func (x *Consent) GetConsentType() string {
	if x != nil {
		return x.ConsentType
	}
	return ""
}

func (x *Consent) GetConsentCategory() string {
	if x != nil {
		return x.ConsentCategory
	}
	return ""
}

type UpdateCreditCardDeliveryStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendor gateway APIs.
	// Denotes the vendor that is supposed to process this request.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// carrier partner of the tracking request (DELHIVERY, BLUEDART etc)
	Carrier string `protobuf:"bytes,2,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// delivery state of the shipment
	DeliveryState DeliveryState `protobuf:"varint,3,opt,name=delivery_state,json=deliveryState,proto3,enum=vendorgateway.creditcard.DeliveryState" json:"delivery_state,omitempty"`
	TrackingUrl   string        `protobuf:"bytes,4,opt,name=tracking_url,json=trackingUrl,proto3" json:"tracking_url,omitempty"`
	UserId        string        `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *UpdateCreditCardDeliveryStateRequest) Reset() {
	*x = UpdateCreditCardDeliveryStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditCardDeliveryStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditCardDeliveryStateRequest) ProtoMessage() {}

func (x *UpdateCreditCardDeliveryStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditCardDeliveryStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateCreditCardDeliveryStateRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateCreditCardDeliveryStateRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateCreditCardDeliveryStateRequest) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *UpdateCreditCardDeliveryStateRequest) GetDeliveryState() DeliveryState {
	if x != nil {
		return x.DeliveryState
	}
	return DeliveryState_DELIVERY_STATE_UNSPECIFIED
}

func (x *UpdateCreditCardDeliveryStateRequest) GetTrackingUrl() string {
	if x != nil {
		return x.TrackingUrl
	}
	return ""
}

func (x *UpdateCreditCardDeliveryStateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UpdateCreditCardDeliveryStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request.
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateCreditCardDeliveryStateResponse) Reset() {
	*x = UpdateCreditCardDeliveryStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditCardDeliveryStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditCardDeliveryStateResponse) ProtoMessage() {}

func (x *UpdateCreditCardDeliveryStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_creditcard_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditCardDeliveryStateResponse.ProtoReflect.Descriptor instead.
func (*UpdateCreditCardDeliveryStateResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_creditcard_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateCreditCardDeliveryStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_vendorgateway_creditcard_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_creditcard_service_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x04, 0x0a, 0x25, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76,
	0x32, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x4f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x50, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x70, 0x61, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x48, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x55, 0x0a, 0x11,
	0x70, 0x72, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0xa2, 0x02, 0x0a, 0x26, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x52, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64,
	0x6b, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x75, 0x74,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x60, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd0, 0x03, 0x0a, 0x1d, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x0f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x76, 0x32, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x76, 0x32, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x40, 0x0a, 0x0a, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0xda, 0x01,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4c, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x07, 0x50, 0x61,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x3f, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xa4, 0x01, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4a, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x10,
	0x70, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x45, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x5f, 0x65, 0x78, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x45, 0x78, 0x70, 0x22, 0x4c, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x69, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0xa2, 0x02, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0d,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2a, 0x97, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x64, 0x6b, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b,
	0x0a, 0x27, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x44,
	0x4b, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x44, 0x4b, 0x5f, 0x4d,
	0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x44, 0x4b, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c,
	0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4d, 0x53, 0x10, 0x02, 0x2a, 0x56, 0x0a, 0x0d,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a,
	0x1a, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12,
	0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x4f, 0x52, 0x49, 0x47,
	0x49, 0x4e, 0x10, 0x02, 0x32, 0xd5, 0x02, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x12, 0xa3, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x1d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x6a, 0x0a, 0x33,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x63,
	0x61, 0x72, 0x64, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_creditcard_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_creditcard_service_proto_rawDescData = file_api_vendorgateway_creditcard_service_proto_rawDesc
)

func file_api_vendorgateway_creditcard_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_creditcard_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_creditcard_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_creditcard_service_proto_rawDescData)
	})
	return file_api_vendorgateway_creditcard_service_proto_rawDescData
}

var file_api_vendorgateway_creditcard_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_vendorgateway_creditcard_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_vendorgateway_creditcard_service_proto_goTypes = []interface{}{
	(CreditCardSdkModuleName)(0),                   // 0: vendorgateway.creditcard.CreditCardSdkModuleName
	(DeliveryState)(0),                             // 1: vendorgateway.creditcard.DeliveryState
	(*GenerateCreditCardSdkAuthTokenRequest)(nil),  // 2: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest
	(*GenerateCreditCardSdkAuthTokenResponse)(nil), // 3: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenResponse
	(*TokenGenerationAdditionalInfo)(nil),          // 4: vendorgateway.creditcard.TokenGenerationAdditionalInfo
	(*DeviceInfo)(nil),                             // 5: vendorgateway.creditcard.DeviceInfo
	(*UserInfo)(nil),                               // 6: vendorgateway.creditcard.UserInfo
	(*PanInfo)(nil),                                // 7: vendorgateway.creditcard.PanInfo
	(*PreApprovedInfo)(nil),                        // 8: vendorgateway.creditcard.PreApprovedInfo
	(*ConsentInfo)(nil),                            // 9: vendorgateway.creditcard.ConsentInfo
	(*Consent)(nil),                                // 10: vendorgateway.creditcard.Consent
	(*UpdateCreditCardDeliveryStateRequest)(nil),   // 11: vendorgateway.creditcard.UpdateCreditCardDeliveryStateRequest
	(*UpdateCreditCardDeliveryStateResponse)(nil),  // 12: vendorgateway.creditcard.UpdateCreditCardDeliveryStateResponse
	(*vendorgateway.RequestHeader)(nil),            // 13: vendorgateway.RequestHeader
	(enums.CreditCardApplicantType)(0),             // 14: api.firefly.v2.enums.CreditCardApplicantType
	(*rpc.Status)(nil),                             // 15: rpc.Status
	(enums.CardRequestStatus)(0),                   // 16: api.firefly.v2.enums.CardRequestStatus
	(enums.CardRequestStage)(0),                    // 17: api.firefly.v2.enums.CardRequestStage
	(*timestamppb.Timestamp)(nil),                  // 18: google.protobuf.Timestamp
	(*common.Device)(nil),                          // 19: api.typesv2.common.Device
	(*common.PhoneNumber)(nil),                     // 20: api.typesv2.common.PhoneNumber
	(*common.Name)(nil),                            // 21: api.typesv2.common.Name
	(*money.Money)(nil),                            // 22: google.type.Money
	(*date.Date)(nil),                              // 23: google.type.Date
}
var file_api_vendorgateway_creditcard_service_proto_depIdxs = []int32{
	13, // 0: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.header:type_name -> vendorgateway.RequestHeader
	14, // 1: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.applicant_type:type_name -> api.firefly.v2.enums.CreditCardApplicantType
	6,  // 2: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.user_info:type_name -> vendorgateway.creditcard.UserInfo
	5,  // 3: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.device_info:type_name -> vendorgateway.creditcard.DeviceInfo
	7,  // 4: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.pan_info:type_name -> vendorgateway.creditcard.PanInfo
	9,  // 5: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.consent_info:type_name -> vendorgateway.creditcard.ConsentInfo
	8,  // 6: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest.pre_approved_info:type_name -> vendorgateway.creditcard.PreApprovedInfo
	15, // 7: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenResponse.status:type_name -> rpc.Status
	0,  // 8: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenResponse.module_name:type_name -> vendorgateway.creditcard.CreditCardSdkModuleName
	4,  // 9: vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenResponse.additional_info:type_name -> vendorgateway.creditcard.TokenGenerationAdditionalInfo
	16, // 10: vendorgateway.creditcard.TokenGenerationAdditionalInfo.workflow_status:type_name -> api.firefly.v2.enums.CardRequestStatus
	17, // 11: vendorgateway.creditcard.TokenGenerationAdditionalInfo.workflow_state:type_name -> api.firefly.v2.enums.CardRequestStage
	18, // 12: vendorgateway.creditcard.TokenGenerationAdditionalInfo.created_at:type_name -> google.protobuf.Timestamp
	18, // 13: vendorgateway.creditcard.TokenGenerationAdditionalInfo.updated_at:type_name -> google.protobuf.Timestamp
	19, // 14: vendorgateway.creditcard.DeviceInfo.device:type_name -> api.typesv2.common.Device
	20, // 15: vendorgateway.creditcard.UserInfo.phone_number:type_name -> api.typesv2.common.PhoneNumber
	21, // 16: vendorgateway.creditcard.PanInfo.user_name:type_name -> api.typesv2.common.Name
	22, // 17: vendorgateway.creditcard.PreApprovedInfo.pre_approved_limit:type_name -> google.type.Money
	23, // 18: vendorgateway.creditcard.PreApprovedInfo.pre_approved_exp:type_name -> google.type.Date
	10, // 19: vendorgateway.creditcard.ConsentInfo.consents:type_name -> vendorgateway.creditcard.Consent
	13, // 20: vendorgateway.creditcard.UpdateCreditCardDeliveryStateRequest.header:type_name -> vendorgateway.RequestHeader
	1,  // 21: vendorgateway.creditcard.UpdateCreditCardDeliveryStateRequest.delivery_state:type_name -> vendorgateway.creditcard.DeliveryState
	15, // 22: vendorgateway.creditcard.UpdateCreditCardDeliveryStateResponse.status:type_name -> rpc.Status
	2,  // 23: vendorgateway.creditcard.CreditCard.GenerateCreditCardSdkAuthToken:input_type -> vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenRequest
	11, // 24: vendorgateway.creditcard.CreditCard.UpdateCreditCardDeliveryState:input_type -> vendorgateway.creditcard.UpdateCreditCardDeliveryStateRequest
	3,  // 25: vendorgateway.creditcard.CreditCard.GenerateCreditCardSdkAuthToken:output_type -> vendorgateway.creditcard.GenerateCreditCardSdkAuthTokenResponse
	12, // 26: vendorgateway.creditcard.CreditCard.UpdateCreditCardDeliveryState:output_type -> vendorgateway.creditcard.UpdateCreditCardDeliveryStateResponse
	25, // [25:27] is the sub-list for method output_type
	23, // [23:25] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_creditcard_service_proto_init() }
func file_api_vendorgateway_creditcard_service_proto_init() {
	if File_api_vendorgateway_creditcard_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_creditcard_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateCreditCardSdkAuthTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateCreditCardSdkAuthTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenGenerationAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PanInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreApprovedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Consent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditCardDeliveryStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_creditcard_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditCardDeliveryStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_creditcard_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_creditcard_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_creditcard_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_creditcard_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_creditcard_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_creditcard_service_proto = out.File
	file_api_vendorgateway_creditcard_service_proto_rawDesc = nil
	file_api_vendorgateway_creditcard_service_proto_goTypes = nil
	file_api_vendorgateway_creditcard_service_proto_depIdxs = nil
}
