// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/lending/preapprovedloan/liquiloans/service.proto

package liquiloans

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Liquiloans_GetCreditLineSchemes_FullMethodName       = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetCreditLineSchemes"
	Liquiloans_AddPersonalDetails_FullMethodName         = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/AddPersonalDetails"
	Liquiloans_AddBankingDetails_FullMethodName          = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/AddBankingDetails"
	Liquiloans_AddAddressDetails_FullMethodName          = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/AddAddressDetails"
	Liquiloans_AddEmploymentDetails_FullMethodName       = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/AddEmploymentDetails"
	Liquiloans_ApplicantLookup_FullMethodName            = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/ApplicantLookup"
	Liquiloans_GetMandateLink_FullMethodName             = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetMandateLink"
	Liquiloans_GetMandateStatus_FullMethodName           = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetMandateStatus"
	Liquiloans_MakeDrawdown_FullMethodName               = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/MakeDrawdown"
	Liquiloans_GetPdfAgreement_FullMethodName            = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetPdfAgreement"
	Liquiloans_SendBorrowerAgreementOtp_FullMethodName   = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/SendBorrowerAgreementOtp"
	Liquiloans_VerifyBorrowerAgreementOtp_FullMethodName = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/VerifyBorrowerAgreementOtp"
	Liquiloans_GetLoanStatus_FullMethodName              = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetLoanStatus"
	Liquiloans_VerifyAndDownloadCkyc_FullMethodName      = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/VerifyAndDownloadCkyc"
	Liquiloans_GetApplicantStatus_FullMethodName         = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetApplicantStatus"
	Liquiloans_GetRepaymentSchedule_FullMethodName       = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetRepaymentSchedule"
	Liquiloans_UploadDocument_FullMethodName             = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/UploadDocument"
	Liquiloans_SaveCollection_FullMethodName             = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/SaveCollection"
	Liquiloans_HashGenerationForOkyc_FullMethodName      = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/HashGenerationForOkyc"
	Liquiloans_CaptchaGenerationForOkyc_FullMethodName   = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/CaptchaGenerationForOkyc"
	Liquiloans_GenerateOtpForOkyc_FullMethodName         = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GenerateOtpForOkyc"
	Liquiloans_ValidateOtpForOkyc_FullMethodName         = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/ValidateOtpForOkyc"
	Liquiloans_UpdateLead_FullMethodName                 = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/UpdateLead"
	Liquiloans_CancelLead_FullMethodName                 = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/CancelLead"
	Liquiloans_ForeClosureDetails_FullMethodName         = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/ForeClosureDetails"
	Liquiloans_UpdateApplicantUdf_FullMethodName         = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/UpdateApplicantUdf"
	Liquiloans_CreateRepaymentSchedule_FullMethodName    = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/CreateRepaymentSchedule"
	Liquiloans_SaveCharges_FullMethodName                = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/SaveCharges"
	Liquiloans_GetApplicationSoa_FullMethodName          = "/vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans/GetApplicationSoa"
)

// LiquiloansClient is the client API for Liquiloans service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LiquiloansClient interface {
	GetCreditLineSchemes(ctx context.Context, in *GetCreditLineSchemesRequest, opts ...grpc.CallOption) (*GetCreditLineSchemesResponse, error)
	// This serves as the first interaction of a new user with Liquiloans, if all details are correct, a new user will be
	// created on Liquiloans side and an unique applicant_id will be returned.
	AddPersonalDetails(ctx context.Context, in *AddPersonalDetailsRequest, opts ...grpc.CallOption) (*AddPersonalDetailsResponse, error)
	AddBankingDetails(ctx context.Context, in *AddBankingDetailsRequest, opts ...grpc.CallOption) (*AddDetailsResponse, error)
	AddAddressDetails(ctx context.Context, in *AddAddressDetailsRequest, opts ...grpc.CallOption) (*AddDetailsResponse, error)
	AddEmploymentDetails(ctx context.Context, in *AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*AddDetailsResponse, error)
	ApplicantLookup(ctx context.Context, in *ApplicantLookupRequest, opts ...grpc.CallOption) (*ApplicantLookupResponse, error)
	GetMandateLink(ctx context.Context, in *GetMandateLinkRequest, opts ...grpc.CallOption) (*GetMandateLinkResponse, error)
	GetMandateStatus(ctx context.Context, in *GetMandateStatusRequest, opts ...grpc.CallOption) (*GetMandateStatusResponse, error)
	MakeDrawdown(ctx context.Context, in *MakeDrawdownRequest, opts ...grpc.CallOption) (*MakeDrawdownResponse, error)
	GetPdfAgreement(ctx context.Context, in *GetPdfAgreementRequest, opts ...grpc.CallOption) (*GetPdfAgreementResponse, error)
	SendBorrowerAgreementOtp(ctx context.Context, in *SendBorrowerAgreementOtpRequest, opts ...grpc.CallOption) (*SendBorrowerAgreementOtpResponse, error)
	VerifyBorrowerAgreementOtp(ctx context.Context, in *VerifyBorrowerAgreementOtpRequest, opts ...grpc.CallOption) (*VerifyBorrowerAgreementOtpResponse, error)
	GetLoanStatus(ctx context.Context, in *GetLoanStatusRequest, opts ...grpc.CallOption) (*GetLoanStatusResponse, error)
	VerifyAndDownloadCkyc(ctx context.Context, in *VerifyAndDownloadCkycRequest, opts ...grpc.CallOption) (*VerifyAndDownloadCkycResponse, error)
	GetApplicantStatus(ctx context.Context, in *GetApplicantStatusRequest, opts ...grpc.CallOption) (*GetApplicantStatusResponse, error)
	// This RPC fetch repayment schedules for a previously created loan application.
	GetRepaymentSchedule(ctx context.Context, in *GetRepaymentScheduleRequest, opts ...grpc.CallOption) (*GetRepaymentScheduleResponse, error)
	UploadDocument(ctx context.Context, in *UploadDocumentRequest, opts ...grpc.CallOption) (*UploadDocumentResponse, error)
	// Purpose of this API is to save repayment collection details against the loan application.
	SaveCollection(ctx context.Context, in *SaveCollectionRequest, opts ...grpc.CallOption) (*SaveCollectionResponse, error)
	HashGenerationForOkyc(ctx context.Context, in *HashGenerationForOkycRequest, opts ...grpc.CallOption) (*HashGenerationForOkycResponse, error)
	CaptchaGenerationForOkyc(ctx context.Context, in *CaptchaGenerationForOkycRequest, opts ...grpc.CallOption) (*CaptchaGenerationForOkycResponse, error)
	GenerateOtpForOkyc(ctx context.Context, in *GenerateOtpForOkycRequest, opts ...grpc.CallOption) (*GenerateOtpForOkycResponse, error)
	ValidateOtpForOkyc(ctx context.Context, in *ValidateOtpForOkycRequest, opts ...grpc.CallOption) (*ValidateOtpForOkycResponse, error)
	// UpdateLead is used to update the lead details on Liquiloans side.
	// vendor doc: https://epifi.slack.com/files/U03A6H5EXEH/F05U99A8VC6/epifi_update_lead_api.docx
	UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...grpc.CallOption) (*UpdateLeadResponse, error)
	// CancelLead API frees the loan amount whih might have been frozen as user might have tried to take a loan in the past but did not complete the loan journey
	// CancelLead API Doc : https://liquiloans.slack.com/files/U04HF36Q4TG/F061C53M2KX/loan_cancellation_api__1_.pdf
	CancelLead(ctx context.Context, in *CancelLeadRequest, opts ...grpc.CallOption) (*CancelLeadResponse, error)
	ForeClosureDetails(ctx context.Context, in *ForeClosureDetailsRequest, opts ...grpc.CallOption) (*ForeClosureDetailsResponse, error)
	// UpdateApplicantUdf RPC used to update any UDFs (User Defined Fields) for ann applicant in the LL system
	UpdateApplicantUdf(ctx context.Context, in *UpdateApplicantUdfRequest, opts ...grpc.CallOption) (*UpdateApplicantUdfResponse, error)
	// CreateRepaymentSchedule RPC creates repayment schedule for a loan after drawdown is done
	CreateRepaymentSchedule(ctx context.Context, in *CreateRepaymentScheduleRequest, opts ...grpc.CallOption) (*CreateRepaymentScheduleResponse, error)
	// SaveCharges RPC is used to post charges against a loan on Liquiloans side
	// the RPC is idempotent based on the charge_id field
	SaveCharges(ctx context.Context, in *SaveChargesRequest, opts ...grpc.CallOption) (*SaveChargesResponse, error)
	// GetApplicationSoa RPC is used to fetch the Statement of Account (SoA) for a given application
	GetApplicationSoa(ctx context.Context, in *GetApplicationSoaRequest, opts ...grpc.CallOption) (*GetApplicationSoaResponse, error)
}

type liquiloansClient struct {
	cc grpc.ClientConnInterface
}

func NewLiquiloansClient(cc grpc.ClientConnInterface) LiquiloansClient {
	return &liquiloansClient{cc}
}

func (c *liquiloansClient) GetCreditLineSchemes(ctx context.Context, in *GetCreditLineSchemesRequest, opts ...grpc.CallOption) (*GetCreditLineSchemesResponse, error) {
	out := new(GetCreditLineSchemesResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetCreditLineSchemes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) AddPersonalDetails(ctx context.Context, in *AddPersonalDetailsRequest, opts ...grpc.CallOption) (*AddPersonalDetailsResponse, error) {
	out := new(AddPersonalDetailsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_AddPersonalDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) AddBankingDetails(ctx context.Context, in *AddBankingDetailsRequest, opts ...grpc.CallOption) (*AddDetailsResponse, error) {
	out := new(AddDetailsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_AddBankingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) AddAddressDetails(ctx context.Context, in *AddAddressDetailsRequest, opts ...grpc.CallOption) (*AddDetailsResponse, error) {
	out := new(AddDetailsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_AddAddressDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) AddEmploymentDetails(ctx context.Context, in *AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*AddDetailsResponse, error) {
	out := new(AddDetailsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_AddEmploymentDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) ApplicantLookup(ctx context.Context, in *ApplicantLookupRequest, opts ...grpc.CallOption) (*ApplicantLookupResponse, error) {
	out := new(ApplicantLookupResponse)
	err := c.cc.Invoke(ctx, Liquiloans_ApplicantLookup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetMandateLink(ctx context.Context, in *GetMandateLinkRequest, opts ...grpc.CallOption) (*GetMandateLinkResponse, error) {
	out := new(GetMandateLinkResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetMandateLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetMandateStatus(ctx context.Context, in *GetMandateStatusRequest, opts ...grpc.CallOption) (*GetMandateStatusResponse, error) {
	out := new(GetMandateStatusResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetMandateStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) MakeDrawdown(ctx context.Context, in *MakeDrawdownRequest, opts ...grpc.CallOption) (*MakeDrawdownResponse, error) {
	out := new(MakeDrawdownResponse)
	err := c.cc.Invoke(ctx, Liquiloans_MakeDrawdown_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetPdfAgreement(ctx context.Context, in *GetPdfAgreementRequest, opts ...grpc.CallOption) (*GetPdfAgreementResponse, error) {
	out := new(GetPdfAgreementResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetPdfAgreement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) SendBorrowerAgreementOtp(ctx context.Context, in *SendBorrowerAgreementOtpRequest, opts ...grpc.CallOption) (*SendBorrowerAgreementOtpResponse, error) {
	out := new(SendBorrowerAgreementOtpResponse)
	err := c.cc.Invoke(ctx, Liquiloans_SendBorrowerAgreementOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) VerifyBorrowerAgreementOtp(ctx context.Context, in *VerifyBorrowerAgreementOtpRequest, opts ...grpc.CallOption) (*VerifyBorrowerAgreementOtpResponse, error) {
	out := new(VerifyBorrowerAgreementOtpResponse)
	err := c.cc.Invoke(ctx, Liquiloans_VerifyBorrowerAgreementOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetLoanStatus(ctx context.Context, in *GetLoanStatusRequest, opts ...grpc.CallOption) (*GetLoanStatusResponse, error) {
	out := new(GetLoanStatusResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetLoanStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) VerifyAndDownloadCkyc(ctx context.Context, in *VerifyAndDownloadCkycRequest, opts ...grpc.CallOption) (*VerifyAndDownloadCkycResponse, error) {
	out := new(VerifyAndDownloadCkycResponse)
	err := c.cc.Invoke(ctx, Liquiloans_VerifyAndDownloadCkyc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetApplicantStatus(ctx context.Context, in *GetApplicantStatusRequest, opts ...grpc.CallOption) (*GetApplicantStatusResponse, error) {
	out := new(GetApplicantStatusResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetApplicantStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetRepaymentSchedule(ctx context.Context, in *GetRepaymentScheduleRequest, opts ...grpc.CallOption) (*GetRepaymentScheduleResponse, error) {
	out := new(GetRepaymentScheduleResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetRepaymentSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) UploadDocument(ctx context.Context, in *UploadDocumentRequest, opts ...grpc.CallOption) (*UploadDocumentResponse, error) {
	out := new(UploadDocumentResponse)
	err := c.cc.Invoke(ctx, Liquiloans_UploadDocument_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) SaveCollection(ctx context.Context, in *SaveCollectionRequest, opts ...grpc.CallOption) (*SaveCollectionResponse, error) {
	out := new(SaveCollectionResponse)
	err := c.cc.Invoke(ctx, Liquiloans_SaveCollection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) HashGenerationForOkyc(ctx context.Context, in *HashGenerationForOkycRequest, opts ...grpc.CallOption) (*HashGenerationForOkycResponse, error) {
	out := new(HashGenerationForOkycResponse)
	err := c.cc.Invoke(ctx, Liquiloans_HashGenerationForOkyc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) CaptchaGenerationForOkyc(ctx context.Context, in *CaptchaGenerationForOkycRequest, opts ...grpc.CallOption) (*CaptchaGenerationForOkycResponse, error) {
	out := new(CaptchaGenerationForOkycResponse)
	err := c.cc.Invoke(ctx, Liquiloans_CaptchaGenerationForOkyc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GenerateOtpForOkyc(ctx context.Context, in *GenerateOtpForOkycRequest, opts ...grpc.CallOption) (*GenerateOtpForOkycResponse, error) {
	out := new(GenerateOtpForOkycResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GenerateOtpForOkyc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) ValidateOtpForOkyc(ctx context.Context, in *ValidateOtpForOkycRequest, opts ...grpc.CallOption) (*ValidateOtpForOkycResponse, error) {
	out := new(ValidateOtpForOkycResponse)
	err := c.cc.Invoke(ctx, Liquiloans_ValidateOtpForOkyc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...grpc.CallOption) (*UpdateLeadResponse, error) {
	out := new(UpdateLeadResponse)
	err := c.cc.Invoke(ctx, Liquiloans_UpdateLead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) CancelLead(ctx context.Context, in *CancelLeadRequest, opts ...grpc.CallOption) (*CancelLeadResponse, error) {
	out := new(CancelLeadResponse)
	err := c.cc.Invoke(ctx, Liquiloans_CancelLead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) ForeClosureDetails(ctx context.Context, in *ForeClosureDetailsRequest, opts ...grpc.CallOption) (*ForeClosureDetailsResponse, error) {
	out := new(ForeClosureDetailsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_ForeClosureDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) UpdateApplicantUdf(ctx context.Context, in *UpdateApplicantUdfRequest, opts ...grpc.CallOption) (*UpdateApplicantUdfResponse, error) {
	out := new(UpdateApplicantUdfResponse)
	err := c.cc.Invoke(ctx, Liquiloans_UpdateApplicantUdf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) CreateRepaymentSchedule(ctx context.Context, in *CreateRepaymentScheduleRequest, opts ...grpc.CallOption) (*CreateRepaymentScheduleResponse, error) {
	out := new(CreateRepaymentScheduleResponse)
	err := c.cc.Invoke(ctx, Liquiloans_CreateRepaymentSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) SaveCharges(ctx context.Context, in *SaveChargesRequest, opts ...grpc.CallOption) (*SaveChargesResponse, error) {
	out := new(SaveChargesResponse)
	err := c.cc.Invoke(ctx, Liquiloans_SaveCharges_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetApplicationSoa(ctx context.Context, in *GetApplicationSoaRequest, opts ...grpc.CallOption) (*GetApplicationSoaResponse, error) {
	out := new(GetApplicationSoaResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetApplicationSoa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LiquiloansServer is the server API for Liquiloans service.
// All implementations should embed UnimplementedLiquiloansServer
// for forward compatibility
type LiquiloansServer interface {
	GetCreditLineSchemes(context.Context, *GetCreditLineSchemesRequest) (*GetCreditLineSchemesResponse, error)
	// This serves as the first interaction of a new user with Liquiloans, if all details are correct, a new user will be
	// created on Liquiloans side and an unique applicant_id will be returned.
	AddPersonalDetails(context.Context, *AddPersonalDetailsRequest) (*AddPersonalDetailsResponse, error)
	AddBankingDetails(context.Context, *AddBankingDetailsRequest) (*AddDetailsResponse, error)
	AddAddressDetails(context.Context, *AddAddressDetailsRequest) (*AddDetailsResponse, error)
	AddEmploymentDetails(context.Context, *AddEmploymentDetailsRequest) (*AddDetailsResponse, error)
	ApplicantLookup(context.Context, *ApplicantLookupRequest) (*ApplicantLookupResponse, error)
	GetMandateLink(context.Context, *GetMandateLinkRequest) (*GetMandateLinkResponse, error)
	GetMandateStatus(context.Context, *GetMandateStatusRequest) (*GetMandateStatusResponse, error)
	MakeDrawdown(context.Context, *MakeDrawdownRequest) (*MakeDrawdownResponse, error)
	GetPdfAgreement(context.Context, *GetPdfAgreementRequest) (*GetPdfAgreementResponse, error)
	SendBorrowerAgreementOtp(context.Context, *SendBorrowerAgreementOtpRequest) (*SendBorrowerAgreementOtpResponse, error)
	VerifyBorrowerAgreementOtp(context.Context, *VerifyBorrowerAgreementOtpRequest) (*VerifyBorrowerAgreementOtpResponse, error)
	GetLoanStatus(context.Context, *GetLoanStatusRequest) (*GetLoanStatusResponse, error)
	VerifyAndDownloadCkyc(context.Context, *VerifyAndDownloadCkycRequest) (*VerifyAndDownloadCkycResponse, error)
	GetApplicantStatus(context.Context, *GetApplicantStatusRequest) (*GetApplicantStatusResponse, error)
	// This RPC fetch repayment schedules for a previously created loan application.
	GetRepaymentSchedule(context.Context, *GetRepaymentScheduleRequest) (*GetRepaymentScheduleResponse, error)
	UploadDocument(context.Context, *UploadDocumentRequest) (*UploadDocumentResponse, error)
	// Purpose of this API is to save repayment collection details against the loan application.
	SaveCollection(context.Context, *SaveCollectionRequest) (*SaveCollectionResponse, error)
	HashGenerationForOkyc(context.Context, *HashGenerationForOkycRequest) (*HashGenerationForOkycResponse, error)
	CaptchaGenerationForOkyc(context.Context, *CaptchaGenerationForOkycRequest) (*CaptchaGenerationForOkycResponse, error)
	GenerateOtpForOkyc(context.Context, *GenerateOtpForOkycRequest) (*GenerateOtpForOkycResponse, error)
	ValidateOtpForOkyc(context.Context, *ValidateOtpForOkycRequest) (*ValidateOtpForOkycResponse, error)
	// UpdateLead is used to update the lead details on Liquiloans side.
	// vendor doc: https://epifi.slack.com/files/U03A6H5EXEH/F05U99A8VC6/epifi_update_lead_api.docx
	UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error)
	// CancelLead API frees the loan amount whih might have been frozen as user might have tried to take a loan in the past but did not complete the loan journey
	// CancelLead API Doc : https://liquiloans.slack.com/files/U04HF36Q4TG/F061C53M2KX/loan_cancellation_api__1_.pdf
	CancelLead(context.Context, *CancelLeadRequest) (*CancelLeadResponse, error)
	ForeClosureDetails(context.Context, *ForeClosureDetailsRequest) (*ForeClosureDetailsResponse, error)
	// UpdateApplicantUdf RPC used to update any UDFs (User Defined Fields) for ann applicant in the LL system
	UpdateApplicantUdf(context.Context, *UpdateApplicantUdfRequest) (*UpdateApplicantUdfResponse, error)
	// CreateRepaymentSchedule RPC creates repayment schedule for a loan after drawdown is done
	CreateRepaymentSchedule(context.Context, *CreateRepaymentScheduleRequest) (*CreateRepaymentScheduleResponse, error)
	// SaveCharges RPC is used to post charges against a loan on Liquiloans side
	// the RPC is idempotent based on the charge_id field
	SaveCharges(context.Context, *SaveChargesRequest) (*SaveChargesResponse, error)
	// GetApplicationSoa RPC is used to fetch the Statement of Account (SoA) for a given application
	GetApplicationSoa(context.Context, *GetApplicationSoaRequest) (*GetApplicationSoaResponse, error)
}

// UnimplementedLiquiloansServer should be embedded to have forward compatible implementations.
type UnimplementedLiquiloansServer struct {
}

func (UnimplementedLiquiloansServer) GetCreditLineSchemes(context.Context, *GetCreditLineSchemesRequest) (*GetCreditLineSchemesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCreditLineSchemes not implemented")
}
func (UnimplementedLiquiloansServer) AddPersonalDetails(context.Context, *AddPersonalDetailsRequest) (*AddPersonalDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPersonalDetails not implemented")
}
func (UnimplementedLiquiloansServer) AddBankingDetails(context.Context, *AddBankingDetailsRequest) (*AddDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBankingDetails not implemented")
}
func (UnimplementedLiquiloansServer) AddAddressDetails(context.Context, *AddAddressDetailsRequest) (*AddDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAddressDetails not implemented")
}
func (UnimplementedLiquiloansServer) AddEmploymentDetails(context.Context, *AddEmploymentDetailsRequest) (*AddDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddEmploymentDetails not implemented")
}
func (UnimplementedLiquiloansServer) ApplicantLookup(context.Context, *ApplicantLookupRequest) (*ApplicantLookupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplicantLookup not implemented")
}
func (UnimplementedLiquiloansServer) GetMandateLink(context.Context, *GetMandateLinkRequest) (*GetMandateLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandateLink not implemented")
}
func (UnimplementedLiquiloansServer) GetMandateStatus(context.Context, *GetMandateStatusRequest) (*GetMandateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandateStatus not implemented")
}
func (UnimplementedLiquiloansServer) MakeDrawdown(context.Context, *MakeDrawdownRequest) (*MakeDrawdownResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeDrawdown not implemented")
}
func (UnimplementedLiquiloansServer) GetPdfAgreement(context.Context, *GetPdfAgreementRequest) (*GetPdfAgreementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPdfAgreement not implemented")
}
func (UnimplementedLiquiloansServer) SendBorrowerAgreementOtp(context.Context, *SendBorrowerAgreementOtpRequest) (*SendBorrowerAgreementOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendBorrowerAgreementOtp not implemented")
}
func (UnimplementedLiquiloansServer) VerifyBorrowerAgreementOtp(context.Context, *VerifyBorrowerAgreementOtpRequest) (*VerifyBorrowerAgreementOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyBorrowerAgreementOtp not implemented")
}
func (UnimplementedLiquiloansServer) GetLoanStatus(context.Context, *GetLoanStatusRequest) (*GetLoanStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanStatus not implemented")
}
func (UnimplementedLiquiloansServer) VerifyAndDownloadCkyc(context.Context, *VerifyAndDownloadCkycRequest) (*VerifyAndDownloadCkycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyAndDownloadCkyc not implemented")
}
func (UnimplementedLiquiloansServer) GetApplicantStatus(context.Context, *GetApplicantStatusRequest) (*GetApplicantStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicantStatus not implemented")
}
func (UnimplementedLiquiloansServer) GetRepaymentSchedule(context.Context, *GetRepaymentScheduleRequest) (*GetRepaymentScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRepaymentSchedule not implemented")
}
func (UnimplementedLiquiloansServer) UploadDocument(context.Context, *UploadDocumentRequest) (*UploadDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadDocument not implemented")
}
func (UnimplementedLiquiloansServer) SaveCollection(context.Context, *SaveCollectionRequest) (*SaveCollectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCollection not implemented")
}
func (UnimplementedLiquiloansServer) HashGenerationForOkyc(context.Context, *HashGenerationForOkycRequest) (*HashGenerationForOkycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HashGenerationForOkyc not implemented")
}
func (UnimplementedLiquiloansServer) CaptchaGenerationForOkyc(context.Context, *CaptchaGenerationForOkycRequest) (*CaptchaGenerationForOkycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CaptchaGenerationForOkyc not implemented")
}
func (UnimplementedLiquiloansServer) GenerateOtpForOkyc(context.Context, *GenerateOtpForOkycRequest) (*GenerateOtpForOkycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOtpForOkyc not implemented")
}
func (UnimplementedLiquiloansServer) ValidateOtpForOkyc(context.Context, *ValidateOtpForOkycRequest) (*ValidateOtpForOkycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateOtpForOkyc not implemented")
}
func (UnimplementedLiquiloansServer) UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLead not implemented")
}
func (UnimplementedLiquiloansServer) CancelLead(context.Context, *CancelLeadRequest) (*CancelLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelLead not implemented")
}
func (UnimplementedLiquiloansServer) ForeClosureDetails(context.Context, *ForeClosureDetailsRequest) (*ForeClosureDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForeClosureDetails not implemented")
}
func (UnimplementedLiquiloansServer) UpdateApplicantUdf(context.Context, *UpdateApplicantUdfRequest) (*UpdateApplicantUdfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateApplicantUdf not implemented")
}
func (UnimplementedLiquiloansServer) CreateRepaymentSchedule(context.Context, *CreateRepaymentScheduleRequest) (*CreateRepaymentScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRepaymentSchedule not implemented")
}
func (UnimplementedLiquiloansServer) SaveCharges(context.Context, *SaveChargesRequest) (*SaveChargesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCharges not implemented")
}
func (UnimplementedLiquiloansServer) GetApplicationSoa(context.Context, *GetApplicationSoaRequest) (*GetApplicationSoaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicationSoa not implemented")
}

// UnsafeLiquiloansServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LiquiloansServer will
// result in compilation errors.
type UnsafeLiquiloansServer interface {
	mustEmbedUnimplementedLiquiloansServer()
}

func RegisterLiquiloansServer(s grpc.ServiceRegistrar, srv LiquiloansServer) {
	s.RegisterService(&Liquiloans_ServiceDesc, srv)
}

func _Liquiloans_GetCreditLineSchemes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditLineSchemesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetCreditLineSchemes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetCreditLineSchemes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetCreditLineSchemes(ctx, req.(*GetCreditLineSchemesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_AddPersonalDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPersonalDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).AddPersonalDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_AddPersonalDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).AddPersonalDetails(ctx, req.(*AddPersonalDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_AddBankingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBankingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).AddBankingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_AddBankingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).AddBankingDetails(ctx, req.(*AddBankingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_AddAddressDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAddressDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).AddAddressDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_AddAddressDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).AddAddressDetails(ctx, req.(*AddAddressDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_AddEmploymentDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEmploymentDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).AddEmploymentDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_AddEmploymentDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).AddEmploymentDetails(ctx, req.(*AddEmploymentDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_ApplicantLookup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicantLookupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).ApplicantLookup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_ApplicantLookup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).ApplicantLookup(ctx, req.(*ApplicantLookupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetMandateLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetMandateLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetMandateLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetMandateLink(ctx, req.(*GetMandateLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetMandateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetMandateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetMandateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetMandateStatus(ctx, req.(*GetMandateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_MakeDrawdown_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeDrawdownRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).MakeDrawdown(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_MakeDrawdown_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).MakeDrawdown(ctx, req.(*MakeDrawdownRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetPdfAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPdfAgreementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetPdfAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetPdfAgreement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetPdfAgreement(ctx, req.(*GetPdfAgreementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_SendBorrowerAgreementOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendBorrowerAgreementOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).SendBorrowerAgreementOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_SendBorrowerAgreementOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).SendBorrowerAgreementOtp(ctx, req.(*SendBorrowerAgreementOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_VerifyBorrowerAgreementOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyBorrowerAgreementOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).VerifyBorrowerAgreementOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_VerifyBorrowerAgreementOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).VerifyBorrowerAgreementOtp(ctx, req.(*VerifyBorrowerAgreementOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetLoanStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetLoanStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetLoanStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetLoanStatus(ctx, req.(*GetLoanStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_VerifyAndDownloadCkyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyAndDownloadCkycRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).VerifyAndDownloadCkyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_VerifyAndDownloadCkyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).VerifyAndDownloadCkyc(ctx, req.(*VerifyAndDownloadCkycRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetApplicantStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicantStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetApplicantStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetApplicantStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetApplicantStatus(ctx, req.(*GetApplicantStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetRepaymentSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRepaymentScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetRepaymentSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetRepaymentSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetRepaymentSchedule(ctx, req.(*GetRepaymentScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_UploadDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).UploadDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_UploadDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).UploadDocument(ctx, req.(*UploadDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_SaveCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveCollectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).SaveCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_SaveCollection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).SaveCollection(ctx, req.(*SaveCollectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_HashGenerationForOkyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HashGenerationForOkycRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).HashGenerationForOkyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_HashGenerationForOkyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).HashGenerationForOkyc(ctx, req.(*HashGenerationForOkycRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_CaptchaGenerationForOkyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CaptchaGenerationForOkycRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).CaptchaGenerationForOkyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_CaptchaGenerationForOkyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).CaptchaGenerationForOkyc(ctx, req.(*CaptchaGenerationForOkycRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GenerateOtpForOkyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateOtpForOkycRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GenerateOtpForOkyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GenerateOtpForOkyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GenerateOtpForOkyc(ctx, req.(*GenerateOtpForOkycRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_ValidateOtpForOkyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateOtpForOkycRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).ValidateOtpForOkyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_ValidateOtpForOkyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).ValidateOtpForOkyc(ctx, req.(*ValidateOtpForOkycRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_UpdateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).UpdateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_UpdateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).UpdateLead(ctx, req.(*UpdateLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_CancelLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).CancelLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_CancelLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).CancelLead(ctx, req.(*CancelLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_ForeClosureDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForeClosureDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).ForeClosureDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_ForeClosureDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).ForeClosureDetails(ctx, req.(*ForeClosureDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_UpdateApplicantUdf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateApplicantUdfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).UpdateApplicantUdf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_UpdateApplicantUdf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).UpdateApplicantUdf(ctx, req.(*UpdateApplicantUdfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_CreateRepaymentSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRepaymentScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).CreateRepaymentSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_CreateRepaymentSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).CreateRepaymentSchedule(ctx, req.(*CreateRepaymentScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_SaveCharges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveChargesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).SaveCharges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_SaveCharges_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).SaveCharges(ctx, req.(*SaveChargesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetApplicationSoa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationSoaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetApplicationSoa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetApplicationSoa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetApplicationSoa(ctx, req.(*GetApplicationSoaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Liquiloans_ServiceDesc is the grpc.ServiceDesc for Liquiloans service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Liquiloans_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans",
	HandlerType: (*LiquiloansServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCreditLineSchemes",
			Handler:    _Liquiloans_GetCreditLineSchemes_Handler,
		},
		{
			MethodName: "AddPersonalDetails",
			Handler:    _Liquiloans_AddPersonalDetails_Handler,
		},
		{
			MethodName: "AddBankingDetails",
			Handler:    _Liquiloans_AddBankingDetails_Handler,
		},
		{
			MethodName: "AddAddressDetails",
			Handler:    _Liquiloans_AddAddressDetails_Handler,
		},
		{
			MethodName: "AddEmploymentDetails",
			Handler:    _Liquiloans_AddEmploymentDetails_Handler,
		},
		{
			MethodName: "ApplicantLookup",
			Handler:    _Liquiloans_ApplicantLookup_Handler,
		},
		{
			MethodName: "GetMandateLink",
			Handler:    _Liquiloans_GetMandateLink_Handler,
		},
		{
			MethodName: "GetMandateStatus",
			Handler:    _Liquiloans_GetMandateStatus_Handler,
		},
		{
			MethodName: "MakeDrawdown",
			Handler:    _Liquiloans_MakeDrawdown_Handler,
		},
		{
			MethodName: "GetPdfAgreement",
			Handler:    _Liquiloans_GetPdfAgreement_Handler,
		},
		{
			MethodName: "SendBorrowerAgreementOtp",
			Handler:    _Liquiloans_SendBorrowerAgreementOtp_Handler,
		},
		{
			MethodName: "VerifyBorrowerAgreementOtp",
			Handler:    _Liquiloans_VerifyBorrowerAgreementOtp_Handler,
		},
		{
			MethodName: "GetLoanStatus",
			Handler:    _Liquiloans_GetLoanStatus_Handler,
		},
		{
			MethodName: "VerifyAndDownloadCkyc",
			Handler:    _Liquiloans_VerifyAndDownloadCkyc_Handler,
		},
		{
			MethodName: "GetApplicantStatus",
			Handler:    _Liquiloans_GetApplicantStatus_Handler,
		},
		{
			MethodName: "GetRepaymentSchedule",
			Handler:    _Liquiloans_GetRepaymentSchedule_Handler,
		},
		{
			MethodName: "UploadDocument",
			Handler:    _Liquiloans_UploadDocument_Handler,
		},
		{
			MethodName: "SaveCollection",
			Handler:    _Liquiloans_SaveCollection_Handler,
		},
		{
			MethodName: "HashGenerationForOkyc",
			Handler:    _Liquiloans_HashGenerationForOkyc_Handler,
		},
		{
			MethodName: "CaptchaGenerationForOkyc",
			Handler:    _Liquiloans_CaptchaGenerationForOkyc_Handler,
		},
		{
			MethodName: "GenerateOtpForOkyc",
			Handler:    _Liquiloans_GenerateOtpForOkyc_Handler,
		},
		{
			MethodName: "ValidateOtpForOkyc",
			Handler:    _Liquiloans_ValidateOtpForOkyc_Handler,
		},
		{
			MethodName: "UpdateLead",
			Handler:    _Liquiloans_UpdateLead_Handler,
		},
		{
			MethodName: "CancelLead",
			Handler:    _Liquiloans_CancelLead_Handler,
		},
		{
			MethodName: "ForeClosureDetails",
			Handler:    _Liquiloans_ForeClosureDetails_Handler,
		},
		{
			MethodName: "UpdateApplicantUdf",
			Handler:    _Liquiloans_UpdateApplicantUdf_Handler,
		},
		{
			MethodName: "CreateRepaymentSchedule",
			Handler:    _Liquiloans_CreateRepaymentSchedule_Handler,
		},
		{
			MethodName: "SaveCharges",
			Handler:    _Liquiloans_SaveCharges_Handler,
		},
		{
			MethodName: "GetApplicationSoa",
			Handler:    _Liquiloans_GetApplicationSoa_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/lending/preapprovedloan/liquiloans/service.proto",
}
