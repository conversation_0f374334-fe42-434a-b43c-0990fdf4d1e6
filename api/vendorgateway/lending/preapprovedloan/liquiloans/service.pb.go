// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/lending/preapprovedloan/liquiloans/service.proto

package liquiloans

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	preapprovedloan "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddDetailsResponse_Status int32

const (
	AddDetailsResponse_OK AddDetailsResponse_Status = 0
	// e.g.: "{"monthly_income":["Monthly income should be between Rs 2,000 to Rs 1,00,00,000"]}" for AddEmploymentDetails API
	AddDetailsResponse_INCOME_VALIDATION_ERROR AddDetailsResponse_Status = 101
)

// Enum value maps for AddDetailsResponse_Status.
var (
	AddDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "INCOME_VALIDATION_ERROR",
	}
	AddDetailsResponse_Status_value = map[string]int32{
		"OK":                      0,
		"INCOME_VALIDATION_ERROR": 101,
	}
)

func (x AddDetailsResponse_Status) Enum() *AddDetailsResponse_Status {
	p := new(AddDetailsResponse_Status)
	*p = x
	return p
}

func (x AddDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes[0].Descriptor()
}

func (AddDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes[0]
}

func (x AddDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddDetailsResponse_Status.Descriptor instead.
func (AddDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{20, 0}
}

type GetMandateLinkResponse_Status int32

const (
	GetMandateLinkResponse_OK GetMandateLinkResponse_Status = 0
	// LL API returns below response if mandate creation limit is exhausted for calling mandate link API for that particular customer in the given program
	// {"status":false,"message":"Cannot recreate mandate, limit exhausted.","data":[],"code":400,"checksum":null}
	GetMandateLinkResponse_MANDATE_CREATION_LIMIT_EXHAUSTED_FOR_CUSTOMER GetMandateLinkResponse_Status = 101
)

// Enum value maps for GetMandateLinkResponse_Status.
var (
	GetMandateLinkResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "MANDATE_CREATION_LIMIT_EXHAUSTED_FOR_CUSTOMER",
	}
	GetMandateLinkResponse_Status_value = map[string]int32{
		"OK": 0,
		"MANDATE_CREATION_LIMIT_EXHAUSTED_FOR_CUSTOMER": 101,
	}
)

func (x GetMandateLinkResponse_Status) Enum() *GetMandateLinkResponse_Status {
	p := new(GetMandateLinkResponse_Status)
	*p = x
	return p
}

func (x GetMandateLinkResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMandateLinkResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes[1].Descriptor()
}

func (GetMandateLinkResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes[1]
}

func (x GetMandateLinkResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMandateLinkResponse_Status.Descriptor instead.
func (GetMandateLinkResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{24, 0}
}

type GetMandateStatusResponse_Status int32

const (
	GetMandateStatusResponse_OK GetMandateStatusResponse_Status = 0
	// LL API returns below response if we call mandate status API first even before calling get mandate link API
	// Ref: https://epifi.slack.com/archives/C04HAQ9VC5T/p1711524336675849
	// {"status":false,"message":"Improper Log Found","data":[],"code":400,"checksum":null}
	// Not returning grpc status NotFound here since it can be mapped to actual http 404 status code
	// but LL is returning http 400 status code for this error
	GetMandateStatusResponse_MANDATE_RECORD_NOT_FOUND GetMandateStatusResponse_Status = 101
)

// Enum value maps for GetMandateStatusResponse_Status.
var (
	GetMandateStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "MANDATE_RECORD_NOT_FOUND",
	}
	GetMandateStatusResponse_Status_value = map[string]int32{
		"OK":                       0,
		"MANDATE_RECORD_NOT_FOUND": 101,
	}
)

func (x GetMandateStatusResponse_Status) Enum() *GetMandateStatusResponse_Status {
	p := new(GetMandateStatusResponse_Status)
	*p = x
	return p
}

func (x GetMandateStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMandateStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes[2].Descriptor()
}

func (GetMandateStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes[2]
}

func (x GetMandateStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMandateStatusResponse_Status.Descriptor instead.
func (GetMandateStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{26, 0}
}

type SaveChargesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanProgram LoanProgram                  `protobuf:"varint,2,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	LoanId      string                       `protobuf:"bytes,3,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// date to which the charge is applied
	// if charge date is between EMI N's due date and EMI N+1's due date, then the charge will be applied to EMI N
	// e.g. if charge date is 2021-08-18 and EMI 3's due date is 2021-08-10, EMI 4's due date is 2021-09-10, then the charge will be applied to EMI 3
	Date *date.Date `protobuf:"bytes,4,opt,name=date,proto3" json:"date,omitempty"`
	// charge amount
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// unique identifier for the charge on a loan
	// idempotency will be checked on this field
	ChargeId      string        `protobuf:"bytes,6,opt,name=charge_id,json=chargeId,proto3" json:"charge_id,omitempty"`
	Remarks       string        `protobuf:"bytes,7,opt,name=remarks,proto3" json:"remarks,omitempty"`
	SchemeVersion SchemeVersion `protobuf:"varint,8,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *SaveChargesRequest) Reset() {
	*x = SaveChargesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveChargesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveChargesRequest) ProtoMessage() {}

func (x *SaveChargesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveChargesRequest.ProtoReflect.Descriptor instead.
func (*SaveChargesRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{0}
}

func (x *SaveChargesRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaveChargesRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *SaveChargesRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *SaveChargesRequest) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *SaveChargesRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *SaveChargesRequest) GetChargeId() string {
	if x != nil {
		return x.ChargeId
	}
	return ""
}

func (x *SaveChargesRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *SaveChargesRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type SaveChargesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SaveChargesResponse) Reset() {
	*x = SaveChargesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveChargesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveChargesResponse) ProtoMessage() {}

func (x *SaveChargesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveChargesResponse.ProtoReflect.Descriptor instead.
func (*SaveChargesResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{1}
}

func (x *SaveChargesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateRepaymentScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header             *vendorgateway.RequestHeader               `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId             string                                     `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	LoanProgram        LoanProgram                                `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	RepaymentFrequency TenureFrequency                            `protobuf:"varint,4,opt,name=repayment_frequency,json=repaymentFrequency,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency" json:"repayment_frequency,omitempty"`
	EmiTenure          int32                                      `protobuf:"varint,5,opt,name=emi_tenure,json=emiTenure,proto3" json:"emi_tenure,omitempty"`
	EmiStartDate       *date.Date                                 `protobuf:"bytes,6,opt,name=emi_start_date,json=emiStartDate,proto3" json:"emi_start_date,omitempty"`
	Schedules          []*CreateRepaymentScheduleRequest_Schedule `protobuf:"bytes,7,rep,name=schedules,proto3" json:"schedules,omitempty"`
	SchemeVersion      SchemeVersion                              `protobuf:"varint,8,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *CreateRepaymentScheduleRequest) Reset() {
	*x = CreateRepaymentScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleRequest) ProtoMessage() {}

func (x *CreateRepaymentScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleRequest.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateRepaymentScheduleRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *CreateRepaymentScheduleRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *CreateRepaymentScheduleRequest) GetRepaymentFrequency() TenureFrequency {
	if x != nil {
		return x.RepaymentFrequency
	}
	return TenureFrequency_TENURE_FREQUENCY_UNSPECIFIED
}

func (x *CreateRepaymentScheduleRequest) GetEmiTenure() int32 {
	if x != nil {
		return x.EmiTenure
	}
	return 0
}

func (x *CreateRepaymentScheduleRequest) GetEmiStartDate() *date.Date {
	if x != nil {
		return x.EmiStartDate
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest) GetSchedules() []*CreateRepaymentScheduleRequest_Schedule {
	if x != nil {
		return x.Schedules
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

// returns the below statuses for known errors
// AlreadyExists: If the repayment schedule is already created for the given loan_id
// FailedPreCondition: If the loan is already disbursed
type CreateRepaymentScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateRepaymentScheduleResponse) Reset() {
	*x = CreateRepaymentScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleResponse) ProtoMessage() {}

func (x *CreateRepaymentScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleResponse.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateRepaymentScheduleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateApplicantUdfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header           *vendorgateway.RequestHeader     `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId      string                           `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	LoanProgram      LoanProgram                      `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	MonthlyIncome    *money.Money                     `protobuf:"bytes,4,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	IncomeDataSource preapprovedloan.IncomeDataSource `protobuf:"varint,5,opt,name=income_data_source,json=incomeDataSource,proto3,enum=vendorgateway.lending.preapprovedloan.IncomeDataSource" json:"income_data_source,omitempty"`
	SchemeVersion    SchemeVersion                    `protobuf:"varint,6,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *UpdateApplicantUdfRequest) Reset() {
	*x = UpdateApplicantUdfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicantUdfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicantUdfRequest) ProtoMessage() {}

func (x *UpdateApplicantUdfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicantUdfRequest.ProtoReflect.Descriptor instead.
func (*UpdateApplicantUdfRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateApplicantUdfRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateApplicantUdfRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *UpdateApplicantUdfRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *UpdateApplicantUdfRequest) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *UpdateApplicantUdfRequest) GetIncomeDataSource() preapprovedloan.IncomeDataSource {
	if x != nil {
		return x.IncomeDataSource
	}
	return preapprovedloan.IncomeDataSource(0)
}

func (x *UpdateApplicantUdfRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type UpdateApplicantUdfResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateApplicantUdfResponse) Reset() {
	*x = UpdateApplicantUdfResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicantUdfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicantUdfResponse) ProtoMessage() {}

func (x *UpdateApplicantUdfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicantUdfResponse.ProtoReflect.Descriptor instead.
func (*UpdateApplicantUdfResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateApplicantUdfResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ForeClosureDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicationId int64                        `protobuf:"varint,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,4,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *ForeClosureDetailsRequest) Reset() {
	*x = ForeClosureDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeClosureDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeClosureDetailsRequest) ProtoMessage() {}

func (x *ForeClosureDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeClosureDetailsRequest.ProtoReflect.Descriptor instead.
func (*ForeClosureDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{6}
}

func (x *ForeClosureDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ForeClosureDetailsRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *ForeClosureDetailsRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *ForeClosureDetailsRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type ForeClosureDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *ForeClosureDetailsResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	FailureReason string                           `protobuf:"bytes,4,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *ForeClosureDetailsResponse) Reset() {
	*x = ForeClosureDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeClosureDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeClosureDetailsResponse) ProtoMessage() {}

func (x *ForeClosureDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeClosureDetailsResponse.ProtoReflect.Descriptor instead.
func (*ForeClosureDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{7}
}

func (x *ForeClosureDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ForeClosureDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ForeClosureDetailsResponse) GetData() *ForeClosureDetailsResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ForeClosureDetailsResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type CancelLeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicationId string                       `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,3,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,4,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *CancelLeadRequest) Reset() {
	*x = CancelLeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadRequest) ProtoMessage() {}

func (x *CancelLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadRequest.ProtoReflect.Descriptor instead.
func (*CancelLeadRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{8}
}

func (x *CancelLeadRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CancelLeadRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CancelLeadRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *CancelLeadRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *CancelLeadRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type CancelLeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *CancelLeadResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	FailureReason string                   `protobuf:"bytes,4,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *CancelLeadResponse) Reset() {
	*x = CancelLeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadResponse) ProtoMessage() {}

func (x *CancelLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadResponse.ProtoReflect.Descriptor instead.
func (*CancelLeadResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{9}
}

func (x *CancelLeadResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CancelLeadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CancelLeadResponse) GetData() *CancelLeadResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CancelLeadResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type UpdateLeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicationId string                       `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Amount        *money.Money                 `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Urn           string                       `protobuf:"bytes,4,opt,name=urn,proto3" json:"urn,omitempty"`
	SchemeDetails *UpdateLeadSchemeDetails     `protobuf:"bytes,5,opt,name=scheme_details,json=schemeDetails,proto3" json:"scheme_details,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,6,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,7,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *UpdateLeadRequest) Reset() {
	*x = UpdateLeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest) ProtoMessage() {}

func (x *UpdateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateLeadRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateLeadRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UpdateLeadRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *UpdateLeadRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *UpdateLeadRequest) GetSchemeDetails() *UpdateLeadSchemeDetails {
	if x != nil {
		return x.SchemeDetails
	}
	return nil
}

func (x *UpdateLeadRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *UpdateLeadRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type UpdateLeadSchemeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstallmentFrequency TenureFrequency `protobuf:"varint,1,opt,name=installment_frequency,json=installmentFrequency,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency" json:"installment_frequency,omitempty"`
	// e.g: 36
	InstallmentTenure int32 `protobuf:"varint,2,opt,name=installment_tenure,json=installmentTenure,proto3" json:"installment_tenure,omitempty"`
	// e.g: 180
	ProcessingFees *money.Money `protobuf:"bytes,3,opt,name=processing_fees,json=processingFees,proto3" json:"processing_fees,omitempty"`
	// e.g: 0.43
	RoiPercentage                   float64      `protobuf:"fixed64,4,opt,name=roi_percentage,json=roiPercentage,proto3" json:"roi_percentage,omitempty"`
	InstallmentStartDate            *date.Date   `protobuf:"bytes,5,opt,name=installment_start_date,json=installmentStartDate,proto3" json:"installment_start_date,omitempty"`
	SubventionPercentage            float64      `protobuf:"fixed64,6,opt,name=subvention_percentage,json=subventionPercentage,proto3" json:"subvention_percentage,omitempty"`
	ProcessingFeesType              FeeType      `protobuf:"varint,7,opt,name=processing_fees_type,json=processingFeesType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.FeeType" json:"processing_fees_type,omitempty"`
	RoiType                         RoiType      `protobuf:"varint,8,opt,name=roi_type,json=roiType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.RoiType" json:"roi_type,omitempty"`
	RoiAppliedOn                    FeeAppliedOn `protobuf:"varint,9,opt,name=roi_applied_on,json=roiAppliedOn,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.FeeAppliedOn" json:"roi_applied_on,omitempty"`
	ProcessingFeesCustomerAppliedOn FeeAppliedOn `protobuf:"varint,10,opt,name=processing_fees_customer_applied_on,json=processingFeesCustomerAppliedOn,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.FeeAppliedOn" json:"processing_fees_customer_applied_on,omitempty"`
	// ??
	ProcessingFeesCustomerGst bool `protobuf:"varint,11,opt,name=processing_fees_customer_gst,json=processingFeesCustomerGst,proto3" json:"processing_fees_customer_gst,omitempty"`
	// ??
	RoiGst  bool    `protobuf:"varint,12,opt,name=roi_gst,json=roiGst,proto3" json:"roi_gst,omitempty"`
	GstType FeeType `protobuf:"varint,13,opt,name=gst_type,json=gstType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.FeeType" json:"gst_type,omitempty"`
	// e.g: 18
	GstValue float64 `protobuf:"fixed64,14,opt,name=gst_value,json=gstValue,proto3" json:"gst_value,omitempty"`
}

func (x *UpdateLeadSchemeDetails) Reset() {
	*x = UpdateLeadSchemeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadSchemeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadSchemeDetails) ProtoMessage() {}

func (x *UpdateLeadSchemeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadSchemeDetails.ProtoReflect.Descriptor instead.
func (*UpdateLeadSchemeDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateLeadSchemeDetails) GetInstallmentFrequency() TenureFrequency {
	if x != nil {
		return x.InstallmentFrequency
	}
	return TenureFrequency_TENURE_FREQUENCY_UNSPECIFIED
}

func (x *UpdateLeadSchemeDetails) GetInstallmentTenure() int32 {
	if x != nil {
		return x.InstallmentTenure
	}
	return 0
}

func (x *UpdateLeadSchemeDetails) GetProcessingFees() *money.Money {
	if x != nil {
		return x.ProcessingFees
	}
	return nil
}

func (x *UpdateLeadSchemeDetails) GetRoiPercentage() float64 {
	if x != nil {
		return x.RoiPercentage
	}
	return 0
}

func (x *UpdateLeadSchemeDetails) GetInstallmentStartDate() *date.Date {
	if x != nil {
		return x.InstallmentStartDate
	}
	return nil
}

func (x *UpdateLeadSchemeDetails) GetSubventionPercentage() float64 {
	if x != nil {
		return x.SubventionPercentage
	}
	return 0
}

func (x *UpdateLeadSchemeDetails) GetProcessingFeesType() FeeType {
	if x != nil {
		return x.ProcessingFeesType
	}
	return FeeType_FEE_TYPE_UNSPECIFIED
}

func (x *UpdateLeadSchemeDetails) GetRoiType() RoiType {
	if x != nil {
		return x.RoiType
	}
	return RoiType_ROI_TYPE_UNSPECIFIED
}

func (x *UpdateLeadSchemeDetails) GetRoiAppliedOn() FeeAppliedOn {
	if x != nil {
		return x.RoiAppliedOn
	}
	return FeeAppliedOn_FEE_APPLIED_ON_UNSPECIFIED
}

func (x *UpdateLeadSchemeDetails) GetProcessingFeesCustomerAppliedOn() FeeAppliedOn {
	if x != nil {
		return x.ProcessingFeesCustomerAppliedOn
	}
	return FeeAppliedOn_FEE_APPLIED_ON_UNSPECIFIED
}

func (x *UpdateLeadSchemeDetails) GetProcessingFeesCustomerGst() bool {
	if x != nil {
		return x.ProcessingFeesCustomerGst
	}
	return false
}

func (x *UpdateLeadSchemeDetails) GetRoiGst() bool {
	if x != nil {
		return x.RoiGst
	}
	return false
}

func (x *UpdateLeadSchemeDetails) GetGstType() FeeType {
	if x != nil {
		return x.GstType
	}
	return FeeType_FEE_TYPE_UNSPECIFIED
}

func (x *UpdateLeadSchemeDetails) GetGstValue() float64 {
	if x != nil {
		return x.GstValue
	}
	return 0
}

type UpdateLeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanId string      `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
}

func (x *UpdateLeadResponse) Reset() {
	*x = UpdateLeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadResponse) ProtoMessage() {}

func (x *UpdateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadResponse.ProtoReflect.Descriptor instead.
func (*UpdateLeadResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateLeadResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateLeadResponse) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

type GetCreditLineSchemesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendor gateway APIs
	// Denotes the vendor that is supposed to process this request
	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,4,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetCreditLineSchemesRequest) Reset() {
	*x = GetCreditLineSchemesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineSchemesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineSchemesRequest) ProtoMessage() {}

func (x *GetCreditLineSchemesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineSchemesRequest.ProtoReflect.Descriptor instead.
func (*GetCreditLineSchemesRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetCreditLineSchemesRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetCreditLineSchemesRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetCreditLineSchemesRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetCreditLineSchemesRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetCreditLineSchemesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status                                      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CreditLineDetails *GetCreditLineSchemesResponse_CreditLineDetails  `protobuf:"bytes,2,opt,name=credit_line_details,json=creditLineDetails,proto3" json:"credit_line_details,omitempty"`
	CreditLineSchemes []*GetCreditLineSchemesResponse_CreditLineScheme `protobuf:"bytes,3,rep,name=credit_line_schemes,json=creditLineSchemes,proto3" json:"credit_line_schemes,omitempty"`
}

func (x *GetCreditLineSchemesResponse) Reset() {
	*x = GetCreditLineSchemesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineSchemesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineSchemesResponse) ProtoMessage() {}

func (x *GetCreditLineSchemesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineSchemesResponse.ProtoReflect.Descriptor instead.
func (*GetCreditLineSchemesResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetCreditLineSchemesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditLineSchemesResponse) GetCreditLineDetails() *GetCreditLineSchemesResponse_CreditLineDetails {
	if x != nil {
		return x.CreditLineDetails
	}
	return nil
}

func (x *GetCreditLineSchemesResponse) GetCreditLineSchemes() []*GetCreditLineSchemesResponse_CreditLineScheme {
	if x != nil {
		return x.CreditLineSchemes
	}
	return nil
}

type AddPersonalDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Name          *common.Name                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                       `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ContactNumber *common.PhoneNumber          `protobuf:"bytes,4,opt,name=contact_number,json=contactNumber,proto3" json:"contact_number,omitempty"`
	Pan           string                       `protobuf:"bytes,5,opt,name=pan,proto3" json:"pan,omitempty"`
	Gender        typesv2.Gender               `protobuf:"varint,6,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	Dob           *date.Date                   `protobuf:"bytes,7,opt,name=dob,proto3" json:"dob,omitempty"`
	Urn           string                       `protobuf:"bytes,8,opt,name=urn,proto3" json:"urn,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,9,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	// send only for AA based income
	MonthlyIncome    *money.Money                     `protobuf:"bytes,10,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	IncomeDataSource preapprovedloan.IncomeDataSource `protobuf:"varint,11,opt,name=income_data_source,json=incomeDataSource,proto3,enum=vendorgateway.lending.preapprovedloan.IncomeDataSource" json:"income_data_source,omitempty"`
	SchemeVersion    SchemeVersion                    `protobuf:"varint,12,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *AddPersonalDetailsRequest) Reset() {
	*x = AddPersonalDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPersonalDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPersonalDetailsRequest) ProtoMessage() {}

func (x *AddPersonalDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPersonalDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddPersonalDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{15}
}

func (x *AddPersonalDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AddPersonalDetailsRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *AddPersonalDetailsRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetContactNumber() *common.PhoneNumber {
	if x != nil {
		return x.ContactNumber
	}
	return nil
}

func (x *AddPersonalDetailsRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *AddPersonalDetailsRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *AddPersonalDetailsRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *AddPersonalDetailsRequest) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *AddPersonalDetailsRequest) GetIncomeDataSource() preapprovedloan.IncomeDataSource {
	if x != nil {
		return x.IncomeDataSource
	}
	return preapprovedloan.IncomeDataSource(0)
}

func (x *AddPersonalDetailsRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type AddPersonalDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Data          *AddPersonalDetailsResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	FailureReason string                           `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *AddPersonalDetailsResponse) Reset() {
	*x = AddPersonalDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPersonalDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPersonalDetailsResponse) ProtoMessage() {}

func (x *AddPersonalDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPersonalDetailsResponse.ProtoReflect.Descriptor instead.
func (*AddPersonalDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{16}
}

func (x *AddPersonalDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AddPersonalDetailsResponse) GetData() *AddPersonalDetailsResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AddPersonalDetailsResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type AddBankingDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header             *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId        string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	BankAccountDetails *typesv2.BankAccountDetails  `protobuf:"bytes,3,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
	LoanProgram        LoanProgram                  `protobuf:"varint,4,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion      SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *AddBankingDetailsRequest) Reset() {
	*x = AddBankingDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBankingDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBankingDetailsRequest) ProtoMessage() {}

func (x *AddBankingDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBankingDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddBankingDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{17}
}

func (x *AddBankingDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AddBankingDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetBankAccountDetails() *typesv2.BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

func (x *AddBankingDetailsRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *AddBankingDetailsRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type AddAddressDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Address       *typesv2.PostalAddress       `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	AddressType   typesv2.AddressType          `protobuf:"varint,9,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,10,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,11,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *AddAddressDetailsRequest) Reset() {
	*x = AddAddressDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAddressDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAddressDetailsRequest) ProtoMessage() {}

func (x *AddAddressDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAddressDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddAddressDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{18}
}

func (x *AddAddressDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AddAddressDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AddAddressDetailsRequest) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

func (x *AddAddressDetailsRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *AddAddressDetailsRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type AddEmploymentDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header           *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId      string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Occupation       typesv2.EmploymentType       `protobuf:"varint,3,opt,name=occupation,proto3,enum=api.typesv2.EmploymentType" json:"occupation,omitempty"`
	OrganizationName string                       `protobuf:"bytes,4,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	MonthlyIncome    *money.Money                 `protobuf:"bytes,5,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	WorkEmail        string                       `protobuf:"bytes,6,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	LoanProgram      LoanProgram                  `protobuf:"varint,7,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion    SchemeVersion                `protobuf:"varint,8,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *AddEmploymentDetailsRequest) Reset() {
	*x = AddEmploymentDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddEmploymentDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEmploymentDetailsRequest) ProtoMessage() {}

func (x *AddEmploymentDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEmploymentDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddEmploymentDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{19}
}

func (x *AddEmploymentDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AddEmploymentDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetOccupation() typesv2.EmploymentType {
	if x != nil {
		return x.Occupation
	}
	return typesv2.EmploymentType(0)
}

func (x *AddEmploymentDetailsRequest) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *AddEmploymentDetailsRequest) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *AddEmploymentDetailsRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

// common response to be used for multiple APIs as Liquiloans returns same response structure for multiple APIs.
type AddDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FailureReason string      `protobuf:"bytes,2,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *AddDetailsResponse) Reset() {
	*x = AddDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDetailsResponse) ProtoMessage() {}

func (x *AddDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDetailsResponse.ProtoReflect.Descriptor instead.
func (*AddDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{20}
}

func (x *AddDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AddDetailsResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type ApplicantLookupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Pan           string                       `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,4,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *ApplicantLookupRequest) Reset() {
	*x = ApplicantLookupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupRequest) ProtoMessage() {}

func (x *ApplicantLookupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupRequest.ProtoReflect.Descriptor instead.
func (*ApplicantLookupRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{21}
}

func (x *ApplicantLookupRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ApplicantLookupRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ApplicantLookupRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *ApplicantLookupRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type ApplicantLookupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Data          *ApplicantLookupResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	FailureReason string                        `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *ApplicantLookupResponse) Reset() {
	*x = ApplicantLookupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse) ProtoMessage() {}

func (x *ApplicantLookupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{22}
}

func (x *ApplicantLookupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ApplicantLookupResponse) GetData() *ApplicantLookupResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ApplicantLookupResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type GetMandateLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	RecreateUrl   bool                         `protobuf:"varint,4,opt,name=recreate_url,json=recreateUrl,proto3" json:"recreate_url,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetMandateLinkRequest) Reset() {
	*x = GetMandateLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateLinkRequest) ProtoMessage() {}

func (x *GetMandateLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateLinkRequest.ProtoReflect.Descriptor instead.
func (*GetMandateLinkRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetMandateLinkRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetMandateLinkRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetMandateLinkRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetMandateLinkRequest) GetRecreateUrl() bool {
	if x != nil {
		return x.RecreateUrl
	}
	return false
}

func (x *GetMandateLinkRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetMandateLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MandateUrl string      `protobuf:"bytes,2,opt,name=mandate_url,json=mandateUrl,proto3" json:"mandate_url,omitempty"`
	MandateId  string      `protobuf:"bytes,3,opt,name=mandate_id,json=mandateId,proto3" json:"mandate_id,omitempty"`
}

func (x *GetMandateLinkResponse) Reset() {
	*x = GetMandateLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateLinkResponse) ProtoMessage() {}

func (x *GetMandateLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateLinkResponse.ProtoReflect.Descriptor instead.
func (*GetMandateLinkResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetMandateLinkResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMandateLinkResponse) GetMandateUrl() string {
	if x != nil {
		return x.MandateUrl
	}
	return ""
}

func (x *GetMandateLinkResponse) GetMandateId() string {
	if x != nil {
		return x.MandateId
	}
	return ""
}

type GetMandateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,9,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetMandateStatusRequest) Reset() {
	*x = GetMandateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateStatusRequest) ProtoMessage() {}

func (x *GetMandateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateStatusRequest.ProtoReflect.Descriptor instead.
func (*GetMandateStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetMandateStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetMandateStatusRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetMandateStatusRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetMandateStatusRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetMandateStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MandateStatus MandateStatus `protobuf:"varint,2,opt,name=mandate_status,json=mandateStatus,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.MandateStatus" json:"mandate_status,omitempty"`
}

func (x *GetMandateStatusResponse) Reset() {
	*x = GetMandateStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateStatusResponse) ProtoMessage() {}

func (x *GetMandateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateStatusResponse.ProtoReflect.Descriptor instead.
func (*GetMandateStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetMandateStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMandateStatusResponse) GetMandateStatus() MandateStatus {
	if x != nil {
		return x.MandateStatus
	}
	return MandateStatus_MANDATE_STATUS_UNSPECIFIED
}

type MakeDrawdownRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader    `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                          `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Tenure        *MakeDrawdownRequest_LoanTenure `protobuf:"bytes,3,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Amount        *money.Money                    `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Urn           string                          `protobuf:"bytes,5,opt,name=urn,proto3" json:"urn,omitempty"`
	LoanProgram   LoanProgram                     `protobuf:"varint,6,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                   `protobuf:"varint,7,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *MakeDrawdownRequest) Reset() {
	*x = MakeDrawdownRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDrawdownRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDrawdownRequest) ProtoMessage() {}

func (x *MakeDrawdownRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDrawdownRequest.ProtoReflect.Descriptor instead.
func (*MakeDrawdownRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{27}
}

func (x *MakeDrawdownRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MakeDrawdownRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *MakeDrawdownRequest) GetTenure() *MakeDrawdownRequest_LoanTenure {
	if x != nil {
		return x.Tenure
	}
	return nil
}

func (x *MakeDrawdownRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *MakeDrawdownRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *MakeDrawdownRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *MakeDrawdownRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type MakeDrawdownResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanId        string      `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	ApplicantId   string      `protobuf:"bytes,3,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Urn           string      `protobuf:"bytes,4,opt,name=urn,proto3" json:"urn,omitempty"`
	FailureReason string      `protobuf:"bytes,5,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *MakeDrawdownResponse) Reset() {
	*x = MakeDrawdownResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDrawdownResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDrawdownResponse) ProtoMessage() {}

func (x *MakeDrawdownResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDrawdownResponse.ProtoReflect.Descriptor instead.
func (*MakeDrawdownResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{28}
}

func (x *MakeDrawdownResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *MakeDrawdownResponse) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *MakeDrawdownResponse) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *MakeDrawdownResponse) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *MakeDrawdownResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type GetPdfAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ApplicationId string                       `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,4,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetPdfAgreementRequest) Reset() {
	*x = GetPdfAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdfAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdfAgreementRequest) ProtoMessage() {}

func (x *GetPdfAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdfAgreementRequest.ProtoReflect.Descriptor instead.
func (*GetPdfAgreementRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetPdfAgreementRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetPdfAgreementRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetPdfAgreementRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetPdfAgreementRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetPdfAgreementRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetPdfAgreementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PdfBase64Text string      `protobuf:"bytes,2,opt,name=pdf_base64_text,json=pdfBase64Text,proto3" json:"pdf_base64_text,omitempty"`
	DocId         string      `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
}

func (x *GetPdfAgreementResponse) Reset() {
	*x = GetPdfAgreementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdfAgreementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdfAgreementResponse) ProtoMessage() {}

func (x *GetPdfAgreementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdfAgreementResponse.ProtoReflect.Descriptor instead.
func (*GetPdfAgreementResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetPdfAgreementResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPdfAgreementResponse) GetPdfBase64Text() string {
	if x != nil {
		return x.PdfBase64Text
	}
	return ""
}

func (x *GetPdfAgreementResponse) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

type SendBorrowerAgreementOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ApplicationId string                       `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,4,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *SendBorrowerAgreementOtpRequest) Reset() {
	*x = SendBorrowerAgreementOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendBorrowerAgreementOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBorrowerAgreementOtpRequest) ProtoMessage() {}

func (x *SendBorrowerAgreementOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBorrowerAgreementOtpRequest.ProtoReflect.Descriptor instead.
func (*SendBorrowerAgreementOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{31}
}

func (x *SendBorrowerAgreementOtpRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SendBorrowerAgreementOtpRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *SendBorrowerAgreementOtpRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *SendBorrowerAgreementOtpRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *SendBorrowerAgreementOtpRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type SendBorrowerAgreementOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SendBorrowerAgreementOtpResponse) Reset() {
	*x = SendBorrowerAgreementOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendBorrowerAgreementOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBorrowerAgreementOtpResponse) ProtoMessage() {}

func (x *SendBorrowerAgreementOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBorrowerAgreementOtpResponse.ProtoReflect.Descriptor instead.
func (*SendBorrowerAgreementOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{32}
}

func (x *SendBorrowerAgreementOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type VerifyBorrowerAgreementOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ApplicationId string                       `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	DocId         string                       `protobuf:"bytes,4,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	Otp           string                       `protobuf:"bytes,5,opt,name=otp,proto3" json:"otp,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,6,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,7,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *VerifyBorrowerAgreementOtpRequest) Reset() {
	*x = VerifyBorrowerAgreementOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBorrowerAgreementOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBorrowerAgreementOtpRequest) ProtoMessage() {}

func (x *VerifyBorrowerAgreementOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBorrowerAgreementOtpRequest.ProtoReflect.Descriptor instead.
func (*VerifyBorrowerAgreementOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{33}
}

func (x *VerifyBorrowerAgreementOtpRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VerifyBorrowerAgreementOtpRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *VerifyBorrowerAgreementOtpRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type VerifyBorrowerAgreementOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// signed copy of the agreement pdf after OTP verificatin
	AgreementSignedCopy string `protobuf:"bytes,2,opt,name=agreement_signed_copy,json=agreementSignedCopy,proto3" json:"agreement_signed_copy,omitempty"`
	FailureReason       string `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *VerifyBorrowerAgreementOtpResponse) Reset() {
	*x = VerifyBorrowerAgreementOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBorrowerAgreementOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBorrowerAgreementOtpResponse) ProtoMessage() {}

func (x *VerifyBorrowerAgreementOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBorrowerAgreementOtpResponse.ProtoReflect.Descriptor instead.
func (*VerifyBorrowerAgreementOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{34}
}

func (x *VerifyBorrowerAgreementOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyBorrowerAgreementOtpResponse) GetAgreementSignedCopy() string {
	if x != nil {
		return x.AgreementSignedCopy
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type GetLoanStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicationId string                       `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Urn           string                       `protobuf:"bytes,3,opt,name=urn,proto3" json:"urn,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,4,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetLoanStatusRequest) Reset() {
	*x = GetLoanStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusRequest) ProtoMessage() {}

func (x *GetLoanStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusRequest.ProtoReflect.Descriptor instead.
func (*GetLoanStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetLoanStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetLoanStatusRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetLoanStatusRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *GetLoanStatusRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetLoanStatusRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetLoanStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status                       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanStatus          *GetLoanStatusResponse_LoanStatus `protobuf:"bytes,2,opt,name=loan_status,json=loanStatus,proto3" json:"loan_status,omitempty"`
	LastStatusTimestamp *timestamppb.Timestamp            `protobuf:"bytes,3,opt,name=last_status_timestamp,json=lastStatusTimestamp,proto3" json:"last_status_timestamp,omitempty"`
	FailureReason       string                            `protobuf:"bytes,4,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *GetLoanStatusResponse) Reset() {
	*x = GetLoanStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse) ProtoMessage() {}

func (x *GetLoanStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetLoanStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanStatusResponse) GetLoanStatus() *GetLoanStatusResponse_LoanStatus {
	if x != nil {
		return x.LoanStatus
	}
	return nil
}

func (x *GetLoanStatusResponse) GetLastStatusTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastStatusTimestamp
	}
	return nil
}

func (x *GetLoanStatusResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type VerifyAndDownloadCkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId    string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Pan            string                       `protobuf:"bytes,3,opt,name=pan,proto3" json:"pan,omitempty"`
	AuthFactor     string                       `protobuf:"bytes,4,opt,name=auth_factor,json=authFactor,proto3" json:"auth_factor,omitempty"`
	AuthFactorType AuthFactorType               `protobuf:"varint,5,opt,name=auth_factor_type,json=authFactorType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.AuthFactorType" json:"auth_factor_type,omitempty"`
	LoanProgram    LoanProgram                  `protobuf:"varint,6,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion  SchemeVersion                `protobuf:"varint,7,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *VerifyAndDownloadCkycRequest) Reset() {
	*x = VerifyAndDownloadCkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycRequest) ProtoMessage() {}

func (x *VerifyAndDownloadCkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycRequest.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{37}
}

func (x *VerifyAndDownloadCkycRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VerifyAndDownloadCkycRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetAuthFactor() string {
	if x != nil {
		return x.AuthFactor
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetAuthFactorType() AuthFactorType {
	if x != nil {
		return x.AuthFactorType
	}
	return AuthFactorType_AUTH_FACTOR_TYPE_UNSPECIFIED
}

func (x *VerifyAndDownloadCkycRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *VerifyAndDownloadCkycRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type VerifyAndDownloadCkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CkycData      *VerifyAndDownloadCkycResponse_CkycData `protobuf:"bytes,2,opt,name=ckyc_data,json=ckycData,proto3" json:"ckyc_data,omitempty"`
	FailureReason string                                  `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse) Reset() {
	*x = VerifyAndDownloadCkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{38}
}

func (x *VerifyAndDownloadCkycResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse) GetCkycData() *VerifyAndDownloadCkycResponse_CkycData {
	if x != nil {
		return x.CkycData
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type GetApplicantStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicantId   string                       `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,4,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetApplicantStatusRequest) Reset() {
	*x = GetApplicantStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicantStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicantStatusRequest) ProtoMessage() {}

func (x *GetApplicantStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicantStatusRequest.ProtoReflect.Descriptor instead.
func (*GetApplicantStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetApplicantStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetApplicantStatusRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetApplicantStatusRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetApplicantStatusRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetApplicantStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicantStatus ApplicantStatus `protobuf:"varint,2,opt,name=applicant_status,json=applicantStatus,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.ApplicantStatus" json:"applicant_status,omitempty"`
}

func (x *GetApplicantStatusResponse) Reset() {
	*x = GetApplicantStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicantStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicantStatusResponse) ProtoMessage() {}

func (x *GetApplicantStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicantStatusResponse.ProtoReflect.Descriptor instead.
func (*GetApplicantStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetApplicantStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetApplicantStatusResponse) GetApplicantStatus() ApplicantStatus {
	if x != nil {
		return x.ApplicantStatus
	}
	return ApplicantStatus_APPLICANT_STATUS_UNSPECIFIED
}

type Tenure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinTenure       int32           `protobuf:"varint,1,opt,name=min_tenure,json=minTenure,proto3" json:"min_tenure,omitempty"`
	MaxTenure       int32           `protobuf:"varint,2,opt,name=max_tenure,json=maxTenure,proto3" json:"max_tenure,omitempty"`
	TenureFrequency TenureFrequency `protobuf:"varint,3,opt,name=tenure_frequency,json=tenureFrequency,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency" json:"tenure_frequency,omitempty"`
}

func (x *Tenure) Reset() {
	*x = Tenure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tenure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tenure) ProtoMessage() {}

func (x *Tenure) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tenure.ProtoReflect.Descriptor instead.
func (*Tenure) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{41}
}

func (x *Tenure) GetMinTenure() int32 {
	if x != nil {
		return x.MinTenure
	}
	return 0
}

func (x *Tenure) GetMaxTenure() int32 {
	if x != nil {
		return x.MaxTenure
	}
	return 0
}

func (x *Tenure) GetTenureFrequency() TenureFrequency {
	if x != nil {
		return x.TenureFrequency
	}
	return TenureFrequency_TENURE_FREQUENCY_UNSPECIFIED
}

type Roi struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoiValue float64 `protobuf:"fixed64,1,opt,name=roi_value,json=roiValue,proto3" json:"roi_value,omitempty"`
	RoiType  RoiType `protobuf:"varint,2,opt,name=roi_type,json=roiType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.RoiType" json:"roi_type,omitempty"`
}

func (x *Roi) Reset() {
	*x = Roi{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Roi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Roi) ProtoMessage() {}

func (x *Roi) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Roi.ProtoReflect.Descriptor instead.
func (*Roi) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{42}
}

func (x *Roi) GetRoiValue() float64 {
	if x != nil {
		return x.RoiValue
	}
	return 0
}

func (x *Roi) GetRoiType() RoiType {
	if x != nil {
		return x.RoiType
	}
	return RoiType_ROI_TYPE_UNSPECIFIED
}

type PfFees struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PfType PfType  `protobuf:"varint,1,opt,name=pf_type,json=pfType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.PfType" json:"pf_type,omitempty"`
	PfFees float64 `protobuf:"fixed64,2,opt,name=pf_fees,json=pfFees,proto3" json:"pf_fees,omitempty"`
}

func (x *PfFees) Reset() {
	*x = PfFees{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PfFees) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PfFees) ProtoMessage() {}

func (x *PfFees) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PfFees.ProtoReflect.Descriptor instead.
func (*PfFees) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{43}
}

func (x *PfFees) GetPfType() PfType {
	if x != nil {
		return x.PfType
	}
	return PfType_PF_TYPE_UNSPECIFIED
}

func (x *PfFees) GetPfFees() float64 {
	if x != nil {
		return x.PfFees
	}
	return 0
}

type GetRepaymentScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId      string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	LoanProgram LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	// will choose the LL API based on the api_version
	// if api_version is not provided, it will default to REPAYMENT_SCHEDULE_API_VERSION_V2
	ApiVersion    RepaymentScheduleApiVersion `protobuf:"varint,4,opt,name=api_version,json=apiVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.RepaymentScheduleApiVersion" json:"api_version,omitempty"`
	SchemeVersion SchemeVersion               `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetRepaymentScheduleRequest) Reset() {
	*x = GetRepaymentScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleRequest) ProtoMessage() {}

func (x *GetRepaymentScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetRepaymentScheduleRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetRepaymentScheduleRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetRepaymentScheduleRequest) GetApiVersion() RepaymentScheduleApiVersion {
	if x != nil {
		return x.ApiVersion
	}
	return RepaymentScheduleApiVersion_REPAYMENT_SCHEDULE_API_VERSION_UNSPECIFIED
}

func (x *GetRepaymentScheduleRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetRepaymentScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Schedules     []*GetRepaymentScheduleResponse_Schedule `protobuf:"bytes,2,rep,name=schedules,proto3" json:"schedules,omitempty"`
	FailureReason string                                   `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *GetRepaymentScheduleResponse) Reset() {
	*x = GetRepaymentScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetRepaymentScheduleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRepaymentScheduleResponse) GetSchedules() []*GetRepaymentScheduleResponse_Schedule {
	if x != nil {
		return x.Schedules
	}
	return nil
}

func (x *GetRepaymentScheduleResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type UploadDocumentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,2,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	File          []byte                       `protobuf:"bytes,3,opt,name=file,proto3" json:"file,omitempty"`
	LoanId        string                       `protobuf:"bytes,4,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	DocumentType  DocumentType                 `protobuf:"varint,5,opt,name=document_type,json=documentType,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.DocumentType" json:"document_type,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,6,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *UploadDocumentRequest) Reset() {
	*x = UploadDocumentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadDocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadDocumentRequest) ProtoMessage() {}

func (x *UploadDocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadDocumentRequest.ProtoReflect.Descriptor instead.
func (*UploadDocumentRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{46}
}

func (x *UploadDocumentRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UploadDocumentRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *UploadDocumentRequest) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *UploadDocumentRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *UploadDocumentRequest) GetDocumentType() DocumentType {
	if x != nil {
		return x.DocumentType
	}
	return DocumentType_DOCUMENT_TYPE_UNSPECIFIED
}

func (x *UploadDocumentRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type UploadDocumentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DocumentId     string      `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	DocumentTypeId string      `protobuf:"bytes,3,opt,name=document_type_id,json=documentTypeId,proto3" json:"document_type_id,omitempty"`
	FileName       string      `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FilePath       string      `protobuf:"bytes,5,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *UploadDocumentResponse) Reset() {
	*x = UploadDocumentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadDocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadDocumentResponse) ProtoMessage() {}

func (x *UploadDocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadDocumentResponse.ProtoReflect.Descriptor instead.
func (*UploadDocumentResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{47}
}

func (x *UploadDocumentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UploadDocumentResponse) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *UploadDocumentResponse) GetDocumentTypeId() string {
	if x != nil {
		return x.DocumentTypeId
	}
	return ""
}

func (x *UploadDocumentResponse) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadDocumentResponse) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type SaveCollectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header           *vendorgateway.RequestHeader             `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId           string                                   `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	LoanProgram      LoanProgram                              `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	PaymentSchedules []*SaveCollectionRequest_PaymentSchedule `protobuf:"bytes,4,rep,name=payment_schedules,json=paymentSchedules,proto3" json:"payment_schedules,omitempty"`
	SchemeVersion    SchemeVersion                            `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *SaveCollectionRequest) Reset() {
	*x = SaveCollectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionRequest) ProtoMessage() {}

func (x *SaveCollectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionRequest.ProtoReflect.Descriptor instead.
func (*SaveCollectionRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{48}
}

func (x *SaveCollectionRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaveCollectionRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *SaveCollectionRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *SaveCollectionRequest) GetPaymentSchedules() []*SaveCollectionRequest_PaymentSchedule {
	if x != nil {
		return x.PaymentSchedules
	}
	return nil
}

func (x *SaveCollectionRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type SaveCollectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FailureReason string      `protobuf:"bytes,2,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *SaveCollectionResponse) Reset() {
	*x = SaveCollectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionResponse) ProtoMessage() {}

func (x *SaveCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionResponse.ProtoReflect.Descriptor instead.
func (*SaveCollectionResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{49}
}

func (x *SaveCollectionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SaveCollectionResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type HashGenerationForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicationId int64                        `protobuf:"varint,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,4,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *HashGenerationForOkycRequest) Reset() {
	*x = HashGenerationForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HashGenerationForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashGenerationForOkycRequest) ProtoMessage() {}

func (x *HashGenerationForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashGenerationForOkycRequest.ProtoReflect.Descriptor instead.
func (*HashGenerationForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{50}
}

func (x *HashGenerationForOkycRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *HashGenerationForOkycRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *HashGenerationForOkycRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *HashGenerationForOkycRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type HashGenerationForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status                         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Data   *HashGenerationForOkycResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *HashGenerationForOkycResponse) Reset() {
	*x = HashGenerationForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HashGenerationForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashGenerationForOkycResponse) ProtoMessage() {}

func (x *HashGenerationForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashGenerationForOkycResponse.ProtoReflect.Descriptor instead.
func (*HashGenerationForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{51}
}

func (x *HashGenerationForOkycResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *HashGenerationForOkycResponse) GetData() *HashGenerationForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CaptchaGenerationForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Hash          string                       `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	ApplicationId int64                        `protobuf:"varint,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,4,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,5,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *CaptchaGenerationForOkycRequest) Reset() {
	*x = CaptchaGenerationForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptchaGenerationForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaGenerationForOkycRequest) ProtoMessage() {}

func (x *CaptchaGenerationForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaGenerationForOkycRequest.ProtoReflect.Descriptor instead.
func (*CaptchaGenerationForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{52}
}

func (x *CaptchaGenerationForOkycRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CaptchaGenerationForOkycRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *CaptchaGenerationForOkycRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *CaptchaGenerationForOkycRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *CaptchaGenerationForOkycRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type CaptchaGenerationForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status                            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Data   *CaptchaGenerationForOkycResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CaptchaGenerationForOkycResponse) Reset() {
	*x = CaptchaGenerationForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptchaGenerationForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaGenerationForOkycResponse) ProtoMessage() {}

func (x *CaptchaGenerationForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaGenerationForOkycResponse.ProtoReflect.Descriptor instead.
func (*CaptchaGenerationForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{53}
}

func (x *CaptchaGenerationForOkycResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CaptchaGenerationForOkycResponse) GetData() *CaptchaGenerationForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GenerateOtpForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Hash          string                       `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	UidNumber     int64                        `protobuf:"varint,3,opt,name=uid_number,json=uidNumber,proto3" json:"uid_number,omitempty"`
	ApplicantId   int64                        `protobuf:"varint,4,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	CaptchaCode   string                       `protobuf:"bytes,5,opt,name=captcha_code,json=captchaCode,proto3" json:"captcha_code,omitempty"`
	RequestToken  string                       `protobuf:"bytes,6,opt,name=request_token,json=requestToken,proto3" json:"request_token,omitempty"`
	CaptchaTxnId  string                       `protobuf:"bytes,7,opt,name=captcha_txn_id,json=captchaTxnId,proto3" json:"captcha_txn_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,8,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,9,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GenerateOtpForOkycRequest) Reset() {
	*x = GenerateOtpForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpForOkycRequest) ProtoMessage() {}

func (x *GenerateOtpForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpForOkycRequest.ProtoReflect.Descriptor instead.
func (*GenerateOtpForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{54}
}

func (x *GenerateOtpForOkycRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GenerateOtpForOkycRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetUidNumber() int64 {
	if x != nil {
		return x.UidNumber
	}
	return 0
}

func (x *GenerateOtpForOkycRequest) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *GenerateOtpForOkycRequest) GetCaptchaCode() string {
	if x != nil {
		return x.CaptchaCode
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetRequestToken() string {
	if x != nil {
		return x.RequestToken
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetCaptchaTxnId() string {
	if x != nil {
		return x.CaptchaTxnId
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GenerateOtpForOkycRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GenerateOtpForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status                      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Data   *GenerateOtpForOkycResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GenerateOtpForOkycResponse) Reset() {
	*x = GenerateOtpForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpForOkycResponse) ProtoMessage() {}

func (x *GenerateOtpForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpForOkycResponse.ProtoReflect.Descriptor instead.
func (*GenerateOtpForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{55}
}

func (x *GenerateOtpForOkycResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateOtpForOkycResponse) GetData() *GenerateOtpForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ValidateOtpForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Hash          string                       `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	Otp           int64                        `protobuf:"varint,3,opt,name=otp,proto3" json:"otp,omitempty"`
	ApplicantId   int64                        `protobuf:"varint,4,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	RequestToken  string                       `protobuf:"bytes,5,opt,name=request_token,json=requestToken,proto3" json:"request_token,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,8,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,9,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *ValidateOtpForOkycRequest) Reset() {
	*x = ValidateOtpForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateOtpForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOtpForOkycRequest) ProtoMessage() {}

func (x *ValidateOtpForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOtpForOkycRequest.ProtoReflect.Descriptor instead.
func (*ValidateOtpForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{56}
}

func (x *ValidateOtpForOkycRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ValidateOtpForOkycRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ValidateOtpForOkycRequest) GetOtp() int64 {
	if x != nil {
		return x.Otp
	}
	return 0
}

func (x *ValidateOtpForOkycRequest) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *ValidateOtpForOkycRequest) GetRequestToken() string {
	if x != nil {
		return x.RequestToken
	}
	return ""
}

func (x *ValidateOtpForOkycRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *ValidateOtpForOkycRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type ValidateOtpForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ValidateOtpForOkycResponse) Reset() {
	*x = ValidateOtpForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateOtpForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOtpForOkycResponse) ProtoMessage() {}

func (x *ValidateOtpForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOtpForOkycResponse.ProtoReflect.Descriptor instead.
func (*ValidateOtpForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{57}
}

func (x *ValidateOtpForOkycResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetApplicationSoaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplicationId string                       `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanProgram   LoanProgram                  `protobuf:"varint,3,opt,name=loan_program,json=loanProgram,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram" json:"loan_program,omitempty"`
	SchemeVersion SchemeVersion                `protobuf:"varint,4,opt,name=scheme_version,json=schemeVersion,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion" json:"scheme_version,omitempty"`
}

func (x *GetApplicationSoaRequest) Reset() {
	*x = GetApplicationSoaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicationSoaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationSoaRequest) ProtoMessage() {}

func (x *GetApplicationSoaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationSoaRequest.ProtoReflect.Descriptor instead.
func (*GetApplicationSoaRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{58}
}

func (x *GetApplicationSoaRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetApplicationSoaRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetApplicationSoaRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *GetApplicationSoaRequest) GetSchemeVersion() SchemeVersion {
	if x != nil {
		return x.SchemeVersion
	}
	return SchemeVersion_SCHEME_VERSION_V1
}

type GetApplicationSoaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Data   *GetApplicationSoaResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetApplicationSoaResponse) Reset() {
	*x = GetApplicationSoaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicationSoaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationSoaResponse) ProtoMessage() {}

func (x *GetApplicationSoaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationSoaResponse.ProtoReflect.Descriptor instead.
func (*GetApplicationSoaResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{59}
}

func (x *GetApplicationSoaResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetApplicationSoaResponse) GetData() *GetApplicationSoaResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateRepaymentScheduleRequest_Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DueDate      *date.Date   `protobuf:"bytes,1,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	Principal    *money.Money `protobuf:"bytes,2,opt,name=principal,proto3" json:"principal,omitempty"`
	Interest     *money.Money `protobuf:"bytes,3,opt,name=interest,proto3" json:"interest,omitempty"`
	TotalAmount  *money.Money `protobuf:"bytes,4,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	OtherCharges *money.Money `protobuf:"bytes,5,opt,name=other_charges,json=otherCharges,proto3" json:"other_charges,omitempty"`
	// optional
	PrincipalOutstanding *money.Money `protobuf:"bytes,6,opt,name=principal_outstanding,json=principalOutstanding,proto3" json:"principal_outstanding,omitempty"`
}

func (x *CreateRepaymentScheduleRequest_Schedule) Reset() {
	*x = CreateRepaymentScheduleRequest_Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleRequest_Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleRequest_Schedule) ProtoMessage() {}

func (x *CreateRepaymentScheduleRequest_Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleRequest_Schedule.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleRequest_Schedule) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetDueDate() *date.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetPrincipal() *money.Money {
	if x != nil {
		return x.Principal
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetInterest() *money.Money {
	if x != nil {
		return x.Interest
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetOtherCharges() *money.Money {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetPrincipalOutstanding() *money.Money {
	if x != nil {
		return x.PrincipalOutstanding
	}
	return nil
}

type ForeClosureDetailsResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId        string       `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	TotalOutstanding     *money.Money `protobuf:"bytes,2,opt,name=total_outstanding,json=totalOutstanding,proto3" json:"total_outstanding,omitempty"`
	PrincipalOutstanding *money.Money `protobuf:"bytes,3,opt,name=principal_outstanding,json=principalOutstanding,proto3" json:"principal_outstanding,omitempty"`
	InterestOutstanding  *money.Money `protobuf:"bytes,4,opt,name=interest_outstanding,json=interestOutstanding,proto3" json:"interest_outstanding,omitempty"`
	PenaltyCharges       *money.Money `protobuf:"bytes,5,opt,name=penalty_charges,json=penaltyCharges,proto3" json:"penalty_charges,omitempty"`
	FeesCharges          *money.Money `protobuf:"bytes,6,opt,name=fees_charges,json=feesCharges,proto3" json:"fees_charges,omitempty"`
	OtherCharges         *money.Money `protobuf:"bytes,7,opt,name=other_charges,json=otherCharges,proto3" json:"other_charges,omitempty"`
}

func (x *ForeClosureDetailsResponse_Data) Reset() {
	*x = ForeClosureDetailsResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeClosureDetailsResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeClosureDetailsResponse_Data) ProtoMessage() {}

func (x *ForeClosureDetailsResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeClosureDetailsResponse_Data.ProtoReflect.Descriptor instead.
func (*ForeClosureDetailsResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ForeClosureDetailsResponse_Data) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *ForeClosureDetailsResponse_Data) GetTotalOutstanding() *money.Money {
	if x != nil {
		return x.TotalOutstanding
	}
	return nil
}

func (x *ForeClosureDetailsResponse_Data) GetPrincipalOutstanding() *money.Money {
	if x != nil {
		return x.PrincipalOutstanding
	}
	return nil
}

func (x *ForeClosureDetailsResponse_Data) GetInterestOutstanding() *money.Money {
	if x != nil {
		return x.InterestOutstanding
	}
	return nil
}

func (x *ForeClosureDetailsResponse_Data) GetPenaltyCharges() *money.Money {
	if x != nil {
		return x.PenaltyCharges
	}
	return nil
}

func (x *ForeClosureDetailsResponse_Data) GetFeesCharges() *money.Money {
	if x != nil {
		return x.FeesCharges
	}
	return nil
}

func (x *ForeClosureDetailsResponse_Data) GetOtherCharges() *money.Money {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

type CancelLeadResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicantId   string `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
}

func (x *CancelLeadResponse_Data) Reset() {
	*x = CancelLeadResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadResponse_Data) ProtoMessage() {}

func (x *CancelLeadResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadResponse_Data.ProtoReflect.Descriptor instead.
func (*CancelLeadResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *CancelLeadResponse_Data) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CancelLeadResponse_Data) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

type GetCreditLineSchemesResponse_CreditLineDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId string `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	// Represents the total credit limit approved for a user (available limit + block limit)
	UpperLimit *money.Money `protobuf:"bytes,2,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit,omitempty"`
	// Represents the amount a user can make a drawdown request for, after a drawdown, this limit will decrease
	AvailableLimit *money.Money `protobuf:"bytes,3,opt,name=available_limit,json=availableLimit,proto3" json:"available_limit,omitempty"`
	// Represents the amount that user has alreaday taken. When user will make a drawdown, this limit will increase
	BlockLimit *money.Money `protobuf:"bytes,4,opt,name=block_limit,json=blockLimit,proto3" json:"block_limit,omitempty"`
}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) Reset() {
	*x = GetCreditLineSchemesResponse_CreditLineDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineSchemesResponse_CreditLineDetails) ProtoMessage() {}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineSchemesResponse_CreditLineDetails.ProtoReflect.Descriptor instead.
func (*GetCreditLineSchemesResponse_CreditLineDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) GetUpperLimit() *money.Money {
	if x != nil {
		return x.UpperLimit
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) GetAvailableLimit() *money.Money {
	if x != nil {
		return x.AvailableLimit
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineDetails) GetBlockLimit() *money.Money {
	if x != nil {
		return x.BlockLimit
	}
	return nil
}

type GetCreditLineSchemesResponse_CreditLineScheme struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roi               *Roi                                                             `protobuf:"bytes,1,opt,name=roi,proto3" json:"roi,omitempty"`
	AmountConstraints *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints `protobuf:"bytes,2,opt,name=amount_constraints,json=amountConstraints,proto3" json:"amount_constraints,omitempty"`
	EmiDueDate        *date.Date                                                       `protobuf:"bytes,3,opt,name=emi_due_date,json=emiDueDate,proto3" json:"emi_due_date,omitempty"` //YYYY-MM-DD
	Tenure            *Tenure                                                          `protobuf:"bytes,4,opt,name=tenure,proto3" json:"tenure,omitempty"`
	PfFees            *PfFees                                                          `protobuf:"bytes,5,opt,name=pf_fees,json=pfFees,proto3" json:"pf_fees,omitempty"`
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) Reset() {
	*x = GetCreditLineSchemesResponse_CreditLineScheme{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineSchemesResponse_CreditLineScheme) ProtoMessage() {}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineSchemesResponse_CreditLineScheme.ProtoReflect.Descriptor instead.
func (*GetCreditLineSchemesResponse_CreditLineScheme) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{14, 1}
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) GetRoi() *Roi {
	if x != nil {
		return x.Roi
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) GetAmountConstraints() *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints {
	if x != nil {
		return x.AmountConstraints
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) GetEmiDueDate() *date.Date {
	if x != nil {
		return x.EmiDueDate
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) GetTenure() *Tenure {
	if x != nil {
		return x.Tenure
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme) GetPfFees() *PfFees {
	if x != nil {
		return x.PfFees
	}
	return nil
}

type GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxEmiAllowed     *money.Money `protobuf:"bytes,1,opt,name=max_emi_allowed,json=maxEmiAllowed,proto3" json:"max_emi_allowed,omitempty"`
	MinDrawdownAmount *money.Money `protobuf:"bytes,2,opt,name=min_drawdown_amount,json=minDrawdownAmount,proto3" json:"min_drawdown_amount,omitempty"`
	MaxDrawdownAmount *money.Money `protobuf:"bytes,3,opt,name=max_drawdown_amount,json=maxDrawdownAmount,proto3" json:"max_drawdown_amount,omitempty"`
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) Reset() {
	*x = GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) ProtoMessage() {}

func (x *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints.ProtoReflect.Descriptor instead.
func (*GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{14, 1, 0}
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) GetMaxEmiAllowed() *money.Money {
	if x != nil {
		return x.MaxEmiAllowed
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) GetMinDrawdownAmount() *money.Money {
	if x != nil {
		return x.MinDrawdownAmount
	}
	return nil
}

func (x *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) GetMaxDrawdownAmount() *money.Money {
	if x != nil {
		return x.MaxDrawdownAmount
	}
	return nil
}

type AddPersonalDetailsResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId   string              `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Name          *common.Name        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string              `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ContactNumber *common.PhoneNumber `protobuf:"bytes,4,opt,name=contact_number,json=contactNumber,proto3" json:"contact_number,omitempty"`
}

func (x *AddPersonalDetailsResponse_Data) Reset() {
	*x = AddPersonalDetailsResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPersonalDetailsResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPersonalDetailsResponse_Data) ProtoMessage() {}

func (x *AddPersonalDetailsResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPersonalDetailsResponse_Data.ProtoReflect.Descriptor instead.
func (*AddPersonalDetailsResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *AddPersonalDetailsResponse_Data) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddPersonalDetailsResponse_Data) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *AddPersonalDetailsResponse_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AddPersonalDetailsResponse_Data) GetContactNumber() *common.PhoneNumber {
	if x != nil {
		return x.ContactNumber
	}
	return nil
}

type ApplicantLookupResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId      string                                         `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	Name             string                                         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Pan              string                                         `protobuf:"bytes,3,opt,name=pan,proto3" json:"pan,omitempty"`
	DetailsStatus    *ApplicantLookupResponse_Data_DetailsStatus    `protobuf:"bytes,4,opt,name=details_status,json=detailsStatus,proto3" json:"details_status,omitempty"`
	ActivationStatus *ApplicantLookupResponse_Data_ActivationStatus `protobuf:"bytes,5,opt,name=activation_status,json=activationStatus,proto3" json:"activation_status,omitempty"`
}

func (x *ApplicantLookupResponse_Data) Reset() {
	*x = ApplicantLookupResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse_Data) ProtoMessage() {}

func (x *ApplicantLookupResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse_Data.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ApplicantLookupResponse_Data) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetDetailsStatus() *ApplicantLookupResponse_Data_DetailsStatus {
	if x != nil {
		return x.DetailsStatus
	}
	return nil
}

func (x *ApplicantLookupResponse_Data) GetActivationStatus() *ApplicantLookupResponse_Data_ActivationStatus {
	if x != nil {
		return x.ActivationStatus
	}
	return nil
}

type ApplicantLookupResponse_Data_DetailsStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankingDetailStatus    bool `protobuf:"varint,1,opt,name=banking_detail_status,json=bankingDetailStatus,proto3" json:"banking_detail_status,omitempty"`
	AddressDetailStatus    bool `protobuf:"varint,2,opt,name=address_detail_status,json=addressDetailStatus,proto3" json:"address_detail_status,omitempty"`
	EmploymentDetailStatus bool `protobuf:"varint,3,opt,name=employment_detail_status,json=employmentDetailStatus,proto3" json:"employment_detail_status,omitempty"`
}

func (x *ApplicantLookupResponse_Data_DetailsStatus) Reset() {
	*x = ApplicantLookupResponse_Data_DetailsStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse_Data_DetailsStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse_Data_DetailsStatus) ProtoMessage() {}

func (x *ApplicantLookupResponse_Data_DetailsStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse_Data_DetailsStatus.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse_Data_DetailsStatus) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{22, 0, 0}
}

func (x *ApplicantLookupResponse_Data_DetailsStatus) GetBankingDetailStatus() bool {
	if x != nil {
		return x.BankingDetailStatus
	}
	return false
}

func (x *ApplicantLookupResponse_Data_DetailsStatus) GetAddressDetailStatus() bool {
	if x != nil {
		return x.AddressDetailStatus
	}
	return false
}

func (x *ApplicantLookupResponse_Data_DetailsStatus) GetEmploymentDetailStatus() bool {
	if x != nil {
		return x.EmploymentDetailStatus
	}
	return false
}

type ApplicantLookupResponse_Data_ActivationStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgreementStatus bool `protobuf:"varint,1,opt,name=agreement_status,json=agreementStatus,proto3" json:"agreement_status,omitempty"`
	MandateStatus   bool `protobuf:"varint,2,opt,name=mandate_status,json=mandateStatus,proto3" json:"mandate_status,omitempty"`
}

func (x *ApplicantLookupResponse_Data_ActivationStatus) Reset() {
	*x = ApplicantLookupResponse_Data_ActivationStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse_Data_ActivationStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse_Data_ActivationStatus) ProtoMessage() {}

func (x *ApplicantLookupResponse_Data_ActivationStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse_Data_ActivationStatus.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse_Data_ActivationStatus) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{22, 0, 1}
}

func (x *ApplicantLookupResponse_Data_ActivationStatus) GetAgreementStatus() bool {
	if x != nil {
		return x.AgreementStatus
	}
	return false
}

func (x *ApplicantLookupResponse_Data_ActivationStatus) GetMandateStatus() bool {
	if x != nil {
		return x.MandateStatus
	}
	return false
}

type MakeDrawdownRequest_LoanTenure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TenureFrequency TenureFrequency `protobuf:"varint,1,opt,name=tenure_frequency,json=tenureFrequency,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency" json:"tenure_frequency,omitempty"`
	Value           int32           `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *MakeDrawdownRequest_LoanTenure) Reset() {
	*x = MakeDrawdownRequest_LoanTenure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDrawdownRequest_LoanTenure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDrawdownRequest_LoanTenure) ProtoMessage() {}

func (x *MakeDrawdownRequest_LoanTenure) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDrawdownRequest_LoanTenure.ProtoReflect.Descriptor instead.
func (*MakeDrawdownRequest_LoanTenure) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{27, 0}
}

func (x *MakeDrawdownRequest_LoanTenure) GetTenureFrequency() TenureFrequency {
	if x != nil {
		return x.TenureFrequency
	}
	return TenureFrequency_TENURE_FREQUENCY_UNSPECIFIED
}

func (x *MakeDrawdownRequest_LoanTenure) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type GetLoanStatusResponse_LoanStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanData    *GetLoanStatusResponse_LoanStatus_LoanData    `protobuf:"bytes,1,opt,name=loan_data,json=loanData,proto3" json:"loan_data,omitempty"`
	LoanDetails *GetLoanStatusResponse_LoanStatus_LoanDetails `protobuf:"bytes,2,opt,name=loan_details,json=loanDetails,proto3" json:"loan_details,omitempty"`
}

func (x *GetLoanStatusResponse_LoanStatus) Reset() {
	*x = GetLoanStatusResponse_LoanStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse_LoanStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse_LoanStatus) ProtoMessage() {}

func (x *GetLoanStatusResponse_LoanStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse_LoanStatus.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse_LoanStatus) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *GetLoanStatusResponse_LoanStatus) GetLoanData() *GetLoanStatusResponse_LoanStatus_LoanData {
	if x != nil {
		return x.LoanData
	}
	return nil
}

func (x *GetLoanStatusResponse_LoanStatus) GetLoanDetails() *GetLoanStatusResponse_LoanStatus_LoanDetails {
	if x != nil {
		return x.LoanDetails
	}
	return nil
}

type GetLoanStatusResponse_LoanStatus_LoanData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId   string `protobuf:"bytes,1,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	LoanCode string `protobuf:"bytes,2,opt,name=loan_code,json=loanCode,proto3" json:"loan_code,omitempty"`
	Status   Status `protobuf:"varint,3,opt,name=status,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.Status" json:"status,omitempty"`
	Urn      string `protobuf:"bytes,4,opt,name=urn,proto3" json:"urn,omitempty"`
	Utr      string `protobuf:"bytes,5,opt,name=utr,proto3" json:"utr,omitempty"`
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) Reset() {
	*x = GetLoanStatusResponse_LoanStatus_LoanData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse_LoanStatus_LoanData) ProtoMessage() {}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse_LoanStatus_LoanData.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse_LoanStatus_LoanData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{36, 0, 0}
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) GetLoanCode() string {
	if x != nil {
		return x.LoanCode
	}
	return ""
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *GetLoanStatusResponse_LoanStatus_LoanData) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

type GetLoanStatusResponse_LoanStatus_LoanDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount           *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	DisbursedAmount  *money.Money `protobuf:"bytes,3,opt,name=disbursed_amount,json=disbursedAmount,proto3" json:"disbursed_amount,omitempty"`
	DisbursementDate *date.Date   `protobuf:"bytes,4,opt,name=disbursement_date,json=disbursementDate,proto3" json:"disbursement_date,omitempty"`
	EmiAmount        *money.Money `protobuf:"bytes,5,opt,name=emi_amount,json=emiAmount,proto3" json:"emi_amount,omitempty"`
	Tenure           int32        `protobuf:"varint,6,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Roi              float64      `protobuf:"fixed64,7,opt,name=roi,proto3" json:"roi,omitempty"`
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) Reset() {
	*x = GetLoanStatusResponse_LoanStatus_LoanDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse_LoanStatus_LoanDetails) ProtoMessage() {}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse_LoanStatus_LoanDetails.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse_LoanStatus_LoanDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{36, 0, 1}
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) GetDisbursedAmount() *money.Money {
	if x != nil {
		return x.DisbursedAmount
	}
	return nil
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) GetDisbursementDate() *date.Date {
	if x != nil {
		return x.DisbursementDate
	}
	return nil
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) GetEmiAmount() *money.Money {
	if x != nil {
		return x.EmiAmount
	}
	return nil
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *GetLoanStatusResponse_LoanStatus_LoanDetails) GetRoi() float64 {
	if x != nil {
		return x.Roi
	}
	return 0
}

type VerifyAndDownloadCkycResponse_CkycData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalData *VerifyAndDownloadCkycResponse_PersonalData `protobuf:"bytes,1,opt,name=personal_data,json=personalData,proto3" json:"personal_data,omitempty"`
	ImageType    common.ImageType                            `protobuf:"varint,2,opt,name=image_type,json=imageType,proto3,enum=api.typesv2.common.ImageType" json:"image_type,omitempty"`
	Photo        string                                      `protobuf:"bytes,3,opt,name=photo,proto3" json:"photo,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse_CkycData) Reset() {
	*x = VerifyAndDownloadCkycResponse_CkycData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse_CkycData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse_CkycData) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse_CkycData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse_CkycData.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse_CkycData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{38, 0}
}

func (x *VerifyAndDownloadCkycResponse_CkycData) GetPersonalData() *VerifyAndDownloadCkycResponse_PersonalData {
	if x != nil {
		return x.PersonalData
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_CkycData) GetImageType() common.ImageType {
	if x != nil {
		return x.ImageType
	}
	return common.ImageType(0)
}

func (x *VerifyAndDownloadCkycResponse_CkycData) GetPhoto() string {
	if x != nil {
		return x.Photo
	}
	return ""
}

type VerifyAndDownloadCkycResponse_PersonalData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycNo     string                                                `protobuf:"bytes,1,opt,name=ckyc_no,json=ckycNo,proto3" json:"ckyc_no,omitempty"`
	Name       *common.Name                                          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	KycDate    *date.Date                                            `protobuf:"bytes,3,opt,name=kyc_date,json=kycDate,proto3" json:"kyc_date,omitempty"`
	Gender     typesv2.Gender                                        `protobuf:"varint,4,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	Dob        *date.Date                                            `protobuf:"bytes,5,opt,name=dob,proto3" json:"dob,omitempty"`
	FatherName *common.Name                                          `protobuf:"bytes,6,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName *common.Name                                          `protobuf:"bytes,7,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	MobNum     *common.PhoneNumber                                   `protobuf:"bytes,8,opt,name=mob_num,json=mobNum,proto3" json:"mob_num,omitempty"`
	Email      string                                                `protobuf:"bytes,9,opt,name=email,proto3" json:"email,omitempty"`
	Addresses  *VerifyAndDownloadCkycResponse_PersonalData_Addresses `protobuf:"bytes,10,opt,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) Reset() {
	*x = VerifyAndDownloadCkycResponse_PersonalData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse_PersonalData) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse_PersonalData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse_PersonalData.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse_PersonalData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{38, 1}
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetCkycNo() string {
	if x != nil {
		return x.CkycNo
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetKycDate() *date.Date {
	if x != nil {
		return x.KycDate
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetMobNum() *common.PhoneNumber {
	if x != nil {
		return x.MobNum
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_PersonalData) GetAddresses() *VerifyAndDownloadCkycResponse_PersonalData_Addresses {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type VerifyAndDownloadCkycResponse_PersonalData_Addresses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermanentAddress      *typesv2.PostalAddress `protobuf:"bytes,1,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	CorrespondenceAddress *typesv2.PostalAddress `protobuf:"bytes,2,opt,name=correspondence_address,json=correspondenceAddress,proto3" json:"correspondence_address,omitempty"`
	PermCorrAddressSame   bool                   `protobuf:"varint,3,opt,name=perm_corr_address_same,json=permCorrAddressSame,proto3" json:"perm_corr_address_same,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse_PersonalData_Addresses) Reset() {
	*x = VerifyAndDownloadCkycResponse_PersonalData_Addresses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse_PersonalData_Addresses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse_PersonalData_Addresses) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse_PersonalData_Addresses) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse_PersonalData_Addresses.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse_PersonalData_Addresses) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{38, 1, 0}
}

func (x *VerifyAndDownloadCkycResponse_PersonalData_Addresses) GetPermanentAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData_Addresses) GetCorrespondenceAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.CorrespondenceAddress
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_PersonalData_Addresses) GetPermCorrAddressSame() bool {
	if x != nil {
		return x.PermCorrAddressSame
	}
	return false
}

type GetRepaymentScheduleResponse_Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId       string        `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	InstallmentNumber   int32         `protobuf:"varint,2,opt,name=installment_number,json=installmentNumber,proto3" json:"installment_number,omitempty"`
	DueDate             *date.Date    `protobuf:"bytes,3,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	DueAmount           *money.Money  `protobuf:"bytes,4,opt,name=due_amount,json=dueAmount,proto3" json:"due_amount,omitempty"`
	PrincipalAmount     *money.Money  `protobuf:"bytes,5,opt,name=principal_amount,json=principalAmount,proto3" json:"principal_amount,omitempty"`
	InterestAmount      *money.Money  `protobuf:"bytes,6,opt,name=interest_amount,json=interestAmount,proto3" json:"interest_amount,omitempty"`
	PaymentStatus       PaymentStatus `protobuf:"varint,7,opt,name=payment_status,json=paymentStatus,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.PaymentStatus" json:"payment_status,omitempty"`
	ReceivedDate        *date.Date    `protobuf:"bytes,8,opt,name=received_date,json=receivedDate,proto3" json:"received_date,omitempty"`
	ReceivedAmount      *money.Money  `protobuf:"bytes,9,opt,name=received_amount,json=receivedAmount,proto3" json:"received_amount,omitempty"`
	PaidPrincipalAmount *money.Money  `protobuf:"bytes,10,opt,name=paid_principal_amount,json=paidPrincipalAmount,proto3" json:"paid_principal_amount,omitempty"`
	PaidInterestAmount  *money.Money  `protobuf:"bytes,11,opt,name=paid_interest_amount,json=paidInterestAmount,proto3" json:"paid_interest_amount,omitempty"`
	// interest charges incurred due to late payment of EMI
	Lpi                             *money.Money `protobuf:"bytes,12,opt,name=lpi,proto3" json:"lpi,omitempty"`
	OtherCharges                    *money.Money `protobuf:"bytes,13,opt,name=other_charges,json=otherCharges,proto3" json:"other_charges,omitempty"`
	BounceCharges                   *money.Money `protobuf:"bytes,14,opt,name=bounce_charges,json=bounceCharges,proto3" json:"bounce_charges,omitempty"`
	PostPaymentPrincipalOutstanding *money.Money `protobuf:"bytes,15,opt,name=post_payment_principal_outstanding,json=postPaymentPrincipalOutstanding,proto3" json:"post_payment_principal_outstanding,omitempty"`
	PostPaymentInterestOutstanding  *money.Money `protobuf:"bytes,16,opt,name=post_payment_interest_outstanding,json=postPaymentInterestOutstanding,proto3" json:"post_payment_interest_outstanding,omitempty"`
	// (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
	WaivedCharges *money.Money `protobuf:"bytes,17,opt,name=waived_charges,json=waivedCharges,proto3" json:"waived_charges,omitempty"`
	// amount paid by user out of lpi (late payment interest)
	// (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
	PaidLatePaymentInterest *money.Money `protobuf:"bytes,18,opt,name=paid_late_payment_interest,json=paidLatePaymentInterest,proto3" json:"paid_late_payment_interest,omitempty"`
	// amount paid till now by user out of other_charges
	// (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
	PaidOtherCharges *money.Money `protobuf:"bytes,19,opt,name=paid_other_charges,json=paidOtherCharges,proto3" json:"paid_other_charges,omitempty"`
	// amount paid till now by user out of bounce_charges
	// (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
	PaidBounceCharges *money.Money `protobuf:"bytes,20,opt,name=paid_bounce_charges,json=paidBounceCharges,proto3" json:"paid_bounce_charges,omitempty"`
	// (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
	PostPaymentChargesOutstanding *money.Money `protobuf:"bytes,21,opt,name=post_payment_charges_outstanding,json=postPaymentChargesOutstanding,proto3" json:"post_payment_charges_outstanding,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Schedule) Reset() {
	*x = GetRepaymentScheduleResponse_Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Schedule) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Schedule.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Schedule) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{45, 0}
}

func (x *GetRepaymentScheduleResponse_Schedule) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetRepaymentScheduleResponse_Schedule) GetInstallmentNumber() int32 {
	if x != nil {
		return x.InstallmentNumber
	}
	return 0
}

func (x *GetRepaymentScheduleResponse_Schedule) GetDueDate() *date.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetDueAmount() *money.Money {
	if x != nil {
		return x.DueAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPrincipalAmount() *money.Money {
	if x != nil {
		return x.PrincipalAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetInterestAmount() *money.Money {
	if x != nil {
		return x.InterestAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPaymentStatus() PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return PaymentStatus_PAYMENT_STATUS_UNSPECIFIED
}

func (x *GetRepaymentScheduleResponse_Schedule) GetReceivedDate() *date.Date {
	if x != nil {
		return x.ReceivedDate
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetReceivedAmount() *money.Money {
	if x != nil {
		return x.ReceivedAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPaidPrincipalAmount() *money.Money {
	if x != nil {
		return x.PaidPrincipalAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPaidInterestAmount() *money.Money {
	if x != nil {
		return x.PaidInterestAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetLpi() *money.Money {
	if x != nil {
		return x.Lpi
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetOtherCharges() *money.Money {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetBounceCharges() *money.Money {
	if x != nil {
		return x.BounceCharges
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPostPaymentPrincipalOutstanding() *money.Money {
	if x != nil {
		return x.PostPaymentPrincipalOutstanding
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPostPaymentInterestOutstanding() *money.Money {
	if x != nil {
		return x.PostPaymentInterestOutstanding
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetWaivedCharges() *money.Money {
	if x != nil {
		return x.WaivedCharges
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPaidLatePaymentInterest() *money.Money {
	if x != nil {
		return x.PaidLatePaymentInterest
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPaidOtherCharges() *money.Money {
	if x != nil {
		return x.PaidOtherCharges
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPaidBounceCharges() *money.Money {
	if x != nil {
		return x.PaidBounceCharges
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Schedule) GetPostPaymentChargesOutstanding() *money.Money {
	if x != nil {
		return x.PostPaymentChargesOutstanding
	}
	return nil
}

type SaveCollectionRequest_PaymentSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PaymentMode     PaymentMode   `protobuf:"varint,1,opt,name=payment_mode,json=paymentMode,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.PaymentMode" json:"payment_mode,omitempty"`
	TransactionDate *date.Date    `protobuf:"bytes,2,opt,name=transaction_date,json=transactionDate,proto3" json:"transaction_date,omitempty"`
	PaymentStatus   PaymentStatus `protobuf:"varint,3,opt,name=payment_status,json=paymentStatus,proto3,enum=vendorgateway.lending.preapprovedloan.liquiloans.PaymentStatus" json:"payment_status,omitempty"`
	// Represents the total transaction amount paid by the user
	PaidTotalAmount *money.Money `protobuf:"bytes,4,opt,name=paid_total_amount,json=paidTotalAmount,proto3" json:"paid_total_amount,omitempty"`
	// UTR or Payment Reference number
	VoucherNo         string       `protobuf:"bytes,5,opt,name=voucher_no,json=voucherNo,proto3" json:"voucher_no,omitempty"`
	BounceCharges     *money.Money `protobuf:"bytes,6,opt,name=bounce_charges,json=bounceCharges,proto3" json:"bounce_charges,omitempty"`
	CollectionCharges *money.Money `protobuf:"bytes,7,opt,name=collection_charges,json=collectionCharges,proto3" json:"collection_charges,omitempty"`
	// Any charges that were posted to LL using SaveCharges API should be passed in this field
	OtherCharges *money.Money `protobuf:"bytes,8,opt,name=other_charges,json=otherCharges,proto3" json:"other_charges,omitempty"`
	// Late payment interest charges
	LpiCharges *money.Money `protobuf:"bytes,9,opt,name=lpi_charges,json=lpiCharges,proto3" json:"lpi_charges,omitempty"`
	// settelment_date is optional field, all the operations and decisions are taken based on transaction_date
	SettelmentDate *date.Date `protobuf:"bytes,10,opt,name=settelment_date,json=settelmentDate,proto3" json:"settelment_date,omitempty"`
	DueDate        *date.Date `protobuf:"bytes,11,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
}

func (x *SaveCollectionRequest_PaymentSchedule) Reset() {
	*x = SaveCollectionRequest_PaymentSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionRequest_PaymentSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionRequest_PaymentSchedule) ProtoMessage() {}

func (x *SaveCollectionRequest_PaymentSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionRequest_PaymentSchedule.ProtoReflect.Descriptor instead.
func (*SaveCollectionRequest_PaymentSchedule) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{48, 0}
}

func (x *SaveCollectionRequest_PaymentSchedule) GetPaymentMode() PaymentMode {
	if x != nil {
		return x.PaymentMode
	}
	return PaymentMode_PAYMENT_MODE_UNSPECIFIED
}

func (x *SaveCollectionRequest_PaymentSchedule) GetTransactionDate() *date.Date {
	if x != nil {
		return x.TransactionDate
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetPaymentStatus() PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return PaymentStatus_PAYMENT_STATUS_UNSPECIFIED
}

func (x *SaveCollectionRequest_PaymentSchedule) GetPaidTotalAmount() *money.Money {
	if x != nil {
		return x.PaidTotalAmount
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetVoucherNo() string {
	if x != nil {
		return x.VoucherNo
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetBounceCharges() *money.Money {
	if x != nil {
		return x.BounceCharges
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetCollectionCharges() *money.Money {
	if x != nil {
		return x.CollectionCharges
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetOtherCharges() *money.Money {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetLpiCharges() *money.Money {
	if x != nil {
		return x.LpiCharges
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetSettelmentDate() *date.Date {
	if x != nil {
		return x.SettelmentDate
	}
	return nil
}

func (x *SaveCollectionRequest_PaymentSchedule) GetDueDate() *date.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

type HashGenerationForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
}

func (x *HashGenerationForOkycResponse_Data) Reset() {
	*x = HashGenerationForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HashGenerationForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashGenerationForOkycResponse_Data) ProtoMessage() {}

func (x *HashGenerationForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashGenerationForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*HashGenerationForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{51, 0}
}

func (x *HashGenerationForOkycResponse_Data) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type CaptchaGenerationForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaptchaImage string `protobuf:"bytes,1,opt,name=captcha_image,json=captchaImage,proto3" json:"captcha_image,omitempty"`
	RequestToken string `protobuf:"bytes,2,opt,name=request_token,json=requestToken,proto3" json:"request_token,omitempty"`
	CaptchaTxnId string `protobuf:"bytes,3,opt,name=captcha_txn_id,json=captchaTxnId,proto3" json:"captcha_txn_id,omitempty"`
}

func (x *CaptchaGenerationForOkycResponse_Data) Reset() {
	*x = CaptchaGenerationForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptchaGenerationForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaGenerationForOkycResponse_Data) ProtoMessage() {}

func (x *CaptchaGenerationForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaGenerationForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*CaptchaGenerationForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{53, 0}
}

func (x *CaptchaGenerationForOkycResponse_Data) GetCaptchaImage() string {
	if x != nil {
		return x.CaptchaImage
	}
	return ""
}

func (x *CaptchaGenerationForOkycResponse_Data) GetRequestToken() string {
	if x != nil {
		return x.RequestToken
	}
	return ""
}

func (x *CaptchaGenerationForOkycResponse_Data) GetCaptchaTxnId() string {
	if x != nil {
		return x.CaptchaTxnId
	}
	return ""
}

type GenerateOtpForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *GenerateOtpForOkycResponse_Data) Reset() {
	*x = GenerateOtpForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpForOkycResponse_Data) ProtoMessage() {}

func (x *GenerateOtpForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*GenerateOtpForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{55, 0}
}

func (x *GenerateOtpForOkycResponse_Data) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type GetApplicationSoaResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Link          string          `protobuf:"bytes,1,opt,name=link,proto3" json:"link,omitempty"`
	ApplicationId *structpb.Value `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetApplicationSoaResponse_Data) Reset() {
	*x = GetApplicationSoaResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicationSoaResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationSoaResponse_Data) ProtoMessage() {}

func (x *GetApplicationSoaResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationSoaResponse_Data.ProtoReflect.Descriptor instead.
func (*GetApplicationSoaResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP(), []int{59, 0}
}

func (x *GetApplicationSoaResponse_Data) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *GetApplicationSoaResponse_Data) GetApplicationId() *structpb.Value {
	if x != nil {
		return x.ApplicationId
	}
	return nil
}

var File_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDesc = []byte{
	0x0a, 0x42, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x30, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xc0, 0x03, 0x0a, 0x12, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x60,
	0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x66, 0x0a, 0x0e,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xd4, 0x07, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x12, 0x72, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x52, 0x12, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6d, 0x69, 0x5f,
	0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x6d,
	0x69, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x37, 0x0a, 0x0e, 0x65, 0x6d, 0x69, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x0c, 0x65, 0x6d, 0x69, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x77, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x09,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x1a, 0xd3, 0x02, 0x0a, 0x08, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x2c,
	0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x09,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x09, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x12, 0x2e,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x35,
	0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x47,
	0x0a, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x14, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x46, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xe0, 0x03, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x55, 0x64, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x10, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x41, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x55, 0x64, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc2, 0x02, 0x0a, 0x19, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x97, 0x05, 0x0a, 0x1a, 0x46,
	0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x65, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xab, 0x03, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x47, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63,
	0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x70, 0x72, 0x69, 0x6e,
	0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x45, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3b, 0x0a, 0x0f, 0x70, 0x65, 0x6e, 0x61, 0x6c,
	0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b,
	0x66, 0x65, 0x65, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x22, 0xdd, 0x02, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xab, 0x02, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x1a, 0x50, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0xea, 0x03, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6e, 0x12, 0x70, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xd9, 0x08, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x76, 0x0a, 0x15, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x14, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x2d, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x73, 0x12,
	0x3e, 0x0a, 0x0e, 0x72, 0x6f, 0x69, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x19, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x0d, 0x72, 0x6f, 0x69, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x47, 0x0a, 0x16, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x14, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4c, 0x0a, 0x15, 0x73, 0x75, 0x62, 0x76,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x19, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x14, 0x73, 0x75, 0x62, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x6b, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x08, 0x72, 0x6f, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x52, 0x6f, 0x69, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x72, 0x6f, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x64, 0x0a, 0x0e, 0x72, 0x6f, 0x69,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x46, 0x65, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x4f,
	0x6e, 0x52, 0x0c, 0x72, 0x6f, 0x69, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x4f, 0x6e, 0x12,
	0x8c, 0x01, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x65, 0x65, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x46, 0x65, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x4f, 0x6e, 0x52, 0x1f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x73, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x3f,
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x46, 0x65, 0x65, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x72, 0x6f, 0x69, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x6f, 0x69, 0x47, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x08, 0x67, 0x73, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x46, 0x65,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x67, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x67, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x52, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x22,
	0xc0, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e,
	0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xfd, 0x09, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x90, 0x01, 0x0a, 0x13, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x60, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x8f, 0x01, 0x0a, 0x13,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5f, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x11, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x1a, 0xdd, 0x01,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0a, 0x75, 0x70, 0x70, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x1a, 0xb2, 0x05,
	0x0a, 0x10, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x12, 0x47, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x52, 0x6f, 0x69, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0xa0, 0x01, 0x0a, 0x12,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x71, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c,
	0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x11, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x33,
	0x0a, 0x0c, 0x65, 0x6d, 0x69, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x65, 0x6d, 0x69, 0x44, 0x75, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x50, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x52, 0x06, 0x74,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x51, 0x0a, 0x07, 0x70, 0x66, 0x5f, 0x66, 0x65, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x50, 0x66, 0x46, 0x65, 0x65, 0x73,
	0x52, 0x06, 0x70, 0x66, 0x46, 0x65, 0x65, 0x73, 0x1a, 0xd7, 0x01, 0x0a, 0x11, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x3a,
	0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x78,
	0x45, 0x6d, 0x69, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x13, 0x6d, 0x69,
	0x6e, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x6d, 0x69, 0x6e,
	0x44, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42,
	0x0a, 0x13, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x11, 0x6d, 0x61, 0x78, 0x44, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0xbf, 0x05, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x70, 0x61, 0x6e, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x10, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x66, 0x0a, 0x0e,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x87, 0x03, 0x0a, 0x1a, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xb5, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x90,
	0x03, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f,
	0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xb0, 0x03, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3b, 0x0a,
	0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x84, 0x04, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a,
	0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x12,
	0x41, 0x64, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x2d,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x65, 0x22, 0xaa, 0x02,
	0x0a, 0x16, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e,
	0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x06, 0x0a, 0x17, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x62, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xfe, 0x04, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x83, 0x01, 0x0a, 0x0e, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x5c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0d, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x8c,
	0x01, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0xb1, 0x01,
	0x0a, 0x0d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x32, 0x0a, 0x15, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x13, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x1a, 0x64, 0x0a, 0x10, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdd, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc2, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x4d, 0x41, 0x4e,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x65, 0x22, 0xbc, 0x02, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd7, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x66, 0x0a,
	0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x4e, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x65, 0x22, 0xf3, 0x04, 0x0a, 0x13, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x72,
	0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x72,
	0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x60,
	0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x90, 0x01, 0x0a, 0x0a, 0x4c, 0x6f, 0x61,
	0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x6c, 0x0a, 0x10, 0x74, 0x65, 0x6e, 0x75, 0x72,
	0x65, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x14,
	0x4d, 0x61, 0x6b, 0x65, 0x44, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xe2,
	0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x64, 0x66, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x64, 0x66, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x64, 0x66, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36,
	0x34, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x64,
	0x66, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x54, 0x65, 0x78, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x64,
	0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63,
	0x49, 0x64, 0x22, 0xeb, 0x02, 0x0a, 0x1f, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x47, 0x0a, 0x20, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x96, 0x03, 0x0a, 0x21, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xa4, 0x01, 0x0a, 0x22, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32,
	0x0a, 0x15, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x43, 0x6f,
	0x70, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xcf, 0x02, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6e, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x86, 0x08, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x73, 0x0a, 0x0b, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x52, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4e, 0x0a, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xdb, 0x05, 0x0a, 0x0a, 0x4c, 0x6f, 0x61, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x78, 0x0a, 0x09, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x81, 0x01, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x1a, 0xb6, 0x01, 0x0a, 0x08, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f,
	0x61, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x50, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x74,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72, 0x1a, 0x95, 0x02, 0x0a,
	0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x62,
	0x75, 0x72, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x62, 0x75,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x10, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x65, 0x6d, 0x69, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x09, 0x65, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x72, 0x6f, 0x69, 0x22, 0xe0, 0x03, 0x0a, 0x1c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41,
	0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x6a, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x61,
	0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x60, 0x0a,
	0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12,
	0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xcb, 0x09, 0x0a, 0x1d, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79,
	0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x75,
	0x0a, 0x09, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x58, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x63, 0x6b, 0x79,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xe2, 0x01, 0x0a,
	0x08, 0x43, 0x6b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x5c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0c, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a,
	0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x1a, 0x81, 0x06, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6b, 0x79, 0x63, 0x4e, 0x6f, 0x12, 0x2c, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x6b, 0x79, 0x63,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07,
	0x6b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x38, 0x0a, 0x07, 0x6d, 0x6f, 0x62, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x84, 0x01, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x66, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x09, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x1a, 0xdc, 0x01, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72,
	0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x51, 0x0a,
	0x16, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x15, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x33, 0x0a, 0x16, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x63, 0x6f, 0x72, 0x72, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x13, 0x70, 0x65, 0x72, 0x6d, 0x43, 0x6f, 0x72, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x53, 0x61, 0x6d, 0x65, 0x22, 0xbe, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66,
	0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xaf, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6c, 0x0a, 0x10, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x06, 0x54, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72,
	0x65, 0x12, 0x6c, 0x0a, 0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0f,
	0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x22,
	0x78, 0x0a, 0x03, 0x52, 0x6f, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x69, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x72, 0x6f, 0x69, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x54, 0x0a, 0x08, 0x72, 0x6f, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x52, 0x6f, 0x69, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x72, 0x6f, 0x69, 0x54, 0x79, 0x70, 0x65, 0x22, 0x74, 0x0a, 0x06, 0x50, 0x66, 0x46,
	0x65, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x07, 0x70, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x50, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06,
	0x70, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x66, 0x5f, 0x66, 0x65, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x66, 0x46, 0x65, 0x65, 0x73, 0x22,
	0xa6, 0x03, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x60,
	0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x6e, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x69, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd5, 0x0c, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x75,
	0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x57, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xf1, 0x0a, 0x0a,
	0x08, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x2c, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a,
	0x0a, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x64, 0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x3d, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3b, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x66, 0x0a, 0x0e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0f,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x15, 0x70, 0x61, 0x69,
	0x64, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x70, 0x61,
	0x69, 0x64, 0x50, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x44, 0x0a, 0x14, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x12, 0x70, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x03, 0x6c, 0x70, 0x69, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x03, 0x6c, 0x70, 0x69, 0x12, 0x37, 0x0a,
	0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0e, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0d, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x12, 0x5f, 0x0a, 0x22, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x1f, 0x70, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72,
	0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x5d, 0x0a, 0x21, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x1e, 0x70, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x39, 0x0a, 0x0e, 0x77, 0x61, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x77,
	0x61, 0x69, 0x76, 0x65, 0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x1a,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x70, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a,
	0x12, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x70,
	0x61, 0x69, 0x64, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12,
	0x42, 0x0a, 0x13, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x11, 0x70, 0x61, 0x69, 0x64, 0x42, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x20, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x1d, 0x70, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x22, 0xa9, 0x03, 0x0a, 0x15, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x63, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc2, 0x01, 0x0a,
	0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x22, 0x88, 0x09, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x84, 0x01, 0x0a,
	0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xce, 0x05, 0x0a, 0x0f,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x60, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x66, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x11, 0x70, 0x61, 0x69, 0x64, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x70, 0x61, 0x69, 0x64, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x6f, 0x75, 0x63, 0x68,
	0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x6f, 0x75,
	0x63, 0x68, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x39, 0x0a, 0x0e, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0d, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x12, 0x41, 0x0a, 0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x33, 0x0a,
	0x0b, 0x6c, 0x70, 0x69, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x6c, 0x70, 0x69, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0f, 0x73, 0x65, 0x74, 0x74, 0x65, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e,
	0x73, 0x65, 0x74, 0x74, 0x65, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c,
	0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x64, 0x0a, 0x16,
	0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0xc5, 0x02, 0x0a, 0x1c, 0x48, 0x61, 0x73, 0x68, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x01, 0x0a, 0x1d, 0x48,
	0x61, 0x73, 0x68, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x68, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x54, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x1a, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0xdc, 0x02, 0x0a, 0x1f, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66,
	0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xac, 0x02, 0x0a, 0x20, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f,
	0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x6b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x57,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x76, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x24, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x54, 0x78, 0x6e, 0x49, 0x64, 0x22, 0xdf, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x69, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x75, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a,
	0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12,
	0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc7, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x1d, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49,
	0x64, 0x22, 0x89, 0x03, 0x0a, 0x19, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x74,
	0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x41, 0x0a,
	0x1a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f,
	0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xc1, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x66, 0x0a, 0x0e,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x81, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x64, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x59, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3d, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x32, 0xe2, 0x27, 0x0a, 0x0a, 0x4c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x12, 0xb5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73,
	0x12, 0x4d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e,
	0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xaf, 0x01, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xa5, 0x01, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x11, 0x41, 0x64,
	0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41,
	0x64, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xab, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4d, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64,
	0x64, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x64, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa6, 0x01, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f,
	0x6b, 0x75, 0x70, 0x12, 0x48, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x49, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x47, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa9,
	0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x0c, 0x4d,
	0x61, 0x6b, 0x65, 0x44, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x45, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4d,
	0x61, 0x6b, 0x65, 0x44, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x46, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x72, 0x61, 0x77, 0x64, 0x6f,
	0x77, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x50, 0x64, 0x66, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x48,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x64, 0x66, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x64, 0x66, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xc1, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72,
	0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70,
	0x12, 0x51, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x52, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xc7, 0x01, 0x0a, 0x1a, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x12, 0x53, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x54, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xa0, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb8, 0x01, 0x0a, 0x15, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41,
	0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x12, 0x4e,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4f,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xaf, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xb5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x4d, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x0e, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x47, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa3, 0x01, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x47, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53,
	0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb8, 0x01, 0x0a, 0x15, 0x48, 0x61, 0x73, 0x68, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x12,
	0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xc1, 0x01, 0x0a, 0x18, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x12, 0x51, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x52, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaf, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x12, 0x4b, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaf, 0x01, 0x0a, 0x12, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x12, 0x4b, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f,
	0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x97, 0x01, 0x0a, 0x0a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x12, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x97, 0x01, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4c, 0x65, 0x61,
	0x64, 0x12, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4c, 0x65, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaf, 0x01, 0x0a,
	0x12, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaf,
	0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x55, 0x64, 0x66, 0x12, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x55, 0x64, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x55, 0x64, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xbe, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x50, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x51,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x9a, 0x01, 0x0a, 0x0b, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x12, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xac,
	0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6f, 0x61, 0x12, 0x4a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x6f, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9a, 0x01,
	0x0a, 0x4b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x5a, 0x4b, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescData = file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDesc
)

func file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescData)
	})
	return file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDescData
}

var file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes = make([]protoimpl.MessageInfo, 83)
var file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_goTypes = []interface{}{
	(AddDetailsResponse_Status)(0),                                          // 0: vendorgateway.lending.preapprovedloan.liquiloans.AddDetailsResponse.Status
	(GetMandateLinkResponse_Status)(0),                                      // 1: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkResponse.Status
	(GetMandateStatusResponse_Status)(0),                                    // 2: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusResponse.Status
	(*SaveChargesRequest)(nil),                                              // 3: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest
	(*SaveChargesResponse)(nil),                                             // 4: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesResponse
	(*CreateRepaymentScheduleRequest)(nil),                                  // 5: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest
	(*CreateRepaymentScheduleResponse)(nil),                                 // 6: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleResponse
	(*UpdateApplicantUdfRequest)(nil),                                       // 7: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest
	(*UpdateApplicantUdfResponse)(nil),                                      // 8: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfResponse
	(*ForeClosureDetailsRequest)(nil),                                       // 9: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsRequest
	(*ForeClosureDetailsResponse)(nil),                                      // 10: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse
	(*CancelLeadRequest)(nil),                                               // 11: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadRequest
	(*CancelLeadResponse)(nil),                                              // 12: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadResponse
	(*UpdateLeadRequest)(nil),                                               // 13: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest
	(*UpdateLeadSchemeDetails)(nil),                                         // 14: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails
	(*UpdateLeadResponse)(nil),                                              // 15: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadResponse
	(*GetCreditLineSchemesRequest)(nil),                                     // 16: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesRequest
	(*GetCreditLineSchemesResponse)(nil),                                    // 17: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse
	(*AddPersonalDetailsRequest)(nil),                                       // 18: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest
	(*AddPersonalDetailsResponse)(nil),                                      // 19: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse
	(*AddBankingDetailsRequest)(nil),                                        // 20: vendorgateway.lending.preapprovedloan.liquiloans.AddBankingDetailsRequest
	(*AddAddressDetailsRequest)(nil),                                        // 21: vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest
	(*AddEmploymentDetailsRequest)(nil),                                     // 22: vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest
	(*AddDetailsResponse)(nil),                                              // 23: vendorgateway.lending.preapprovedloan.liquiloans.AddDetailsResponse
	(*ApplicantLookupRequest)(nil),                                          // 24: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupRequest
	(*ApplicantLookupResponse)(nil),                                         // 25: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse
	(*GetMandateLinkRequest)(nil),                                           // 26: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkRequest
	(*GetMandateLinkResponse)(nil),                                          // 27: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkResponse
	(*GetMandateStatusRequest)(nil),                                         // 28: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusRequest
	(*GetMandateStatusResponse)(nil),                                        // 29: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusResponse
	(*MakeDrawdownRequest)(nil),                                             // 30: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest
	(*MakeDrawdownResponse)(nil),                                            // 31: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownResponse
	(*GetPdfAgreementRequest)(nil),                                          // 32: vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementRequest
	(*GetPdfAgreementResponse)(nil),                                         // 33: vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementResponse
	(*SendBorrowerAgreementOtpRequest)(nil),                                 // 34: vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpRequest
	(*SendBorrowerAgreementOtpResponse)(nil),                                // 35: vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpResponse
	(*VerifyBorrowerAgreementOtpRequest)(nil),                               // 36: vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpRequest
	(*VerifyBorrowerAgreementOtpResponse)(nil),                              // 37: vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpResponse
	(*GetLoanStatusRequest)(nil),                                            // 38: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusRequest
	(*GetLoanStatusResponse)(nil),                                           // 39: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse
	(*VerifyAndDownloadCkycRequest)(nil),                                    // 40: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycRequest
	(*VerifyAndDownloadCkycResponse)(nil),                                   // 41: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse
	(*GetApplicantStatusRequest)(nil),                                       // 42: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusRequest
	(*GetApplicantStatusResponse)(nil),                                      // 43: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusResponse
	(*Tenure)(nil),                                                          // 44: vendorgateway.lending.preapprovedloan.liquiloans.Tenure
	(*Roi)(nil),                                                             // 45: vendorgateway.lending.preapprovedloan.liquiloans.Roi
	(*PfFees)(nil),                                                          // 46: vendorgateway.lending.preapprovedloan.liquiloans.PfFees
	(*GetRepaymentScheduleRequest)(nil),                                     // 47: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleRequest
	(*GetRepaymentScheduleResponse)(nil),                                    // 48: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse
	(*UploadDocumentRequest)(nil),                                           // 49: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentRequest
	(*UploadDocumentResponse)(nil),                                          // 50: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentResponse
	(*SaveCollectionRequest)(nil),                                           // 51: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest
	(*SaveCollectionResponse)(nil),                                          // 52: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionResponse
	(*HashGenerationForOkycRequest)(nil),                                    // 53: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycRequest
	(*HashGenerationForOkycResponse)(nil),                                   // 54: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycResponse
	(*CaptchaGenerationForOkycRequest)(nil),                                 // 55: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycRequest
	(*CaptchaGenerationForOkycResponse)(nil),                                // 56: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycResponse
	(*GenerateOtpForOkycRequest)(nil),                                       // 57: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycRequest
	(*GenerateOtpForOkycResponse)(nil),                                      // 58: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycResponse
	(*ValidateOtpForOkycRequest)(nil),                                       // 59: vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycRequest
	(*ValidateOtpForOkycResponse)(nil),                                      // 60: vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycResponse
	(*GetApplicationSoaRequest)(nil),                                        // 61: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaRequest
	(*GetApplicationSoaResponse)(nil),                                       // 62: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse
	(*CreateRepaymentScheduleRequest_Schedule)(nil),                         // 63: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule
	(*ForeClosureDetailsResponse_Data)(nil),                                 // 64: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data
	(*CancelLeadResponse_Data)(nil),                                         // 65: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadResponse.Data
	(*GetCreditLineSchemesResponse_CreditLineDetails)(nil),                  // 66: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineDetails
	(*GetCreditLineSchemesResponse_CreditLineScheme)(nil),                   // 67: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme
	(*GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints)(nil), // 68: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.AmountConstraints
	(*AddPersonalDetailsResponse_Data)(nil),                                 // 69: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse.Data
	(*ApplicantLookupResponse_Data)(nil),                                    // 70: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data
	(*ApplicantLookupResponse_Data_DetailsStatus)(nil),                      // 71: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data.DetailsStatus
	(*ApplicantLookupResponse_Data_ActivationStatus)(nil),                   // 72: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data.ActivationStatus
	(*MakeDrawdownRequest_LoanTenure)(nil),                                  // 73: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.LoanTenure
	(*GetLoanStatusResponse_LoanStatus)(nil),                                // 74: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus
	(*GetLoanStatusResponse_LoanStatus_LoanData)(nil),                       // 75: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanData
	(*GetLoanStatusResponse_LoanStatus_LoanDetails)(nil),                    // 76: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanDetails
	(*VerifyAndDownloadCkycResponse_CkycData)(nil),                          // 77: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.CkycData
	(*VerifyAndDownloadCkycResponse_PersonalData)(nil),                      // 78: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData
	(*VerifyAndDownloadCkycResponse_PersonalData_Addresses)(nil),            // 79: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.Addresses
	(*GetRepaymentScheduleResponse_Schedule)(nil),                           // 80: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule
	(*SaveCollectionRequest_PaymentSchedule)(nil),                           // 81: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule
	(*HashGenerationForOkycResponse_Data)(nil),                              // 82: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycResponse.Data
	(*CaptchaGenerationForOkycResponse_Data)(nil),                           // 83: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycResponse.Data
	(*GenerateOtpForOkycResponse_Data)(nil),                                 // 84: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycResponse.Data
	(*GetApplicationSoaResponse_Data)(nil),                                  // 85: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse.Data
	(*vendorgateway.RequestHeader)(nil),                                     // 86: vendorgateway.RequestHeader
	(LoanProgram)(0),                                                        // 87: vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	(*date.Date)(nil),                                                       // 88: google.type.Date
	(*money.Money)(nil),                                                     // 89: google.type.Money
	(SchemeVersion)(0),                                                      // 90: vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	(*rpc.Status)(nil),                                                      // 91: rpc.Status
	(TenureFrequency)(0),                                                    // 92: vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency
	(preapprovedloan.IncomeDataSource)(0),                                   // 93: vendorgateway.lending.preapprovedloan.IncomeDataSource
	(FeeType)(0),                                                            // 94: vendorgateway.lending.preapprovedloan.liquiloans.FeeType
	(RoiType)(0),                                                            // 95: vendorgateway.lending.preapprovedloan.liquiloans.RoiType
	(FeeAppliedOn)(0),                                                       // 96: vendorgateway.lending.preapprovedloan.liquiloans.FeeAppliedOn
	(*common.Name)(nil),                                                     // 97: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                                              // 98: api.typesv2.common.PhoneNumber
	(typesv2.Gender)(0),                                                     // 99: api.typesv2.Gender
	(*typesv2.BankAccountDetails)(nil),                                      // 100: api.typesv2.BankAccountDetails
	(*typesv2.PostalAddress)(nil),                                           // 101: api.typesv2.PostalAddress
	(typesv2.AddressType)(0),                                                // 102: api.typesv2.AddressType
	(typesv2.EmploymentType)(0),                                             // 103: api.typesv2.EmploymentType
	(MandateStatus)(0),                                                      // 104: vendorgateway.lending.preapprovedloan.liquiloans.MandateStatus
	(*timestamppb.Timestamp)(nil),                                           // 105: google.protobuf.Timestamp
	(AuthFactorType)(0),                                                     // 106: vendorgateway.lending.preapprovedloan.liquiloans.AuthFactorType
	(ApplicantStatus)(0),                                                    // 107: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantStatus
	(PfType)(0),                                                             // 108: vendorgateway.lending.preapprovedloan.liquiloans.PfType
	(RepaymentScheduleApiVersion)(0),                                        // 109: vendorgateway.lending.preapprovedloan.liquiloans.RepaymentScheduleApiVersion
	(DocumentType)(0),                                                       // 110: vendorgateway.lending.preapprovedloan.liquiloans.DocumentType
	(Status)(0),                                                             // 111: vendorgateway.lending.preapprovedloan.liquiloans.Status
	(common.ImageType)(0),                                                   // 112: api.typesv2.common.ImageType
	(PaymentStatus)(0),                                                      // 113: vendorgateway.lending.preapprovedloan.liquiloans.PaymentStatus
	(PaymentMode)(0),                                                        // 114: vendorgateway.lending.preapprovedloan.liquiloans.PaymentMode
	(*structpb.Value)(nil),                                                  // 115: google.protobuf.Value
}
var file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_depIdxs = []int32{
	86,  // 0: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 1: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	88,  // 2: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest.date:type_name -> google.type.Date
	89,  // 3: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest.amount:type_name -> google.type.Money
	90,  // 4: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 5: vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesResponse.status:type_name -> rpc.Status
	86,  // 6: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 7: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	92,  // 8: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.repayment_frequency:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency
	88,  // 9: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.emi_start_date:type_name -> google.type.Date
	63,  // 10: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.schedules:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule
	90,  // 11: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 12: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleResponse.status:type_name -> rpc.Status
	86,  // 13: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 14: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	89,  // 15: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest.monthly_income:type_name -> google.type.Money
	93,  // 16: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest.income_data_source:type_name -> vendorgateway.lending.preapprovedloan.IncomeDataSource
	90,  // 17: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 18: vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfResponse.status:type_name -> rpc.Status
	86,  // 19: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 20: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 21: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 22: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.status:type_name -> rpc.Status
	64,  // 23: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data
	86,  // 24: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 25: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 26: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 27: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadResponse.status:type_name -> rpc.Status
	65,  // 28: vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadResponse.Data
	86,  // 29: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest.header:type_name -> vendorgateway.RequestHeader
	89,  // 30: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest.amount:type_name -> google.type.Money
	14,  // 31: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest.scheme_details:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails
	87,  // 32: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 33: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	92,  // 34: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.installment_frequency:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency
	89,  // 35: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.processing_fees:type_name -> google.type.Money
	88,  // 36: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.installment_start_date:type_name -> google.type.Date
	94,  // 37: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.processing_fees_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.FeeType
	95,  // 38: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.roi_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.RoiType
	96,  // 39: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.roi_applied_on:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.FeeAppliedOn
	96,  // 40: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.processing_fees_customer_applied_on:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.FeeAppliedOn
	94,  // 41: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadSchemeDetails.gst_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.FeeType
	91,  // 42: vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadResponse.status:type_name -> rpc.Status
	86,  // 43: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 44: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 45: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 46: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.status:type_name -> rpc.Status
	66,  // 47: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.credit_line_details:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineDetails
	67,  // 48: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.credit_line_schemes:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme
	86,  // 49: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	97,  // 50: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.name:type_name -> api.typesv2.common.Name
	98,  // 51: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.contact_number:type_name -> api.typesv2.common.PhoneNumber
	99,  // 52: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.gender:type_name -> api.typesv2.Gender
	88,  // 53: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.dob:type_name -> google.type.Date
	87,  // 54: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	89,  // 55: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.monthly_income:type_name -> google.type.Money
	93,  // 56: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.income_data_source:type_name -> vendorgateway.lending.preapprovedloan.IncomeDataSource
	90,  // 57: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 58: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse.status:type_name -> rpc.Status
	69,  // 59: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse.Data
	86,  // 60: vendorgateway.lending.preapprovedloan.liquiloans.AddBankingDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	100, // 61: vendorgateway.lending.preapprovedloan.liquiloans.AddBankingDetailsRequest.bank_account_details:type_name -> api.typesv2.BankAccountDetails
	87,  // 62: vendorgateway.lending.preapprovedloan.liquiloans.AddBankingDetailsRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 63: vendorgateway.lending.preapprovedloan.liquiloans.AddBankingDetailsRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	86,  // 64: vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	101, // 65: vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest.address:type_name -> api.typesv2.PostalAddress
	102, // 66: vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest.address_type:type_name -> api.typesv2.AddressType
	87,  // 67: vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 68: vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	86,  // 69: vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	103, // 70: vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest.occupation:type_name -> api.typesv2.EmploymentType
	89,  // 71: vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest.monthly_income:type_name -> google.type.Money
	87,  // 72: vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 73: vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 74: vendorgateway.lending.preapprovedloan.liquiloans.AddDetailsResponse.status:type_name -> rpc.Status
	86,  // 75: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 76: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 77: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 78: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.status:type_name -> rpc.Status
	70,  // 79: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data
	86,  // 80: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 81: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 82: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 83: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkResponse.status:type_name -> rpc.Status
	86,  // 84: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 85: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 86: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 87: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusResponse.status:type_name -> rpc.Status
	104, // 88: vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusResponse.mandate_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.MandateStatus
	86,  // 89: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.header:type_name -> vendorgateway.RequestHeader
	73,  // 90: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.tenure:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.LoanTenure
	89,  // 91: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.amount:type_name -> google.type.Money
	87,  // 92: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 93: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 94: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownResponse.status:type_name -> rpc.Status
	86,  // 95: vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 96: vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 97: vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 98: vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementResponse.status:type_name -> rpc.Status
	86,  // 99: vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 100: vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 101: vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 102: vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpResponse.status:type_name -> rpc.Status
	86,  // 103: vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 104: vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 105: vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 106: vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpResponse.status:type_name -> rpc.Status
	86,  // 107: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 108: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 109: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 110: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.status:type_name -> rpc.Status
	74,  // 111: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.loan_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus
	105, // 112: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.last_status_timestamp:type_name -> google.protobuf.Timestamp
	86,  // 113: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycRequest.header:type_name -> vendorgateway.RequestHeader
	106, // 114: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycRequest.auth_factor_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.AuthFactorType
	87,  // 115: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 116: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 117: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.status:type_name -> rpc.Status
	77,  // 118: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.ckyc_data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.CkycData
	86,  // 119: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 120: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 121: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 122: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusResponse.status:type_name -> rpc.Status
	107, // 123: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusResponse.applicant_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.ApplicantStatus
	92,  // 124: vendorgateway.lending.preapprovedloan.liquiloans.Tenure.tenure_frequency:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency
	95,  // 125: vendorgateway.lending.preapprovedloan.liquiloans.Roi.roi_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.RoiType
	108, // 126: vendorgateway.lending.preapprovedloan.liquiloans.PfFees.pf_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.PfType
	86,  // 127: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 128: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	109, // 129: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleRequest.api_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.RepaymentScheduleApiVersion
	90,  // 130: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 131: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.status:type_name -> rpc.Status
	80,  // 132: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.schedules:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule
	86,  // 133: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 134: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	110, // 135: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentRequest.document_type:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.DocumentType
	90,  // 136: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 137: vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentResponse.status:type_name -> rpc.Status
	86,  // 138: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 139: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	81,  // 140: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.payment_schedules:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule
	90,  // 141: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 142: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionResponse.status:type_name -> rpc.Status
	86,  // 143: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 144: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 145: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 146: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycResponse.status:type_name -> rpc.Status
	82,  // 147: vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycResponse.Data
	86,  // 148: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 149: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 150: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 151: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycResponse.status:type_name -> rpc.Status
	83,  // 152: vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycResponse.Data
	86,  // 153: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 154: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 155: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 156: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycResponse.status:type_name -> rpc.Status
	84,  // 157: vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycResponse.Data
	86,  // 158: vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 159: vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 160: vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 161: vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycResponse.status:type_name -> rpc.Status
	86,  // 162: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaRequest.header:type_name -> vendorgateway.RequestHeader
	87,  // 163: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaRequest.loan_program:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.LoanProgram
	90,  // 164: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaRequest.scheme_version:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.SchemeVersion
	91,  // 165: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse.status:type_name -> rpc.Status
	85,  // 166: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse.data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse.Data
	88,  // 167: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule.due_date:type_name -> google.type.Date
	89,  // 168: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule.principal:type_name -> google.type.Money
	89,  // 169: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule.interest:type_name -> google.type.Money
	89,  // 170: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule.total_amount:type_name -> google.type.Money
	89,  // 171: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule.other_charges:type_name -> google.type.Money
	89,  // 172: vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest.Schedule.principal_outstanding:type_name -> google.type.Money
	89,  // 173: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data.total_outstanding:type_name -> google.type.Money
	89,  // 174: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data.principal_outstanding:type_name -> google.type.Money
	89,  // 175: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data.interest_outstanding:type_name -> google.type.Money
	89,  // 176: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data.penalty_charges:type_name -> google.type.Money
	89,  // 177: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data.fees_charges:type_name -> google.type.Money
	89,  // 178: vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse.Data.other_charges:type_name -> google.type.Money
	89,  // 179: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineDetails.upper_limit:type_name -> google.type.Money
	89,  // 180: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineDetails.available_limit:type_name -> google.type.Money
	89,  // 181: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineDetails.block_limit:type_name -> google.type.Money
	45,  // 182: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.roi:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.Roi
	68,  // 183: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.amount_constraints:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.AmountConstraints
	88,  // 184: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.emi_due_date:type_name -> google.type.Date
	44,  // 185: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.tenure:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.Tenure
	46,  // 186: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.pf_fees:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.PfFees
	89,  // 187: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.AmountConstraints.max_emi_allowed:type_name -> google.type.Money
	89,  // 188: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.AmountConstraints.min_drawdown_amount:type_name -> google.type.Money
	89,  // 189: vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse.CreditLineScheme.AmountConstraints.max_drawdown_amount:type_name -> google.type.Money
	97,  // 190: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse.Data.name:type_name -> api.typesv2.common.Name
	98,  // 191: vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse.Data.contact_number:type_name -> api.typesv2.common.PhoneNumber
	71,  // 192: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data.details_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data.DetailsStatus
	72,  // 193: vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data.activation_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse.Data.ActivationStatus
	92,  // 194: vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest.LoanTenure.tenure_frequency:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.TenureFrequency
	75,  // 195: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.loan_data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanData
	76,  // 196: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.loan_details:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanDetails
	111, // 197: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanData.status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.Status
	89,  // 198: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanDetails.amount:type_name -> google.type.Money
	89,  // 199: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanDetails.disbursed_amount:type_name -> google.type.Money
	88,  // 200: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanDetails.disbursement_date:type_name -> google.type.Date
	89,  // 201: vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse.LoanStatus.LoanDetails.emi_amount:type_name -> google.type.Money
	78,  // 202: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.CkycData.personal_data:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData
	112, // 203: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.CkycData.image_type:type_name -> api.typesv2.common.ImageType
	97,  // 204: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.name:type_name -> api.typesv2.common.Name
	88,  // 205: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.kyc_date:type_name -> google.type.Date
	99,  // 206: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.gender:type_name -> api.typesv2.Gender
	88,  // 207: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.dob:type_name -> google.type.Date
	97,  // 208: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.father_name:type_name -> api.typesv2.common.Name
	97,  // 209: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.mother_name:type_name -> api.typesv2.common.Name
	98,  // 210: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.mob_num:type_name -> api.typesv2.common.PhoneNumber
	79,  // 211: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.addresses:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.Addresses
	101, // 212: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.Addresses.permanent_address:type_name -> api.typesv2.PostalAddress
	101, // 213: vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse.PersonalData.Addresses.correspondence_address:type_name -> api.typesv2.PostalAddress
	88,  // 214: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.due_date:type_name -> google.type.Date
	89,  // 215: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.due_amount:type_name -> google.type.Money
	89,  // 216: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.principal_amount:type_name -> google.type.Money
	89,  // 217: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.interest_amount:type_name -> google.type.Money
	113, // 218: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.payment_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.PaymentStatus
	88,  // 219: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.received_date:type_name -> google.type.Date
	89,  // 220: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.received_amount:type_name -> google.type.Money
	89,  // 221: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.paid_principal_amount:type_name -> google.type.Money
	89,  // 222: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.paid_interest_amount:type_name -> google.type.Money
	89,  // 223: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.lpi:type_name -> google.type.Money
	89,  // 224: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.other_charges:type_name -> google.type.Money
	89,  // 225: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.bounce_charges:type_name -> google.type.Money
	89,  // 226: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.post_payment_principal_outstanding:type_name -> google.type.Money
	89,  // 227: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.post_payment_interest_outstanding:type_name -> google.type.Money
	89,  // 228: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.waived_charges:type_name -> google.type.Money
	89,  // 229: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.paid_late_payment_interest:type_name -> google.type.Money
	89,  // 230: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.paid_other_charges:type_name -> google.type.Money
	89,  // 231: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.paid_bounce_charges:type_name -> google.type.Money
	89,  // 232: vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse.Schedule.post_payment_charges_outstanding:type_name -> google.type.Money
	114, // 233: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.payment_mode:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.PaymentMode
	88,  // 234: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.transaction_date:type_name -> google.type.Date
	113, // 235: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.payment_status:type_name -> vendorgateway.lending.preapprovedloan.liquiloans.PaymentStatus
	89,  // 236: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.paid_total_amount:type_name -> google.type.Money
	89,  // 237: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.bounce_charges:type_name -> google.type.Money
	89,  // 238: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.collection_charges:type_name -> google.type.Money
	89,  // 239: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.other_charges:type_name -> google.type.Money
	89,  // 240: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.lpi_charges:type_name -> google.type.Money
	88,  // 241: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.settelment_date:type_name -> google.type.Date
	88,  // 242: vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest.PaymentSchedule.due_date:type_name -> google.type.Date
	115, // 243: vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse.Data.application_id:type_name -> google.protobuf.Value
	16,  // 244: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetCreditLineSchemes:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesRequest
	18,  // 245: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddPersonalDetails:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsRequest
	20,  // 246: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddBankingDetails:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddBankingDetailsRequest
	21,  // 247: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddAddressDetails:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddAddressDetailsRequest
	22,  // 248: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddEmploymentDetails:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddEmploymentDetailsRequest
	24,  // 249: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.ApplicantLookup:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupRequest
	26,  // 250: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetMandateLink:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkRequest
	28,  // 251: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetMandateStatus:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusRequest
	30,  // 252: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.MakeDrawdown:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownRequest
	32,  // 253: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetPdfAgreement:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementRequest
	34,  // 254: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.SendBorrowerAgreementOtp:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpRequest
	36,  // 255: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.VerifyBorrowerAgreementOtp:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpRequest
	38,  // 256: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetLoanStatus:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusRequest
	40,  // 257: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.VerifyAndDownloadCkyc:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycRequest
	42,  // 258: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetApplicantStatus:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusRequest
	47,  // 259: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetRepaymentSchedule:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleRequest
	49,  // 260: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.UploadDocument:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentRequest
	51,  // 261: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.SaveCollection:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionRequest
	53,  // 262: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.HashGenerationForOkyc:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycRequest
	55,  // 263: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.CaptchaGenerationForOkyc:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycRequest
	57,  // 264: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GenerateOtpForOkyc:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycRequest
	59,  // 265: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.ValidateOtpForOkyc:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycRequest
	13,  // 266: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.UpdateLead:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadRequest
	11,  // 267: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.CancelLead:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadRequest
	9,   // 268: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.ForeClosureDetails:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsRequest
	7,   // 269: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.UpdateApplicantUdf:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfRequest
	5,   // 270: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.CreateRepaymentSchedule:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleRequest
	3,   // 271: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.SaveCharges:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesRequest
	61,  // 272: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetApplicationSoa:input_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaRequest
	17,  // 273: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetCreditLineSchemes:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetCreditLineSchemesResponse
	19,  // 274: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddPersonalDetails:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddPersonalDetailsResponse
	23,  // 275: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddBankingDetails:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddDetailsResponse
	23,  // 276: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddAddressDetails:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddDetailsResponse
	23,  // 277: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.AddEmploymentDetails:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.AddDetailsResponse
	25,  // 278: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.ApplicantLookup:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.ApplicantLookupResponse
	27,  // 279: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetMandateLink:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetMandateLinkResponse
	29,  // 280: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetMandateStatus:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetMandateStatusResponse
	31,  // 281: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.MakeDrawdown:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.MakeDrawdownResponse
	33,  // 282: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetPdfAgreement:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetPdfAgreementResponse
	35,  // 283: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.SendBorrowerAgreementOtp:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.SendBorrowerAgreementOtpResponse
	37,  // 284: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.VerifyBorrowerAgreementOtp:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyBorrowerAgreementOtpResponse
	39,  // 285: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetLoanStatus:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetLoanStatusResponse
	41,  // 286: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.VerifyAndDownloadCkyc:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.VerifyAndDownloadCkycResponse
	43,  // 287: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetApplicantStatus:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetApplicantStatusResponse
	48,  // 288: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetRepaymentSchedule:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetRepaymentScheduleResponse
	50,  // 289: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.UploadDocument:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.UploadDocumentResponse
	52,  // 290: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.SaveCollection:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.SaveCollectionResponse
	54,  // 291: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.HashGenerationForOkyc:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.HashGenerationForOkycResponse
	56,  // 292: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.CaptchaGenerationForOkyc:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.CaptchaGenerationForOkycResponse
	58,  // 293: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GenerateOtpForOkyc:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GenerateOtpForOkycResponse
	60,  // 294: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.ValidateOtpForOkyc:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.ValidateOtpForOkycResponse
	15,  // 295: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.UpdateLead:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.UpdateLeadResponse
	12,  // 296: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.CancelLead:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.CancelLeadResponse
	10,  // 297: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.ForeClosureDetails:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.ForeClosureDetailsResponse
	8,   // 298: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.UpdateApplicantUdf:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.UpdateApplicantUdfResponse
	6,   // 299: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.CreateRepaymentSchedule:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.CreateRepaymentScheduleResponse
	4,   // 300: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.SaveCharges:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.SaveChargesResponse
	62,  // 301: vendorgateway.lending.preapprovedloan.liquiloans.Liquiloans.GetApplicationSoa:output_type -> vendorgateway.lending.preapprovedloan.liquiloans.GetApplicationSoaResponse
	273, // [273:302] is the sub-list for method output_type
	244, // [244:273] is the sub-list for method input_type
	244, // [244:244] is the sub-list for extension type_name
	244, // [244:244] is the sub-list for extension extendee
	0,   // [0:244] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_init() }
func file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_init() {
	if File_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto != nil {
		return
	}
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveChargesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveChargesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicantUdfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicantUdfResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeClosureDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeClosureDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadSchemeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineSchemesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineSchemesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPersonalDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPersonalDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBankingDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAddressDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddEmploymentDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDrawdownRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDrawdownResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdfAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdfAgreementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendBorrowerAgreementOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendBorrowerAgreementOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBorrowerAgreementOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBorrowerAgreementOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicantStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicantStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tenure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Roi); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PfFees); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadDocumentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadDocumentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HashGenerationForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HashGenerationForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptchaGenerationForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptchaGenerationForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateOtpForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateOtpForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicationSoaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicationSoaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleRequest_Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeClosureDetailsResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineSchemesResponse_CreditLineDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineSchemesResponse_CreditLineScheme); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPersonalDetailsResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse_Data_DetailsStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse_Data_ActivationStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDrawdownRequest_LoanTenure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse_LoanStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse_LoanStatus_LoanData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse_LoanStatus_LoanDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse_CkycData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse_PersonalData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse_PersonalData_Addresses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionRequest_PaymentSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HashGenerationForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptchaGenerationForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicationSoaResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   83,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto = out.File
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_rawDesc = nil
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_goTypes = nil
	file_api_vendorgateway_lending_preapprovedloan_liquiloans_service_proto_depIdxs = nil
}
