// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/pay/internationalfundtransfer/file_generator/service.proto

package file_generator

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FileGenerator_GenerateLRSCheckFile_FullMethodName             = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateLRSCheckFile"
	FileGenerator_GenerateSOFCheckFile_FullMethodName             = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateSOFCheckFile"
	FileGenerator_GenerateSwiftTransferFile_FullMethodName        = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateSwiftTransferFile"
	FileGenerator_GenerateRefundTransferFile_FullMethodName       = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateRefundTransferFile"
	FileGenerator_GenerateInwardFundTransferFile_FullMethodName   = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateInwardFundTransferFile"
	FileGenerator_GenerateA2Form_FullMethodName                   = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateA2Form"
	FileGenerator_UploadFile_FullMethodName                       = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/UploadFile"
	FileGenerator_GetFileGenerationAttempt_FullMethodName         = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GetFileGenerationAttempt"
	FileGenerator_GetFileGenerationAttempts_FullMethodName        = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GetFileGenerationAttempts"
	FileGenerator_AcknowledgeFileGenerationAttempt_FullMethodName = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/AcknowledgeFileGenerationAttempt"
	FileGenerator_GenerateLRSReportingFile_FullMethodName         = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateLRSReportingFile"
	FileGenerator_GenerateTaxTTMFile_FullMethodName               = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateTaxTTMFile"
	FileGenerator_GenerateGSTReportingFile_FullMethodName         = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateGSTReportingFile"
	FileGenerator_GenerateTCSReportingFile_FullMethodName         = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateTCSReportingFile"
	FileGenerator_GenerateGSTReportingInwardFile_FullMethodName   = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateGSTReportingInwardFile"
	FileGenerator_PreCheckForSwiftFileGeneration_FullMethodName   = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/PreCheckForSwiftFileGeneration"
	FileGenerator_GenerateAggregatedTaxReport_FullMethodName      = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateAggregatedTaxReport"
	FileGenerator_UpdateFileGenerationAttempt_FullMethodName      = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/UpdateFileGenerationAttempt"
	FileGenerator_GenerateMt199MessageAttachment_FullMethodName   = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GenerateMt199MessageAttachment"
	FileGenerator_SoftDeleteFileEntityMapping_FullMethodName      = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/SoftDeleteFileEntityMapping"
	FileGenerator_GetParsedFileContents_FullMethodName            = "/api.pay.internationalfundtransfer.file_generator.FileGenerator/GetParsedFileContents"
)

// FileGeneratorClient is the client API for FileGenerator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileGeneratorClient interface {
	// GenerateLRSCheckFile rpc would take in a vendor and client request id and would generate an lrs check file with all the PAN ids
	// whose LRS is to be fetched from vendor in the format that is expected by the input vendor
	// and would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateLRSCheckFile(ctx context.Context, in *GenerateLRSCheckFileRequest, opts ...grpc.CallOption) (*GenerateLRSCheckFileResponse, error)
	// GenerateSOFCheckFile rpc would take in a vendor and client request id and would generate an sof check file with all the transaction details
	// from whom SOF is to be verified by the vendor in the format that is expected by the input vendor
	// it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateSOFCheckFile(ctx context.Context, in *GenerateSOFCheckFileRequest, opts ...grpc.CallOption) (*GenerateSOFCheckFileResponse, error)
	// GenerateSwiftTransferFile rpc would take in a vendor and client request id and would generate a swift transfer file with all the transaction details
	// from whom swift transaction is to be made by the vendor in the format that is expected by the input vendor
	// it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateSwiftTransferFile(ctx context.Context, in *GenerateSwiftTransferFileRequest, opts ...grpc.CallOption) (*GenerateSwiftTransferFileResponse, error)
	// GenerateRefundTransferFile rpc would take in a vendor name and client request id and would generate a refund transfer file for users whom a refund
	// is to be initiated by the vendor in the format that is expected by the input vendor and would upload the file to a pre-configured
	// s3 bucket. After the file gets uploaded to s3 bucket, it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateRefundTransferFile(ctx context.Context, in *GenerateRefundTransferFileRequest, opts ...grpc.CallOption) (*GenerateRefundTransferFileResponse, error)
	// GenerateInwardFundTransferFile rpc would take in a vendor name and client request id and would generate an Inward Remittance transfer file
	// for all the transactions for whom money is to be remitted from  from vendor in the format that is expected by the input vendor
	// it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateInwardFundTransferFile(ctx context.Context, in *GenerateInwardFundTransferFileRequest, opts ...grpc.CallOption) (*GenerateInwardFundTransferFileResponse, error)
	// GenerateA2Form rpc would take in vendor name, client request id and A2 form generation data and would generate an A2 form respective to that vendor
	// using the doc service with proper doc template and data passed to the rpc. Doc service will generate a pdf file from the data and then upload it to their
	// pre-configured bucket whose presigned s3 url will be made available to the caller api for certain time.
	// this rpc would also read the file from the pre-signed url and store the file within respective bucket of ownership
	GenerateA2Form(ctx context.Context, in *GenerateA2FormRequest, opts ...grpc.CallOption) (*GenerateA2FormResponse, error)
	// UploadFile rpc will take in a particular file type and the file contents as input along with the vendor and upload the file to the desired location as per the file type
	// It will return the file path url to the caller api
	UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error)
	// GetFileGenerationAttempt fetches the file generation attempt for the identifier passed. returns error if no record is found
	GetFileGenerationAttempt(ctx context.Context, in *GetFileGenerationAttemptRequest, opts ...grpc.CallOption) (*GetFileGenerationAttemptResponse, error)
	// GetFileGenerationAttempts fetches the file generation attempts for the file type defined in the request
	GetFileGenerationAttempts(ctx context.Context, in *GetFileGenerationAttemptsRequest, opts ...grpc.CallOption) (*GetFileGenerationAttemptsResponse, error)
	// AcknowledgeFileGenerationAttempt acknowledges the file generation attempt for the given client request id and updates the
	// corresponding file generation attempt with data relevant to the file type
	AcknowledgeFileGenerationAttempt(ctx context.Context, in *AcknowledgeFileGenerationAttemptRequest, opts ...grpc.CallOption) (*AcknowledgeFileGenerationAttemptResponse, error)
	// GenerateLRSReportingFile rpc will generate an overall report file which contains all the information corresponding to PAN ids for whom LRS is verified
	// e.g: Some sample data points that we need to send in this file are: pan id, name of the remitter, date of remittance, purpose code, amount in usd, etc
	// rpc would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// also will update the corresponding state for the file entry in file generation attempt relation
	GenerateLRSReportingFile(ctx context.Context, in *GenerateLRSReportingFileRequest, opts ...grpc.CallOption) (*GenerateLRSReportingFileResponse, error)
	// GenerateTaxTTMFile rcp will generate an overall TAX TTM file which needs to be transferred from pool account to federal's tax accounts
	// When swift transfer happens, we send the buy amount from pool account to the vendor(e.g alpaca) and the taxes are settled from the customer to the
	// banking vendor(e.g federal) using this TAX TTM file
	GenerateTaxTTMFile(ctx context.Context, in *GenerateTaxTTMFileRequest, opts ...grpc.CallOption) (*GenerateTaxTTMFileResponse, error)
	// GenerateGSTReportingFile rpc will generate overall GST reporting file for the successful swift transactions within the system
	GenerateGSTReportingFile(ctx context.Context, in *GenerateGSTReportingFileRequest, opts ...grpc.CallOption) (*GenerateGSTReportingFileResponse, error)
	// GenerateTCSReportingFile rpc will generate overall TCS reporting file for the successful swift transactions within the system
	GenerateTCSReportingFile(ctx context.Context, in *GenerateTCSReportingFileRequest, opts ...grpc.CallOption) (*GenerateTCSReportingFileResponse, error)
	// GenerateGSTReportingInwardFile rpc will generate overall GST reporting inward file for the successful swift transactions within the system
	GenerateGSTReportingInwardFile(ctx context.Context, in *GenerateGSTReportingInwardFileRequest, opts ...grpc.CallOption) (*GenerateGSTReportingInwardFileResponse, error)
	// PreCheckForSwiftFileGeneration RPC is used to check if a SWIFT transfer file is ready to be created or not.
	// A SWIFT transfer file is ready to be generated when:
	// 1. all transactions for all the actors in the incoming LRS response file are processed, and
	// the corresponding workflows have processed those signals
	// 2. all non-rejected transactions in a previously generated SWIFT transfer file have been reset to their previous
	// workflow states
	// This RPC is used to allow/prevent partner bank agents from generating SWIFT transfer file via CX/Sherlock web portal.
	PreCheckForSwiftFileGeneration(ctx context.Context, in *PreCheckForSwiftFileGenerationRequest, opts ...grpc.CallOption) (*PreCheckForSwiftFileGenerationResponse, error)
	// RPC to generate GST & TCS reports for all international fund transfers over a time period
	GenerateAggregatedTaxReport(ctx context.Context, in *GenerateAggregatedTaxReportRequest, opts ...grpc.CallOption) (*GenerateAggregatedTaxReportResponse, error)
	// UpdateFileGenerationAttempt RPC updates the file generation attempt details for given field_masks value
	// Usecase is need to update file status during dropping account from existing inward ttum file, and marking it as a invalid file
	UpdateFileGenerationAttempt(ctx context.Context, in *UpdateFileGenerationAttemptRequest, opts ...grpc.CallOption) (*UpdateFileGenerationAttemptResponse, error)
	// GenerateMT199Attachment creates a file containing the list of users (along with other relevant details)
	// whose money is being remitted in the pooled SWIFT transfer
	// The file is encrypted and can be opened using the password in the MT-199 message
	// Once opened, the file can be used to distribute the pooled SWIFT transfer amount among the recipient users
	GenerateMt199MessageAttachment(ctx context.Context, in *GenerateMt199MessageAttachmentRequest, opts ...grpc.CallOption) (*GenerateMt199MessageAttachmentResponse, error)
	// SoftDeleteFileEntityMapping RPC deletes the file entity mapping records for the given file generation attempt
	SoftDeleteFileEntityMapping(ctx context.Context, in *SoftDeleteFileEntityMappingRequest, opts ...grpc.CallOption) (*SoftDeleteFileEntityMappingResponse, error)
	GetParsedFileContents(ctx context.Context, in *GetParsedFileContentsRequest, opts ...grpc.CallOption) (*GetParsedFileContentsResponse, error)
}

type fileGeneratorClient struct {
	cc grpc.ClientConnInterface
}

func NewFileGeneratorClient(cc grpc.ClientConnInterface) FileGeneratorClient {
	return &fileGeneratorClient{cc}
}

func (c *fileGeneratorClient) GenerateLRSCheckFile(ctx context.Context, in *GenerateLRSCheckFileRequest, opts ...grpc.CallOption) (*GenerateLRSCheckFileResponse, error) {
	out := new(GenerateLRSCheckFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateLRSCheckFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateSOFCheckFile(ctx context.Context, in *GenerateSOFCheckFileRequest, opts ...grpc.CallOption) (*GenerateSOFCheckFileResponse, error) {
	out := new(GenerateSOFCheckFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateSOFCheckFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateSwiftTransferFile(ctx context.Context, in *GenerateSwiftTransferFileRequest, opts ...grpc.CallOption) (*GenerateSwiftTransferFileResponse, error) {
	out := new(GenerateSwiftTransferFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateSwiftTransferFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateRefundTransferFile(ctx context.Context, in *GenerateRefundTransferFileRequest, opts ...grpc.CallOption) (*GenerateRefundTransferFileResponse, error) {
	out := new(GenerateRefundTransferFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateRefundTransferFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateInwardFundTransferFile(ctx context.Context, in *GenerateInwardFundTransferFileRequest, opts ...grpc.CallOption) (*GenerateInwardFundTransferFileResponse, error) {
	out := new(GenerateInwardFundTransferFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateInwardFundTransferFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateA2Form(ctx context.Context, in *GenerateA2FormRequest, opts ...grpc.CallOption) (*GenerateA2FormResponse, error) {
	out := new(GenerateA2FormResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateA2Form_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error) {
	out := new(UploadFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_UploadFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GetFileGenerationAttempt(ctx context.Context, in *GetFileGenerationAttemptRequest, opts ...grpc.CallOption) (*GetFileGenerationAttemptResponse, error) {
	out := new(GetFileGenerationAttemptResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GetFileGenerationAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GetFileGenerationAttempts(ctx context.Context, in *GetFileGenerationAttemptsRequest, opts ...grpc.CallOption) (*GetFileGenerationAttemptsResponse, error) {
	out := new(GetFileGenerationAttemptsResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GetFileGenerationAttempts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) AcknowledgeFileGenerationAttempt(ctx context.Context, in *AcknowledgeFileGenerationAttemptRequest, opts ...grpc.CallOption) (*AcknowledgeFileGenerationAttemptResponse, error) {
	out := new(AcknowledgeFileGenerationAttemptResponse)
	err := c.cc.Invoke(ctx, FileGenerator_AcknowledgeFileGenerationAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateLRSReportingFile(ctx context.Context, in *GenerateLRSReportingFileRequest, opts ...grpc.CallOption) (*GenerateLRSReportingFileResponse, error) {
	out := new(GenerateLRSReportingFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateLRSReportingFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateTaxTTMFile(ctx context.Context, in *GenerateTaxTTMFileRequest, opts ...grpc.CallOption) (*GenerateTaxTTMFileResponse, error) {
	out := new(GenerateTaxTTMFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateTaxTTMFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateGSTReportingFile(ctx context.Context, in *GenerateGSTReportingFileRequest, opts ...grpc.CallOption) (*GenerateGSTReportingFileResponse, error) {
	out := new(GenerateGSTReportingFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateGSTReportingFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateTCSReportingFile(ctx context.Context, in *GenerateTCSReportingFileRequest, opts ...grpc.CallOption) (*GenerateTCSReportingFileResponse, error) {
	out := new(GenerateTCSReportingFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateTCSReportingFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateGSTReportingInwardFile(ctx context.Context, in *GenerateGSTReportingInwardFileRequest, opts ...grpc.CallOption) (*GenerateGSTReportingInwardFileResponse, error) {
	out := new(GenerateGSTReportingInwardFileResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateGSTReportingInwardFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) PreCheckForSwiftFileGeneration(ctx context.Context, in *PreCheckForSwiftFileGenerationRequest, opts ...grpc.CallOption) (*PreCheckForSwiftFileGenerationResponse, error) {
	out := new(PreCheckForSwiftFileGenerationResponse)
	err := c.cc.Invoke(ctx, FileGenerator_PreCheckForSwiftFileGeneration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateAggregatedTaxReport(ctx context.Context, in *GenerateAggregatedTaxReportRequest, opts ...grpc.CallOption) (*GenerateAggregatedTaxReportResponse, error) {
	out := new(GenerateAggregatedTaxReportResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateAggregatedTaxReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) UpdateFileGenerationAttempt(ctx context.Context, in *UpdateFileGenerationAttemptRequest, opts ...grpc.CallOption) (*UpdateFileGenerationAttemptResponse, error) {
	out := new(UpdateFileGenerationAttemptResponse)
	err := c.cc.Invoke(ctx, FileGenerator_UpdateFileGenerationAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GenerateMt199MessageAttachment(ctx context.Context, in *GenerateMt199MessageAttachmentRequest, opts ...grpc.CallOption) (*GenerateMt199MessageAttachmentResponse, error) {
	out := new(GenerateMt199MessageAttachmentResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GenerateMt199MessageAttachment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) SoftDeleteFileEntityMapping(ctx context.Context, in *SoftDeleteFileEntityMappingRequest, opts ...grpc.CallOption) (*SoftDeleteFileEntityMappingResponse, error) {
	out := new(SoftDeleteFileEntityMappingResponse)
	err := c.cc.Invoke(ctx, FileGenerator_SoftDeleteFileEntityMapping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileGeneratorClient) GetParsedFileContents(ctx context.Context, in *GetParsedFileContentsRequest, opts ...grpc.CallOption) (*GetParsedFileContentsResponse, error) {
	out := new(GetParsedFileContentsResponse)
	err := c.cc.Invoke(ctx, FileGenerator_GetParsedFileContents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileGeneratorServer is the server API for FileGenerator service.
// All implementations should embed UnimplementedFileGeneratorServer
// for forward compatibility
type FileGeneratorServer interface {
	// GenerateLRSCheckFile rpc would take in a vendor and client request id and would generate an lrs check file with all the PAN ids
	// whose LRS is to be fetched from vendor in the format that is expected by the input vendor
	// and would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateLRSCheckFile(context.Context, *GenerateLRSCheckFileRequest) (*GenerateLRSCheckFileResponse, error)
	// GenerateSOFCheckFile rpc would take in a vendor and client request id and would generate an sof check file with all the transaction details
	// from whom SOF is to be verified by the vendor in the format that is expected by the input vendor
	// it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateSOFCheckFile(context.Context, *GenerateSOFCheckFileRequest) (*GenerateSOFCheckFileResponse, error)
	// GenerateSwiftTransferFile rpc would take in a vendor and client request id and would generate a swift transfer file with all the transaction details
	// from whom swift transaction is to be made by the vendor in the format that is expected by the input vendor
	// it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateSwiftTransferFile(context.Context, *GenerateSwiftTransferFileRequest) (*GenerateSwiftTransferFileResponse, error)
	// GenerateRefundTransferFile rpc would take in a vendor name and client request id and would generate a refund transfer file for users whom a refund
	// is to be initiated by the vendor in the format that is expected by the input vendor and would upload the file to a pre-configured
	// s3 bucket. After the file gets uploaded to s3 bucket, it will update the corresponding state for the file entry in file generation attempt relation.
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateRefundTransferFile(context.Context, *GenerateRefundTransferFileRequest) (*GenerateRefundTransferFileResponse, error)
	// GenerateInwardFundTransferFile rpc would take in a vendor name and client request id and would generate an Inward Remittance transfer file
	// for all the transactions for whom money is to be remitted from  from vendor in the format that is expected by the input vendor
	// it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// it will update the corresponding state for the file entry in file generation attempt relation
	// if the client request id is the same as that of a previous client request id and there is already a generated file
	// for that client request id, it will send the previously generated url, or else will generate a new file
	GenerateInwardFundTransferFile(context.Context, *GenerateInwardFundTransferFileRequest) (*GenerateInwardFundTransferFileResponse, error)
	// GenerateA2Form rpc would take in vendor name, client request id and A2 form generation data and would generate an A2 form respective to that vendor
	// using the doc service with proper doc template and data passed to the rpc. Doc service will generate a pdf file from the data and then upload it to their
	// pre-configured bucket whose presigned s3 url will be made available to the caller api for certain time.
	// this rpc would also read the file from the pre-signed url and store the file within respective bucket of ownership
	GenerateA2Form(context.Context, *GenerateA2FormRequest) (*GenerateA2FormResponse, error)
	// UploadFile rpc will take in a particular file type and the file contents as input along with the vendor and upload the file to the desired location as per the file type
	// It will return the file path url to the caller api
	UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error)
	// GetFileGenerationAttempt fetches the file generation attempt for the identifier passed. returns error if no record is found
	GetFileGenerationAttempt(context.Context, *GetFileGenerationAttemptRequest) (*GetFileGenerationAttemptResponse, error)
	// GetFileGenerationAttempts fetches the file generation attempts for the file type defined in the request
	GetFileGenerationAttempts(context.Context, *GetFileGenerationAttemptsRequest) (*GetFileGenerationAttemptsResponse, error)
	// AcknowledgeFileGenerationAttempt acknowledges the file generation attempt for the given client request id and updates the
	// corresponding file generation attempt with data relevant to the file type
	AcknowledgeFileGenerationAttempt(context.Context, *AcknowledgeFileGenerationAttemptRequest) (*AcknowledgeFileGenerationAttemptResponse, error)
	// GenerateLRSReportingFile rpc will generate an overall report file which contains all the information corresponding to PAN ids for whom LRS is verified
	// e.g: Some sample data points that we need to send in this file are: pan id, name of the remitter, date of remittance, purpose code, amount in usd, etc
	// rpc would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
	// also will update the corresponding state for the file entry in file generation attempt relation
	GenerateLRSReportingFile(context.Context, *GenerateLRSReportingFileRequest) (*GenerateLRSReportingFileResponse, error)
	// GenerateTaxTTMFile rcp will generate an overall TAX TTM file which needs to be transferred from pool account to federal's tax accounts
	// When swift transfer happens, we send the buy amount from pool account to the vendor(e.g alpaca) and the taxes are settled from the customer to the
	// banking vendor(e.g federal) using this TAX TTM file
	GenerateTaxTTMFile(context.Context, *GenerateTaxTTMFileRequest) (*GenerateTaxTTMFileResponse, error)
	// GenerateGSTReportingFile rpc will generate overall GST reporting file for the successful swift transactions within the system
	GenerateGSTReportingFile(context.Context, *GenerateGSTReportingFileRequest) (*GenerateGSTReportingFileResponse, error)
	// GenerateTCSReportingFile rpc will generate overall TCS reporting file for the successful swift transactions within the system
	GenerateTCSReportingFile(context.Context, *GenerateTCSReportingFileRequest) (*GenerateTCSReportingFileResponse, error)
	// GenerateGSTReportingInwardFile rpc will generate overall GST reporting inward file for the successful swift transactions within the system
	GenerateGSTReportingInwardFile(context.Context, *GenerateGSTReportingInwardFileRequest) (*GenerateGSTReportingInwardFileResponse, error)
	// PreCheckForSwiftFileGeneration RPC is used to check if a SWIFT transfer file is ready to be created or not.
	// A SWIFT transfer file is ready to be generated when:
	// 1. all transactions for all the actors in the incoming LRS response file are processed, and
	// the corresponding workflows have processed those signals
	// 2. all non-rejected transactions in a previously generated SWIFT transfer file have been reset to their previous
	// workflow states
	// This RPC is used to allow/prevent partner bank agents from generating SWIFT transfer file via CX/Sherlock web portal.
	PreCheckForSwiftFileGeneration(context.Context, *PreCheckForSwiftFileGenerationRequest) (*PreCheckForSwiftFileGenerationResponse, error)
	// RPC to generate GST & TCS reports for all international fund transfers over a time period
	GenerateAggregatedTaxReport(context.Context, *GenerateAggregatedTaxReportRequest) (*GenerateAggregatedTaxReportResponse, error)
	// UpdateFileGenerationAttempt RPC updates the file generation attempt details for given field_masks value
	// Usecase is need to update file status during dropping account from existing inward ttum file, and marking it as a invalid file
	UpdateFileGenerationAttempt(context.Context, *UpdateFileGenerationAttemptRequest) (*UpdateFileGenerationAttemptResponse, error)
	// GenerateMT199Attachment creates a file containing the list of users (along with other relevant details)
	// whose money is being remitted in the pooled SWIFT transfer
	// The file is encrypted and can be opened using the password in the MT-199 message
	// Once opened, the file can be used to distribute the pooled SWIFT transfer amount among the recipient users
	GenerateMt199MessageAttachment(context.Context, *GenerateMt199MessageAttachmentRequest) (*GenerateMt199MessageAttachmentResponse, error)
	// SoftDeleteFileEntityMapping RPC deletes the file entity mapping records for the given file generation attempt
	SoftDeleteFileEntityMapping(context.Context, *SoftDeleteFileEntityMappingRequest) (*SoftDeleteFileEntityMappingResponse, error)
	GetParsedFileContents(context.Context, *GetParsedFileContentsRequest) (*GetParsedFileContentsResponse, error)
}

// UnimplementedFileGeneratorServer should be embedded to have forward compatible implementations.
type UnimplementedFileGeneratorServer struct {
}

func (UnimplementedFileGeneratorServer) GenerateLRSCheckFile(context.Context, *GenerateLRSCheckFileRequest) (*GenerateLRSCheckFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateLRSCheckFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateSOFCheckFile(context.Context, *GenerateSOFCheckFileRequest) (*GenerateSOFCheckFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateSOFCheckFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateSwiftTransferFile(context.Context, *GenerateSwiftTransferFileRequest) (*GenerateSwiftTransferFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateSwiftTransferFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateRefundTransferFile(context.Context, *GenerateRefundTransferFileRequest) (*GenerateRefundTransferFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateRefundTransferFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateInwardFundTransferFile(context.Context, *GenerateInwardFundTransferFileRequest) (*GenerateInwardFundTransferFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateInwardFundTransferFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateA2Form(context.Context, *GenerateA2FormRequest) (*GenerateA2FormResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateA2Form not implemented")
}
func (UnimplementedFileGeneratorServer) UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedFileGeneratorServer) GetFileGenerationAttempt(context.Context, *GetFileGenerationAttemptRequest) (*GetFileGenerationAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileGenerationAttempt not implemented")
}
func (UnimplementedFileGeneratorServer) GetFileGenerationAttempts(context.Context, *GetFileGenerationAttemptsRequest) (*GetFileGenerationAttemptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileGenerationAttempts not implemented")
}
func (UnimplementedFileGeneratorServer) AcknowledgeFileGenerationAttempt(context.Context, *AcknowledgeFileGenerationAttemptRequest) (*AcknowledgeFileGenerationAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcknowledgeFileGenerationAttempt not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateLRSReportingFile(context.Context, *GenerateLRSReportingFileRequest) (*GenerateLRSReportingFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateLRSReportingFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateTaxTTMFile(context.Context, *GenerateTaxTTMFileRequest) (*GenerateTaxTTMFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateTaxTTMFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateGSTReportingFile(context.Context, *GenerateGSTReportingFileRequest) (*GenerateGSTReportingFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateGSTReportingFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateTCSReportingFile(context.Context, *GenerateTCSReportingFileRequest) (*GenerateTCSReportingFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateTCSReportingFile not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateGSTReportingInwardFile(context.Context, *GenerateGSTReportingInwardFileRequest) (*GenerateGSTReportingInwardFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateGSTReportingInwardFile not implemented")
}
func (UnimplementedFileGeneratorServer) PreCheckForSwiftFileGeneration(context.Context, *PreCheckForSwiftFileGenerationRequest) (*PreCheckForSwiftFileGenerationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreCheckForSwiftFileGeneration not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateAggregatedTaxReport(context.Context, *GenerateAggregatedTaxReportRequest) (*GenerateAggregatedTaxReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateAggregatedTaxReport not implemented")
}
func (UnimplementedFileGeneratorServer) UpdateFileGenerationAttempt(context.Context, *UpdateFileGenerationAttemptRequest) (*UpdateFileGenerationAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFileGenerationAttempt not implemented")
}
func (UnimplementedFileGeneratorServer) GenerateMt199MessageAttachment(context.Context, *GenerateMt199MessageAttachmentRequest) (*GenerateMt199MessageAttachmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateMt199MessageAttachment not implemented")
}
func (UnimplementedFileGeneratorServer) SoftDeleteFileEntityMapping(context.Context, *SoftDeleteFileEntityMappingRequest) (*SoftDeleteFileEntityMappingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SoftDeleteFileEntityMapping not implemented")
}
func (UnimplementedFileGeneratorServer) GetParsedFileContents(context.Context, *GetParsedFileContentsRequest) (*GetParsedFileContentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParsedFileContents not implemented")
}

// UnsafeFileGeneratorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileGeneratorServer will
// result in compilation errors.
type UnsafeFileGeneratorServer interface {
	mustEmbedUnimplementedFileGeneratorServer()
}

func RegisterFileGeneratorServer(s grpc.ServiceRegistrar, srv FileGeneratorServer) {
	s.RegisterService(&FileGenerator_ServiceDesc, srv)
}

func _FileGenerator_GenerateLRSCheckFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateLRSCheckFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateLRSCheckFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateLRSCheckFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateLRSCheckFile(ctx, req.(*GenerateLRSCheckFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateSOFCheckFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateSOFCheckFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateSOFCheckFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateSOFCheckFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateSOFCheckFile(ctx, req.(*GenerateSOFCheckFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateSwiftTransferFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateSwiftTransferFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateSwiftTransferFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateSwiftTransferFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateSwiftTransferFile(ctx, req.(*GenerateSwiftTransferFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateRefundTransferFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateRefundTransferFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateRefundTransferFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateRefundTransferFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateRefundTransferFile(ctx, req.(*GenerateRefundTransferFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateInwardFundTransferFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateInwardFundTransferFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateInwardFundTransferFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateInwardFundTransferFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateInwardFundTransferFile(ctx, req.(*GenerateInwardFundTransferFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateA2Form_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateA2FormRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateA2Form(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateA2Form_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateA2Form(ctx, req.(*GenerateA2FormRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_UploadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).UploadFile(ctx, req.(*UploadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GetFileGenerationAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileGenerationAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GetFileGenerationAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GetFileGenerationAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GetFileGenerationAttempt(ctx, req.(*GetFileGenerationAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GetFileGenerationAttempts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileGenerationAttemptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GetFileGenerationAttempts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GetFileGenerationAttempts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GetFileGenerationAttempts(ctx, req.(*GetFileGenerationAttemptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_AcknowledgeFileGenerationAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcknowledgeFileGenerationAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).AcknowledgeFileGenerationAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_AcknowledgeFileGenerationAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).AcknowledgeFileGenerationAttempt(ctx, req.(*AcknowledgeFileGenerationAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateLRSReportingFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateLRSReportingFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateLRSReportingFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateLRSReportingFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateLRSReportingFile(ctx, req.(*GenerateLRSReportingFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateTaxTTMFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateTaxTTMFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateTaxTTMFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateTaxTTMFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateTaxTTMFile(ctx, req.(*GenerateTaxTTMFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateGSTReportingFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateGSTReportingFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateGSTReportingFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateGSTReportingFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateGSTReportingFile(ctx, req.(*GenerateGSTReportingFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateTCSReportingFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateTCSReportingFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateTCSReportingFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateTCSReportingFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateTCSReportingFile(ctx, req.(*GenerateTCSReportingFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateGSTReportingInwardFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateGSTReportingInwardFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateGSTReportingInwardFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateGSTReportingInwardFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateGSTReportingInwardFile(ctx, req.(*GenerateGSTReportingInwardFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_PreCheckForSwiftFileGeneration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreCheckForSwiftFileGenerationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).PreCheckForSwiftFileGeneration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_PreCheckForSwiftFileGeneration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).PreCheckForSwiftFileGeneration(ctx, req.(*PreCheckForSwiftFileGenerationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateAggregatedTaxReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateAggregatedTaxReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateAggregatedTaxReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateAggregatedTaxReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateAggregatedTaxReport(ctx, req.(*GenerateAggregatedTaxReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_UpdateFileGenerationAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFileGenerationAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).UpdateFileGenerationAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_UpdateFileGenerationAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).UpdateFileGenerationAttempt(ctx, req.(*UpdateFileGenerationAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GenerateMt199MessageAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateMt199MessageAttachmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GenerateMt199MessageAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GenerateMt199MessageAttachment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GenerateMt199MessageAttachment(ctx, req.(*GenerateMt199MessageAttachmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_SoftDeleteFileEntityMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SoftDeleteFileEntityMappingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).SoftDeleteFileEntityMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_SoftDeleteFileEntityMapping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).SoftDeleteFileEntityMapping(ctx, req.(*SoftDeleteFileEntityMappingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileGenerator_GetParsedFileContents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetParsedFileContentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileGeneratorServer).GetParsedFileContents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileGenerator_GetParsedFileContents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileGeneratorServer).GetParsedFileContents(ctx, req.(*GetParsedFileContentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FileGenerator_ServiceDesc is the grpc.ServiceDesc for FileGenerator service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileGenerator_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.pay.internationalfundtransfer.file_generator.FileGenerator",
	HandlerType: (*FileGeneratorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateLRSCheckFile",
			Handler:    _FileGenerator_GenerateLRSCheckFile_Handler,
		},
		{
			MethodName: "GenerateSOFCheckFile",
			Handler:    _FileGenerator_GenerateSOFCheckFile_Handler,
		},
		{
			MethodName: "GenerateSwiftTransferFile",
			Handler:    _FileGenerator_GenerateSwiftTransferFile_Handler,
		},
		{
			MethodName: "GenerateRefundTransferFile",
			Handler:    _FileGenerator_GenerateRefundTransferFile_Handler,
		},
		{
			MethodName: "GenerateInwardFundTransferFile",
			Handler:    _FileGenerator_GenerateInwardFundTransferFile_Handler,
		},
		{
			MethodName: "GenerateA2Form",
			Handler:    _FileGenerator_GenerateA2Form_Handler,
		},
		{
			MethodName: "UploadFile",
			Handler:    _FileGenerator_UploadFile_Handler,
		},
		{
			MethodName: "GetFileGenerationAttempt",
			Handler:    _FileGenerator_GetFileGenerationAttempt_Handler,
		},
		{
			MethodName: "GetFileGenerationAttempts",
			Handler:    _FileGenerator_GetFileGenerationAttempts_Handler,
		},
		{
			MethodName: "AcknowledgeFileGenerationAttempt",
			Handler:    _FileGenerator_AcknowledgeFileGenerationAttempt_Handler,
		},
		{
			MethodName: "GenerateLRSReportingFile",
			Handler:    _FileGenerator_GenerateLRSReportingFile_Handler,
		},
		{
			MethodName: "GenerateTaxTTMFile",
			Handler:    _FileGenerator_GenerateTaxTTMFile_Handler,
		},
		{
			MethodName: "GenerateGSTReportingFile",
			Handler:    _FileGenerator_GenerateGSTReportingFile_Handler,
		},
		{
			MethodName: "GenerateTCSReportingFile",
			Handler:    _FileGenerator_GenerateTCSReportingFile_Handler,
		},
		{
			MethodName: "GenerateGSTReportingInwardFile",
			Handler:    _FileGenerator_GenerateGSTReportingInwardFile_Handler,
		},
		{
			MethodName: "PreCheckForSwiftFileGeneration",
			Handler:    _FileGenerator_PreCheckForSwiftFileGeneration_Handler,
		},
		{
			MethodName: "GenerateAggregatedTaxReport",
			Handler:    _FileGenerator_GenerateAggregatedTaxReport_Handler,
		},
		{
			MethodName: "UpdateFileGenerationAttempt",
			Handler:    _FileGenerator_UpdateFileGenerationAttempt_Handler,
		},
		{
			MethodName: "GenerateMt199MessageAttachment",
			Handler:    _FileGenerator_GenerateMt199MessageAttachment_Handler,
		},
		{
			MethodName: "SoftDeleteFileEntityMapping",
			Handler:    _FileGenerator_SoftDeleteFileEntityMapping_Handler,
		},
		{
			MethodName: "GetParsedFileContents",
			Handler:    _FileGenerator_GetParsedFileContents_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/pay/internationalfundtransfer/file_generator/service.proto",
}
