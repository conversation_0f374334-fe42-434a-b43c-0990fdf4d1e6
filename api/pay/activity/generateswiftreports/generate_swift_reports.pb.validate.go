// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/activity/generateswiftreports/generate_swift_reports.proto

package generateswiftreports

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	stage "github.com/epifi/be-common/api/celestial/workflow/stage"

	workflow "github.com/epifi/be-common/api/celestial/workflow"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = stage.Status(0)

	_ = workflow.Stage(0)
)

// Validate checks the field values on VerifyWFStatusUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyWFStatusUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyWFStatusUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyWFStatusUpdateRequestMultiError, or nil if none found.
func (m *VerifyWFStatusUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyWFStatusUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyWFStatusUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyWFStatusUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyWFStatusUpdateRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetClientIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VerifyWFStatusUpdateRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VerifyWFStatusUpdateRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VerifyWFStatusUpdateRequestValidationError{
					field:  fmt.Sprintf("ClientIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SleepDuration

	// no validation rules for NumRetries

	// no validation rules for WantedStage

	// no validation rules for WantedStatus

	if all {
		switch v := interface{}(m.GetWantedStageEnum()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyWFStatusUpdateRequestValidationError{
					field:  "WantedStageEnum",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyWFStatusUpdateRequestValidationError{
					field:  "WantedStageEnum",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWantedStageEnum()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyWFStatusUpdateRequestValidationError{
				field:  "WantedStageEnum",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyWFStatusUpdateRequestMultiError(errors)
	}

	return nil
}

// VerifyWFStatusUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by VerifyWFStatusUpdateRequest.ValidateAll() if
// the designated constraints aren't met.
type VerifyWFStatusUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyWFStatusUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyWFStatusUpdateRequestMultiError) AllErrors() []error { return m }

// VerifyWFStatusUpdateRequestValidationError is the validation error returned
// by VerifyWFStatusUpdateRequest.Validate if the designated constraints
// aren't met.
type VerifyWFStatusUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyWFStatusUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyWFStatusUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyWFStatusUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyWFStatusUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyWFStatusUpdateRequestValidationError) ErrorName() string {
	return "VerifyWFStatusUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyWFStatusUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyWFStatusUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyWFStatusUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyWFStatusUpdateRequestValidationError{}

// Validate checks the field values on VerifyWFStatusUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyWFStatusUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyWFStatusUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyWFStatusUpdateResponseMultiError, or nil if none found.
func (m *VerifyWFStatusUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyWFStatusUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyWFStatusUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyWFStatusUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyWFStatusUpdateResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyWFStatusUpdateResponseMultiError(errors)
	}

	return nil
}

// VerifyWFStatusUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by VerifyWFStatusUpdateResponse.ValidateAll() if
// the designated constraints aren't met.
type VerifyWFStatusUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyWFStatusUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyWFStatusUpdateResponseMultiError) AllErrors() []error { return m }

// VerifyWFStatusUpdateResponseValidationError is the validation error returned
// by VerifyWFStatusUpdateResponse.Validate if the designated constraints
// aren't met.
type VerifyWFStatusUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyWFStatusUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyWFStatusUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyWFStatusUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyWFStatusUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyWFStatusUpdateResponseValidationError) ErrorName() string {
	return "VerifyWFStatusUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyWFStatusUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyWFStatusUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyWFStatusUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyWFStatusUpdateResponseValidationError{}

// Validate checks the field values on GenerateLRSFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateLRSFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateLRSFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateLRSFileRequestMultiError, or nil if none found.
func (m *GenerateLRSFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateLRSFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateLRSFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateLRSFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateLRSFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetClientIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateLRSFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateLRSFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateLRSFileRequestValidationError{
					field:  fmt.Sprintf("ClientIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateLRSFileRequestMultiError(errors)
	}

	return nil
}

// GenerateLRSFileRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateLRSFileRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateLRSFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateLRSFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateLRSFileRequestMultiError) AllErrors() []error { return m }

// GenerateLRSFileRequestValidationError is the validation error returned by
// GenerateLRSFileRequest.Validate if the designated constraints aren't met.
type GenerateLRSFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateLRSFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateLRSFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateLRSFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateLRSFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateLRSFileRequestValidationError) ErrorName() string {
	return "GenerateLRSFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateLRSFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateLRSFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateLRSFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateLRSFileRequestValidationError{}

// Validate checks the field values on GenerateLRSFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateLRSFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateLRSFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateLRSFileResponseMultiError, or nil if none found.
func (m *GenerateLRSFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateLRSFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateLRSFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateLRSFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateLRSFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	for idx, item := range m.GetUsedIftWfClientReqIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateLRSFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateLRSFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateLRSFileResponseValidationError{
					field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateLRSFileResponseMultiError(errors)
	}

	return nil
}

// GenerateLRSFileResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateLRSFileResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateLRSFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateLRSFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateLRSFileResponseMultiError) AllErrors() []error { return m }

// GenerateLRSFileResponseValidationError is the validation error returned by
// GenerateLRSFileResponse.Validate if the designated constraints aren't met.
type GenerateLRSFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateLRSFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateLRSFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateLRSFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateLRSFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateLRSFileResponseValidationError) ErrorName() string {
	return "GenerateLRSFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateLRSFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateLRSFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateLRSFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateLRSFileResponseValidationError{}

// Validate checks the field values on GenerateTTMFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTTMFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTTMFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTTMFileRequestMultiError, or nil if none found.
func (m *GenerateTTMFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTTMFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTTMFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTTMFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTTMFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetClientIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateTTMFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateTTMFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateTTMFileRequestValidationError{
					field:  fmt.Sprintf("ClientIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateTTMFileRequestMultiError(errors)
	}

	return nil
}

// GenerateTTMFileRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateTTMFileRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateTTMFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTTMFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTTMFileRequestMultiError) AllErrors() []error { return m }

// GenerateTTMFileRequestValidationError is the validation error returned by
// GenerateTTMFileRequest.Validate if the designated constraints aren't met.
type GenerateTTMFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTTMFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTTMFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTTMFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTTMFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTTMFileRequestValidationError) ErrorName() string {
	return "GenerateTTMFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTTMFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTTMFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTTMFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTTMFileRequestValidationError{}

// Validate checks the field values on GenerateTTMFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTTMFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTTMFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTTMFileResponseMultiError, or nil if none found.
func (m *GenerateTTMFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTTMFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTTMFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTTMFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTTMFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	for idx, item := range m.GetUsedIftWfClientReqIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateTTMFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateTTMFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateTTMFileResponseValidationError{
					field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateTTMFileResponseMultiError(errors)
	}

	return nil
}

// GenerateTTMFileResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateTTMFileResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateTTMFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTTMFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTTMFileResponseMultiError) AllErrors() []error { return m }

// GenerateTTMFileResponseValidationError is the validation error returned by
// GenerateTTMFileResponse.Validate if the designated constraints aren't met.
type GenerateTTMFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTTMFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTTMFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTTMFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTTMFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTTMFileResponseValidationError) ErrorName() string {
	return "GenerateTTMFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTTMFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTTMFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTTMFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTTMFileResponseValidationError{}

// Validate checks the field values on GenerateGSTFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateGSTFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateGSTFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateGSTFileRequestMultiError, or nil if none found.
func (m *GenerateGSTFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateGSTFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateGSTFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateGSTFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateGSTFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetClientIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateGSTFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateGSTFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateGSTFileRequestValidationError{
					field:  fmt.Sprintf("ClientIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateGSTFileRequestMultiError(errors)
	}

	return nil
}

// GenerateGSTFileRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateGSTFileRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateGSTFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateGSTFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateGSTFileRequestMultiError) AllErrors() []error { return m }

// GenerateGSTFileRequestValidationError is the validation error returned by
// GenerateGSTFileRequest.Validate if the designated constraints aren't met.
type GenerateGSTFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateGSTFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateGSTFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateGSTFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateGSTFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateGSTFileRequestValidationError) ErrorName() string {
	return "GenerateGSTFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateGSTFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateGSTFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateGSTFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateGSTFileRequestValidationError{}

// Validate checks the field values on GenerateGSTFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateGSTFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateGSTFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateGSTFileResponseMultiError, or nil if none found.
func (m *GenerateGSTFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateGSTFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateGSTFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateGSTFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateGSTFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	for idx, item := range m.GetUsedIftWfClientReqIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateGSTFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateGSTFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateGSTFileResponseValidationError{
					field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateGSTFileResponseMultiError(errors)
	}

	return nil
}

// GenerateGSTFileResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateGSTFileResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateGSTFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateGSTFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateGSTFileResponseMultiError) AllErrors() []error { return m }

// GenerateGSTFileResponseValidationError is the validation error returned by
// GenerateGSTFileResponse.Validate if the designated constraints aren't met.
type GenerateGSTFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateGSTFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateGSTFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateGSTFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateGSTFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateGSTFileResponseValidationError) ErrorName() string {
	return "GenerateGSTFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateGSTFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateGSTFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateGSTFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateGSTFileResponseValidationError{}

// Validate checks the field values on GenerateTCSFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTCSFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTCSFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTCSFileRequestMultiError, or nil if none found.
func (m *GenerateTCSFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTCSFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTCSFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTCSFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTCSFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetClientIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateTCSFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateTCSFileRequestValidationError{
						field:  fmt.Sprintf("ClientIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateTCSFileRequestValidationError{
					field:  fmt.Sprintf("ClientIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateTCSFileRequestMultiError(errors)
	}

	return nil
}

// GenerateTCSFileRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateTCSFileRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateTCSFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTCSFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTCSFileRequestMultiError) AllErrors() []error { return m }

// GenerateTCSFileRequestValidationError is the validation error returned by
// GenerateTCSFileRequest.Validate if the designated constraints aren't met.
type GenerateTCSFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTCSFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTCSFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTCSFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTCSFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTCSFileRequestValidationError) ErrorName() string {
	return "GenerateTCSFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTCSFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTCSFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTCSFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTCSFileRequestValidationError{}

// Validate checks the field values on GenerateTCSFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTCSFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTCSFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTCSFileResponseMultiError, or nil if none found.
func (m *GenerateTCSFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTCSFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTCSFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTCSFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTCSFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	for idx, item := range m.GetUsedIftWfClientReqIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateTCSFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateTCSFileResponseValidationError{
						field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateTCSFileResponseValidationError{
					field:  fmt.Sprintf("UsedIftWfClientReqIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateTCSFileResponseMultiError(errors)
	}

	return nil
}

// GenerateTCSFileResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateTCSFileResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateTCSFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTCSFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTCSFileResponseMultiError) AllErrors() []error { return m }

// GenerateTCSFileResponseValidationError is the validation error returned by
// GenerateTCSFileResponse.Validate if the designated constraints aren't met.
type GenerateTCSFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTCSFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTCSFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTCSFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTCSFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTCSFileResponseValidationError) ErrorName() string {
	return "GenerateTCSFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTCSFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTCSFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTCSFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTCSFileResponseValidationError{}

// Validate checks the field values on GenerateMt199MessageAttachmentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateMt199MessageAttachmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateMt199MessageAttachmentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateMt199MessageAttachmentRequestMultiError, or nil if none found.
func (m *GenerateMt199MessageAttachmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateMt199MessageAttachmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMt199MessageAttachmentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMt199MessageAttachmentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMt199MessageAttachmentRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SwiftTransactionId

	for idx, item := range m.GetIftWfClientReqIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateMt199MessageAttachmentRequestValidationError{
						field:  fmt.Sprintf("IftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateMt199MessageAttachmentRequestValidationError{
						field:  fmt.Sprintf("IftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateMt199MessageAttachmentRequestValidationError{
					field:  fmt.Sprintf("IftWfClientReqIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GenerateMt199MessageAttachmentRequestMultiError(errors)
	}

	return nil
}

// GenerateMt199MessageAttachmentRequestMultiError is an error wrapping
// multiple validation errors returned by
// GenerateMt199MessageAttachmentRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateMt199MessageAttachmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateMt199MessageAttachmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateMt199MessageAttachmentRequestMultiError) AllErrors() []error { return m }

// GenerateMt199MessageAttachmentRequestValidationError is the validation error
// returned by GenerateMt199MessageAttachmentRequest.Validate if the
// designated constraints aren't met.
type GenerateMt199MessageAttachmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateMt199MessageAttachmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateMt199MessageAttachmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateMt199MessageAttachmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateMt199MessageAttachmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateMt199MessageAttachmentRequestValidationError) ErrorName() string {
	return "GenerateMt199MessageAttachmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateMt199MessageAttachmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateMt199MessageAttachmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateMt199MessageAttachmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateMt199MessageAttachmentRequestValidationError{}

// Validate checks the field values on GenerateMt199MessageAttachmentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateMt199MessageAttachmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateMt199MessageAttachmentResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GenerateMt199MessageAttachmentResponseMultiError, or nil if none found.
func (m *GenerateMt199MessageAttachmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateMt199MessageAttachmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMt199MessageAttachmentResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMt199MessageAttachmentResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMt199MessageAttachmentResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileGenAttemptId

	if len(errors) > 0 {
		return GenerateMt199MessageAttachmentResponseMultiError(errors)
	}

	return nil
}

// GenerateMt199MessageAttachmentResponseMultiError is an error wrapping
// multiple validation errors returned by
// GenerateMt199MessageAttachmentResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateMt199MessageAttachmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateMt199MessageAttachmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateMt199MessageAttachmentResponseMultiError) AllErrors() []error { return m }

// GenerateMt199MessageAttachmentResponseValidationError is the validation
// error returned by GenerateMt199MessageAttachmentResponse.Validate if the
// designated constraints aren't met.
type GenerateMt199MessageAttachmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateMt199MessageAttachmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateMt199MessageAttachmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateMt199MessageAttachmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateMt199MessageAttachmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateMt199MessageAttachmentResponseValidationError) ErrorName() string {
	return "GenerateMt199MessageAttachmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateMt199MessageAttachmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateMt199MessageAttachmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateMt199MessageAttachmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateMt199MessageAttachmentResponseValidationError{}

// Validate checks the field values on GetOutwardBulkTransactionInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOutwardBulkTransactionInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutwardBulkTransactionInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOutwardBulkTransactionInfoRequestMultiError, or nil if none found.
func (m *GetOutwardBulkTransactionInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutwardBulkTransactionInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOutwardBulkTransactionInfoRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOutwardBulkTransactionInfoRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOutwardBulkTransactionInfoRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIftWfClientReqIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOutwardBulkTransactionInfoRequestValidationError{
						field:  fmt.Sprintf("IftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOutwardBulkTransactionInfoRequestValidationError{
						field:  fmt.Sprintf("IftWfClientReqIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOutwardBulkTransactionInfoRequestValidationError{
					field:  fmt.Sprintf("IftWfClientReqIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOutwardBulkTransactionInfoRequestMultiError(errors)
	}

	return nil
}

// GetOutwardBulkTransactionInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetOutwardBulkTransactionInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOutwardBulkTransactionInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutwardBulkTransactionInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutwardBulkTransactionInfoRequestMultiError) AllErrors() []error { return m }

// GetOutwardBulkTransactionInfoRequestValidationError is the validation error
// returned by GetOutwardBulkTransactionInfoRequest.Validate if the designated
// constraints aren't met.
type GetOutwardBulkTransactionInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutwardBulkTransactionInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutwardBulkTransactionInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutwardBulkTransactionInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutwardBulkTransactionInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutwardBulkTransactionInfoRequestValidationError) ErrorName() string {
	return "GetOutwardBulkTransactionInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutwardBulkTransactionInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutwardBulkTransactionInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutwardBulkTransactionInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutwardBulkTransactionInfoRequestValidationError{}

// Validate checks the field values on GetOutwardBulkTransactionInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOutwardBulkTransactionInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutwardBulkTransactionInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOutwardBulkTransactionInfoResponseMultiError, or nil if none found.
func (m *GetOutwardBulkTransactionInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutwardBulkTransactionInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOutwardBulkTransactionInfoResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOutwardBulkTransactionInfoResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOutwardBulkTransactionInfoResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTtumTransactionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOutwardBulkTransactionInfoResponseValidationError{
					field:  "TtumTransactionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOutwardBulkTransactionInfoResponseValidationError{
					field:  "TtumTransactionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTtumTransactionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOutwardBulkTransactionInfoResponseValidationError{
				field:  "TtumTransactionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOutwardBulkTransactionInfoResponseMultiError(errors)
	}

	return nil
}

// GetOutwardBulkTransactionInfoResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetOutwardBulkTransactionInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOutwardBulkTransactionInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutwardBulkTransactionInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutwardBulkTransactionInfoResponseMultiError) AllErrors() []error { return m }

// GetOutwardBulkTransactionInfoResponseValidationError is the validation error
// returned by GetOutwardBulkTransactionInfoResponse.Validate if the
// designated constraints aren't met.
type GetOutwardBulkTransactionInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutwardBulkTransactionInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutwardBulkTransactionInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutwardBulkTransactionInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutwardBulkTransactionInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutwardBulkTransactionInfoResponseValidationError) ErrorName() string {
	return "GetOutwardBulkTransactionInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutwardBulkTransactionInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutwardBulkTransactionInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutwardBulkTransactionInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutwardBulkTransactionInfoResponseValidationError{}
