// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/salaryestimation/salary_estimation_attempts.pb.go

package salaryestimation

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the Source in string format in DB
func (p Source) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Source while reading from DB
func (p *Source) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Source_value[val]
	if !ok {
		return fmt.Errorf("unexpected Source value: %s", val)
	}
	*p = Source(valInt)
	return nil
}

// Marshaler interface implementation for Source
func (x Source) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Source
func (x *Source) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Source(Source_value[val])
	return nil
}

// Valuer interface implementation for storing the AttemptStatus in string format in DB
func (p AttemptStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AttemptStatus while reading from DB
func (p *AttemptStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AttemptStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected AttemptStatus value: %s", val)
	}
	*p = AttemptStatus(valInt)
	return nil
}

// Marshaler interface implementation for AttemptStatus
func (x AttemptStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AttemptStatus
func (x *AttemptStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AttemptStatus(AttemptStatus_value[val])
	return nil
}
