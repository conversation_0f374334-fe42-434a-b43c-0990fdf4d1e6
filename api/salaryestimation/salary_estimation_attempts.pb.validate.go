// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/salaryestimation/salary_estimation_attempts.proto

package salaryestimation

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SalaryEstimationAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SalaryEstimationAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SalaryEstimationAttempt with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SalaryEstimationAttemptMultiError, or nil if none found.
func (m *SalaryEstimationAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *SalaryEstimationAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := SalaryEstimationAttemptValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Source

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := SalaryEstimationAttemptValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SalaryEstimationAttemptMultiError(errors)
	}

	return nil
}

// SalaryEstimationAttemptMultiError is an error wrapping multiple validation
// errors returned by SalaryEstimationAttempt.ValidateAll() if the designated
// constraints aren't met.
type SalaryEstimationAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SalaryEstimationAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SalaryEstimationAttemptMultiError) AllErrors() []error { return m }

// SalaryEstimationAttemptValidationError is the validation error returned by
// SalaryEstimationAttempt.Validate if the designated constraints aren't met.
type SalaryEstimationAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SalaryEstimationAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SalaryEstimationAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SalaryEstimationAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SalaryEstimationAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SalaryEstimationAttemptValidationError) ErrorName() string {
	return "SalaryEstimationAttemptValidationError"
}

// Error satisfies the builtin error interface
func (e SalaryEstimationAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSalaryEstimationAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SalaryEstimationAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SalaryEstimationAttemptValidationError{}
