//nolint:gocritic, dupl
package comms

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
)

// Interface which implements all email types we will have
// Adding a new version for a template or adding a new template is as simple as adding new methods here
// service level code does not need to change for any new template additions
type IEmailOption interface {
	GetType() EmailType
	GetActualEmailBody(templateBody string) (string, error)
	GetTemplateVersion() TemplateVersion
	GetEmailSubject(subject string) (string, error)
}

func (o *EmailOption_SherlockVerificationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SherlockVerificationEmailOption.GetEmailType()
}

func (o *EmailOption_SherlockVerificationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SherlockVerificationEmailOption.GetOption().(type) {
	case *SherlockVerificationEmailOption_SherlockVerificationEmailOptionV1:
		return o.SherlockVerificationEmailOption.GetSherlockVerificationEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SherlockVerificationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sherlock verification email option is nil")
	}
	switch o.SherlockVerificationEmailOption.GetOption().(type) {
	case *SherlockVerificationEmailOption_SherlockVerificationEmailOptionV1:
		name := o.SherlockVerificationEmailOption.GetSherlockVerificationEmailOptionV1().GetNameInBody()
		url := o.SherlockVerificationEmailOption.GetSherlockVerificationEmailOptionV1().GetVerificationLink()
		expiryMinutes := o.SherlockVerificationEmailOption.GetSherlockVerificationEmailOptionV1().GetExpiryMinutes()
		expiryMinutesString := strconv.FormatInt(expiryMinutes, 10)
		msg := strings.Replace(templateBody, "{#name#}", name, 2)
		msg = strings.Replace(msg, "{#verification_url#}", url, 1)
		msg = strings.Replace(msg, "{#expiry_minutes#}", expiryMinutesString, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for sherlock verification email template")
}

func (o *EmailOption_SherlockVerificationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sherlock verification email option is nil")
	}
	switch o.SherlockVerificationEmailOption.GetOption().(type) {
	case *SherlockVerificationEmailOption_SherlockVerificationEmailOptionV1:
		name := o.SherlockVerificationEmailOption.GetSherlockVerificationEmailOptionV1().GetNameInSubject()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for sherlock verification email template")
}

func (o *EmailOption_WaitlistUserAcceptedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserAcceptedEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserAcceptedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserAcceptedEmailOption.GetOption().(type) {
	case *WaitlistUserAcceptedEmailOption_WaitlistUserAcceptedOptionV1:
		return o.WaitlistUserAcceptedEmailOption.GetWaitlistUserAcceptedOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistUserAcceptedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user accepted email option is nil")
	}
	switch o.WaitlistUserAcceptedEmailOption.GetOption().(type) {
	case *WaitlistUserAcceptedEmailOption_WaitlistUserAcceptedOptionV1:
		name := o.WaitlistUserAcceptedEmailOption.GetWaitlistUserAcceptedOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user accepted email template")
}

func (o *EmailOption_WaitlistUserAcceptedEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

func (o *EmailOption_WaitlistUserRejectedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserRejectedEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserRejectedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserRejectedEmailOption.GetOption().(type) {
	case *WaitlistUserRejectedEmailOption_WaitlistUserRejectedOptionV1:
		return o.WaitlistUserRejectedEmailOption.GetWaitlistUserRejectedOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistUserRejectedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user rejected email option is nil")
	}
	switch o.WaitlistUserRejectedEmailOption.GetOption().(type) {
	case *WaitlistUserRejectedEmailOption_WaitlistUserRejectedOptionV1:
		return templateBody, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user rejected email template")
}

func (o *EmailOption_WaitlistUserRejectedEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

func (o *EmailOption_WaitlistUserCboAcceptedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserCboAcceptedEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserCboAcceptedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserCboAcceptedEmailOption.GetOption().(type) {
	case *WaitlistUserCBOAcceptedEmailOption_WaitlistUserCboAcceptedOptionV1:
		return o.WaitlistUserCboAcceptedEmailOption.GetWaitlistUserCboAcceptedOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistUserCboAcceptedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user cbo accepted email option is nil")
	}
	switch o.WaitlistUserCboAcceptedEmailOption.GetOption().(type) {
	case *WaitlistUserCBOAcceptedEmailOption_WaitlistUserCboAcceptedOptionV1:
		name := o.WaitlistUserCboAcceptedEmailOption.GetWaitlistUserCboAcceptedOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistUserCboAcceptedEmailOption.GetWaitlistUserCboAcceptedOptionV1().GetVoucherCode()
		msg = strings.Replace(msg, "{#voucher_code#}", vc, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo accepted email template")
}

func (o *EmailOption_WaitlistUserCboAcceptedEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

func (o *EmailOption_WaitlistUserCboRejectedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserCboRejectedEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserCboRejectedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserCboRejectedEmailOption.GetOption().(type) {
	case *WaitlistUserCBORejectedEmailOption_WaitlistUserCboRejectedOptionV1:
		return o.WaitlistUserCboRejectedEmailOption.GetWaitlistUserCboRejectedOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistUserCboRejectedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user cbo rejected email option is nil")
	}
	switch o.WaitlistUserCboRejectedEmailOption.GetOption().(type) {
	case *WaitlistUserCBORejectedEmailOption_WaitlistUserCboRejectedOptionV1:
		name := o.WaitlistUserCboRejectedEmailOption.GetWaitlistUserCboRejectedOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistUserCboRejectedEmailOption.GetWaitlistUserCboRejectedOptionV1().GetVoucherCode()
		msg = strings.Replace(msg, "{#voucher_code#}", vc, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo rejected email template")
}

func (o *EmailOption_WaitlistUserCboRejectedEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

func (o *EmailOption_AccountStatementEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.AccountStatementEmailOption.GetEmailType()
}

func (o *EmailOption_AccountStatementEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AccountStatementEmailOption.GetOption().(type) {
	case *AccountStatementEmailOption_AccountStatementEmailOptionV1:
		return o.AccountStatementEmailOption.GetAccountStatementEmailOptionV1().GetTemplateVersion()
	case *AccountStatementEmailOption_AccountStatementEmailOptionV2:
		return o.AccountStatementEmailOption.GetAccountStatementEmailOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_AccountStatementEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("account statement email option is nil")
	}
	switch o.AccountStatementEmailOption.GetOption().(type) {
	case *AccountStatementEmailOption_AccountStatementEmailOptionV1:
		medianAmount := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV1().GetMedianAmount()
		numberOfTransactions := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV1().GetNumberOfTransactions()
		msg := strings.Replace(templateBody, "{#medianAmount#}", strconv.Itoa(int(medianAmount)), 1)
		msg = strings.Replace(msg, "{#numberOfTransactions#}", strconv.Itoa(int(numberOfTransactions)), 1)
		return msg, nil
	case *AccountStatementEmailOption_AccountStatementEmailOptionV2:
		medianAmount := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV2().GetMedianAmount()
		numberOfTransactions := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV2().GetNumberOfTransactions()
		mfRedirectUrl := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV2().GetMutualFundsPageUrl()
		msg := strings.Replace(templateBody, "{#medianAmount#}", strconv.Itoa(int(medianAmount)), 1)
		msg = strings.Replace(msg, "{#numberOfTransactions#}", strconv.Itoa(int(numberOfTransactions)), 1)
		msg = strings.Replace(msg, "{#mutualFundsPageUrl}", mfRedirectUrl, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for account statement email template")
}

func (o *EmailOption_AccountStatementEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("account statement email option is nil")
	}
	switch o.AccountStatementEmailOption.GetOption().(type) {
	case *AccountStatementEmailOption_AccountStatementEmailOptionV1:
		fromDate := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV1().GetFromDate()
		toDate := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV1().GetToDate()
		sub := strings.Replace(subject, "{#fromDate#}", fromDate, 1)
		sub = strings.Replace(sub, "{#toDate#}", toDate, 1)
		return sub, nil
	case *AccountStatementEmailOption_AccountStatementEmailOptionV2:
		fromDate := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV2().GetFromDate()
		toDate := o.AccountStatementEmailOption.GetAccountStatementEmailOptionV2().GetToDate()
		sub := strings.Replace(subject, "{#fromDate#}", fromDate, 1)
		sub = strings.Replace(sub, "{#toDate#}", toDate, 1)
		return sub, nil

	}
	return "", fmt.Errorf("no valid version found for account statement email template")
}

func (o *EmailOption_DepositStatementEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.DepositStatementEmailOption.GetEmailType()
}

func (o *EmailOption_DepositStatementEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DepositStatementEmailOption.GetOption().(type) {
	case *DepositStatementEmailOption_DepositStatementEmailOptionV1:
		return o.DepositStatementEmailOption.GetDepositStatementEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_DepositStatementEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("deposit statement email option is nil")
	}
	switch o.DepositStatementEmailOption.GetOption().(type) {
	case *DepositStatementEmailOption_DepositStatementEmailOptionV1:
		medianAmount := o.DepositStatementEmailOption.GetDepositStatementEmailOptionV1().GetMedianAmount()
		numberOfTransactions := o.DepositStatementEmailOption.GetDepositStatementEmailOptionV1().GetNumberOfTransactions()
		mfRedirectUrl := o.DepositStatementEmailOption.GetDepositStatementEmailOptionV1().GetMutualFundsPageUrl()
		msg := strings.Replace(templateBody, "{#medianAmount#}", strconv.Itoa(int(medianAmount)), 1)
		msg = strings.Replace(msg, "{#numberOfTransactions#}", strconv.Itoa(int(numberOfTransactions)), 1)
		msg = strings.Replace(msg, "{#mutualFundsPageUrl}", mfRedirectUrl, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for deposit statement email template")
}

func (o *EmailOption_DepositStatementEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("deposit statement email option is nil")
	}
	switch o.DepositStatementEmailOption.GetOption().(type) {
	case *DepositStatementEmailOption_DepositStatementEmailOptionV1:
		fromDate := o.DepositStatementEmailOption.GetDepositStatementEmailOptionV1().GetFromDate()
		toDate := o.DepositStatementEmailOption.GetDepositStatementEmailOptionV1().GetToDate()
		sub := strings.Replace(subject, "{#fromDate#}", fromDate, 1)
		sub = strings.Replace(sub, "{#toDate#}", toDate, 1)
		return sub, nil

	}
	return "", fmt.Errorf("no valid version found for deposit statement email template")
}

func (o *EmailOption_InternalOfferRedemptionReportEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.InternalOfferRedemptionReportEmailOption.GetEmailType()
}

func (o *EmailOption_InternalOfferRedemptionReportEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.InternalOfferRedemptionReportEmailOption.GetOption().(type) {
	case *InternalOfferRedemptionReportEmailOption_InternalOfferRedemptionReportEmailOptionV1:
		return o.InternalOfferRedemptionReportEmailOption.GetInternalOfferRedemptionReportEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_InternalOfferRedemptionReportEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("internal offer redemption report email option is nil")
	}
	// do not require parameterized handling on template body for now.
	return templateBody, nil
}

func (o *EmailOption_InternalOfferRedemptionReportEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("account statement email option is nil")
	}
	switch o.InternalOfferRedemptionReportEmailOption.GetOption().(type) {
	case *InternalOfferRedemptionReportEmailOption_InternalOfferRedemptionReportEmailOptionV1:
		reportDate := o.InternalOfferRedemptionReportEmailOption.GetInternalOfferRedemptionReportEmailOptionV1().GetReportDate()
		sub := strings.Replace(subject, "{#reportDate#}", reportDate, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for internal offer redemption report email template")
}

// *************************************** //

func (o *EmailOption_InternalReportEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.InternalReportEmailOption.GetEmailType()
}

func (o *EmailOption_InternalReportEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.InternalReportEmailOption.GetOption().(type) {
	case *InternalReportEmailOption_InternalReportEmailOptionV1:
		return o.InternalReportEmailOption.GetInternalReportEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_InternalReportEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("internal report email option is nil")
	}
	// do not require parameterized handling on template body for now.
	return templateBody, nil
}

func (o *EmailOption_InternalReportEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("account statement email option is nil")
	}
	switch o.InternalReportEmailOption.GetOption().(type) {
	case *InternalReportEmailOption_InternalReportEmailOptionV1:
		reportTitle := o.InternalReportEmailOption.GetInternalReportEmailOptionV1().GetReportTitle()
		reportDate := o.InternalReportEmailOption.GetInternalReportEmailOptionV1().GetReportDate()
		sub := strings.Replace(subject, "{#reportTitle#}", reportTitle, 1)
		sub = strings.Replace(sub, "{#reportDate#}", reportDate, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for internal report email template")
}

// *************************************** //

func (o *EmailOption_WaitlistUserAppAccessEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserAppAccessEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserAppAccessEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserAppAccessEmailOption.GetOption().(type) {
	case *WaitlistUserAppAccessEmailOption_WaitlistUserAppAccessOptionV1:
		return o.WaitlistUserAppAccessEmailOption.GetWaitlistUserAppAccessOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistUserAppAccessEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user cbo accepted email option is nil")
	}
	switch o.WaitlistUserAppAccessEmailOption.GetOption().(type) {
	case *WaitlistUserAppAccessEmailOption_WaitlistUserAppAccessOptionV1:
		name := o.WaitlistUserAppAccessEmailOption.GetWaitlistUserAppAccessOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistUserAppAccessEmailOption.GetWaitlistUserAppAccessOptionV1().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo accepted email template")
}

func (o *EmailOption_WaitlistUserAppAccessEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user cbo accepted email option is nil")
	}
	switch o.WaitlistUserAppAccessEmailOption.GetOption().(type) {
	case *WaitlistUserAppAccessEmailOption_WaitlistUserAppAccessOptionV1:
		name := o.WaitlistUserAppAccessEmailOption.GetWaitlistUserAppAccessOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo accepted email template")
}

// *************************************** //

func (o *EmailOption_WaitlistUserGmailInputEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserGmailInputEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserGmailInputEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserGmailInputEmailOption.GetOption().(type) {
	case *WaitlistUserGmailInputEmailOption_WaitlistUserGmailInputOptionV1:
		return o.WaitlistUserGmailInputEmailOption.GetWaitlistUserGmailInputOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistUserGmailInputEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user cbo accepted email option is nil")
	}
	switch o.WaitlistUserGmailInputEmailOption.GetOption().(type) {
	case *WaitlistUserGmailInputEmailOption_WaitlistUserGmailInputOptionV1:
		name := o.WaitlistUserGmailInputEmailOption.GetWaitlistUserGmailInputOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistUserGmailInputEmailOption.GetWaitlistUserGmailInputOptionV1().GetGmailInputLink()
		msg = strings.Replace(msg, "{#gmail_input_link#}", vc, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo accepted email template")
}

func (o *EmailOption_WaitlistUserGmailInputEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user cbo accepted email option is nil")
	}
	switch o.WaitlistUserGmailInputEmailOption.GetOption().(type) {
	case *WaitlistUserGmailInputEmailOption_WaitlistUserGmailInputOptionV1:
		name := o.WaitlistUserGmailInputEmailOption.GetWaitlistUserGmailInputOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo accepted email template")
}

// *************************************** //

func (o *EmailOption_WaitlistUserFiniteCodeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistUserFiniteCodeEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistUserFiniteCodeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistUserFiniteCodeEmailOption.GetOption().(type) {
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV1:
		return o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV1().GetTemplateVersion()
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV2:
		return o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV2().GetTemplateVersion()
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV3:
		return o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV3().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_WaitlistUserFiniteCodeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user finite code email option is nil")
	}
	switch o.WaitlistUserFiniteCodeEmailOption.GetOption().(type) {
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV1:
		name := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV1().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		finiteCode := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV2:
		name := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV2().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		finiteCode := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV2().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV3:
		name := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV3().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 2)
		vc := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV3().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		finiteCode := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV3().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist user finite code email template")
	}
}

func (o *EmailOption_WaitlistUserFiniteCodeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist user finite code email option is nil")
	}
	switch o.WaitlistUserFiniteCodeEmailOption.GetOption().(type) {
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV1:
		name := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV2:
		name := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV2().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	case *WaitlistUserFiniteCodeEmailOption_WaitlistUserFiniteCodeOptionV3:
		name := o.WaitlistUserFiniteCodeEmailOption.GetWaitlistUserFiniteCodeOptionV3().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist user finite code email template")
	}
}

func (o *EmailOption_VkycScheduleCallEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.VkycScheduleCallEmailOption.GetEmailType()
}

func (o *EmailOption_VkycScheduleCallEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycScheduleCallEmailOption.GetOption().(type) {
	case *VKYCScheduleCallEmailOption_VkycScheduleCallEmailOptionV1:
		return o.VkycScheduleCallEmailOption.GetVkycScheduleCallEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_VkycScheduleCallEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc schedule call email option is nil")
	}
	switch o.VkycScheduleCallEmailOption.GetOption().(type) {
	case *VKYCScheduleCallEmailOption_VkycScheduleCallEmailOptionV1:
		vkycScheduleCallEmailOptionV1 := o.VkycScheduleCallEmailOption.GetVkycScheduleCallEmailOptionV1()
		msg := getMsg(templateBody, vkycScheduleCallEmailOptionV1.GetFirstName(), vkycScheduleCallEmailOptionV1.GetCalendarEventName(),
			vkycScheduleCallEmailOptionV1.GetCalendarEventDetails(), vkycScheduleCallEmailOptionV1.GetDateTime().AsTime())
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for vkyc schedule call email template")
	}
}

func getMsg(templateBody, optFirstName, optCalendarEventName, optCalendarEventDetails string, optDateTime time.Time) string {
	firstName := optFirstName
	dateTime := optDateTime.In(datetime.IST)
	displaydate := dateTime.Format("Mon, Jan 02")
	displayTime := dateTime.Format("03:04 PM")
	calendarStartTime := strings.Split(dateTime.Format("2006-01-02 15:04:05"), " ")
	startTime := strings.ReplaceAll(calendarStartTime[0], "-", "") + "T" +
		strings.ReplaceAll(calendarStartTime[1], ":", "")
	calendarEndTime := strings.Split(dateTime.Add(15*time.Minute).Format("2006-01-02 15:04:05"), " ")
	endTime := strings.ReplaceAll(calendarEndTime[0], "-", "") + "T" +
		strings.ReplaceAll(calendarEndTime[1], ":", "")

	msg := strings.Replace(templateBody, "{#first_name#}", firstName, 1)
	msg = strings.Replace(msg, "{#date#}", displaydate, 1)
	msg = strings.Replace(msg, "{#time#}", displayTime, 1)
	msg = strings.Replace(msg, "{#event_name#}", optCalendarEventName, 1)
	msg = strings.Replace(msg, "{#details#}", optCalendarEventDetails, 1)
	msg = strings.Replace(msg, "{#start_time#}", startTime, 1)
	msg = strings.Replace(msg, "{#end_time#}", endTime, 1)
	return msg
}

func (o *EmailOption_VkycScheduleCallEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc schedule call email option is nil")
	}
	switch o.VkycScheduleCallEmailOption.GetOption().(type) {
	case *VKYCScheduleCallEmailOption_VkycScheduleCallEmailOptionV1:
		firstName := o.VkycScheduleCallEmailOption.GetVkycScheduleCallEmailOptionV1().GetFirstName()
		sub := strings.Replace(subject, "{#first_name#}", firstName, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for vkyc schedule call email template")
	}
}

func (o *EmailOption_VkycScheduleCallReminderEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.VkycScheduleCallReminderEmailOption.GetEmailType()
}

func (o *EmailOption_VkycScheduleCallReminderEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycScheduleCallReminderEmailOption.GetOption().(type) {
	case *VKYCScheduleCallReminderEmailOption_VkycScheduleCallReminderEmailOptionV1:
		return o.VkycScheduleCallReminderEmailOption.GetVkycScheduleCallReminderEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_VkycScheduleCallReminderEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc schedule call reminder email option is nil")
	}
	switch o.VkycScheduleCallReminderEmailOption.GetOption().(type) {
	case *VKYCScheduleCallReminderEmailOption_VkycScheduleCallReminderEmailOptionV1:
		vkycScheduleCallEmailOptionV1 := o.VkycScheduleCallReminderEmailOption.GetVkycScheduleCallReminderEmailOptionV1()
		msg := getMsg(templateBody, vkycScheduleCallEmailOptionV1.GetFirstName(), vkycScheduleCallEmailOptionV1.GetCalendarEventName(),
			vkycScheduleCallEmailOptionV1.GetCalendarEventDetails(), vkycScheduleCallEmailOptionV1.GetDateTime().AsTime())
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for vkyc schedule call reminder email template")
	}
}

func (o *EmailOption_VkycScheduleCallReminderEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

// *************************************** //

func (o *EmailOption_WaitlistCboUserFiniteCodeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistCboUserFiniteCodeEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistCboUserFiniteCodeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistCboUserFiniteCodeEmailOption.GetOption().(type) {
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV1:
		return o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV1().GetTemplateVersion()
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV2:
		return o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV2().GetTemplateVersion()
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV3:
		return o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV3().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_WaitlistCboUserFiniteCodeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo user finite code email option is nil")
	}
	switch o.WaitlistCboUserFiniteCodeEmailOption.GetOption().(type) {
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV1:
		name := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV1().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		finiteCode := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV2:
		name := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		vc := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV2().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		finiteCode := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV2().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV3:
		name := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV3().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 2)
		vc := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV3().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, 1)
		finiteCode := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV3().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist cbo user finite code email template")
	}
}

// nolint:dupl
func (o *EmailOption_WaitlistCboUserFiniteCodeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo user finite code email option is nil")
	}
	switch o.WaitlistCboUserFiniteCodeEmailOption.GetOption().(type) {
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV1:
		name := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV2:
		name := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV2().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	case *WaitlistCboUserFiniteCodeEmailOption_WaitlistCboUserFiniteCodeOptionV3:
		name := o.WaitlistCboUserFiniteCodeEmailOption.GetWaitlistCboUserFiniteCodeOptionV3().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist cbo user finite code email template")
	}
}

func (m *EmailOption_MinKycUserWelcomeEmailOption) GetType() EmailType {
	if m == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return m.MinKycUserWelcomeEmailOption.GetEmailType()
}

func (m *EmailOption_MinKycUserWelcomeEmailOption) GetTemplateVersion() TemplateVersion {
	if m == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch m.MinKycUserWelcomeEmailOption.GetOption().(type) {
	case *MinKycUserWelcomeEmailOption_MinKycWelcomeEmailOptionV1:
		return m.MinKycUserWelcomeEmailOption.GetMinKycWelcomeEmailOptionV1().GetTemplateVersion()
	case *MinKycUserWelcomeEmailOption_MinKycWelcomeEmailOptionV2:
		return m.MinKycUserWelcomeEmailOption.GetMinKycWelcomeEmailOptionV2().GetTemplateVersion()
	case *MinKycUserWelcomeEmailOption_MinKycWelcomeEmailOptionV3:
		return m.MinKycUserWelcomeEmailOption.GetMinKycWelcomeEmailOptionV3().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (m *EmailOption_MinKycUserWelcomeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if m == nil {
		return "", fmt.Errorf("min kyc user welcome email option is nil")
	}
	switch m.MinKycUserWelcomeEmailOption.GetOption().(type) {
	case *MinKycUserWelcomeEmailOption_MinKycWelcomeEmailOptionV1:
		name := m.MinKycUserWelcomeEmailOption.GetMinKycWelcomeEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	case *MinKycUserWelcomeEmailOption_MinKycWelcomeEmailOptionV2:
		name := m.MinKycUserWelcomeEmailOption.GetMinKycWelcomeEmailOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	case *MinKycUserWelcomeEmailOption_MinKycWelcomeEmailOptionV3:
		name := m.MinKycUserWelcomeEmailOption.GetMinKycWelcomeEmailOptionV3().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for min kyc user welcome email template")
}

func (m *EmailOption_MinKycUserWelcomeEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

func (m *EmailOption_FullKycUserWelcomeEmailOption) GetType() EmailType {
	if m == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return m.FullKycUserWelcomeEmailOption.GetEmailType()
}

func (m *EmailOption_FullKycUserWelcomeEmailOption) GetTemplateVersion() TemplateVersion {
	if m == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch m.FullKycUserWelcomeEmailOption.GetOption().(type) {
	case *FullKycUserWelcomeEmailOption_FullKycWelcomeEmailOptionV1:
		return m.FullKycUserWelcomeEmailOption.GetFullKycWelcomeEmailOptionV1().GetTemplateVersion()
	case *FullKycUserWelcomeEmailOption_FullKycWelcomeEmailOptionV2:
		return m.FullKycUserWelcomeEmailOption.GetFullKycWelcomeEmailOptionV2().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (m *EmailOption_FullKycUserWelcomeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if m == nil {
		return "", fmt.Errorf("full kyc user welcome email option is nil")
	}
	switch m.FullKycUserWelcomeEmailOption.GetOption().(type) {
	case *FullKycUserWelcomeEmailOption_FullKycWelcomeEmailOptionV1:
		name := m.FullKycUserWelcomeEmailOption.GetFullKycWelcomeEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	case *FullKycUserWelcomeEmailOption_FullKycWelcomeEmailOptionV2:
		name := m.FullKycUserWelcomeEmailOption.GetFullKycWelcomeEmailOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for full kyc user welcome email template")
}

func (m *EmailOption_FullKycUserWelcomeEmailOption) GetEmailSubject(subject string) (string, error) {
	return subject, nil
}

// *************************************** //

func (o *EmailOption_WaitlistCboShortlistEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistCboShortlistEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistCboShortlistEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistCboShortlistEmailOption.GetOption().(type) {
	case *WaitlistCboShortlistEmailOption_WaitlistCboShortlistOptionV1:
		return o.WaitlistCboShortlistEmailOption.GetWaitlistCboShortlistOptionV1().GetTemplateVersion()
	case *WaitlistCboShortlistEmailOption_WaitlistCboShortlistOptionV2:
		return o.WaitlistCboShortlistEmailOption.GetWaitlistCboShortlistOptionV2().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_WaitlistCboShortlistEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo user finite code email option is nil")
	}
	switch o.WaitlistCboShortlistEmailOption.GetOption().(type) {
	case *WaitlistCboShortlistEmailOption_WaitlistCboShortlistOptionV1:
		name := o.WaitlistCboShortlistEmailOption.GetWaitlistCboShortlistOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	case *WaitlistCboShortlistEmailOption_WaitlistCboShortlistOptionV2:
		name := o.WaitlistCboShortlistEmailOption.GetWaitlistCboShortlistOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist cbo user finite code email template")
	}
}

func (o *EmailOption_WaitlistCboShortlistEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo user finite code email option is nil")
	}
	switch o.WaitlistCboShortlistEmailOption.GetOption().(type) {
	case *WaitlistCboShortlistEmailOption_WaitlistCboShortlistOptionV1:
		name := o.WaitlistCboShortlistEmailOption.GetWaitlistCboShortlistOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	case *WaitlistCboShortlistEmailOption_WaitlistCboShortlistOptionV2:
		name := o.WaitlistCboShortlistEmailOption.GetWaitlistCboShortlistOptionV2().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist cbo user finite code email template")
	}
}

// *************************************** //

func (o *EmailOption_WaitlistExclusiveAccessFiniteCodeOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistExclusiveAccessFiniteCodeOption.GetEmailType()
}

func (o *EmailOption_WaitlistExclusiveAccessFiniteCodeOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	if _, ok := o.WaitlistExclusiveAccessFiniteCodeOption.GetOption().(*WaitlistExclusiveAccessFiniteCodeOption_WaitlistExclusiveAccessFiniteCodeOptionV1); ok {
		return o.WaitlistExclusiveAccessFiniteCodeOption.GetWaitlistExclusiveAccessFiniteCodeOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *EmailOption_WaitlistExclusiveAccessFiniteCodeOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist exclusive app access email option is nil")
	}
	if _, ok := o.WaitlistExclusiveAccessFiniteCodeOption.GetOption().(*WaitlistExclusiveAccessFiniteCodeOption_WaitlistExclusiveAccessFiniteCodeOptionV1); ok {
		name := o.WaitlistExclusiveAccessFiniteCodeOption.GetWaitlistExclusiveAccessFiniteCodeOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		finiteCode := o.WaitlistExclusiveAccessFiniteCodeOption.GetWaitlistExclusiveAccessFiniteCodeOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		appLink := o.WaitlistExclusiveAccessFiniteCodeOption.GetWaitlistExclusiveAccessFiniteCodeOptionV1().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", appLink, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist exclusive app access email template")
}

func (o *EmailOption_WaitlistExclusiveAccessFiniteCodeOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist exclusive app access email option is nil")
	}
	if _, ok := o.WaitlistExclusiveAccessFiniteCodeOption.GetOption().(*WaitlistExclusiveAccessFiniteCodeOption_WaitlistExclusiveAccessFiniteCodeOptionV1); ok {
		name := o.WaitlistExclusiveAccessFiniteCodeOption.GetWaitlistExclusiveAccessFiniteCodeOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist exclusive app access email template")
}

// *************************************** //

func (o *EmailOption_WaitlistCboIosShortlistEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistCboIosShortlistEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistCboIosShortlistEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistCboIosShortlistEmailOption.GetOption().(type) {
	case *WaitlistCboIosShortlistEmailOption_WaitlistCboIosShortlistOptionV1:
		return o.WaitlistCboIosShortlistEmailOption.GetWaitlistCboIosShortlistOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_WaitlistCboIosShortlistEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo user finite code email option is nil")
	}
	switch o.WaitlistCboIosShortlistEmailOption.GetOption().(type) {
	case *WaitlistCboIosShortlistEmailOption_WaitlistCboIosShortlistOptionV1:
		name := o.WaitlistCboIosShortlistEmailOption.GetWaitlistCboIosShortlistOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist ios cbo user finite code email template")
	}
}

func (o *EmailOption_WaitlistCboIosShortlistEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo user finite code email option is nil")
	}
	switch o.WaitlistCboIosShortlistEmailOption.GetOption().(type) {
	case *WaitlistCboIosShortlistEmailOption_WaitlistCboIosShortlistOptionV1:
		name := o.WaitlistCboIosShortlistEmailOption.GetWaitlistCboIosShortlistOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for waitlist ios cbo user finite code email template")
	}
}

func (o *EmailOption_UnNameCheckEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return EmailType_UN_NAME_CHECK_EMAIL
}

func (o *EmailOption_UnNameCheckEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("nil email to send un name check failure")
	}
	switch o.UnNameCheckEmailOption.GetOption().(type) {
	case *UNNameCheckEmailOption_UnNameCheckEmailOptionV1:
		emailOption := o.UnNameCheckEmailOption.GetUnNameCheckEmailOptionV1()

		msg := strings.Replace(templateBody, "{#name#}", emailOption.GetName(), 1)
		msg = strings.Replace(msg, "{#phonenumber#}", emailOption.GetPhoneNumber(), 1)
		msg = strings.Replace(msg, "{#dob#}", emailOption.GetDob(), 1)
		msg = strings.Replace(msg, "{#pan#}", emailOption.GetPan(), 1)
		msg = strings.Replace(msg, "{#ckycno#}", emailOption.GetCkycNo(), 1)
		msg = strings.Replace(msg, "{#ekycno#}", emailOption.GetEkycNo(), 1)
		msg = strings.Replace(msg, "{#permaddr#}", emailOption.GetPermanentAddress(), 1)
		msg = strings.Replace(msg, "{#corresaddr#}", emailOption.GetCorrespondenceAddress(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("unknown/unhandled template version for un name check email")
	}
}

func (o *EmailOption_UnNameCheckEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UnNameCheckEmailOption.GetOption().(type) {
	case *UNNameCheckEmailOption_UnNameCheckEmailOptionV1:
		return o.UnNameCheckEmailOption.GetUnNameCheckEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_UnNameCheckEmailOption) GetEmailSubject(subject string) (string, error) {
	switch o.UnNameCheckEmailOption.GetOption().(type) {
	case *UNNameCheckEmailOption_UnNameCheckEmailOptionV1:
		emailOption := o.UnNameCheckEmailOption.GetUnNameCheckEmailOptionV1()
		subject = strings.Replace(subject, "{#phonenumber#}", emailOption.GetPhoneNumber(), 1)
	}
	return subject, nil
}

// *************************************** //

func (o *EmailOption_FiniteCodeReminderEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.FiniteCodeReminderEmailOption.GetEmailType()
}

func (o *EmailOption_FiniteCodeReminderEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FiniteCodeReminderEmailOption.GetOption().(type) {
	case *FiniteCodeReminderEmailOption_FiniteCodeReminderEmailOptionV1:
		return o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV1().GetTemplateVersion()
	case *FiniteCodeReminderEmailOption_FiniteCodeReminderEmailOptionV2:
		return o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV2().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_FiniteCodeReminderEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code reminder email option is nil")
	}
	switch o.FiniteCodeReminderEmailOption.GetOption().(type) {
	case *FiniteCodeReminderEmailOption_FiniteCodeReminderEmailOptionV1:
		name := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		finiteCode := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		iosOptionLink := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV1().GetIosOptionLink()
		msg = strings.Replace(msg, "{#ios_option_link#}", iosOptionLink, 1)
		return msg, nil
	case *FiniteCodeReminderEmailOption_FiniteCodeReminderEmailOptionV2:
		name := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		finiteCode := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV2().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, -1)
		iosOptionLink := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV2().GetIosOptionLink()
		msg = strings.Replace(msg, "{#ios_option_link#}", iosOptionLink, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for finite code reminder email template")
	}
}

// nolint:dupl
func (o *EmailOption_FiniteCodeReminderEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code reminder email option is nil")
	}
	switch o.FiniteCodeReminderEmailOption.GetOption().(type) {
	case *FiniteCodeReminderEmailOption_FiniteCodeReminderEmailOptionV1:
		name := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	case *FiniteCodeReminderEmailOption_FiniteCodeReminderEmailOptionV2:
		name := o.FiniteCodeReminderEmailOption.GetFiniteCodeReminderEmailOptionV2().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for finite code reminder email template")
	}
}

// *************************************** //

func (o *EmailOption_DirectAccessFiniteCodeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.DirectAccessFiniteCodeEmailOption.GetEmailType()
}

func (o *EmailOption_DirectAccessFiniteCodeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DirectAccessFiniteCodeEmailOption.GetOption().(type) {
	case *DirectAccessFiniteCodeEmailOption_DirectAccessFiniteCodeEmailOptionV1:
		return o.DirectAccessFiniteCodeEmailOption.GetDirectAccessFiniteCodeEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_DirectAccessFiniteCodeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code reminder email option is nil")
	}
	switch o.DirectAccessFiniteCodeEmailOption.GetOption().(type) {
	case *DirectAccessFiniteCodeEmailOption_DirectAccessFiniteCodeEmailOptionV1:
		name := o.DirectAccessFiniteCodeEmailOption.GetDirectAccessFiniteCodeEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 2)
		finiteCode := o.DirectAccessFiniteCodeEmailOption.GetDirectAccessFiniteCodeEmailOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		iosOptionLink := o.DirectAccessFiniteCodeEmailOption.GetDirectAccessFiniteCodeEmailOptionV1().GetIosOptionLink()
		msg = strings.Replace(msg, "{#ios_option_link#}", iosOptionLink, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for direct access finite code email template")
	}
}

// nolint:dupl
func (o *EmailOption_DirectAccessFiniteCodeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code reminder email option is nil")
	}
	switch o.DirectAccessFiniteCodeEmailOption.GetOption().(type) {
	case *DirectAccessFiniteCodeEmailOption_DirectAccessFiniteCodeEmailOptionV1:
		name := o.DirectAccessFiniteCodeEmailOption.GetDirectAccessFiniteCodeEmailOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for direct access finite code email template")
	}
}

// *************************************** //

func (o *EmailOption_CboUnknownDeviceFiniteCodeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CboUnknownDeviceFiniteCodeEmailOption.GetEmailType()
}

func (o *EmailOption_CboUnknownDeviceFiniteCodeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CboUnknownDeviceFiniteCodeEmailOption.GetOption().(type) {
	case *CboUnknownDeviceFiniteCodeEmailOption_CboUnknownDeviceFiniteCodeEmailOptionV1:
		return o.CboUnknownDeviceFiniteCodeEmailOption.GetCboUnknownDeviceFiniteCodeEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_CboUnknownDeviceFiniteCodeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code reminder email option is nil")
	}
	switch o.CboUnknownDeviceFiniteCodeEmailOption.GetOption().(type) {
	case *CboUnknownDeviceFiniteCodeEmailOption_CboUnknownDeviceFiniteCodeEmailOptionV1:
		name := o.CboUnknownDeviceFiniteCodeEmailOption.GetCboUnknownDeviceFiniteCodeEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, 2)
		finiteCode := o.CboUnknownDeviceFiniteCodeEmailOption.GetCboUnknownDeviceFiniteCodeEmailOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, 1)
		iosOptionLink := o.CboUnknownDeviceFiniteCodeEmailOption.GetCboUnknownDeviceFiniteCodeEmailOptionV1().GetIosOptionLink()
		msg = strings.Replace(msg, "{#ios_option_link#}", iosOptionLink, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for cbo unknown device finite code email template")
	}
}

// nolint:dupl
func (o *EmailOption_CboUnknownDeviceFiniteCodeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code reminder email option is nil")
	}
	switch o.CboUnknownDeviceFiniteCodeEmailOption.GetOption().(type) {
	case *CboUnknownDeviceFiniteCodeEmailOption_CboUnknownDeviceFiniteCodeEmailOptionV1:
		name := o.CboUnknownDeviceFiniteCodeEmailOption.GetCboUnknownDeviceFiniteCodeEmailOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for cbo unknown device finite code email template")
	}
}

func (o *EmailOption_AddFundsSecondLegAlertEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return EmailType_ADD_FUND_SECOND_LEG_ALERT
}

func (o *EmailOption_AddFundsSecondLegAlertEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("nil email to send for add fund second leg alert")
	}
	switch o.AddFundsSecondLegAlertEmailOption.GetOption().(type) {
	case *AddFundsSecondLegAlertEmailOption_AddFundSecondLegAlertEmailOptionV1:
		emailOption := o.AddFundsSecondLegAlertEmailOption.GetAddFundSecondLegAlertEmailOptionV1()

		msg := strings.Replace(templateBody, "{#upiTxnId#}", emailOption.GetReqId(), 1)
		msg = strings.Replace(msg, "{#utr#}", emailOption.GetUtr(), 1)
		msg = strings.Replace(msg, "{#accountNumber#}", emailOption.GetAccountNumber(), 1)
		msg = strings.Replace(msg, "{#timestamp#}", emailOption.GetCreatedTimestamp(), 1)
		msg = strings.Replace(msg, "{#errorCode#}", emailOption.GetErrorCode(), 1)
		msg = strings.Replace(msg, "{#errorDescription#}", emailOption.GetErrorDescription(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("unknown/unhandled template version for add funds alert email")
	}
}

func (o *EmailOption_AddFundsSecondLegAlertEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AddFundsSecondLegAlertEmailOption.GetOption().(type) {
	case *AddFundsSecondLegAlertEmailOption_AddFundSecondLegAlertEmailOptionV1:
		return o.AddFundsSecondLegAlertEmailOption.GetAddFundSecondLegAlertEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_AddFundsSecondLegAlertEmailOption) GetEmailSubject(subject string) (string, error) {
	switch o.AddFundsSecondLegAlertEmailOption.GetOption().(type) {
	case *AddFundsSecondLegAlertEmailOption_AddFundSecondLegAlertEmailOptionV1:
		emailOption := o.AddFundsSecondLegAlertEmailOption.GetAddFundSecondLegAlertEmailOptionV1()
		subject = strings.Replace(subject, "{#upiTxnId#}", emailOption.GetReqId(), 1)
	}
	return subject, nil
}

// ............................................//

func (o *EmailOption_WaitlistIosWelcomeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistIosWelcomeEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistIosWelcomeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistIosWelcomeEmailOption.GetOption().(type) {
	case *WaitlistIosWelcomeEmailOption_WaitlistIosWelcomeEmailOptionV1:
		return o.WaitlistIosWelcomeEmailOption.GetWaitlistIosWelcomeEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistIosWelcomeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist ios welcome email option is nil")
	}
	switch o.WaitlistIosWelcomeEmailOption.GetOption().(type) {
	case *WaitlistIosWelcomeEmailOption_WaitlistIosWelcomeEmailOptionV1:
		name := o.WaitlistIosWelcomeEmailOption.GetWaitlistIosWelcomeEmailOptionV1().GetFirstName()
		msg := strings.Replace(templateBody, "{#name#}", name, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist ios welcome email template")
}

func (o *EmailOption_WaitlistIosWelcomeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist ios welcome email option is nil")
	}
	switch o.WaitlistIosWelcomeEmailOption.GetOption().(type) {
	case *WaitlistIosWelcomeEmailOption_WaitlistIosWelcomeEmailOptionV1:
		name := o.WaitlistIosWelcomeEmailOption.GetWaitlistIosWelcomeEmailOptionV1().GetFirstName()
		sub := strings.Replace(subject, "{#name#}", name, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist ios welcome email template")
}

// ............................................//

func (o *EmailOption_CboFeedbackEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CboFeedbackEmailOption.GetEmailType()
}

func (o *EmailOption_CboFeedbackEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CboFeedbackEmailOption.GetOption().(type) {
	case *CboFeedbackEmailOption_CboFeedbackEmailOptionV1:
		return o.CboFeedbackEmailOption.GetCboFeedbackEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CboFeedbackEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cbo feedback email option is nil")
	}
	switch o.CboFeedbackEmailOption.GetOption().(type) {
	case *CboFeedbackEmailOption_CboFeedbackEmailOptionV1:
		name := o.CboFeedbackEmailOption.GetCboFeedbackEmailOptionV1().GetFirstName()
		msg := strings.Replace(templateBody, "{#name#}", name, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cbo feedback email template")
}

func (o *EmailOption_CboFeedbackEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cbo feedback welcome email option is nil")
	}
	switch o.CboFeedbackEmailOption.GetOption().(type) {
	case *CboFeedbackEmailOption_CboFeedbackEmailOptionV1:
		name := o.CboFeedbackEmailOption.GetCboFeedbackEmailOptionV1().GetFirstName()
		sub := strings.Replace(subject, "{#name#}", name, -1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for cbo feedback email template")
}

// *************************************** //

func (o *EmailOption_WorkEmailVerificationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WorkEmailVerificationEmailOption.GetEmailType()
}

func (o *EmailOption_WorkEmailVerificationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WorkEmailVerificationEmailOption.GetOption().(type) {
	case *WorkEmailVerificationEmailOption_WorkEmailVerificationEmailOptionV1:
		return o.WorkEmailVerificationEmailOption.GetWorkEmailVerificationEmailOptionV1().GetTemplateVersion()
	case *WorkEmailVerificationEmailOption_WorkEmailVerificationEmailOptionV2:
		return o.WorkEmailVerificationEmailOption.GetWorkEmailVerificationEmailOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SalaryB_2BLeadWorkEmailVerificationEmailOption.GetEmailType()
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SalaryB_2BLeadWorkEmailVerificationEmailOption.GetOption().(type) {
	case *SalaryB2BLeadWorkEmailVerificationEmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOptionV1:
		return o.SalaryB_2BLeadWorkEmailVerificationEmailOption.GetSalaryB_2BLeadWorkEmailVerificationEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("work email verification email option is nil")
	}
	switch o.SalaryB_2BLeadWorkEmailVerificationEmailOption.GetOption().(type) {
	case *SalaryB2BLeadWorkEmailVerificationEmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOptionV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for work email verification email template")
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.SalaryB_2BLeadWorkEmailVerificationEmailOption.GetOption().(type) {
	case *SalaryB2BLeadWorkEmailVerificationEmailOption_SalaryB_2BLeadWorkEmailVerificationEmailOptionV1:
		options := o.SalaryB_2BLeadWorkEmailVerificationEmailOption.GetSalaryB_2BLeadWorkEmailVerificationEmailOptionV1()
		body := strings.Replace(templateBody, "{#otp#}", options.GetOtp(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for execution report email template")
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption.GetEmailType()
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1:
		return o.SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption.GetSalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("salary b2b otp verified slot not booked email option is nil")
	}
	switch o.SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for salary b2b otp verified slot not booked email template")
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1:
		options := o.SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption.GetSalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for execution report email template")
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption.GetEmailType()
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadOTPVerifiedSlotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1:
		return o.SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption.GetSalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("salary b2b otp verified slot booked email option is nil")
	}
	switch o.SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadOTPVerifiedSlotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for salary b2b otp verified slot booked email template")
}

func (o *EmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadOTPVerifiedSlotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1:
		options := o.SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption.GetSalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for execution report email template")
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SalaryB_2BLeadWorkEmailNotVerifiedEmailOption.GetEmailType()
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SalaryB_2BLeadWorkEmailNotVerifiedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadWorkEmailNotVerifiedEmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1:
		return o.SalaryB_2BLeadWorkEmailNotVerifiedEmailOption.GetSalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("salary b2b work email not verified email option is nil")
	}
	switch o.SalaryB_2BLeadWorkEmailNotVerifiedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadWorkEmailNotVerifiedEmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for salary b2b work email not verified email template")
}

func (o *EmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.SalaryB_2BLeadWorkEmailNotVerifiedEmailOption.GetOption().(type) {
	case *SalaryB2BLeadWorkEmailNotVerifiedEmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1:
		options := o.SalaryB_2BLeadWorkEmailNotVerifiedEmailOption.GetSalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for execution report email template")
}

func (o *EmailOption_WorkEmailVerificationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("work email verification email option is nil")
	}
	switch o.WorkEmailVerificationEmailOption.GetOption().(type) {
	case *WorkEmailVerificationEmailOption_WorkEmailVerificationEmailOptionV1:
		msg := strings.ReplaceAll(templateBody, "{#otp#}", o.WorkEmailVerificationEmailOption.GetWorkEmailVerificationEmailOptionV1().GetOtp())
		return msg, nil
	case *WorkEmailVerificationEmailOption_WorkEmailVerificationEmailOptionV2:
		msg := strings.ReplaceAll(templateBody, "{#otp#}", o.WorkEmailVerificationEmailOption.GetWorkEmailVerificationEmailOptionV2().GetOtp())
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist user cbo accepted email template")
}

func (o *EmailOption_WorkEmailVerificationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("work email verification email option is nil")
	}
	switch o.WorkEmailVerificationEmailOption.GetOption().(type) {
	case *WorkEmailVerificationEmailOption_WorkEmailVerificationEmailOptionV1:
		sub := strings.ReplaceAll(subject, "{#otp#}", o.WorkEmailVerificationEmailOption.GetWorkEmailVerificationEmailOptionV1().GetOtp())
		return sub, nil
	case *WorkEmailVerificationEmailOption_WorkEmailVerificationEmailOptionV2:
		sub := strings.ReplaceAll(subject, "{#otp#}", o.WorkEmailVerificationEmailOption.GetWorkEmailVerificationEmailOptionV2().GetOtp())
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for work email verification email template")
}

// *************************************** //

func (o *EmailOption_WaitlistIosAppAccessEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistIosAppAccessEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistIosAppAccessEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistIosAppAccessEmailOption.GetOption().(type) {
	case *WaitlistIosAppAccessEmailOption_WaitlistIosAppAccessEmailOptionV1:
		return o.WaitlistIosAppAccessEmailOption.GetWaitlistIosAppAccessEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistIosAppAccessEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist ios app access email option is nil")
	}
	switch o.WaitlistIosAppAccessEmailOption.GetOption().(type) {
	case *WaitlistIosAppAccessEmailOption_WaitlistIosAppAccessEmailOptionV1:
		name := o.WaitlistIosAppAccessEmailOption.GetWaitlistIosAppAccessEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, -1)
		vc := o.WaitlistIosAppAccessEmailOption.GetWaitlistIosAppAccessEmailOptionV1().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, -1)
		finiteCode := o.WaitlistIosAppAccessEmailOption.GetWaitlistIosAppAccessEmailOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist ios app access email template")
}

func (o *EmailOption_WaitlistIosAppAccessEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist ios app access email option is nil")
	}
	switch o.WaitlistIosAppAccessEmailOption.GetOption().(type) {
	case *WaitlistIosAppAccessEmailOption_WaitlistIosAppAccessEmailOptionV1:
		name := o.WaitlistIosAppAccessEmailOption.GetWaitlistIosAppAccessEmailOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, -1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist ios app access email template")
}

// *************************************** //

func (o *EmailOption_WaitlistCboIosAppAccessEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WaitlistCboIosAppAccessEmailOption.GetEmailType()
}

func (o *EmailOption_WaitlistCboIosAppAccessEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistCboIosAppAccessEmailOption.GetOption().(type) {
	case *WaitlistCboIosAppAccessEmailOption_WaitlistCboIosAppAccessEmailOptionV1:
		return o.WaitlistCboIosAppAccessEmailOption.GetWaitlistCboIosAppAccessEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WaitlistCboIosAppAccessEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo ios app access email option is nil")
	}
	switch o.WaitlistCboIosAppAccessEmailOption.GetOption().(type) {
	case *WaitlistCboIosAppAccessEmailOption_WaitlistCboIosAppAccessEmailOptionV1:
		name := o.WaitlistCboIosAppAccessEmailOption.GetWaitlistCboIosAppAccessEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#name#}", name, -1)
		vc := o.WaitlistCboIosAppAccessEmailOption.GetWaitlistCboIosAppAccessEmailOptionV1().GetAppLink()
		msg = strings.Replace(msg, "{#app_link#}", vc, -1)
		finiteCode := o.WaitlistCboIosAppAccessEmailOption.GetWaitlistCboIosAppAccessEmailOptionV1().GetFiniteCode()
		msg = strings.Replace(msg, "{#finite_code#}", finiteCode, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist cbo ios app access email template")
}

func (o *EmailOption_WaitlistCboIosAppAccessEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist cbo ios app access email option is nil")
	}
	switch o.WaitlistCboIosAppAccessEmailOption.GetOption().(type) {
	case *WaitlistCboIosAppAccessEmailOption_WaitlistCboIosAppAccessEmailOptionV1:
		name := o.WaitlistCboIosAppAccessEmailOption.GetWaitlistCboIosAppAccessEmailOptionV1().GetName()
		sub := strings.Replace(subject, "{#name#}", name, -1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist cbo ios app access email template")
}

func (o *EmailOption_CxAutoResolutionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CxAutoResolutionEmailOption.GetEmailType()
}

func (o *EmailOption_CxAutoResolutionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CxAutoResolutionEmailOption.GetOption().(type) {
	case *CxAutoResolutionEmailOption_CxAutoResolutionEmailOptionV1:
		return o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CxAutoResolutionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cx auto resolution email option is nil")
	}
	switch o.CxAutoResolutionEmailOption.GetOption().(type) {
	case *CxAutoResolutionEmailOption_CxAutoResolutionEmailOptionV1:
		totalTicketsProcessed := strconv.FormatInt(o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetTotalTicketsProcessed(), 10)
		msg := strings.Replace(templateBody, "{#total_tickets_processed#}", totalTicketsProcessed, -1)
		updatedTicketCount := strconv.FormatInt(o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetUpdatedTicketCount(), 10)
		msg = strings.Replace(msg, "{#updated_ticket_count#}", updatedTicketCount, -1)
		resolvedTicketCount := strconv.FormatInt(o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetResolvedCount(), 10)
		msg = strings.Replace(msg, "{#resolved_ticket_count#}", resolvedTicketCount, -1)
		emailMsg := o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetEmailMsg()
		msg = strings.Replace(msg, "{#email_msg#}", emailMsg, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cx auto resolution email template")
}

func (o *EmailOption_CxAutoResolutionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cx auto resolution email option is nil")
	}
	switch o.CxAutoResolutionEmailOption.GetOption().(type) {
	case *CxAutoResolutionEmailOption_CxAutoResolutionEmailOptionV1:
		currDate := o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetCurrDate()
		sub := strings.Replace(subject, "{#curr_date#}", currDate, -1)
		productCategory := o.CxAutoResolutionEmailOption.GetCxAutoResolutionEmailOptionV1().GetProductCategory()
		sub = strings.Replace(sub, "{#product_category#}", productCategory, -1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for sherlock verification email template")
}

func (o *EmailOption_DepositMaturityMinKycTMinusXEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.DepositMaturityMinKycTMinusXEmailOption.GetEmailType()
}

func (o *EmailOption_DepositMaturityMinKycTMinusXEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DepositMaturityMinKycTMinusXEmailOption.GetOption().(type) {
	case *DepositMaturingMinKycTMinusXEmailOption_DepositMaturingMinKycTMinusXEmailOptionV1:
		return o.DepositMaturityMinKycTMinusXEmailOption.GetDepositMaturingMinKycTMinusXEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_DepositMaturityMinKycTMinusXEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("deposit min kyc maturity t minus X email option is nil")
	}
	switch o.DepositMaturityMinKycTMinusXEmailOption.GetOption().(type) {
	case *DepositMaturingMinKycTMinusXEmailOption_DepositMaturingMinKycTMinusXEmailOptionV1:
		depositName := o.DepositMaturityMinKycTMinusXEmailOption.GetDepositMaturingMinKycTMinusXEmailOptionV1().GetDepositName()
		msg := strings.Replace(templateBody, "{#deposit_name#}", depositName, -1)
		xDays := o.DepositMaturityMinKycTMinusXEmailOption.GetDepositMaturingMinKycTMinusXEmailOptionV1().GetXDays()
		msg = strings.Replace(msg, "{#x_days#}", xDays, -1)
		maturityDate := o.DepositMaturityMinKycTMinusXEmailOption.GetDepositMaturingMinKycTMinusXEmailOptionV1().GetMaturityDate()
		msg = strings.Replace(msg, "{#maturity_date#}", maturityDate, -1)
		depositType := o.DepositMaturityMinKycTMinusXEmailOption.GetDepositMaturingMinKycTMinusXEmailOptionV1().GetDepositType()
		msg = strings.Replace(msg, "{#deposit_type#}", depositType, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for deposit min kyc maturity t minus X email template")
}

func (o *EmailOption_DepositMaturityMinKycTMinusXEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("deposit min kyc maturity t minus X email option is nil")
	}
	switch o.DepositMaturityMinKycTMinusXEmailOption.GetOption().(type) {
	case *DepositMaturingMinKycTMinusXEmailOption_DepositMaturingMinKycTMinusXEmailOptionV1:
		depositType := o.DepositMaturityMinKycTMinusXEmailOption.GetDepositMaturingMinKycTMinusXEmailOptionV1().GetDepositType()
		msg := strings.Replace(subject, "{#deposit_type#}", depositType, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for deposit min kyc maturity t minus X email template")
}

func (o *EmailOption_DepositMaturityMinKycTPlusXEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.DepositMaturityMinKycTPlusXEmailOption.GetEmailType()
}

func (o *EmailOption_DepositMaturityMinKycTPlusXEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DepositMaturityMinKycTPlusXEmailOption.GetOption().(type) {
	case *DepositMaturingMinKycTPlusXEmailOption_DepositMaturingMinKycTPlusXEmailOptionV1:
		return o.DepositMaturityMinKycTPlusXEmailOption.GetDepositMaturingMinKycTPlusXEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_DepositMaturityMinKycTPlusXEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("deposit min kyc maturity t minus X email option is nil")
	}
	switch o.DepositMaturityMinKycTPlusXEmailOption.GetOption().(type) {
	case *DepositMaturingMinKycTPlusXEmailOption_DepositMaturingMinKycTPlusXEmailOptionV1:
		depositName := o.DepositMaturityMinKycTPlusXEmailOption.GetDepositMaturingMinKycTPlusXEmailOptionV1().GetDepositName()
		msg := strings.Replace(templateBody, "{#deposit_name#}", depositName, -1)
		xDays := o.DepositMaturityMinKycTPlusXEmailOption.GetDepositMaturingMinKycTPlusXEmailOptionV1().GetXDays()
		msg = strings.Replace(msg, "{#x_days#}", xDays, -1)
		depositType := o.DepositMaturityMinKycTPlusXEmailOption.GetDepositMaturingMinKycTPlusXEmailOptionV1().GetDepositType()
		msg = strings.Replace(msg, "{#deposit_type#}", depositType, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for deposit min kyc maturity t minus X email template")
}

func (o *EmailOption_DepositMaturityMinKycTPlusXEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("deposit min kyc maturity t minus X email option is nil")
	}
	switch o.DepositMaturityMinKycTPlusXEmailOption.GetOption().(type) {
	case *DepositMaturingMinKycTPlusXEmailOption_DepositMaturingMinKycTPlusXEmailOptionV1:
		depositType := o.DepositMaturityMinKycTPlusXEmailOption.GetDepositMaturingMinKycTPlusXEmailOptionV1().GetDepositType()
		msg := strings.Replace(subject, "{#deposit_type#}", depositType, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for deposit min kyc maturity t minus X email template")
}

// Finite Code Share Email
func (o *EmailOption_InternalFiniteCodeDumpShareOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.InternalFiniteCodeDumpShareOption.GetEmailType()
}

func (o *EmailOption_InternalFiniteCodeDumpShareOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.InternalFiniteCodeDumpShareOption.GetOption().(type) {
	case *InternalFiniteCodeDumpShareOption_InternalFiniteCodeDumpShareOptionV1:
		return o.InternalFiniteCodeDumpShareOption.GetInternalFiniteCodeDumpShareOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_InternalFiniteCodeDumpShareOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("internal finite code share email option is nil")
	}
	// do not require parameterized handling on template body for now.
	return templateBody, nil
}

func (o *EmailOption_InternalFiniteCodeDumpShareOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("subject for email option is nil")
	}
	switch o.InternalFiniteCodeDumpShareOption.GetOption().(type) {
	case *InternalFiniteCodeDumpShareOption_InternalFiniteCodeDumpShareOptionV1:
		reportDate := o.InternalFiniteCodeDumpShareOption.GetInternalFiniteCodeDumpShareOptionV1().GetReportDate()
		sub := strings.Replace(subject, "{#reportDate#}", reportDate, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for internal finite code share email template")
}

func (o *EmailOption_CxBulkUserDetailsEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CxBulkUserDetailsEmailOption.GetEmailType()
}

func (o *EmailOption_CxBulkUserDetailsEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CxBulkUserDetailsEmailOption.GetOption().(type) {
	case *CxBulkUserDetailsEmailOption_CxBulkUserDetailsEmailOptionV1:
		return o.CxBulkUserDetailsEmailOption.GetCxBulkUserDetailsEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CxBulkUserDetailsEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.CxBulkUserDetailsEmailOption.GetOption().(type) {
	case *CxBulkUserDetailsEmailOption_CxBulkUserDetailsEmailOptionV1:
		options := o.CxBulkUserDetailsEmailOption.GetCxBulkUserDetailsEmailOptionV1()
		body := strings.Replace(templateBody, "{#processed_ids_count#}", strconv.FormatInt(options.GetProcessedIdsCount(), 10), 1)
		body = strings.Replace(body, "{#failed_ids_count#}", strconv.FormatInt(options.GetFailedIdsCount(), 10), 1)
		body = strings.Replace(body, "{#additional_msg#}", options.GetAdditionalMsg(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for internal finite code share email template")
}

func (o *EmailOption_CxBulkUserDetailsEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_CxBulkAccountValidationsEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CxBulkAccountValidationsEmailOption.GetEmailType()
}

func (o *EmailOption_CxBulkAccountValidationsEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CxBulkAccountValidationsEmailOption.GetOption().(type) {
	case *CxBulkAccountValidationsEmailOption_CxBulkAccountValidationsEmailOptionV1:
		return o.CxBulkAccountValidationsEmailOption.GetCxBulkAccountValidationsEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CxBulkAccountValidationsEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("cx bulk account validation email option is nil")
	}
	switch o.CxBulkAccountValidationsEmailOption.GetOption().(type) {
	case *CxBulkAccountValidationsEmailOption_CxBulkAccountValidationsEmailOptionV1:
		options := o.CxBulkAccountValidationsEmailOption.GetCxBulkAccountValidationsEmailOptionV1()
		body := strings.Replace(templateBody, "{#total_validations#}", strconv.FormatInt(options.GetTotalValidations(), 10), 1)
		body = strings.Replace(body, "{#successful_validations#}", strconv.FormatInt(options.GetSuccessfulValidations(), 10), 1)
		body = strings.Replace(body, "{#additional_msg#}", options.GetAdditionalMsg(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for cx bulk account validation email template")
}

func (o *EmailOption_CxBulkAccountValidationsEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_PostAccountClosureFundTransferEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.PostAccountClosureFundTransferEmailOption.GetEmailType()
}

func (o *EmailOption_PostAccountClosureFundTransferEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PostAccountClosureFundTransferEmailOption.GetOption().(type) {
	case *PostAccountClosureFundTransferEmailOption_PostAccountClosureFundTransferEmailOptionV1:
		return o.PostAccountClosureFundTransferEmailOption.GetPostAccountClosureFundTransferEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_PostAccountClosureFundTransferEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("post account closure fund transfer email option is nil")
	}
	switch o.PostAccountClosureFundTransferEmailOption.GetOption().(type) {
	case *PostAccountClosureFundTransferEmailOption_PostAccountClosureFundTransferEmailOptionV1:
		options := o.PostAccountClosureFundTransferEmailOption.GetPostAccountClosureFundTransferEmailOptionV1()
		body := strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), 1)
		body = strings.Replace(body, "{#balance_transfered#}", options.GetBalanceTransfered(), 1)
		body = strings.Replace(body, "{#balance_transfered_date#}", options.GetBalanceTransferedDate(), 1)
		body = strings.Replace(body, "{#utr_number#}", options.GetUtrNumber(), 1)
		body = strings.Replace(body, "{#balance_transfered_account_number#}", options.GetBalanceTransferedAccountNumber(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for post account closure fund transfer email template")
}

func (o *EmailOption_PostAccountClosureFundTransferEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for post account closure fund transfer email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_MinToFullKycConversionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MinToFullKycConversionEmailOption.GetEmailType()
}

func (o *EmailOption_MinToFullKycConversionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MinToFullKycConversionEmailOption.GetOption().(type) {
	case *MinToFullKycConversionEmailOption_MinToFullKycConversionEmailOptionV1:
		return o.MinToFullKycConversionEmailOption.GetMinToFullKycConversionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MinToFullKycConversionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("min full kyc conversion email option is nil")
	}
	switch o.MinToFullKycConversionEmailOption.GetOption().(type) {
	case *MinToFullKycConversionEmailOption_MinToFullKycConversionEmailOptionV1:
		options := o.MinToFullKycConversionEmailOption.GetMinToFullKycConversionEmailOptionV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for min to full kyc conversion email template")
}

func (o *EmailOption_MinToFullKycConversionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for min to full kyc conversion email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_UserStuckInVkycReviewStateFederalEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.UserStuckInVkycReviewStateFederalEmailOption.GetEmailType()
}

func (o *EmailOption_UserStuckInVkycReviewStateFederalEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UserStuckInVkycReviewStateFederalEmailOption.GetOption().(type) {
	case *UserStuckInVkycReviewStateFederalEmailOption_UserStuckInVkycReviewStateFederalEmailOptionV1:
		return o.UserStuckInVkycReviewStateFederalEmailOption.GetUserStuckInVkycReviewStateFederalEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_UserStuckInVkycReviewStateFederalEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("user stuck in vkyc review review state federal email option is nil")
	}
	switch o.UserStuckInVkycReviewStateFederalEmailOption.GetOption().(type) {
	case *UserStuckInVkycReviewStateFederalEmailOption_UserStuckInVkycReviewStateFederalEmailOptionV1:
		return templateBody, nil
	}
	return "", errors.New("no valid version found for user stuck in vkyc review review state federal email option")
}

func (o *EmailOption_UserStuckInVkycReviewStateFederalEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for user stuck in review state email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_MinKycAccountClosureUserVerifiedCxEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MinKycAccountClosureUserVerifiedCxEmailOption.GetEmailType()
}

func (o *EmailOption_MinKycAccountClosureUserVerifiedCxEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MinKycAccountClosureUserVerifiedCxEmailOption.GetOption().(type) {
	case *MinKycAccountClosureUserVerifiedEmailOption_MinKycAccountClosureUserVerifiedMailToCxV1:
		return o.MinKycAccountClosureUserVerifiedCxEmailOption.GetMinKycAccountClosureUserVerifiedMailToCxV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MinKycAccountClosureUserVerifiedCxEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("min kyc account closure user verified cx email option is nil")
	}
	switch o.MinKycAccountClosureUserVerifiedCxEmailOption.GetOption().(type) {
	case *MinKycAccountClosureUserVerifiedEmailOption_MinKycAccountClosureUserVerifiedMailToCxV1:
		options := o.MinKycAccountClosureUserVerifiedCxEmailOption.GetMinKycAccountClosureUserVerifiedMailToCxV1()
		body := strings.Replace(templateBody, "{#additional_msg#}", options.GetAdditionalMsg(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for min kyc account closure user verified cx email option")
}

func (o *EmailOption_MinKycAccountClosureUserVerifiedCxEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for min kyc account closure user verified cx email option is nil")
	}
	switch o.MinKycAccountClosureUserVerifiedCxEmailOption.GetOption().(type) {
	case *MinKycAccountClosureUserVerifiedEmailOption_MinKycAccountClosureUserVerifiedMailToCxV1:
		options := o.MinKycAccountClosureUserVerifiedCxEmailOption.GetMinKycAccountClosureUserVerifiedMailToCxV1()
		subject = strings.Replace(subject, "{#current_date#}", options.GetCurrentDate(), 1)
		return subject, nil
	}
	return "", errors.New("no valid version found for min kyc account closure user verified cx email option")
}
func (o *EmailOption_WorkflowExecutionReportEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WorkflowExecutionReportEmailOption.GetEmailType()
}

func (o *EmailOption_WorkflowExecutionReportEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WorkflowExecutionReportEmailOption.GetOption().(type) {
	case *WorkflowExecutionReportEmailOption_WorkflowExecutionReportEmailOptionV1:
		return o.WorkflowExecutionReportEmailOption.GetWorkflowExecutionReportEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WorkflowExecutionReportEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.WorkflowExecutionReportEmailOption.GetOption().(type) {
	case *WorkflowExecutionReportEmailOption_WorkflowExecutionReportEmailOptionV1:
		options := o.WorkflowExecutionReportEmailOption.GetWorkflowExecutionReportEmailOptionV1()
		body := strings.Replace(templateBody, "{#message#}", options.GetMessage(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for execution report email template")
}

func (o *EmailOption_WorkflowExecutionReportEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_IssueResolutionUserFeedbackEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.IssueResolutionUserFeedbackEmailOption.GetEmailType()
}

func (o *EmailOption_IssueResolutionUserFeedbackEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.IssueResolutionUserFeedbackEmailOption.GetOption().(type) {
	case *IssueResolutionUserFeedbackEmailOption_IssueResolutionUserFeedbackEmailOptionV1:
		return o.IssueResolutionUserFeedbackEmailOption.GetIssueResolutionUserFeedbackEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_IssueResolutionUserFeedbackEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("email body for issue resolution user feedback email option is nil")
	}
	switch o.IssueResolutionUserFeedbackEmailOption.GetOption().(type) {
	case *IssueResolutionUserFeedbackEmailOption_IssueResolutionUserFeedbackEmailOptionV1:
		options := o.IssueResolutionUserFeedbackEmailOption.GetIssueResolutionUserFeedbackEmailOptionV1()
		body := strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), -1)
		body = strings.Replace(body, "{#issue_name#}", options.GetIssueType(), -1)
		body = strings.Replace(body, "{#eta_text#}", options.GetIssueResolutionEtaInfo(), -1)
		body = strings.Replace(body, "{#yes_text#}", options.GetYesActionInfo(), -1)
		body = strings.Replace(body, "{#no_text#}", options.GetNoActionInfo(), -1)
		body = strings.Replace(body, "{#steps_for_checking#}", options.GetActivitySteps(), -1)
		body = strings.Replace(body, "{#url_for_answer_yes#}", options.GetYesActionCtaUrl(), -1)
		body = strings.Replace(body, "{#url_for_answer_no#}", options.GetNoActionCtaUrl(), -1)
		return body, nil
	}
	return "", errors.New("no valid version found for issue resolution user feedback email option")
}

func (o *EmailOption_IssueResolutionUserFeedbackEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for issue resolution user feedback email option is nil")
	}
	switch o.IssueResolutionUserFeedbackEmailOption.GetOption().(type) {
	case *IssueResolutionUserFeedbackEmailOption_IssueResolutionUserFeedbackEmailOptionV1:
		options := o.IssueResolutionUserFeedbackEmailOption.GetIssueResolutionUserFeedbackEmailOptionV1()
		subject = strings.Replace(subject, "{#subject#}", options.GetSubject(), 1)
		return subject, nil
	}
	return "", errors.New("no valid version found for issue resolution user feedback email option is nil")
}

func (o *EmailOption_FederalMarkAccountsForFreezeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.FederalMarkAccountsForFreezeEmailOption.GetEmailType()
}

func (o *EmailOption_FederalMarkAccountsForFreezeEmailOption) GetTemplateVersion() TemplateVersion {
	logger.InfoNoCtx("template version present", zap.Any("template version", o.FederalMarkAccountsForFreezeEmailOption.GetFederalMarkAccountsForFreezeEmailOptionV1().GetTemplateVersion()))
	if o == nil {
		logger.InfoNoCtx("o value is nill")
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FederalMarkAccountsForFreezeEmailOption.GetOption().(type) {
	case *FederalMarkAccountsForFreezeEmailOption_FederalMarkAccountsForFreezeEmailOptionV1:
		return o.FederalMarkAccountsForFreezeEmailOption.GetFederalMarkAccountsForFreezeEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_FederalMarkAccountsForFreezeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mark accounts for freeze email option is nil")
	}
	switch o.FederalMarkAccountsForFreezeEmailOption.GetOption().(type) {
	case *FederalMarkAccountsForFreezeEmailOption_FederalMarkAccountsForFreezeEmailOptionV1:
		numberOfAccounts := strconv.FormatInt(o.FederalMarkAccountsForFreezeEmailOption.GetFederalMarkAccountsForFreezeEmailOptionV1().GetTotalAccountsForFreeze(), 10)
		msg := strings.Replace(templateBody, "{#total_accounts#}", numberOfAccounts, -1)
		dateString := o.FederalMarkAccountsForFreezeEmailOption.GetFederalMarkAccountsForFreezeEmailOptionV1().GetDate()
		msg = strings.Replace(msg, "{#date#}", dateString, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mark Accounts For Freeze Email Option")
}

func (o *EmailOption_FederalMarkAccountsForFreezeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mark accounts for freeze email option is nil")
	}
	switch o.FederalMarkAccountsForFreezeEmailOption.GetOption().(type) {
	case *FederalMarkAccountsForFreezeEmailOption_FederalMarkAccountsForFreezeEmailOptionV1:
		currDate := o.FederalMarkAccountsForFreezeEmailOption.GetFederalMarkAccountsForFreezeEmailOptionV1().GetDate()
		sub := strings.Replace(subject, "{#date#}", currDate, -1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for Mark Accounts For Freeze Email Option")
}

func (o *EmailOption_FederalMarkAccountsForUnfreezeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.FederalMarkAccountsForUnfreezeEmailOption.GetEmailType()
}

func (o *EmailOption_FederalMarkAccountsForUnfreezeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FederalMarkAccountsForUnfreezeEmailOption.GetOption().(type) {
	case *FederalMarkAccountsForUnfreezeEmailOption_FederalMarkAccountsForUnfreezeEmailOptionV1:
		return o.FederalMarkAccountsForUnfreezeEmailOption.GetFederalMarkAccountsForUnfreezeEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_FederalMarkAccountsForUnfreezeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mark accounts for unfreeze email option is nil")
	}
	switch o.FederalMarkAccountsForUnfreezeEmailOption.GetOption().(type) {
	case *FederalMarkAccountsForUnfreezeEmailOption_FederalMarkAccountsForUnfreezeEmailOptionV1:
		numberOfAccounts := strconv.FormatInt(o.FederalMarkAccountsForUnfreezeEmailOption.GetFederalMarkAccountsForUnfreezeEmailOptionV1().GetTotalAccountsForUnfreeze(), 10)
		msg := strings.Replace(templateBody, "{#total_accounts#}", numberOfAccounts, -1)
		dateString := o.FederalMarkAccountsForUnfreezeEmailOption.GetFederalMarkAccountsForUnfreezeEmailOptionV1().GetDate()
		msg = strings.Replace(msg, "{#date#}", dateString, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mark Accounts For Freeze Email Option")
}

func (o *EmailOption_FederalMarkAccountsForUnfreezeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mark accounts for unfreeze email option is nil")
	}
	switch o.FederalMarkAccountsForUnfreezeEmailOption.GetOption().(type) {
	case *FederalMarkAccountsForUnfreezeEmailOption_FederalMarkAccountsForUnfreezeEmailOptionV1:
		currDate := o.FederalMarkAccountsForUnfreezeEmailOption.GetFederalMarkAccountsForUnfreezeEmailOptionV1().GetDate()
		sub := strings.Replace(subject, "{#date#}", currDate, -1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for Mark Accounts For Freeze Email Option")
}

func (o *EmailOption_RiskOpsUserEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskOpsUserEmailOption.GetEmailType()
}

func (o *EmailOption_RiskOpsUserEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskOpsUserEmailOption.GetOption().(type) {
	case *RiskOpsUserEmailOption_RiskOpsUserEmailOptionV1:
		return o.RiskOpsUserEmailOption.GetRiskOpsUserEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskOpsUserEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk ops email body object is nil")
	}
	switch o.RiskOpsUserEmailOption.GetOption().(type) {
	case *RiskOpsUserEmailOption_RiskOpsUserEmailOptionV1:
		firstName := o.RiskOpsUserEmailOption.GetRiskOpsUserEmailOptionV1().GetFirstName()
		msg := strings.Replace(templateBody, "{#name#}", firstName, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mark Accounts For Freeze Email Option")
}

func (o *EmailOption_RiskOpsUserEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for risk ops email is nil")
	}
	return subject, nil
}

func (o *EmailOption_P2PReconFileEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.P2PReconFileEmailOption.GetEmailType()
}

func (o *EmailOption_P2PReconFileEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.P2PReconFileEmailOption.GetOption().(type) {
	case *P2PReconFileEmailOption_P2PReconFileEmailOptionV1:
		return o.P2PReconFileEmailOption.GetP2PReconFileEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_P2PReconFileEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("p2p recon file email body object is nil")
	}
	return templateBody, nil
}

func (o *EmailOption_P2PReconFileEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for p2p recon file email is nil")
	}
	return subject, nil
}

func (o *EmailOption_SalaryProgramRegistrationCompletedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SalaryProgramRegistrationCompletedEmailOption.GetEmailType()
}

func (o *EmailOption_SalaryProgramRegistrationCompletedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SalaryProgramRegistrationCompletedEmailOption.GetOption().(type) {
	case *SalaryProgramRegistrationCompletedEmailOption_SalaryProgramRegistrationCompletedEmailOptionV1:
		return o.SalaryProgramRegistrationCompletedEmailOption.GetSalaryProgramRegistrationCompletedEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SalaryProgramRegistrationCompletedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("salary program registration completed email option object is nil")
	}
	switch o.SalaryProgramRegistrationCompletedEmailOption.GetOption().(type) {
	case *SalaryProgramRegistrationCompletedEmailOption_SalaryProgramRegistrationCompletedEmailOptionV1:
		firstName := o.SalaryProgramRegistrationCompletedEmailOption.GetSalaryProgramRegistrationCompletedEmailOptionV1().GetFirstName()
		body := strings.Replace(templateBody, "{#firstName#}", firstName, -1)
		accountHolderName := o.SalaryProgramRegistrationCompletedEmailOption.GetSalaryProgramRegistrationCompletedEmailOptionV1().GetAccountHolderName()
		body = strings.Replace(body, "{#accountHolderName#}", accountHolderName, -1)
		accountNumber := o.SalaryProgramRegistrationCompletedEmailOption.GetSalaryProgramRegistrationCompletedEmailOptionV1().GetAccountNumber()
		body = strings.Replace(body, "{#accountNumber#}", accountNumber, -1)
		ifscCode := o.SalaryProgramRegistrationCompletedEmailOption.GetSalaryProgramRegistrationCompletedEmailOptionV1().GetIfscCode()
		body = strings.Replace(body, "{#ifscCode#}", ifscCode, -1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for salary program registration completed email option")
}

func (o *EmailOption_SalaryProgramRegistrationCompletedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("salary program registration completed email option object is nil")
	}
	return subject, nil
}

func (o *EmailOption_UsStocksUserEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.UsStocksUserEmailOption.GetEmailType()
}

func (o *EmailOption_UsStocksUserEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UsStocksUserEmailOption.GetOption().(type) {
	case *UsStocksUserEmailOption_UserEmailOptionV1:
		return o.UsStocksUserEmailOption.GetUserEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_UsStocksUserEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("usstocks user email body object is nil")
	}
	switch o.UsStocksUserEmailOption.GetOption().(type) {
	case *UsStocksUserEmailOption_UserEmailOptionV1:
		accountNumber := o.UsStocksUserEmailOption.GetUserEmailOptionV1().GetMaskedAccountNumber()
		msg := strings.Replace(templateBody, "{#masked_account_number#}", accountNumber, -1)
		dateString := o.UsStocksUserEmailOption.GetUserEmailOptionV1().GetStatementDate()
		msg = strings.Replace(msg, "{#statement_date#}", dateString, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for UsStock User Email Option")
}

func (o *EmailOption_UsStocksUserEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for usstocks user email is nil")
	}
	return subject, nil
}

func (o *EmailOption_MfTaxStatementEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MfTaxStatementEmailOption.GetEmailType()
}

func (o *EmailOption_MfTaxStatementEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfTaxStatementEmailOption.GetOption().(type) {
	case *MfTaxStatementEmailOption_MfTaxStatementEmailOptionV1:
		return o.MfTaxStatementEmailOption.GetMfTaxStatementEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MfTaxStatementEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mf TaxStatement user email body object is nil")
	}
	switch o.MfTaxStatementEmailOption.GetOption().(type) {
	case *MfTaxStatementEmailOption_MfTaxStatementEmailOptionV1:
		financialYear := o.MfTaxStatementEmailOption.GetMfTaxStatementEmailOptionV1().GetFinancialYear()
		msg := strings.Replace(templateBody, "{#financialYear#}", financialYear, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mf TaxStatement Email Option")
}

func (o *EmailOption_MfTaxStatementEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for MfTaxStatement user email is nil")
	}
	financialYear := o.MfTaxStatementEmailOption.GetMfTaxStatementEmailOptionV1().GetFinancialYear()
	subject = strings.Replace(subject, "{#financialYear#}", financialYear, -1)
	return subject, nil
}

func (o *EmailOption_MfOneTimeBuyOtpEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MfOneTimeBuyOtpEmailOption.GetEmailType()
}

func (o *EmailOption_MfOneTimeBuyOtpEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfOneTimeBuyOtpEmailOption.GetOption().(type) {
	case *MfOneTimeBuyOtpEmailOption_MfOneTimeBuyOtpEmailOptionV1:
		return o.MfOneTimeBuyOtpEmailOption.GetMfOneTimeBuyOtpEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MfOneTimeBuyOtpEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mf otp one time buy email option body object is nil")
	}
	switch o.MfOneTimeBuyOtpEmailOption.GetOption().(type) {
	case *MfOneTimeBuyOtpEmailOption_MfOneTimeBuyOtpEmailOptionV1:
		msg := strings.Replace(templateBody, "{#otp#}", o.MfOneTimeBuyOtpEmailOption.GetMfOneTimeBuyOtpEmailOptionV1().GetOtp(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mf otp one time buy Email Option")
}

func (o *EmailOption_MfOneTimeBuyOtpEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for mf otp one time buy email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_MfRegisterSipOtpEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MfRegisterSipOtpEmailOption.GetEmailType()
}

func (o *EmailOption_MfRegisterSipOtpEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfRegisterSipOtpEmailOption.GetOption().(type) {
	case *MfRegisterSIPOtpEmailOption_MfRegisterSipOtpEmailOptionV1:
		return o.MfRegisterSipOtpEmailOption.GetMfRegisterSipOtpEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MfRegisterSipOtpEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mf register sip email option body is nil")
	}
	switch o.MfRegisterSipOtpEmailOption.GetOption().(type) {
	case *MfRegisterSIPOtpEmailOption_MfRegisterSipOtpEmailOptionV1:
		msg := strings.Replace(templateBody, "{#otp#}", o.MfRegisterSipOtpEmailOption.GetMfRegisterSipOtpEmailOptionV1().GetOtp(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mf register sip email option")
}

func (o *EmailOption_MfRegisterSipOtpEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for mf register sip email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_CxWatsonIncidentCreationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CxWatsonIncidentCreationEmailOption.GetEmailType()
}

func (o *EmailOption_CxWatsonIncidentCreationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CxWatsonIncidentCreationEmailOption.GetOption().(type) {
	case *CxWatsonIncidentCreationEmailOption_CxWatsonIncidentCreationEmailOptionV1:
		return o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1().GetTemplateVersion()
	case *CxWatsonIncidentCreationEmailOption_CxWatsonIncidentCreationEmailOptionV2:
		return o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CxWatsonIncidentCreationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cx watson incident creation email option is nil")
	}
	switch o.CxWatsonIncidentCreationEmailOption.GetOption().(type) {
	case *CxWatsonIncidentCreationEmailOption_CxWatsonIncidentCreationEmailOptionV1:
		name := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1().GetName()
		msg := strings.Replace(templateBody, "{#first_name#}", name, -1)
		issueName := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1().GetIssueName()
		msg = strings.Replace(msg, "{#issue_name#}", issueName, -1)
		ticketId := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1().GetTicketId()
		msg = strings.Replace(msg, "{#ticket_id#}", strconv.FormatInt(ticketId, 10), -1)
		reportedOn := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1().GetIssueIdentifiedTime().AsTime().In(datetime.IST).Format(time.RFC1123)
		msg = strings.Replace(msg, "{#reported_on_date_time#}", reportedOn, -1)
		expectedToResolveBy := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1().GetExpectedResolutionTime().AsTime().In(datetime.IST).Format(time.RFC1123)
		msg = strings.Replace(msg, "{#resolved_by_date_time#}", expectedToResolveBy, -1)
		return msg, nil
	case *CxWatsonIncidentCreationEmailOption_CxWatsonIncidentCreationEmailOptionV2:
		name := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2().GetName()
		msg := strings.Replace(templateBody, "{#first_name#}", name, -1)
		issueName := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2().GetIssueName()
		msg = strings.Replace(msg, "{#issue_name#}", issueName, -1)
		ticketId := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2().GetTicketId()
		msg = strings.Replace(msg, "{#ticket_id#}", strconv.FormatInt(ticketId, 10), -1)
		msgBody := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2().GetMsg()
		msg = strings.Replace(msg, "{#msg#}", msgBody, -1)
		expectedToResolveBy := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2().GetExpectedResolutionTime().AsTime().In(datetime.IST).Format(time.RFC1123)
		msg = strings.Replace(msg, "{#resolved_by_date_time#}", expectedToResolveBy, -1)
		return msg, nil
	}
	return "", errors.New("no valid version found for cx watson incident creation email template")
}

func (o *EmailOption_CxWatsonIncidentCreationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cx watson incident creation email option is nil")
	}
	switch o.CxWatsonIncidentCreationEmailOption.GetOption().(type) {
	case *CxWatsonIncidentCreationEmailOption_CxWatsonIncidentCreationEmailOptionV1:
		options := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV1()
		subject = strings.Replace(subject, "{#subject#}", options.GetSubject(), 1)
		return subject, nil
	case *CxWatsonIncidentCreationEmailOption_CxWatsonIncidentCreationEmailOptionV2:
		options := o.CxWatsonIncidentCreationEmailOption.GetCxWatsonIncidentCreationEmailOptionV2()
		subject = strings.Replace(subject, "{#subject#}", options.GetSubject(), 1)
		return subject, nil
	}
	return "", errors.New("no valid version found for cx watson incident creation email template")
}

func (o *EmailOption_CategorySpendsExceededReminderEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CategorySpendsExceededReminderEmailOption.GetEmailType()
}

func (o *EmailOption_CategorySpendsExceededReminderEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CategorySpendsExceededReminderEmailOption.GetOption().(type) {
	case *CategorySpendsExceededReminderEmailOption_ReminderEmailOptionV1:
		return o.CategorySpendsExceededReminderEmailOption.GetReminderEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CategorySpendsExceededReminderEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("category exceeded reminder email option is nil")
	}
	switch o.CategorySpendsExceededReminderEmailOption.GetOption().(type) {
	case *CategorySpendsExceededReminderEmailOption_ReminderEmailOptionV1:
		category := o.CategorySpendsExceededReminderEmailOption.GetReminderEmailOptionV1().GetCategory()
		msg := ReplaceWithTrim(templateBody, "{#category#}", category, 1)
		amount := o.CategorySpendsExceededReminderEmailOption.GetReminderEmailOptionV1().GetAmount()
		msg = ReplaceWithTrim(msg, "{#amount#}", amount, 1)
		deeplink := o.CategorySpendsExceededReminderEmailOption.GetReminderEmailOptionV1().GetDeeplink()
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for category exceeded reminder")
}

func (o *EmailOption_CategorySpendsExceededReminderEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for category spends exceeded email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_CreditCardBillPaymentDueDateReminderEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardBillPaymentDueDateReminderEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardBillPaymentDueDateReminderEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardBillPaymentDueDateReminderEmailOption.GetOption().(type) {
	case *CreditCardBillPaymentDueDateReminderEmailOption_ReminderEmailOptionV1:
		return o.CreditCardBillPaymentDueDateReminderEmailOption.GetReminderEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CreditCardBillPaymentDueDateReminderEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cc due date reminder email option is nil")
	}
	switch o.CreditCardBillPaymentDueDateReminderEmailOption.GetOption().(type) {
	case *CreditCardBillPaymentDueDateReminderEmailOption_ReminderEmailOptionV1:
		date := o.CreditCardBillPaymentDueDateReminderEmailOption.GetReminderEmailOptionV1().GetDate()
		msg := ReplaceWithTrim(templateBody, "{#date#}", date.AsTime().Format("January 2, 2006"), 1)
		amount := o.CreditCardBillPaymentDueDateReminderEmailOption.GetReminderEmailOptionV1().GetAmount()
		msg = ReplaceWithTrim(msg, "{#amount#}", amount, 1)
		deeplink := o.CreditCardBillPaymentDueDateReminderEmailOption.GetReminderEmailOptionV1().GetDeeplink()
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cc due date reminder")
}

func (o *EmailOption_CreditCardBillPaymentDueDateReminderEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for credit card bill payment email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_MfCapitalGainsEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MfCapitalGainsEmailOption.GetEmailType()
}

func (o *EmailOption_MfCapitalGainsEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfCapitalGainsEmailOption.GetOption().(type) {
	case *MfCapitalGainsEmailOption_MfCapitalGainsEmailOptionV1:
		return o.MfCapitalGainsEmailOption.GetMfCapitalGainsEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MfCapitalGainsEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mf CapitalGainsStatement user email body object is nil")
	}
	switch o.MfCapitalGainsEmailOption.GetOption().(type) {
	case *MfCapitalGainsEmailOption_MfCapitalGainsEmailOptionV1:
		financialYear := o.MfCapitalGainsEmailOption.GetMfCapitalGainsEmailOptionV1().GetFinancialYear()
		msg := strings.Replace(templateBody, "{#financialYear#}", financialYear, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mf CapitalGainsStatement Email Option")
}

func (o *EmailOption_MfCapitalGainsEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for CapitalGainsStatement user email is nil")
	}
	financialYear := o.MfCapitalGainsEmailOption.GetMfCapitalGainsEmailOptionV1().GetFinancialYear()
	subject = strings.Replace(subject, "{#financialYear#}", financialYear, -1)
	return subject, nil
}

func (o *EmailOption_CreditTxnEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditTxnEmailOption.GetEmailType()
}

func (o *EmailOption_CreditTxnEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditTxnEmailOption.GetOption().(type) {
	case *CreditTxnEmailOption_CreditTxnEmailOptionV1:
		return o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CreditTxnEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit transaction email option is nil")
	}
	switch o.CreditTxnEmailOption.GetOption().(type) {
	case *CreditTxnEmailOption_CreditTxnEmailOptionV1:
		txnAmount := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetTxnAmount()
		txnDate := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetTxnDate()
		lastFourDigitOfAccount := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetLastFourDigitOfAccount()
		fromActorName := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetSenderDetails()

		msg := strings.Replace(templateBody, "{#txn_amount#}", moneyPkg.ToDisplayString(txnAmount), 1)
		msg = strings.Replace(msg, "{#date_time#}", txnDate, 1)
		msg = strings.Replace(msg, "{#account_no_last_four#}", lastFourDigitOfAccount, 1)
		msg = strings.Replace(msg, "{#sender_pi#}", fromActorName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for credit txn email template")
}

func (o *EmailOption_CreditTxnEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit txn email option is nil")
	}
	switch o.CreditTxnEmailOption.GetOption().(type) {
	case *CreditTxnEmailOption_CreditTxnEmailOptionV1:
		txnAmount := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetTxnAmount()
		bankName := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetBankName()
		accountType := o.CreditTxnEmailOption.GetCreditTxnEmailOptionV1().GetAccountType()
		if bankName == "" {
			bankName = "Federal"
		}
		if accountType == "" {
			accountType = "Savings"
		}
		sub := strings.Replace(subject, "{#txn_amount#}", moneyPkg.ToDisplayString(txnAmount), 1)
		sub = strings.Replace(sub, "{#bank_name#}", bankName, 1)
		sub = strings.Replace(sub, "{#account_type#}", accountType, 1)

		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for credit transaction email template")
}

func (o *EmailOption_RiskAutoActionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskAutoActionEmailOption.GetEmailType()
}

func (o *EmailOption_RiskAutoActionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskAutoActionEmailOption.GetOption().(type) {
	case *RiskAutoActionEmailOption_RiskAutoActionEmailOptionV1:
		return o.RiskAutoActionEmailOption.GetRiskAutoActionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskAutoActionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk auto action user email body object is nil")
	}
	switch o.RiskAutoActionEmailOption.GetOption().(type) {
	case *RiskAutoActionEmailOption_RiskAutoActionEmailOptionV1:
		firstName := o.RiskAutoActionEmailOption.GetRiskAutoActionEmailOptionV1().GetFirstName()
		documentQuestions := o.RiskAutoActionEmailOption.GetRiskAutoActionEmailOptionV1().GetDocumentRequiredQuestions()
		generalQuestions := o.RiskAutoActionEmailOption.GetRiskAutoActionEmailOptionV1().GetGeneralQuestions()
		var documentString strings.Builder
		var generalString strings.Builder
		for _, str := range documentQuestions {
			documentString.WriteString(str)
			documentString.WriteString("<br>")
		}
		for _, str := range generalQuestions {
			generalString.WriteString(str)
			generalString.WriteString("<br>")
		}
		msg := strings.Replace(templateBody, "{#name#}", firstName, -1)
		msg = strings.Replace(msg, "{#document_required_questions#}", documentString.String(), 1)
		msg = strings.Replace(msg, "{#general_questions#}", generalString.String(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for risk auto action email")
}

func (o *EmailOption_RiskAutoActionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for risk auto action email is nil")
	}
	return subject, nil
}

func (o *EmailOption_RiskLeaEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskLeaEmailOption.GetEmailType()
}

func (o *EmailOption_RiskLeaEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskLeaEmailOption.GetOption().(type) {
	case *RiskLEAEmailOption_RiskLeaEmailOptionV1:
		return o.RiskLeaEmailOption.GetRiskLeaEmailOptionV1().GetTemplateVersion()
	case *RiskLEAEmailOption_RiskLeaEmailOptionV2:
		return o.RiskLeaEmailOption.GetRiskLeaEmailOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskLeaEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk LEA user email body object is nil")
	}
	switch o.RiskLeaEmailOption.GetOption().(type) {
	case *RiskLEAEmailOption_RiskLeaEmailOptionV1:
		infoFields := o.RiskLeaEmailOption.GetRiskLeaEmailOptionV1().GetInfoFields()
		var documentString strings.Builder
		for _, str := range infoFields {
			documentString.WriteString(str)
			documentString.WriteString("<br>")
		}

		msg := strings.Replace(templateBody, "{#metadata#}", documentString.String(), 1)
		return msg, nil
	case *RiskLEAEmailOption_RiskLeaEmailOptionV2:
		complaintDetails := o.RiskLeaEmailOption.GetRiskLeaEmailOptionV2().GetComplaintDetails()
		leaContactInfo := o.RiskLeaEmailOption.GetRiskLeaEmailOptionV2().GetLeaContactInfo()
		msg := strings.Replace(templateBody, "{#metadata2#}", complaintDetails, 1)
		msg = strings.Replace(msg, "{#metadata1#}", leaContactInfo, 1)
		switch {
		case leaContactInfo == "":
			msg = strings.Replace(msg, "{#metadata3#}", "NOTE: Specific LEA contact details will be shared with you as soon as we receive them.", 1)
		case leaContactInfo != "":
			msg = strings.Replace(msg, "{#metadata3#}", "", 1)
		}
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for risk LEA template")
}

func (o *EmailOption_RiskLeaEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for risk LEA email is nil")
	}
	return subject, nil
}

func (o *EmailOption_RiskLeaContactInfoOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskLeaContactInfoOption.GetEmailType()
}

func (o *EmailOption_RiskLeaContactInfoOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskLeaContactInfoOption.GetOption().(type) {
	case *RiskLEAContactInfoOption_LeaContactInfoOptionV1:
		return o.RiskLeaContactInfoOption.GetLeaContactInfoOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskLeaContactInfoOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk lea contact email body object is nil")
	}
	switch o.RiskLeaContactInfoOption.GetOption().(type) {
	case *RiskLEAContactInfoOption_LeaContactInfoOptionV1:
		complaintDetails := o.RiskLeaContactInfoOption.GetLeaContactInfoOptionV1().GetComplaintDetails()
		leaContactInfo := o.RiskLeaContactInfoOption.GetLeaContactInfoOptionV1().GetLeaContactInfo()
		msg := strings.Replace(templateBody, "{#metadata1#}", leaContactInfo, 1)
		msg = strings.Replace(msg, "{#metadata2#}", complaintDetails, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for lea contact template")
}

func (o *EmailOption_RiskLeaContactInfoOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for lea contact email is nil")
	}
	return subject, nil
}

func (o *EmailOption_CreditCardClosureConfirmationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardClosureConfirmationEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardClosureConfirmationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardClosureConfirmationEmailOption.GetOption().(type) {
	case *CreditCardClosureConfirmationEmailOption_CreditCardClosureConfirmationEmailOptionV1:
		return o.CreditCardClosureConfirmationEmailOption.GetCreditCardClosureConfirmationEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CreditCardClosureConfirmationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card closure confirmation email body object is nil")
	}
	switch o.CreditCardClosureConfirmationEmailOption.GetOption().(type) {
	case *CreditCardClosureConfirmationEmailOption_CreditCardClosureConfirmationEmailOptionV1:
		firstName := o.CreditCardClosureConfirmationEmailOption.GetCreditCardClosureConfirmationEmailOptionV1().GetCustomerName().GetFirstName()
		ccLastFourDigits := o.CreditCardClosureConfirmationEmailOption.GetCreditCardClosureConfirmationEmailOptionV1().GetCcLastFourDigits()
		msg := strings.Replace(templateBody, "{#first_name#}", firstName, 1)
		msg = strings.Replace(msg, "{#last_four_digits#}", ccLastFourDigits, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for credit card closure confirmation email")
}

func (o *EmailOption_CreditCardClosureConfirmationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for credit card closure confirmation email is nil")
	}
	switch o.CreditCardClosureConfirmationEmailOption.GetOption().(type) {
	case *CreditCardClosureConfirmationEmailOption_CreditCardClosureConfirmationEmailOptionV1:
		ccLastFourDigits := o.CreditCardClosureConfirmationEmailOption.GetCreditCardClosureConfirmationEmailOptionV1().GetCcLastFourDigits()
		sub := strings.Replace(subject, "{#last_four_digits#}", ccLastFourDigits, 1)
		return sub, nil
	}
	return subject, nil
}

func (o *EmailOption_KycAgentWelcomeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.KycAgentWelcomeEmailOption.GetEmailType()
}

func (o *EmailOption_KycAgentWelcomeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.KycAgentWelcomeEmailOption.GetOption().(type) {
	case *KycAgentWelcomeEmailOption_KycAgentWelcomeEmailOptionV1:
		return o.KycAgentWelcomeEmailOption.GetKycAgentWelcomeEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_KycAgentWelcomeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card closure confirmation email body object is nil")
	}
	switch o.KycAgentWelcomeEmailOption.GetOption().(type) {
	case *KycAgentWelcomeEmailOption_KycAgentWelcomeEmailOptionV1:
		firstName := o.KycAgentWelcomeEmailOption.GetKycAgentWelcomeEmailOptionV1().GetAgentName().GetFirstName()
		id := o.KycAgentWelcomeEmailOption.GetKycAgentWelcomeEmailOptionV1().GetAgentId()
		pin := o.KycAgentWelcomeEmailOption.GetKycAgentWelcomeEmailOptionV1().GetPin()
		msg := strings.Replace(templateBody, "{#first_name#}", firstName, 1)
		msg = strings.Replace(msg, "{#agent_id#}", id, 1)
		msg = strings.Replace(msg, "{#pin#}", pin, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for credit card closure confirmation email")
}

func (o *EmailOption_KycAgentWelcomeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for email is nil")
	}
	return subject, nil
}

func (o *EmailOption_GenieLoginOtpEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.GenieLoginOtpEmailOption.GetEmailType()
}

func (o *EmailOption_GenieLoginOtpEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.GenieLoginOtpEmailOption.GetOption().(type) {
	case *GenieLoginOTPEmailOption_GenieLoginOtpEmailOptionV1:
		return o.GenieLoginOtpEmailOption.GetGenieLoginOtpEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_GenieLoginOtpEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mf otp one time buy email option body object is nil")
	}
	switch o.GenieLoginOtpEmailOption.GetOption().(type) {
	case *GenieLoginOTPEmailOption_GenieLoginOtpEmailOptionV1:
		msg := strings.Replace(templateBody, "{#otp#}", o.GenieLoginOtpEmailOption.GetGenieLoginOtpEmailOptionV1().GetOtp(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mf otp one time buy Email Option")
}

func (o *EmailOption_GenieLoginOtpEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for mf otp one time buy email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_MfWithdrawalOtpEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MfWithdrawalOtpEmailOption.GetEmailType()
}

func (o *EmailOption_MfWithdrawalOtpEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfWithdrawalOtpEmailOption.GetOption().(type) {
	case *MfWithdrawalOtpEmailOption_MfWithdrawalOtpEmailV1:
		return o.MfWithdrawalOtpEmailOption.GetMfWithdrawalOtpEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MfWithdrawalOtpEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mf withdrawal otp email option body object is nil")
	}
	switch o.MfWithdrawalOtpEmailOption.GetOption().(type) {
	case *MfWithdrawalOtpEmailOption_MfWithdrawalOtpEmailV1:
		msg := strings.Replace(templateBody, "{#otp#}", o.MfWithdrawalOtpEmailOption.GetMfWithdrawalOtpEmailV1().GetOtp(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mf withdrawal otp Email Option")
}

func (o *EmailOption_MfWithdrawalOtpEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for mf withdrawal buy email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_DisputeStatusEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.DisputeStatusEmailOption.GetEmailType()
}
func (o *EmailOption_DisputeStatusEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("dispute status email option is nil")
	}
	switch o.DisputeStatusEmailOption.GetOption().(type) {
	case *DisputeStatusEmailOption_DisputeStatusEmailOptionsV1:
		name := o.DisputeStatusEmailOption.GetDisputeStatusEmailOptionsV1().GetFirstName()
		msg := strings.Replace(templateBody, "{#first_name#}", name, -1)
		disputeId := o.DisputeStatusEmailOption.GetDisputeStatusEmailOptionsV1().GetDisputeCaseNumber()
		msg = strings.Replace(msg, "{#dispute_case_number#}", disputeId, -1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for dispute status email template")
}
func (o *EmailOption_DisputeStatusEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DisputeStatusEmailOption.GetOption().(type) {
	case *DisputeStatusEmailOption_DisputeStatusEmailOptionsV1:
		return o.DisputeStatusEmailOption.GetDisputeStatusEmailOptionsV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}
func (o *EmailOption_DisputeStatusEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("dispute status email option is nil")
	}
	return subject, nil
}

/* end email option impl for dc forex refunds email */

func (o *EmailOption_WebMinKycClosedAccBalanceTransferEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.WebMinKycClosedAccBalanceTransferEmailOption.GetEmailType()
}

func (o *EmailOption_WebMinKycClosedAccBalanceTransferEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WebMinKycClosedAccBalanceTransferEmailOption.GetOption().(type) {
	case *WebMinKycClosedAccBalanceTransferEmailOption_WebMinKycClosedAccBalanceTransferEmailV1:
		return o.WebMinKycClosedAccBalanceTransferEmailOption.GetWebMinKycClosedAccBalanceTransferEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_WebMinKycClosedAccBalanceTransferEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("work email verification email option is nil")
	}
	switch o.WebMinKycClosedAccBalanceTransferEmailOption.GetOption().(type) {
	case *WebMinKycClosedAccBalanceTransferEmailOption_WebMinKycClosedAccBalanceTransferEmailV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for web min kyc closed account balance transfer email template")
}

func (o *EmailOption_WebMinKycClosedAccBalanceTransferEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("internal finite code share email option is nil")
	}
	switch o.WebMinKycClosedAccBalanceTransferEmailOption.GetOption().(type) {
	case *WebMinKycClosedAccBalanceTransferEmailOption_WebMinKycClosedAccBalanceTransferEmailV1:
		options := o.WebMinKycClosedAccBalanceTransferEmailOption.GetWebMinKycClosedAccBalanceTransferEmailV1()
		body := strings.Replace(templateBody, "{#otp#}", options.GetOtp(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for web min kyc closed account balance transfer email template")
}

func (o *EmailOption_SavingsAccountSummaryEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.SavingsAccountSummaryEmailOption.GetEmailType()
}

func (o *EmailOption_SavingsAccountSummaryEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SavingsAccountSummaryEmailOption.GetOption().(type) {
	case *SavingsAccountSummaryEmailOption_SavingsAccountSummaryEmailV1:
		return o.SavingsAccountSummaryEmailOption.GetSavingsAccountSummaryEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SavingsAccountSummaryEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("savings account summary email option is nil")
	}
	switch o.SavingsAccountSummaryEmailOption.GetOption().(type) {
	case *SavingsAccountSummaryEmailOption_SavingsAccountSummaryEmailV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for savings account summary email template")
}

func (o *EmailOption_SavingsAccountSummaryEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("savings account summary email option is nil")
	}
	switch o.SavingsAccountSummaryEmailOption.GetOption().(type) {
	case *SavingsAccountSummaryEmailOption_SavingsAccountSummaryEmailV1:
		options := o.SavingsAccountSummaryEmailOption.GetSavingsAccountSummaryEmailV1()
		templateBody = strings.Replace(templateBody, "{#interest_rate#}", options.GetInterestRate(), -1)
		templateBody = strings.Replace(templateBody, "{#current_date#}", options.GetCurrentDate(), -1)
		return templateBody, nil
	}
	return "", errors.New("no valid version found for savings account summary email template")
}

func (o *EmailOption_EmailUpdateFederalEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.EmailUpdateFederalEmailOption.GetEmailType()
}

func (o *EmailOption_EmailUpdateFederalEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.EmailUpdateFederalEmailOption.GetOption().(type) {
	case *EmailUpdateFederalEmailOption_EmailUpdateFederalEmailV1:
		return o.EmailUpdateFederalEmailOption.GetEmailUpdateFederalEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_EmailUpdateFederalEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("email update federal email option is nil")
	}
	switch o.EmailUpdateFederalEmailOption.GetOption().(type) {
	case *EmailUpdateFederalEmailOption_EmailUpdateFederalEmailV1:
		return subject, nil
	}
	return "", fmt.Errorf("no valid version found for savings account summary email template")
}

func (o *EmailOption_EmailUpdateFederalEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("email update federal email option is nil")
	}
	switch o.EmailUpdateFederalEmailOption.GetOption().(type) {
	case *EmailUpdateFederalEmailOption_EmailUpdateFederalEmailV1:
		options := o.EmailUpdateFederalEmailOption.GetEmailUpdateFederalEmailV1()
		templateBody = strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), 1)
		templateBody = strings.Replace(templateBody, "{#requested_date#}", options.GetRequestedDate(), 1)
		templateBody = strings.Replace(templateBody, "{#requested_time#}", options.GetRequestedTime(), 1)
		templateBody = strings.Replace(templateBody, "{#new_email#}", options.GetNewEmail(), 1)
		return templateBody, nil
	}
	return "", errors.New("no valid version found for email update federal email template")
}

/* begin email option impl for risk outcall attempt email */

func (o *EmailOption_RiskOutcallAttemptReminderEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskOutcallAttemptReminderEmailOption.GetEmailType()
}

func (o *EmailOption_RiskOutcallAttemptReminderEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("email option nil for RiskOutcallAttemptReminderEmailOption")
	}
	switch o.RiskOutcallAttemptReminderEmailOption.GetOption().(type) {
	case *RiskOutcallAttemptReminderEmailOption_RiskOutcallAttemptReminderEmailOptionV1:
		templateBody = strings.Replace(templateBody, "{#name#}",
			o.RiskOutcallAttemptReminderEmailOption.GetRiskOutcallAttemptReminderEmailOptionV1().GetName().ToFirstNameLastNameString(), 1)
		templateBody = strings.Replace(templateBody, "{#phone_number#}",
			o.RiskOutcallAttemptReminderEmailOption.GetRiskOutcallAttemptReminderEmailOptionV1().GetMaskedPhoneNumber(), 1)
		templateBody = strings.Replace(templateBody, "{#time#}",
			o.RiskOutcallAttemptReminderEmailOption.GetRiskOutcallAttemptReminderEmailOptionV1().GetFollowUpWindow(), 1)
		templateBody = strings.Replace(templateBody, "{#verified_numbers#}",
			o.RiskOutcallAttemptReminderEmailOption.GetRiskOutcallAttemptReminderEmailOptionV1().GetVerifiedNumbers(), 1)
		return templateBody, nil
	default:
		return "", errors.New("invalid option for RiskOutcallAttemptReminderEmailOption")
	}
}

func (o *EmailOption_RiskOutcallAttemptReminderEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskOutcallAttemptReminderEmailOption.GetOption().(type) {
	case *RiskOutcallAttemptReminderEmailOption_RiskOutcallAttemptReminderEmailOptionV1:
		return TemplateVersion_VERSION_V1
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_RiskOutcallAttemptReminderEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("email option nil for RiskOutcallAttemptReminderEmailOption subject")
	}
	switch o.RiskOutcallAttemptReminderEmailOption.GetOption().(type) {
	case *RiskOutcallAttemptReminderEmailOption_RiskOutcallAttemptReminderEmailOptionV1:
		return subject, nil
	default:
		return "", errors.New("invalid version for RiskOutcallAttemptReminderEmailOption")
	}
}

/* end email option impl for risk outcall attempt email */

func (o *EmailOption_PreCustomerCreationMinToFullKycConversionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.PreCustomerCreationMinToFullKycConversionEmailOption.GetEmailType()
}

func (o *EmailOption_PreCustomerCreationMinToFullKycConversionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PreCustomerCreationMinToFullKycConversionEmailOption.GetOption().(type) {
	case *PreCustomerCreationMinToFullKycConversionEmailOption_PreCustomerCreationMinToFullKycConversionEmailOptionV1:
		return o.PreCustomerCreationMinToFullKycConversionEmailOption.GetPreCustomerCreationMinToFullKycConversionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_PreCustomerCreationMinToFullKycConversionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("min full kyc conversion email option is nil")
	}
	switch o.PreCustomerCreationMinToFullKycConversionEmailOption.GetOption().(type) {
	case *PreCustomerCreationMinToFullKycConversionEmailOption_PreCustomerCreationMinToFullKycConversionEmailOptionV1:
		options := o.PreCustomerCreationMinToFullKycConversionEmailOption.GetPreCustomerCreationMinToFullKycConversionEmailOptionV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for pre customer creation min to full kyc conversion email template")
}

func (o *EmailOption_PreCustomerCreationMinToFullKycConversionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for pre customer creation min to full kyc conversion email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_LoansPaymentFileEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LoansPaymentFileEmailOption.GetEmailType()
}

func (o *EmailOption_LoansPaymentFileEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LoansPaymentFileEmailOption.GetOption().(type) {
	case *LoansPaymentFileEmailOption_LoansPaymentFileEmailV1:
		return o.LoansPaymentFileEmailOption.GetLoansPaymentFileEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LoansPaymentFileEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("loans payment file email option is nil")
	}
	switch o.LoansPaymentFileEmailOption.GetOption().(type) {
	case *LoansPaymentFileEmailOption_LoansPaymentFileEmailV1:
		options := o.LoansPaymentFileEmailOption.GetLoansPaymentFileEmailV1()
		body := strings.Replace(templateBody, "{#bodyText#}", options.GetBodyText(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for loans payment file email template")
}

func (o *EmailOption_LoansPaymentFileEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for loans payment file email option is nil")
	}
	switch o.LoansPaymentFileEmailOption.GetOption().(type) {
	case *LoansPaymentFileEmailOption_LoansPaymentFileEmailV1:
		options := o.LoansPaymentFileEmailOption.GetLoansPaymentFileEmailV1()
		updatedSubject := strings.Replace(subject, "{#reportDate#}", options.GetReportDate(), 1)
		return updatedSubject, nil
	}
	return subject, nil
}

func (o *EmailOption_LamfLoanAmountDisbursedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfLoanAmountDisbursedEmailOption.GetEmailType()
}

func (o *EmailOption_LamfLoanAmountDisbursedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfLoanAmountDisbursedEmailOption.GetOption().(type) {
	case *LamfLoanAmountDisbursedEmailOption_LamfAmountDisbursedEmailV1:
		return o.LamfLoanAmountDisbursedEmailOption.GetLamfAmountDisbursedEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfLoanAmountDisbursedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf loan amount disbursed email option is nil")
	}
	switch o.LamfLoanAmountDisbursedEmailOption.GetOption().(type) {
	case *LamfLoanAmountDisbursedEmailOption_LamfAmountDisbursedEmailV1:
		options := o.LamfLoanAmountDisbursedEmailOption.GetLamfAmountDisbursedEmailV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetInvestorName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for lamf loan amount disbursed email template")
}

func (o *EmailOption_LamfLoanAmountDisbursedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for lamf loan amount disbursed email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_LamfLoanClosedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfLoanClosedEmailOption.GetEmailType()
}

func (o *EmailOption_LamfLoanClosedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfLoanClosedEmailOption.GetOption().(type) {
	case *LamfLoanClosedEmailOption_LamfLoanClosedEmailV1:
		return o.LamfLoanClosedEmailOption.GetLamfLoanClosedEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfLoanClosedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf loan closed email option is nil")
	}
	switch o.LamfLoanClosedEmailOption.GetOption().(type) {
	case *LamfLoanClosedEmailOption_LamfLoanClosedEmailV1:
		options := o.LamfLoanClosedEmailOption.GetLamfLoanClosedEmailV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetInvestorName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for lamf loan closed email template")
}

func (o *EmailOption_LamfLoanClosedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for lamf loan closed email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_LamfLoanEmiOverdueOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfLoanEmiOverdueOption.GetEmailType()
}

func (o *EmailOption_LamfLoanEmiOverdueOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfLoanEmiOverdueOption.GetOption().(type) {
	case *LamfLoanEmiOverdueOption_LamfLoanEmiOverdueEmailV1:
		return o.LamfLoanEmiOverdueOption.GetLamfLoanEmiOverdueEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfLoanEmiOverdueOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf loan emi overdue email option is nil")
	}
	switch o.LamfLoanEmiOverdueOption.GetOption().(type) {
	case *LamfLoanEmiOverdueOption_LamfLoanEmiOverdueEmailV1:
		options := o.LamfLoanEmiOverdueOption.GetLamfLoanEmiOverdueEmailV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetInvestorName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for lamf loan emi overdue email template")
}

func (o *EmailOption_LamfLoanEmiOverdueOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for lamf loan emi overdue email is nil")
	}
	return subject, nil
}

func (o *EmailOption_LamfLoanEmiDueLowBalanceOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfLoanEmiDueLowBalanceOption.GetEmailType()
}

func (o *EmailOption_LamfLoanEmiDueLowBalanceOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfLoanEmiDueLowBalanceOption.GetOption().(type) {
	case *LamfLoanEmiDueLowBalanceOption_LamfLoanEmiDueLowBalanceEmailV1:
		return o.LamfLoanEmiDueLowBalanceOption.GetLamfLoanEmiDueLowBalanceEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfLoanEmiDueLowBalanceOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf loan emi due low balance email option is nil")
	}
	switch o.LamfLoanEmiDueLowBalanceOption.GetOption().(type) {
	case *LamfLoanEmiDueLowBalanceOption_LamfLoanEmiDueLowBalanceEmailV1:
		options := o.LamfLoanEmiDueLowBalanceOption.GetLamfLoanEmiDueLowBalanceEmailV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#due_date#}", options.GetDueDate(), 1)
		body = strings.Replace(body, "{#emi_amount#}", options.GetEmiAmount(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for lamf loan emi due low balance option template")
}

func (o *EmailOption_LamfLoanEmiDueLowBalanceOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for lamf loan emi due low balance option is nil")
	}
	return subject, nil
}

func (o *EmailOption_CreditCardEligibleWebFlowEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardEligibleWebFlowEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardEligibleWebFlowEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEligibleWebFlowEmailOption.GetOption().(type) {
	case *CreditCardEligibleWebFlowEmailOption_CcEligibleWebFlowEmailOptionV1:
		return o.CreditCardEligibleWebFlowEmailOption.GetCcEligibleWebFlowEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_CreditCardEligibleWebFlowEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("cc eligible web flow email option is nil")
	}
	switch o.CreditCardEligibleWebFlowEmailOption.GetOption().(type) {
	case *CreditCardEligibleWebFlowEmailOption_CcEligibleWebFlowEmailOptionV1:
		options := o.CreditCardEligibleWebFlowEmailOption.GetCcEligibleWebFlowEmailOptionV1()
		body := strings.Replace(templateBody, "{#name#}", options.GetName(), 1)
		body = strings.Replace(body, "{#application_download_url#}", options.GetApplicationDownloadUrl(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for cc eligible  web flow email template")
}

func (o *EmailOption_CreditCardEligibleWebFlowEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for cc eligible  web flow email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_SaClosureRequestEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}

	return o.SaClosureRequestEmailOption.GetEmailType()
}

func (o *EmailOption_SaClosureRequestEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch o.SaClosureRequestEmailOption.GetOption().(type) {
	case *SaClosureRequestEmailOption_SaClosureSubmitToCxEmailV1:
		return o.SaClosureRequestEmailOption.GetSaClosureSubmitToCxEmailV1().GetTemplateVersion()
	default:
	}

	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_SaClosureRequestEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("min kyc account closure user verified cx email option is nil")
	}

	switch o.SaClosureRequestEmailOption.GetOption().(type) {
	case *SaClosureRequestEmailOption_SaClosureSubmitToCxEmailV1:
		options := o.SaClosureRequestEmailOption.GetSaClosureSubmitToCxEmailV1()
		body := strings.Replace(templateBody, "{#additional_msg#}", options.GetAdditionalMessage(), 1)
		return body, nil
	default:
	}
	return "", errors.New("no valid version found for min kyc account closure user verified cx email option")
}

func (o *EmailOption_SaClosureRequestEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for min kyc account closure user verified cx email option is nil")
	}
	switch o.SaClosureRequestEmailOption.GetOption().(type) {
	case *SaClosureRequestEmailOption_SaClosureSubmitToCxEmailV1:
		options := o.SaClosureRequestEmailOption.GetSaClosureSubmitToCxEmailV1()
		subject = strings.Replace(subject, "{#current_date#}", options.GetCurrentDate(), 1)
		return subject, nil
	default:
	}
	return "", errors.New("no valid version found for min kyc account closure user verified cx email option")
}

func (o *EmailOption_ClubItcGreenPointsTransferRequestEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.ClubItcGreenPointsTransferRequestEmailOption.GetEmailType()
}

func (o *EmailOption_ClubItcGreenPointsTransferRequestEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ClubItcGreenPointsTransferRequestEmailOption.GetOption().(type) {
	case *ClubItcGreenPointsTransferRequestEmailOption_ClubItcGreenPointsTransferRequestEmailOptionV1:
		return o.ClubItcGreenPointsTransferRequestEmailOption.GetClubItcGreenPointsTransferRequestEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_ClubItcGreenPointsTransferRequestEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("club itc green points transfer request email option is nil")
	}
	switch o.ClubItcGreenPointsTransferRequestEmailOption.GetOption().(type) {
	case *ClubItcGreenPointsTransferRequestEmailOption_ClubItcGreenPointsTransferRequestEmailOptionV1:
		options := o.ClubItcGreenPointsTransferRequestEmailOption.GetClubItcGreenPointsTransferRequestEmailOptionV1()
		body := strings.Replace(templateBody, "{#body_text#}", options.GetBodyText(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for club itc green points transfer request email template")
}

func (o *EmailOption_ClubItcGreenPointsTransferRequestEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for club itc green points transfer request email option is nil")
	}
	switch o.ClubItcGreenPointsTransferRequestEmailOption.GetOption().(type) {
	case *ClubItcGreenPointsTransferRequestEmailOption_ClubItcGreenPointsTransferRequestEmailOptionV1:
		options := o.ClubItcGreenPointsTransferRequestEmailOption.GetClubItcGreenPointsTransferRequestEmailOptionV1()
		subject = strings.Replace(subject, "{#report_date#}", options.GetReportDate(), 1)
		return subject, nil
	default:
	}
	return subject, nil
}

func (o *EmailOption_RiskOutcallEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskOutcallEmailOption.GetEmailType()
}

func (o *EmailOption_RiskOutcallEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskOutcallEmailOption.GetOption().(type) {
	case *RiskOutcallEmailOption_RiskOutcallEmailOptionV1:
		return o.RiskOutcallEmailOption.GetRiskOutcallEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskOutcallEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("Risk Outcall Email Option is nil")
	}
	switch o.RiskOutcallEmailOption.GetOption().(type) {
	case *RiskOutcallEmailOption_RiskOutcallEmailOptionV1:
		options := o.RiskOutcallEmailOption.GetRiskOutcallEmailOptionV1()
		body := strings.Replace(templateBody, "{#form_url#}", options.GetFormUrl(), 1)
		body = strings.Replace(body, "{#name#}", options.GetName(), 1)
		body = strings.Replace(body, "{#form_expiry#}", options.GetFormExpiry(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for Risk Outcall Email Option template")
}

func (o *EmailOption_RiskOutcallEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for Risk Outcall Email Option is nil")
	}
	switch o.RiskOutcallEmailOption.GetOption().(type) {
	case *RiskOutcallEmailOption_RiskOutcallEmailOptionV1:
		options := o.RiskOutcallEmailOption.GetRiskOutcallEmailOptionV1()
		if !options.GetIsReminderEmail() {
			return subject, nil
		}
		return fmt.Sprintf("Reminder: %s", subject), nil
	}
	return subject, nil
}

func (o *EmailOption_RiskOutcallEmailOption) SetFormAttributes(formUrl, fromExpiry string) error {
	if o == nil {
		return fmt.Errorf("Risk Outcall Email Option is nil")
	}
	switch o.RiskOutcallEmailOption.GetOption().(type) {
	case *RiskOutcallEmailOption_RiskOutcallEmailOptionV1:
		o.RiskOutcallEmailOption.GetRiskOutcallEmailOptionV1().FormUrl = formUrl
		o.RiskOutcallEmailOption.GetRiskOutcallEmailOptionV1().FormExpiry = fromExpiry
		return nil
	}
	return fmt.Errorf("no valid version found for Risk Outcall Email Option")
}

func (o *EmailOption_PushAppLogsEmail) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.PushAppLogsEmail.GetEmailType()
}

func (o *EmailOption_PushAppLogsEmail) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PushAppLogsEmail.GetOption().(type) {
	case *PushAppLogsEmailOption_PushAppLogsEmailOptionV1:
		return o.PushAppLogsEmail.GetPushAppLogsEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_PushAppLogsEmail) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("Push App Logs Email is nil")
	}
	switch o.PushAppLogsEmail.GetOption().(type) {
	case *PushAppLogsEmailOption_PushAppLogsEmailOptionV1:
		options := o.PushAppLogsEmail.GetPushAppLogsEmailOptionV1()
		body := strings.Replace(templateBody, "{#open_fi_url#}", options.GetDeepLink(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for Push App Logs Email template")
}

func (o *EmailOption_PushAppLogsEmail) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for Push App Logs Email is nil")
	}
	return subject, nil
}

func (o *EmailOption_RiskOpsVkycCreditFreezeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskOpsVkycCreditFreezeEmailOption.GetEmailType()
}

func (o *EmailOption_RiskOpsVkycCreditFreezeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskOpsVkycCreditFreezeEmailOption.GetOption().(type) {
	case *RiskOpsVKYCCreditFreezeEmailOption_RiskOpsVkycCreditFreezeEmailOptionV1:
		return o.RiskOpsVkycCreditFreezeEmailOption.GetRiskOpsVkycCreditFreezeEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskOpsVkycCreditFreezeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("video kyc freeze email option is nil")
	}
	switch o.RiskOpsVkycCreditFreezeEmailOption.GetOption().(type) {
	case *RiskOpsVKYCCreditFreezeEmailOption_RiskOpsVkycCreditFreezeEmailOptionV1:
		options := o.RiskOpsVkycCreditFreezeEmailOption.GetRiskOpsVkycCreditFreezeEmailOptionV1()
		body := strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), 1)
		body = strings.Replace(body, "{#masked_account_number#}", options.GetMaskedAccountNumber(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for video kyc freeze email template")
}

func (o *EmailOption_RiskOpsVkycCreditFreezeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("VKYC freeze email subject empty")
	}
	return subject, nil
}

func (o *EmailOption_LamfCxLoanApplicationClosureResolutionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfCxLoanApplicationClosureResolutionEmailOption.GetEmailType()
}

func (o *EmailOption_LamfCxLoanApplicationClosureResolutionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfCxLoanApplicationClosureResolutionEmailOption.GetOption().(type) {
	case *LamfCxLoanApplicationClosureResolutionEmailOption_LamfCxLoanApplicationClosureResolutionEmailOptionV1:
		return o.LamfCxLoanApplicationClosureResolutionEmailOption.GetLamfCxLoanApplicationClosureResolutionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfCxLoanApplicationClosureResolutionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan application closure email option is nil")
	}
	switch o.LamfCxLoanApplicationClosureResolutionEmailOption.GetOption().(type) {
	case *LamfCxLoanApplicationClosureResolutionEmailOption_LamfCxLoanApplicationClosureResolutionEmailOptionV1:
		return templateBody, nil
	}
	return "", errors.New("no valid version found for lamf cx loan application closure email template")
}

func (o *EmailOption_LamfCxLoanApplicationClosureResolutionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan application closure email subject empty")
	}
	return subject, nil
}

func (o *EmailOption_LamfCxLoanAccountClosureResolutionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfCxLoanAccountClosureResolutionEmailOption.GetEmailType()
}

func (o *EmailOption_LamfCxLoanAccountClosureResolutionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfCxLoanAccountClosureResolutionEmailOption.GetOption().(type) {
	case *LamfCxLoanAccountClosureResolutionEmailOption_LamfCxLoanAccountClosureResolutionEmailOptionV1:
		return o.LamfCxLoanAccountClosureResolutionEmailOption.GetLamfCxLoanAccountClosureResolutionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfCxLoanAccountClosureResolutionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan account closure email option is nil")
	}
	switch o.LamfCxLoanAccountClosureResolutionEmailOption.GetOption().(type) {
	case *LamfCxLoanAccountClosureResolutionEmailOption_LamfCxLoanAccountClosureResolutionEmailOptionV1:
		return templateBody, nil
	}
	return "", errors.New("no valid version found for lamf cx loan account closure email template")
}

func (o *EmailOption_LamfCxLoanAccountClosureResolutionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan account closure email subject empty")
	}
	return subject, nil
}

func (o *EmailOption_LamfCxLoanApplicationRetryResolutionEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LamfCxLoanApplicationRetryResolutionEmailOption.GetEmailType()
}

func (o *EmailOption_LamfCxLoanApplicationRetryResolutionEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfCxLoanApplicationRetryResolutionEmailOption.GetOption().(type) {
	case *LamfCxLoanApplicationRetryResolutionEmailOption_LamfCxLoanApplicationRetryResolutionEmailOptionV1:
		return o.LamfCxLoanApplicationRetryResolutionEmailOption.GetLamfCxLoanApplicationRetryResolutionEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_LamfCxLoanApplicationRetryResolutionEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan application retry email option is nil")
	}
	switch o.LamfCxLoanApplicationRetryResolutionEmailOption.GetOption().(type) {
	case *LamfCxLoanApplicationRetryResolutionEmailOption_LamfCxLoanApplicationRetryResolutionEmailOptionV1:
		return templateBody, nil
	}
	return "", errors.New("no valid version found for lamf cx loan application retry email template")
}

func (o *EmailOption_LamfCxLoanApplicationRetryResolutionEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan application retry email subject empty")
	}
	return subject, nil
}

func (o *EmailOption_UssTaxDocumentEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.UssTaxDocumentEmailOption.GetEmailType()
}

func (o *EmailOption_UssTaxDocumentEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UssTaxDocumentEmailOption.GetOption().(type) {
	case *UssTaxDocumentEmailOption_UssTaxDocumentEmailOptionsV1:
		return o.UssTaxDocumentEmailOption.GetUssTaxDocumentEmailOptionsV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_UssTaxDocumentEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("lamf cx loan application retry email option is nil")
	}
	switch o.UssTaxDocumentEmailOption.GetOption().(type) {
	case *UssTaxDocumentEmailOption_UssTaxDocumentEmailOptionsV1:
		options := o.UssTaxDocumentEmailOption.GetUssTaxDocumentEmailOptionsV1()
		templateBody = strings.Replace(templateBody, "{#firstName#}", options.GetFirstName(), 1)
		templateBody = strings.Replace(templateBody, "{#financialYear#}", options.GetFinancialYearStr(), 1)
		return templateBody, nil
	}
	return "", errors.New("no valid version found for us stocks tax documents email template")
}

func (o *EmailOption_UssTaxDocumentEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("us stocks tax documents email subject cannot be empty")
	}
	options := o.UssTaxDocumentEmailOption.GetUssTaxDocumentEmailOptionsV1()
	subject = strings.Replace(subject, "{#financialYear#}", options.GetFinancialYearStr(), 1)
	return subject, nil
}

func (o *EmailOption_JumpYearlyStatementEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.JumpYearlyStatementEmailOption.GetEmailType()
}

func (o *EmailOption_JumpYearlyStatementEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.JumpYearlyStatementEmailOption.GetOption().(type) {
	case *JumpYearlyStatementEmailOption_JumpYearlyStatementEmailOptionV1:
		return o.JumpYearlyStatementEmailOption.GetJumpYearlyStatementEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_JumpYearlyStatementEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("jump yearly statement email option is nil")
	}
	switch o.JumpYearlyStatementEmailOption.GetOption().(type) {
	case *JumpYearlyStatementEmailOption_JumpYearlyStatementEmailOptionV1:
		options := o.JumpYearlyStatementEmailOption.GetJumpYearlyStatementEmailOptionV1()
		body := strings.Replace(templateBody, "{#financialYear#}", options.GetFinancialYearStr(), 1)
		body = strings.Replace(body, "{#firstName#}", options.GetFirstName(), 1)
		return body, nil
	}
	return "", errors.New("no valid version found for Jump Yearly Statement Email Option template")
}

func (o *EmailOption_JumpYearlyStatementEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for Jump Yearly Statement Email Option is nil")
	}
	switch o.JumpYearlyStatementEmailOption.GetOption().(type) {
	case *JumpYearlyStatementEmailOption_JumpYearlyStatementEmailOptionV1:
		options := o.JumpYearlyStatementEmailOption.GetJumpYearlyStatementEmailOptionV1()
		subject = strings.Replace(subject, "{#financialYear#}", options.GetFinancialYearStr(), 1)
		return subject, nil
	}
	return "", errors.New("no valid version found for Jump Yearly Statement Email Option template")
}

func (o *EmailOption_MfExternalCapitalGainsEmail) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MfExternalCapitalGainsEmail.GetEmailType()
}

func (o *EmailOption_MfExternalCapitalGainsEmail) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfExternalCapitalGainsEmail.GetOption().(type) {
	case *MfExternalCapitalGainsEmail_MfExternalCapitalGainsEmailV1:
		return o.MfExternalCapitalGainsEmail.GetMfExternalCapitalGainsEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_MfExternalCapitalGainsEmail) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("mf external capital gains email is nil")
	}
	switch o.MfExternalCapitalGainsEmail.GetOption().(type) {
	case *MfExternalCapitalGainsEmail_MfExternalCapitalGainsEmailV1:
		options := o.MfExternalCapitalGainsEmail.GetMfExternalCapitalGainsEmailV1()
		body := strings.ReplaceAll(templateBody, "{#financialYear#}", options.GetFinancialYearStr())
		body = strings.ReplaceAll(body, "{#firstName#}", options.GetFirstName())
		return body, nil
	}
	return "", errors.New("no valid version found for Mf External Capital Gains Email template")
}

func (o *EmailOption_MfExternalCapitalGainsEmail) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for mf external capital gains email is nil")
	}
	switch o.MfExternalCapitalGainsEmail.GetOption().(type) {
	case *MfExternalCapitalGainsEmail_MfExternalCapitalGainsEmailV1:
		options := o.MfExternalCapitalGainsEmail.GetMfExternalCapitalGainsEmailV1()
		subject = strings.ReplaceAll(subject, "{#financialYear#}", options.GetFinancialYearStr())
		return subject, nil
	}
	return "", errors.New("no valid version found for Mf External Capital Gains Email template")
}

func (o *EmailOption_RiskUnifiedLeaEmail) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskUnifiedLeaEmail.GetEmailType()
}

func (o *EmailOption_RiskUnifiedLeaEmail) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskUnifiedLeaEmail.GetOption().(type) {
	case *RiskUnifiedLeaEmail_RiskUnifiedLeaEmailV1:
		return o.RiskUnifiedLeaEmail.GetRiskUnifiedLeaEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskUnifiedLeaEmail) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("risk unified lea retry email option is nil")
	}
	switch o.RiskUnifiedLeaEmail.GetOption().(type) {
	case *RiskUnifiedLeaEmail_RiskUnifiedLeaEmailV1:
		options := o.RiskUnifiedLeaEmail.GetRiskUnifiedLeaEmailV1()
		templateBody = strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), 1)
		templateBody = strings.Replace(templateBody, "{#account_restrictions#}", options.GetAccountRestrictions(), 1)
		templateBody = strings.Replace(templateBody, "{#restriction_meaning#}", options.GetRestrictionMeaning(), 1)
		templateBody = strings.Replace(templateBody, "{#complaint_table_rows#}", options.GetComplaintTableRows(), 1)
		templateBody = strings.Replace(templateBody, "{#reminder_text#}", options.GetReminderText(), 1)
		return templateBody, nil
	}
	return "", errors.New("no valid version found for unified lea email template")
}

func (o *EmailOption_RiskUnifiedLeaEmail) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("unified lea email subject cannot be empty")
	}
	return subject, nil
}

func (o *EmailOption_RiskUnifiedLeaLayerInvestigationEmail) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskUnifiedLeaLayerInvestigationEmail.GetEmailType()
}

func (o *EmailOption_RiskUnifiedLeaLayerInvestigationEmail) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskUnifiedLeaLayerInvestigationEmail.GetOption().(type) {
	case *RiskUnifiedLEALayerInvestigationEmail_RiskUnifiedLeaLayerInvestigationEmailOptionV1:
		return o.RiskUnifiedLeaLayerInvestigationEmail.GetRiskUnifiedLeaLayerInvestigationEmailOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskUnifiedLeaLayerInvestigationEmail) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("risk unified lea retry email option is nil")
	}
	switch o.RiskUnifiedLeaLayerInvestigationEmail.GetOption().(type) {
	case *RiskUnifiedLEALayerInvestigationEmail_RiskUnifiedLeaLayerInvestigationEmailOptionV1:
		options := o.RiskUnifiedLeaLayerInvestigationEmail.GetRiskUnifiedLeaLayerInvestigationEmailOptionV1()
		templateBody = strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), 1)
		templateBody = strings.Replace(templateBody, "{#account_restrictions#}", options.GetAccountRestrictions(), 1)
		templateBody = strings.Replace(templateBody, "{#restriction_meaning#}", options.GetRestrictionMeaning(), 1)
		templateBody = strings.Replace(templateBody, "{#complaint_list#}", options.GetComplaintList(), 1)
		templateBody = strings.Replace(templateBody, "{#reminder_text#}", options.GetReminderText(), 1)
		return templateBody, nil
	}
	return "", errors.New("no valid version found for unified lea email template")
}

func (o *EmailOption_RiskUnifiedLeaLayerInvestigationEmail) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("unified lea email subject cannot be empty")
	}
	return subject, nil
}

func (o *EmailOption_StockguardianLoanApplicationEsignEmail) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.StockguardianLoanApplicationEsignEmail.GetEmailType()
}

func (o *EmailOption_StockguardianLoanApplicationEsignEmail) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.StockguardianLoanApplicationEsignEmail.GetOption().(type) {
	case *StockguardianLoanApplicationEsignEmail_StockguardianLoanApplicationEsignEmailV1:
		return o.StockguardianLoanApplicationEsignEmail.GetStockguardianLoanApplicationEsignEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_StockguardianLoanApplicationEsignEmail) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("esign otp email option body object is nil")
	}
	switch o.StockguardianLoanApplicationEsignEmail.GetOption().(type) {
	case *StockguardianLoanApplicationEsignEmail_StockguardianLoanApplicationEsignEmailV1:
		msg := strings.Replace(templateBody, "{#otp#}", o.StockguardianLoanApplicationEsignEmail.GetStockguardianLoanApplicationEsignEmailV1().GetOtp(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for esign otp Email Option")
}

func (o *EmailOption_StockguardianLoanApplicationEsignEmail) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for esign email option is nil")
	}
	return subject, nil
}

func (o *EmailOption_LoanConfirmationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.LoanConfirmationEmailOption.GetEmailType()
}

func (o *EmailOption_LoanConfirmationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LoanConfirmationEmailOption.GetOption().(type) {
	case *LoanConfirmationEmailOption_LoansPaymentFileEmailV1:
		return o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint: staticcheck
func (o *EmailOption_LoanConfirmationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("loan confirmation email option body object is nil")
	}
	switch o.LoanConfirmationEmailOption.GetOption().(type) {
	case *LoanConfirmationEmailOption_LoansPaymentFileEmailV1:
		templateBody = strings.Replace(templateBody, "{#name#}", o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetName(), -1)
		templateBody = strings.Replace(templateBody, "{#contact_number#}", o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetContactNumber(), -1)
		templateBody = strings.Replace(templateBody, "{#last_four_digits_of_account_number#}", o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetLastFourDigitsOfAccountNumber(), -1)
		templateBody = strings.Replace(templateBody, "{#lsp_logo#}", o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetLspLogo(), -1)
		templateBody = strings.Replace(templateBody, "{#lsp_name#}", o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetLspName(), -1)
		return templateBody, nil
	}
	return "", fmt.Errorf("no valid version found for loan confirmation Email Option")
}

func (o *EmailOption_LoanConfirmationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("subject for loan confirmation email option is nil")
	}
	switch o.LoanConfirmationEmailOption.GetOption().(type) {
	case *LoanConfirmationEmailOption_LoansPaymentFileEmailV1:
		msg := strings.Replace(subject, "{#last_four_digits_of_account_number#}", o.LoanConfirmationEmailOption.GetLoansPaymentFileEmailV1().GetLastFourDigitsOfAccountNumber(), -1)
		return msg, nil
	}
	return subject, nil
}

func (o *EmailOption_RiskopsUserMediumRiskCf) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.RiskopsUserMediumRiskCf.GetEmailType()
}

func (o *EmailOption_RiskopsUserMediumRiskCf) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskopsUserMediumRiskCf.GetOption().(type) {
	case *RiskopsUserMediumRiskCF_RiskopsUserMediumRiskCfV1:
		return o.RiskopsUserMediumRiskCf.GetRiskopsUserMediumRiskCfV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_RiskopsUserMediumRiskCf) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("user medium risk credit freeze email retry email option is nil")
	}
	switch o.RiskopsUserMediumRiskCf.GetOption().(type) {
	case *RiskopsUserMediumRiskCF_RiskopsUserMediumRiskCfV1:
		options := o.RiskopsUserMediumRiskCf.GetRiskopsUserMediumRiskCfV1()
		templateBody = strings.Replace(templateBody, "{#first_name#}", options.GetFirstName(), 1)
		templateBody = strings.Replace(templateBody, "{#form_url#}", options.GetFormUrl(), 1)
		return templateBody, nil
	}
	return "", errors.New("no valid version found for user medium risk credit freeze email template")
}

func (o *EmailOption_RiskopsUserMediumRiskCf) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("user medium risk credit freeze email subject cannot be empty")
	}
	return subject, nil
}

func (o *EmailOption_TicketResolutionCsatEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.TicketResolutionCsatEmailOption.GetEmailType()
}

func (o *EmailOption_TicketResolutionCsatEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.TicketResolutionCsatEmailOption.GetOption().(type) {
	case *CxTicketResolutionCsatEmailOption_CxTicketResolutionCsatEmailOptionVersion1:
		return o.TicketResolutionCsatEmailOption.GetCxTicketResolutionCsatEmailOptionVersion1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_TicketResolutionCsatEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", errors.New("user medium risk credit freeze email retry email option is nil")
	}
	switch o.TicketResolutionCsatEmailOption.GetOption().(type) {
	case *CxTicketResolutionCsatEmailOption_CxTicketResolutionCsatEmailOptionVersion1:
		options := o.TicketResolutionCsatEmailOption.GetCxTicketResolutionCsatEmailOptionVersion1()
		templateBody = strings.ReplaceAll(templateBody, "{#first_name#}", options.GetFirstName())
		templateBody = strings.ReplaceAll(templateBody, "{#csat_form_link#}", options.GetCsatLink())
		templateBody = strings.ReplaceAll(templateBody, "{#ticket_id#}", options.GetTicketId())
		return templateBody, nil
	}
	return "", errors.New("no valid version found for sending csat to customer")
}

func (o *EmailOption_TicketResolutionCsatEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", errors.New("csat resolution email template subject not found")
	}
	switch o.TicketResolutionCsatEmailOption.GetOption().(type) {
	case *CxTicketResolutionCsatEmailOption_CxTicketResolutionCsatEmailOptionVersion1:
		options := o.TicketResolutionCsatEmailOption.GetCxTicketResolutionCsatEmailOptionVersion1()
		subject = strings.ReplaceAll(subject, "{#name#}", options.GetFirstName())
		return subject, nil
	}
	return subject, nil
}
