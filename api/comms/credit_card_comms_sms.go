// nolint
package comms

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	types "github.com/epifi/gamma/api/typesv2"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

// TRANSACTION CROSS BORDER SUCCESSFUL

func (o *SmsOption_CreditCardCrossBorderTransactionSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardCrossBorderTransactionSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardCrossBorderTransactionSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardCrossBorderTransactionSuccessSmsOption.GetOption().(type) {
	case *CreditCardCrossBorderTransactionSuccessSmsOption_CreditCardCrossBorderTransactionSuccessSmsOptionV1:
		return o.CreditCardCrossBorderTransactionSuccessSmsOption.GetCreditCardCrossBorderTransactionSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardCrossBorderTransactionSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card cross border txn success option is nil")
	}
	switch o.CreditCardCrossBorderTransactionSuccessSmsOption.GetOption().(type) {
	case *CreditCardCrossBorderTransactionSuccessSmsOption_CreditCardCrossBorderTransactionSuccessSmsOptionV1:
		amount := o.CreditCardCrossBorderTransactionSuccessSmsOption.GetCreditCardCrossBorderTransactionSuccessSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardCrossBorderTransactionSuccessSmsOption.GetCreditCardCrossBorderTransactionSuccessSmsOptionV1().GetBeneficiaryName()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		markupFee := o.CreditCardCrossBorderTransactionSuccessSmsOption.GetCreditCardCrossBorderTransactionSuccessSmsOptionV1().GetMarkupFee()
		markupFeeStr := moneyPkg.ToDisplayStringWithINRSymbol(markupFee)
		msg = ReplaceWithTrim(msg, "{#mark_up_fee#}", markupFeeStr, 1)
		currencyExchangeRate := o.CreditCardCrossBorderTransactionSuccessSmsOption.GetCreditCardCrossBorderTransactionSuccessSmsOptionV1().GetCurrencyExchangeRate()
		currencyExchangeRateStr := strconv.FormatFloat(float64(currencyExchangeRate), 'f', 3, 64)
		msg = ReplaceWithTrim(msg, "{#currency_exchange_rate#}", currencyExchangeRateStr, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card cross border txn success template")
}

// ATM TXN FAILURE

func (o *SmsOption_CreditCardAtmTransactionFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardAtmTransactionFailureSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardAtmTransactionFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardAtmTransactionFailureSmsOption.GetOption().(type) {
	case *CreditCardAtmTransactionFailureSmsOption_CreditCardAtmTransactionFailureSmsOptionV1:
		return o.CreditCardAtmTransactionFailureSmsOption.GetCreditCardAtmTransactionFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardAtmTransactionFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card atm txn failure option is nil")
	}
	switch o.CreditCardAtmTransactionFailureSmsOption.GetOption().(type) {
	case *CreditCardAtmTransactionFailureSmsOption_CreditCardAtmTransactionFailureSmsOptionV1:
		amount := o.CreditCardAtmTransactionFailureSmsOption.GetCreditCardAtmTransactionFailureSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card atm txn failure template")
}

// ATM TXN SUCCESS

func (o *SmsOption_CreditCardAtmTransactionSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardAtmTransactionSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardAtmTransactionSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardAtmTransactionSuccessSmsOption.GetOption().(type) {
	case *CreditCardAtmTransactionSuccessSmsOption_CreditCardAtmTransactionSuccessSmsOptionV1:
		return o.CreditCardAtmTransactionSuccessSmsOption.GetCreditCardAtmTransactionSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardAtmTransactionSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card atm txn success option is nil")
	}
	switch o.CreditCardAtmTransactionSuccessSmsOption.GetOption().(type) {
	case *CreditCardAtmTransactionSuccessSmsOption_CreditCardAtmTransactionSuccessSmsOptionV1:
		amount := o.CreditCardAtmTransactionSuccessSmsOption.GetCreditCardAtmTransactionSuccessSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		availableBalance := o.CreditCardAtmTransactionSuccessSmsOption.GetCreditCardAtmTransactionSuccessSmsOptionV1().GetAvailableAccountBalance()
		availableBalanceStr := moneyPkg.ToDisplayStringWithINRSymbol(availableBalance)
		msg = ReplaceWithTrim(msg, "{#available_account_balance#}", availableBalanceStr, 1)
		msg = ReplaceWithTrim(msg, "{#first_name#}", o.CreditCardAtmTransactionSuccessSmsOption.GetCreditCardAtmTransactionSuccessSmsOptionV1().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardAtmTransactionSuccessSmsOption.GetCreditCardAtmTransactionSuccessSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card atm txn success template")
}

// FAILED TXN REVERSAL SUCCESS

func (o *SmsOption_CreditCardFailedTransactionReversalSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardFailedTransactionReversalSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardFailedTransactionReversalSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardFailedTransactionReversalSuccessSmsOption.GetOption().(type) {
	case *CreditCardFailedTransactionReversalSuccessSmsOption_CreditCardFailedTransactionReversalSuccessSmsOptionV1:
		return o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV1().GetTemplateVersion()
	case *CreditCardFailedTransactionReversalSuccessSmsOption_CreditCardFailedTransactionReversalSuccessSmsOptionV2:
		return o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardFailedTransactionReversalSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card failed txn reversal success option is nil")
	}
	switch o.CreditCardFailedTransactionReversalSuccessSmsOption.GetOption().(type) {
	case *CreditCardFailedTransactionReversalSuccessSmsOption_CreditCardFailedTransactionReversalSuccessSmsOptionV1:
		amount := o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		availableBalance := o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV1().GetAvailableAccountBalance()
		availableBalanceStr := moneyPkg.ToDisplayStringWithINRSymbol(availableBalance)
		msg = ReplaceWithTrim(msg, "{#available_account_balance#}", availableBalanceStr, 1)
		msg = ReplaceWithTrim(msg, "{#account_number#}", o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV1().GetAccountNumber(), 1)
		reversalDate := o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV1().GetReversalDate()
		reversalDateString := datetime.DateToString(reversalDate, "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#reversal_date#}", reversalDateString, 1)
		return msg, nil
	case *CreditCardFailedTransactionReversalSuccessSmsOption_CreditCardFailedTransactionReversalSuccessSmsOptionV2:
		amount := o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV2().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		availableBalance := o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV2().GetAvailableAccountBalance()
		availableBalanceStr := moneyPkg.ToDisplayStringWithINRSymbol(availableBalance)
		msg = ReplaceWithTrim(msg, "{#available_account_balance#}", availableBalanceStr, 1)
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV2().GetCcEndingDigits(), 1)
		reversalDate := o.CreditCardFailedTransactionReversalSuccessSmsOption.GetCreditCardFailedTransactionReversalSuccessSmsOptionV2().GetReversalDate()
		reversalDateString := datetime.DateToString(reversalDate, "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#reversal_date#}", reversalDateString, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card failed txn reversal success  template")
}

// TXN DECLINED

func (o *SmsOption_CreditCardTransactionDeclinedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardTransactionDeclinedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardTransactionDeclinedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardTransactionDeclinedSmsOption.GetOption().(type) {
	case *CreditCardTransactionDeclinedSmsOption_CreditCardTransactionDeclinedSmsOptionV1:
		return o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV1().GetTemplateVersion()
	case *CreditCardTransactionDeclinedSmsOption_CreditCardTransactionDeclinedSmsOptionV2:
		return o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardTransactionDeclinedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card txn declined option is nil")
	}
	switch o.CreditCardTransactionDeclinedSmsOption.GetOption().(type) {
	case *CreditCardTransactionDeclinedSmsOption_CreditCardTransactionDeclinedSmsOptionV1:
		amount := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV1().GetBeneficiary()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		txnDate := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV1().GetTxnDate()
		txnDateString := datetime.DateToString(txnDate, "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDateString, 1)
		return msg, nil
	case *CreditCardTransactionDeclinedSmsOption_CreditCardTransactionDeclinedSmsOptionV2:
		amount := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV2().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV2().GetBeneficiary()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		txnDate := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV2().GetTxnDate()
		txnDateString := datetime.DateToString(txnDate, "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDateString, 1)
		ccEndingDigits := o.CreditCardTransactionDeclinedSmsOption.GetCreditCardTransactionDeclinedSmsOptionV2().GetCcEndingDigits()
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", ccEndingDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card txn declined template")
}

// TXN SUCCESS

func (o *SmsOption_CreditCardTransactionSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardTransactionSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardTransactionSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardTransactionSuccessSmsOption.GetOption().(type) {
	case *CreditCardTransactionSuccessSmsOption_CreditCardTransactionSuccessSmsOptionV1:
		return o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV1().GetTemplateVersion()
	case *CreditCardTransactionSuccessSmsOption_CreditCardTransactionSuccessSmsOptionV2:
		return o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardTransactionSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card txn success option is nil")
	}
	switch o.CreditCardTransactionSuccessSmsOption.GetOption().(type) {
	case *CreditCardTransactionSuccessSmsOption_CreditCardTransactionSuccessSmsOptionV1:
		amount := o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV1().GetBeneficiary()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		txnDate := o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV1().GetTxnDate()
		txnDateString := datetime.DateToString(txnDate, "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDateString, 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	case *CreditCardTransactionSuccessSmsOption_CreditCardTransactionSuccessSmsOptionV2:
		amount := o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV2().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV2().GetBeneficiary()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		txnDate, txnTime := getTxnDateAndTime(o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV2().GetTxnTimestamp())
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDate, 1)
		msg = ReplaceWithTrim(msg, "{#txn_time#}", txnTime, 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV2().GetHelplineNumber(), 1)
		ccEndingDigits := o.CreditCardTransactionSuccessSmsOption.GetCreditCardTransactionSuccessSmsOptionV2().GetCcEndingDigits()
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", ccEndingDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card txn success  template")
}

// INTERNATIONAL TRANSACTIONS DISABLED

func (o *SmsOption_CreditCardInternationalTransactionsDisabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardInternationalTransactionsDisabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardInternationalTransactionsDisabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardInternationalTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardInternationalTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardInternationalTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardInternationalTransactionsDisabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card disable international txn is nil")
	}
	switch o.CreditCardInternationalTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardInternationalTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardInternationalTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		cardType := o.CreditCardInternationalTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardInternationalTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card disable international txn template")
}

// INTERNATIONAL TRANSACTIONS ENABLED

func (o *SmsOption_CreditCardInternationalTransactionsEnabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardInternationalTransactionsEnabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardInternationalTransactionsEnabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardInternationalTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardInternationalTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardInternationalTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardInternationalTransactionsEnabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card enable international txn is nil")
	}
	switch o.CreditCardInternationalTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardInternationalTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardInternationalTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardInternationalTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardInternationalTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card enable international txn")
}

// POS TRANSACTIONS DISABLED

func (o *SmsOption_CreditCardPosTransactionsDisabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPosTransactionsDisabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPosTransactionsDisabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPosTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardPosTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardPosTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPosTransactionsDisabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card disable pos txn option is nil")
	}
	switch o.CreditCardPosTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardPosTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardPosTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardPosTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardPosTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card pos txns disable template")
}

// POS TRANSACTIONS ENABLED

func (o *SmsOption_CreditCardPosTransactionsEnabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPosTransactionsEnabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPosTransactionsEnabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPosTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardPosTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardPosTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPosTransactionsEnabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card enable pos txn option is nil")
	}
	switch o.CreditCardPosTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardPosTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardPosTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardPosTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardPosTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card pos txns enabled template")
}

// CONTACTLESS TRANSACTIONS DISABLED

func (o *SmsOption_CreditCardContactlessTransactionsDisabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardContactlessTransactionsDisabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardContactlessTransactionsDisabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardContactlessTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardContactlessTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardContactlessTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardContactlessTransactionsDisabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card disable contactless txn option is nil")
	}
	switch o.CreditCardContactlessTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardContactlessTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardContactlessTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardContactlessTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardContactlessTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card contactless txns disabled template")
}

// CONTACTLESS TRANSACTIONS ENABLED

func (o *SmsOption_CreditCardContactlessTransactionsEnabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardContactlessTransactionsEnabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardContactlessTransactionsEnabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardContactlessTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardContactlessTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardContactlessTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardContactlessTransactionsEnabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card enable contactless txn option is nil")
	}
	switch o.CreditCardContactlessTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardContactlessTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardContactlessTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardContactlessTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardContactlessTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card contactless txns enabled template")
}

// ONLINE TRANSACTIONS DISABLED

func (o *SmsOption_CreditCardOnlineTransactionsDisabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardOnlineTransactionsDisabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardOnlineTransactionsDisabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardOnlineTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardOnlineTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardOnlineTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardOnlineTransactionsDisabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card disable online txn option is nil")
	}
	switch o.CreditCardOnlineTransactionsDisabledSmsOption.GetOption().(type) {
	case *CreditCardOnlineTransactionsDisabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardOnlineTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardOnlineTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardOnlineTransactionsDisabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card online txns disable template")
}

// ONLINE TRANSACTIONS ENABLED

func (o *SmsOption_CreditCardOnlineTransactionsEnabledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardOnlineTransactionsEnabledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardOnlineTransactionsEnabledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardOnlineTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardOnlineTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardOnlineTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardOnlineTransactionsEnabledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card enable online txn option is nil")
	}
	switch o.CreditCardOnlineTransactionsEnabledSmsOption.GetOption().(type) {
	case *CreditCardOnlineTransactionsEnabledSmsOption_CreditCardControlsSmsOptionV1:
		firstName := o.CreditCardOnlineTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		cardType := o.CreditCardOnlineTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType()
		msg = ReplaceWithTrim(msg, "{#card_type#}", cardType, 1)
		lastFourDigits := o.CreditCardOnlineTransactionsEnabledSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits()
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", lastFourDigits, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card online txns enabled template")
}

// PIN TRIES EXCEEDED FOR TRANSACTIONS

func (o *SmsOption_CreditCardPinTriesExceededForTransactionsSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPinTriesExceededForTransactionsSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPinTriesExceededForTransactionsSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPinTriesExceededForTransactionsSmsOption.GetOption().(type) {
	case *CreditCardPinTriesExceededForTransactionsSmsOption_CreditCardPinTriesExceededForTransactionsSmsOptionV1:
		return o.CreditCardPinTriesExceededForTransactionsSmsOption.GetCreditCardPinTriesExceededForTransactionsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPinTriesExceededForTransactionsSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card pin tries exceeded for transactions option is nil")
	}
	switch o.CreditCardPinTriesExceededForTransactionsSmsOption.GetOption().(type) {
	case *CreditCardPinTriesExceededForTransactionsSmsOption_CreditCardPinTriesExceededForTransactionsSmsOptionV1:
		lastFourDigits := o.CreditCardPinTriesExceededForTransactionsSmsOption.GetCreditCardPinTriesExceededForTransactionsSmsOptionV1().GetCardLastFourDigits()
		msg := ReplaceWithTrim(template, "{#last_four_digits#}", lastFourDigits, 1)
		helplineNumber := o.CreditCardPinTriesExceededForTransactionsSmsOption.GetCreditCardPinTriesExceededForTransactionsSmsOptionV1().GetHelplineNumber()
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", helplineNumber, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card pin tries exceeded for transactions template")
}

// PAYMENT NOT DONE REMINDER WITH INTEREST CHARGE

func (o *SmsOption_CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption.GetOption().(type) {
	case *CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption_CreditCardPaymentNotDoneReminderWithInterestChargeSmsOptionV1:
		return o.CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption.GetCreditCardPaymentNotDoneReminderWithInterestChargeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card payment not done reminder with interest charge option is nil")
	}
	switch o.CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption.GetOption().(type) {
	case *CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption_CreditCardPaymentNotDoneReminderWithInterestChargeSmsOptionV1:
		creditCardType := o.CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption.GetCreditCardPaymentNotDoneReminderWithInterestChargeSmsOptionV1().GetCreditCardType()
		msg := ReplaceWithTrim(template, "{#credit_card_type#}", creditCardType, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card payment not done reminder with interest charge template")
}

// PAYMENT NOT DONE REMINDER

func (o *SmsOption_CreditCardPaymentNotDoneReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPaymentNotDoneReminderSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPaymentNotDoneReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPaymentNotDoneReminderSmsOption.GetOption().(type) {
	case *CreditCardPaymentNotDoneReminderSmsOption_CreditCardPaymentNotDoneReminderSmsOptionV1:
		return o.CreditCardPaymentNotDoneReminderSmsOption.GetCreditCardPaymentNotDoneReminderSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPaymentNotDoneReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card payment not done reminder option is nil")
	}
	switch o.CreditCardPaymentNotDoneReminderSmsOption.GetOption().(type) {
	case *CreditCardPaymentNotDoneReminderSmsOption_CreditCardPaymentNotDoneReminderSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardPaymentNotDoneReminderSmsOption.GetCreditCardPaymentNotDoneReminderSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#masked_card_number#}", o.CreditCardPaymentNotDoneReminderSmsOption.GetCreditCardPaymentNotDoneReminderSmsOptionV1().GetMaskedCardNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card payment not done reminder template")
}

// BILL SUCCESSFUL REPAYMENT

func (o *SmsOption_CreditCardBillSuccessfulRepaymentSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardBillSuccessfulRepaymentSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardBillSuccessfulRepaymentSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardBillSuccessfulRepaymentSmsOption.GetOption().(type) {
	case *CreditCardBillSuccessfulRepaymentSmsOption_CreditCardBillSuccessfulRepaymentSmsOptionV1:
		return o.CreditCardBillSuccessfulRepaymentSmsOption.GetCreditCardBillSuccessfulRepaymentSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardBillSuccessfulRepaymentSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card bill successful repayment option is nil")
	}
	switch o.CreditCardBillSuccessfulRepaymentSmsOption.GetOption().(type) {
	case *CreditCardBillSuccessfulRepaymentSmsOption_CreditCardBillSuccessfulRepaymentSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardBillSuccessfulRepaymentSmsOption.GetCreditCardBillSuccessfulRepaymentSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#credit_card_type#}", o.CreditCardBillSuccessfulRepaymentSmsOption.GetCreditCardBillSuccessfulRepaymentSmsOptionV1().GetCreditCardType(), 1)
		msg = ReplaceWithTrim(msg, "{#masked_card_number#}", o.CreditCardBillSuccessfulRepaymentSmsOption.GetCreditCardBillSuccessfulRepaymentSmsOptionV1().GetMaskedCardNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card bill successful repayment template")
}

// BILL REPAYMENT DUE

func (o *SmsOption_CreditCardBillRepaymentDueSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardBillRepaymentDueSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardBillRepaymentDueSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardBillRepaymentDueSmsOption.GetOption().(type) {
	case *CreditCardBillRepaymentDueSmsOption_CreditCardBillRepaymentDueSmsOptionV1:
		return o.CreditCardBillRepaymentDueSmsOption.GetCreditCardBillRepaymentDueSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardBillRepaymentDueSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card bill repayment due option is nil")
	}
	switch o.CreditCardBillRepaymentDueSmsOption.GetOption().(type) {
	case *CreditCardBillRepaymentDueSmsOption_CreditCardBillRepaymentDueSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardBillRepaymentDueSmsOption.GetCreditCardBillRepaymentDueSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#day#}", o.CreditCardBillRepaymentDueSmsOption.GetCreditCardBillRepaymentDueSmsOptionV1().GetDaysLeft(), 1)
		msg = ReplaceWithTrim(msg, "{#link#}", o.CreditCardBillRepaymentDueSmsOption.GetCreditCardBillRepaymentDueSmsOptionV1().GetPaymentLink(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card bill repayment due template")
}

// STATEMENT GENERATION

func (o *SmsOption_CreditCardStatementGenerationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardStatementGenerationSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardStatementGenerationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardStatementGenerationSmsOption.GetOption().(type) {
	case *CreditCardStatementGenerationSmsOption_CreditCardStatementGenerationSmsOptionV1:
		return o.CreditCardStatementGenerationSmsOption.GetCreditCardStatementGenerationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardStatementGenerationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card statement generation option is nil")
	}
	switch o.CreditCardStatementGenerationSmsOption.GetOption().(type) {
	case *CreditCardStatementGenerationSmsOption_CreditCardStatementGenerationSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardStatementGenerationSmsOption.GetCreditCardStatementGenerationSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#card_number#}", o.CreditCardStatementGenerationSmsOption.GetCreditCardStatementGenerationSmsOptionV1().GetMaskedCardNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card statement generation template")
}

// COMMUNICATE TRANSACTION CHARGES

func (o *SmsOption_CreditCardCommunicateTransactionChargesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardCommunicateTransactionChargesSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardCommunicateTransactionChargesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardCommunicateTransactionChargesSmsOption.GetOption().(type) {
	case *CreditCardCommunicateTransactionChargesSmsOption_CreditCardCommunicateTransactionChargesSmsOptionV1:
		return o.CreditCardCommunicateTransactionChargesSmsOption.GetCreditCardCommunicateTransactionChargesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardCommunicateTransactionChargesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card communicate transaction charges option is nil")
	}
	switch o.CreditCardCommunicateTransactionChargesSmsOption.GetOption().(type) {
	case *CreditCardCommunicateTransactionChargesSmsOption_CreditCardCommunicateTransactionChargesSmsOptionV1:
		fee := moneyPkg.ToDisplayStringWithINRSymbol(o.CreditCardCommunicateTransactionChargesSmsOption.GetCreditCardCommunicateTransactionChargesSmsOptionV1().GetWithdrawnAmount())
		msg := ReplaceWithTrim(template, "{#fee#}", fee, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card communicate transaction charges template")
}

// REPLACEMENT

func (o *SmsOption_CreditCardReplacementSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardReplacementSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardReplacementSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardReplacementSmsOption.GetOption().(type) {
	case *CreditCardReplacementSmsOption_CreditCardReplacementSmsOptionV1:
		return o.CreditCardReplacementSmsOption.GetCreditCardReplacementSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardReplacementSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card replacement option is nil")
	}
	switch o.CreditCardReplacementSmsOption.GetOption().(type) {
	case *CreditCardReplacementSmsOption_CreditCardReplacementSmsOptionV1:
		return template, nil
	}
	return "", errors.New("no valid version found for credit card replacement template")
}

// LIMIT CHANGE FAILURE

func (o *SmsOption_CreditCardLimitChangeFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardLimitChangeFailureSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardLimitChangeFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardLimitChangeFailureSmsOption.GetOption().(type) {
	case *CreditCardLimitChangeFailureSmsOption_CreditCardLimitChangeFailureSmsOptionV1:
		return o.CreditCardLimitChangeFailureSmsOption.GetCreditCardLimitChangeFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardLimitChangeFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card limit change failure option is nil")
	}
	switch o.CreditCardLimitChangeFailureSmsOption.GetOption().(type) {
	case *CreditCardLimitChangeFailureSmsOption_CreditCardLimitChangeFailureSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardLimitChangeFailureSmsOption.GetCreditCardLimitChangeFailureSmsOptionV1().GetFirstName().GetFirstName(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card limit change failure template")
}

// CARD USAGE CHANGE FAILURE

func (o *SmsOption_CreditCardCardUsageChangeFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardCardUsageChangeFailureSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardCardUsageChangeFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardCardUsageChangeFailureSmsOption.GetOption().(type) {
	case *CreditCardCardUsageChangeFailureSmsOption_CreditCardCardUsageChangeFailureSmsOptionV1:
		return o.CreditCardCardUsageChangeFailureSmsOption.GetCreditCardCardUsageChangeFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardCardUsageChangeFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card card usage change failure option is nil")
	}
	switch o.CreditCardCardUsageChangeFailureSmsOption.GetOption().(type) {
	case *CreditCardCardUsageChangeFailureSmsOption_CreditCardCardUsageChangeFailureSmsOptionV1:
		return template, nil
	}
	return "", errors.New("no valid version found for credit card card usage change failure template")
}

// CONTACTLESS PURCHASE LIMIT CHANGED

func (o *SmsOption_CreditCardContactlessPurchaseLimitChangedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardContactlessPurchaseLimitChangedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardContactlessPurchaseLimitChangedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardContactlessPurchaseLimitChangedSmsOption.GetOption().(type) {
	case *CreditCardContactlessPurchaseLimitChangedSmsOption_CreditCardContactlessPurchaseLimitChangedSmsOptionV1:
		return o.CreditCardContactlessPurchaseLimitChangedSmsOption.GetCreditCardContactlessPurchaseLimitChangedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardContactlessPurchaseLimitChangedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card contactless purchase limit changed option is nil")
	}
	switch o.CreditCardContactlessPurchaseLimitChangedSmsOption.GetOption().(type) {
	case *CreditCardContactlessPurchaseLimitChangedSmsOption_CreditCardContactlessPurchaseLimitChangedSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardContactlessPurchaseLimitChangedSmsOption.GetCreditCardContactlessPurchaseLimitChangedSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardContactlessPurchaseLimitChangedSmsOption.GetCreditCardContactlessPurchaseLimitChangedSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card contactless purchase limit changed template")
}

// ONLINE PURCHASE LIMIT CHANGED

func (o *SmsOption_CreditCardOnlinePurchaseLimitChangedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardOnlinePurchaseLimitChangedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardOnlinePurchaseLimitChangedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardOnlinePurchaseLimitChangedSmsOption.GetOption().(type) {
	case *CreditCardOnlinePurchaseLimitChangedSmsOption_CreditCardOnlinePurchaseLimitChangedSmsOptionV1:
		return o.CreditCardOnlinePurchaseLimitChangedSmsOption.GetCreditCardOnlinePurchaseLimitChangedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardOnlinePurchaseLimitChangedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card online purchase limit changed option is nil")
	}
	switch o.CreditCardOnlinePurchaseLimitChangedSmsOption.GetOption().(type) {
	case *CreditCardOnlinePurchaseLimitChangedSmsOption_CreditCardOnlinePurchaseLimitChangedSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardOnlinePurchaseLimitChangedSmsOption.GetCreditCardOnlinePurchaseLimitChangedSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardOnlinePurchaseLimitChangedSmsOption.GetCreditCardOnlinePurchaseLimitChangedSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card online purchase limit changed template")
}

// POS PURCHASE LIMIT CHANGED

func (o *SmsOption_CreditCardPosPurchaseLimitChangedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPosPurchaseLimitChangedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPosPurchaseLimitChangedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPosPurchaseLimitChangedSmsOption.GetOption().(type) {
	case *CreditCardPosPurchaseLimitChangedSmsOption_CreditCardPosPurchaseLimitChangedSmsOptionV1:
		return o.CreditCardPosPurchaseLimitChangedSmsOption.GetCreditCardPosPurchaseLimitChangedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPosPurchaseLimitChangedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card pos purchase limit changed option is nil")
	}
	switch o.CreditCardPosPurchaseLimitChangedSmsOption.GetOption().(type) {
	case *CreditCardPosPurchaseLimitChangedSmsOption_CreditCardPosPurchaseLimitChangedSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#helpline_number#}", o.CreditCardPosPurchaseLimitChangedSmsOption.GetCreditCardPosPurchaseLimitChangedSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card pos purchase limit changed template")
}

// UNFREEZING FAILURE

func (o *SmsOption_CreditCardUnfreezingFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardUnfreezingFailureSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardUnfreezingFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardUnfreezingFailureSmsOption.GetOption().(type) {
	case *CreditCardUnfreezingFailureSmsOption_CreditCardControlsWithHelplineSmsOptionV1:
		return o.CreditCardUnfreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardUnfreezingFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card unfreezing failure option is nil")
	}
	switch o.CreditCardUnfreezingFailureSmsOption.GetOption().(type) {
	case *CreditCardUnfreezingFailureSmsOption_CreditCardControlsWithHelplineSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardUnfreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetCustomerFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#card_type#}", o.CreditCardUnfreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetCreditCardType(), 1)
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", o.CreditCardUnfreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetLastFourDigits(), 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardUnfreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card unfreezing failure template")
}

// FREEZING FAILURE

func (o *SmsOption_CreditCardFreezingFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardFreezingFailureSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardFreezingFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardFreezingFailureSmsOption.GetOption().(type) {
	case *CreditCardFreezingFailureSmsOption_CreditCardControlsWithHelplineSmsOptionV1:
		return o.CreditCardFreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardFreezingFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card freezing failure option is nil")
	}
	switch o.CreditCardFreezingFailureSmsOption.GetOption().(type) {
	case *CreditCardFreezingFailureSmsOption_CreditCardControlsWithHelplineSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardFreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetCustomerFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#card_type#}", o.CreditCardFreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetCreditCardType(), 1)
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", o.CreditCardFreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetLastFourDigits(), 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardFreezingFailureSmsOption.GetCreditCardControlsWithHelplineSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card freezing failure template")
}

// UNFREEZING SUCCESS

func (o *SmsOption_CreditCardUnfreezingSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardUnfreezingSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardUnfreezingSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardUnfreezingSuccessSmsOption.GetOption().(type) {
	case *CreditCardUnfreezingSuccessSmsOption_CreditCardControlsSmsOptionV1:
		return o.CreditCardUnfreezingSuccessSmsOption.GetCreditCardControlsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardUnfreezingSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card unfreezing success option is nil")
	}
	switch o.CreditCardUnfreezingSuccessSmsOption.GetOption().(type) {
	case *CreditCardUnfreezingSuccessSmsOption_CreditCardControlsSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardUnfreezingSuccessSmsOption.GetCreditCardControlsSmsOptionV1().GetCustomerFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#card_type#}", o.CreditCardUnfreezingSuccessSmsOption.GetCreditCardControlsSmsOptionV1().GetCreditCardType(), 1)
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", o.CreditCardUnfreezingSuccessSmsOption.GetCreditCardControlsSmsOptionV1().GetLastFourDigits(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card unfreezing success template")
}

// FREEZING SUCCESS

func (o *SmsOption_CreditCardFreezingSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardFreezingSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardFreezingSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardFreezingSuccessSmsOption.GetOption().(type) {
	case *CreditCardFreezingSuccessSmsOption_CreditCardFreezingSuccessSmsOptionV1:
		return o.CreditCardFreezingSuccessSmsOption.GetCreditCardFreezingSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardFreezingSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card freezing success option is nil")
	}
	switch o.CreditCardFreezingSuccessSmsOption.GetOption().(type) {
	case *CreditCardFreezingSuccessSmsOption_CreditCardFreezingSuccessSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#card_type#}", o.CreditCardFreezingSuccessSmsOption.GetCreditCardFreezingSuccessSmsOptionV1().GetCreditCardType(), 1)
		msg = ReplaceWithTrim(msg, "{#last_four_digits#}", o.CreditCardFreezingSuccessSmsOption.GetCreditCardFreezingSuccessSmsOptionV1().GetLastFourDigits(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card freezing success template")
}

// PIN CHANGE SUCCESS

func (o *SmsOption_CreditCardPinChangeSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPinChangeSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPinChangeSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPinChangeSuccessSmsOption.GetOption().(type) {
	case *CreditCardPinChangeSuccessSmsOption_CreditCardPinChangeSuccessSmsOptionV1:
		return o.CreditCardPinChangeSuccessSmsOption.GetCreditCardPinChangeSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPinChangeSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card pin change success option is nil")
	}
	switch o.CreditCardPinChangeSuccessSmsOption.GetOption().(type) {
	case *CreditCardPinChangeSuccessSmsOption_CreditCardPinChangeSuccessSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardPinChangeSuccessSmsOption.GetCreditCardPinChangeSuccessSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#helpline_number#}", o.CreditCardPinChangeSuccessSmsOption.GetCreditCardPinChangeSuccessSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card pin change success template")
}

// ACTIVATION FAILURE

func (o *SmsOption_CreditCardActivationFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardActivationFailureSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardActivationFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardActivationFailureSmsOption.GetOption().(type) {
	case *CreditCardActivationFailureSmsOption_CreditCardActivationFailureSmsOptionV1:
		return o.CreditCardActivationFailureSmsOption.GetCreditCardActivationFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardActivationFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card activation failure option is nil")
	}
	switch o.CreditCardActivationFailureSmsOption.GetOption().(type) {
	case *CreditCardActivationFailureSmsOption_CreditCardActivationFailureSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#helpline_number#}", o.CreditCardActivationFailureSmsOption.GetCreditCardActivationFailureSmsOptionV1().GetHelplineNumber(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card activation failure template")
}

// PHYSICAL CARD ACTIVATION SUCCESS

func (o *SmsOption_CreditCardPhysicalCardActivationSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardPhysicalCardActivationSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardPhysicalCardActivationSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardPhysicalCardActivationSuccessSmsOption.GetOption().(type) {
	case *CreditCardPhysicalCardActivationSuccessSmsOption_CreditCardPhysicalCardActivationSuccessSmsOptionV1:
		return o.CreditCardPhysicalCardActivationSuccessSmsOption.GetCreditCardPhysicalCardActivationSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardPhysicalCardActivationSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card physical card activation success option is nil")
	}
	switch o.CreditCardPhysicalCardActivationSuccessSmsOption.GetOption().(type) {
	case *CreditCardPhysicalCardActivationSuccessSmsOption_CreditCardPhysicalCardActivationSuccessSmsOptionV1:
		return template, nil
	}
	return "", errors.New("no valid version found for credit card physical card activation success template")
}

// DIGITAL CARD ACTIVATION SUCCESS

func (o *SmsOption_CreditCardDigitalCardActivationSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardDigitalCardActivationSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardDigitalCardActivationSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardDigitalCardActivationSuccessSmsOption.GetOption().(type) {
	case *CreditCardDigitalCardActivationSuccessSmsOption_CreditCardDigitalCardActivationSuccessSmsOptionV1:
		return o.CreditCardDigitalCardActivationSuccessSmsOption.GetCreditCardDigitalCardActivationSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardDigitalCardActivationSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card digital card activation success option is nil")
	}
	switch o.CreditCardDigitalCardActivationSuccessSmsOption.GetOption().(type) {
	case *CreditCardDigitalCardActivationSuccessSmsOption_CreditCardDigitalCardActivationSuccessSmsOptionV1:
		return template, nil
	}
	return "", errors.New("no valid version found for credit card digital card activation success template")
}

// ACTIVATION INFORMATION

func (o *SmsOption_CreditCardActivationInformationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardActivationInformationSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardActivationInformationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardActivationInformationSmsOption.GetOption().(type) {
	case *CreditCardActivationInformationSmsOption_CreditCardActivationInformationSmsOptionV1:
		return o.CreditCardActivationInformationSmsOption.GetCreditCardActivationInformationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardActivationInformationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card activation information option is nil")
	}
	switch o.CreditCardActivationInformationSmsOption.GetOption().(type) {
	case *CreditCardActivationInformationSmsOption_CreditCardActivationInformationSmsOptionV1:
		return template, nil
	}
	return "", errors.New("no valid version found for credit card activation information template")
}

// SHIPMENT DELAY

func (o *SmsOption_CreditCardShipmentDelaySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardShipmentDelaySmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardShipmentDelaySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardShipmentDelaySmsOption.GetOption().(type) {
	case *CreditCardShipmentDelaySmsOption_CreditCardShipmentDelaySmsOptionV1:
		return o.CreditCardShipmentDelaySmsOption.GetCreditCardShipmentDelaySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardShipmentDelaySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card shipment delay option is nil")
	}
	switch o.CreditCardShipmentDelaySmsOption.GetOption().(type) {
	case *CreditCardShipmentDelaySmsOption_CreditCardShipmentDelaySmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardShipmentDelaySmsOption.GetCreditCardShipmentDelaySmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#tracking_id#}", o.CreditCardShipmentDelaySmsOption.GetCreditCardShipmentDelaySmsOptionV1().GetTrackingId(), 1)
		msg = ReplaceWithTrim(msg, "{#tracking_link#}", o.CreditCardShipmentDelaySmsOption.GetCreditCardShipmentDelaySmsOptionV1().GetTrackingLink(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card shipment delay template")
}

// DISPTACH DELAY

func (o *SmsOption_CreditCardDisptachDelaySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardDisptachDelaySmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardDisptachDelaySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardDisptachDelaySmsOption.GetOption().(type) {
	case *CreditCardDisptachDelaySmsOption_CreditCardDisptachDelaySmsOptionV1:
		return o.CreditCardDisptachDelaySmsOption.GetCreditCardDisptachDelaySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardDisptachDelaySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card disptach delay option is nil")
	}
	switch o.CreditCardDisptachDelaySmsOption.GetOption().(type) {
	case *CreditCardDisptachDelaySmsOption_CreditCardDisptachDelaySmsOptionV1:
		return template, nil
	}
	return "", errors.New("no valid version found for credit card disptach delay template")
}

// DISPTACHED WITH TRACKING NUMBER

func (o *SmsOption_CreditCardDisptachedWithTrackingNumberSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardDisptachedWithTrackingNumberSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardDisptachedWithTrackingNumberSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardDisptachedWithTrackingNumberSmsOption.GetOption().(type) {
	case *CreditCardDisptachedWithTrackingNumberSmsOption_CreditCardDisptachedWithTrackingNumberSmsOptionV1:
		return o.CreditCardDisptachedWithTrackingNumberSmsOption.GetCreditCardDisptachedWithTrackingNumberSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardDisptachedWithTrackingNumberSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card disptached with tracking number option is nil")
	}
	switch o.CreditCardDisptachedWithTrackingNumberSmsOption.GetOption().(type) {
	case *CreditCardDisptachedWithTrackingNumberSmsOption_CreditCardDisptachedWithTrackingNumberSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardDisptachedWithTrackingNumberSmsOption.GetCreditCardDisptachedWithTrackingNumberSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#card_type#}", o.CreditCardDisptachedWithTrackingNumberSmsOption.GetCreditCardDisptachedWithTrackingNumberSmsOptionV1().GetCreditCardType(), 1)
		msg = ReplaceWithTrim(msg, "{#tracking_name#}", o.CreditCardDisptachedWithTrackingNumberSmsOption.GetCreditCardDisptachedWithTrackingNumberSmsOptionV1().GetTrackingName(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card disptached with tracking number template")
}

// ISSUED WITH CREDIT LIMIT

func (o *SmsOption_CreditCardIssuedWithCreditLimitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardIssuedWithCreditLimitSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardIssuedWithCreditLimitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardIssuedWithCreditLimitSmsOption.GetOption().(type) {
	case *CreditCardIssuedWithCreditLimitSmsOption_CreditCardIssuedWithCreditLimitSmsOptionV1:
		return o.CreditCardIssuedWithCreditLimitSmsOption.GetCreditCardIssuedWithCreditLimitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardIssuedWithCreditLimitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card issued with credit limit option is nil")
	}
	switch o.CreditCardIssuedWithCreditLimitSmsOption.GetOption().(type) {
	case *CreditCardIssuedWithCreditLimitSmsOption_CreditCardIssuedWithCreditLimitSmsOptionV1:
		cardLimit := moneyPkg.ToDisplayStringInIndianFormatWithCurrencyCode(o.CreditCardIssuedWithCreditLimitSmsOption.GetCreditCardIssuedWithCreditLimitSmsOptionV1().GetCreditLimit(), 0)
		msg := ReplaceWithTrim(template, "{#card_limit#}", cardLimit, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card issued with credit limit template")
}

// ISSUED

func (o *SmsOption_CreditCardIssuedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardIssuedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardIssuedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardIssuedSmsOption.GetOption().(type) {
	case *CreditCardIssuedSmsOption_CreditCardIssuedSmsOptionV1:
		return o.CreditCardIssuedSmsOption.GetCreditCardIssuedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardIssuedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card issued option is nil")
	}
	switch o.CreditCardIssuedSmsOption.GetOption().(type) {
	case *CreditCardIssuedSmsOption_CreditCardIssuedSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#card_type#}", o.CreditCardIssuedSmsOption.GetCreditCardIssuedSmsOptionV1().GetCreditCardType(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card issued template")
}

// INCOMPLETE APPLICATION PROCESS

func (o *SmsOption_CreditCardIncompleteApplicationProcessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardIncompleteApplicationProcessSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardIncompleteApplicationProcessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardIncompleteApplicationProcessSmsOption.GetOption().(type) {
	case *CreditCardIncompleteApplicationProcessSmsOption_CreditCardIncompleteApplicationProcessSmsOptionV1:
		return o.CreditCardIncompleteApplicationProcessSmsOption.GetCreditCardIncompleteApplicationProcessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardIncompleteApplicationProcessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card incomplete application process option is nil")
	}
	switch o.CreditCardIncompleteApplicationProcessSmsOption.GetOption().(type) {
	case *CreditCardIncompleteApplicationProcessSmsOption_CreditCardIncompleteApplicationProcessSmsOptionV1:
		creditLimit := moneyPkg.ToDisplayStringWithINRSymbol(o.CreditCardIncompleteApplicationProcessSmsOption.GetCreditCardIncompleteApplicationProcessSmsOptionV1().GetCreditLimit())
		msg := ReplaceWithTrim(template, "{#credit_limit#}", creditLimit, 1)
		msg = ReplaceWithTrim(msg, "{#link#}", o.CreditCardIncompleteApplicationProcessSmsOption.GetCreditCardIncompleteApplicationProcessSmsOptionV1().GetCompleteApplicationLink(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card incomplete application process template")
}

// COMPLETE VIDEO KYC FOR CREDIT CARD

func (o *SmsOption_CreditCardCompleteVideoKycForCreditCardSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardCompleteVideoKycForCreditCardSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetOption().(type) {
	case *CreditCardCompleteVideoKycForCreditCardSmsOption_CreditCardCompleteVideoKycForCreditCardSmsOptionV1:
		return o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetCreditCardCompleteVideoKycForCreditCardSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardCompleteVideoKycForCreditCardSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card complete video kyc for credit card option is nil")
	}
	switch o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetOption().(type) {
	case *CreditCardCompleteVideoKycForCreditCardSmsOption_CreditCardCompleteVideoKycForCreditCardSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetCreditCardCompleteVideoKycForCreditCardSmsOptionV1().GetFirstName().GetFirstName(), 1)
		cardLimit := moneyPkg.ToDisplayStringWithINRSymbol(o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetCreditCardCompleteVideoKycForCreditCardSmsOptionV1().GetCreditLimit())
		msg = ReplaceWithTrim(msg, "{#card_limit#}", cardLimit, 1)
		msg = ReplaceWithTrim(msg, "{#link#}", o.CreditCardCompleteVideoKycForCreditCardSmsOption.GetCreditCardCompleteVideoKycForCreditCardSmsOptionV1().GetCompleteApplicationLink(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card complete video kyc for credit card template")
}

// OTP FOR CHANGING CARD PIN

func (o *SmsOption_CreditCardOtpForChangingCardPinSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardOtpForChangingCardPinSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardOtpForChangingCardPinSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardOtpForChangingCardPinSmsOption.GetOption().(type) {
	case *CreditCardOtpForChangingCardPinSmsOption_CreditCardOtpForChangingCardPinSmsOptionV1:
		return o.CreditCardOtpForChangingCardPinSmsOption.GetCreditCardOtpForChangingCardPinSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardOtpForChangingCardPinSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card otp for changing card pin option is nil")
	}
	switch o.CreditCardOtpForChangingCardPinSmsOption.GetOption().(type) {
	case *CreditCardOtpForChangingCardPinSmsOption_CreditCardOtpForChangingCardPinSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#otp#}", o.CreditCardOtpForChangingCardPinSmsOption.GetCreditCardOtpForChangingCardPinSmsOptionV1().GetOtp(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card otp for changing card pin template")
}

// REWARD POINTS CREDITED

func (o *SmsOption_CreditCardRewardPointsCreditedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardRewardPointsCreditedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardRewardPointsCreditedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardRewardPointsCreditedSmsOption.GetOption().(type) {
	case *CreditCardRewardPointsCreditedSmsOption_CreditCardRewardPointsCreditedSmsOptionV1:
		return o.CreditCardRewardPointsCreditedSmsOption.GetCreditCardRewardPointsCreditedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardRewardPointsCreditedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card reward points credited option is nil")
	}
	switch o.CreditCardRewardPointsCreditedSmsOption.GetOption().(type) {
	case *CreditCardRewardPointsCreditedSmsOption_CreditCardRewardPointsCreditedSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#name#}", o.CreditCardRewardPointsCreditedSmsOption.GetCreditCardRewardPointsCreditedSmsOptionV1().GetFirstName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#coins#}", o.CreditCardRewardPointsCreditedSmsOption.GetCreditCardRewardPointsCreditedSmsOptionV1().GetCoins(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card reward points credited template")
}

// REACHING THRESHOLD OF CREDIT LIMIT
func (o *SmsOption_CreditCardLimitReachingThresholdSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardLimitReachingThresholdSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardLimitReachingThresholdSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardLimitReachingThresholdSmsOption.GetOption().(type) {
	case *CreditCardLimitReachingThresholdSmsOption_CreditCardLimitReachingThresholdSmsOptionV1:
		return o.CreditCardLimitReachingThresholdSmsOption.GetCreditCardLimitReachingThresholdSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardLimitReachingThresholdSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card limit reaching threshold sms option is nil")
	}
	switch o.CreditCardLimitReachingThresholdSmsOption.GetOption().(type) {
	case *CreditCardLimitReachingThresholdSmsOption_CreditCardLimitReachingThresholdSmsOptionV1:
		thresholdPercentage := strconv.Itoa(int(o.CreditCardLimitReachingThresholdSmsOption.GetCreditCardLimitReachingThresholdSmsOptionV1().GetThresholdPercentage()))
		msg := ReplaceWithTrim(template, "{#threshold_percentage#}", thresholdPercentage, 1)
		msg = ReplaceWithTrim(msg, "{#available_limit#}", moneyPkg.ToDisplayStringWithINRSymbol(o.CreditCardLimitReachingThresholdSmsOption.GetCreditCardLimitReachingThresholdSmsOptionV1().GetAvailableLimit()), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card limit reaching threshold template")
}

// TXN DECLINED WITH REASON
func (o *SmsOption_CreditCardTransactionDeclinedWithReasonSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardTransactionDeclinedWithReasonSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardTransactionDeclinedWithReasonSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardTransactionDeclinedWithReasonSmsOption.GetOption().(type) {
	case *CreditCardTransactionDeclinedWithReasonSmsOption_CreditCardTransactionDeclinedWithReasonSmsOptionV1:
		return o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV1().GetTemplateVersion()
	case *CreditCardTransactionDeclinedWithReasonSmsOption_CreditCardTransactionDeclinedWithReasonSmsOptionV2:
		return o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetTemplateVersion()
	case *CreditCardTransactionDeclinedWithReasonSmsOption_CreditCardTransactionDeclinedWithReasonSmsOptionV3:
		return o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV3().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardTransactionDeclinedWithReasonSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card txn declined with reason option is nil")
	}
	switch o.CreditCardTransactionDeclinedWithReasonSmsOption.GetOption().(type) {
	case *CreditCardTransactionDeclinedWithReasonSmsOption_CreditCardTransactionDeclinedWithReasonSmsOptionV1:
		amount := o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV1().GetBeneficiary()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		txnDateString := datetime.DateToString(datetime.TimestampToDateInLoc(o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV1().GetTxnTime(), datetime.IST), "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDateString, 1)
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV1().GetCcEndingDigits(), 1)
		msg = ReplaceWithTrim(msg, "{#decline_reason#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV1().GetReason(), 1)
		return msg, nil
	case *CreditCardTransactionDeclinedWithReasonSmsOption_CreditCardTransactionDeclinedWithReasonSmsOptionV2:
		amount := o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		beneficiary := o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetBeneficiary()
		msg = ReplaceWithTrim(msg, "{#beneficiary#}", beneficiary, 1)
		txnDateString := datetime.DateToString(datetime.TimestampToDateInLoc(o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetTxnTime(), datetime.IST), "02 Jan 2006", datetime.IST)
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDateString, 1)
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetCcEndingDigits(), 1)
		msg = ReplaceWithTrim(msg, "{#decline_reason#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetReason(), 1)
		msg = ReplaceWithTrim(msg, "{#action_item#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV2().GetActionItem(), 1)
		return msg, nil
	case *CreditCardTransactionDeclinedWithReasonSmsOption_CreditCardTransactionDeclinedWithReasonSmsOptionV3:
		msg := ReplaceWithTrim(template, "{#txn_failure_reason#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV3().GetTxnFailureReason(), 1)
		msg = ReplaceWithTrim(msg, "{#txn_failure_fix#}", o.CreditCardTransactionDeclinedWithReasonSmsOption.GetCreditCardTransactionDeclinedWithReasonSmsOptionV3().GetTxnFailureFix(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card txn declined with reason template")
}

// CC JOINING FEE
func (o *SmsOption_CreditCardJoiningFeeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardJoiningFeeSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardJoiningFeeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardJoiningFeeSmsOption.GetOption().(type) {
	case *CreditCardJoiningFeeSmsOption_CreditCardJoiningFeeSmsOptionV1:
		return o.CreditCardJoiningFeeSmsOption.GetCreditCardJoiningFeeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardJoiningFeeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card joining fees option is nil")
	}
	switch o.CreditCardJoiningFeeSmsOption.GetOption().(type) {
	case *CreditCardJoiningFeeSmsOption_CreditCardJoiningFeeSmsOptionV1:
		amount := o.CreditCardJoiningFeeSmsOption.GetCreditCardJoiningFeeSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		txnDate, txnTime := getTxnDateAndTime(o.CreditCardJoiningFeeSmsOption.GetCreditCardJoiningFeeSmsOptionV1().GetTxnTime())
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDate, 1)
		msg = ReplaceWithTrim(msg, "{#txn_time#}", txnTime, 1)
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", o.CreditCardJoiningFeeSmsOption.GetCreditCardJoiningFeeSmsOptionV1().GetCcEndingDigits(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card joining fees template")
}

// CC unpaid due fees
func (o *SmsOption_CreditCardUnpaidDueFeesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardUnpaidDueFeesSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardUnpaidDueFeesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardUnpaidDueFeesSmsOption.GetOption().(type) {
	case *CreditCardUnpaidDueFeesSmsOption_CreditCardUnpaidDueFeesSmsOptionV1:
		return o.CreditCardUnpaidDueFeesSmsOption.GetCreditCardUnpaidDueFeesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardUnpaidDueFeesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card unpaid due fees option is nil")
	}
	switch o.CreditCardUnpaidDueFeesSmsOption.GetOption().(type) {
	case *CreditCardUnpaidDueFeesSmsOption_CreditCardUnpaidDueFeesSmsOptionV1:
		amount := o.CreditCardUnpaidDueFeesSmsOption.GetCreditCardUnpaidDueFeesSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		txnDate, txnTime := getTxnDateAndTime(o.CreditCardUnpaidDueFeesSmsOption.GetCreditCardUnpaidDueFeesSmsOptionV1().GetTxnTime())
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDate, 1)
		msg = ReplaceWithTrim(msg, "{#txn_time#}", txnTime, 1)
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", o.CreditCardUnpaidDueFeesSmsOption.GetCreditCardUnpaidDueFeesSmsOptionV1().GetCcEndingDigits(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card unpaid due fees template")
}

// CC generic credit sms
func (o *SmsOption_CreditCardGenericCreditSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardGenericCreditSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardGenericCreditSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardGenericCreditSmsOption.GetOption().(type) {
	case *CreditCardGenericCreditSmsOption_CreditCardGenericCreditSmsOptionV1:
		return o.CreditCardGenericCreditSmsOption.GetCreditCardGenericCreditSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardGenericCreditSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card generic credit option is nil")
	}
	switch o.CreditCardGenericCreditSmsOption.GetOption().(type) {
	case *CreditCardGenericCreditSmsOption_CreditCardGenericCreditSmsOptionV1:
		amount := o.CreditCardGenericCreditSmsOption.GetCreditCardGenericCreditSmsOptionV1().GetAmount()
		amountStr := moneyPkg.ToDisplayStringWithINRSymbol(amount)
		msg := ReplaceWithTrim(template, "{#amount#}", amountStr, 1)
		txnDate, txnTime := getTxnDateAndTime(o.CreditCardGenericCreditSmsOption.GetCreditCardGenericCreditSmsOptionV1().GetTxnTime())
		msg = ReplaceWithTrim(msg, "{#txn_date#}", txnDate, 1)
		msg = ReplaceWithTrim(msg, "{#txn_time#}", txnTime, 1)
		msg = ReplaceWithTrim(msg, "{#cc_ending_digits#}", o.CreditCardGenericCreditSmsOption.GetCreditCardGenericCreditSmsOptionV1().GetCcEndingDigits(), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card generic credit template")
}

func getTxnDateAndTime(timestamp *timestampPb.Timestamp) (string, string) {
	txnDate := datetime.TimestampToDateInLoc(timestamp, datetime.IST)
	txnDateString := datetime.DateToString(txnDate, "02 Jan 2006", datetime.IST)
	txnTimeString := datetime.TimestampToTime(timestamp).In(datetime.IST).Format(time.Kitchen)
	return txnDateString, txnTimeString
}

func getTxnDateAndTimeV2(timestamp *timestampPb.Timestamp, dateLayout, timeLayout string) (string, string) {
	txnDate := datetime.TimestampToDateInLoc(timestamp, datetime.IST)
	txnDateString := datetime.DateToString(txnDate, dateLayout, datetime.IST)
	txnTimeString := datetime.TimestampToTime(timestamp).In(datetime.IST).Format(timeLayout)
	return txnDateString, txnTimeString
}

// CC Card closure intimation sms : sent as cc not activated
func (o *SmsOption_CreditCardNotActivatedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardNotActivatedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardNotActivatedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card not activated sms option is nil")
	}
	switch o.CreditCardNotActivatedSmsOption.GetOption().(type) {
	case *CreditCardNotActivatedSmsOption_CreditCardNotActivatedSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardNotActivatedSmsOption.GetCreditCardNotActivatedSmsOptionV1().GetCustomerName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#cc_last_four_digits#}", o.CreditCardNotActivatedSmsOption.GetCreditCardNotActivatedSmsOptionV1().GetCreditCardLastFourDigits(), 1)
		msg = ReplaceWithTrim(msg, "{#total_inactive_days#}", strconv.Itoa(int(o.CreditCardNotActivatedSmsOption.GetCreditCardNotActivatedSmsOptionV1().GetTotalInactiveDays())), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found credit card not activated template")
}

func (o *SmsOption_CreditCardNotActivatedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardNotActivatedSmsOption.GetOption().(type) {
	case *CreditCardNotActivatedSmsOption_CreditCardNotActivatedSmsOptionV1:
		return o.CreditCardNotActivatedSmsOption.GetCreditCardNotActivatedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// CC card closure confirmaiton sms
func (o *SmsOption_CreditCardClosureConfirmationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardClosureConfirmationSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardClosureConfirmationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card not activated sms option is nil")
	}
	switch o.CreditCardClosureConfirmationSmsOption.GetOption().(type) {
	case *CreditCardClosureConfirmationSmsOption_CreditCardClosureConfirmationSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.CreditCardClosureConfirmationSmsOption.GetCreditCardClosureConfirmationSmsOptionV1().GetCustomerName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#cc_last_four_digits#}", o.CreditCardClosureConfirmationSmsOption.GetCreditCardClosureConfirmationSmsOptionV1().GetCcLastFourDigits(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found credit card not activated template")
}

func (o *SmsOption_CreditCardClosureConfirmationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardClosureConfirmationSmsOption.GetOption().(type) {
	case *CreditCardClosureConfirmationSmsOption_CreditCardClosureConfirmationSmsOptionV1:
		return o.CreditCardClosureConfirmationSmsOption.GetCreditCardClosureConfirmationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// Secured CC fd created comms
func (o *SmsOption_SecuredCreditCardSuccessfulFdCreationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSmsType()
}

func (o *SmsOption_SecuredCreditCardSuccessfulFdCreationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("secured credit card successful fd creation sms option is nil")
	}
	switch o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetOption().(type) {
	case *SecuredCreditCardSuccessfulFdCreationSmsOption_SecuredCreditCardSuccessfulFdCreationSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#deposit_identifier#}", o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSecuredCreditCardSuccessfulFdCreationSmsOptionV1().GetDepositIdentifier(), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_amount#}", moneyPkg.ToDisplayStringWithINRSymbol(o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSecuredCreditCardSuccessfulFdCreationSmsOptionV1().GetDepositAmount()), 1)
		msg = ReplaceWithTrim(msg, "{#interest_rate#}", o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSecuredCreditCardSuccessfulFdCreationSmsOptionV1().GetInterestRate(), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_duration#}", GetDepositDurationString(o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSecuredCreditCardSuccessfulFdCreationSmsOptionV1().GetDepositTerm()), 1)
		msg = ReplaceWithTrim(msg, "{#maturity_date#}", datetime.DateToString(o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSecuredCreditCardSuccessfulFdCreationSmsOptionV1().GetMaturityDate(), "January 2, 2006", datetime.IST), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found secured credit card successful fd creation template")
}

func (o *SmsOption_SecuredCreditCardSuccessfulFdCreationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetOption().(type) {
	case *SecuredCreditCardSuccessfulFdCreationSmsOption_SecuredCreditCardSuccessfulFdCreationSmsOptionV1:
		return o.SecuredCreditCardSuccessfulFdCreationSmsOption.GetSecuredCreditCardSuccessfulFdCreationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// Secured CC lien marking success sms
func (o *SmsOption_SecuredCreditCardFdLienMarkingIntimationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SecuredCreditCardFdLienMarkingIntimationSmsOption.GetSmsType()
}

func (o *SmsOption_SecuredCreditCardFdLienMarkingIntimationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("secured credit card successful fd lien marking sms option is nil")
	}
	switch o.SecuredCreditCardFdLienMarkingIntimationSmsOption.GetOption().(type) {
	case *SecuredCreditCardFdLienMarkingIntimationSmsOption_SecuredCreditCardFdLienMarkingIntimationSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#deposit_identifier#}", o.SecuredCreditCardFdLienMarkingIntimationSmsOption.GetSecuredCreditCardFdLienMarkingIntimationSmsOptionV1().GetDepositIdentifier(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found secured card successful fd lien marking template")
}

func (o *SmsOption_SecuredCreditCardFdLienMarkingIntimationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SecuredCreditCardFdLienMarkingIntimationSmsOption.GetOption().(type) {
	case *SecuredCreditCardFdLienMarkingIntimationSmsOption_SecuredCreditCardFdLienMarkingIntimationSmsOptionV1:
		return o.SecuredCreditCardFdLienMarkingIntimationSmsOption.GetSecuredCreditCardFdLienMarkingIntimationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SecuredCreditCardFdClosureConfirmationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SecuredCreditCardFdClosureConfirmationSmsOption.GetSmsType()
}

func (o *SmsOption_SecuredCreditCardFdClosureConfirmationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("secured credit card successful fd closure sms option is nil")
	}
	switch o.SecuredCreditCardFdClosureConfirmationSmsOption.GetOption().(type) {
	case *SecuredCreditCardFdClosureConfirmationSmsOption_SecuredCreditCardFdClosureConfirmationSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#deposit_identifier#}", o.SecuredCreditCardFdClosureConfirmationSmsOption.GetSecuredCreditCardFdClosureConfirmationSmsOptionV1().GetDepositIdentifier(), 1)
		msg = ReplaceWithTrim(msg, "{#maturity_amount#}", moneyPkg.ToDisplayStringWithINRSymbol(o.SecuredCreditCardFdClosureConfirmationSmsOption.GetSecuredCreditCardFdClosureConfirmationSmsOptionV1().GetMaturityAmount()), 1)
		msg = ReplaceWithTrim(msg, "{#account_number#}", o.SecuredCreditCardFdClosureConfirmationSmsOption.GetSecuredCreditCardFdClosureConfirmationSmsOptionV1().GetMaskedSavingsAccNo(), 1)
		msg = ReplaceWithTrim(msg, "{#closure_date#}", datetime.DateToString(o.SecuredCreditCardFdClosureConfirmationSmsOption.GetSecuredCreditCardFdClosureConfirmationSmsOptionV1().GetFdClosureDate(), "January 2, 2006", datetime.IST), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found secured credit card successful fd closure template")
}

func (o *SmsOption_SecuredCreditCardFdClosureConfirmationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SecuredCreditCardFdClosureConfirmationSmsOption.GetOption().(type) {
	case *SecuredCreditCardFdClosureConfirmationSmsOption_SecuredCreditCardFdClosureConfirmationSmsOptionV1:
		return o.SecuredCreditCardFdClosureConfirmationSmsOption.GetSecuredCreditCardFdClosureConfirmationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// Secured cc fd closure warning

func (o *SmsOption_SecuredCreditCardFdClosureWarningSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SecuredCreditCardFdClosureWarningSmsOption.GetSmsType()
}

func (o *SmsOption_SecuredCreditCardFdClosureWarningSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("secured credit card fd closure warning sms option is nil")
	}
	switch o.SecuredCreditCardFdClosureWarningSmsOption.GetOption().(type) {
	case *SecuredCreditCardFdClosureWarningSmsOption_SecuredCreditCardFdClosureWarningSmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}", o.SecuredCreditCardFdClosureWarningSmsOption.GetSecuredCreditCardFdClosureWarningSmsOptionV1().GetCustomerName().GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#cc_last_four_digits#}", o.SecuredCreditCardFdClosureWarningSmsOption.GetSecuredCreditCardFdClosureWarningSmsOptionV1().GetCcLastFourDigits(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for secured credit card fd closure warning template")
}

func (o *SmsOption_SecuredCreditCardFdClosureWarningSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SecuredCreditCardFdClosureWarningSmsOption.GetOption().(type) {
	case *SecuredCreditCardFdClosureWarningSmsOption_SecuredCreditCardFdClosureWarningSmsOptionV1:
		return o.SecuredCreditCardFdClosureWarningSmsOption.GetSecuredCreditCardFdClosureWarningSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// GetDepositDurationString returns a string such that the days suffix is not added since sms templates
// already contain the same.
func GetDepositDurationString(depositTerm *types.DepositTerm) string {
	if depositTerm.GetMonths() != 0 {
		return fmt.Sprintf("%d months %d", depositTerm.GetMonths(), depositTerm.GetDays())
	}
	return fmt.Sprintf("%d", depositTerm.GetDays())
}

func (o *SmsOption_CreditCardWebEligibilityApprovedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardWebEligibilityApprovedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardWebEligibilityApprovedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for web eligibility approved sms")
	}
	switch o.CreditCardWebEligibilityApprovedSmsOption.GetOption().(type) {
	case *CreditCardWebEligibilityApprovedSmsOption_CreditCardWebEligibilityApprovedSmsOptionV1:
		template = strings.Replace(template, "{#name#}", o.CreditCardWebEligibilityApprovedSmsOption.GetCreditCardWebEligibilityApprovedSmsOptionV1().GetName().ToString(), 1)
		template = strings.Replace(template, "{#download_link#}", o.CreditCardWebEligibilityApprovedSmsOption.GetCreditCardWebEligibilityApprovedSmsOptionV1().GetDownloadLink(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cc web eligibility approved sms")
}

func (o *SmsOption_CreditCardWebEligibilityApprovedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardWebEligibilityApprovedSmsOption.GetOption().(type) {
	case *CreditCardWebEligibilityApprovedSmsOption_CreditCardWebEligibilityApprovedSmsOptionV1:
		return o.CreditCardWebEligibilityApprovedSmsOption.GetCreditCardWebEligibilityApprovedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardWebEligibilityLoginOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardWebEligibilityLoginOtpSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardWebEligibilityLoginOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for web eligibility otp received sms")
	}
	switch o.CreditCardWebEligibilityLoginOtpSmsOption.GetOption().(type) {
	case *CreditCardWebEligibilityLoginOtpSmsOption_CreditCardWebEligibilityLoginOtpSmsOptionV1:
		otp := o.CreditCardWebEligibilityLoginOtpSmsOption.GetCreditCardWebEligibilityLoginOtpSmsOptionV1().GetOtp()
		template = strings.Replace(template, "{#otp#}", otp, 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cc web otp received sms")
}

func (o *SmsOption_CreditCardWebEligibilityLoginOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardWebEligibilityLoginOtpSmsOption.GetOption().(type) {
	case *CreditCardWebEligibilityLoginOtpSmsOption_CreditCardWebEligibilityLoginOtpSmsOptionV1:
		return o.CreditCardWebEligibilityLoginOtpSmsOption.GetCreditCardWebEligibilityLoginOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardWebEligibilityRejectedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardWebEligibilityRejectedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardWebEligibilityRejectedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for web eligibility rejected sms")
	}
	switch o.CreditCardWebEligibilityRejectedSmsOption.GetOption().(type) {
	case *CreditCardWebEligibilityRejectedSmsOption_CreditCardWebEligibilityRejectedSmsOptionV1:
		template = strings.Replace(template, "{#download_link#}", o.CreditCardWebEligibilityRejectedSmsOption.GetCreditCardWebEligibilityRejectedSmsOptionV1().GetDownloadLink(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cc web eligibility rejected sms")
}

func (o *SmsOption_CreditCardWebEligibilityRejectedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardWebEligibilityRejectedSmsOption.GetOption().(type) {
	case *CreditCardWebEligibilityRejectedSmsOption_CreditCardWebEligibilityRejectedSmsOptionV1:
		return o.CreditCardWebEligibilityRejectedSmsOption.GetCreditCardWebEligibilityRejectedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardEligibleWebFlowStartApplicationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardEligibleWebFlowStartApplicationSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardEligibleWebFlowStartApplicationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for cc elegibile web flow drop off sms")
	}
	switch o.CreditCardEligibleWebFlowStartApplicationSmsOption.GetOption().(type) {
	case *CreditCardEligibleWebFlowStartApplicationSmsOption_CreditCardEligibleWebFlowStartApplicationSmsOptionV1:
		template = strings.Replace(template, "{#name#}", o.CreditCardEligibleWebFlowStartApplicationSmsOption.GetCreditCardEligibleWebFlowStartApplicationSmsOptionV1().GetName(), 1)
		template = strings.Replace(template, "{#application_download_url#}", o.CreditCardEligibleWebFlowStartApplicationSmsOption.GetCreditCardEligibleWebFlowStartApplicationSmsOptionV1().GetAppDownloadUrl(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cc elegibile web flow drop off sms")
}

func (o *SmsOption_CreditCardEligibleWebFlowStartApplicationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEligibleWebFlowStartApplicationSmsOption.GetOption().(type) {
	case *CreditCardEligibleWebFlowStartApplicationSmsOption_CreditCardEligibleWebFlowStartApplicationSmsOptionV1:
		return o.CreditCardEligibleWebFlowStartApplicationSmsOption.GetCreditCardEligibleWebFlowStartApplicationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardEligibleWebFlowCompleteApplicationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardEligibleWebFlowCompleteApplicationSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardEligibleWebFlowCompleteApplicationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for cc elegibile web flow drop off sms")
	}
	switch o.CreditCardEligibleWebFlowCompleteApplicationSmsOption.GetOption().(type) {
	case *CreditCardEligibleWebFlowCompleteApplicationSmsOption_CreditCardEligibleWebFlowCompleteApplicationSmsOptionV1:
		template = strings.Replace(template, "{#name#}", o.CreditCardEligibleWebFlowCompleteApplicationSmsOption.GetCreditCardEligibleWebFlowCompleteApplicationSmsOptionV1().GetName(), 1)
		template = strings.Replace(template, "{#application_download_url#}", o.CreditCardEligibleWebFlowCompleteApplicationSmsOption.GetCreditCardEligibleWebFlowCompleteApplicationSmsOptionV1().GetAppDownloadUrl(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cc elegibile web flow drop off sms")
}

func (o *SmsOption_CreditCardEligibleWebFlowCompleteApplicationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEligibleWebFlowCompleteApplicationSmsOption.GetOption().(type) {
	case *CreditCardEligibleWebFlowCompleteApplicationSmsOption_CreditCardEligibleWebFlowCompleteApplicationSmsOptionV1:
		return o.CreditCardEligibleWebFlowCompleteApplicationSmsOption.GetCreditCardEligibleWebFlowCompleteApplicationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}
func (o *SmsOption_CreditCardRevisedLimitAppliedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}

	return o.CreditCardRevisedLimitAppliedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardRevisedLimitAppliedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for credit card revised limit applied")
	}
	switch o.CreditCardRevisedLimitAppliedSmsOption.GetOption().(type) {
	case *CreditCardRevisedLimitSmsOption_CreditCardRevisedLimitSmsOptionV1:
		template = strings.Replace(template, "{#first_name#}", o.CreditCardRevisedLimitAppliedSmsOption.GetCreditCardRevisedLimitSmsOptionV1().GetCustomerName().GetFirstName(), 1)
		template = strings.Replace(template, "{#limit#}", moneyPkg.ToDisplayStringInIndianFormatWithCurrencyCode(o.CreditCardRevisedLimitAppliedSmsOption.GetCreditCardRevisedLimitSmsOptionV1().GetCreditLimit(), 0), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cc revised limit applied sms")
}

func (o *SmsOption_CreditCardRevisedLimitAppliedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardRevisedLimitAppliedSmsOption.GetOption().(type) {
	case *CreditCardRevisedLimitSmsOption_CreditCardRevisedLimitSmsOptionV1:
		return o.CreditCardRevisedLimitAppliedSmsOption.GetCreditCardRevisedLimitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardBlockKycExpirySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}

	return o.CreditCardBlockKycExpirySmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardBlockKycExpirySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for credit card block kyc expiry")
	}
	switch o.CreditCardBlockKycExpirySmsOption.GetOption().(type) {
	case *CreditCardBlockKycExpirySmsOption_CreditCardBlockKycExpirySmsOptionV1:
		template = strings.Replace(template, "{#card_last_four_digit#}", o.CreditCardBlockKycExpirySmsOption.GetCreditCardBlockKycExpirySmsOptionV1().GetCardLastFourDigits(), 1)
		template = strings.Replace(template, "{#complete_kyc_link_url#}", o.CreditCardBlockKycExpirySmsOption.GetCreditCardBlockKycExpirySmsOptionV1().GetCompleteKycLinkUrl(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for credit card block kyc expiry")
}

func (o *SmsOption_CreditCardBlockKycExpirySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardBlockKycExpirySmsOption.GetOption().(type) {
	case *CreditCardBlockKycExpirySmsOption_CreditCardBlockKycExpirySmsOptionV1:
		return o.CreditCardBlockKycExpirySmsOption.GetCreditCardBlockKycExpirySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardUnblockKycCompletedSmOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}

	return o.CreditCardUnblockKycCompletedSmOption.GetSmsType()
}

func (o *SmsOption_CreditCardUnblockKycCompletedSmOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for credit card unlock kyc completed")
	}
	switch o.CreditCardUnblockKycCompletedSmOption.GetOption().(type) {
	case *CreditCardUnblockKycCompletedSmOption_CreditCardUnblockKycCompletedSmOptionV1:
		template = strings.Replace(template, "{#card_last_four_digit#}", o.CreditCardUnblockKycCompletedSmOption.GetCreditCardUnblockKycCompletedSmOptionV1().GetCardLastFourDigits(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for credit card unlock kyc completed")
}

func (o *SmsOption_CreditCardUnblockKycCompletedSmOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardUnblockKycCompletedSmOption.GetOption().(type) {
	case *CreditCardUnblockKycCompletedSmOption_CreditCardUnblockKycCompletedSmOptionV1:
		return o.CreditCardUnblockKycCompletedSmOption.GetCreditCardUnblockKycCompletedSmOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}
