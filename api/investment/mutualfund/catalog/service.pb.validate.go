// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/mutualfund/catalog/service.proto

package catalog

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	mutualfund "github.com/epifi/gamma/api/investment/mutualfund"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)

	_ = mutualfund.Amc(0)
)

// Validate checks the field values on GetHistoricalReturnRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHistoricalReturnRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHistoricalReturnRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHistoricalReturnRequestMultiError, or nil if none found.
func (m *GetHistoricalReturnRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHistoricalReturnRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHistoricalReturnRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHistoricalReturnRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHistoricalReturnRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InvestmentDurationInMonths

	if all {
		switch v := interface{}(m.GetInvestmentCycle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHistoricalReturnRequestValidationError{
					field:  "InvestmentCycle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHistoricalReturnRequestValidationError{
					field:  "InvestmentCycle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentCycle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHistoricalReturnRequestValidationError{
				field:  "InvestmentCycle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHistoricalReturnRequestMultiError(errors)
	}

	return nil
}

// GetHistoricalReturnRequestMultiError is an error wrapping multiple
// validation errors returned by GetHistoricalReturnRequest.ValidateAll() if
// the designated constraints aren't met.
type GetHistoricalReturnRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHistoricalReturnRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHistoricalReturnRequestMultiError) AllErrors() []error { return m }

// GetHistoricalReturnRequestValidationError is the validation error returned
// by GetHistoricalReturnRequest.Validate if the designated constraints aren't met.
type GetHistoricalReturnRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHistoricalReturnRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHistoricalReturnRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHistoricalReturnRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHistoricalReturnRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHistoricalReturnRequestValidationError) ErrorName() string {
	return "GetHistoricalReturnRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetHistoricalReturnRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHistoricalReturnRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHistoricalReturnRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHistoricalReturnRequestValidationError{}

// Validate checks the field values on GetHistoricalReturnResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHistoricalReturnResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHistoricalReturnResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHistoricalReturnResponseMultiError, or nil if none found.
func (m *GetHistoricalReturnResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHistoricalReturnResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHistoricalReturnResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHistoricalReturnResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHistoricalReturnResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentInvestmentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHistoricalReturnResponseValidationError{
					field:  "CurrentInvestmentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHistoricalReturnResponseValidationError{
					field:  "CurrentInvestmentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentInvestmentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHistoricalReturnResponseValidationError{
				field:  "CurrentInvestmentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReturnPercent

	if all {
		switch v := interface{}(m.GetTotalInvestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHistoricalReturnResponseValidationError{
					field:  "TotalInvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHistoricalReturnResponseValidationError{
					field:  "TotalInvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalInvestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHistoricalReturnResponseValidationError{
				field:  "TotalInvestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHistoricalReturnResponseMultiError(errors)
	}

	return nil
}

// GetHistoricalReturnResponseMultiError is an error wrapping multiple
// validation errors returned by GetHistoricalReturnResponse.ValidateAll() if
// the designated constraints aren't met.
type GetHistoricalReturnResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHistoricalReturnResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHistoricalReturnResponseMultiError) AllErrors() []error { return m }

// GetHistoricalReturnResponseValidationError is the validation error returned
// by GetHistoricalReturnResponse.Validate if the designated constraints
// aren't met.
type GetHistoricalReturnResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHistoricalReturnResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHistoricalReturnResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHistoricalReturnResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHistoricalReturnResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHistoricalReturnResponseValidationError) ErrorName() string {
	return "GetHistoricalReturnResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetHistoricalReturnResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHistoricalReturnResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHistoricalReturnResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHistoricalReturnResponseValidationError{}

// Validate checks the field values on GetSelectedFiltersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSelectedFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSelectedFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSelectedFiltersRequestMultiError, or nil if none found.
func (m *GetSelectedFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSelectedFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CollectionId

	if len(errors) > 0 {
		return GetSelectedFiltersRequestMultiError(errors)
	}

	return nil
}

// GetSelectedFiltersRequestMultiError is an error wrapping multiple validation
// errors returned by GetSelectedFiltersRequest.ValidateAll() if the
// designated constraints aren't met.
type GetSelectedFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSelectedFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSelectedFiltersRequestMultiError) AllErrors() []error { return m }

// GetSelectedFiltersRequestValidationError is the validation error returned by
// GetSelectedFiltersRequest.Validate if the designated constraints aren't met.
type GetSelectedFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSelectedFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSelectedFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSelectedFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSelectedFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSelectedFiltersRequestValidationError) ErrorName() string {
	return "GetSelectedFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSelectedFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSelectedFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSelectedFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSelectedFiltersRequestValidationError{}

// Validate checks the field values on GetSelectedFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSelectedFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSelectedFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSelectedFiltersResponseMultiError, or nil if none found.
func (m *GetSelectedFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSelectedFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSelectedFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSelectedFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSelectedFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CollectionId

	if len(errors) > 0 {
		return GetSelectedFiltersResponseMultiError(errors)
	}

	return nil
}

// GetSelectedFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by GetSelectedFiltersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSelectedFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSelectedFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSelectedFiltersResponseMultiError) AllErrors() []error { return m }

// GetSelectedFiltersResponseValidationError is the validation error returned
// by GetSelectedFiltersResponse.Validate if the designated constraints aren't met.
type GetSelectedFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSelectedFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSelectedFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSelectedFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSelectedFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSelectedFiltersResponseValidationError) ErrorName() string {
	return "GetSelectedFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSelectedFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSelectedFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSelectedFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSelectedFiltersResponseValidationError{}

// Validate checks the field values on GetAllUpdatedFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllUpdatedFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllUpdatedFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllUpdatedFiltersRequestMultiError, or nil if none found.
func (m *GetAllUpdatedFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllUpdatedFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAllUpdatedFiltersRequestMultiError(errors)
	}

	return nil
}

// GetAllUpdatedFiltersRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllUpdatedFiltersRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAllUpdatedFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllUpdatedFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllUpdatedFiltersRequestMultiError) AllErrors() []error { return m }

// GetAllUpdatedFiltersRequestValidationError is the validation error returned
// by GetAllUpdatedFiltersRequest.Validate if the designated constraints
// aren't met.
type GetAllUpdatedFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllUpdatedFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllUpdatedFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllUpdatedFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllUpdatedFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllUpdatedFiltersRequestValidationError) ErrorName() string {
	return "GetAllUpdatedFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllUpdatedFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllUpdatedFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllUpdatedFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllUpdatedFiltersRequestValidationError{}

// Validate checks the field values on GetAllUpdatedFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllUpdatedFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllUpdatedFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllUpdatedFiltersResponseMultiError, or nil if none found.
func (m *GetAllUpdatedFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllUpdatedFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllUpdatedFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllUpdatedFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllUpdatedFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFilterGroups()))
		i := 0
		for key := range m.GetFilterGroups() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFilterGroups()[key]
			_ = val

			// no validation rules for FilterGroups[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetAllUpdatedFiltersResponseValidationError{
							field:  fmt.Sprintf("FilterGroups[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetAllUpdatedFiltersResponseValidationError{
							field:  fmt.Sprintf("FilterGroups[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetAllUpdatedFiltersResponseValidationError{
						field:  fmt.Sprintf("FilterGroups[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetAllUpdatedFiltersResponseMultiError(errors)
	}

	return nil
}

// GetAllUpdatedFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllUpdatedFiltersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAllUpdatedFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllUpdatedFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllUpdatedFiltersResponseMultiError) AllErrors() []error { return m }

// GetAllUpdatedFiltersResponseValidationError is the validation error returned
// by GetAllUpdatedFiltersResponse.Validate if the designated constraints
// aren't met.
type GetAllUpdatedFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllUpdatedFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllUpdatedFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllUpdatedFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllUpdatedFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllUpdatedFiltersResponseValidationError) ErrorName() string {
	return "GetAllUpdatedFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllUpdatedFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllUpdatedFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllUpdatedFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllUpdatedFiltersResponseValidationError{}

// Validate checks the field values on GetAvailableFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAvailableFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAvailableFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAvailableFiltersRequestMultiError, or nil if none found.
func (m *GetAvailableFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAvailableFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAvailableFiltersRequestMultiError(errors)
	}

	return nil
}

// GetAvailableFiltersRequestMultiError is an error wrapping multiple
// validation errors returned by GetAvailableFiltersRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAvailableFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAvailableFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAvailableFiltersRequestMultiError) AllErrors() []error { return m }

// GetAvailableFiltersRequestValidationError is the validation error returned
// by GetAvailableFiltersRequest.Validate if the designated constraints aren't met.
type GetAvailableFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAvailableFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAvailableFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAvailableFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAvailableFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAvailableFiltersRequestValidationError) ErrorName() string {
	return "GetAvailableFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAvailableFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAvailableFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAvailableFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAvailableFiltersRequestValidationError{}

// Validate checks the field values on GetAvailableFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAvailableFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAvailableFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAvailableFiltersResponseMultiError, or nil if none found.
func (m *GetAvailableFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAvailableFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAvailableFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAvailableFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAvailableFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAvailableFiltersResponseMultiError(errors)
	}

	return nil
}

// GetAvailableFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by GetAvailableFiltersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAvailableFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAvailableFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAvailableFiltersResponseMultiError) AllErrors() []error { return m }

// GetAvailableFiltersResponseValidationError is the validation error returned
// by GetAvailableFiltersResponse.Validate if the designated constraints
// aren't met.
type GetAvailableFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAvailableFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAvailableFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAvailableFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAvailableFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAvailableFiltersResponseValidationError) ErrorName() string {
	return "GetAvailableFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAvailableFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAvailableFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAvailableFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAvailableFiltersResponseValidationError{}

// Validate checks the field values on GetFiltersForCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFiltersForCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFiltersForCollectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFiltersForCollectionRequestMultiError, or nil if none found.
func (m *GetFiltersForCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiltersForCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CollectionId

	if len(errors) > 0 {
		return GetFiltersForCollectionRequestMultiError(errors)
	}

	return nil
}

// GetFiltersForCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by GetFiltersForCollectionRequest.ValidateAll()
// if the designated constraints aren't met.
type GetFiltersForCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiltersForCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiltersForCollectionRequestMultiError) AllErrors() []error { return m }

// GetFiltersForCollectionRequestValidationError is the validation error
// returned by GetFiltersForCollectionRequest.Validate if the designated
// constraints aren't met.
type GetFiltersForCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiltersForCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiltersForCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiltersForCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiltersForCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiltersForCollectionRequestValidationError) ErrorName() string {
	return "GetFiltersForCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiltersForCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiltersForCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiltersForCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiltersForCollectionRequestValidationError{}

// Validate checks the field values on GetFiltersForCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFiltersForCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFiltersForCollectionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFiltersForCollectionResponseMultiError, or nil if none found.
func (m *GetFiltersForCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiltersForCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiltersForCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiltersForCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiltersForCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFilterGroups()))
		i := 0
		for key := range m.GetFilterGroups() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFilterGroups()[key]
			_ = val

			// no validation rules for FilterGroups[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetFiltersForCollectionResponseValidationError{
							field:  fmt.Sprintf("FilterGroups[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetFiltersForCollectionResponseValidationError{
							field:  fmt.Sprintf("FilterGroups[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetFiltersForCollectionResponseValidationError{
						field:  fmt.Sprintf("FilterGroups[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetFiltersForCollectionResponseMultiError(errors)
	}

	return nil
}

// GetFiltersForCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by GetFiltersForCollectionResponse.ValidateAll()
// if the designated constraints aren't met.
type GetFiltersForCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiltersForCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiltersForCollectionResponseMultiError) AllErrors() []error { return m }

// GetFiltersForCollectionResponseValidationError is the validation error
// returned by GetFiltersForCollectionResponse.Validate if the designated
// constraints aren't met.
type GetFiltersForCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiltersForCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiltersForCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiltersForCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiltersForCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiltersForCollectionResponseValidationError) ErrorName() string {
	return "GetFiltersForCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiltersForCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiltersForCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiltersForCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiltersForCollectionResponseValidationError{}

// Validate checks the field values on GetFundsForFiltersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundsForFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundsForFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundsForFiltersRequestMultiError, or nil if none found.
func (m *GetFundsForFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundsForFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsForFiltersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsForFiltersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsForFiltersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortParam

	// no validation rules for SortOrder

	// no validation rules for CollectionId

	// no validation rules for ActorId

	// no validation rules for AppPlatform

	// no validation rules for AppVersion

	// no validation rules for SearchText

	if len(errors) > 0 {
		return GetFundsForFiltersRequestMultiError(errors)
	}

	return nil
}

// GetFundsForFiltersRequestMultiError is an error wrapping multiple validation
// errors returned by GetFundsForFiltersRequest.ValidateAll() if the
// designated constraints aren't met.
type GetFundsForFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundsForFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundsForFiltersRequestMultiError) AllErrors() []error { return m }

// GetFundsForFiltersRequestValidationError is the validation error returned by
// GetFundsForFiltersRequest.Validate if the designated constraints aren't met.
type GetFundsForFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundsForFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundsForFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundsForFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundsForFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundsForFiltersRequestValidationError) ErrorName() string {
	return "GetFundsForFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundsForFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundsForFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundsForFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundsForFiltersRequestValidationError{}

// Validate checks the field values on GetFundsForFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundsForFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundsForFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundsForFiltersResponseMultiError, or nil if none found.
func (m *GetFundsForFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundsForFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsForFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsForFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsForFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsForFiltersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsForFiltersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsForFiltersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFundInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundsForFiltersResponseValidationError{
						field:  fmt.Sprintf("FundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundsForFiltersResponseValidationError{
						field:  fmt.Sprintf("FundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundsForFiltersResponseValidationError{
					field:  fmt.Sprintf("FundInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalFunds

	if len(errors) > 0 {
		return GetFundsForFiltersResponseMultiError(errors)
	}

	return nil
}

// GetFundsForFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by GetFundsForFiltersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFundsForFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundsForFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundsForFiltersResponseMultiError) AllErrors() []error { return m }

// GetFundsForFiltersResponseValidationError is the validation error returned
// by GetFundsForFiltersResponse.Validate if the designated constraints aren't met.
type GetFundsForFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundsForFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundsForFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundsForFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundsForFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundsForFiltersResponseValidationError) ErrorName() string {
	return "GetFundsForFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundsForFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundsForFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundsForFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundsForFiltersResponseValidationError{}

// Validate checks the field values on GetCollectionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCollectionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCollectionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCollectionsRequestMultiError, or nil if none found.
func (m *GetCollectionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollectionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Screen

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetCollectionsRequestMultiError(errors)
	}

	return nil
}

// GetCollectionsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCollectionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCollectionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollectionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollectionsRequestMultiError) AllErrors() []error { return m }

// GetCollectionsRequestValidationError is the validation error returned by
// GetCollectionsRequest.Validate if the designated constraints aren't met.
type GetCollectionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollectionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollectionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollectionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollectionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollectionsRequestValidationError) ErrorName() string {
	return "GetCollectionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollectionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollectionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollectionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollectionsRequestValidationError{}

// Validate checks the field values on GetCollectionsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCollectionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCollectionsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCollectionsResponseMultiError, or nil if none found.
func (m *GetCollectionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollectionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCollections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCollectionsResponseValidationError{
						field:  fmt.Sprintf("Collections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCollectionsResponseValidationError{
						field:  fmt.Sprintf("Collections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCollectionsResponseValidationError{
					field:  fmt.Sprintf("Collections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetCollectionsWithFilterCounts()))
		i := 0
		for key := range m.GetCollectionsWithFilterCounts() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCollectionsWithFilterCounts()[key]
			_ = val

			// no validation rules for CollectionsWithFilterCounts[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetCollectionsResponseValidationError{
							field:  fmt.Sprintf("CollectionsWithFilterCounts[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetCollectionsResponseValidationError{
							field:  fmt.Sprintf("CollectionsWithFilterCounts[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetCollectionsResponseValidationError{
						field:  fmt.Sprintf("CollectionsWithFilterCounts[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetCollectionsResponseMultiError(errors)
	}

	return nil
}

// GetCollectionsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCollectionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCollectionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollectionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollectionsResponseMultiError) AllErrors() []error { return m }

// GetCollectionsResponseValidationError is the validation error returned by
// GetCollectionsResponse.Validate if the designated constraints aren't met.
type GetCollectionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollectionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollectionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollectionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollectionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollectionsResponseValidationError) ErrorName() string {
	return "GetCollectionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollectionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollectionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollectionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollectionsResponseValidationError{}

// Validate checks the field values on GetCollectionInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCollectionInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCollectionInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCollectionInfoRequestMultiError, or nil if none found.
func (m *GetCollectionInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollectionInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Id.(type) {
	case *GetCollectionInfoRequest_CollectionId:
		if v == nil {
			err := GetCollectionInfoRequestValidationError{
				field:  "Id",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CollectionId
	case *GetCollectionInfoRequest_CollectionName:
		if v == nil {
			err := GetCollectionInfoRequestValidationError{
				field:  "Id",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CollectionName
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCollectionInfoRequestMultiError(errors)
	}

	return nil
}

// GetCollectionInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetCollectionInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCollectionInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollectionInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollectionInfoRequestMultiError) AllErrors() []error { return m }

// GetCollectionInfoRequestValidationError is the validation error returned by
// GetCollectionInfoRequest.Validate if the designated constraints aren't met.
type GetCollectionInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollectionInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollectionInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollectionInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollectionInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollectionInfoRequestValidationError) ErrorName() string {
	return "GetCollectionInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollectionInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollectionInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollectionInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollectionInfoRequestValidationError{}

// Validate checks the field values on GetCollectionInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCollectionInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCollectionInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCollectionInfoResponseMultiError, or nil if none found.
func (m *GetCollectionInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollectionInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectionInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectionInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectionInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCollection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectionInfoResponseValidationError{
					field:  "Collection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectionInfoResponseValidationError{
					field:  "Collection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectionInfoResponseValidationError{
				field:  "Collection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterCount

	if len(errors) > 0 {
		return GetCollectionInfoResponseMultiError(errors)
	}

	return nil
}

// GetCollectionInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetCollectionInfoResponse.ValidateAll() if the
// designated constraints aren't met.
type GetCollectionInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollectionInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollectionInfoResponseMultiError) AllErrors() []error { return m }

// GetCollectionInfoResponseValidationError is the validation error returned by
// GetCollectionInfoResponse.Validate if the designated constraints aren't met.
type GetCollectionInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollectionInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollectionInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollectionInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollectionInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollectionInfoResponseValidationError) ErrorName() string {
	return "GetCollectionInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollectionInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollectionInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollectionInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollectionInfoResponseValidationError{}

// Validate checks the field values on GetWatchListsByActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWatchListsByActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWatchListsByActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWatchListsByActorRequestMultiError, or nil if none found.
func (m *GetWatchListsByActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWatchListsByActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetWatchListsByActorRequestMultiError(errors)
	}

	return nil
}

// GetWatchListsByActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetWatchListsByActorRequest.ValidateAll() if
// the designated constraints aren't met.
type GetWatchListsByActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWatchListsByActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWatchListsByActorRequestMultiError) AllErrors() []error { return m }

// GetWatchListsByActorRequestValidationError is the validation error returned
// by GetWatchListsByActorRequest.Validate if the designated constraints
// aren't met.
type GetWatchListsByActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWatchListsByActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWatchListsByActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWatchListsByActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWatchListsByActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWatchListsByActorRequestValidationError) ErrorName() string {
	return "GetWatchListsByActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWatchListsByActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWatchListsByActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWatchListsByActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWatchListsByActorRequestValidationError{}

// Validate checks the field values on GetWatchListsByActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWatchListsByActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWatchListsByActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWatchListsByActorResponseMultiError, or nil if none found.
func (m *GetWatchListsByActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWatchListsByActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWatchListsByActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWatchListsByActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWatchListsByActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWatchLists() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetWatchListsByActorResponseValidationError{
						field:  fmt.Sprintf("WatchLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetWatchListsByActorResponseValidationError{
						field:  fmt.Sprintf("WatchLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetWatchListsByActorResponseValidationError{
					field:  fmt.Sprintf("WatchLists[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetWatchListsByActorResponseMultiError(errors)
	}

	return nil
}

// GetWatchListsByActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetWatchListsByActorResponse.ValidateAll() if
// the designated constraints aren't met.
type GetWatchListsByActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWatchListsByActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWatchListsByActorResponseMultiError) AllErrors() []error { return m }

// GetWatchListsByActorResponseValidationError is the validation error returned
// by GetWatchListsByActorResponse.Validate if the designated constraints
// aren't met.
type GetWatchListsByActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWatchListsByActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWatchListsByActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWatchListsByActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWatchListsByActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWatchListsByActorResponseValidationError) ErrorName() string {
	return "GetWatchListsByActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWatchListsByActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWatchListsByActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWatchListsByActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWatchListsByActorResponseValidationError{}

// Validate checks the field values on UpdateWatchListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateWatchListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateWatchListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateWatchListRequestMultiError, or nil if none found.
func (m *UpdateWatchListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateWatchListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for MutualFundId

	// no validation rules for RequestType

	// no validation rules for WatchlistId

	if len(errors) > 0 {
		return UpdateWatchListRequestMultiError(errors)
	}

	return nil
}

// UpdateWatchListRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateWatchListRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateWatchListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateWatchListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateWatchListRequestMultiError) AllErrors() []error { return m }

// UpdateWatchListRequestValidationError is the validation error returned by
// UpdateWatchListRequest.Validate if the designated constraints aren't met.
type UpdateWatchListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateWatchListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateWatchListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateWatchListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateWatchListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateWatchListRequestValidationError) ErrorName() string {
	return "UpdateWatchListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateWatchListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateWatchListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateWatchListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateWatchListRequestValidationError{}

// Validate checks the field values on UpdateWatchListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateWatchListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateWatchListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateWatchListResponseMultiError, or nil if none found.
func (m *UpdateWatchListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateWatchListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateWatchListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateWatchListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateWatchListResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateWatchListResponseMultiError(errors)
	}

	return nil
}

// UpdateWatchListResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateWatchListResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateWatchListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateWatchListResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateWatchListResponseMultiError) AllErrors() []error { return m }

// UpdateWatchListResponseValidationError is the validation error returned by
// UpdateWatchListResponse.Validate if the designated constraints aren't met.
type UpdateWatchListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateWatchListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateWatchListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateWatchListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateWatchListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateWatchListResponseValidationError) ErrorName() string {
	return "UpdateWatchListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateWatchListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateWatchListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateWatchListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateWatchListResponseValidationError{}

// Validate checks the field values on GetRecentFundsForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecentFundsForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecentFundsForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecentFundsForActorRequestMultiError, or nil if none found.
func (m *GetRecentFundsForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecentFundsForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for SortParam

	// no validation rules for SortOrder

	// no validation rules for AppPlatform

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return GetRecentFundsForActorRequestMultiError(errors)
	}

	return nil
}

// GetRecentFundsForActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetRecentFundsForActorRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRecentFundsForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecentFundsForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecentFundsForActorRequestMultiError) AllErrors() []error { return m }

// GetRecentFundsForActorRequestValidationError is the validation error
// returned by GetRecentFundsForActorRequest.Validate if the designated
// constraints aren't met.
type GetRecentFundsForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecentFundsForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecentFundsForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecentFundsForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecentFundsForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecentFundsForActorRequestValidationError) ErrorName() string {
	return "GetRecentFundsForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecentFundsForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecentFundsForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecentFundsForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecentFundsForActorRequestValidationError{}

// Validate checks the field values on GetRecentFundsForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecentFundsForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecentFundsForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecentFundsForActorResponseMultiError, or nil if none found.
func (m *GetRecentFundsForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecentFundsForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecentFundsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecentFundsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecentFundsForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecentFundInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecentFundsForActorResponseValidationError{
						field:  fmt.Sprintf("RecentFundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecentFundsForActorResponseValidationError{
						field:  fmt.Sprintf("RecentFundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecentFundsForActorResponseValidationError{
					field:  fmt.Sprintf("RecentFundInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRecentFundsForActorResponseMultiError(errors)
	}

	return nil
}

// GetRecentFundsForActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetRecentFundsForActorResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRecentFundsForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecentFundsForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecentFundsForActorResponseMultiError) AllErrors() []error { return m }

// GetRecentFundsForActorResponseValidationError is the validation error
// returned by GetRecentFundsForActorResponse.Validate if the designated
// constraints aren't met.
type GetRecentFundsForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecentFundsForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecentFundsForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecentFundsForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecentFundsForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecentFundsForActorResponseValidationError) ErrorName() string {
	return "GetRecentFundsForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecentFundsForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecentFundsForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecentFundsForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecentFundsForActorResponseValidationError{}

// Validate checks the field values on CheckFundsInCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFundsInCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFundsInCollectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckFundsInCollectionRequestMultiError, or nil if none found.
func (m *CheckFundsInCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFundsInCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *CheckFundsInCollectionRequest_CollectionId:
		if v == nil {
			err := CheckFundsInCollectionRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CollectionId
	case *CheckFundsInCollectionRequest_CollectionName:
		if v == nil {
			err := CheckFundsInCollectionRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CollectionName
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CheckFundsInCollectionRequestMultiError(errors)
	}

	return nil
}

// CheckFundsInCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by CheckFundsInCollectionRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckFundsInCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFundsInCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFundsInCollectionRequestMultiError) AllErrors() []error { return m }

// CheckFundsInCollectionRequestValidationError is the validation error
// returned by CheckFundsInCollectionRequest.Validate if the designated
// constraints aren't met.
type CheckFundsInCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFundsInCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFundsInCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFundsInCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFundsInCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFundsInCollectionRequestValidationError) ErrorName() string {
	return "CheckFundsInCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFundsInCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFundsInCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFundsInCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFundsInCollectionRequestValidationError{}

// Validate checks the field values on CheckFundsInCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFundsInCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFundsInCollectionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckFundsInCollectionResponseMultiError, or nil if none found.
func (m *CheckFundsInCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFundsInCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckFundsInCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckFundsInCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckFundsInCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFundInfo()))
		i := 0
		for key := range m.GetFundInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFundInfo()[key]
			_ = val

			// no validation rules for FundInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CheckFundsInCollectionResponseValidationError{
							field:  fmt.Sprintf("FundInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CheckFundsInCollectionResponseValidationError{
							field:  fmt.Sprintf("FundInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CheckFundsInCollectionResponseValidationError{
						field:  fmt.Sprintf("FundInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CheckFundsInCollectionResponseMultiError(errors)
	}

	return nil
}

// CheckFundsInCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by CheckFundsInCollectionResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckFundsInCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFundsInCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFundsInCollectionResponseMultiError) AllErrors() []error { return m }

// CheckFundsInCollectionResponseValidationError is the validation error
// returned by CheckFundsInCollectionResponse.Validate if the designated
// constraints aren't met.
type CheckFundsInCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFundsInCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFundsInCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFundsInCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFundsInCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFundsInCollectionResponseValidationError) ErrorName() string {
	return "CheckFundsInCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFundsInCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFundsInCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFundsInCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFundsInCollectionResponseValidationError{}

// Validate checks the field values on CollectionFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectionFundInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectionFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectionFundInfoMultiError, or nil if none found.
func (m *CollectionFundInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectionFundInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsPresent

	if len(errors) > 0 {
		return CollectionFundInfoMultiError(errors)
	}

	return nil
}

// CollectionFundInfoMultiError is an error wrapping multiple validation errors
// returned by CollectionFundInfo.ValidateAll() if the designated constraints
// aren't met.
type CollectionFundInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectionFundInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectionFundInfoMultiError) AllErrors() []error { return m }

// CollectionFundInfoValidationError is the validation error returned by
// CollectionFundInfo.Validate if the designated constraints aren't met.
type CollectionFundInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectionFundInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectionFundInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectionFundInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectionFundInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectionFundInfoValidationError) ErrorName() string {
	return "CollectionFundInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CollectionFundInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectionFundInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectionFundInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectionFundInfoValidationError{}

// Validate checks the field values on WatchlistFundInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WatchlistFundInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchlistFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchlistFundInfoMultiError, or nil if none found.
func (m *WatchlistFundInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchlistFundInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsPresent

	if all {
		switch v := interface{}(m.GetWatchlistDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchlistFundInfoValidationError{
					field:  "WatchlistDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchlistFundInfoValidationError{
					field:  "WatchlistDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWatchlistDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchlistFundInfoValidationError{
				field:  "WatchlistDisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WatchlistFundInfoMultiError(errors)
	}

	return nil
}

// WatchlistFundInfoMultiError is an error wrapping multiple validation errors
// returned by WatchlistFundInfo.ValidateAll() if the designated constraints
// aren't met.
type WatchlistFundInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchlistFundInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchlistFundInfoMultiError) AllErrors() []error { return m }

// WatchlistFundInfoValidationError is the validation error returned by
// WatchlistFundInfo.Validate if the designated constraints aren't met.
type WatchlistFundInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchlistFundInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchlistFundInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchlistFundInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchlistFundInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchlistFundInfoValidationError) ErrorName() string {
	return "WatchlistFundInfoValidationError"
}

// Error satisfies the builtin error interface
func (e WatchlistFundInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchlistFundInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchlistFundInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchlistFundInfoValidationError{}

// Validate checks the field values on CheckFundsInWatchListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFundsInWatchListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFundsInWatchListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFundsInWatchListRequestMultiError, or nil if none found.
func (m *CheckFundsInWatchListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFundsInWatchListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return CheckFundsInWatchListRequestMultiError(errors)
	}

	return nil
}

// CheckFundsInWatchListRequestMultiError is an error wrapping multiple
// validation errors returned by CheckFundsInWatchListRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckFundsInWatchListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFundsInWatchListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFundsInWatchListRequestMultiError) AllErrors() []error { return m }

// CheckFundsInWatchListRequestValidationError is the validation error returned
// by CheckFundsInWatchListRequest.Validate if the designated constraints
// aren't met.
type CheckFundsInWatchListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFundsInWatchListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFundsInWatchListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFundsInWatchListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFundsInWatchListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFundsInWatchListRequestValidationError) ErrorName() string {
	return "CheckFundsInWatchListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFundsInWatchListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFundsInWatchListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFundsInWatchListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFundsInWatchListRequestValidationError{}

// Validate checks the field values on CheckFundsInWatchListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFundsInWatchListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFundsInWatchListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckFundsInWatchListResponseMultiError, or nil if none found.
func (m *CheckFundsInWatchListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFundsInWatchListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckFundsInWatchListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckFundsInWatchListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckFundsInWatchListResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFundInfo()))
		i := 0
		for key := range m.GetFundInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFundInfo()[key]
			_ = val

			// no validation rules for FundInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CheckFundsInWatchListResponseValidationError{
							field:  fmt.Sprintf("FundInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CheckFundsInWatchListResponseValidationError{
							field:  fmt.Sprintf("FundInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CheckFundsInWatchListResponseValidationError{
						field:  fmt.Sprintf("FundInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CheckFundsInWatchListResponseMultiError(errors)
	}

	return nil
}

// CheckFundsInWatchListResponseMultiError is an error wrapping multiple
// validation errors returned by CheckFundsInWatchListResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckFundsInWatchListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFundsInWatchListResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFundsInWatchListResponseMultiError) AllErrors() []error { return m }

// CheckFundsInWatchListResponseValidationError is the validation error
// returned by CheckFundsInWatchListResponse.Validate if the designated
// constraints aren't met.
type CheckFundsInWatchListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFundsInWatchListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFundsInWatchListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFundsInWatchListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFundsInWatchListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFundsInWatchListResponseValidationError) ErrorName() string {
	return "CheckFundsInWatchListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFundsInWatchListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFundsInWatchListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFundsInWatchListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFundsInWatchListResponseValidationError{}

// Validate checks the field values on GetFundsInCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundsInCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundsInCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundsInCollectionRequestMultiError, or nil if none found.
func (m *GetFundsInCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundsInCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsInCollectionRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsInCollectionRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsInCollectionRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortParam

	// no validation rules for SortOrder

	// no validation rules for ActorId

	// no validation rules for CollectionId

	// no validation rules for AppPlatform

	// no validation rules for AppVersion

	// no validation rules for SearchText

	if len(errors) > 0 {
		return GetFundsInCollectionRequestMultiError(errors)
	}

	return nil
}

// GetFundsInCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by GetFundsInCollectionRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFundsInCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundsInCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundsInCollectionRequestMultiError) AllErrors() []error { return m }

// GetFundsInCollectionRequestValidationError is the validation error returned
// by GetFundsInCollectionRequest.Validate if the designated constraints
// aren't met.
type GetFundsInCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundsInCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundsInCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundsInCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundsInCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundsInCollectionRequestValidationError) ErrorName() string {
	return "GetFundsInCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundsInCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundsInCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundsInCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundsInCollectionRequestValidationError{}

// Validate checks the field values on GetFundsInCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundsInCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundsInCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundsInCollectionResponseMultiError, or nil if none found.
func (m *GetFundsInCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundsInCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsInCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsInCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsInCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsInCollectionResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsInCollectionResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsInCollectionResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFundInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundsInCollectionResponseValidationError{
						field:  fmt.Sprintf("FundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundsInCollectionResponseValidationError{
						field:  fmt.Sprintf("FundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundsInCollectionResponseValidationError{
					field:  fmt.Sprintf("FundInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalFunds

	if len(errors) > 0 {
		return GetFundsInCollectionResponseMultiError(errors)
	}

	return nil
}

// GetFundsInCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by GetFundsInCollectionResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFundsInCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundsInCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundsInCollectionResponseMultiError) AllErrors() []error { return m }

// GetFundsInCollectionResponseValidationError is the validation error returned
// by GetFundsInCollectionResponse.Validate if the designated constraints
// aren't met.
type GetFundsInCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundsInCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundsInCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundsInCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundsInCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundsInCollectionResponseValidationError) ErrorName() string {
	return "GetFundsInCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundsInCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundsInCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundsInCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundsInCollectionResponseValidationError{}

// Validate checks the field values on CollectionMutualFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectionMutualFundInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectionMutualFundInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectionMutualFundInfoMultiError, or nil if none found.
func (m *CollectionMutualFundInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectionMutualFundInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CollectionId

	// no validation rules for MutualFundId

	// no validation rules for Amc

	if all {
		switch v := interface{}(m.GetNameData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "NameData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "NameData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "NameData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OptionType

	// no validation rules for AssetClass

	// no validation rules for CategoryName

	// no validation rules for FundhouseDefinedRiskLevel

	if all {
		switch v := interface{}(m.GetReturns()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "Returns",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "Returns",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturns()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "Returns",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerformanceMetrics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "PerformanceMetrics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "PerformanceMetrics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerformanceMetrics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "PerformanceMetrics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFundFundamentalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "FundFundamentalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "FundFundamentalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFundFundamentalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "FundFundamentalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "FiContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "FiContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "FiContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCollectionFundDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "CollectionFundDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "CollectionFundDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollectionFundDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "CollectionFundDisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanType

	if all {
		switch v := interface{}(m.GetNav()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "Nav",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "Nav",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNav()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "Nav",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "TxnConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionMutualFundInfoValidationError{
					field:  "TxnConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionMutualFundInfoValidationError{
				field:  "TxnConstraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComputedMinSipAmount

	if len(errors) > 0 {
		return CollectionMutualFundInfoMultiError(errors)
	}

	return nil
}

// CollectionMutualFundInfoMultiError is an error wrapping multiple validation
// errors returned by CollectionMutualFundInfo.ValidateAll() if the designated
// constraints aren't met.
type CollectionMutualFundInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectionMutualFundInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectionMutualFundInfoMultiError) AllErrors() []error { return m }

// CollectionMutualFundInfoValidationError is the validation error returned by
// CollectionMutualFundInfo.Validate if the designated constraints aren't met.
type CollectionMutualFundInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectionMutualFundInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectionMutualFundInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectionMutualFundInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectionMutualFundInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectionMutualFundInfoValidationError) ErrorName() string {
	return "CollectionMutualFundInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CollectionMutualFundInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectionMutualFundInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectionMutualFundInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectionMutualFundInfoValidationError{}

// Validate checks the field values on RecentMutualFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecentMutualFundInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecentMutualFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecentMutualFundInfoMultiError, or nil if none found.
func (m *RecentMutualFundInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RecentMutualFundInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLastViewedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "LastViewedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "LastViewedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastViewedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "LastViewedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MutualFundId

	// no validation rules for Amc

	if all {
		switch v := interface{}(m.GetNameData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "NameData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "NameData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "NameData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OptionType

	// no validation rules for AssetClass

	// no validation rules for CategoryName

	// no validation rules for FundhouseDefinedRiskLevel

	if all {
		switch v := interface{}(m.GetReturns()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "Returns",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "Returns",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturns()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "Returns",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerformanceMetrics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "PerformanceMetrics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "PerformanceMetrics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerformanceMetrics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "PerformanceMetrics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFundFundamentalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "FundFundamentalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "FundFundamentalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFundFundamentalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "FundFundamentalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "FiContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "FiContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "FiContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanType

	if all {
		switch v := interface{}(m.GetTxnConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "TxnConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecentMutualFundInfoValidationError{
					field:  "TxnConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecentMutualFundInfoValidationError{
				field:  "TxnConstraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComputedMinSipAmount

	if len(errors) > 0 {
		return RecentMutualFundInfoMultiError(errors)
	}

	return nil
}

// RecentMutualFundInfoMultiError is an error wrapping multiple validation
// errors returned by RecentMutualFundInfo.ValidateAll() if the designated
// constraints aren't met.
type RecentMutualFundInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecentMutualFundInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecentMutualFundInfoMultiError) AllErrors() []error { return m }

// RecentMutualFundInfoValidationError is the validation error returned by
// RecentMutualFundInfo.Validate if the designated constraints aren't met.
type RecentMutualFundInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecentMutualFundInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecentMutualFundInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecentMutualFundInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecentMutualFundInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecentMutualFundInfoValidationError) ErrorName() string {
	return "RecentMutualFundInfoValidationError"
}

// Error satisfies the builtin error interface
func (e RecentMutualFundInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecentMutualFundInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecentMutualFundInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecentMutualFundInfoValidationError{}

// Validate checks the field values on GetWatchListedFundsForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWatchListedFundsForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWatchListedFundsForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetWatchListedFundsForActorRequestMultiError, or nil if none found.
func (m *GetWatchListedFundsForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWatchListedFundsForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWatchListedFundsForActorRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWatchListedFundsForActorRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWatchListedFundsForActorRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortParam

	// no validation rules for SortOrder

	// no validation rules for ActorId

	// no validation rules for WatchListId

	// no validation rules for AppPlatform

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return GetWatchListedFundsForActorRequestMultiError(errors)
	}

	return nil
}

// GetWatchListedFundsForActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetWatchListedFundsForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetWatchListedFundsForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWatchListedFundsForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWatchListedFundsForActorRequestMultiError) AllErrors() []error { return m }

// GetWatchListedFundsForActorRequestValidationError is the validation error
// returned by GetWatchListedFundsForActorRequest.Validate if the designated
// constraints aren't met.
type GetWatchListedFundsForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWatchListedFundsForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWatchListedFundsForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWatchListedFundsForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWatchListedFundsForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWatchListedFundsForActorRequestValidationError) ErrorName() string {
	return "GetWatchListedFundsForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWatchListedFundsForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWatchListedFundsForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWatchListedFundsForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWatchListedFundsForActorRequestValidationError{}

// Validate checks the field values on GetWatchListedFundsForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWatchListedFundsForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWatchListedFundsForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetWatchListedFundsForActorResponseMultiError, or nil if none found.
func (m *GetWatchListedFundsForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWatchListedFundsForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWatchListedFundsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWatchListedFundsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWatchListedFundsForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWatchListedFundsForActorResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWatchListedFundsForActorResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWatchListedFundsForActorResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFundInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetWatchListedFundsForActorResponseValidationError{
						field:  fmt.Sprintf("FundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetWatchListedFundsForActorResponseValidationError{
						field:  fmt.Sprintf("FundInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetWatchListedFundsForActorResponseValidationError{
					field:  fmt.Sprintf("FundInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalFunds

	if len(errors) > 0 {
		return GetWatchListedFundsForActorResponseMultiError(errors)
	}

	return nil
}

// GetWatchListedFundsForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetWatchListedFundsForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetWatchListedFundsForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWatchListedFundsForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWatchListedFundsForActorResponseMultiError) AllErrors() []error { return m }

// GetWatchListedFundsForActorResponseValidationError is the validation error
// returned by GetWatchListedFundsForActorResponse.Validate if the designated
// constraints aren't met.
type GetWatchListedFundsForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWatchListedFundsForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWatchListedFundsForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWatchListedFundsForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWatchListedFundsForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWatchListedFundsForActorResponseValidationError) ErrorName() string {
	return "GetWatchListedFundsForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWatchListedFundsForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWatchListedFundsForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWatchListedFundsForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWatchListedFundsForActorResponseValidationError{}

// Validate checks the field values on WatchListedMutualFundInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WatchListedMutualFundInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchListedMutualFundInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchListedMutualFundInfoMultiError, or nil if none found.
func (m *WatchListedMutualFundInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchListedMutualFundInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WatchListId

	// no validation rules for MutualFundId

	// no validation rules for Amc

	if all {
		switch v := interface{}(m.GetNameData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "NameData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "NameData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "NameData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OptionType

	// no validation rules for AssetClass

	// no validation rules for CategoryName

	// no validation rules for FundhouseDefinedRiskLevel

	if all {
		switch v := interface{}(m.GetReturns()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "Returns",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "Returns",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturns()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "Returns",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerformanceMetrics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "PerformanceMetrics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "PerformanceMetrics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerformanceMetrics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "PerformanceMetrics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFundFundamentalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "FundFundamentalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "FundFundamentalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFundFundamentalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "FundFundamentalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "FiContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "FiContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "FiContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanType

	if all {
		switch v := interface{}(m.GetDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "DisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNav()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "Nav",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "Nav",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNav()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "Nav",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "TxnConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchListedMutualFundInfoValidationError{
					field:  "TxnConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchListedMutualFundInfoValidationError{
				field:  "TxnConstraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComputedMinSipAmount

	if len(errors) > 0 {
		return WatchListedMutualFundInfoMultiError(errors)
	}

	return nil
}

// WatchListedMutualFundInfoMultiError is an error wrapping multiple validation
// errors returned by WatchListedMutualFundInfo.ValidateAll() if the
// designated constraints aren't met.
type WatchListedMutualFundInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchListedMutualFundInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchListedMutualFundInfoMultiError) AllErrors() []error { return m }

// WatchListedMutualFundInfoValidationError is the validation error returned by
// WatchListedMutualFundInfo.Validate if the designated constraints aren't met.
type WatchListedMutualFundInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchListedMutualFundInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchListedMutualFundInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchListedMutualFundInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchListedMutualFundInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchListedMutualFundInfoValidationError) ErrorName() string {
	return "WatchListedMutualFundInfoValidationError"
}

// Error satisfies the builtin error interface
func (e WatchListedMutualFundInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchListedMutualFundInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchListedMutualFundInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchListedMutualFundInfoValidationError{}

// Validate checks the field values on GetMutualFundCategoryAverageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMutualFundCategoryAverageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundCategoryAverageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMutualFundCategoryAverageRequestMultiError, or nil if none found.
func (m *GetMutualFundCategoryAverageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundCategoryAverageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Category

	if len(errors) > 0 {
		return GetMutualFundCategoryAverageRequestMultiError(errors)
	}

	return nil
}

// GetMutualFundCategoryAverageRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetMutualFundCategoryAverageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMutualFundCategoryAverageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundCategoryAverageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundCategoryAverageRequestMultiError) AllErrors() []error { return m }

// GetMutualFundCategoryAverageRequestValidationError is the validation error
// returned by GetMutualFundCategoryAverageRequest.Validate if the designated
// constraints aren't met.
type GetMutualFundCategoryAverageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundCategoryAverageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundCategoryAverageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundCategoryAverageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundCategoryAverageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundCategoryAverageRequestValidationError) ErrorName() string {
	return "GetMutualFundCategoryAverageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundCategoryAverageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundCategoryAverageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundCategoryAverageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundCategoryAverageRequestValidationError{}

// Validate checks the field values on GetMutualFundCategoryAverageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetMutualFundCategoryAverageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundCategoryAverageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMutualFundCategoryAverageResponseMultiError, or nil if none found.
func (m *GetMutualFundCategoryAverageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundCategoryAverageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundCategoryAverageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundCategoryAverageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundCategoryAverageResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCategoryAverage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundCategoryAverageResponseValidationError{
					field:  "CategoryAverage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundCategoryAverageResponseValidationError{
					field:  "CategoryAverage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCategoryAverage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundCategoryAverageResponseValidationError{
				field:  "CategoryAverage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMutualFundCategoryAverageResponseMultiError(errors)
	}

	return nil
}

// GetMutualFundCategoryAverageResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetMutualFundCategoryAverageResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMutualFundCategoryAverageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundCategoryAverageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundCategoryAverageResponseMultiError) AllErrors() []error { return m }

// GetMutualFundCategoryAverageResponseValidationError is the validation error
// returned by GetMutualFundCategoryAverageResponse.Validate if the designated
// constraints aren't met.
type GetMutualFundCategoryAverageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundCategoryAverageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundCategoryAverageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundCategoryAverageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundCategoryAverageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundCategoryAverageResponseValidationError) ErrorName() string {
	return "GetMutualFundCategoryAverageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundCategoryAverageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundCategoryAverageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundCategoryAverageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundCategoryAverageResponseValidationError{}

// Validate checks the field values on CreateMutualFundRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMutualFundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMutualFundRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMutualFundRequestMultiError, or nil if none found.
func (m *CreateMutualFundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMutualFundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFund()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMutualFundRequestValidationError{
					field:  "Fund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMutualFundRequestValidationError{
					field:  "Fund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFund()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMutualFundRequestValidationError{
				field:  "Fund",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateMutualFundRequestMultiError(errors)
	}

	return nil
}

// CreateMutualFundRequestMultiError is an error wrapping multiple validation
// errors returned by CreateMutualFundRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateMutualFundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMutualFundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMutualFundRequestMultiError) AllErrors() []error { return m }

// CreateMutualFundRequestValidationError is the validation error returned by
// CreateMutualFundRequest.Validate if the designated constraints aren't met.
type CreateMutualFundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMutualFundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMutualFundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMutualFundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMutualFundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMutualFundRequestValidationError) ErrorName() string {
	return "CreateMutualFundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMutualFundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMutualFundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMutualFundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMutualFundRequestValidationError{}

// Validate checks the field values on CreateMutualFundResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMutualFundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMutualFundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMutualFundResponseMultiError, or nil if none found.
func (m *CreateMutualFundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMutualFundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMutualFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMutualFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMutualFundResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFund()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMutualFundResponseValidationError{
					field:  "Fund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMutualFundResponseValidationError{
					field:  "Fund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFund()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMutualFundResponseValidationError{
				field:  "Fund",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateMutualFundResponseMultiError(errors)
	}

	return nil
}

// CreateMutualFundResponseMultiError is an error wrapping multiple validation
// errors returned by CreateMutualFundResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateMutualFundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMutualFundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMutualFundResponseMultiError) AllErrors() []error { return m }

// CreateMutualFundResponseValidationError is the validation error returned by
// CreateMutualFundResponse.Validate if the designated constraints aren't met.
type CreateMutualFundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMutualFundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMutualFundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMutualFundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMutualFundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMutualFundResponseValidationError) ErrorName() string {
	return "CreateMutualFundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMutualFundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMutualFundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMutualFundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMutualFundResponseValidationError{}

// Validate checks the field values on UpdateMutualFundRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMutualFundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMutualFundRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateMutualFundRequestMultiError, or nil if none found.
func (m *UpdateMutualFundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMutualFundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedFund()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMutualFundRequestValidationError{
					field:  "UpdatedFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMutualFundRequestValidationError{
					field:  "UpdatedFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedFund()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMutualFundRequestValidationError{
				field:  "UpdatedFund",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateMutualFundRequestMultiError(errors)
	}

	return nil
}

// UpdateMutualFundRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateMutualFundRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateMutualFundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMutualFundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMutualFundRequestMultiError) AllErrors() []error { return m }

// UpdateMutualFundRequestValidationError is the validation error returned by
// UpdateMutualFundRequest.Validate if the designated constraints aren't met.
type UpdateMutualFundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMutualFundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMutualFundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMutualFundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMutualFundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMutualFundRequestValidationError) ErrorName() string {
	return "UpdateMutualFundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMutualFundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMutualFundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMutualFundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMutualFundRequestValidationError{}

// Validate checks the field values on UpdateMutualFundResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMutualFundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMutualFundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateMutualFundResponseMultiError, or nil if none found.
func (m *UpdateMutualFundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMutualFundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMutualFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMutualFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMutualFundResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateMutualFundResponseMultiError(errors)
	}

	return nil
}

// UpdateMutualFundResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateMutualFundResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateMutualFundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMutualFundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMutualFundResponseMultiError) AllErrors() []error { return m }

// UpdateMutualFundResponseValidationError is the validation error returned by
// UpdateMutualFundResponse.Validate if the designated constraints aren't met.
type UpdateMutualFundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMutualFundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMutualFundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMutualFundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMutualFundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMutualFundResponseValidationError) ErrorName() string {
	return "UpdateMutualFundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMutualFundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMutualFundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMutualFundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMutualFundResponseValidationError{}

// Validate checks the field values on GetMutualFundRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMutualFundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMutualFundRequestMultiError, or nil if none found.
func (m *GetMutualFundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetMutualFundRequestMultiError(errors)
	}

	return nil
}

// GetMutualFundRequestMultiError is an error wrapping multiple validation
// errors returned by GetMutualFundRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMutualFundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundRequestMultiError) AllErrors() []error { return m }

// GetMutualFundRequestValidationError is the validation error returned by
// GetMutualFundRequest.Validate if the designated constraints aren't met.
type GetMutualFundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundRequestValidationError) ErrorName() string {
	return "GetMutualFundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundRequestValidationError{}

// Validate checks the field values on GetMutualFundResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMutualFundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMutualFundResponseMultiError, or nil if none found.
func (m *GetMutualFundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMutualFund()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundResponseValidationError{
					field:  "MutualFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundResponseValidationError{
					field:  "MutualFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMutualFund()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundResponseValidationError{
				field:  "MutualFund",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMutualFundResponseMultiError(errors)
	}

	return nil
}

// GetMutualFundResponseMultiError is an error wrapping multiple validation
// errors returned by GetMutualFundResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMutualFundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundResponseMultiError) AllErrors() []error { return m }

// GetMutualFundResponseValidationError is the validation error returned by
// GetMutualFundResponse.Validate if the designated constraints aren't met.
type GetMutualFundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundResponseValidationError) ErrorName() string {
	return "GetMutualFundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundResponseValidationError{}

// Validate checks the field values on GetMutualFundsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMutualFundsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMutualFundsRequestMultiError, or nil if none found.
func (m *GetMutualFundsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FundIdentifier

	if len(errors) > 0 {
		return GetMutualFundsRequestMultiError(errors)
	}

	return nil
}

// GetMutualFundsRequestMultiError is an error wrapping multiple validation
// errors returned by GetMutualFundsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMutualFundsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundsRequestMultiError) AllErrors() []error { return m }

// GetMutualFundsRequestValidationError is the validation error returned by
// GetMutualFundsRequest.Validate if the designated constraints aren't met.
type GetMutualFundsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundsRequestValidationError) ErrorName() string {
	return "GetMutualFundsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundsRequestValidationError{}

// Validate checks the field values on GetMutualFundsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMutualFundsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMutualFundsResponseMultiError, or nil if none found.
func (m *GetMutualFundsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetMutualFunds()))
		i := 0
		for key := range m.GetMutualFunds() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMutualFunds()[key]
			_ = val

			// no validation rules for MutualFunds[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetMutualFundsResponseValidationError{
							field:  fmt.Sprintf("MutualFunds[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetMutualFundsResponseValidationError{
							field:  fmt.Sprintf("MutualFunds[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetMutualFundsResponseValidationError{
						field:  fmt.Sprintf("MutualFunds[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetMutualFundsResponseMultiError(errors)
	}

	return nil
}

// GetMutualFundsResponseMultiError is an error wrapping multiple validation
// errors returned by GetMutualFundsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMutualFundsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundsResponseMultiError) AllErrors() []error { return m }

// GetMutualFundsResponseValidationError is the validation error returned by
// GetMutualFundsResponse.Validate if the designated constraints aren't met.
type GetMutualFundsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundsResponseValidationError) ErrorName() string {
	return "GetMutualFundsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundsResponseValidationError{}

// Validate checks the field values on GetPaginatedMutualFundByFilterRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaginatedMutualFundByFilterRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaginatedMutualFundByFilterRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaginatedMutualFundByFilterRequestMultiError, or nil if none found.
func (m *GetPaginatedMutualFundByFilterRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedMutualFundByFilterRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amc

	// no validation rules for MutualFundName

	// no validation rules for IsinNumber

	// no validation rules for AssetClass

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedMutualFundByFilterRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedMutualFundByFilterRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedMutualFundByFilterRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatus

	// no validation rules for SortParam

	// no validation rules for SortOrder

	// no validation rules for ActorId

	// no validation rules for AppPlatform

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return GetPaginatedMutualFundByFilterRequestMultiError(errors)
	}

	return nil
}

// GetPaginatedMutualFundByFilterRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedMutualFundByFilterRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedMutualFundByFilterRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedMutualFundByFilterRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedMutualFundByFilterRequestMultiError) AllErrors() []error { return m }

// GetPaginatedMutualFundByFilterRequestValidationError is the validation error
// returned by GetPaginatedMutualFundByFilterRequest.Validate if the
// designated constraints aren't met.
type GetPaginatedMutualFundByFilterRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedMutualFundByFilterRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedMutualFundByFilterRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedMutualFundByFilterRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedMutualFundByFilterRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedMutualFundByFilterRequestValidationError) ErrorName() string {
	return "GetPaginatedMutualFundByFilterRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedMutualFundByFilterRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedMutualFundByFilterRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedMutualFundByFilterRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedMutualFundByFilterRequestValidationError{}

// Validate checks the field values on GetPaginatedMutualFundByFilterResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaginatedMutualFundByFilterResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPaginatedMutualFundByFilterResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetPaginatedMutualFundByFilterResponseMultiError, or nil if none found.
func (m *GetPaginatedMutualFundByFilterResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedMutualFundByFilterResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedMutualFundByFilterResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedMutualFundByFilterResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedMutualFundByFilterResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMutualFunds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaginatedMutualFundByFilterResponseValidationError{
						field:  fmt.Sprintf("MutualFunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaginatedMutualFundByFilterResponseValidationError{
						field:  fmt.Sprintf("MutualFunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaginatedMutualFundByFilterResponseValidationError{
					field:  fmt.Sprintf("MutualFunds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedMutualFundByFilterResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedMutualFundByFilterResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedMutualFundByFilterResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalFunds

	if len(errors) > 0 {
		return GetPaginatedMutualFundByFilterResponseMultiError(errors)
	}

	return nil
}

// GetPaginatedMutualFundByFilterResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedMutualFundByFilterResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedMutualFundByFilterResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedMutualFundByFilterResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedMutualFundByFilterResponseMultiError) AllErrors() []error { return m }

// GetPaginatedMutualFundByFilterResponseValidationError is the validation
// error returned by GetPaginatedMutualFundByFilterResponse.Validate if the
// designated constraints aren't met.
type GetPaginatedMutualFundByFilterResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedMutualFundByFilterResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedMutualFundByFilterResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedMutualFundByFilterResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedMutualFundByFilterResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedMutualFundByFilterResponseValidationError) ErrorName() string {
	return "GetPaginatedMutualFundByFilterResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedMutualFundByFilterResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedMutualFundByFilterResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedMutualFundByFilterResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedMutualFundByFilterResponseValidationError{}

// Validate checks the field values on CreateAmcInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAmcInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAmcInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAmcInfoRequestMultiError, or nil if none found.
func (m *CreateAmcInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAmcInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAmcInfoRequestValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAmcInfoRequestValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAmcInfoRequestValidationError{
				field:  "AmcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAmcInfoRequestMultiError(errors)
	}

	return nil
}

// CreateAmcInfoRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAmcInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAmcInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAmcInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAmcInfoRequestMultiError) AllErrors() []error { return m }

// CreateAmcInfoRequestValidationError is the validation error returned by
// CreateAmcInfoRequest.Validate if the designated constraints aren't met.
type CreateAmcInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAmcInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAmcInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAmcInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAmcInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAmcInfoRequestValidationError) ErrorName() string {
	return "CreateAmcInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAmcInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAmcInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAmcInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAmcInfoRequestValidationError{}

// Validate checks the field values on CreateAmcInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAmcInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAmcInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAmcInfoResponseMultiError, or nil if none found.
func (m *CreateAmcInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAmcInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAmcInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAmcInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAmcInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAmcInfoResponseValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAmcInfoResponseValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAmcInfoResponseValidationError{
				field:  "AmcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAmcInfoResponseMultiError(errors)
	}

	return nil
}

// CreateAmcInfoResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAmcInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAmcInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAmcInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAmcInfoResponseMultiError) AllErrors() []error { return m }

// CreateAmcInfoResponseValidationError is the validation error returned by
// CreateAmcInfoResponse.Validate if the designated constraints aren't met.
type CreateAmcInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAmcInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAmcInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAmcInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAmcInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAmcInfoResponseValidationError) ErrorName() string {
	return "CreateAmcInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAmcInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAmcInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAmcInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAmcInfoResponseValidationError{}

// Validate checks the field values on UpdateAmcInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAmcInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAmcInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAmcInfoRequestMultiError, or nil if none found.
func (m *UpdateAmcInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAmcInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedAmcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAmcInfoRequestValidationError{
					field:  "UpdatedAmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAmcInfoRequestValidationError{
					field:  "UpdatedAmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAmcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAmcInfoRequestValidationError{
				field:  "UpdatedAmcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAmcInfoRequestMultiError(errors)
	}

	return nil
}

// UpdateAmcInfoRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAmcInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAmcInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAmcInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAmcInfoRequestMultiError) AllErrors() []error { return m }

// UpdateAmcInfoRequestValidationError is the validation error returned by
// UpdateAmcInfoRequest.Validate if the designated constraints aren't met.
type UpdateAmcInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAmcInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAmcInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAmcInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAmcInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAmcInfoRequestValidationError) ErrorName() string {
	return "UpdateAmcInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAmcInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAmcInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAmcInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAmcInfoRequestValidationError{}

// Validate checks the field values on UpdateAmcInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAmcInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAmcInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAmcInfoResponseMultiError, or nil if none found.
func (m *UpdateAmcInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAmcInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAmcInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAmcInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAmcInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAmcInfoResponseMultiError(errors)
	}

	return nil
}

// UpdateAmcInfoResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateAmcInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAmcInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAmcInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAmcInfoResponseMultiError) AllErrors() []error { return m }

// UpdateAmcInfoResponseValidationError is the validation error returned by
// UpdateAmcInfoResponse.Validate if the designated constraints aren't met.
type UpdateAmcInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAmcInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAmcInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAmcInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAmcInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAmcInfoResponseValidationError) ErrorName() string {
	return "UpdateAmcInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAmcInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAmcInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAmcInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAmcInfoResponseValidationError{}

// Validate checks the field values on GetAmcInfoByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcInfoByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcInfoByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcInfoByIdRequestMultiError, or nil if none found.
func (m *GetAmcInfoByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcInfoByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAmcInfoByIdRequestMultiError(errors)
	}

	return nil
}

// GetAmcInfoByIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetAmcInfoByIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAmcInfoByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcInfoByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcInfoByIdRequestMultiError) AllErrors() []error { return m }

// GetAmcInfoByIdRequestValidationError is the validation error returned by
// GetAmcInfoByIdRequest.Validate if the designated constraints aren't met.
type GetAmcInfoByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcInfoByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcInfoByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcInfoByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcInfoByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcInfoByIdRequestValidationError) ErrorName() string {
	return "GetAmcInfoByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcInfoByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcInfoByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcInfoByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcInfoByIdRequestValidationError{}

// Validate checks the field values on GetAmcInfoByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcInfoByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcInfoByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcInfoByIdResponseMultiError, or nil if none found.
func (m *GetAmcInfoByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcInfoByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcInfoByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcInfoByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcInfoByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcInfoByIdResponseValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcInfoByIdResponseValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcInfoByIdResponseValidationError{
				field:  "AmcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAmcInfoByIdResponseMultiError(errors)
	}

	return nil
}

// GetAmcInfoByIdResponseMultiError is an error wrapping multiple validation
// errors returned by GetAmcInfoByIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAmcInfoByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcInfoByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcInfoByIdResponseMultiError) AllErrors() []error { return m }

// GetAmcInfoByIdResponseValidationError is the validation error returned by
// GetAmcInfoByIdResponse.Validate if the designated constraints aren't met.
type GetAmcInfoByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcInfoByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcInfoByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcInfoByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcInfoByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcInfoByIdResponseValidationError) ErrorName() string {
	return "GetAmcInfoByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcInfoByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcInfoByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcInfoByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcInfoByIdResponseValidationError{}

// Validate checks the field values on UpdateMutualFundByISINRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMutualFundByISINRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMutualFundByISINRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateMutualFundByISINRequestMultiError, or nil if none found.
func (m *UpdateMutualFundByISINRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMutualFundByISINRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedFund()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMutualFundByISINRequestValidationError{
					field:  "UpdatedFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMutualFundByISINRequestValidationError{
					field:  "UpdatedFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedFund()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMutualFundByISINRequestValidationError{
				field:  "UpdatedFund",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateMutualFundByISINRequestMultiError(errors)
	}

	return nil
}

// UpdateMutualFundByISINRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateMutualFundByISINRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateMutualFundByISINRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMutualFundByISINRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMutualFundByISINRequestMultiError) AllErrors() []error { return m }

// UpdateMutualFundByISINRequestValidationError is the validation error
// returned by UpdateMutualFundByISINRequest.Validate if the designated
// constraints aren't met.
type UpdateMutualFundByISINRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMutualFundByISINRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMutualFundByISINRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMutualFundByISINRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMutualFundByISINRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMutualFundByISINRequestValidationError) ErrorName() string {
	return "UpdateMutualFundByISINRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMutualFundByISINRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMutualFundByISINRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMutualFundByISINRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMutualFundByISINRequestValidationError{}

// Validate checks the field values on UpdateMutualFundByISINResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMutualFundByISINResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMutualFundByISINResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateMutualFundByISINResponseMultiError, or nil if none found.
func (m *UpdateMutualFundByISINResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMutualFundByISINResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMutualFundByISINResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMutualFundByISINResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMutualFundByISINResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateMutualFundByISINResponseMultiError(errors)
	}

	return nil
}

// UpdateMutualFundByISINResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateMutualFundByISINResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateMutualFundByISINResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMutualFundByISINResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMutualFundByISINResponseMultiError) AllErrors() []error { return m }

// UpdateMutualFundByISINResponseValidationError is the validation error
// returned by UpdateMutualFundByISINResponse.Validate if the designated
// constraints aren't met.
type UpdateMutualFundByISINResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMutualFundByISINResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMutualFundByISINResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMutualFundByISINResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMutualFundByISINResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMutualFundByISINResponseValidationError) ErrorName() string {
	return "UpdateMutualFundByISINResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMutualFundByISINResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMutualFundByISINResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMutualFundByISINResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMutualFundByISINResponseValidationError{}

// Validate checks the field values on GetMutualFundByISINRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMutualFundByISINRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundByISINRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMutualFundByISINRequestMultiError, or nil if none found.
func (m *GetMutualFundByISINRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundByISINRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Isin

	if len(errors) > 0 {
		return GetMutualFundByISINRequestMultiError(errors)
	}

	return nil
}

// GetMutualFundByISINRequestMultiError is an error wrapping multiple
// validation errors returned by GetMutualFundByISINRequest.ValidateAll() if
// the designated constraints aren't met.
type GetMutualFundByISINRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundByISINRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundByISINRequestMultiError) AllErrors() []error { return m }

// GetMutualFundByISINRequestValidationError is the validation error returned
// by GetMutualFundByISINRequest.Validate if the designated constraints aren't met.
type GetMutualFundByISINRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundByISINRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundByISINRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundByISINRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundByISINRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundByISINRequestValidationError) ErrorName() string {
	return "GetMutualFundByISINRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundByISINRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundByISINRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundByISINRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundByISINRequestValidationError{}

// Validate checks the field values on GetMutualFundByISINResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMutualFundByISINResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMutualFundByISINResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMutualFundByISINResponseMultiError, or nil if none found.
func (m *GetMutualFundByISINResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMutualFundByISINResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundByISINResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundByISINResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundByISINResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMutualFund()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMutualFundByISINResponseValidationError{
					field:  "MutualFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMutualFundByISINResponseValidationError{
					field:  "MutualFund",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMutualFund()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMutualFundByISINResponseValidationError{
				field:  "MutualFund",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMutualFundByISINResponseMultiError(errors)
	}

	return nil
}

// GetMutualFundByISINResponseMultiError is an error wrapping multiple
// validation errors returned by GetMutualFundByISINResponse.ValidateAll() if
// the designated constraints aren't met.
type GetMutualFundByISINResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMutualFundByISINResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMutualFundByISINResponseMultiError) AllErrors() []error { return m }

// GetMutualFundByISINResponseValidationError is the validation error returned
// by GetMutualFundByISINResponse.Validate if the designated constraints
// aren't met.
type GetMutualFundByISINResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMutualFundByISINResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMutualFundByISINResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMutualFundByISINResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMutualFundByISINResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMutualFundByISINResponseValidationError) ErrorName() string {
	return "GetMutualFundByISINResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMutualFundByISINResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMutualFundByISINResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMutualFundByISINResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMutualFundByISINResponseValidationError{}

// Validate checks the field values on GetAmcInfoByAMCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcInfoByAMCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcInfoByAMCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcInfoByAMCRequestMultiError, or nil if none found.
func (m *GetAmcInfoByAMCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcInfoByAMCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amc

	if len(errors) > 0 {
		return GetAmcInfoByAMCRequestMultiError(errors)
	}

	return nil
}

// GetAmcInfoByAMCRequestMultiError is an error wrapping multiple validation
// errors returned by GetAmcInfoByAMCRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAmcInfoByAMCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcInfoByAMCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcInfoByAMCRequestMultiError) AllErrors() []error { return m }

// GetAmcInfoByAMCRequestValidationError is the validation error returned by
// GetAmcInfoByAMCRequest.Validate if the designated constraints aren't met.
type GetAmcInfoByAMCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcInfoByAMCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcInfoByAMCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcInfoByAMCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcInfoByAMCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcInfoByAMCRequestValidationError) ErrorName() string {
	return "GetAmcInfoByAMCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcInfoByAMCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcInfoByAMCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcInfoByAMCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcInfoByAMCRequestValidationError{}

// Validate checks the field values on GetAmcInfoByAMCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcInfoByAMCResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcInfoByAMCResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcInfoByAMCResponseMultiError, or nil if none found.
func (m *GetAmcInfoByAMCResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcInfoByAMCResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcInfoByAMCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcInfoByAMCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcInfoByAMCResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcInfoByAMCResponseValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcInfoByAMCResponseValidationError{
					field:  "AmcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcInfoByAMCResponseValidationError{
				field:  "AmcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAmcInfoByAMCResponseMultiError(errors)
	}

	return nil
}

// GetAmcInfoByAMCResponseMultiError is an error wrapping multiple validation
// errors returned by GetAmcInfoByAMCResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAmcInfoByAMCResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcInfoByAMCResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcInfoByAMCResponseMultiError) AllErrors() []error { return m }

// GetAmcInfoByAMCResponseValidationError is the validation error returned by
// GetAmcInfoByAMCResponse.Validate if the designated constraints aren't met.
type GetAmcInfoByAMCResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcInfoByAMCResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcInfoByAMCResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcInfoByAMCResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcInfoByAMCResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcInfoByAMCResponseValidationError) ErrorName() string {
	return "GetAmcInfoByAMCResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcInfoByAMCResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcInfoByAMCResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcInfoByAMCResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcInfoByAMCResponseValidationError{}

// Validate checks the field values on GetInvestmentSummaryInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentSummaryInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryInfoRequestMultiError, or nil if none found.
func (m *GetInvestmentSummaryInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetInvestmentSummaryInfoRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentSummaryInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type GetInvestmentSummaryInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryInfoRequestMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryInfoRequestValidationError is the validation error
// returned by GetInvestmentSummaryInfoRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentSummaryInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryInfoRequestValidationError) ErrorName() string {
	return "GetInvestmentSummaryInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryInfoRequestValidationError{}

// Validate checks the field values on GetInvestmentSummaryInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentSummaryInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryInfoResponseMultiError, or nil if none found.
func (m *GetInvestmentSummaryInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryInfoResponseValidationError{
					field:  "InvestmentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryInfoResponseValidationError{
					field:  "InvestmentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryInfoResponseValidationError{
				field:  "InvestmentSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentSummaryInfoResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryInfoResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentSummaryInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentSummaryInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryInfoResponseMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryInfoResponseValidationError is the validation error
// returned by GetInvestmentSummaryInfoResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentSummaryInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryInfoResponseValidationError) ErrorName() string {
	return "GetInvestmentSummaryInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryInfoResponseValidationError{}

// Validate checks the field values on GetInvestmentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentsRequestMultiError, or nil if none found.
func (m *GetInvestmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentsRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentsRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentsRequestValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetInvestmentsRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentsRequestMultiError is an error wrapping multiple validation
// errors returned by GetInvestmentsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentsRequestMultiError) AllErrors() []error { return m }

// GetInvestmentsRequestValidationError is the validation error returned by
// GetInvestmentsRequest.Validate if the designated constraints aren't met.
type GetInvestmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentsRequestValidationError) ErrorName() string {
	return "GetInvestmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentsRequestValidationError{}

// Validate checks the field values on Filter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Filter with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FilterMultiError, or nil if none found.
func (m *Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Comparator

	// no validation rules for FilterField

	switch v := m.FilterValue.(type) {
	case *Filter_NumericVal:
		if v == nil {
			err := FilterValidationError{
				field:  "FilterValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for NumericVal
	case *Filter_StringVal:
		if v == nil {
			err := FilterValidationError{
				field:  "FilterValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for StringVal
	case *Filter_TimeVal:
		if v == nil {
			err := FilterValidationError{
				field:  "FilterValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTimeVal()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FilterValidationError{
						field:  "TimeVal",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FilterValidationError{
						field:  "TimeVal",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTimeVal()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FilterValidationError{
					field:  "TimeVal",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Filter_AmcVal:
		if v == nil {
			err := FilterValidationError{
				field:  "FilterValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AmcVal
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FilterMultiError(errors)
	}

	return nil
}

// FilterMultiError is an error wrapping multiple validation errors returned by
// Filter.ValidateAll() if the designated constraints aren't met.
type FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilterMultiError) AllErrors() []error { return m }

// FilterValidationError is the validation error returned by Filter.Validate if
// the designated constraints aren't met.
type FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilterValidationError) ErrorName() string { return "FilterValidationError" }

// Error satisfies the builtin error interface
func (e FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilterValidationError{}

// Validate checks the field values on GetInvestmentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentsResponseMultiError, or nil if none found.
func (m *GetInvestmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMutualFundsInvestmentInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentsResponseValidationError{
						field:  fmt.Sprintf("MutualFundsInvestmentInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentsResponseValidationError{
						field:  fmt.Sprintf("MutualFundsInvestmentInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentsResponseValidationError{
					field:  fmt.Sprintf("MutualFundsInvestmentInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentsResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentsResponseMultiError is an error wrapping multiple validation
// errors returned by GetInvestmentsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentsResponseMultiError) AllErrors() []error { return m }

// GetInvestmentsResponseValidationError is the validation error returned by
// GetInvestmentsResponse.Validate if the designated constraints aren't met.
type GetInvestmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentsResponseValidationError) ErrorName() string {
	return "GetInvestmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentsResponseValidationError{}

// Validate checks the field values on InvestmentSummaryInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentSummaryInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentSummaryInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentSummaryInfoMultiError, or nil if none found.
func (m *InvestmentSummaryInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentSummaryInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "TotalInvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "TotalInvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "TotalInvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GrowthPercentage

	if all {
		switch v := interface{}(m.GetNavUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "NavUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "NavUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNavUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "NavUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PendingOrdersPresent

	if all {
		switch v := interface{}(m.GetLatestPendingOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "LatestPendingOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "LatestPendingOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestPendingOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "LatestPendingOrder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BalancedUnits

	if all {
		switch v := interface{}(m.GetLatestFolioUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "LatestFolioUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "LatestFolioUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestFolioUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "LatestFolioUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLatestOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "LatestOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "LatestOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "LatestOrder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInProgressBuyOrderAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "InProgressBuyOrderAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryInfoValidationError{
					field:  "InProgressBuyOrderAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInProgressBuyOrderAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryInfoValidationError{
				field:  "InProgressBuyOrderAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PendingOrdersCount

	// no validation rules for HasInvestment

	if len(errors) > 0 {
		return InvestmentSummaryInfoMultiError(errors)
	}

	return nil
}

// InvestmentSummaryInfoMultiError is an error wrapping multiple validation
// errors returned by InvestmentSummaryInfo.ValidateAll() if the designated
// constraints aren't met.
type InvestmentSummaryInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentSummaryInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentSummaryInfoMultiError) AllErrors() []error { return m }

// InvestmentSummaryInfoValidationError is the validation error returned by
// InvestmentSummaryInfo.Validate if the designated constraints aren't met.
type InvestmentSummaryInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentSummaryInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentSummaryInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentSummaryInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentSummaryInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentSummaryInfoValidationError) ErrorName() string {
	return "InvestmentSummaryInfoValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentSummaryInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentSummaryInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentSummaryInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentSummaryInfoValidationError{}

// Validate checks the field values on MutualFundInvestmentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MutualFundInvestmentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MutualFundInvestmentInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MutualFundInvestmentInfoMultiError, or nil if none found.
func (m *MutualFundInvestmentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MutualFundInvestmentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	// no validation rules for MutualFundName

	// no validation rules for Amc

	// no validation rules for AssetClass

	if all {
		switch v := interface{}(m.GetInvestmentSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MutualFundInvestmentInfoValidationError{
					field:  "InvestmentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MutualFundInvestmentInfoValidationError{
					field:  "InvestmentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MutualFundInvestmentInfoValidationError{
				field:  "InvestmentSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFolioUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MutualFundInvestmentInfoValidationError{
					field:  "FolioUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MutualFundInvestmentInfoValidationError{
					field:  "FolioUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFolioUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MutualFundInvestmentInfoValidationError{
				field:  "FolioUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalFundStatus

	if all {
		switch v := interface{}(m.GetLastTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MutualFundInvestmentInfoValidationError{
					field:  "LastTransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MutualFundInvestmentInfoValidationError{
					field:  "LastTransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MutualFundInvestmentInfoValidationError{
				field:  "LastTransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CategoryName

	if len(errors) > 0 {
		return MutualFundInvestmentInfoMultiError(errors)
	}

	return nil
}

// MutualFundInvestmentInfoMultiError is an error wrapping multiple validation
// errors returned by MutualFundInvestmentInfo.ValidateAll() if the designated
// constraints aren't met.
type MutualFundInvestmentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MutualFundInvestmentInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MutualFundInvestmentInfoMultiError) AllErrors() []error { return m }

// MutualFundInvestmentInfoValidationError is the validation error returned by
// MutualFundInvestmentInfo.Validate if the designated constraints aren't met.
type MutualFundInvestmentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MutualFundInvestmentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MutualFundInvestmentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MutualFundInvestmentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MutualFundInvestmentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MutualFundInvestmentInfoValidationError) ErrorName() string {
	return "MutualFundInvestmentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MutualFundInvestmentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMutualFundInvestmentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MutualFundInvestmentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MutualFundInvestmentInfoValidationError{}

// Validate checks the field values on GetInvestmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentRequestMultiError, or nil if none found.
func (m *GetInvestmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetInvestmentRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentRequestMultiError is an error wrapping multiple validation
// errors returned by GetInvestmentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRequestMultiError) AllErrors() []error { return m }

// GetInvestmentRequestValidationError is the validation error returned by
// GetInvestmentRequest.Validate if the designated constraints aren't met.
type GetInvestmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRequestValidationError) ErrorName() string {
	return "GetInvestmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRequestValidationError{}

// Validate checks the field values on GetInvestmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentResponseMultiError, or nil if none found.
func (m *GetInvestmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentResponseValidationError{
					field:  "InvestmentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentResponseValidationError{
					field:  "InvestmentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentResponseValidationError{
				field:  "InvestmentSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentResponseMultiError is an error wrapping multiple validation
// errors returned by GetInvestmentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentResponseMultiError) AllErrors() []error { return m }

// GetInvestmentResponseValidationError is the validation error returned by
// GetInvestmentResponse.Validate if the designated constraints aren't met.
type GetInvestmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentResponseValidationError) ErrorName() string {
	return "GetInvestmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentResponseValidationError{}

// Validate checks the field values on GetFolioDetailsOutput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioDetailsOutput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioDetailsOutput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolioDetailsOutputMultiError, or nil if none found.
func (m *GetFolioDetailsOutput) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioDetailsOutput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFolioLedger()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolioDetailsOutputValidationError{
					field:  "FolioLedger",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolioDetailsOutputValidationError{
					field:  "FolioLedger",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFolioLedger()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolioDetailsOutputValidationError{
				field:  "FolioLedger",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if len(errors) > 0 {
		return GetFolioDetailsOutputMultiError(errors)
	}

	return nil
}

// GetFolioDetailsOutputMultiError is an error wrapping multiple validation
// errors returned by GetFolioDetailsOutput.ValidateAll() if the designated
// constraints aren't met.
type GetFolioDetailsOutputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioDetailsOutputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioDetailsOutputMultiError) AllErrors() []error { return m }

// GetFolioDetailsOutputValidationError is the validation error returned by
// GetFolioDetailsOutput.Validate if the designated constraints aren't met.
type GetFolioDetailsOutputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioDetailsOutputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioDetailsOutputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioDetailsOutputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioDetailsOutputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioDetailsOutputValidationError) ErrorName() string {
	return "GetFolioDetailsOutputValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioDetailsOutputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioDetailsOutput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioDetailsOutputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioDetailsOutputValidationError{}

// Validate checks the field values on GetFolioDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolioDetailsRequestMultiError, or nil if none found.
func (m *GetFolioDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amc

	switch v := m.MutualFundIdentifier.(type) {
	case *GetFolioDetailsRequest_SchemeCode:
		if v == nil {
			err := GetFolioDetailsRequestValidationError{
				field:  "MutualFundIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for SchemeCode
	case *GetFolioDetailsRequest_Id:
		if v == nil {
			err := GetFolioDetailsRequestValidationError{
				field:  "MutualFundIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetFolioDetailsRequestMultiError(errors)
	}

	return nil
}

// GetFolioDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetFolioDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFolioDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioDetailsRequestMultiError) AllErrors() []error { return m }

// GetFolioDetailsRequestValidationError is the validation error returned by
// GetFolioDetailsRequest.Validate if the designated constraints aren't met.
type GetFolioDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioDetailsRequestValidationError) ErrorName() string {
	return "GetFolioDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioDetailsRequestValidationError{}

// Validate checks the field values on GetFolioDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolioDetailsResponseMultiError, or nil if none found.
func (m *GetFolioDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolioDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolioDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolioDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFolioIdToDetailsMap()))
		i := 0
		for key := range m.GetFolioIdToDetailsMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFolioIdToDetailsMap()[key]
			_ = val

			// no validation rules for FolioIdToDetailsMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetFolioDetailsResponseValidationError{
							field:  fmt.Sprintf("FolioIdToDetailsMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetFolioDetailsResponseValidationError{
							field:  fmt.Sprintf("FolioIdToDetailsMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetFolioDetailsResponseValidationError{
						field:  fmt.Sprintf("FolioIdToDetailsMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetFolioDetailsResponseMultiError(errors)
	}

	return nil
}

// GetFolioDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetFolioDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFolioDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioDetailsResponseMultiError) AllErrors() []error { return m }

// GetFolioDetailsResponseValidationError is the validation error returned by
// GetFolioDetailsResponse.Validate if the designated constraints aren't met.
type GetFolioDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioDetailsResponseValidationError) ErrorName() string {
	return "GetFolioDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioDetailsResponseValidationError{}

// Validate checks the field values on UploadCatalogFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadCatalogFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadCatalogFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadCatalogFileRequestMultiError, or nil if none found.
func (m *UploadCatalogFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadCatalogFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for FileContent

	// no validation rules for FolderName

	if len(errors) > 0 {
		return UploadCatalogFileRequestMultiError(errors)
	}

	return nil
}

// UploadCatalogFileRequestMultiError is an error wrapping multiple validation
// errors returned by UploadCatalogFileRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadCatalogFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadCatalogFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadCatalogFileRequestMultiError) AllErrors() []error { return m }

// UploadCatalogFileRequestValidationError is the validation error returned by
// UploadCatalogFileRequest.Validate if the designated constraints aren't met.
type UploadCatalogFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadCatalogFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadCatalogFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadCatalogFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadCatalogFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadCatalogFileRequestValidationError) ErrorName() string {
	return "UploadCatalogFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadCatalogFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadCatalogFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadCatalogFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadCatalogFileRequestValidationError{}

// Validate checks the field values on UploadCatalogFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadCatalogFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadCatalogFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadCatalogFileResponseMultiError, or nil if none found.
func (m *UploadCatalogFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadCatalogFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadCatalogFileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadCatalogFileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadCatalogFileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadCatalogFileResponseMultiError(errors)
	}

	return nil
}

// UploadCatalogFileResponseMultiError is an error wrapping multiple validation
// errors returned by UploadCatalogFileResponse.ValidateAll() if the
// designated constraints aren't met.
type UploadCatalogFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadCatalogFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadCatalogFileResponseMultiError) AllErrors() []error { return m }

// UploadCatalogFileResponseValidationError is the validation error returned by
// UploadCatalogFileResponse.Validate if the designated constraints aren't met.
type UploadCatalogFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadCatalogFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadCatalogFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadCatalogFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadCatalogFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadCatalogFileResponseValidationError) ErrorName() string {
	return "UploadCatalogFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadCatalogFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadCatalogFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadCatalogFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadCatalogFileResponseValidationError{}

// Validate checks the field values on GetInvestmentSummaryForMfsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentSummaryForMfsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryForMfsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryForMfsRequestMultiError, or nil if none found.
func (m *GetInvestmentSummaryForMfsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryForMfsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetInvestmentSummaryForMfsRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryForMfsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentSummaryForMfsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentSummaryForMfsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryForMfsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryForMfsRequestMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryForMfsRequestValidationError is the validation error
// returned by GetInvestmentSummaryForMfsRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentSummaryForMfsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryForMfsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryForMfsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryForMfsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryForMfsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryForMfsRequestValidationError) ErrorName() string {
	return "GetInvestmentSummaryForMfsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryForMfsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryForMfsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryForMfsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryForMfsRequestValidationError{}

// Validate checks the field values on InvestmentSummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InvestmentSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentSummaryMultiError, or nil if none found.
func (m *InvestmentSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryValidationError{
					field:  "TotalInvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryValidationError{
					field:  "TotalInvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryValidationError{
				field:  "TotalInvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentSummaryValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentSummaryValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentSummaryValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GrowthPercentage

	// no validation rules for Invested

	if len(errors) > 0 {
		return InvestmentSummaryMultiError(errors)
	}

	return nil
}

// InvestmentSummaryMultiError is an error wrapping multiple validation errors
// returned by InvestmentSummary.ValidateAll() if the designated constraints
// aren't met.
type InvestmentSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentSummaryMultiError) AllErrors() []error { return m }

// InvestmentSummaryValidationError is the validation error returned by
// InvestmentSummary.Validate if the designated constraints aren't met.
type InvestmentSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentSummaryValidationError) ErrorName() string {
	return "InvestmentSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentSummaryValidationError{}

// Validate checks the field values on GetInvestmentSummaryForMfsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentSummaryForMfsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryForMfsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryForMfsResponseMultiError, or nil if none found.
func (m *GetInvestmentSummaryForMfsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryForMfsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryForMfsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryForMfsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryForMfsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetInvestmentSummaries()))
		i := 0
		for key := range m.GetInvestmentSummaries() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetInvestmentSummaries()[key]
			_ = val

			// no validation rules for InvestmentSummaries[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetInvestmentSummaryForMfsResponseValidationError{
							field:  fmt.Sprintf("InvestmentSummaries[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetInvestmentSummaryForMfsResponseValidationError{
							field:  fmt.Sprintf("InvestmentSummaries[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetInvestmentSummaryForMfsResponseValidationError{
						field:  fmt.Sprintf("InvestmentSummaries[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetInvestmentSummaryForMfsResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryForMfsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentSummaryForMfsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentSummaryForMfsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryForMfsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryForMfsResponseMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryForMfsResponseValidationError is the validation error
// returned by GetInvestmentSummaryForMfsResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentSummaryForMfsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryForMfsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryForMfsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryForMfsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryForMfsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryForMfsResponseValidationError) ErrorName() string {
	return "GetInvestmentSummaryForMfsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryForMfsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryForMfsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryForMfsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryForMfsResponseValidationError{}

// Validate checks the field values on CreateCollectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCollectionRequestMultiError, or nil if none found.
func (m *CreateCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCollection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCollectionRequestValidationError{
					field:  "Collection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCollectionRequestValidationError{
					field:  "Collection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCollectionRequestValidationError{
				field:  "Collection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCollectionRequestMultiError(errors)
	}

	return nil
}

// CreateCollectionRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCollectionRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCollectionRequestMultiError) AllErrors() []error { return m }

// CreateCollectionRequestValidationError is the validation error returned by
// CreateCollectionRequest.Validate if the designated constraints aren't met.
type CreateCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCollectionRequestValidationError) ErrorName() string {
	return "CreateCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCollectionRequestValidationError{}

// Validate checks the field values on CreateCollectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCollectionResponseMultiError, or nil if none found.
func (m *CreateCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCollectionResponseMultiError(errors)
	}

	return nil
}

// CreateCollectionResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCollectionResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCollectionResponseMultiError) AllErrors() []error { return m }

// CreateCollectionResponseValidationError is the validation error returned by
// CreateCollectionResponse.Validate if the designated constraints aren't met.
type CreateCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCollectionResponseValidationError) ErrorName() string {
	return "CreateCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCollectionResponseValidationError{}

// Validate checks the field values on UpdateCollectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCollectionRequestMultiError, or nil if none found.
func (m *UpdateCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCollection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCollectionRequestValidationError{
					field:  "Collection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCollectionRequestValidationError{
					field:  "Collection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCollectionRequestValidationError{
				field:  "Collection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCollectionRequestMultiError(errors)
	}

	return nil
}

// UpdateCollectionRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCollectionRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCollectionRequestMultiError) AllErrors() []error { return m }

// UpdateCollectionRequestValidationError is the validation error returned by
// UpdateCollectionRequest.Validate if the designated constraints aren't met.
type UpdateCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCollectionRequestValidationError) ErrorName() string {
	return "UpdateCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCollectionRequestValidationError{}

// Validate checks the field values on UpdateCollectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCollectionResponseMultiError, or nil if none found.
func (m *UpdateCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCollectionResponseMultiError(errors)
	}

	return nil
}

// UpdateCollectionResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCollectionResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCollectionResponseMultiError) AllErrors() []error { return m }

// UpdateCollectionResponseValidationError is the validation error returned by
// UpdateCollectionResponse.Validate if the designated constraints aren't met.
type UpdateCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCollectionResponseValidationError) ErrorName() string {
	return "UpdateCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCollectionResponseValidationError{}

// Validate checks the field values on AddFundToCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddFundToCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddFundToCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddFundToCollectionRequestMultiError, or nil if none found.
func (m *AddFundToCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddFundToCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	// no validation rules for CollectionId

	if all {
		switch v := interface{}(m.GetDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddFundToCollectionRequestValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddFundToCollectionRequestValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddFundToCollectionRequestValidationError{
				field:  "DisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddFundToCollectionRequestMultiError(errors)
	}

	return nil
}

// AddFundToCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by AddFundToCollectionRequest.ValidateAll() if
// the designated constraints aren't met.
type AddFundToCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddFundToCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddFundToCollectionRequestMultiError) AllErrors() []error { return m }

// AddFundToCollectionRequestValidationError is the validation error returned
// by AddFundToCollectionRequest.Validate if the designated constraints aren't met.
type AddFundToCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddFundToCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddFundToCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddFundToCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddFundToCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddFundToCollectionRequestValidationError) ErrorName() string {
	return "AddFundToCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddFundToCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddFundToCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddFundToCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddFundToCollectionRequestValidationError{}

// Validate checks the field values on AddFundToCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddFundToCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddFundToCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddFundToCollectionResponseMultiError, or nil if none found.
func (m *AddFundToCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddFundToCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddFundToCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddFundToCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddFundToCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddFundToCollectionResponseMultiError(errors)
	}

	return nil
}

// AddFundToCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by AddFundToCollectionResponse.ValidateAll() if
// the designated constraints aren't met.
type AddFundToCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddFundToCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddFundToCollectionResponseMultiError) AllErrors() []error { return m }

// AddFundToCollectionResponseValidationError is the validation error returned
// by AddFundToCollectionResponse.Validate if the designated constraints
// aren't met.
type AddFundToCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddFundToCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddFundToCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddFundToCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddFundToCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddFundToCollectionResponseValidationError) ErrorName() string {
	return "AddFundToCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddFundToCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddFundToCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddFundToCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddFundToCollectionResponseValidationError{}

// Validate checks the field values on UpdateFundInCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFundInCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFundInCollectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFundInCollectionRequestMultiError, or nil if none found.
func (m *UpdateFundInCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFundInCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	// no validation rules for CollectionId

	if all {
		switch v := interface{}(m.GetDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFundInCollectionRequestValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFundInCollectionRequestValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFundInCollectionRequestValidationError{
				field:  "DisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFundInCollectionRequestMultiError(errors)
	}

	return nil
}

// UpdateFundInCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateFundInCollectionRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateFundInCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFundInCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFundInCollectionRequestMultiError) AllErrors() []error { return m }

// UpdateFundInCollectionRequestValidationError is the validation error
// returned by UpdateFundInCollectionRequest.Validate if the designated
// constraints aren't met.
type UpdateFundInCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFundInCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFundInCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFundInCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFundInCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFundInCollectionRequestValidationError) ErrorName() string {
	return "UpdateFundInCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFundInCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFundInCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFundInCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFundInCollectionRequestValidationError{}

// Validate checks the field values on UpdateFundInCollectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFundInCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFundInCollectionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFundInCollectionResponseMultiError, or nil if none found.
func (m *UpdateFundInCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFundInCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFundInCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFundInCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFundInCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFundInCollectionResponseMultiError(errors)
	}

	return nil
}

// UpdateFundInCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateFundInCollectionResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateFundInCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFundInCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFundInCollectionResponseMultiError) AllErrors() []error { return m }

// UpdateFundInCollectionResponseValidationError is the validation error
// returned by UpdateFundInCollectionResponse.Validate if the designated
// constraints aren't met.
type UpdateFundInCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFundInCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFundInCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFundInCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFundInCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFundInCollectionResponseValidationError) ErrorName() string {
	return "UpdateFundInCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFundInCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFundInCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFundInCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFundInCollectionResponseValidationError{}

// Validate checks the field values on RemoveFundFromCollectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveFundFromCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveFundFromCollectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RemoveFundFromCollectionRequestMultiError, or nil if none found.
func (m *RemoveFundFromCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveFundFromCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	// no validation rules for CollectionId

	if len(errors) > 0 {
		return RemoveFundFromCollectionRequestMultiError(errors)
	}

	return nil
}

// RemoveFundFromCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by RemoveFundFromCollectionRequest.ValidateAll()
// if the designated constraints aren't met.
type RemoveFundFromCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveFundFromCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveFundFromCollectionRequestMultiError) AllErrors() []error { return m }

// RemoveFundFromCollectionRequestValidationError is the validation error
// returned by RemoveFundFromCollectionRequest.Validate if the designated
// constraints aren't met.
type RemoveFundFromCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveFundFromCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveFundFromCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveFundFromCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveFundFromCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveFundFromCollectionRequestValidationError) ErrorName() string {
	return "RemoveFundFromCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveFundFromCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveFundFromCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveFundFromCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveFundFromCollectionRequestValidationError{}

// Validate checks the field values on RemoveFundFromCollectionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RemoveFundFromCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveFundFromCollectionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RemoveFundFromCollectionResponseMultiError, or nil if none found.
func (m *RemoveFundFromCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveFundFromCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoveFundFromCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoveFundFromCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoveFundFromCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RemoveFundFromCollectionResponseMultiError(errors)
	}

	return nil
}

// RemoveFundFromCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by
// RemoveFundFromCollectionResponse.ValidateAll() if the designated
// constraints aren't met.
type RemoveFundFromCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveFundFromCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveFundFromCollectionResponseMultiError) AllErrors() []error { return m }

// RemoveFundFromCollectionResponseValidationError is the validation error
// returned by RemoveFundFromCollectionResponse.Validate if the designated
// constraints aren't met.
type RemoveFundFromCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveFundFromCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveFundFromCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveFundFromCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveFundFromCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveFundFromCollectionResponseValidationError) ErrorName() string {
	return "RemoveFundFromCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveFundFromCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveFundFromCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveFundFromCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveFundFromCollectionResponseValidationError{}

// Validate checks the field values on GetFolioLedgersByFilterRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioLedgersByFilterRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioLedgersByFilterRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFolioLedgersByFilterRequestMultiError, or nil if none found.
func (m *GetFolioLedgersByFilterRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioLedgersByFilterRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolioLedgersByFilterRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolioLedgersByFilterRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolioLedgersByFilterRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetFolioLedgersByFilterRequestMultiError(errors)
	}

	return nil
}

// GetFolioLedgersByFilterRequestMultiError is an error wrapping multiple
// validation errors returned by GetFolioLedgersByFilterRequest.ValidateAll()
// if the designated constraints aren't met.
type GetFolioLedgersByFilterRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioLedgersByFilterRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioLedgersByFilterRequestMultiError) AllErrors() []error { return m }

// GetFolioLedgersByFilterRequestValidationError is the validation error
// returned by GetFolioLedgersByFilterRequest.Validate if the designated
// constraints aren't met.
type GetFolioLedgersByFilterRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioLedgersByFilterRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioLedgersByFilterRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioLedgersByFilterRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioLedgersByFilterRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioLedgersByFilterRequestValidationError) ErrorName() string {
	return "GetFolioLedgersByFilterRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioLedgersByFilterRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioLedgersByFilterRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioLedgersByFilterRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioLedgersByFilterRequestValidationError{}

// Validate checks the field values on GetFolioLedgersByFilterResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioLedgersByFilterResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioLedgersByFilterResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFolioLedgersByFilterResponseMultiError, or nil if none found.
func (m *GetFolioLedgersByFilterResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioLedgersByFilterResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolioLedgersByFilterResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolioLedgersByFilterResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolioLedgersByFilterResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolioLedgersByFilterResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolioLedgersByFilterResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolioLedgersByFilterResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFolioLedgers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFolioLedgersByFilterResponseValidationError{
						field:  fmt.Sprintf("FolioLedgers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFolioLedgersByFilterResponseValidationError{
						field:  fmt.Sprintf("FolioLedgers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFolioLedgersByFilterResponseValidationError{
					field:  fmt.Sprintf("FolioLedgers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFolioLedgersByFilterResponseMultiError(errors)
	}

	return nil
}

// GetFolioLedgersByFilterResponseMultiError is an error wrapping multiple
// validation errors returned by GetFolioLedgersByFilterResponse.ValidateAll()
// if the designated constraints aren't met.
type GetFolioLedgersByFilterResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioLedgersByFilterResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioLedgersByFilterResponseMultiError) AllErrors() []error { return m }

// GetFolioLedgersByFilterResponseValidationError is the validation error
// returned by GetFolioLedgersByFilterResponse.Validate if the designated
// constraints aren't met.
type GetFolioLedgersByFilterResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioLedgersByFilterResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioLedgersByFilterResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioLedgersByFilterResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioLedgersByFilterResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioLedgersByFilterResponseValidationError) ErrorName() string {
	return "GetFolioLedgersByFilterResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioLedgersByFilterResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioLedgersByFilterResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioLedgersByFilterResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioLedgersByFilterResponseValidationError{}

// Validate checks the field values on UpdateNomineeForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNomineeForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNomineeForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNomineeForActorRequestMultiError, or nil if none found.
func (m *UpdateNomineeForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNomineeForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return UpdateNomineeForActorRequestMultiError(errors)
	}

	return nil
}

// UpdateNomineeForActorRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateNomineeForActorRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateNomineeForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNomineeForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNomineeForActorRequestMultiError) AllErrors() []error { return m }

// UpdateNomineeForActorRequestValidationError is the validation error returned
// by UpdateNomineeForActorRequest.Validate if the designated constraints
// aren't met.
type UpdateNomineeForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNomineeForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNomineeForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNomineeForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNomineeForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNomineeForActorRequestValidationError) ErrorName() string {
	return "UpdateNomineeForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNomineeForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNomineeForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNomineeForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNomineeForActorRequestValidationError{}

// Validate checks the field values on UpdateNomineeForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNomineeForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNomineeForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateNomineeForActorResponseMultiError, or nil if none found.
func (m *UpdateNomineeForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNomineeForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNomineeForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNomineeForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNomineeForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateNomineeForActorResponseMultiError(errors)
	}

	return nil
}

// UpdateNomineeForActorResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateNomineeForActorResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateNomineeForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNomineeForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNomineeForActorResponseMultiError) AllErrors() []error { return m }

// UpdateNomineeForActorResponseValidationError is the validation error
// returned by UpdateNomineeForActorResponse.Validate if the designated
// constraints aren't met.
type UpdateNomineeForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNomineeForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNomineeForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNomineeForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNomineeForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNomineeForActorResponseValidationError) ErrorName() string {
	return "UpdateNomineeForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNomineeForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNomineeForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNomineeForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNomineeForActorResponseValidationError{}

// Validate checks the field values on CreateOrUpdateSearchCatalogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrUpdateSearchCatalogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrUpdateSearchCatalogRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOrUpdateSearchCatalogRequestMultiError, or nil if none found.
func (m *CreateOrUpdateSearchCatalogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrUpdateSearchCatalogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestType

	if _, ok := _CreateOrUpdateSearchCatalogRequest_FundIdentifier_InLookup[m.GetFundIdentifier()]; !ok {
		err := CreateOrUpdateSearchCatalogRequestValidationError{
			field:  "FundIdentifier",
			reason: "value must be in list [MUTUAL_FUND_IDENTIFIER_ID]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateOrUpdateSearchCatalogRequestMultiError(errors)
	}

	return nil
}

// CreateOrUpdateSearchCatalogRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrUpdateSearchCatalogRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOrUpdateSearchCatalogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrUpdateSearchCatalogRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrUpdateSearchCatalogRequestMultiError) AllErrors() []error { return m }

// CreateOrUpdateSearchCatalogRequestValidationError is the validation error
// returned by CreateOrUpdateSearchCatalogRequest.Validate if the designated
// constraints aren't met.
type CreateOrUpdateSearchCatalogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrUpdateSearchCatalogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrUpdateSearchCatalogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrUpdateSearchCatalogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrUpdateSearchCatalogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrUpdateSearchCatalogRequestValidationError) ErrorName() string {
	return "CreateOrUpdateSearchCatalogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrUpdateSearchCatalogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrUpdateSearchCatalogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrUpdateSearchCatalogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrUpdateSearchCatalogRequestValidationError{}

var _CreateOrUpdateSearchCatalogRequest_FundIdentifier_InLookup = map[mutualfund.MutualFundIdentifier]struct{}{
	1: {},
}

// Validate checks the field values on CreateOrUpdateSearchCatalogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrUpdateSearchCatalogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrUpdateSearchCatalogResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOrUpdateSearchCatalogResponseMultiError, or nil if none found.
func (m *CreateOrUpdateSearchCatalogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrUpdateSearchCatalogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrUpdateSearchCatalogResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrUpdateSearchCatalogResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrUpdateSearchCatalogResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrUpdateSearchCatalogResponseMultiError(errors)
	}

	return nil
}

// CreateOrUpdateSearchCatalogResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrUpdateSearchCatalogResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOrUpdateSearchCatalogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrUpdateSearchCatalogResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrUpdateSearchCatalogResponseMultiError) AllErrors() []error { return m }

// CreateOrUpdateSearchCatalogResponseValidationError is the validation error
// returned by CreateOrUpdateSearchCatalogResponse.Validate if the designated
// constraints aren't met.
type CreateOrUpdateSearchCatalogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrUpdateSearchCatalogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrUpdateSearchCatalogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrUpdateSearchCatalogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrUpdateSearchCatalogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrUpdateSearchCatalogResponseValidationError) ErrorName() string {
	return "CreateOrUpdateSearchCatalogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrUpdateSearchCatalogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrUpdateSearchCatalogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrUpdateSearchCatalogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrUpdateSearchCatalogResponseValidationError{}

// Validate checks the field values on ProcessRecentlyVisitedFundRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessRecentlyVisitedFundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessRecentlyVisitedFundRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessRecentlyVisitedFundRequestMultiError, or nil if none found.
func (m *ProcessRecentlyVisitedFundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRecentlyVisitedFundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ProcessRecentlyVisitedFundRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetMfId()) < 1 {
		err := ProcessRecentlyVisitedFundRequestValidationError{
			field:  "MfId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ProcessRecentlyVisitedFundRequestMultiError(errors)
	}

	return nil
}

// ProcessRecentlyVisitedFundRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessRecentlyVisitedFundRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessRecentlyVisitedFundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRecentlyVisitedFundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRecentlyVisitedFundRequestMultiError) AllErrors() []error { return m }

// ProcessRecentlyVisitedFundRequestValidationError is the validation error
// returned by ProcessRecentlyVisitedFundRequest.Validate if the designated
// constraints aren't met.
type ProcessRecentlyVisitedFundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRecentlyVisitedFundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRecentlyVisitedFundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRecentlyVisitedFundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRecentlyVisitedFundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRecentlyVisitedFundRequestValidationError) ErrorName() string {
	return "ProcessRecentlyVisitedFundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRecentlyVisitedFundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRecentlyVisitedFundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRecentlyVisitedFundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRecentlyVisitedFundRequestValidationError{}

// Validate checks the field values on ProcessRecentlyVisitedFundResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessRecentlyVisitedFundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessRecentlyVisitedFundResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessRecentlyVisitedFundResponseMultiError, or nil if none found.
func (m *ProcessRecentlyVisitedFundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRecentlyVisitedFundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessRecentlyVisitedFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessRecentlyVisitedFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessRecentlyVisitedFundResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessRecentlyVisitedFundResponseMultiError(errors)
	}

	return nil
}

// ProcessRecentlyVisitedFundResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessRecentlyVisitedFundResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessRecentlyVisitedFundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRecentlyVisitedFundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRecentlyVisitedFundResponseMultiError) AllErrors() []error { return m }

// ProcessRecentlyVisitedFundResponseValidationError is the validation error
// returned by ProcessRecentlyVisitedFundResponse.Validate if the designated
// constraints aren't met.
type ProcessRecentlyVisitedFundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRecentlyVisitedFundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRecentlyVisitedFundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRecentlyVisitedFundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRecentlyVisitedFundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRecentlyVisitedFundResponseValidationError) ErrorName() string {
	return "ProcessRecentlyVisitedFundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRecentlyVisitedFundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRecentlyVisitedFundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRecentlyVisitedFundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRecentlyVisitedFundResponseValidationError{}

// Validate checks the field values on ReconcileFoliosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReconcileFoliosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReconcileFoliosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReconcileFoliosRequestMultiError, or nil if none found.
func (m *ReconcileFoliosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReconcileFoliosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amc

	if len(errors) > 0 {
		return ReconcileFoliosRequestMultiError(errors)
	}

	return nil
}

// ReconcileFoliosRequestMultiError is an error wrapping multiple validation
// errors returned by ReconcileFoliosRequest.ValidateAll() if the designated
// constraints aren't met.
type ReconcileFoliosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReconcileFoliosRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReconcileFoliosRequestMultiError) AllErrors() []error { return m }

// ReconcileFoliosRequestValidationError is the validation error returned by
// ReconcileFoliosRequest.Validate if the designated constraints aren't met.
type ReconcileFoliosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReconcileFoliosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReconcileFoliosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReconcileFoliosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReconcileFoliosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReconcileFoliosRequestValidationError) ErrorName() string {
	return "ReconcileFoliosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReconcileFoliosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReconcileFoliosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReconcileFoliosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReconcileFoliosRequestValidationError{}

// Validate checks the field values on ReconcileFoliosResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReconcileFoliosResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReconcileFoliosResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReconcileFoliosResponseMultiError, or nil if none found.
func (m *ReconcileFoliosResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReconcileFoliosResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReconcileFoliosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReconcileFoliosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReconcileFoliosResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FolioIdToReconciliationFailureReason

	if len(errors) > 0 {
		return ReconcileFoliosResponseMultiError(errors)
	}

	return nil
}

// ReconcileFoliosResponseMultiError is an error wrapping multiple validation
// errors returned by ReconcileFoliosResponse.ValidateAll() if the designated
// constraints aren't met.
type ReconcileFoliosResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReconcileFoliosResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReconcileFoliosResponseMultiError) AllErrors() []error { return m }

// ReconcileFoliosResponseValidationError is the validation error returned by
// ReconcileFoliosResponse.Validate if the designated constraints aren't met.
type ReconcileFoliosResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReconcileFoliosResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReconcileFoliosResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReconcileFoliosResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReconcileFoliosResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReconcileFoliosResponseValidationError) ErrorName() string {
	return "ReconcileFoliosResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReconcileFoliosResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReconcileFoliosResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReconcileFoliosResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReconcileFoliosResponseValidationError{}

// Validate checks the field values on GetFolioWiseBalanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioWiseBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioWiseBalanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolioWiseBalanceRequestMultiError, or nil if none found.
func (m *GetFolioWiseBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioWiseBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetFolioWiseBalanceRequestMultiError(errors)
	}

	return nil
}

// GetFolioWiseBalanceRequestMultiError is an error wrapping multiple
// validation errors returned by GetFolioWiseBalanceRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFolioWiseBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioWiseBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioWiseBalanceRequestMultiError) AllErrors() []error { return m }

// GetFolioWiseBalanceRequestValidationError is the validation error returned
// by GetFolioWiseBalanceRequest.Validate if the designated constraints aren't met.
type GetFolioWiseBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioWiseBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioWiseBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioWiseBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioWiseBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioWiseBalanceRequestValidationError) ErrorName() string {
	return "GetFolioWiseBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioWiseBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioWiseBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioWiseBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioWiseBalanceRequestValidationError{}

// Validate checks the field values on GetFolioWiseBalanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFolioWiseBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolioWiseBalanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolioWiseBalanceResponseMultiError, or nil if none found.
func (m *GetFolioWiseBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolioWiseBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolioWiseBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolioWiseBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolioWiseBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFolioDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFolioWiseBalanceResponseValidationError{
						field:  fmt.Sprintf("FolioDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFolioWiseBalanceResponseValidationError{
						field:  fmt.Sprintf("FolioDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFolioWiseBalanceResponseValidationError{
					field:  fmt.Sprintf("FolioDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFolioWiseBalanceResponseMultiError(errors)
	}

	return nil
}

// GetFolioWiseBalanceResponseMultiError is an error wrapping multiple
// validation errors returned by GetFolioWiseBalanceResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFolioWiseBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolioWiseBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolioWiseBalanceResponseMultiError) AllErrors() []error { return m }

// GetFolioWiseBalanceResponseValidationError is the validation error returned
// by GetFolioWiseBalanceResponse.Validate if the designated constraints
// aren't met.
type GetFolioWiseBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolioWiseBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolioWiseBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolioWiseBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolioWiseBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolioWiseBalanceResponseValidationError) ErrorName() string {
	return "GetFolioWiseBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolioWiseBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolioWiseBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolioWiseBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolioWiseBalanceResponseValidationError{}

// Validate checks the field values on FolioDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FolioDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FolioDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FolioDetailsMultiError, or
// nil if none found.
func (m *FolioDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FolioDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FolioNumber

	// no validation rules for BalanceUnits

	// no validation rules for WithdrawableUnits

	if len(errors) > 0 {
		return FolioDetailsMultiError(errors)
	}

	return nil
}

// FolioDetailsMultiError is an error wrapping multiple validation errors
// returned by FolioDetails.ValidateAll() if the designated constraints aren't met.
type FolioDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FolioDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FolioDetailsMultiError) AllErrors() []error { return m }

// FolioDetailsValidationError is the validation error returned by
// FolioDetails.Validate if the designated constraints aren't met.
type FolioDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FolioDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FolioDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FolioDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FolioDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FolioDetailsValidationError) ErrorName() string { return "FolioDetailsValidationError" }

// Error satisfies the builtin error interface
func (e FolioDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFolioDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FolioDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FolioDetailsValidationError{}

// Validate checks the field values on CalculateAmountGrowthRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CalculateAmountGrowthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateAmountGrowthRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CalculateAmountGrowthRequestMultiError, or nil if none found.
func (m *CalculateAmountGrowthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateAmountGrowthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InvestmentFrequency

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateAmountGrowthRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateAmountGrowthRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateAmountGrowthRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MutualFundId

	if len(errors) > 0 {
		return CalculateAmountGrowthRequestMultiError(errors)
	}

	return nil
}

// CalculateAmountGrowthRequestMultiError is an error wrapping multiple
// validation errors returned by CalculateAmountGrowthRequest.ValidateAll() if
// the designated constraints aren't met.
type CalculateAmountGrowthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateAmountGrowthRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateAmountGrowthRequestMultiError) AllErrors() []error { return m }

// CalculateAmountGrowthRequestValidationError is the validation error returned
// by CalculateAmountGrowthRequest.Validate if the designated constraints
// aren't met.
type CalculateAmountGrowthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateAmountGrowthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateAmountGrowthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateAmountGrowthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateAmountGrowthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateAmountGrowthRequestValidationError) ErrorName() string {
	return "CalculateAmountGrowthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateAmountGrowthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateAmountGrowthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateAmountGrowthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateAmountGrowthRequestValidationError{}

// Validate checks the field values on CalculateAmountGrowthResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CalculateAmountGrowthResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateAmountGrowthResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CalculateAmountGrowthResponseMultiError, or nil if none found.
func (m *CalculateAmountGrowthResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateAmountGrowthResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateAmountGrowthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateAmountGrowthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateAmountGrowthResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGrowthAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateAmountGrowthResponseValidationError{
					field:  "GrowthAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateAmountGrowthResponseValidationError{
					field:  "GrowthAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrowthAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateAmountGrowthResponseValidationError{
				field:  "GrowthAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Days

	if len(errors) > 0 {
		return CalculateAmountGrowthResponseMultiError(errors)
	}

	return nil
}

// CalculateAmountGrowthResponseMultiError is an error wrapping multiple
// validation errors returned by CalculateAmountGrowthResponse.ValidateAll()
// if the designated constraints aren't met.
type CalculateAmountGrowthResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateAmountGrowthResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateAmountGrowthResponseMultiError) AllErrors() []error { return m }

// CalculateAmountGrowthResponseValidationError is the validation error
// returned by CalculateAmountGrowthResponse.Validate if the designated
// constraints aren't met.
type CalculateAmountGrowthResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateAmountGrowthResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateAmountGrowthResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateAmountGrowthResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateAmountGrowthResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateAmountGrowthResponseValidationError) ErrorName() string {
	return "CalculateAmountGrowthResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateAmountGrowthResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateAmountGrowthResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateAmountGrowthResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateAmountGrowthResponseValidationError{}

// Validate checks the field values on GetNavHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNavHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNavHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNavHistoryRequestMultiError, or nil if none found.
func (m *GetNavHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNavHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetNavRequests()) > 20 {
		err := GetNavHistoryRequestValidationError{
			field:  "NavRequests",
			reason: "value must contain no more than 20 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetNavRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNavHistoryRequestValidationError{
						field:  fmt.Sprintf("NavRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNavHistoryRequestValidationError{
						field:  fmt.Sprintf("NavRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNavHistoryRequestValidationError{
					field:  fmt.Sprintf("NavRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNavHistoryRequestMultiError(errors)
	}

	return nil
}

// GetNavHistoryRequestMultiError is an error wrapping multiple validation
// errors returned by GetNavHistoryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNavHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNavHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNavHistoryRequestMultiError) AllErrors() []error { return m }

// GetNavHistoryRequestValidationError is the validation error returned by
// GetNavHistoryRequest.Validate if the designated constraints aren't met.
type GetNavHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNavHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNavHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNavHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNavHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNavHistoryRequestValidationError) ErrorName() string {
	return "GetNavHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNavHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNavHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNavHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNavHistoryRequestValidationError{}

// Validate checks the field values on NavRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NavRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NavRequestMultiError, or
// nil if none found.
func (m *NavRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *NavRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Isin

	for idx, item := range m.GetDates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NavRequestValidationError{
						field:  fmt.Sprintf("Dates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NavRequestValidationError{
						field:  fmt.Sprintf("Dates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NavRequestValidationError{
					field:  fmt.Sprintf("Dates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NavRequestMultiError(errors)
	}

	return nil
}

// NavRequestMultiError is an error wrapping multiple validation errors
// returned by NavRequest.ValidateAll() if the designated constraints aren't met.
type NavRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavRequestMultiError) AllErrors() []error { return m }

// NavRequestValidationError is the validation error returned by
// NavRequest.Validate if the designated constraints aren't met.
type NavRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavRequestValidationError) ErrorName() string { return "NavRequestValidationError" }

// Error satisfies the builtin error interface
func (e NavRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavRequestValidationError{}

// Validate checks the field values on GetNavHistoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNavHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNavHistoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNavHistoryResponseMultiError, or nil if none found.
func (m *GetNavHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNavHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNavHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNavHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNavHistoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNavHistories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNavHistoryResponseValidationError{
						field:  fmt.Sprintf("NavHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNavHistoryResponseValidationError{
						field:  fmt.Sprintf("NavHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNavHistoryResponseValidationError{
					field:  fmt.Sprintf("NavHistories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNavHistoryResponseMultiError(errors)
	}

	return nil
}

// GetNavHistoryResponseMultiError is an error wrapping multiple validation
// errors returned by GetNavHistoryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNavHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNavHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNavHistoryResponseMultiError) AllErrors() []error { return m }

// GetNavHistoryResponseValidationError is the validation error returned by
// GetNavHistoryResponse.Validate if the designated constraints aren't met.
type GetNavHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNavHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNavHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNavHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNavHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNavHistoryResponseValidationError) ErrorName() string {
	return "GetNavHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNavHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNavHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNavHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNavHistoryResponseValidationError{}

// Validate checks the field values on NavHistory with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NavHistory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavHistory with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NavHistoryMultiError, or
// nil if none found.
func (m *NavHistory) ValidateAll() error {
	return m.validate(true)
}

func (m *NavHistory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Isin

	// no validation rules for MutualFundId

	for idx, item := range m.GetNavDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NavHistoryValidationError{
						field:  fmt.Sprintf("NavDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NavHistoryValidationError{
						field:  fmt.Sprintf("NavDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NavHistoryValidationError{
					field:  fmt.Sprintf("NavDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMissingDates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NavHistoryValidationError{
						field:  fmt.Sprintf("MissingDates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NavHistoryValidationError{
						field:  fmt.Sprintf("MissingDates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NavHistoryValidationError{
					field:  fmt.Sprintf("MissingDates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NavHistoryMultiError(errors)
	}

	return nil
}

// NavHistoryMultiError is an error wrapping multiple validation errors
// returned by NavHistory.ValidateAll() if the designated constraints aren't met.
type NavHistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavHistoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavHistoryMultiError) AllErrors() []error { return m }

// NavHistoryValidationError is the validation error returned by
// NavHistory.Validate if the designated constraints aren't met.
type NavHistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavHistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavHistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavHistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavHistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavHistoryValidationError) ErrorName() string { return "NavHistoryValidationError" }

// Error satisfies the builtin error interface
func (e NavHistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavHistory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavHistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavHistoryValidationError{}

// Validate checks the field values on NavDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NavDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NavDetailsMultiError, or
// nil if none found.
func (m *NavDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NavDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NavDetailsValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NavDetailsValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NavDetailsValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNav()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NavDetailsValidationError{
					field:  "Nav",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NavDetailsValidationError{
					field:  "Nav",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNav()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NavDetailsValidationError{
				field:  "Nav",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NavSource

	if all {
		switch v := interface{}(m.GetNavDerivedDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NavDetailsValidationError{
					field:  "NavDerivedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NavDetailsValidationError{
					field:  "NavDerivedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNavDerivedDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NavDetailsValidationError{
				field:  "NavDerivedDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NavDetailsMultiError(errors)
	}

	return nil
}

// NavDetailsMultiError is an error wrapping multiple validation errors
// returned by NavDetails.ValidateAll() if the designated constraints aren't met.
type NavDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavDetailsMultiError) AllErrors() []error { return m }

// NavDetailsValidationError is the validation error returned by
// NavDetails.Validate if the designated constraints aren't met.
type NavDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavDetailsValidationError) ErrorName() string { return "NavDetailsValidationError" }

// Error satisfies the builtin error interface
func (e NavDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavDetailsValidationError{}

// Validate checks the field values on GetNavHistoryByMutualFundIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNavHistoryByMutualFundIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNavHistoryByMutualFundIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNavHistoryByMutualFundIdRequestMultiError, or nil if none found.
func (m *GetNavHistoryByMutualFundIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNavHistoryByMutualFundIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetNavRequests()) > 20 {
		err := GetNavHistoryByMutualFundIdRequestValidationError{
			field:  "NavRequests",
			reason: "value must contain no more than 20 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetNavRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNavHistoryByMutualFundIdRequestValidationError{
						field:  fmt.Sprintf("NavRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNavHistoryByMutualFundIdRequestValidationError{
						field:  fmt.Sprintf("NavRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNavHistoryByMutualFundIdRequestValidationError{
					field:  fmt.Sprintf("NavRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNavHistoryByMutualFundIdRequestMultiError(errors)
	}

	return nil
}

// GetNavHistoryByMutualFundIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetNavHistoryByMutualFundIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNavHistoryByMutualFundIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNavHistoryByMutualFundIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNavHistoryByMutualFundIdRequestMultiError) AllErrors() []error { return m }

// GetNavHistoryByMutualFundIdRequestValidationError is the validation error
// returned by GetNavHistoryByMutualFundIdRequest.Validate if the designated
// constraints aren't met.
type GetNavHistoryByMutualFundIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNavHistoryByMutualFundIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNavHistoryByMutualFundIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNavHistoryByMutualFundIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNavHistoryByMutualFundIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNavHistoryByMutualFundIdRequestValidationError) ErrorName() string {
	return "GetNavHistoryByMutualFundIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNavHistoryByMutualFundIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNavHistoryByMutualFundIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNavHistoryByMutualFundIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNavHistoryByMutualFundIdRequestValidationError{}

// Validate checks the field values on NavRequestByMutualFundId with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NavRequestByMutualFundId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavRequestByMutualFundId with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NavRequestByMutualFundIdMultiError, or nil if none found.
func (m *NavRequestByMutualFundId) ValidateAll() error {
	return m.validate(true)
}

func (m *NavRequestByMutualFundId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	for idx, item := range m.GetDates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NavRequestByMutualFundIdValidationError{
						field:  fmt.Sprintf("Dates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NavRequestByMutualFundIdValidationError{
						field:  fmt.Sprintf("Dates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NavRequestByMutualFundIdValidationError{
					field:  fmt.Sprintf("Dates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NavRequestByMutualFundIdMultiError(errors)
	}

	return nil
}

// NavRequestByMutualFundIdMultiError is an error wrapping multiple validation
// errors returned by NavRequestByMutualFundId.ValidateAll() if the designated
// constraints aren't met.
type NavRequestByMutualFundIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavRequestByMutualFundIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavRequestByMutualFundIdMultiError) AllErrors() []error { return m }

// NavRequestByMutualFundIdValidationError is the validation error returned by
// NavRequestByMutualFundId.Validate if the designated constraints aren't met.
type NavRequestByMutualFundIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavRequestByMutualFundIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavRequestByMutualFundIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavRequestByMutualFundIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavRequestByMutualFundIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavRequestByMutualFundIdValidationError) ErrorName() string {
	return "NavRequestByMutualFundIdValidationError"
}

// Error satisfies the builtin error interface
func (e NavRequestByMutualFundIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavRequestByMutualFundId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavRequestByMutualFundIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavRequestByMutualFundIdValidationError{}

// Validate checks the field values on GetNavHistoryByMutualFundIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNavHistoryByMutualFundIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNavHistoryByMutualFundIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNavHistoryByMutualFundIdResponseMultiError, or nil if none found.
func (m *GetNavHistoryByMutualFundIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNavHistoryByMutualFundIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNavHistoryByMutualFundIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNavHistoryByMutualFundIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNavHistoryByMutualFundIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNavHistories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNavHistoryByMutualFundIdResponseValidationError{
						field:  fmt.Sprintf("NavHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNavHistoryByMutualFundIdResponseValidationError{
						field:  fmt.Sprintf("NavHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNavHistoryByMutualFundIdResponseValidationError{
					field:  fmt.Sprintf("NavHistories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNavHistoryByMutualFundIdResponseMultiError(errors)
	}

	return nil
}

// GetNavHistoryByMutualFundIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetNavHistoryByMutualFundIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNavHistoryByMutualFundIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNavHistoryByMutualFundIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNavHistoryByMutualFundIdResponseMultiError) AllErrors() []error { return m }

// GetNavHistoryByMutualFundIdResponseValidationError is the validation error
// returned by GetNavHistoryByMutualFundIdResponse.Validate if the designated
// constraints aren't met.
type GetNavHistoryByMutualFundIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNavHistoryByMutualFundIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNavHistoryByMutualFundIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNavHistoryByMutualFundIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNavHistoryByMutualFundIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNavHistoryByMutualFundIdResponseValidationError) ErrorName() string {
	return "GetNavHistoryByMutualFundIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNavHistoryByMutualFundIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNavHistoryByMutualFundIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNavHistoryByMutualFundIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNavHistoryByMutualFundIdResponseValidationError{}

// Validate checks the field values on GetNavChartDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNavChartDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNavChartDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNavChartDataRequestMultiError, or nil if none found.
func (m *GetNavChartDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNavChartDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundId

	if all {
		switch v := interface{}(m.GetDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNavChartDataRequestValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNavChartDataRequestValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNavChartDataRequestValidationError{
				field:  "Duration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNavChartDataRequestMultiError(errors)
	}

	return nil
}

// GetNavChartDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetNavChartDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNavChartDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNavChartDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNavChartDataRequestMultiError) AllErrors() []error { return m }

// GetNavChartDataRequestValidationError is the validation error returned by
// GetNavChartDataRequest.Validate if the designated constraints aren't met.
type GetNavChartDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNavChartDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNavChartDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNavChartDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNavChartDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNavChartDataRequestValidationError) ErrorName() string {
	return "GetNavChartDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNavChartDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNavChartDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNavChartDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNavChartDataRequestValidationError{}

// Validate checks the field values on GetNavChartDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNavChartDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNavChartDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNavChartDataResponseMultiError, or nil if none found.
func (m *GetNavChartDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNavChartDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNavChartDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNavChartDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNavChartDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNavChartDataPoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNavChartDataResponseValidationError{
						field:  fmt.Sprintf("NavChartDataPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNavChartDataResponseValidationError{
						field:  fmt.Sprintf("NavChartDataPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNavChartDataResponseValidationError{
					field:  fmt.Sprintf("NavChartDataPoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNavChartDataResponseMultiError(errors)
	}

	return nil
}

// GetNavChartDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetNavChartDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNavChartDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNavChartDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNavChartDataResponseMultiError) AllErrors() []error { return m }

// GetNavChartDataResponseValidationError is the validation error returned by
// GetNavChartDataResponse.Validate if the designated constraints aren't met.
type GetNavChartDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNavChartDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNavChartDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNavChartDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNavChartDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNavChartDataResponseValidationError) ErrorName() string {
	return "GetNavChartDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNavChartDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNavChartDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNavChartDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNavChartDataResponseValidationError{}

// Validate checks the field values on NavChartDataPoint with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NavChartDataPoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavChartDataPoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NavChartDataPointMultiError, or nil if none found.
func (m *NavChartDataPoint) ValidateAll() error {
	return m.validate(true)
}

func (m *NavChartDataPoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNavDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NavChartDataPointValidationError{
					field:  "NavDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NavChartDataPointValidationError{
					field:  "NavDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNavDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NavChartDataPointValidationError{
				field:  "NavDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Nav

	if len(errors) > 0 {
		return NavChartDataPointMultiError(errors)
	}

	return nil
}

// NavChartDataPointMultiError is an error wrapping multiple validation errors
// returned by NavChartDataPoint.ValidateAll() if the designated constraints
// aren't met.
type NavChartDataPointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavChartDataPointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavChartDataPointMultiError) AllErrors() []error { return m }

// NavChartDataPointValidationError is the validation error returned by
// NavChartDataPoint.Validate if the designated constraints aren't met.
type NavChartDataPointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavChartDataPointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavChartDataPointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavChartDataPointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavChartDataPointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavChartDataPointValidationError) ErrorName() string {
	return "NavChartDataPointValidationError"
}

// Error satisfies the builtin error interface
func (e NavChartDataPointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavChartDataPoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavChartDataPointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavChartDataPointValidationError{}

// Validate checks the field values on NavChartDuration with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NavChartDuration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavChartDuration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NavChartDurationMultiError, or nil if none found.
func (m *NavChartDuration) ValidateAll() error {
	return m.validate(true)
}

func (m *NavChartDuration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.ChartDuration.(type) {
	case *NavChartDuration_DurationInMonths:
		if v == nil {
			err := NavChartDurationValidationError{
				field:  "ChartDuration",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DurationInMonths
	case *NavChartDuration_FromStartOfFund:
		if v == nil {
			err := NavChartDurationValidationError{
				field:  "ChartDuration",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FromStartOfFund
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NavChartDurationMultiError(errors)
	}

	return nil
}

// NavChartDurationMultiError is an error wrapping multiple validation errors
// returned by NavChartDuration.ValidateAll() if the designated constraints
// aren't met.
type NavChartDurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavChartDurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavChartDurationMultiError) AllErrors() []error { return m }

// NavChartDurationValidationError is the validation error returned by
// NavChartDuration.Validate if the designated constraints aren't met.
type NavChartDurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavChartDurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavChartDurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavChartDurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavChartDurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavChartDurationValidationError) ErrorName() string { return "NavChartDurationValidationError" }

// Error satisfies the builtin error interface
func (e NavChartDurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavChartDuration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavChartDurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavChartDurationValidationError{}

// Validate checks the field values on InvestmentCycle with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InvestmentCycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentCycle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentCycleMultiError, or nil if none found.
func (m *InvestmentCycle) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentCycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.InvestmentCycle.(type) {
	case *InvestmentCycle_DurationInDays:
		if v == nil {
			err := InvestmentCycleValidationError{
				field:  "InvestmentCycle",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DurationInDays
	case *InvestmentCycle_DurationInMonths:
		if v == nil {
			err := InvestmentCycleValidationError{
				field:  "InvestmentCycle",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DurationInMonths
	case *InvestmentCycle_IsOneTimeInvestment:
		if v == nil {
			err := InvestmentCycleValidationError{
				field:  "InvestmentCycle",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for IsOneTimeInvestment
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return InvestmentCycleMultiError(errors)
	}

	return nil
}

// InvestmentCycleMultiError is an error wrapping multiple validation errors
// returned by InvestmentCycle.ValidateAll() if the designated constraints
// aren't met.
type InvestmentCycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentCycleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentCycleMultiError) AllErrors() []error { return m }

// InvestmentCycleValidationError is the validation error returned by
// InvestmentCycle.Validate if the designated constraints aren't met.
type InvestmentCycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentCycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentCycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentCycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentCycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentCycleValidationError) ErrorName() string { return "InvestmentCycleValidationError" }

// Error satisfies the builtin error interface
func (e InvestmentCycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentCycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentCycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentCycleValidationError{}
