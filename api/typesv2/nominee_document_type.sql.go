package typesv2

import (
	"database/sql/driver"
	"fmt"
)

// Valuer interface implementation for storing the data in JSONB format in DB
func (x NomineeDocumentType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (x *NomineeDocumentType) Scan(input interface{}) error {
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", input)
	}

	valInt, ok := RelationType_value[val]
	if !ok {
		return fmt.Errorf("unexpected value: %s", val)
	}

	*x = NomineeDocumentType(valInt)
	return nil
}
