// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/prelaunch/screen_options.proto

package prelaunch

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PreLaunchScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreLaunchScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreLaunchScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreLaunchScreenOptionsMultiError, or nil if none found.
func (m *PreLaunchScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *PreLaunchScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreLaunchPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchScreenOptionsValidationError{
					field:  "PreLaunchPage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchScreenOptionsValidationError{
					field:  "PreLaunchPage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreLaunchPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchScreenOptionsValidationError{
				field:  "PreLaunchPage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrelaunchFlowType

	if len(errors) > 0 {
		return PreLaunchScreenOptionsMultiError(errors)
	}

	return nil
}

// PreLaunchScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by PreLaunchScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type PreLaunchScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreLaunchScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreLaunchScreenOptionsMultiError) AllErrors() []error { return m }

// PreLaunchScreenOptionsValidationError is the validation error returned by
// PreLaunchScreenOptions.Validate if the designated constraints aren't met.
type PreLaunchScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreLaunchScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreLaunchScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreLaunchScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreLaunchScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreLaunchScreenOptionsValidationError) ErrorName() string {
	return "PreLaunchScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e PreLaunchScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreLaunchScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreLaunchScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreLaunchScreenOptionsValidationError{}

// Validate checks the field values on PreLaunchPage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PreLaunchPage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreLaunchPage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PreLaunchPageMultiError, or
// nil if none found.
func (m *PreLaunchPage) ValidateAll() error {
	return m.validate(true)
}

func (m *PreLaunchPage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "BgImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PreLaunchPageValidationError{
						field:  fmt.Sprintf("Benefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PreLaunchPageValidationError{
						field:  fmt.Sprintf("Benefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PreLaunchPageValidationError{
					field:  fmt.Sprintf("Benefits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBonus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "Bonus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "Bonus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBonus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "Bonus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecordInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "RecordInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "RecordInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecordInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "RecordInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSuccess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "Success",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "Success",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSuccess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "Success",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTryAgain()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "TryAgain",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "TryAgain",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTryAgain()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "TryAgain",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisclaimerMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "DisclaimerMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "DisclaimerMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisclaimerMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "DisclaimerMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSuccessMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "SuccessMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "SuccessMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSuccessMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "SuccessMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTryAgainMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "TryAgainMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreLaunchPageValidationError{
					field:  "TryAgainMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTryAgainMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreLaunchPageValidationError{
				field:  "TryAgainMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PreLaunchPageMultiError(errors)
	}

	return nil
}

// PreLaunchPageMultiError is an error wrapping multiple validation errors
// returned by PreLaunchPage.ValidateAll() if the designated constraints
// aren't met.
type PreLaunchPageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreLaunchPageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreLaunchPageMultiError) AllErrors() []error { return m }

// PreLaunchPageValidationError is the validation error returned by
// PreLaunchPage.Validate if the designated constraints aren't met.
type PreLaunchPageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreLaunchPageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreLaunchPageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreLaunchPageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreLaunchPageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreLaunchPageValidationError) ErrorName() string { return "PreLaunchPageValidationError" }

// Error satisfies the builtin error interface
func (e PreLaunchPageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreLaunchPage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreLaunchPageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreLaunchPageValidationError{}

// Validate checks the field values on Button with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Button) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Button with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ButtonMultiError, or nil if none found.
func (m *Button) ValidateAll() error {
	return m.validate(true)
}

func (m *Button) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ButtonValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ButtonValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ButtonValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for ImgUrl

	if len(errors) > 0 {
		return ButtonMultiError(errors)
	}

	return nil
}

// ButtonMultiError is an error wrapping multiple validation errors returned by
// Button.ValidateAll() if the designated constraints aren't met.
type ButtonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ButtonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ButtonMultiError) AllErrors() []error { return m }

// ButtonValidationError is the validation error returned by Button.Validate if
// the designated constraints aren't met.
type ButtonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ButtonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ButtonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ButtonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ButtonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ButtonValidationError) ErrorName() string { return "ButtonValidationError" }

// Error satisfies the builtin error interface
func (e ButtonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sButton.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ButtonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ButtonValidationError{}
