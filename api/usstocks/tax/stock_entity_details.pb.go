// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/usstocks/tax/stock_entity_details.proto

package tax

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StockEntityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Symbol             string               `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	DailyClosingPrices []*DailyClosingPrice `protobuf:"bytes,2,rep,name=daily_closing_prices,json=dailyClosingPrices,proto3" json:"daily_closing_prices,omitempty"`
	// address of the company/ETF
	EntityAddress string `protobuf:"bytes,3,opt,name=entity_address,json=entityAddress,proto3" json:"entity_address,omitempty"`
}

func (x *StockEntityDetails) Reset() {
	*x = StockEntityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_tax_stock_entity_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StockEntityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockEntityDetails) ProtoMessage() {}

func (x *StockEntityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_tax_stock_entity_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockEntityDetails.ProtoReflect.Descriptor instead.
func (*StockEntityDetails) Descriptor() ([]byte, []int) {
	return file_api_usstocks_tax_stock_entity_details_proto_rawDescGZIP(), []int{0}
}

func (x *StockEntityDetails) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *StockEntityDetails) GetDailyClosingPrices() []*DailyClosingPrice {
	if x != nil {
		return x.DailyClosingPrices
	}
	return nil
}

func (x *StockEntityDetails) GetEntityAddress() string {
	if x != nil {
		return x.EntityAddress
	}
	return ""
}

type DailyClosingPrice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The time for which the closing price is recorded.
	ClosingPriceTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=closing_price_time,json=closingPriceTime,proto3" json:"closing_price_time,omitempty"`
	// The closing price of the stock on the given timestamp.
	ClosingPrice *money.Money `protobuf:"bytes,2,opt,name=closing_price,json=closingPrice,proto3" json:"closing_price,omitempty"`
}

func (x *DailyClosingPrice) Reset() {
	*x = DailyClosingPrice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_tax_stock_entity_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyClosingPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyClosingPrice) ProtoMessage() {}

func (x *DailyClosingPrice) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_tax_stock_entity_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyClosingPrice.ProtoReflect.Descriptor instead.
func (*DailyClosingPrice) Descriptor() ([]byte, []int) {
	return file_api_usstocks_tax_stock_entity_details_proto_rawDescGZIP(), []int{1}
}

func (x *DailyClosingPrice) GetClosingPriceTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ClosingPriceTime
	}
	return nil
}

func (x *DailyClosingPrice) GetClosingPrice() *money.Money {
	if x != nil {
		return x.ClosingPrice
	}
	return nil
}

var File_api_usstocks_tax_stock_entity_details_proto protoreflect.FileDescriptor

var file_api_usstocks_tax_stock_entity_details_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x74,
	0x61, 0x78, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x74, 0x61, 0x78, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x01, 0x0a, 0x12, 0x53, 0x74,
	0x6f, 0x63, 0x6b, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x55, 0x0a, 0x14, 0x64, 0x61, 0x69, 0x6c,
	0x79, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x74, 0x61, 0x78, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43,
	0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x12, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x11, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x43, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x12,
	0x63, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x63, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x6c, 0x6f, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0c, 0x63, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x74, 0x61, 0x78, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f,
	0x74, 0x61, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_usstocks_tax_stock_entity_details_proto_rawDescOnce sync.Once
	file_api_usstocks_tax_stock_entity_details_proto_rawDescData = file_api_usstocks_tax_stock_entity_details_proto_rawDesc
)

func file_api_usstocks_tax_stock_entity_details_proto_rawDescGZIP() []byte {
	file_api_usstocks_tax_stock_entity_details_proto_rawDescOnce.Do(func() {
		file_api_usstocks_tax_stock_entity_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_usstocks_tax_stock_entity_details_proto_rawDescData)
	})
	return file_api_usstocks_tax_stock_entity_details_proto_rawDescData
}

var file_api_usstocks_tax_stock_entity_details_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_usstocks_tax_stock_entity_details_proto_goTypes = []interface{}{
	(*StockEntityDetails)(nil),    // 0: api.usstocks.tax.StockEntityDetails
	(*DailyClosingPrice)(nil),     // 1: api.usstocks.tax.DailyClosingPrice
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
	(*money.Money)(nil),           // 3: google.type.Money
}
var file_api_usstocks_tax_stock_entity_details_proto_depIdxs = []int32{
	1, // 0: api.usstocks.tax.StockEntityDetails.daily_closing_prices:type_name -> api.usstocks.tax.DailyClosingPrice
	2, // 1: api.usstocks.tax.DailyClosingPrice.closing_price_time:type_name -> google.protobuf.Timestamp
	3, // 2: api.usstocks.tax.DailyClosingPrice.closing_price:type_name -> google.type.Money
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_usstocks_tax_stock_entity_details_proto_init() }
func file_api_usstocks_tax_stock_entity_details_proto_init() {
	if File_api_usstocks_tax_stock_entity_details_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_usstocks_tax_stock_entity_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StockEntityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_usstocks_tax_stock_entity_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyClosingPrice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_usstocks_tax_stock_entity_details_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_usstocks_tax_stock_entity_details_proto_goTypes,
		DependencyIndexes: file_api_usstocks_tax_stock_entity_details_proto_depIdxs,
		MessageInfos:      file_api_usstocks_tax_stock_entity_details_proto_msgTypes,
	}.Build()
	File_api_usstocks_tax_stock_entity_details_proto = out.File
	file_api_usstocks_tax_stock_entity_details_proto_rawDesc = nil
	file_api_usstocks_tax_stock_entity_details_proto_goTypes = nil
	file_api_usstocks_tax_stock_entity_details_proto_depIdxs = nil
}
