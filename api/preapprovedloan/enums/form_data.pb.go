// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/enums/form_data.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// represents different form fields collected from the user
// ref: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-63839&t=AzSZYhZyj4avLgl4-0
type FieldId int32

const (
	FieldId_FIELD_ID_UNSPECIFIED FieldId = 0
	FieldId_NAME                 FieldId = 1
	FieldId_DOB                  FieldId = 2
	FieldId_PAN                  FieldId = 3
	FieldId_GENDER               FieldId = 4
	FieldId_MARITAL_STATUS       FieldId = 5
	FieldId_PINCODE              FieldId = 6
	FieldId_ADDRESS_TYPE         FieldId = 7
	FieldId_RENT                 FieldId = 8
	FieldId_LOAN_PURPOSE         FieldId = 9
	FieldId_DESIRED_LOAN_AMOUNT  FieldId = 10
	FieldId_EMPLOYMENT_TYPE      FieldId = 11
	FieldId_EMPLOYER_NAME        FieldId = 12
	FieldId_WORK_EMAIL           FieldId = 13
	FieldId_MONTHLY_INCOME       FieldId = 14
	FieldId_WORK_ADDRESS         FieldId = 15
	FieldId_GSTIN                FieldId = 16
	FieldId_ANNUAL_REVENUE       FieldId = 17
	FieldId_MOTHER_NAME          FieldId = 18
	FieldId_FATHER_NAME          FieldId = 19
)

// Enum value maps for FieldId.
var (
	FieldId_name = map[int32]string{
		0:  "FIELD_ID_UNSPECIFIED",
		1:  "NAME",
		2:  "DOB",
		3:  "PAN",
		4:  "GENDER",
		5:  "MARITAL_STATUS",
		6:  "PINCODE",
		7:  "ADDRESS_TYPE",
		8:  "RENT",
		9:  "LOAN_PURPOSE",
		10: "DESIRED_LOAN_AMOUNT",
		11: "EMPLOYMENT_TYPE",
		12: "EMPLOYER_NAME",
		13: "WORK_EMAIL",
		14: "MONTHLY_INCOME",
		15: "WORK_ADDRESS",
		16: "GSTIN",
		17: "ANNUAL_REVENUE",
		18: "MOTHER_NAME",
		19: "FATHER_NAME",
	}
	FieldId_value = map[string]int32{
		"FIELD_ID_UNSPECIFIED": 0,
		"NAME":                 1,
		"DOB":                  2,
		"PAN":                  3,
		"GENDER":               4,
		"MARITAL_STATUS":       5,
		"PINCODE":              6,
		"ADDRESS_TYPE":         7,
		"RENT":                 8,
		"LOAN_PURPOSE":         9,
		"DESIRED_LOAN_AMOUNT":  10,
		"EMPLOYMENT_TYPE":      11,
		"EMPLOYER_NAME":        12,
		"WORK_EMAIL":           13,
		"MONTHLY_INCOME":       14,
		"WORK_ADDRESS":         15,
		"GSTIN":                16,
		"ANNUAL_REVENUE":       17,
		"MOTHER_NAME":          18,
		"FATHER_NAME":          19,
	}
)

func (x FieldId) Enum() *FieldId {
	p := new(FieldId)
	*p = x
	return p
}

func (x FieldId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldId) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_form_data_proto_enumTypes[0].Descriptor()
}

func (FieldId) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_form_data_proto_enumTypes[0]
}

func (x FieldId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldId.Descriptor instead.
func (FieldId) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_form_data_proto_rawDescGZIP(), []int{0}
}

type LoanPurpose int32

const (
	LoanPurpose_LOAN_PURPOSE_UNSPECIFIED                LoanPurpose = 0
	LoanPurpose_LOAN_PURPOSE_HOME_RENOVATION            LoanPurpose = 1
	LoanPurpose_LOAN_PURPOSE_TRAVEL                     LoanPurpose = 2
	LoanPurpose_LOAN_PURPOSE_EDUCATION                  LoanPurpose = 3
	LoanPurpose_LOAN_PURPOSE_WEDDING                    LoanPurpose = 4
	LoanPurpose_LOAN_PURPOSE_MEDICAL                    LoanPurpose = 5
	LoanPurpose_LOAN_PURPOSE_RELOCATION                 LoanPurpose = 6
	LoanPurpose_LOAN_PURPOSE_CONSOLIDATE_EXISTING_LOANS LoanPurpose = 7
	LoanPurpose_LOAN_PURPOSE_INVESTMENT                 LoanPurpose = 8
	LoanPurpose_LOAN_PURPOSE_OTHER                      LoanPurpose = 9
)

// Enum value maps for LoanPurpose.
var (
	LoanPurpose_name = map[int32]string{
		0: "LOAN_PURPOSE_UNSPECIFIED",
		1: "LOAN_PURPOSE_HOME_RENOVATION",
		2: "LOAN_PURPOSE_TRAVEL",
		3: "LOAN_PURPOSE_EDUCATION",
		4: "LOAN_PURPOSE_WEDDING",
		5: "LOAN_PURPOSE_MEDICAL",
		6: "LOAN_PURPOSE_RELOCATION",
		7: "LOAN_PURPOSE_CONSOLIDATE_EXISTING_LOANS",
		8: "LOAN_PURPOSE_INVESTMENT",
		9: "LOAN_PURPOSE_OTHER",
	}
	LoanPurpose_value = map[string]int32{
		"LOAN_PURPOSE_UNSPECIFIED":                0,
		"LOAN_PURPOSE_HOME_RENOVATION":            1,
		"LOAN_PURPOSE_TRAVEL":                     2,
		"LOAN_PURPOSE_EDUCATION":                  3,
		"LOAN_PURPOSE_WEDDING":                    4,
		"LOAN_PURPOSE_MEDICAL":                    5,
		"LOAN_PURPOSE_RELOCATION":                 6,
		"LOAN_PURPOSE_CONSOLIDATE_EXISTING_LOANS": 7,
		"LOAN_PURPOSE_INVESTMENT":                 8,
		"LOAN_PURPOSE_OTHER":                      9,
	}
)

func (x LoanPurpose) Enum() *LoanPurpose {
	p := new(LoanPurpose)
	*p = x
	return p
}

func (x LoanPurpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanPurpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_form_data_proto_enumTypes[1].Descriptor()
}

func (LoanPurpose) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_form_data_proto_enumTypes[1]
}

func (x LoanPurpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanPurpose.Descriptor instead.
func (LoanPurpose) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_form_data_proto_rawDescGZIP(), []int{1}
}

var File_api_preapprovedloan_enums_form_data_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_enums_form_data_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2a, 0xd2, 0x02, 0x0a, 0x07, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x14, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x49, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x41, 0x4d, 0x45,
	0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x4f, 0x42, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x50,
	0x41, 0x4e, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x04,
	0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x41, 0x52, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x49, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10,
	0x06, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x10, 0x0a,
	0x0c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x10, 0x09, 0x12,
	0x17, 0x0a, 0x13, 0x44, 0x45, 0x53, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0b, 0x12, 0x11, 0x0a,
	0x0d, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0c,
	0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x0d,
	0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x4f,
	0x4d, 0x45, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x41, 0x44, 0x44,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x0f, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x53, 0x54, 0x49, 0x4e, 0x10,
	0x10, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4e, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x4e, 0x55, 0x45, 0x10, 0x11, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x10, 0x12, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x41, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x13, 0x2a, 0xb5, 0x02, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e,
	0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x55,
	0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x4e, 0x4f, 0x56,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10, 0x02,
	0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45,
	0x5f, 0x45, 0x44, 0x55, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x57, 0x45, 0x44,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x05,
	0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45,
	0x5f, 0x52, 0x45, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x2b, 0x0a,
	0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x4f, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x09, 0x42,
	0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_enums_form_data_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_enums_form_data_proto_rawDescData = file_api_preapprovedloan_enums_form_data_proto_rawDesc
)

func file_api_preapprovedloan_enums_form_data_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_enums_form_data_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_enums_form_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_enums_form_data_proto_rawDescData)
	})
	return file_api_preapprovedloan_enums_form_data_proto_rawDescData
}

var file_api_preapprovedloan_enums_form_data_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_preapprovedloan_enums_form_data_proto_goTypes = []interface{}{
	(FieldId)(0),     // 0: preapprovedloan.enums.FieldId
	(LoanPurpose)(0), // 1: preapprovedloan.enums.LoanPurpose
}
var file_api_preapprovedloan_enums_form_data_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_enums_form_data_proto_init() }
func file_api_preapprovedloan_enums_form_data_proto_init() {
	if File_api_preapprovedloan_enums_form_data_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_enums_form_data_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_enums_form_data_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_enums_form_data_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_enums_form_data_proto_enumTypes,
	}.Build()
	File_api_preapprovedloan_enums_form_data_proto = out.File
	file_api_preapprovedloan_enums_form_data_proto_rawDesc = nil
	file_api_preapprovedloan_enums_form_data_proto_goTypes = nil
	file_api_preapprovedloan_enums_form_data_proto_depIdxs = nil
}
