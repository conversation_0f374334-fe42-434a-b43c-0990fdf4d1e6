package preapprovedloan

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
)

func (lo *LoanOffer) IsEqualUserConstraints(offer *LoanOffer) bool {
	if len(lo.GetProcessingInfo().GetProcessingFee()) > 0 && len(offer.GetProcessingInfo().GetProcessingFee()) > 0 && (lo.GetProcessingInfo().GetProcessingFee()[0].GetPercentage() != offer.GetProcessingInfo().GetProcessingFee()[0].GetPercentage()) {
		return false
	}
	if len(lo.GetProcessingInfo().GetInterestRate()) > 0 && len(offer.GetProcessingInfo().GetInterestRate()) > 0 && (lo.GetProcessingInfo().GetInterestRate()[0].GetPercentage() != offer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()) {
		return false
	}
	return true
}

func (lo *LoanOffer) IsActiveNow() bool {
	if lo != nil && lo.GetDeactivatedAt() == nil && datetimePkg.IsBefore(timestampPb.Now(), lo.GetValidTill()) && datetimePkg.IsAfter(timestampPb.Now(), lo.GetValidSince()) {
		return true
	}
	return false
}

func (lo *LoanOffer) GetLoanHeader() *LoanHeader {
	if lo == nil {
		return nil
	}
	return &LoanHeader{
		LoanProgram: lo.GetLoanProgram(),
		Vendor:      lo.GetVendor(),
	}
}
