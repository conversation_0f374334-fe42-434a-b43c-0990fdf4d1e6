// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=<PERSON><PERSON><PERSON>,LoanRequestType,LoanRequestStatus,LoanRequestSubStatus,LoanAccountStatus,LoanType,LoanRequestFieldMask,LoanAccountFieldMask,LoanOfferFieldMask,LoanStepExecutionStatus,LoanStepExecutionSubStatus,LoanStepExecutionFieldMask,LoanStepExecutionFlow,LoanStepExecutionStepName,LoanOfferEligibilityCriteriaFieldMask,LoanOfferEligibilityCriteriaStatus,LoanOfferEligibilityCriteriaSubStatus,LoanActivityType,LoanActivityFieldMask,LoanPaymentRequestType,LoanPaymentRequestStatus,LoanPaymentRequestSubStatus,LoanInstallmentInfoStatus,LoanInstallmentInfoFieldMask,LoanInstallmentPayoutStatus,LoanInstallmentPayoutFieldMask,LoanApplicationStatus,LoanApplicationSubStatus,LoanProgram,LoanApplicantFieldMask,LoanApplicantSubStatus,LoanApplicantStatus,GroupStage,LoanProgram,AssetType,FetchedAssetFieldMask,IdfcCkycAddressPinCodeType,OtpType,OtpStatus,MandateRequestFieldMask,MandateRequestStatus,DataRequirementType,LoanPaymentAccountType,LoanOfferType,PreEligibilityOfferFieldMask

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/enums.proto

package preapprovedloan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Vendor int32

const (
	Vendor_VENDOR_UNSPECIFIED Vendor = 0
	Vendor_FEDERAL            Vendor = 1
	Vendor_LIQUILOANS         Vendor = 2
	Vendor_IDFC               Vendor = 3
	Vendor_FIFTYFIN           Vendor = 4
	Vendor_ABFL               Vendor = 5
	Vendor_EPIFI_TECH         Vendor = 6
	Vendor_MONEYVIEW          Vendor = 7
	Vendor_VENDOR_MF_CENTRAL  Vendor = 8
	Vendor_STOCK_GUARDIAN_LSP Vendor = 9
	Vendor_LENDEN             Vendor = 10
)

// Enum value maps for Vendor.
var (
	Vendor_name = map[int32]string{
		0:  "VENDOR_UNSPECIFIED",
		1:  "FEDERAL",
		2:  "LIQUILOANS",
		3:  "IDFC",
		4:  "FIFTYFIN",
		5:  "ABFL",
		6:  "EPIFI_TECH",
		7:  "MONEYVIEW",
		8:  "VENDOR_MF_CENTRAL",
		9:  "STOCK_GUARDIAN_LSP",
		10: "LENDEN",
	}
	Vendor_value = map[string]int32{
		"VENDOR_UNSPECIFIED": 0,
		"FEDERAL":            1,
		"LIQUILOANS":         2,
		"IDFC":               3,
		"FIFTYFIN":           4,
		"ABFL":               5,
		"EPIFI_TECH":         6,
		"MONEYVIEW":          7,
		"VENDOR_MF_CENTRAL":  8,
		"STOCK_GUARDIAN_LSP": 9,
		"LENDEN":             10,
	}
)

func (x Vendor) Enum() *Vendor {
	p := new(Vendor)
	*p = x
	return p
}

func (x Vendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[0].Descriptor()
}

func (Vendor) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[0]
}

func (x Vendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vendor.Descriptor instead.
func (Vendor) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{0}
}

type LoanRequestType int32

const (
	LoanRequestType_LOAN_REQUEST_TYPE_UNSPECIFIED         LoanRequestType = 0
	LoanRequestType_LOAN_REQUEST_TYPE_CREATION            LoanRequestType = 1 // Represents LoanRequests in Creation Flow (Offer, Application)
	LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE             LoanRequestType = 2 // Represents LoanRequests in Closure Flow (Closure, Enquiry)
	LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY         LoanRequestType = 3
	LoanRequestType_LOAN_REQUEST_TYPE_NEW_PORTFOLIO_FETCH LoanRequestType = 4
	LoanRequestType_LOAN_REQUEST_TYPE_REFRESH_PORTFOLIO   LoanRequestType = 5
	LoanRequestType_LOAN_REQUEST_TYPE_UPDATE_USER         LoanRequestType = 6
	LoanRequestType_LOAN_REQUEST_TYPE_SETUP_SI            LoanRequestType = 7
	// This type of loan request is used for non-financial updates required in mutual fund folios, such as updating phone number and email address associated with the folios.
	LoanRequestType_LOAN_REQUEST_TYPE_MUTUAL_FUND_NFT LoanRequestType = 8
	LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY LoanRequestType = 9
)

// Enum value maps for LoanRequestType.
var (
	LoanRequestType_name = map[int32]string{
		0: "LOAN_REQUEST_TYPE_UNSPECIFIED",
		1: "LOAN_REQUEST_TYPE_CREATION",
		2: "LOAN_REQUEST_TYPE_CLOSURE",
		3: "LOAN_REQUEST_TYPE_ELIGIBILITY",
		4: "LOAN_REQUEST_TYPE_NEW_PORTFOLIO_FETCH",
		5: "LOAN_REQUEST_TYPE_REFRESH_PORTFOLIO",
		6: "LOAN_REQUEST_TYPE_UPDATE_USER",
		7: "LOAN_REQUEST_TYPE_SETUP_SI",
		8: "LOAN_REQUEST_TYPE_MUTUAL_FUND_NFT",
		9: "LOAN_REQUEST_TYPE_PRE_ELIGIBILITY",
	}
	LoanRequestType_value = map[string]int32{
		"LOAN_REQUEST_TYPE_UNSPECIFIED":         0,
		"LOAN_REQUEST_TYPE_CREATION":            1,
		"LOAN_REQUEST_TYPE_CLOSURE":             2,
		"LOAN_REQUEST_TYPE_ELIGIBILITY":         3,
		"LOAN_REQUEST_TYPE_NEW_PORTFOLIO_FETCH": 4,
		"LOAN_REQUEST_TYPE_REFRESH_PORTFOLIO":   5,
		"LOAN_REQUEST_TYPE_UPDATE_USER":         6,
		"LOAN_REQUEST_TYPE_SETUP_SI":            7,
		"LOAN_REQUEST_TYPE_MUTUAL_FUND_NFT":     8,
		"LOAN_REQUEST_TYPE_PRE_ELIGIBILITY":     9,
	}
)

func (x LoanRequestType) Enum() *LoanRequestType {
	p := new(LoanRequestType)
	*p = x
	return p
}

func (x LoanRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[1].Descriptor()
}

func (LoanRequestType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[1]
}

func (x LoanRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanRequestType.Descriptor instead.
func (LoanRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{1}
}

type LoanRequestStatus int32

const (
	LoanRequestStatus_LOAN_REQUEST_STATUS_UNSPECIFIED LoanRequestStatus = 0
	// Loan request is created in the database and is ready to be processed, i.e. ready for eligibility checks, etc.
	LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED LoanRequestStatus = 1
	// This state is when workflow is blocked for verification through vkyc, liveness, manual review, ot OTP
	// SUBSTATUS: VKYC, LIVENESS, MANUAL REVIEW, OTP
	LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING LoanRequestStatus = 2
	// Failed verification state (not terminal)
	// SUBSTATUS: FAILED_OTP, FAILED_OTP_LAST_ATTEMPT, FAILED_KYC, etc.
	LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED LoanRequestStatus = 3
	// After OTP is verified successfully
	// SUBSTATUS: OTP, LIVENESS, KYC, etc.
	LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED LoanRequestStatus = 4
	// This is the state when loan application request has been raised with vendor.
	// Sub statuses will define at what stage of initialisation are we at.
	LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED LoanRequestStatus = 5
	// Terminal failure states
	// Loan Application is permanently marked failed due to failure at vendor or permanent failure at any of the previous
	// steps such as OTP_MAX_ATTEMPT_FAILURE, MANUAL_REVIEW_FAILURE, etc.
	LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED LoanRequestStatus = 6
	// LOAN APPLICATION SUCCESS STATE
	// Money is disbursed from vendor and is verified in user's account.
	LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS LoanRequestStatus = 7
	// LOAN APPLICATION DISBURSED STATE
	// Money is disbursed from vendor
	LoanRequestStatus_LOAN_REQUEST_STATUS_DISBURSED LoanRequestStatus = 8
	// Caused due to a technical issue such as API failure
	// or some internal system is down. retryable error.
	LoanRequestStatus_LOAN_REQUEST_STATUS_UNKNOWN LoanRequestStatus = 9
	// State when user cancels loan application
	LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED LoanRequestStatus = 10
	// State when all retries have been exhausted and loan request required manual intervention
	LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION LoanRequestStatus = 11
)

// Enum value maps for LoanRequestStatus.
var (
	LoanRequestStatus_name = map[int32]string{
		0:  "LOAN_REQUEST_STATUS_UNSPECIFIED",
		1:  "LOAN_REQUEST_STATUS_CREATED",
		2:  "LOAN_REQUEST_STATUS_PENDING",
		3:  "LOAN_REQUEST_STATUS_VERIFICATION_FAILED",
		4:  "LOAN_REQUEST_STATUS_VERIFIED",
		5:  "LOAN_REQUEST_STATUS_INITIATED",
		6:  "LOAN_REQUEST_STATUS_FAILED",
		7:  "LOAN_REQUEST_STATUS_SUCCESS",
		8:  "LOAN_REQUEST_STATUS_DISBURSED",
		9:  "LOAN_REQUEST_STATUS_UNKNOWN",
		10: "LOAN_REQUEST_STATUS_CANCELLED",
		11: "LOAN_REQUEST_STATUS_MANUAL_INTERVENTION",
	}
	LoanRequestStatus_value = map[string]int32{
		"LOAN_REQUEST_STATUS_UNSPECIFIED":         0,
		"LOAN_REQUEST_STATUS_CREATED":             1,
		"LOAN_REQUEST_STATUS_PENDING":             2,
		"LOAN_REQUEST_STATUS_VERIFICATION_FAILED": 3,
		"LOAN_REQUEST_STATUS_VERIFIED":            4,
		"LOAN_REQUEST_STATUS_INITIATED":           5,
		"LOAN_REQUEST_STATUS_FAILED":              6,
		"LOAN_REQUEST_STATUS_SUCCESS":             7,
		"LOAN_REQUEST_STATUS_DISBURSED":           8,
		"LOAN_REQUEST_STATUS_UNKNOWN":             9,
		"LOAN_REQUEST_STATUS_CANCELLED":           10,
		"LOAN_REQUEST_STATUS_MANUAL_INTERVENTION": 11,
	}
)

func (x LoanRequestStatus) Enum() *LoanRequestStatus {
	p := new(LoanRequestStatus)
	*p = x
	return p
}

func (x LoanRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[2].Descriptor()
}

func (LoanRequestStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[2]
}

func (x LoanRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanRequestStatus.Descriptor instead.
func (LoanRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{2}
}

type LoanRequestSubStatus int32

const (
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED                           LoanRequestSubStatus = 0
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED                               LoanRequestSubStatus = 1
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC                          LoanRequestSubStatus = 2
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS                      LoanRequestSubStatus = 3
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW                 LoanRequestSubStatus = 4
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP               LoanRequestSubStatus = 5
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP                          LoanRequestSubStatus = 7
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_LIVENESS                     LoanRequestSubStatus = 8
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_MANUAL_REVIEW                LoanRequestSubStatus = 9
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_VKYC                         LoanRequestSubStatus = 10
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED     LoanRequestSubStatus = 11
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_LIVENESS                       LoanRequestSubStatus = 12
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_VKYC                           LoanRequestSubStatus = 13
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_MANUAL_REVIEW                  LoanRequestSubStatus = 14
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS              LoanRequestSubStatus = 15
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS          LoanRequestSubStatus = 16
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW_IN_PROGRESS     LoanRequestSubStatus = 17
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_IN_PROGRESS                 LoanRequestSubStatus = 18
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR                   LoanRequestSubStatus = 19
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_SUCCESS                               LoanRequestSubStatus = 20
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH                    LoanRequestSubStatus = 21
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH_IN_PROGRESS        LoanRequestSubStatus = 22
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_FACE_MATCH                     LoanRequestSubStatus = 23
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_FACE_MATCH                   LoanRequestSubStatus = 24
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_OTP                           LoanRequestSubStatus = 25
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_OTP_GENERATED                         LoanRequestSubStatus = 26
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CANCELLED                             LoanRequestSubStatus = 27
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS                           LoanRequestSubStatus = 28
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS_IN_PROGRESS               LoanRequestSubStatus = 29
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_KFS                            LoanRequestSubStatus = 30
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_KFS                          LoanRequestSubStatus = 31
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION             LoanRequestSubStatus = 32
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_PROFILE_VALIDATION           LoanRequestSubStatus = 33
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION            LoanRequestSubStatus = 34
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_AFTER_VKYC           LoanRequestSubStatus = 35
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS_AT_BANK                   LoanRequestSubStatus = 36
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT                    LoanRequestSubStatus = 37
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_RISK_CHECK                     LoanRequestSubStatus = 38
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_PRE_CONDITION                  LoanRequestSubStatus = 39
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED                    LoanRequestSubStatus = 40
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED                    LoanRequestSubStatus = 41
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED                    LoanRequestSubStatus = 42
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND                   LoanRequestSubStatus = 43
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED                  LoanRequestSubStatus = 44
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOW_PORTFOLIO                         LoanRequestSubStatus = 45
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_EMAIL_SELECTION_FAILURE               LoanRequestSubStatus = 46
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED        LoanRequestSubStatus = 47
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED        LoanRequestSubStatus = 48
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_DETAILED_FETCH_FAILED             LoanRequestSubStatus = 49
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS LoanRequestSubStatus = 50
	LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED  LoanRequestSubStatus = 51
)

// Enum value maps for LoanRequestSubStatus.
var (
	LoanRequestSubStatus_name = map[int32]string{
		0:  "LOAN_REQUEST_SUB_STATUS_UNSPECIFIED",
		1:  "LOAN_REQUEST_SUB_STATUS_CREATED",
		2:  "LOAN_REQUEST_SUB_STATUS_PENDING_VKYC",
		3:  "LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS",
		4:  "LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW",
		5:  "LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP",
		7:  "LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP",
		8:  "LOAN_REQUEST_SUB_STATUS_VERIFIED_LIVENESS",
		9:  "LOAN_REQUEST_SUB_STATUS_VERIFIED_MANUAL_REVIEW",
		10: "LOAN_REQUEST_SUB_STATUS_VERIFIED_VKYC",
		11: "LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED",
		12: "LOAN_REQUEST_SUB_STATUS_FAILED_LIVENESS",
		13: "LOAN_REQUEST_SUB_STATUS_FAILED_VKYC",
		14: "LOAN_REQUEST_SUB_STATUS_FAILED_MANUAL_REVIEW",
		15: "LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS",
		16: "LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS",
		17: "LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW_IN_PROGRESS",
		18: "LOAN_REQUEST_SUB_STATUS_INITIATED_IN_PROGRESS",
		19: "LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR",
		20: "LOAN_REQUEST_SUB_STATUS_SUCCESS",
		21: "LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH",
		22: "LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH_IN_PROGRESS",
		23: "LOAN_REQUEST_SUB_STATUS_FAILED_FACE_MATCH",
		24: "LOAN_REQUEST_SUB_STATUS_VERIFIED_FACE_MATCH",
		25: "LOAN_REQUEST_SUB_STATUS_PENDING_OTP",
		26: "LOAN_REQUEST_SUB_STATUS_OTP_GENERATED",
		27: "LOAN_REQUEST_SUB_STATUS_CANCELLED",
		28: "LOAN_REQUEST_SUB_STATUS_PENDING_KFS",
		29: "LOAN_REQUEST_SUB_STATUS_PENDING_KFS_IN_PROGRESS",
		30: "LOAN_REQUEST_SUB_STATUS_FAILED_KFS",
		31: "LOAN_REQUEST_SUB_STATUS_VERIFIED_KFS",
		32: "LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION",
		33: "LOAN_REQUEST_SUB_STATUS_VERIFIED_PROFILE_VALIDATION",
		34: "LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION",
		35: "LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_AFTER_VKYC",
		36: "LOAN_REQUEST_SUB_STATUS_PENDING_KFS_AT_BANK",
		37: "LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT",
		38: "LOAN_REQUEST_SUB_STATUS_FAILED_RISK_CHECK",
		39: "LOAN_REQUEST_SUB_STATUS_FAILED_PRE_CONDITION",
		40: "LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED",
		41: "LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED",
		42: "LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED",
		43: "LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND",
		44: "LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED",
		45: "LOAN_REQUEST_SUB_STATUS_LOW_PORTFOLIO",
		46: "LOAN_REQUEST_SUB_STATUS_EMAIL_SELECTION_FAILURE",
		47: "LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED",
		48: "LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED",
		49: "LOAN_REQUEST_SUB_STATUS_CAS_DETAILED_FETCH_FAILED",
		50: "LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS",
		51: "LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED",
	}
	LoanRequestSubStatus_value = map[string]int32{
		"LOAN_REQUEST_SUB_STATUS_UNSPECIFIED":                           0,
		"LOAN_REQUEST_SUB_STATUS_CREATED":                               1,
		"LOAN_REQUEST_SUB_STATUS_PENDING_VKYC":                          2,
		"LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS":                      3,
		"LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW":                 4,
		"LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP":               5,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP":                          7,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_LIVENESS":                     8,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_MANUAL_REVIEW":                9,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_VKYC":                         10,
		"LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED":     11,
		"LOAN_REQUEST_SUB_STATUS_FAILED_LIVENESS":                       12,
		"LOAN_REQUEST_SUB_STATUS_FAILED_VKYC":                           13,
		"LOAN_REQUEST_SUB_STATUS_FAILED_MANUAL_REVIEW":                  14,
		"LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS":              15,
		"LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS":          16,
		"LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW_IN_PROGRESS":     17,
		"LOAN_REQUEST_SUB_STATUS_INITIATED_IN_PROGRESS":                 18,
		"LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR":                   19,
		"LOAN_REQUEST_SUB_STATUS_SUCCESS":                               20,
		"LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH":                    21,
		"LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH_IN_PROGRESS":        22,
		"LOAN_REQUEST_SUB_STATUS_FAILED_FACE_MATCH":                     23,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_FACE_MATCH":                   24,
		"LOAN_REQUEST_SUB_STATUS_PENDING_OTP":                           25,
		"LOAN_REQUEST_SUB_STATUS_OTP_GENERATED":                         26,
		"LOAN_REQUEST_SUB_STATUS_CANCELLED":                             27,
		"LOAN_REQUEST_SUB_STATUS_PENDING_KFS":                           28,
		"LOAN_REQUEST_SUB_STATUS_PENDING_KFS_IN_PROGRESS":               29,
		"LOAN_REQUEST_SUB_STATUS_FAILED_KFS":                            30,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_KFS":                          31,
		"LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION":             32,
		"LOAN_REQUEST_SUB_STATUS_VERIFIED_PROFILE_VALIDATION":           33,
		"LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION":            34,
		"LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_AFTER_VKYC":           35,
		"LOAN_REQUEST_SUB_STATUS_PENDING_KFS_AT_BANK":                   36,
		"LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT":                    37,
		"LOAN_REQUEST_SUB_STATUS_FAILED_RISK_CHECK":                     38,
		"LOAN_REQUEST_SUB_STATUS_FAILED_PRE_CONDITION":                  39,
		"LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED":                    40,
		"LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED":                    41,
		"LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED":                    42,
		"LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND":                   43,
		"LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED":                  44,
		"LOAN_REQUEST_SUB_STATUS_LOW_PORTFOLIO":                         45,
		"LOAN_REQUEST_SUB_STATUS_EMAIL_SELECTION_FAILURE":               46,
		"LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED":        47,
		"LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED":        48,
		"LOAN_REQUEST_SUB_STATUS_CAS_DETAILED_FETCH_FAILED":             49,
		"LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS": 50,
		"LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED":  51,
	}
)

func (x LoanRequestSubStatus) Enum() *LoanRequestSubStatus {
	p := new(LoanRequestSubStatus)
	*p = x
	return p
}

func (x LoanRequestSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanRequestSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[3].Descriptor()
}

func (LoanRequestSubStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[3]
}

func (x LoanRequestSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanRequestSubStatus.Descriptor instead.
func (LoanRequestSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{3}
}

type LoanAccountStatus int32

const (
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED LoanAccountStatus = 0
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE      LoanAccountStatus = 1
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED      LoanAccountStatus = 2
)

// Enum value maps for LoanAccountStatus.
var (
	LoanAccountStatus_name = map[int32]string{
		0: "LOAN_ACCOUNT_STATUS_UNSPECIFIED",
		1: "LOAN_ACCOUNT_STATUS_ACTIVE",
		2: "LOAN_ACCOUNT_STATUS_CLOSED",
	}
	LoanAccountStatus_value = map[string]int32{
		"LOAN_ACCOUNT_STATUS_UNSPECIFIED": 0,
		"LOAN_ACCOUNT_STATUS_ACTIVE":      1,
		"LOAN_ACCOUNT_STATUS_CLOSED":      2,
	}
)

func (x LoanAccountStatus) Enum() *LoanAccountStatus {
	p := new(LoanAccountStatus)
	*p = x
	return p
}

func (x LoanAccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanAccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[4].Descriptor()
}

func (LoanAccountStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[4]
}

func (x LoanAccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanAccountStatus.Descriptor instead.
func (LoanAccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{4}
}

type LoanType int32

const (
	// LoanType specifies the particular kind of loan product.
	// Ex: Personal loan, home loan, etc.
	// Please ensure that there is a one-to-one mapping between frontend &
	// backend loanType enum values, so that the conversion is automatically handled.
	LoanType_LOAN_TYPE_UNSPECIFIED  LoanType = 0
	LoanType_LOAN_TYPE_PERSONAL     LoanType = 1
	LoanType_LOAN_TYPE_EARLY_SALARY LoanType = 2
	LoanType_LOAN_TYPE_SECURED_LOAN LoanType = 3
)

// Enum value maps for LoanType.
var (
	LoanType_name = map[int32]string{
		0: "LOAN_TYPE_UNSPECIFIED",
		1: "LOAN_TYPE_PERSONAL",
		2: "LOAN_TYPE_EARLY_SALARY",
		3: "LOAN_TYPE_SECURED_LOAN",
	}
	LoanType_value = map[string]int32{
		"LOAN_TYPE_UNSPECIFIED":  0,
		"LOAN_TYPE_PERSONAL":     1,
		"LOAN_TYPE_EARLY_SALARY": 2,
		"LOAN_TYPE_SECURED_LOAN": 3,
	}
)

func (x LoanType) Enum() *LoanType {
	p := new(LoanType)
	*p = x
	return p
}

func (x LoanType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[5].Descriptor()
}

func (LoanType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[5]
}

func (x LoanType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanType.Descriptor instead.
func (LoanType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{5}
}

type LoanRequestFieldMask int32

const (
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_UNSPECIFIED         LoanRequestFieldMask = 0
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_OFFER_ID            LoanRequestFieldMask = 1
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_ORCH_ID             LoanRequestFieldMask = 2
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER LoanRequestFieldMask = 3
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID   LoanRequestFieldMask = 4
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_VENDOR              LoanRequestFieldMask = 5
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS             LoanRequestFieldMask = 6
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_TYPE                LoanRequestFieldMask = 7
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS              LoanRequestFieldMask = 8
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS          LoanRequestFieldMask = 9
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT        LoanRequestFieldMask = 10
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION         LoanRequestFieldMask = 11
	LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_PROGRAM        LoanRequestFieldMask = 12
)

// Enum value maps for LoanRequestFieldMask.
var (
	LoanRequestFieldMask_name = map[int32]string{
		0:  "LOAN_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_REQUEST_FIELD_MASK_OFFER_ID",
		2:  "LOAN_REQUEST_FIELD_MASK_ORCH_ID",
		3:  "LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER",
		4:  "LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID",
		5:  "LOAN_REQUEST_FIELD_MASK_VENDOR",
		6:  "LOAN_REQUEST_FIELD_MASK_DETAILS",
		7:  "LOAN_REQUEST_FIELD_MASK_TYPE",
		8:  "LOAN_REQUEST_FIELD_MASK_STATUS",
		9:  "LOAN_REQUEST_FIELD_MASK_SUB_STATUS",
		10: "LOAN_REQUEST_FIELD_MASK_COMPLETED_AT",
		11: "LOAN_REQUEST_FIELD_MASK_NEXT_ACTION",
		12: "LOAN_REQUEST_FIELD_MASK_LOAN_PROGRAM",
	}
	LoanRequestFieldMask_value = map[string]int32{
		"LOAN_REQUEST_FIELD_MASK_UNSPECIFIED":         0,
		"LOAN_REQUEST_FIELD_MASK_OFFER_ID":            1,
		"LOAN_REQUEST_FIELD_MASK_ORCH_ID":             2,
		"LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER": 3,
		"LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID":   4,
		"LOAN_REQUEST_FIELD_MASK_VENDOR":              5,
		"LOAN_REQUEST_FIELD_MASK_DETAILS":             6,
		"LOAN_REQUEST_FIELD_MASK_TYPE":                7,
		"LOAN_REQUEST_FIELD_MASK_STATUS":              8,
		"LOAN_REQUEST_FIELD_MASK_SUB_STATUS":          9,
		"LOAN_REQUEST_FIELD_MASK_COMPLETED_AT":        10,
		"LOAN_REQUEST_FIELD_MASK_NEXT_ACTION":         11,
		"LOAN_REQUEST_FIELD_MASK_LOAN_PROGRAM":        12,
	}
)

func (x LoanRequestFieldMask) Enum() *LoanRequestFieldMask {
	p := new(LoanRequestFieldMask)
	*p = x
	return p
}

func (x LoanRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[6].Descriptor()
}

func (LoanRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[6]
}

func (x LoanRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanRequestFieldMask.Descriptor instead.
func (LoanRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{6}
}

type LoanAccountFieldMask int32

const (
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED          LoanAccountFieldMask = 0
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_ACCOUNT_NUMBER  LoanAccountFieldMask = 1
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_TYPE            LoanAccountFieldMask = 2
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_IFSC_CODE            LoanAccountFieldMask = 3
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_AMOUNT          LoanAccountFieldMask = 4
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DISBURSED_AMOUNT     LoanAccountFieldMask = 5
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_OUTSTANDING_AMOUNT   LoanAccountFieldMask = 6
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_TOTAL_PAYABLE_AMOUNT LoanAccountFieldMask = 7
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE        LoanAccountFieldMask = 8
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_MATURITY_DATE        LoanAccountFieldMask = 9
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_VENDOR               LoanAccountFieldMask = 10
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DETAILS              LoanAccountFieldMask = 11
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS               LoanAccountFieldMask = 12
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LMS_PARTNER_LOAN_ID  LoanAccountFieldMask = 13
)

// Enum value maps for LoanAccountFieldMask.
var (
	LoanAccountFieldMask_name = map[int32]string{
		0:  "LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_ACCOUNT_FIELD_MASK_LOAN_ACCOUNT_NUMBER",
		2:  "LOAN_ACCOUNT_FIELD_MASK_LOAN_TYPE",
		3:  "LOAN_ACCOUNT_FIELD_MASK_IFSC_CODE",
		4:  "LOAN_ACCOUNT_FIELD_MASK_LOAN_AMOUNT",
		5:  "LOAN_ACCOUNT_FIELD_MASK_DISBURSED_AMOUNT",
		6:  "LOAN_ACCOUNT_FIELD_MASK_OUTSTANDING_AMOUNT",
		7:  "LOAN_ACCOUNT_FIELD_MASK_TOTAL_PAYABLE_AMOUNT",
		8:  "LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE",
		9:  "LOAN_ACCOUNT_FIELD_MASK_MATURITY_DATE",
		10: "LOAN_ACCOUNT_FIELD_MASK_VENDOR",
		11: "LOAN_ACCOUNT_FIELD_MASK_DETAILS",
		12: "LOAN_ACCOUNT_FIELD_MASK_STATUS",
		13: "LOAN_ACCOUNT_FIELD_MASK_LMS_PARTNER_LOAN_ID",
	}
	LoanAccountFieldMask_value = map[string]int32{
		"LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED":          0,
		"LOAN_ACCOUNT_FIELD_MASK_LOAN_ACCOUNT_NUMBER":  1,
		"LOAN_ACCOUNT_FIELD_MASK_LOAN_TYPE":            2,
		"LOAN_ACCOUNT_FIELD_MASK_IFSC_CODE":            3,
		"LOAN_ACCOUNT_FIELD_MASK_LOAN_AMOUNT":          4,
		"LOAN_ACCOUNT_FIELD_MASK_DISBURSED_AMOUNT":     5,
		"LOAN_ACCOUNT_FIELD_MASK_OUTSTANDING_AMOUNT":   6,
		"LOAN_ACCOUNT_FIELD_MASK_TOTAL_PAYABLE_AMOUNT": 7,
		"LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE":        8,
		"LOAN_ACCOUNT_FIELD_MASK_MATURITY_DATE":        9,
		"LOAN_ACCOUNT_FIELD_MASK_VENDOR":               10,
		"LOAN_ACCOUNT_FIELD_MASK_DETAILS":              11,
		"LOAN_ACCOUNT_FIELD_MASK_STATUS":               12,
		"LOAN_ACCOUNT_FIELD_MASK_LMS_PARTNER_LOAN_ID":  13,
	}
)

func (x LoanAccountFieldMask) Enum() *LoanAccountFieldMask {
	p := new(LoanAccountFieldMask)
	*p = x
	return p
}

func (x LoanAccountFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanAccountFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[7].Descriptor()
}

func (LoanAccountFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[7]
}

func (x LoanAccountFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanAccountFieldMask.Descriptor instead.
func (LoanAccountFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{7}
}

type LoanOfferFieldMask int32

const (
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_UNSPECIFIED       LoanOfferFieldMask = 0
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID   LoanOfferFieldMask = 1
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS LoanOfferFieldMask = 2
	// deprecated since an offer (by design) is an immutable entity and therefore its configuration shouldn't be updated once the offer is created.
	// If anything needs to be changed in the offer config, then the existing offer should be marked as deactivated and
	// a new instance of the offer should be created for auditing purposes
	//
	// Deprecated: Marked as deprecated in api/preapprovedloan/enums.proto.
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_PRECESSING_INFO                    LoanOfferFieldMask = 3
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT                     LoanOfferFieldMask = 4
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_UPDATED_AT                         LoanOfferFieldMask = 5
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LOAN_OFFER_ELIGIBILITY_CRITERIA_ID LoanOfferFieldMask = 6
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LOAN_PROGRAM                       LoanOfferFieldMask = 7
	LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LAST_VIEWED_AT                     LoanOfferFieldMask = 8
)

// Enum value maps for LoanOfferFieldMask.
var (
	LoanOfferFieldMask_name = map[int32]string{
		0: "LOAN_OFFER_FIELD_MASK_UNSPECIFIED",
		1: "LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID",
		2: "LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS",
		3: "LOAN_OFFER_FIELD_MASK_PRECESSING_INFO",
		4: "LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT",
		5: "LOAN_OFFER_FIELD_MASK_UPDATED_AT",
		6: "LOAN_OFFER_FIELD_MASK_LOAN_OFFER_ELIGIBILITY_CRITERIA_ID",
		7: "LOAN_OFFER_FIELD_MASK_LOAN_PROGRAM",
		8: "LOAN_OFFER_FIELD_MASK_LAST_VIEWED_AT",
	}
	LoanOfferFieldMask_value = map[string]int32{
		"LOAN_OFFER_FIELD_MASK_UNSPECIFIED":                        0,
		"LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID":                    1,
		"LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS":                  2,
		"LOAN_OFFER_FIELD_MASK_PRECESSING_INFO":                    3,
		"LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT":                     4,
		"LOAN_OFFER_FIELD_MASK_UPDATED_AT":                         5,
		"LOAN_OFFER_FIELD_MASK_LOAN_OFFER_ELIGIBILITY_CRITERIA_ID": 6,
		"LOAN_OFFER_FIELD_MASK_LOAN_PROGRAM":                       7,
		"LOAN_OFFER_FIELD_MASK_LAST_VIEWED_AT":                     8,
	}
)

func (x LoanOfferFieldMask) Enum() *LoanOfferFieldMask {
	p := new(LoanOfferFieldMask)
	*p = x
	return p
}

func (x LoanOfferFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[8].Descriptor()
}

func (LoanOfferFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[8]
}

func (x LoanOfferFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferFieldMask.Descriptor instead.
func (LoanOfferFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{8}
}

type LoanApplicantFieldMask int32

const (
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_UNSPECIFIED            LoanApplicantFieldMask = 0
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_STATUS                 LoanApplicantFieldMask = 1
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_SUB_STATUS             LoanApplicantFieldMask = 2
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID    LoanApplicantFieldMask = 3
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_REQUEST_ID      LoanApplicantFieldMask = 4
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_EMPLOYMENT_DETAILS     LoanApplicantFieldMask = 5
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS       LoanApplicantFieldMask = 6
	LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_LOAN_APPLICANT_DETAILS LoanApplicantFieldMask = 7
)

// Enum value maps for LoanApplicantFieldMask.
var (
	LoanApplicantFieldMask_name = map[int32]string{
		0: "LOAN_APPLICANT_FIELD_MASK_UNSPECIFIED",
		1: "LOAN_APPLICANT_FIELD_MASK_STATUS",
		2: "LOAN_APPLICANT_FIELD_MASK_SUB_STATUS",
		3: "LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID",
		4: "LOAN_APPLICANT_FIELD_MASK_VENDOR_REQUEST_ID",
		5: "LOAN_APPLICANT_FIELD_MASK_EMPLOYMENT_DETAILS",
		6: "LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS",
		7: "LOAN_APPLICANT_FIELD_MASK_LOAN_APPLICANT_DETAILS",
	}
	LoanApplicantFieldMask_value = map[string]int32{
		"LOAN_APPLICANT_FIELD_MASK_UNSPECIFIED":            0,
		"LOAN_APPLICANT_FIELD_MASK_STATUS":                 1,
		"LOAN_APPLICANT_FIELD_MASK_SUB_STATUS":             2,
		"LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID":    3,
		"LOAN_APPLICANT_FIELD_MASK_VENDOR_REQUEST_ID":      4,
		"LOAN_APPLICANT_FIELD_MASK_EMPLOYMENT_DETAILS":     5,
		"LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS":       6,
		"LOAN_APPLICANT_FIELD_MASK_LOAN_APPLICANT_DETAILS": 7,
	}
)

func (x LoanApplicantFieldMask) Enum() *LoanApplicantFieldMask {
	p := new(LoanApplicantFieldMask)
	*p = x
	return p
}

func (x LoanApplicantFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicantFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[9].Descriptor()
}

func (LoanApplicantFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[9]
}

func (x LoanApplicantFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicantFieldMask.Descriptor instead.
func (LoanApplicantFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{9}
}

type LoanStepExecutionStatus int32

const (
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED         LoanStepExecutionStatus = 0
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED             LoanStepExecutionStatus = 1
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS         LoanStepExecutionStatus = 2
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED              LoanStepExecutionStatus = 3
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS             LoanStepExecutionStatus = 4
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED           LoanStepExecutionStatus = 5
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION LoanStepExecutionStatus = 6
	// LSE will be maked in EXPIRED status when stage gets failed due to retry exhaustion/ timed out on waiting for user action.
	LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED LoanStepExecutionStatus = 7
)

// Enum value maps for LoanStepExecutionStatus.
var (
	LoanStepExecutionStatus_name = map[int32]string{
		0: "LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED",
		1: "LOAN_STEP_EXECUTION_STATUS_CREATED",
		2: "LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS",
		3: "LOAN_STEP_EXECUTION_STATUS_FAILED",
		4: "LOAN_STEP_EXECUTION_STATUS_SUCCESS",
		5: "LOAN_STEP_EXECUTION_STATUS_CANCELLED",
		6: "LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION",
		7: "LOAN_STEP_EXECUTION_STATUS_EXPIRED",
	}
	LoanStepExecutionStatus_value = map[string]int32{
		"LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED":         0,
		"LOAN_STEP_EXECUTION_STATUS_CREATED":             1,
		"LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS":         2,
		"LOAN_STEP_EXECUTION_STATUS_FAILED":              3,
		"LOAN_STEP_EXECUTION_STATUS_SUCCESS":             4,
		"LOAN_STEP_EXECUTION_STATUS_CANCELLED":           5,
		"LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION": 6,
		"LOAN_STEP_EXECUTION_STATUS_EXPIRED":             7,
	}
)

func (x LoanStepExecutionStatus) Enum() *LoanStepExecutionStatus {
	p := new(LoanStepExecutionStatus)
	*p = x
	return p
}

func (x LoanStepExecutionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanStepExecutionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[10].Descriptor()
}

func (LoanStepExecutionStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[10]
}

func (x LoanStepExecutionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanStepExecutionStatus.Descriptor instead.
func (LoanStepExecutionStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{10}
}

type LoanStepExecutionSubStatus int32

const (
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED                 LoanStepExecutionSubStatus = 0
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_CREATED           LoanStepExecutionSubStatus = 1
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED    LoanStepExecutionSubStatus = 2
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED       LoanStepExecutionSubStatus = 3
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED        LoanStepExecutionSubStatus = 4
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW               LoanStepExecutionSubStatus = 5
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_OTP_FAILED            LoanStepExecutionSubStatus = 6
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR LoanStepExecutionSubStatus = 7
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED        LoanStepExecutionSubStatus = 8
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_STATUS_FETCHED    LoanStepExecutionSubStatus = 9
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_VERIFIED                LoanStepExecutionSubStatus = 10
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED       LoanStepExecutionSubStatus = 11
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED                   LoanStepExecutionSubStatus = 12
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED    LoanStepExecutionSubStatus = 13
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED          LoanStepExecutionSubStatus = 14
	// Deprecated: Marked as deprecated in api/preapprovedloan/enums.proto.
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD                   LoanStepExecutionSubStatus = 15
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RISKY_USER_CHECK                       LoanStepExecutionSubStatus = 16
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RISK_CREDIT_CHECK                      LoanStepExecutionSubStatus = 17
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_USER_BALANCE_IS_ZERO LoanStepExecutionSubStatus = 18
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_EXECUTED             LoanStepExecutionSubStatus = 19
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED               LoanStepExecutionSubStatus = 20
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND              LoanStepExecutionSubStatus = 21
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CREDIT_REPORT_FETCH_INITIATED          LoanStepExecutionSubStatus = 22
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_EXPIRED                           LoanStepExecutionSubStatus = 23
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_LINKED                     LoanStepExecutionSubStatus = 24
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_NOT_LINKED                 LoanStepExecutionSubStatus = 25
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFIED                           LoanStepExecutionSubStatus = 26
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_INITIATED                         LoanStepExecutionSubStatus = 27
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BANK_LINKED                            LoanStepExecutionSubStatus = 28
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED                          LoanStepExecutionSubStatus = 29
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_SIGNED                 LoanStepExecutionSubStatus = 30
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MISSING_BANK_DETAILS                   LoanStepExecutionSubStatus = 31
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE                 LoanStepExecutionSubStatus = 32
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_LIEN_MARKED_PORTFOLIO               LoanStepExecutionSubStatus = 33
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LIEN_MARKED_PORTFOLIO     LoanStepExecutionSubStatus = 34
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATED_SUCCESSFULLY          LoanStepExecutionSubStatus = 35
	// can be used when waiting on any data pull or polling an api
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_WAITING_FOR_DATA          LoanStepExecutionSubStatus = 36
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_MAKER_CHECK_APPROVED LoanStepExecutionSubStatus = 37
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_VERIFICATION_FAILED  LoanStepExecutionSubStatus = 38
	// we were not able to mark lien on entire portfolio but the marked portfolio is enough to
	// disburse the requested loan amount.
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PARTIAL_LIEN_MARKED  LoanStepExecutionSubStatus = 39
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND   LoanStepExecutionSubStatus = 40
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE  LoanStepExecutionSubStatus = 41
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED LoanStepExecutionSubStatus = 42
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_EXPIRED        LoanStepExecutionSubStatus = 43
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED   LoanStepExecutionSubStatus = 44
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED   LoanStepExecutionSubStatus = 45
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_REJECTED         LoanStepExecutionSubStatus = 46
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_EXPIRED          LoanStepExecutionSubStatus = 47
	// applicable for LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION
	// accounts connected but at least one of the accounts is in DATA_SYNC_PENDING status
	// we cannot estimate income from accounts in this status, we have to wait until status changes to DATA_SYNC_ON
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CA_DATA_SYNC_PENDING LoanStepExecutionSubStatus = 48
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED              LoanStepExecutionSubStatus = 49
	// user gets rejected at vendor's end as user details (PAN, phnNum etc..) already registered with vendor against some other user
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DEDUPE_CHECK_FAILURE LoanStepExecutionSubStatus = 50
	// user got rejected during BRE checks run at the vendor's end
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION                 LoanStepExecutionSubStatus = 51
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN               LoanStepExecutionSubStatus = 52
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE         LoanStepExecutionSubStatus = 53
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED     LoanStepExecutionSubStatus = 54
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS            LoanStepExecutionSubStatus = 55
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT         LoanStepExecutionSubStatus = 56
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED                         LoanStepExecutionSubStatus = 57
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED                        LoanStepExecutionSubStatus = 58
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE LoanStepExecutionSubStatus = 59
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_AUTHENTICATION_FAILED  LoanStepExecutionSubStatus = 60
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_VALIDATION_FAILED      LoanStepExecutionSubStatus = 61
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PAN_AND_KYC_MISMATCH   LoanStepExecutionSubStatus = 62
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_CKYC_ACCOUNT_TYPE      LoanStepExecutionSubStatus = 63
	// sub status to be used when user is out of india
	// example usage: while doing IDFC VKYC, we set this sub-status when the user is out of india
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_OUT_OF_INDIA LoanStepExecutionSubStatus = 64
	// applicable for LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CONNECTED_ACCOUNTS_NOT_FOUND                LoanStepExecutionSubStatus = 65
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ALL_CONNECTED_ACCOUNTS_ARE_CURRENT_ACCOUNTS LoanStepExecutionSubStatus = 66
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_STALE_CONNECTED_ACCOUNTS                    LoanStepExecutionSubStatus = 67
	// valid cases where income could not be estimated using the existing connected accounts
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_CANNOT_BE_ESTIMATED                  LoanStepExecutionSubStatus = 68
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_FAILED_WITH_UNKNOWN_REASON LoanStepExecutionSubStatus = 69
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_LOW_CONFIDENCE             LoanStepExecutionSubStatus = 70
	// user started connected account flow for income verification
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CA_FLOW_STARTED                                 LoanStepExecutionSubStatus = 71
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LOAN_AMOUNT_POST_FUND_VERIFICATION LoanStepExecutionSubStatus = 72
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_AGE_CRITERIA_NOT_SATISFIED          LoanStepExecutionSubStatus = 73
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED       LoanStepExecutionSubStatus = 74
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_AADHAAR_DETAILS_ADDED                           LoanStepExecutionSubStatus = 75
	// when ckyc record is not found for the user
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND                     LoanStepExecutionSubStatus = 76
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_SELECTED_BANK_NOT_FOUND   LoanStepExecutionSubStatus = 77
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_MERCHANT_CODE_MISMATCH    LoanStepExecutionSubStatus = 78
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_DUPLICATE_TXN_ID          LoanStepExecutionSubStatus = 79
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_IMPS_NAME_EMPTY           LoanStepExecutionSubStatus = 80
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_PAN_NAME_EMPTY            LoanStepExecutionSubStatus = 81
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_NAME_MISMATCH             LoanStepExecutionSubStatus = 82
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_ACCOUNT_HOLDER_NAME_EMPTY LoanStepExecutionSubStatus = 83
	// example usage: IDFCValidateOffer activity
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PHOTO_MATCH_FAILED                                       LoanStepExecutionSubStatus = 84
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EPFO_DATA_NOT_FOUND                                      LoanStepExecutionSubStatus = 85
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_APPLICATION_NOT_FOUND_AT_VENDOR                          LoanStepExecutionSubStatus = 86
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LEAD_CANCELLATION_FAILED                                 LoanStepExecutionSubStatus = 87
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_NOT_FOUNT                    LoanStepExecutionSubStatus = 88
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_REVOKED                      LoanStepExecutionSubStatus = 89
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_AMOUNT_LESS_THAN_MINIMUM        LoanStepExecutionSubStatus = 90
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DOB_MISMATCH                                        LoanStepExecutionSubStatus = 91
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_MISMATCH                              LoanStepExecutionSubStatus = 92
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_DOES_NOT_EXIST_FOR_CKYC_NUMBER        LoanStepExecutionSubStatus = 93
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_INVALID_VALUE_ENTERED_IN_AUTHENTICATION_FACTOR_TYPE LoanStepExecutionSubStatus = 94
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_IMAGES_CANNOT_BE_LOADED                             LoanStepExecutionSubStatus = 95
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_FAILED                                              LoanStepExecutionSubStatus = 96
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_EMI_GREATER_THAN_MAX_EMI_ALLOWED                LoanStepExecutionSubStatus = 97
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_AMOUNT_IS_NOT_WITHIN_THE_PRESCRIBED_LIMITS      LoanStepExecutionSubStatus = 98
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_ACTION_WAIT_TIME_OUT                                LoanStepExecutionSubStatus = 99
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_MANDATE_REVOKED                        LoanStepExecutionSubStatus = 100
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_MAX_ATTEMPTS_REACHED                             LoanStepExecutionSubStatus = 101
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILED_FROM_VENDOR                               LoanStepExecutionSubStatus = 102
	// denotes that mandate init deeplink is generated for user to take action and setup auto-pay
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_DEEPLINK_GENERATED                  LoanStepExecutionSubStatus = 103
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_LOAN_APPLICATION_EXPIRED             LoanStepExecutionSubStatus = 104
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_REJECTED_KFS                           LoanStepExecutionSubStatus = 105
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ITR_INTIMATION_STARTED                      LoanStepExecutionSubStatus = 106
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK                         LoanStepExecutionSubStatus = 107
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL              LoanStepExecutionSubStatus = 108
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_NOT_ENTERED                   LoanStepExecutionSubStatus = 109
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_VERIFICATION_FAILED           LoanStepExecutionSubStatus = 110
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_NOT_ENTERED LoanStepExecutionSubStatus = 111
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_FAILED      LoanStepExecutionSubStatus = 112
	// example usage: if we get this error from LL "Monthly income should be between Rs 2,000 to Rs 1,00,00,000"
	// we can fail the loan application with this sub-status
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_NOT_IN_ACCEPTABLE_RANGE_FOR_VENDOR LoanStepExecutionSubStatus = 113
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED            LoanStepExecutionSubStatus = 114
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED            LoanStepExecutionSubStatus = 115
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_WITHDRAWN                            LoanStepExecutionSubStatus = 116
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_INITIATION_FAILED                 LoanStepExecutionSubStatus = 117
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DISBURSAL_TIMED_OUT                       LoanStepExecutionSubStatus = 118
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_STUCK_IN_READY_TO_DISBURSE_STATE          LoanStepExecutionSubStatus = 119
	// used in adding skipping alternate contact information if user is not risky
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIP_NON_RISKY_USER LoanStepExecutionSubStatus = 120
	// used to identify if the user is in cool off period after exhausting attempt limits for OTPs
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD LoanStepExecutionSubStatus = 121
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_WEB_URL_FETCHED                LoanStepExecutionSubStatus = 122
	// can be used when step not relevant for a particular loan process.
	// ex. While processing a full loan amount pre-payment request, we have a step to close user's loan account at lender end.
	// This Closure step is processed only for loan programs/lenders where the functionality is supported, for other
	// programs/lenders the step is skipped with LOAN_STEP_EXECUTION_SUB_STATUS_STEP_INAPPLICABLE sub-status.
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_STEP_INAPPLICABLE                   LoanStepExecutionSubStatus = 123
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_DETAILED_FETCH_FAILED           LoanStepExecutionSubStatus = 124
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LIEN_ATTEMPT_EXPIRED                LoanStepExecutionSubStatus = 125
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ACTIVE_LOAN_WITH_VENDOR             LoanStepExecutionSubStatus = 126
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DOB_DOES_NOT_MATCH_PAN_DATA         LoanStepExecutionSubStatus = 127
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL            LoanStepExecutionSubStatus = 128
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_DROPPED_OFF_FROM_JOURNEY       LoanStepExecutionSubStatus = 129
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PREVIOUS_MANDATE_ALREADY_SUCCESSFUL LoanStepExecutionSubStatus = 130
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_ACCOUNT_ALREADY_CLOSED         LoanStepExecutionSubStatus = 131
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INVALID_DRAWDOWN_ARGUMENT           LoanStepExecutionSubStatus = 132
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SI_NOT_SETUP                        LoanStepExecutionSubStatus = 133
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_INITIATED                 LoanStepExecutionSubStatus = 134
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW                 LoanStepExecutionSubStatus = 135
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_IN_PROGRESS      LoanStepExecutionSubStatus = 136
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE             LoanStepExecutionSubStatus = 137
	// to mark payments failed due to user related issues like insufficient balance, wrong otp etc.
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_FAILED_USER_ERROR LoanStepExecutionSubStatus = 138
	// added bifurcation in skipped
	// 1: Skipped because it is already done
	// 2: Skipped because it is not needed
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_ALREADY_DONE LoanStepExecutionSubStatus = 139
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_NOT_NEEDED   LoanStepExecutionSubStatus = 140
	// User temporarily blocked to prevent excessive retries
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_TEMPORARY_BLOCKED      LoanStepExecutionSubStatus = 141
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_PENDING    LoanStepExecutionSubStatus = 142
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_SUCCESSFUL LoanStepExecutionSubStatus = 143
	LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_FAILED     LoanStepExecutionSubStatus = 144
)

// Enum value maps for LoanStepExecutionSubStatus.
var (
	LoanStepExecutionSubStatus_name = map[int32]string{
		0:   "LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED",
		1:   "LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_CREATED",
		2:   "LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED",
		3:   "LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED",
		4:   "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED",
		5:   "LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW",
		6:   "LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_OTP_FAILED",
		7:   "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR",
		8:   "LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED",
		9:   "LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_STATUS_FETCHED",
		10:  "LOAN_STEP_EXECUTION_SUB_STATUS_PAN_VERIFIED",
		11:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED",
		12:  "LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED",
		13:  "LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED",
		14:  "LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED",
		15:  "LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD",
		16:  "LOAN_STEP_EXECUTION_SUB_STATUS_RISKY_USER_CHECK",
		17:  "LOAN_STEP_EXECUTION_SUB_STATUS_RISK_CREDIT_CHECK",
		18:  "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_USER_BALANCE_IS_ZERO",
		19:  "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_EXECUTED",
		20:  "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED",
		21:  "LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND",
		22:  "LOAN_STEP_EXECUTION_SUB_STATUS_CREDIT_REPORT_FETCH_INITIATED",
		23:  "LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_EXPIRED",
		24:  "LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_LINKED",
		25:  "LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_NOT_LINKED",
		26:  "LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFIED",
		27:  "LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_INITIATED",
		28:  "LOAN_STEP_EXECUTION_SUB_STATUS_BANK_LINKED",
		29:  "LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED",
		30:  "LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_SIGNED",
		31:  "LOAN_STEP_EXECUTION_SUB_STATUS_MISSING_BANK_DETAILS",
		32:  "LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE",
		33:  "LOAN_STEP_EXECUTION_SUB_STATUS_NO_LIEN_MARKED_PORTFOLIO",
		34:  "LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LIEN_MARKED_PORTFOLIO",
		35:  "LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATED_SUCCESSFULLY",
		36:  "LOAN_STEP_EXECUTION_SUB_STATUS_WAITING_FOR_DATA",
		37:  "LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_MAKER_CHECK_APPROVED",
		38:  "LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_VERIFICATION_FAILED",
		39:  "LOAN_STEP_EXECUTION_SUB_STATUS_PARTIAL_LIEN_MARKED",
		40:  "LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND",
		41:  "LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE",
		42:  "LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED",
		43:  "LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_EXPIRED",
		44:  "LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED",
		45:  "LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED",
		46:  "LOAN_STEP_EXECUTION_SUB_STATUS_KYC_REJECTED",
		47:  "LOAN_STEP_EXECUTION_SUB_STATUS_KYC_EXPIRED",
		48:  "LOAN_STEP_EXECUTION_SUB_STATUS_CA_DATA_SYNC_PENDING",
		49:  "LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED",
		50:  "LOAN_STEP_EXECUTION_SUB_STATUS_DEDUPE_CHECK_FAILURE",
		51:  "LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION",
		52:  "LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN",
		53:  "LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE",
		54:  "LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED",
		55:  "LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS",
		56:  "LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT",
		57:  "LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED",
		58:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED",
		59:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE",
		60:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_AUTHENTICATION_FAILED",
		61:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_VALIDATION_FAILED",
		62:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PAN_AND_KYC_MISMATCH",
		63:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_CKYC_ACCOUNT_TYPE",
		64:  "LOAN_STEP_EXECUTION_SUB_STATUS_USER_OUT_OF_INDIA",
		65:  "LOAN_STEP_EXECUTION_SUB_STATUS_CONNECTED_ACCOUNTS_NOT_FOUND",
		66:  "LOAN_STEP_EXECUTION_SUB_STATUS_ALL_CONNECTED_ACCOUNTS_ARE_CURRENT_ACCOUNTS",
		67:  "LOAN_STEP_EXECUTION_SUB_STATUS_STALE_CONNECTED_ACCOUNTS",
		68:  "LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_CANNOT_BE_ESTIMATED",
		69:  "LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_FAILED_WITH_UNKNOWN_REASON",
		70:  "LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_LOW_CONFIDENCE",
		71:  "LOAN_STEP_EXECUTION_SUB_STATUS_CA_FLOW_STARTED",
		72:  "LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LOAN_AMOUNT_POST_FUND_VERIFICATION",
		73:  "LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_AGE_CRITERIA_NOT_SATISFIED",
		74:  "LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED",
		75:  "LOAN_STEP_EXECUTION_SUB_STATUS_AADHAAR_DETAILS_ADDED",
		76:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND",
		77:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_SELECTED_BANK_NOT_FOUND",
		78:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_MERCHANT_CODE_MISMATCH",
		79:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_DUPLICATE_TXN_ID",
		80:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_IMPS_NAME_EMPTY",
		81:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_PAN_NAME_EMPTY",
		82:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_NAME_MISMATCH",
		83:  "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_ACCOUNT_HOLDER_NAME_EMPTY",
		84:  "LOAN_STEP_EXECUTION_SUB_STATUS_PHOTO_MATCH_FAILED",
		85:  "LOAN_STEP_EXECUTION_SUB_STATUS_EPFO_DATA_NOT_FOUND",
		86:  "LOAN_STEP_EXECUTION_SUB_STATUS_APPLICATION_NOT_FOUND_AT_VENDOR",
		87:  "LOAN_STEP_EXECUTION_SUB_STATUS_LEAD_CANCELLATION_FAILED",
		88:  "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_NOT_FOUNT",
		89:  "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_REVOKED",
		90:  "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_AMOUNT_LESS_THAN_MINIMUM",
		91:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DOB_MISMATCH",
		92:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_MISMATCH",
		93:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_DOES_NOT_EXIST_FOR_CKYC_NUMBER",
		94:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_INVALID_VALUE_ENTERED_IN_AUTHENTICATION_FACTOR_TYPE",
		95:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_IMAGES_CANNOT_BE_LOADED",
		96:  "LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_FAILED",
		97:  "LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_EMI_GREATER_THAN_MAX_EMI_ALLOWED",
		98:  "LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_AMOUNT_IS_NOT_WITHIN_THE_PRESCRIBED_LIMITS",
		99:  "LOAN_STEP_EXECUTION_SUB_STATUS_USER_ACTION_WAIT_TIME_OUT",
		100: "LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_MANDATE_REVOKED",
		101: "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_MAX_ATTEMPTS_REACHED",
		102: "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILED_FROM_VENDOR",
		103: "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_DEEPLINK_GENERATED",
		104: "LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_LOAN_APPLICATION_EXPIRED",
		105: "LOAN_STEP_EXECUTION_SUB_STATUS_USER_REJECTED_KFS",
		106: "LOAN_STEP_EXECUTION_SUB_STATUS_ITR_INTIMATION_STARTED",
		107: "LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK",
		108: "LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL",
		109: "LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_NOT_ENTERED",
		110: "LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_VERIFICATION_FAILED",
		111: "LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_NOT_ENTERED",
		112: "LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_FAILED",
		113: "LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_NOT_IN_ACCEPTABLE_RANGE_FOR_VENDOR",
		114: "LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED",
		115: "LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED",
		116: "LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_WITHDRAWN",
		117: "LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_INITIATION_FAILED",
		118: "LOAN_STEP_EXECUTION_SUB_STATUS_DISBURSAL_TIMED_OUT",
		119: "LOAN_STEP_EXECUTION_SUB_STATUS_STUCK_IN_READY_TO_DISBURSE_STATE",
		120: "LOAN_STEP_EXECUTION_SUB_STATUS_SKIP_NON_RISKY_USER",
		121: "LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD",
		122: "LOAN_STEP_EXECUTION_SUB_STATUS_WEB_URL_FETCHED",
		123: "LOAN_STEP_EXECUTION_SUB_STATUS_STEP_INAPPLICABLE",
		124: "LOAN_STEP_EXECUTION_SUB_STATUS_CAS_DETAILED_FETCH_FAILED",
		125: "LOAN_STEP_EXECUTION_SUB_STATUS_LIEN_ATTEMPT_EXPIRED",
		126: "LOAN_STEP_EXECUTION_SUB_STATUS_ACTIVE_LOAN_WITH_VENDOR",
		127: "LOAN_STEP_EXECUTION_SUB_STATUS_DOB_DOES_NOT_MATCH_PAN_DATA",
		128: "LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL",
		129: "LOAN_STEP_EXECUTION_SUB_STATUS_USER_DROPPED_OFF_FROM_JOURNEY",
		130: "LOAN_STEP_EXECUTION_SUB_STATUS_PREVIOUS_MANDATE_ALREADY_SUCCESSFUL",
		131: "LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_ACCOUNT_ALREADY_CLOSED",
		132: "LOAN_STEP_EXECUTION_SUB_STATUS_INVALID_DRAWDOWN_ARGUMENT",
		133: "LOAN_STEP_EXECUTION_SUB_STATUS_SI_NOT_SETUP",
		134: "LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_INITIATED",
		135: "LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW",
		136: "LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_IN_PROGRESS",
		137: "LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE",
		138: "LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_FAILED_USER_ERROR",
		139: "LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_ALREADY_DONE",
		140: "LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_NOT_NEEDED",
		141: "LOAN_STEP_EXECUTION_SUB_STATUS_USER_TEMPORARY_BLOCKED",
		142: "LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_PENDING",
		143: "LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_SUCCESSFUL",
		144: "LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_FAILED",
	}
	LoanStepExecutionSubStatus_value = map[string]int32{
		"LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED":                                              0,
		"LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_CREATED":                                        1,
		"LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED":                                 2,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED":                                    3,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED":                                     4,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW":                                            5,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_OTP_FAILED":                                         6,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR":                              7,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED":                                     8,
		"LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_STATUS_FETCHED":                                 9,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PAN_VERIFIED":                                             10,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED":                                    11,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED":                                                12,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED":                                 13,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED":                                       14,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD":                                     15,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RISKY_USER_CHECK":                                         16,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RISK_CREDIT_CHECK":                                        17,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_USER_BALANCE_IS_ZERO":                   18,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_EXECUTED":                               19,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED":                                 20,
		"LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND":                                21,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CREDIT_REPORT_FETCH_INITIATED":                            22,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_EXPIRED":                                             23,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_LINKED":                                       24,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_NOT_LINKED":                                   25,
		"LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFIED":                                             26,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_INITIATED":                                           27,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BANK_LINKED":                                              28,
		"LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED":                                            29,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_SIGNED":                                   30,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MISSING_BANK_DETAILS":                                     31,
		"LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE":                                   32,
		"LOAN_STEP_EXECUTION_SUB_STATUS_NO_LIEN_MARKED_PORTFOLIO":                                 33,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LIEN_MARKED_PORTFOLIO":                       34,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATED_SUCCESSFULLY":                            35,
		"LOAN_STEP_EXECUTION_SUB_STATUS_WAITING_FOR_DATA":                                         36,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_MAKER_CHECK_APPROVED":                                37,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_VERIFICATION_FAILED":                                 38,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PARTIAL_LIEN_MARKED":                                      39,
		"LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND":                                       40,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE":                                      41,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED":                                     42,
		"LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_EXPIRED":                                            43,
		"LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED":                                       44,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED":                                       45,
		"LOAN_STEP_EXECUTION_SUB_STATUS_KYC_REJECTED":                                             46,
		"LOAN_STEP_EXECUTION_SUB_STATUS_KYC_EXPIRED":                                              47,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CA_DATA_SYNC_PENDING":                                     48,
		"LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED":                                                  49,
		"LOAN_STEP_EXECUTION_SUB_STATUS_DEDUPE_CHECK_FAILURE":                                     50,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION":                                     51,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN":                                   52,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE":                             53,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED":                         54,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS":                                55,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT":                             56,
		"LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED":                                             57,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED":                                            58,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE":                     59,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_AUTHENTICATION_FAILED":                      60,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_VALIDATION_FAILED":                          61,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PAN_AND_KYC_MISMATCH":                       62,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_CKYC_ACCOUNT_TYPE":                          63,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_OUT_OF_INDIA":                                        64,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CONNECTED_ACCOUNTS_NOT_FOUND":                             65,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ALL_CONNECTED_ACCOUNTS_ARE_CURRENT_ACCOUNTS":              66,
		"LOAN_STEP_EXECUTION_SUB_STATUS_STALE_CONNECTED_ACCOUNTS":                                 67,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_CANNOT_BE_ESTIMATED":                               68,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_FAILED_WITH_UNKNOWN_REASON":              69,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_LOW_CONFIDENCE":                          70,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CA_FLOW_STARTED":                                          71,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LOAN_AMOUNT_POST_FUND_VERIFICATION":          72,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_AGE_CRITERIA_NOT_SATISFIED":                   73,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED":                74,
		"LOAN_STEP_EXECUTION_SUB_STATUS_AADHAAR_DETAILS_ADDED":                                    75,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND":                                    76,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_SELECTED_BANK_NOT_FOUND":                  77,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_MERCHANT_CODE_MISMATCH":                   78,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_DUPLICATE_TXN_ID":                         79,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_IMPS_NAME_EMPTY":                          80,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_PAN_NAME_EMPTY":                           81,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_NAME_MISMATCH":                            82,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_ACCOUNT_HOLDER_NAME_EMPTY":                83,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PHOTO_MATCH_FAILED":                                       84,
		"LOAN_STEP_EXECUTION_SUB_STATUS_EPFO_DATA_NOT_FOUND":                                      85,
		"LOAN_STEP_EXECUTION_SUB_STATUS_APPLICATION_NOT_FOUND_AT_VENDOR":                          86,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LEAD_CANCELLATION_FAILED":                                 87,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_NOT_FOUNT":                    88,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_REVOKED":                      89,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_AMOUNT_LESS_THAN_MINIMUM":        90,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DOB_MISMATCH":                                        91,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_MISMATCH":                              92,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_DOES_NOT_EXIST_FOR_CKYC_NUMBER":        93,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_INVALID_VALUE_ENTERED_IN_AUTHENTICATION_FACTOR_TYPE": 94,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_IMAGES_CANNOT_BE_LOADED":                             95,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_FAILED":                                              96,
		"LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_EMI_GREATER_THAN_MAX_EMI_ALLOWED":                97,
		"LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_AMOUNT_IS_NOT_WITHIN_THE_PRESCRIBED_LIMITS":      98,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_ACTION_WAIT_TIME_OUT":                                99,
		"LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_MANDATE_REVOKED":                        100,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_MAX_ATTEMPTS_REACHED":                             101,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILED_FROM_VENDOR":                               102,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_DEEPLINK_GENERATED":                               103,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_LOAN_APPLICATION_EXPIRED":                          104,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_REJECTED_KFS":                                        105,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ITR_INTIMATION_STARTED":                                   106,
		"LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK":                                      107,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL":                           108,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_NOT_ENTERED":                                109,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_VERIFICATION_FAILED":                        110,
		"LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_NOT_ENTERED":              111,
		"LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_FAILED":                   112,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_NOT_IN_ACCEPTABLE_RANGE_FOR_VENDOR":                113,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED":                           114,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED":                           115,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_WITHDRAWN":                                           116,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_INITIATION_FAILED":                                117,
		"LOAN_STEP_EXECUTION_SUB_STATUS_DISBURSAL_TIMED_OUT":                                      118,
		"LOAN_STEP_EXECUTION_SUB_STATUS_STUCK_IN_READY_TO_DISBURSE_STATE":                         119,
		"LOAN_STEP_EXECUTION_SUB_STATUS_SKIP_NON_RISKY_USER":                                      120,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD":                           121,
		"LOAN_STEP_EXECUTION_SUB_STATUS_WEB_URL_FETCHED":                                          122,
		"LOAN_STEP_EXECUTION_SUB_STATUS_STEP_INAPPLICABLE":                                        123,
		"LOAN_STEP_EXECUTION_SUB_STATUS_CAS_DETAILED_FETCH_FAILED":                                124,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LIEN_ATTEMPT_EXPIRED":                                     125,
		"LOAN_STEP_EXECUTION_SUB_STATUS_ACTIVE_LOAN_WITH_VENDOR":                                  126,
		"LOAN_STEP_EXECUTION_SUB_STATUS_DOB_DOES_NOT_MATCH_PAN_DATA":                              127,
		"LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL":                                 128,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_DROPPED_OFF_FROM_JOURNEY":                            129,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PREVIOUS_MANDATE_ALREADY_SUCCESSFUL":                      130,
		"LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_ACCOUNT_ALREADY_CLOSED":                              131,
		"LOAN_STEP_EXECUTION_SUB_STATUS_INVALID_DRAWDOWN_ARGUMENT":                                132,
		"LOAN_STEP_EXECUTION_SUB_STATUS_SI_NOT_SETUP":                                             133,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_INITIATED":                                      134,
		"LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW":                                      135,
		"LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_IN_PROGRESS":                           136,
		"LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE":                                  137,
		"LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_FAILED_USER_ERROR":                                138,
		"LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_ALREADY_DONE":                                     139,
		"LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_NOT_NEEDED":                                       140,
		"LOAN_STEP_EXECUTION_SUB_STATUS_USER_TEMPORARY_BLOCKED":                                   141,
		"LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_PENDING":                                 142,
		"LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_SUCCESSFUL":                              143,
		"LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_FAILED":                                  144,
	}
)

func (x LoanStepExecutionSubStatus) Enum() *LoanStepExecutionSubStatus {
	p := new(LoanStepExecutionSubStatus)
	*p = x
	return p
}

func (x LoanStepExecutionSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanStepExecutionSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[11].Descriptor()
}

func (LoanStepExecutionSubStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[11]
}

func (x LoanStepExecutionSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanStepExecutionSubStatus.Descriptor instead.
func (LoanStepExecutionSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{11}
}

type LoanStepExecutionFlow int32

const (
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED      LoanStepExecutionFlow = 0
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_OFFER       LoanStepExecutionFlow = 1
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION LoanStepExecutionFlow = 2
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_CLOSURE     LoanStepExecutionFlow = 3
	// flow name for loan prepayments done on Fi app.
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PRE_PAY         LoanStepExecutionFlow = 4
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_COLLECTIONS     LoanStepExecutionFlow = 5
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY     LoanStepExecutionFlow = 6
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH LoanStepExecutionFlow = 7
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_SETUP_SI        LoanStepExecutionFlow = 8
	// flow name loan prepayments done outside Fi app.
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_OFF_APP_PRE_PAY LoanStepExecutionFlow = 9
	// This flow is used for non-financial updates required in mutual fund folios, such as updating phone numbers and email addresses associated with the folios.
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_MUTUAL_FUND_NFT LoanStepExecutionFlow = 10
	// This flow is used for orchestrating loan repayments by executing an (already setup) auto-pay mandate.
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_AUTO_PAY LoanStepExecutionFlow = 11
	// This flow is used for orchestrating pre eligibility check
	LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PRE_ELIGIBILITY LoanStepExecutionFlow = 12
)

// Enum value maps for LoanStepExecutionFlow.
var (
	LoanStepExecutionFlow_name = map[int32]string{
		0:  "LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED",
		1:  "LOAN_STEP_EXECUTION_FLOW_LOAN_OFFER",
		2:  "LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION",
		3:  "LOAN_STEP_EXECUTION_FLOW_LOAN_CLOSURE",
		4:  "LOAN_STEP_EXECUTION_FLOW_PRE_PAY",
		5:  "LOAN_STEP_EXECUTION_FLOW_COLLECTIONS",
		6:  "LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY",
		7:  "LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH",
		8:  "LOAN_STEP_EXECUTION_FLOW_SETUP_SI",
		9:  "LOAN_STEP_EXECUTION_FLOW_OFF_APP_PRE_PAY",
		10: "LOAN_STEP_EXECUTION_FLOW_MUTUAL_FUND_NFT",
		11: "LOAN_STEP_EXECUTION_FLOW_AUTO_PAY",
		12: "LOAN_STEP_EXECUTION_FLOW_PRE_ELIGIBILITY",
	}
	LoanStepExecutionFlow_value = map[string]int32{
		"LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED":      0,
		"LOAN_STEP_EXECUTION_FLOW_LOAN_OFFER":       1,
		"LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION": 2,
		"LOAN_STEP_EXECUTION_FLOW_LOAN_CLOSURE":     3,
		"LOAN_STEP_EXECUTION_FLOW_PRE_PAY":          4,
		"LOAN_STEP_EXECUTION_FLOW_COLLECTIONS":      5,
		"LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY":      6,
		"LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH":  7,
		"LOAN_STEP_EXECUTION_FLOW_SETUP_SI":         8,
		"LOAN_STEP_EXECUTION_FLOW_OFF_APP_PRE_PAY":  9,
		"LOAN_STEP_EXECUTION_FLOW_MUTUAL_FUND_NFT":  10,
		"LOAN_STEP_EXECUTION_FLOW_AUTO_PAY":         11,
		"LOAN_STEP_EXECUTION_FLOW_PRE_ELIGIBILITY":  12,
	}
)

func (x LoanStepExecutionFlow) Enum() *LoanStepExecutionFlow {
	p := new(LoanStepExecutionFlow)
	*p = x
	return p
}

func (x LoanStepExecutionFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanStepExecutionFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[12].Descriptor()
}

func (LoanStepExecutionFlow) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[12]
}

func (x LoanStepExecutionFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanStepExecutionFlow.Descriptor instead.
func (LoanStepExecutionFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{12}
}

type LoanStepExecutionStepName int32

const (
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED    LoanStepExecutionStepName = 0
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK      LoanStepExecutionStepName = 1
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK LoanStepExecutionStepName = 2
	// loan step execution for manual review of liveness
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MR_LIVENESS LoanStepExecutionStepName = 3
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FACE_MATCH  LoanStepExecutionStepName = 4
	// loan step execution for manual review of face-match
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MR_FACE_MATCH LoanStepExecutionStepName = 5
	// loan step execution status for E-Sign
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS LoanStepExecutionStepName = 6
	// loan step for liveness federal review
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW LoanStepExecutionStepName = 7
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC                   LoanStepExecutionStepName = 8
	// loan step for onboarding group stage, can be used in other group stages as well if needed
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION       LoanStepExecutionStepName = 9
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE                  LoanStepExecutionStepName = 10
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER              LoanStepExecutionStepName = 11
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION       LoanStepExecutionStepName = 12
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION    LoanStepExecutionStepName = 13
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL    LoanStepExecutionStepName = 14
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN                 LoanStepExecutionStepName = 15
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS              LoanStepExecutionStepName = 16
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK               LoanStepExecutionStepName = 17
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAYMENT                  LoanStepExecutionStepName = 18
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RECON_PAYMENT            LoanStepExecutionStepName = 19
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPLOAD_FILE              LoanStepExecutionStepName = 20
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_COLLECTION_BALANCE LoanStepExecutionStepName = 21
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_SI               LoanStepExecutionStepName = 22
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LMS               LoanStepExecutionStepName = 23
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_NAME_AND_GENDER          LoanStepExecutionStepName = 24
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH      LoanStepExecutionStepName = 25
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS          LoanStepExecutionStepName = 26
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE             LoanStepExecutionStepName = 27
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC                     LoanStepExecutionStepName = 28
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS           LoanStepExecutionStepName = 29
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB              LoanStepExecutionStepName = 30
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CALL_VENDOR              LoanStepExecutionStepName = 31
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH          LoanStepExecutionStepName = 32
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR                  LoanStepExecutionStepName = 33
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OFFER_GENERATION         LoanStepExecutionStepName = 34
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LEAD_DETAILS      LoanStepExecutionStepName = 35
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_PRE_BRE         LoanStepExecutionStepName = 36
	// Stage to validate if the pan used for onboarding was already onboarded with us or not
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_UNIQUE_CHECK LoanStepExecutionStepName = 37
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN        LoanStepExecutionStepName = 38
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INITIALISE_LOAN  LoanStepExecutionStepName = 39
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS          LoanStepExecutionStepName = 40
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT       LoanStepExecutionStepName = 41
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE       LoanStepExecutionStepName = 42
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE   LoanStepExecutionStepName = 43
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES       LoanStepExecutionStepName = 44
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD      LoanStepExecutionStepName = 45
	// Stage to cover multiple stages happening in vendor pwa flow under a single step/stage on our side, metadata of this stage can optionally store the details of the stages which were processed in the vendor pwa flow.
	// **Note** : should be used in cases multiple steps can take place within a vendor pwa flow and we want to map it to a single step on our end to avoid tight coupling with vendor flow/stages.
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES           LoanStepExecutionStepName = 46
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION                 LoanStepExecutionStepName = 47
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LAT_LONG                            LoanStepExecutionStepName = 48
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS                 LoanStepExecutionStepName = 49
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT                         LoanStepExecutionStepName = 50
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION                   LoanStepExecutionStepName = 51
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RESET_VENDOR_LOAN_APPLICATION       LoanStepExecutionStepName = 52
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_USER_CREATION           LoanStepExecutionStepName = 53
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_CREATION           LoanStepExecutionStepName = 54
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_DISBURSAL          LoanStepExecutionStepName = 55
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREATE_REPAYMENT_SCHEDULE_AT_LENDER LoanStepExecutionStepName = 56
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS                             LoanStepExecutionStepName = 57
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RECORD_PAYMENT_IN_PARTNER_LMS       LoanStepExecutionStepName = 58
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REFRESH_MIRRORED_LMS_FROM_VENDOR    LoanStepExecutionStepName = 59
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREATE_PAYMENT_LOAN_ACTIVITY        LoanStepExecutionStepName = 60
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH                  LoanStepExecutionStepName = 61
	// This loan step is used to update details in mutual fund folios, such as email addresses and phone numbers.
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_MF_FOLIO_DETAILS LoanStepExecutionStepName = 62
	// loan step to denote the account aggregator data fetch from the vendor
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH LoanStepExecutionStepName = 63
	// loan step to execute an already setup recurring payment setup for performing auto-repayment to the loan account.
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_RECURRING_PAYMENT LoanStepExecutionStepName = 64
	// loan step to store alternate phone number for risky users
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CONTACTABILITY              LoanStepExecutionStepName = 65
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LENDER_LOAN_ACCOUNT_CLOSURE LoanStepExecutionStepName = 66
	// to evaluate the current user state at the end of a data collection flow and set the next action in LR
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SET_NEXT_ACTION LoanStepExecutionStepName = 67
	// to update the data collection status in LOEC after the data collection is done
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LOEC_DATA_STATUS LoanStepExecutionStepName = 68
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_EPFO_DATA         LoanStepExecutionStepName = 69
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP              LoanStepExecutionStepName = 70
	// loan step to fetch the data before calling vendors bre and after calling fi-pre bre in eligibility flow of vendors like lenden.
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_DATA_FETCH_LOAN_DETAILS LoanStepExecutionStepName = 71
	// loan step to fetch the consent before calling vendors bre and after calling fi-pre bre in eligibility flow of vendors like lenden.
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_CONSENT               LoanStepExecutionStepName = 72
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AML                           LoanStepExecutionStepName = 73
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_DATA_COMPLETENESS       LoanStepExecutionStepName = 74
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION                LoanStepExecutionStepName = 75
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION             LoanStepExecutionStepName = 76
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_ADD_DETAILS            LoanStepExecutionStepName = 77
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION              LoanStepExecutionStepName = 78
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE           LoanStepExecutionStepName = 79
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE         LoanStepExecutionStepName = 80
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE LoanStepExecutionStepName = 90
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE      LoanStepExecutionStepName = 91
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION              LoanStepExecutionStepName = 92
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RE_KFS                        LoanStepExecutionStepName = 93
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION          LoanStepExecutionStepName = 94
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_OWNED_PAYMENT          LoanStepExecutionStepName = 95
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE      LoanStepExecutionStepName = 96
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE      LoanStepExecutionStepName = 97
	LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SUBMIT_ADDITIONAL_KYC_DETAILS LoanStepExecutionStepName = 98
)

// Enum value maps for LoanStepExecutionStepName.
var (
	LoanStepExecutionStepName_name = map[int32]string{
		0:  "LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED",
		1:  "LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK",
		2:  "LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK",
		3:  "LOAN_STEP_EXECUTION_STEP_NAME_MR_LIVENESS",
		4:  "LOAN_STEP_EXECUTION_STEP_NAME_FACE_MATCH",
		5:  "LOAN_STEP_EXECUTION_STEP_NAME_MR_FACE_MATCH",
		6:  "LOAN_STEP_EXECUTION_STEP_NAME_KFS",
		7:  "LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW",
		8:  "LOAN_STEP_EXECUTION_STEP_NAME_CKYC",
		9:  "LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION",
		10: "LOAN_STEP_EXECUTION_STEP_NAME_MANDATE",
		11: "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER",
		12: "LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION",
		13: "LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION",
		14: "LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL",
		15: "LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN",
		16: "LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS",
		17: "LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK",
		18: "LOAN_STEP_EXECUTION_STEP_NAME_PAYMENT",
		19: "LOAN_STEP_EXECUTION_STEP_NAME_RECON_PAYMENT",
		20: "LOAN_STEP_EXECUTION_STEP_NAME_UPLOAD_FILE",
		21: "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_COLLECTION_BALANCE",
		22: "LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_SI",
		23: "LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LMS",
		24: "LOAN_STEP_EXECUTION_STEP_NAME_NAME_AND_GENDER",
		25: "LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH",
		26: "LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS",
		27: "LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE",
		28: "LOAN_STEP_EXECUTION_STEP_NAME_VKYC",
		29: "LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS",
		30: "LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB",
		31: "LOAN_STEP_EXECUTION_STEP_NAME_CALL_VENDOR",
		32: "LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH",
		33: "LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR",
		34: "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_GENERATION",
		35: "LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LEAD_DETAILS",
		36: "LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_PRE_BRE",
		37: "LOAN_STEP_EXECUTION_STEP_NAME_PAN_UNIQUE_CHECK",
		38: "LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN",
		39: "LOAN_STEP_EXECUTION_STEP_NAME_INITIALISE_LOAN",
		40: "LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS",
		41: "LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT",
		42: "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE",
		43: "LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE",
		44: "LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES",
		45: "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD",
		46: "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES",
		47: "LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION",
		48: "LOAN_STEP_EXECUTION_STEP_NAME_LAT_LONG",
		49: "LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS",
		50: "LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT",
		51: "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION",
		52: "LOAN_STEP_EXECUTION_STEP_NAME_RESET_VENDOR_LOAN_APPLICATION",
		53: "LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_USER_CREATION",
		54: "LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_CREATION",
		55: "LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_DISBURSAL",
		56: "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_REPAYMENT_SCHEDULE_AT_LENDER",
		57: "LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS",
		58: "LOAN_STEP_EXECUTION_STEP_NAME_RECORD_PAYMENT_IN_PARTNER_LMS",
		59: "LOAN_STEP_EXECUTION_STEP_NAME_REFRESH_MIRRORED_LMS_FROM_VENDOR",
		60: "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_PAYMENT_LOAN_ACTIVITY",
		61: "LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH",
		62: "LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_MF_FOLIO_DETAILS",
		63: "LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH",
		64: "LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_RECURRING_PAYMENT",
		65: "LOAN_STEP_EXECUTION_STEP_NAME_CONTACTABILITY",
		66: "LOAN_STEP_EXECUTION_STEP_NAME_LENDER_LOAN_ACCOUNT_CLOSURE",
		67: "LOAN_STEP_EXECUTION_STEP_NAME_SET_NEXT_ACTION",
		68: "LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LOEC_DATA_STATUS",
		69: "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_EPFO_DATA",
		70: "LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP",
		71: "LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_DATA_FETCH_LOAN_DETAILS",
		72: "LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_CONSENT",
		73: "LOAN_STEP_EXECUTION_STEP_NAME_AML",
		74: "LOAN_STEP_EXECUTION_STEP_NAME_CHECK_DATA_COMPLETENESS",
		75: "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION",
		76: "LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION",
		77: "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_ADD_DETAILS",
		78: "LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION",
		79: "LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE",
		80: "LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE",
		90: "LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE",
		91: "LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE",
		92: "LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION",
		93: "LOAN_STEP_EXECUTION_STEP_NAME_RE_KFS",
		94: "LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION",
		95: "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_OWNED_PAYMENT",
		96: "LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE",
		97: "LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE",
		98: "LOAN_STEP_EXECUTION_STEP_NAME_SUBMIT_ADDITIONAL_KYC_DETAILS",
	}
	LoanStepExecutionStepName_value = map[string]int32{
		"LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED":                         0,
		"LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK":                           1,
		"LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK":                      2,
		"LOAN_STEP_EXECUTION_STEP_NAME_MR_LIVENESS":                         3,
		"LOAN_STEP_EXECUTION_STEP_NAME_FACE_MATCH":                          4,
		"LOAN_STEP_EXECUTION_STEP_NAME_MR_FACE_MATCH":                       5,
		"LOAN_STEP_EXECUTION_STEP_NAME_KFS":                                 6,
		"LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW":              7,
		"LOAN_STEP_EXECUTION_STEP_NAME_CKYC":                                8,
		"LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION":                  9,
		"LOAN_STEP_EXECUTION_STEP_NAME_MANDATE":                             10,
		"LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER":                         11,
		"LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION":                  12,
		"LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION":               13,
		"LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL":               14,
		"LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN":                            15,
		"LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS":                         16,
		"LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK":                          17,
		"LOAN_STEP_EXECUTION_STEP_NAME_PAYMENT":                             18,
		"LOAN_STEP_EXECUTION_STEP_NAME_RECON_PAYMENT":                       19,
		"LOAN_STEP_EXECUTION_STEP_NAME_UPLOAD_FILE":                         20,
		"LOAN_STEP_EXECUTION_STEP_NAME_FETCH_COLLECTION_BALANCE":            21,
		"LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_SI":                          22,
		"LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LMS":                          23,
		"LOAN_STEP_EXECUTION_STEP_NAME_NAME_AND_GENDER":                     24,
		"LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH":                 25,
		"LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS":                     26,
		"LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE":                        27,
		"LOAN_STEP_EXECUTION_STEP_NAME_VKYC":                                28,
		"LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS":                      29,
		"LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB":                         30,
		"LOAN_STEP_EXECUTION_STEP_NAME_CALL_VENDOR":                         31,
		"LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH":                     32,
		"LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR":                             33,
		"LOAN_STEP_EXECUTION_STEP_NAME_OFFER_GENERATION":                    34,
		"LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LEAD_DETAILS":                 35,
		"LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_PRE_BRE":                    36,
		"LOAN_STEP_EXECUTION_STEP_NAME_PAN_UNIQUE_CHECK":                    37,
		"LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN":                           38,
		"LOAN_STEP_EXECUTION_STEP_NAME_INITIALISE_LOAN":                     39,
		"LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS":                             40,
		"LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT":                          41,
		"LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE":                          42,
		"LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE":                      43,
		"LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES":                          44,
		"LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD":                         45,
		"LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES":           46,
		"LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION":                 47,
		"LOAN_STEP_EXECUTION_STEP_NAME_LAT_LONG":                            48,
		"LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS":                 49,
		"LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT":                         50,
		"LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION":                   51,
		"LOAN_STEP_EXECUTION_STEP_NAME_RESET_VENDOR_LOAN_APPLICATION":       52,
		"LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_USER_CREATION":           53,
		"LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_CREATION":           54,
		"LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_DISBURSAL":          55,
		"LOAN_STEP_EXECUTION_STEP_NAME_CREATE_REPAYMENT_SCHEDULE_AT_LENDER": 56,
		"LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS":                             57,
		"LOAN_STEP_EXECUTION_STEP_NAME_RECORD_PAYMENT_IN_PARTNER_LMS":       58,
		"LOAN_STEP_EXECUTION_STEP_NAME_REFRESH_MIRRORED_LMS_FROM_VENDOR":    59,
		"LOAN_STEP_EXECUTION_STEP_NAME_CREATE_PAYMENT_LOAN_ACTIVITY":        60,
		"LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH":                  61,
		"LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_MF_FOLIO_DETAILS":             62,
		"LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH":                       63,
		"LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_RECURRING_PAYMENT":           64,
		"LOAN_STEP_EXECUTION_STEP_NAME_CONTACTABILITY":                      65,
		"LOAN_STEP_EXECUTION_STEP_NAME_LENDER_LOAN_ACCOUNT_CLOSURE":         66,
		"LOAN_STEP_EXECUTION_STEP_NAME_SET_NEXT_ACTION":                     67,
		"LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LOEC_DATA_STATUS":             68,
		"LOAN_STEP_EXECUTION_STEP_NAME_FETCH_EPFO_DATA":                     69,
		"LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP":                          70,
		"LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_DATA_FETCH_LOAN_DETAILS":     71,
		"LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_CONSENT":                     72,
		"LOAN_STEP_EXECUTION_STEP_NAME_AML":                                 73,
		"LOAN_STEP_EXECUTION_STEP_NAME_CHECK_DATA_COMPLETENESS":             74,
		"LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION":                      75,
		"LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION":                   76,
		"LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_ADD_DETAILS":                  77,
		"LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION":                    78,
		"LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE":                 79,
		"LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE":               80,
		"LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE":       90,
		"LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE":            91,
		"LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION":                    92,
		"LOAN_STEP_EXECUTION_STEP_NAME_RE_KFS":                              93,
		"LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION":                94,
		"LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_OWNED_PAYMENT":                95,
		"LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE":            96,
		"LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE":            97,
		"LOAN_STEP_EXECUTION_STEP_NAME_SUBMIT_ADDITIONAL_KYC_DETAILS":       98,
	}
)

func (x LoanStepExecutionStepName) Enum() *LoanStepExecutionStepName {
	p := new(LoanStepExecutionStepName)
	*p = x
	return p
}

func (x LoanStepExecutionStepName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanStepExecutionStepName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[13].Descriptor()
}

func (LoanStepExecutionStepName) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[13]
}

func (x LoanStepExecutionStepName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanStepExecutionStepName.Descriptor instead.
func (LoanStepExecutionStepName) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{13}
}

type LoanStepExecutionFieldMask int32

const (
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_UNSPECIFIED  LoanStepExecutionFieldMask = 0
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_REF_ID       LoanStepExecutionFieldMask = 1
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_FLOW         LoanStepExecutionFieldMask = 2
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID      LoanStepExecutionFieldMask = 3
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STEP_NAME    LoanStepExecutionFieldMask = 4
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS      LoanStepExecutionFieldMask = 5
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS       LoanStepExecutionFieldMask = 6
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS   LoanStepExecutionFieldMask = 7
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STALED_AT    LoanStepExecutionFieldMask = 8
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT LoanStepExecutionFieldMask = 9
	LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_GROUP_STAGE  LoanStepExecutionFieldMask = 10
)

// Enum value maps for LoanStepExecutionFieldMask.
var (
	LoanStepExecutionFieldMask_name = map[int32]string{
		0:  "LOAN_STEP_EXECUTION_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_STEP_EXECUTION_FIELD_MASK_REF_ID",
		2:  "LOAN_STEP_EXECUTION_FIELD_MASK_FLOW",
		3:  "LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID",
		4:  "LOAN_STEP_EXECUTION_FIELD_MASK_STEP_NAME",
		5:  "LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS",
		6:  "LOAN_STEP_EXECUTION_FIELD_MASK_STATUS",
		7:  "LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS",
		8:  "LOAN_STEP_EXECUTION_FIELD_MASK_STALED_AT",
		9:  "LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT",
		10: "LOAN_STEP_EXECUTION_FIELD_MASK_GROUP_STAGE",
	}
	LoanStepExecutionFieldMask_value = map[string]int32{
		"LOAN_STEP_EXECUTION_FIELD_MASK_UNSPECIFIED":  0,
		"LOAN_STEP_EXECUTION_FIELD_MASK_REF_ID":       1,
		"LOAN_STEP_EXECUTION_FIELD_MASK_FLOW":         2,
		"LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID":      3,
		"LOAN_STEP_EXECUTION_FIELD_MASK_STEP_NAME":    4,
		"LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS":      5,
		"LOAN_STEP_EXECUTION_FIELD_MASK_STATUS":       6,
		"LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS":   7,
		"LOAN_STEP_EXECUTION_FIELD_MASK_STALED_AT":    8,
		"LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT": 9,
		"LOAN_STEP_EXECUTION_FIELD_MASK_GROUP_STAGE":  10,
	}
)

func (x LoanStepExecutionFieldMask) Enum() *LoanStepExecutionFieldMask {
	p := new(LoanStepExecutionFieldMask)
	*p = x
	return p
}

func (x LoanStepExecutionFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanStepExecutionFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[14].Descriptor()
}

func (LoanStepExecutionFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[14]
}

func (x LoanStepExecutionFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanStepExecutionFieldMask.Descriptor instead.
func (LoanStepExecutionFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{14}
}

type LoanOfferEligibilityCriteriaFieldMask int32

const (
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_UNSPECIFIED              LoanOfferEligibilityCriteriaFieldMask = 0
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS                   LoanOfferEligibilityCriteriaFieldMask = 1
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE          LoanOfferEligibilityCriteriaFieldMask = 2
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID                 LoanOfferEligibilityCriteriaFieldMask = 3
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_CREATED_AT               LoanOfferEligibilityCriteriaFieldMask = 4
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_BATCH_ID                 LoanOfferEligibilityCriteriaFieldMask = 5
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS               LoanOfferEligibilityCriteriaFieldMask = 6
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_LOAN_SCHEME              LoanOfferEligibilityCriteriaFieldMask = 7
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS            LoanOfferEligibilityCriteriaFieldMask = 8
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_REQUEST_ID        LoanOfferEligibilityCriteriaFieldMask = 9
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT               LoanOfferEligibilityCriteriaFieldMask = 10
	LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS LoanOfferEligibilityCriteriaFieldMask = 11
)

// Enum value maps for LoanOfferEligibilityCriteriaFieldMask.
var (
	LoanOfferEligibilityCriteriaFieldMask_name = map[int32]string{
		0:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS",
		2:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE",
		3:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID",
		4:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_CREATED_AT",
		5:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_BATCH_ID",
		6:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS",
		7:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_LOAN_SCHEME",
		8:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS",
		9:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_REQUEST_ID",
		10: "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT",
		11: "LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS",
	}
	LoanOfferEligibilityCriteriaFieldMask_value = map[string]int32{
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_UNSPECIFIED":              0,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS":                   1,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE":          2,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID":                 3,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_CREATED_AT":               4,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_BATCH_ID":                 5,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS":               6,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_LOAN_SCHEME":              7,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS":            8,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_REQUEST_ID":        9,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT":               10,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS": 11,
	}
)

func (x LoanOfferEligibilityCriteriaFieldMask) Enum() *LoanOfferEligibilityCriteriaFieldMask {
	p := new(LoanOfferEligibilityCriteriaFieldMask)
	*p = x
	return p
}

func (x LoanOfferEligibilityCriteriaFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferEligibilityCriteriaFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[15].Descriptor()
}

func (LoanOfferEligibilityCriteriaFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[15]
}

func (x LoanOfferEligibilityCriteriaFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferEligibilityCriteriaFieldMask.Descriptor instead.
func (LoanOfferEligibilityCriteriaFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{15}
}

type LoanOfferEligibilityCriteriaStatus int32

const (
	LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_UNSPECIFIED LoanOfferEligibilityCriteriaStatus = 0
	LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED     LoanOfferEligibilityCriteriaStatus = 1
	LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED    LoanOfferEligibilityCriteriaStatus = 2
	LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED    LoanOfferEligibilityCriteriaStatus = 3
)

// Enum value maps for LoanOfferEligibilityCriteriaStatus.
var (
	LoanOfferEligibilityCriteriaStatus_name = map[int32]string{
		0: "LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_UNSPECIFIED",
		1: "LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED",
		2: "LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED",
		3: "LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED",
	}
	LoanOfferEligibilityCriteriaStatus_value = map[string]int32{
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_UNSPECIFIED": 0,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED":     1,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED":    2,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED":    3,
	}
)

func (x LoanOfferEligibilityCriteriaStatus) Enum() *LoanOfferEligibilityCriteriaStatus {
	p := new(LoanOfferEligibilityCriteriaStatus)
	*p = x
	return p
}

func (x LoanOfferEligibilityCriteriaStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferEligibilityCriteriaStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[16].Descriptor()
}

func (LoanOfferEligibilityCriteriaStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[16]
}

func (x LoanOfferEligibilityCriteriaStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferEligibilityCriteriaStatus.Descriptor instead.
func (LoanOfferEligibilityCriteriaStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{16}
}

type LoanOfferEligibilityCriteriaSubStatus int32

const (
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED            LoanOfferEligibilityCriteriaSubStatus = 0
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR     LoanOfferEligibilityCriteriaSubStatus = 1
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI         LoanOfferEligibilityCriteriaSubStatus = 2
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR     LoanOfferEligibilityCriteriaSubStatus = 3
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI         LoanOfferEligibilityCriteriaSubStatus = 4
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI_PRE_BRE LoanOfferEligibilityCriteriaSubStatus = 5
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE LoanOfferEligibilityCriteriaSubStatus = 6
	// prequalified by vendor for real time eligibility/BRE checks
	// current use case - to determine the users who are qualified for AA income based real time eligibility check
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR                            LoanOfferEligibilityCriteriaSubStatus = 7
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_REPORT_DOWNLOAD_FAILED                      LoanOfferEligibilityCriteriaSubStatus = 8
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_EPFO_DATA_NOT_FOUND                         LoanOfferEligibilityCriteriaSubStatus = 9
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE_WITH_DATA_REQUIREMENT       LoanOfferEligibilityCriteriaSubStatus = 10
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_WITH_CHANGED_OFFER              LoanOfferEligibilityCriteriaSubStatus = 11
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE_WITH_DATA_REQUIREMENT          LoanOfferEligibilityCriteriaSubStatus = 12
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE                                LoanOfferEligibilityCriteriaSubStatus = 13
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_PRE_BRE                                LoanOfferEligibilityCriteriaSubStatus = 14
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_CHECK                           LoanOfferEligibilityCriteriaSubStatus = 15
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_SERVICEABILITY_CHECK                   LoanOfferEligibilityCriteriaSubStatus = 16
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_RESTRICTED_OCCUPATION_OR_QUALIFICATION LoanOfferEligibilityCriteriaSubStatus = 17
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_PASSED_BY_RETRY                 LoanOfferEligibilityCriteriaSubStatus = 18
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_INVALID_OFFER_FROM_VENDOR                          LoanOfferEligibilityCriteriaSubStatus = 19
	LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_MAX_ACTIVE_LOANS_WITH_LENDER_REACHED               LoanOfferEligibilityCriteriaSubStatus = 20
)

// Enum value maps for LoanOfferEligibilityCriteriaSubStatus.
var (
	LoanOfferEligibilityCriteriaSubStatus_name = map[int32]string{
		0:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED",
		1:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR",
		2:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI",
		3:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR",
		4:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI",
		5:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI_PRE_BRE",
		6:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE",
		7:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR",
		8:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_REPORT_DOWNLOAD_FAILED",
		9:  "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_EPFO_DATA_NOT_FOUND",
		10: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE_WITH_DATA_REQUIREMENT",
		11: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_WITH_CHANGED_OFFER",
		12: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE_WITH_DATA_REQUIREMENT",
		13: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE",
		14: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_PRE_BRE",
		15: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_CHECK",
		16: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_SERVICEABILITY_CHECK",
		17: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_RESTRICTED_OCCUPATION_OR_QUALIFICATION",
		18: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_PASSED_BY_RETRY",
		19: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_INVALID_OFFER_FROM_VENDOR",
		20: "LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_MAX_ACTIVE_LOANS_WITH_LENDER_REACHED",
	}
	LoanOfferEligibilityCriteriaSubStatus_value = map[string]int32{
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED":                                        0,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR":                                 1,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI":                                     2,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR":                                 3,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI":                                     4,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI_PRE_BRE":                             5,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE":                             6,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR":                            7,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_REPORT_DOWNLOAD_FAILED":                      8,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_EPFO_DATA_NOT_FOUND":                         9,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE_WITH_DATA_REQUIREMENT":       10,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_WITH_CHANGED_OFFER":              11,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE_WITH_DATA_REQUIREMENT":          12,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE":                                13,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_PRE_BRE":                                14,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_CHECK":                           15,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_SERVICEABILITY_CHECK":                   16,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_RESTRICTED_OCCUPATION_OR_QUALIFICATION": 17,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_PASSED_BY_RETRY":                 18,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_INVALID_OFFER_FROM_VENDOR":                          19,
		"LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_MAX_ACTIVE_LOANS_WITH_LENDER_REACHED":               20,
	}
)

func (x LoanOfferEligibilityCriteriaSubStatus) Enum() *LoanOfferEligibilityCriteriaSubStatus {
	p := new(LoanOfferEligibilityCriteriaSubStatus)
	*p = x
	return p
}

func (x LoanOfferEligibilityCriteriaSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferEligibilityCriteriaSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[17].Descriptor()
}

func (LoanOfferEligibilityCriteriaSubStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[17]
}

func (x LoanOfferEligibilityCriteriaSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferEligibilityCriteriaSubStatus.Descriptor instead.
func (LoanOfferEligibilityCriteriaSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{17}
}

type LoanInstallmentInfoStatus int32

const (
	LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_UNSPECIFIED LoanInstallmentInfoStatus = 0
	LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE      LoanInstallmentInfoStatus = 1
	LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_COMPLETED   LoanInstallmentInfoStatus = 2
	LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_CLOSED      LoanInstallmentInfoStatus = 3
	LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_UNKNOWN     LoanInstallmentInfoStatus = 4
)

// Enum value maps for LoanInstallmentInfoStatus.
var (
	LoanInstallmentInfoStatus_name = map[int32]string{
		0: "LOAN_INSTALLMENT_INFO_STATUS_UNSPECIFIED",
		1: "LOAN_INSTALLMENT_INFO_STATUS_ACTIVE",
		2: "LOAN_INSTALLMENT_INFO_STATUS_COMPLETED",
		3: "LOAN_INSTALLMENT_INFO_STATUS_CLOSED",
		4: "LOAN_INSTALLMENT_INFO_STATUS_UNKNOWN",
	}
	LoanInstallmentInfoStatus_value = map[string]int32{
		"LOAN_INSTALLMENT_INFO_STATUS_UNSPECIFIED": 0,
		"LOAN_INSTALLMENT_INFO_STATUS_ACTIVE":      1,
		"LOAN_INSTALLMENT_INFO_STATUS_COMPLETED":   2,
		"LOAN_INSTALLMENT_INFO_STATUS_CLOSED":      3,
		"LOAN_INSTALLMENT_INFO_STATUS_UNKNOWN":     4,
	}
)

func (x LoanInstallmentInfoStatus) Enum() *LoanInstallmentInfoStatus {
	p := new(LoanInstallmentInfoStatus)
	*p = x
	return p
}

func (x LoanInstallmentInfoStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanInstallmentInfoStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[18].Descriptor()
}

func (LoanInstallmentInfoStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[18]
}

func (x LoanInstallmentInfoStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanInstallmentInfoStatus.Descriptor instead.
func (LoanInstallmentInfoStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{18}
}

type LoanInstallmentInfoFieldMask int32

const (
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_UNSPECIFIED             LoanInstallmentInfoFieldMask = 0
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_ACCOUNT_ID              LoanInstallmentInfoFieldMask = 1
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_AMOUNT            LoanInstallmentInfoFieldMask = 2
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_START_DATE              LoanInstallmentInfoFieldMask = 3
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_END_DATE                LoanInstallmentInfoFieldMask = 4
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_INSTALLMENT_COUNT LoanInstallmentInfoFieldMask = 5
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE   LoanInstallmentInfoFieldMask = 6
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS                 LoanInstallmentInfoFieldMask = 7
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_STATUS                  LoanInstallmentInfoFieldMask = 8
	LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_DEACTIVATED_AT          LoanInstallmentInfoFieldMask = 9
)

// Enum value maps for LoanInstallmentInfoFieldMask.
var (
	LoanInstallmentInfoFieldMask_name = map[int32]string{
		0: "LOAN_INSTALLMENT_INFO_FIELD_MASK_UNSPECIFIED",
		1: "LOAN_INSTALLMENT_INFO_FIELD_MASK_ACCOUNT_ID",
		2: "LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_AMOUNT",
		3: "LOAN_INSTALLMENT_INFO_FIELD_MASK_START_DATE",
		4: "LOAN_INSTALLMENT_INFO_FIELD_MASK_END_DATE",
		5: "LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_INSTALLMENT_COUNT",
		6: "LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE",
		7: "LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS",
		8: "LOAN_INSTALLMENT_INFO_FIELD_MASK_STATUS",
		9: "LOAN_INSTALLMENT_INFO_FIELD_MASK_DEACTIVATED_AT",
	}
	LoanInstallmentInfoFieldMask_value = map[string]int32{
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_UNSPECIFIED":             0,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_ACCOUNT_ID":              1,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_AMOUNT":            2,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_START_DATE":              3,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_END_DATE":                4,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_INSTALLMENT_COUNT": 5,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE":   6,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS":                 7,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_STATUS":                  8,
		"LOAN_INSTALLMENT_INFO_FIELD_MASK_DEACTIVATED_AT":          9,
	}
)

func (x LoanInstallmentInfoFieldMask) Enum() *LoanInstallmentInfoFieldMask {
	p := new(LoanInstallmentInfoFieldMask)
	*p = x
	return p
}

func (x LoanInstallmentInfoFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanInstallmentInfoFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[19].Descriptor()
}

func (LoanInstallmentInfoFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[19]
}

func (x LoanInstallmentInfoFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanInstallmentInfoFieldMask.Descriptor instead.
func (LoanInstallmentInfoFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{19}
}

type LoanActivityType int32

const (
	LoanActivityType_LOAN_ACTIVITY_TYPE_UNSPECIFIED       LoanActivityType = 0
	LoanActivityType_LOAN_ACTIVITY_TYPE_EMI               LoanActivityType = 1
	LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM           LoanActivityType = 2
	LoanActivityType_LOAN_ACTIVITY_TYPE_LATE_FEE          LoanActivityType = 3
	LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT LoanActivityType = 4
)

// Enum value maps for LoanActivityType.
var (
	LoanActivityType_name = map[int32]string{
		0: "LOAN_ACTIVITY_TYPE_UNSPECIFIED",
		1: "LOAN_ACTIVITY_TYPE_EMI",
		2: "LOAN_ACTIVITY_TYPE_LUMPSUM",
		3: "LOAN_ACTIVITY_TYPE_LATE_FEE",
		4: "LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT",
	}
	LoanActivityType_value = map[string]int32{
		"LOAN_ACTIVITY_TYPE_UNSPECIFIED":       0,
		"LOAN_ACTIVITY_TYPE_EMI":               1,
		"LOAN_ACTIVITY_TYPE_LUMPSUM":           2,
		"LOAN_ACTIVITY_TYPE_LATE_FEE":          3,
		"LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT": 4,
	}
)

func (x LoanActivityType) Enum() *LoanActivityType {
	p := new(LoanActivityType)
	*p = x
	return p
}

func (x LoanActivityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanActivityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[20].Descriptor()
}

func (LoanActivityType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[20]
}

func (x LoanActivityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanActivityType.Descriptor instead.
func (LoanActivityType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{20}
}

type LoanActivityFieldMask int32

const (
	LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_UNSPECIFIED     LoanActivityFieldMask = 0
	LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_LOAN_ACCOUNT_ID LoanActivityFieldMask = 1
	LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_TYPE            LoanActivityFieldMask = 2
	LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_DETAILS         LoanActivityFieldMask = 3
	LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_REFERENCE_ID    LoanActivityFieldMask = 4
	LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_CREATED_AT      LoanActivityFieldMask = 5
)

// Enum value maps for LoanActivityFieldMask.
var (
	LoanActivityFieldMask_name = map[int32]string{
		0: "LOAN_ACTIVITY_FIELD_MASK_UNSPECIFIED",
		1: "LOAN_ACTIVITY_FIELD_MASK_LOAN_ACCOUNT_ID",
		2: "LOAN_ACTIVITY_FIELD_MASK_TYPE",
		3: "LOAN_ACTIVITY_FIELD_MASK_DETAILS",
		4: "LOAN_ACTIVITY_FIELD_MASK_REFERENCE_ID",
		5: "LOAN_ACTIVITY_FIELD_MASK_CREATED_AT",
	}
	LoanActivityFieldMask_value = map[string]int32{
		"LOAN_ACTIVITY_FIELD_MASK_UNSPECIFIED":     0,
		"LOAN_ACTIVITY_FIELD_MASK_LOAN_ACCOUNT_ID": 1,
		"LOAN_ACTIVITY_FIELD_MASK_TYPE":            2,
		"LOAN_ACTIVITY_FIELD_MASK_DETAILS":         3,
		"LOAN_ACTIVITY_FIELD_MASK_REFERENCE_ID":    4,
		"LOAN_ACTIVITY_FIELD_MASK_CREATED_AT":      5,
	}
)

func (x LoanActivityFieldMask) Enum() *LoanActivityFieldMask {
	p := new(LoanActivityFieldMask)
	*p = x
	return p
}

func (x LoanActivityFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanActivityFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[21].Descriptor()
}

func (LoanActivityFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[21]
}

func (x LoanActivityFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanActivityFieldMask.Descriptor instead.
func (LoanActivityFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{21}
}

type LoanPaymentRequestType int32

const (
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_UNSPECIFIED LoanPaymentRequestType = 0
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_EMI         LoanPaymentRequestType = 1
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM     LoanPaymentRequestType = 2
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE LoanPaymentRequestType = 3
	// Parent LPR type to orchestrate a list of transactions for collections
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_BATCH_COLLECTION LoanPaymentRequestType = 4
	// Individual collection LPR type to orchestrate a single payment
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_COLLECTION_PAYMENT LoanPaymentRequestType = 5
	// To orchestrate loan repayments made by executing (a already setup) recurring payment e.g SI or NACH mandate execution for loan repayments.
	LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_RECURRING_PAYMENT_EXECUTION LoanPaymentRequestType = 6
)

// Enum value maps for LoanPaymentRequestType.
var (
	LoanPaymentRequestType_name = map[int32]string{
		0: "LOAN_PAYMENT_REQUEST_TYPE_UNSPECIFIED",
		1: "LOAN_PAYMENT_REQUEST_TYPE_EMI",
		2: "LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM",
		3: "LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE",
		4: "LOAN_PAYMENT_REQUEST_TYPE_BATCH_COLLECTION",
		5: "LOAN_PAYMENT_REQUEST_TYPE_COLLECTION_PAYMENT",
		6: "LOAN_PAYMENT_REQUEST_TYPE_RECURRING_PAYMENT_EXECUTION",
	}
	LoanPaymentRequestType_value = map[string]int32{
		"LOAN_PAYMENT_REQUEST_TYPE_UNSPECIFIED":                 0,
		"LOAN_PAYMENT_REQUEST_TYPE_EMI":                         1,
		"LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM":                     2,
		"LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE":                 3,
		"LOAN_PAYMENT_REQUEST_TYPE_BATCH_COLLECTION":            4,
		"LOAN_PAYMENT_REQUEST_TYPE_COLLECTION_PAYMENT":          5,
		"LOAN_PAYMENT_REQUEST_TYPE_RECURRING_PAYMENT_EXECUTION": 6,
	}
)

func (x LoanPaymentRequestType) Enum() *LoanPaymentRequestType {
	p := new(LoanPaymentRequestType)
	*p = x
	return p
}

func (x LoanPaymentRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanPaymentRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[22].Descriptor()
}

func (LoanPaymentRequestType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[22]
}

func (x LoanPaymentRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanPaymentRequestType.Descriptor instead.
func (LoanPaymentRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{22}
}

type LoanPaymentRequestStatus int32

const (
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_UNSPECIFIED LoanPaymentRequestStatus = 0
	// when db entry is created
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED LoanPaymentRequestStatus = 1
	// when the payment process is initialised
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED LoanPaymentRequestStatus = 2
	// when request is sent to the vendor
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS LoanPaymentRequestStatus = 3
	// when vendor responds with Successful transaction
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS LoanPaymentRequestStatus = 4
	// when vendor responds with transaction failed
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_FAILED LoanPaymentRequestStatus = 5
	// for any other corner encountered case
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_UNKNOWN LoanPaymentRequestStatus = 6
	// when request doesn't complete in any given time
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_EXPIRED LoanPaymentRequestStatus = 7
	// when request is not complete and needs manual efforts to complete
	LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION LoanPaymentRequestStatus = 8
)

// Enum value maps for LoanPaymentRequestStatus.
var (
	LoanPaymentRequestStatus_name = map[int32]string{
		0: "LOAN_PAYMENT_REQUEST_STATUS_UNSPECIFIED",
		1: "LOAN_PAYMENT_REQUEST_STATUS_CREATED",
		2: "LOAN_PAYMENT_REQUEST_STATUS_INITIATED",
		3: "LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS",
		4: "LOAN_PAYMENT_REQUEST_STATUS_SUCCESS",
		5: "LOAN_PAYMENT_REQUEST_STATUS_FAILED",
		6: "LOAN_PAYMENT_REQUEST_STATUS_UNKNOWN",
		7: "LOAN_PAYMENT_REQUEST_STATUS_EXPIRED",
		8: "LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION",
	}
	LoanPaymentRequestStatus_value = map[string]int32{
		"LOAN_PAYMENT_REQUEST_STATUS_UNSPECIFIED":         0,
		"LOAN_PAYMENT_REQUEST_STATUS_CREATED":             1,
		"LOAN_PAYMENT_REQUEST_STATUS_INITIATED":           2,
		"LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS":         3,
		"LOAN_PAYMENT_REQUEST_STATUS_SUCCESS":             4,
		"LOAN_PAYMENT_REQUEST_STATUS_FAILED":              5,
		"LOAN_PAYMENT_REQUEST_STATUS_UNKNOWN":             6,
		"LOAN_PAYMENT_REQUEST_STATUS_EXPIRED":             7,
		"LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION": 8,
	}
)

func (x LoanPaymentRequestStatus) Enum() *LoanPaymentRequestStatus {
	p := new(LoanPaymentRequestStatus)
	*p = x
	return p
}

func (x LoanPaymentRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanPaymentRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[23].Descriptor()
}

func (LoanPaymentRequestStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[23]
}

func (x LoanPaymentRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanPaymentRequestStatus.Descriptor instead.
func (LoanPaymentRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{23}
}

type LoanPaymentRequestSubStatus int32

const (
	LoanPaymentRequestSubStatus_LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED LoanPaymentRequestSubStatus = 0
)

// Enum value maps for LoanPaymentRequestSubStatus.
var (
	LoanPaymentRequestSubStatus_name = map[int32]string{
		0: "LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED",
	}
	LoanPaymentRequestSubStatus_value = map[string]int32{
		"LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED": 0,
	}
)

func (x LoanPaymentRequestSubStatus) Enum() *LoanPaymentRequestSubStatus {
	p := new(LoanPaymentRequestSubStatus)
	*p = x
	return p
}

func (x LoanPaymentRequestSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanPaymentRequestSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[24].Descriptor()
}

func (LoanPaymentRequestSubStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[24]
}

func (x LoanPaymentRequestSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanPaymentRequestSubStatus.Descriptor instead.
func (LoanPaymentRequestSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{24}
}

type LoanPaymentRequestFieldMask int32

const (
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_UNSPECIFIED LoanPaymentRequestFieldMask = 0
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_ACTOR_ID    LoanPaymentRequestFieldMask = 1
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_ACCOUNT_ID  LoanPaymentRequestFieldMask = 2
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_ORCH_ID     LoanPaymentRequestFieldMask = 3
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_AMOUNT      LoanPaymentRequestFieldMask = 4
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_DETAILS     LoanPaymentRequestFieldMask = 5
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_TYPE        LoanPaymentRequestFieldMask = 6
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_STATUS      LoanPaymentRequestFieldMask = 7
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_SUB_STATUS  LoanPaymentRequestFieldMask = 8
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_DELETED_AT  LoanPaymentRequestFieldMask = 9
	LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_PARENT_ID   LoanPaymentRequestFieldMask = 10
)

// Enum value maps for LoanPaymentRequestFieldMask.
var (
	LoanPaymentRequestFieldMask_name = map[int32]string{
		0:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_ACTOR_ID",
		2:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_ACCOUNT_ID",
		3:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_ORCH_ID",
		4:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_AMOUNT",
		5:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_DETAILS",
		6:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_TYPE",
		7:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_STATUS",
		8:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_SUB_STATUS",
		9:  "LOAN_PAYMENT_REQUEST_FIELD_MASK_DELETED_AT",
		10: "LOAN_PAYMENT_REQUEST_FIELD_MASK_PARENT_ID",
	}
	LoanPaymentRequestFieldMask_value = map[string]int32{
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_UNSPECIFIED": 0,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_ACTOR_ID":    1,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_ACCOUNT_ID":  2,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_ORCH_ID":     3,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_AMOUNT":      4,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_DETAILS":     5,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_TYPE":        6,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_STATUS":      7,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_SUB_STATUS":  8,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_DELETED_AT":  9,
		"LOAN_PAYMENT_REQUEST_FIELD_MASK_PARENT_ID":   10,
	}
)

func (x LoanPaymentRequestFieldMask) Enum() *LoanPaymentRequestFieldMask {
	p := new(LoanPaymentRequestFieldMask)
	*p = x
	return p
}

func (x LoanPaymentRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanPaymentRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[25].Descriptor()
}

func (LoanPaymentRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[25]
}

func (x LoanPaymentRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanPaymentRequestFieldMask.Descriptor instead.
func (LoanPaymentRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{25}
}

type LoanInstallmentPayoutStatus int32

const (
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_UNSPECIFIED LoanInstallmentPayoutStatus = 0
	// Loan installment is yet to be paid
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING LoanInstallmentPayoutStatus = 1
	// Loan installment was successfully paid to completion
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS LoanInstallmentPayoutStatus = 2
	// TODO: Deprecate if not needed
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_FAILED LoanInstallmentPayoutStatus = 3
	// When user makes partial payment
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID LoanInstallmentPayoutStatus = 4
	// When loan is cancelled, the EMIs can also get cancelled.
	// (After the loan amount is disbursed, there is certain cool-off period in which the user is allowed to cancel their loan)
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_CANCELLED LoanInstallmentPayoutStatus = 5
	// When user's instalment was settled due to preclosure
	LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PRE_CLOSURE LoanInstallmentPayoutStatus = 6
)

// Enum value maps for LoanInstallmentPayoutStatus.
var (
	LoanInstallmentPayoutStatus_name = map[int32]string{
		0: "LOAN_INSTALLMENT_PAYOUT_STATUS_UNSPECIFIED",
		1: "LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING",
		2: "LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS",
		3: "LOAN_INSTALLMENT_PAYOUT_STATUS_FAILED",
		4: "LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID",
		5: "LOAN_INSTALLMENT_PAYOUT_STATUS_CANCELLED",
		6: "LOAN_INSTALLMENT_PAYOUT_STATUS_PRE_CLOSURE",
	}
	LoanInstallmentPayoutStatus_value = map[string]int32{
		"LOAN_INSTALLMENT_PAYOUT_STATUS_UNSPECIFIED":    0,
		"LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING":        1,
		"LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS":        2,
		"LOAN_INSTALLMENT_PAYOUT_STATUS_FAILED":         3,
		"LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID": 4,
		"LOAN_INSTALLMENT_PAYOUT_STATUS_CANCELLED":      5,
		"LOAN_INSTALLMENT_PAYOUT_STATUS_PRE_CLOSURE":    6,
	}
)

func (x LoanInstallmentPayoutStatus) Enum() *LoanInstallmentPayoutStatus {
	p := new(LoanInstallmentPayoutStatus)
	*p = x
	return p
}

func (x LoanInstallmentPayoutStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanInstallmentPayoutStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[26].Descriptor()
}

func (LoanInstallmentPayoutStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[26]
}

func (x LoanInstallmentPayoutStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanInstallmentPayoutStatus.Descriptor instead.
func (LoanInstallmentPayoutStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{26}
}

type LoanInstallmentPayoutFieldMask int32

const (
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_UNSPECIFIED              LoanInstallmentPayoutFieldMask = 0
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_STATUS                   LoanInstallmentPayoutFieldMask = 1
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS                  LoanInstallmentPayoutFieldMask = 2
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PAYOUT_DATE              LoanInstallmentPayoutFieldMask = 3
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_INSTALLMENT_INFO_ID LoanInstallmentPayoutFieldMask = 4
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_AMOUNT                   LoanInstallmentPayoutFieldMask = 5
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_DATE                 LoanInstallmentPayoutFieldMask = 6
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PRINCIPAL_AMT            LoanInstallmentPayoutFieldMask = 7
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_INTEREST_AMT             LoanInstallmentPayoutFieldMask = 8
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_AMT                  LoanInstallmentPayoutFieldMask = 9
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_ACCOUNT_ID          LoanInstallmentPayoutFieldMask = 10
	LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS_POSTED_TO_VENDOR LoanInstallmentPayoutFieldMask = 11
)

// Enum value maps for LoanInstallmentPayoutFieldMask.
var (
	LoanInstallmentPayoutFieldMask_name = map[int32]string{
		0:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_STATUS",
		2:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS",
		3:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PAYOUT_DATE",
		4:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_INSTALLMENT_INFO_ID",
		5:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_AMOUNT",
		6:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_DATE",
		7:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PRINCIPAL_AMT",
		8:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_INTEREST_AMT",
		9:  "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_AMT",
		10: "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_ACCOUNT_ID",
		11: "LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS_POSTED_TO_VENDOR",
	}
	LoanInstallmentPayoutFieldMask_value = map[string]int32{
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_UNSPECIFIED":              0,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_STATUS":                   1,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS":                  2,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PAYOUT_DATE":              3,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_INSTALLMENT_INFO_ID": 4,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_AMOUNT":                   5,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_DATE":                 6,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PRINCIPAL_AMT":            7,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_INTEREST_AMT":             8,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_AMT":                  9,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_ACCOUNT_ID":          10,
		"LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS_POSTED_TO_VENDOR": 11,
	}
)

func (x LoanInstallmentPayoutFieldMask) Enum() *LoanInstallmentPayoutFieldMask {
	p := new(LoanInstallmentPayoutFieldMask)
	*p = x
	return p
}

func (x LoanInstallmentPayoutFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanInstallmentPayoutFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[27].Descriptor()
}

func (LoanInstallmentPayoutFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[27]
}

func (x LoanInstallmentPayoutFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanInstallmentPayoutFieldMask.Descriptor instead.
func (LoanInstallmentPayoutFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{27}
}

type LoanApplicationStatus int32

const (
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_UNSPECIFIED         LoanApplicationStatus = 0
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_CREATED             LoanApplicationStatus = 1
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS         LoanApplicationStatus = 2
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_ACCOUNT_CREATED     LoanApplicationStatus = 3
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_CANCELLED           LoanApplicationStatus = 4
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED              LoanApplicationStatus = 5
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION LoanApplicationStatus = 6
)

// Enum value maps for LoanApplicationStatus.
var (
	LoanApplicationStatus_name = map[int32]string{
		0: "LOAN_APPLICATION_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICATION_STATUS_CREATED",
		2: "LOAN_APPLICATION_STATUS_IN_PROGRESS",
		3: "LOAN_APPLICATION_STATUS_ACCOUNT_CREATED",
		4: "LOAN_APPLICATION_STATUS_CANCELLED",
		5: "LOAN_APPLICATION_STATUS_FAILED",
		6: "LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION",
	}
	LoanApplicationStatus_value = map[string]int32{
		"LOAN_APPLICATION_STATUS_UNSPECIFIED":         0,
		"LOAN_APPLICATION_STATUS_CREATED":             1,
		"LOAN_APPLICATION_STATUS_IN_PROGRESS":         2,
		"LOAN_APPLICATION_STATUS_ACCOUNT_CREATED":     3,
		"LOAN_APPLICATION_STATUS_CANCELLED":           4,
		"LOAN_APPLICATION_STATUS_FAILED":              5,
		"LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION": 6,
	}
)

func (x LoanApplicationStatus) Enum() *LoanApplicationStatus {
	p := new(LoanApplicationStatus)
	*p = x
	return p
}

func (x LoanApplicationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[28].Descriptor()
}

func (LoanApplicationStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[28]
}

func (x LoanApplicationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationStatus.Descriptor instead.
func (LoanApplicationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{28}
}

type LoanApplicationSubStatus int32

const (
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED           LoanApplicationSubStatus = 0
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_VKYC                  LoanApplicationSubStatus = 1
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_LIVENESS              LoanApplicationSubStatus = 2
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_FACEMATCH             LoanApplicationSubStatus = 3
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_MANUAL_REVIEW         LoanApplicationSubStatus = 4
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION    LoanApplicationSubStatus = 5
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_ESIGN                 LoanApplicationSubStatus = 6
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_APPLICATION_SUBMITTED LoanApplicationSubStatus = 7
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_DISBURSED             LoanApplicationSubStatus = 8
)

// Enum value maps for LoanApplicationSubStatus.
var (
	LoanApplicationSubStatus_name = map[int32]string{
		0: "LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICATION_SUB_STATUS_VKYC",
		2: "LOAN_APPLICATION_SUB_STATUS_LIVENESS",
		3: "LOAN_APPLICATION_SUB_STATUS_FACEMATCH",
		4: "LOAN_APPLICATION_SUB_STATUS_MANUAL_REVIEW",
		5: "LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION",
		6: "LOAN_APPLICATION_SUB_STATUS_ESIGN",
		7: "LOAN_APPLICATION_SUB_STATUS_APPLICATION_SUBMITTED",
		8: "LOAN_APPLICATION_SUB_STATUS_DISBURSED",
	}
	LoanApplicationSubStatus_value = map[string]int32{
		"LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED":           0,
		"LOAN_APPLICATION_SUB_STATUS_VKYC":                  1,
		"LOAN_APPLICATION_SUB_STATUS_LIVENESS":              2,
		"LOAN_APPLICATION_SUB_STATUS_FACEMATCH":             3,
		"LOAN_APPLICATION_SUB_STATUS_MANUAL_REVIEW":         4,
		"LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION":    5,
		"LOAN_APPLICATION_SUB_STATUS_ESIGN":                 6,
		"LOAN_APPLICATION_SUB_STATUS_APPLICATION_SUBMITTED": 7,
		"LOAN_APPLICATION_SUB_STATUS_DISBURSED":             8,
	}
)

func (x LoanApplicationSubStatus) Enum() *LoanApplicationSubStatus {
	p := new(LoanApplicationSubStatus)
	*p = x
	return p
}

func (x LoanApplicationSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[29].Descriptor()
}

func (LoanApplicationSubStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[29]
}

func (x LoanApplicationSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationSubStatus.Descriptor instead.
func (LoanApplicationSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{29}
}

type GroupStage int32

const (
	GroupStage_GROUP_STAGE_UNSPECIFIED                    GroupStage = 0
	GroupStage_GROUP_STAGE_ONBOARDING                     GroupStage = 1
	GroupStage_GROUP_STAGE_RISK                           GroupStage = 2
	GroupStage_GROUP_STAGE_DRAWDOWN                       GroupStage = 3
	GroupStage_GROUP_STAGE_MANDATE                        GroupStage = 4
	GroupStage_GROUP_STAGE_AUTH                           GroupStage = 5
	GroupStage_GROUP_STAGE_E_SIGN                         GroupStage = 6
	GroupStage_GROUP_STAGE_DISBURSAL                      GroupStage = 8
	GroupStage_GROUP_STAGE_GET_BALANCES                   GroupStage = 9
	GroupStage_GROUP_STAGE_EXECUTE_PAYMENT                GroupStage = 10
	GroupStage_GROUP_STAGE_LMS                            GroupStage = 11
	GroupStage_GROUP_STAGE_PAYMENT                        GroupStage = 12
	GroupStage_GROUP_STAGE_CHECK_BRE                      GroupStage = 13
	GroupStage_GROUP_STAGE_KYC                            GroupStage = 14
	GroupStage_GROUP_STAGE_REVIEW_DETAILS                 GroupStage = 15
	GroupStage_GROUP_STAGE_PORTFOLIO_FETCH                GroupStage = 16
	GroupStage_GROUP_STAGE_OFFER_CREATION                 GroupStage = 17
	GroupStage_GROUP_STAGE_LIEN_MARK                      GroupStage = 18
	GroupStage_GROUP_STAGE_INIT_LOAN                      GroupStage = 19
	GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION          GroupStage = 20
	GroupStage_GROUP_STAGE_VENDOR_PWA                     GroupStage = 21
	GroupStage_GROUP_STAGE_VKYC                           GroupStage = 22
	GroupStage_GROUP_STAGE_VERIFY_LOAN_DETAILS            GroupStage = 23
	GroupStage_GROUP_STAGE_KFS                            GroupStage = 24
	GroupStage_GROUP_STAGE_USER_REGISTRATION              GroupStage = 25
	GroupStage_GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION  GroupStage = 26
	GroupStage_GROUP_STAGE_PRE_KFS                        GroupStage = 27
	GroupStage_GROUP_STAGE_MUTUAL_FUND_NFT_UPDATE_DETAILS GroupStage = 28
	GroupStage_GROUP_STAGE_CONTACTABILITY                 GroupStage = 29
	GroupStage_GROUP_STAGE_VENDOR_LOAN_ACCOUNT_CLOSURE    GroupStage = 30
	GroupStage_GROUP_STAGE_REFRESH_LMS                    GroupStage = 31
	GroupStage_GROUP_STAGE_DATA_COLLECTION                GroupStage = 32
	GroupStage_GROUP_STAGE_UPDATE_LOECS                   GroupStage = 33
	GroupStage_GROUP_STAGE_CHECK_DATA_COMPLETENESS        GroupStage = 34
)

// Enum value maps for GroupStage.
var (
	GroupStage_name = map[int32]string{
		0:  "GROUP_STAGE_UNSPECIFIED",
		1:  "GROUP_STAGE_ONBOARDING",
		2:  "GROUP_STAGE_RISK",
		3:  "GROUP_STAGE_DRAWDOWN",
		4:  "GROUP_STAGE_MANDATE",
		5:  "GROUP_STAGE_AUTH",
		6:  "GROUP_STAGE_E_SIGN",
		8:  "GROUP_STAGE_DISBURSAL",
		9:  "GROUP_STAGE_GET_BALANCES",
		10: "GROUP_STAGE_EXECUTE_PAYMENT",
		11: "GROUP_STAGE_LMS",
		12: "GROUP_STAGE_PAYMENT",
		13: "GROUP_STAGE_CHECK_BRE",
		14: "GROUP_STAGE_KYC",
		15: "GROUP_STAGE_REVIEW_DETAILS",
		16: "GROUP_STAGE_PORTFOLIO_FETCH",
		17: "GROUP_STAGE_OFFER_CREATION",
		18: "GROUP_STAGE_LIEN_MARK",
		19: "GROUP_STAGE_INIT_LOAN",
		20: "GROUP_STAGE_LOAN_ACCOUNT_CREATION",
		21: "GROUP_STAGE_VENDOR_PWA",
		22: "GROUP_STAGE_VKYC",
		23: "GROUP_STAGE_VERIFY_LOAN_DETAILS",
		24: "GROUP_STAGE_KFS",
		25: "GROUP_STAGE_USER_REGISTRATION",
		26: "GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION",
		27: "GROUP_STAGE_PRE_KFS",
		28: "GROUP_STAGE_MUTUAL_FUND_NFT_UPDATE_DETAILS",
		29: "GROUP_STAGE_CONTACTABILITY",
		30: "GROUP_STAGE_VENDOR_LOAN_ACCOUNT_CLOSURE",
		31: "GROUP_STAGE_REFRESH_LMS",
		32: "GROUP_STAGE_DATA_COLLECTION",
		33: "GROUP_STAGE_UPDATE_LOECS",
		34: "GROUP_STAGE_CHECK_DATA_COMPLETENESS",
	}
	GroupStage_value = map[string]int32{
		"GROUP_STAGE_UNSPECIFIED":                    0,
		"GROUP_STAGE_ONBOARDING":                     1,
		"GROUP_STAGE_RISK":                           2,
		"GROUP_STAGE_DRAWDOWN":                       3,
		"GROUP_STAGE_MANDATE":                        4,
		"GROUP_STAGE_AUTH":                           5,
		"GROUP_STAGE_E_SIGN":                         6,
		"GROUP_STAGE_DISBURSAL":                      8,
		"GROUP_STAGE_GET_BALANCES":                   9,
		"GROUP_STAGE_EXECUTE_PAYMENT":                10,
		"GROUP_STAGE_LMS":                            11,
		"GROUP_STAGE_PAYMENT":                        12,
		"GROUP_STAGE_CHECK_BRE":                      13,
		"GROUP_STAGE_KYC":                            14,
		"GROUP_STAGE_REVIEW_DETAILS":                 15,
		"GROUP_STAGE_PORTFOLIO_FETCH":                16,
		"GROUP_STAGE_OFFER_CREATION":                 17,
		"GROUP_STAGE_LIEN_MARK":                      18,
		"GROUP_STAGE_INIT_LOAN":                      19,
		"GROUP_STAGE_LOAN_ACCOUNT_CREATION":          20,
		"GROUP_STAGE_VENDOR_PWA":                     21,
		"GROUP_STAGE_VKYC":                           22,
		"GROUP_STAGE_VERIFY_LOAN_DETAILS":            23,
		"GROUP_STAGE_KFS":                            24,
		"GROUP_STAGE_USER_REGISTRATION":              25,
		"GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION":  26,
		"GROUP_STAGE_PRE_KFS":                        27,
		"GROUP_STAGE_MUTUAL_FUND_NFT_UPDATE_DETAILS": 28,
		"GROUP_STAGE_CONTACTABILITY":                 29,
		"GROUP_STAGE_VENDOR_LOAN_ACCOUNT_CLOSURE":    30,
		"GROUP_STAGE_REFRESH_LMS":                    31,
		"GROUP_STAGE_DATA_COLLECTION":                32,
		"GROUP_STAGE_UPDATE_LOECS":                   33,
		"GROUP_STAGE_CHECK_DATA_COMPLETENESS":        34,
	}
)

func (x GroupStage) Enum() *GroupStage {
	p := new(GroupStage)
	*p = x
	return p
}

func (x GroupStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[30].Descriptor()
}

func (GroupStage) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[30]
}

func (x GroupStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupStage.Descriptor instead.
func (GroupStage) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{30}
}

type LoanProgram int32

const (
	LoanProgram_LOAN_PROGRAM_UNSPECIFIED       LoanProgram = 0
	LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN LoanProgram = 1
	LoanProgram_LOAN_PROGRAM_EARLY_SALARY      LoanProgram = 2
	LoanProgram_LOAN_PROGRAM_FLDG              LoanProgram = 3
	LoanProgram_LOAN_PROGRAM_FI_LITE_PL        LoanProgram = 4
	LoanProgram_LOAN_PROGRAM_FED_REAL_TIME     LoanProgram = 5
	LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND       LoanProgram = 6
	LoanProgram_LOAN_PROGRAM_LAMF              LoanProgram = 7
	// program to identify real time eligibility check for ETB users
	LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB LoanProgram = 8
	// Distribution program where we do not have pre-approved offers from vendor
	LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION LoanProgram = 9
	// small token personal loan
	LoanProgram_LOAN_PROGRAM_STPL LoanProgram = 10
	// Subvention program with realtime BRE capability
	LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION LoanProgram = 11
	// STPL program with realtime BRE capability
	LoanProgram_LOAN_PROGRAM_REALTIME_STPL LoanProgram = 12
	// generic program for Lending eligibility flows
	LoanProgram_LOAN_PROGRAM_ELIGIBILITY            LoanProgram = 13
	LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL       LoanProgram = 14
	LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION LoanProgram = 15
	// this loan program is getting added to provide more flexibility
	// for addition of program in realtime distribution
	//
	// Deprecated: Marked as deprecated in api/preapprovedloan/enums.proto.
	LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_V1 LoanProgram = 16
	// program to identify real time eligibility check for NTB users
	LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB LoanProgram = 17
)

// Enum value maps for LoanProgram.
var (
	LoanProgram_name = map[int32]string{
		0:  "LOAN_PROGRAM_UNSPECIFIED",
		1:  "LOAN_PROGRAM_PRE_APPROVED_LOAN",
		2:  "LOAN_PROGRAM_EARLY_SALARY",
		3:  "LOAN_PROGRAM_FLDG",
		4:  "LOAN_PROGRAM_FI_LITE_PL",
		5:  "LOAN_PROGRAM_FED_REAL_TIME",
		6:  "LOAN_PROGRAM_ACQ_TO_LEND",
		7:  "LOAN_PROGRAM_LAMF",
		8:  "LOAN_PROGRAM_REAL_TIME_ETB",
		9:  "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION",
		10: "LOAN_PROGRAM_STPL",
		11: "LOAN_PROGRAM_REALTIME_SUBVENTION",
		12: "LOAN_PROGRAM_REALTIME_STPL",
		13: "LOAN_PROGRAM_ELIGIBILITY",
		14: "LOAN_PROGRAM_NON_FI_CORE_STPL",
		15: "LOAN_PROGRAM_NON_FI_CORE_SUBVENTION",
		16: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_V1",
		17: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB",
	}
	LoanProgram_value = map[string]int32{
		"LOAN_PROGRAM_UNSPECIFIED":                0,
		"LOAN_PROGRAM_PRE_APPROVED_LOAN":          1,
		"LOAN_PROGRAM_EARLY_SALARY":               2,
		"LOAN_PROGRAM_FLDG":                       3,
		"LOAN_PROGRAM_FI_LITE_PL":                 4,
		"LOAN_PROGRAM_FED_REAL_TIME":              5,
		"LOAN_PROGRAM_ACQ_TO_LEND":                6,
		"LOAN_PROGRAM_LAMF":                       7,
		"LOAN_PROGRAM_REAL_TIME_ETB":              8,
		"LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":     9,
		"LOAN_PROGRAM_STPL":                       10,
		"LOAN_PROGRAM_REALTIME_SUBVENTION":        11,
		"LOAN_PROGRAM_REALTIME_STPL":              12,
		"LOAN_PROGRAM_ELIGIBILITY":                13,
		"LOAN_PROGRAM_NON_FI_CORE_STPL":           14,
		"LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":     15,
		"LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_V1":  16,
		"LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB": 17,
	}
)

func (x LoanProgram) Enum() *LoanProgram {
	p := new(LoanProgram)
	*p = x
	return p
}

func (x LoanProgram) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanProgram) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[31].Descriptor()
}

func (LoanProgram) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[31]
}

func (x LoanProgram) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanProgram.Descriptor instead.
func (LoanProgram) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{31}
}

type LoanApplicantStatus int32

const (
	LoanApplicantStatus_LOAN_APPLICANT_STATUS_UNSPECIFIED LoanApplicantStatus = 0
	// loan applicant is created in our system. Using sub-status we can figure out if applicant
	// is created at vendor's end or not.
	LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED LoanApplicantStatus = 1
	// denotes the state where loan applicant is created at the vendor, and all the necessary
	// details needed to onboard applicant at vendor are added.
	LoanApplicantStatus_LOAN_APPLICANT_STATUS_APPROVED LoanApplicantStatus = 2
	// applicant is blocked either by Fi or by vendor due to compliance or any other reason.
	LoanApplicantStatus_LOAN_APPLICANT_STATUS_BLOCKED  LoanApplicantStatus = 3
	LoanApplicantStatus_LOAN_APPLICANT_STATUS_REJECTED LoanApplicantStatus = 4
)

// Enum value maps for LoanApplicantStatus.
var (
	LoanApplicantStatus_name = map[int32]string{
		0: "LOAN_APPLICANT_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICANT_STATUS_CREATED",
		2: "LOAN_APPLICANT_STATUS_APPROVED",
		3: "LOAN_APPLICANT_STATUS_BLOCKED",
		4: "LOAN_APPLICANT_STATUS_REJECTED",
	}
	LoanApplicantStatus_value = map[string]int32{
		"LOAN_APPLICANT_STATUS_UNSPECIFIED": 0,
		"LOAN_APPLICANT_STATUS_CREATED":     1,
		"LOAN_APPLICANT_STATUS_APPROVED":    2,
		"LOAN_APPLICANT_STATUS_BLOCKED":     3,
		"LOAN_APPLICANT_STATUS_REJECTED":    4,
	}
)

func (x LoanApplicantStatus) Enum() *LoanApplicantStatus {
	p := new(LoanApplicantStatus)
	*p = x
	return p
}

func (x LoanApplicantStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicantStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[32].Descriptor()
}

func (LoanApplicantStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[32]
}

func (x LoanApplicantStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicantStatus.Descriptor instead.
func (LoanApplicantStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{32}
}

type LoanApplicantSubStatus int32

const (
	LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_UNSPECIFIED LoanApplicantSubStatus = 0
	// Loan applicant is created but only at Fi, creation at vendor need to be initiated or is in progress.
	LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI LoanApplicantSubStatus = 1
	// Loan applicant is created at both Fi and vendor's end.
	LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR LoanApplicantSubStatus = 2
)

// Enum value maps for LoanApplicantSubStatus.
var (
	LoanApplicantSubStatus_name = map[int32]string{
		0: "LOAN_APPLICANT_SUB_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI",
		2: "LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR",
	}
	LoanApplicantSubStatus_value = map[string]int32{
		"LOAN_APPLICANT_SUB_STATUS_UNSPECIFIED":       0,
		"LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI":     1,
		"LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR": 2,
	}
)

func (x LoanApplicantSubStatus) Enum() *LoanApplicantSubStatus {
	p := new(LoanApplicantSubStatus)
	*p = x
	return p
}

func (x LoanApplicantSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicantSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[33].Descriptor()
}

func (LoanApplicantSubStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[33]
}

func (x LoanApplicantSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicantSubStatus.Descriptor instead.
func (LoanApplicantSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{33}
}

type PaymentAllocationType int32

const (
	PaymentAllocationType_PAYMENT_ALLOCATION_TYPE_UNSPECIFIED PaymentAllocationType = 0
	// Payment made by the user is allocated in the sequential order of EMIs.
	// The first EMI's principal, interest and charges is settled first and the then the next EMI is settled
	PaymentAllocationType_PAYMENT_ALLOCATION_TYPE_SEQUENTIAL PaymentAllocationType = 1
	// Payment made by the user is first allocated to all the pending due EMIs and then the charges is settled
	PaymentAllocationType_PAYMENT_ALLOCATION_TYPE_PRINCIPAL_AND_INTEREST_FIRST PaymentAllocationType = 2
)

// Enum value maps for PaymentAllocationType.
var (
	PaymentAllocationType_name = map[int32]string{
		0: "PAYMENT_ALLOCATION_TYPE_UNSPECIFIED",
		1: "PAYMENT_ALLOCATION_TYPE_SEQUENTIAL",
		2: "PAYMENT_ALLOCATION_TYPE_PRINCIPAL_AND_INTEREST_FIRST",
	}
	PaymentAllocationType_value = map[string]int32{
		"PAYMENT_ALLOCATION_TYPE_UNSPECIFIED":                  0,
		"PAYMENT_ALLOCATION_TYPE_SEQUENTIAL":                   1,
		"PAYMENT_ALLOCATION_TYPE_PRINCIPAL_AND_INTEREST_FIRST": 2,
	}
)

func (x PaymentAllocationType) Enum() *PaymentAllocationType {
	p := new(PaymentAllocationType)
	*p = x
	return p
}

func (x PaymentAllocationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentAllocationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[34].Descriptor()
}

func (PaymentAllocationType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[34]
}

func (x PaymentAllocationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentAllocationType.Descriptor instead.
func (PaymentAllocationType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{34}
}

type CkycPositiveConfirmationStatus int32

const (
	CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_UNSPECIFIED CkycPositiveConfirmationStatus = 0
	CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_YES         CkycPositiveConfirmationStatus = 1
	CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_NO          CkycPositiveConfirmationStatus = 2
	CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_INPROGRESS  CkycPositiveConfirmationStatus = 3
)

// Enum value maps for CkycPositiveConfirmationStatus.
var (
	CkycPositiveConfirmationStatus_name = map[int32]string{
		0: "CKYC_POSITIVE_CONFIRMATION_STATUS_UNSPECIFIED",
		1: "CKYC_POSITIVE_CONFIRMATION_STATUS_YES",
		2: "CKYC_POSITIVE_CONFIRMATION_STATUS_NO",
		3: "CKYC_POSITIVE_CONFIRMATION_STATUS_INPROGRESS",
	}
	CkycPositiveConfirmationStatus_value = map[string]int32{
		"CKYC_POSITIVE_CONFIRMATION_STATUS_UNSPECIFIED": 0,
		"CKYC_POSITIVE_CONFIRMATION_STATUS_YES":         1,
		"CKYC_POSITIVE_CONFIRMATION_STATUS_NO":          2,
		"CKYC_POSITIVE_CONFIRMATION_STATUS_INPROGRESS":  3,
	}
)

func (x CkycPositiveConfirmationStatus) Enum() *CkycPositiveConfirmationStatus {
	p := new(CkycPositiveConfirmationStatus)
	*p = x
	return p
}

func (x CkycPositiveConfirmationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CkycPositiveConfirmationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[35].Descriptor()
}

func (CkycPositiveConfirmationStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[35]
}

func (x CkycPositiveConfirmationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CkycPositiveConfirmationStatus.Descriptor instead.
func (CkycPositiveConfirmationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{35}
}

type LoanUserDetailsType int32

const (
	LoanUserDetailsType_LOAN_USER_DETAILS_TYPE_UNSPECIFIED       LoanUserDetailsType = 0
	LoanUserDetailsType_LOAN_USER_DETAILS_TYPE_KYC               LoanUserDetailsType = 1
	LoanUserDetailsType_LOAN_USER_DETAILS_TYPE_PERMANENT_ADDRESS LoanUserDetailsType = 2
)

// Enum value maps for LoanUserDetailsType.
var (
	LoanUserDetailsType_name = map[int32]string{
		0: "LOAN_USER_DETAILS_TYPE_UNSPECIFIED",
		1: "LOAN_USER_DETAILS_TYPE_KYC",
		2: "LOAN_USER_DETAILS_TYPE_PERMANENT_ADDRESS",
	}
	LoanUserDetailsType_value = map[string]int32{
		"LOAN_USER_DETAILS_TYPE_UNSPECIFIED":       0,
		"LOAN_USER_DETAILS_TYPE_KYC":               1,
		"LOAN_USER_DETAILS_TYPE_PERMANENT_ADDRESS": 2,
	}
)

func (x LoanUserDetailsType) Enum() *LoanUserDetailsType {
	p := new(LoanUserDetailsType)
	*p = x
	return p
}

func (x LoanUserDetailsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanUserDetailsType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[36].Descriptor()
}

func (LoanUserDetailsType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[36]
}

func (x LoanUserDetailsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanUserDetailsType.Descriptor instead.
func (LoanUserDetailsType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{36}
}

type AssetType int32

const (
	AssetType_ASSET_TYPE_UNSPECIFIED  AssetType = 0
	AssetType_ASSET_TYPE_MUTUAL_FUNDS AssetType = 1
)

// Enum value maps for AssetType.
var (
	AssetType_name = map[int32]string{
		0: "ASSET_TYPE_UNSPECIFIED",
		1: "ASSET_TYPE_MUTUAL_FUNDS",
	}
	AssetType_value = map[string]int32{
		"ASSET_TYPE_UNSPECIFIED":  0,
		"ASSET_TYPE_MUTUAL_FUNDS": 1,
	}
)

func (x AssetType) Enum() *AssetType {
	p := new(AssetType)
	*p = x
	return p
}

func (x AssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[37].Descriptor()
}

func (AssetType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[37]
}

func (x AssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetType.Descriptor instead.
func (AssetType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{37}
}

type FetchedAssetFieldMask int32

const (
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_UNSPECIFIED           FetchedAssetFieldMask = 0
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_VENDOR_ASSET_ID       FetchedAssetFieldMask = 1
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_USER_ASSET_IDENTIFIER FetchedAssetFieldMask = 2
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_FETCHED_AT            FetchedAssetFieldMask = 3
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_ID                    FetchedAssetFieldMask = 4
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_ACTOR_ID              FetchedAssetFieldMask = 5
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_VENDOR                FetchedAssetFieldMask = 6
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_ASSET_TYPE            FetchedAssetFieldMask = 7
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_DETAILS               FetchedAssetFieldMask = 8
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_CREATED_AT            FetchedAssetFieldMask = 9
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_UPDATED_AT            FetchedAssetFieldMask = 10
	FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_DELETED_AT            FetchedAssetFieldMask = 11
)

// Enum value maps for FetchedAssetFieldMask.
var (
	FetchedAssetFieldMask_name = map[int32]string{
		0:  "FETCHED_ASSET_FIELD_MASK_UNSPECIFIED",
		1:  "FETCHED_ASSET_FIELD_MASK_VENDOR_ASSET_ID",
		2:  "FETCHED_ASSET_FIELD_MASK_USER_ASSET_IDENTIFIER",
		3:  "FETCHED_ASSET_FIELD_MASK_FETCHED_AT",
		4:  "FETCHED_ASSET_FIELD_MASK_ID",
		5:  "FETCHED_ASSET_FIELD_MASK_ACTOR_ID",
		6:  "FETCHED_ASSET_FIELD_MASK_VENDOR",
		7:  "FETCHED_ASSET_FIELD_MASK_ASSET_TYPE",
		8:  "FETCHED_ASSET_FIELD_MASK_DETAILS",
		9:  "FETCHED_ASSET_FIELD_MASK_CREATED_AT",
		10: "FETCHED_ASSET_FIELD_MASK_UPDATED_AT",
		11: "FETCHED_ASSET_FIELD_MASK_DELETED_AT",
	}
	FetchedAssetFieldMask_value = map[string]int32{
		"FETCHED_ASSET_FIELD_MASK_UNSPECIFIED":           0,
		"FETCHED_ASSET_FIELD_MASK_VENDOR_ASSET_ID":       1,
		"FETCHED_ASSET_FIELD_MASK_USER_ASSET_IDENTIFIER": 2,
		"FETCHED_ASSET_FIELD_MASK_FETCHED_AT":            3,
		"FETCHED_ASSET_FIELD_MASK_ID":                    4,
		"FETCHED_ASSET_FIELD_MASK_ACTOR_ID":              5,
		"FETCHED_ASSET_FIELD_MASK_VENDOR":                6,
		"FETCHED_ASSET_FIELD_MASK_ASSET_TYPE":            7,
		"FETCHED_ASSET_FIELD_MASK_DETAILS":               8,
		"FETCHED_ASSET_FIELD_MASK_CREATED_AT":            9,
		"FETCHED_ASSET_FIELD_MASK_UPDATED_AT":            10,
		"FETCHED_ASSET_FIELD_MASK_DELETED_AT":            11,
	}
)

func (x FetchedAssetFieldMask) Enum() *FetchedAssetFieldMask {
	p := new(FetchedAssetFieldMask)
	*p = x
	return p
}

func (x FetchedAssetFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchedAssetFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[38].Descriptor()
}

func (FetchedAssetFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[38]
}

func (x FetchedAssetFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchedAssetFieldMask.Descriptor instead.
func (FetchedAssetFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{38}
}

type IdfcCkycAddressPinCodeType int32

const (
	IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_UNSPECIFIED IdfcCkycAddressPinCodeType = 0
	IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_URBAN       IdfcCkycAddressPinCodeType = 1
	IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_CORE  IdfcCkycAddressPinCodeType = 2
	IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_IFBL  IdfcCkycAddressPinCodeType = 3
)

// Enum value maps for IdfcCkycAddressPinCodeType.
var (
	IdfcCkycAddressPinCodeType_name = map[int32]string{
		0: "IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_UNSPECIFIED",
		1: "IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_URBAN",
		2: "IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_CORE",
		3: "IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_IFBL",
	}
	IdfcCkycAddressPinCodeType_value = map[string]int32{
		"IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_UNSPECIFIED": 0,
		"IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_URBAN":       1,
		"IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_CORE":  2,
		"IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_IFBL":  3,
	}
)

func (x IdfcCkycAddressPinCodeType) Enum() *IdfcCkycAddressPinCodeType {
	p := new(IdfcCkycAddressPinCodeType)
	*p = x
	return p
}

func (x IdfcCkycAddressPinCodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdfcCkycAddressPinCodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[39].Descriptor()
}

func (IdfcCkycAddressPinCodeType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[39]
}

func (x IdfcCkycAddressPinCodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdfcCkycAddressPinCodeType.Descriptor instead.
func (IdfcCkycAddressPinCodeType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{39}
}

type OtpType int32

const (
	OtpType_OTP_TYPE_UNSPECIFIED                               OtpType = 0
	OtpType_OTP_TYPE_CAMS_PF_FETCH                             OtpType = 1
	OtpType_OTP_TYPE_KARVY_PF_FETCH                            OtpType = 2
	OtpType_OTP_TYPE_CAMS_LIEN_MARK                            OtpType = 3
	OtpType_OTP_TYPE_KARVY_LIEN_MARK                           OtpType = 4
	OtpType_OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH                 OtpType = 5
	OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH                 OtpType = 6
	OtpType_OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH                  OtpType = 7
	OtpType_OTP_TYPE_MF_CENTRAL_NFT_UPDATE_DETAIL_VERIFICATION OtpType = 8
)

// Enum value maps for OtpType.
var (
	OtpType_name = map[int32]string{
		0: "OTP_TYPE_UNSPECIFIED",
		1: "OTP_TYPE_CAMS_PF_FETCH",
		2: "OTP_TYPE_KARVY_PF_FETCH",
		3: "OTP_TYPE_CAMS_LIEN_MARK",
		4: "OTP_TYPE_KARVY_LIEN_MARK",
		5: "OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH",
		6: "OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH",
		7: "OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH",
		8: "OTP_TYPE_MF_CENTRAL_NFT_UPDATE_DETAIL_VERIFICATION",
	}
	OtpType_value = map[string]int32{
		"OTP_TYPE_UNSPECIFIED":                               0,
		"OTP_TYPE_CAMS_PF_FETCH":                             1,
		"OTP_TYPE_KARVY_PF_FETCH":                            2,
		"OTP_TYPE_CAMS_LIEN_MARK":                            3,
		"OTP_TYPE_KARVY_LIEN_MARK":                           4,
		"OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH":                 5,
		"OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH":                 6,
		"OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH":                  7,
		"OTP_TYPE_MF_CENTRAL_NFT_UPDATE_DETAIL_VERIFICATION": 8,
	}
)

func (x OtpType) Enum() *OtpType {
	p := new(OtpType)
	*p = x
	return p
}

func (x OtpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OtpType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[40].Descriptor()
}

func (OtpType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[40]
}

func (x OtpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OtpType.Descriptor instead.
func (OtpType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{40}
}

type OtpStatus int32

const (
	OtpStatus_OTP_STATUS_UNSPECIFIED OtpStatus = 0
	OtpStatus_OTP_STATUS_GENERATED   OtpStatus = 1
	OtpStatus_OTP_STATUS_SUCCESS     OtpStatus = 2
	OtpStatus_OTP_STATUS_FAILED      OtpStatus = 3
	OtpStatus_OTP_STATUS_NOT_FOUND   OtpStatus = 4
	// otp verification wasn't done within a time window.
	OtpStatus_OTP_STATUS_EXPIRED OtpStatus = 5
)

// Enum value maps for OtpStatus.
var (
	OtpStatus_name = map[int32]string{
		0: "OTP_STATUS_UNSPECIFIED",
		1: "OTP_STATUS_GENERATED",
		2: "OTP_STATUS_SUCCESS",
		3: "OTP_STATUS_FAILED",
		4: "OTP_STATUS_NOT_FOUND",
		5: "OTP_STATUS_EXPIRED",
	}
	OtpStatus_value = map[string]int32{
		"OTP_STATUS_UNSPECIFIED": 0,
		"OTP_STATUS_GENERATED":   1,
		"OTP_STATUS_SUCCESS":     2,
		"OTP_STATUS_FAILED":      3,
		"OTP_STATUS_NOT_FOUND":   4,
		"OTP_STATUS_EXPIRED":     5,
	}
)

func (x OtpStatus) Enum() *OtpStatus {
	p := new(OtpStatus)
	*p = x
	return p
}

func (x OtpStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OtpStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[41].Descriptor()
}

func (OtpStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[41]
}

func (x OtpStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OtpStatus.Descriptor instead.
func (OtpStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{41}
}

// MutualFundFacilitator specifies which vendor is facilitating access to folio units to obtain secured loan
type MutualFundFacilitator int32

const (
	MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_UNSPECIFIED MutualFundFacilitator = 0
	MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS        MutualFundFacilitator = 1
	MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY       MutualFundFacilitator = 2
)

// Enum value maps for MutualFundFacilitator.
var (
	MutualFundFacilitator_name = map[int32]string{
		0: "MUTUAL_FUND_FACILITATOR_UNSPECIFIED",
		1: "MUTUAL_FUND_FACILITATOR_CAMS",
		2: "MUTUAL_FUND_FACILITATOR_KARVY",
	}
	MutualFundFacilitator_value = map[string]int32{
		"MUTUAL_FUND_FACILITATOR_UNSPECIFIED": 0,
		"MUTUAL_FUND_FACILITATOR_CAMS":        1,
		"MUTUAL_FUND_FACILITATOR_KARVY":       2,
	}
)

func (x MutualFundFacilitator) Enum() *MutualFundFacilitator {
	p := new(MutualFundFacilitator)
	*p = x
	return p
}

func (x MutualFundFacilitator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundFacilitator) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[42].Descriptor()
}

func (MutualFundFacilitator) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[42]
}

func (x MutualFundFacilitator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundFacilitator.Descriptor instead.
func (MutualFundFacilitator) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{42}
}

type LoansUserStatus int32

const (
	LoansUserStatus_LOANS_USER_STATUS_UNSPECIFIED              LoansUserStatus = 0
	LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT      LoansUserStatus = 2
	LoansUserStatus_LOANS_USER_STATUS_CLOSED_LOAN_ACCOUNT      LoansUserStatus = 3
	LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_APPLICATION  LoansUserStatus = 4
	LoansUserStatus_LOANS_USER_STATUS_NOT_AN_ACTIVE_LOANS_USER LoansUserStatus = 5
)

// Enum value maps for LoansUserStatus.
var (
	LoansUserStatus_name = map[int32]string{
		0: "LOANS_USER_STATUS_UNSPECIFIED",
		2: "LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT",
		3: "LOANS_USER_STATUS_CLOSED_LOAN_ACCOUNT",
		4: "LOANS_USER_STATUS_ACTIVE_LOAN_APPLICATION",
		5: "LOANS_USER_STATUS_NOT_AN_ACTIVE_LOANS_USER",
	}
	LoansUserStatus_value = map[string]int32{
		"LOANS_USER_STATUS_UNSPECIFIED":              0,
		"LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT":      2,
		"LOANS_USER_STATUS_CLOSED_LOAN_ACCOUNT":      3,
		"LOANS_USER_STATUS_ACTIVE_LOAN_APPLICATION":  4,
		"LOANS_USER_STATUS_NOT_AN_ACTIVE_LOANS_USER": 5,
	}
)

func (x LoansUserStatus) Enum() *LoansUserStatus {
	p := new(LoansUserStatus)
	*p = x
	return p
}

func (x LoansUserStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoansUserStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[43].Descriptor()
}

func (LoansUserStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[43]
}

func (x LoansUserStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoansUserStatus.Descriptor instead.
func (LoansUserStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{43}
}

type ItrFormType int32

const (
	ItrFormType_ITR_FORM_TYPE_UNSPECIFIED ItrFormType = 0
	ItrFormType_ITR_FORM_TYPE_ONE         ItrFormType = 1
	ItrFormType_ITR_FORM_TYPE_TWO         ItrFormType = 2
	ItrFormType_ITR_FORM_TYPE_THREE       ItrFormType = 3
	ItrFormType_ITR_FORM_TYPE_FOUR        ItrFormType = 4
	ItrFormType_ITR_FORM_TYPE_FIVE        ItrFormType = 5
	ItrFormType_ITR_FORM_TYPE_SIX         ItrFormType = 6
	ItrFormType_ITR_FORM_TYPE_SEVEN       ItrFormType = 7
)

// Enum value maps for ItrFormType.
var (
	ItrFormType_name = map[int32]string{
		0: "ITR_FORM_TYPE_UNSPECIFIED",
		1: "ITR_FORM_TYPE_ONE",
		2: "ITR_FORM_TYPE_TWO",
		3: "ITR_FORM_TYPE_THREE",
		4: "ITR_FORM_TYPE_FOUR",
		5: "ITR_FORM_TYPE_FIVE",
		6: "ITR_FORM_TYPE_SIX",
		7: "ITR_FORM_TYPE_SEVEN",
	}
	ItrFormType_value = map[string]int32{
		"ITR_FORM_TYPE_UNSPECIFIED": 0,
		"ITR_FORM_TYPE_ONE":         1,
		"ITR_FORM_TYPE_TWO":         2,
		"ITR_FORM_TYPE_THREE":       3,
		"ITR_FORM_TYPE_FOUR":        4,
		"ITR_FORM_TYPE_FIVE":        5,
		"ITR_FORM_TYPE_SIX":         6,
		"ITR_FORM_TYPE_SEVEN":       7,
	}
)

func (x ItrFormType) Enum() *ItrFormType {
	p := new(ItrFormType)
	*p = x
	return p
}

func (x ItrFormType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItrFormType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[44].Descriptor()
}

func (ItrFormType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[44]
}

func (x ItrFormType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItrFormType.Descriptor instead.
func (ItrFormType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{44}
}

type ItrTaxpayerStatus int32

const (
	ItrTaxpayerStatus_ITR_TAXPAYER_STATUS_UNSPECIFIED ItrTaxpayerStatus = 0
	ItrTaxpayerStatus_ITR_TAXPAYER_STATUS_INDIVIDUAL  ItrTaxpayerStatus = 1
)

// Enum value maps for ItrTaxpayerStatus.
var (
	ItrTaxpayerStatus_name = map[int32]string{
		0: "ITR_TAXPAYER_STATUS_UNSPECIFIED",
		1: "ITR_TAXPAYER_STATUS_INDIVIDUAL",
	}
	ItrTaxpayerStatus_value = map[string]int32{
		"ITR_TAXPAYER_STATUS_UNSPECIFIED": 0,
		"ITR_TAXPAYER_STATUS_INDIVIDUAL":  1,
	}
)

func (x ItrTaxpayerStatus) Enum() *ItrTaxpayerStatus {
	p := new(ItrTaxpayerStatus)
	*p = x
	return p
}

func (x ItrTaxpayerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItrTaxpayerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[45].Descriptor()
}

func (ItrTaxpayerStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[45]
}

func (x ItrTaxpayerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItrTaxpayerStatus.Descriptor instead.
func (ItrTaxpayerStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{45}
}

type ItrTaxpayerResidentialStatus int32

const (
	ItrTaxpayerResidentialStatus_ITR_TAXPAYER_RESIDENTIAL_STATUS_UNSPECIFIED                          ItrTaxpayerResidentialStatus = 0
	ItrTaxpayerResidentialStatus_ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT                             ItrTaxpayerResidentialStatus = 1
	ItrTaxpayerResidentialStatus_ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT_BUT_NOT_ORDINARILY_RESIDENT ItrTaxpayerResidentialStatus = 2
	ItrTaxpayerResidentialStatus_ITR_TAXPAYER_RESIDENTIAL_STATUS_NON_RESIDENT                         ItrTaxpayerResidentialStatus = 3
)

// Enum value maps for ItrTaxpayerResidentialStatus.
var (
	ItrTaxpayerResidentialStatus_name = map[int32]string{
		0: "ITR_TAXPAYER_RESIDENTIAL_STATUS_UNSPECIFIED",
		1: "ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT",
		2: "ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT_BUT_NOT_ORDINARILY_RESIDENT",
		3: "ITR_TAXPAYER_RESIDENTIAL_STATUS_NON_RESIDENT",
	}
	ItrTaxpayerResidentialStatus_value = map[string]int32{
		"ITR_TAXPAYER_RESIDENTIAL_STATUS_UNSPECIFIED":                          0,
		"ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT":                             1,
		"ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT_BUT_NOT_ORDINARILY_RESIDENT": 2,
		"ITR_TAXPAYER_RESIDENTIAL_STATUS_NON_RESIDENT":                         3,
	}
)

func (x ItrTaxpayerResidentialStatus) Enum() *ItrTaxpayerResidentialStatus {
	p := new(ItrTaxpayerResidentialStatus)
	*p = x
	return p
}

func (x ItrTaxpayerResidentialStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItrTaxpayerResidentialStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[46].Descriptor()
}

func (ItrTaxpayerResidentialStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[46]
}

func (x ItrTaxpayerResidentialStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItrTaxpayerResidentialStatus.Descriptor instead.
func (ItrTaxpayerResidentialStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{46}
}

type MandateRequestFieldMask int32

const (
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_UNSPECIFIED       MandateRequestFieldMask = 0
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_ID                MandateRequestFieldMask = 1
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_ACTOR_ID          MandateRequestFieldMask = 2
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_VENDOR_MANDATE_ID MandateRequestFieldMask = 3
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_UPDATED_AT        MandateRequestFieldMask = 4
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_CREATED_AT        MandateRequestFieldMask = 5
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_DELETED_AT        MandateRequestFieldMask = 6
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_DETAILS           MandateRequestFieldMask = 7
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_STATUS            MandateRequestFieldMask = 8
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_LOAN_PROGRAM      MandateRequestFieldMask = 9
	MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_LOAN_VENDOR       MandateRequestFieldMask = 10
)

// Enum value maps for MandateRequestFieldMask.
var (
	MandateRequestFieldMask_name = map[int32]string{
		0:  "MANDATE_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "MANDATE_REQUEST_FIELD_MASK_ID",
		2:  "MANDATE_REQUEST_FIELD_MASK_ACTOR_ID",
		3:  "MANDATE_REQUEST_FIELD_MASK_VENDOR_MANDATE_ID",
		4:  "MANDATE_REQUEST_FIELD_MASK_UPDATED_AT",
		5:  "MANDATE_REQUEST_FIELD_MASK_CREATED_AT",
		6:  "MANDATE_REQUEST_FIELD_MASK_DELETED_AT",
		7:  "MANDATE_REQUEST_FIELD_MASK_DETAILS",
		8:  "MANDATE_REQUEST_FIELD_MASK_STATUS",
		9:  "MANDATE_REQUEST_FIELD_MASK_LOAN_PROGRAM",
		10: "MANDATE_REQUEST_FIELD_MASK_LOAN_VENDOR",
	}
	MandateRequestFieldMask_value = map[string]int32{
		"MANDATE_REQUEST_FIELD_MASK_UNSPECIFIED":       0,
		"MANDATE_REQUEST_FIELD_MASK_ID":                1,
		"MANDATE_REQUEST_FIELD_MASK_ACTOR_ID":          2,
		"MANDATE_REQUEST_FIELD_MASK_VENDOR_MANDATE_ID": 3,
		"MANDATE_REQUEST_FIELD_MASK_UPDATED_AT":        4,
		"MANDATE_REQUEST_FIELD_MASK_CREATED_AT":        5,
		"MANDATE_REQUEST_FIELD_MASK_DELETED_AT":        6,
		"MANDATE_REQUEST_FIELD_MASK_DETAILS":           7,
		"MANDATE_REQUEST_FIELD_MASK_STATUS":            8,
		"MANDATE_REQUEST_FIELD_MASK_LOAN_PROGRAM":      9,
		"MANDATE_REQUEST_FIELD_MASK_LOAN_VENDOR":       10,
	}
)

func (x MandateRequestFieldMask) Enum() *MandateRequestFieldMask {
	p := new(MandateRequestFieldMask)
	*p = x
	return p
}

func (x MandateRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[47].Descriptor()
}

func (MandateRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[47]
}

func (x MandateRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateRequestFieldMask.Descriptor instead.
func (MandateRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{47}
}

type RecordUserActionIdentifier int32

const (
	RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_UNSPECIFIED                           RecordUserActionIdentifier = 0
	RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_MOBILE RecordUserActionIdentifier = 1
	RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_EMAIL  RecordUserActionIdentifier = 2
	RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF         RecordUserActionIdentifier = 3
	RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_RESTART_STAGE     RecordUserActionIdentifier = 4
)

// Enum value maps for RecordUserActionIdentifier.
var (
	RecordUserActionIdentifier_name = map[int32]string{
		0: "RECORD_USER_ACTION_IDENTIFIER_UNSPECIFIED",
		1: "RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_MOBILE",
		2: "RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_EMAIL",
		3: "RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF",
		4: "RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_RESTART_STAGE",
	}
	RecordUserActionIdentifier_value = map[string]int32{
		"RECORD_USER_ACTION_IDENTIFIER_UNSPECIFIED":                           0,
		"RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_MOBILE": 1,
		"RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_EMAIL":  2,
		"RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF":         3,
		"RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_RESTART_STAGE":     4,
	}
)

func (x RecordUserActionIdentifier) Enum() *RecordUserActionIdentifier {
	p := new(RecordUserActionIdentifier)
	*p = x
	return p
}

func (x RecordUserActionIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecordUserActionIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[48].Descriptor()
}

func (RecordUserActionIdentifier) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[48]
}

func (x RecordUserActionIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecordUserActionIdentifier.Descriptor instead.
func (RecordUserActionIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{48}
}

type FiftyFinLamfOfferSource int32

const (
	FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_UNSPECIFIED              FiftyFinLamfOfferSource = 0
	FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED FiftyFinLamfOfferSource = 1
)

// Enum value maps for FiftyFinLamfOfferSource.
var (
	FiftyFinLamfOfferSource_name = map[int32]string{
		0: "FIFTYFIN_LAMF_OFFER_SOURCE_UNSPECIFIED",
		1: "FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED",
	}
	FiftyFinLamfOfferSource_value = map[string]int32{
		"FIFTYFIN_LAMF_OFFER_SOURCE_UNSPECIFIED":              0,
		"FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED": 1,
	}
)

func (x FiftyFinLamfOfferSource) Enum() *FiftyFinLamfOfferSource {
	p := new(FiftyFinLamfOfferSource)
	*p = x
	return p
}

func (x FiftyFinLamfOfferSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FiftyFinLamfOfferSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[49].Descriptor()
}

func (FiftyFinLamfOfferSource) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[49]
}

func (x FiftyFinLamfOfferSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FiftyFinLamfOfferSource.Descriptor instead.
func (FiftyFinLamfOfferSource) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{49}
}

type MandateRequestStatus int32

const (
	MandateRequestStatus_MANDATE_REQUEST_STATUS_UNSPECIFIED MandateRequestStatus = 0
	// This status has to be used when mandate is successfully setup by the user
	MandateRequestStatus_MANDATE_REQUEST_STATUS_SUCCESS MandateRequestStatus = 1
	// This status has to be used when we receive failed status code from vendor when user tries to setup mandate
	// vendor can return failed status code because of multiple reasons like incorrect details shared by the customer
	MandateRequestStatus_MANDATE_REQUEST_STATUS_FAILED MandateRequestStatus = 2
	// This is to be used only if user doesn't complete the mandate and the request gets expired.
	MandateRequestStatus_MANDATE_REQUEST_STATUS_EXPIRED MandateRequestStatus = 3
	// This status has to be used when mandate is initiated at the vendor side such that
	// vendor mandate id is known to the vendor in both cases including cases where
	// mandate id generated by the vendor or by us in our backend system
	MandateRequestStatus_MANDATE_REQUEST_STATUS_INITIATED MandateRequestStatus = 4
	// This status has to be used when mandate is initiated but it is pending because of user has not
	// completed all actions necessary to setup mandate on SDK or webview flow
	MandateRequestStatus_MANDATE_REQUEST_STATUS_PENDING MandateRequestStatus = 5
	// This status has to be used when user cancels or drops off from the mandate journey either from SDK or webview journey
	MandateRequestStatus_MANDATE_REQUEST_STATUS_CANCELED_BY_USER MandateRequestStatus = 6
)

// Enum value maps for MandateRequestStatus.
var (
	MandateRequestStatus_name = map[int32]string{
		0: "MANDATE_REQUEST_STATUS_UNSPECIFIED",
		1: "MANDATE_REQUEST_STATUS_SUCCESS",
		2: "MANDATE_REQUEST_STATUS_FAILED",
		3: "MANDATE_REQUEST_STATUS_EXPIRED",
		4: "MANDATE_REQUEST_STATUS_INITIATED",
		5: "MANDATE_REQUEST_STATUS_PENDING",
		6: "MANDATE_REQUEST_STATUS_CANCELED_BY_USER",
	}
	MandateRequestStatus_value = map[string]int32{
		"MANDATE_REQUEST_STATUS_UNSPECIFIED":      0,
		"MANDATE_REQUEST_STATUS_SUCCESS":          1,
		"MANDATE_REQUEST_STATUS_FAILED":           2,
		"MANDATE_REQUEST_STATUS_EXPIRED":          3,
		"MANDATE_REQUEST_STATUS_INITIATED":        4,
		"MANDATE_REQUEST_STATUS_PENDING":          5,
		"MANDATE_REQUEST_STATUS_CANCELED_BY_USER": 6,
	}
)

func (x MandateRequestStatus) Enum() *MandateRequestStatus {
	p := new(MandateRequestStatus)
	*p = x
	return p
}

func (x MandateRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[50].Descriptor()
}

func (MandateRequestStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[50]
}

func (x MandateRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateRequestStatus.Descriptor instead.
func (MandateRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{50}
}

type MfOfferApprovalStatus int32

const (
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNSPECIFIED                                             MfOfferApprovalStatus = 0
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_ALREADY_LIEN_MARKED                                     MfOfferApprovalStatus = 1
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO                              MfOfferApprovalStatus = 2
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_IS_DEMAT                                                MfOfferApprovalStatus = 3
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST                               MfOfferApprovalStatus = 4
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST_IN_LOAN_ELIGIBILITY_CHECK_API MfOfferApprovalStatus = 5
	MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY                         MfOfferApprovalStatus = 6
)

// Enum value maps for MfOfferApprovalStatus.
var (
	MfOfferApprovalStatus_name = map[int32]string{
		0: "MF_OFFER_APPROVAL_STATUS_UNSPECIFIED",
		1: "MF_OFFER_APPROVAL_STATUS_ALREADY_LIEN_MARKED",
		2: "MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO",
		3: "MF_OFFER_APPROVAL_STATUS_IS_DEMAT",
		4: "MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST",
		5: "MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST_IN_LOAN_ELIGIBILITY_CHECK_API",
		6: "MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY",
	}
	MfOfferApprovalStatus_value = map[string]int32{
		"MF_OFFER_APPROVAL_STATUS_UNSPECIFIED":                                             0,
		"MF_OFFER_APPROVAL_STATUS_ALREADY_LIEN_MARKED":                                     1,
		"MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO":                              2,
		"MF_OFFER_APPROVAL_STATUS_IS_DEMAT":                                                3,
		"MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST":                               4,
		"MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST_IN_LOAN_ELIGIBILITY_CHECK_API": 5,
		"MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY":                         6,
	}
)

func (x MfOfferApprovalStatus) Enum() *MfOfferApprovalStatus {
	p := new(MfOfferApprovalStatus)
	*p = x
	return p
}

func (x MfOfferApprovalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MfOfferApprovalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[51].Descriptor()
}

func (MfOfferApprovalStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[51]
}

func (x MfOfferApprovalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MfOfferApprovalStatus.Descriptor instead.
func (MfOfferApprovalStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{51}
}

type DataRequirementType int32

const (
	DataRequirementType_DATA_REQUIREMENT_TYPE_UNSPECIFIED DataRequirementType = 0
	// deprecated, use DATA_REQUIREMENT_TYPE_TECH_AA
	DataRequirementType_DATA_REQUIREMENT_TYPE_AA    DataRequirementType = 1
	DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL DataRequirementType = 2
	DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO  DataRequirementType = 3
	// here common represents  DATA_REQUIREMENT_TYPES which will be common for data collection for all policies
	// for eg: NameGender,PanDob, Address, Employment,CreditReport
	DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON DataRequirementType = 4
	// loan Data we need to fetch prior calling lenden bre.
	DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION DataRequirementType = 5
	// consent data we need to fetch prior calling lenden bre.
	DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_CONSENT DataRequirementType = 6
	// income estimation with ownership of epifi tech
	DataRequirementType_DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME DataRequirementType = 7
	// consent data we need to fetch prior calling federal bre.
	DataRequirementType_DATA_REQUIREMENT_TYPE_FEDERAL_BRE_CONSENT DataRequirementType = 8
	// pan verification from onboarding needs to be done prior calling federal bre.
	DataRequirementType_DATA_REQUIREMENT_TYPE_FEDERAL_PAN_VERIFICATION DataRequirementType = 9
	// some lender evaluations require full address, this will be used to identify and collect full address in such cases
	DataRequirementType_DATA_REQUIREMENT_TYPE_FULL_ADDRESS DataRequirementType = 10
)

// Enum value maps for DataRequirementType.
var (
	DataRequirementType_name = map[int32]string{
		0:  "DATA_REQUIREMENT_TYPE_UNSPECIFIED",
		1:  "DATA_REQUIREMENT_TYPE_AA",
		2:  "DATA_REQUIREMENT_TYPE_CIBIL",
		3:  "DATA_REQUIREMENT_TYPE_EPFO",
		4:  "DATA_REQUIREMENT_TYPE_COMMON",
		5:  "DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION",
		6:  "DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_CONSENT",
		7:  "DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME",
		8:  "DATA_REQUIREMENT_TYPE_FEDERAL_BRE_CONSENT",
		9:  "DATA_REQUIREMENT_TYPE_FEDERAL_PAN_VERIFICATION",
		10: "DATA_REQUIREMENT_TYPE_FULL_ADDRESS",
	}
	DataRequirementType_value = map[string]int32{
		"DATA_REQUIREMENT_TYPE_UNSPECIFIED":                         0,
		"DATA_REQUIREMENT_TYPE_AA":                                  1,
		"DATA_REQUIREMENT_TYPE_CIBIL":                               2,
		"DATA_REQUIREMENT_TYPE_EPFO":                                3,
		"DATA_REQUIREMENT_TYPE_COMMON":                              4,
		"DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION": 5,
		"DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_CONSENT":              6,
		"DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME":                     7,
		"DATA_REQUIREMENT_TYPE_FEDERAL_BRE_CONSENT":                 8,
		"DATA_REQUIREMENT_TYPE_FEDERAL_PAN_VERIFICATION":            9,
		"DATA_REQUIREMENT_TYPE_FULL_ADDRESS":                        10,
	}
)

func (x DataRequirementType) Enum() *DataRequirementType {
	p := new(DataRequirementType)
	*p = x
	return p
}

func (x DataRequirementType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataRequirementType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[52].Descriptor()
}

func (DataRequirementType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[52]
}

func (x DataRequirementType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataRequirementType.Descriptor instead.
func (DataRequirementType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{52}
}

type LoanDocType int32

const (
	// (default) Used when there is a single esign document
	LoanDocType_LOAN_DOC_TYPE_UNSPECIFIED LoanDocType = 0
	// Used to generate the KFS document
	LoanDocType_LOAN_DOC_TYPE_KFS LoanDocType = 1
	// Used to generate Loan agreement document
	LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT LoanDocType = 2
)

// Enum value maps for LoanDocType.
var (
	LoanDocType_name = map[int32]string{
		0: "LOAN_DOC_TYPE_UNSPECIFIED",
		1: "LOAN_DOC_TYPE_KFS",
		2: "LOAN_DOC_TYPE_LOAN_AGREEMENT",
	}
	LoanDocType_value = map[string]int32{
		"LOAN_DOC_TYPE_UNSPECIFIED":    0,
		"LOAN_DOC_TYPE_KFS":            1,
		"LOAN_DOC_TYPE_LOAN_AGREEMENT": 2,
	}
)

func (x LoanDocType) Enum() *LoanDocType {
	p := new(LoanDocType)
	*p = x
	return p
}

func (x LoanDocType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanDocType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[53].Descriptor()
}

func (LoanDocType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[53]
}

func (x LoanDocType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanDocType.Descriptor instead.
func (LoanDocType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{53}
}

// For certain lenders, interest and principal payments should be made against separate accounts
// this will be used to differentiate between loan payment account type in a loan payment request
type LoanPaymentAccountType int32

const (
	LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED LoanPaymentAccountType = 0
	// for only interest loan payments
	LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST LoanPaymentAccountType = 1
	// for only principal loan payments
	LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL LoanPaymentAccountType = 2
	// for all types of loan payments
	LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_COMMON LoanPaymentAccountType = 3
)

// Enum value maps for LoanPaymentAccountType.
var (
	LoanPaymentAccountType_name = map[int32]string{
		0: "LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED",
		1: "LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST",
		2: "LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL",
		3: "LOAN_PAYMENT_ACCOUNT_TYPE_COMMON",
	}
	LoanPaymentAccountType_value = map[string]int32{
		"LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED": 0,
		"LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST":    1,
		"LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL":   2,
		"LOAN_PAYMENT_ACCOUNT_TYPE_COMMON":      3,
	}
)

func (x LoanPaymentAccountType) Enum() *LoanPaymentAccountType {
	p := new(LoanPaymentAccountType)
	*p = x
	return p
}

func (x LoanPaymentAccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanPaymentAccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[54].Descriptor()
}

func (LoanPaymentAccountType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[54]
}

func (x LoanPaymentAccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanPaymentAccountType.Descriptor instead.
func (LoanPaymentAccountType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{54}
}

type LoanOfferType int32

const (
	LoanOfferType_LOAN_OFFER_TYPE_UNSPECIFIED   LoanOfferType = 0
	LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED LoanOfferType = 1
	LoanOfferType_LOAN_OFFER_TYPE_SOFT          LoanOfferType = 2
	LoanOfferType_LOAN_OFFER_TYPE_HARD          LoanOfferType = 3
)

// Enum value maps for LoanOfferType.
var (
	LoanOfferType_name = map[int32]string{
		0: "LOAN_OFFER_TYPE_UNSPECIFIED",
		1: "LOAN_OFFER_TYPE_PRE_QUALIFIED",
		2: "LOAN_OFFER_TYPE_SOFT",
		3: "LOAN_OFFER_TYPE_HARD",
	}
	LoanOfferType_value = map[string]int32{
		"LOAN_OFFER_TYPE_UNSPECIFIED":   0,
		"LOAN_OFFER_TYPE_PRE_QUALIFIED": 1,
		"LOAN_OFFER_TYPE_SOFT":          2,
		"LOAN_OFFER_TYPE_HARD":          3,
	}
)

func (x LoanOfferType) Enum() *LoanOfferType {
	p := new(LoanOfferType)
	*p = x
	return p
}

func (x LoanOfferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[55].Descriptor()
}

func (LoanOfferType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[55]
}

func (x LoanOfferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferType.Descriptor instead.
func (LoanOfferType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{55}
}

type PreEligibilityOfferFieldMask int32

const (
	PreEligibilityOfferFieldMask_PRE_ELIGIBILITY_OFFER_FIELD_MASK_UNSPECIFIED       PreEligibilityOfferFieldMask = 0
	PreEligibilityOfferFieldMask_PRE_ELIGIBILITY_OFFER_FIELD_MASK_ACTOR_ID          PreEligibilityOfferFieldMask = 1
	PreEligibilityOfferFieldMask_PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_SINCE PreEligibilityOfferFieldMask = 2
	PreEligibilityOfferFieldMask_PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_TILL  PreEligibilityOfferFieldMask = 3
	PreEligibilityOfferFieldMask_PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_DETAILS     PreEligibilityOfferFieldMask = 4
)

// Enum value maps for PreEligibilityOfferFieldMask.
var (
	PreEligibilityOfferFieldMask_name = map[int32]string{
		0: "PRE_ELIGIBILITY_OFFER_FIELD_MASK_UNSPECIFIED",
		1: "PRE_ELIGIBILITY_OFFER_FIELD_MASK_ACTOR_ID",
		2: "PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_SINCE",
		3: "PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_TILL",
		4: "PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_DETAILS",
	}
	PreEligibilityOfferFieldMask_value = map[string]int32{
		"PRE_ELIGIBILITY_OFFER_FIELD_MASK_UNSPECIFIED":       0,
		"PRE_ELIGIBILITY_OFFER_FIELD_MASK_ACTOR_ID":          1,
		"PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_SINCE": 2,
		"PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_TILL":  3,
		"PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_DETAILS":     4,
	}
)

func (x PreEligibilityOfferFieldMask) Enum() *PreEligibilityOfferFieldMask {
	p := new(PreEligibilityOfferFieldMask)
	*p = x
	return p
}

func (x PreEligibilityOfferFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PreEligibilityOfferFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[56].Descriptor()
}

func (PreEligibilityOfferFieldMask) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[56]
}

func (x PreEligibilityOfferFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PreEligibilityOfferFieldMask.Descriptor instead.
func (PreEligibilityOfferFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{56}
}

type LandingInfoFilter int32

const (
	LandingInfoFilter_LANDING_INFO_FILTERS_UNSPECIFIED LandingInfoFilter = 0
	// Skips failed loan request fetch in GETLANDINGINFO_V2 RPC to bypass dashboard screen and direct users to new second look screens
	LandingInfoFilter_LANDING_INFO_SKIP_FAILED_LR_FETCH LandingInfoFilter = 1
)

// Enum value maps for LandingInfoFilter.
var (
	LandingInfoFilter_name = map[int32]string{
		0: "LANDING_INFO_FILTERS_UNSPECIFIED",
		1: "LANDING_INFO_SKIP_FAILED_LR_FETCH",
	}
	LandingInfoFilter_value = map[string]int32{
		"LANDING_INFO_FILTERS_UNSPECIFIED":  0,
		"LANDING_INFO_SKIP_FAILED_LR_FETCH": 1,
	}
)

func (x LandingInfoFilter) Enum() *LandingInfoFilter {
	p := new(LandingInfoFilter)
	*p = x
	return p
}

func (x LandingInfoFilter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LandingInfoFilter) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[57].Descriptor()
}

func (LandingInfoFilter) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[57]
}

func (x LandingInfoFilter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LandingInfoFilter.Descriptor instead.
func (LandingInfoFilter) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{57}
}

// UserStatus represents the current status of a user in the system.
type UserStatus int32

const (
	UserStatus_USER_STATUS_UNSPECIFIED UserStatus = 0
	// user having active eligibility loan request
	UserStatus_USER_STATUS_ACTIVE_ELIGIBILITY UserStatus = 1
	// user having active application loan request
	UserStatus_USER_STATUS_ACTIVE_APPLICATION UserStatus = 2
	// user having active loan account
	UserStatus_USER_STATUS_ACTIVE_LOAN UserStatus = 3
	// user having active loan offer
	UserStatus_USER_STATUS_OFFER_AVAILABLE UserStatus = 4
	// user is either rejected or not eligible for beginning any jounrey in loans
	UserStatus_USER_STATUS_REJECTED UserStatus = 5
	// user is eligible to apply for a loan offer, i.e. user can start eligibility journey
	UserStatus_USER_STATUS_ELIGIBLE_TO_APPLY UserStatus = 6
)

// Enum value maps for UserStatus.
var (
	UserStatus_name = map[int32]string{
		0: "USER_STATUS_UNSPECIFIED",
		1: "USER_STATUS_ACTIVE_ELIGIBILITY",
		2: "USER_STATUS_ACTIVE_APPLICATION",
		3: "USER_STATUS_ACTIVE_LOAN",
		4: "USER_STATUS_OFFER_AVAILABLE",
		5: "USER_STATUS_REJECTED",
		6: "USER_STATUS_ELIGIBLE_TO_APPLY",
	}
	UserStatus_value = map[string]int32{
		"USER_STATUS_UNSPECIFIED":        0,
		"USER_STATUS_ACTIVE_ELIGIBILITY": 1,
		"USER_STATUS_ACTIVE_APPLICATION": 2,
		"USER_STATUS_ACTIVE_LOAN":        3,
		"USER_STATUS_OFFER_AVAILABLE":    4,
		"USER_STATUS_REJECTED":           5,
		"USER_STATUS_ELIGIBLE_TO_APPLY":  6,
	}
)

func (x UserStatus) Enum() *UserStatus {
	p := new(UserStatus)
	*p = x
	return p
}

func (x UserStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_proto_enumTypes[58].Descriptor()
}

func (UserStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_proto_enumTypes[58]
}

func (x UserStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserStatus.Descriptor instead.
func (UserStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{58}
}

type LoanHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanProgram LoanProgram `protobuf:"varint,1,opt,name=loan_program,json=loanProgram,proto3,enum=preapprovedloan.LoanProgram" json:"loan_program,omitempty"`
	Vendor      Vendor      `protobuf:"varint,2,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
	// Deprecated: Use loan type in GetLandingInfoV2Request instead.
	LoanType LoanType `protobuf:"varint,3,opt,name=loan_type,json=loanType,proto3,enum=preapprovedloan.LoanType" json:"loan_type,omitempty"`
	// to be used in flows where data ownership and workflow ownership is not same.
	DataOwner Vendor `protobuf:"varint,4,opt,name=data_owner,json=dataOwner,proto3,enum=preapprovedloan.Vendor" json:"data_owner,omitempty"`
}

func (x *LoanHeader) Reset() {
	*x = LoanHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_enums_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanHeader) ProtoMessage() {}

func (x *LoanHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_enums_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanHeader.ProtoReflect.Descriptor instead.
func (*LoanHeader) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_proto_rawDescGZIP(), []int{0}
}

func (x *LoanHeader) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *LoanHeader) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

func (x *LoanHeader) GetLoanType() LoanType {
	if x != nil {
		return x.LoanType
	}
	return LoanType_LOAN_TYPE_UNSPECIFIED
}

func (x *LoanHeader) GetDataOwner() Vendor {
	if x != nil {
		return x.DataOwner
	}
	return Vendor_VENDOR_UNSPECIFIED
}

var File_api_preapprovedloan_enums_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_enums_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x22, 0xee, 0x01, 0x0a, 0x0a, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x09, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x2a, 0xb9, 0x01, 0x0a, 0x06, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x16,
	0x0a, 0x12, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x46, 0x43, 0x10, 0x03, 0x12, 0x0c, 0x0a,
	0x08, 0x46, 0x49, 0x46, 0x54, 0x59, 0x46, 0x49, 0x4e, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x41,
	0x42, 0x46, 0x4c, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x54,
	0x45, 0x43, 0x48, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4d,
	0x46, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41, 0x4c, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x4c, 0x53,
	0x50, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x4e, 0x10, 0x0a, 0x2a,
	0xfb, 0x02, 0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53,
	0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x46, 0x45, 0x54, 0x43,
	0x48, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x06, 0x12,
	0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x49, 0x10, 0x07, 0x12,
	0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x4e, 0x46, 0x54, 0x10, 0x08, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x09, 0x2a, 0xc1, 0x03,
	0x0a, 0x11, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x07, 0x12, 0x21, 0x0a,
	0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45, 0x44, 0x10, 0x08,
	0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x09, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c,
	0x45, 0x44, 0x10, 0x0a, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0b, 0x2a, 0xfc, 0x13, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x4b, 0x59, 0x43,
	0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x03,
	0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x10, 0x04, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4f, 0x54, 0x50,
	0x10, 0x07, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10,
	0x08, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x10, 0x09, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x0a,
	0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x53, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53, 0x54, 0x45, 0x44, 0x10, 0x0b, 0x12,
	0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x0c, 0x12, 0x27, 0x0a, 0x23,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x56,
	0x4b, 0x59, 0x43, 0x10, 0x0d, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x0e, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x0f, 0x12, 0x38, 0x0a,
	0x34, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x10, 0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x11, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x12, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x13, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x14, 0x12,
	0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x15, 0x12,
	0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x4e,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x16, 0x12, 0x2d, 0x0a, 0x29, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x17, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x18, 0x12, 0x27, 0x0a, 0x23, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4f,
	0x54, 0x50, 0x10, 0x19, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4f, 0x54, 0x50, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x1a, 0x12,
	0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x4c, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4b, 0x46, 0x53, 0x10, 0x1c, 0x12,
	0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x1d, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4b, 0x46, 0x53, 0x10, 0x1e, 0x12, 0x28, 0x0a, 0x24,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x5f, 0x4b, 0x46, 0x53, 0x10, 0x1f, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x20, 0x12, 0x37, 0x0a,
	0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x21, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x22, 0x12, 0x37,
	0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x46, 0x54, 0x45, 0x52,
	0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x23, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x41,
	0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x24, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x25, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x26, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x27, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x41, 0x44, 0x44, 0x45, 0x44, 0x10, 0x28, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x10, 0x29, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x10, 0x2a, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x2b, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54, 0x50, 0x53, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x2c, 0x12, 0x29, 0x0a, 0x25,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x57, 0x5f, 0x50, 0x4f, 0x52, 0x54,
	0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x10, 0x2d, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x2e, 0x12, 0x3a, 0x0a, 0x36,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x2f, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f,
	0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x30, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x41, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x45, 0x54,
	0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x31, 0x12, 0x41, 0x0a, 0x3d, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x32, 0x12, 0x40,
	0x0a, 0x3c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x33,
	0x2a, 0x78, 0x0a, 0x11, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x75, 0x0a, 0x08, 0x4c, 0x6f,
	0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10,
	0x03, 0x2a, 0x9e, 0x04, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52, 0x43, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x2f,
	0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x03, 0x12,
	0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x22,
	0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x06, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x07, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x08, 0x12, 0x26, 0x0a,
	0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x09, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12,
	0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d,
	0x10, 0x0c, 0x2a, 0xeb, 0x04, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x46, 0x53, 0x43, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45,
	0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x41,
	0x42, 0x4c, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4e, 0x44,
	0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x45,
	0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0x0a, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0c, 0x12,
	0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4d, 0x53, 0x5f, 0x50,
	0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x0d,
	0x2a, 0xa2, 0x03, 0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29,
	0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x41,
	0x49, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x50, 0x52, 0x45, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10,
	0x03, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44,
	0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x04, 0x12,
	0x24, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x49,
	0x44, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x07, 0x12, 0x28, 0x0a, 0x24, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x08, 0x2a, 0x8f, 0x03, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x01, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x02, 0x12, 0x31, 0x0a, 0x2d, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2f,
	0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12,
	0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x4d, 0x50,
	0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x06, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x2a, 0xee, 0x02, 0x0a, 0x17, 0x4c, 0x6f, 0x61, 0x6e,
	0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x32, 0x0a, 0x2e,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06,
	0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45,
	0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x07, 0x2a, 0xcf, 0x46, 0x0a, 0x1a, 0x4c, 0x6f, 0x61,
	0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75,
	0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x3b, 0x0a,
	0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x45, 0x44, 0x10, 0x02, 0x12, 0x38, 0x0a, 0x34, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x44, 0x44,
	0x52, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x41, 0x44, 0x44,
	0x45, 0x44, 0x10, 0x03, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c,
	0x49, 0x4e, 0x4b, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0x04, 0x12, 0x30, 0x0a,
	0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x05, 0x12,
	0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x06, 0x12, 0x3e, 0x0a, 0x3a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x10, 0x07, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x53, 0x45, 0x44, 0x10, 0x08, 0x12, 0x3b, 0x0a,
	0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0x09, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x4e,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x38, 0x0a, 0x34, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b,
	0x59, 0x43, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x45,
	0x44, 0x10, 0x0c, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x41, 0x47, 0x52, 0x45,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44, 0x10, 0x0d,
	0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x45, 0x44, 0x10, 0x0e, 0x12, 0x3b, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f,
	0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x0f,
	0x1a, 0x02, 0x08, 0x01, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x59, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x10, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x11, 0x12,
	0x49, 0x0a, 0x45, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x49, 0x53, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x12, 0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43,
	0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x44, 0x10, 0x13, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x55,
	0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x14, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x15, 0x12, 0x40, 0x0a, 0x3c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x16, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x17, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x41, 0x41,
	0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x18, 0x12, 0x39,
	0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x19, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x1a, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x2e, 0x0a,
	0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x1c, 0x12, 0x30, 0x0a,
	0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x44, 0x10, 0x1d, 0x12,
	0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44, 0x10, 0x1e, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x1f, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f,
	0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x20, 0x12, 0x3b,
	0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4e, 0x4f, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x44, 0x5f,
	0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x10, 0x21, 0x12, 0x45, 0x0a, 0x41, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e,
	0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f,
	0x4d, 0x41, 0x52, 0x4b, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f,
	0x10, 0x22, 0x12, 0x40, 0x0a, 0x3c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c,
	0x4c, 0x59, 0x10, 0x23, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x24, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x4d, 0x41, 0x4b, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x25, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x26, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4c,
	0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x44, 0x10, 0x27, 0x12, 0x35, 0x0a, 0x31,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e,
	0x4f, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x28, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x57, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f,
	0x4c, 0x49, 0x4f, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x29, 0x12, 0x37, 0x0a, 0x33, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c,
	0x4c, 0x5f, 0x4f, 0x54, 0x50, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x45, 0x44, 0x10, 0x2a, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x2b, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41,
	0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x10, 0x2c, 0x12, 0x35, 0x0a,
	0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x55, 0x53,
	0x45, 0x44, 0x10, 0x2d, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x2e, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x2f, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x53, 0x59, 0x4e, 0x43, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x30, 0x12, 0x2a,
	0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x31, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x44,
	0x55, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x10, 0x32, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x42, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x33, 0x12, 0x39, 0x0a, 0x35,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x56, 0x45,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x34, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x4f,
	0x52, 0x4d, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x35, 0x12, 0x43, 0x0a, 0x3f, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x56, 0x49, 0x4f, 0x55, 0x53,
	0x4c, 0x59, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x36, 0x12, 0x3c, 0x0a,
	0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x42, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x47, 0x41, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x53, 0x10, 0x37, 0x12, 0x3f, 0x0a, 0x3b, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x47,
	0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x38, 0x12, 0x2f, 0x0a, 0x2b,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x39, 0x12, 0x30, 0x0a,
	0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x3a, 0x12,
	0x47, 0x0a, 0x43, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x3b, 0x12, 0x46, 0x0a, 0x42, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f,
	0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x3c,
	0x12, 0x42, 0x0a, 0x3e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x3d, 0x12, 0x45, 0x0a, 0x41, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4b, 0x59, 0x43,
	0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x3e, 0x12, 0x42, 0x0a, 0x3e, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b,
	0x59, 0x43, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4b, 0x59, 0x43,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x3f, 0x12,
	0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x49, 0x4e,
	0x44, 0x49, 0x41, 0x10, 0x40, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x41, 0x12, 0x4e, 0x0a, 0x4a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e,
	0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f,
	0x41, 0x52, 0x45, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x53, 0x10, 0x42, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x53, 0x10, 0x43, 0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x43, 0x41, 0x4e,
	0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x44, 0x12, 0x4e, 0x0a, 0x4a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x57, 0x49, 0x54,
	0x48, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x10, 0x45, 0x12, 0x42, 0x0a, 0x3e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x44,
	0x45, 0x4e, 0x43, 0x45, 0x10, 0x46, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x47, 0x12, 0x52, 0x0a, 0x4e, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x53,
	0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x48, 0x12, 0x49,
	0x0a, 0x45, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x47, 0x45,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x41,
	0x54, 0x49, 0x53, 0x46, 0x49, 0x45, 0x44, 0x10, 0x49, 0x12, 0x4c, 0x0a, 0x48, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x43,
	0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x41, 0x54, 0x49,
	0x53, 0x46, 0x49, 0x45, 0x44, 0x10, 0x4a, 0x12, 0x38, 0x0a, 0x34, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41,
	0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x45, 0x44, 0x10,
	0x4b, 0x12, 0x38, 0x0a, 0x34, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x4c, 0x12, 0x4a, 0x0a, 0x46, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41,
	0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x4d, 0x12, 0x49, 0x0a, 0x45, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41,
	0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x10, 0x4e, 0x12, 0x43, 0x0a, 0x3f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x54,
	0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x4f, 0x12, 0x42, 0x0a, 0x3e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x50, 0x12, 0x41, 0x0a, 0x3d, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41,
	0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41,
	0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x51, 0x12, 0x40,
	0x0a, 0x3c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x52,
	0x12, 0x4c, 0x0a, 0x48, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x45,
	0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x53, 0x12, 0x35,
	0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x54, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x55, 0x12, 0x42, 0x0a,
	0x3e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10,
	0x56, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x57, 0x12, 0x48,
	0x0a, 0x44, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x58, 0x12, 0x46, 0x0a, 0x42, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52,
	0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x10, 0x59,
	0x12, 0x54, 0x0a, 0x50, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4d, 0x49, 0x4e,
	0x49, 0x4d, 0x55, 0x4d, 0x10, 0x5a, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x4f,
	0x42, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x5b, 0x12, 0x3e, 0x0a, 0x3a,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43,
	0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x5c, 0x12, 0x54, 0x0a, 0x50,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43,
	0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x10, 0x5d, 0x12, 0x5b, 0x0a, 0x57, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x44, 0x5f,
	0x49, 0x4e, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x5e, 0x12,
	0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x53, 0x5f, 0x43, 0x41,
	0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44, 0x10, 0x5f,
	0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x60,
	0x12, 0x4c, 0x0a, 0x48, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x44, 0x52, 0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x4d, 0x49, 0x5f,
	0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4d, 0x41, 0x58,
	0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x61, 0x12, 0x56,
	0x0a, 0x52, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x44, 0x52, 0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x49, 0x4e, 0x5f, 0x54,
	0x48, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x43, 0x52, 0x49, 0x42, 0x45, 0x44, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x53, 0x10, 0x62, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4f,
	0x55, 0x54, 0x10, 0x63, 0x12, 0x44, 0x0a, 0x40, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x10, 0x64, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x53, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x65, 0x12, 0x3d, 0x0a, 0x39, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41,
	0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x66, 0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x47, 0x45,
	0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x67, 0x12, 0x42, 0x0a, 0x3e, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x68, 0x12, 0x34, 0x0a,
	0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x4b, 0x46,
	0x53, 0x10, 0x69, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x6a, 0x12, 0x36,
	0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x5f,
	0x42, 0x41, 0x4e, 0x4b, 0x10, 0x6b, 0x12, 0x41, 0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x6c, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e,
	0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x6d, 0x12, 0x44, 0x0a, 0x40, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x6e, 0x12, 0x4e, 0x0a,
	0x4a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x50, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x6f, 0x12, 0x49, 0x0a,
	0x45, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x50, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x70, 0x12, 0x4c, 0x0a, 0x48, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d,
	0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41,
	0x42, 0x4c, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0x71, 0x12, 0x41, 0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x53, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x72, 0x12, 0x41, 0x0a, 0x3d, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x53, 0x5f,
	0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x46, 0x45,
	0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x73, 0x12, 0x31, 0x0a, 0x2d,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x4e, 0x10, 0x74, 0x12,
	0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x75, 0x12, 0x36, 0x0a,
	0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x44, 0x5f,
	0x4f, 0x55, 0x54, 0x10, 0x76, 0x12, 0x43, 0x0a, 0x3f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x55, 0x43, 0x4b, 0x5f, 0x49, 0x4e,
	0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52,
	0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x77, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4b, 0x49,
	0x50, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x10, 0x78, 0x12, 0x41, 0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x50, 0x45, 0x52,
	0x49, 0x4f, 0x44, 0x10, 0x79, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x55, 0x52, 0x4c, 0x5f,
	0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0x7a, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x7b, 0x12,
	0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x41, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x46,
	0x45, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x7c, 0x12, 0x37, 0x0a,
	0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x7d, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x10, 0x7e, 0x12, 0x3e, 0x0a, 0x3a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x10, 0x7f, 0x12, 0x3c, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x54,
	0x55, 0x50, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x80, 0x01,
	0x12, 0x41, 0x0a, 0x3c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x5f,
	0x4f, 0x46, 0x46, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59,
	0x10, 0x81, 0x01, 0x12, 0x47, 0x0a, 0x42, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x56, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x4d,
	0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x82, 0x01, 0x12, 0x3f, 0x0a, 0x3a,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x83, 0x01, 0x12, 0x3d, 0x0a,
	0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x52, 0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x84, 0x01, 0x12, 0x30, 0x0a, 0x2b,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53,
	0x49, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0x85, 0x01, 0x12, 0x37,
	0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x86, 0x01, 0x12, 0x37, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43,
	0x41, 0x4c, 0x4c, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x87, 0x01,
	0x12, 0x42, 0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x44, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x88, 0x01, 0x12, 0x3b, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x52, 0x45, 0x44, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x4f, 0x4e, 0x45, 0x10, 0x89,
	0x01, 0x12, 0x3d, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x8a, 0x01,
	0x12, 0x38, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x44, 0x4f, 0x4e, 0x45, 0x10, 0x8b, 0x01, 0x12, 0x36, 0x0a, 0x31, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4b, 0x49,
	0x50, 0x50, 0x45, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10,
	0x8c, 0x01, 0x12, 0x3a, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52,
	0x41, 0x52, 0x59, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x8d, 0x01, 0x12, 0x3c,
	0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x8e, 0x01, 0x12, 0x3f, 0x0a, 0x3a,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f,
	0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x8f, 0x01, 0x12, 0x3b, 0x0a,
	0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x90, 0x01, 0x2a, 0xc4, 0x04, 0x0a, 0x15, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x6c, 0x6f, 0x77, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27,
	0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10,
	0x03, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10,
	0x05, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x45, 0x4c,
	0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x06, 0x12, 0x2c, 0x0a, 0x28, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49,
	0x4f, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x49, 0x10, 0x08,
	0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4f, 0x46, 0x46,
	0x5f, 0x41, 0x50, 0x50, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x09, 0x12, 0x2c,
	0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41,
	0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x46, 0x54, 0x10, 0x0a, 0x12, 0x25, 0x0a, 0x21,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41,
	0x59, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0x0c, 0x2a, 0xf7, 0x24, 0x0a, 0x19, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b,
	0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4b, 0x59, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x2d, 0x0a,
	0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d,
	0x52, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x52, 0x5f, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4b, 0x46, 0x53,
	0x10, 0x06, 0x12, 0x38, 0x0a, 0x34, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4b,
	0x59, 0x43, 0x10, 0x08, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x0a, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x10, 0x0b, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x0d, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x0e, 0x12, 0x2a, 0x0a, 0x26,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x52,
	0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x0f, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x10, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x10, 0x11, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x12,
	0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x13, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x14,
	0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x15, 0x12, 0x2c, 0x0a, 0x28,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x49, 0x10, 0x16, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x4c, 0x4d, 0x53, 0x10, 0x17, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x18, 0x12, 0x35, 0x0a, 0x31, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48,
	0x10, 0x19, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0x1a, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x49, 0x5f,
	0x42, 0x52, 0x45, 0x10, 0x1b, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x1c, 0x12, 0x30, 0x0a,
	0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x1d, 0x12,
	0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x44, 0x4f, 0x42, 0x10, 0x1e, 0x12, 0x2d,
	0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x1f, 0x12, 0x31, 0x0a,
	0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50,
	0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0x20,
	0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x10, 0x21, 0x12, 0x32, 0x0a, 0x2e, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x22, 0x12,
	0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x23, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x49,
	0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x10, 0x24, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f,
	0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x25, 0x12, 0x2b,
	0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x10, 0x26, 0x12, 0x31, 0x0a, 0x2d, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x4c, 0x49, 0x53, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x27, 0x12, 0x29,
	0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x28, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x29, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x42, 0x52, 0x45, 0x10, 0x2a, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x49, 0x45, 0x5f, 0x43, 0x41,
	0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x2b, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e,
	0x43, 0x45, 0x53, 0x10, 0x2c, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x45,
	0x41, 0x44, 0x10, 0x2d, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x53, 0x10,
	0x2e, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x2f, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x54, 0x5f, 0x4c, 0x4f,
	0x4e, 0x47, 0x10, 0x30, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x31, 0x12, 0x2d, 0x0a, 0x29, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x5f, 0x41,
	0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x32, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x33, 0x12,
	0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x34,
	0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4c, 0x4d, 0x53, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x35, 0x12, 0x3b, 0x0a,
	0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50,
	0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4c, 0x4d, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x36, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x54,
	0x4e, 0x45, 0x52, 0x5f, 0x4c, 0x4d, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x49, 0x53,
	0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x37, 0x12, 0x45, 0x0a, 0x41, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x5f, 0x41, 0x54, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x38, 0x12,
	0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x50, 0x52, 0x45, 0x5f, 0x4b, 0x46, 0x53, 0x10, 0x39, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x41,
	0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4c, 0x4d, 0x53, 0x10, 0x3a, 0x12, 0x42, 0x0a, 0x3e, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x46,
	0x52, 0x45, 0x53, 0x48, 0x5f, 0x4d, 0x49, 0x52, 0x52, 0x4f, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x4d,
	0x53, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x3b, 0x12,
	0x3e, 0x0a, 0x3a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x3c, 0x12,
	0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x43, 0x49, 0x42, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x45,
	0x54, 0x43, 0x48, 0x10, 0x3d, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x46,
	0x5f, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x3e,
	0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x41, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10,
	0x3f, 0x12, 0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52,
	0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x40, 0x12, 0x30,
	0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x41,
	0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x42, 0x12,
	0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x53, 0x45, 0x54, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x43, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x4f, 0x45, 0x43, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x44, 0x12, 0x31, 0x0a,
	0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x46,
	0x45, 0x54, 0x43, 0x48, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x45,
	0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x10, 0x46, 0x12, 0x41,
	0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x45, 0x54,
	0x43, 0x48, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x47, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x10, 0x48, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x4d, 0x4c, 0x10, 0x49, 0x12, 0x39, 0x0a, 0x35, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45,
	0x4e, 0x45, 0x53, 0x53, 0x10, 0x4a, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4b, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45,
	0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4c, 0x12, 0x34, 0x0a,
	0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x4d, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4e, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x4f, 0x12, 0x37,
	0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43, 0x41,
	0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x50, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x41,
	0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43,
	0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x5a, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55,
	0x52, 0x45, 0x10, 0x5b, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x4f, 0x49, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x5c, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x5f, 0x4b, 0x46, 0x53,
	0x10, 0x5d, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53,
	0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x5e, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x5f, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x60, 0x12, 0x3a,
	0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x61, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x4d,
	0x49, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4b, 0x59,
	0x43, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x62, 0x2a, 0x8f, 0x04, 0x0a, 0x1a,
	0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46,
	0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54,
	0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x12, 0x2a,
	0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x4f, 0x52, 0x43, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x06, 0x12,
	0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x07, 0x12, 0x2c,
	0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x2f, 0x0a, 0x2b,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x2e, 0x0a,
	0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10, 0x0a, 0x2a, 0x82, 0x06,
	0x0a, 0x25, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x3e, 0x0a, 0x3a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x02, 0x12, 0x37, 0x0a, 0x33, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x03, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x04, 0x12, 0x37,
	0x0a, 0x33, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x42, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x05, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0x06, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49,
	0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x10, 0x07, 0x12, 0x3c,
	0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x4f, 0x4c,
	0x49, 0x43, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x10, 0x08, 0x12, 0x40, 0x0a, 0x3c,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x09, 0x12, 0x39,
	0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x47, 0x0a, 0x43, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0x0b, 0x2a, 0xfa, 0x01, 0x0a, 0x22, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54,
	0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a,
	0xda, 0x0c, 0x0a, 0x25, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x41, 0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x42, 0x59, 0x5f, 0x46, 0x49, 0x10, 0x02, 0x12, 0x41, 0x0a, 0x3d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x42,
	0x59, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x3d, 0x0a, 0x39, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45,
	0x44, 0x5f, 0x42, 0x59, 0x5f, 0x46, 0x49, 0x10, 0x04, 0x12, 0x45, 0x0a, 0x41, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x42, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x10, 0x05,
	0x12, 0x45, 0x0a, 0x41, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45,
	0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45,
	0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41,
	0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x42, 0x52, 0x45, 0x10, 0x06, 0x12, 0x46, 0x0a, 0x42, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x07, 0x12,
	0x4c, 0x0a, 0x48, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c,
	0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52,
	0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e,
	0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x12, 0x49, 0x0a,
	0x45, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x09, 0x12, 0x5b, 0x0a, 0x57, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f,
	0x42, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x54, 0x0a, 0x50, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x47, 0x45, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x58, 0x0a, 0x54, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x42, 0x0a, 0x3e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x10, 0x0d, 0x12, 0x42, 0x0a, 0x3e, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x42, 0x59, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x10, 0x0e, 0x12, 0x47, 0x0a,
	0x43, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x10, 0x0f, 0x12, 0x4f, 0x0a, 0x4b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x59,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x10, 0x12, 0x61, 0x0a, 0x5d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x42,
	0x59, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x4f, 0x43, 0x43,
	0x55, 0x50, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x11, 0x12, 0x51, 0x0a, 0x4d, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x42, 0x59, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53,
	0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x12, 0x12, 0x48, 0x0a,
	0x44, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x13, 0x12, 0x53, 0x0a, 0x4f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x4c, 0x45, 0x4e, 0x44,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x14, 0x2a, 0xf1, 0x01, 0x0a,
	0x19, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x01, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x27, 0x0a,
	0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c,
	0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04,
	0x2a, 0x9e, 0x04, 0x0a, 0x1c, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x52,
	0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x4e, 0x44,
	0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x3c, 0x0a, 0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x54, 0x41,
	0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x3a, 0x0a, 0x36, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x06, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x12,
	0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x08, 0x12, 0x33, 0x0a, 0x2f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x09, 0x2a, 0xbd, 0x01, 0x0a, 0x10, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x45, 0x4d, 0x49, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x55, 0x4d,
	0x50, 0x53, 0x55, 0x4d, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x04, 0x2a, 0x8c, 0x02, 0x0a, 0x15, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x28, 0x0a, 0x24, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05,
	0x2a, 0xd5, 0x02, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x49, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x55, 0x4d, 0x50, 0x53, 0x55, 0x4d, 0x10, 0x02,
	0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x03, 0x12, 0x2e, 0x0a, 0x2a, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x43,
	0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x30, 0x0a, 0x2c, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x39, 0x0a,
	0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52,
	0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x2a, 0xa0, 0x03, 0x0a, 0x18, 0x4c, 0x6f, 0x61,
	0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x06, 0x12, 0x27, 0x0a,
	0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x07, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x2a, 0x4e, 0x0a, 0x1b, 0x4c,
	0x6f, 0x61, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a, 0x97, 0x04, 0x0a, 0x1b,
	0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2f, 0x0a, 0x2b, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52,
	0x43, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x05,
	0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x07, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x08, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54,
	0x5f, 0x49, 0x44, 0x10, 0x0a, 0x2a, 0xe1, 0x02, 0x0a, 0x1b, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x29, 0x0a,
	0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59,
	0x4f, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49,
	0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41,
	0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41,
	0x59, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x06, 0x2a, 0x9c, 0x05, 0x0a, 0x1e, 0x4c, 0x6f,
	0x61, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x0a, 0x2e,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12,
	0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x02, 0x12,
	0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x03, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x49, 0x44, 0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x05, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x55, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x06, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43,
	0x49, 0x50, 0x41, 0x4c, 0x5f, 0x41, 0x4d, 0x54, 0x10, 0x07, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50,
	0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x4d, 0x54, 0x10, 0x08, 0x12,
	0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x55, 0x45, 0x5f, 0x41, 0x4d, 0x54, 0x10, 0x09, 0x12,
	0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x0a, 0x12, 0x3f, 0x0a, 0x3b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f,
	0x55, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x0b, 0x2a, 0xb7, 0x02, 0x0a, 0x15, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x22, 0x0a,
	0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x06, 0x2a, 0xae, 0x03, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x4b, 0x59, 0x43,
	0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x43, 0x45,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x03, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x10,
	0x06, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45,
	0x44, 0x10, 0x08, 0x2a, 0x8b, 0x08, 0x0a, 0x0a, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1a, 0x0a, 0x16, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10,
	0x02, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x44, 0x52, 0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x5f, 0x53, 0x49, 0x47, 0x4e,
	0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x08, 0x12, 0x1c, 0x0a,
	0x18, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x09, 0x12, 0x1f, 0x0a, 0x1b, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f,
	0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x4d, 0x53, 0x10,
	0x0b, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f,
	0x42, 0x52, 0x45, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x0e, 0x12, 0x1e, 0x0a, 0x1a, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0f, 0x12, 0x1f, 0x0a, 0x1b, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f,
	0x4c, 0x49, 0x4f, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0x10, 0x12, 0x1e, 0x0a, 0x1a, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x11, 0x12, 0x19, 0x0a, 0x15, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f,
	0x4d, 0x41, 0x52, 0x4b, 0x10, 0x12, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10,
	0x13, 0x12, 0x25, 0x0a, 0x21, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x14, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x52, 0x4f, 0x55,
	0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x50,
	0x57, 0x41, 0x10, 0x15, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x16, 0x12, 0x23, 0x0a, 0x1f, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x17, 0x12,
	0x13, 0x0a, 0x0f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4b,
	0x46, 0x53, 0x10, 0x18, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x19, 0x12, 0x2d, 0x0a, 0x29, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1a, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x4b, 0x46, 0x53, 0x10, 0x1b, 0x12,
	0x2e, 0x0a, 0x2a, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4d,
	0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x1c, 0x12,
	0x1e, 0x0a, 0x1a, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x1d, 0x12,
	0x2b, 0x0a, 0x27, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x1e, 0x12, 0x1b, 0x0a, 0x17,
	0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x52,
	0x45, 0x53, 0x48, 0x5f, 0x4c, 0x4d, 0x53, 0x10, 0x1f, 0x12, 0x1f, 0x0a, 0x1b, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x4f,
	0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x20, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x4c, 0x4f, 0x45, 0x43, 0x53, 0x10, 0x21, 0x12, 0x27, 0x0a, 0x23, 0x47, 0x52, 0x4f, 0x55,
	0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10,
	0x22, 0x2a, 0xe4, 0x04, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x41, 0x4d, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59,
	0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x41, 0x4d, 0x5f, 0x46, 0x4c, 0x44, 0x47, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54,
	0x45, 0x5f, 0x50, 0x4c, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x41, 0x43, 0x51, 0x5f, 0x54, 0x4f, 0x5f, 0x4c, 0x45,
	0x4e, 0x44, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x4c, 0x41, 0x4d, 0x46, 0x10, 0x07, 0x12, 0x1e, 0x0a, 0x1a, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x45, 0x54, 0x42, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x53, 0x54, 0x50, 0x4c, 0x10, 0x0a, 0x12, 0x24, 0x0a, 0x20, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c,
	0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x54, 0x50, 0x4c, 0x10,
	0x0c, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x0d, 0x12,
	0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f,
	0x4e, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x50, 0x4c,
	0x10, 0x0e, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x41, 0x4d, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x53,
	0x55, 0x42, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x2e, 0x0a, 0x26, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x56, 0x31, 0x10, 0x10, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x54, 0x42, 0x10, 0x11, 0x2a, 0xca, 0x01, 0x0a, 0x13, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x21,
	0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x04, 0x2a, 0xa1, 0x01, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x4e, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x10, 0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x02, 0x2a, 0xa2, 0x01, 0x0a, 0x15, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41,
	0x4c, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x54, 0x49,
	0x41, 0x4c, 0x10, 0x01, 0x12, 0x38, 0x0a, 0x34, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x10, 0x02, 0x2a, 0xda,
	0x01, 0x0a, 0x1e, 0x43, 0x6b, 0x79, 0x63, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49,
	0x56, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x59, 0x45, 0x53, 0x10, 0x01, 0x12,
	0x28, 0x0a, 0x24, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45,
	0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4b, 0x59,
	0x43, 0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49,
	0x4e, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x2a, 0x8b, 0x01, 0x0a, 0x13,
	0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x2a, 0x44, 0x0a, 0x09, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x01, 0x2a,
	0x83, 0x04, 0x0a, 0x15, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x45, 0x54,
	0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x49, 0x44, 0x10,
	0x01, 0x12, 0x32, 0x0a, 0x2e, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46,
	0x49, 0x45, 0x52, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44,
	0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x03, 0x12, 0x1f,
	0x0a, 0x1b, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12,
	0x25, 0x0a, 0x21, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f,
	0x52, 0x5f, 0x49, 0x44, 0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45,
	0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x46,
	0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x45,
	0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x09, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x27, 0x0a, 0x23,
	0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x0b, 0x2a, 0xd8, 0x01, 0x0a, 0x1a, 0x49, 0x64, 0x66, 0x63, 0x43, 0x6b,
	0x79, 0x63, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x2b, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x43, 0x4b, 0x59,
	0x43, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x43, 0x4b,
	0x59, 0x43, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x52, 0x42, 0x41, 0x4e, 0x10, 0x01,
	0x12, 0x2e, 0x0a, 0x2a, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x52, 0x55, 0x52, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x02,
	0x12, 0x2e, 0x0a, 0x2a, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x52, 0x55, 0x52, 0x41, 0x4c, 0x5f, 0x49, 0x46, 0x42, 0x4c, 0x10, 0x03,
	0x2a, 0xc6, 0x02, 0x0a, 0x07, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14,
	0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x41, 0x4d, 0x53, 0x5f, 0x50, 0x46, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48,
	0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b,
	0x41, 0x52, 0x56, 0x59, 0x5f, 0x50, 0x46, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0x02, 0x12,
	0x1b, 0x0a, 0x17, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x4d, 0x53,
	0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18,
	0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x41, 0x52, 0x56, 0x59, 0x5f, 0x4c,
	0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x54,
	0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41,
	0x4c, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x50, 0x46, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48,
	0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x46, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41, 0x4c, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x50, 0x46, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0x06, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x54,
	0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41,
	0x4c, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x10,
	0x07, 0x12, 0x36, 0x0a, 0x32, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x46,
	0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41, 0x4c, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x2a, 0xa2, 0x01, 0x0a, 0x09, 0x4f, 0x74,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x54, 0x50, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14,
	0x4f, 0x54, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x85,
	0x01, 0x0a, 0x15, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x46, 0x61, 0x63,
	0x69, 0x6c, 0x69, 0x74, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x23, 0x4d, 0x55, 0x54, 0x55,
	0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x41, 0x43, 0x49, 0x4c, 0x49, 0x54, 0x41,
	0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x46, 0x41, 0x43, 0x49, 0x4c, 0x49, 0x54, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4d,
	0x53, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x46, 0x41, 0x43, 0x49, 0x4c, 0x49, 0x54, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4b,
	0x41, 0x52, 0x56, 0x59, 0x10, 0x02, 0x2a, 0xe9, 0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x73,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a,
	0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c,
	0x4f, 0x53, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x03, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x04, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x10, 0x05, 0x2a, 0xd3, 0x01, 0x0a, 0x0b, 0x49, 0x74, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x54, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x54, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x54, 0x52, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x49, 0x54, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x54, 0x52, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x55, 0x52, 0x10, 0x04,
	0x12, 0x16, 0x0a, 0x12, 0x49, 0x54, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x54, 0x52, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x49, 0x58, 0x10, 0x06, 0x12,
	0x17, 0x0a, 0x13, 0x49, 0x54, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x45, 0x56, 0x45, 0x4e, 0x10, 0x07, 0x2a, 0x5c, 0x0a, 0x11, 0x49, 0x74, 0x72, 0x54,
	0x61, 0x78, 0x70, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x1f, 0x49, 0x54, 0x52, 0x5f, 0x54, 0x41, 0x58, 0x50, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x54, 0x52, 0x5f, 0x54, 0x41, 0x58, 0x50, 0x41, 0x59,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x56, 0x49,
	0x44, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x2a, 0xf9, 0x01, 0x0a, 0x1c, 0x49, 0x74, 0x72, 0x54, 0x61,
	0x78, 0x70, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x2b, 0x49, 0x54, 0x52, 0x5f, 0x54,
	0x41, 0x58, 0x50, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x49, 0x54, 0x52, 0x5f,
	0x54, 0x41, 0x58, 0x50, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x48, 0x0a, 0x44, 0x49, 0x54, 0x52, 0x5f, 0x54, 0x41,
	0x58, 0x50, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x5f, 0x42, 0x55, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x49, 0x4e,
	0x41, 0x52, 0x49, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x10, 0x02,
	0x12, 0x30, 0x0a, 0x2c, 0x49, 0x54, 0x52, 0x5f, 0x54, 0x41, 0x58, 0x50, 0x41, 0x59, 0x45, 0x52,
	0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x2a, 0xec, 0x03, 0x0a, 0x17, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2a,
	0x0a, 0x26, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x41,
	0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a,
	0x23, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f,
	0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x29,
	0x0a, 0x25, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x41, 0x4e,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x07, 0x12, 0x25, 0x0a, 0x21, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x08, 0x12, 0x2b, 0x0a, 0x27, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x41, 0x4d, 0x10, 0x09, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10,
	0x0a, 0x2a, 0xe2, 0x02, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x47, 0x0a, 0x43, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4d, 0x46, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x46, 0x0a, 0x42, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c,
	0x49, 0x4e, 0x4b, 0x5f, 0x4d, 0x46, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x46,
	0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02,
	0x12, 0x3f, 0x0a, 0x3b, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x52, 0x5f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4d, 0x46, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x4d, 0x46, 0x10,
	0x03, 0x12, 0x43, 0x0a, 0x3f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49,
	0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4d, 0x46, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x10, 0x04, 0x2a, 0x7e, 0x0a, 0x17, 0x46, 0x69, 0x66, 0x74, 0x79, 0x46,
	0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x49, 0x46, 0x54, 0x59, 0x46, 0x49, 0x4e, 0x5f, 0x4c, 0x41,
	0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x37, 0x0a,
	0x33, 0x46, 0x49, 0x46, 0x54, 0x59, 0x46, 0x49, 0x4e, 0x5f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x5f,
	0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x2a, 0xa0, 0x02, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x26, 0x0a, 0x22, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x41, 0x4e, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x4d,
	0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x22,
	0x0a, 0x1e, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x2b, 0x0a, 0x27,
	0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x5f,
	0x42, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x06, 0x2a, 0x9f, 0x03, 0x0a, 0x15, 0x4d, 0x66,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x24, 0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a,
	0x2c, 0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x37, 0x0a, 0x33, 0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52,
	0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x41, 0x56,
	0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x53, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x4d, 0x46, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x44, 0x45, 0x4d, 0x41, 0x54, 0x10, 0x03, 0x12,
	0x36, 0x0a, 0x32, 0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52,
	0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x49, 0x53, 0x49, 0x4e,
	0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x04, 0x12, 0x54, 0x0a, 0x50, 0x4d, 0x46, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x5f, 0x49, 0x53, 0x49, 0x4e, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x49, 0x4e,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x49, 0x10, 0x05, 0x12, 0x3c, 0x0a,
	0x38, 0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x52, 0x45, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x06, 0x2a, 0xe4, 0x03, 0x0a, 0x13,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x41, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x49, 0x42, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x3d, 0x0a, 0x39, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x42, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x4f,
	0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x30, 0x0a, 0x2c, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42,
	0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x29, 0x0a, 0x25,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x45, 0x5f, 0x49,
	0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x07, 0x12, 0x2d, 0x0a, 0x29, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x32, 0x0a, 0x2e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x0a, 0x2a, 0x65, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4b, 0x46, 0x53, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x47,
	0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0xba, 0x01, 0x0a, 0x16, 0x4c, 0x6f,
	0x61, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x10, 0x02,
	0x12, 0x24, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x4e, 0x10, 0x03, 0x2a, 0x87, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45,
	0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x4f, 0x46, 0x54, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x10, 0x03,
	0x2a, 0xa2, 0x02, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x50, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44,
	0x10, 0x01, 0x12, 0x36, 0x0a, 0x32, 0x50, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x53, 0x49, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x35, 0x0a, 0x31, 0x50, 0x52,
	0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x49, 0x4c, 0x4c, 0x10,
	0x03, 0x12, 0x32, 0x0a, 0x2e, 0x50, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0x04, 0x2a, 0x60, 0x0a, 0x11, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45,
	0x52, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x25, 0x0a, 0x21, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x53, 0x4b, 0x49, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x52, 0x5f,
	0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0x01, 0x2a, 0xec, 0x01, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x56,
	0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x59, 0x10, 0x06, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_enums_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_enums_proto_rawDescData = file_api_preapprovedloan_enums_proto_rawDesc
)

func file_api_preapprovedloan_enums_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_enums_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_enums_proto_rawDescData)
	})
	return file_api_preapprovedloan_enums_proto_rawDescData
}

var file_api_preapprovedloan_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 59)
var file_api_preapprovedloan_enums_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_preapprovedloan_enums_proto_goTypes = []interface{}{
	(Vendor)(0),                                // 0: preapprovedloan.Vendor
	(LoanRequestType)(0),                       // 1: preapprovedloan.LoanRequestType
	(LoanRequestStatus)(0),                     // 2: preapprovedloan.LoanRequestStatus
	(LoanRequestSubStatus)(0),                  // 3: preapprovedloan.LoanRequestSubStatus
	(LoanAccountStatus)(0),                     // 4: preapprovedloan.LoanAccountStatus
	(LoanType)(0),                              // 5: preapprovedloan.LoanType
	(LoanRequestFieldMask)(0),                  // 6: preapprovedloan.LoanRequestFieldMask
	(LoanAccountFieldMask)(0),                  // 7: preapprovedloan.LoanAccountFieldMask
	(LoanOfferFieldMask)(0),                    // 8: preapprovedloan.LoanOfferFieldMask
	(LoanApplicantFieldMask)(0),                // 9: preapprovedloan.LoanApplicantFieldMask
	(LoanStepExecutionStatus)(0),               // 10: preapprovedloan.LoanStepExecutionStatus
	(LoanStepExecutionSubStatus)(0),            // 11: preapprovedloan.LoanStepExecutionSubStatus
	(LoanStepExecutionFlow)(0),                 // 12: preapprovedloan.LoanStepExecutionFlow
	(LoanStepExecutionStepName)(0),             // 13: preapprovedloan.LoanStepExecutionStepName
	(LoanStepExecutionFieldMask)(0),            // 14: preapprovedloan.LoanStepExecutionFieldMask
	(LoanOfferEligibilityCriteriaFieldMask)(0), // 15: preapprovedloan.LoanOfferEligibilityCriteriaFieldMask
	(LoanOfferEligibilityCriteriaStatus)(0),    // 16: preapprovedloan.LoanOfferEligibilityCriteriaStatus
	(LoanOfferEligibilityCriteriaSubStatus)(0), // 17: preapprovedloan.LoanOfferEligibilityCriteriaSubStatus
	(LoanInstallmentInfoStatus)(0),             // 18: preapprovedloan.LoanInstallmentInfoStatus
	(LoanInstallmentInfoFieldMask)(0),          // 19: preapprovedloan.LoanInstallmentInfoFieldMask
	(LoanActivityType)(0),                      // 20: preapprovedloan.LoanActivityType
	(LoanActivityFieldMask)(0),                 // 21: preapprovedloan.LoanActivityFieldMask
	(LoanPaymentRequestType)(0),                // 22: preapprovedloan.LoanPaymentRequestType
	(LoanPaymentRequestStatus)(0),              // 23: preapprovedloan.LoanPaymentRequestStatus
	(LoanPaymentRequestSubStatus)(0),           // 24: preapprovedloan.LoanPaymentRequestSubStatus
	(LoanPaymentRequestFieldMask)(0),           // 25: preapprovedloan.LoanPaymentRequestFieldMask
	(LoanInstallmentPayoutStatus)(0),           // 26: preapprovedloan.LoanInstallmentPayoutStatus
	(LoanInstallmentPayoutFieldMask)(0),        // 27: preapprovedloan.LoanInstallmentPayoutFieldMask
	(LoanApplicationStatus)(0),                 // 28: preapprovedloan.LoanApplicationStatus
	(LoanApplicationSubStatus)(0),              // 29: preapprovedloan.LoanApplicationSubStatus
	(GroupStage)(0),                            // 30: preapprovedloan.GroupStage
	(LoanProgram)(0),                           // 31: preapprovedloan.LoanProgram
	(LoanApplicantStatus)(0),                   // 32: preapprovedloan.LoanApplicantStatus
	(LoanApplicantSubStatus)(0),                // 33: preapprovedloan.LoanApplicantSubStatus
	(PaymentAllocationType)(0),                 // 34: preapprovedloan.PaymentAllocationType
	(CkycPositiveConfirmationStatus)(0),        // 35: preapprovedloan.CkycPositiveConfirmationStatus
	(LoanUserDetailsType)(0),                   // 36: preapprovedloan.LoanUserDetailsType
	(AssetType)(0),                             // 37: preapprovedloan.AssetType
	(FetchedAssetFieldMask)(0),                 // 38: preapprovedloan.FetchedAssetFieldMask
	(IdfcCkycAddressPinCodeType)(0),            // 39: preapprovedloan.IdfcCkycAddressPinCodeType
	(OtpType)(0),                               // 40: preapprovedloan.OtpType
	(OtpStatus)(0),                             // 41: preapprovedloan.OtpStatus
	(MutualFundFacilitator)(0),                 // 42: preapprovedloan.MutualFundFacilitator
	(LoansUserStatus)(0),                       // 43: preapprovedloan.LoansUserStatus
	(ItrFormType)(0),                           // 44: preapprovedloan.ItrFormType
	(ItrTaxpayerStatus)(0),                     // 45: preapprovedloan.ItrTaxpayerStatus
	(ItrTaxpayerResidentialStatus)(0),          // 46: preapprovedloan.ItrTaxpayerResidentialStatus
	(MandateRequestFieldMask)(0),               // 47: preapprovedloan.MandateRequestFieldMask
	(RecordUserActionIdentifier)(0),            // 48: preapprovedloan.RecordUserActionIdentifier
	(FiftyFinLamfOfferSource)(0),               // 49: preapprovedloan.FiftyFinLamfOfferSource
	(MandateRequestStatus)(0),                  // 50: preapprovedloan.MandateRequestStatus
	(MfOfferApprovalStatus)(0),                 // 51: preapprovedloan.MfOfferApprovalStatus
	(DataRequirementType)(0),                   // 52: preapprovedloan.DataRequirementType
	(LoanDocType)(0),                           // 53: preapprovedloan.LoanDocType
	(LoanPaymentAccountType)(0),                // 54: preapprovedloan.LoanPaymentAccountType
	(LoanOfferType)(0),                         // 55: preapprovedloan.LoanOfferType
	(PreEligibilityOfferFieldMask)(0),          // 56: preapprovedloan.PreEligibilityOfferFieldMask
	(LandingInfoFilter)(0),                     // 57: preapprovedloan.LandingInfoFilter
	(UserStatus)(0),                            // 58: preapprovedloan.UserStatus
	(*LoanHeader)(nil),                         // 59: preapprovedloan.LoanHeader
}
var file_api_preapprovedloan_enums_proto_depIdxs = []int32{
	31, // 0: preapprovedloan.LoanHeader.loan_program:type_name -> preapprovedloan.LoanProgram
	0,  // 1: preapprovedloan.LoanHeader.vendor:type_name -> preapprovedloan.Vendor
	5,  // 2: preapprovedloan.LoanHeader.loan_type:type_name -> preapprovedloan.LoanType
	0,  // 3: preapprovedloan.LoanHeader.data_owner:type_name -> preapprovedloan.Vendor
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_enums_proto_init() }
func file_api_preapprovedloan_enums_proto_init() {
	if File_api_preapprovedloan_enums_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_enums_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_enums_proto_rawDesc,
			NumEnums:      59,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_enums_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_enums_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_enums_proto_enumTypes,
		MessageInfos:      file_api_preapprovedloan_enums_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_enums_proto = out.File
	file_api_preapprovedloan_enums_proto_rawDesc = nil
	file_api_preapprovedloan_enums_proto_goTypes = nil
	file_api_preapprovedloan_enums_proto_depIdxs = nil
}
