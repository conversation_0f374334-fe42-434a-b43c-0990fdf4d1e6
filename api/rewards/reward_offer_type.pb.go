// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/reward_offer_type.proto

package rewards

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Not added in api/rewards/rewardOffers as it was creating a dependency cycle
// between api/rewards/rewardoffers and api/rewards pkg in the auto generated GO code.
// RewardOfferType denotes type of reward offer.
type RewardOfferType int32

const (
	RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE RewardOfferType = 0
	// denotes a referral reward offer for referrer
	RewardOfferType_REFERRAL_REFERRER_OFFER RewardOfferType = 1
	// denotes a referral reward offer for referee
	RewardOfferType_REFERRAL_REFEREE_OFFER RewardOfferType = 2
	// denotes a reward offer for extra interest SD bonus payout
	RewardOfferType_EXTRA_INTEREST_SD_BONUS_PAYOUT_OFFER RewardOfferType = 3
	// denotes a reward offer for salary program
	RewardOfferType_SALARY_PROGRAM_OFFER RewardOfferType = 4
	// denotes a referral reward offer for referrer
	// used in cases where we need to give additional special reward to referrer for referring a referee.
	RewardOfferType_REFERRAL_REFERRER_SPECIAL_OFFER RewardOfferType = 5
	// denotes a referral reward offer for referee
	// used in cases where we need to give additional special reward to referee
	// it allows one referral reward per offer for a user unlike REFERRAL_REFEREE_OFFER which allows one reward globally for a user.
	RewardOfferType_REFERRAL_REFEREE_SPECIAL_OFFER RewardOfferType = 30
	// denotes a reward offer for giving 1x rewards on merchant spends using credit card.
	RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER RewardOfferType = 6
	// denotes a reward offer for giving rewards on top merchant spends (in a billing/monthly cycle) using credit card.
	RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER RewardOfferType = 7
	// denotes a salary program referral reward offer for referrer as salary program is activated for referee
	RewardOfferType_SALARY_PROGRAM_INAPP_REFERRAL_REFERRER_OFFER RewardOfferType = 8
	// denotes a salary program referral reward offer for referee as salary program is activated for referee
	RewardOfferType_SALARY_PROGRAM_INAPP_REFERRAL_REFEREE_OFFER RewardOfferType = 9
	// denotes a welcome reward offer for credit card users
	RewardOfferType_CREDIT_CARD_WELCOME_OFFER RewardOfferType = 10
	// denotes a reward offer for giving an additional (on top of base 1x reward) reward for credit card spends on a curated set of merchants,
	// current construct is to give additional 1x reward on credit card spends done on a curated set of merchants in a billing cycle.
	RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER RewardOfferType = 11
	// denotes a reward offer for giving quarterly lounge access to credit card users on some qualifying action.
	RewardOfferType_CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER RewardOfferType = 12
	// denotes an offer for giving rewards to users on pulling credit report through our app
	RewardOfferType_CREDIT_REPORT_DOWNLOAD_OFFER RewardOfferType = 13
	// denotes an offer for generating rewards on weekdays for secured CC
	RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER RewardOfferType = 14
	// denotes an offer for generating rewards on weekdays for secured weekends
	RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER RewardOfferType = 15
	// denotes a reward given to retain user's investment
	RewardOfferType_INVESTMENT_RETENTION_REWARD RewardOfferType = 16
	// denotes an offer for generating 1X rewards on AMPLIFI credit card on all spends other than done to a list of merchants
	RewardOfferType_UNSECURED_CREDIT_CARD_BASE_OFFER RewardOfferType = 17
	// denotes an offer for generating rewards on AMPLIFI credit card on all spends done to a list of curated merchants
	RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER RewardOfferType = 18
	// denotes an offer for generating rewards on AMPLIFI credit card on all spends done to a list of curated merchants
	RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER RewardOfferType = 19
	// denotes mass unsecured credit card base reward offer
	RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER RewardOfferType = 20
	// denotes mass unsecured credit card accelerated reward offer
	RewardOfferType_MASS_UNSECURED_CREDIT_CARD_ACCELERATED_OFFER RewardOfferType = 21
	// reward which is being trigger during activation of usstocks
	// this is one time offer
	RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER RewardOfferType = 22
	// denotes an offer for generating first milestone reward for customers owning amplifi credit card for 1 year
	RewardOfferType_UNSECURED_CREDIT_CARD_MILESTONE_1_OFFER RewardOfferType = 23
	// denotes an offer for generating second milestone reward for customers owning amplifi credit card for 1 year
	RewardOfferType_UNSECURED_CREDIT_CARD_MILESTONE_2_OFFER RewardOfferType = 24
	// denotes an offer for generating reward for renewing amplifi credit card
	RewardOfferType_UNSECURED_CREDIT_CARD_RENEWAL_OFFER RewardOfferType = 25
	// rewards earned for being in the plus tier
	RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER RewardOfferType = 35
	// rewards earned for being in the infinite tier
	RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER RewardOfferType = 36
	// rewards earned for being in the aa salary tier band 1
	RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER RewardOfferType = 27
	// rewards earned for being in the aa salary tier band 2
	RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER RewardOfferType = 28
	// rewards earned for being in the aa salary tier band 3
	RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER RewardOfferType = 29
	// rewards earned for being in the salary lite tier
	RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER RewardOfferType = 26
	// rewards earned for being in the salary tier
	RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER RewardOfferType = 37
	// rewards earned for being in the infinite/salary tier
	RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER RewardOfferType = 34
	// rewards earned for being in the salary basic tier
	RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER RewardOfferType = 38
	// rewards earned for connecting epf account on fi
	RewardOfferType_EPF_PASSBOOK_IMPORT_OFFER RewardOfferType = 31
	// reward earned for connecting syncing account on fi
	RewardOfferType_CONNECTED_ACCOUNT_DATA_SYNC_OFFER RewardOfferType = 32
	// reward earned for connecting account on fi
	RewardOfferType_CONNECTED_ACCOUNT_DATA_CONNECT_OFFER RewardOfferType = 33
	// rewards earned for magnifi cvp
	RewardOfferType_MASS_UNSECURED_CREDIT_CARD_CVP_OFFER RewardOfferType = 39
)

// Enum value maps for RewardOfferType.
var (
	RewardOfferType_name = map[int32]string{
		0:  "UNSPECIFIED_REWARD_OFFER_TYPE",
		1:  "REFERRAL_REFERRER_OFFER",
		2:  "REFERRAL_REFEREE_OFFER",
		3:  "EXTRA_INTEREST_SD_BONUS_PAYOUT_OFFER",
		4:  "SALARY_PROGRAM_OFFER",
		5:  "REFERRAL_REFERRER_SPECIAL_OFFER",
		30: "REFERRAL_REFEREE_SPECIAL_OFFER",
		6:  "CREDIT_CARD_SPENDS_1X_OFFER",
		7:  "CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER",
		8:  "SALARY_PROGRAM_INAPP_REFERRAL_REFERRER_OFFER",
		9:  "SALARY_PROGRAM_INAPP_REFERRAL_REFEREE_OFFER",
		10: "CREDIT_CARD_WELCOME_OFFER",
		11: "CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER",
		12: "CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER",
		13: "CREDIT_REPORT_DOWNLOAD_OFFER",
		14: "SECURED_CREDIT_CARD_WEEKDAYS_OFFER",
		15: "SECURED_CREDIT_CARD_WEEKEND_OFFER",
		16: "INVESTMENT_RETENTION_REWARD",
		17: "UNSECURED_CREDIT_CARD_BASE_OFFER",
		18: "UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER",
		19: "UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER",
		20: "MASS_UNSECURED_CREDIT_CARD_BASE_OFFER",
		21: "MASS_UNSECURED_CREDIT_CARD_ACCELERATED_OFFER",
		22: "USSTOCKS_ACTIVATION_REWARD_OFFER",
		23: "UNSECURED_CREDIT_CARD_MILESTONE_1_OFFER",
		24: "UNSECURED_CREDIT_CARD_MILESTONE_2_OFFER",
		25: "UNSECURED_CREDIT_CARD_RENEWAL_OFFER",
		35: "PLUS_TIER_1_PERCENT_CASHBACK_OFFER",
		36: "INFINITE_TIER_2_PERCENT_CASHBACK_OFFER",
		27: "AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER",
		28: "AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER",
		29: "AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER",
		26: "SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER",
		37: "SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
		34: "INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
		38: "SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER",
		31: "EPF_PASSBOOK_IMPORT_OFFER",
		32: "CONNECTED_ACCOUNT_DATA_SYNC_OFFER",
		33: "CONNECTED_ACCOUNT_DATA_CONNECT_OFFER",
		39: "MASS_UNSECURED_CREDIT_CARD_CVP_OFFER",
	}
	RewardOfferType_value = map[string]int32{
		"UNSPECIFIED_REWARD_OFFER_TYPE":                             0,
		"REFERRAL_REFERRER_OFFER":                                   1,
		"REFERRAL_REFEREE_OFFER":                                    2,
		"EXTRA_INTEREST_SD_BONUS_PAYOUT_OFFER":                      3,
		"SALARY_PROGRAM_OFFER":                                      4,
		"REFERRAL_REFERRER_SPECIAL_OFFER":                           5,
		"REFERRAL_REFEREE_SPECIAL_OFFER":                            30,
		"CREDIT_CARD_SPENDS_1X_OFFER":                               6,
		"CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER":                    7,
		"SALARY_PROGRAM_INAPP_REFERRAL_REFERRER_OFFER":              8,
		"SALARY_PROGRAM_INAPP_REFERRAL_REFEREE_OFFER":               9,
		"CREDIT_CARD_WELCOME_OFFER":                                 10,
		"CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER":                11,
		"CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER":                12,
		"CREDIT_REPORT_DOWNLOAD_OFFER":                              13,
		"SECURED_CREDIT_CARD_WEEKDAYS_OFFER":                        14,
		"SECURED_CREDIT_CARD_WEEKEND_OFFER":                         15,
		"INVESTMENT_RETENTION_REWARD":                               16,
		"UNSECURED_CREDIT_CARD_BASE_OFFER":                          17,
		"UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER":        18,
		"UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER": 19,
		"MASS_UNSECURED_CREDIT_CARD_BASE_OFFER":                     20,
		"MASS_UNSECURED_CREDIT_CARD_ACCELERATED_OFFER":              21,
		"USSTOCKS_ACTIVATION_REWARD_OFFER":                          22,
		"UNSECURED_CREDIT_CARD_MILESTONE_1_OFFER":                   23,
		"UNSECURED_CREDIT_CARD_MILESTONE_2_OFFER":                   24,
		"UNSECURED_CREDIT_CARD_RENEWAL_OFFER":                       25,
		"PLUS_TIER_1_PERCENT_CASHBACK_OFFER":                        35,
		"INFINITE_TIER_2_PERCENT_CASHBACK_OFFER":                    36,
		"AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER":            27,
		"AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER":            28,
		"AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER":            29,
		"SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER":                 26,
		"SALARY_TIER_2_PERCENT_CASHBACK_OFFER":                      37,
		"INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER":          34,
		"SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER":                38,
		"EPF_PASSBOOK_IMPORT_OFFER":                                 31,
		"CONNECTED_ACCOUNT_DATA_SYNC_OFFER":                         32,
		"CONNECTED_ACCOUNT_DATA_CONNECT_OFFER":                      33,
		"MASS_UNSECURED_CREDIT_CARD_CVP_OFFER":                      39,
	}
)

func (x RewardOfferType) Enum() *RewardOfferType {
	p := new(RewardOfferType)
	*p = x
	return p
}

func (x RewardOfferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardOfferType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_offer_type_proto_enumTypes[0].Descriptor()
}

func (RewardOfferType) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_offer_type_proto_enumTypes[0]
}

func (x RewardOfferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardOfferType.Descriptor instead.
func (RewardOfferType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_offer_type_proto_rawDescGZIP(), []int{0}
}

var File_api_rewards_reward_offer_type_proto protoreflect.FileDescriptor

var file_api_rewards_reward_offer_type_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2a, 0xa7,
	0x0d, 0x0a, 0x0f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41,
	0x4c, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x52,
	0x45, 0x46, 0x45, 0x52, 0x45, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x02, 0x12, 0x28,
	0x0a, 0x24, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x44, 0x5f, 0x42, 0x4f, 0x4e, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x52,
	0x45, 0x46, 0x45, 0x52, 0x52, 0x45, 0x52, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x52, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x1e, 0x12, 0x1f, 0x0a, 0x1b, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44,
	0x53, 0x5f, 0x31, 0x58, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f, 0x50, 0x5f,
	0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x53,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x07, 0x12, 0x30, 0x0a, 0x2c, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50,
	0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x08, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x49, 0x4e, 0x41,
	0x50, 0x50, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x46, 0x45,
	0x52, 0x45, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x09, 0x12, 0x1d, 0x0a, 0x19, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x57, 0x45, 0x4c, 0x43, 0x4f,
	0x4d, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x55, 0x52, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x53, 0x50, 0x45, 0x4e,
	0x44, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x54, 0x45,
	0x52, 0x4c, 0x59, 0x5f, 0x41, 0x49, 0x52, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4c, 0x4f, 0x55, 0x4e,
	0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x0c, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e,
	0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x0d, 0x12, 0x26, 0x0a, 0x22,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x44, 0x41, 0x59, 0x53, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x10, 0x0e, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x57, 0x45, 0x45, 0x4b,
	0x45, 0x4e, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x0f, 0x12, 0x1f, 0x0a, 0x1b, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x54, 0x45, 0x4e, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x10, 0x12, 0x24, 0x0a, 0x20,
	0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x10, 0x11, 0x12, 0x36, 0x0a, 0x32, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x55, 0x52, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x42, 0x41,
	0x53, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x12, 0x12, 0x3d, 0x0a, 0x39, 0x55, 0x4e,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x43, 0x55, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x52, 0x43,
	0x48, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x13, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x41, 0x53,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x10, 0x14, 0x12, 0x30, 0x0a, 0x2c, 0x4d, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x10, 0x15, 0x12, 0x24, 0x0a, 0x20, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x16, 0x12, 0x2b, 0x0a, 0x27,
	0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x5f,
	0x31, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x17, 0x12, 0x2b, 0x0a, 0x27, 0x55, 0x4e, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x5f, 0x32, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x10, 0x18, 0x12, 0x27, 0x0a, 0x23, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x19, 0x12,
	0x26, 0x0a, 0x22, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x31, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x23, 0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x46, 0x49, 0x4e,
	0x49, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x10, 0x24, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59,
	0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x31, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x31, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x1b, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x41, 0x5f, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x32, 0x5f, 0x54, 0x49, 0x45, 0x52,
	0x5f, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x1c, 0x12, 0x32, 0x0a, 0x2e, 0x41,
	0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x33, 0x5f,
	0x54, 0x49, 0x45, 0x52, 0x5f, 0x33, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x1d, 0x12,
	0x2d, 0x0a, 0x29, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x54,
	0x49, 0x45, 0x52, 0x5f, 0x31, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41,
	0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x1a, 0x12, 0x28,
	0x0a, 0x24, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x25, 0x12, 0x34, 0x0a, 0x30, 0x49, 0x4e, 0x46, 0x49,
	0x4e, 0x49, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x54,
	0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41,
	0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x22, 0x12, 0x2e,
	0x0a, 0x2a, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x54,
	0x49, 0x45, 0x52, 0x5f, 0x31, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41,
	0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x26, 0x12, 0x1d,
	0x0a, 0x19, 0x45, 0x50, 0x46, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x49,
	0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x1f, 0x12, 0x25, 0x0a,
	0x21, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x10, 0x20, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x21, 0x12, 0x28,
	0x0a, 0x24, 0x4d, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x56, 0x50,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x27, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5a, 0x22,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_reward_offer_type_proto_rawDescOnce sync.Once
	file_api_rewards_reward_offer_type_proto_rawDescData = file_api_rewards_reward_offer_type_proto_rawDesc
)

func file_api_rewards_reward_offer_type_proto_rawDescGZIP() []byte {
	file_api_rewards_reward_offer_type_proto_rawDescOnce.Do(func() {
		file_api_rewards_reward_offer_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_reward_offer_type_proto_rawDescData)
	})
	return file_api_rewards_reward_offer_type_proto_rawDescData
}

var file_api_rewards_reward_offer_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_rewards_reward_offer_type_proto_goTypes = []interface{}{
	(RewardOfferType)(0), // 0: rewards.RewardOfferType
}
var file_api_rewards_reward_offer_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_rewards_reward_offer_type_proto_init() }
func file_api_rewards_reward_offer_type_proto_init() {
	if File_api_rewards_reward_offer_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_reward_offer_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_rewards_reward_offer_type_proto_goTypes,
		DependencyIndexes: file_api_rewards_reward_offer_type_proto_depIdxs,
		EnumInfos:         file_api_rewards_reward_offer_type_proto_enumTypes,
	}.Build()
	File_api_rewards_reward_offer_type_proto = out.File
	file_api_rewards_reward_offer_type_proto_rawDesc = nil
	file_api_rewards_reward_offer_type_proto_goTypes = nil
	file_api_rewards_reward_offer_type_proto_depIdxs = nil
}
