// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/enums.proto

package webfe

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateOTPFlow int32

const (
	GenerateOTPFlow_GENERATE_OTP_FLOW_UNSPECIFIED                  GenerateOTPFlow = 0
	GenerateOTPFlow_GENERATE_OTP_FLOW_B2B_ONBOARDING               GenerateOTPFlow = 1
	GenerateOTPFlow_GENERATE_OTP_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK GenerateOTPFlow = 2
)

// Enum value maps for GenerateOTPFlow.
var (
	GenerateOTPFlow_name = map[int32]string{
		0: "GENERATE_OTP_FLOW_UNSPECIFIED",
		1: "GENERATE_OTP_FLOW_B2B_ONBOARDING",
		2: "GENERATE_OTP_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK",
	}
	GenerateOTPFlow_value = map[string]int32{
		"GENERATE_OTP_FLOW_UNSPECIFIED":                  0,
		"GENERATE_OTP_FLOW_B2B_ONBOARDING":               1,
		"GENERATE_OTP_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK": 2,
	}
)

func (x GenerateOTPFlow) Enum() *GenerateOTPFlow {
	p := new(GenerateOTPFlow)
	*p = x
	return p
}

func (x GenerateOTPFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateOTPFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_enums_proto_enumTypes[0].Descriptor()
}

func (GenerateOTPFlow) Type() protoreflect.EnumType {
	return &file_api_webfe_enums_proto_enumTypes[0]
}

func (x GenerateOTPFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateOTPFlow.Descriptor instead.
func (GenerateOTPFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_enums_proto_rawDescGZIP(), []int{0}
}

type ConsentType int32

const (
	ConsentType_CONSENT_TYPE_UNSPECIFIED       ConsentType = 0
	ConsentType_CONSENT_TYPE_FI_TNC            ConsentType = 1
	ConsentType_CONSENT_TYPE_FED_TNC           ConsentType = 2
	ConsentType_CONSENT_TYPE_FI_PRIVACY_POLICY ConsentType = 3
	ConsentType_CONSENT_TYPE_FI_WEALTH_TNC     ConsentType = 4
	// This consent will tell users that their credit report data will be pulled from the vendor (eg. Experian)
	ConsentType_CONSENT_TYPE_CREDIT_REPORT_DATA_PULL ConsentType = 5
)

// Enum value maps for ConsentType.
var (
	ConsentType_name = map[int32]string{
		0: "CONSENT_TYPE_UNSPECIFIED",
		1: "CONSENT_TYPE_FI_TNC",
		2: "CONSENT_TYPE_FED_TNC",
		3: "CONSENT_TYPE_FI_PRIVACY_POLICY",
		4: "CONSENT_TYPE_FI_WEALTH_TNC",
		5: "CONSENT_TYPE_CREDIT_REPORT_DATA_PULL",
	}
	ConsentType_value = map[string]int32{
		"CONSENT_TYPE_UNSPECIFIED":             0,
		"CONSENT_TYPE_FI_TNC":                  1,
		"CONSENT_TYPE_FED_TNC":                 2,
		"CONSENT_TYPE_FI_PRIVACY_POLICY":       3,
		"CONSENT_TYPE_FI_WEALTH_TNC":           4,
		"CONSENT_TYPE_CREDIT_REPORT_DATA_PULL": 5,
	}
)

func (x ConsentType) Enum() *ConsentType {
	p := new(ConsentType)
	*p = x
	return p
}

func (x ConsentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_enums_proto_enumTypes[1].Descriptor()
}

func (ConsentType) Type() protoreflect.EnumType {
	return &file_api_webfe_enums_proto_enumTypes[1]
}

func (x ConsentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsentType.Descriptor instead.
func (ConsentType) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_enums_proto_rawDescGZIP(), []int{1}
}

type WebFlow int32

const (
	WebFlow_WEB_FLOW_UNSPECIFIED                  WebFlow = 0
	WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK WebFlow = 1
)

// Enum value maps for WebFlow.
var (
	WebFlow_name = map[int32]string{
		0: "WEB_FLOW_UNSPECIFIED",
		1: "WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK",
	}
	WebFlow_value = map[string]int32{
		"WEB_FLOW_UNSPECIFIED":                  0,
		"WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK": 1,
	}
)

func (x WebFlow) Enum() *WebFlow {
	p := new(WebFlow)
	*p = x
	return p
}

func (x WebFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WebFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_enums_proto_enumTypes[2].Descriptor()
}

func (WebFlow) Type() protoreflect.EnumType {
	return &file_api_webfe_enums_proto_enumTypes[2]
}

func (x WebFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WebFlow.Descriptor instead.
func (WebFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_enums_proto_rawDescGZIP(), []int{2}
}

type FlowName int32

const (
	FlowName_FLOW_NAME_UNSPECIFIED           FlowName = 0
	FlowName_FLOW_NAME_LOANS_ELIGIBILITY     FlowName = 1
	FlowName_FLOW_NAME_CREDIT_SCORE_ANALYSER FlowName = 2
)

// Enum value maps for FlowName.
var (
	FlowName_name = map[int32]string{
		0: "FLOW_NAME_UNSPECIFIED",
		1: "FLOW_NAME_LOANS_ELIGIBILITY",
		2: "FLOW_NAME_CREDIT_SCORE_ANALYSER",
	}
	FlowName_value = map[string]int32{
		"FLOW_NAME_UNSPECIFIED":           0,
		"FLOW_NAME_LOANS_ELIGIBILITY":     1,
		"FLOW_NAME_CREDIT_SCORE_ANALYSER": 2,
	}
)

func (x FlowName) Enum() *FlowName {
	p := new(FlowName)
	*p = x
	return p
}

func (x FlowName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_enums_proto_enumTypes[3].Descriptor()
}

func (FlowName) Type() protoreflect.EnumType {
	return &file_api_webfe_enums_proto_enumTypes[3]
}

func (x FlowName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowName.Descriptor instead.
func (FlowName) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_enums_proto_rawDescGZIP(), []int{3}
}

var File_api_webfe_enums_proto protoreflect.FileDescriptor

var file_api_webfe_enums_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2a, 0x8e,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x54, 0x50, 0x46, 0x6c,
	0x6f, 0x77, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4f,
	0x54, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54,
	0x45, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x42, 0x32, 0x42, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e, 0x47,
	0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x43, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x02, 0x2a,
	0xcc, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49,
	0x5f, 0x54, 0x4e, 0x43, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x45, 0x44, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x02,
	0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x49, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x43, 0x59, 0x5f, 0x50, 0x4f, 0x4c, 0x49,
	0x43, 0x59, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x54,
	0x4e, 0x43, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x10, 0x05, 0x2a, 0x4e,
	0x0a, 0x07, 0x57, 0x65, 0x62, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x45, 0x42,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x57, 0x45, 0x42, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x4f, 0x46, 0x46, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x43, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x01, 0x2a, 0x6b,
	0x0a, 0x08, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x02, 0x42, 0x44, 0x0a, 0x20, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x5a,
	0x20, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_enums_proto_rawDescOnce sync.Once
	file_api_webfe_enums_proto_rawDescData = file_api_webfe_enums_proto_rawDesc
)

func file_api_webfe_enums_proto_rawDescGZIP() []byte {
	file_api_webfe_enums_proto_rawDescOnce.Do(func() {
		file_api_webfe_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_enums_proto_rawDescData)
	})
	return file_api_webfe_enums_proto_rawDescData
}

var file_api_webfe_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_webfe_enums_proto_goTypes = []interface{}{
	(GenerateOTPFlow)(0), // 0: webfe.GenerateOTPFlow
	(ConsentType)(0),     // 1: webfe.ConsentType
	(WebFlow)(0),         // 2: webfe.WebFlow
	(FlowName)(0),        // 3: webfe.FlowName
}
var file_api_webfe_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_webfe_enums_proto_init() }
func file_api_webfe_enums_proto_init() {
	if File_api_webfe_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_webfe_enums_proto_goTypes,
		DependencyIndexes: file_api_webfe_enums_proto_depIdxs,
		EnumInfos:         file_api_webfe_enums_proto_enumTypes,
	}.Build()
	File_api_webfe_enums_proto = out.File
	file_api_webfe_enums_proto_rawDesc = nil
	file_api_webfe_enums_proto_goTypes = nil
	file_api_webfe_enums_proto_depIdxs = nil
}
