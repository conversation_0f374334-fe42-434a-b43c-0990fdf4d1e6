// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/service.proto

package webfe

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status of the flow that was re-triggered
type GetRequestStatusResponse_RequestStatus int32

const (
	GetRequestStatusResponse_STATUS_UNSPECIFIED GetRequestStatusResponse_RequestStatus = 0
	GetRequestStatusResponse_STATUS_IN_PROGRESS GetRequestStatusResponse_RequestStatus = 1
	GetRequestStatusResponse_STATUS_SUCCESSFUL  GetRequestStatusResponse_RequestStatus = 2
	GetRequestStatusResponse_STATUS_FAILED      GetRequestStatusResponse_RequestStatus = 3
)

// Enum value maps for GetRequestStatusResponse_RequestStatus.
var (
	GetRequestStatusResponse_RequestStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_IN_PROGRESS",
		2: "STATUS_SUCCESSFUL",
		3: "STATUS_FAILED",
	}
	GetRequestStatusResponse_RequestStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_IN_PROGRESS": 1,
		"STATUS_SUCCESSFUL":  2,
		"STATUS_FAILED":      3,
	}
)

func (x GetRequestStatusResponse_RequestStatus) Enum() *GetRequestStatusResponse_RequestStatus {
	p := new(GetRequestStatusResponse_RequestStatus)
	*p = x
	return p
}

func (x GetRequestStatusResponse_RequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRequestStatusResponse_RequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_service_proto_enumTypes[0].Descriptor()
}

func (GetRequestStatusResponse_RequestStatus) Type() protoreflect.EnumType {
	return &file_api_webfe_service_proto_enumTypes[0]
}

func (x GetRequestStatusResponse_RequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRequestStatusResponse_RequestStatus.Descriptor instead.
func (GetRequestStatusResponse_RequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{5, 0}
}

type GeneratePhoneOtpResponse_Status int32

const (
	// Success
	GeneratePhoneOtpResponse_OK GeneratePhoneOtpResponse_Status = 0
	// Input token is not available on the system
	// Generate new token
	GeneratePhoneOtpResponse_NOT_FOUND GeneratePhoneOtpResponse_Status = 5
	// Internal error
	GeneratePhoneOtpResponse_INTERNAL GeneratePhoneOtpResponse_Status = 13
	// Input token has been used already and is not active.
	// Cannot reuse it
	GeneratePhoneOtpResponse_TOKEN_INACTIVE GeneratePhoneOtpResponse_Status = 100
	// Input token is expired and cannot use it
	// Generate new token
	GeneratePhoneOtpResponse_TOKEN_EXPIRY GeneratePhoneOtpResponse_Status = 101
	// Too many resend attempts
	// Generate new token
	GeneratePhoneOtpResponse_RESEND_LIMIT GeneratePhoneOtpResponse_Status = 102
	// Resend request too soon
	// Wait for some time and retry
	GeneratePhoneOtpResponse_RESEND_REQ_TOO_SOON GeneratePhoneOtpResponse_Status = 103
)

// Enum value maps for GeneratePhoneOtpResponse_Status.
var (
	GeneratePhoneOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		100: "TOKEN_INACTIVE",
		101: "TOKEN_EXPIRY",
		102: "RESEND_LIMIT",
		103: "RESEND_REQ_TOO_SOON",
	}
	GeneratePhoneOtpResponse_Status_value = map[string]int32{
		"OK":                  0,
		"NOT_FOUND":           5,
		"INTERNAL":            13,
		"TOKEN_INACTIVE":      100,
		"TOKEN_EXPIRY":        101,
		"RESEND_LIMIT":        102,
		"RESEND_REQ_TOO_SOON": 103,
	}
)

func (x GeneratePhoneOtpResponse_Status) Enum() *GeneratePhoneOtpResponse_Status {
	p := new(GeneratePhoneOtpResponse_Status)
	*p = x
	return p
}

func (x GeneratePhoneOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GeneratePhoneOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_service_proto_enumTypes[1].Descriptor()
}

func (GeneratePhoneOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_webfe_service_proto_enumTypes[1]
}

func (x GeneratePhoneOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GeneratePhoneOtpResponse_Status.Descriptor instead.
func (GeneratePhoneOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{8, 0}
}

type VerifyPhoneOtpResponse_Status int32

const (
	// Success
	VerifyPhoneOtpResponse_OK VerifyPhoneOtpResponse_Status = 0
	// Client specified an invalid argument
	VerifyPhoneOtpResponse_INVALID_ARGUMENT VerifyPhoneOtpResponse_Status = 3
	// Input token is not available on the system
	// Generate new token
	VerifyPhoneOtpResponse_NOT_FOUND VerifyPhoneOtpResponse_Status = 5
	// Internal error
	VerifyPhoneOtpResponse_INTERNAL VerifyPhoneOtpResponse_Status = 13
	// Input token has been used already and is not active.
	// Cannot reuse it
	VerifyPhoneOtpResponse_TOKEN_INACTIVE VerifyPhoneOtpResponse_Status = 100
	// Input token is expired and cannot use it
	// Generate new token
	VerifyPhoneOtpResponse_TOKEN_EXPIRY VerifyPhoneOtpResponse_Status = 101
	// Otp is not correct
	VerifyPhoneOtpResponse_OTP_INCORRECT VerifyPhoneOtpResponse_Status = 102
	// Otp is not valid
	// Account will be locked if additional incorrect attempts are made
	VerifyPhoneOtpResponse_OTP_INCORRECT_LAST_ATTEMPT VerifyPhoneOtpResponse_Status = 103
	// Account is locked temporarily
	// There have been too many incorrect attempts
	// Wait for some time and retry with a new token
	VerifyPhoneOtpResponse_OTP_INCORRECT_LOCKED VerifyPhoneOtpResponse_Status = 104
	// Too many incorrect attempts on the token
	// Generate new token
	VerifyPhoneOtpResponse_OTP_VERIFY_LIMIT_EXCEEDED VerifyPhoneOtpResponse_Status = 105
	// The user doesn't have access to the app due to access being revoked
	VerifyPhoneOtpResponse_OTP_USER_ACCESS_SOFT_BLOCK VerifyPhoneOtpResponse_Status = 108
	// The user doesn't have access to the app due to user being blacklisted by FI
	VerifyPhoneOtpResponse_OTP_USER_ACCESS_BLACKLISTED VerifyPhoneOtpResponse_Status = 111
	// Error while fetcing Otp entry from DB
	VerifyPhoneOtpResponse_ERR_FETCHING_OTP_ENTRY VerifyPhoneOtpResponse_Status = 112
	// device detail in request is different from the details present in otp entry
	VerifyPhoneOtpResponse_DIFFERENT_DEVICE VerifyPhoneOtpResponse_Status = 113
	// otp token in request does not match with the one present in DB entry
	VerifyPhoneOtpResponse_INVALID_OTP_TOKEN VerifyPhoneOtpResponse_Status = 114
)

// Enum value maps for VerifyPhoneOtpResponse_Status.
var (
	VerifyPhoneOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		100: "TOKEN_INACTIVE",
		101: "TOKEN_EXPIRY",
		102: "OTP_INCORRECT",
		103: "OTP_INCORRECT_LAST_ATTEMPT",
		104: "OTP_INCORRECT_LOCKED",
		105: "OTP_VERIFY_LIMIT_EXCEEDED",
		108: "OTP_USER_ACCESS_SOFT_BLOCK",
		111: "OTP_USER_ACCESS_BLACKLISTED",
		112: "ERR_FETCHING_OTP_ENTRY",
		113: "DIFFERENT_DEVICE",
		114: "INVALID_OTP_TOKEN",
	}
	VerifyPhoneOtpResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INVALID_ARGUMENT":            3,
		"NOT_FOUND":                   5,
		"INTERNAL":                    13,
		"TOKEN_INACTIVE":              100,
		"TOKEN_EXPIRY":                101,
		"OTP_INCORRECT":               102,
		"OTP_INCORRECT_LAST_ATTEMPT":  103,
		"OTP_INCORRECT_LOCKED":        104,
		"OTP_VERIFY_LIMIT_EXCEEDED":   105,
		"OTP_USER_ACCESS_SOFT_BLOCK":  108,
		"OTP_USER_ACCESS_BLACKLISTED": 111,
		"ERR_FETCHING_OTP_ENTRY":      112,
		"DIFFERENT_DEVICE":            113,
		"INVALID_OTP_TOKEN":           114,
	}
)

func (x VerifyPhoneOtpResponse_Status) Enum() *VerifyPhoneOtpResponse_Status {
	p := new(VerifyPhoneOtpResponse_Status)
	*p = x
	return p
}

func (x VerifyPhoneOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyPhoneOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_service_proto_enumTypes[2].Descriptor()
}

func (VerifyPhoneOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_webfe_service_proto_enumTypes[2]
}

func (x VerifyPhoneOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyPhoneOtpResponse_Status.Descriptor instead.
func (VerifyPhoneOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{12, 0}
}

type GenerateEmailOtpResponse_Status int32

const (
	// Success
	GenerateEmailOtpResponse_OK GenerateEmailOtpResponse_Status = 0
	// Input token is not available on the system
	// Generate new token
	GenerateEmailOtpResponse_NOT_FOUND GenerateEmailOtpResponse_Status = 5
	// Internal error
	GenerateEmailOtpResponse_INTERNAL GenerateEmailOtpResponse_Status = 13
	// Input token has been used already and is not active.
	// Cannot reuse it
	GenerateEmailOtpResponse_TOKEN_INACTIVE GenerateEmailOtpResponse_Status = 100
	// Input token is expired and cannot use it
	// Generate new token
	GenerateEmailOtpResponse_TOKEN_EXPIRY GenerateEmailOtpResponse_Status = 101
	// Too many resend attempts
	// Generate new token
	GenerateEmailOtpResponse_RESEND_LIMIT GenerateEmailOtpResponse_Status = 102
	// Resend request too soon
	// Wait for some time and retry
	GenerateEmailOtpResponse_RESEND_REQ_TOO_SOON GenerateEmailOtpResponse_Status = 103
)

// Enum value maps for GenerateEmailOtpResponse_Status.
var (
	GenerateEmailOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		100: "TOKEN_INACTIVE",
		101: "TOKEN_EXPIRY",
		102: "RESEND_LIMIT",
		103: "RESEND_REQ_TOO_SOON",
	}
	GenerateEmailOtpResponse_Status_value = map[string]int32{
		"OK":                  0,
		"NOT_FOUND":           5,
		"INTERNAL":            13,
		"TOKEN_INACTIVE":      100,
		"TOKEN_EXPIRY":        101,
		"RESEND_LIMIT":        102,
		"RESEND_REQ_TOO_SOON": 103,
	}
)

func (x GenerateEmailOtpResponse_Status) Enum() *GenerateEmailOtpResponse_Status {
	p := new(GenerateEmailOtpResponse_Status)
	*p = x
	return p
}

func (x GenerateEmailOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateEmailOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_service_proto_enumTypes[3].Descriptor()
}

func (GenerateEmailOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_webfe_service_proto_enumTypes[3]
}

func (x GenerateEmailOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateEmailOtpResponse_Status.Descriptor instead.
func (GenerateEmailOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{14, 0}
}

type VerifyEmailOtpResponse_Status int32

const (
	// Success
	VerifyEmailOtpResponse_OK VerifyEmailOtpResponse_Status = 0
	// Client specified an invalid argument
	VerifyEmailOtpResponse_INVALID_ARGUMENT VerifyEmailOtpResponse_Status = 3
	// Input token is not available on the system
	// Generate new token
	VerifyEmailOtpResponse_NOT_FOUND VerifyEmailOtpResponse_Status = 5
	// Internal error
	VerifyEmailOtpResponse_INTERNAL VerifyEmailOtpResponse_Status = 13
	// Input token has been used already and is not active.
	// Cannot reuse it
	VerifyEmailOtpResponse_TOKEN_INACTIVE VerifyEmailOtpResponse_Status = 100
	// Input token is expired and cannot use it
	// Generate new token
	VerifyEmailOtpResponse_TOKEN_EXPIRY VerifyEmailOtpResponse_Status = 101
	// Otp is not correct
	VerifyEmailOtpResponse_OTP_INCORRECT VerifyEmailOtpResponse_Status = 102
	// Otp is not valid
	// Account will be locked if additional incorrect attempts are made
	VerifyEmailOtpResponse_OTP_INCORRECT_LAST_ATTEMPT VerifyEmailOtpResponse_Status = 103
	// Account is locked temporarily
	// There have been too many incorrect attempts
	// Wait for some time and retry with a new token
	VerifyEmailOtpResponse_OTP_INCORRECT_LOCKED VerifyEmailOtpResponse_Status = 104
	// Too many incorrect attempts on the token
	// Generate new token
	VerifyEmailOtpResponse_OTP_VERIFY_LIMIT_EXCEEDED VerifyEmailOtpResponse_Status = 105
	// The user doesn't have access to the app due to access being revoked
	VerifyEmailOtpResponse_OTP_USER_ACCESS_SOFT_BLOCK VerifyEmailOtpResponse_Status = 108
	// The user doesn't have access to the app due to user being blacklisted by FI
	VerifyEmailOtpResponse_OTP_USER_ACCESS_BLACKLISTED VerifyEmailOtpResponse_Status = 111
	// Error while fetcing Otp entry from DB
	VerifyEmailOtpResponse_ERR_FETCHING_OTP_ENTRY VerifyEmailOtpResponse_Status = 112
	// device detail in request is different from the details present in otp entry
	VerifyEmailOtpResponse_DIFFERENT_DEVICE VerifyEmailOtpResponse_Status = 113
	// otp token in request does not match with the one present in DB entry
	VerifyEmailOtpResponse_INVALID_OTP_TOKEN VerifyEmailOtpResponse_Status = 114
)

// Enum value maps for VerifyEmailOtpResponse_Status.
var (
	VerifyEmailOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		100: "TOKEN_INACTIVE",
		101: "TOKEN_EXPIRY",
		102: "OTP_INCORRECT",
		103: "OTP_INCORRECT_LAST_ATTEMPT",
		104: "OTP_INCORRECT_LOCKED",
		105: "OTP_VERIFY_LIMIT_EXCEEDED",
		108: "OTP_USER_ACCESS_SOFT_BLOCK",
		111: "OTP_USER_ACCESS_BLACKLISTED",
		112: "ERR_FETCHING_OTP_ENTRY",
		113: "DIFFERENT_DEVICE",
		114: "INVALID_OTP_TOKEN",
	}
	VerifyEmailOtpResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INVALID_ARGUMENT":            3,
		"NOT_FOUND":                   5,
		"INTERNAL":                    13,
		"TOKEN_INACTIVE":              100,
		"TOKEN_EXPIRY":                101,
		"OTP_INCORRECT":               102,
		"OTP_INCORRECT_LAST_ATTEMPT":  103,
		"OTP_INCORRECT_LOCKED":        104,
		"OTP_VERIFY_LIMIT_EXCEEDED":   105,
		"OTP_USER_ACCESS_SOFT_BLOCK":  108,
		"OTP_USER_ACCESS_BLACKLISTED": 111,
		"ERR_FETCHING_OTP_ENTRY":      112,
		"DIFFERENT_DEVICE":            113,
		"INVALID_OTP_TOKEN":           114,
	}
)

func (x VerifyEmailOtpResponse_Status) Enum() *VerifyEmailOtpResponse_Status {
	p := new(VerifyEmailOtpResponse_Status)
	*p = x
	return p
}

func (x VerifyEmailOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyEmailOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_service_proto_enumTypes[4].Descriptor()
}

func (VerifyEmailOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_webfe_service_proto_enumTypes[4]
}

func (x VerifyEmailOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyEmailOtpResponse_Status.Descriptor instead.
func (VerifyEmailOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{16, 0}
}

// Request for checking the eligibility of a credit card application.
type CheckCreditCardEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header containing metadata for the frontend.
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Name of the user.
	Name *common.Name `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// PAN (Permanent Account Number) of the user. Used to enrich user entry.
	Pan string `protobuf:"bytes,3,opt,name=pan,proto3" json:"pan,omitempty"`
	// Date of birth of the user. Used to enrich user entry.
	Dob *date.Date `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
	// Email ID of the user.
	EmailId string `protobuf:"bytes,5,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// Consents that the user has given as part of the eligibility check.
	Consents []ConsentType `protobuf:"varint,6,rep,packed,name=consents,proto3,enum=webfe.ConsentType" json:"consents,omitempty"`
	// Token for authentication and authorization.
	Token string `protobuf:"bytes,7,opt,name=token,proto3" json:"token,omitempty"`
	// Type of card program for which eligibility is being checked.
	CardProgramType typesv2.CardProgramType `protobuf:"varint,8,opt,name=card_program_type,json=cardProgramType,proto3,enum=api.typesv2.CardProgramType" json:"card_program_type,omitempty"`
	// Vendor providing the card program.
	CardProgramVendor typesv2.CardProgramVendor `protobuf:"varint,9,opt,name=card_program_vendor,json=cardProgramVendor,proto3,enum=api.typesv2.CardProgramVendor" json:"card_program_vendor,omitempty"`
}

func (x *CheckCreditCardEligibilityRequest) Reset() {
	*x = CheckCreditCardEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreditCardEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreditCardEligibilityRequest) ProtoMessage() {}

func (x *CheckCreditCardEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreditCardEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CheckCreditCardEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{0}
}

func (x *CheckCreditCardEligibilityRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *CheckCreditCardEligibilityRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CheckCreditCardEligibilityRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *CheckCreditCardEligibilityRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *CheckCreditCardEligibilityRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *CheckCreditCardEligibilityRequest) GetConsents() []ConsentType {
	if x != nil {
		return x.Consents
	}
	return nil
}

func (x *CheckCreditCardEligibilityRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CheckCreditCardEligibilityRequest) GetCardProgramType() typesv2.CardProgramType {
	if x != nil {
		return x.CardProgramType
	}
	return typesv2.CardProgramType(0)
}

func (x *CheckCreditCardEligibilityRequest) GetCardProgramVendor() typesv2.CardProgramVendor {
	if x != nil {
		return x.CardProgramVendor
	}
	return typesv2.CardProgramVendor(0)
}

type CheckCreditCardEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// identifier for fetching the status of the flow that has been initiated on the basis of the
	// flow inputted in the request
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *CheckCreditCardEligibilityResponse) Reset() {
	*x = CheckCreditCardEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreditCardEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreditCardEligibilityResponse) ProtoMessage() {}

func (x *CheckCreditCardEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreditCardEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CheckCreditCardEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{1}
}

func (x *CheckCreditCardEligibilityResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CheckCreditCardEligibilityResponse) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type ValidateLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Unique identifier of ValidateLoginRequest
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// OTP that is shared to the phone number via SMS.
	Otp string `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	// enum to determine which flow has to be initiated.
	WebFlow WebFlow `protobuf:"varint,5,opt,name=web_flow,json=webFlow,proto3,enum=webfe.WebFlow" json:"web_flow,omitempty"`
	// email id of the user
	EmailId string `protobuf:"bytes,7,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// PAN number of the user. This will be used to enrich the user entry
	Pan string `protobuf:"bytes,8,opt,name=pan,proto3" json:"pan,omitempty"`
	// date of birth of the user. This will be used to enrich the user entry
	Dob *date.Date `protobuf:"bytes,9,opt,name=dob,proto3" json:"dob,omitempty"`
	// phone number of the user for which the otp verification will be done
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,10,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// consents that the user has given as a part of entering OTP
	Consents []ConsentType `protobuf:"varint,11,rep,packed,name=consents,proto3,enum=webfe.ConsentType" json:"consents,omitempty"`
	// name of the user
	Name *common.Name `protobuf:"bytes,12,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ValidateLoginRequest) Reset() {
	*x = ValidateLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateLoginRequest) ProtoMessage() {}

func (x *ValidateLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateLoginRequest.ProtoReflect.Descriptor instead.
func (*ValidateLoginRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateLoginRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ValidateLoginRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ValidateLoginRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *ValidateLoginRequest) GetWebFlow() WebFlow {
	if x != nil {
		return x.WebFlow
	}
	return WebFlow_WEB_FLOW_UNSPECIFIED
}

func (x *ValidateLoginRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *ValidateLoginRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ValidateLoginRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *ValidateLoginRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *ValidateLoginRequest) GetConsents() []ConsentType {
	if x != nil {
		return x.Consents
	}
	return nil
}

func (x *ValidateLoginRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

type ValidateLoginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// identifier for fetching the status of the flow that has been initiated on the basis of the
	// flow inputted in the request
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Represents a User's access token.
	// This token will serve as authentication parameter.
	AccessToken string `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	ActorId     string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *ValidateLoginResponse) Reset() {
	*x = ValidateLoginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateLoginResponse) ProtoMessage() {}

func (x *ValidateLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateLoginResponse.ProtoReflect.Descriptor instead.
func (*ValidateLoginResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateLoginResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *ValidateLoginResponse) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *ValidateLoginResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *ValidateLoginResponse) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetRequestStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// this will be required as an identifier for polling
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// flow for which the status has to be checked
	WebFlow WebFlow `protobuf:"varint,3,opt,name=web_flow,json=webFlow,proto3,enum=webfe.WebFlow" json:"web_flow,omitempty"`
	// actor id of the user for whom the flow has been started
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetRequestStatusRequest) Reset() {
	*x = GetRequestStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequestStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequestStatusRequest) ProtoMessage() {}

func (x *GetRequestStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequestStatusRequest.ProtoReflect.Descriptor instead.
func (*GetRequestStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetRequestStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetRequestStatusRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *GetRequestStatusRequest) GetWebFlow() WebFlow {
	if x != nil {
		return x.WebFlow
	}
	return WebFlow_WEB_FLOW_UNSPECIFIED
}

func (x *GetRequestStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetRequestStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader    *header.ResponseHeader                 `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	RequestStatus GetRequestStatusResponse_RequestStatus `protobuf:"varint,2,opt,name=request_status,json=requestStatus,proto3,enum=webfe.GetRequestStatusResponse_RequestStatus" json:"request_status,omitempty"`
	DisplayInfo   *DisplayInfo                           `protobuf:"bytes,3,opt,name=display_info,json=displayInfo,proto3" json:"display_info,omitempty"`
}

func (x *GetRequestStatusResponse) Reset() {
	*x = GetRequestStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequestStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequestStatusResponse) ProtoMessage() {}

func (x *GetRequestStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequestStatusResponse.ProtoReflect.Descriptor instead.
func (*GetRequestStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetRequestStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetRequestStatusResponse) GetRequestStatus() GetRequestStatusResponse_RequestStatus {
	if x != nil {
		return x.RequestStatus
	}
	return GetRequestStatusResponse_STATUS_UNSPECIFIED
}

func (x *GetRequestStatusResponse) GetDisplayInfo() *DisplayInfo {
	if x != nil {
		return x.DisplayInfo
	}
	return nil
}

// a generic structure to display a screen
type DisplayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// icon to be displayed above the screen title. This can be used to
	// represent the status of the reuqest
	HeaderIcon string `protobuf:"bytes,1,opt,name=header_icon,json=headerIcon,proto3" json:"header_icon,omitempty"`
	// this will be displayed as a screen heading
	ScreenTitle string `protobuf:"bytes,2,opt,name=screen_title,json=screenTitle,proto3" json:"screen_title,omitempty"`
	// this will be a screen message that will be displayed below the
	// screen title
	ScreenMessage string `protobuf:"bytes,3,opt,name=screen_message,json=screenMessage,proto3" json:"screen_message,omitempty"`
	// image occurring on the background
	ScreenImage string `protobuf:"bytes,4,opt,name=screen_image,json=screenImage,proto3" json:"screen_image,omitempty"`
	// text to be displayed on the cta at the bottom of the screen
	CtaText string `protobuf:"bytes,5,opt,name=cta_text,json=ctaText,proto3" json:"cta_text,omitempty"`
	// additional text to be displayed below the screen message
	AdditionalText string `protobuf:"bytes,6,opt,name=additional_text,json=additionalText,proto3" json:"additional_text,omitempty"`
	// text to be displayed at the bottom of the screen. This will be
	// an informatory text
	BottomText string `protobuf:"bytes,7,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
}

func (x *DisplayInfo) Reset() {
	*x = DisplayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayInfo) ProtoMessage() {}

func (x *DisplayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayInfo.ProtoReflect.Descriptor instead.
func (*DisplayInfo) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{6}
}

func (x *DisplayInfo) GetHeaderIcon() string {
	if x != nil {
		return x.HeaderIcon
	}
	return ""
}

func (x *DisplayInfo) GetScreenTitle() string {
	if x != nil {
		return x.ScreenTitle
	}
	return ""
}

func (x *DisplayInfo) GetScreenMessage() string {
	if x != nil {
		return x.ScreenMessage
	}
	return ""
}

func (x *DisplayInfo) GetScreenImage() string {
	if x != nil {
		return x.ScreenImage
	}
	return ""
}

func (x *DisplayInfo) GetCtaText() string {
	if x != nil {
		return x.CtaText
	}
	return ""
}

func (x *DisplayInfo) GetAdditionalText() string {
	if x != nil {
		return x.AdditionalText
	}
	return ""
}

func (x *DisplayInfo) GetBottomText() string {
	if x != nil {
		return x.BottomText
	}
	return ""
}

type GeneratePhoneOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Phone number that is to be verified
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Flow for which OTP is being generated.
	GenerateOtpFlow GenerateOTPFlow `protobuf:"varint,3,opt,name=generate_otp_flow,json=generateOtpFlow,proto3,enum=webfe.GenerateOTPFlow" json:"generate_otp_flow,omitempty"`
	// Unique identifier of GenerateOtp request
	// If token(+phone_number) is not sent, a new OTP is generated and sent to the user via SMS
	// If token(+phone_number) is sent, request is treated as ResendOtp and existing OTP is sent to the user phone via SMS
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *GeneratePhoneOtpRequest) Reset() {
	*x = GeneratePhoneOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePhoneOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePhoneOtpRequest) ProtoMessage() {}

func (x *GeneratePhoneOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePhoneOtpRequest.ProtoReflect.Descriptor instead.
func (*GeneratePhoneOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{7}
}

func (x *GeneratePhoneOtpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GeneratePhoneOtpRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GeneratePhoneOtpRequest) GetGenerateOtpFlow() GenerateOTPFlow {
	if x != nil {
		return x.GenerateOtpFlow
	}
	return GenerateOTPFlow_GENERATE_OTP_FLOW_UNSPECIFIED
}

func (x *GeneratePhoneOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type GeneratePhoneOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Unique identifier of GeneratePhoneOtp request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// A timer(in seconds) for the client post which it should raise a new request for Otp
	// Any attempt prior to this timer will not be honored & will result in error
	RetryTimerSeconds uint32 `protobuf:"varint,3,opt,name=retry_timer_seconds,json=retryTimerSeconds,proto3" json:"retry_timer_seconds,omitempty"`
}

func (x *GeneratePhoneOtpResponse) Reset() {
	*x = GeneratePhoneOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePhoneOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePhoneOtpResponse) ProtoMessage() {}

func (x *GeneratePhoneOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePhoneOtpResponse.ProtoReflect.Descriptor instead.
func (*GeneratePhoneOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{8}
}

func (x *GeneratePhoneOtpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GeneratePhoneOtpResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GeneratePhoneOtpResponse) GetRetryTimerSeconds() uint32 {
	if x != nil {
		return x.RetryTimerSeconds
	}
	return 0
}

// Request message for verifying a phone number with OTP.
type VerifyPhoneOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Header information for the request.
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Phone number that is being verified.
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Unique identifier of the GeneratePhoneOtp request.
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// OTP from the user.
	// todo(teja) otp validation rule
	Otp string `protobuf:"bytes,4,opt,name=otp,proto3" json:"otp,omitempty"`
	// Flag to indicate whether to send a link to WhatsApp.
	HasWhatsappConsent common.BooleanEnum `protobuf:"varint,5,opt,name=has_whatsapp_consent,json=hasWhatsappConsent,proto3,enum=api.typesv2.common.BooleanEnum" json:"has_whatsapp_consent,omitempty"`
	// List of consents from the user.
	ConsentTypes []ConsentType `protobuf:"varint,6,rep,packed,name=consent_types,json=consentTypes,proto3,enum=webfe.ConsentType" json:"consent_types,omitempty"`
	// Optional URL through which the user entered the web flow.
	WebUrl string `protobuf:"bytes,7,opt,name=web_url,json=webUrl,proto3" json:"web_url,omitempty"`
	// Enum to determine which web flow has to be initiated.
	WebFlow WebFlow `protobuf:"varint,8,opt,name=web_flow,json=webFlow,proto3,enum=webfe.WebFlow" json:"web_flow,omitempty"`
	// Additional details for web flow.
	AdditionalDetails *AdditionalDetails `protobuf:"bytes,9,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
}

func (x *VerifyPhoneOtpRequest) Reset() {
	*x = VerifyPhoneOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPhoneOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPhoneOtpRequest) ProtoMessage() {}

func (x *VerifyPhoneOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPhoneOtpRequest.ProtoReflect.Descriptor instead.
func (*VerifyPhoneOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{9}
}

func (x *VerifyPhoneOtpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *VerifyPhoneOtpRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *VerifyPhoneOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *VerifyPhoneOtpRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyPhoneOtpRequest) GetHasWhatsappConsent() common.BooleanEnum {
	if x != nil {
		return x.HasWhatsappConsent
	}
	return common.BooleanEnum(0)
}

func (x *VerifyPhoneOtpRequest) GetConsentTypes() []ConsentType {
	if x != nil {
		return x.ConsentTypes
	}
	return nil
}

func (x *VerifyPhoneOtpRequest) GetWebUrl() string {
	if x != nil {
		return x.WebUrl
	}
	return ""
}

func (x *VerifyPhoneOtpRequest) GetWebFlow() WebFlow {
	if x != nil {
		return x.WebFlow
	}
	return WebFlow_WEB_FLOW_UNSPECIFIED
}

func (x *VerifyPhoneOtpRequest) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

// Additional details container.
type AdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Oneof for various types of web flow data.
	//
	// Types that are assignable to WebFlowData:
	//
	//	*AdditionalDetails_CreditCardEligibilityCheckData
	WebFlowData isAdditionalDetails_WebFlowData `protobuf_oneof:"WebFlowData"`
}

func (x *AdditionalDetails) Reset() {
	*x = AdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalDetails) ProtoMessage() {}

func (x *AdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalDetails.ProtoReflect.Descriptor instead.
func (*AdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{10}
}

func (m *AdditionalDetails) GetWebFlowData() isAdditionalDetails_WebFlowData {
	if m != nil {
		return m.WebFlowData
	}
	return nil
}

func (x *AdditionalDetails) GetCreditCardEligibilityCheckData() *CreditCardEligibilityCheckData {
	if x, ok := x.GetWebFlowData().(*AdditionalDetails_CreditCardEligibilityCheckData); ok {
		return x.CreditCardEligibilityCheckData
	}
	return nil
}

type isAdditionalDetails_WebFlowData interface {
	isAdditionalDetails_WebFlowData()
}

type AdditionalDetails_CreditCardEligibilityCheckData struct {
	CreditCardEligibilityCheckData *CreditCardEligibilityCheckData `protobuf:"bytes,1,opt,name=credit_card_eligibility_check_data,json=creditCardEligibilityCheckData,proto3,oneof"`
}

func (*AdditionalDetails_CreditCardEligibilityCheckData) isAdditionalDetails_WebFlowData() {}

// Web eligibility check data for the cc eligibility web flow.
type CreditCardEligibilityCheckData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of card program for which eligibility is being checked.
	CardProgramType typesv2.CardProgramType `protobuf:"varint,1,opt,name=card_program_type,json=cardProgramType,proto3,enum=api.typesv2.CardProgramType" json:"card_program_type,omitempty"`
	// Vendor providing the card program.
	CardProgramVendor typesv2.CardProgramVendor `protobuf:"varint,2,opt,name=card_program_vendor,json=cardProgramVendor,proto3,enum=api.typesv2.CardProgramVendor" json:"card_program_vendor,omitempty"`
}

func (x *CreditCardEligibilityCheckData) Reset() {
	*x = CreditCardEligibilityCheckData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardEligibilityCheckData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardEligibilityCheckData) ProtoMessage() {}

func (x *CreditCardEligibilityCheckData) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardEligibilityCheckData.ProtoReflect.Descriptor instead.
func (*CreditCardEligibilityCheckData) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreditCardEligibilityCheckData) GetCardProgramType() typesv2.CardProgramType {
	if x != nil {
		return x.CardProgramType
	}
	return typesv2.CardProgramType(0)
}

func (x *CreditCardEligibilityCheckData) GetCardProgramVendor() typesv2.CardProgramVendor {
	if x != nil {
		return x.CardProgramVendor
	}
	return typesv2.CardProgramVendor(0)
}

type VerifyPhoneOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Access token which allows user for email verification
	// todo(teja) otp validation rule
	AccessToken string             `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	NextScreen  *deeplink.Deeplink `protobuf:"bytes,3,opt,name=next_screen,json=nextScreen,proto3" json:"next_screen,omitempty"`
	// actor id of the user for whom the flow has been started
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *VerifyPhoneOtpResponse) Reset() {
	*x = VerifyPhoneOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPhoneOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPhoneOtpResponse) ProtoMessage() {}

func (x *VerifyPhoneOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPhoneOtpResponse.ProtoReflect.Descriptor instead.
func (*VerifyPhoneOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{12}
}

func (x *VerifyPhoneOtpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *VerifyPhoneOtpResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *VerifyPhoneOtpResponse) GetNextScreen() *deeplink.Deeplink {
	if x != nil {
		return x.NextScreen
	}
	return nil
}

func (x *VerifyPhoneOtpResponse) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GenerateEmailOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// email that is to be verified
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// Flow for which OTP is being generated.
	GenerateOtpFlow GenerateOTPFlow `protobuf:"varint,3,opt,name=generate_otp_flow,json=generateOtpFlow,proto3,enum=webfe.GenerateOTPFlow" json:"generate_otp_flow,omitempty"`
	// Unique identifier of GenerateEmailOtp request
	// If token(+email) is not sent, a new OTP is generated and sent to the user via SMS
	// If token(+email) is sent, request is treated as ResendOtp and existing OTP is sent to the corresponding email
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	// client_req_id is the unique identifier of a process entry for a client,
	// client would get this in the screen options of SendWorkEmailOtp screen
	ClientReqId string `protobuf:"bytes,5,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GenerateEmailOtpRequest) Reset() {
	*x = GenerateEmailOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateEmailOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateEmailOtpRequest) ProtoMessage() {}

func (x *GenerateEmailOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateEmailOtpRequest.ProtoReflect.Descriptor instead.
func (*GenerateEmailOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{13}
}

func (x *GenerateEmailOtpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GenerateEmailOtpRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GenerateEmailOtpRequest) GetGenerateOtpFlow() GenerateOTPFlow {
	if x != nil {
		return x.GenerateOtpFlow
	}
	return GenerateOTPFlow_GENERATE_OTP_FLOW_UNSPECIFIED
}

func (x *GenerateEmailOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GenerateEmailOtpRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GenerateEmailOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Unique identifier of GenerateEmailOtp request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// A timer(in seconds) for the client post which it should raise a new request for Otp
	// Any attempt prior to this timer will not be honored & will result in error
	RetryTimerSeconds uint32 `protobuf:"varint,3,opt,name=retry_timer_seconds,json=retryTimerSeconds,proto3" json:"retry_timer_seconds,omitempty"`
}

func (x *GenerateEmailOtpResponse) Reset() {
	*x = GenerateEmailOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateEmailOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateEmailOtpResponse) ProtoMessage() {}

func (x *GenerateEmailOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateEmailOtpResponse.ProtoReflect.Descriptor instead.
func (*GenerateEmailOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{14}
}

func (x *GenerateEmailOtpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GenerateEmailOtpResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GenerateEmailOtpResponse) GetRetryTimerSeconds() uint32 {
	if x != nil {
		return x.RetryTimerSeconds
	}
	return 0
}

type VerifyEmailOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// email that is to be verified
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// OTP from user
	Otp string `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	// unique identifier of GenerateEmailRequest
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	// client_req_id is the unique identifier of a process entry for a client,
	// client would get this in the screen options of SendWorkEmailOtp screen
	ClientReqId string `protobuf:"bytes,5,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *VerifyEmailOtpRequest) Reset() {
	*x = VerifyEmailOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyEmailOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyEmailOtpRequest) ProtoMessage() {}

func (x *VerifyEmailOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyEmailOtpRequest.ProtoReflect.Descriptor instead.
func (*VerifyEmailOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{15}
}

func (x *VerifyEmailOtpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *VerifyEmailOtpRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *VerifyEmailOtpRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyEmailOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *VerifyEmailOtpRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type VerifyEmailOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *VerifyEmailOtpResponse) Reset() {
	*x = VerifyEmailOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyEmailOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyEmailOtpResponse) ProtoMessage() {}

func (x *VerifyEmailOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyEmailOtpResponse.ProtoReflect.Descriptor instead.
func (*VerifyEmailOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{16}
}

func (x *VerifyEmailOtpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type SendAppLinkToUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *SendAppLinkToUserRequest) Reset() {
	*x = SendAppLinkToUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAppLinkToUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAppLinkToUserRequest) ProtoMessage() {}

func (x *SendAppLinkToUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAppLinkToUserRequest.ProtoReflect.Descriptor instead.
func (*SendAppLinkToUserRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{17}
}

func (x *SendAppLinkToUserRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type SendAppLinkToUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SendAppLinkToUserResponse) Reset() {
	*x = SendAppLinkToUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAppLinkToUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAppLinkToUserResponse) ProtoMessage() {}

func (x *SendAppLinkToUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAppLinkToUserResponse.ProtoReflect.Descriptor instead.
func (*SendAppLinkToUserResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_service_proto_rawDescGZIP(), []int{18}
}

func (x *SendAppLinkToUserResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

var File_api_webfe_service_proto protoreflect.FileDescriptor

var file_api_webfe_service_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x77, 0x65, 0x62, 0x66, 0x65,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x03, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2c,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x23,
	0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03,
	0x64, 0x6f, 0x62, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x2e,
	0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x48, 0x0a, 0x11, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e,
	0x0a, 0x13, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x11, 0x63, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x22, 0x8a,
	0x01, 0x0a, 0x22, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0xad, 0x03, 0x0a, 0x14,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xb0, 0x01, 0x01, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x32, 0x08, 0x5e, 0x5b, 0x30, 0x2d,
	0x39, 0x5d, 0x2b, 0x24, 0x98, 0x01, 0x06, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x29, 0x0a, 0x08,
	0x77, 0x65, 0x62, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e,
	0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x57, 0x65, 0x62, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x07,
	0x77, 0x65, 0x62, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x70, 0x61, 0x6e, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a,
	0x08, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x12, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2c, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x15,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x77,
	0x65, 0x62, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x57, 0x65, 0x62, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x07, 0x77,
	0x65, 0x62, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x22, 0xd4, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x54, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x77,
	0x65, 0x62, 0x66, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x69, 0x0a,
	0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x15,
	0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x22, 0x80, 0x02, 0x0a, 0x0b, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x74, 0x61, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x74, 0x61, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x22, 0xe9, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x42, 0x0a,
	0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65,
	0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x54, 0x50, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa2, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x13,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x7e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x64, 0x12,
	0x10, 0x0a, 0x0c, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10,
	0x65, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x10, 0x66, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x53, 0x4f, 0x4f, 0x4e, 0x10, 0x67, 0x22, 0xce, 0x03, 0x0a,
	0x15, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6f, 0x74, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x68, 0x61, 0x73, 0x5f, 0x77, 0x68, 0x61, 0x74,
	0x73, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x12, 0x68, 0x61, 0x73, 0x57, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x17, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x77, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x08, 0x77, 0x65, 0x62,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x77, 0x65,
	0x62, 0x66, 0x65, 0x2e, 0x57, 0x65, 0x62, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x07, 0x77, 0x65, 0x62,
	0x46, 0x6c, 0x6f, 0x77, 0x12, 0x47, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x97, 0x01,
	0x0a, 0x11, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x73, 0x0a, 0x22, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x0d, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x46,
	0x6c, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0xba, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x11, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x13, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x11, 0x63, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x22, 0xb8, 0x04, 0x0a, 0x16, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xdf, 0x02,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x54, 0x50,
	0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x10, 0x66, 0x12, 0x1e, 0x0a, 0x1a,
	0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x4c, 0x41,
	0x53, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x14,
	0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x4c, 0x4f,
	0x43, 0x4b, 0x45, 0x44, 0x10, 0x68, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x59, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45,
	0x44, 0x45, 0x44, 0x10, 0x69, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x54, 0x50, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x5f, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x10, 0x6c, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x54, 0x50, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x4c, 0x49,
	0x53, 0x54, 0x45, 0x44, 0x10, 0x6f, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x52, 0x52, 0x5f, 0x46, 0x45,
	0x54, 0x43, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x10, 0x70, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x71, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x72, 0x22,
	0xdf, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x42, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f,
	0x6f, 0x74, 0x70, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f,
	0x54, 0x50, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x4f, 0x74, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x22, 0xa2, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x11, 0x72, 0x65, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x7e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49,
	0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x4f, 0x4b,
	0x45, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x52,
	0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x66, 0x12, 0x17, 0x0a,
	0x13, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x54, 0x4f, 0x4f, 0x5f,
	0x53, 0x4f, 0x4f, 0x4e, 0x10, 0x67, 0x22, 0xab, 0x01, 0x0a, 0x15, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x49, 0x64, 0x22, 0xbc, 0x03, 0x0a, 0x16, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0xdf, 0x02, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x65, 0x12, 0x11, 0x0a,
	0x0d, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x10, 0x66,
	0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43,
	0x54, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x10, 0x67,
	0x12, 0x18, 0x0a, 0x14, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43,
	0x54, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x68, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x54,
	0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x69, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x54, 0x50,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x4f, 0x46,
	0x54, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x6c, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x54, 0x50,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x4c, 0x41,
	0x43, 0x4b, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x10, 0x6f, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x52,
	0x52, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x45,
	0x4e, 0x54, 0x52, 0x59, 0x10, 0x70, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52,
	0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x71, 0x12, 0x15, 0x0a, 0x11,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x4f, 0x4b, 0x45,
	0x4e, 0x10, 0x72, 0x22, 0x4c, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x70, 0x70, 0x4c, 0x69,
	0x6e, 0x6b, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x22, 0x5d, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x6e, 0x6b,
	0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x32, 0xc4, 0x06, 0x0a, 0x05, 0x57, 0x65, 0x62, 0x66, 0x65, 0x12, 0x64, 0x0a, 0x10, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x12, 0x1e,
	0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x12, 0x5e, 0x0a, 0x0e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4f,
	0x74, 0x70, 0x12, 0x1c, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x12, 0x64, 0x0a, 0x10, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4f, 0x74, 0x70, 0x12, 0x1e, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x5e, 0x0a, 0x0e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x12, 0x1c, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65,
	0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x67, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x70,
	0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x77, 0x65,
	0x62, 0x66, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x54,
	0x6f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x77,
	0x65, 0x62, 0x66, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x6e, 0x6b,
	0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0x5b, 0x0a, 0x0d, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x12, 0x1b, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7,
	0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x64, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1e, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x12, 0x28, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x77, 0x65,
	0x62, 0x66, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7,
	0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x5a, 0x20, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_service_proto_rawDescOnce sync.Once
	file_api_webfe_service_proto_rawDescData = file_api_webfe_service_proto_rawDesc
)

func file_api_webfe_service_proto_rawDescGZIP() []byte {
	file_api_webfe_service_proto_rawDescOnce.Do(func() {
		file_api_webfe_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_service_proto_rawDescData)
	})
	return file_api_webfe_service_proto_rawDescData
}

var file_api_webfe_service_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_webfe_service_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_webfe_service_proto_goTypes = []interface{}{
	(GetRequestStatusResponse_RequestStatus)(0), // 0: webfe.GetRequestStatusResponse.RequestStatus
	(GeneratePhoneOtpResponse_Status)(0),        // 1: webfe.GeneratePhoneOtpResponse.Status
	(VerifyPhoneOtpResponse_Status)(0),          // 2: webfe.VerifyPhoneOtpResponse.Status
	(GenerateEmailOtpResponse_Status)(0),        // 3: webfe.GenerateEmailOtpResponse.Status
	(VerifyEmailOtpResponse_Status)(0),          // 4: webfe.VerifyEmailOtpResponse.Status
	(*CheckCreditCardEligibilityRequest)(nil),   // 5: webfe.CheckCreditCardEligibilityRequest
	(*CheckCreditCardEligibilityResponse)(nil),  // 6: webfe.CheckCreditCardEligibilityResponse
	(*ValidateLoginRequest)(nil),                // 7: webfe.ValidateLoginRequest
	(*ValidateLoginResponse)(nil),               // 8: webfe.ValidateLoginResponse
	(*GetRequestStatusRequest)(nil),             // 9: webfe.GetRequestStatusRequest
	(*GetRequestStatusResponse)(nil),            // 10: webfe.GetRequestStatusResponse
	(*DisplayInfo)(nil),                         // 11: webfe.DisplayInfo
	(*GeneratePhoneOtpRequest)(nil),             // 12: webfe.GeneratePhoneOtpRequest
	(*GeneratePhoneOtpResponse)(nil),            // 13: webfe.GeneratePhoneOtpResponse
	(*VerifyPhoneOtpRequest)(nil),               // 14: webfe.VerifyPhoneOtpRequest
	(*AdditionalDetails)(nil),                   // 15: webfe.AdditionalDetails
	(*CreditCardEligibilityCheckData)(nil),      // 16: webfe.CreditCardEligibilityCheckData
	(*VerifyPhoneOtpResponse)(nil),              // 17: webfe.VerifyPhoneOtpResponse
	(*GenerateEmailOtpRequest)(nil),             // 18: webfe.GenerateEmailOtpRequest
	(*GenerateEmailOtpResponse)(nil),            // 19: webfe.GenerateEmailOtpResponse
	(*VerifyEmailOtpRequest)(nil),               // 20: webfe.VerifyEmailOtpRequest
	(*VerifyEmailOtpResponse)(nil),              // 21: webfe.VerifyEmailOtpResponse
	(*SendAppLinkToUserRequest)(nil),            // 22: webfe.SendAppLinkToUserRequest
	(*SendAppLinkToUserResponse)(nil),           // 23: webfe.SendAppLinkToUserResponse
	(*header.RequestHeader)(nil),                // 24: frontend.header.RequestHeader
	(*common.Name)(nil),                         // 25: api.typesv2.common.Name
	(*date.Date)(nil),                           // 26: google.type.Date
	(ConsentType)(0),                            // 27: webfe.ConsentType
	(typesv2.CardProgramType)(0),                // 28: api.typesv2.CardProgramType
	(typesv2.CardProgramVendor)(0),              // 29: api.typesv2.CardProgramVendor
	(*header.ResponseHeader)(nil),               // 30: frontend.header.ResponseHeader
	(WebFlow)(0),                                // 31: webfe.WebFlow
	(*common.PhoneNumber)(nil),                  // 32: api.typesv2.common.PhoneNumber
	(GenerateOTPFlow)(0),                        // 33: webfe.GenerateOTPFlow
	(common.BooleanEnum)(0),                     // 34: api.typesv2.common.BooleanEnum
	(*deeplink.Deeplink)(nil),                   // 35: frontend.deeplink.Deeplink
}
var file_api_webfe_service_proto_depIdxs = []int32{
	24, // 0: webfe.CheckCreditCardEligibilityRequest.req:type_name -> frontend.header.RequestHeader
	25, // 1: webfe.CheckCreditCardEligibilityRequest.name:type_name -> api.typesv2.common.Name
	26, // 2: webfe.CheckCreditCardEligibilityRequest.dob:type_name -> google.type.Date
	27, // 3: webfe.CheckCreditCardEligibilityRequest.consents:type_name -> webfe.ConsentType
	28, // 4: webfe.CheckCreditCardEligibilityRequest.card_program_type:type_name -> api.typesv2.CardProgramType
	29, // 5: webfe.CheckCreditCardEligibilityRequest.card_program_vendor:type_name -> api.typesv2.CardProgramVendor
	30, // 6: webfe.CheckCreditCardEligibilityResponse.resp_header:type_name -> frontend.header.ResponseHeader
	24, // 7: webfe.ValidateLoginRequest.req:type_name -> frontend.header.RequestHeader
	31, // 8: webfe.ValidateLoginRequest.web_flow:type_name -> webfe.WebFlow
	26, // 9: webfe.ValidateLoginRequest.dob:type_name -> google.type.Date
	32, // 10: webfe.ValidateLoginRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	27, // 11: webfe.ValidateLoginRequest.consents:type_name -> webfe.ConsentType
	25, // 12: webfe.ValidateLoginRequest.name:type_name -> api.typesv2.common.Name
	30, // 13: webfe.ValidateLoginResponse.resp_header:type_name -> frontend.header.ResponseHeader
	24, // 14: webfe.GetRequestStatusRequest.req:type_name -> frontend.header.RequestHeader
	31, // 15: webfe.GetRequestStatusRequest.web_flow:type_name -> webfe.WebFlow
	30, // 16: webfe.GetRequestStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	0,  // 17: webfe.GetRequestStatusResponse.request_status:type_name -> webfe.GetRequestStatusResponse.RequestStatus
	11, // 18: webfe.GetRequestStatusResponse.display_info:type_name -> webfe.DisplayInfo
	24, // 19: webfe.GeneratePhoneOtpRequest.req:type_name -> frontend.header.RequestHeader
	32, // 20: webfe.GeneratePhoneOtpRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	33, // 21: webfe.GeneratePhoneOtpRequest.generate_otp_flow:type_name -> webfe.GenerateOTPFlow
	30, // 22: webfe.GeneratePhoneOtpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	24, // 23: webfe.VerifyPhoneOtpRequest.req:type_name -> frontend.header.RequestHeader
	32, // 24: webfe.VerifyPhoneOtpRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	34, // 25: webfe.VerifyPhoneOtpRequest.has_whatsapp_consent:type_name -> api.typesv2.common.BooleanEnum
	27, // 26: webfe.VerifyPhoneOtpRequest.consent_types:type_name -> webfe.ConsentType
	31, // 27: webfe.VerifyPhoneOtpRequest.web_flow:type_name -> webfe.WebFlow
	15, // 28: webfe.VerifyPhoneOtpRequest.additional_details:type_name -> webfe.AdditionalDetails
	16, // 29: webfe.AdditionalDetails.credit_card_eligibility_check_data:type_name -> webfe.CreditCardEligibilityCheckData
	28, // 30: webfe.CreditCardEligibilityCheckData.card_program_type:type_name -> api.typesv2.CardProgramType
	29, // 31: webfe.CreditCardEligibilityCheckData.card_program_vendor:type_name -> api.typesv2.CardProgramVendor
	30, // 32: webfe.VerifyPhoneOtpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	35, // 33: webfe.VerifyPhoneOtpResponse.next_screen:type_name -> frontend.deeplink.Deeplink
	24, // 34: webfe.GenerateEmailOtpRequest.req:type_name -> frontend.header.RequestHeader
	33, // 35: webfe.GenerateEmailOtpRequest.generate_otp_flow:type_name -> webfe.GenerateOTPFlow
	30, // 36: webfe.GenerateEmailOtpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	24, // 37: webfe.VerifyEmailOtpRequest.req:type_name -> frontend.header.RequestHeader
	30, // 38: webfe.VerifyEmailOtpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	24, // 39: webfe.SendAppLinkToUserRequest.req:type_name -> frontend.header.RequestHeader
	30, // 40: webfe.SendAppLinkToUserResponse.resp_header:type_name -> frontend.header.ResponseHeader
	12, // 41: webfe.Webfe.GeneratePhoneOtp:input_type -> webfe.GeneratePhoneOtpRequest
	14, // 42: webfe.Webfe.VerifyPhoneOtp:input_type -> webfe.VerifyPhoneOtpRequest
	18, // 43: webfe.Webfe.GenerateEmailOtp:input_type -> webfe.GenerateEmailOtpRequest
	20, // 44: webfe.Webfe.VerifyEmailOtp:input_type -> webfe.VerifyEmailOtpRequest
	22, // 45: webfe.Webfe.SendAppLinkToUser:input_type -> webfe.SendAppLinkToUserRequest
	7,  // 46: webfe.Webfe.ValidateLogin:input_type -> webfe.ValidateLoginRequest
	9,  // 47: webfe.Webfe.GetRequestStatus:input_type -> webfe.GetRequestStatusRequest
	5,  // 48: webfe.Webfe.CheckCreditCardEligibility:input_type -> webfe.CheckCreditCardEligibilityRequest
	13, // 49: webfe.Webfe.GeneratePhoneOtp:output_type -> webfe.GeneratePhoneOtpResponse
	17, // 50: webfe.Webfe.VerifyPhoneOtp:output_type -> webfe.VerifyPhoneOtpResponse
	19, // 51: webfe.Webfe.GenerateEmailOtp:output_type -> webfe.GenerateEmailOtpResponse
	21, // 52: webfe.Webfe.VerifyEmailOtp:output_type -> webfe.VerifyEmailOtpResponse
	23, // 53: webfe.Webfe.SendAppLinkToUser:output_type -> webfe.SendAppLinkToUserResponse
	8,  // 54: webfe.Webfe.ValidateLogin:output_type -> webfe.ValidateLoginResponse
	10, // 55: webfe.Webfe.GetRequestStatus:output_type -> webfe.GetRequestStatusResponse
	6,  // 56: webfe.Webfe.CheckCreditCardEligibility:output_type -> webfe.CheckCreditCardEligibilityResponse
	49, // [49:57] is the sub-list for method output_type
	41, // [41:49] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_api_webfe_service_proto_init() }
func file_api_webfe_service_proto_init() {
	if File_api_webfe_service_proto != nil {
		return
	}
	file_api_webfe_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_webfe_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreditCardEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreditCardEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateLoginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequestStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequestStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePhoneOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePhoneOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPhoneOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardEligibilityCheckData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPhoneOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateEmailOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateEmailOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyEmailOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyEmailOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAppLinkToUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAppLinkToUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_webfe_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*AdditionalDetails_CreditCardEligibilityCheckData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_service_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_webfe_service_proto_goTypes,
		DependencyIndexes: file_api_webfe_service_proto_depIdxs,
		EnumInfos:         file_api_webfe_service_proto_enumTypes,
		MessageInfos:      file_api_webfe_service_proto_msgTypes,
	}.Build()
	File_api_webfe_service_proto = out.File
	file_api_webfe_service_proto_rawDesc = nil
	file_api_webfe_service_proto_goTypes = nil
	file_api_webfe_service_proto_depIdxs = nil
}
