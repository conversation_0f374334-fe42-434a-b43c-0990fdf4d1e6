// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/tiering/service.proto

package tiering

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	enum "github.com/epifi/gamma/api/frontend/tiering/enum"
	tiering "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	sections "github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This used to B.E purpose only.
type BenefitsType int32

const (
	BenefitsType_BENEFITS_TYPE_UNSPECIFIED           BenefitsType = 0
	BenefitsType_BENEFITS_TYPE_DEBIT_CARD            BenefitsType = 1
	BenefitsType_BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW  BenefitsType = 2
	BenefitsType_BENEFITS_TYPE_MORE_INFO_VIEW        BenefitsType = 3
	BenefitsType_BENEFITS_TYPE_MONTHLY_VIEW          BenefitsType = 4
	BenefitsType_BENEFITS_TYPE_OTHER                 BenefitsType = 5
	BenefitsType_BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW BenefitsType = 6
	BenefitsType_BENEFITS_TYPE_REWARD_VIEW           BenefitsType = 7
	BenefitsType_BENEFITS_TYPE_WARNING_VIEW          BenefitsType = 8
	BenefitsType_BENEFITS_TYPE_HEALTH_INSURANCE_VIEW BenefitsType = 9
)

// Enum value maps for BenefitsType.
var (
	BenefitsType_name = map[int32]string{
		0: "BENEFITS_TYPE_UNSPECIFIED",
		1: "BENEFITS_TYPE_DEBIT_CARD",
		2: "BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW",
		3: "BENEFITS_TYPE_MORE_INFO_VIEW",
		4: "BENEFITS_TYPE_MONTHLY_VIEW",
		5: "BENEFITS_TYPE_OTHER",
		6: "BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW",
		7: "BENEFITS_TYPE_REWARD_VIEW",
		8: "BENEFITS_TYPE_WARNING_VIEW",
		9: "BENEFITS_TYPE_HEALTH_INSURANCE_VIEW",
	}
	BenefitsType_value = map[string]int32{
		"BENEFITS_TYPE_UNSPECIFIED":           0,
		"BENEFITS_TYPE_DEBIT_CARD":            1,
		"BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW":  2,
		"BENEFITS_TYPE_MORE_INFO_VIEW":        3,
		"BENEFITS_TYPE_MONTHLY_VIEW":          4,
		"BENEFITS_TYPE_OTHER":                 5,
		"BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW": 6,
		"BENEFITS_TYPE_REWARD_VIEW":           7,
		"BENEFITS_TYPE_WARNING_VIEW":          8,
		"BENEFITS_TYPE_HEALTH_INSURANCE_VIEW": 9,
	}
)

func (x BenefitsType) Enum() *BenefitsType {
	p := new(BenefitsType)
	*p = x
	return p
}

func (x BenefitsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BenefitsType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_tiering_service_proto_enumTypes[0].Descriptor()
}

func (BenefitsType) Type() protoreflect.EnumType {
	return &file_api_frontend_tiering_service_proto_enumTypes[0]
}

func (x BenefitsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BenefitsType.Descriptor instead.
func (BenefitsType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{0}
}

type GetTierEarnedBenefitsResponse_Status int32

const (
	// rpc successful
	GetTierEarnedBenefitsResponse_OK GetTierEarnedBenefitsResponse_Status = 0
	// Internal error while processing the request
	GetTierEarnedBenefitsResponse_INTERNAL GetTierEarnedBenefitsResponse_Status = 13
	// status code when fetching of any benefit failed
	// the screen will load for this status code with partial data
	GetTierEarnedBenefitsResponse_FETCH_FAILED GetTierEarnedBenefitsResponse_Status = 101
	// status code when screen is not enabled for the tier user is in
	GetTierEarnedBenefitsResponse_SCREEN_UNAVAILABLE_FOR_TIER GetTierEarnedBenefitsResponse_Status = 102
)

// Enum value maps for GetTierEarnedBenefitsResponse_Status.
var (
	GetTierEarnedBenefitsResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "FETCH_FAILED",
		102: "SCREEN_UNAVAILABLE_FOR_TIER",
	}
	GetTierEarnedBenefitsResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INTERNAL":                    13,
		"FETCH_FAILED":                101,
		"SCREEN_UNAVAILABLE_FOR_TIER": 102,
	}
)

func (x GetTierEarnedBenefitsResponse_Status) Enum() *GetTierEarnedBenefitsResponse_Status {
	p := new(GetTierEarnedBenefitsResponse_Status)
	*p = x
	return p
}

func (x GetTierEarnedBenefitsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTierEarnedBenefitsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_tiering_service_proto_enumTypes[1].Descriptor()
}

func (GetTierEarnedBenefitsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_tiering_service_proto_enumTypes[1]
}

func (x GetTierEarnedBenefitsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTierEarnedBenefitsResponse_Status.Descriptor instead.
func (GetTierEarnedBenefitsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{16, 0}
}

type GetDetailedBenefitsBottomSheetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// meta data for the request
	MetaData string `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *GetDetailedBenefitsBottomSheetRequest) Reset() {
	*x = GetDetailedBenefitsBottomSheetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedBenefitsBottomSheetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedBenefitsBottomSheetRequest) ProtoMessage() {}

func (x *GetDetailedBenefitsBottomSheetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedBenefitsBottomSheetRequest.ProtoReflect.Descriptor instead.
func (*GetDetailedBenefitsBottomSheetRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetDetailedBenefitsBottomSheetRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetDetailedBenefitsBottomSheetRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

type GetDetailedBenefitsBottomSheetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Section    *sections.Section      `protobuf:"bytes,2,opt,name=section,proto3" json:"section,omitempty"`
	// Next action cta
	Cta *deeplink.Cta `protobuf:"bytes,3,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *GetDetailedBenefitsBottomSheetResponse) Reset() {
	*x = GetDetailedBenefitsBottomSheetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedBenefitsBottomSheetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedBenefitsBottomSheetResponse) ProtoMessage() {}

func (x *GetDetailedBenefitsBottomSheetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedBenefitsBottomSheetResponse.ProtoReflect.Descriptor instead.
func (*GetDetailedBenefitsBottomSheetResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetDetailedBenefitsBottomSheetResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetDetailedBenefitsBottomSheetResponse) GetSection() *sections.Section {
	if x != nil {
		return x.Section
	}
	return nil
}

func (x *GetDetailedBenefitsBottomSheetResponse) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

type RecordComponentShownToActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Component name to be sent in request by client whenever it is displayed to user (mandatory)
	ComponentName enum.DisplayComponent `protobuf:"varint,2,opt,name=component_name,json=componentName,proto3,enum=frontend.tiering.enum.DisplayComponent" json:"component_name,omitempty"`
}

func (x *RecordComponentShownToActorRequest) Reset() {
	*x = RecordComponentShownToActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordComponentShownToActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordComponentShownToActorRequest) ProtoMessage() {}

func (x *RecordComponentShownToActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordComponentShownToActorRequest.ProtoReflect.Descriptor instead.
func (*RecordComponentShownToActorRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{2}
}

func (x *RecordComponentShownToActorRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *RecordComponentShownToActorRequest) GetComponentName() enum.DisplayComponent {
	if x != nil {
		return x.ComponentName
	}
	return enum.DisplayComponent(0)
}

type GetEarnedBenefitsHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// after token is to be passed if client wants to fetch activities that happened after previous page.
	// in this case the events will be ordered in ASCENDING order of activity time.
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *GetEarnedBenefitsHistoryRequest) Reset() {
	*x = GetEarnedBenefitsHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEarnedBenefitsHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarnedBenefitsHistoryRequest) ProtoMessage() {}

func (x *GetEarnedBenefitsHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarnedBenefitsHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetEarnedBenefitsHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetEarnedBenefitsHistoryRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type GetEarnedBenefitsHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Plus lifetime benefits
	PageTitle                *common.Text                                 `protobuf:"bytes,2,opt,name=page_title,json=pageTitle,proto3" json:"page_title,omitempty"`
	HeaderView               *GetEarnedBenefitsHistoryResponse_HeaderView `protobuf:"bytes,3,opt,name=header_view,json=headerView,proto3" json:"header_view,omitempty"`
	MonthlyRewardEarnedViews []*MonthlyRewardEarnedView                   `protobuf:"bytes,4,rep,name=monthly_reward_earned_views,json=monthlyRewardEarnedViews,proto3" json:"monthly_reward_earned_views,omitempty"`
	// view more cta
	ViewMoreCta *ui.IconTextComponent `protobuf:"bytes,5,opt,name=view_more_cta,json=viewMoreCta,proto3" json:"view_more_cta,omitempty"`
	// before_token to be used when client wants to fetch lifetime benefit that happened before the current page.
	BeforeToken string `protobuf:"bytes,6,opt,name=before_token,json=beforeToken,proto3" json:"before_token,omitempty"`
	// after_token to be used when client wants to fetch lifetime benefit that happened after the current page.
	AfterToken string `protobuf:"bytes,7,opt,name=after_token,json=afterToken,proto3" json:"after_token,omitempty"`
}

func (x *GetEarnedBenefitsHistoryResponse) Reset() {
	*x = GetEarnedBenefitsHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEarnedBenefitsHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarnedBenefitsHistoryResponse) ProtoMessage() {}

func (x *GetEarnedBenefitsHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarnedBenefitsHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetEarnedBenefitsHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetEarnedBenefitsHistoryResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse) GetPageTitle() *common.Text {
	if x != nil {
		return x.PageTitle
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse) GetHeaderView() *GetEarnedBenefitsHistoryResponse_HeaderView {
	if x != nil {
		return x.HeaderView
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse) GetMonthlyRewardEarnedViews() []*MonthlyRewardEarnedView {
	if x != nil {
		return x.MonthlyRewardEarnedViews
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse) GetViewMoreCta() *ui.IconTextComponent {
	if x != nil {
		return x.ViewMoreCta
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse) GetBeforeToken() string {
	if x != nil {
		return x.BeforeToken
	}
	return ""
}

func (x *GetEarnedBenefitsHistoryResponse) GetAfterToken() string {
	if x != nil {
		return x.AfterToken
	}
	return ""
}

type MonthlyRewardEarnedView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeaderView           *MonthlyRewardEarnedView_HeaderView `protobuf:"bytes,1,opt,name=header_view,json=headerView,proto3" json:"header_view,omitempty"`
	CardBackgroundColour *widget.BackgroundColour            `protobuf:"bytes,2,opt,name=card_background_colour,json=cardBackgroundColour,proto3" json:"card_background_colour,omitempty"`
	// EX: BENEFITS EARNED
	TotalBenefitsEarned *common.Text `protobuf:"bytes,3,opt,name=total_benefits_earned,json=totalBenefitsEarned,proto3" json:"total_benefits_earned,omitempty"`
	// EX: ₹1,000
	TotalBenefitsEarnedValue *common.Text                               `protobuf:"bytes,4,opt,name=total_benefits_earned_value,json=totalBenefitsEarnedValue,proto3" json:"total_benefits_earned_value,omitempty"`
	BenefitBackgroundColour  *widget.BackgroundColour                   `protobuf:"bytes,5,opt,name=benefit_background_colour,json=benefitBackgroundColour,proto3" json:"benefit_background_colour,omitempty"`
	BenefitCardItems         []*MonthlyRewardEarnedView_BenefitCardItem `protobuf:"bytes,6,rep,name=benefit_card_items,json=benefitCardItems,proto3" json:"benefit_card_items,omitempty"`
}

func (x *MonthlyRewardEarnedView) Reset() {
	*x = MonthlyRewardEarnedView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyRewardEarnedView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyRewardEarnedView) ProtoMessage() {}

func (x *MonthlyRewardEarnedView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyRewardEarnedView.ProtoReflect.Descriptor instead.
func (*MonthlyRewardEarnedView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{5}
}

func (x *MonthlyRewardEarnedView) GetHeaderView() *MonthlyRewardEarnedView_HeaderView {
	if x != nil {
		return x.HeaderView
	}
	return nil
}

func (x *MonthlyRewardEarnedView) GetCardBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.CardBackgroundColour
	}
	return nil
}

func (x *MonthlyRewardEarnedView) GetTotalBenefitsEarned() *common.Text {
	if x != nil {
		return x.TotalBenefitsEarned
	}
	return nil
}

func (x *MonthlyRewardEarnedView) GetTotalBenefitsEarnedValue() *common.Text {
	if x != nil {
		return x.TotalBenefitsEarnedValue
	}
	return nil
}

func (x *MonthlyRewardEarnedView) GetBenefitBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BenefitBackgroundColour
	}
	return nil
}

func (x *MonthlyRewardEarnedView) GetBenefitCardItems() []*MonthlyRewardEarnedView_BenefitCardItem {
	if x != nil {
		return x.BenefitCardItems
	}
	return nil
}

type RecordComponentShownToActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *RecordComponentShownToActorResponse) Reset() {
	*x = RecordComponentShownToActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordComponentShownToActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordComponentShownToActorResponse) ProtoMessage() {}

func (x *RecordComponentShownToActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordComponentShownToActorResponse.ProtoReflect.Descriptor instead.
func (*RecordComponentShownToActorResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{6}
}

func (x *RecordComponentShownToActorResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetTieringLaunchInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header for frontend rpc's
	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetTieringLaunchInfoRequest) Reset() {
	*x = GetTieringLaunchInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTieringLaunchInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTieringLaunchInfoRequest) ProtoMessage() {}

func (x *GetTieringLaunchInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTieringLaunchInfoRequest.ProtoReflect.Descriptor instead.
func (*GetTieringLaunchInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetTieringLaunchInfoRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetTieringLaunchInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// response header common across frontend rpc's
	// this has the status and error view
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Flag to denote whether tiering as a feature is enabled/disabled
	// In case tiering is disabled client will not show anything related to tiering to the user
	IsTieringEnabled common.BooleanEnum `protobuf:"varint,1,opt,name=is_tiering_enabled,json=isTieringEnabled,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_tiering_enabled,omitempty"`
	// Play the animation only after an inactivity of these many seconds
	LaunchAnimationInactivitySeconds int32 `protobuf:"varint,2,opt,name=launch_animation_inactivity_seconds,json=launchAnimationInactivitySeconds,proto3" json:"launch_animation_inactivity_seconds,omitempty"`
	// tier introduction deeplink with launch animation lottie file
	TierIntroductionDeeplink *deeplink.Deeplink `protobuf:"bytes,3,opt,name=tier_introduction_deeplink,json=tierIntroductionDeeplink,proto3" json:"tier_introduction_deeplink,omitempty"`
	// To enable disable animation from backend
	IsLaunchAnimationEnabled common.BooleanEnum `protobuf:"varint,4,opt,name=is_launch_animation_enabled,json=isLaunchAnimationEnabled,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_launch_animation_enabled,omitempty"`
	// identifier will be used by client to persist this and have logic regarding showing tier intro
	// Will be a plain string passed
	Identifier string `protobuf:"bytes,5,opt,name=identifier,proto3" json:"identifier,omitempty"`
}

func (x *GetTieringLaunchInfoResponse) Reset() {
	*x = GetTieringLaunchInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTieringLaunchInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTieringLaunchInfoResponse) ProtoMessage() {}

func (x *GetTieringLaunchInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTieringLaunchInfoResponse.ProtoReflect.Descriptor instead.
func (*GetTieringLaunchInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetTieringLaunchInfoResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetTieringLaunchInfoResponse) GetIsTieringEnabled() common.BooleanEnum {
	if x != nil {
		return x.IsTieringEnabled
	}
	return common.BooleanEnum(0)
}

func (x *GetTieringLaunchInfoResponse) GetLaunchAnimationInactivitySeconds() int32 {
	if x != nil {
		return x.LaunchAnimationInactivitySeconds
	}
	return 0
}

func (x *GetTieringLaunchInfoResponse) GetTierIntroductionDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.TierIntroductionDeeplink
	}
	return nil
}

func (x *GetTieringLaunchInfoResponse) GetIsLaunchAnimationEnabled() common.BooleanEnum {
	if x != nil {
		return x.IsLaunchAnimationEnabled
	}
	return common.BooleanEnum(0)
}

func (x *GetTieringLaunchInfoResponse) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

type GetDeeplinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// screen for which deeplink need to be shared
	Screen deeplink.Screen `protobuf:"varint,2,opt,name=screen,proto3,enum=frontend.deeplink.Screen" json:"screen,omitempty"`
}

func (x *GetDeeplinkRequest) Reset() {
	*x = GetDeeplinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeeplinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeeplinkRequest) ProtoMessage() {}

func (x *GetDeeplinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeeplinkRequest.ProtoReflect.Descriptor instead.
func (*GetDeeplinkRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetDeeplinkRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetDeeplinkRequest) GetScreen() deeplink.Screen {
	if x != nil {
		return x.Screen
	}
	return deeplink.Screen(0)
}

type GetDeeplinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink for the requested screen
	// this must contain the screen options of the corresponding link screen if any
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *GetDeeplinkResponse) Reset() {
	*x = GetDeeplinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeeplinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeeplinkResponse) ProtoMessage() {}

func (x *GetDeeplinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeeplinkResponse.ProtoReflect.Descriptor instead.
func (*GetDeeplinkResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetDeeplinkResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetDeeplinkResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type UpgradeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *UpgradeRequest) Reset() {
	*x = UpgradeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeRequest) ProtoMessage() {}

func (x *UpgradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeRequest.ProtoReflect.Descriptor instead.
func (*UpgradeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpgradeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type UpgradeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Deeplink   *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *UpgradeResponse) Reset() {
	*x = UpgradeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeResponse) ProtoMessage() {}

func (x *UpgradeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeResponse.ProtoReflect.Descriptor instead.
func (*UpgradeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpgradeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *UpgradeResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type GetTierAllPlansRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// UI context from where the user came to the flow
	UiContext string `protobuf:"bytes,2,opt,name=ui_context,json=uiContext,proto3" json:"ui_context,omitempty"`
}

func (x *GetTierAllPlansRequest) Reset() {
	*x = GetTierAllPlansRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierAllPlansRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierAllPlansRequest) ProtoMessage() {}

func (x *GetTierAllPlansRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierAllPlansRequest.ProtoReflect.Descriptor instead.
func (*GetTierAllPlansRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetTierAllPlansRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetTierAllPlansRequest) GetUiContext() string {
	if x != nil {
		return x.UiContext
	}
	return ""
}

type GetTierAllPlansResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink for TIER_ALL_PLANS
	TierAllPlan *deeplink.Deeplink `protobuf:"bytes,2,opt,name=tier_all_plan,json=tierAllPlan,proto3" json:"tier_all_plan,omitempty"`
}

func (x *GetTierAllPlansResponse) Reset() {
	*x = GetTierAllPlansResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierAllPlansResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierAllPlansResponse) ProtoMessage() {}

func (x *GetTierAllPlansResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierAllPlansResponse.ProtoReflect.Descriptor instead.
func (*GetTierAllPlansResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetTierAllPlansResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetTierAllPlansResponse) GetTierAllPlan() *deeplink.Deeplink {
	if x != nil {
		return x.TierAllPlan
	}
	return nil
}

type GetTierEarnedBenefitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// It will have a list of all string of benefit type that need to be refreshed.
	// will be empty to fetch all the components
	// benefit type:
	// - BENEFITS_TYPE_DEBIT_CARD : for retrying debit card benefits
	// - BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW : for retrying upgrade benefit
	// - BENEFITS_TYPE_MORE_INFO_VIEW :- for retrying more info view benefits
	// - BENEFITS_TYPE_MONTHLY_VIEW :- for retrying monthly view benefits
	// - BENEFITS_TYPE_OTHER :- for retrying other benefits
	BenefitType []string `protobuf:"bytes,2,rep,name=benefit_type,json=benefitType,proto3" json:"benefit_type,omitempty"`
}

func (x *GetTierEarnedBenefitsRequest) Reset() {
	*x = GetTierEarnedBenefitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierEarnedBenefitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierEarnedBenefitsRequest) ProtoMessage() {}

func (x *GetTierEarnedBenefitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierEarnedBenefitsRequest.ProtoReflect.Descriptor instead.
func (*GetTierEarnedBenefitsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetTierEarnedBenefitsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetTierEarnedBenefitsRequest) GetBenefitType() []string {
	if x != nil {
		return x.BenefitType
	}
	return nil
}

type GetTierEarnedBenefitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// screen options to load header and shimmer
	ScreenOptions *tiering.EarnedBenefitScreenOptions `protobuf:"bytes,2,opt,name=screen_options,json=screenOptions,proto3" json:"screen_options,omitempty"`
	// total benefit title view
	// ex: TOTAL BENEFITS EARNED
	TotalBenefitEarned *ui.IconTextComponent `protobuf:"bytes,3,opt,name=total_benefit_earned,json=totalBenefitEarned,proto3" json:"total_benefit_earned,omitempty"`
	// Value of total benefit
	// ex: [Icon] totalBenefitAmount [Icon]
	TotalBenefitEarnedValue *ui.IconTextComponent `protobuf:"bytes,4,opt,name=total_benefit_earned_value,json=totalBenefitEarnedValue,proto3" json:"total_benefit_earned_value,omitempty"`
	//Optional:- Warning banner
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	WarningCounterView *WarningView `protobuf:"bytes,5,opt,name=warning_counter_view,json=warningCounterView,proto3" json:"warning_counter_view,omitempty"`
	// Total Reward earned by user in a month
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	MonthlyBenefitView *MonthlyBenefitView `protobuf:"bytes,6,opt,name=monthly_benefit_view,json=monthlyBenefitView,proto3" json:"monthly_benefit_view,omitempty"`
	// List of all reward option on the page
	BenefitOptions []*BenefitsOptions `protobuf:"bytes,7,rep,name=benefit_options,json=benefitOptions,proto3" json:"benefit_options,omitempty"`
	// Users current tier for events purpose.
	TierIdentifier string `protobuf:"bytes,8,opt,name=tier_identifier,json=tierIdentifier,proto3" json:"tier_identifier,omitempty"`
	// no popup is shown if celebration_popup is nil
	// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-7332&mode=design&t=C8MVAZ1k7PtG8bnN-4
	CelebrationPopup *deeplink.Deeplink `protobuf:"bytes,9,opt,name=celebration_popup,json=celebrationPopup,proto3" json:"celebration_popup,omitempty"`
	// duration for which pop up will not be shown after user dismisses the popup
	// default will be 0 and user will see the popup every time
	CelebrationPopupCooloffDuration *durationpb.Duration `protobuf:"bytes,10,opt,name=celebration_popup_cooloff_duration,json=celebrationPopupCooloffDuration,proto3" json:"celebration_popup_cooloff_duration,omitempty"`
	// cache key for the popup. used for persisting whether to show the celebration popup
	PopupCacheKey string `protobuf:"bytes,11,opt,name=popup_cache_key,json=popupCacheKey,proto3" json:"popup_cache_key,omitempty"`
}

func (x *GetTierEarnedBenefitsResponse) Reset() {
	*x = GetTierEarnedBenefitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierEarnedBenefitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierEarnedBenefitsResponse) ProtoMessage() {}

func (x *GetTierEarnedBenefitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierEarnedBenefitsResponse.ProtoReflect.Descriptor instead.
func (*GetTierEarnedBenefitsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetTierEarnedBenefitsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetScreenOptions() *tiering.EarnedBenefitScreenOptions {
	if x != nil {
		return x.ScreenOptions
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetTotalBenefitEarned() *ui.IconTextComponent {
	if x != nil {
		return x.TotalBenefitEarned
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetTotalBenefitEarnedValue() *ui.IconTextComponent {
	if x != nil {
		return x.TotalBenefitEarnedValue
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *GetTierEarnedBenefitsResponse) GetWarningCounterView() *WarningView {
	if x != nil {
		return x.WarningCounterView
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *GetTierEarnedBenefitsResponse) GetMonthlyBenefitView() *MonthlyBenefitView {
	if x != nil {
		return x.MonthlyBenefitView
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetBenefitOptions() []*BenefitsOptions {
	if x != nil {
		return x.BenefitOptions
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetTierIdentifier() string {
	if x != nil {
		return x.TierIdentifier
	}
	return ""
}

func (x *GetTierEarnedBenefitsResponse) GetCelebrationPopup() *deeplink.Deeplink {
	if x != nil {
		return x.CelebrationPopup
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetCelebrationPopupCooloffDuration() *durationpb.Duration {
	if x != nil {
		return x.CelebrationPopupCooloffDuration
	}
	return nil
}

func (x *GetTierEarnedBenefitsResponse) GetPopupCacheKey() string {
	if x != nil {
		return x.PopupCacheKey
	}
	return ""
}

type BenefitsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Top title for the view
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// corner radius for benefits view
	CornerRadius uint32 `protobuf:"varint,2,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// renders the component in greyscale
	ShouldGrayscale bool `protobuf:"varint,3,opt,name=should_grayscale,json=shouldGrayscale,proto3" json:"should_grayscale,omitempty"`
	// backgroundColour for benefits view
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,4,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// benefit type: frontend.tiering.BenefitsType.String()
	// - BENEFITS_TYPE_DEBIT_CARD
	// - BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW
	// - BENEFITS_TYPE_MORE_INFO_VIEW
	// - BENEFITS_TYPE_OTHER
	BenefitType string `protobuf:"bytes,5,opt,name=benefit_type,json=benefitType,proto3" json:"benefit_type,omitempty"`
	// the benefit option with higher priority is shown at the top of the screen
	// [used by backend for ordering the list of benefit options before sending it to client]
	Priority uint32 `protobuf:"varint,13,opt,name=priority,proto3" json:"priority,omitempty"`
	// Types that are assignable to Option:
	//	*BenefitsOptions_ActiveStateListView
	//	*BenefitsOptions_InActiveStateListView
	//	*BenefitsOptions_UpgradeBenefits
	//	*BenefitsOptions_MoreBenefits
	//	*BenefitsOptions_RetryView
	//	*BenefitsOptions_TransferSalaryView
	//	*BenefitsOptions_MonthlyBenefitView
	//	*BenefitsOptions_WarningView
	//	*BenefitsOptions_RewardView
	Option isBenefitsOptions_Option `protobuf_oneof:"option"`
}

func (x *BenefitsOptions) Reset() {
	*x = BenefitsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitsOptions) ProtoMessage() {}

func (x *BenefitsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitsOptions.ProtoReflect.Descriptor instead.
func (*BenefitsOptions) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{17}
}

func (x *BenefitsOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BenefitsOptions) GetCornerRadius() uint32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *BenefitsOptions) GetShouldGrayscale() bool {
	if x != nil {
		return x.ShouldGrayscale
	}
	return false
}

func (x *BenefitsOptions) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *BenefitsOptions) GetBenefitType() string {
	if x != nil {
		return x.BenefitType
	}
	return ""
}

func (x *BenefitsOptions) GetPriority() uint32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (m *BenefitsOptions) GetOption() isBenefitsOptions_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *BenefitsOptions) GetActiveStateListView() *ActiveStateListView {
	if x, ok := x.GetOption().(*BenefitsOptions_ActiveStateListView); ok {
		return x.ActiveStateListView
	}
	return nil
}

func (x *BenefitsOptions) GetInActiveStateListView() *InActiveStateListView {
	if x, ok := x.GetOption().(*BenefitsOptions_InActiveStateListView); ok {
		return x.InActiveStateListView
	}
	return nil
}

func (x *BenefitsOptions) GetUpgradeBenefits() *UpgradeBenefitsView {
	if x, ok := x.GetOption().(*BenefitsOptions_UpgradeBenefits); ok {
		return x.UpgradeBenefits
	}
	return nil
}

func (x *BenefitsOptions) GetMoreBenefits() *MoreInfoView {
	if x, ok := x.GetOption().(*BenefitsOptions_MoreBenefits); ok {
		return x.MoreBenefits
	}
	return nil
}

func (x *BenefitsOptions) GetRetryView() *RetryView {
	if x, ok := x.GetOption().(*BenefitsOptions_RetryView); ok {
		return x.RetryView
	}
	return nil
}

func (x *BenefitsOptions) GetTransferSalaryView() *TransferSalaryView {
	if x, ok := x.GetOption().(*BenefitsOptions_TransferSalaryView); ok {
		return x.TransferSalaryView
	}
	return nil
}

func (x *BenefitsOptions) GetMonthlyBenefitView() *MonthlyBenefitView {
	if x, ok := x.GetOption().(*BenefitsOptions_MonthlyBenefitView); ok {
		return x.MonthlyBenefitView
	}
	return nil
}

func (x *BenefitsOptions) GetWarningView() *WarningView {
	if x, ok := x.GetOption().(*BenefitsOptions_WarningView); ok {
		return x.WarningView
	}
	return nil
}

func (x *BenefitsOptions) GetRewardView() *RewardView {
	if x, ok := x.GetOption().(*BenefitsOptions_RewardView); ok {
		return x.RewardView
	}
	return nil
}

type isBenefitsOptions_Option interface {
	isBenefitsOptions_Option()
}

type BenefitsOptions_ActiveStateListView struct {
	// List of benefits with zero / non - zero stats
	// Figma:- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112531&mode=design&t=gh3sCRk4mMBlAMvK-4
	// Figma:- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112490&mode=design&t=<gh3sCRk4mMBlAMvK-4> </gh3sCRk4mMBlAMvK-4>
	// Figma : https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-8462&mode=design&t=8UxlG4zMJ5MiBaO4-4
	// Figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20975-50543&mode=design&t=hUIpI3dWiLLsDMfm-4
	ActiveStateListView *ActiveStateListView `protobuf:"bytes,6,opt,name=active_state_list_view,json=activeStateListView,proto3,oneof"`
}

type BenefitsOptions_InActiveStateListView struct {
	InActiveStateListView *InActiveStateListView `protobuf:"bytes,7,opt,name=in_active_state_list_view,json=inActiveStateListView,proto3,oneof"`
}

type BenefitsOptions_UpgradeBenefits struct {
	UpgradeBenefits *UpgradeBenefitsView `protobuf:"bytes,8,opt,name=upgrade_benefits,json=upgradeBenefits,proto3,oneof"`
}

type BenefitsOptions_MoreBenefits struct {
	MoreBenefits *MoreInfoView `protobuf:"bytes,9,opt,name=more_benefits,json=moreBenefits,proto3,oneof"`
}

type BenefitsOptions_RetryView struct {
	RetryView *RetryView `protobuf:"bytes,10,opt,name=retry_view,json=retryView,proto3,oneof"`
}

type BenefitsOptions_TransferSalaryView struct {
	TransferSalaryView *TransferSalaryView `protobuf:"bytes,11,opt,name=transfer_salary_view,json=transferSalaryView,proto3,oneof"`
}

type BenefitsOptions_MonthlyBenefitView struct {
	MonthlyBenefitView *MonthlyBenefitView `protobuf:"bytes,12,opt,name=monthly_benefit_view,json=monthlyBenefitView,proto3,oneof"`
}

type BenefitsOptions_WarningView struct {
	WarningView *WarningView `protobuf:"bytes,14,opt,name=warning_view,json=warningView,proto3,oneof"`
}

type BenefitsOptions_RewardView struct {
	RewardView *RewardView `protobuf:"bytes,15,opt,name=reward_view,json=rewardView,proto3,oneof"`
}

func (*BenefitsOptions_ActiveStateListView) isBenefitsOptions_Option() {}

func (*BenefitsOptions_InActiveStateListView) isBenefitsOptions_Option() {}

func (*BenefitsOptions_UpgradeBenefits) isBenefitsOptions_Option() {}

func (*BenefitsOptions_MoreBenefits) isBenefitsOptions_Option() {}

func (*BenefitsOptions_RetryView) isBenefitsOptions_Option() {}

func (*BenefitsOptions_TransferSalaryView) isBenefitsOptions_Option() {}

func (*BenefitsOptions_MonthlyBenefitView) isBenefitsOptions_Option() {}

func (*BenefitsOptions_WarningView) isBenefitsOptions_Option() {}

func (*BenefitsOptions_RewardView) isBenefitsOptions_Option() {}

// Figma: https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10218-144968&t=TjMOuYP2DfC6NzfV-4
type TransferSalaryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeaderView *TitleView `protobuf:"bytes,1,opt,name=header_view,json=headerView,proto3" json:"header_view,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	Amount                  *common.Text             `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	EditAmount              *ui.IconTextComponent    `protobuf:"bytes,3,opt,name=edit_amount,json=editAmount,proto3" json:"edit_amount,omitempty"`
	RightCta                *ui.IconTextComponent    `protobuf:"bytes,4,opt,name=right_cta,json=rightCta,proto3" json:"right_cta,omitempty"`
	InfoView                *ui.IconTextComponent    `protobuf:"bytes,5,opt,name=info_view,json=infoView,proto3" json:"info_view,omitempty"`
	CornerRadius            uint32                   `protobuf:"varint,6,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	BackgroundColour        *widget.BackgroundColour `protobuf:"bytes,7,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	AmountIconTextComponent *ui.IconTextComponent    `protobuf:"bytes,8,opt,name=amount_icon_text_component,json=amountIconTextComponent,proto3" json:"amount_icon_text_component,omitempty"`
}

func (x *TransferSalaryView) Reset() {
	*x = TransferSalaryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferSalaryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferSalaryView) ProtoMessage() {}

func (x *TransferSalaryView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferSalaryView.ProtoReflect.Descriptor instead.
func (*TransferSalaryView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{18}
}

func (x *TransferSalaryView) GetHeaderView() *TitleView {
	if x != nil {
		return x.HeaderView
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *TransferSalaryView) GetAmount() *common.Text {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *TransferSalaryView) GetEditAmount() *ui.IconTextComponent {
	if x != nil {
		return x.EditAmount
	}
	return nil
}

func (x *TransferSalaryView) GetRightCta() *ui.IconTextComponent {
	if x != nil {
		return x.RightCta
	}
	return nil
}

func (x *TransferSalaryView) GetInfoView() *ui.IconTextComponent {
	if x != nil {
		return x.InfoView
	}
	return nil
}

func (x *TransferSalaryView) GetCornerRadius() uint32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *TransferSalaryView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *TransferSalaryView) GetAmountIconTextComponent() *ui.IconTextComponent {
	if x != nil {
		return x.AmountIconTextComponent
	}
	return nil
}

// Figma:-  https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-8462&mode=design&t=8UxlG4zMJ5MiBaO4-4
type UpgradeBenefitsView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Same transactions, higher rewards
	ComponentTitle *ui.IconTextComponent `protobuf:"bytes,1,opt,name=component_title,json=componentTitle,proto3" json:"component_title,omitempty"`
	// Left card
	LeftCard *UpgradeBenefitsView_Card `protobuf:"bytes,2,opt,name=left_card,json=leftCard,proto3" json:"left_card,omitempty"`
	// Right card
	RightCard *UpgradeBenefitsView_Card `protobuf:"bytes,3,opt,name=right_card,json=rightCard,proto3" json:"right_card,omitempty"`
	// VS text
	VsItc *ui.IconTextComponent `protobuf:"bytes,4,opt,name=vs_itc,json=vsItc,proto3" json:"vs_itc,omitempty"`
	// Upgrade now
	Cta *ui.IconTextComponent `protobuf:"bytes,5,opt,name=cta,proto3" json:"cta,omitempty"`
	// Higher tier user want to upgrade to.
	TierIdentifier string `protobuf:"bytes,8,opt,name=tier_identifier,json=tierIdentifier,proto3" json:"tier_identifier,omitempty"`
}

func (x *UpgradeBenefitsView) Reset() {
	*x = UpgradeBenefitsView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeBenefitsView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeBenefitsView) ProtoMessage() {}

func (x *UpgradeBenefitsView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeBenefitsView.ProtoReflect.Descriptor instead.
func (*UpgradeBenefitsView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpgradeBenefitsView) GetComponentTitle() *ui.IconTextComponent {
	if x != nil {
		return x.ComponentTitle
	}
	return nil
}

func (x *UpgradeBenefitsView) GetLeftCard() *UpgradeBenefitsView_Card {
	if x != nil {
		return x.LeftCard
	}
	return nil
}

func (x *UpgradeBenefitsView) GetRightCard() *UpgradeBenefitsView_Card {
	if x != nil {
		return x.RightCard
	}
	return nil
}

func (x *UpgradeBenefitsView) GetVsItc() *ui.IconTextComponent {
	if x != nil {
		return x.VsItc
	}
	return nil
}

func (x *UpgradeBenefitsView) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *UpgradeBenefitsView) GetTierIdentifier() string {
	if x != nil {
		return x.TierIdentifier
	}
	return ""
}

// Figma:- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44066&mode=design&t=gh3sCRk4mMBlAMvK-4
type MoreInfoView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of more benefits for CTA
	IconTextComponents []*ui.IconTextComponent `protobuf:"bytes,1,rep,name=icon_text_components,json=iconTextComponents,proto3" json:"icon_text_components,omitempty"`
}

func (x *MoreInfoView) Reset() {
	*x = MoreInfoView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoreInfoView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoreInfoView) ProtoMessage() {}

func (x *MoreInfoView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoreInfoView.ProtoReflect.Descriptor instead.
func (*MoreInfoView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{20}
}

func (x *MoreInfoView) GetIconTextComponents() []*ui.IconTextComponent {
	if x != nil {
		return x.IconTextComponents
	}
	return nil
}

// Figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44385&mode=design&t=B7odRS6EUTFnulAi-4
type RetryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [icon]
	Icon *common.VisualElement `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// Retry
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Retry cta [No deeplink expected]
	Cta *ui.IconTextComponent `protobuf:"bytes,3,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *RetryView) Reset() {
	*x = RetryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryView) ProtoMessage() {}

func (x *RetryView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryView.ProtoReflect.Descriptor instead.
func (*RetryView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{21}
}

func (x *RetryView) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *RetryView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *RetryView) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

type TitleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Month (ex: January rewards)
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Title background color
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Right info icon
	// on tap it should move to info deeplink
	RightIcon *ui.IconTextComponent `protobuf:"bytes,3,opt,name=right_icon,json=rightIcon,proto3" json:"right_icon,omitempty"`
}

func (x *TitleView) Reset() {
	*x = TitleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TitleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TitleView) ProtoMessage() {}

func (x *TitleView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TitleView.ProtoReflect.Descriptor instead.
func (*TitleView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{22}
}

func (x *TitleView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *TitleView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *TitleView) GetRightIcon() *ui.IconTextComponent {
	if x != nil {
		return x.RightIcon
	}
	return nil
}

// Figma https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1221-24019&mode=design&t=MH9sve3mUacfqMPW-4
// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1221-24275&mode=design&t=MH9sve3mUacfqMPW-4
type MonthlyBenefitView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonthlyBenefits *MonthlyBenefitView_MonthlyBenefits `protobuf:"bytes,1,opt,name=monthly_benefits,json=monthlyBenefits,proto3" json:"monthly_benefits,omitempty"`
	// benefit type: (value can only be `BENEFITS_TYPE_MONTHLY_VIEW`)
	// - BENEFITS_TYPE_MONTHLY_VIEW
	BenefitType string `protobuf:"bytes,2,opt,name=benefit_type,json=benefitType,proto3" json:"benefit_type,omitempty"`
}

func (x *MonthlyBenefitView) Reset() {
	*x = MonthlyBenefitView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView) ProtoMessage() {}

func (x *MonthlyBenefitView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23}
}

func (x *MonthlyBenefitView) GetMonthlyBenefits() *MonthlyBenefitView_MonthlyBenefits {
	if x != nil {
		return x.MonthlyBenefits
	}
	return nil
}

func (x *MonthlyBenefitView) GetBenefitType() string {
	if x != nil {
		return x.BenefitType
	}
	return ""
}

type WarningView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to LeftView:
	//	*WarningView_WarningCounterView_
	//	*WarningView_VisualElement
	LeftView isWarningView_LeftView `protobuf_oneof:"left_view"`
	// Title for the view
	// Example :- Plus Plan expires soon! Add ₹8,000 to continue enjoying benefits.
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// subtitle for the warning view
	Subtitle *common.Text `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// Right icon for component (ex: chevron icon)
	RightIcon *common.VisualElement `protobuf:"bytes,5,opt,name=right_icon,json=rightIcon,proto3" json:"right_icon,omitempty"`
	// Background color for whole component
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,6,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Example: 20px
	CornerRadius uint32 `protobuf:"varint,7,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// to navigate on click of warning view
	Deeplink *deeplink.Deeplink `protobuf:"bytes,8,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// right text component
	RightTextComponent *ui.IconTextComponent `protobuf:"bytes,9,opt,name=right_text_component,json=rightTextComponent,proto3" json:"right_text_component,omitempty"`
}

func (x *WarningView) Reset() {
	*x = WarningView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarningView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarningView) ProtoMessage() {}

func (x *WarningView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarningView.ProtoReflect.Descriptor instead.
func (*WarningView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{24}
}

func (m *WarningView) GetLeftView() isWarningView_LeftView {
	if m != nil {
		return m.LeftView
	}
	return nil
}

func (x *WarningView) GetWarningCounterView() *WarningView_WarningCounterView {
	if x, ok := x.GetLeftView().(*WarningView_WarningCounterView_); ok {
		return x.WarningCounterView
	}
	return nil
}

func (x *WarningView) GetVisualElement() *common.VisualElement {
	if x, ok := x.GetLeftView().(*WarningView_VisualElement); ok {
		return x.VisualElement
	}
	return nil
}

func (x *WarningView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *WarningView) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *WarningView) GetRightIcon() *common.VisualElement {
	if x != nil {
		return x.RightIcon
	}
	return nil
}

func (x *WarningView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *WarningView) GetCornerRadius() uint32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *WarningView) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *WarningView) GetRightTextComponent() *ui.IconTextComponent {
	if x != nil {
		return x.RightTextComponent
	}
	return nil
}

type isWarningView_LeftView interface {
	isWarningView_LeftView()
}

type WarningView_WarningCounterView_ struct {
	WarningCounterView *WarningView_WarningCounterView `protobuf:"bytes,1,opt,name=warning_counter_view,json=warningCounterView,proto3,oneof"`
}

type WarningView_VisualElement struct {
	// ex warning icon
	VisualElement *common.VisualElement `protobuf:"bytes,2,opt,name=visual_element,json=visualElement,proto3,oneof"`
}

func (*WarningView_WarningCounterView_) isWarningView_LeftView() {}

func (*WarningView_VisualElement) isWarningView_LeftView() {}

// Figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112385&mode=design&t=gh3sCRk4mMBlAMvK-4
type InActiveStateListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//[ICON] header icon for the view
	TopIcon *common.VisualElement `protobuf:"bytes,1,opt,name=top_icon,json=topIcon,proto3" json:"top_icon,omitempty"`
	//Ex: Save more with Fi-Federal Debit Card
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Ex: On spends up to ₹30k/month
	SubTitle *common.Text `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// Cta to move to next screen
	// Ex: On spends up to ₹30k/month
	Action *ui.IconTextComponent `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	// Container view with list of benefits
	ContainerView *InActiveStateListView_ContainerView `protobuf:"bytes,5,opt,name=container_view,json=containerView,proto3" json:"container_view,omitempty"`
}

func (x *InActiveStateListView) Reset() {
	*x = InActiveStateListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InActiveStateListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InActiveStateListView) ProtoMessage() {}

func (x *InActiveStateListView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InActiveStateListView.ProtoReflect.Descriptor instead.
func (*InActiveStateListView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{25}
}

func (x *InActiveStateListView) GetTopIcon() *common.VisualElement {
	if x != nil {
		return x.TopIcon
	}
	return nil
}

func (x *InActiveStateListView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *InActiveStateListView) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *InActiveStateListView) GetAction() *ui.IconTextComponent {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *InActiveStateListView) GetContainerView() *InActiveStateListView_ContainerView {
	if x != nil {
		return x.ContainerView
	}
	return nil
}

// FIGMA: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112543&mode=design&t=gh3sCRk4mMBlAMvK-4
type ActiveStateListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of benefits
	BenefitList []*ActiveStateListView_BenefitList `protobuf:"bytes,1,rep,name=benefit_list,json=benefitList,proto3" json:"benefit_list,omitempty"`
	// Info banner on bottom
	// [Icon] Explore debit card offers [Icon]
	Info *ui.IconTextComponent `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *ActiveStateListView) Reset() {
	*x = ActiveStateListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActiveStateListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveStateListView) ProtoMessage() {}

func (x *ActiveStateListView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveStateListView.ProtoReflect.Descriptor instead.
func (*ActiveStateListView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{26}
}

func (x *ActiveStateListView) GetBenefitList() []*ActiveStateListView_BenefitList {
	if x != nil {
		return x.BenefitList
	}
	return nil
}

func (x *ActiveStateListView) GetInfo() *ui.IconTextComponent {
	if x != nil {
		return x.Info
	}
	return nil
}

type GetTierFlowScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req      *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	MetaData string                `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *GetTierFlowScreenRequest) Reset() {
	*x = GetTierFlowScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierFlowScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierFlowScreenRequest) ProtoMessage() {}

func (x *GetTierFlowScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierFlowScreenRequest.ProtoReflect.Descriptor instead.
func (*GetTierFlowScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetTierFlowScreenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetTierFlowScreenRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

type GetTierFlowScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink for the requested screen
	// this must contain the screen options of the corresponding link screen if any
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *GetTierFlowScreenResponse) Reset() {
	*x = GetTierFlowScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierFlowScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierFlowScreenResponse) ProtoMessage() {}

func (x *GetTierFlowScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierFlowScreenResponse.ProtoReflect.Descriptor instead.
func (*GetTierFlowScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetTierFlowScreenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetTierFlowScreenResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type GetTierAllPlansV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req      *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	MetaData string                `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *GetTierAllPlansV2Request) Reset() {
	*x = GetTierAllPlansV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierAllPlansV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierAllPlansV2Request) ProtoMessage() {}

func (x *GetTierAllPlansV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierAllPlansV2Request.ProtoReflect.Descriptor instead.
func (*GetTierAllPlansV2Request) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetTierAllPlansV2Request) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetTierAllPlansV2Request) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

type GetTierAllPlansV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Title of the page
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// tier plan to focus on in all tier plans
	// this will be an index for a tier plan in list of tier plans
	TierIndexToFocus int32       `protobuf:"varint,3,opt,name=tier_index_to_focus,json=tierIndexToFocus,proto3" json:"tier_index_to_focus,omitempty"`
	TierPlans        []*TierPlan `protobuf:"bytes,4,rep,name=tier_plans,json=tierPlans,proto3" json:"tier_plans,omitempty"`
}

func (x *GetTierAllPlansV2Response) Reset() {
	*x = GetTierAllPlansV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierAllPlansV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierAllPlansV2Response) ProtoMessage() {}

func (x *GetTierAllPlansV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierAllPlansV2Response.ProtoReflect.Descriptor instead.
func (*GetTierAllPlansV2Response) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetTierAllPlansV2Response) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetTierAllPlansV2Response) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GetTierAllPlansV2Response) GetTierIndexToFocus() int32 {
	if x != nil {
		return x.TierIndexToFocus
	}
	return 0
}

func (x *GetTierAllPlansV2Response) GetTierPlans() []*TierPlan {
	if x != nil {
		return x.TierPlans
	}
	return nil
}

type TierPlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Gradient background color
	HeaderBackgroundColor *widget.BackgroundColour `protobuf:"bytes,1,opt,name=header_background_color,json=headerBackgroundColor,proto3" json:"header_background_color,omitempty"`
	// Header overlay background color
	HeaderOverlayBackgroundColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=header_overlay_background_color,json=headerOverlayBackgroundColor,proto3" json:"header_overlay_background_color,omitempty"`
	// Content background color
	ContentBackgroundColor *widget.BackgroundColour `protobuf:"bytes,3,opt,name=content_background_color,json=contentBackgroundColor,proto3" json:"content_background_color,omitempty"`
	// Plan Icon
	Icon *common.VisualElement `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	// Icon used for swipe action
	// Ref : https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10002-56105&m=dev
	// We are keeping it single at client we will decide which icon to show based on the tier plan
	SwipeIcon    *common.VisualElement `protobuf:"bytes,5,opt,name=swipe_icon,json=swipeIcon,proto3" json:"swipe_icon,omitempty"`
	PlanCard     *PlanCard             `protobuf:"bytes,6,opt,name=plan_card,json=planCard,proto3" json:"plan_card,omitempty"`
	PlanBenefits []*PlanBenefit        `protobuf:"bytes,7,rep,name=plan_benefits,json=planBenefits,proto3" json:"plan_benefits,omitempty"`
	// Next action cta
	Cta *deeplink.Cta `protobuf:"bytes,8,opt,name=cta,proto3" json:"cta,omitempty"`
	// Toolbar right image
	ToolbarRightImage *common.VisualElement `protobuf:"bytes,9,opt,name=toolbar_right_image,json=toolbarRightImage,proto3" json:"toolbar_right_image,omitempty"`
	// Toolbar right image deeplink
	ToolbarRightImageDeeplink *deeplink.Deeplink `protobuf:"bytes,10,opt,name=toolbar_right_image_deeplink,json=toolbarRightImageDeeplink,proto3" json:"toolbar_right_image_deeplink,omitempty"`
	// Metadata for plan which is rendered.
	PlanMeta string `protobuf:"bytes,11,opt,name=plan_meta,json=planMeta,proto3" json:"plan_meta,omitempty"`
}

func (x *TierPlan) Reset() {
	*x = TierPlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierPlan) ProtoMessage() {}

func (x *TierPlan) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierPlan.ProtoReflect.Descriptor instead.
func (*TierPlan) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{31}
}

func (x *TierPlan) GetHeaderBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.HeaderBackgroundColor
	}
	return nil
}

func (x *TierPlan) GetHeaderOverlayBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.HeaderOverlayBackgroundColor
	}
	return nil
}

func (x *TierPlan) GetContentBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.ContentBackgroundColor
	}
	return nil
}

func (x *TierPlan) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *TierPlan) GetSwipeIcon() *common.VisualElement {
	if x != nil {
		return x.SwipeIcon
	}
	return nil
}

func (x *TierPlan) GetPlanCard() *PlanCard {
	if x != nil {
		return x.PlanCard
	}
	return nil
}

func (x *TierPlan) GetPlanBenefits() []*PlanBenefit {
	if x != nil {
		return x.PlanBenefits
	}
	return nil
}

func (x *TierPlan) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *TierPlan) GetToolbarRightImage() *common.VisualElement {
	if x != nil {
		return x.ToolbarRightImage
	}
	return nil
}

func (x *TierPlan) GetToolbarRightImageDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.ToolbarRightImageDeeplink
	}
	return nil
}

func (x *TierPlan) GetPlanMeta() string {
	if x != nil {
		return x.PlanMeta
	}
	return ""
}

// May contain benefits of the plan or info about the plan or banners
type PlanBenefit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BackgroundColor *widget.BackgroundColour `protobuf:"bytes,1,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// Title of the info
	Title *ui.IconTextComponent `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Types that are assignable to Benefit:
	//	*PlanBenefit_BenefitCard
	//	*PlanBenefit_SmallEntryBanner
	//	*PlanBenefit_EntryBanner
	//	*PlanBenefit_InfoBanner
	//	*PlanBenefit_LearnMoreBanner
	Benefit isPlanBenefit_Benefit `protobuf_oneof:"benefit"`
	// On tap of the whole card, if deeplink is there, need to navigate
	Deeplink *deeplink.Deeplink `protobuf:"bytes,8,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Metadata for benefit which is shown on tap of this.
	BenefitMeta string `protobuf:"bytes,9,opt,name=benefit_meta,json=benefitMeta,proto3" json:"benefit_meta,omitempty"`
}

func (x *PlanBenefit) Reset() {
	*x = PlanBenefit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanBenefit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanBenefit) ProtoMessage() {}

func (x *PlanBenefit) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanBenefit.ProtoReflect.Descriptor instead.
func (*PlanBenefit) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{32}
}

func (x *PlanBenefit) GetBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColor
	}
	return nil
}

func (x *PlanBenefit) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (m *PlanBenefit) GetBenefit() isPlanBenefit_Benefit {
	if m != nil {
		return m.Benefit
	}
	return nil
}

func (x *PlanBenefit) GetBenefitCard() *BenefitCard {
	if x, ok := x.GetBenefit().(*PlanBenefit_BenefitCard); ok {
		return x.BenefitCard
	}
	return nil
}

func (x *PlanBenefit) GetSmallEntryBanner() *EntryBannerSmall {
	if x, ok := x.GetBenefit().(*PlanBenefit_SmallEntryBanner); ok {
		return x.SmallEntryBanner
	}
	return nil
}

func (x *PlanBenefit) GetEntryBanner() *EntryBanner {
	if x, ok := x.GetBenefit().(*PlanBenefit_EntryBanner); ok {
		return x.EntryBanner
	}
	return nil
}

func (x *PlanBenefit) GetInfoBanner() *InfoBanner {
	if x, ok := x.GetBenefit().(*PlanBenefit_InfoBanner); ok {
		return x.InfoBanner
	}
	return nil
}

func (x *PlanBenefit) GetLearnMoreBanner() *LearnMoreBanner {
	if x, ok := x.GetBenefit().(*PlanBenefit_LearnMoreBanner); ok {
		return x.LearnMoreBanner
	}
	return nil
}

func (x *PlanBenefit) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *PlanBenefit) GetBenefitMeta() string {
	if x != nil {
		return x.BenefitMeta
	}
	return ""
}

type isPlanBenefit_Benefit interface {
	isPlanBenefit_Benefit()
}

type PlanBenefit_BenefitCard struct {
	BenefitCard *BenefitCard `protobuf:"bytes,3,opt,name=benefit_card,json=benefitCard,proto3,oneof"`
}

type PlanBenefit_SmallEntryBanner struct {
	SmallEntryBanner *EntryBannerSmall `protobuf:"bytes,4,opt,name=small_entry_banner,json=smallEntryBanner,proto3,oneof"`
}

type PlanBenefit_EntryBanner struct {
	EntryBanner *EntryBanner `protobuf:"bytes,5,opt,name=entry_banner,json=entryBanner,proto3,oneof"`
}

type PlanBenefit_InfoBanner struct {
	InfoBanner *InfoBanner `protobuf:"bytes,6,opt,name=info_banner,json=infoBanner,proto3,oneof"`
}

type PlanBenefit_LearnMoreBanner struct {
	LearnMoreBanner *LearnMoreBanner `protobuf:"bytes,7,opt,name=learn_more_banner,json=learnMoreBanner,proto3,oneof"`
}

func (*PlanBenefit_BenefitCard) isPlanBenefit_Benefit() {}

func (*PlanBenefit_SmallEntryBanner) isPlanBenefit_Benefit() {}

func (*PlanBenefit_EntryBanner) isPlanBenefit_Benefit() {}

func (*PlanBenefit_InfoBanner) isPlanBenefit_Benefit() {}

func (*PlanBenefit_LearnMoreBanner) isPlanBenefit_Benefit() {}

type GetAMBScreenDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetAMBScreenDetailsRequest) Reset() {
	*x = GetAMBScreenDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAMBScreenDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAMBScreenDetailsRequest) ProtoMessage() {}

func (x *GetAMBScreenDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAMBScreenDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetAMBScreenDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetAMBScreenDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetAMBScreenDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Section    *sections.Section      `protobuf:"bytes,2,opt,name=section,proto3" json:"section,omitempty"`
	Title      *common.Text           `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *GetAMBScreenDetailsResponse) Reset() {
	*x = GetAMBScreenDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAMBScreenDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAMBScreenDetailsResponse) ProtoMessage() {}

func (x *GetAMBScreenDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAMBScreenDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetAMBScreenDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetAMBScreenDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetAMBScreenDetailsResponse) GetSection() *sections.Section {
	if x != nil {
		return x.Section
	}
	return nil
}

func (x *GetAMBScreenDetailsResponse) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

type GetEarnedBenefitsHistoryResponse_HeaderView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ex: [ICON] - plus tier
	PlanIcon *ui.IconTextComponent `protobuf:"bytes,1,opt,name=plan_icon,json=planIcon,proto3" json:"plan_icon,omitempty"`
	// Ex: TOTAL BENEFITS EARNED
	TotalBenefitsEarned *common.Text `protobuf:"bytes,2,opt,name=total_benefits_earned,json=totalBenefitsEarned,proto3" json:"total_benefits_earned,omitempty"`
	// EX: Amount ₹5,000
	TotalBenefitsEarnedValue *common.Text `protobuf:"bytes,3,opt,name=total_benefits_earned_value,json=totalBenefitsEarnedValue,proto3" json:"total_benefits_earned_value,omitempty"`
	// EX: How is this calculated?
	BottomText *ui.IconTextComponent `protobuf:"bytes,4,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) Reset() {
	*x = GetEarnedBenefitsHistoryResponse_HeaderView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarnedBenefitsHistoryResponse_HeaderView) ProtoMessage() {}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarnedBenefitsHistoryResponse_HeaderView.ProtoReflect.Descriptor instead.
func (*GetEarnedBenefitsHistoryResponse_HeaderView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) GetPlanIcon() *ui.IconTextComponent {
	if x != nil {
		return x.PlanIcon
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) GetTotalBenefitsEarned() *common.Text {
	if x != nil {
		return x.TotalBenefitsEarned
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) GetTotalBenefitsEarnedValue() *common.Text {
	if x != nil {
		return x.TotalBenefitsEarnedValue
	}
	return nil
}

func (x *GetEarnedBenefitsHistoryResponse_HeaderView) GetBottomText() *ui.IconTextComponent {
	if x != nil {
		return x.BottomText
	}
	return nil
}

type MonthlyRewardEarnedView_HeaderView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ex: Background color for header
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,1,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	//Ex: December 2023
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// EX: ONGOING
	Tag *ui.IconTextComponent `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *MonthlyRewardEarnedView_HeaderView) Reset() {
	*x = MonthlyRewardEarnedView_HeaderView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyRewardEarnedView_HeaderView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyRewardEarnedView_HeaderView) ProtoMessage() {}

func (x *MonthlyRewardEarnedView_HeaderView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyRewardEarnedView_HeaderView.ProtoReflect.Descriptor instead.
func (*MonthlyRewardEarnedView_HeaderView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{5, 0}
}

func (x *MonthlyRewardEarnedView_HeaderView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *MonthlyRewardEarnedView_HeaderView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *MonthlyRewardEarnedView_HeaderView) GetTag() *ui.IconTextComponent {
	if x != nil {
		return x.Tag
	}
	return nil
}

type MonthlyRewardEarnedView_BenefitCardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ex: Fi-COINS EARNED
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Ex: ₹899
	Value *ui.IconTextComponent `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *MonthlyRewardEarnedView_BenefitCardItem) Reset() {
	*x = MonthlyRewardEarnedView_BenefitCardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyRewardEarnedView_BenefitCardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyRewardEarnedView_BenefitCardItem) ProtoMessage() {}

func (x *MonthlyRewardEarnedView_BenefitCardItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyRewardEarnedView_BenefitCardItem.ProtoReflect.Descriptor instead.
func (*MonthlyRewardEarnedView_BenefitCardItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{5, 1}
}

func (x *MonthlyRewardEarnedView_BenefitCardItem) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *MonthlyRewardEarnedView_BenefitCardItem) GetValue() *ui.IconTextComponent {
	if x != nil {
		return x.Value
	}
	return nil
}

type UpgradeBenefitsView_Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ex: RECOMMENDED
	Tag *ui.IconTextComponent `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	// icon
	VisualElement *common.VisualElement `protobuf:"bytes,2,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	// ex: 8000
	Cashback *ui.IconTextComponent `protobuf:"bytes,3,opt,name=cashback,proto3" json:"cashback,omitempty"`
	// ex: Fi-Coins
	Coins *ui.IconTextComponent `protobuf:"bytes,4,opt,name=coins,proto3" json:"coins,omitempty"`
	// background color for complete card
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// corner radius
	CornerRadius int32 `protobuf:"varint,6,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// Tier identifier for events and testing purpose
	TierIdentifier string `protobuf:"bytes,7,opt,name=tier_identifier,json=tierIdentifier,proto3" json:"tier_identifier,omitempty"`
}

func (x *UpgradeBenefitsView_Card) Reset() {
	*x = UpgradeBenefitsView_Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeBenefitsView_Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeBenefitsView_Card) ProtoMessage() {}

func (x *UpgradeBenefitsView_Card) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeBenefitsView_Card.ProtoReflect.Descriptor instead.
func (*UpgradeBenefitsView_Card) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{19, 0}
}

func (x *UpgradeBenefitsView_Card) GetTag() *ui.IconTextComponent {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *UpgradeBenefitsView_Card) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *UpgradeBenefitsView_Card) GetCashback() *ui.IconTextComponent {
	if x != nil {
		return x.Cashback
	}
	return nil
}

func (x *UpgradeBenefitsView_Card) GetCoins() *ui.IconTextComponent {
	if x != nil {
		return x.Coins
	}
	return nil
}

func (x *UpgradeBenefitsView_Card) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *UpgradeBenefitsView_Card) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *UpgradeBenefitsView_Card) GetTierIdentifier() string {
	if x != nil {
		return x.TierIdentifier
	}
	return ""
}

type MonthlyBenefitView_MonthlyBenefits struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header view
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	HeaderView *MonthlyBenefitView_MonthlyBenefits_TitleView `protobuf:"bytes,1,opt,name=header_view,json=headerView,proto3" json:"header_view,omitempty"`
	// list of benefit earn by user
	MonthlyBenefitTiles []*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile `protobuf:"bytes,2,rep,name=monthly_benefit_tiles,json=monthlyBenefitTiles,proto3" json:"monthly_benefit_tiles,omitempty"`
	// bottom info title
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	InfoView *ui.IconTextComponent `protobuf:"bytes,3,opt,name=info_view,json=infoView,proto3" json:"info_view,omitempty"`
	// corner radius for complete view
	CornerRadius uint32 `protobuf:"varint,4,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// background color for complete view
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// renders the component in greyscale
	ShouldGrayscale bool `protobuf:"varint,6,opt,name=should_grayscale,json=shouldGrayscale,proto3" json:"should_grayscale,omitempty"`
	// header view
	HeaderViewV2  *TitleView                                       `protobuf:"bytes,7,opt,name=header_view_v2,json=headerViewV2,proto3" json:"header_view_v2,omitempty"`
	OfferSection  *MonthlyBenefitView_MonthlyBenefits_OfferSection `protobuf:"bytes,8,opt,name=offer_section,json=offerSection,proto3" json:"offer_section,omitempty"`
	AmbEntryPoint *ui.IconTextComponent                            `protobuf:"bytes,9,opt,name=amb_entry_point,json=ambEntryPoint,proto3" json:"amb_entry_point,omitempty"`
}

func (x *MonthlyBenefitView_MonthlyBenefits) Reset() {
	*x = MonthlyBenefitView_MonthlyBenefits{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView_MonthlyBenefits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView_MonthlyBenefits) ProtoMessage() {}

func (x *MonthlyBenefitView_MonthlyBenefits) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView_MonthlyBenefits.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView_MonthlyBenefits) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23, 0}
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *MonthlyBenefitView_MonthlyBenefits) GetHeaderView() *MonthlyBenefitView_MonthlyBenefits_TitleView {
	if x != nil {
		return x.HeaderView
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetMonthlyBenefitTiles() []*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile {
	if x != nil {
		return x.MonthlyBenefitTiles
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *MonthlyBenefitView_MonthlyBenefits) GetInfoView() *ui.IconTextComponent {
	if x != nil {
		return x.InfoView
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetCornerRadius() uint32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetShouldGrayscale() bool {
	if x != nil {
		return x.ShouldGrayscale
	}
	return false
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetHeaderViewV2() *TitleView {
	if x != nil {
		return x.HeaderViewV2
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetOfferSection() *MonthlyBenefitView_MonthlyBenefits_OfferSection {
	if x != nil {
		return x.OfferSection
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits) GetAmbEntryPoint() *ui.IconTextComponent {
	if x != nil {
		return x.AmbEntryPoint
	}
	return nil
}

// Title view for month view
//
// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
type MonthlyBenefitView_MonthlyBenefits_TitleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Month (ex: January rewards)
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Title background color
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Right info icon
	// on tap it should move to info deeplink
	RightIcon *ui.IconTextComponent `protobuf:"bytes,3,opt,name=right_icon,json=rightIcon,proto3" json:"right_icon,omitempty"`
}

func (x *MonthlyBenefitView_MonthlyBenefits_TitleView) Reset() {
	*x = MonthlyBenefitView_MonthlyBenefits_TitleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView_MonthlyBenefits_TitleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView_MonthlyBenefits_TitleView) ProtoMessage() {}

func (x *MonthlyBenefitView_MonthlyBenefits_TitleView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView_MonthlyBenefits_TitleView.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView_MonthlyBenefits_TitleView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23, 0, 0}
}

func (x *MonthlyBenefitView_MonthlyBenefits_TitleView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_TitleView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_TitleView) GetRightIcon() *ui.IconTextComponent {
	if x != nil {
		return x.RightIcon
	}
	return nil
}

type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ex: fi coin icon / Cashback icon
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	LeftIcon *common.VisualElement `protobuf:"bytes,1,opt,name=left_icon,json=leftIcon,proto3" json:"left_icon,omitempty"`
	// Example : FI-COINS EARNED (2X)
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Divider view $earned amount/$ potential total amount
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	DividerView *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView `protobuf:"bytes,3,opt,name=divider_view,json=dividerView,proto3" json:"divider_view,omitempty"`
	// [0-100]
	ProgressPercentage uint32 `protobuf:"varint,4,opt,name=progress_percentage,json=progressPercentage,proto3" json:"progress_percentage,omitempty"`
	// color for progress bar active state
	ProgressActiveColor string `protobuf:"bytes,5,opt,name=progress_active_color,json=progressActiveColor,proto3" json:"progress_active_color,omitempty"`
	// color for progress bar background
	ProgressBackgroundColor string `protobuf:"bytes,6,opt,name=progress_background_color,json=progressBackgroundColor,proto3" json:"progress_background_color,omitempty"`
	// E.g., Credited to account on 5th of every month
	// Monthly limit reached
	//
	// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
	Subtitle *ui.IconTextComponent `protobuf:"bytes,7,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// ex: 3% back on all transactions / (icon) Monthly limit reached
	TitleItc *ui.IconTextComponent `protobuf:"bytes,8,opt,name=title_itc,json=titleItc,proto3" json:"title_itc,omitempty"`
	// Plant icon
	PlantIcon *common.VisualElement `protobuf:"bytes,9,opt,name=plant_icon,json=plantIcon,proto3" json:"plant_icon,omitempty"`
	// List of earning view to show values in rotating manner
	// Note : If the list contains less than 2 items, then there won't be any rotating animation.
	EarningViews []*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView `protobuf:"bytes,10,rep,name=earning_views,json=earningViews,proto3" json:"earning_views,omitempty"`
	// No of times to rotate the divider view and display 1st element after that.
	NoOfRotations int32 `protobuf:"varint,11,opt,name=no_of_rotations,json=noOfRotations,proto3" json:"no_of_rotations,omitempty"`
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) Reset() {
	*x = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) ProtoMessage() {}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23, 0, 1}
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetLeftIcon() *common.VisualElement {
	if x != nil {
		return x.LeftIcon
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetDividerView() *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView {
	if x != nil {
		return x.DividerView
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetProgressPercentage() uint32 {
	if x != nil {
		return x.ProgressPercentage
	}
	return 0
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetProgressActiveColor() string {
	if x != nil {
		return x.ProgressActiveColor
	}
	return ""
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetProgressBackgroundColor() string {
	if x != nil {
		return x.ProgressBackgroundColor
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetSubtitle() *ui.IconTextComponent {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetTitleItc() *ui.IconTextComponent {
	if x != nil {
		return x.TitleItc
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetPlantIcon() *common.VisualElement {
	if x != nil {
		return x.PlantIcon
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetEarningViews() []*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView {
	if x != nil {
		return x.EarningViews
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) GetNoOfRotations() int32 {
	if x != nil {
		return x.NoOfRotations
	}
	return 0
}

type MonthlyBenefitView_MonthlyBenefits_OfferSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Types that are assignable to OfferContent:
	//	*MonthlyBenefitView_MonthlyBenefits_OfferSection_VisualElement
	//	*MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView
	OfferContent isMonthlyBenefitView_MonthlyBenefits_OfferSection_OfferContent `protobuf_oneof:"offer_content"`
}

func (x *MonthlyBenefitView_MonthlyBenefits_OfferSection) Reset() {
	*x = MonthlyBenefitView_MonthlyBenefits_OfferSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView_MonthlyBenefits_OfferSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView_MonthlyBenefits_OfferSection) ProtoMessage() {}

func (x *MonthlyBenefitView_MonthlyBenefits_OfferSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView_MonthlyBenefits_OfferSection.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView_MonthlyBenefits_OfferSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23, 0, 2}
}

func (x *MonthlyBenefitView_MonthlyBenefits_OfferSection) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (m *MonthlyBenefitView_MonthlyBenefits_OfferSection) GetOfferContent() isMonthlyBenefitView_MonthlyBenefits_OfferSection_OfferContent {
	if m != nil {
		return m.OfferContent
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_OfferSection) GetVisualElement() *common.VisualElement {
	if x, ok := x.GetOfferContent().(*MonthlyBenefitView_MonthlyBenefits_OfferSection_VisualElement); ok {
		return x.VisualElement
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_OfferSection) GetOfferView() *OfferView {
	if x, ok := x.GetOfferContent().(*MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView); ok {
		return x.OfferView
	}
	return nil
}

type isMonthlyBenefitView_MonthlyBenefits_OfferSection_OfferContent interface {
	isMonthlyBenefitView_MonthlyBenefits_OfferSection_OfferContent()
}

type MonthlyBenefitView_MonthlyBenefits_OfferSection_VisualElement struct {
	VisualElement *common.VisualElement `protobuf:"bytes,2,opt,name=visual_element,json=visualElement,proto3,oneof"`
}

type MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView struct {
	OfferView *OfferView `protobuf:"bytes,3,opt,name=offer_view,json=offerView,proto3,oneof"`
}

func (*MonthlyBenefitView_MonthlyBenefits_OfferSection_VisualElement) isMonthlyBenefitView_MonthlyBenefits_OfferSection_OfferContent() {
}

func (*MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView) isMonthlyBenefitView_MonthlyBenefits_OfferSection_OfferContent() {
}

// ex ₹0 / ₹5,000
//
// Deprecated: Marked as deprecated in api/frontend/tiering/service.proto.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ex ₹0
	Numerator *ui.IconTextComponent `protobuf:"bytes,1,opt,name=numerator,proto3" json:"numerator,omitempty"`
	// ex: /
	Divider *common.Text `protobuf:"bytes,2,opt,name=divider,proto3" json:"divider,omitempty"`
	// ex: ₹5,000
	Denominator *ui.IconTextComponent `protobuf:"bytes,3,opt,name=denominator,proto3" json:"denominator,omitempty"`
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) Reset() {
	*x = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) ProtoMessage() {}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23, 0, 1, 0}
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) GetNumerator() *ui.IconTextComponent {
	if x != nil {
		return x.Numerator
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) GetDivider() *common.Text {
	if x != nil {
		return x.Divider
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) GetDenominator() *ui.IconTextComponent {
	if x != nil {
		return x.Denominator
	}
	return nil
}

type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ui.IconTextComponent `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// Content color taken as background color as need to add support for gradient
	// Will be applied to each of the text in the items
	ContentColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=content_color,json=contentColor,proto3" json:"content_color,omitempty"`
	// The content will be visible for given time
	VisibleMillis int32 `protobuf:"varint,3,opt,name=visible_millis,json=visibleMillis,proto3" json:"visible_millis,omitempty"`
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) Reset() {
	*x = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) ProtoMessage() {}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView.ProtoReflect.Descriptor instead.
func (*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{23, 0, 1, 1}
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) GetItems() []*ui.IconTextComponent {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) GetContentColor() *widget.BackgroundColour {
	if x != nil {
		return x.ContentColor
	}
	return nil
}

func (x *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) GetVisibleMillis() int32 {
	if x != nil {
		return x.VisibleMillis
	}
	return 0
}

// Figma :- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44909&mode=design&t=gh3sCRk4mMBlAMvK-4
type WarningView_WarningCounterView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Background color for the view
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,1,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Title for the Counter view (ex: 5)
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Subtitle align vertically for the container view (ex: DAYS LEFT)
	SubTitle *common.Text `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
}

func (x *WarningView_WarningCounterView) Reset() {
	*x = WarningView_WarningCounterView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarningView_WarningCounterView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarningView_WarningCounterView) ProtoMessage() {}

func (x *WarningView_WarningCounterView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarningView_WarningCounterView.ProtoReflect.Descriptor instead.
func (*WarningView_WarningCounterView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *WarningView_WarningCounterView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *WarningView_WarningCounterView) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *WarningView_WarningCounterView) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

type InActiveStateListView_ContainerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// background color for active container
	BackgroundColour *widget.BackgroundColour                           `protobuf:"bytes,1,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	BenefitLists     []*InActiveStateListView_ContainerView_BenefitList `protobuf:"bytes,2,rep,name=benefit_lists,json=benefitLists,proto3" json:"benefit_lists,omitempty"`
	CornerRadius     uint32                                             `protobuf:"varint,3,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
}

func (x *InActiveStateListView_ContainerView) Reset() {
	*x = InActiveStateListView_ContainerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InActiveStateListView_ContainerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InActiveStateListView_ContainerView) ProtoMessage() {}

func (x *InActiveStateListView_ContainerView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InActiveStateListView_ContainerView.ProtoReflect.Descriptor instead.
func (*InActiveStateListView_ContainerView) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{25, 0}
}

func (x *InActiveStateListView_ContainerView) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *InActiveStateListView_ContainerView) GetBenefitLists() []*InActiveStateListView_ContainerView_BenefitList {
	if x != nil {
		return x.BenefitLists
	}
	return nil
}

func (x *InActiveStateListView_ContainerView) GetCornerRadius() uint32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

// list of all benefit for active state
type InActiveStateListView_ContainerView_BenefitList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ex: [ICON]
	LeftIcon *common.VisualElement `protobuf:"bytes,1,opt,name=left_icon,json=leftIcon,proto3" json:"left_icon,omitempty"`
	// ex: 0 Forex charges
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// ex: On spends up to ₹30k/month
	Subtitle *common.Text `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
}

func (x *InActiveStateListView_ContainerView_BenefitList) Reset() {
	*x = InActiveStateListView_ContainerView_BenefitList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InActiveStateListView_ContainerView_BenefitList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InActiveStateListView_ContainerView_BenefitList) ProtoMessage() {}

func (x *InActiveStateListView_ContainerView_BenefitList) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InActiveStateListView_ContainerView_BenefitList.ProtoReflect.Descriptor instead.
func (*InActiveStateListView_ContainerView_BenefitList) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{25, 0, 0}
}

func (x *InActiveStateListView_ContainerView_BenefitList) GetLeftIcon() *common.VisualElement {
	if x != nil {
		return x.LeftIcon
	}
	return nil
}

func (x *InActiveStateListView_ContainerView_BenefitList) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *InActiveStateListView_ContainerView_BenefitList) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

// List of benefit
type ActiveStateListView_BenefitList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [ICON]
	LeftIcon *common.VisualElement `protobuf:"bytes,1,opt,name=left_icon,json=leftIcon,proto3" json:"left_icon,omitempty"`
	// Ex: Debit Card order charges [info-icon]
	Title *ui.IconTextComponent `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Types that are assignable to RightView:
	//	*ActiveStateListView_BenefitList_RightAmount_
	//	*ActiveStateListView_BenefitList_Icon
	RightView isActiveStateListView_BenefitList_RightView `protobuf_oneof:"right_view"`
}

func (x *ActiveStateListView_BenefitList) Reset() {
	*x = ActiveStateListView_BenefitList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActiveStateListView_BenefitList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveStateListView_BenefitList) ProtoMessage() {}

func (x *ActiveStateListView_BenefitList) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveStateListView_BenefitList.ProtoReflect.Descriptor instead.
func (*ActiveStateListView_BenefitList) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ActiveStateListView_BenefitList) GetLeftIcon() *common.VisualElement {
	if x != nil {
		return x.LeftIcon
	}
	return nil
}

func (x *ActiveStateListView_BenefitList) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (m *ActiveStateListView_BenefitList) GetRightView() isActiveStateListView_BenefitList_RightView {
	if m != nil {
		return m.RightView
	}
	return nil
}

func (x *ActiveStateListView_BenefitList) GetRightAmount() *ActiveStateListView_BenefitList_RightAmount {
	if x, ok := x.GetRightView().(*ActiveStateListView_BenefitList_RightAmount_); ok {
		return x.RightAmount
	}
	return nil
}

func (x *ActiveStateListView_BenefitList) GetIcon() *common.VisualElement {
	if x, ok := x.GetRightView().(*ActiveStateListView_BenefitList_Icon); ok {
		return x.Icon
	}
	return nil
}

type isActiveStateListView_BenefitList_RightView interface {
	isActiveStateListView_BenefitList_RightView()
}

type ActiveStateListView_BenefitList_RightAmount_ struct {
	RightAmount *ActiveStateListView_BenefitList_RightAmount `protobuf:"bytes,3,opt,name=right_amount,json=rightAmount,proto3,oneof"`
}

type ActiveStateListView_BenefitList_Icon struct {
	// collected options
	// [Icon]
	Icon *common.VisualElement `protobuf:"bytes,4,opt,name=icon,proto3,oneof"`
}

func (*ActiveStateListView_BenefitList_RightAmount_) isActiveStateListView_BenefitList_RightView() {}

func (*ActiveStateListView_BenefitList_Icon) isActiveStateListView_BenefitList_RightView() {}

type ActiveStateListView_BenefitList_RightAmount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Amount saved
	// Ex: ₹299
	Amount *common.Text `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// Amount that was paid if not in the tier
	// --499--
	StrikethroughAmount *common.Text `protobuf:"bytes,2,opt,name=strikethrough_amount,json=strikethroughAmount,proto3" json:"strikethrough_amount,omitempty"`
}

func (x *ActiveStateListView_BenefitList_RightAmount) Reset() {
	*x = ActiveStateListView_BenefitList_RightAmount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_tiering_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActiveStateListView_BenefitList_RightAmount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveStateListView_BenefitList_RightAmount) ProtoMessage() {}

func (x *ActiveStateListView_BenefitList_RightAmount) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_tiering_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveStateListView_BenefitList_RightAmount.ProtoReflect.Descriptor instead.
func (*ActiveStateListView_BenefitList_RightAmount) Descriptor() ([]byte, []int) {
	return file_api_frontend_tiering_service_proto_rawDescGZIP(), []int{26, 0, 0}
}

func (x *ActiveStateListView_BenefitList_RightAmount) GetAmount() *common.Text {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *ActiveStateListView_BenefitList_RightAmount) GetStrikethroughAmount() *common.Text {
	if x != nil {
		return x.StrikethroughAmount
	}
	return nil
}

var File_api_frontend_tiering_service_proto protoreflect.FileDescriptor

var file_api_frontend_tiering_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64,
	0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65,
	0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68,
	0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x73,
	0x64, 0x75, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x76, 0x0a, 0x25, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x22, 0xd5, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x3f, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x28, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x22, 0xa6, 0x01, 0x0a, 0x22, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x68,
	0x6f, 0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x4e, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x69, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xac,
	0x06, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5e,
	0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x68,
	0x0a, 0x1b, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x56, 0x69, 0x65, 0x77, 0x52, 0x18,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x61, 0x72,
	0x6e, 0x65, 0x64, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0b, 0x76, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x72, 0x65, 0x43, 0x74, 0x61, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x66, 0x74, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x1a, 0xb7, 0x02, 0x0a, 0x0a, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x63,
	0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x13, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64,
	0x12, 0x57, 0x0a, 0x1b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x45, 0x61,
	0x72, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x62, 0x6f, 0x74,
	0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x22, 0x9f, 0x07,
	0x0a, 0x17, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45,
	0x61, 0x72, 0x6e, 0x65, 0x64, 0x56, 0x69, 0x65, 0x77, 0x12, 0x55, 0x0a, 0x0b, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45,
	0x61, 0x72, 0x6e, 0x65, 0x64, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x64, 0x0a, 0x16, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x14, 0x63, 0x61, 0x72, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x4c, 0x0a, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x45, 0x61,
	0x72, 0x6e, 0x65, 0x64, 0x12, 0x57, 0x0a, 0x1b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x6a, 0x0a,
	0x19, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x17, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x67, 0x0a, 0x12, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x56, 0x69, 0x65, 0x77,
	0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x1a, 0xce, 0x01, 0x0a, 0x0a, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33,
	0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03,
	0x74, 0x61, 0x67, 0x1a, 0x7a, 0x0a, 0x0f, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x67, 0x0a, 0x23, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x4f, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xd9, 0x03, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x12,
	0x69, 0x73, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x10, 0x69, 0x73, 0x54, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x4d, 0x0a, 0x23, 0x6c,
	0x61, 0x75, 0x6e, 0x63, 0x68, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x20, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68,
	0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x59, 0x0a, 0x1a, 0x74, 0x69,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x18, 0x74, 0x69, 0x65,
	0x72, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x5e, 0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x6c, 0x61, 0x75, 0x6e,
	0x63, 0x68, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x18, 0x69, 0x73, 0x4c,
	0x61, 0x75, 0x6e, 0x63, 0x68, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x79, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x31, 0x0a,
	0x06, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x06, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x22, 0x90, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x22, 0x42, 0x0a, 0x0e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x8c, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x69, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65,
	0x72, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x22, 0x9c, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c,
	0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x3f, 0x0a, 0x0d, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x6c, 0x61, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x0b, 0x74, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x6e,
	0x22, 0x73, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x45, 0x61, 0x72, 0x6e, 0x65,
	0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd8, 0x07, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65,
	0x72, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6d, 0x0a, 0x0e, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x45,
	0x61, 0x72, 0x6e, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x53, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x12, 0x5e, 0x0a,
	0x1a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x65,
	0x61, 0x72, 0x6e, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x53, 0x0a,
	0x14, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x57,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12,
	0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x5a, 0x0a, 0x14, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x4a,
	0x0a, 0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0e, 0x62, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x69, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x11, 0x63, 0x65, 0x6c, 0x65, 0x62, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x63, 0x65, 0x6c,
	0x65, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x66, 0x0a,
	0x22, 0x63, 0x65, 0x6c, 0x65, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x70,
	0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6f, 0x6c, 0x6f, 0x66, 0x66, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1f, 0x63, 0x65, 0x6c, 0x65, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x43, 0x6f, 0x6f, 0x6c, 0x6f, 0x66, 0x66, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x5f, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x70, 0x6f, 0x70, 0x75, 0x70, 0x43, 0x61, 0x63, 0x68, 0x65, 0x4b, 0x65, 0x79, 0x22, 0x51, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x10, 0x0a,
	0x0c, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49,
	0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x10, 0x66,
	0x22, 0x8c, 0x08, 0x0a, 0x0f, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x6f, 0x72,
	0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x68, 0x6f,
	0x75, 0x6c, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x79, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x47, 0x72, 0x61, 0x79, 0x73,
	0x63, 0x61, 0x6c, 0x65, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x5c, 0x0a, 0x16, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x63,
	0x0a, 0x19, 0x69, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x15, 0x69, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x52, 0x0a, 0x10, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x6d, 0x6f, 0x72, 0x65, 0x5f,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x4d, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00,
	0x52, 0x0c, 0x6d, 0x6f, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x3c,
	0x0a, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x48,
	0x00, 0x52, 0x09, 0x72, 0x65, 0x74, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x58, 0x0a, 0x14,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77,
	0x48, 0x00, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x58, 0x0a, 0x14, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x12, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x42, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x65, 0x77,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0b, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x3f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x56, 0x69, 0x65, 0x77, 0x42, 0x08, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xae, 0x04, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x3c, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0b, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e,
	0x0a, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x69, 0x67, 0x68, 0x74, 0x43, 0x74, 0x61, 0x12, 0x3e,
	0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x12, 0x5e, 0x0a, 0x1a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x17, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x22, 0xb8, 0x06, 0x0a, 0x13, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x4a, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x49, 0x0a,
	0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x09, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x38, 0x0a, 0x06, 0x76, 0x73, 0x5f, 0x69,
	0x74, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x76, 0x73, 0x49,
	0x74, 0x63, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x74, 0x69, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x1a, 0xa8, 0x03, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12, 0x33, 0x0a, 0x03, 0x74, 0x61, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x48,
	0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x08, 0x63, 0x61, 0x73, 0x68,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63,
	0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x37, 0x0a, 0x05, 0x63, 0x6f, 0x69, 0x6e, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x63, 0x6f, 0x69, 0x6e, 0x73,
	0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x69, 0x65,
	0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x63, 0x0a, 0x0c, 0x4d,
	0x6f, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x12, 0x53, 0x0a, 0x14, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x69, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0xa7, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x74, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x35,
	0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x22, 0xda, 0x01, 0x0a, 0x09, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x40, 0x0a, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0xe1, 0x13, 0x0a, 0x12, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x5f,
	0x0a, 0x10, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x0f,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x1a, 0xc6, 0x12, 0x0a, 0x0f, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x63, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x7b, 0x0a, 0x15, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x74,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x54,
	0x69, 0x6c, 0x65, 0x52, 0x13, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x29,
	0x0a, 0x10, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x79, 0x73, 0x63, 0x61,
	0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64,
	0x47, 0x72, 0x61, 0x79, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x76, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x56, 0x32, 0x12, 0x66, 0x0a, 0x0d,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0f, 0x61, 0x6d, 0x62, 0x5f, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x0d, 0x61, 0x6d, 0x62, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x1a,
	0xde, 0x01, 0x0a, 0x09, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2e, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5b, 0x0a,
	0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x40, 0x0a, 0x0a, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x3a, 0x02, 0x18, 0x01,
	0x1a, 0xa3, 0x09, 0x0a, 0x12, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x7a, 0x0a, 0x0c, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x2e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x2e, 0x44,
	0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b,
	0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2f, 0x0a, 0x13, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x15,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x3a, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x08,
	0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x3e, 0x0a, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x74, 0x63, 0x12,
	0x40, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x78, 0x0a, 0x0d, 0x65, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x2e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x54, 0x69, 0x6c,
	0x65, 0x2e, 0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x65,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x6f, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x6f, 0x4f, 0x66, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x1a, 0xcb, 0x01, 0x0a, 0x0b, 0x44, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x3f, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x07, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x07, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0b, 0x64, 0x65, 0x6e, 0x6f,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x0b, 0x64, 0x65, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x3a, 0x02, 0x18,
	0x01, 0x1a, 0xc2, 0x01, 0x0a, 0x0b, 0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x53, 0x0a, 0x0d, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x1a, 0xe2, 0x01, 0x0a, 0x0c, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x4a, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x0a,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52,
	0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x42, 0x0f, 0x0a, 0x0d, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xdf, 0x06, 0x0a, 0x0b,
	0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x64, 0x0a, 0x14, 0x77,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x57, 0x61, 0x72,
	0x6e, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x12, 0x77,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x4a, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0d,
	0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a,
	0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65,
	0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x53, 0x0a, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x1a, 0xd8, 0x01, 0x0a, 0x12, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x5b, 0x0a, 0x11,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x42, 0x0b, 0x0a, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x22, 0x87, 0x06,
	0x0a, 0x15, 0x49, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x3c, 0x0a, 0x08, 0x74, 0x6f, 0x70, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x74, 0x6f,
	0x70, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x56, 0x69, 0x65, 0x77, 0x1a, 0xaf, 0x03, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x12, 0x66, 0x0a, 0x0d, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49,
	0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x56, 0x69,
	0x65, 0x77, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0c,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x1a, 0xb3, 0x01, 0x0a, 0x0b, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x3e, 0x0a, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0xe5, 0x04, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x54, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0xc0, 0x03, 0x0a,
	0x0b, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x09,
	0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x62, 0x0a, 0x0c, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x52,
	0x69, 0x67, 0x68, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x1a, 0x8c, 0x01, 0x0a, 0x0b, 0x52, 0x69, 0x67, 0x68, 0x74, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x30, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x69, 0x6b, 0x65, 0x74, 0x68,
	0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x13, 0x73, 0x74,
	0x72, 0x69, 0x6b, 0x65, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x22,
	0x69, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0x96, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x22, 0x69, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x6c,
	0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0xf7,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61,
	0x6e, 0x73, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2d,
	0x0a, 0x13, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x74, 0x6f, 0x5f,
	0x66, 0x6f, 0x63, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x74, 0x69, 0x65,
	0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x09, 0x74,
	0x69, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x22, 0xc1, 0x06, 0x0a, 0x08, 0x54, 0x69, 0x65,
	0x72, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x66, 0x0a, 0x17, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x15, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x75, 0x0a,
	0x1f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x1c, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x76,
	0x65, 0x72, 0x6c, 0x61, 0x79, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x68, 0x0a, 0x18, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x16, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x35,
	0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0a, 0x73, 0x77, 0x69, 0x70, 0x65, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x73, 0x77,
	0x69, 0x70, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c,
	0x61, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x43, 0x61, 0x72, 0x64,
	0x12, 0x42, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x6e, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x51,
	0x0a, 0x13, 0x74, 0x6f, 0x6f, 0x6c, 0x62, 0x61, 0x72, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11,
	0x74, 0x6f, 0x6f, 0x6c, 0x62, 0x61, 0x72, 0x52, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x5c, 0x0a, 0x1c, 0x74, 0x6f, 0x6f, 0x6c, 0x62, 0x61, 0x72, 0x5f, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x19, 0x74, 0x6f, 0x6f, 0x6c, 0x62, 0x61, 0x72, 0x52, 0x69, 0x67,
	0x68, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x22, 0xf6, 0x04, 0x0a,
	0x0b, 0x50, 0x6c, 0x61, 0x6e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x12, 0x59, 0x0a, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x42, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x52, 0x0a, 0x12, 0x73, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53,
	0x6d, 0x61, 0x6c, 0x6c, 0x48, 0x00, 0x52, 0x10, 0x73, 0x6d, 0x61, 0x6c, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0b,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x0a, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x4f, 0x0a,
	0x11, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x65, 0x61, 0x72,
	0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x6c,
	0x65, 0x61, 0x72, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x37,
	0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x42, 0x09, 0x0a, 0x07, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x22, 0x4e, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xd0, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x2a, 0xdf, 0x02, 0x0a, 0x0c, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x45, 0x4e, 0x45,
	0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49,
	0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f,
	0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x20,
	0x0a, 0x1c, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x03,
	0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x04,
	0x12, 0x17, 0x0a, 0x13, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48,
	0x4c, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x07, 0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x08, 0x12, 0x27, 0x0a, 0x23, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41,
	0x4e, 0x43, 0x45, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x09, 0x32, 0x8f, 0x0a, 0x0a, 0x07, 0x54,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x77, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x5c, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x24,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a,
	0x07, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x68, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61,
	0x6e, 0x73, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c,
	0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x1b, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x68, 0x6f,
	0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77,
	0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x65, 0x72, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x12, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x45, 0x61, 0x72, 0x6e,
	0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x45, 0x61, 0x72, 0x6e,
	0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65,
	0x72, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2a, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69,
	0x65, 0x72, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41,
	0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32, 0x12, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72,
	0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x53, 0x68, 0x65, 0x65, 0x74, 0x12, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41,
	0x4d, 0x42, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5a, 0x0a, 0x2b,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5a, 0x2b, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_tiering_service_proto_rawDescOnce sync.Once
	file_api_frontend_tiering_service_proto_rawDescData = file_api_frontend_tiering_service_proto_rawDesc
)

func file_api_frontend_tiering_service_proto_rawDescGZIP() []byte {
	file_api_frontend_tiering_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_tiering_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_tiering_service_proto_rawDescData)
	})
	return file_api_frontend_tiering_service_proto_rawDescData
}

var file_api_frontend_tiering_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_frontend_tiering_service_proto_msgTypes = make([]protoimpl.MessageInfo, 50)
var file_api_frontend_tiering_service_proto_goTypes = []interface{}{
	(BenefitsType)(0),                                                         // 0: frontend.tiering.BenefitsType
	(GetTierEarnedBenefitsResponse_Status)(0),                                 // 1: frontend.tiering.GetTierEarnedBenefitsResponse.Status
	(*GetDetailedBenefitsBottomSheetRequest)(nil),                             // 2: frontend.tiering.GetDetailedBenefitsBottomSheetRequest
	(*GetDetailedBenefitsBottomSheetResponse)(nil),                            // 3: frontend.tiering.GetDetailedBenefitsBottomSheetResponse
	(*RecordComponentShownToActorRequest)(nil),                                // 4: frontend.tiering.RecordComponentShownToActorRequest
	(*GetEarnedBenefitsHistoryRequest)(nil),                                   // 5: frontend.tiering.GetEarnedBenefitsHistoryRequest
	(*GetEarnedBenefitsHistoryResponse)(nil),                                  // 6: frontend.tiering.GetEarnedBenefitsHistoryResponse
	(*MonthlyRewardEarnedView)(nil),                                           // 7: frontend.tiering.MonthlyRewardEarnedView
	(*RecordComponentShownToActorResponse)(nil),                               // 8: frontend.tiering.RecordComponentShownToActorResponse
	(*GetTieringLaunchInfoRequest)(nil),                                       // 9: frontend.tiering.GetTieringLaunchInfoRequest
	(*GetTieringLaunchInfoResponse)(nil),                                      // 10: frontend.tiering.GetTieringLaunchInfoResponse
	(*GetDeeplinkRequest)(nil),                                                // 11: frontend.tiering.GetDeeplinkRequest
	(*GetDeeplinkResponse)(nil),                                               // 12: frontend.tiering.GetDeeplinkResponse
	(*UpgradeRequest)(nil),                                                    // 13: frontend.tiering.UpgradeRequest
	(*UpgradeResponse)(nil),                                                   // 14: frontend.tiering.UpgradeResponse
	(*GetTierAllPlansRequest)(nil),                                            // 15: frontend.tiering.GetTierAllPlansRequest
	(*GetTierAllPlansResponse)(nil),                                           // 16: frontend.tiering.GetTierAllPlansResponse
	(*GetTierEarnedBenefitsRequest)(nil),                                      // 17: frontend.tiering.GetTierEarnedBenefitsRequest
	(*GetTierEarnedBenefitsResponse)(nil),                                     // 18: frontend.tiering.GetTierEarnedBenefitsResponse
	(*BenefitsOptions)(nil),                                                   // 19: frontend.tiering.BenefitsOptions
	(*TransferSalaryView)(nil),                                                // 20: frontend.tiering.TransferSalaryView
	(*UpgradeBenefitsView)(nil),                                               // 21: frontend.tiering.UpgradeBenefitsView
	(*MoreInfoView)(nil),                                                      // 22: frontend.tiering.MoreInfoView
	(*RetryView)(nil),                                                         // 23: frontend.tiering.RetryView
	(*TitleView)(nil),                                                         // 24: frontend.tiering.TitleView
	(*MonthlyBenefitView)(nil),                                                // 25: frontend.tiering.MonthlyBenefitView
	(*WarningView)(nil),                                                       // 26: frontend.tiering.WarningView
	(*InActiveStateListView)(nil),                                             // 27: frontend.tiering.InActiveStateListView
	(*ActiveStateListView)(nil),                                               // 28: frontend.tiering.ActiveStateListView
	(*GetTierFlowScreenRequest)(nil),                                          // 29: frontend.tiering.GetTierFlowScreenRequest
	(*GetTierFlowScreenResponse)(nil),                                         // 30: frontend.tiering.GetTierFlowScreenResponse
	(*GetTierAllPlansV2Request)(nil),                                          // 31: frontend.tiering.GetTierAllPlansV2Request
	(*GetTierAllPlansV2Response)(nil),                                         // 32: frontend.tiering.GetTierAllPlansV2Response
	(*TierPlan)(nil),                                                          // 33: frontend.tiering.TierPlan
	(*PlanBenefit)(nil),                                                       // 34: frontend.tiering.PlanBenefit
	(*GetAMBScreenDetailsRequest)(nil),                                        // 35: frontend.tiering.GetAMBScreenDetailsRequest
	(*GetAMBScreenDetailsResponse)(nil),                                       // 36: frontend.tiering.GetAMBScreenDetailsResponse
	(*GetEarnedBenefitsHistoryResponse_HeaderView)(nil),                       // 37: frontend.tiering.GetEarnedBenefitsHistoryResponse.HeaderView
	(*MonthlyRewardEarnedView_HeaderView)(nil),                                // 38: frontend.tiering.MonthlyRewardEarnedView.HeaderView
	(*MonthlyRewardEarnedView_BenefitCardItem)(nil),                           // 39: frontend.tiering.MonthlyRewardEarnedView.BenefitCardItem
	(*UpgradeBenefitsView_Card)(nil),                                          // 40: frontend.tiering.UpgradeBenefitsView.Card
	(*MonthlyBenefitView_MonthlyBenefits)(nil),                                // 41: frontend.tiering.MonthlyBenefitView.MonthlyBenefits
	(*MonthlyBenefitView_MonthlyBenefits_TitleView)(nil),                      // 42: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.TitleView
	(*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile)(nil),             // 43: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile
	(*MonthlyBenefitView_MonthlyBenefits_OfferSection)(nil),                   // 44: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.OfferSection
	(*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView)(nil), // 45: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.DividerView
	(*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView)(nil), // 46: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.EarningView
	(*WarningView_WarningCounterView)(nil),                                    // 47: frontend.tiering.WarningView.WarningCounterView
	(*InActiveStateListView_ContainerView)(nil),                               // 48: frontend.tiering.InActiveStateListView.ContainerView
	(*InActiveStateListView_ContainerView_BenefitList)(nil),                   // 49: frontend.tiering.InActiveStateListView.ContainerView.BenefitList
	(*ActiveStateListView_BenefitList)(nil),                                   // 50: frontend.tiering.ActiveStateListView.BenefitList
	(*ActiveStateListView_BenefitList_RightAmount)(nil),                       // 51: frontend.tiering.ActiveStateListView.BenefitList.RightAmount
	(*header.RequestHeader)(nil),                                              // 52: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil),                                             // 53: frontend.header.ResponseHeader
	(*sections.Section)(nil),                                                  // 54: api.typesv2.ui.sdui.sections.Section
	(*deeplink.Cta)(nil),                                                      // 55: frontend.deeplink.Cta
	(enum.DisplayComponent)(0),                                                // 56: frontend.tiering.enum.DisplayComponent
	(*common.Text)(nil),                                                       // 57: api.typesv2.common.Text
	(*ui.IconTextComponent)(nil),                                              // 58: api.typesv2.ui.IconTextComponent
	(*widget.BackgroundColour)(nil),                                           // 59: api.typesv2.common.ui.widget.BackgroundColour
	(common.BooleanEnum)(0),                                                   // 60: api.typesv2.common.BooleanEnum
	(*deeplink.Deeplink)(nil),                                                 // 61: frontend.deeplink.Deeplink
	(deeplink.Screen)(0),                                                      // 62: frontend.deeplink.Screen
	(*tiering.EarnedBenefitScreenOptions)(nil),                                // 63: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions
	(*durationpb.Duration)(nil),                                               // 64: google.protobuf.Duration
	(*RewardView)(nil),                                                        // 65: frontend.tiering.RewardView
	(*common.VisualElement)(nil),                                              // 66: api.typesv2.common.VisualElement
	(*PlanCard)(nil),                                                          // 67: frontend.tiering.PlanCard
	(*BenefitCard)(nil),                                                       // 68: frontend.tiering.BenefitCard
	(*EntryBannerSmall)(nil),                                                  // 69: frontend.tiering.EntryBannerSmall
	(*EntryBanner)(nil),                                                       // 70: frontend.tiering.EntryBanner
	(*InfoBanner)(nil),                                                        // 71: frontend.tiering.InfoBanner
	(*LearnMoreBanner)(nil),                                                   // 72: frontend.tiering.LearnMoreBanner
	(*OfferView)(nil),                                                         // 73: frontend.tiering.OfferView
}
var file_api_frontend_tiering_service_proto_depIdxs = []int32{
	52,  // 0: frontend.tiering.GetDetailedBenefitsBottomSheetRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 1: frontend.tiering.GetDetailedBenefitsBottomSheetResponse.resp_header:type_name -> frontend.header.ResponseHeader
	54,  // 2: frontend.tiering.GetDetailedBenefitsBottomSheetResponse.section:type_name -> api.typesv2.ui.sdui.sections.Section
	55,  // 3: frontend.tiering.GetDetailedBenefitsBottomSheetResponse.cta:type_name -> frontend.deeplink.Cta
	52,  // 4: frontend.tiering.RecordComponentShownToActorRequest.req:type_name -> frontend.header.RequestHeader
	56,  // 5: frontend.tiering.RecordComponentShownToActorRequest.component_name:type_name -> frontend.tiering.enum.DisplayComponent
	52,  // 6: frontend.tiering.GetEarnedBenefitsHistoryRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 7: frontend.tiering.GetEarnedBenefitsHistoryResponse.resp_header:type_name -> frontend.header.ResponseHeader
	57,  // 8: frontend.tiering.GetEarnedBenefitsHistoryResponse.page_title:type_name -> api.typesv2.common.Text
	37,  // 9: frontend.tiering.GetEarnedBenefitsHistoryResponse.header_view:type_name -> frontend.tiering.GetEarnedBenefitsHistoryResponse.HeaderView
	7,   // 10: frontend.tiering.GetEarnedBenefitsHistoryResponse.monthly_reward_earned_views:type_name -> frontend.tiering.MonthlyRewardEarnedView
	58,  // 11: frontend.tiering.GetEarnedBenefitsHistoryResponse.view_more_cta:type_name -> api.typesv2.ui.IconTextComponent
	38,  // 12: frontend.tiering.MonthlyRewardEarnedView.header_view:type_name -> frontend.tiering.MonthlyRewardEarnedView.HeaderView
	59,  // 13: frontend.tiering.MonthlyRewardEarnedView.card_background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	57,  // 14: frontend.tiering.MonthlyRewardEarnedView.total_benefits_earned:type_name -> api.typesv2.common.Text
	57,  // 15: frontend.tiering.MonthlyRewardEarnedView.total_benefits_earned_value:type_name -> api.typesv2.common.Text
	59,  // 16: frontend.tiering.MonthlyRewardEarnedView.benefit_background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	39,  // 17: frontend.tiering.MonthlyRewardEarnedView.benefit_card_items:type_name -> frontend.tiering.MonthlyRewardEarnedView.BenefitCardItem
	53,  // 18: frontend.tiering.RecordComponentShownToActorResponse.resp_header:type_name -> frontend.header.ResponseHeader
	52,  // 19: frontend.tiering.GetTieringLaunchInfoRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 20: frontend.tiering.GetTieringLaunchInfoResponse.resp_header:type_name -> frontend.header.ResponseHeader
	60,  // 21: frontend.tiering.GetTieringLaunchInfoResponse.is_tiering_enabled:type_name -> api.typesv2.common.BooleanEnum
	61,  // 22: frontend.tiering.GetTieringLaunchInfoResponse.tier_introduction_deeplink:type_name -> frontend.deeplink.Deeplink
	60,  // 23: frontend.tiering.GetTieringLaunchInfoResponse.is_launch_animation_enabled:type_name -> api.typesv2.common.BooleanEnum
	52,  // 24: frontend.tiering.GetDeeplinkRequest.req:type_name -> frontend.header.RequestHeader
	62,  // 25: frontend.tiering.GetDeeplinkRequest.screen:type_name -> frontend.deeplink.Screen
	53,  // 26: frontend.tiering.GetDeeplinkResponse.resp_header:type_name -> frontend.header.ResponseHeader
	61,  // 27: frontend.tiering.GetDeeplinkResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	52,  // 28: frontend.tiering.UpgradeRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 29: frontend.tiering.UpgradeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	61,  // 30: frontend.tiering.UpgradeResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	52,  // 31: frontend.tiering.GetTierAllPlansRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 32: frontend.tiering.GetTierAllPlansResponse.resp_header:type_name -> frontend.header.ResponseHeader
	61,  // 33: frontend.tiering.GetTierAllPlansResponse.tier_all_plan:type_name -> frontend.deeplink.Deeplink
	52,  // 34: frontend.tiering.GetTierEarnedBenefitsRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 35: frontend.tiering.GetTierEarnedBenefitsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	63,  // 36: frontend.tiering.GetTierEarnedBenefitsResponse.screen_options:type_name -> api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions
	58,  // 37: frontend.tiering.GetTierEarnedBenefitsResponse.total_benefit_earned:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 38: frontend.tiering.GetTierEarnedBenefitsResponse.total_benefit_earned_value:type_name -> api.typesv2.ui.IconTextComponent
	26,  // 39: frontend.tiering.GetTierEarnedBenefitsResponse.warning_counter_view:type_name -> frontend.tiering.WarningView
	25,  // 40: frontend.tiering.GetTierEarnedBenefitsResponse.monthly_benefit_view:type_name -> frontend.tiering.MonthlyBenefitView
	19,  // 41: frontend.tiering.GetTierEarnedBenefitsResponse.benefit_options:type_name -> frontend.tiering.BenefitsOptions
	61,  // 42: frontend.tiering.GetTierEarnedBenefitsResponse.celebration_popup:type_name -> frontend.deeplink.Deeplink
	64,  // 43: frontend.tiering.GetTierEarnedBenefitsResponse.celebration_popup_cooloff_duration:type_name -> google.protobuf.Duration
	57,  // 44: frontend.tiering.BenefitsOptions.title:type_name -> api.typesv2.common.Text
	59,  // 45: frontend.tiering.BenefitsOptions.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	28,  // 46: frontend.tiering.BenefitsOptions.active_state_list_view:type_name -> frontend.tiering.ActiveStateListView
	27,  // 47: frontend.tiering.BenefitsOptions.in_active_state_list_view:type_name -> frontend.tiering.InActiveStateListView
	21,  // 48: frontend.tiering.BenefitsOptions.upgrade_benefits:type_name -> frontend.tiering.UpgradeBenefitsView
	22,  // 49: frontend.tiering.BenefitsOptions.more_benefits:type_name -> frontend.tiering.MoreInfoView
	23,  // 50: frontend.tiering.BenefitsOptions.retry_view:type_name -> frontend.tiering.RetryView
	20,  // 51: frontend.tiering.BenefitsOptions.transfer_salary_view:type_name -> frontend.tiering.TransferSalaryView
	25,  // 52: frontend.tiering.BenefitsOptions.monthly_benefit_view:type_name -> frontend.tiering.MonthlyBenefitView
	26,  // 53: frontend.tiering.BenefitsOptions.warning_view:type_name -> frontend.tiering.WarningView
	65,  // 54: frontend.tiering.BenefitsOptions.reward_view:type_name -> frontend.tiering.RewardView
	24,  // 55: frontend.tiering.TransferSalaryView.header_view:type_name -> frontend.tiering.TitleView
	57,  // 56: frontend.tiering.TransferSalaryView.amount:type_name -> api.typesv2.common.Text
	58,  // 57: frontend.tiering.TransferSalaryView.edit_amount:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 58: frontend.tiering.TransferSalaryView.right_cta:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 59: frontend.tiering.TransferSalaryView.info_view:type_name -> api.typesv2.ui.IconTextComponent
	59,  // 60: frontend.tiering.TransferSalaryView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	58,  // 61: frontend.tiering.TransferSalaryView.amount_icon_text_component:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 62: frontend.tiering.UpgradeBenefitsView.component_title:type_name -> api.typesv2.ui.IconTextComponent
	40,  // 63: frontend.tiering.UpgradeBenefitsView.left_card:type_name -> frontend.tiering.UpgradeBenefitsView.Card
	40,  // 64: frontend.tiering.UpgradeBenefitsView.right_card:type_name -> frontend.tiering.UpgradeBenefitsView.Card
	58,  // 65: frontend.tiering.UpgradeBenefitsView.vs_itc:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 66: frontend.tiering.UpgradeBenefitsView.cta:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 67: frontend.tiering.MoreInfoView.icon_text_components:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 68: frontend.tiering.RetryView.icon:type_name -> api.typesv2.common.VisualElement
	57,  // 69: frontend.tiering.RetryView.title:type_name -> api.typesv2.common.Text
	58,  // 70: frontend.tiering.RetryView.cta:type_name -> api.typesv2.ui.IconTextComponent
	57,  // 71: frontend.tiering.TitleView.title:type_name -> api.typesv2.common.Text
	59,  // 72: frontend.tiering.TitleView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	58,  // 73: frontend.tiering.TitleView.right_icon:type_name -> api.typesv2.ui.IconTextComponent
	41,  // 74: frontend.tiering.MonthlyBenefitView.monthly_benefits:type_name -> frontend.tiering.MonthlyBenefitView.MonthlyBenefits
	47,  // 75: frontend.tiering.WarningView.warning_counter_view:type_name -> frontend.tiering.WarningView.WarningCounterView
	66,  // 76: frontend.tiering.WarningView.visual_element:type_name -> api.typesv2.common.VisualElement
	57,  // 77: frontend.tiering.WarningView.title:type_name -> api.typesv2.common.Text
	57,  // 78: frontend.tiering.WarningView.subtitle:type_name -> api.typesv2.common.Text
	66,  // 79: frontend.tiering.WarningView.right_icon:type_name -> api.typesv2.common.VisualElement
	59,  // 80: frontend.tiering.WarningView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	61,  // 81: frontend.tiering.WarningView.deeplink:type_name -> frontend.deeplink.Deeplink
	58,  // 82: frontend.tiering.WarningView.right_text_component:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 83: frontend.tiering.InActiveStateListView.top_icon:type_name -> api.typesv2.common.VisualElement
	57,  // 84: frontend.tiering.InActiveStateListView.title:type_name -> api.typesv2.common.Text
	57,  // 85: frontend.tiering.InActiveStateListView.sub_title:type_name -> api.typesv2.common.Text
	58,  // 86: frontend.tiering.InActiveStateListView.action:type_name -> api.typesv2.ui.IconTextComponent
	48,  // 87: frontend.tiering.InActiveStateListView.container_view:type_name -> frontend.tiering.InActiveStateListView.ContainerView
	50,  // 88: frontend.tiering.ActiveStateListView.benefit_list:type_name -> frontend.tiering.ActiveStateListView.BenefitList
	58,  // 89: frontend.tiering.ActiveStateListView.info:type_name -> api.typesv2.ui.IconTextComponent
	52,  // 90: frontend.tiering.GetTierFlowScreenRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 91: frontend.tiering.GetTierFlowScreenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	61,  // 92: frontend.tiering.GetTierFlowScreenResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	52,  // 93: frontend.tiering.GetTierAllPlansV2Request.req:type_name -> frontend.header.RequestHeader
	53,  // 94: frontend.tiering.GetTierAllPlansV2Response.resp_header:type_name -> frontend.header.ResponseHeader
	57,  // 95: frontend.tiering.GetTierAllPlansV2Response.title:type_name -> api.typesv2.common.Text
	33,  // 96: frontend.tiering.GetTierAllPlansV2Response.tier_plans:type_name -> frontend.tiering.TierPlan
	59,  // 97: frontend.tiering.TierPlan.header_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	59,  // 98: frontend.tiering.TierPlan.header_overlay_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	59,  // 99: frontend.tiering.TierPlan.content_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	66,  // 100: frontend.tiering.TierPlan.icon:type_name -> api.typesv2.common.VisualElement
	66,  // 101: frontend.tiering.TierPlan.swipe_icon:type_name -> api.typesv2.common.VisualElement
	67,  // 102: frontend.tiering.TierPlan.plan_card:type_name -> frontend.tiering.PlanCard
	34,  // 103: frontend.tiering.TierPlan.plan_benefits:type_name -> frontend.tiering.PlanBenefit
	55,  // 104: frontend.tiering.TierPlan.cta:type_name -> frontend.deeplink.Cta
	66,  // 105: frontend.tiering.TierPlan.toolbar_right_image:type_name -> api.typesv2.common.VisualElement
	61,  // 106: frontend.tiering.TierPlan.toolbar_right_image_deeplink:type_name -> frontend.deeplink.Deeplink
	59,  // 107: frontend.tiering.PlanBenefit.background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	58,  // 108: frontend.tiering.PlanBenefit.title:type_name -> api.typesv2.ui.IconTextComponent
	68,  // 109: frontend.tiering.PlanBenefit.benefit_card:type_name -> frontend.tiering.BenefitCard
	69,  // 110: frontend.tiering.PlanBenefit.small_entry_banner:type_name -> frontend.tiering.EntryBannerSmall
	70,  // 111: frontend.tiering.PlanBenefit.entry_banner:type_name -> frontend.tiering.EntryBanner
	71,  // 112: frontend.tiering.PlanBenefit.info_banner:type_name -> frontend.tiering.InfoBanner
	72,  // 113: frontend.tiering.PlanBenefit.learn_more_banner:type_name -> frontend.tiering.LearnMoreBanner
	61,  // 114: frontend.tiering.PlanBenefit.deeplink:type_name -> frontend.deeplink.Deeplink
	52,  // 115: frontend.tiering.GetAMBScreenDetailsRequest.req:type_name -> frontend.header.RequestHeader
	53,  // 116: frontend.tiering.GetAMBScreenDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	54,  // 117: frontend.tiering.GetAMBScreenDetailsResponse.section:type_name -> api.typesv2.ui.sdui.sections.Section
	57,  // 118: frontend.tiering.GetAMBScreenDetailsResponse.title:type_name -> api.typesv2.common.Text
	58,  // 119: frontend.tiering.GetEarnedBenefitsHistoryResponse.HeaderView.plan_icon:type_name -> api.typesv2.ui.IconTextComponent
	57,  // 120: frontend.tiering.GetEarnedBenefitsHistoryResponse.HeaderView.total_benefits_earned:type_name -> api.typesv2.common.Text
	57,  // 121: frontend.tiering.GetEarnedBenefitsHistoryResponse.HeaderView.total_benefits_earned_value:type_name -> api.typesv2.common.Text
	58,  // 122: frontend.tiering.GetEarnedBenefitsHistoryResponse.HeaderView.bottom_text:type_name -> api.typesv2.ui.IconTextComponent
	59,  // 123: frontend.tiering.MonthlyRewardEarnedView.HeaderView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	57,  // 124: frontend.tiering.MonthlyRewardEarnedView.HeaderView.title:type_name -> api.typesv2.common.Text
	58,  // 125: frontend.tiering.MonthlyRewardEarnedView.HeaderView.tag:type_name -> api.typesv2.ui.IconTextComponent
	57,  // 126: frontend.tiering.MonthlyRewardEarnedView.BenefitCardItem.title:type_name -> api.typesv2.common.Text
	58,  // 127: frontend.tiering.MonthlyRewardEarnedView.BenefitCardItem.value:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 128: frontend.tiering.UpgradeBenefitsView.Card.tag:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 129: frontend.tiering.UpgradeBenefitsView.Card.visual_element:type_name -> api.typesv2.common.VisualElement
	58,  // 130: frontend.tiering.UpgradeBenefitsView.Card.cashback:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 131: frontend.tiering.UpgradeBenefitsView.Card.coins:type_name -> api.typesv2.ui.IconTextComponent
	59,  // 132: frontend.tiering.UpgradeBenefitsView.Card.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	42,  // 133: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.header_view:type_name -> frontend.tiering.MonthlyBenefitView.MonthlyBenefits.TitleView
	43,  // 134: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.monthly_benefit_tiles:type_name -> frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile
	58,  // 135: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.info_view:type_name -> api.typesv2.ui.IconTextComponent
	59,  // 136: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	24,  // 137: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.header_view_v2:type_name -> frontend.tiering.TitleView
	44,  // 138: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.offer_section:type_name -> frontend.tiering.MonthlyBenefitView.MonthlyBenefits.OfferSection
	58,  // 139: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.amb_entry_point:type_name -> api.typesv2.ui.IconTextComponent
	57,  // 140: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.TitleView.title:type_name -> api.typesv2.common.Text
	59,  // 141: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.TitleView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	58,  // 142: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.TitleView.right_icon:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 143: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.left_icon:type_name -> api.typesv2.common.VisualElement
	57,  // 144: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.title:type_name -> api.typesv2.common.Text
	45,  // 145: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.divider_view:type_name -> frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.DividerView
	58,  // 146: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.subtitle:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 147: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.title_itc:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 148: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.plant_icon:type_name -> api.typesv2.common.VisualElement
	46,  // 149: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.earning_views:type_name -> frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.EarningView
	58,  // 150: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.OfferSection.title:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 151: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.OfferSection.visual_element:type_name -> api.typesv2.common.VisualElement
	73,  // 152: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.OfferSection.offer_view:type_name -> frontend.tiering.OfferView
	58,  // 153: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.DividerView.numerator:type_name -> api.typesv2.ui.IconTextComponent
	57,  // 154: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.DividerView.divider:type_name -> api.typesv2.common.Text
	58,  // 155: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.DividerView.denominator:type_name -> api.typesv2.ui.IconTextComponent
	58,  // 156: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.EarningView.items:type_name -> api.typesv2.ui.IconTextComponent
	59,  // 157: frontend.tiering.MonthlyBenefitView.MonthlyBenefits.MonthlyBenefitTile.EarningView.content_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	59,  // 158: frontend.tiering.WarningView.WarningCounterView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	57,  // 159: frontend.tiering.WarningView.WarningCounterView.title:type_name -> api.typesv2.common.Text
	57,  // 160: frontend.tiering.WarningView.WarningCounterView.sub_title:type_name -> api.typesv2.common.Text
	59,  // 161: frontend.tiering.InActiveStateListView.ContainerView.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	49,  // 162: frontend.tiering.InActiveStateListView.ContainerView.benefit_lists:type_name -> frontend.tiering.InActiveStateListView.ContainerView.BenefitList
	66,  // 163: frontend.tiering.InActiveStateListView.ContainerView.BenefitList.left_icon:type_name -> api.typesv2.common.VisualElement
	57,  // 164: frontend.tiering.InActiveStateListView.ContainerView.BenefitList.title:type_name -> api.typesv2.common.Text
	57,  // 165: frontend.tiering.InActiveStateListView.ContainerView.BenefitList.subtitle:type_name -> api.typesv2.common.Text
	66,  // 166: frontend.tiering.ActiveStateListView.BenefitList.left_icon:type_name -> api.typesv2.common.VisualElement
	58,  // 167: frontend.tiering.ActiveStateListView.BenefitList.title:type_name -> api.typesv2.ui.IconTextComponent
	51,  // 168: frontend.tiering.ActiveStateListView.BenefitList.right_amount:type_name -> frontend.tiering.ActiveStateListView.BenefitList.RightAmount
	66,  // 169: frontend.tiering.ActiveStateListView.BenefitList.icon:type_name -> api.typesv2.common.VisualElement
	57,  // 170: frontend.tiering.ActiveStateListView.BenefitList.RightAmount.amount:type_name -> api.typesv2.common.Text
	57,  // 171: frontend.tiering.ActiveStateListView.BenefitList.RightAmount.strikethrough_amount:type_name -> api.typesv2.common.Text
	9,   // 172: frontend.tiering.Tiering.GetTieringLaunchInfo:input_type -> frontend.tiering.GetTieringLaunchInfoRequest
	11,  // 173: frontend.tiering.Tiering.GetDeeplink:input_type -> frontend.tiering.GetDeeplinkRequest
	13,  // 174: frontend.tiering.Tiering.Upgrade:input_type -> frontend.tiering.UpgradeRequest
	15,  // 175: frontend.tiering.Tiering.GetTierAllPlans:input_type -> frontend.tiering.GetTierAllPlansRequest
	4,   // 176: frontend.tiering.Tiering.RecordComponentShownToActor:input_type -> frontend.tiering.RecordComponentShownToActorRequest
	17,  // 177: frontend.tiering.Tiering.GetTierEarnedBenefits:input_type -> frontend.tiering.GetTierEarnedBenefitsRequest
	5,   // 178: frontend.tiering.Tiering.GetEarnedBenefitsHistory:input_type -> frontend.tiering.GetEarnedBenefitsHistoryRequest
	29,  // 179: frontend.tiering.Tiering.GetTierFlowScreen:input_type -> frontend.tiering.GetTierFlowScreenRequest
	31,  // 180: frontend.tiering.Tiering.GetTierAllPlansV2:input_type -> frontend.tiering.GetTierAllPlansV2Request
	2,   // 181: frontend.tiering.Tiering.GetDetailedBenefitsBottomSheet:input_type -> frontend.tiering.GetDetailedBenefitsBottomSheetRequest
	35,  // 182: frontend.tiering.Tiering.GetAMBScreenDetails:input_type -> frontend.tiering.GetAMBScreenDetailsRequest
	10,  // 183: frontend.tiering.Tiering.GetTieringLaunchInfo:output_type -> frontend.tiering.GetTieringLaunchInfoResponse
	12,  // 184: frontend.tiering.Tiering.GetDeeplink:output_type -> frontend.tiering.GetDeeplinkResponse
	14,  // 185: frontend.tiering.Tiering.Upgrade:output_type -> frontend.tiering.UpgradeResponse
	16,  // 186: frontend.tiering.Tiering.GetTierAllPlans:output_type -> frontend.tiering.GetTierAllPlansResponse
	8,   // 187: frontend.tiering.Tiering.RecordComponentShownToActor:output_type -> frontend.tiering.RecordComponentShownToActorResponse
	18,  // 188: frontend.tiering.Tiering.GetTierEarnedBenefits:output_type -> frontend.tiering.GetTierEarnedBenefitsResponse
	6,   // 189: frontend.tiering.Tiering.GetEarnedBenefitsHistory:output_type -> frontend.tiering.GetEarnedBenefitsHistoryResponse
	30,  // 190: frontend.tiering.Tiering.GetTierFlowScreen:output_type -> frontend.tiering.GetTierFlowScreenResponse
	32,  // 191: frontend.tiering.Tiering.GetTierAllPlansV2:output_type -> frontend.tiering.GetTierAllPlansV2Response
	3,   // 192: frontend.tiering.Tiering.GetDetailedBenefitsBottomSheet:output_type -> frontend.tiering.GetDetailedBenefitsBottomSheetResponse
	36,  // 193: frontend.tiering.Tiering.GetAMBScreenDetails:output_type -> frontend.tiering.GetAMBScreenDetailsResponse
	183, // [183:194] is the sub-list for method output_type
	172, // [172:183] is the sub-list for method input_type
	172, // [172:172] is the sub-list for extension type_name
	172, // [172:172] is the sub-list for extension extendee
	0,   // [0:172] is the sub-list for field type_name
}

func init() { file_api_frontend_tiering_service_proto_init() }
func file_api_frontend_tiering_service_proto_init() {
	if File_api_frontend_tiering_service_proto != nil {
		return
	}
	file_api_frontend_tiering_earned_benefits_layout_proto_init()
	file_api_frontend_tiering_tiering_layout_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_tiering_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedBenefitsBottomSheetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedBenefitsBottomSheetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordComponentShownToActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEarnedBenefitsHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEarnedBenefitsHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyRewardEarnedView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordComponentShownToActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTieringLaunchInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTieringLaunchInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeeplinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeeplinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierAllPlansRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierAllPlansResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierEarnedBenefitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierEarnedBenefitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferSalaryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeBenefitsView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoreInfoView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TitleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarningView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InActiveStateListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActiveStateListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierFlowScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierFlowScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierAllPlansV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierAllPlansV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierPlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanBenefit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAMBScreenDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAMBScreenDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEarnedBenefitsHistoryResponse_HeaderView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyRewardEarnedView_HeaderView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyRewardEarnedView_BenefitCardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeBenefitsView_Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView_MonthlyBenefits); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView_MonthlyBenefits_TitleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView_MonthlyBenefits_OfferSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarningView_WarningCounterView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InActiveStateListView_ContainerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InActiveStateListView_ContainerView_BenefitList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActiveStateListView_BenefitList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_tiering_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActiveStateListView_BenefitList_RightAmount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_tiering_service_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*BenefitsOptions_ActiveStateListView)(nil),
		(*BenefitsOptions_InActiveStateListView)(nil),
		(*BenefitsOptions_UpgradeBenefits)(nil),
		(*BenefitsOptions_MoreBenefits)(nil),
		(*BenefitsOptions_RetryView)(nil),
		(*BenefitsOptions_TransferSalaryView)(nil),
		(*BenefitsOptions_MonthlyBenefitView)(nil),
		(*BenefitsOptions_WarningView)(nil),
		(*BenefitsOptions_RewardView)(nil),
	}
	file_api_frontend_tiering_service_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*WarningView_WarningCounterView_)(nil),
		(*WarningView_VisualElement)(nil),
	}
	file_api_frontend_tiering_service_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*PlanBenefit_BenefitCard)(nil),
		(*PlanBenefit_SmallEntryBanner)(nil),
		(*PlanBenefit_EntryBanner)(nil),
		(*PlanBenefit_InfoBanner)(nil),
		(*PlanBenefit_LearnMoreBanner)(nil),
	}
	file_api_frontend_tiering_service_proto_msgTypes[42].OneofWrappers = []interface{}{
		(*MonthlyBenefitView_MonthlyBenefits_OfferSection_VisualElement)(nil),
		(*MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView)(nil),
	}
	file_api_frontend_tiering_service_proto_msgTypes[48].OneofWrappers = []interface{}{
		(*ActiveStateListView_BenefitList_RightAmount_)(nil),
		(*ActiveStateListView_BenefitList_Icon)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_tiering_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   50,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_tiering_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_tiering_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_tiering_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_tiering_service_proto_msgTypes,
	}.Build()
	File_api_frontend_tiering_service_proto = out.File
	file_api_frontend_tiering_service_proto_rawDesc = nil
	file_api_frontend_tiering_service_proto_goTypes = nil
	file_api_frontend_tiering_service_proto_depIdxs = nil
}
