// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/rewards/service.proto

package rewards

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Rewards_GetRewardDigest_FullMethodName                           = "/frontend.rewards.Rewards/GetRewardDigest"
	Rewards_GetRewardSummary_FullMethodName                          = "/frontend.rewards.Rewards/GetRewardSummary"
	Rewards_GetActiveRewardOffers_FullMethodName                     = "/frontend.rewards.Rewards/GetActiveRewardOffers"
	Rewards_GetRewardOfferDetails_FullMethodName                     = "/frontend.rewards.Rewards/GetRewardOfferDetails"
	Rewards_GetEarnedRewards_FullMethodName                          = "/frontend.rewards.Rewards/GetEarnedRewards"
	Rewards_GetRewardsForActorV1_FullMethodName                      = "/frontend.rewards.Rewards/GetRewardsForActorV1"
	Rewards_GetExchangerOrdersForActor_FullMethodName                = "/frontend.rewards.Rewards/GetExchangerOrdersForActor"
	Rewards_GetClaimRewardInputScreen_FullMethodName                 = "/frontend.rewards.Rewards/GetClaimRewardInputScreen"
	Rewards_ClaimReward_FullMethodName                               = "/frontend.rewards.Rewards/ClaimReward"
	Rewards_BulkClaimRewards_FullMethodName                          = "/frontend.rewards.Rewards/BulkClaimRewards"
	Rewards_GetActiveOffers_FullMethodName                           = "/frontend.rewards.Rewards/GetActiveOffers"
	Rewards_GetOfferDetailsById_FullMethodName                       = "/frontend.rewards.Rewards/GetOfferDetailsById"
	Rewards_GetOfferCatalogV1_FullMethodName                         = "/frontend.rewards.Rewards/GetOfferCatalogV1"
	Rewards_GetRewardsAndOffersForHome_FullMethodName                = "/frontend.rewards.Rewards/GetRewardsAndOffersForHome"
	Rewards_GetRewardsAndOffersWidget_FullMethodName                 = "/frontend.rewards.Rewards/GetRewardsAndOffersWidget"
	Rewards_GetRedeemOfferInputScreen_FullMethodName                 = "/frontend.rewards.Rewards/GetRedeemOfferInputScreen"
	Rewards_InitiateRedemption_FullMethodName                        = "/frontend.rewards.Rewards/InitiateRedemption"
	Rewards_ConfirmRedemption_FullMethodName                         = "/frontend.rewards.Rewards/ConfirmRedemption"
	Rewards_GetRedeemedOffersV1_FullMethodName                       = "/frontend.rewards.Rewards/GetRedeemedOffersV1"
	Rewards_GetRedeemedOffersCatalogLayout_FullMethodName            = "/frontend.rewards.Rewards/GetRedeemedOffersCatalogLayout"
	Rewards_GetActiveRedeemedOffers_FullMethodName                   = "/frontend.rewards.Rewards/GetActiveRedeemedOffers"
	Rewards_GetExpiredRedeemedOffers_FullMethodName                  = "/frontend.rewards.Rewards/GetExpiredRedeemedOffers"
	Rewards_GetRedeemedOfferDetailsRedirectionInfo_FullMethodName    = "/frontend.rewards.Rewards/GetRedeemedOfferDetailsRedirectionInfo"
	Rewards_GetExchangerOfferById_FullMethodName                     = "/frontend.rewards.Rewards/GetExchangerOfferById"
	Rewards_RedeemExchangerOffer_FullMethodName                      = "/frontend.rewards.Rewards/RedeemExchangerOffer"
	Rewards_ChooseExchangerOrderOption_FullMethodName                = "/frontend.rewards.Rewards/ChooseExchangerOrderOption"
	Rewards_GetExchangerOrderInputScreen_FullMethodName              = "/frontend.rewards.Rewards/GetExchangerOrderInputScreen"
	Rewards_SubmitExchangerOrderUserInput_FullMethodName             = "/frontend.rewards.Rewards/SubmitExchangerOrderUserInput"
	Rewards_GetCatalogOffersAndFilters_FullMethodName                = "/frontend.rewards.Rewards/GetCatalogOffersAndFilters"
	Rewards_GetOffersCatalogPage_FullMethodName                      = "/frontend.rewards.Rewards/GetOffersCatalogPage"
	Rewards_GetOffersCatalogPageVersionToRender_FullMethodName       = "/frontend.rewards.Rewards/GetOffersCatalogPageVersionToRender"
	Rewards_GetCardOffersTabs_FullMethodName                         = "/frontend.rewards.Rewards/GetCardOffersTabs"
	Rewards_GetCardOffersCatalogLayout_FullMethodName                = "/frontend.rewards.Rewards/GetCardOffersCatalogLayout"
	Rewards_GetCardOffers_FullMethodName                             = "/frontend.rewards.Rewards/GetCardOffers"
	Rewards_GetCardOfferDetails_FullMethodName                       = "/frontend.rewards.Rewards/GetCardOfferDetails"
	Rewards_GetMyRewardsScreenDetails_FullMethodName                 = "/frontend.rewards.Rewards/GetMyRewardsScreenDetails"
	Rewards_GetConvertFiCoinsOfferRedemptionScreen_FullMethodName    = "/frontend.rewards.Rewards/GetConvertFiCoinsOfferRedemptionScreen"
	Rewards_GetUserDetailsInputOfferRedemptionScreen_FullMethodName  = "/frontend.rewards.Rewards/GetUserDetailsInputOfferRedemptionScreen"
	Rewards_GetDynamicUrlForWebPageScreen_FullMethodName             = "/frontend.rewards.Rewards/GetDynamicUrlForWebPageScreen"
	Rewards_GetWaysToEarnRewardsScreen_FullMethodName                = "/frontend.rewards.Rewards/GetWaysToEarnRewardsScreen"
	Rewards_GetWaysToEarnRewardDetailScreen_FullMethodName           = "/frontend.rewards.Rewards/GetWaysToEarnRewardDetailScreen"
	Rewards_GetWaysToEarnRewardsScreenVersionToRender_FullMethodName = "/frontend.rewards.Rewards/GetWaysToEarnRewardsScreenVersionToRender"
	Rewards_GetClaimedRewardDetails_FullMethodName                   = "/frontend.rewards.Rewards/GetClaimedRewardDetails"
	Rewards_ForceRetryRewardProcessing_FullMethodName                = "/frontend.rewards.Rewards/ForceRetryRewardProcessing"
)

// RewardsClient is the client API for Rewards service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RewardsClient interface {
	// will be called for home screen tile
	// takes in only auth params and return quick summary need to be displayed on home screen tile
	GetRewardDigest(ctx context.Context, in *GetRewardDigestRequest, opts ...grpc.CallOption) (*GetRewardDigestResponse, error)
	// will be called for my rewards screen
	// takes in only auth params and return summary need to be displayed on my rewards screen
	GetRewardSummary(ctx context.Context, in *GetRewardSummaryRequest, opts ...grpc.CallOption) (*GetRewardSummaryResponse, error)
	// Deprecated: Do not use.
	// will be called for How to earn finvelops screen
	// takes in only auth params and return active reward offerings now
	// Deprecated in favour of GetWaysToEarnRewardsScreen
	GetActiveRewardOffers(ctx context.Context, in *GetActiveRewardOffersRequest, opts ...grpc.CallOption) (*GetActiveRewardOffersResponse, error)
	// Deprecated: Do not use.
	// GetRewardOfferDetails rpc is useful to fetch rewardOffer details by offer-id.
	// Deprecated in favour of GetWaysToEarnRewardDetailScreen
	GetRewardOfferDetails(ctx context.Context, in *GetRewardOfferDetailsRequest, opts ...grpc.CallOption) (*GetRewardOfferDetailsResponse, error)
	// will be called for How to earn finvelops screen
	// takes in only auth params and return active reward offerings now
	GetEarnedRewards(ctx context.Context, in *GetEarnedRewardsRequest, opts ...grpc.CallOption) (*GetEarnedRewardsResponse, error)
	// fetches all the rewards for the actor for displaying on "My Rewards" Screen.
	// On "My Rewards" screen we'll show a combined list of rewards earned by doing a qualifying action as well as the
	// rewards earned by the user by redeeming an exchanger offer.
	GetRewardsForActorV1(ctx context.Context, in *GetRewardsForActorV1Request, opts ...grpc.CallOption) (*GetRewardsForActorV1Response, error)
	// RPC to fetch the exchanger orders for the actor.
	// This RPC returns only the claimed orders, i.e. the ones in in-progress/fulfilled state.
	// Note: verify the logic here if we plan to include "user-intervention-reqd" orders as well in the future.
	GetExchangerOrdersForActor(ctx context.Context, in *GetExchangerOrdersForActorRequest, opts ...grpc.CallOption) (*GetExchangerOrdersForActorResponse, error)
	// will be called just before claimReward rpc to check if input is required for claiming reward.
	// takes in reward id and reward option id and returns deeplink to the screen where user input
	// should be taken.
	GetClaimRewardInputScreen(ctx context.Context, in *ClaimRewardInputScreenRequest, opts ...grpc.CallOption) (*ClaimRewardInputScreenResponse, error)
	// will be called to claim a reward
	ClaimReward(ctx context.Context, in *ClaimRewardRequest, opts ...grpc.CallOption) (*ClaimRewardResponse, error)
	// will be called to claim rewards in bulk
	BulkClaimRewards(ctx context.Context, in *BulkClaimRewardsRequest, opts ...grpc.CallOption) (*BulkClaimRewardsResponse, error)
	// will be called from offers screen
	// takes in only auth params and return active offers now
	GetActiveOffers(ctx context.Context, in *GetActiveOffersRequest, opts ...grpc.CallOption) (*GetActiveOffersResponse, error)
	// takes in offer_id and returns details of the offer only if offer is active
	// will be called from deeplink DEBIT_CARD_OFFERS_HOME_SCREEN if DebitCardOffersHomeScreenOptions has offer_id field set
	GetOfferDetailsById(ctx context.Context, in *GetOfferDetailsByIdRequest, opts ...grpc.CallOption) (*GetOfferDetailsByIdResponse, error)
	// RPC to fetch the catalog of offers where the offers could vary based on their type.
	GetOfferCatalogV1(ctx context.Context, in *GetOfferCatalogV1Request, opts ...grpc.CallOption) (*GetOfferCatalogV1Response, error)
	// RPC to fetch rewards and offers related information for home including -
	// fi coins balance and cta to ways to earn page based on current fi coins balance
	// offers catalog consisting of fi coin offers, debit card offers and exchanger offers
	// Deprecated
	GetRewardsAndOffersForHome(ctx context.Context, in *GetRewardsAndOffersForHomeRequest, opts ...grpc.CallOption) (*GetRewardsAndOffersForHomeResponse, error)
	// RPC to fetch rewards, catalog offers and card offers related information including -
	// fi coins balance, cta to ways to earn page and catalog offers
	// debit card and credit offers
	GetRewardsAndOffersWidget(ctx context.Context, in *GetRewardsAndOffersWidgetRequest, opts ...grpc.CallOption) (*GetRewardsAndOffersWidgetResponse, error)
	// will be called just before InitiateRedemption rpc to check if input is required for redeeming offer.
	// takes in offer id and and returns deeplink to the screen where user input should be taken.
	GetRedeemOfferInputScreen(ctx context.Context, in *RedeemOfferInputScreenRequest, opts ...grpc.CallOption) (*RedeemOfferInputScreenResponse, error)
	// will be called from fi perks screen.
	// used to initiate a redemption.
	InitiateRedemption(ctx context.Context, in *InitiateRedemptionRequest, opts ...grpc.CallOption) (*InitiateRedemptionResponse, error)
	// will be called from fi perks screen.
	// used to confirm an already initiated redemption.
	ConfirmRedemption(ctx context.Context, in *ConfirmRedemptionRequest, opts ...grpc.CallOption) (*ConfirmRedemptionResponse, error)
	// GetRedeemedOffersV1 rpc to fetch active redeemed offers along with exchanger orders (of physical merch and EGV type) for an actor
	// it will be called from the "My Orders" screen.
	GetRedeemedOffersV1(ctx context.Context, in *GetRedeemedOffersV1Request, opts ...grpc.CallOption) (*GetRedeemedOffersV1Response, error)
	// RPC for fetching collected offers page layout.
	// figma -> https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=20606-18937&t=kK4kZztOBk6ehR82-4
	GetRedeemedOffersCatalogLayout(ctx context.Context, in *GetRedeemedOffersCatalogLayoutRequest, opts ...grpc.CallOption) (*GetRedeemedOffersCatalogLayoutResponse, error)
	// will be called from fi perks screen.
	// fetches active redeemed offers for an actor.
	GetActiveRedeemedOffers(ctx context.Context, in *GetActiveRedeemedOffersRequest, opts ...grpc.CallOption) (*GetActiveRedeemedOffersResponse, error)
	// will be called from fi perks screen.
	// fetches expired redeemed offers for an actor.
	GetExpiredRedeemedOffers(ctx context.Context, in *GetExpiredRedeemedOffersRequest, opts ...grpc.CallOption) (*GetExpiredRedeemedOffersResponse, error)
	// rpc to fetch the redirection url for viewing the redeemed offer details,
	// useful for offers where the post the offer redemption on the Fi App, the redeemed offer details are visible on the vendor app only.
	GetRedeemedOfferDetailsRedirectionInfo(ctx context.Context, in *GetRedeemedOfferDetailsRedirectionInfoRequest, opts ...grpc.CallOption) (*GetRedeemedOfferDetailsRedirectionInfoResponse, error)
	// RPC to fetch exchanger offer by id
	GetExchangerOfferById(ctx context.Context, in *GetExchangerOfferByIdRequest, opts ...grpc.CallOption) (*GetExchangerOfferByIdResponse, error)
	// RPC redeems the exchanger offer by trading the currency and returning the options to choose from
	RedeemExchangerOffer(ctx context.Context, in *RedeemExchangerOfferRequest, opts ...grpc.CallOption) (*RedeemExchangerOfferResponse, error)
	// RPC to choose one of the options for an exchanger offer order
	ChooseExchangerOrderOption(ctx context.Context, in *ChooseExchangerOrderOptionRequest, opts ...grpc.CallOption) (*ChooseExchangerOrderOptionResponse, error)
	// RPC to check if any user input is required for the exchanger order. If required,
	// it returns a deeplink to the screen from where input should be taken.
	GetExchangerOrderInputScreen(ctx context.Context, in *GetExchangerOrderInputScreenRequest, opts ...grpc.CallOption) (*GetExchangerOrderInputScreenResponse, error)
	// RPC for submitting input obtained from users required for fulfillment of exchanger order.
	SubmitExchangerOrderUserInput(ctx context.Context, in *SubmitExchangerOrderUserInputRequest, opts ...grpc.CallOption) (*SubmitExchangerOrderUserInputResponse, error)
	// RPC for fetching different components to be shown on CatalogOffersScreen
	GetCatalogOffersAndFilters(ctx context.Context, in *GetCatalogOffersAndFiltersRequest, opts ...grpc.CallOption) (*GetCatalogOffersAndFiltersResponse, error)
	// RPC for fetching catalog offers page
	// Ref : https://docs.google.com/document/d/1RWsQAALGw3N9BR_thBooYhr-GugYKLbpIWPMKy3vSgI/edit#heading=h.c7ch7ebjupsg
	// figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=10010-14735&node-type=canvas&t=9r3xrmHUKNR6nOz8-0
	GetOffersCatalogPage(ctx context.Context, in *GetOffersCatalogPageRequest, opts ...grpc.CallOption) (*GetOffersCatalogPageResponse, error)
	// RPC for fetching which version of offers catalog page to render
	// Ref :
	GetOffersCatalogPageVersionToRender(ctx context.Context, in *GetOffersCatalogPageVersionToRenderRequest, opts ...grpc.CallOption) (*GetOffersCatalogPageVersionToRenderResponse, error)
	// RPC for fetching credit/debit card offers page layout tabs
	// https://www.figma.com/file/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=10230%3A12810&mode=design&t=e3mtrgFYWCSAMclM-1
	GetCardOffersTabs(ctx context.Context, in *GetCardOffersTabsRequest, opts ...grpc.CallOption) (*GetCardOffersTabsResponse, error)
	// RPC for fetching credit/debit card offers page layout.
	GetCardOffersCatalogLayout(ctx context.Context, in *GetCardOffersCatalogLayoutRequest, opts ...grpc.CallOption) (*GetCardOffersCatalogLayoutResponse, error)
	// RPC for fetching fi card offers.
	GetCardOffers(ctx context.Context, in *GetCardOffersRequest, opts ...grpc.CallOption) (*GetCardOffersResponse, error)
	// GetCardOfferDetails fetches credit/debit card offer details by offer id.
	GetCardOfferDetails(ctx context.Context, in *GetCardOfferDetailsRequest, opts ...grpc.CallOption) (*GetCardOfferDetailsResponse, error)
	// rpc to fetch MyRewards screen details for actor
	GetMyRewardsScreenDetails(ctx context.Context, in *GetMyRewardsScreenDetailsRequest, opts ...grpc.CallOption) (*GetMyRewardsScreenDetailsResponse, error)
	// RPC for getting details for the convert fi coins screen in offer redemption flow
	GetConvertFiCoinsOfferRedemptionScreen(ctx context.Context, in *GetConvertFiCoinsOfferRedemptionScreenRequest, opts ...grpc.CallOption) (*GetConvertFiCoinsOfferRedemptionScreenResponse, error)
	// RPC for getting details for the user details screen in offer redemption flow
	GetUserDetailsInputOfferRedemptionScreen(ctx context.Context, in *GetUserDetailsInputOfferRedemptionScreenRequest, opts ...grpc.CallOption) (*GetUserDetailsInputOfferRedemptionScreenResponse, error)
	// RPC for getting dynamic url for webpage with card details screen
	GetDynamicUrlForWebPageScreen(ctx context.Context, in *GetDynamicUrlForWebPageScreenRequest, opts ...grpc.CallOption) (*GetDynamicUrlForWebPageScreenResponse, error)
	// RPC for getting ways to earn rewards screen
	GetWaysToEarnRewardsScreen(ctx context.Context, in *GetWaysToEarnRewardsRequest, opts ...grpc.CallOption) (*GetWaysToEarnRewardsResponse, error)
	// RPC for getting ways to earn detail screen
	GetWaysToEarnRewardDetailScreen(ctx context.Context, in *GetWaysToEarnRewardDetailRequest, opts ...grpc.CallOption) (*GetWaysToEarnRewardDetailResponse, error)
	// RPC for telling client which WaysToEarnRewards screen is to be rendered (v1 or v2)
	// this is a temporary RPC being used for transitioning from V1 of ways to earn rewards screen to V2
	GetWaysToEarnRewardsScreenVersionToRender(ctx context.Context, in *GetWaysToEarnRewardsScreenVersionToRenderRequest, opts ...grpc.CallOption) (*GetWaysToEarnRewardsScreenVersionToRenderResponse, error)
	// RPC to return the details to render a Claimed Reward or Exchanged rewards screen on clients. Figma:
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=12244%3A79466&t=buqkfpbtIexoCbix-4
	GetClaimedRewardDetails(ctx context.Context, in *GetClaimedRewardDetailsRequest, opts ...grpc.CallOption) (*GetClaimedRewardDetailsResponse, error)
	// RPC to force retry reward processing for stuck rewards
	ForceRetryRewardProcessing(ctx context.Context, in *ForceRetryRewardProcessingRequest, opts ...grpc.CallOption) (*ForceRetryRewardProcessingResponse, error)
}

type rewardsClient struct {
	cc grpc.ClientConnInterface
}

func NewRewardsClient(cc grpc.ClientConnInterface) RewardsClient {
	return &rewardsClient{cc}
}

func (c *rewardsClient) GetRewardDigest(ctx context.Context, in *GetRewardDigestRequest, opts ...grpc.CallOption) (*GetRewardDigestResponse, error) {
	out := new(GetRewardDigestResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRewardDigest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRewardSummary(ctx context.Context, in *GetRewardSummaryRequest, opts ...grpc.CallOption) (*GetRewardSummaryResponse, error) {
	out := new(GetRewardSummaryResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRewardSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *rewardsClient) GetActiveRewardOffers(ctx context.Context, in *GetActiveRewardOffersRequest, opts ...grpc.CallOption) (*GetActiveRewardOffersResponse, error) {
	out := new(GetActiveRewardOffersResponse)
	err := c.cc.Invoke(ctx, Rewards_GetActiveRewardOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *rewardsClient) GetRewardOfferDetails(ctx context.Context, in *GetRewardOfferDetailsRequest, opts ...grpc.CallOption) (*GetRewardOfferDetailsResponse, error) {
	out := new(GetRewardOfferDetailsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRewardOfferDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetEarnedRewards(ctx context.Context, in *GetEarnedRewardsRequest, opts ...grpc.CallOption) (*GetEarnedRewardsResponse, error) {
	out := new(GetEarnedRewardsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetEarnedRewards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRewardsForActorV1(ctx context.Context, in *GetRewardsForActorV1Request, opts ...grpc.CallOption) (*GetRewardsForActorV1Response, error) {
	out := new(GetRewardsForActorV1Response)
	err := c.cc.Invoke(ctx, Rewards_GetRewardsForActorV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetExchangerOrdersForActor(ctx context.Context, in *GetExchangerOrdersForActorRequest, opts ...grpc.CallOption) (*GetExchangerOrdersForActorResponse, error) {
	out := new(GetExchangerOrdersForActorResponse)
	err := c.cc.Invoke(ctx, Rewards_GetExchangerOrdersForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetClaimRewardInputScreen(ctx context.Context, in *ClaimRewardInputScreenRequest, opts ...grpc.CallOption) (*ClaimRewardInputScreenResponse, error) {
	out := new(ClaimRewardInputScreenResponse)
	err := c.cc.Invoke(ctx, Rewards_GetClaimRewardInputScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) ClaimReward(ctx context.Context, in *ClaimRewardRequest, opts ...grpc.CallOption) (*ClaimRewardResponse, error) {
	out := new(ClaimRewardResponse)
	err := c.cc.Invoke(ctx, Rewards_ClaimReward_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) BulkClaimRewards(ctx context.Context, in *BulkClaimRewardsRequest, opts ...grpc.CallOption) (*BulkClaimRewardsResponse, error) {
	out := new(BulkClaimRewardsResponse)
	err := c.cc.Invoke(ctx, Rewards_BulkClaimRewards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetActiveOffers(ctx context.Context, in *GetActiveOffersRequest, opts ...grpc.CallOption) (*GetActiveOffersResponse, error) {
	out := new(GetActiveOffersResponse)
	err := c.cc.Invoke(ctx, Rewards_GetActiveOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetOfferDetailsById(ctx context.Context, in *GetOfferDetailsByIdRequest, opts ...grpc.CallOption) (*GetOfferDetailsByIdResponse, error) {
	out := new(GetOfferDetailsByIdResponse)
	err := c.cc.Invoke(ctx, Rewards_GetOfferDetailsById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetOfferCatalogV1(ctx context.Context, in *GetOfferCatalogV1Request, opts ...grpc.CallOption) (*GetOfferCatalogV1Response, error) {
	out := new(GetOfferCatalogV1Response)
	err := c.cc.Invoke(ctx, Rewards_GetOfferCatalogV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRewardsAndOffersForHome(ctx context.Context, in *GetRewardsAndOffersForHomeRequest, opts ...grpc.CallOption) (*GetRewardsAndOffersForHomeResponse, error) {
	out := new(GetRewardsAndOffersForHomeResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRewardsAndOffersForHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRewardsAndOffersWidget(ctx context.Context, in *GetRewardsAndOffersWidgetRequest, opts ...grpc.CallOption) (*GetRewardsAndOffersWidgetResponse, error) {
	out := new(GetRewardsAndOffersWidgetResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRewardsAndOffersWidget_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRedeemOfferInputScreen(ctx context.Context, in *RedeemOfferInputScreenRequest, opts ...grpc.CallOption) (*RedeemOfferInputScreenResponse, error) {
	out := new(RedeemOfferInputScreenResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRedeemOfferInputScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) InitiateRedemption(ctx context.Context, in *InitiateRedemptionRequest, opts ...grpc.CallOption) (*InitiateRedemptionResponse, error) {
	out := new(InitiateRedemptionResponse)
	err := c.cc.Invoke(ctx, Rewards_InitiateRedemption_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) ConfirmRedemption(ctx context.Context, in *ConfirmRedemptionRequest, opts ...grpc.CallOption) (*ConfirmRedemptionResponse, error) {
	out := new(ConfirmRedemptionResponse)
	err := c.cc.Invoke(ctx, Rewards_ConfirmRedemption_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRedeemedOffersV1(ctx context.Context, in *GetRedeemedOffersV1Request, opts ...grpc.CallOption) (*GetRedeemedOffersV1Response, error) {
	out := new(GetRedeemedOffersV1Response)
	err := c.cc.Invoke(ctx, Rewards_GetRedeemedOffersV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRedeemedOffersCatalogLayout(ctx context.Context, in *GetRedeemedOffersCatalogLayoutRequest, opts ...grpc.CallOption) (*GetRedeemedOffersCatalogLayoutResponse, error) {
	out := new(GetRedeemedOffersCatalogLayoutResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRedeemedOffersCatalogLayout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetActiveRedeemedOffers(ctx context.Context, in *GetActiveRedeemedOffersRequest, opts ...grpc.CallOption) (*GetActiveRedeemedOffersResponse, error) {
	out := new(GetActiveRedeemedOffersResponse)
	err := c.cc.Invoke(ctx, Rewards_GetActiveRedeemedOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetExpiredRedeemedOffers(ctx context.Context, in *GetExpiredRedeemedOffersRequest, opts ...grpc.CallOption) (*GetExpiredRedeemedOffersResponse, error) {
	out := new(GetExpiredRedeemedOffersResponse)
	err := c.cc.Invoke(ctx, Rewards_GetExpiredRedeemedOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetRedeemedOfferDetailsRedirectionInfo(ctx context.Context, in *GetRedeemedOfferDetailsRedirectionInfoRequest, opts ...grpc.CallOption) (*GetRedeemedOfferDetailsRedirectionInfoResponse, error) {
	out := new(GetRedeemedOfferDetailsRedirectionInfoResponse)
	err := c.cc.Invoke(ctx, Rewards_GetRedeemedOfferDetailsRedirectionInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetExchangerOfferById(ctx context.Context, in *GetExchangerOfferByIdRequest, opts ...grpc.CallOption) (*GetExchangerOfferByIdResponse, error) {
	out := new(GetExchangerOfferByIdResponse)
	err := c.cc.Invoke(ctx, Rewards_GetExchangerOfferById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) RedeemExchangerOffer(ctx context.Context, in *RedeemExchangerOfferRequest, opts ...grpc.CallOption) (*RedeemExchangerOfferResponse, error) {
	out := new(RedeemExchangerOfferResponse)
	err := c.cc.Invoke(ctx, Rewards_RedeemExchangerOffer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) ChooseExchangerOrderOption(ctx context.Context, in *ChooseExchangerOrderOptionRequest, opts ...grpc.CallOption) (*ChooseExchangerOrderOptionResponse, error) {
	out := new(ChooseExchangerOrderOptionResponse)
	err := c.cc.Invoke(ctx, Rewards_ChooseExchangerOrderOption_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetExchangerOrderInputScreen(ctx context.Context, in *GetExchangerOrderInputScreenRequest, opts ...grpc.CallOption) (*GetExchangerOrderInputScreenResponse, error) {
	out := new(GetExchangerOrderInputScreenResponse)
	err := c.cc.Invoke(ctx, Rewards_GetExchangerOrderInputScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) SubmitExchangerOrderUserInput(ctx context.Context, in *SubmitExchangerOrderUserInputRequest, opts ...grpc.CallOption) (*SubmitExchangerOrderUserInputResponse, error) {
	out := new(SubmitExchangerOrderUserInputResponse)
	err := c.cc.Invoke(ctx, Rewards_SubmitExchangerOrderUserInput_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetCatalogOffersAndFilters(ctx context.Context, in *GetCatalogOffersAndFiltersRequest, opts ...grpc.CallOption) (*GetCatalogOffersAndFiltersResponse, error) {
	out := new(GetCatalogOffersAndFiltersResponse)
	err := c.cc.Invoke(ctx, Rewards_GetCatalogOffersAndFilters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetOffersCatalogPage(ctx context.Context, in *GetOffersCatalogPageRequest, opts ...grpc.CallOption) (*GetOffersCatalogPageResponse, error) {
	out := new(GetOffersCatalogPageResponse)
	err := c.cc.Invoke(ctx, Rewards_GetOffersCatalogPage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetOffersCatalogPageVersionToRender(ctx context.Context, in *GetOffersCatalogPageVersionToRenderRequest, opts ...grpc.CallOption) (*GetOffersCatalogPageVersionToRenderResponse, error) {
	out := new(GetOffersCatalogPageVersionToRenderResponse)
	err := c.cc.Invoke(ctx, Rewards_GetOffersCatalogPageVersionToRender_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetCardOffersTabs(ctx context.Context, in *GetCardOffersTabsRequest, opts ...grpc.CallOption) (*GetCardOffersTabsResponse, error) {
	out := new(GetCardOffersTabsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetCardOffersTabs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetCardOffersCatalogLayout(ctx context.Context, in *GetCardOffersCatalogLayoutRequest, opts ...grpc.CallOption) (*GetCardOffersCatalogLayoutResponse, error) {
	out := new(GetCardOffersCatalogLayoutResponse)
	err := c.cc.Invoke(ctx, Rewards_GetCardOffersCatalogLayout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetCardOffers(ctx context.Context, in *GetCardOffersRequest, opts ...grpc.CallOption) (*GetCardOffersResponse, error) {
	out := new(GetCardOffersResponse)
	err := c.cc.Invoke(ctx, Rewards_GetCardOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetCardOfferDetails(ctx context.Context, in *GetCardOfferDetailsRequest, opts ...grpc.CallOption) (*GetCardOfferDetailsResponse, error) {
	out := new(GetCardOfferDetailsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetCardOfferDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetMyRewardsScreenDetails(ctx context.Context, in *GetMyRewardsScreenDetailsRequest, opts ...grpc.CallOption) (*GetMyRewardsScreenDetailsResponse, error) {
	out := new(GetMyRewardsScreenDetailsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetMyRewardsScreenDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetConvertFiCoinsOfferRedemptionScreen(ctx context.Context, in *GetConvertFiCoinsOfferRedemptionScreenRequest, opts ...grpc.CallOption) (*GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	out := new(GetConvertFiCoinsOfferRedemptionScreenResponse)
	err := c.cc.Invoke(ctx, Rewards_GetConvertFiCoinsOfferRedemptionScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetUserDetailsInputOfferRedemptionScreen(ctx context.Context, in *GetUserDetailsInputOfferRedemptionScreenRequest, opts ...grpc.CallOption) (*GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	out := new(GetUserDetailsInputOfferRedemptionScreenResponse)
	err := c.cc.Invoke(ctx, Rewards_GetUserDetailsInputOfferRedemptionScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetDynamicUrlForWebPageScreen(ctx context.Context, in *GetDynamicUrlForWebPageScreenRequest, opts ...grpc.CallOption) (*GetDynamicUrlForWebPageScreenResponse, error) {
	out := new(GetDynamicUrlForWebPageScreenResponse)
	err := c.cc.Invoke(ctx, Rewards_GetDynamicUrlForWebPageScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetWaysToEarnRewardsScreen(ctx context.Context, in *GetWaysToEarnRewardsRequest, opts ...grpc.CallOption) (*GetWaysToEarnRewardsResponse, error) {
	out := new(GetWaysToEarnRewardsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetWaysToEarnRewardsScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetWaysToEarnRewardDetailScreen(ctx context.Context, in *GetWaysToEarnRewardDetailRequest, opts ...grpc.CallOption) (*GetWaysToEarnRewardDetailResponse, error) {
	out := new(GetWaysToEarnRewardDetailResponse)
	err := c.cc.Invoke(ctx, Rewards_GetWaysToEarnRewardDetailScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetWaysToEarnRewardsScreenVersionToRender(ctx context.Context, in *GetWaysToEarnRewardsScreenVersionToRenderRequest, opts ...grpc.CallOption) (*GetWaysToEarnRewardsScreenVersionToRenderResponse, error) {
	out := new(GetWaysToEarnRewardsScreenVersionToRenderResponse)
	err := c.cc.Invoke(ctx, Rewards_GetWaysToEarnRewardsScreenVersionToRender_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) GetClaimedRewardDetails(ctx context.Context, in *GetClaimedRewardDetailsRequest, opts ...grpc.CallOption) (*GetClaimedRewardDetailsResponse, error) {
	out := new(GetClaimedRewardDetailsResponse)
	err := c.cc.Invoke(ctx, Rewards_GetClaimedRewardDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardsClient) ForceRetryRewardProcessing(ctx context.Context, in *ForceRetryRewardProcessingRequest, opts ...grpc.CallOption) (*ForceRetryRewardProcessingResponse, error) {
	out := new(ForceRetryRewardProcessingResponse)
	err := c.cc.Invoke(ctx, Rewards_ForceRetryRewardProcessing_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RewardsServer is the server API for Rewards service.
// All implementations should embed UnimplementedRewardsServer
// for forward compatibility
type RewardsServer interface {
	// will be called for home screen tile
	// takes in only auth params and return quick summary need to be displayed on home screen tile
	GetRewardDigest(context.Context, *GetRewardDigestRequest) (*GetRewardDigestResponse, error)
	// will be called for my rewards screen
	// takes in only auth params and return summary need to be displayed on my rewards screen
	GetRewardSummary(context.Context, *GetRewardSummaryRequest) (*GetRewardSummaryResponse, error)
	// Deprecated: Do not use.
	// will be called for How to earn finvelops screen
	// takes in only auth params and return active reward offerings now
	// Deprecated in favour of GetWaysToEarnRewardsScreen
	GetActiveRewardOffers(context.Context, *GetActiveRewardOffersRequest) (*GetActiveRewardOffersResponse, error)
	// Deprecated: Do not use.
	// GetRewardOfferDetails rpc is useful to fetch rewardOffer details by offer-id.
	// Deprecated in favour of GetWaysToEarnRewardDetailScreen
	GetRewardOfferDetails(context.Context, *GetRewardOfferDetailsRequest) (*GetRewardOfferDetailsResponse, error)
	// will be called for How to earn finvelops screen
	// takes in only auth params and return active reward offerings now
	GetEarnedRewards(context.Context, *GetEarnedRewardsRequest) (*GetEarnedRewardsResponse, error)
	// fetches all the rewards for the actor for displaying on "My Rewards" Screen.
	// On "My Rewards" screen we'll show a combined list of rewards earned by doing a qualifying action as well as the
	// rewards earned by the user by redeeming an exchanger offer.
	GetRewardsForActorV1(context.Context, *GetRewardsForActorV1Request) (*GetRewardsForActorV1Response, error)
	// RPC to fetch the exchanger orders for the actor.
	// This RPC returns only the claimed orders, i.e. the ones in in-progress/fulfilled state.
	// Note: verify the logic here if we plan to include "user-intervention-reqd" orders as well in the future.
	GetExchangerOrdersForActor(context.Context, *GetExchangerOrdersForActorRequest) (*GetExchangerOrdersForActorResponse, error)
	// will be called just before claimReward rpc to check if input is required for claiming reward.
	// takes in reward id and reward option id and returns deeplink to the screen where user input
	// should be taken.
	GetClaimRewardInputScreen(context.Context, *ClaimRewardInputScreenRequest) (*ClaimRewardInputScreenResponse, error)
	// will be called to claim a reward
	ClaimReward(context.Context, *ClaimRewardRequest) (*ClaimRewardResponse, error)
	// will be called to claim rewards in bulk
	BulkClaimRewards(context.Context, *BulkClaimRewardsRequest) (*BulkClaimRewardsResponse, error)
	// will be called from offers screen
	// takes in only auth params and return active offers now
	GetActiveOffers(context.Context, *GetActiveOffersRequest) (*GetActiveOffersResponse, error)
	// takes in offer_id and returns details of the offer only if offer is active
	// will be called from deeplink DEBIT_CARD_OFFERS_HOME_SCREEN if DebitCardOffersHomeScreenOptions has offer_id field set
	GetOfferDetailsById(context.Context, *GetOfferDetailsByIdRequest) (*GetOfferDetailsByIdResponse, error)
	// RPC to fetch the catalog of offers where the offers could vary based on their type.
	GetOfferCatalogV1(context.Context, *GetOfferCatalogV1Request) (*GetOfferCatalogV1Response, error)
	// RPC to fetch rewards and offers related information for home including -
	// fi coins balance and cta to ways to earn page based on current fi coins balance
	// offers catalog consisting of fi coin offers, debit card offers and exchanger offers
	// Deprecated
	GetRewardsAndOffersForHome(context.Context, *GetRewardsAndOffersForHomeRequest) (*GetRewardsAndOffersForHomeResponse, error)
	// RPC to fetch rewards, catalog offers and card offers related information including -
	// fi coins balance, cta to ways to earn page and catalog offers
	// debit card and credit offers
	GetRewardsAndOffersWidget(context.Context, *GetRewardsAndOffersWidgetRequest) (*GetRewardsAndOffersWidgetResponse, error)
	// will be called just before InitiateRedemption rpc to check if input is required for redeeming offer.
	// takes in offer id and and returns deeplink to the screen where user input should be taken.
	GetRedeemOfferInputScreen(context.Context, *RedeemOfferInputScreenRequest) (*RedeemOfferInputScreenResponse, error)
	// will be called from fi perks screen.
	// used to initiate a redemption.
	InitiateRedemption(context.Context, *InitiateRedemptionRequest) (*InitiateRedemptionResponse, error)
	// will be called from fi perks screen.
	// used to confirm an already initiated redemption.
	ConfirmRedemption(context.Context, *ConfirmRedemptionRequest) (*ConfirmRedemptionResponse, error)
	// GetRedeemedOffersV1 rpc to fetch active redeemed offers along with exchanger orders (of physical merch and EGV type) for an actor
	// it will be called from the "My Orders" screen.
	GetRedeemedOffersV1(context.Context, *GetRedeemedOffersV1Request) (*GetRedeemedOffersV1Response, error)
	// RPC for fetching collected offers page layout.
	// figma -> https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=20606-18937&t=kK4kZztOBk6ehR82-4
	GetRedeemedOffersCatalogLayout(context.Context, *GetRedeemedOffersCatalogLayoutRequest) (*GetRedeemedOffersCatalogLayoutResponse, error)
	// will be called from fi perks screen.
	// fetches active redeemed offers for an actor.
	GetActiveRedeemedOffers(context.Context, *GetActiveRedeemedOffersRequest) (*GetActiveRedeemedOffersResponse, error)
	// will be called from fi perks screen.
	// fetches expired redeemed offers for an actor.
	GetExpiredRedeemedOffers(context.Context, *GetExpiredRedeemedOffersRequest) (*GetExpiredRedeemedOffersResponse, error)
	// rpc to fetch the redirection url for viewing the redeemed offer details,
	// useful for offers where the post the offer redemption on the Fi App, the redeemed offer details are visible on the vendor app only.
	GetRedeemedOfferDetailsRedirectionInfo(context.Context, *GetRedeemedOfferDetailsRedirectionInfoRequest) (*GetRedeemedOfferDetailsRedirectionInfoResponse, error)
	// RPC to fetch exchanger offer by id
	GetExchangerOfferById(context.Context, *GetExchangerOfferByIdRequest) (*GetExchangerOfferByIdResponse, error)
	// RPC redeems the exchanger offer by trading the currency and returning the options to choose from
	RedeemExchangerOffer(context.Context, *RedeemExchangerOfferRequest) (*RedeemExchangerOfferResponse, error)
	// RPC to choose one of the options for an exchanger offer order
	ChooseExchangerOrderOption(context.Context, *ChooseExchangerOrderOptionRequest) (*ChooseExchangerOrderOptionResponse, error)
	// RPC to check if any user input is required for the exchanger order. If required,
	// it returns a deeplink to the screen from where input should be taken.
	GetExchangerOrderInputScreen(context.Context, *GetExchangerOrderInputScreenRequest) (*GetExchangerOrderInputScreenResponse, error)
	// RPC for submitting input obtained from users required for fulfillment of exchanger order.
	SubmitExchangerOrderUserInput(context.Context, *SubmitExchangerOrderUserInputRequest) (*SubmitExchangerOrderUserInputResponse, error)
	// RPC for fetching different components to be shown on CatalogOffersScreen
	GetCatalogOffersAndFilters(context.Context, *GetCatalogOffersAndFiltersRequest) (*GetCatalogOffersAndFiltersResponse, error)
	// RPC for fetching catalog offers page
	// Ref : https://docs.google.com/document/d/1RWsQAALGw3N9BR_thBooYhr-GugYKLbpIWPMKy3vSgI/edit#heading=h.c7ch7ebjupsg
	// figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=10010-14735&node-type=canvas&t=9r3xrmHUKNR6nOz8-0
	GetOffersCatalogPage(context.Context, *GetOffersCatalogPageRequest) (*GetOffersCatalogPageResponse, error)
	// RPC for fetching which version of offers catalog page to render
	// Ref :
	GetOffersCatalogPageVersionToRender(context.Context, *GetOffersCatalogPageVersionToRenderRequest) (*GetOffersCatalogPageVersionToRenderResponse, error)
	// RPC for fetching credit/debit card offers page layout tabs
	// https://www.figma.com/file/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=10230%3A12810&mode=design&t=e3mtrgFYWCSAMclM-1
	GetCardOffersTabs(context.Context, *GetCardOffersTabsRequest) (*GetCardOffersTabsResponse, error)
	// RPC for fetching credit/debit card offers page layout.
	GetCardOffersCatalogLayout(context.Context, *GetCardOffersCatalogLayoutRequest) (*GetCardOffersCatalogLayoutResponse, error)
	// RPC for fetching fi card offers.
	GetCardOffers(context.Context, *GetCardOffersRequest) (*GetCardOffersResponse, error)
	// GetCardOfferDetails fetches credit/debit card offer details by offer id.
	GetCardOfferDetails(context.Context, *GetCardOfferDetailsRequest) (*GetCardOfferDetailsResponse, error)
	// rpc to fetch MyRewards screen details for actor
	GetMyRewardsScreenDetails(context.Context, *GetMyRewardsScreenDetailsRequest) (*GetMyRewardsScreenDetailsResponse, error)
	// RPC for getting details for the convert fi coins screen in offer redemption flow
	GetConvertFiCoinsOfferRedemptionScreen(context.Context, *GetConvertFiCoinsOfferRedemptionScreenRequest) (*GetConvertFiCoinsOfferRedemptionScreenResponse, error)
	// RPC for getting details for the user details screen in offer redemption flow
	GetUserDetailsInputOfferRedemptionScreen(context.Context, *GetUserDetailsInputOfferRedemptionScreenRequest) (*GetUserDetailsInputOfferRedemptionScreenResponse, error)
	// RPC for getting dynamic url for webpage with card details screen
	GetDynamicUrlForWebPageScreen(context.Context, *GetDynamicUrlForWebPageScreenRequest) (*GetDynamicUrlForWebPageScreenResponse, error)
	// RPC for getting ways to earn rewards screen
	GetWaysToEarnRewardsScreen(context.Context, *GetWaysToEarnRewardsRequest) (*GetWaysToEarnRewardsResponse, error)
	// RPC for getting ways to earn detail screen
	GetWaysToEarnRewardDetailScreen(context.Context, *GetWaysToEarnRewardDetailRequest) (*GetWaysToEarnRewardDetailResponse, error)
	// RPC for telling client which WaysToEarnRewards screen is to be rendered (v1 or v2)
	// this is a temporary RPC being used for transitioning from V1 of ways to earn rewards screen to V2
	GetWaysToEarnRewardsScreenVersionToRender(context.Context, *GetWaysToEarnRewardsScreenVersionToRenderRequest) (*GetWaysToEarnRewardsScreenVersionToRenderResponse, error)
	// RPC to return the details to render a Claimed Reward or Exchanged rewards screen on clients. Figma:
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=12244%3A79466&t=buqkfpbtIexoCbix-4
	GetClaimedRewardDetails(context.Context, *GetClaimedRewardDetailsRequest) (*GetClaimedRewardDetailsResponse, error)
	// RPC to force retry reward processing for stuck rewards
	ForceRetryRewardProcessing(context.Context, *ForceRetryRewardProcessingRequest) (*ForceRetryRewardProcessingResponse, error)
}

// UnimplementedRewardsServer should be embedded to have forward compatible implementations.
type UnimplementedRewardsServer struct {
}

func (UnimplementedRewardsServer) GetRewardDigest(context.Context, *GetRewardDigestRequest) (*GetRewardDigestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardDigest not implemented")
}
func (UnimplementedRewardsServer) GetRewardSummary(context.Context, *GetRewardSummaryRequest) (*GetRewardSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardSummary not implemented")
}
func (UnimplementedRewardsServer) GetActiveRewardOffers(context.Context, *GetActiveRewardOffersRequest) (*GetActiveRewardOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveRewardOffers not implemented")
}
func (UnimplementedRewardsServer) GetRewardOfferDetails(context.Context, *GetRewardOfferDetailsRequest) (*GetRewardOfferDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardOfferDetails not implemented")
}
func (UnimplementedRewardsServer) GetEarnedRewards(context.Context, *GetEarnedRewardsRequest) (*GetEarnedRewardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEarnedRewards not implemented")
}
func (UnimplementedRewardsServer) GetRewardsForActorV1(context.Context, *GetRewardsForActorV1Request) (*GetRewardsForActorV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardsForActorV1 not implemented")
}
func (UnimplementedRewardsServer) GetExchangerOrdersForActor(context.Context, *GetExchangerOrdersForActorRequest) (*GetExchangerOrdersForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExchangerOrdersForActor not implemented")
}
func (UnimplementedRewardsServer) GetClaimRewardInputScreen(context.Context, *ClaimRewardInputScreenRequest) (*ClaimRewardInputScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClaimRewardInputScreen not implemented")
}
func (UnimplementedRewardsServer) ClaimReward(context.Context, *ClaimRewardRequest) (*ClaimRewardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClaimReward not implemented")
}
func (UnimplementedRewardsServer) BulkClaimRewards(context.Context, *BulkClaimRewardsRequest) (*BulkClaimRewardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkClaimRewards not implemented")
}
func (UnimplementedRewardsServer) GetActiveOffers(context.Context, *GetActiveOffersRequest) (*GetActiveOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveOffers not implemented")
}
func (UnimplementedRewardsServer) GetOfferDetailsById(context.Context, *GetOfferDetailsByIdRequest) (*GetOfferDetailsByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfferDetailsById not implemented")
}
func (UnimplementedRewardsServer) GetOfferCatalogV1(context.Context, *GetOfferCatalogV1Request) (*GetOfferCatalogV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfferCatalogV1 not implemented")
}
func (UnimplementedRewardsServer) GetRewardsAndOffersForHome(context.Context, *GetRewardsAndOffersForHomeRequest) (*GetRewardsAndOffersForHomeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardsAndOffersForHome not implemented")
}
func (UnimplementedRewardsServer) GetRewardsAndOffersWidget(context.Context, *GetRewardsAndOffersWidgetRequest) (*GetRewardsAndOffersWidgetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardsAndOffersWidget not implemented")
}
func (UnimplementedRewardsServer) GetRedeemOfferInputScreen(context.Context, *RedeemOfferInputScreenRequest) (*RedeemOfferInputScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemOfferInputScreen not implemented")
}
func (UnimplementedRewardsServer) InitiateRedemption(context.Context, *InitiateRedemptionRequest) (*InitiateRedemptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateRedemption not implemented")
}
func (UnimplementedRewardsServer) ConfirmRedemption(context.Context, *ConfirmRedemptionRequest) (*ConfirmRedemptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmRedemption not implemented")
}
func (UnimplementedRewardsServer) GetRedeemedOffersV1(context.Context, *GetRedeemedOffersV1Request) (*GetRedeemedOffersV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemedOffersV1 not implemented")
}
func (UnimplementedRewardsServer) GetRedeemedOffersCatalogLayout(context.Context, *GetRedeemedOffersCatalogLayoutRequest) (*GetRedeemedOffersCatalogLayoutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemedOffersCatalogLayout not implemented")
}
func (UnimplementedRewardsServer) GetActiveRedeemedOffers(context.Context, *GetActiveRedeemedOffersRequest) (*GetActiveRedeemedOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveRedeemedOffers not implemented")
}
func (UnimplementedRewardsServer) GetExpiredRedeemedOffers(context.Context, *GetExpiredRedeemedOffersRequest) (*GetExpiredRedeemedOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpiredRedeemedOffers not implemented")
}
func (UnimplementedRewardsServer) GetRedeemedOfferDetailsRedirectionInfo(context.Context, *GetRedeemedOfferDetailsRedirectionInfoRequest) (*GetRedeemedOfferDetailsRedirectionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemedOfferDetailsRedirectionInfo not implemented")
}
func (UnimplementedRewardsServer) GetExchangerOfferById(context.Context, *GetExchangerOfferByIdRequest) (*GetExchangerOfferByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExchangerOfferById not implemented")
}
func (UnimplementedRewardsServer) RedeemExchangerOffer(context.Context, *RedeemExchangerOfferRequest) (*RedeemExchangerOfferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedeemExchangerOffer not implemented")
}
func (UnimplementedRewardsServer) ChooseExchangerOrderOption(context.Context, *ChooseExchangerOrderOptionRequest) (*ChooseExchangerOrderOptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseExchangerOrderOption not implemented")
}
func (UnimplementedRewardsServer) GetExchangerOrderInputScreen(context.Context, *GetExchangerOrderInputScreenRequest) (*GetExchangerOrderInputScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExchangerOrderInputScreen not implemented")
}
func (UnimplementedRewardsServer) SubmitExchangerOrderUserInput(context.Context, *SubmitExchangerOrderUserInputRequest) (*SubmitExchangerOrderUserInputResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitExchangerOrderUserInput not implemented")
}
func (UnimplementedRewardsServer) GetCatalogOffersAndFilters(context.Context, *GetCatalogOffersAndFiltersRequest) (*GetCatalogOffersAndFiltersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCatalogOffersAndFilters not implemented")
}
func (UnimplementedRewardsServer) GetOffersCatalogPage(context.Context, *GetOffersCatalogPageRequest) (*GetOffersCatalogPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOffersCatalogPage not implemented")
}
func (UnimplementedRewardsServer) GetOffersCatalogPageVersionToRender(context.Context, *GetOffersCatalogPageVersionToRenderRequest) (*GetOffersCatalogPageVersionToRenderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOffersCatalogPageVersionToRender not implemented")
}
func (UnimplementedRewardsServer) GetCardOffersTabs(context.Context, *GetCardOffersTabsRequest) (*GetCardOffersTabsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardOffersTabs not implemented")
}
func (UnimplementedRewardsServer) GetCardOffersCatalogLayout(context.Context, *GetCardOffersCatalogLayoutRequest) (*GetCardOffersCatalogLayoutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardOffersCatalogLayout not implemented")
}
func (UnimplementedRewardsServer) GetCardOffers(context.Context, *GetCardOffersRequest) (*GetCardOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardOffers not implemented")
}
func (UnimplementedRewardsServer) GetCardOfferDetails(context.Context, *GetCardOfferDetailsRequest) (*GetCardOfferDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardOfferDetails not implemented")
}
func (UnimplementedRewardsServer) GetMyRewardsScreenDetails(context.Context, *GetMyRewardsScreenDetailsRequest) (*GetMyRewardsScreenDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyRewardsScreenDetails not implemented")
}
func (UnimplementedRewardsServer) GetConvertFiCoinsOfferRedemptionScreen(context.Context, *GetConvertFiCoinsOfferRedemptionScreenRequest) (*GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConvertFiCoinsOfferRedemptionScreen not implemented")
}
func (UnimplementedRewardsServer) GetUserDetailsInputOfferRedemptionScreen(context.Context, *GetUserDetailsInputOfferRedemptionScreenRequest) (*GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDetailsInputOfferRedemptionScreen not implemented")
}
func (UnimplementedRewardsServer) GetDynamicUrlForWebPageScreen(context.Context, *GetDynamicUrlForWebPageScreenRequest) (*GetDynamicUrlForWebPageScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDynamicUrlForWebPageScreen not implemented")
}
func (UnimplementedRewardsServer) GetWaysToEarnRewardsScreen(context.Context, *GetWaysToEarnRewardsRequest) (*GetWaysToEarnRewardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaysToEarnRewardsScreen not implemented")
}
func (UnimplementedRewardsServer) GetWaysToEarnRewardDetailScreen(context.Context, *GetWaysToEarnRewardDetailRequest) (*GetWaysToEarnRewardDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaysToEarnRewardDetailScreen not implemented")
}
func (UnimplementedRewardsServer) GetWaysToEarnRewardsScreenVersionToRender(context.Context, *GetWaysToEarnRewardsScreenVersionToRenderRequest) (*GetWaysToEarnRewardsScreenVersionToRenderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaysToEarnRewardsScreenVersionToRender not implemented")
}
func (UnimplementedRewardsServer) GetClaimedRewardDetails(context.Context, *GetClaimedRewardDetailsRequest) (*GetClaimedRewardDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClaimedRewardDetails not implemented")
}
func (UnimplementedRewardsServer) ForceRetryRewardProcessing(context.Context, *ForceRetryRewardProcessingRequest) (*ForceRetryRewardProcessingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceRetryRewardProcessing not implemented")
}

// UnsafeRewardsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RewardsServer will
// result in compilation errors.
type UnsafeRewardsServer interface {
	mustEmbedUnimplementedRewardsServer()
}

func RegisterRewardsServer(s grpc.ServiceRegistrar, srv RewardsServer) {
	s.RegisterService(&Rewards_ServiceDesc, srv)
}

func _Rewards_GetRewardDigest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardDigestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRewardDigest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRewardDigest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRewardDigest(ctx, req.(*GetRewardDigestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRewardSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRewardSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRewardSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRewardSummary(ctx, req.(*GetRewardSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetActiveRewardOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveRewardOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetActiveRewardOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetActiveRewardOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetActiveRewardOffers(ctx, req.(*GetActiveRewardOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRewardOfferDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardOfferDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRewardOfferDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRewardOfferDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRewardOfferDetails(ctx, req.(*GetRewardOfferDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetEarnedRewards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEarnedRewardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetEarnedRewards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetEarnedRewards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetEarnedRewards(ctx, req.(*GetEarnedRewardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRewardsForActorV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardsForActorV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRewardsForActorV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRewardsForActorV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRewardsForActorV1(ctx, req.(*GetRewardsForActorV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetExchangerOrdersForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangerOrdersForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetExchangerOrdersForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetExchangerOrdersForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetExchangerOrdersForActor(ctx, req.(*GetExchangerOrdersForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetClaimRewardInputScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimRewardInputScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetClaimRewardInputScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetClaimRewardInputScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetClaimRewardInputScreen(ctx, req.(*ClaimRewardInputScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_ClaimReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).ClaimReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_ClaimReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).ClaimReward(ctx, req.(*ClaimRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_BulkClaimRewards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkClaimRewardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).BulkClaimRewards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_BulkClaimRewards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).BulkClaimRewards(ctx, req.(*BulkClaimRewardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetActiveOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetActiveOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetActiveOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetActiveOffers(ctx, req.(*GetActiveOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetOfferDetailsById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfferDetailsByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetOfferDetailsById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetOfferDetailsById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetOfferDetailsById(ctx, req.(*GetOfferDetailsByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetOfferCatalogV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfferCatalogV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetOfferCatalogV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetOfferCatalogV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetOfferCatalogV1(ctx, req.(*GetOfferCatalogV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRewardsAndOffersForHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardsAndOffersForHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRewardsAndOffersForHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRewardsAndOffersForHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRewardsAndOffersForHome(ctx, req.(*GetRewardsAndOffersForHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRewardsAndOffersWidget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardsAndOffersWidgetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRewardsAndOffersWidget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRewardsAndOffersWidget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRewardsAndOffersWidget(ctx, req.(*GetRewardsAndOffersWidgetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRedeemOfferInputScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeemOfferInputScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRedeemOfferInputScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRedeemOfferInputScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRedeemOfferInputScreen(ctx, req.(*RedeemOfferInputScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_InitiateRedemption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateRedemptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).InitiateRedemption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_InitiateRedemption_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).InitiateRedemption(ctx, req.(*InitiateRedemptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_ConfirmRedemption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmRedemptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).ConfirmRedemption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_ConfirmRedemption_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).ConfirmRedemption(ctx, req.(*ConfirmRedemptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRedeemedOffersV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedeemedOffersV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRedeemedOffersV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRedeemedOffersV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRedeemedOffersV1(ctx, req.(*GetRedeemedOffersV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRedeemedOffersCatalogLayout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedeemedOffersCatalogLayoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRedeemedOffersCatalogLayout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRedeemedOffersCatalogLayout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRedeemedOffersCatalogLayout(ctx, req.(*GetRedeemedOffersCatalogLayoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetActiveRedeemedOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveRedeemedOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetActiveRedeemedOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetActiveRedeemedOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetActiveRedeemedOffers(ctx, req.(*GetActiveRedeemedOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetExpiredRedeemedOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpiredRedeemedOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetExpiredRedeemedOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetExpiredRedeemedOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetExpiredRedeemedOffers(ctx, req.(*GetExpiredRedeemedOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetRedeemedOfferDetailsRedirectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedeemedOfferDetailsRedirectionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetRedeemedOfferDetailsRedirectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetRedeemedOfferDetailsRedirectionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetRedeemedOfferDetailsRedirectionInfo(ctx, req.(*GetRedeemedOfferDetailsRedirectionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetExchangerOfferById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangerOfferByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetExchangerOfferById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetExchangerOfferById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetExchangerOfferById(ctx, req.(*GetExchangerOfferByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_RedeemExchangerOffer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeemExchangerOfferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).RedeemExchangerOffer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_RedeemExchangerOffer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).RedeemExchangerOffer(ctx, req.(*RedeemExchangerOfferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_ChooseExchangerOrderOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChooseExchangerOrderOptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).ChooseExchangerOrderOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_ChooseExchangerOrderOption_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).ChooseExchangerOrderOption(ctx, req.(*ChooseExchangerOrderOptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetExchangerOrderInputScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangerOrderInputScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetExchangerOrderInputScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetExchangerOrderInputScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetExchangerOrderInputScreen(ctx, req.(*GetExchangerOrderInputScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_SubmitExchangerOrderUserInput_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitExchangerOrderUserInputRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).SubmitExchangerOrderUserInput(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_SubmitExchangerOrderUserInput_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).SubmitExchangerOrderUserInput(ctx, req.(*SubmitExchangerOrderUserInputRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetCatalogOffersAndFilters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCatalogOffersAndFiltersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetCatalogOffersAndFilters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetCatalogOffersAndFilters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetCatalogOffersAndFilters(ctx, req.(*GetCatalogOffersAndFiltersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetOffersCatalogPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOffersCatalogPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetOffersCatalogPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetOffersCatalogPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetOffersCatalogPage(ctx, req.(*GetOffersCatalogPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetOffersCatalogPageVersionToRender_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOffersCatalogPageVersionToRenderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetOffersCatalogPageVersionToRender(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetOffersCatalogPageVersionToRender_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetOffersCatalogPageVersionToRender(ctx, req.(*GetOffersCatalogPageVersionToRenderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetCardOffersTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardOffersTabsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetCardOffersTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetCardOffersTabs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetCardOffersTabs(ctx, req.(*GetCardOffersTabsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetCardOffersCatalogLayout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardOffersCatalogLayoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetCardOffersCatalogLayout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetCardOffersCatalogLayout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetCardOffersCatalogLayout(ctx, req.(*GetCardOffersCatalogLayoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetCardOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetCardOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetCardOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetCardOffers(ctx, req.(*GetCardOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetCardOfferDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardOfferDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetCardOfferDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetCardOfferDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetCardOfferDetails(ctx, req.(*GetCardOfferDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetMyRewardsScreenDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyRewardsScreenDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetMyRewardsScreenDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetMyRewardsScreenDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetMyRewardsScreenDetails(ctx, req.(*GetMyRewardsScreenDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetConvertFiCoinsOfferRedemptionScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConvertFiCoinsOfferRedemptionScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetConvertFiCoinsOfferRedemptionScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetConvertFiCoinsOfferRedemptionScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetConvertFiCoinsOfferRedemptionScreen(ctx, req.(*GetConvertFiCoinsOfferRedemptionScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetUserDetailsInputOfferRedemptionScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDetailsInputOfferRedemptionScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetUserDetailsInputOfferRedemptionScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetUserDetailsInputOfferRedemptionScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetUserDetailsInputOfferRedemptionScreen(ctx, req.(*GetUserDetailsInputOfferRedemptionScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetDynamicUrlForWebPageScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDynamicUrlForWebPageScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetDynamicUrlForWebPageScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetDynamicUrlForWebPageScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetDynamicUrlForWebPageScreen(ctx, req.(*GetDynamicUrlForWebPageScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetWaysToEarnRewardsScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaysToEarnRewardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetWaysToEarnRewardsScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetWaysToEarnRewardsScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetWaysToEarnRewardsScreen(ctx, req.(*GetWaysToEarnRewardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetWaysToEarnRewardDetailScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaysToEarnRewardDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetWaysToEarnRewardDetailScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetWaysToEarnRewardDetailScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetWaysToEarnRewardDetailScreen(ctx, req.(*GetWaysToEarnRewardDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetWaysToEarnRewardsScreenVersionToRender_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaysToEarnRewardsScreenVersionToRenderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetWaysToEarnRewardsScreenVersionToRender(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetWaysToEarnRewardsScreenVersionToRender_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetWaysToEarnRewardsScreenVersionToRender(ctx, req.(*GetWaysToEarnRewardsScreenVersionToRenderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_GetClaimedRewardDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClaimedRewardDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).GetClaimedRewardDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_GetClaimedRewardDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).GetClaimedRewardDetails(ctx, req.(*GetClaimedRewardDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rewards_ForceRetryRewardProcessing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForceRetryRewardProcessingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardsServer).ForceRetryRewardProcessing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rewards_ForceRetryRewardProcessing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardsServer).ForceRetryRewardProcessing(ctx, req.(*ForceRetryRewardProcessingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Rewards_ServiceDesc is the grpc.ServiceDesc for Rewards service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Rewards_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.rewards.Rewards",
	HandlerType: (*RewardsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRewardDigest",
			Handler:    _Rewards_GetRewardDigest_Handler,
		},
		{
			MethodName: "GetRewardSummary",
			Handler:    _Rewards_GetRewardSummary_Handler,
		},
		{
			MethodName: "GetActiveRewardOffers",
			Handler:    _Rewards_GetActiveRewardOffers_Handler,
		},
		{
			MethodName: "GetRewardOfferDetails",
			Handler:    _Rewards_GetRewardOfferDetails_Handler,
		},
		{
			MethodName: "GetEarnedRewards",
			Handler:    _Rewards_GetEarnedRewards_Handler,
		},
		{
			MethodName: "GetRewardsForActorV1",
			Handler:    _Rewards_GetRewardsForActorV1_Handler,
		},
		{
			MethodName: "GetExchangerOrdersForActor",
			Handler:    _Rewards_GetExchangerOrdersForActor_Handler,
		},
		{
			MethodName: "GetClaimRewardInputScreen",
			Handler:    _Rewards_GetClaimRewardInputScreen_Handler,
		},
		{
			MethodName: "ClaimReward",
			Handler:    _Rewards_ClaimReward_Handler,
		},
		{
			MethodName: "BulkClaimRewards",
			Handler:    _Rewards_BulkClaimRewards_Handler,
		},
		{
			MethodName: "GetActiveOffers",
			Handler:    _Rewards_GetActiveOffers_Handler,
		},
		{
			MethodName: "GetOfferDetailsById",
			Handler:    _Rewards_GetOfferDetailsById_Handler,
		},
		{
			MethodName: "GetOfferCatalogV1",
			Handler:    _Rewards_GetOfferCatalogV1_Handler,
		},
		{
			MethodName: "GetRewardsAndOffersForHome",
			Handler:    _Rewards_GetRewardsAndOffersForHome_Handler,
		},
		{
			MethodName: "GetRewardsAndOffersWidget",
			Handler:    _Rewards_GetRewardsAndOffersWidget_Handler,
		},
		{
			MethodName: "GetRedeemOfferInputScreen",
			Handler:    _Rewards_GetRedeemOfferInputScreen_Handler,
		},
		{
			MethodName: "InitiateRedemption",
			Handler:    _Rewards_InitiateRedemption_Handler,
		},
		{
			MethodName: "ConfirmRedemption",
			Handler:    _Rewards_ConfirmRedemption_Handler,
		},
		{
			MethodName: "GetRedeemedOffersV1",
			Handler:    _Rewards_GetRedeemedOffersV1_Handler,
		},
		{
			MethodName: "GetRedeemedOffersCatalogLayout",
			Handler:    _Rewards_GetRedeemedOffersCatalogLayout_Handler,
		},
		{
			MethodName: "GetActiveRedeemedOffers",
			Handler:    _Rewards_GetActiveRedeemedOffers_Handler,
		},
		{
			MethodName: "GetExpiredRedeemedOffers",
			Handler:    _Rewards_GetExpiredRedeemedOffers_Handler,
		},
		{
			MethodName: "GetRedeemedOfferDetailsRedirectionInfo",
			Handler:    _Rewards_GetRedeemedOfferDetailsRedirectionInfo_Handler,
		},
		{
			MethodName: "GetExchangerOfferById",
			Handler:    _Rewards_GetExchangerOfferById_Handler,
		},
		{
			MethodName: "RedeemExchangerOffer",
			Handler:    _Rewards_RedeemExchangerOffer_Handler,
		},
		{
			MethodName: "ChooseExchangerOrderOption",
			Handler:    _Rewards_ChooseExchangerOrderOption_Handler,
		},
		{
			MethodName: "GetExchangerOrderInputScreen",
			Handler:    _Rewards_GetExchangerOrderInputScreen_Handler,
		},
		{
			MethodName: "SubmitExchangerOrderUserInput",
			Handler:    _Rewards_SubmitExchangerOrderUserInput_Handler,
		},
		{
			MethodName: "GetCatalogOffersAndFilters",
			Handler:    _Rewards_GetCatalogOffersAndFilters_Handler,
		},
		{
			MethodName: "GetOffersCatalogPage",
			Handler:    _Rewards_GetOffersCatalogPage_Handler,
		},
		{
			MethodName: "GetOffersCatalogPageVersionToRender",
			Handler:    _Rewards_GetOffersCatalogPageVersionToRender_Handler,
		},
		{
			MethodName: "GetCardOffersTabs",
			Handler:    _Rewards_GetCardOffersTabs_Handler,
		},
		{
			MethodName: "GetCardOffersCatalogLayout",
			Handler:    _Rewards_GetCardOffersCatalogLayout_Handler,
		},
		{
			MethodName: "GetCardOffers",
			Handler:    _Rewards_GetCardOffers_Handler,
		},
		{
			MethodName: "GetCardOfferDetails",
			Handler:    _Rewards_GetCardOfferDetails_Handler,
		},
		{
			MethodName: "GetMyRewardsScreenDetails",
			Handler:    _Rewards_GetMyRewardsScreenDetails_Handler,
		},
		{
			MethodName: "GetConvertFiCoinsOfferRedemptionScreen",
			Handler:    _Rewards_GetConvertFiCoinsOfferRedemptionScreen_Handler,
		},
		{
			MethodName: "GetUserDetailsInputOfferRedemptionScreen",
			Handler:    _Rewards_GetUserDetailsInputOfferRedemptionScreen_Handler,
		},
		{
			MethodName: "GetDynamicUrlForWebPageScreen",
			Handler:    _Rewards_GetDynamicUrlForWebPageScreen_Handler,
		},
		{
			MethodName: "GetWaysToEarnRewardsScreen",
			Handler:    _Rewards_GetWaysToEarnRewardsScreen_Handler,
		},
		{
			MethodName: "GetWaysToEarnRewardDetailScreen",
			Handler:    _Rewards_GetWaysToEarnRewardDetailScreen_Handler,
		},
		{
			MethodName: "GetWaysToEarnRewardsScreenVersionToRender",
			Handler:    _Rewards_GetWaysToEarnRewardsScreenVersionToRender_Handler,
		},
		{
			MethodName: "GetClaimedRewardDetails",
			Handler:    _Rewards_GetClaimedRewardDetails_Handler,
		},
		{
			MethodName: "ForceRetryRewardProcessing",
			Handler:    _Rewards_ForceRetryRewardProcessing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/rewards/service.proto",
}
