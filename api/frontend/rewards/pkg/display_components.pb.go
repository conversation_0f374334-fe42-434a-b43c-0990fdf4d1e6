// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/rewards/pkg/display_components.proto

package pkg

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	sections "github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ScrollOrientation defines the orientation of the scroll i.e. vertical or horizontal
type ScrollOrientation int32

const (
	// default scroll orientation
	ScrollOrientation_SCROLL_ORIENTATION_UNSPECIFIED ScrollOrientation = 0
	// vertical scroll
	ScrollOrientation_SCROLL_ORIENTATION_VERTICAL ScrollOrientation = 1
	// horizontal scroll
	ScrollOrientation_SCROLL_ORIENTATION_HORIZONTAL ScrollOrientation = 2
)

// Enum value maps for ScrollOrientation.
var (
	ScrollOrientation_name = map[int32]string{
		0: "SCROLL_ORIENTATION_UNSPECIFIED",
		1: "SCROLL_ORIENTATION_VERTICAL",
		2: "SCROLL_ORIENTATION_HORIZONTAL",
	}
	ScrollOrientation_value = map[string]int32{
		"SCROLL_ORIENTATION_UNSPECIFIED": 0,
		"SCROLL_ORIENTATION_VERTICAL":    1,
		"SCROLL_ORIENTATION_HORIZONTAL":  2,
	}
)

func (x ScrollOrientation) Enum() *ScrollOrientation {
	p := new(ScrollOrientation)
	*p = x
	return p
}

func (x ScrollOrientation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScrollOrientation) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_pkg_display_components_proto_enumTypes[0].Descriptor()
}

func (ScrollOrientation) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_pkg_display_components_proto_enumTypes[0]
}

func (x ScrollOrientation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScrollOrientation.Descriptor instead.
func (ScrollOrientation) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{0}
}

// ScrollMode defines the mode of scroll like infinite or finite scroll
type ScrollMode int32

const (
	// default scroll mode
	ScrollMode_SCROLL_MODE_UNSPECIFIED ScrollMode = 0
	// scroll mode for infinite scroll
	ScrollMode_SCROLL_MODE_INFINITE ScrollMode = 1
	// scroll mode for finite scroll
	ScrollMode_SCROLL_MODE_FINITE ScrollMode = 2
)

// Enum value maps for ScrollMode.
var (
	ScrollMode_name = map[int32]string{
		0: "SCROLL_MODE_UNSPECIFIED",
		1: "SCROLL_MODE_INFINITE",
		2: "SCROLL_MODE_FINITE",
	}
	ScrollMode_value = map[string]int32{
		"SCROLL_MODE_UNSPECIFIED": 0,
		"SCROLL_MODE_INFINITE":    1,
		"SCROLL_MODE_FINITE":      2,
	}
)

func (x ScrollMode) Enum() *ScrollMode {
	p := new(ScrollMode)
	*p = x
	return p
}

func (x ScrollMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScrollMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_pkg_display_components_proto_enumTypes[1].Descriptor()
}

func (ScrollMode) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_pkg_display_components_proto_enumTypes[1]
}

func (x ScrollMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScrollMode.Descriptor instead.
func (ScrollMode) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{1}
}

// ScrollType defines the type of scroll like user scroll or auto scroll
type ScrollType int32

const (
	// default scroll type
	ScrollType_SCROLL_TYPE_UNSPECIFIED ScrollType = 0
	// user scroll
	ScrollType_SCROLL_TYPE_USER_SCROLL ScrollType = 1
	// auto scroll
	ScrollType_SCROLL_TYPE_AUTO_SCROLL ScrollType = 2
)

// Enum value maps for ScrollType.
var (
	ScrollType_name = map[int32]string{
		0: "SCROLL_TYPE_UNSPECIFIED",
		1: "SCROLL_TYPE_USER_SCROLL",
		2: "SCROLL_TYPE_AUTO_SCROLL",
	}
	ScrollType_value = map[string]int32{
		"SCROLL_TYPE_UNSPECIFIED": 0,
		"SCROLL_TYPE_USER_SCROLL": 1,
		"SCROLL_TYPE_AUTO_SCROLL": 2,
	}
)

func (x ScrollType) Enum() *ScrollType {
	p := new(ScrollType)
	*p = x
	return p
}

func (x ScrollType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScrollType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_pkg_display_components_proto_enumTypes[2].Descriptor()
}

func (ScrollType) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_pkg_display_components_proto_enumTypes[2]
}

func (x ScrollType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScrollType.Descriptor instead.
func (ScrollType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{2}
}

// Action types to be performed for the particular action.
type CustomAction_ActionType int32

const (
	// no action to be performed
	CustomAction_ACTION_TYPE_UNSPECIFIED CustomAction_ActionType = 0
	// this action is used to make rpc calls
	CustomAction_MAKE_API_CALL CustomAction_ActionType = 1
)

// Enum value maps for CustomAction_ActionType.
var (
	CustomAction_ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "MAKE_API_CALL",
	}
	CustomAction_ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED": 0,
		"MAKE_API_CALL":           1,
	}
)

func (x CustomAction_ActionType) Enum() *CustomAction_ActionType {
	p := new(CustomAction_ActionType)
	*p = x
	return p
}

func (x CustomAction_ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomAction_ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_pkg_display_components_proto_enumTypes[3].Descriptor()
}

func (CustomAction_ActionType) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_pkg_display_components_proto_enumTypes[3]
}

func (x CustomAction_ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomAction_ActionType.Descriptor instead.
func (CustomAction_ActionType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{1, 0}
}

// custom api to be called is defined here
type CustomAction_CustomActionApi int32

const (
	CustomAction_CUSTOM_ACTION_API_UNSPECIFIED CustomAction_CustomActionApi = 0
	// Custom API action to trigger initiate redemption RPC
	// rpc name : InitiateRedemption
	CustomAction_INITIATE_REDEMPTION_RPC CustomAction_CustomActionApi = 1
	// Get redeemed offer details redirection info rpc
	// rpc name : GetRedeemOfferInputScreen
	CustomAction_GET_REDEEM_OFFER_INPUT_SCREEN CustomAction_CustomActionApi = 2
)

// Enum value maps for CustomAction_CustomActionApi.
var (
	CustomAction_CustomActionApi_name = map[int32]string{
		0: "CUSTOM_ACTION_API_UNSPECIFIED",
		1: "INITIATE_REDEMPTION_RPC",
		2: "GET_REDEEM_OFFER_INPUT_SCREEN",
	}
	CustomAction_CustomActionApi_value = map[string]int32{
		"CUSTOM_ACTION_API_UNSPECIFIED": 0,
		"INITIATE_REDEMPTION_RPC":       1,
		"GET_REDEEM_OFFER_INPUT_SCREEN": 2,
	}
)

func (x CustomAction_CustomActionApi) Enum() *CustomAction_CustomActionApi {
	p := new(CustomAction_CustomActionApi)
	*p = x
	return p
}

func (x CustomAction_CustomActionApi) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomAction_CustomActionApi) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_pkg_display_components_proto_enumTypes[4].Descriptor()
}

func (CustomAction_CustomActionApi) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_pkg_display_components_proto_enumTypes[4]
}

func (x CustomAction_CustomActionApi) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomAction_CustomActionApi.Descriptor instead.
func (CustomAction_CustomActionApi) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{1, 1}
}

// moved frontend.rewards.CtaV1 here, it was causing cyclic
type Cta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// icon text component to be displayed in the CTA
	Itc *ui.IconTextComponent `protobuf:"bytes,1,opt,name=itc,proto3" json:"itc,omitempty"`
	// Shadow displayed below the CTA
	Shadow *ui.Shadow `protobuf:"bytes,2,opt,name=shadow,proto3" json:"shadow,omitempty"`
	// deeplink or custom action to be performed
	//
	// Types that are assignable to Action:
	//
	//	*Cta_DeeplinkAction
	//	*Cta_CustomAction
	Action isCta_Action `protobuf_oneof:"action"`
}

func (x *Cta) Reset() {
	*x = Cta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cta) ProtoMessage() {}

func (x *Cta) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cta.ProtoReflect.Descriptor instead.
func (*Cta) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{0}
}

func (x *Cta) GetItc() *ui.IconTextComponent {
	if x != nil {
		return x.Itc
	}
	return nil
}

func (x *Cta) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

func (m *Cta) GetAction() isCta_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *Cta) GetDeeplinkAction() *deeplink.Deeplink {
	if x, ok := x.GetAction().(*Cta_DeeplinkAction); ok {
		return x.DeeplinkAction
	}
	return nil
}

func (x *Cta) GetCustomAction() *CustomAction {
	if x, ok := x.GetAction().(*Cta_CustomAction); ok {
		return x.CustomAction
	}
	return nil
}

type isCta_Action interface {
	isCta_Action()
}

type Cta_DeeplinkAction struct {
	DeeplinkAction *deeplink.Deeplink `protobuf:"bytes,3,opt,name=deeplink_action,json=deeplinkAction,proto3,oneof"`
}

type Cta_CustomAction struct {
	CustomAction *CustomAction `protobuf:"bytes,4,opt,name=custom_action,json=customAction,proto3,oneof"`
}

func (*Cta_DeeplinkAction) isCta_Action() {}

func (*Cta_CustomAction) isCta_Action() {}

// CustomAction describes the action to be taken by the parent component using this.
// For e.g.,
// Action -> share code, open tnc page.
type CustomAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// custom action to be performed.
	ActionType CustomAction_ActionType `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=frontend.rewards.pkg.CustomAction_ActionType" json:"action_type,omitempty"`
	// API to be called for the action, will be present if actionType is MAKE_API_CALL.
	ActionApi CustomAction_CustomActionApi `protobuf:"varint,2,opt,name=action_api,json=actionApi,proto3,enum=frontend.rewards.pkg.CustomAction_CustomActionApi" json:"action_api,omitempty"`
	// action specific data
	//
	// Types that are assignable to ActionData:
	//
	//	*CustomAction_GetRedeemOfferInputScreenApiActionData_
	//	*CustomAction_GetInitiateRedemptionApiActionData_
	ActionData isCustomAction_ActionData `protobuf_oneof:"ActionData"`
}

func (x *CustomAction) Reset() {
	*x = CustomAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomAction) ProtoMessage() {}

func (x *CustomAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomAction.ProtoReflect.Descriptor instead.
func (*CustomAction) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{1}
}

func (x *CustomAction) GetActionType() CustomAction_ActionType {
	if x != nil {
		return x.ActionType
	}
	return CustomAction_ACTION_TYPE_UNSPECIFIED
}

func (x *CustomAction) GetActionApi() CustomAction_CustomActionApi {
	if x != nil {
		return x.ActionApi
	}
	return CustomAction_CUSTOM_ACTION_API_UNSPECIFIED
}

func (m *CustomAction) GetActionData() isCustomAction_ActionData {
	if m != nil {
		return m.ActionData
	}
	return nil
}

func (x *CustomAction) GetGetRedeemOfferInputScreenApiActionData() *CustomAction_GetRedeemOfferInputScreenApiActionData {
	if x, ok := x.GetActionData().(*CustomAction_GetRedeemOfferInputScreenApiActionData_); ok {
		return x.GetRedeemOfferInputScreenApiActionData
	}
	return nil
}

func (x *CustomAction) GetGetInitiateRedemptionApiActionData() *CustomAction_GetInitiateRedemptionApiActionData {
	if x, ok := x.GetActionData().(*CustomAction_GetInitiateRedemptionApiActionData_); ok {
		return x.GetInitiateRedemptionApiActionData
	}
	return nil
}

type isCustomAction_ActionData interface {
	isCustomAction_ActionData()
}

type CustomAction_GetRedeemOfferInputScreenApiActionData_ struct {
	// action data for GET_REDEEM_OFFER_INPUT_SCREEN
	GetRedeemOfferInputScreenApiActionData *CustomAction_GetRedeemOfferInputScreenApiActionData `protobuf:"bytes,3,opt,name=get_redeem_offer_input_screen_api_action_data,json=getRedeemOfferInputScreenApiActionData,proto3,oneof"`
}

type CustomAction_GetInitiateRedemptionApiActionData_ struct {
	// action data for INITIATE_REDEMPTION_RPC
	GetInitiateRedemptionApiActionData *CustomAction_GetInitiateRedemptionApiActionData `protobuf:"bytes,4,opt,name=get_initiate_redemption_api_action_data,json=getInitiateRedemptionApiActionData,proto3,oneof"`
}

func (*CustomAction_GetRedeemOfferInputScreenApiActionData_) isCustomAction_ActionData() {}

func (*CustomAction_GetInitiateRedemptionApiActionData_) isCustomAction_ActionData() {}

// Banner component
// can be used for promo banners etc
// Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11136-7837&node-type=frame&t=S8bbx1vBnvAXai1n-0
type Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// left visual element to be displayed
	// usually the brand logo
	LeftVisualElement *common.VisualElement `protobuf:"bytes,1,opt,name=left_visual_element,json=leftVisualElement,proto3" json:"left_visual_element,omitempty"`
	// banner title
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// right visual element to be displayed
	// usually a product image
	RightVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=right_visual_element,json=rightVisualElement,proto3" json:"right_visual_element,omitempty"`
	// PageControlDetails is used to represent the indicator details of the banner
	IndicatorDetails *Banner_PageControlDetails `protobuf:"bytes,4,opt,name=indicator_details,json=indicatorDetails,proto3" json:"indicator_details,omitempty"`
	// background color of the banner
	BackgroundColor *widget.BackgroundColour `protobuf:"bytes,5,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// shadow of the banner
	Shadow *widget.Shadow `protobuf:"bytes,6,opt,name=shadow,proto3" json:"shadow,omitempty"`
	// Banner CTA
	Cta *Cta `protobuf:"bytes,7,opt,name=cta,proto3" json:"cta,omitempty"`
	// analytics details for the banner
	AnalyticsDetails *Banner_AnalyticsDetails `protobuf:"bytes,8,opt,name=analytics_details,json=analyticsDetails,proto3" json:"analytics_details,omitempty"`
}

func (x *Banner) Reset() {
	*x = Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner) ProtoMessage() {}

func (x *Banner) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner.ProtoReflect.Descriptor instead.
func (*Banner) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{2}
}

func (x *Banner) GetLeftVisualElement() *common.VisualElement {
	if x != nil {
		return x.LeftVisualElement
	}
	return nil
}

func (x *Banner) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *Banner) GetRightVisualElement() *common.VisualElement {
	if x != nil {
		return x.RightVisualElement
	}
	return nil
}

func (x *Banner) GetIndicatorDetails() *Banner_PageControlDetails {
	if x != nil {
		return x.IndicatorDetails
	}
	return nil
}

func (x *Banner) GetBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColor
	}
	return nil
}

func (x *Banner) GetShadow() *widget.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

func (x *Banner) GetCta() *Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *Banner) GetAnalyticsDetails() *Banner_AnalyticsDetails {
	if x != nil {
		return x.AnalyticsDetails
	}
	return nil
}

// ScrollBehaviour defines the scroll behaviour of the component
// Please note that this needs to be implemented by the client where the component is used
type ScrollBehaviour struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// scroll orientation
	ScrollOrientation ScrollOrientation `protobuf:"varint,1,opt,name=scroll_orientation,json=scrollOrientation,proto3,enum=frontend.rewards.pkg.ScrollOrientation" json:"scroll_orientation,omitempty"`
	// scroll mode
	ScrollMode ScrollMode `protobuf:"varint,2,opt,name=scroll_mode,json=scrollMode,proto3,enum=frontend.rewards.pkg.ScrollMode" json:"scroll_mode,omitempty"`
	// scroll type
	ScrollType ScrollType `protobuf:"varint,3,opt,name=scroll_type,json=scrollType,proto3,enum=frontend.rewards.pkg.ScrollType" json:"scroll_type,omitempty"`
	// scroll type data defines any scroll type specific data
	//
	// Types that are assignable to ScrollTypeData:
	//
	//	*ScrollBehaviour_ScrollTypeAutoScrollData_
	ScrollTypeData isScrollBehaviour_ScrollTypeData `protobuf_oneof:"scroll_type_data"`
}

func (x *ScrollBehaviour) Reset() {
	*x = ScrollBehaviour{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScrollBehaviour) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrollBehaviour) ProtoMessage() {}

func (x *ScrollBehaviour) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrollBehaviour.ProtoReflect.Descriptor instead.
func (*ScrollBehaviour) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{3}
}

func (x *ScrollBehaviour) GetScrollOrientation() ScrollOrientation {
	if x != nil {
		return x.ScrollOrientation
	}
	return ScrollOrientation_SCROLL_ORIENTATION_UNSPECIFIED
}

func (x *ScrollBehaviour) GetScrollMode() ScrollMode {
	if x != nil {
		return x.ScrollMode
	}
	return ScrollMode_SCROLL_MODE_UNSPECIFIED
}

func (x *ScrollBehaviour) GetScrollType() ScrollType {
	if x != nil {
		return x.ScrollType
	}
	return ScrollType_SCROLL_TYPE_UNSPECIFIED
}

func (m *ScrollBehaviour) GetScrollTypeData() isScrollBehaviour_ScrollTypeData {
	if m != nil {
		return m.ScrollTypeData
	}
	return nil
}

func (x *ScrollBehaviour) GetScrollTypeAutoScrollData() *ScrollBehaviour_ScrollTypeAutoScrollData {
	if x, ok := x.GetScrollTypeData().(*ScrollBehaviour_ScrollTypeAutoScrollData_); ok {
		return x.ScrollTypeAutoScrollData
	}
	return nil
}

type isScrollBehaviour_ScrollTypeData interface {
	isScrollBehaviour_ScrollTypeData()
}

type ScrollBehaviour_ScrollTypeAutoScrollData_ struct {
	ScrollTypeAutoScrollData *ScrollBehaviour_ScrollTypeAutoScrollData `protobuf:"bytes,4,opt,name=scroll_type_auto_scroll_data,json=scrollTypeAutoScrollData,proto3,oneof"`
}

func (*ScrollBehaviour_ScrollTypeAutoScrollData_) isScrollBehaviour_ScrollTypeData() {}

// GenericServerDrivenSection is a wrapper for types.ui.sdui.sections.Section
// This is used to represent a generic server-driven section that can be used in various adhoc context to improve design flexibility
type GenericServerDrivenSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Section *sections.Section `protobuf:"bytes,1,opt,name=section,proto3" json:"section,omitempty"`
}

func (x *GenericServerDrivenSection) Reset() {
	*x = GenericServerDrivenSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenericServerDrivenSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenericServerDrivenSection) ProtoMessage() {}

func (x *GenericServerDrivenSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenericServerDrivenSection.ProtoReflect.Descriptor instead.
func (*GenericServerDrivenSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{4}
}

func (x *GenericServerDrivenSection) GetSection() *sections.Section {
	if x != nil {
		return x.Section
	}
	return nil
}

type CustomAction_GetRedeemOfferInputScreenApiActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// offer id for which the redemption input screen is to be fetched
	OfferId         string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	RedemptionPrice uint32 `protobuf:"varint,2,opt,name=redemption_price,json=redemptionPrice,proto3" json:"redemption_price,omitempty"`
}

func (x *CustomAction_GetRedeemOfferInputScreenApiActionData) Reset() {
	*x = CustomAction_GetRedeemOfferInputScreenApiActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomAction_GetRedeemOfferInputScreenApiActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomAction_GetRedeemOfferInputScreenApiActionData) ProtoMessage() {}

func (x *CustomAction_GetRedeemOfferInputScreenApiActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomAction_GetRedeemOfferInputScreenApiActionData.ProtoReflect.Descriptor instead.
func (*CustomAction_GetRedeemOfferInputScreenApiActionData) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CustomAction_GetRedeemOfferInputScreenApiActionData) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *CustomAction_GetRedeemOfferInputScreenApiActionData) GetRedemptionPrice() uint32 {
	if x != nil {
		return x.RedemptionPrice
	}
	return 0
}

type CustomAction_GetInitiateRedemptionApiActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// offer id for which the redemption input screen is to be fetched
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *CustomAction_GetInitiateRedemptionApiActionData) Reset() {
	*x = CustomAction_GetInitiateRedemptionApiActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomAction_GetInitiateRedemptionApiActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomAction_GetInitiateRedemptionApiActionData) ProtoMessage() {}

func (x *CustomAction_GetInitiateRedemptionApiActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomAction_GetInitiateRedemptionApiActionData.ProtoReflect.Descriptor instead.
func (*CustomAction_GetInitiateRedemptionApiActionData) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{1, 1}
}

func (x *CustomAction_GetInitiateRedemptionApiActionData) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// PageControlDetails defines the indicator details of the banner
// indicator is used to represent each banner as a dot and a dash when selected
type Banner_PageControlDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultIndicatorColor  *widget.BackgroundColour `protobuf:"bytes,1,opt,name=default_indicator_color,json=defaultIndicatorColor,proto3" json:"default_indicator_color,omitempty"`
	SelectedIndicatorColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=selected_indicator_color,json=selectedIndicatorColor,proto3" json:"selected_indicator_color,omitempty"`
}

func (x *Banner_PageControlDetails) Reset() {
	*x = Banner_PageControlDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Banner_PageControlDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner_PageControlDetails) ProtoMessage() {}

func (x *Banner_PageControlDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner_PageControlDetails.ProtoReflect.Descriptor instead.
func (*Banner_PageControlDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Banner_PageControlDetails) GetDefaultIndicatorColor() *widget.BackgroundColour {
	if x != nil {
		return x.DefaultIndicatorColor
	}
	return nil
}

func (x *Banner_PageControlDetails) GetSelectedIndicatorColor() *widget.BackgroundColour {
	if x != nil {
		return x.SelectedIndicatorColor
	}
	return nil
}

// AnalyticsDetails stores the details required for analytic events for the banner
type Banner_AnalyticsDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// generic map of key value pairs for event properties for the banner
	// e.g. { "offerId": "offer-id-1", "category_tag": "CATEGORY_TAG_VOUCHERS" }
	EventProperties map[string]string `protobuf:"bytes,1,rep,name=event_properties,json=eventProperties,proto3" json:"event_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Banner_AnalyticsDetails) Reset() {
	*x = Banner_AnalyticsDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Banner_AnalyticsDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner_AnalyticsDetails) ProtoMessage() {}

func (x *Banner_AnalyticsDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner_AnalyticsDetails.ProtoReflect.Descriptor instead.
func (*Banner_AnalyticsDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Banner_AnalyticsDetails) GetEventProperties() map[string]string {
	if x != nil {
		return x.EventProperties
	}
	return nil
}

// ScrollTypeAutoScrollData stores SCROLL_TYPE_AUTO_SCROLL specific data
type ScrollBehaviour_ScrollTypeAutoScrollData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time in milliseconds after which the first scroll should start
	FirstScrollDelay uint32 `protobuf:"varint,1,opt,name=first_scroll_delay,json=firstScrollDelay,proto3" json:"first_scroll_delay,omitempty"`
	// time in milliseconds between each scroll
	ScrollInterval uint32 `protobuf:"varint,2,opt,name=scroll_interval,json=scrollInterval,proto3" json:"scroll_interval,omitempty"`
}

func (x *ScrollBehaviour_ScrollTypeAutoScrollData) Reset() {
	*x = ScrollBehaviour_ScrollTypeAutoScrollData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScrollBehaviour_ScrollTypeAutoScrollData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrollBehaviour_ScrollTypeAutoScrollData) ProtoMessage() {}

func (x *ScrollBehaviour_ScrollTypeAutoScrollData) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_pkg_display_components_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrollBehaviour_ScrollTypeAutoScrollData.ProtoReflect.Descriptor instead.
func (*ScrollBehaviour_ScrollTypeAutoScrollData) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ScrollBehaviour_ScrollTypeAutoScrollData) GetFirstScrollDelay() uint32 {
	if x != nil {
		return x.FirstScrollDelay
	}
	return 0
}

func (x *ScrollBehaviour_ScrollTypeAutoScrollData) GetScrollInterval() uint32 {
	if x != nil {
		return x.ScrollInterval
	}
	return 0
}

var File_api_frontend_rewards_pkg_display_components_proto protoreflect.FileDescriptor

var file_api_frontend_rewards_pkg_display_components_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x14, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f,
	0x73, 0x64, 0x75, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75,
	0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x87, 0x02, 0x0a, 0x03, 0x43, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x03, 0x69,
	0x74, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x69, 0x74, 0x63,
	0x12, 0x2e, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77,
	0x12, 0x46, 0x0a, 0x0f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf0, 0x06,
	0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51,
	0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70,
	0x69, 0x12, 0xaa, 0x01, 0x0a, 0x2d, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x26, 0x67, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x41, 0x70, 0x69, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x9c,
	0x01, 0x0a, 0x27, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x45, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x22, 0x67, 0x65, 0x74, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x70, 0x69, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x6e, 0x0a,
	0x26, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x72, 0x65,
	0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x1a, 0x3f, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3c,
	0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x41, 0x4b,
	0x45, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x22, 0x74, 0x0a, 0x0f,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x12,
	0x21, 0x0a, 0x1d, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x50, 0x49, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x50, 0x43, 0x10, 0x01, 0x12,
	0x21, 0x0a, 0x1d, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x02, 0x42, 0x0c, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x91, 0x08, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x13, 0x6c,
	0x65, 0x66, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x6c, 0x65, 0x66,
	0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x53,
	0x0a, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x11, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x10, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x59, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0f, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3c, 0x0a, 0x06,
	0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x53, 0x68, 0x61, 0x64,
	0x6f, 0x77, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x12, 0x2b, 0x0a, 0x03, 0x63, 0x74,
	0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x43,
	0x74, 0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x5a, 0x0a, 0x11, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x10, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x1a, 0xe6, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a, 0x17, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x15, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x68, 0x0a, 0x18, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x16, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0xc5, 0x01, 0x0a,
	0x10, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x6d, 0x0a, 0x10, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70,
	0x6b, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x1a, 0x42, 0x0a, 0x14, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xf9, 0x03, 0x0a, 0x0f, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x42,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x75, 0x72, 0x12, 0x56, 0x0a, 0x12, 0x73, 0x63, 0x72, 0x6f,
	0x6c, 0x6c, 0x5f, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x53, 0x63, 0x72, 0x6f,
	0x6c, 0x6c, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x73,
	0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x41, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x53, 0x63, 0x72,
	0x6f, 0x6c, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x6f,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x1c, 0x73, 0x63, 0x72, 0x6f, 0x6c,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x73, 0x63, 0x72, 0x6f,
	0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x42, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x75, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x41,
	0x75, 0x74, 0x6f, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x18, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x53,
	0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x71, 0x0a, 0x18, 0x53, 0x63, 0x72,
	0x6f, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x63, 0x72, 0x6f, 0x6c,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x73,
	0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x73, 0x63,
	0x72, 0x6f, 0x6c, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x12, 0x0a, 0x10,
	0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x5d, 0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x44, 0x72, 0x69, 0x76, 0x65, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f,
	0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2a,
	0x7b, 0x0a, 0x11, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x4f,
	0x52, 0x49, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x43, 0x52, 0x4f,
	0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x49, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56,
	0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x43, 0x52,
	0x4f, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x49, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x48, 0x4f, 0x52, 0x49, 0x5a, 0x4f, 0x4e, 0x54, 0x41, 0x4c, 0x10, 0x02, 0x2a, 0x5b, 0x0a, 0x0a,
	0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x43,
	0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x43, 0x52, 0x4f, 0x4c,
	0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x63, 0x0a, 0x0a, 0x53, 0x63, 0x72,
	0x6f, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x43, 0x52, 0x4f, 0x4c,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x10,
	0x01, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0x02, 0x42, 0x62,
	0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x6b,
	0x67, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70,
	0x6b, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_rewards_pkg_display_components_proto_rawDescOnce sync.Once
	file_api_frontend_rewards_pkg_display_components_proto_rawDescData = file_api_frontend_rewards_pkg_display_components_proto_rawDesc
)

func file_api_frontend_rewards_pkg_display_components_proto_rawDescGZIP() []byte {
	file_api_frontend_rewards_pkg_display_components_proto_rawDescOnce.Do(func() {
		file_api_frontend_rewards_pkg_display_components_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_rewards_pkg_display_components_proto_rawDescData)
	})
	return file_api_frontend_rewards_pkg_display_components_proto_rawDescData
}

var file_api_frontend_rewards_pkg_display_components_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_frontend_rewards_pkg_display_components_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_frontend_rewards_pkg_display_components_proto_goTypes = []interface{}{
	(ScrollOrientation)(0),             // 0: frontend.rewards.pkg.ScrollOrientation
	(ScrollMode)(0),                    // 1: frontend.rewards.pkg.ScrollMode
	(ScrollType)(0),                    // 2: frontend.rewards.pkg.ScrollType
	(CustomAction_ActionType)(0),       // 3: frontend.rewards.pkg.CustomAction.ActionType
	(CustomAction_CustomActionApi)(0),  // 4: frontend.rewards.pkg.CustomAction.CustomActionApi
	(*Cta)(nil),                        // 5: frontend.rewards.pkg.Cta
	(*CustomAction)(nil),               // 6: frontend.rewards.pkg.CustomAction
	(*Banner)(nil),                     // 7: frontend.rewards.pkg.Banner
	(*ScrollBehaviour)(nil),            // 8: frontend.rewards.pkg.ScrollBehaviour
	(*GenericServerDrivenSection)(nil), // 9: frontend.rewards.pkg.GenericServerDrivenSection
	(*CustomAction_GetRedeemOfferInputScreenApiActionData)(nil), // 10: frontend.rewards.pkg.CustomAction.GetRedeemOfferInputScreenApiActionData
	(*CustomAction_GetInitiateRedemptionApiActionData)(nil),     // 11: frontend.rewards.pkg.CustomAction.GetInitiateRedemptionApiActionData
	(*Banner_PageControlDetails)(nil),                           // 12: frontend.rewards.pkg.Banner.PageControlDetails
	(*Banner_AnalyticsDetails)(nil),                             // 13: frontend.rewards.pkg.Banner.AnalyticsDetails
	nil,                                                         // 14: frontend.rewards.pkg.Banner.AnalyticsDetails.EventPropertiesEntry
	(*ScrollBehaviour_ScrollTypeAutoScrollData)(nil),            // 15: frontend.rewards.pkg.ScrollBehaviour.ScrollTypeAutoScrollData
	(*ui.IconTextComponent)(nil),                                // 16: api.typesv2.ui.IconTextComponent
	(*ui.Shadow)(nil),                                           // 17: api.typesv2.ui.Shadow
	(*deeplink.Deeplink)(nil),                                   // 18: frontend.deeplink.Deeplink
	(*common.VisualElement)(nil),                                // 19: api.typesv2.common.VisualElement
	(*common.Text)(nil),                                         // 20: api.typesv2.common.Text
	(*widget.BackgroundColour)(nil),                             // 21: api.typesv2.common.ui.widget.BackgroundColour
	(*widget.Shadow)(nil),                                       // 22: api.typesv2.common.ui.widget.Shadow
	(*sections.Section)(nil),                                    // 23: api.typesv2.ui.sdui.sections.Section
}
var file_api_frontend_rewards_pkg_display_components_proto_depIdxs = []int32{
	16, // 0: frontend.rewards.pkg.Cta.itc:type_name -> api.typesv2.ui.IconTextComponent
	17, // 1: frontend.rewards.pkg.Cta.shadow:type_name -> api.typesv2.ui.Shadow
	18, // 2: frontend.rewards.pkg.Cta.deeplink_action:type_name -> frontend.deeplink.Deeplink
	6,  // 3: frontend.rewards.pkg.Cta.custom_action:type_name -> frontend.rewards.pkg.CustomAction
	3,  // 4: frontend.rewards.pkg.CustomAction.action_type:type_name -> frontend.rewards.pkg.CustomAction.ActionType
	4,  // 5: frontend.rewards.pkg.CustomAction.action_api:type_name -> frontend.rewards.pkg.CustomAction.CustomActionApi
	10, // 6: frontend.rewards.pkg.CustomAction.get_redeem_offer_input_screen_api_action_data:type_name -> frontend.rewards.pkg.CustomAction.GetRedeemOfferInputScreenApiActionData
	11, // 7: frontend.rewards.pkg.CustomAction.get_initiate_redemption_api_action_data:type_name -> frontend.rewards.pkg.CustomAction.GetInitiateRedemptionApiActionData
	19, // 8: frontend.rewards.pkg.Banner.left_visual_element:type_name -> api.typesv2.common.VisualElement
	20, // 9: frontend.rewards.pkg.Banner.title:type_name -> api.typesv2.common.Text
	19, // 10: frontend.rewards.pkg.Banner.right_visual_element:type_name -> api.typesv2.common.VisualElement
	12, // 11: frontend.rewards.pkg.Banner.indicator_details:type_name -> frontend.rewards.pkg.Banner.PageControlDetails
	21, // 12: frontend.rewards.pkg.Banner.background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	22, // 13: frontend.rewards.pkg.Banner.shadow:type_name -> api.typesv2.common.ui.widget.Shadow
	5,  // 14: frontend.rewards.pkg.Banner.cta:type_name -> frontend.rewards.pkg.Cta
	13, // 15: frontend.rewards.pkg.Banner.analytics_details:type_name -> frontend.rewards.pkg.Banner.AnalyticsDetails
	0,  // 16: frontend.rewards.pkg.ScrollBehaviour.scroll_orientation:type_name -> frontend.rewards.pkg.ScrollOrientation
	1,  // 17: frontend.rewards.pkg.ScrollBehaviour.scroll_mode:type_name -> frontend.rewards.pkg.ScrollMode
	2,  // 18: frontend.rewards.pkg.ScrollBehaviour.scroll_type:type_name -> frontend.rewards.pkg.ScrollType
	15, // 19: frontend.rewards.pkg.ScrollBehaviour.scroll_type_auto_scroll_data:type_name -> frontend.rewards.pkg.ScrollBehaviour.ScrollTypeAutoScrollData
	23, // 20: frontend.rewards.pkg.GenericServerDrivenSection.section:type_name -> api.typesv2.ui.sdui.sections.Section
	21, // 21: frontend.rewards.pkg.Banner.PageControlDetails.default_indicator_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	21, // 22: frontend.rewards.pkg.Banner.PageControlDetails.selected_indicator_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	14, // 23: frontend.rewards.pkg.Banner.AnalyticsDetails.event_properties:type_name -> frontend.rewards.pkg.Banner.AnalyticsDetails.EventPropertiesEntry
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_frontend_rewards_pkg_display_components_proto_init() }
func file_api_frontend_rewards_pkg_display_components_proto_init() {
	if File_api_frontend_rewards_pkg_display_components_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScrollBehaviour); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenericServerDrivenSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomAction_GetRedeemOfferInputScreenApiActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomAction_GetInitiateRedemptionApiActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Banner_PageControlDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Banner_AnalyticsDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_pkg_display_components_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScrollBehaviour_ScrollTypeAutoScrollData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_rewards_pkg_display_components_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Cta_DeeplinkAction)(nil),
		(*Cta_CustomAction)(nil),
	}
	file_api_frontend_rewards_pkg_display_components_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*CustomAction_GetRedeemOfferInputScreenApiActionData_)(nil),
		(*CustomAction_GetInitiateRedemptionApiActionData_)(nil),
	}
	file_api_frontend_rewards_pkg_display_components_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*ScrollBehaviour_ScrollTypeAutoScrollData_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_rewards_pkg_display_components_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_rewards_pkg_display_components_proto_goTypes,
		DependencyIndexes: file_api_frontend_rewards_pkg_display_components_proto_depIdxs,
		EnumInfos:         file_api_frontend_rewards_pkg_display_components_proto_enumTypes,
		MessageInfos:      file_api_frontend_rewards_pkg_display_components_proto_msgTypes,
	}.Build()
	File_api_frontend_rewards_pkg_display_components_proto = out.File
	file_api_frontend_rewards_pkg_display_components_proto_rawDesc = nil
	file_api_frontend_rewards_pkg_display_components_proto_goTypes = nil
	file_api_frontend_rewards_pkg_display_components_proto_depIdxs = nil
}
