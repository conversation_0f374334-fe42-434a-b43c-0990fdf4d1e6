// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/securities/catalog/service.proto

package catalog

import (
	rpc "github.com/epifi/be-common/api/rpc"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSecurityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Specifies which fields of the Security object should be populated in the response.
	// Only the requested fields will be fetched and populated with data.
	// Fields not included in this list might have their zero/default values.
	// This allows for more efficient and targeted data retrieval.
	Fields []SecurityFieldMask `protobuf:"varint,2,rep,packed,name=fields,proto3,enum=api.securities.catalog.SecurityFieldMask" json:"fields,omitempty"`
}

func (x *GetSecurityRequest) Reset() {
	*x = GetSecurityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityRequest) ProtoMessage() {}

func (x *GetSecurityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityRequest.ProtoReflect.Descriptor instead.
func (*GetSecurityRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSecurityRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetSecurityRequest) GetFields() []SecurityFieldMask {
	if x != nil {
		return x.Fields
	}
	return nil
}

type GetSecurityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Security *Security   `protobuf:"bytes,2,opt,name=security,proto3" json:"security,omitempty"`
}

func (x *GetSecurityResponse) Reset() {
	*x = GetSecurityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityResponse) ProtoMessage() {}

func (x *GetSecurityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityResponse.ProtoReflect.Descriptor instead.
func (*GetSecurityResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSecurityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecurityResponse) GetSecurity() *Security {
	if x != nil {
		return x.Security
	}
	return nil
}

type GetSecuritiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	// Specifies which fields of the Security object should be populated in the response.
	// Only the requested fields will be fetched and populated with data.
	// Fields not included in this list might have their zero/default values.
	// This allows for more efficient and targeted data retrieval.
	Fields []SecurityFieldMask `protobuf:"varint,2,rep,packed,name=fields,proto3,enum=api.securities.catalog.SecurityFieldMask" json:"fields,omitempty"`
}

func (x *GetSecuritiesRequest) Reset() {
	*x = GetSecuritiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecuritiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecuritiesRequest) ProtoMessage() {}

func (x *GetSecuritiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecuritiesRequest.ProtoReflect.Descriptor instead.
func (*GetSecuritiesRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetSecuritiesRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetSecuritiesRequest) GetFields() []SecurityFieldMask {
	if x != nil {
		return x.Fields
	}
	return nil
}

type GetSecuritiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map of security id to security object
	SecuritiesMap map[string]*Security `protobuf:"bytes,2,rep,name=securities_map,json=securitiesMap,proto3" json:"securities_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetSecuritiesResponse) Reset() {
	*x = GetSecuritiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecuritiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecuritiesResponse) ProtoMessage() {}

func (x *GetSecuritiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecuritiesResponse.ProtoReflect.Descriptor instead.
func (*GetSecuritiesResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetSecuritiesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecuritiesResponse) GetSecuritiesMap() map[string]*Security {
	if x != nil {
		return x.SecuritiesMap
	}
	return nil
}

type ExchangeSymbol struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Symbol   string   `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Exchange Exchange `protobuf:"varint,2,opt,name=exchange,proto3,enum=api.securities.catalog.Exchange" json:"exchange,omitempty"`
}

func (x *ExchangeSymbol) Reset() {
	*x = ExchangeSymbol{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeSymbol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeSymbol) ProtoMessage() {}

func (x *ExchangeSymbol) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeSymbol.ProtoReflect.Descriptor instead.
func (*ExchangeSymbol) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{4}
}

func (x *ExchangeSymbol) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ExchangeSymbol) GetExchange() Exchange {
	if x != nil {
		return x.Exchange
	}
	return Exchange_EXCHANGE_UNSPECIFIED
}

type GetSecurityListingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetSecurityListingRequest_ExternalId
	//	*GetSecurityListingRequest_ExchangeSymbol
	Identifier isGetSecurityListingRequest_Identifier `protobuf_oneof:"identifier"`
	// Specifies which fields of the Security object should be populated in the response.
	// Only the requested fields will be fetched and populated with data.
	// Fields not included in this list might have their zero/default values.
	// This allows for more efficient and targeted data retrieval.
	SecurityFields []SecurityFieldMask `protobuf:"varint,4,rep,packed,name=security_fields,json=securityFields,proto3,enum=api.securities.catalog.SecurityFieldMask" json:"security_fields,omitempty"`
	// Specifies which fields of the Security Listing object should be populated in the response.
	// Only the requested fields will be fetched and populated with data.
	// Fields not included in this list might have their zero/default values.
	// While requesting for both security and security listing data, we will be making 2 dao calls,
	// How?, use external_id/exchange_symbol to fetch security listing and then use security_id from the listing to fetch security data.
	// This might add latency to this RPC.
	SecurityListingFields []SecurityListingFieldMask `protobuf:"varint,5,rep,packed,name=security_listing_fields,json=securityListingFields,proto3,enum=api.securities.catalog.SecurityListingFieldMask" json:"security_listing_fields,omitempty"`
}

func (x *GetSecurityListingRequest) Reset() {
	*x = GetSecurityListingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityListingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityListingRequest) ProtoMessage() {}

func (x *GetSecurityListingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityListingRequest.ProtoReflect.Descriptor instead.
func (*GetSecurityListingRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{5}
}

func (m *GetSecurityListingRequest) GetIdentifier() isGetSecurityListingRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetSecurityListingRequest) GetExternalId() string {
	if x, ok := x.GetIdentifier().(*GetSecurityListingRequest_ExternalId); ok {
		return x.ExternalId
	}
	return ""
}

func (x *GetSecurityListingRequest) GetExchangeSymbol() *ExchangeSymbol {
	if x, ok := x.GetIdentifier().(*GetSecurityListingRequest_ExchangeSymbol); ok {
		return x.ExchangeSymbol
	}
	return nil
}

func (x *GetSecurityListingRequest) GetSecurityFields() []SecurityFieldMask {
	if x != nil {
		return x.SecurityFields
	}
	return nil
}

func (x *GetSecurityListingRequest) GetSecurityListingFields() []SecurityListingFieldMask {
	if x != nil {
		return x.SecurityListingFields
	}
	return nil
}

type isGetSecurityListingRequest_Identifier interface {
	isGetSecurityListingRequest_Identifier()
}

type GetSecurityListingRequest_ExternalId struct {
	ExternalId string `protobuf:"bytes,1,opt,name=external_id,json=externalId,proto3,oneof"`
}

type GetSecurityListingRequest_ExchangeSymbol struct {
	ExchangeSymbol *ExchangeSymbol `protobuf:"bytes,2,opt,name=exchange_symbol,json=exchangeSymbol,proto3,oneof"`
}

func (*GetSecurityListingRequest_ExternalId) isGetSecurityListingRequest_Identifier() {}

func (*GetSecurityListingRequest_ExchangeSymbol) isGetSecurityListingRequest_Identifier() {}

type GetSecurityListingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Only the requested fields per object will be sent in the response.
	Security        *Security        `protobuf:"bytes,2,opt,name=security,proto3" json:"security,omitempty"`
	SecurityListing *SecurityListing `protobuf:"bytes,3,opt,name=security_listing,json=securityListing,proto3" json:"security_listing,omitempty"`
}

func (x *GetSecurityListingResponse) Reset() {
	*x = GetSecurityListingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityListingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityListingResponse) ProtoMessage() {}

func (x *GetSecurityListingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityListingResponse.ProtoReflect.Descriptor instead.
func (*GetSecurityListingResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetSecurityListingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecurityListingResponse) GetSecurity() *Security {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *GetSecurityListingResponse) GetSecurityListing() *SecurityListing {
	if x != nil {
		return x.SecurityListing
	}
	return nil
}

type GetSecurityListingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifiers:
	//
	//	*GetSecurityListingsRequest_ExternalIds_
	//	*GetSecurityListingsRequest_ExchangeSymbols_
	Identifiers isGetSecurityListingsRequest_Identifiers `protobuf_oneof:"identifiers"`
	// Specifies which fields of the Security object should be populated in the response.
	// Only the requested fields will be fetched and populated with data.
	// Fields not included in this list might have their zero/default values.
	// This allows for more efficient and targeted data retrieval.
	SecurityFields []SecurityFieldMask `protobuf:"varint,4,rep,packed,name=security_fields,json=securityFields,proto3,enum=api.securities.catalog.SecurityFieldMask" json:"security_fields,omitempty"`
	// Specifies which fields of the Security Listing object should be populated in the response.
	// Only the requested fields will be fetched and populated with data.
	// Fields not included in this list might have their zero/default values.
	// While requesting for both security and security listing data, we will be making 2 dao calls,
	// How?, use external_id/exchange_symbol to fetch security listing and then use security_id from the listing to fetch security data.
	// This might add latency to this RPC.
	SecurityListingFields []SecurityListingFieldMask `protobuf:"varint,5,rep,packed,name=security_listing_fields,json=securityListingFields,proto3,enum=api.securities.catalog.SecurityListingFieldMask" json:"security_listing_fields,omitempty"`
}

func (x *GetSecurityListingsRequest) Reset() {
	*x = GetSecurityListingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityListingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityListingsRequest) ProtoMessage() {}

func (x *GetSecurityListingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityListingsRequest.ProtoReflect.Descriptor instead.
func (*GetSecurityListingsRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{7}
}

func (m *GetSecurityListingsRequest) GetIdentifiers() isGetSecurityListingsRequest_Identifiers {
	if m != nil {
		return m.Identifiers
	}
	return nil
}

func (x *GetSecurityListingsRequest) GetExternalIds() *GetSecurityListingsRequest_ExternalIds {
	if x, ok := x.GetIdentifiers().(*GetSecurityListingsRequest_ExternalIds_); ok {
		return x.ExternalIds
	}
	return nil
}

func (x *GetSecurityListingsRequest) GetExchangeSymbols() *GetSecurityListingsRequest_ExchangeSymbols {
	if x, ok := x.GetIdentifiers().(*GetSecurityListingsRequest_ExchangeSymbols_); ok {
		return x.ExchangeSymbols
	}
	return nil
}

func (x *GetSecurityListingsRequest) GetSecurityFields() []SecurityFieldMask {
	if x != nil {
		return x.SecurityFields
	}
	return nil
}

func (x *GetSecurityListingsRequest) GetSecurityListingFields() []SecurityListingFieldMask {
	if x != nil {
		return x.SecurityListingFields
	}
	return nil
}

type isGetSecurityListingsRequest_Identifiers interface {
	isGetSecurityListingsRequest_Identifiers()
}

type GetSecurityListingsRequest_ExternalIds_ struct {
	ExternalIds *GetSecurityListingsRequest_ExternalIds `protobuf:"bytes,1,opt,name=external_ids,json=externalIds,proto3,oneof"`
}

type GetSecurityListingsRequest_ExchangeSymbols_ struct {
	ExchangeSymbols *GetSecurityListingsRequest_ExchangeSymbols `protobuf:"bytes,2,opt,name=exchange_symbols,json=exchangeSymbols,proto3,oneof"`
}

func (*GetSecurityListingsRequest_ExternalIds_) isGetSecurityListingsRequest_Identifiers() {}

func (*GetSecurityListingsRequest_ExchangeSymbols_) isGetSecurityListingsRequest_Identifiers() {}

type GetSecurityListingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                      *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SecurityAndSecurityListings []*SecurityAndSecurityListing `protobuf:"bytes,2,rep,name=security_and_security_listings,json=securityAndSecurityListings,proto3" json:"security_and_security_listings,omitempty"`
}

func (x *GetSecurityListingsResponse) Reset() {
	*x = GetSecurityListingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityListingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityListingsResponse) ProtoMessage() {}

func (x *GetSecurityListingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityListingsResponse.ProtoReflect.Descriptor instead.
func (*GetSecurityListingsResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetSecurityListingsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecurityListingsResponse) GetSecurityAndSecurityListings() []*SecurityAndSecurityListing {
	if x != nil {
		return x.SecurityAndSecurityListings
	}
	return nil
}

type SecurityAndSecurityListing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Only the requested fields per object will be sent in the response.
	Security        *Security        `protobuf:"bytes,2,opt,name=security,proto3" json:"security,omitempty"`
	SecurityListing *SecurityListing `protobuf:"bytes,3,opt,name=security_listing,json=securityListing,proto3" json:"security_listing,omitempty"`
}

func (x *SecurityAndSecurityListing) Reset() {
	*x = SecurityAndSecurityListing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityAndSecurityListing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityAndSecurityListing) ProtoMessage() {}

func (x *SecurityAndSecurityListing) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityAndSecurityListing.ProtoReflect.Descriptor instead.
func (*SecurityAndSecurityListing) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{9}
}

func (x *SecurityAndSecurityListing) GetSecurity() *Security {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *SecurityAndSecurityListing) GetSecurityListing() *SecurityListing {
	if x != nil {
		return x.SecurityListing
	}
	return nil
}

type GetPriceByDateAndSecListingIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of security listing identifiers to fetch historical prices for
	SecurityListingIds []string `protobuf:"bytes,1,rep,name=security_listing_ids,json=securityListingIds,proto3" json:"security_listing_ids,omitempty"`
	// The date for which historical prices are requested
	PriceDate *date.Date `protobuf:"bytes,2,opt,name=price_date,json=priceDate,proto3" json:"price_date,omitempty"`
}

func (x *GetPriceByDateAndSecListingIDsRequest) Reset() {
	*x = GetPriceByDateAndSecListingIDsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPriceByDateAndSecListingIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPriceByDateAndSecListingIDsRequest) ProtoMessage() {}

func (x *GetPriceByDateAndSecListingIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPriceByDateAndSecListingIDsRequest.ProtoReflect.Descriptor instead.
func (*GetPriceByDateAndSecListingIDsRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetPriceByDateAndSecListingIDsRequest) GetSecurityListingIds() []string {
	if x != nil {
		return x.SecurityListingIds
	}
	return nil
}

func (x *GetPriceByDateAndSecListingIDsRequest) GetPriceDate() *date.Date {
	if x != nil {
		return x.PriceDate
	}
	return nil
}

type GetPriceByDateAndSecListingIDsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Map of security_listing_id to its price data
	Prices map[string]*money.Money `protobuf:"bytes,2,rep,name=prices,proto3" json:"prices,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetPriceByDateAndSecListingIDsResponse) Reset() {
	*x = GetPriceByDateAndSecListingIDsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPriceByDateAndSecListingIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPriceByDateAndSecListingIDsResponse) ProtoMessage() {}

func (x *GetPriceByDateAndSecListingIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPriceByDateAndSecListingIDsResponse.ProtoReflect.Descriptor instead.
func (*GetPriceByDateAndSecListingIDsResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetPriceByDateAndSecListingIDsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPriceByDateAndSecListingIDsResponse) GetPrices() map[string]*money.Money {
	if x != nil {
		return x.Prices
	}
	return nil
}

type GetSecListingIdsByISINsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of ISIN and exchange combinations to fetch security listing IDs for
	IsinExchangePairs []*ISINExchangePair `protobuf:"bytes,1,rep,name=isin_exchange_pairs,json=isinExchangePairs,proto3" json:"isin_exchange_pairs,omitempty"`
}

func (x *GetSecListingIdsByISINsRequest) Reset() {
	*x = GetSecListingIdsByISINsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecListingIdsByISINsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecListingIdsByISINsRequest) ProtoMessage() {}

func (x *GetSecListingIdsByISINsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecListingIdsByISINsRequest.ProtoReflect.Descriptor instead.
func (*GetSecListingIdsByISINsRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetSecListingIdsByISINsRequest) GetIsinExchangePairs() []*ISINExchangePair {
	if x != nil {
		return x.IsinExchangePairs
	}
	return nil
}

type ISINExchangePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ISIN identifier of the security
	Isin string `protobuf:"bytes,1,opt,name=isin,proto3" json:"isin,omitempty"`
	// The exchange for which to get the listing ID
	Exchange Exchange `protobuf:"varint,2,opt,name=exchange,proto3,enum=api.securities.catalog.Exchange" json:"exchange,omitempty"`
}

func (x *ISINExchangePair) Reset() {
	*x = ISINExchangePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ISINExchangePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ISINExchangePair) ProtoMessage() {}

func (x *ISINExchangePair) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ISINExchangePair.ProtoReflect.Descriptor instead.
func (*ISINExchangePair) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{13}
}

func (x *ISINExchangePair) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *ISINExchangePair) GetExchange() Exchange {
	if x != nil {
		return x.Exchange
	}
	return Exchange_EXCHANGE_UNSPECIFIED
}

type SecListingIdWithISIN struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsinExchangePair          *ISINExchangePair `protobuf:"bytes,1,opt,name=isin_exchange_pair,json=isinExchangePair,proto3" json:"isin_exchange_pair,omitempty"`
	SecurityListingExternalId string            `protobuf:"bytes,2,opt,name=security_listing_external_id,json=securityListingExternalId,proto3" json:"security_listing_external_id,omitempty"`
}

func (x *SecListingIdWithISIN) Reset() {
	*x = SecListingIdWithISIN{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecListingIdWithISIN) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecListingIdWithISIN) ProtoMessage() {}

func (x *SecListingIdWithISIN) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecListingIdWithISIN.ProtoReflect.Descriptor instead.
func (*SecListingIdWithISIN) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{14}
}

func (x *SecListingIdWithISIN) GetIsinExchangePair() *ISINExchangePair {
	if x != nil {
		return x.IsinExchangePair
	}
	return nil
}

func (x *SecListingIdWithISIN) GetSecurityListingExternalId() string {
	if x != nil {
		return x.SecurityListingExternalId
	}
	return ""
}

type GetSecListingIdsByISINsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of security listing IDs, each corresponding to an ISIN and exchange.
	SecListingIdsWithIsins []*SecListingIdWithISIN `protobuf:"bytes,2,rep,name=sec_listing_ids_with_isins,json=secListingIdsWithIsins,proto3" json:"sec_listing_ids_with_isins,omitempty"`
}

func (x *GetSecListingIdsByISINsResponse) Reset() {
	*x = GetSecListingIdsByISINsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecListingIdsByISINsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecListingIdsByISINsResponse) ProtoMessage() {}

func (x *GetSecListingIdsByISINsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecListingIdsByISINsResponse.ProtoReflect.Descriptor instead.
func (*GetSecListingIdsByISINsResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetSecListingIdsByISINsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecListingIdsByISINsResponse) GetSecListingIdsWithIsins() []*SecListingIdWithISIN {
	if x != nil {
		return x.SecListingIdsWithIsins
	}
	return nil
}

type AddSecurityWithISINsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isins []string `protobuf:"bytes,1,rep,name=isins,proto3" json:"isins,omitempty"`
}

func (x *AddSecurityWithISINsRequest) Reset() {
	*x = AddSecurityWithISINsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSecurityWithISINsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSecurityWithISINsRequest) ProtoMessage() {}

func (x *AddSecurityWithISINsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSecurityWithISINsRequest.ProtoReflect.Descriptor instead.
func (*AddSecurityWithISINsRequest) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{16}
}

func (x *AddSecurityWithISINsRequest) GetIsins() []string {
	if x != nil {
		return x.Isins
	}
	return nil
}

type AddSecurityWithISINsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                   *rpc.Status                `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsinSecurityListingPairs []*IsinSecurityListingPair `protobuf:"bytes,2,rep,name=isin_security_listing_pairs,json=isinSecurityListingPairs,proto3" json:"isin_security_listing_pairs,omitempty"`
}

func (x *AddSecurityWithISINsResponse) Reset() {
	*x = AddSecurityWithISINsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSecurityWithISINsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSecurityWithISINsResponse) ProtoMessage() {}

func (x *AddSecurityWithISINsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSecurityWithISINsResponse.ProtoReflect.Descriptor instead.
func (*AddSecurityWithISINsResponse) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{17}
}

func (x *AddSecurityWithISINsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AddSecurityWithISINsResponse) GetIsinSecurityListingPairs() []*IsinSecurityListingPair {
	if x != nil {
		return x.IsinSecurityListingPairs
	}
	return nil
}

type IsinSecurityListingPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isin              string `protobuf:"bytes,1,opt,name=isin,proto3" json:"isin,omitempty"`
	SecurityListingId string `protobuf:"bytes,2,opt,name=security_listing_id,json=securityListingId,proto3" json:"security_listing_id,omitempty"`
}

func (x *IsinSecurityListingPair) Reset() {
	*x = IsinSecurityListingPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsinSecurityListingPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsinSecurityListingPair) ProtoMessage() {}

func (x *IsinSecurityListingPair) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsinSecurityListingPair.ProtoReflect.Descriptor instead.
func (*IsinSecurityListingPair) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{18}
}

func (x *IsinSecurityListingPair) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *IsinSecurityListingPair) GetSecurityListingId() string {
	if x != nil {
		return x.SecurityListingId
	}
	return ""
}

type GetSecurityListingsRequest_ExternalIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalIds []string `protobuf:"bytes,1,rep,name=external_ids,json=externalIds,proto3" json:"external_ids,omitempty"`
}

func (x *GetSecurityListingsRequest_ExternalIds) Reset() {
	*x = GetSecurityListingsRequest_ExternalIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityListingsRequest_ExternalIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityListingsRequest_ExternalIds) ProtoMessage() {}

func (x *GetSecurityListingsRequest_ExternalIds) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityListingsRequest_ExternalIds.ProtoReflect.Descriptor instead.
func (*GetSecurityListingsRequest_ExternalIds) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetSecurityListingsRequest_ExternalIds) GetExternalIds() []string {
	if x != nil {
		return x.ExternalIds
	}
	return nil
}

type GetSecurityListingsRequest_ExchangeSymbols struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExchangeSymbols []*ExchangeSymbol `protobuf:"bytes,1,rep,name=exchange_symbols,json=exchangeSymbols,proto3" json:"exchange_symbols,omitempty"`
}

func (x *GetSecurityListingsRequest_ExchangeSymbols) Reset() {
	*x = GetSecurityListingsRequest_ExchangeSymbols{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecurityListingsRequest_ExchangeSymbols) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityListingsRequest_ExchangeSymbols) ProtoMessage() {}

func (x *GetSecurityListingsRequest_ExchangeSymbols) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityListingsRequest_ExchangeSymbols.ProtoReflect.Descriptor instead.
func (*GetSecurityListingsRequest_ExchangeSymbols) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_service_proto_rawDescGZIP(), []int{7, 1}
}

func (x *GetSecurityListingsRequest_ExchangeSymbols) GetExchangeSymbols() []*ExchangeSymbol {
	if x != nil {
		return x.ExchangeSymbols
	}
	return nil
}

var File_api_securities_catalog_service_proto protoreflect.FileDescriptor

var file_api_securities_catalog_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x1a, 0x14,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x67, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x41, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x06, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x22, 0x78, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3c, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x22, 0x6b,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x89, 0x02, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x67, 0x0a, 0x0e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x4d, 0x61, 0x70, 0x1a, 0x62, 0x0a, 0x12, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61,
	0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x0e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x22,
	0xdd, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64,
	0x12, 0x51, 0x0a, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x68, 0x0a, 0x17, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x15, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xd3, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x52, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xd7, 0x04, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x63, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61,
	0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x6f, 0x0a, 0x10, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x68,
	0x0a, 0x17, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x52, 0x15, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x30, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x1a, 0x64, 0x0a, 0x0f, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x12, 0x51, 0x0a,
	0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52,
	0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73,
	0x42, 0x0d, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22,
	0xbb, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x77, 0x0a, 0x1e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x41, 0x6e,
	0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x1b, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xae, 0x01,
	0x0a, 0x1a, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x0a, 0x08,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e,
	0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x52, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x52, 0x0a, 0x10, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0f, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x8b,
	0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x44,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x80, 0x02, 0x0a,
	0x26, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x41,
	0x6e, 0x64, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x44, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x62, 0x0a, 0x06,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73,
	0x1a, 0x4d, 0x0a, 0x0b, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x7a, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x73, 0x42, 0x79, 0x49, 0x53, 0x49, 0x4e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x58, 0x0a, 0x13, 0x69, 0x73, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e,
	0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x49, 0x53, 0x49, 0x4e, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x11, 0x69, 0x73, 0x69, 0x6e, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x69, 0x72, 0x73, 0x22, 0x64, 0x0a, 0x10, 0x49,
	0x53, 0x49, 0x4e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x73, 0x69, 0x6e, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x22, 0xaf, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x57, 0x69, 0x74, 0x68, 0x49, 0x53, 0x49, 0x4e, 0x12, 0x56, 0x0a, 0x12, 0x69, 0x73,
	0x69, 0x6e, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x49, 0x53, 0x49, 0x4e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x69, 0x72,
	0x52, 0x10, 0x69, 0x73, 0x69, 0x6e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61,
	0x69, 0x72, 0x12, 0x3f, 0x0a, 0x1c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x22, 0xb0, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x4c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x42, 0x79, 0x49, 0x53, 0x49, 0x4e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x68, 0x0a, 0x1a,
	0x73, 0x65, 0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x5f,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x69, 0x73, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x57, 0x69, 0x74, 0x68, 0x49, 0x53, 0x49, 0x4e, 0x52, 0x16,
	0x73, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x57, 0x69, 0x74,
	0x68, 0x49, 0x73, 0x69, 0x6e, 0x73, 0x22, 0x33, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x49, 0x53, 0x49, 0x4e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x69, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x69, 0x6e, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x1c,
	0x41, 0x64, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x49,
	0x53, 0x49, 0x4e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x6e, 0x0a, 0x1b, 0x69, 0x73, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x49, 0x73, 0x69, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x69, 0x72, 0x52, 0x18, 0x69, 0x73, 0x69, 0x6e, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x69, 0x72,
	0x73, 0x22, 0x5d, 0x0a, 0x17, 0x49, 0x73, 0x69, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x69, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x73, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x73, 0x69, 0x6e,
	0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x32, 0x99, 0x07, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x12, 0x66, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9f, 0x01, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53,
	0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x44, 0x73, 0x12, 0x3d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79, 0x44,
	0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73,
	0x42, 0x79, 0x49, 0x53, 0x49, 0x4e, 0x73, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x73, 0x42, 0x79, 0x49, 0x53, 0x49, 0x4e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x42, 0x79, 0x49, 0x53, 0x49, 0x4e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x49, 0x53, 0x49, 0x4e,
	0x73, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x49, 0x53, 0x49, 0x4e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x41, 0x64, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x49,
	0x53, 0x49, 0x4e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x2f, 0x5a, 0x2d,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_securities_catalog_service_proto_rawDescOnce sync.Once
	file_api_securities_catalog_service_proto_rawDescData = file_api_securities_catalog_service_proto_rawDesc
)

func file_api_securities_catalog_service_proto_rawDescGZIP() []byte {
	file_api_securities_catalog_service_proto_rawDescOnce.Do(func() {
		file_api_securities_catalog_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_securities_catalog_service_proto_rawDescData)
	})
	return file_api_securities_catalog_service_proto_rawDescData
}

var file_api_securities_catalog_service_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_api_securities_catalog_service_proto_goTypes = []interface{}{
	(*GetSecurityRequest)(nil),                         // 0: api.securities.catalog.GetSecurityRequest
	(*GetSecurityResponse)(nil),                        // 1: api.securities.catalog.GetSecurityResponse
	(*GetSecuritiesRequest)(nil),                       // 2: api.securities.catalog.GetSecuritiesRequest
	(*GetSecuritiesResponse)(nil),                      // 3: api.securities.catalog.GetSecuritiesResponse
	(*ExchangeSymbol)(nil),                             // 4: api.securities.catalog.ExchangeSymbol
	(*GetSecurityListingRequest)(nil),                  // 5: api.securities.catalog.GetSecurityListingRequest
	(*GetSecurityListingResponse)(nil),                 // 6: api.securities.catalog.GetSecurityListingResponse
	(*GetSecurityListingsRequest)(nil),                 // 7: api.securities.catalog.GetSecurityListingsRequest
	(*GetSecurityListingsResponse)(nil),                // 8: api.securities.catalog.GetSecurityListingsResponse
	(*SecurityAndSecurityListing)(nil),                 // 9: api.securities.catalog.SecurityAndSecurityListing
	(*GetPriceByDateAndSecListingIDsRequest)(nil),      // 10: api.securities.catalog.GetPriceByDateAndSecListingIDsRequest
	(*GetPriceByDateAndSecListingIDsResponse)(nil),     // 11: api.securities.catalog.GetPriceByDateAndSecListingIDsResponse
	(*GetSecListingIdsByISINsRequest)(nil),             // 12: api.securities.catalog.GetSecListingIdsByISINsRequest
	(*ISINExchangePair)(nil),                           // 13: api.securities.catalog.ISINExchangePair
	(*SecListingIdWithISIN)(nil),                       // 14: api.securities.catalog.SecListingIdWithISIN
	(*GetSecListingIdsByISINsResponse)(nil),            // 15: api.securities.catalog.GetSecListingIdsByISINsResponse
	(*AddSecurityWithISINsRequest)(nil),                // 16: api.securities.catalog.AddSecurityWithISINsRequest
	(*AddSecurityWithISINsResponse)(nil),               // 17: api.securities.catalog.AddSecurityWithISINsResponse
	(*IsinSecurityListingPair)(nil),                    // 18: api.securities.catalog.IsinSecurityListingPair
	nil,                                                // 19: api.securities.catalog.GetSecuritiesResponse.SecuritiesMapEntry
	(*GetSecurityListingsRequest_ExternalIds)(nil),     // 20: api.securities.catalog.GetSecurityListingsRequest.ExternalIds
	(*GetSecurityListingsRequest_ExchangeSymbols)(nil), // 21: api.securities.catalog.GetSecurityListingsRequest.ExchangeSymbols
	nil,                           // 22: api.securities.catalog.GetPriceByDateAndSecListingIDsResponse.PricesEntry
	(SecurityFieldMask)(0),        // 23: api.securities.catalog.SecurityFieldMask
	(*rpc.Status)(nil),            // 24: rpc.Status
	(*Security)(nil),              // 25: api.securities.catalog.Security
	(Exchange)(0),                 // 26: api.securities.catalog.Exchange
	(SecurityListingFieldMask)(0), // 27: api.securities.catalog.SecurityListingFieldMask
	(*SecurityListing)(nil),       // 28: api.securities.catalog.SecurityListing
	(*date.Date)(nil),             // 29: google.type.Date
	(*money.Money)(nil),           // 30: google.type.Money
}
var file_api_securities_catalog_service_proto_depIdxs = []int32{
	23, // 0: api.securities.catalog.GetSecurityRequest.fields:type_name -> api.securities.catalog.SecurityFieldMask
	24, // 1: api.securities.catalog.GetSecurityResponse.status:type_name -> rpc.Status
	25, // 2: api.securities.catalog.GetSecurityResponse.security:type_name -> api.securities.catalog.Security
	23, // 3: api.securities.catalog.GetSecuritiesRequest.fields:type_name -> api.securities.catalog.SecurityFieldMask
	24, // 4: api.securities.catalog.GetSecuritiesResponse.status:type_name -> rpc.Status
	19, // 5: api.securities.catalog.GetSecuritiesResponse.securities_map:type_name -> api.securities.catalog.GetSecuritiesResponse.SecuritiesMapEntry
	26, // 6: api.securities.catalog.ExchangeSymbol.exchange:type_name -> api.securities.catalog.Exchange
	4,  // 7: api.securities.catalog.GetSecurityListingRequest.exchange_symbol:type_name -> api.securities.catalog.ExchangeSymbol
	23, // 8: api.securities.catalog.GetSecurityListingRequest.security_fields:type_name -> api.securities.catalog.SecurityFieldMask
	27, // 9: api.securities.catalog.GetSecurityListingRequest.security_listing_fields:type_name -> api.securities.catalog.SecurityListingFieldMask
	24, // 10: api.securities.catalog.GetSecurityListingResponse.status:type_name -> rpc.Status
	25, // 11: api.securities.catalog.GetSecurityListingResponse.security:type_name -> api.securities.catalog.Security
	28, // 12: api.securities.catalog.GetSecurityListingResponse.security_listing:type_name -> api.securities.catalog.SecurityListing
	20, // 13: api.securities.catalog.GetSecurityListingsRequest.external_ids:type_name -> api.securities.catalog.GetSecurityListingsRequest.ExternalIds
	21, // 14: api.securities.catalog.GetSecurityListingsRequest.exchange_symbols:type_name -> api.securities.catalog.GetSecurityListingsRequest.ExchangeSymbols
	23, // 15: api.securities.catalog.GetSecurityListingsRequest.security_fields:type_name -> api.securities.catalog.SecurityFieldMask
	27, // 16: api.securities.catalog.GetSecurityListingsRequest.security_listing_fields:type_name -> api.securities.catalog.SecurityListingFieldMask
	24, // 17: api.securities.catalog.GetSecurityListingsResponse.status:type_name -> rpc.Status
	9,  // 18: api.securities.catalog.GetSecurityListingsResponse.security_and_security_listings:type_name -> api.securities.catalog.SecurityAndSecurityListing
	25, // 19: api.securities.catalog.SecurityAndSecurityListing.security:type_name -> api.securities.catalog.Security
	28, // 20: api.securities.catalog.SecurityAndSecurityListing.security_listing:type_name -> api.securities.catalog.SecurityListing
	29, // 21: api.securities.catalog.GetPriceByDateAndSecListingIDsRequest.price_date:type_name -> google.type.Date
	24, // 22: api.securities.catalog.GetPriceByDateAndSecListingIDsResponse.status:type_name -> rpc.Status
	22, // 23: api.securities.catalog.GetPriceByDateAndSecListingIDsResponse.prices:type_name -> api.securities.catalog.GetPriceByDateAndSecListingIDsResponse.PricesEntry
	13, // 24: api.securities.catalog.GetSecListingIdsByISINsRequest.isin_exchange_pairs:type_name -> api.securities.catalog.ISINExchangePair
	26, // 25: api.securities.catalog.ISINExchangePair.exchange:type_name -> api.securities.catalog.Exchange
	13, // 26: api.securities.catalog.SecListingIdWithISIN.isin_exchange_pair:type_name -> api.securities.catalog.ISINExchangePair
	24, // 27: api.securities.catalog.GetSecListingIdsByISINsResponse.status:type_name -> rpc.Status
	14, // 28: api.securities.catalog.GetSecListingIdsByISINsResponse.sec_listing_ids_with_isins:type_name -> api.securities.catalog.SecListingIdWithISIN
	24, // 29: api.securities.catalog.AddSecurityWithISINsResponse.status:type_name -> rpc.Status
	18, // 30: api.securities.catalog.AddSecurityWithISINsResponse.isin_security_listing_pairs:type_name -> api.securities.catalog.IsinSecurityListingPair
	25, // 31: api.securities.catalog.GetSecuritiesResponse.SecuritiesMapEntry.value:type_name -> api.securities.catalog.Security
	4,  // 32: api.securities.catalog.GetSecurityListingsRequest.ExchangeSymbols.exchange_symbols:type_name -> api.securities.catalog.ExchangeSymbol
	30, // 33: api.securities.catalog.GetPriceByDateAndSecListingIDsResponse.PricesEntry.value:type_name -> google.type.Money
	0,  // 34: api.securities.catalog.SecuritiesCatalog.GetSecurity:input_type -> api.securities.catalog.GetSecurityRequest
	2,  // 35: api.securities.catalog.SecuritiesCatalog.GetSecurities:input_type -> api.securities.catalog.GetSecuritiesRequest
	5,  // 36: api.securities.catalog.SecuritiesCatalog.GetSecurityListing:input_type -> api.securities.catalog.GetSecurityListingRequest
	7,  // 37: api.securities.catalog.SecuritiesCatalog.GetSecurityListings:input_type -> api.securities.catalog.GetSecurityListingsRequest
	10, // 38: api.securities.catalog.SecuritiesCatalog.GetPriceByDateAndSecListingIDs:input_type -> api.securities.catalog.GetPriceByDateAndSecListingIDsRequest
	12, // 39: api.securities.catalog.SecuritiesCatalog.GetSecListingIdsByISINs:input_type -> api.securities.catalog.GetSecListingIdsByISINsRequest
	16, // 40: api.securities.catalog.SecuritiesCatalog.AddSecurityWithISINs:input_type -> api.securities.catalog.AddSecurityWithISINsRequest
	1,  // 41: api.securities.catalog.SecuritiesCatalog.GetSecurity:output_type -> api.securities.catalog.GetSecurityResponse
	3,  // 42: api.securities.catalog.SecuritiesCatalog.GetSecurities:output_type -> api.securities.catalog.GetSecuritiesResponse
	6,  // 43: api.securities.catalog.SecuritiesCatalog.GetSecurityListing:output_type -> api.securities.catalog.GetSecurityListingResponse
	8,  // 44: api.securities.catalog.SecuritiesCatalog.GetSecurityListings:output_type -> api.securities.catalog.GetSecurityListingsResponse
	11, // 45: api.securities.catalog.SecuritiesCatalog.GetPriceByDateAndSecListingIDs:output_type -> api.securities.catalog.GetPriceByDateAndSecListingIDsResponse
	15, // 46: api.securities.catalog.SecuritiesCatalog.GetSecListingIdsByISINs:output_type -> api.securities.catalog.GetSecListingIdsByISINsResponse
	17, // 47: api.securities.catalog.SecuritiesCatalog.AddSecurityWithISINs:output_type -> api.securities.catalog.AddSecurityWithISINsResponse
	41, // [41:48] is the sub-list for method output_type
	34, // [34:41] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_api_securities_catalog_service_proto_init() }
func file_api_securities_catalog_service_proto_init() {
	if File_api_securities_catalog_service_proto != nil {
		return
	}
	file_api_securities_catalog_enums_proto_init()
	file_api_securities_catalog_model_security_proto_init()
	file_api_securities_catalog_model_security_listing_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_securities_catalog_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecuritiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecuritiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeSymbol); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityListingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityListingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityListingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityListingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityAndSecurityListing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPriceByDateAndSecListingIDsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPriceByDateAndSecListingIDsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecListingIdsByISINsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ISINExchangePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecListingIdWithISIN); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecListingIdsByISINsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddSecurityWithISINsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddSecurityWithISINsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsinSecurityListingPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityListingsRequest_ExternalIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecurityListingsRequest_ExchangeSymbols); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_securities_catalog_service_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*GetSecurityListingRequest_ExternalId)(nil),
		(*GetSecurityListingRequest_ExchangeSymbol)(nil),
	}
	file_api_securities_catalog_service_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*GetSecurityListingsRequest_ExternalIds_)(nil),
		(*GetSecurityListingsRequest_ExchangeSymbols_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_securities_catalog_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_securities_catalog_service_proto_goTypes,
		DependencyIndexes: file_api_securities_catalog_service_proto_depIdxs,
		MessageInfos:      file_api_securities_catalog_service_proto_msgTypes,
	}.Build()
	File_api_securities_catalog_service_proto = out.File
	file_api_securities_catalog_service_proto_rawDesc = nil
	file_api_securities_catalog_service_proto_goTypes = nil
	file_api_securities_catalog_service_proto_depIdxs = nil
}
