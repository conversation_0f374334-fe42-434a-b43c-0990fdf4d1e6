package servergenhook

import (
	"context"
	"fmt"
	"net/http"

	cmdGenConf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/sms"
	vnLoansAbflPb "github.com/epifi/gamma/api/vendornotification/lending/loans/abfl"
	"github.com/epifi/gamma/simulator/events"
	"github.com/epifi/gamma/simulator/serverhelper"

	"golang.org/x/sync/errgroup" // nolint <PERSON>(team): No linting to unblock. Need to migrate

	"github.com/aws/aws-sdk-go-v2/aws"

	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"

	types2 "github.com/epifi/be-common/pkg/cmd/types"

	"github.com/epifi/gamma/simulator/wire/types"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/simulator/wire"

	"github.com/gorilla/mux"
	"google.golang.org/grpc"

	"github.com/epifi/be-common/pkg/cfg"
	pkgTracing "github.com/epifi/be-common/pkg/tracing/opentelemetry"

	simulatorconf "github.com/epifi/gamma/simulator/config"
	genconf2 "github.com/epifi/gamma/simulator/config/genconf"
)

type vgServiceClientConn *grpc.ClientConn

func vgServiceClientConnProvider(conn vgServiceClientConn) *grpc.ClientConn {
	return conn
}

// nolint: funlen
func InitSimulatorServer(
	ctx context.Context,
	s *grpc.Server,
	gconf *cmdGenConf.Config,
	awsConf aws.Config,
	simDb types.SimulatorSqlDB,
	simCrdb types2.SimulatorCRDB,
	vgconn types.VendorgatewayClientConn,
	vnconn types.VendornotificationClientConn,
	dobbyCacheStorage types.DobbyCacheStorage,
	usersClient userPb.UsersClient,
	initNotifier chan<- cfg.ServerName) (func(), error) {

	cleanupFn := func() {}

	storagev2.InitDefaultCRDBTransactionExecutor(simCrdb)

	dbConn := types.SimulatorSqlDBProvider(simDb)
	simulatorDbConn := types2.SimulatorCRDBGormDBProvider(simCrdb)
	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	conf, err := simulatorconf.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return cleanupFn, err
	}
	dynConf, err := dynconf.LoadConfig(simulatorconf.Load, genconf2.NewConfig, cfg.SIMULATOR_GRPC_SERVICE)
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SIMULATOR_GRPC_SERVICE))
		return cleanupFn, err
	}
	_ = dynConf

	simulatorConf, sErr := cmdGenConf.Load(cfg.SIMULATOR_GRPC_SERVER)
	if sErr != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return cleanupFn, err
	}

	federalAccountsCallbackDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, dynConf.FederalAccountsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.ErrorNoCtx("failed to create sqs publisher", zap.Error(err))
		return cleanupFn, err
	}
	federalInboundTransactionNotificationDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, dynConf.FederalInboundTransactionNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.ErrorNoCtx("failed to create sqs publisher", zap.Error(err))
		return cleanupFn, err
	}
	federalUpiCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, dynConf.FederalUpiCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.ErrorNoCtx("failed to create sqs publisher", zap.Error(err))
		return cleanupFn, err
	}
	complaintStatusPublisher, err := sqs.NewPublisherWithConfig(ctx, dynConf.ComplaintStatusPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.ErrorNoCtx("failed to create sqs publisher", zap.Error(err))
		return cleanupFn, err
	}

	vgServiceConn := types.VendorgatewayClientConnProvider(vgconn)
	vnServiceConn := types.VendornotificationClientConnProvider(vnconn)
	vnLoansAbflClient := vnLoansAbflPb.NewAbflCallbackClient(vnServiceConn)
	g, grpCtx := errgroup.WithContext(context.Background())

	// Start grpc backed http server for the grpc reverse proxy
	httpGwClosure := serverhelper.StartHttpGwServer(conf, grpCtx, g)

	cs := wire.InitializeCustomerService(dbConn, simulatorDbConn)
	openBankingCustomerService := wire.InitializeOpenbankingCustomerService(dynConf, dbConn, simulatorDbConn, federalAccountsCallbackDelayPublisher)
	upiService := wire.InitializeUpiService(dynConf, simulatorDbConn, dbConn, federalInboundTransactionNotificationDelayPublisher, federalUpiCallbackPublisher, complaintStatusPublisher, conf.UPICallbackUrl, conf.AddFunds, conf.InboundTxnNotification, conf.Secrets.Ids[simulatorconf.SenderCode], conf)
	accountService := wire.InitializeAccountService(dbConn, simulatorDbConn)
	fedPANSvc := wire.InitializeFederalPANService(conf, dynConf)
	unNameCheckSvc := wire.InitializeFederalUNNameCheckService(conf)
	loyltyService := wire.InitializeLoyltyService(serverhelper.LoyltyAesGcmCryptor())
	leadSquaredService := wire.InitiaLeadSquaredService()
	preApprovedService := wire.InitializePreApprovedLoanService(simulatorDbConn, dbConn, vnLoansAbflClient, conf, usersClient, dynConf)
	qwikcilverService := wire.InitialiseQwikcilverService()
	fekyc := wire.InitializeFederalEKycService(sms.NewSMSClient(vgServiceClientConnProvider(vgServiceConn)), simulatorDbConn)

	r := mux.NewRouter()
	if conf.Tracing.Enable {
		pkgTracing.AddTracerToMux(r, cfg.SIMULATOR_HTTP_SERVER)
	}
	http.Handle("/", r)
	r.HandleFunc("/checkCustomerStatusForNonResident", openBankingCustomerService.CheckCustomerStatusForNonResident)
	r.HandleFunc("/createCustomerForNonResident", openBankingCustomerService.CreateCustomerForNonResident)
	r.HandleFunc("/upgradeKycLevel", openBankingCustomerService.UpgradeKYCLevel)
	r.HandleFunc("/dedupeCheck", cs.DedupeCheck)
	r.HandleFunc("/checkCustomerStatusFederal", cs.CreateCustomerStatusFederal)
	r.HandleFunc("/checkAccountStatusFederal", accountService.CreateAccountStatusFederal)
	r.HandleFunc("/createVirtualIdFederal", upiService.CreateVirtualId)
	r.HandleFunc("/listKeys", upiService.GetToken)
	r.HandleFunc("/generateUpiOtp", upiService.GenerateUpiOtp)
	r.HandleFunc("/registerMobile", upiService.RegisterMobile)
	r.HandleFunc("/ListKeys", upiService.ListKeys)
	r.HandleFunc("/ReqPay", upiService.ReqPay)
	r.HandleFunc("/ReqMandate", upiService.ReqMandate)
	r.HandleFunc("/RespAuthDetails", upiService.RespAuthDetails)
	r.HandleFunc("/RespAuthMandate", upiService.RespAuthMandate)
	r.HandleFunc("/RespMandateConfirmation", upiService.RespMandateConfirmation)
	r.HandleFunc("/ListAccount", upiService.ListAccount)
	r.HandleFunc("/ListAcctProvider", upiService.ListAccountProviders)
	r.HandleFunc("/ValAddFederal", upiService.ValidateAddress)
	r.HandleFunc("/RespTxnConfirmationFederal", upiService.RespTxnConfirmation)
	r.HandleFunc("/SetCredFederal", upiService.SetPin)
	r.HandleFunc("/ListVaeFederal", upiService.ListVae)
	r.HandleFunc("/panValidation", fedPANSvc.ValidatePAN_XML)
	r.HandleFunc("/UNNameCheck", unNameCheckSvc.UNNameCheck_XML)
	r.HandleFunc("/ReqCheckTxnStatusFederal", upiService.CheckTxnStatus)
	r.HandleFunc("/BalEnq", upiService.BalanceEnquiry)
	r.HandleFunc("/reqComplaint", upiService.ReqComplaint)
	r.HandleFunc("/checkComplaintStatus", upiService.CheckComplaintStatus)
	r.HandleFunc("/ListPsp", upiService.ListPsp)
	r.HandleFunc("/GetMapperInfo", upiService.GetMapperInfo)
	r.HandleFunc("/RegMapper", upiService.RegMapper)
	r.HandleFunc("/GetUpiLite", upiService.GetUpiLite)
	r.HandleFunc("/SyncUpiLiteInfo", upiService.SyncUpiLiteInfo)
	r.HandleFunc("/ActivateInternationalPayments", upiService.ActivateInternationalPayments)
	r.HandleFunc("/ValidateInternationalQR", upiService.ValidateInternationalQR)
	// loylty simulator apis
	r.HandleFunc("/offers/loylty/auth", loyltyService.CreateAuthToken)
	r.HandleFunc("/offers/loylty/egvbooking", loyltyService.CreateEGVBooking)
	r.HandleFunc("/offers/loylty/charitybooking", loyltyService.CreateCharityBooking)
	r.HandleFunc("/offers/loylty/create-order", loyltyService.CreateOrder)
	r.HandleFunc("/offers/loylty/order-details/{order-id}", loyltyService.GetOrderDetails)
	r.HandleFunc("/offers/loylty/confirm-order/{order-id}", loyltyService.ConfirmOrder)
	r.HandleFunc("/offers/loylty/giftcard/product", loyltyService.GetProductList)
	r.HandleFunc("/ekyc/namedob/validation", fekyc.ValidateNameDob_XML)
	r.HandleFunc("/v1/batch", events.RudderStack)
	r.HandleFunc("/api/credit-line/v1/credit-line-details", preApprovedService.GetCreditLineDetails)
	r.HandleFunc("/api/credit-line/v1/applicant-lookup", preApprovedService.ApplicantLookupLl)
	r.HandleFunc("/api/apiintegration/v2/GetRepaymentDetails", preApprovedService.GetRepaymentScheduleLL)
	r.HandleFunc("/api/credit-line/v1/mandate-log-status", preApprovedService.GetMandateStatusLl)
	r.HandleFunc("/api/apiintegration/v2/getMandateLink", preApprovedService.GetMandateLinkLl)
	r.HandleFunc("/api/apiintegration/v4/getApplicationSoa", preApprovedService.GetApplicationSoa).Methods("GET")
	r.HandleFunc("/api/credit-line/v1/get-limit", preApprovedService.GetApplicantStatus)
	r.HandleFunc("/IDFCEMandate/EMandateB2BPaynimmo.aspx", preApprovedService.GetMandateWebPage).Methods("POST")
	r.HandleFunc("/digital-lending-lms-utility-enc/getIFSCDetails", preApprovedService.GetIfscDetails).Methods("POST")
	r.HandleFunc("/api/apiintegration/v2/UploadDocument", preApprovedService.UploadDocument).Methods("POST")
	r.HandleFunc("/loan/account/v2.0.0/creation", preApprovedService.LoanAccountCreation).Methods("POST")
	r.HandleFunc("/loan/v1.0.0/enquiry", preApprovedService.LoanAccountCreationEnquiry).Methods("POST")
	r.HandleFunc("/createLoanCustomerFederal", openBankingCustomerService.CreateLoanCustomerFederalA2L).Methods("POST")
	r.HandleFunc("/loanCustomerCreationStatusFederal", openBankingCustomerService.CheckLoanCustomerStatusFederalA2L).Methods("POST")
	r.HandleFunc("/lending/federal/v1/enquiry", openBankingCustomerService.LoanEligibiltyRequestLentraFedA2L).Methods("POST")
	r.HandleFunc("/moneyview/lead/dedupe/bulk/pan", preApprovedService.MvGetDedupeStatus).Methods("GET")
	r.HandleFunc("/moneyview/lead/dedupe", preApprovedService.MvGetSingleDedupeStatus).Methods("POST")
	// leadsquared service simulator api
	r.HandleFunc("/salaryprogram/leadsquared/CreateOrUpdateLead", leadSquaredService.CreateOrUpdateLead).Methods("POST")

	// qwikcilver simulator apis
	r.HandleFunc("/offers/qwikcilver/authorization-code", qwikcilverService.GetAuthorizationCode)
	r.HandleFunc("/offers/qwikcilver/access-token", qwikcilverService.GetAccessToken)
	r.HandleFunc("/offers/qwikcilver/create-order", qwikcilverService.CreateOrder)
	r.HandleFunc("/offers/qwikcilver/activated-card-details/{order-id}", qwikcilverService.GetActivatedCardDetails)
	r.HandleFunc("/offers/qwikcilver/category-details", qwikcilverService.GetCategoryDetails)
	r.HandleFunc("/offers/qwikcilver/order-status/{order-id}", qwikcilverService.GetOrderStatus)
	r.HandleFunc("/offers/qwikcilver/product-details/{product-id}", qwikcilverService.GetProductDetails)
	r.HandleFunc("/offers/qwikcilver/product-list/{category-id}", qwikcilverService.GetProductList)

	vkycService := wire.InitializeVKYCService(dynConf, simulatorDbConn)

	r.HandleFunc("/v3/get-jwt", vkycService.GenerateSessionToken).Methods("POST")
	vkycRouter := r.PathPrefix("/test/videokyc/api").Subrouter()
	vkycRouter.HandleFunc("/customers", vkycService.AddNewCustomer).Methods("POST")
	vkycRouter.HandleFunc("/v2/generate-usertoken/{transaction_id}", vkycService.GenerateCustomerToken).Methods("GET")
	vkycRouter.HandleFunc("/v2/get-slot", vkycService.GetSlot).Methods("POST")
	vkycRouter.HandleFunc("/v2/book-slot", vkycService.BookSlot).Methods("POST")
	vkycRouter.HandleFunc("/v2/slot-agents", vkycService.AddNewCustomer).Methods("POST")
	vkycRouter.HandleFunc("/v2/link", vkycService.GenerateCustomerWeblink).Methods("GET")
	vkycRouter.HandleFunc("/v3/customers/{transaction_id}", vkycService.UpdateNewCustomerV3).Methods("PUT")
	vkycRouter.HandleFunc("/v2/transaction-events/{transaction_id}", vkycService.TransactionStatusEnquiry).Methods("GET")
	vkycRouter.HandleFunc("/v3/customers", vkycService.AddNewCustomerV3).Methods("POST")
	vkycRouter.HandleFunc("/agent-dashboard", vkycService.GetAgentCount).Methods("GET")
	vkycRouter.HandleFunc("/agent-dashboard-auth", vkycService.GetAgentDashToken).Methods("POST")

	// USStocks simulator api
	usstocksSvc := wire.InitializeUSStockService(dynConf, simulatorDbConn, conf.Stubs)
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/positions", usstocksSvc.GetAllOpenPositions).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/cip", usstocksSvc.UploadCIPDetails).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/orders", usstocksSvc.CreateOrder).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/accounts/activities", usstocksSvc.GetNonTradeActivity).Methods("GET")
	// regex is added on param value because above url will fall in same pattern
	// with out regex it will represent "/test/alpaca/v1/accounts/*"
	// ref: for understanding https://github.com/gorilla/mux/issues/593
	// and this regex make account id while pooling should not be null and start with Alphabet capital
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id:[A-Za-z0-9]*-.[^/]*}", usstocksSvc.GetAccountDetails).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/orders:by_client_order_id", usstocksSvc.GetOrderDetails).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/positions/{symbol}", usstocksSvc.GetPositionForSymbol).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/account/configurations", usstocksSvc.UpdateTradeConfiguration).Methods("PATCH")
	r.HandleFunc("/test/alpaca/v1/transfers/jit/reports", usstocksSvc.GetReportFile).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/orders/{order_id}", usstocksSvc.CancelOrder).Methods("DELETE")
	r.HandleFunc("/test/alpaca/v1/accounts", usstocksSvc.CreateAccount).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/clock", usstocksSvc.GetExchangeStatus).Methods("GET")
	r.HandleFunc("/test/v2/iex", usstocksSvc.GetPriceUpdates)
	r.HandleFunc("/test/alpaca/v1/trade/events", usstocksSvc.GetOrderUpdates).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/account/events", usstocksSvc.GetAccountUpdates).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/recipient_banks", usstocksSvc.SendBankDetails).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/recipient_banks", usstocksSvc.GetBankDetails).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/recipient_banks/{relationship_id}", usstocksSvc.DeleteBankDetails).Methods("DELETE")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/transfers", usstocksSvc.InitiateFundTransfer).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/transfers", usstocksSvc.GetAllFundTransfers).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/journals", usstocksSvc.CreateJournal).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/journals", usstocksSvc.GetAllJournals).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/documents", usstocksSvc.GetDocument).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/accounts/{account_id}/documents/{document_id}/download", usstocksSvc.DownloadDocument).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/journals/{journal_id:[A-Za-z0-9]*-.[^/]*}", usstocksSvc.GetJournal).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/journals/batch", usstocksSvc.CreateBatchJournal).Methods("POST")
	r.HandleFunc("/test/alpaca/v1/trading/accounts/{account_id}/account", usstocksSvc.GetTradingAccount).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/events/journals/status", usstocksSvc.GetJournalUpdates).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/events/transfers/status", usstocksSvc.GetFundTransferUpdates).Methods("GET")
	r.HandleFunc("/test/alpaca/v1/assets/{symbol}", usstocksSvc.GetAsset).Methods("GET")

	// DMP dispute simulator api
	dmpDisputeService := wire.InitializeDmpFederalDisputeService()
	r.HandleFunc("/DMP/v1.0.0/createDispute", dmpDisputeService.CreateDispute).Methods("POST")
	r.HandleFunc("/DMP/v1.0.0/disputeStatusCheck", dmpDisputeService.GetDisputeStatus).Methods("GET")
	r.HandleFunc("/DMP/v1.0.0/disputeCorrespondence", dmpDisputeService.AttachCorrespondence).Methods("POST")
	r.HandleFunc("/DMP/v1.0.0/disputeDocument", dmpDisputeService.SendDocument).Methods("POST")

	freshdeskSvc := wire.InitializeFreshdeskService()
	ozonetelSvc := wire.InitializeOzonetelService()
	// freshdesk ticket api's
	ticketRouter := r.PathPrefix("/api/v2/tickets").Subrouter()
	ticketRouter.HandleFunc("/{ticketId}", freshdeskSvc.UpdateTicket).Methods("PUT")
	ticketRouter.HandleFunc("", freshdeskSvc.CreateTicket).Methods("POST")
	ticketRouter.HandleFunc("/{ticketId}/notes", freshdeskSvc.AddPrivateNoteTicket).Methods("POST")
	ticketRouter.HandleFunc("/{ticketId}/conversations", freshdeskSvc.GetTicketConversations).Methods("GET")
	ticketRouter.HandleFunc("/{ticketId}", freshdeskSvc.GetTicketById).Methods("GET")

	solutionRouter := r.PathPrefix("/api/v2/solutions").Subrouter()
	solutionRouter.HandleFunc("/categories", freshdeskSvc.GetAllCategories).Methods("GET")
	solutionRouter.HandleFunc("/categories/{category_id}/folders", freshdeskSvc.GetAllFoldersForCategory).Methods("GET")
	solutionRouter.HandleFunc("/folders/{article_id}/articles", freshdeskSvc.GetAllArticlesForFolder).Methods("GET")
	solutionRouter.HandleFunc("/articles/{article_id}", freshdeskSvc.GetArticle).Methods("GET")

	// freshdesk contact api's
	contactRouter := r.PathPrefix("/api/v2/contacts").Subrouter()
	contactRouter.HandleFunc("/{contactId}", freshdeskSvc.GetContactById).Methods("GET")
	contactRouter.HandleFunc("/{contactId}", freshdeskSvc.DeleteContact).Methods("DELETE")

	// freshdesk agent api's
	r.HandleFunc("/api/v2/agents", freshdeskSvc.FetchAgent).Methods("GET")

	// Ozonetel api's
	r.HandleFunc("/CAServices/AgentManualDial.php", ozonetelSvc.CallUser).Methods("GET")

	smsSvc := wire.InitializeSmsService()
	r.HandleFunc("/exotel/v1/Accounts/test/SMS/send.json", smsSvc.SendSmsExotel)
	r.HandleFunc("/exotel/v1/Accounts/test/SMS/Messages/{sid}.json", smsSvc.GetSmsStatusExotel)
	r.HandleFunc("/v4", smsSvc.SendSmsKaleyra).Methods("GET")
	r.HandleFunc("/servlet/com.aclwireless.pushconnectivity.listeners.TextListener", smsSvc.SendSmsACL)
	r.HandleFunc("/2010-04-01/Accounts/test/Messages.json", smsSvc.SendSmsTwilio)
	r.HandleFunc("/2010-04-01/Accounts/test/Messages/{sid}.json", smsSvc.GetSmsStatusTwilio)
	fmt.Println("Starting old http server on port:", conf.Server.Port)

	s3ClientForMutualFund := serverhelper.GetS3Client(ctx, conf, conf.Application.Cams.S3Bucket)
	camsSvc := wire.InitializeCamsService(s3ClientForMutualFund)
	r.HandleFunc("/cams/ProcessOrderFeedFile", camsSvc.ProcessOrderFeedFile)
	r.HandleFunc("/cams/ProcessFATCAFeedFile", camsSvc.ProcessFATCAFeedFile)
	r.HandleFunc("/cams/ProcessElogFile", camsSvc.ProcessElogFile)
	r.HandleFunc("/cams/GetOrderFeedFileStatus", camsSvc.GetOrderFeedFileStatus)

	karvySvc := wire.InitializeKarvyService()
	r.HandleFunc("/karvy/ProcessFATCAFeedFile", karvySvc.ProcessFATCAFeedFile)
	r.HandleFunc("/karvy/ProcessOrderFeedFileSync", karvySvc.ProcessOrderFeedFile)

	fitttSvc := wire.InitialiseFitttService(simulatorDbConn)
	r.HandleFunc("/test/rounaz/cricket/{project_id}/match/{match_id}/", fitttSvc.MatchDetails).Methods("GET")
	r.HandleFunc("/test/rounaz/cricket/{project_id}/auth/", fitttSvc.GenerateAccessToken).Methods("POST")
	r.HandleFunc("/test/rounaz/football/match/{match_id}/", fitttSvc.FootballMatchDetails).Methods("GET")
	r.HandleFunc("/test/rounaz/football/auth/", fitttSvc.GenerateFootballAccessToken).Methods("POST")

	// Init IPStack Service
	ipStackService := wire.InitializeIpStackService()
	r.HandleFunc("/test/ipstack/{ip}", ipStackService.GetLocationDetailsFromIp).Methods("GET")

	whatsappSvc := wire.InitializeWhatsappService(conf.AclDlrStatusCallbackUrl)
	r.HandleFunc("/pull-platform-receiver/wa/messages", whatsappSvc.SendWhatsappACL).Methods("POST")

	// onemoney AA api's
	onemoneyAA := wire.InitializeOneMoneyAAService(simulatorDbConn, conf, vgServiceConn, dynConf)
	r.HandleFunc("/Consent", onemoneyAA.PostConsent).Methods("POST")
	r.HandleFunc("/Consent/handle/{consent_handle}", onemoneyAA.GetConsentStatus).Methods("GET")
	r.HandleFunc("/Consent/{consent_id}", onemoneyAA.GetConsentArtefact).Methods("GET")
	r.HandleFunc("/FI/request", onemoneyAA.FIData).Methods("POST")
	r.HandleFunc("/FI/fetch/{session_id}", onemoneyAA.FIFetchData).Methods("GET")
	r.HandleFunc("/Account/link/status", onemoneyAA.GetAccountLinkStatus).Methods("POST")
	r.HandleFunc("/fiplist", onemoneyAA.GetFipList).Methods("GET")
	r.HandleFunc("/accounts/discover", onemoneyAA.GetDiscoveredAccounts).Methods("GET")
	r.HandleFunc("/accounts/discover/multiplefips", onemoneyAA.GetDiscoveredAccountsMultipleFips).Methods("POST")
	r.HandleFunc("/linkedaccount/list", onemoneyAA.GetLinkedAaAccounts).Methods("GET")
	r.HandleFunc("/account/link", onemoneyAA.AccountLink).Methods("POST")
	r.HandleFunc("/account/confirmLink", onemoneyAA.VerifyOtpToLinkAccount).Methods("POST")
	r.HandleFunc("/account/delink", onemoneyAA.AccountDeLink).Methods("POST")
	r.HandleFunc("/consent/approve", onemoneyAA.ConsentApprove).Methods("POST")
	r.HandleFunc("/consent/update", onemoneyAA.ConsentUpdate).Methods("POST")
	r.HandleFunc("/Heartbeat", onemoneyAA.GetHeartbeatStatus).Methods("GET")
	r.HandleFunc("/Consent/status/bulk", onemoneyAA.GetBulkConsentStatus).Methods("POST")
	r.HandleFunc("/loginUser", onemoneyAA.LoginUser).Methods("POST")
	r.HandleFunc("/verifyOtpToLoginUser", onemoneyAA.VerifyOtpToLoginUser).Methods("POST")
	// finvu AA api's
	finvuAA := wire.InitializeFinvuAAService(simulatorDbConn, conf, vgServiceConn, dynConf)
	r.HandleFunc("/loginOtp", finvuAA.LoginOtp).Methods("POST")
	r.HandleFunc("/verifyOtp", finvuAA.VerifyOtp).Methods("POST")
	r.HandleFunc("/finvu/accounts/discover", finvuAA.GetDiscoveredAccounts).Methods("POST")
	r.HandleFunc("/finvu/account/link", finvuAA.LinkAccount).Methods("POST")
	r.HandleFunc("/finvu/account/confirmLink", finvuAA.ConfirmLinkAccount).Methods("POST")
	r.HandleFunc("/finvu/linkedaccount/list", finvuAA.GetLinkedAccounts).Methods("POST")
	r.HandleFunc("/finvu/account/consentRequest", finvuAA.AccountConsent).Methods("POST")
	r.HandleFunc("/Account/link/Status", finvuAA.GetAccountLinkStatusBulk).Methods("POST")
	r.HandleFunc("/web/token", finvuAA.GenerateFinvuJwtToken).Methods("POST")
	r.HandleFunc("/consentapi", finvuAA.GenerateCookie).Methods("POST")
	r.HandleFunc("/consent/revoke", finvuAA.ConsentRevoke).Methods("POST")

	creditReportSvc := wire.InitializeCreditReportService()
	r.HandleFunc("/ECV-P2/content/enhancedMatch.action", creditReportSvc.FetchCreditReport).Methods("POST")
	r.HandleFunc("/ECV-P2/content/onDemandRefresh.action", creditReportSvc.FetchCreditReportForExistingUser).Methods("POST")
	r.HandleFunc("/ECV-P2/content/consumerConsentReRegistration.action", creditReportSvc.ExtendSubscription).Methods("POST")

	bcSvc := wire.InitializeBcService()
	r.HandleFunc("/ecc/v1/generateKey", bcSvc.GenerateKeyPair).Methods("GET")
	r.HandleFunc("/ecc/v1/getSharedKey", bcSvc.GetSharedSecret).Methods("POST")
	r.HandleFunc("/ecc/v1/decrypt", bcSvc.DecryptData).Methods("POST")

	shipwaySvc := wire.InitialiseShipwayService2()
	r.HandleFunc("/shipway/BulkPushOrderData", shipwaySvc.BulkPushOrderData).Methods("POST")

	cvlSvc := wire.InitializeWealthCvlService(conf.Stubs["wealthonb"], dynConf)
	r.HandleFunc("/CVLPanInquiry.svc", cvlSvc.Handler).Methods("POST")

	internationalfundtransferSvc := wire.InitializeInternationalFundTransfer(dynConf)
	r.HandleFunc("/test/federal/checkEligibility", internationalfundtransferSvc.CheckLRSEligibility).Methods("POST")
	r.HandleFunc("/test/federal/fetchRate", internationalfundtransferSvc.GetForexRate).Methods("POST")
	r.HandleFunc("/test/federal/TCSCalculation", internationalfundtransferSvc.CalculateTcs).Methods("POST")
	// todo(numan) added exact url once federal gives url
	r.HandleFunc("/test/federal/InquireOrReportGSTCollection", internationalfundtransferSvc.InquireOrReportGSTCollection).Methods("POST")

	manchSvc := wire.InitializeWealthManchService(simulatorDbConn)
	manchSubrouter := r.PathPrefix("/app/api/").Subrouter()
	manchSubrouter.HandleFunc("/transactions", manchSvc.CreateTransaction).Methods("POST")
	manchSubrouter.HandleFunc("/transactions/{txn_id}/status", manchSvc.GetTransactionStatus).Methods("GET")
	manchSubrouter.HandleFunc("/documents/{txn_id}/sign-url", manchSvc.GetSignUrl).Methods("GET")
	manchSubrouter.HandleFunc("/documents/{doc_id}/content", manchSvc.DownloadDocument).Methods("GET")
	manchSubrouter.HandleFunc("/transactions/{transactionId}/documents", manchSvc.GetAllDocsOfTxn).Methods("GET")
	manchSubrouter.HandleFunc("/transactions/{transactionId}/documents", manchSvc.DeleteAllDocsOfTxn).Methods("DELETE")

	wealthCkycSvc := wire.InitializeWealthCkycService(conf.Stubs["wealthonb"])
	r.HandleFunc("/Search/ckycverificationservice/verify", wealthCkycSvc.CkycSearch).Methods("POST")
	r.HandleFunc("/Search/ckycverificationservice/download", wealthCkycSvc.CkycDownload).Methods("POST")

	wealthNsdlSvc := wire.InitializeWealthNsdlService(conf.Stubs["wealthonb"])
	r.HandleFunc("/TIN/PanInquiryBackEnd", wealthNsdlSvc.VerifyPan).Methods("POST")

	wealthKarzaSvc := wire.InitializeWealthKarzaService()
	r.HandleFunc("/v3/kycocr", wealthKarzaSvc.GetDocOcr).Methods("POST")

	wealthDigilockcerSvc := wire.InitializeWealthDigilockerService(conf)
	r.HandleFunc("/public/oauth2/1/token", wealthDigilockcerSvc.GetAccessToken).Methods("POST")
	r.HandleFunc("/public/oauth2/2/files/issued", wealthDigilockcerSvc.GetListOfIssuedDocuments).Methods("GET")
	r.HandleFunc("/public/oauth2/1/file/{uri}", wealthDigilockcerSvc.GetFileFromUri).Methods("GET")
	r.HandleFunc("/public/oauth2/3/xml/eaadhaar", wealthDigilockcerSvc.GetAadhaarInXml).Methods("GET")

	tssSvc := wire.InitializeTssService(dynConf)
	r.HandleFunc("/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert", tssSvc.GenerateScreeningAlert).Methods("POST")

	mfCentralService := wire.InitializeMFCentralService(dynConf, vendorapi.New(http.DefaultClient, nil, httpcontentredactor.GetInstance(), simulatorConf.VendorApiConf(), conf.Application.Environment), simulatorDbConn)
	r.HandleFunc("/mfcentral/GenerateToken", mfCentralService.GenerateToken).Methods(http.MethodPost)
	r.HandleFunc("/mfcentral/api/client/v1/updateEmail", mfCentralService.UpdateEmail).Methods(http.MethodPost)
	r.HandleFunc("/mfcentral/api/client/v1/updateMobile", mfCentralService.UpdateMobile).Methods(http.MethodPost)
	r.HandleFunc("/mfcentral/api/client/v1/investorconsent", mfCentralService.InvestorConsent).Methods(http.MethodPost)
	r.HandleFunc("/mfcentral/api/client/v1/submitcassummaryrequest", mfCentralService.SubmitCasSummary).Methods(http.MethodPost)
	r.HandleFunc("/mfcentral/api/client/v1/getcasdocument", mfCentralService.GetCasDocument).Methods(http.MethodPost)

	smallCaseService := wire.InitializeSmallcaseService(dynConf, vendorapi.New(http.DefaultClient, nil, httpcontentredactor.GetInstance(), simulatorConf.VendorApiConf(), conf.Application.Environment), simulatorDbConn)
	r.HandleFunc("/smallcase/CreateTransaction", smallCaseService.CreateTransaction).Methods(http.MethodPost)
	r.HandleFunc("/smallcase/InitiateHoldingsImportURL", smallCaseService.InitiateHoldingsImport).Methods(http.MethodPost)
	r.HandleFunc("/smallcase/TriggerHoldingsImportFetchURL", smallCaseService.TriggerHoldingsImportFetch).Methods(http.MethodPost)

	txnCategorizerService := wire.InitialiseTxnCategorizerService(dynConf)
	r.HandleFunc("/categories", txnCategorizerService.GetDsCategories).Methods(http.MethodPost)

	fiftyfinService := wire.InitializeFiftyfinService(simCrdb, conf.Stubs["fiftyfin"])
	r.HandleFunc("/fiftyfin/auth/api/v2/user_signup/", fiftyfinService.UserSignup).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/auth/api/v2/user/user_id/{user_id}", fiftyfinService.FetchUser).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/auth/api/v1/update_user/", fiftyfinService.UpdateUser).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/kyc/api/v2/check_kyc_verification/{user_id}/PAN", fiftyfinService.CheckPan).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/kyc/api/v2/kyc_verification/", fiftyfinService.LinkPan).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/generate_karvy_otp/", fiftyfinService.GenerateKarvyOtp).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/validate_karvy_otp/", fiftyfinService.ValidateKarvyOtp).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/generate_cams_otp/", fiftyfinService.GenerateCamsOtp).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/validate_cams_otp/", fiftyfinService.ValidateCamsOtp).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/fetch_mutual_fund_portfolio/{user_id}", fiftyfinService.FetchMfPortfolio).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/lien_creation_karvy/", fiftyfinService.CreateLienKarvy).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/lien_confirmation_karvy/", fiftyfinService.ConfirmLienKarvy).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/lien_creation_cams/", fiftyfinService.CreateLienCams).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/lien_confirmation_cams/", fiftyfinService.ConfirmLienCams).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/portfolio/api/v2/fetch_loan_portfolio/{user_id}", fiftyfinService.FetchLoanPortfolio).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/kyc/api/v2/check_ckyc/", fiftyfinService.InitiateKycProcess).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/kyc/api/v2/add_extra_details/", fiftyfinService.AddAdditionalKycDetails).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/loans/api/v1/fetch_bank_account/{user_id}", fiftyfinService.FetchBankAccountDetails).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/loans/api/v2/initiate_loan/", fiftyfinService.InitiateLoan).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/loans/api/v1/link_bank_account/", fiftyfinService.LinkBankAccount).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/loans/api/v1/fetch_existing_loan/{user_id}/{loan_id}", fiftyfinService.FetchExistingLoanDetails).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/loans/api/v1/fetch_individual_loan/{user_id}/{loan_id}", fiftyfinService.FetchIndividualLoanDetails).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/loans/api/v2/create_loan/", fiftyfinService.CreateLoan).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/post_loan/api/v1/close_loan/", fiftyfinService.CloseLoan).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/post_loan/api/v1/get_bank_details/", fiftyfinService.GetBankDetailsForRepayment).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/post_loan/api/v1/get_soa_statement/", fiftyfinService.GetLoanSoaStatement).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/post_loan/api/v1/get_foreclosure_statement/", fiftyfinService.GetForeClosureStatement).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/post_loan/api/v1/part_payment/", fiftyfinService.InitiateLoanPartPayment).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/auth/api/v2/update_user_bulk/", fiftyfinService.UpdateUserBulk).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/post_loan/api/v1/void_loan/", fiftyfinService.VoidLoan).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/auth/api/v1/notifications/{user_id}", fiftyfinService.FetchUserNotifications).Methods(http.MethodGet)
	r.HandleFunc("/fiftyfin/eligibility/api/v2/loan_eligibility_check/", fiftyfinService.LoanEligibilityCheck).Methods(http.MethodPost)
	r.HandleFunc("/fiftyfin/auth/api/v2/dedupe_check/{user_id}", fiftyfinService.DedupeCheck).Methods(http.MethodGet)

	fennelService := wire.InitialiseFennelService(dobbyCacheStorage, usersClient)
	r.HandleFunc("/fennel/api/v1/log", fennelService.LogDatasets).Methods(http.MethodPost)
	r.HandleFunc("/fennel/api/v1/extract_features", fennelService.ExtractFeatureSets).Methods(http.MethodPost)

	onsurityService := wire.InitialiseOnsurityService(simulatorDbConn, dynConf)
	r.HandleFunc("/api/invite-member", onsurityService.MemberInvitationApi).Methods(http.MethodPost)
	r.HandleFunc("/api/fetch-membership-details", onsurityService.MemberDetailsApi).Methods(http.MethodPost)
	r.HandleFunc("/api/download-certificate", onsurityService.DownloadDocumentsApi).Methods(http.MethodGet)
	r.HandleFunc("/api/deactivate-member", onsurityService.DeactivateMemberApi).Methods(http.MethodPost)

	setuService := wire.InitialiseSetuService(dynConf)
	r.HandleFunc("/api/bbps/setu/bills/fetch/request", setuService.FetchBill).Methods(http.MethodPost)
	r.HandleFunc("/api/bbps/setu/bills/fetch/response", setuService.GetFetchedBill).Methods(http.MethodPost)
	r.HandleFunc("/api/bbps/setu/bills/payment/request", setuService.PostPaymentDetails).Methods(http.MethodPost)
	r.HandleFunc("/api/bbps/setu/bills/payment/response", setuService.CheckPaymentStatus).Methods(http.MethodPost)
	r.HandleFunc("/api/bbps/setu/health", setuService.CheckHealth).Methods(http.MethodGet)
	r.HandleFunc("/api/bbps/setu/billers", setuService.ListBillersForCategory).Methods(http.MethodGet)
	r.HandleFunc("/api/bbps/setu/categories", setuService.ListAllCategories).Methods(http.MethodPost)
	r.HandleFunc("/api/bbps/setu/disputes", setuService.ListDisputes).Methods(http.MethodPost)
	r.HandleFunc("/api/bbps/setu/transactions", setuService.ListPaidBills).Methods(http.MethodPost)

	ignosisAaService := wire.InitialiseIgnosisAaService(conf.Stubs)
	r.HandleFunc("/transactions/analysis/sync/consolidated-analysis/initiate", ignosisAaService.SyncFastAnalysis).Methods(http.MethodPost)
	r.HandleFunc("/transactions/analysis/async/comprehensive/status/{tracking_id}/{reference_id}", ignosisAaService.SyncDetailedAnalysisStatus).Methods(http.MethodGet)
	r.HandleFunc("/transactions/analysis/async/comprehensive/fetch/response/{tracking_id}/{reference_id}", ignosisAaService.SyncDetailedAnalysis).Methods(http.MethodGet)

	savenCcService, err := wire.InitializeSavenCreditCardService(conf)
	if err != nil {
		logger.Error(ctx, "failed to initialize saven credit card service", zap.Error(err))
		return cleanupFn, err
	}
	r.HandleFunc("/onb/handshake", savenCcService.GenerateCreditCardSdkAuthToken).Methods(http.MethodPost)
	r.HandleFunc("/card/webhook/cardDeliveredStatus", savenCcService.UpdateCreditCardDeliveryState).Methods(http.MethodPost)

	// start secure http application server
	oldHttpServer := epifiserver.NewHttpServer(conf.Server.Port, http.DefaultServeMux)
	oldHttpServer.TLSConfig, err = serverhelper.GetTLSConfig(conf)
	if err != nil {
		logger.Panic("failed to load ssl cert for simulator", zap.Error(err))
	}
	// tls config has certificates, it will ignore the passed-in filenames.
	oldHttpServerClosure := epifiserver.StartHttpsServer(grpCtx, g, oldHttpServer, "", "")

	// Use case for unsecure http server:
	//   * event broker RudderStack client
	unsecureHttpServer := epifiserver.NewHttpServer(conf.Server.UnsecureHttpPort, http.DefaultServeMux)
	unsecureHttpServerClosure := epifiserver.StartHttpServer(grpCtx, g, unsecureHttpServer)
	healthCheckClosure := func() {}
	// skip http servers since all the use-cases with this is not required for TEST_TENANT setup where
	// all servers are hosted in the same instance for short time for testing purpose.
	if !cfg.IsTestTenantEnabled() {
		// instantiate health check server
		healthCheckServer := epifiserver.NewHttpServer(conf.Server.HealthCheckPort, http.DefaultServeMux)
		epifiserver.RegisterHealthCheckEndpoint(http.DefaultServeMux, conf.Application.Name)
		epifiserver.RegisterMonitoringEndpoint(http.DefaultServeMux)
		healthCheckClosure = epifiserver.StartHttpServer(grpCtx, g, healthCheckServer)
	}
	initNotifier <- cfg.SIMULATOR_GRPC_SERVER

	grpcClosure := epifiserver.StartGRPCServer(grpCtx, g, s, string(gconf.Name()), gconf.ServerPorts().GrpcPort)

	// block till we get sig term or one of server crashes
	epifiserver.HandleGracefulShutdown(grpCtx, g, grpcClosure, healthCheckClosure, httpGwClosure, oldHttpServerClosure, unsecureHttpServerClosure)

	return cleanupFn, nil
}
