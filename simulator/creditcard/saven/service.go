package saven

import (
	"errors"
	"io/ioutil"
	"net/http"
	"time"

	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	ccVendorsPb "github.com/epifi/gamma/api/vendors/saven/creditcard"
	"github.com/epifi/gamma/simulator/config"
)

type Service struct {
	conf *config.Config
	// TODO(CB): get the secrets added for jwtVerifier and uncomment this section
	// jwtVerifier *JWTVerifier
}

func NewService(conf *config.Config) *Service {
	return &Service{
		conf: conf,
	}
}

func (s *Service) GenerateCreditCardSdkAuthToken(w http.ResponseWriter, req *http.Request) {
	verifyReqErr := s.verifyRequestHeader(req)
	if verifyReqErr != nil {
		logger.ErrorNoCtx("failed to verify request header", zap.Error(verifyReqErr))
		s.sendErrorResponse(w, http.StatusUnauthorized, "invalid request header")
		return
	}

	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		s.sendErrorResponse(w, http.StatusInternalServerError, "invalid request")
		return
	}

	tokenizedVgReq := &ccVendorsPb.CreditCardSdkAuthTokenizedRequest{}
	err = protojson.Unmarshal(data, tokenizedVgReq)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		s.sendErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}
	/*
		TODO(CB): get the secrets added for jwtVerifier and uncomment this section

			tokenClaims, err := s.jwtVerifier.VerifyTokenAndGetClaims(tokenizedVgReq.GetToken())
			if err != nil {
				logger.ErrorNoCtx("failed to verify token and get claims", zap.Error(err))
				s.sendErrorResponse(w, http.StatusUnauthorized, "invalid token in request")
				return
			}

			// extract and log JTI
			jti, ok := tokenClaims["jti"].(string)
			if !ok {
				logger.ErrorNoCtx("failed to get jti from token claims")
				s.sendErrorResponse(w, http.StatusInternalServerError, "failed to get JTI from token claims")
				return
			}
			logger.InfoNoCtx("JTI from token claims for GenerateCreditCardSdkAuthTokenRequest", zap.String("JTI", jti))

			// Create a map to hold your structured data
			requestData := make(map[string]interface{})
			// Copy fields you need from tokenClaims
			for key, value := range tokenClaims {
				if key != "exp" && key != "iat" && key != "jti" { // Skip JWT-specific fields
					requestData[key] = value
				}
			}

			// Convert to JSON
			jsonBytes, err := json.Marshal(requestData)
			if err != nil {
				logger.ErrorNoCtx("failed to marshal request data", zap.Error(err))
				s.sendErrorResponse(w, http.StatusInternalServerError, "failed to process request data")
				return
			}

			// Unmarshal to protobuf
			var ccAuthRequest = &ccVendorsPb.GenerateCreditCardSdkAuthTokenRequest{}
			if err := protojson.Unmarshal(jsonBytes, ccAuthRequest); err != nil {
				logger.ErrorNoCtx("failed to unmarshal to proto", zap.Error(err))
				s.sendErrorResponse(w, http.StatusBadRequest, "invalid token format")
				return
			}
	*/

	ccAuthResp := &ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse{
		Status: "success",
		Data: &ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse_Data{
			ModuleName: getModuleNameForApplicantType(""),
			Token:      uuid.NewString(),
			Workflow:   getMockWorkflowDetailsForApplicantType("", "dummy_ext_user_id"),
		},
	}

	respData, err := protojson.Marshal(ccAuthResp)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal GenerateCreditCardSdkAuthTokenResponse", zap.Error(err))
		s.sendErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(respData)
	if err != nil {
		logger.ErrorNoCtx("failed to write response", zap.Error(err))
		return
	}
}

func (s *Service) UpdateCreditCardDeliveryState(w http.ResponseWriter, _ *http.Request) {
	w.WriteHeader(http.StatusOK)
	return // nolint:staticcheck
}

func getModuleNameForApplicantType(applicantType string) string {
	switch applicantType {
	case "CMS":
		return "CMS"
	default:
		return "ONBOARDING"
	}
}

func getMockWorkflowDetailsForApplicantType(applicantType, extUserId string) *ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse_Data_Workflow {
	switch applicantType {
	case "PRE-APPROVED":
		return &ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse_Data_Workflow{
			WorkflowId:           uuid.NewString(),
			UserLocalId:          "userLocalId",
			HandshakeUserLocalId: extUserId,
			WorkflowStatus:       "COMPLETED",
			WorkflowState:        "CMS_READY",
			WorkflowType:         "ONBOARDING",
			WorkflowVersion:      "v1",
			WorkflowMessage:      "successfully generated CMS auth token",
			CreatedAt:            dateTimePkg.TimestampToString(timestamp.Now(), time.RFC3339, dateTimePkg.IST),
			UpdatedAt:            dateTimePkg.TimestampToString(timestamp.Now(), time.RFC3339, dateTimePkg.IST),
		}
	default:
		return &ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse_Data_Workflow{
			WorkflowId:           uuid.NewString(),
			UserLocalId:          "userLocalId",
			HandshakeUserLocalId: extUserId,
			WorkflowStatus:       "IN_PROGRESS",
			WorkflowState:        "PAN_CHECK",
			WorkflowType:         "ONBOARDING",
			WorkflowVersion:      "v1",
			WorkflowMessage:      "workflow successfully moved to pan check state",
			CreatedAt:            dateTimePkg.TimestampToString(timestamp.Now(), time.RFC3339, dateTimePkg.IST),
			UpdatedAt:            dateTimePkg.TimestampToString(timestamp.Now(), time.RFC3339, dateTimePkg.IST),
		}
	}
}

func (s *Service) verifyRequestHeader(req *http.Request) error {
	if req.Header.Get("Content-Type") != "application/json" {
		return errors.New("invalid content type")
	}

	if req.Header.Get("api-key") == "" {
		return errors.New("invalid API key")
	}
	return nil
}

func (s *Service) sendErrorResponse(w http.ResponseWriter, statusCode int, errorMsg string) {
	w.WriteHeader(statusCode)
	errorResp, err := protojson.Marshal(&ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse{
		Status: "Error",
		Error: &ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse_Error{
			ErrorCode:    "SE061",
			ErrorType:    "ServiceError",
			ErrorMessage: errorMsg,
		},
	})
	if err != nil {
		logger.ErrorNoCtx("failed to marshal error response", zap.Error(err))
		return
	}
	_, err = w.Write(errorResp)
	if err != nil {
		logger.ErrorNoCtx("failed to write error response", zap.Error(err))
		return
	}
}
