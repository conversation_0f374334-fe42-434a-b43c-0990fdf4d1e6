package userattributesfetcher

import (
	"context"
	"fmt"
	"github.com/epifi/gamma/insights/networth/utils"
	"github.com/epifi/gamma/pkg/networth"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	stocksVendorPb "github.com/epifi/gamma/api/vendors/moengage/stocks"
)

var (
	WirePortfolioTrackerAttributeFetcherSet = wire.NewSet(NewPortfolioTrackerAttributeFetcher, wire.Bind(new(IUserAttributesFetcher), new(*PortfolioTrackerAttributeFetcher)))
)

type PortfolioTrackerAttributeFetcher struct {
	netWorthClient          networthPb.NetWorthClient
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
	catalogManagerClient    catalogPb.CatalogManagerClient
}

func NewPortfolioTrackerAttributeFetcher(netWorthClient networthPb.NetWorthClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	catalogManagerClient catalogPb.CatalogManagerClient) *PortfolioTrackerAttributeFetcher {
	return &PortfolioTrackerAttributeFetcher{
		netWorthClient:          netWorthClient,
		variableGeneratorClient: variableGeneratorClient,
		catalogManagerClient:    catalogManagerClient,
	}
}

var (
	analyserVariables = []analyserVariablePb.AnalysisVariableName{
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO}
)

func (p *PortfolioTrackerAttributeFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	actorId := req.GetActorId()
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Summary:
			portfolioTrackerSummary, err := p.getSummary(ctx, actorId)
			if err != nil {
				logger.Error(ctx, "failed to get portfolio tracker summary for content api", zap.Error(err))
				return nil, errors.Wrapf(err, "failed to get portfolio tracker summary for content api")
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_Summary)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_PortfolioChangeDetails{
					PortfolioChangeDetails: portfolioTrackerSummary,
				},
			}
		default:
			logger.Error(ctx, "unsupported parameter field encountered in loan attributes fetcher", zap.String("field", string(field)))
			return nil, fmt.Errorf("unsupported parameter field encountered in loan attributes fetcher %v", string(field))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (p *PortfolioTrackerAttributeFetcher) getSummary(ctx context.Context, actorId string) (*moengageVendorPb.PortfolioChangeDetails, error) {
	previousDayTimestamp := timestampPb.New(time.Now().AddDate(0, 0, -1))

	analyserVariableMap, err := p.getAnalysisVariableMap(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get analysis variable map: %v", err)
	}

	var topGainerName, topGainerPercentage string
	topGainers, _, err := utils.GetTopGainerAndTopLoser(ctx, &utils.GetTopGainerAndTopLoserRequest{
		AnalyserVariableMap: analyserVariableMap,
		AnalysisVariables:   analyserVariables,
		TopGainers:          1,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get top gainers and losers: %v", err)
	}
	if len(topGainers) == 1 {
		topGainerName = topGainers[0].Name
		topGainerPercentage = fmt.Sprintf("%.2f", topGainers[0].ReturnPercentage)
	}

	dailyNavChangePercentage, err := getNifty50Returns(analyserVariableMap)
	if err != nil {
		return nil, fmt.Errorf("failed to get nifty50 daily change percentage: %v", err)
	}

	dailyChangeAmount, dailyChangePercentage, err := getPortfolioDailyChange(ctx, analyserVariableMap)
	if err != nil {
		return nil, fmt.Errorf("failed to get portfolio daily change percentage: %v", err)
	}

	return &moengageVendorPb.PortfolioChangeDetails{
		PortfolioDailyChangeAmount: dailyChangeAmount,
		PortfolioDailyChangePercentage: &stocksVendorPb.NumericMetric{
			DisplayValue: fmt.Sprintf("%.2f", dailyChangePercentage),
			Value:        dailyChangePercentage,
		},
		PortfolioSummaryDate:           previousDayTimestamp.AsTime().Format("02 January 2006"),
		DailyTopGainerName:             topGainerName,
		DailyTopGainerChangePercentage: topGainerPercentage,
		DailyBenchmarkChangePercentage: &stocksVendorPb.NumericMetric{
			DisplayValue: fmt.Sprintf("%.2f", dailyNavChangePercentage),
			Value:        dailyNavChangePercentage,
		},
	}, nil
}

func (p *PortfolioTrackerAttributeFetcher) getAnalysisVariableMap(ctx context.Context, actorId string) (map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable, error) {
	getAnalysisVariableResp, err := p.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: analyserVariables,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get analysis variable read only map for portfolio tracker %w", err)
	}
	return getAnalysisVariableResp.GetVariableEnumMap(), nil
}

func getNifty50Returns(analyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable) (float64, error) {
	nifty50ReturnsResp, ok := analyserVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO]
	if !ok {
		return 0, fmt.Errorf("no analysis variable found for analysis variable name %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO)
	}
	return nifty50ReturnsResp.GetMfPortfolioPerformanceDetails().GetNifty50PercentageReturns(), nil
}

func getPortfolioDailyChange(ctx context.Context, analyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable) (string, float64, error) {
	portfolioDailyChangeResp, ok := analyserVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		return "", 0, fmt.Errorf("no analysis variable found for analysis variable name %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}
	_, _, dailyChangeAmount, dailyChangePercentage := networth.GetMfAggregatedValues(ctx, portfolioDailyChangeResp.GetMfSecretsSchemeAnalytics())
	dailyChangeAmountString := fmt.Sprintf("%.2f", dailyChangeAmount)
	return dailyChangeAmountString, dailyChangePercentage, nil
}

func convertMoneyToFloat(value *moneyPb.Money) float64 {
	return float64(value.GetUnits()) + (float64(value.GetNanos()) / 1000000000.0)
}
