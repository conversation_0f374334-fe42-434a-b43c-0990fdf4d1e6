package server

import (
	"reflect"
	"testing"
)

func TestGetEnvListCorrespondingToRepo(t *testing.T) {
	tests := []struct {
		name        string
		envs        string
		currentRepo string
		expected    []string
	}{
		{
			name:        "all envs for gringott repo",
			envs:        "All",
			currentRepo: "gringott",
			expected:    []string{"qa", "staging"},
		},
		{
			name:        "all envs for gamma repo",
			envs:        "All",
			currentRepo: "sample-repo",
			expected:    []string{"qa", "staging", "uat"},
		},
		{
			name:        "single env",
			envs:        "sample-env",
			currentRepo: "sample-repo",
			expected:    []string{"sample-env"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getEnvListCorrespondingToRepo(tt.envs, tt.currentRepo)
			if !reflect.DeepEqual(result, tt.expected) {
				t.<PERSON>("getEnvListCorrespondingToRepo(%q, %q) = %v, expected %v",
					tt.envs, tt.currentRepo, result, tt.expected)
			}
		})
	}
}
