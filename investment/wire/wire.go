//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"context"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/redis/go-redis/v9"
	"github.com/slack-go/slack"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/google/wire"
	"gorm.io/gorm"
	gormv2 "gorm.io/gorm"

	pkgSqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	connectedAccPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/fittt"
	"github.com/epifi/gamma/api/investment/auth"
	dynamicUIElementPb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	fbPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	prereqPb "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/gamma/api/kyc"
	nudgePb "github.com/epifi/gamma/api/nudge"
	omsPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/pan"
	paymentinstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/rms/manager"
	rms "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	userPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	usStocksAccountPb "github.com/epifi/gamma/api/usstocks/account"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usStocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	mf "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	holdingsimporterPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/investment/aggregator"
	invAggConsumer "github.com/epifi/gamma/investment/aggregator/consumer"
	invEventProcessor "github.com/epifi/gamma/investment/aggregator/consumer/instrument_event"
	"github.com/epifi/gamma/investment/aggregator/rewardtriggerer"
	invAuth "github.com/epifi/gamma/investment/auth"
	invAuthDao "github.com/epifi/gamma/investment/auth/dao"
	"github.com/epifi/gamma/investment/config"
	investmentGenConf "github.com/epifi/gamma/investment/config/genconf"
	dynamicUIElement "github.com/epifi/gamma/investment/dynamic_ui_element"
	"github.com/epifi/gamma/investment/dynamic_ui_element/ab_experiment_evaluator/investment"
	"github.com/epifi/gamma/investment/dynamic_ui_element/ab_experiment_evaluator/usstocks"
	dynamicUIElementDao "github.com/epifi/gamma/investment/dynamic_ui_element/dao"
	eventProcessor "github.com/epifi/gamma/investment/event_processor"
	investmentUserJourneyDao "github.com/epifi/gamma/investment/event_processor/dao"
	"github.com/epifi/gamma/investment/event_processor/event_processor_plugins"
	"github.com/epifi/gamma/investment/event_processor/event_processor_plugins/jump"
	activityProcessorPb "github.com/epifi/gamma/investment/mutualfund/activity"
	"github.com/epifi/gamma/investment/mutualfund/catalog"
	"github.com/epifi/gamma/investment/mutualfund/catalog/catalog_aggregator"
	catalogFilePrcFactory "github.com/epifi/gamma/investment/mutualfund/catalog/catalog_file_processor"
	mfCatalogConsumer "github.com/epifi/gamma/investment/mutualfund/catalog/consumer"
	catDao "github.com/epifi/gamma/investment/mutualfund/catalog/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/catalog/nominee_update"
	catalogSch "github.com/epifi/gamma/investment/mutualfund/catalog/scheduler"
	mfDao "github.com/epifi/gamma/investment/mutualfund/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/developer"
	"github.com/epifi/gamma/investment/mutualfund/developer/processor"
	etaHandlerPb "github.com/epifi/gamma/investment/mutualfund/eta_handler"
	orderEtaProcessorPb "github.com/epifi/gamma/investment/mutualfund/eta_handler/order_eta_processor"
	mfExternal "github.com/epifi/gamma/investment/mutualfund/external"
	mfExternalConsumer "github.com/epifi/gamma/investment/mutualfund/external/consumer"
	mfExternalDao "github.com/epifi/gamma/investment/mutualfund/external/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/external/processor/factory"
	"github.com/epifi/gamma/investment/mutualfund/external/processor/impl"
	folioPbClient "github.com/epifi/gamma/investment/mutualfund/foliodetails"
	"github.com/epifi/gamma/investment/mutualfund/notifications"
	"github.com/epifi/gamma/investment/mutualfund/notifications/actor_segmentation"
	notiDao "github.com/epifi/gamma/investment/mutualfund/notifications/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/notifications/dynamic_elements_getter"
	omsNoti "github.com/epifi/gamma/investment/mutualfund/notifications/oms_order_update_processor"
	omsExec "github.com/epifi/gamma/investment/mutualfund/notifications/oms_order_update_processor/oms_order_update_executor"
	"github.com/epifi/gamma/investment/mutualfund/order"
	orderConsumer "github.com/epifi/gamma/investment/mutualfund/order/consumer"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/accessors"
	fgDao "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao"
	fgVP "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor"
	vpCams "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/cams"
	fng "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/file_name_generator"
	sng "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/file_name_generator/sequence_number_generator"
	vpKarvy "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/karvy"
	camsCfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/credit_mis_report_file_processor/cams"
	karvyCfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/credit_mis_report_file_processor/karvy"
	camsEfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/elog_file_processor/cams"
	camsFfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/fatca_file_processor/cams"
	karvyFfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/fatca_file_processor/karvy"
	camsNfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/nft_file_for_folio_processor/cams"
	camsOfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/order_feed_file_processor/cams"
	karvyOfp "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/order_feed_file_processor/karvy"
	ltp "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor"
	wec "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor/withdrawable_entity_calculator"
	mfOperations "github.com/epifi/gamma/investment/mutualfund/order/operations"
	orc "github.com/epifi/gamma/investment/mutualfund/order/orchestrator/consumer"
	"github.com/epifi/gamma/investment/mutualfund/order/order_type_processor"
	prh "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler"
	prhConsumer "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/consumer"
	prhVgAccessor "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/consumer/vendor_gateway_accessor"
	prhDao "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/dao"
	prhProcessor "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/processor"
	rf "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed"
	rfConsumer "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/consumer"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/rta_order_data"
	rvVP "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor"
	camsRf "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/cams"
	camsFolioReconcileFile "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/folio_reconcilation_file_processor/cams"
	karvyRf "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/karvy"
	camsPayFile "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/payment_details_file_processor/cams"
	karvyPayFile "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/payment_details_file_processor/karvy"
	camsRejectedFile "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/rejected_orders_file_processor/cams"
	camsSchemaMasterFile "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/schema_master_file_processor/cams"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/cams"
	camsEop "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/cams/wbr2/external_order_processor"
	camsIop "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/cams/wbr2/internal_order_processor"
	karvyTxnFeedFile "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/karvy"
	karvyEop "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/karvy/wbr201/external_order_processor"
	karvyIop "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/karvy/wbr201/internal_order_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/scheduler"
	sipLedgerUtil "github.com/epifi/gamma/investment/mutualfund/order/sip_ledger_util"
	updOrder "github.com/epifi/gamma/investment/mutualfund/order/update_order"
	"github.com/epifi/gamma/investment/mutualfund/order/validator"
	vos "github.com/epifi/gamma/investment/mutualfund/order/vendor_order_sender"
	vosConsumer "github.com/epifi/gamma/investment/mutualfund/order/vendor_order_sender/consumer"
	paymentHandler "github.com/epifi/gamma/investment/mutualfund/payment_handler"
	phConsumer "github.com/epifi/gamma/investment/mutualfund/payment_handler/consumer"
	phConsumerPu "github.com/epifi/gamma/investment/mutualfund/payment_handler/consumer/payment_updater"
	payDao "github.com/epifi/gamma/investment/mutualfund/payment_handler/dao"
	paymentExecutor "github.com/epifi/gamma/investment/mutualfund/payment_handler/payment_executor"
	paySch "github.com/epifi/gamma/investment/mutualfund/payment_handler/scheduler"
	"github.com/epifi/gamma/investment/mutualfund/reconciliation"
	"github.com/epifi/gamma/investment/mutualfund/reconciliation/rta_processor"
	statementPb "github.com/epifi/gamma/investment/mutualfund/statement"
	mfStatementConsumer "github.com/epifi/gamma/investment/mutualfund/statement/consumer"
	nonFinEventsConsumer "github.com/epifi/gamma/investment/non_financial_events/consumer"
	nonFinEventsProcessor "github.com/epifi/gamma/investment/non_financial_events/consumer/processor"
	"github.com/epifi/gamma/investment/profile"
	profileDao "github.com/epifi/gamma/investment/profile/dao/profile_impl"
	surveyDao "github.com/epifi/gamma/investment/profile/dao/survey_impl"
	investmentWatsonClient "github.com/epifi/gamma/investment/watson"
	wtypes "github.com/epifi/gamma/investment/wire/types"
	pkgFittt "github.com/epifi/gamma/pkg/fittt"
	"github.com/epifi/gamma/pkg/zinc/search"
	workerConf "github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/ocr"
)

func InitialiseInvestmentOrchestratorConsumer(db types.EpifiWealthCRDB, fileGeneratorClient fgPb.FileGeneratorClient, processFileGenPublisher wtypes.ProcessFileGenPublisher, processFileGenSuccessPublisher wtypes.ProcessFileGenSuccessPublisher,
	preReqClient prereqPb.PrerequisiteHandlerClient, config *investmentGenConf.Config, omsClient omsPb.OrderServiceClient, catalogueManagerClient catalogPb.CatalogManagerClient,
	actorClient actorPb.ActorClient, commsClient wtypes.InvestmentCommsClientWithInterceptors, usersClient userPb.UsersClient, sellOrderUpdateQueuePublisher updOrder.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher updOrder.InvestmentInstrumentEventPublisher) *orc.Service {
	wire.Build(wtypes.CommsClientProvider, once.NewDoOnce, orc.NewService, GormProvider,
		mfDao.AmcWireSet, mfDao.BatchOrderProcessingDetailsWireSet, mfDao.BatchOrderProcessingStepDetailsWireSet, orc.NewInvestmentOrcPublishers,
		mfDao.OrderWireSet, mfDao.MutualfundWireSet, mfDao.FolioLedgerWireSet, updOrder.NewOrderStatusCountObserver, updOrder.NewLoggerObserver, updOrder.NewOrderStuckObserver,
		updOrder.NewSellOrderSettlementObserver, updOrder.NewOrderUpdateEventObserver,
		updOrder.NewOrderStatusNotifier, updOrder.NewOrderFailedNotificationObserver, mfDao.OrderStatusUpdateWireSet, idgen.NewClock, idgen.WireSet, storagev2.IdempotentTxnExecutorWireSet, mfDao.SIPLedgerWireSet, sipLedgerUtil.SIPLedgerHelperWireSet)
	return &orc.Service{}
}

func uploadCreditMISToVendorPublisherProvider(ctx context.Context, gconf *investmentGenConf.Config, sqsClient *sqs.Client) (order.UploadCreditMISToVendorPublisher, error) {
	// Don't initialize this publisher for prod env since it is used only in non-prod env.
	if cfg.IsProdEnv(gconf.Application().Environment()) {
		return nil, nil
	}
	uploadCreditMISToVendorPublisher, err := pkgSqs.NewPublisherWithConfig(ctx, gconf.UploadCreditMISToVendorPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		return nil, err
	}
	return uploadCreditMISToVendorPublisher, nil
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitialiseOrderManagerSvc(ctx context.Context, db types.EpifiWealthCRDB, sqsClient *sqs.Client, vgmfClient mf.MutualFundClient, fileGeneratorClient fgPb.FileGeneratorClient,
	publisher wtypes.VendorOrderFileStatusPublisher, camsS3Client wtypes.CamsS3Client, karvyS3Client wtypes.KarvyS3Client, paymentHandlerClient phPb.PaymentHandlerClient, catalogueManagerClient catalogPb.CatalogManagerClient, genConf *investmentGenConf.Config,
	savingsClient savings.SavingsClient, actorClient actorPb.ActorClient, commsClient wtypes.InvestmentCommsClientWithInterceptors, kycClient kyc.KycClient, client userPb.UsersClient, authClient authPb.AuthClient,
	wealthOBClient wob.WealthOnboardingClient, fitttClient fittt.FitttClient,
	deferredNotificationPublisher wtypes.DeferredNotificationDelayPublisher, orderDelayedNotificationPublisher wtypes.OrderDelayedNotificationDelayPublisher,
	sellOrderUpdateQueuePublisher updOrder.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher updOrder.InvestmentInstrumentEventPublisher, investmentAuthClient auth.AuthClient, panClient pan.PanClient, bcClient bankcust.BankCustomerServiceClient,
	orderETADelayPublisher wtypes.OrderETADelayPublisher, ruleManagerClient rms.RuleManagerClient,
) (*order.Service, error) {
	wire.Build(GormProvider, mfDao.OrderWireSet, mfDao.FolioLedgerWireSet, mfDao.MutualfundWireSet, mfDao.AmcWireSet, mfDao.OrderStatusUpdateWireSet, mfDao.OrderRejectionInfoWireSet,
		wtypes.CommsClientProvider,
		uploadCreditMISToVendorPublisherProvider,
		idgen.NewClock, idgen.WireSet, mfDao.FileStateWireSet, mfDao.OrderConfirmationInfoWireSet, mfDao.SIPLedgerWireSet,
		once.NewDoOnce,
		wec.NewHardLockInCalculator, wec.NewNoLockInCalculator, ltp.NewHardLockinProcessor, ltp.NewNoLockinProcessor, ltp.NewSoftLockinProcessor, ltp.NewLockInProcessorFactory,
		vos.NewVendorOrderFileSender,
		vos.NewVendorOrderSenderFactory,
		updOrder.NewOrderStatusCountObserver, updOrder.NewLoggerObserver, updOrder.NewOrderStuckObserver,
		updOrder.NewSellOrderSettlementObserver,
		updOrder.NewOrderUpdateEventObserver,
		updOrder.NewOrderStatusNotifier,
		updOrder.NewOrderFailedNotificationObserver,
		storagev2.IdempotentTxnExecutorWireSet,
		validator.NewFundEligibilityValidator,
		validator.NewPurchaseConstraintsValidator,
		validator.NewNomineeDeclarationValidator,
		validator.NewPanAadhaarLinkValidator,
		order_type_processor.NewBuyOrderProcessor,
		order_type_processor.NewSellOrderProcessor,
		order.NewService,
		sipLedgerUtil.SIPLedgerHelperWireSet,
	)
	return &order.Service{}, nil
}

// config: {"s3Client": "Application().Operations().S3Bucket", "camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitialiseMFOrderOperationsSvc(db types.EpifiWealthCRDB, s3Client wtypes.MFOpsS3Client, catalogueManagerClient catalogPb.CatalogManagerClient,
	wealthOnboardingClient wob.WealthOnboardingClient, paymentHandlerClient phPb.PaymentHandlerClient, userClient userPb.UsersClient,
	actorClient actorPb.ActorClient, savingsClient savings.SavingsClient, orderManagerClient orderPb.OrderManagerClient,
	fileGeneratorClient fgPb.FileGeneratorClient, config *investmentGenConf.Config,
	ticketClient ticket.TicketClient, camsS3Client wtypes.CamsS3Client, karvyS3Client wtypes.KarvyS3Client, paymentClient paymentPb.PaymentClient, piClient paymentinstrumentPb.PiClient) *mfOperations.Service {
	wire.Build(GormProvider, mfDao.FileStateWireSet, mfDao.OrderWireSet, prhDao.PrerequisiteStatusWireSet, idgen.NewClock, idgen.WireSet, mfOperations.NewService, fgDao.EntityFileMapperWireSet, fgDao.FileGenerationAttemptWireSet)
	return &mfOperations.Service{}
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitializeReverseFeedConsumerService(
	camsS3Client rvVP.CamsS3Client,
	orderManagerClient orderPb.OrderManagerClient,
	karvyS3Client rvVP.KarvyS3Client,
	db types.EpifiWealthCRDB,
	config *investmentGenConf.Config,
	catalogClient catalogPb.CatalogManagerClient,
) *rfConsumer.Service {
	wire.Build(
		GormProvider,
		cams.NewWBR2FeedFileProcessor,
		camsIop.NewNormalBuyAndSellOrderProcessor,
		camsIop.NewSwitchOrderProcessor,
		camsEop.NewExternalOrderProcessor,
		camsEop.NewSwitchOrderProcessor,
		karvyIop.NewNormalBuyAndSellOrderProcessor,
		karvyEop.NewExternalOrderProcessor,
		karvyIop.NewSwitchOrderProcessor,
		karvyEop.NewSwitchOrderProcessor,
		camsSchemaMasterFile.NewSchemaMasterFileProcessor,
		camsPayFile.NewWBR52FileProcessor,
		camsRejectedFile.NewWBR46FileProcessor,
		camsRf.NewCamsVendorProcessor,
		rf.NewVendorProcessorFactory,
		rta_order_data.NewRTAOrderDataProcessor,
		rfConsumer.NewService,
		karvyRf.NewKarvyVendorProcessor,
		karvyTxnFeedFile.NewWBR201FeedFileProcessor,
		karvyPayFile.NewWBR245FileProcessor,
		mfDao.OrderWireSet,
		idgen.NewClock,
		idgen.WireSet,
		mfDao.MutualfundWireSet,
		mfDao.RTAOrderDataDaoCrdbDaoWireSet,
		mfDao.FolioLedgerWireSet,
	)
	return &rfConsumer.Service{}
}

// config: {"s3Client": "Application().FileGenerator().CamsS3Bucket"}
func InitializeReverseFeedService(catalogueManagerClient catalogPb.CatalogManagerClient, s3Client wtypes.CamsS3Client, wob wob.WealthOnboardingClient, fileGeneratorClient fgPb.FileGeneratorClient, mfClient mf.MutualFundClient) *rf.Service {
	wire.Build(camsFolioReconcileFile.NewWBR9FeedFileProcessor, rf.NewService)
	return &rf.Service{}
}

func InitializePaymentUpdateConsumerSvc(db types.EpifiWealthCRDB, orderManagerClient orderPb.OrderManagerClient, omsClient omsPb.OrderServiceClient, recurringPaymentClient recurringPay.RecurringPaymentServiceClient,
	cfg *investmentGenConf.Config) *phConsumer.Service {
	wire.Build(
		GormProvider,
		payDao.PaymentsWireSet, phConsumer.NewPaymentUpdateConsumerFactory,
		phConsumerPu.NewOneTimeMFOrderPaymentUpdater,
		paymentHandler.NewPaymentExecutorFactory,
		paymentExecutor.NewSIPaymentExecutor,
		paymentExecutor.NewP2PTransferExecutor,
		phConsumerPu.NewAutoInvestMFOrderPaymentUpdater,
		phConsumer.NewService)
	return &phConsumer.Service{}
}

func S3UrlDownloaderProvider() accessors.S3UrlDownloader {
	return accessors.FetchFileFromS3URL
}

func httpClientProvider() *http.Client {
	return &http.Client{}
}

func EmptyAWSConfProvider() []*aws.Config {
	return nil
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitialiseFileGeneratorSvc(db types.EpifiWealthCRDB, camsS3Client fgVP.CamsS3Client, karvyS3Client fgVP.KarvyS3Client, genConf *investmentGenConf.Config,
	orderManagerClient orderPb.OrderManagerClient, wealthOnboardingClient wob.WealthOnboardingClient,
	catalogueManagerClient catalogPb.CatalogManagerClient, paymentHandlerClient phPb.PaymentHandlerClient,
	client savings.SavingsClient,
	authClient auth.AuthClient, docsClient docs.DocsClient, awsConf aws.Config,
	userClient userPb.UsersClient, ruleManagerClient rms.RuleManagerClient, accountBalanceClient accountBalancePb.BalanceClient) *filegenerator.Service {
	wire.Build(GormProvider, lambda.LambdaClientWireset,
		httpClientProvider, S3UrlDownloaderProvider, accessors.NewDocsAccessor,
		fgDao.EntityFileMapperWireSet, accessors.NewWealthOnBoardingAccessor, accessors.NewNomineeAccessor, accessors.NewOrderManagerAccessor,
		accessors.NewCatalogueManagerAccessor, accessors.NewPaymentHandlerAccessor, accessors.NewAuthAccessor, accessors.NewSavingsAccessor, accessors.NewRMSAccessor, fgDao.FileGenerationAttemptWireSet,
		fgDao.FileSequenceGeneratorWireSet, filegenerator.NewVendorProcessorFactory, mfDao.AmcWireSet, mfDao.SIPLedgerWireSet, accessors.NewAMCInfoAccessor, accessors.NewSipLedgerAccessor,
		vpCams.NewCamsVendorProcessor, camsCfp.NewCamsCreditMISReportFileProcessor, camsFfp.NewCamsFATCAFileProcessor, camsNfp.NewNFTFileForFolioProcessor,
		camsEfp.NewCamsElogFileProcessor, camsOfp.NewCamsOrderFeedFileProcessor, fng.NewCamsFileNameGenerator,
		sng.NewIntraDaySequenceNumberGenerator, idgen.NewClock, idgen.WireSet, filegenerator.NewService,
		vpKarvy.NewKarvyVendorProcessor, karvyCfp.NewKarvyCreditMISReportFileProcessor, karvyFfp.NewKarvyFATCAFileProcessor, karvyOfp.NewKarvyOrderFeedFileProcessor, fng.NewKarvyFileNameGenerator,
		storagev2.IdempotentTxnExecutorWireSet)

	return &filegenerator.Service{}
}

func InitialisePrerequisiteHandlerService(db types.EpifiWealthCRDB, wobClient wob.WealthOnboardingClient, fileGenClient fgPb.FileGeneratorClient,
	elogPreRequisiteStatusPublisher wtypes.ElogPreRequisiteStatusPublisher, fatcaPreRequisiteStatusPublisher wtypes.FATCAPreRequisiteStatusPublisher,
	retryPublisher wtypes.PreRequisiteHandlerRetryPublisher) *prh.Service {
	wire.Build(GormProvider, prhDao.PrerequisiteStatusWireSet, prhDao.ActorPrerequisiteRequestWireSet, prhProcessor.NewPreRequisiteProcessorFactory, prhProcessor.NewKRAPreRequisiteProcessor,
		storagev2.IdempotentTxnExecutorWireSet,
		prhProcessor.NewElogPreRequisiteProcessor,
		prhProcessor.NewFATCAPreRequisiteProcessor,
		prh.NewService)

	return &prh.Service{}
}

func InitialisePrerequisiteConsumerSvc(db types.EpifiWealthCRDB, mfClient mf.MutualFundClient, orderManagerClient orderPb.OrderManagerClient, wobClient wob.WealthOnboardingClient, fileGenClient fgPb.FileGeneratorClient,
	elogPreRequisiteStatusPublisher wtypes.ElogPreRequisiteStatusPublisher, fatcaPreRequisiteStatusPublisher wtypes.FATCAPreRequisiteStatusPublisher,
	retryPublisher wtypes.PreRequisiteHandlerRetryPublisher) *prhConsumer.Service {
	wire.Build(GormProvider, prhDao.PrerequisiteStatusWireSet, prhDao.ActorPrerequisiteRequestWireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		prhVgAccessor.NewElogAccessor,
		prhVgAccessor.NewFatcaAccessor,
		prhVgAccessor.NewVendorGateWayAccessorFactory,
		prhProcessor.NewPreRequisiteProcessorFactory, prhProcessor.NewKRAPreRequisiteProcessor,
		prhProcessor.NewElogPreRequisiteProcessor,
		prhProcessor.NewFATCAPreRequisiteProcessor,
		prh.NewService,
		prhConsumer.NewService)
	return &prhConsumer.Service{}
}

// config: {"mfCatalogS3Client": "MutualFundCatalogS3Conf().BucketName"}
func InitialiseCatalogManagerSvc(db types.EpifiWealthCRDB, mfCatalogS3Client wtypes.MFCatalogS3Client, genConf *investmentGenConf.Config,
	userGroupClient usergroupPb.GroupClient, userClient userPb.UsersClient, actorClient actorPb.ActorClient, searchClient search.SearchClient,
	recentlyVisitedPublisher wtypes.RecentlyVisitedPublisher, deferredNotificationPublisher wtypes.DeferredNotificationDelayPublisher, segmentationServiceClient segment.SegmentationServiceClient,
	vgMfClient mf.MutualFundClient, wealthOnboardingClient wob.WealthOnboardingClient, mfRedis wtypes.MfCatalogRedisStore) *catalog.Service {
	wire.Build(GormProvider, mfDao.MutualfundWireSet, mfDao.AmcWireSet, mfDao.FolioLedgerWireSet, idgen.NewClock, idgen.WireSet, mfDao.OrderWireSet, mfDao.OrderStatusUpdateWireSet,
		mfDao.MutualFundOrderWireSet, mfDao.CollectionMutualFundWireSet, mfDao.WatchlistMutualFundWireSet, mfDao.RecentMutualFundInfoWireSet, mfDao.NomineeStatusWireSet,
		catDao.CollectionWireSet, catDao.CollectionFundMappingWireSet, catDao.WatchListWireSet, catDao.WatchListFundMappingWireSet, catDao.RecentMutualFundWireSet,
		mfDao.MutualFundCategoryAverageWireSet, catDao.FundFilterGroupWireSet, catDao.FundFilterWireSet, catDao.CollectionFilterMappingWireSet,
		catDao.FiltersWithGroupWireSet, storagev2.IdempotentTxnExecutorWireSet, catalog.NewService,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		nominee_update.NewKarvyNomineeUpdater,
		nominee_update.NewCamsNomineeUpdater,
		nominee_update.NewNomineeUpdaterFactory,
		NewMfCatalogRedisClient,
		wec.NewHardLockInCalculator, wec.NewNoLockInCalculator, ltp.NewHardLockinProcessor, ltp.NewNoLockinProcessor, ltp.NewSoftLockinProcessor, ltp.NewLockInProcessorFactory,
		mfDao.MfHistoricalNavDaoWireSet,
	)
	return &catalog.Service{}
}
func NewMfCatalogRedisClient(mfCacheRedisStore wtypes.MfCatalogRedisStore) *redis.Client {
	return mfCacheRedisStore
}

func DynamicUIElementRedisStoreRedisClientProvider(cl wtypes.DynamicUIElementCacheRedisStore) *redis.Client {
	return cl
}

func InitialiseVendorOrderStatusConsumerSvc(db types.EpifiWealthCRDB, vgmfClient mf.MutualFundClient, genConf *investmentGenConf.Config,
	commsClient wtypes.InvestmentCommsClientWithInterceptors, actorClient actorPb.ActorClient, catalogCLient catalogPb.CatalogManagerClient, phClient phPb.PaymentHandlerClient, authClient authPb.AuthClient,
	sellOrderUpdateQueuePublisher updOrder.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher updOrder.InvestmentInstrumentEventPublisher, userClient userPb.UsersClient) *vosConsumer.Service {
	wire.Build(GormProvider, mfDao.FileStateWireSet, mfDao.OrderWireSet, mfDao.OrderStatusUpdateWireSet, mfDao.MutualfundWireSet, mfDao.FolioLedgerWireSet, idgen.NewClock, idgen.WireSet,
		wtypes.CommsClientProvider, storagev2.IdempotentTxnExecutorWireSet, updOrder.NewOrderStatusCountObserver, updOrder.NewLoggerObserver, updOrder.NewOrderStuckObserver,
		updOrder.NewSellOrderSettlementObserver, updOrder.NewOrderUpdateEventObserver, once.NewDoOnce,
		updOrder.NewOrderStatusNotifier, updOrder.NewOrderFailedNotificationObserver, vosConsumer.NewKarvyOrderUpdater, vosConsumer.NewCamsOrderUpdater, vosConsumer.NewOrderUpdaterFactory,
		vosConsumer.NewService)
	return &vosConsumer.Service{}
}

func InitialiseSchedulerService(db types.EpifiWealthCRDB, fgClient fbPb.FileGeneratorClient, catalogCLient catalogPb.CatalogManagerClient, phClient phPb.PaymentHandlerClient,
	orchestratorPublisher wtypes.ProcessMutualFundOrderPublisher, commsClient wtypes.InvestmentCommsClientWithInterceptors, userClient userPb.UsersClient,
	genConf *investmentGenConf.Config, mutualFundClient mf.MutualFundClient, wealthOnBoardingClient wob.WealthOnboardingClient, actorClient actorPb.ActorClient,
	sellOrderUpdateQueuePublisher updOrder.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher updOrder.InvestmentInstrumentEventPublisher) *scheduler.Service {
	wire.Build(
		wtypes.CommsClientProvider,
		once.NewDoOnce,
		GormProvider,
		mfDao.OrderWireSet,
		mfDao.OrderStatusUpdateWireSet,
		mfDao.BatchOrderProcessingDetailsWireSet,
		mfDao.FileStateWireSet,
		mfDao.AmcWireSet,
		mfDao.MutualfundWireSet,
		idgen.NewClock,
		idgen.WireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		updOrder.NewOrderStatusCountObserver,
		updOrder.NewLoggerObserver,
		updOrder.NewOrderStuckObserver,
		updOrder.NewSellOrderSettlementObserver,
		updOrder.NewOrderUpdateEventObserver,
		updOrder.NewOrderStatusNotifier,
		updOrder.NewOrderFailedNotificationObserver,
		scheduler.NewService)
	return &scheduler.Service{}
}

func InitialisePaymentHandlerSvc(db types.EpifiWealthCRDB, orderManagerClient orderPb.OrderManagerClient, recurringPaymentClient recurringPay.RecurringPaymentServiceClient,
	omsClient omsPb.OrderServiceClient, genConf *investmentGenConf.Config) *paymentHandler.Service {
	wire.Build(GormProvider, paymentHandler.NewPaymentExecutorFactory, paymentExecutor.NewSIPaymentExecutor, paymentExecutor.NewP2PTransferExecutor, payDao.PaymentsWireSet,
		mfDao.MutualfundWireSet, idgen.NewClock, idgen.WireSet,
		mfDao.FolioLedgerWireSet,
		mfDao.AmcWireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		paymentHandler.NewService)
	return &paymentHandler.Service{}
}

// config: {"mfCatalogS3Client": "MutualFundCatalogS3Conf().BucketName"}
func InitializeMutualFundCatalogConsumerSvc(conf *config.Config, db types.EpifiWealthCRDB, mfCatalogS3Client wtypes.MFCatalogS3Client,
	vgMfClient mf.MutualFundClient, genConf *investmentGenConf.Config, redisClient types.InvestmentRedisStore) *mfCatalogConsumer.Service {
	wire.Build(
		GormProvider,
		mfDao.MutualfundWireSet,
		mfDao.MutualFundCategoryAverageWireSet,
		idgen.WireSet,
		catalogFilePrcFactory.NewCatalogFileProcessorFactory,
		catDao.RecentMutualFundWireSet,
		mfDao.MfHistoricalNavDaoWireSet,
		mfCatalogConsumer.NewService,
		investmentSlackAlertClientProvider,
		types.InvestmentRedisStoreRedisClientProvider,
		lock.RedisV9LockManagerWireSet,
	)
	return &mfCatalogConsumer.Service{}
}

func InitializeMutualFundDBStatesSvc(db types.EpifiWealthCRDB, cfg *investmentGenConf.Config, paymentHandlerClient phPb.PaymentHandlerClient,
	dynamicUIElementRedisStore wtypes.DynamicUIElementCacheRedisStore) *developer.MutualFundDbStatesService {
	wire.Build(
		GormProvider,
		idgen.NewClock, idgen.WireSet,
		mfDao.OrderWireSet,
		mfDao.FolioLedgerWireSet,
		fgDao.EntityFileMapperWireSet,
		fgDao.FileGenerationAttemptWireSet,
		prhDao.PrerequisiteStatusWireSet,
		payDao.PaymentsWireSet,
		mfDao.FileStateWireSet,
		mfDao.BatchOrderProcessingStepDetailsWireSet,
		mfDao.BatchOrderProcessingDetailsWireSet,
		mfDao.MutualfundWireSet,
		mfDao.AmcWireSet,
		catDao.CollectionWireSet,
		mfDao.CollectionMutualFundWireSet,
		catDao.CollectionFilterMappingWireSet,
		mfDao.OrderStatusUpdateWireSet,
		catDao.FundFilterWireSet,
		catDao.FiltersWithGroupWireSet,
		mfDao.SIPLedgerWireSet,
		mfExternalDao.MfExternalHoldingsImportReqWireSet,
		mfExternalDao.MfSingleOtpCasImportRequestWireSet,
		mfExternalDao.MfExternalNftRequestWireSet,
		dynamicUIElementDao.DynamicUIElementWireSet,
		DynamicUIElementEvaluatorCacheConfigProvider,
		DynamicUIElementCacheConfigProvider,
		DynamicUIElementRedisStoreRedisClientProvider,
		cache.RedisStorageWireSet,
		processor.NewMFOrderProcessor,
		processor.NewMFEntityFileMapperProcessor,
		processor.NewMFActorPrerequisiteProcessor,
		processor.NewMFFolioLedgerForActorProcessor,
		processor.NewMFFileGenerationAttemptsProcessor,
		processor.NewMFFileStateProcessor,
		processor.NewMFBatchOrderProcessingStepDetailsProcessor,
		processor.NewMFBatchOrderProcessingDetailsProcessor,
		processor.NewMFPaymentProcessor,
		processor.NewMutualFundsProcessor,
		processor.NewAMCInfoProcessor,
		processor.NewOrderSummaryProcessor,
		processor.NewMfCollectionsProcessor,
		processor.NewMfCollectionFundMappingProcessor,
		processor.NewMfCollectionFiltersMappingProcessor,
		processor.NewOrderTimelineProcessor,
		processor.NewMfFiltersProcessor,
		processor.NewMfFiltersWithGroupProcessor,
		processor.NewPastOrdersForActorProcessor,
		processor.NewMFSipLedgerProcessor,
		processor.NewMFExternalHoldingsImportReqProcessor,
		processor.NewDynamicUIElementProcessor,
		processor.NewDynamicUIElementEvaluatorConfigProcessor,
		processor.NewMfSingleOtpCasImportReqProcessor,
		processor.NewMfExternalNftReqProcessor,
		developer.NewDevFactory,
		developer.NewMutualFundDbStatesService)
	return &developer.MutualFundDbStatesService{}
}

func DynamicUIElementEvaluatorCacheConfigProvider(cfg *investmentGenConf.Config) *investmentGenConf.DynamicUIElementEvaluatorCacheConfig {
	return cfg.DynamicUIElementEvaluatorCacheConfig()
}

func DynamicUIElementCacheConfigProvider(cfg *investmentGenConf.Config) *investmentGenConf.DynamicUIElementCacheConfig {
	return cfg.DynamicUIElementCacheConfig()
}

func InitialiseAuthSvc(db types.EpifiWealthCRDB) *invAuth.Service {
	wire.Build(GormProvider, invAuthDao.AuthWireSet, idgen.NewClock, idgen.WireSet, invAuth.NewService)
	return &invAuth.Service{}
}

func InitializeCatalogSchedulerSvc(db types.EpifiWealthCRDB) *catalogSch.Service {
	wire.Build(
		GormProvider,
		mfDao.MutualfundWireSet,
		mfDao.MutualFundCategoryAverageWireSet,
		idgen.NewClock,
		idgen.WireSet,
		catalog_aggregator.NewCategoryAverageAggregator,
		catalog_aggregator.NewPercentileRankAggregator,
		wire.NewSet(catalog_aggregator.NewCatalogAggregatorFactory, wire.Bind(new(catalog_aggregator.ICatalogAggregatorFactory), new(*catalog_aggregator.CatalogAggregatorFactory))),
		catalogSch.NewService,
	)
	return &catalogSch.Service{}
}

func InitializeMutualFundNotificationsSvc(
	cfg *investmentGenConf.Config,
	db types.EpifiWealthCRDB,
	rmsClient manager.RuleManagerClient,
	commsClient wtypes.InvestmentCommsClientWithInterceptors,
	phClient phPb.PaymentHandlerClient,
	authClient authPb.AuthClient,
	actorClient actorPb.ActorClient,
	orderDelayedNotificationPublisher notifications.OrderDelayedNotificationDelayPublisher,
	deferredNotificationQueuePublisher notifications.DeferredNotificationDelayPublisher,
	catalogClient catalogPb.CatalogManagerClient,
	wealthOnb wob.WealthOnboardingClient,
	userClient userPb.UsersClient,
	userGroupClient usergroupPb.GroupClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	dynamicUIElementServiceClient dynamicUIElementPb.DynamicUIElementServiceClient,
) *notifications.Service {
	wire.Build(
		wtypes.CommsClientProvider,
		pkgFittt.NewStandingInstructionFitttAggregator,
		once.NewDoOnce,
		GormProvider,
		idgen.NewClock,
		idgen.WireSet,
		mfDao.MutualfundWireSet,
		mfDao.OrderWireSet,
		mfDao.FolioLedgerWireSet,
		GetInvestLandingBannerABEvaluatorProvider,
		wire.NewSet(actor_segmentation.NewActorSegmentationHelper, wire.Bind(new(actor_segmentation.IActorSegmentationHelper), new(*actor_segmentation.ActorSegmentationHelper))),
		dynamic_elements_getter.NewInvestmentDigestDynamicElementsGetter,
		ABFeatureReleaseConfigProvider,
		dynamic_elements_getter.NewInvestmentLandingDynamicElementsGetter,
		dynamic_elements_getter.NewHomePromotionDynamicElementsGetter,
		dynamic_elements_getter.NewHomeTopBarAlertDynamicElementsGetter,
		wire.NewSet(notifications.NewDynamicElementsGetterFactory, wire.Bind(new(notifications.IDynamicElementsGetterFactory), new(*notifications.DynamicElementsGetterFactory))),
		catDao.WatchListFundMappingWireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		notiDao.ActorNotificationDaoWireSet,
		omsNoti.NewProcessOmsOrderUpdateFactory,
		omsExec.NewAddFundsNotificationProcessor,
		omsExec.NewDebitPushNotificationProcessor,
		omsExec.NewPaymentFailedPushNotificationProcessor,
		notifications.NewService,
	)
	return &notifications.Service{}
}

func InitializePaymentHandlerSchedulerSvc(db types.EpifiWealthCRDB, orderManagerClient orderPb.OrderManagerClient, recurringPaymentClient recurringPay.RecurringPaymentServiceClient,
	omsClient omsPb.OrderServiceClient, genConf *investmentGenConf.Config) *paySch.Service {
	wire.Build(GormProvider, paymentHandler.NewPaymentExecutorFactory, paymentExecutor.NewSIPaymentExecutor, paymentExecutor.NewP2PTransferExecutor, payDao.PaymentsWireSet, paySch.NewService)
	return &paySch.Service{}
}

func InitializeSettlementOrderUpdatesConsumerService(db types.EpifiWealthCRDB, orderManagerClient orderPb.OrderManagerClient) *orderConsumer.Service {
	wire.Build(GormProvider, idgen.NewClock,
		idgen.WireSet,
		mfDao.OrderWireSet, mfDao.OrderStatusUpdateWireSet, orderConsumer.NewService)
	return &orderConsumer.Service{}
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitializeReconciliationSvc(db types.EpifiWealthCRDB, orderManagerClient orderPb.OrderManagerClient, catalogueManagerClient catalogPb.CatalogManagerClient, camsS3Client wtypes.CamsS3Client, karvyS3Client wtypes.KarvyS3Client,
	cfg *investmentGenConf.Config) *reconciliation.Service {
	wire.Build(GormProvider, idgen.NewClock, idgen.WireSet, rta_processor.NewKarvyRTAProcessor, rta_processor.NewCamsRTAProcessor, rta_processor.NewRTAProcessorFactory, mfDao.OrderWireSet, reconciliation.NewService)
	return &reconciliation.Service{}
}

func InitializeInvestmentAggregatorSvc(catalogueManagerClient catalogPb.CatalogManagerClient, depositClient depositPb.DepositClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, redisClient types.InvestmentRedisStore,
	usStocksPortFolioManagerClient portfolio.PortfolioManagerClient, investmentEventQueuePublisher wtypes.InvestmentEventPublisher) *aggregator.Service {
	wire.Build(cache.RedisStorageWireSet, types.InvestmentRedisStoreRedisClientProvider,
		rewardtriggerer.NewDepositRewardTriggerer,
		rewardtriggerer.NewRewardTriggererFactory,
		rewardtriggerer.NewUSStocksRewardTriggerer,
		aggregator.NewService)
	return &aggregator.Service{}
}

func InitializeInvestmentAggregatorConsumerSvc(investmentEventQueuePublisher wtypes.InvestmentEventPublisher,
	orderManagerClient orderPb.OrderManagerClient,
	paymentInstrumentClient paymentinstrumentPb.PiClient, savingsClient savings.SavingsClient,
	rmsClient rms.RuleManagerClient, depositClient depositPb.DepositClient, usStockOrderManagerClient usStocksOrderPb.OrderManagerClient) *invAggConsumer.Service {
	wire.Build(invEventProcessor.NewMFEventProcessor, invEventProcessor.NewP2PEventProcessor, invEventProcessor.NewSDEventProcessor,
		invEventProcessor.NewFDEventProcessor, invEventProcessor.NewUSStocksEventProcessor,
		invAggConsumer.NewService)
	return &invAggConsumer.Service{}
}

func InitializeMFExternalOrderService(db types.EpifiWealthCRDB,
	cfg *investmentGenConf.Config, holdingsImportedClient holdingsimporterPb.HoldingImporterClient, wealthOnboardingClient wob.WealthOnboardingClient,
	client mf.MutualFundClient) *mfExternal.Service {
	wire.Build(
		datetime.WireDefaultTimeSet,
		storagev2.IdempotentTxnExecutorWireSet,
		mfExternal.NewService,
		mfExternalDao.MutualFundExternalOrderWireSet,
		mfExternalDao.MfExternalHoldingsImportReqWireSet,
		mfExternalDao.WireMutualFundExternalHoldingsSummaryDaoCRDBSet,
		mfExternalDao.MfExternalNftRequestWireSet,
		mfExternalDao.MfSingleOtpCasImportRequestWireSet,
		GormProvider,
		idgen.NewClock,
		idgen.WireSet,
		idgen.UuidGeneratorWireSet,
		factory.WireNftProcessorFactorySet,
		impl.NewEmailUpdateProcessor,
		impl.NewMobileUpdateProcessor,
	)
	return &mfExternal.Service{}
}

func InitializeMFExternalConsumerService(db types.EpifiWealthCRDB, cfg *investmentGenConf.Config, rmsClient rms.RuleManagerClient, orderManagerClient orderPb.OrderManagerClient,
	investmentEventQueuePublisher wtypes.InvestmentEventPublisher, holdingImporterClient holdingsimporterPb.HoldingImporterClient, userClient userPb.UsersClient, nameCheckClient ncPb.UNNameCheckClient, eventBroker events.Broker,
	savingsClient savings.SavingsClient, onbClient onbPb.OnboardingClient) *mfExternalConsumer.MFExternalConsumer {
	wire.Build(
		mfExternalDao.MutualFundExternalOrderWireSet,
		mfExternalDao.MfExternalHoldingsImportReqWireSet,
		mfExternalDao.WireMutualFundExternalHoldingsSummaryDaoCRDBSet,
		mfDao.MutualfundWireSet,
		invEventProcessor.NewMFEventProcessor,
		GormProvider,
		idgen.NewClock,
		idgen.WireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		insightsPkg.InsightsNameCheckProcessorWireSet,
		mfExternalConsumer.NewMFExternalConsumer,
	)
	return &mfExternalConsumer.MFExternalConsumer{}
}

func IDbResourceProviderProvider(dbConnProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) storagev2.IDbResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func InitializeEventProcessorService(
	pgdbConns *gencfg.PgdbConns,
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	investmentConf *investmentGenConf.Config,
	epifiWealthCRDB types.EpifiWealthCRDB,
	dbConnProvider *storagev2.DBResourceProvider[*gorm.DB],
	notificationsPublisher wtypes.InvestmentEventBasedNotificationsSqsCustomDelayPublisher,
	commsClient wtypes.InvestmentCommsClientWithInterceptors,
	actorClient actorPb.ActorClient,
	usStocksAccountManagerClient usStocksAccountPb.AccountManagerClient,
	usStocksCatalogManagerClient usStocksCatalogPb.CatalogManagerClient,
	usStocksOrderManagerClient usStocksOrderPb.OrderManagerClient,
	connectedAccountClient connectedAccPb.ConnectedAccountClient,
	p2PInvestmentClient p2pinvestment.P2PInvestmentClient,
	nudgeClient nudgePb.NudgeServiceClient,
) *eventProcessor.Service {
	wire.Build(
		wtypes.CommsClientProvider,
		eventProcessor.NewService,
		wire.NewSet(GormProvider, once.NewDoOnce),
		investmentUserJourneyDao.UserJourneyDaoWireSet,
		event_processor_plugins.NewUSStocksEventProcessor,
		IDbResourceProviderProvider,
		jump.NewEventProcessor,
	)
	return &eventProcessor.Service{}
}

func InitialiseMfStatementConsumerService(
	db types.EpifiWealthCRDB,
	usersClient userPb.UsersClient,
	docsClient docs.DocsClient,
	wealthOBClient wob.WealthOnboardingClient,
	commsClient wtypes.InvestmentCommsClientWithInterceptors,
	rmsClient rms.RuleManagerClient,
	investmentConf *investmentGenConf.Config,
) *mfStatementConsumer.Service {
	wire.Build(
		wtypes.CommsClientProvider,
		mfDao.MutualFundOrderWireSet,
		mfDao.MutualfundWireSet,
		mfDao.RTAOrderDataDaoCrdbDaoWireSet,
		GormProvider,
		idgen.NewClock,
		idgen.WireSet,
		mfStatementConsumer.NewService,
	)
	return &mfStatementConsumer.Service{}
}

func InitialiseStatementSvc(MfStatementPublisher wtypes.MfStatementPublisher,
) *statementPb.Service {
	wire.Build(
		statementPb.NewService,
	)
	return &statementPb.Service{}
}

func InitialiseProfileService(
	wealthDb types.EpifiWealthCRDB,
	techDb types.EpifiCRDB,
	usersClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	consentClient consent.ConsentClient,
	cfg *investmentGenConf.Config,
	eventPublisher wtypes.NonFinancialEventSqsPublisher,
) *profile.Service {
	wire.Build(
		profileDao.InvestmentRiskProfileWireSet,
		surveyDao.InvestmentRiskSurveyWireSet,
		profile.NewService,
	)
	return &profile.Service{}
}

func InitialiseDynamicUIElementService(
	db types.EpifiWealthCRDB,
	cfg *investmentGenConf.Config,
	segmentationServiceClient segment.SegmentationServiceClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	userGroupClient usergroupPb.GroupClient,
	dynamicUIElementRedisStore wtypes.DynamicUIElementCacheRedisStore,
) *dynamicUIElement.Service {
	wire.Build(
		GormProvider,
		GetUSSCollectionScreenABEvaluatorProvider,
		GetUSSLandingPageABEvaluatorProvider,
		GetInvHomeRecommendationABExperimentEvaluator,
		ABFeatureReleaseConfigProvider,
		investment.NewInvHomeRecommendationABExperimentEvaluator,
		usstocks.NewUSSCollectionScreenABExperimentEvaluator,
		usstocks.NewUSSLandingPageComponentABExperimentEvaluator,
		dynamicUIElement.NewABExperimentEvaluatorFactory,
		dynamicUIElement.ABExperimentEvaluatorFactoryWireset,
		idgen.NewClock, idgen.WireSet,
		DynamicUIElementEvaluatorCacheConfigProvider,
		DynamicUIElementCacheConfigProvider,
		dynamicUIElementDao.DynamicUIElementWireSet,
		DynamicUIElementRedisStoreRedisClientProvider,
		cache.RedisStorageWireSet,
		dynamicUIElement.NewService,
		wire.NewSet(dynamicUIElement.NewExpressionEvaluator, wire.Bind(new(dynamicUIElement.IExpressionEvaluator), new(*dynamicUIElement.ExpressionEvaluator))),
	)
	return &dynamicUIElement.Service{}
}

func InitialiseNonFinancialEventsConsumerService(eventPublisher wtypes.NonFinancialEventSnsPublisher) *nonFinEventsConsumer.Service {
	wire.Build(
		nonFinEventsProcessor.NewRiskSurveyStatusProcessor,
		nonFinEventsProcessor.NewUSStocksAccountProcessor,
		nonFinEventsConsumer.NewService,
	)
	return &nonFinEventsConsumer.Service{}
}

func InitialiseOrderETAConsumerService(
	db types.EpifiWealthCRDB,
	cfg *investmentGenConf.Config,
	orderETADelayPublisher wtypes.OrderETADelayPublisher,
	watsonClient watsonPb.WatsonClient,
	paymentHandlerClient phPb.PaymentHandlerClient,
) *etaHandlerPb.Service {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		GormProvider,
		mfDao.OrderWireSet,
		mfDao.MutualfundWireSet,
		etaHandlerPb.NewProcessOrderETAFactory,
		orderEtaProcessorPb.NewMutualFundsOrderEtaProcessor,
		etaHandlerPb.NewService,
	)
	return &etaHandlerPb.Service{}
}

func InitialiseInvestmentWatsonClientService(
	db types.EpifiWealthCRDB,
	cfg *investmentGenConf.Config,
) *investmentWatsonClient.Service {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		GormProvider,
		mfDao.OrderWireSet,
		mfDao.MutualfundWireSet,
		investmentWatsonClient.NewService,
	)
	return &investmentWatsonClient.Service{}
}

func InitialiseMfFolioService(
	db types.EpifiWealthCRDB,
	cfg *investmentGenConf.Config,
) *folioPbClient.MfFolioService {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		GormProvider,
		mfDao.FolioLedgerWireSet,
		folioPbClient.NewMfFolioService,
	)
	return &folioPbClient.MfFolioService{}
}

func InitializeActivityProcessor(
	db types.EpifiWealthCRDB,
	conf *workerConf.Config,
	s3Client s3.S3Client,
	ocrClient inhouseocr.OcrClient,
	awsConf aws.Config,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	catalogManagerClient catalogPb.CatalogManagerClient,
) *activityProcessorPb.Processor {
	wire.Build(
		mfAumReconSlackAlertClientProvider,
		activityProcessorPb.NewProcessor,
		mfDao.MutualfundWireSet,
		mfDao.OrderWireSet,
		mfDao.FolioLedgerWireSet,
		mfDao.OrderConfirmationInfoWireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		idgen.NewClock,
		idgen.WireSet,
		gormProvider,
		datetime.WireDefaultTimeSet,
		helper.DocProofHelperWireSet,
		HttpClientProvider,
		ocr.InhouseOCRWireSet,
		LambdaClientProvider,
	)
	return &activityProcessorPb.Processor{}
}

func mfAumReconSlackAlertClientProvider(conf *workerConf.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[workerConf.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[workerConf.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func investmentSlackAlertClientProvider(conf *config.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.SlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.SlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func gormProvider(db types.EpifiWealthCRDB) *gormv2.DB {
	return db
}

func LambdaClientProvider(awsConf aws.Config) lambda.LambdaClient {
	return lambda.NewAwsLambda(awsConf)
}

func HttpClientProvider() *http.Client {
	return &http.Client{}
}
