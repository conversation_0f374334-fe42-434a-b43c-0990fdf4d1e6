// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	sqs2 "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	genconf2 "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/fittt"
	auth2 "github.com/epifi/gamma/api/investment/auth"
	"github.com/epifi/gamma/api/investment/dynamic_ui_element"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	order3 "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	"github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/usstocks/account"
	catalog3 "github.com/epifi/gamma/api/usstocks/catalog"
	order4 "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	"github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/investment/aggregator"
	consumer8 "github.com/epifi/gamma/investment/aggregator/consumer"
	"github.com/epifi/gamma/investment/aggregator/consumer/instrument_event"
	"github.com/epifi/gamma/investment/aggregator/rewardtriggerer"
	auth3 "github.com/epifi/gamma/investment/auth"
	dao5 "github.com/epifi/gamma/investment/auth/dao"
	"github.com/epifi/gamma/investment/config"
	"github.com/epifi/gamma/investment/config/genconf"
	dynamic_ui_element2 "github.com/epifi/gamma/investment/dynamic_ui_element"
	"github.com/epifi/gamma/investment/dynamic_ui_element/ab_experiment_evaluator/investment"
	"github.com/epifi/gamma/investment/dynamic_ui_element/ab_experiment_evaluator/usstocks"
	dao4 "github.com/epifi/gamma/investment/dynamic_ui_element/dao"
	"github.com/epifi/gamma/investment/event_processor"
	dao6 "github.com/epifi/gamma/investment/event_processor/dao"
	"github.com/epifi/gamma/investment/event_processor/event_processor_plugins"
	"github.com/epifi/gamma/investment/event_processor/event_processor_plugins/jump"
	"github.com/epifi/gamma/investment/mutualfund/activity"
	catalog2 "github.com/epifi/gamma/investment/mutualfund/catalog"
	"github.com/epifi/gamma/investment/mutualfund/catalog/catalog_aggregator"
	"github.com/epifi/gamma/investment/mutualfund/catalog/catalog_file_processor"
	consumer6 "github.com/epifi/gamma/investment/mutualfund/catalog/consumer"
	impl2 "github.com/epifi/gamma/investment/mutualfund/catalog/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/catalog/nominee_update"
	scheduler2 "github.com/epifi/gamma/investment/mutualfund/catalog/scheduler"
	"github.com/epifi/gamma/investment/mutualfund/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/developer"
	processor2 "github.com/epifi/gamma/investment/mutualfund/developer/processor"
	"github.com/epifi/gamma/investment/mutualfund/eta_handler"
	"github.com/epifi/gamma/investment/mutualfund/eta_handler/order_eta_processor"
	"github.com/epifi/gamma/investment/mutualfund/external"
	consumer9 "github.com/epifi/gamma/investment/mutualfund/external/consumer"
	impl3 "github.com/epifi/gamma/investment/mutualfund/external/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/external/processor/factory"
	impl5 "github.com/epifi/gamma/investment/mutualfund/external/processor/impl"
	"github.com/epifi/gamma/investment/mutualfund/foliodetails"
	"github.com/epifi/gamma/investment/mutualfund/notifications"
	"github.com/epifi/gamma/investment/mutualfund/notifications/actor_segmentation"
	impl4 "github.com/epifi/gamma/investment/mutualfund/notifications/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/notifications/dynamic_elements_getter"
	"github.com/epifi/gamma/investment/mutualfund/notifications/oms_order_update_processor"
	"github.com/epifi/gamma/investment/mutualfund/notifications/oms_order_update_processor/oms_order_update_executor"
	order2 "github.com/epifi/gamma/investment/mutualfund/order"
	consumer7 "github.com/epifi/gamma/investment/mutualfund/order/consumer"
	filegenerator2 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/accessors"
	dao2 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao"
	vendor_processor2 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor"
	cams12 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/cams"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/file_name_generator"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/file_name_generator/sequence_number_generator"
	karvy7 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/karvy"
	cams10 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/credit_mis_report_file_processor/cams"
	karvy6 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/credit_mis_report_file_processor/karvy"
	cams9 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/elog_file_processor/cams"
	cams8 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/fatca_file_processor/cams"
	karvy5 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/fatca_file_processor/karvy"
	cams11 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/nft_file_for_folio_processor/cams"
	cams7 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/order_feed_file_processor/cams"
	karvy4 "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/processor/order_feed_file_processor/karvy"
	"github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor/withdrawable_entity_calculator"
	"github.com/epifi/gamma/investment/mutualfund/order/operations"
	"github.com/epifi/gamma/investment/mutualfund/order/orchestrator/consumer"
	"github.com/epifi/gamma/investment/mutualfund/order/order_type_processor"
	prerequisite_handler2 "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler"
	consumer4 "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/consumer"
	"github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/consumer/vendor_gateway_accessor"
	"github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/dao"
	"github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/processor"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed"
	consumer2 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/consumer"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/rta_order_data"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor"
	cams5 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/cams"
	cams6 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/folio_reconcilation_file_processor/cams"
	karvy3 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/karvy"
	cams3 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/payment_details_file_processor/cams"
	karvy2 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/payment_details_file_processor/karvy"
	cams4 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/rejected_orders_file_processor/cams"
	cams2 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/schema_master_file_processor/cams"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/cams"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/cams/wbr2/external_order_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/cams/wbr2/internal_order_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/karvy"
	external_order_processor2 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/karvy/wbr201/external_order_processor"
	internal_order_processor2 "github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/transaction_feed_file_processor/karvy/wbr201/internal_order_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/scheduler"
	"github.com/epifi/gamma/investment/mutualfund/order/sip_ledger_util"
	"github.com/epifi/gamma/investment/mutualfund/order/update_order"
	"github.com/epifi/gamma/investment/mutualfund/order/validator"
	"github.com/epifi/gamma/investment/mutualfund/order/vendor_order_sender"
	consumer5 "github.com/epifi/gamma/investment/mutualfund/order/vendor_order_sender/consumer"
	payment_handler2 "github.com/epifi/gamma/investment/mutualfund/payment_handler"
	consumer3 "github.com/epifi/gamma/investment/mutualfund/payment_handler/consumer"
	"github.com/epifi/gamma/investment/mutualfund/payment_handler/consumer/payment_updater"
	dao3 "github.com/epifi/gamma/investment/mutualfund/payment_handler/dao"
	"github.com/epifi/gamma/investment/mutualfund/payment_handler/payment_executor"
	scheduler3 "github.com/epifi/gamma/investment/mutualfund/payment_handler/scheduler"
	"github.com/epifi/gamma/investment/mutualfund/reconciliation"
	"github.com/epifi/gamma/investment/mutualfund/reconciliation/rta_processor"
	"github.com/epifi/gamma/investment/mutualfund/statement"
	consumer10 "github.com/epifi/gamma/investment/mutualfund/statement/consumer"
	consumer11 "github.com/epifi/gamma/investment/non_financial_events/consumer"
	processor3 "github.com/epifi/gamma/investment/non_financial_events/consumer/processor"
	"github.com/epifi/gamma/investment/profile"
	"github.com/epifi/gamma/investment/profile/dao/profile_impl"
	"github.com/epifi/gamma/investment/profile/dao/survey_impl"
	watson2 "github.com/epifi/gamma/investment/watson"
	types2 "github.com/epifi/gamma/investment/wire/types"
	fittt2 "github.com/epifi/gamma/pkg/fittt"
	"github.com/epifi/gamma/pkg/zinc/search"
	"github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/ocr"
	"github.com/redis/go-redis/v9"
	"github.com/slack-go/slack"
	"gorm.io/gorm"
	"net/http"
)

// Injectors from wire.go:

func InitialiseInvestmentOrchestratorConsumer(db types.EpifiWealthCRDB, fileGeneratorClient filegenerator.FileGeneratorClient, processFileGenPublisher types2.ProcessFileGenPublisher, processFileGenSuccessPublisher types2.ProcessFileGenSuccessPublisher, preReqClient prerequisite_handler.PrerequisiteHandlerClient, config *genconf.Config, omsClient order.OrderServiceClient, catalogueManagerClient catalog.CatalogManagerClient, actorClient actor.ActorClient, commsClient types2.InvestmentCommsClientWithInterceptors, usersClient user.UsersClient, sellOrderUpdateQueuePublisher update_order.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher update_order.InvestmentInstrumentEventPublisher) *consumer.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	batchOrderProcessingDetailsCrdb := impl.NewBatchOrderProcessingStepDetailsCrdb(gormDB)
	batchOrderProcessingStepDetailsCrdb := impl.NewOrderProcessingStepCrdb(gormDB)
	investmentOrcPublishers := consumer.NewInvestmentOrcPublishers(processFileGenPublisher, processFileGenSuccessPublisher)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	loggerObserver := update_order.NewLoggerObserver()
	orderStatusCountObserver := update_order.NewOrderStatusCountObserver()
	orderStuckObserver := update_order.NewOrderStuckObserver()
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	sellOrderSettlementObserver := update_order.NewSellOrderSettlementObserver(sellOrderUpdateQueuePublisher, orderCrdb, mutualFundCrdb)
	orderUpdateEventObserver := update_order.NewOrderUpdateEventObserver(investmentInstrumentQueuePublisher)
	doOnce := once.NewDoOnce(gormDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	orderFailedNotificationObserver := update_order.NewOrderFailedNotificationObserver(orderCrdb, doOnce, catalogueManagerClient, actorClient, usersClient, commsCommsClient, config)
	orderStatusNotifier := update_order.NewOrderStatusNotifier(loggerObserver, orderStatusCountObserver, orderStuckObserver, sellOrderSettlementObserver, orderUpdateEventObserver, orderFailedNotificationObserver)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	sipLedgerCrdb := impl.NewSIPLedgerCrdb(gormDB)
	sipLedgerHelper := sip_ledger_util.NewSIPLedgerHelper(orderCrdb, sipLedgerCrdb, orderStatusUpdateCrdb, folioLedgerCrdb, orderStatusNotifier, crdbIdempotentTxnExecutor)
	service := consumer.NewService(orderCrdb, orderStatusUpdateCrdb, amcInfoCrdb, batchOrderProcessingDetailsCrdb, batchOrderProcessingStepDetailsCrdb, preReqClient, fileGeneratorClient, omsClient, investmentOrcPublishers, crdbIdempotentTxnExecutor, orderStatusNotifier, config, folioLedgerCrdb, sipLedgerHelper)
	return service
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitialiseOrderManagerSvc(ctx context.Context, db types.EpifiWealthCRDB, sqsClient *sqs.Client, vgmfClient mutualfund.MutualFundClient, fileGeneratorClient filegenerator.FileGeneratorClient, publisher types2.VendorOrderFileStatusPublisher, camsS3Client types2.CamsS3Client, karvyS3Client types2.KarvyS3Client, paymentHandlerClient payment_handler.PaymentHandlerClient, catalogueManagerClient catalog.CatalogManagerClient, genConf *genconf.Config, savingsClient savings.SavingsClient, actorClient actor.ActorClient, commsClient types2.InvestmentCommsClientWithInterceptors, kycClient kyc.KycClient, client user.UsersClient, authClient auth.AuthClient, wealthOBClient wealthonboarding.WealthOnboardingClient, fitttClient fittt.FitttClient, deferredNotificationPublisher types2.DeferredNotificationDelayPublisher, orderDelayedNotificationPublisher types2.OrderDelayedNotificationDelayPublisher, sellOrderUpdateQueuePublisher update_order.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher update_order.InvestmentInstrumentEventPublisher, investmentAuthClient auth2.AuthClient, panClient pan.PanClient, bcClient bankcust.BankCustomerServiceClient, orderETADelayPublisher types2.OrderETADelayPublisher, ruleManagerClient manager.RuleManagerClient) (*order2.Service, error) {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	fileStateCrdb := impl.NewFileStateCrdb(gormDB)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	orderRejectionInfoCrdb := impl.NewMFOrderRejectionInfoCrdb(gormDB)
	orderConfirmationInfoCrdb := impl.NewMFOrderConfirmationInfoCrdb(gormDB)
	sipLedgerCrdb := impl.NewSIPLedgerCrdb(gormDB)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	loggerObserver := update_order.NewLoggerObserver()
	orderStatusCountObserver := update_order.NewOrderStatusCountObserver()
	orderStuckObserver := update_order.NewOrderStuckObserver()
	sellOrderSettlementObserver := update_order.NewSellOrderSettlementObserver(sellOrderUpdateQueuePublisher, orderCrdb, mutualFundCrdb)
	orderUpdateEventObserver := update_order.NewOrderUpdateEventObserver(investmentInstrumentQueuePublisher)
	doOnce := once.NewDoOnce(gormDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	orderFailedNotificationObserver := update_order.NewOrderFailedNotificationObserver(orderCrdb, doOnce, catalogueManagerClient, actorClient, client, commsCommsClient, genConf)
	orderStatusNotifier := update_order.NewOrderStatusNotifier(loggerObserver, orderStatusCountObserver, orderStuckObserver, sellOrderSettlementObserver, orderUpdateEventObserver, orderFailedNotificationObserver)
	vendorOrderFileSender := vendor_order_sender.NewVendorOrderFileSender(fileStateCrdb, orderCrdb, orderStatusUpdateCrdb, vgmfClient, publisher, fileGeneratorClient, genConf, crdbIdempotentTxnExecutor, orderStatusNotifier, folioLedgerCrdb)
	vendorOrderSenderFactory := vendor_order_sender.NewVendorOrderSenderFactory(vendorOrderFileSender)
	uploadCreditMISToVendorPublisher, err := uploadCreditMISToVendorPublisherProvider(ctx, genConf, sqsClient)
	if err != nil {
		return nil, err
	}
	hardLockInCalculator := withdrawable_entity_calculator.NewHardLockInCalculator()
	hardLockinProcessor := lockin_type_processor.NewHardLockinProcessor(hardLockInCalculator)
	noLockInCalculator := withdrawable_entity_calculator.NewNoLockInCalculator()
	softLockinProcessor := lockin_type_processor.NewSoftLockinProcessor(noLockInCalculator)
	noLockinProcessor := lockin_type_processor.NewNoLockinProcessor(noLockInCalculator)
	lockinProcessorFactory := lockin_type_processor.NewLockInProcessorFactory(hardLockinProcessor, softLockinProcessor, noLockinProcessor)
	purchaseConstraintsValidator := validator.NewPurchaseConstraintsValidator()
	fundEligibilityValidator := validator.NewFundEligibilityValidator()
	nomineeDeclarationValidator := validator.NewNomineeDeclarationValidator(wealthOBClient)
	panAadhaarLinkValidator := validator.NewPanAadhaarLinkValidator(genConf, panClient)
	buyOrderProcessor := order_type_processor.NewBuyOrderProcessor(folioLedgerCrdb, orderCrdb, sipLedgerCrdb, purchaseConstraintsValidator, fundEligibilityValidator, nomineeDeclarationValidator, panAadhaarLinkValidator, genConf, investmentAuthClient)
	sellOrderProcessor := order_type_processor.NewSellOrderProcessor(lockinProcessorFactory, crdbIdempotentTxnExecutor, folioLedgerCrdb, orderCrdb, genConf, orderStatusUpdateCrdb, savingsClient, orderStatusNotifier, panAadhaarLinkValidator, ruleManagerClient, client, wealthOBClient)
	sipLedgerHelper := sip_ledger_util.NewSIPLedgerHelper(orderCrdb, sipLedgerCrdb, orderStatusUpdateCrdb, folioLedgerCrdb, orderStatusNotifier, crdbIdempotentTxnExecutor)
	service := order2.NewService(orderCrdb, folioLedgerCrdb, fileStateCrdb, mutualFundCrdb, amcInfoCrdb, orderStatusUpdateCrdb, orderRejectionInfoCrdb, orderConfirmationInfoCrdb, sipLedgerCrdb, vendorOrderSenderFactory, camsS3Client, karvyS3Client, paymentHandlerClient, catalogueManagerClient, genConf, fileGeneratorClient, savingsClient, actorClient, commsCommsClient, uploadCreditMISToVendorPublisher, kycClient, lockinProcessorFactory, crdbIdempotentTxnExecutor, client, doOnce, wealthOBClient, fitttClient, deferredNotificationPublisher, orderDelayedNotificationPublisher, orderStatusNotifier, buyOrderProcessor, sellOrderProcessor, bcClient, orderETADelayPublisher, sipLedgerHelper)
	return service, nil
}

// config: {"s3Client": "Application().Operations().S3Bucket", "camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitialiseMFOrderOperationsSvc(db types.EpifiWealthCRDB, s3Client types2.MFOpsS3Client, catalogueManagerClient catalog.CatalogManagerClient, wealthOnboardingClient wealthonboarding.WealthOnboardingClient, paymentHandlerClient payment_handler.PaymentHandlerClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, orderManagerClient order3.OrderManagerClient, fileGeneratorClient filegenerator.FileGeneratorClient, config *genconf.Config, ticketClient ticket.TicketClient, camsS3Client types2.CamsS3Client, karvyS3Client types2.KarvyS3Client, paymentClient payment.PaymentClient, piClient paymentinstrument.PiClient) *operations.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	prerequisiteStatusCrdb := dao.NewPrerequisitesCrdb(gormDB)
	entityFileMapperCrdb := dao2.NewEntityFileMapperCrdb(gormDB, domainIdGenerator)
	fileGenerationAttemptCrdb := dao2.NewFileGenerationAttemptCrdb(gormDB)
	fileStateCrdb := impl.NewFileStateCrdb(gormDB)
	service := operations.NewService(orderCrdb, s3Client, catalogueManagerClient, wealthOnboardingClient, paymentHandlerClient, savingsClient, actorClient, userClient, fileGeneratorClient, orderManagerClient, config, prerequisiteStatusCrdb, entityFileMapperCrdb, fileGenerationAttemptCrdb, fileStateCrdb, ticketClient, camsS3Client, karvyS3Client, paymentClient, piClient)
	return service
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitializeReverseFeedConsumerService(camsS3Client vendor_processor.CamsS3Client, orderManagerClient order3.OrderManagerClient, karvyS3Client vendor_processor.KarvyS3Client, db types.EpifiWealthCRDB, config *genconf.Config, catalogClient catalog.CatalogManagerClient) *consumer2.Service {
	externalOrderProcessor := external_order_processor.NewExternalOrderProcessor(orderManagerClient)
	normalBuyAndSellOrderProcessor := internal_order_processor.NewNormalBuyAndSellOrderProcessor(orderManagerClient)
	switchOrderProcessor := internal_order_processor.NewSwitchOrderProcessor(orderManagerClient)
	external_order_processorSwitchOrderProcessor := external_order_processor.NewSwitchOrderProcessor(orderManagerClient)
	gormDB := GormProvider(db)
	rtaOrderDataCrdb := impl.NewRTAOrderDataDaoCrdb(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	rtaOrderDataProcessor := rta_order_data.NewRTAOrderDataProcessor(rtaOrderDataCrdb, mutualFundCrdb, folioLedgerCrdb)
	wbr2FeedFileProcessor := cams.NewWBR2FeedFileProcessor(camsS3Client, config, externalOrderProcessor, normalBuyAndSellOrderProcessor, switchOrderProcessor, external_order_processorSwitchOrderProcessor, rtaOrderDataProcessor)
	schemaMasterFileProcessor := cams2.NewSchemaMasterFileProcessor(camsS3Client, catalogClient)
	wbr52FileProcessor := cams3.NewWBR52FileProcessor(camsS3Client, orderManagerClient)
	wbr46FileProcessor := cams4.NewWBR46FileProcessor(camsS3Client, orderManagerClient)
	camsVendorProcessor := cams5.NewCamsVendorProcessor(wbr2FeedFileProcessor, schemaMasterFileProcessor, wbr52FileProcessor, wbr46FileProcessor, config)
	external_order_processorExternalOrderProcessor := external_order_processor2.NewExternalOrderProcessor(orderManagerClient)
	internal_order_processorSwitchOrderProcessor := internal_order_processor2.NewSwitchOrderProcessor(orderManagerClient)
	internal_order_processorNormalBuyAndSellOrderProcessor := internal_order_processor2.NewNormalBuyAndSellOrderProcessor(orderManagerClient)
	switchOrderProcessor2 := external_order_processor2.NewSwitchOrderProcessor(orderManagerClient)
	wbr201FeedFileProcessor := karvy.NewWBR201FeedFileProcessor(karvyS3Client, config, external_order_processorExternalOrderProcessor, internal_order_processorSwitchOrderProcessor, internal_order_processorNormalBuyAndSellOrderProcessor, mutualFundCrdb, rtaOrderDataProcessor, switchOrderProcessor2)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	wbr245FileProcessor := karvy2.NewWBR245FileProcessor(karvyS3Client, orderManagerClient, orderCrdb, mutualFundCrdb)
	karvyVendorProcessor := karvy3.NewKarvyVendorProcessor(wbr201FeedFileProcessor, wbr245FileProcessor)
	vendorProcessorFactory := reverse_feed.NewVendorProcessorFactory(camsVendorProcessor, karvyVendorProcessor)
	service := consumer2.NewService(vendorProcessorFactory)
	return service
}

// config: {"s3Client": "Application().FileGenerator().CamsS3Bucket"}
func InitializeReverseFeedService(catalogueManagerClient catalog.CatalogManagerClient, s3Client types2.CamsS3Client, wob wealthonboarding.WealthOnboardingClient, fileGeneratorClient filegenerator.FileGeneratorClient, mfClient mutualfund.MutualFundClient) *reverse_feed.Service {
	wbr9FeedFileProcessor := cams6.NewWBR9FeedFileProcessor(catalogueManagerClient, s3Client, wob, fileGeneratorClient, mfClient)
	service := reverse_feed.NewService(wbr9FeedFileProcessor)
	return service
}

func InitializePaymentUpdateConsumerSvc(db types.EpifiWealthCRDB, orderManagerClient order3.OrderManagerClient, omsClient order.OrderServiceClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, cfg *genconf.Config) *consumer3.Service {
	gormDB := GormProvider(db)
	paymentCrdb := dao3.NewPaymentCrdb(gormDB)
	siPaymentExecutor := payment_executor.NewSIPaymentExecutor(recurringPaymentClient, orderManagerClient, cfg)
	p2PTransferExecutor := payment_executor.NewP2PTransferExecutor(orderManagerClient, omsClient, cfg)
	paymentExecutorFactory := payment_handler2.NewPaymentExecutorFactory(siPaymentExecutor, p2PTransferExecutor)
	oneTimeMFOrderPaymentUpdater := payment_updater.NewOneTimeMFOrderPaymentUpdater(paymentCrdb, orderManagerClient, paymentExecutorFactory)
	autoInvestMFOrderPaymentUpdater := payment_updater.NewAutoInvestMFOrderPaymentUpdater(paymentCrdb, orderManagerClient, paymentExecutorFactory)
	paymentUpdateConsumerFactory := consumer3.NewPaymentUpdateConsumerFactory(oneTimeMFOrderPaymentUpdater, autoInvestMFOrderPaymentUpdater, orderManagerClient)
	service := consumer3.NewService(orderManagerClient, paymentUpdateConsumerFactory, paymentCrdb)
	return service
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitialiseFileGeneratorSvc(db types.EpifiWealthCRDB, camsS3Client vendor_processor2.CamsS3Client, karvyS3Client vendor_processor2.KarvyS3Client, genConf *genconf.Config, orderManagerClient order3.OrderManagerClient, wealthOnboardingClient wealthonboarding.WealthOnboardingClient, catalogueManagerClient catalog.CatalogManagerClient, paymentHandlerClient payment_handler.PaymentHandlerClient, client savings.SavingsClient, authClient auth2.AuthClient, docsClient docs.DocsClient, awsConf aws.Config, userClient user.UsersClient, ruleManagerClient manager.RuleManagerClient, accountBalanceClient balance.BalanceClient) *filegenerator2.Service {
	wealthOnBoardingAccessor := accessors.NewWealthOnBoardingAccessor(wealthOnboardingClient)
	orderManagerAccessor := accessors.NewOrderManagerAccessor(orderManagerClient)
	catalogueManagerAccessor := accessors.NewCatalogueManagerAccessor(catalogueManagerClient)
	rmsAccessor := accessors.NewRMSAccessor(ruleManagerClient)
	authAccessor := accessors.NewAuthAccessor(authClient, rmsAccessor)
	savingsAccessor := accessors.NewSavingsAccessor(client, accountBalanceClient)
	gormDB := GormProvider(db)
	sipLedgerCrdb := impl.NewSIPLedgerCrdb(gormDB)
	sipLedgerAccessor := accessors.NewSipLedgerAccessor(sipLedgerCrdb)
	nomineeAccessor := accessors.NewNomineeAccessor(userClient)
	paymentHandlerAccessor := accessors.NewPaymentHandlerAccessor(paymentHandlerClient, genConf)
	orderFeedFileProcessor := cams7.NewCamsOrderFeedFileProcessor(wealthOnBoardingAccessor, orderManagerAccessor, catalogueManagerAccessor, authAccessor, savingsAccessor, sipLedgerAccessor, nomineeAccessor, paymentHandlerAccessor, genConf)
	fatcaFileProcessor := cams8.NewCamsFATCAFileProcessor(wealthOnBoardingAccessor, genConf)
	elogFileProccessor := cams9.NewCamsElogFileProcessor(wealthOnBoardingAccessor, genConf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	amcInfoAccessor := accessors.NewAMCInfoAccessor(amcInfoCrdb)
	creditMISReportFileProcessor := cams10.NewCamsCreditMISReportFileProcessor(wealthOnBoardingAccessor, orderManagerAccessor, catalogueManagerAccessor, paymentHandlerAccessor, amcInfoAccessor, genConf, savingsAccessor)
	fileSequenceGeneratorCrdb := dao2.NewFileSequenceGeneratorCrdb(gormDB, domainIdGenerator)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	intraDaySequenceNumberGenerator := sequence_number_generator.NewIntraDaySequenceNumberGenerator(fileSequenceGeneratorCrdb, crdbIdempotentTxnExecutor)
	camsFileNameGenerator := file_name_generator.NewCamsFileNameGenerator(intraDaySequenceNumberGenerator)
	nftFileForFolioProcessor := cams11.NewNFTFileForFolioProcessor(wealthOnBoardingAccessor, genConf)
	camsVendorProcessor := cams12.NewCamsVendorProcessor(orderFeedFileProcessor, fatcaFileProcessor, elogFileProccessor, creditMISReportFileProcessor, camsFileNameGenerator, nftFileForFolioProcessor, camsS3Client)
	karvyOrderFeedFileProcessor := karvy4.NewKarvyOrderFeedFileProcessor(wealthOnBoardingAccessor, orderManagerAccessor, catalogueManagerAccessor, authAccessor, savingsAccessor, sipLedgerAccessor, nomineeAccessor, paymentHandlerAccessor, genConf)
	karvyFATCAFileProcessor := karvy5.NewKarvyFATCAFileProcessor(wealthOnBoardingAccessor, genConf)
	karvyCreditMISReportFileProcessor := karvy6.NewKarvyCreditMISReportFileProcessor(wealthOnBoardingAccessor, orderManagerAccessor, catalogueManagerAccessor, paymentHandlerAccessor, amcInfoAccessor, genConf, savingsAccessor)
	karvyFileNameGenerator := file_name_generator.NewKarvyFileNameGenerator(intraDaySequenceNumberGenerator)
	karvyVendorProcessor := karvy7.NewKarvyVendorProcessor(karvyOrderFeedFileProcessor, karvyFATCAFileProcessor, karvyCreditMISReportFileProcessor, karvyFileNameGenerator, karvyS3Client)
	vendorProcessorFactory := filegenerator2.NewVendorProcessorFactory(camsVendorProcessor, karvyVendorProcessor)
	fileGenerationAttemptCrdb := dao2.NewFileGenerationAttemptCrdb(gormDB)
	entityFileMapperCrdb := dao2.NewEntityFileMapperCrdb(gormDB, domainIdGenerator)
	awsLambda := lambda.NewAwsLambda(awsConf)
	s3UrlDownloader := S3UrlDownloaderProvider()
	httpClient := httpClientProvider()
	docsAccessor := accessors.NewDocsAccessor(docsClient, s3UrlDownloader, httpClient)
	service := filegenerator2.NewService(vendorProcessorFactory, genConf, fileGenerationAttemptCrdb, entityFileMapperCrdb, orderManagerClient, catalogueManagerAccessor, crdbIdempotentTxnExecutor, docsClient, awsLambda, wealthOnboardingClient, docsAccessor)
	return service
}

func InitialisePrerequisiteHandlerService(db types.EpifiWealthCRDB, wobClient wealthonboarding.WealthOnboardingClient, fileGenClient filegenerator.FileGeneratorClient, elogPreRequisiteStatusPublisher types2.ElogPreRequisiteStatusPublisher, fatcaPreRequisiteStatusPublisher types2.FATCAPreRequisiteStatusPublisher, retryPublisher types2.PreRequisiteHandlerRetryPublisher) *prerequisite_handler2.Service {
	elogPreRequisiteProcessor := processor.NewElogPreRequisiteProcessor(fileGenClient, elogPreRequisiteStatusPublisher)
	fatcaPreRequisiteProcessor := processor.NewFATCAPreRequisiteProcessor(fileGenClient, fatcaPreRequisiteStatusPublisher)
	kraPreRequisiteProcessor := processor.NewKRAPreRequisiteProcessor(wobClient)
	preRequisiteProcessorFactory := processor.NewPreRequisiteProcessorFactory(elogPreRequisiteProcessor, fatcaPreRequisiteProcessor, kraPreRequisiteProcessor)
	gormDB := GormProvider(db)
	prerequisiteStatusCrdb := dao.NewPrerequisitesCrdb(gormDB)
	actorPrerequisiteRequestCrdb := dao.NewActorPrerequisiteRequestCrdb(gormDB)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	service := prerequisite_handler2.NewService(preRequisiteProcessorFactory, prerequisiteStatusCrdb, retryPublisher, actorPrerequisiteRequestCrdb, crdbIdempotentTxnExecutor)
	return service
}

func InitialisePrerequisiteConsumerSvc(db types.EpifiWealthCRDB, mfClient mutualfund.MutualFundClient, orderManagerClient order3.OrderManagerClient, wobClient wealthonboarding.WealthOnboardingClient, fileGenClient filegenerator.FileGeneratorClient, elogPreRequisiteStatusPublisher types2.ElogPreRequisiteStatusPublisher, fatcaPreRequisiteStatusPublisher types2.FATCAPreRequisiteStatusPublisher, retryPublisher types2.PreRequisiteHandlerRetryPublisher) *consumer4.Service {
	elogAccessor := vendor_gateway_accessor.NewElogAccessor(mfClient)
	fatcaAccessor := vendor_gateway_accessor.NewFatcaAccessor(mfClient)
	vendorGateWayAccessorFactory := vendor_gateway_accessor.NewVendorGateWayAccessorFactory(elogAccessor, fatcaAccessor)
	gormDB := GormProvider(db)
	prerequisiteStatusCrdb := dao.NewPrerequisitesCrdb(gormDB)
	actorPrerequisiteRequestCrdb := dao.NewActorPrerequisiteRequestCrdb(gormDB)
	elogPreRequisiteProcessor := processor.NewElogPreRequisiteProcessor(fileGenClient, elogPreRequisiteStatusPublisher)
	fatcaPreRequisiteProcessor := processor.NewFATCAPreRequisiteProcessor(fileGenClient, fatcaPreRequisiteStatusPublisher)
	kraPreRequisiteProcessor := processor.NewKRAPreRequisiteProcessor(wobClient)
	preRequisiteProcessorFactory := processor.NewPreRequisiteProcessorFactory(elogPreRequisiteProcessor, fatcaPreRequisiteProcessor, kraPreRequisiteProcessor)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	service := prerequisite_handler2.NewService(preRequisiteProcessorFactory, prerequisiteStatusCrdb, retryPublisher, actorPrerequisiteRequestCrdb, crdbIdempotentTxnExecutor)
	consumerService := consumer4.NewService(vendorGateWayAccessorFactory, prerequisiteStatusCrdb, orderManagerClient, actorPrerequisiteRequestCrdb, service, crdbIdempotentTxnExecutor)
	return consumerService
}

// config: {"mfCatalogS3Client": "MutualFundCatalogS3Conf().BucketName"}
func InitialiseCatalogManagerSvc(db types.EpifiWealthCRDB, mfCatalogS3Client types2.MFCatalogS3Client, genConf *genconf.Config, userGroupClient group.GroupClient, userClient user.UsersClient, actorClient actor.ActorClient, searchClient search.SearchClient, recentlyVisitedPublisher types2.RecentlyVisitedPublisher, deferredNotificationPublisher types2.DeferredNotificationDelayPublisher, segmentationServiceClient segment.SegmentationServiceClient, vgMfClient mutualfund.MutualFundClient, wealthOnboardingClient wealthonboarding.WealthOnboardingClient, mfRedis types2.MfCatalogRedisStore) *catalog2.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	mutualFundOrderCRDB := impl.NewMutualFundOrderCRDB(gormDB)
	collectionMutualFundCRDB := impl.NewCollectionMutualFundCRDB(gormDB)
	watchlistMutualFundCRDB := impl.NewWatchlistMutualFundCRDB(gormDB)
	recentMutualFundInfoCRDB := impl.NewRecentMutualFundInfoCRDB(gormDB)
	collectionCrdb := impl2.NewCollectionCrdb(gormDB, domainIdGenerator)
	collectionFundMappingCrdb := impl2.NewCollectionFundMappingCrdb(gormDB)
	watchListCrdb := impl2.NewWatchListCrdb(gormDB, domainIdGenerator)
	watchListFundMappingCrdb := impl2.NewWatchListFundMappingCrdb(gormDB)
	recentMutualFundCrdb := impl2.NewRecentMutualFundCrdb(gormDB)
	mutualFundCategoryAverageCrdb := impl.NewMutualFundCategoryAverageCrdb(gormDB, domainIdGenerator)
	fundFilterGroupCrdb := impl2.NewFundFilterGroupCrdb(gormDB, domainIdGenerator)
	fundFilterCrdb := impl2.NewFundFilterCrdb(gormDB)
	collectionFilterMappingCrdb := impl2.NewCollectionFilterMappingCrdb(gormDB)
	filtersWithGroupCrdb := impl2.NewFiltersWithGroupCrdb(gormDB)
	mutualFundHistoryNavDaoCrdb := impl.NewMutualFundHistoryNavDaoCrdb(gormDB)
	nomineeInfoCrdb := impl.NewFolioNomineeUpdateStatusCrdb(gormDB, domainIdGenerator)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	hardLockInCalculator := withdrawable_entity_calculator.NewHardLockInCalculator()
	hardLockinProcessor := lockin_type_processor.NewHardLockinProcessor(hardLockInCalculator)
	noLockInCalculator := withdrawable_entity_calculator.NewNoLockInCalculator()
	softLockinProcessor := lockin_type_processor.NewSoftLockinProcessor(noLockInCalculator)
	noLockinProcessor := lockin_type_processor.NewNoLockinProcessor(noLockInCalculator)
	lockinProcessorFactory := lockin_type_processor.NewLockInProcessorFactory(hardLockinProcessor, softLockinProcessor, noLockinProcessor)
	camsNomineeUpdater := nominee_update.NewCamsNomineeUpdater(vgMfClient)
	karvyNomineeUpdater := nominee_update.NewKarvyNomineeUpdater(vgMfClient)
	nomineeUpdaterFactory := nominee_update.NewNomineeUpdaterFactory(camsNomineeUpdater, karvyNomineeUpdater)
	client := NewMfCatalogRedisClient(mfRedis)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	service := catalog2.NewService(mutualFundCrdb, amcInfoCrdb, folioLedgerCrdb, orderCrdb, orderStatusUpdateCrdb, mutualFundOrderCRDB, mfCatalogS3Client, genConf, userGroupClient, actorClient, userClient, collectionMutualFundCRDB, watchlistMutualFundCRDB, recentMutualFundInfoCRDB, collectionCrdb, collectionFundMappingCrdb, watchListCrdb, watchListFundMappingCrdb, recentMutualFundCrdb, mutualFundCategoryAverageCrdb, fundFilterGroupCrdb, fundFilterCrdb, collectionFilterMappingCrdb, searchClient, filtersWithGroupCrdb, mutualFundHistoryNavDaoCrdb, nomineeInfoCrdb, recentlyVisitedPublisher, crdbIdempotentTxnExecutor, lockinProcessorFactory, deferredNotificationPublisher, segmentationServiceClient, wealthOnboardingClient, nomineeUpdaterFactory, redisCacheStorage)
	return service
}

func InitialiseVendorOrderStatusConsumerSvc(db types.EpifiWealthCRDB, vgmfClient mutualfund.MutualFundClient, genConf *genconf.Config, commsClient types2.InvestmentCommsClientWithInterceptors, actorClient actor.ActorClient, catalogCLient catalog.CatalogManagerClient, phClient payment_handler.PaymentHandlerClient, authClient auth.AuthClient, sellOrderUpdateQueuePublisher update_order.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher update_order.InvestmentInstrumentEventPublisher, userClient user.UsersClient) *consumer5.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	fileStateCrdb := impl.NewFileStateCrdb(gormDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	loggerObserver := update_order.NewLoggerObserver()
	orderStatusCountObserver := update_order.NewOrderStatusCountObserver()
	orderStuckObserver := update_order.NewOrderStuckObserver()
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	sellOrderSettlementObserver := update_order.NewSellOrderSettlementObserver(sellOrderUpdateQueuePublisher, orderCrdb, mutualFundCrdb)
	orderUpdateEventObserver := update_order.NewOrderUpdateEventObserver(investmentInstrumentQueuePublisher)
	doOnce := once.NewDoOnce(gormDB)
	orderFailedNotificationObserver := update_order.NewOrderFailedNotificationObserver(orderCrdb, doOnce, catalogCLient, actorClient, userClient, commsCommsClient, genConf)
	orderStatusNotifier := update_order.NewOrderStatusNotifier(loggerObserver, orderStatusCountObserver, orderStuckObserver, sellOrderSettlementObserver, orderUpdateEventObserver, orderFailedNotificationObserver)
	karvyOrderUpdater := consumer5.NewKarvyOrderUpdater(orderCrdb, orderStatusUpdateCrdb, folioLedgerCrdb, crdbIdempotentTxnExecutor, orderStatusNotifier)
	camsOrderUpdater := consumer5.NewCamsOrderUpdater(orderCrdb, orderStatusUpdateCrdb, folioLedgerCrdb, crdbIdempotentTxnExecutor, orderStatusNotifier)
	orderUpdaterFactory := consumer5.NewOrderUpdaterFactory(karvyOrderUpdater, camsOrderUpdater)
	service := consumer5.NewService(vgmfClient, orderCrdb, orderStatusUpdateCrdb, fileStateCrdb, genConf, commsCommsClient, actorClient, catalogCLient, phClient, userClient, crdbIdempotentTxnExecutor, orderUpdaterFactory, doOnce)
	return service
}

func InitialiseSchedulerService(db types.EpifiWealthCRDB, fgClient filegenerator.FileGeneratorClient, catalogCLient catalog.CatalogManagerClient, phClient payment_handler.PaymentHandlerClient, orchestratorPublisher types2.ProcessMutualFundOrderPublisher, commsClient types2.InvestmentCommsClientWithInterceptors, userClient user.UsersClient, genConf *genconf.Config, mutualFundClient mutualfund.MutualFundClient, wealthOnBoardingClient wealthonboarding.WealthOnboardingClient, actorClient actor.ActorClient, sellOrderUpdateQueuePublisher update_order.SettlementOrderUpdatesDelayPublisher, investmentInstrumentQueuePublisher update_order.InvestmentInstrumentEventPublisher) *scheduler.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	batchOrderProcessingDetailsCrdb := impl.NewBatchOrderProcessingStepDetailsCrdb(gormDB)
	fileStateCrdb := impl.NewFileStateCrdb(gormDB)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	loggerObserver := update_order.NewLoggerObserver()
	orderStatusCountObserver := update_order.NewOrderStatusCountObserver()
	orderStuckObserver := update_order.NewOrderStuckObserver()
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	sellOrderSettlementObserver := update_order.NewSellOrderSettlementObserver(sellOrderUpdateQueuePublisher, orderCrdb, mutualFundCrdb)
	orderUpdateEventObserver := update_order.NewOrderUpdateEventObserver(investmentInstrumentQueuePublisher)
	doOnce := once.NewDoOnce(gormDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	orderFailedNotificationObserver := update_order.NewOrderFailedNotificationObserver(orderCrdb, doOnce, catalogCLient, actorClient, userClient, commsCommsClient, genConf)
	orderStatusNotifier := update_order.NewOrderStatusNotifier(loggerObserver, orderStatusCountObserver, orderStuckObserver, sellOrderSettlementObserver, orderUpdateEventObserver, orderFailedNotificationObserver)
	service := scheduler.NewService(fgClient, catalogCLient, mutualFundClient, phClient, wealthOnBoardingClient, orderCrdb, orderStatusUpdateCrdb, batchOrderProcessingDetailsCrdb, fileStateCrdb, amcInfoCrdb, orchestratorPublisher, genConf, crdbIdempotentTxnExecutor, orderStatusNotifier)
	return service
}

func InitialisePaymentHandlerSvc(db types.EpifiWealthCRDB, orderManagerClient order3.OrderManagerClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, omsClient order.OrderServiceClient, genConf *genconf.Config) *payment_handler2.Service {
	siPaymentExecutor := payment_executor.NewSIPaymentExecutor(recurringPaymentClient, orderManagerClient, genConf)
	p2PTransferExecutor := payment_executor.NewP2PTransferExecutor(orderManagerClient, omsClient, genConf)
	paymentExecutorFactory := payment_handler2.NewPaymentExecutorFactory(siPaymentExecutor, p2PTransferExecutor)
	gormDB := GormProvider(db)
	paymentCrdb := dao3.NewPaymentCrdb(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	service := payment_handler2.NewService(genConf, paymentExecutorFactory, paymentCrdb, mutualFundCrdb, folioLedgerCrdb, amcInfoCrdb, orderManagerClient, crdbIdempotentTxnExecutor)
	return service
}

// config: {"mfCatalogS3Client": "MutualFundCatalogS3Conf().BucketName"}
func InitializeMutualFundCatalogConsumerSvc(conf *config.Config, db types.EpifiWealthCRDB, mfCatalogS3Client types2.MFCatalogS3Client, vgMfClient mutualfund.MutualFundClient, genConf *genconf.Config, redisClient types.InvestmentRedisStore) *consumer6.Service {
	gormDB := GormProvider(db)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	mutualFundCategoryAverageCrdb := impl.NewMutualFundCategoryAverageCrdb(gormDB, domainIdGenerator)
	client := investmentSlackAlertClientProvider(conf)
	catalogFileProcessorFactory := catalog_file_processor.NewCatalogFileProcessorFactory(mfCatalogS3Client, mutualFundCrdb, mutualFundCategoryAverageCrdb, genConf, client)
	recentMutualFundCrdb := impl2.NewRecentMutualFundCrdb(gormDB)
	mutualFundHistoryNavDaoCrdb := impl.NewMutualFundHistoryNavDaoCrdb(gormDB)
	client2 := types.InvestmentRedisStoreRedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	service := consumer6.NewService(catalogFileProcessorFactory, recentMutualFundCrdb, vgMfClient, mutualFundHistoryNavDaoCrdb, mutualFundCrdb, client, redisV9LockManager)
	return service
}

func InitializeMutualFundDBStatesSvc(db types.EpifiWealthCRDB, cfg *genconf.Config, paymentHandlerClient payment_handler.PaymentHandlerClient, dynamicUIElementRedisStore types2.DynamicUIElementCacheRedisStore) *developer.MutualFundDbStatesService {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	mfOrderProcessor := processor2.NewMFOrderProcessor(orderCrdb)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	mfFolioLedgerForActorProcessor := processor2.NewMFFolioLedgerForActorProcessor(folioLedgerCrdb)
	prerequisiteStatusCrdb := dao.NewPrerequisitesCrdb(gormDB)
	mfActorPrerequisiteProcessor := processor2.NewMFActorPrerequisiteProcessor(prerequisiteStatusCrdb)
	entityFileMapperCrdb := dao2.NewEntityFileMapperCrdb(gormDB, domainIdGenerator)
	mfEntityFileMapperProcessor := processor2.NewMFEntityFileMapperProcessor(entityFileMapperCrdb)
	fileGenerationAttemptCrdb := dao2.NewFileGenerationAttemptCrdb(gormDB)
	mfFileGenerationAttemptsProcessor := processor2.NewMFFileGenerationAttemptsProcessor(fileGenerationAttemptCrdb)
	batchOrderProcessingDetailsCrdb := impl.NewBatchOrderProcessingStepDetailsCrdb(gormDB)
	mfBatchOrderProcessingDetailsProcessor := processor2.NewMFBatchOrderProcessingDetailsProcessor(batchOrderProcessingDetailsCrdb)
	batchOrderProcessingStepDetailsCrdb := impl.NewOrderProcessingStepCrdb(gormDB)
	mfBatchOrderProcessingStepDetailsProcessor := processor2.NewMFBatchOrderProcessingStepDetailsProcessor(batchOrderProcessingStepDetailsCrdb)
	paymentCrdb := dao3.NewPaymentCrdb(gormDB)
	mfPaymentProcessor := processor2.NewMFPaymentProcessor(paymentCrdb)
	fileStateCrdb := impl.NewFileStateCrdb(gormDB)
	mfFileStateProcessor := processor2.NewMFFileStateProcessor(fileStateCrdb)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	mutualFundsProcessor := processor2.NewMutualFundsProcessor(mutualFundCrdb)
	amcInfoCrdb := impl.NewAmcInfoCrdb(gormDB, domainIdGenerator)
	amcInfoProcessor := processor2.NewAMCInfoProcessor(amcInfoCrdb)
	orderSummaryProcessor := processor2.NewOrderSummaryProcessor(orderCrdb, prerequisiteStatusCrdb, entityFileMapperCrdb, fileGenerationAttemptCrdb, paymentCrdb, fileStateCrdb, paymentHandlerClient)
	collectionCrdb := impl2.NewCollectionCrdb(gormDB, domainIdGenerator)
	mfCollectionsProcessor := processor2.NewMfCollectionsProcessor(collectionCrdb)
	collectionMutualFundCRDB := impl.NewCollectionMutualFundCRDB(gormDB)
	mfCollectionFundMappingProcessor := processor2.NewMfCollectionFundMappingProcessor(collectionMutualFundCRDB)
	collectionFilterMappingCrdb := impl2.NewCollectionFilterMappingCrdb(gormDB)
	mfCollectionFiltersMappingProcessor := processor2.NewMfCollectionFiltersMappingProcessor(collectionFilterMappingCrdb)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	orderTimelineProcessor := processor2.NewOrderTimelineProcessor(orderStatusUpdateCrdb)
	pastOrdersForActorProcessor := processor2.NewPastOrdersForActorProcessor(orderCrdb)
	filtersWithGroupCrdb := impl2.NewFiltersWithGroupCrdb(gormDB)
	mfFiltersWithGroupProcessor := processor2.NewMfFiltersWithGroupProcessor(filtersWithGroupCrdb)
	fundFilterCrdb := impl2.NewFundFilterCrdb(gormDB)
	mfFiltersProcessor := processor2.NewMfFiltersProcessor(fundFilterCrdb)
	sipLedgerCrdb := impl.NewSIPLedgerCrdb(gormDB)
	mfSipLedgerProcessor := processor2.NewMFSipLedgerProcessor(sipLedgerCrdb)
	mfHoldingsImportRequestTrackerCrdb := impl3.NewMFHoldingsImportRequestTrackerCrdb(gormDB, domainIdGenerator, cfg)
	mfExternalHoldingsImportReqProcessor := processor2.NewMFExternalHoldingsImportReqProcessor(mfHoldingsImportRequestTrackerCrdb)
	dynamicUIElementCacheConfig := DynamicUIElementCacheConfigProvider(cfg)
	client := DynamicUIElementRedisStoreRedisClientProvider(dynamicUIElementRedisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	dynamicUIElementCrdb := dao4.NewDynamicUIElementCrdb(gormDB, domainIdGenerator)
	dynamicUIElementDBDao := dao4.ProvideDynamicUIElementDBDao(dynamicUIElementCrdb)
	dynamicUIElementDaoCache := dao4.NewDynamicUIElementDaoCache(dynamicUIElementCacheConfig, redisCacheStorage, dynamicUIElementDBDao)
	dynamicUIElementDao := dao4.ProvideDynamicUIElementDao(dynamicUIElementDaoCache, dynamicUIElementCacheConfig)
	dynamicUIElementProcessor := processor2.NewDynamicUIElementProcessor(dynamicUIElementDao)
	dynamicUIElementEvaluatorCacheConfig := DynamicUIElementEvaluatorCacheConfigProvider(cfg)
	dynamicUIElementEvaluatorConfigCrdb := dao4.NewDynamicUIElementEvaluatorConfigCrdb(gormDB, domainIdGenerator)
	dynamicUIElementEvaluatorConfigDBDao := dao4.ProvideDynamicUIElementEvaluatorConfigDBDao(dynamicUIElementEvaluatorConfigCrdb)
	dynamicUIElementEvaluatorConfigDaoCache := dao4.NewDynamicUIElementEvaluatorConfigDaoCache(dynamicUIElementEvaluatorCacheConfig, redisCacheStorage, dynamicUIElementEvaluatorConfigDBDao)
	dynamicUIElementEvaluatorConfigDao := dao4.ProvideDynamicUIElementEvaluatorConfigDao(dynamicUIElementEvaluatorConfigDaoCache, dynamicUIElementEvaluatorCacheConfig)
	dynamicUIElementEvaluatorConfigProcessor := processor2.NewDynamicUIElementEvaluatorConfigProcessor(dynamicUIElementEvaluatorConfigDao)
	mfExternalNftRequestPgdb := impl3.NewMFExternalNftRequestPgdb(gormDB)
	mfExternalNftReqProcessor := processor2.NewMfExternalNftReqProcessor(mfExternalNftRequestPgdb)
	mfSingleOtpCasImportRequestPgdb := impl3.NewMfSingleOtpCasImportRequestPgdb(gormDB)
	mfSingleOtpCasImportReqProcessor := processor2.NewMfSingleOtpCasImportReqProcessor(mfSingleOtpCasImportRequestPgdb)
	devFactory := developer.NewDevFactory(mfOrderProcessor, mfFolioLedgerForActorProcessor, mfActorPrerequisiteProcessor, mfEntityFileMapperProcessor, mfFileGenerationAttemptsProcessor, mfBatchOrderProcessingDetailsProcessor, mfBatchOrderProcessingStepDetailsProcessor, mfPaymentProcessor, mfFileStateProcessor, mutualFundsProcessor, amcInfoProcessor, orderSummaryProcessor, mfCollectionsProcessor, mfCollectionFundMappingProcessor, mfCollectionFiltersMappingProcessor, orderTimelineProcessor, pastOrdersForActorProcessor, mfFiltersWithGroupProcessor, mfFiltersProcessor, mfSipLedgerProcessor, mfExternalHoldingsImportReqProcessor, dynamicUIElementProcessor, dynamicUIElementEvaluatorConfigProcessor, mfExternalNftReqProcessor, mfSingleOtpCasImportReqProcessor)
	mutualFundDbStatesService := developer.NewMutualFundDbStatesService(devFactory)
	return mutualFundDbStatesService
}

func InitialiseAuthSvc(db types.EpifiWealthCRDB) *auth3.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	authAttemptCrdb := dao5.NewAuthAttemptCrdb(gormDB, domainIdGenerator)
	service := auth3.NewService(authAttemptCrdb)
	return service
}

func InitializeCatalogSchedulerSvc(db types.EpifiWealthCRDB) *scheduler2.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	mutualFundCategoryAverageCrdb := impl.NewMutualFundCategoryAverageCrdb(gormDB, domainIdGenerator)
	categoryAverageAggregator := catalog_aggregator.NewCategoryAverageAggregator(mutualFundCategoryAverageCrdb)
	percentileRankAggregator := catalog_aggregator.NewPercentileRankAggregator(mutualFundCrdb)
	catalogAggregatorFactory := catalog_aggregator.NewCatalogAggregatorFactory(categoryAverageAggregator, percentileRankAggregator)
	service := scheduler2.NewService(mutualFundCrdb, mutualFundCategoryAverageCrdb, catalogAggregatorFactory)
	return service
}

func InitializeMutualFundNotificationsSvc(cfg *genconf.Config, db types.EpifiWealthCRDB, rmsClient manager.RuleManagerClient, commsClient types2.InvestmentCommsClientWithInterceptors, phClient payment_handler.PaymentHandlerClient, authClient auth.AuthClient, actorClient actor.ActorClient, orderDelayedNotificationPublisher notifications.OrderDelayedNotificationDelayPublisher, deferredNotificationQueuePublisher notifications.DeferredNotificationDelayPublisher, catalogClient catalog.CatalogManagerClient, wealthOnb wealthonboarding.WealthOnboardingClient, userClient user.UsersClient, userGroupClient group.GroupClient, segmentationServiceClient segment.SegmentationServiceClient, dynamicUIElementServiceClient dynamic_ui_element.DynamicUIElementServiceClient) *notifications.Service {
	gormDB := GormProvider(db)
	doOnce := once.NewDoOnce(gormDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	actorSegmentationHelper := actor_segmentation.NewActorSegmentationHelper(folioLedgerCrdb, orderCrdb)
	investmentDigestDynamicElementsGetter := dynamic_elements_getter.NewInvestmentDigestDynamicElementsGetter(actorSegmentationHelper, wealthOnb, cfg, dynamicUIElementServiceClient)
	abFeatureReleaseConfig := ABFeatureReleaseConfigProvider(cfg)
	abEvaluator := GetInvestLandingBannerABEvaluatorProvider(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	investmentLandingDynamicElementsGetter := dynamic_elements_getter.NewInvestmentLandingDynamicElementsGetter(actorSegmentationHelper, cfg, abEvaluator, segmentationServiceClient, dynamicUIElementServiceClient)
	homePromotionDynamicElementsGetter := dynamic_elements_getter.NewHomePromotionDynamicElementsGetter(actorSegmentationHelper, dynamicUIElementServiceClient, cfg)
	standingInstructionFitttAggregator := fittt2.NewStandingInstructionFitttAggregator(rmsClient, phClient)
	homeTopBarAlertDynamicElementsGetter := dynamic_elements_getter.NewHomeTopBarAlertDynamicElementsGetter(cfg, standingInstructionFitttAggregator)
	dynamicElementsGetterFactory := notifications.NewDynamicElementsGetterFactory(investmentDigestDynamicElementsGetter, investmentLandingDynamicElementsGetter, homePromotionDynamicElementsGetter, homeTopBarAlertDynamicElementsGetter)
	watchListFundMappingCrdb := impl2.NewWatchListFundMappingCrdb(gormDB)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	actorNotificationStatusCrdb := impl4.NewActorNotificationStatusCrdb(gormDB, domainIdGenerator)
	addFundsNotificationProcessor := oms_order_update_executor.NewAddFundsNotificationProcessor(doOnce, cfg, actorClient, commsCommsClient)
	debitPushNotificationProcessor := oms_order_update_executor.NewDebitPushNotificationProcessor(commsCommsClient, actorClient, doOnce, cfg, phClient, userClient, crdbIdempotentTxnExecutor)
	paymentFailedPushNotificationProcessor := oms_order_update_executor.NewPaymentFailedPushNotificationProcessor(commsCommsClient, actorClient, doOnce, cfg, phClient, userClient)
	processOmsOrderUpdateFactory := oms_order_update_processor.NewProcessOmsOrderUpdateFactory(addFundsNotificationProcessor, debitPushNotificationProcessor, paymentFailedPushNotificationProcessor)
	service := notifications.NewService(cfg, doOnce, commsCommsClient, phClient, actorClient, orderCrdb, mutualFundCrdb, orderDelayedNotificationPublisher, deferredNotificationQueuePublisher, dynamicElementsGetterFactory, watchListFundMappingCrdb, catalogClient, crdbIdempotentTxnExecutor, actorNotificationStatusCrdb, processOmsOrderUpdateFactory, userClient)
	return service
}

func InitializePaymentHandlerSchedulerSvc(db types.EpifiWealthCRDB, orderManagerClient order3.OrderManagerClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, omsClient order.OrderServiceClient, genConf *genconf.Config) *scheduler3.Service {
	siPaymentExecutor := payment_executor.NewSIPaymentExecutor(recurringPaymentClient, orderManagerClient, genConf)
	p2PTransferExecutor := payment_executor.NewP2PTransferExecutor(orderManagerClient, omsClient, genConf)
	paymentExecutorFactory := payment_handler2.NewPaymentExecutorFactory(siPaymentExecutor, p2PTransferExecutor)
	gormDB := GormProvider(db)
	paymentCrdb := dao3.NewPaymentCrdb(gormDB)
	service := scheduler3.NewService(genConf, paymentExecutorFactory, paymentCrdb)
	return service
}

func InitializeSettlementOrderUpdatesConsumerService(db types.EpifiWealthCRDB, orderManagerClient order3.OrderManagerClient) *consumer7.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	orderStatusUpdateCrdb := impl.NewOrderStatusUpdateCrdb(gormDB, domainIdGenerator)
	service := consumer7.NewService(orderCrdb, orderStatusUpdateCrdb, orderManagerClient)
	return service
}

// config: {"camsS3Client": "Application().FileGenerator().CamsS3Bucket", "karvyS3Client": "Application().FileGenerator().KarvyS3Bucket"}
func InitializeReconciliationSvc(db types.EpifiWealthCRDB, orderManagerClient order3.OrderManagerClient, catalogueManagerClient catalog.CatalogManagerClient, camsS3Client types2.CamsS3Client, karvyS3Client types2.KarvyS3Client, cfg *genconf.Config) *reconciliation.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	camsRTAProcessor := rta_processor.NewCamsRTAProcessor(camsS3Client, orderManagerClient, cfg, orderCrdb)
	karvyRTAProcessor := rta_processor.NewKarvyRTAProcessor(karvyS3Client, orderManagerClient)
	rtaProcessorFactory := rta_processor.NewRTAProcessorFactory(camsRTAProcessor, karvyRTAProcessor)
	service := reconciliation.NewService(rtaProcessorFactory, cfg, orderManagerClient, catalogueManagerClient)
	return service
}

func InitializeInvestmentAggregatorSvc(catalogueManagerClient catalog.CatalogManagerClient, depositClient deposit.DepositClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, redisClient types.InvestmentRedisStore, usStocksPortFolioManagerClient portfolio.PortfolioManagerClient, investmentEventQueuePublisher types2.InvestmentEventPublisher) *aggregator.Service {
	client := types.InvestmentRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	depositRewardTriggerer := rewardtriggerer.NewDepositRewardTriggerer(investmentEventQueuePublisher, depositClient)
	usStocksRewardTriggerer := rewardtriggerer.NewUSStocksRewardTriggerer(investmentEventQueuePublisher)
	factory := rewardtriggerer.NewRewardTriggererFactory(depositRewardTriggerer, usStocksRewardTriggerer)
	service := aggregator.NewService(catalogueManagerClient, depositClient, p2pInvestmentClient, redisCacheStorage, usStocksPortFolioManagerClient, factory)
	return service
}

func InitializeInvestmentAggregatorConsumerSvc(investmentEventQueuePublisher types2.InvestmentEventPublisher, orderManagerClient order3.OrderManagerClient, paymentInstrumentClient paymentinstrument.PiClient, savingsClient savings.SavingsClient, rmsClient manager.RuleManagerClient, depositClient deposit.DepositClient, usStockOrderManagerClient order4.OrderManagerClient) *consumer8.Service {
	mfEventProcessor := instrument_event.NewMFEventProcessor(investmentEventQueuePublisher, orderManagerClient, rmsClient)
	p2PEventProcessor := instrument_event.NewP2PEventProcessor(investmentEventQueuePublisher)
	sdEventProcessor := instrument_event.NewSDEventProcessor(investmentEventQueuePublisher, paymentInstrumentClient, savingsClient, rmsClient, depositClient)
	fdEventProcessor := instrument_event.NewFDEventProcessor(investmentEventQueuePublisher, depositClient)
	usStocksEventProcessor := instrument_event.NewUSStocksEventProcessor(investmentEventQueuePublisher, usStockOrderManagerClient)
	service := consumer8.NewService(mfEventProcessor, p2PEventProcessor, sdEventProcessor, fdEventProcessor, usStocksEventProcessor)
	return service
}

func InitializeMFExternalOrderService(db types.EpifiWealthCRDB, cfg *genconf.Config, holdingsImportedClient holdingsimporter.HoldingImporterClient, wealthOnboardingClient wealthonboarding.WealthOnboardingClient, client mutualfund.MutualFundClient) *external.Service {
	defaultTime := datetime.NewDefaultTime()
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundExternalOrderCrdb := impl3.NewMutualFundExternalOrderCrdb(gormDB, domainIdGenerator)
	mfHoldingsImportRequestTrackerCrdb := impl3.NewMFHoldingsImportRequestTrackerCrdb(gormDB, domainIdGenerator, cfg)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	mutualFundExternalHoldingsSummaryCRDB := impl3.NewMutualFundExternalHoldingsSummaryCrdb(gormDB)
	mfExternalNftRequestPgdb := impl3.NewMFExternalNftRequestPgdb(gormDB)
	uuidGenerator := idgen.NewUuidGenerator()
	emailUpdateProcessor := impl5.NewEmailUpdateProcessor(mfExternalNftRequestPgdb, wealthOnboardingClient, client, defaultTime, uuidGenerator)
	mobileUpdateProcessor := impl5.NewMobileUpdateProcessor(mfExternalNftRequestPgdb, wealthOnboardingClient, client, defaultTime, uuidGenerator)
	nftProcessorFactory := factory.NewNftProcessorFactory(emailUpdateProcessor, mobileUpdateProcessor)
	mfSingleOtpCasImportRequestPgdb := impl3.NewMfSingleOtpCasImportRequestPgdb(gormDB)
	service := external.NewService(cfg, defaultTime, mutualFundExternalOrderCrdb, mfHoldingsImportRequestTrackerCrdb, holdingsImportedClient, crdbIdempotentTxnExecutor, mutualFundExternalHoldingsSummaryCRDB, wealthOnboardingClient, mfExternalNftRequestPgdb, uuidGenerator, nftProcessorFactory, mfSingleOtpCasImportRequestPgdb, client)
	return service
}

func InitializeMFExternalConsumerService(db types.EpifiWealthCRDB, cfg *genconf.Config, rmsClient manager.RuleManagerClient, orderManagerClient order3.OrderManagerClient, investmentEventQueuePublisher types2.InvestmentEventPublisher, holdingImporterClient holdingsimporter.HoldingImporterClient, userClient user.UsersClient, nameCheckClient namecheck.UNNameCheckClient, eventBroker events.Broker, savingsClient savings.SavingsClient, onbClient onboarding.OnboardingClient) *consumer9.MFExternalConsumer {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mfHoldingsImportRequestTrackerCrdb := impl3.NewMFHoldingsImportRequestTrackerCrdb(gormDB, domainIdGenerator, cfg)
	mutualFundExternalOrderCrdb := impl3.NewMutualFundExternalOrderCrdb(gormDB, domainIdGenerator)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	mfEventProcessor := instrument_event.NewMFEventProcessor(investmentEventQueuePublisher, orderManagerClient, rmsClient)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	mutualFundExternalHoldingsSummaryCRDB := impl3.NewMutualFundExternalHoldingsSummaryCrdb(gormDB)
	nameCheckProcessor := pkg.NewInsightNameCheckProcessor(userClient, nameCheckClient)
	mfExternalConsumer := consumer9.NewMFExternalConsumer(cfg, mfHoldingsImportRequestTrackerCrdb, mutualFundExternalOrderCrdb, mutualFundCrdb, mfEventProcessor, holdingImporterClient, crdbIdempotentTxnExecutor, mutualFundExternalHoldingsSummaryCRDB, userClient, nameCheckProcessor, eventBroker, savingsClient, onbClient)
	return mfExternalConsumer
}

func InitializeEventProcessorService(pgdbConns *genconf2.PgdbConns, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], investmentConf *genconf.Config, epifiWealthCRDB types.EpifiWealthCRDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], notificationsPublisher types2.InvestmentEventBasedNotificationsSqsCustomDelayPublisher, commsClient types2.InvestmentCommsClientWithInterceptors, actorClient actor.ActorClient, usStocksAccountManagerClient account.AccountManagerClient, usStocksCatalogManagerClient catalog3.CatalogManagerClient, usStocksOrderManagerClient order4.OrderManagerClient, connectedAccountClient connected_account.ConnectedAccountClient, p2PInvestmentClient p2pinvestment.P2PInvestmentClient, nudgeClient nudge.NudgeServiceClient) *event_processor.Service {
	usStocksEventProcessor := event_processor_plugins.NewUSStocksEventProcessor(investmentConf, usStocksAccountManagerClient, usStocksCatalogManagerClient, usStocksOrderManagerClient, connectedAccountClient)
	userJourneyDAOCrdb := dao6.NewUserJourneyDAOCrdb(dbConnProvider)
	userJourneyDaoPgdb := dao6.NewUserJourneyDaoPgdb(pgdbConns, dbConnProvider)
	userJourneyDAO := dao6.NewUserJourneyDaoProvider(pgdbConns, userJourneyDAOCrdb, userJourneyDaoPgdb)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	db := GormProvider(epifiWealthCRDB)
	doOnce := once.NewDoOnce(db)
	iDbResourceProvider := IDbResourceProviderProvider(txnExecutorProvider)
	eventProcessor := jump.NewEventProcessor(investmentConf, p2PInvestmentClient)
	service := event_processor.NewService(investmentConf, usStocksEventProcessor, userJourneyDAO, notificationsPublisher, commsCommsClient, actorClient, doOnce, iDbResourceProvider, eventProcessor, nudgeClient)
	return service
}

func InitialiseMfStatementConsumerService(db types.EpifiWealthCRDB, usersClient user.UsersClient, docsClient docs.DocsClient, wealthOBClient wealthonboarding.WealthOnboardingClient, commsClient types2.InvestmentCommsClientWithInterceptors, rmsClient manager.RuleManagerClient, investmentConf *genconf.Config) *consumer10.Service {
	gormDB := GormProvider(db)
	mutualFundOrderCRDB := impl.NewMutualFundOrderCRDB(gormDB)
	rtaOrderDataCrdb := impl.NewRTAOrderDataDaoCrdb(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	service := consumer10.NewService(mutualFundOrderCRDB, rtaOrderDataCrdb, rmsClient, mutualFundCrdb, usersClient, docsClient, wealthOBClient, commsCommsClient, investmentConf)
	return service
}

func InitialiseStatementSvc(MfStatementPublisher types2.MfStatementPublisher) *statement.Service {
	service := statement.NewService(MfStatementPublisher)
	return service
}

func InitialiseProfileService(wealthDb types.EpifiWealthCRDB, techDb types.EpifiCRDB, usersClient user.UsersClient, actorClient actor.ActorClient, consentClient consent.ConsentClient, cfg *genconf.Config, eventPublisher types2.NonFinancialEventSqsPublisher) *profile.Service {
	investmentRiskSurveyDaoImpl := survey_impl.NewInvestmentRiskSurveyDaoImpl(techDb)
	investmentRiskProfileDaoImpl := profile_impl.NewInvestmentRiskProfileDaoImpl(wealthDb)
	service := profile.NewService(cfg, investmentRiskSurveyDaoImpl, investmentRiskProfileDaoImpl, usersClient, actorClient, consentClient, eventPublisher)
	return service
}

func InitialiseDynamicUIElementService(db types.EpifiWealthCRDB, cfg *genconf.Config, segmentationServiceClient segment.SegmentationServiceClient, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient, dynamicUIElementRedisStore types2.DynamicUIElementCacheRedisStore) *dynamic_ui_element2.Service {
	dynamicUIElementCacheConfig := DynamicUIElementCacheConfigProvider(cfg)
	client := DynamicUIElementRedisStoreRedisClientProvider(dynamicUIElementRedisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	dynamicUIElementCrdb := dao4.NewDynamicUIElementCrdb(gormDB, domainIdGenerator)
	dynamicUIElementDBDao := dao4.ProvideDynamicUIElementDBDao(dynamicUIElementCrdb)
	dynamicUIElementDaoCache := dao4.NewDynamicUIElementDaoCache(dynamicUIElementCacheConfig, redisCacheStorage, dynamicUIElementDBDao)
	dynamicUIElementDao := dao4.ProvideDynamicUIElementDao(dynamicUIElementDaoCache, dynamicUIElementCacheConfig)
	dynamicUIElementEvaluatorCacheConfig := DynamicUIElementEvaluatorCacheConfigProvider(cfg)
	dynamicUIElementEvaluatorConfigCrdb := dao4.NewDynamicUIElementEvaluatorConfigCrdb(gormDB, domainIdGenerator)
	dynamicUIElementEvaluatorConfigDBDao := dao4.ProvideDynamicUIElementEvaluatorConfigDBDao(dynamicUIElementEvaluatorConfigCrdb)
	dynamicUIElementEvaluatorConfigDaoCache := dao4.NewDynamicUIElementEvaluatorConfigDaoCache(dynamicUIElementEvaluatorCacheConfig, redisCacheStorage, dynamicUIElementEvaluatorConfigDBDao)
	dynamicUIElementEvaluatorConfigDao := dao4.ProvideDynamicUIElementEvaluatorConfigDao(dynamicUIElementEvaluatorConfigDaoCache, dynamicUIElementEvaluatorCacheConfig)
	abFeatureReleaseConfig := ABFeatureReleaseConfigProvider(cfg)
	abEvaluator := GetUSSLandingPageABEvaluatorProvider(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	ussLandingPageABExperimentEvaluator := usstocks.NewUSSLandingPageComponentABExperimentEvaluator(abEvaluator)
	releaseABEvaluator := GetUSSCollectionScreenABEvaluatorProvider(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	ussCollectionScreenABExperimentEvaluator := usstocks.NewUSSCollectionScreenABExperimentEvaluator(releaseABEvaluator)
	abEvaluator2 := GetInvHomeRecommendationABExperimentEvaluator(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	invHomeRecommendationABExperimentEvaluator := investment.NewInvHomeRecommendationABExperimentEvaluator(abEvaluator2)
	abExperimentEvaluatorFactory := dynamic_ui_element2.NewABExperimentEvaluatorFactory(ussLandingPageABExperimentEvaluator, ussCollectionScreenABExperimentEvaluator, invHomeRecommendationABExperimentEvaluator)
	expressionEvaluator := dynamic_ui_element2.NewExpressionEvaluator(segmentationServiceClient, abExperimentEvaluatorFactory)
	service := dynamic_ui_element2.NewService(cfg, dynamicUIElementDao, dynamicUIElementEvaluatorConfigDao, segmentationServiceClient, expressionEvaluator, abExperimentEvaluatorFactory)
	return service
}

func InitialiseNonFinancialEventsConsumerService(eventPublisher types2.NonFinancialEventSnsPublisher) *consumer11.Service {
	riskSurveyStatusProcessor := processor3.NewRiskSurveyStatusProcessor(eventPublisher)
	usStocksAccountProcessor := processor3.NewUSStocksAccountProcessor(eventPublisher)
	service := consumer11.NewService(riskSurveyStatusProcessor, usStocksAccountProcessor)
	return service
}

func InitialiseOrderETAConsumerService(db types.EpifiWealthCRDB, cfg *genconf.Config, orderETADelayPublisher types2.OrderETADelayPublisher, watsonClient watson.WatsonClient, paymentHandlerClient payment_handler.PaymentHandlerClient) *etaHandler.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	mutualFundsOrderEtaProcessor := orderETAProcessor.NewMutualFundsOrderEtaProcessor(cfg, orderCrdb, mutualFundCrdb, watsonClient, paymentHandlerClient, orderETADelayPublisher)
	processOrderETAFactory := etaHandler.NewProcessOrderETAFactory(mutualFundsOrderEtaProcessor)
	service := etaHandler.NewService(cfg, orderCrdb, mutualFundCrdb, processOrderETAFactory, orderETADelayPublisher)
	return service
}

func InitialiseInvestmentWatsonClientService(db types.EpifiWealthCRDB, cfg *genconf.Config) *watson2.Service {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	service := watson2.NewService(orderCrdb, mutualFundCrdb, cfg)
	return service
}

func InitialiseMfFolioService(db types.EpifiWealthCRDB, cfg *genconf.Config) *foliodetails.MfFolioService {
	gormDB := GormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	mfFolioService := foliodetails.NewMfFolioService(cfg, folioLedgerCrdb)
	return mfFolioService
}

func InitializeActivityProcessor(db types.EpifiWealthCRDB, conf *worker.Config, s3Client s3.S3Client, ocrClient inhouseocr.OcrClient, awsConf aws.Config, userClient user.UsersClient, actorClient actor.ActorClient, catalogManagerClient catalog.CatalogManagerClient) *activity.Processor {
	client := HttpClientProvider()
	docHelperImpl := helper.NewDocProofHelperImpl(s3Client, client)
	inhouseOcr := ocr.NewInhouseOcr(ocrClient)
	lambdaClient := LambdaClientProvider(awsConf)
	slackClient := mfAumReconSlackAlertClientProvider(conf)
	gormDB := gormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl.NewMutualFundCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	orderCrdb := impl.NewOrderCrdb(gormDB, domainIdGenerator)
	orderConfirmationInfoCrdb := impl.NewMFOrderConfirmationInfoCrdb(gormDB)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	defaultTime := datetime.NewDefaultTime()
	activityProcessor := activity.NewProcessor(s3Client, ocrClient, conf, docHelperImpl, inhouseOcr, lambdaClient, userClient, actorClient, slackClient, catalogManagerClient, mutualFundCrdb, folioLedgerCrdb, orderCrdb, orderConfirmationInfoCrdb, crdbIdempotentTxnExecutor, defaultTime)
	return activityProcessor
}

// wire.go:

func uploadCreditMISToVendorPublisherProvider(ctx context.Context, gconf *genconf.Config, sqsClient *sqs.Client) (order2.UploadCreditMISToVendorPublisher, error) {

	if cfg.IsProdEnv(gconf.Application().Environment()) {
		return nil, nil
	}
	uploadCreditMISToVendorPublisher, err := sqs2.NewPublisherWithConfig(ctx, gconf.UploadCreditMISToVendorPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		return nil, err
	}
	return uploadCreditMISToVendorPublisher, nil
}

func S3UrlDownloaderProvider() accessors.S3UrlDownloader {
	return accessors.FetchFileFromS3URL
}

func httpClientProvider() *http.Client {
	return &http.Client{}
}

func EmptyAWSConfProvider() []*aws.Config {
	return nil
}

func NewMfCatalogRedisClient(mfCacheRedisStore types2.MfCatalogRedisStore) *redis.Client {
	return mfCacheRedisStore
}

func DynamicUIElementRedisStoreRedisClientProvider(cl types2.DynamicUIElementCacheRedisStore) *redis.Client {
	return cl
}

func DynamicUIElementEvaluatorCacheConfigProvider(cfg2 *genconf.Config) *genconf.DynamicUIElementEvaluatorCacheConfig {
	return cfg2.DynamicUIElementEvaluatorCacheConfig()
}

func DynamicUIElementCacheConfigProvider(cfg2 *genconf.Config) *genconf.DynamicUIElementCacheConfig {
	return cfg2.DynamicUIElementCacheConfig()
}

func IDbResourceProviderProvider(dbConnProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) storagev2.IDbResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func mfAumReconSlackAlertClientProvider(conf *worker.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func investmentSlackAlertClientProvider(conf *config.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.SlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.SlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func gormProvider(db types.EpifiWealthCRDB) *gorm.DB {
	return db
}

func LambdaClientProvider(awsConf aws.Config) lambda.LambdaClient {
	return lambda.NewAwsLambda(awsConf)
}

func HttpClientProvider() *http.Client {
	return &http.Client{}
}
