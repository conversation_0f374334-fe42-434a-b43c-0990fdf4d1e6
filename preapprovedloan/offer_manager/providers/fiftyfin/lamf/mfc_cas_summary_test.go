package lamf_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	structPb "google.golang.org/protobuf/types/known/structpb"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/preapprovedloan"
	fiftyFinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	"github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/gamma/api/vendors/mfcentral"
	config "github.com/epifi/gamma/preapprovedloan/config/worker"
	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/offer_manager/providers/fiftyfin/lamf"
)

func TestMfcCasSummaryOfferManager_CreateOffer(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx                            context.Context
		actorId                        string
		loanOfferEligibilityCriteriaId string
		conf                           *config.Config
	}
	tests := []struct {
		name        string
		args        args
		setupMocks  func(f *mockFields)
		want        *preapprovedloan.LoanOffer
		wantErr     bool
		expectedErr error
	}{
		{
			name: "success - most valid folios, but two are locked due to link with different mobile and email",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
				conf: &config.Config{
					Lamf: &config.Lamf{
						ExperimentConfig: &config.ExperimentConfig{
							ProcessingFeesConfig: &config.ProcessingFeesConfig{
								FirstSegment: &config.ProcessingFeesSegment{
									Enabled:                  true,
									LoanAmountLowerLimit:     25000,
									LoanAmountUpperLimit:     10000000,
									IsPercentage:             true,
									PercentageProcessingFees: "1.25",
									MinProcessingFees:        999,
									MaxProcessingFees:        20000,
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "2",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 300, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 6000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "3",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 1000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "4",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "5",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 10000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "P",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "+911234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "100",
												},
											},
											Folio: "1",
											Isin:  "INF209KB10I9",
										},
										{
											Mobile: "1234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "200",
												},
											},
											Folio: "2",
											Isin:  "INF209KB10I9",
										},
									},
								},
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "XYZ",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "01234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_NumberValue{
													NumberValue: 100,
												},
											},
											Folio: "3",
											Isin:  "INF084M01AG7",
										},
									},
								},
							},
						},
					},
				}, nil)

				f.fiftyfinVgClient.EXPECT().LoanEligibilityCheck(gomock.Any(), &fiftyFinPb.LoanEligibilityCheckRequest{
					Portfolio: map[string]float64{
						"INF209KB10I9": 300,
						"INF084M01AG7": 100,
					},
				}).Return(&fiftyFinPb.LoanEligibilityCheckResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.LoanEligibilityCheckData{
						ApprovedIsin: map[string]*fiftyfin.LoanEligibilityCheckData_ApprovedIsinDetail{
							"INF209KB10I9": {
								Isin:             "INF209KB10I9",
								Price:            300,
								LoanToValueRatio: 0.5,
							},
							"INF084M01AG7": {
								Isin:             "INF084M01AG7",
								Price:            200,
								LoanToValueRatio: 1,
							},
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), "actorId1", []preapprovedloan.LoanProgram{
					preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}).Return(nil, nil)

				f.time.EXPECT().Now().Return(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC))
				f.uuidGenerator.EXPECT().GenerateUuid().Return("testUuid")

				offer := &preapprovedloan.LoanOffer{
					ActorId: "actorId1",
					OfferConstraints: &preapprovedloan.OfferConstraints{
						MaxLoanAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        59800,
							Nanos:        0,
						},
						MinLoanAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        25000,
							Nanos:        0,
						},
						MinTenureMonths: 12,
						MaxTenureMonths: 36,
						AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
							FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
								MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
									ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 30000},
											VendorLtv:             0.5,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.46,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 13800},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "2",
											Isin:                  "INF209KB10I9",
											Quantity:              200,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 60000},
											VendorLtv:             0.5,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.46,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 27600},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "3",
											Isin:                  "INF084M01AG7",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
											VendorLtv:             1,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.92,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 18400},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
									},
									LockedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "4",
											Isin:                  "INF084M01AG7",
											Quantity:              200,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "5",
											Isin:                  "INF084M01AG7",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
										},
									},
									UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              20,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 4000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "2",
											Isin:                  "INF209KB10I9",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
										},
									},
								},
								Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
							},
						},
					},
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						Gst: 18,
						InterestRate: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 10.5,
								},
							},
						},
						ProcessingFee: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
									MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
								},
								MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
									MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
								},
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 1.25,
								},
							},
						},
					},
					LoanOfferEligibilityCriteriaId: "",
					VendorOfferId:                  "testUuid",
					LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
					ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
					Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
				}

				var loanOfferReq *preapprovedloan.LoanOffer
				f.loanOffersDao.EXPECT().Create(gomock.Any(), gomock.AssignableToTypeOf(loanOfferReq)).
					DoAndReturn(func(ctx context.Context, offerParams *preapprovedloan.LoanOffer) (*preapprovedloan.LoanOffer, error) {
						if diff := cmp.Diff(offerParams, offer, protocmp.Transform()); diff != "" {
							return nil, fmt.Errorf("loan offer does not match, got: %s\n want: %s\n diff: %s", offerParams, offer, diff)
						}
						return offer, nil
					})
			},
			want: &preapprovedloan.LoanOffer{
				ActorId: "actorId1",
				OfferConstraints: &preapprovedloan.OfferConstraints{
					MaxLoanAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        59800,
						Nanos:        0,
					},
					MinLoanAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        25000,
						Nanos:        0,
					},
					MinTenureMonths: 12,
					MaxTenureMonths: 36,
					AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
						FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
							MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
								ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 30000},
										VendorLtv:             0.5,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.46,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 13800},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "2",
										Isin:                  "INF209KB10I9",
										Quantity:              200,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 60000},
										VendorLtv:             0.5,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.46,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 27600},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "3",
										Isin:                  "INF084M01AG7",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
										VendorLtv:             1,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.92,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 18400},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
								},
								LockedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "4",
										Isin:                  "INF084M01AG7",
										Quantity:              200,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "5",
										Isin:                  "INF084M01AG7",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
									},
								},
								UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              20,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 4000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "2",
										Isin:                  "INF209KB10I9",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
									},
								},
							},
							Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
						},
					},
				},
				ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
					Gst: 18,
					InterestRate: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 10.5,
							},
						},
					},
					ProcessingFee: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
								MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
							},
							MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
								MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
							},
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 1.25,
							},
						},
					},
				},
				LoanOfferEligibilityCriteriaId: "",
				VendorOfferId:                  "testUuid",
				LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
				ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
				Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
			},
			wantErr:     false,
			expectedErr: nil,
		},
		{
			name: "invalid folios - isin is missing, folio number is missing, has zero units",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "",
							Email:          "",
						},
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 300, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 6000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "3",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 0, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 0, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{},
						},
					},
				}, nil)
			},
			want:        nil,
			wantErr:     true,
			expectedErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "all funds unapproved - demat fund, isin not allowed for lien mark, fifityfin api did not approve",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
				conf: &config.Config{
					Lamf: &config.Lamf{
						ExperimentConfig: &config.ExperimentConfig{
							ProcessingFeesConfig: &config.ProcessingFeesConfig{
								FirstSegment: &config.ProcessingFeesSegment{
									Enabled:                  true,
									LoanAmountLowerLimit:     25000,
									LoanAmountUpperLimit:     10000000,
									IsPercentage:             true,
									PercentageProcessingFees: "1.25",
									MinProcessingFees:        999,
									MaxProcessingFees:        20000,
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 24000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        true,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "2",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 300, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 60000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "3",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 10000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "XYZ",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "1234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "0",
												},
											},
											Folio: "1",
											Isin:  "INF209KB10I9",
										},
										{
											Mobile: "01234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "100",
												},
											},
											Folio: "3",
											Isin:  "INF084M01AG7",
										},
									},
								},
							},
						},
					},
				}, nil)

				f.fiftyfinVgClient.EXPECT().LoanEligibilityCheck(gomock.Any(), &fiftyFinPb.LoanEligibilityCheckRequest{
					Portfolio: map[string]float64{
						"INF084M01AG7": 100,
					},
				}).Return(&fiftyFinPb.LoanEligibilityCheckResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.LoanEligibilityCheckData{
						UnapprovedIsin: map[string]float64{
							"INF209KB10I9": 100,
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), "actorId1", []preapprovedloan.LoanProgram{
					preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}).Return(nil, nil)

				f.time.EXPECT().Now().Return(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC))
				f.uuidGenerator.EXPECT().GenerateUuid().Return("testUuid")

				offer := &preapprovedloan.LoanOffer{
					ActorId: "actorId1",
					OfferConstraints: &preapprovedloan.OfferConstraints{
						MaxLoanAmount:   &money.Money{CurrencyCode: "INR"},
						MinLoanAmount:   &money.Money{CurrencyCode: "INR", Units: 25000},
						MinTenureMonths: 12,
						MaxTenureMonths: 36,
						AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
							FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
								MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
									UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              120,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 24000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_IS_DEMAT,
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "3",
											Isin:                  "INF084M01AG7",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST_IN_LOAN_ELIGIBILITY_CHECK_API,
										},
									},
									LockedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "2",
											Isin:                  "INF209KB10I9",
											Quantity:              300,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 60000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
										},
									},
								},
								Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
							},
						},
					},
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						Gst: 18,
						InterestRate: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 10.5,
								},
							},
						},
						ProcessingFee: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
									MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
								},
								MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
									MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
								},
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 1.25,
								},
							},
						},
					},
					LoanOfferEligibilityCriteriaId: "",
					VendorOfferId:                  "testUuid",
					LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
					ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
					Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
				}

				var loanOfferReq *preapprovedloan.LoanOffer
				f.loanOffersDao.EXPECT().Create(gomock.Any(), gomock.AssignableToTypeOf(loanOfferReq)).
					DoAndReturn(func(ctx context.Context, offerParams *preapprovedloan.LoanOffer) (*preapprovedloan.LoanOffer, error) {
						if diff := cmp.Diff(offerParams, offer, protocmp.Transform()); diff != "" {
							return nil, fmt.Errorf("loan offer does not match, got: %s\n want: %s\n diff: %s", offerParams, offer, diff)
						}
						return offer, nil
					})
			},
			want: &preapprovedloan.LoanOffer{
				ActorId: "actorId1",
				OfferConstraints: &preapprovedloan.OfferConstraints{
					MaxLoanAmount:   &money.Money{CurrencyCode: "INR"},
					MinLoanAmount:   &money.Money{CurrencyCode: "INR", Units: 25000},
					MinTenureMonths: 12,
					MaxTenureMonths: 36,
					AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
						FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
							MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
								UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              120,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 24000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_IS_DEMAT,
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "3",
										Isin:                  "INF084M01AG7",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST_IN_LOAN_ELIGIBILITY_CHECK_API,
									},
								},
								LockedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "2",
										Isin:                  "INF209KB10I9",
										Quantity:              300,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 60000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
									},
								},
							},
							Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
						},
					},
				},
				ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
					Gst: 18,
					InterestRate: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 10.5,
							},
						},
					},
					ProcessingFee: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
								MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
							},
							MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
								MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
							},
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 1.25,
							},
						},
					},
				},
				LoanOfferEligibilityCriteriaId: "",
				VendorOfferId:                  "testUuid",
				LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
				ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
				Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
			},
			wantErr:     true,
			expectedErr: nil,
		},
		{
			name: "failure - failed to convert phone number from cas summary",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "P",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "+91123456",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "100",
												},
											},
											Folio: "1",
											Isin:  "INF209KB10I9",
										},
									},
								},
							},
						},
					},
				}, nil)
			},
			want:        nil,
			wantErr:     true,
			expectedErr: nil,
		},
		{
			name: "failure - failed to convert available units from cas summary",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "P",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "1234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "abc",
												},
											},
											Folio: "1",
											Isin:  "INF209KB10I9",
										},
									},
								},
							},
						},
					},
				}, nil)
			},
			want:        nil,
			wantErr:     true,
			expectedErr: nil,
		},
		{
			name: "success - no folio linked to primary email",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
				conf: &config.Config{
					Lamf: &config.Lamf{
						ExperimentConfig: &config.ExperimentConfig{
							ProcessingFeesConfig: &config.ProcessingFeesConfig{
								FirstSegment: &config.ProcessingFeesSegment{
									Enabled:                  true,
									LoanAmountLowerLimit:     25000,
									LoanAmountUpperLimit:     10000000,
									IsPercentage:             true,
									PercentageProcessingFees: "1.25",
									MinProcessingFees:        999,
									MaxProcessingFees:        20000,
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "P",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "+911234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "100",
												},
											},
											Folio: "1",
											Isin:  "INF209KB10I9",
										},
									},
								},
							},
						},
					},
				}, nil)

				f.fiftyfinVgClient.EXPECT().LoanEligibilityCheck(gomock.Any(), &fiftyFinPb.LoanEligibilityCheckRequest{
					Portfolio: map[string]float64{
						"INF209KB10I9": 100,
					},
				}).Return(&fiftyFinPb.LoanEligibilityCheckResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.LoanEligibilityCheckData{
						ApprovedIsin: map[string]*fiftyfin.LoanEligibilityCheckData_ApprovedIsinDetail{
							"INF209KB10I9": {
								Isin:             "INF209KB10I9",
								Price:            300,
								LoanToValueRatio: 0.5,
							},
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), "actorId1", []preapprovedloan.LoanProgram{
					preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}).Return(nil, nil)

				f.time.EXPECT().Now().Return(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC))
				f.uuidGenerator.EXPECT().GenerateUuid().Return("testUuid")

				offer := &preapprovedloan.LoanOffer{
					ActorId: "actorId1",
					OfferConstraints: &preapprovedloan.OfferConstraints{
						MaxLoanAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        13800,
							Nanos:        0,
						},
						MinLoanAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        25000,
							Nanos:        0,
						},
						MinTenureMonths: 12,
						MaxTenureMonths: 36,
						AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
							FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
								MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
									ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 30000},
											VendorLtv:             0.5,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.46,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 13800},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
									},
									UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              20,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 4000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
										},
									},
								},
								Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
							},
						},
					},
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						Gst: 18,
						InterestRate: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 10.5,
								},
							},
						},
						ProcessingFee: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
									MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
								},
								MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
									MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
								},
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 1.25,
								},
							},
						},
					},
					LoanOfferEligibilityCriteriaId: "",
					VendorOfferId:                  "testUuid",
					LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
					ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
					Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
				}

				var loanOfferReq *preapprovedloan.LoanOffer
				f.loanOffersDao.EXPECT().Create(gomock.Any(), gomock.AssignableToTypeOf(loanOfferReq)).
					DoAndReturn(func(ctx context.Context, offerParams *preapprovedloan.LoanOffer) (*preapprovedloan.LoanOffer, error) {
						if diff := cmp.Diff(offerParams, offer, protocmp.Transform()); diff != "" {
							return nil, fmt.Errorf("loan offer does not match, got: %s\n want: %s\n diff: %s", offerParams, offer, diff)
						}
						return offer, nil
					})
			},
			want: &preapprovedloan.LoanOffer{
				ActorId: "actorId1",
				OfferConstraints: &preapprovedloan.OfferConstraints{
					MaxLoanAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        13800,
						Nanos:        0,
					},
					MinLoanAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        25000,
						Nanos:        0,
					},
					MinTenureMonths: 12,
					MaxTenureMonths: 36,
					AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
						FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
							MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
								ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 30000},
										VendorLtv:             0.5,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.46,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 13800},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
								},
								UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              20,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 4000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
									},
								},
							},
							Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
						},
					},
				},
				ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
					Gst: 18,
					InterestRate: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 10.5,
							},
						},
					},
					ProcessingFee: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
								MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
							},
							MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
								MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
							},
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 1.25,
							},
						},
					},
				},
				LoanOfferEligibilityCriteriaId: "",
				VendorOfferId:                  "testUuid",
				LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
				ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
				Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
			},
			wantErr:     true,
			expectedErr: nil,
		},
		{
			name: "failure - failed to fetch cas summary for phone",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:        nil,
			wantErr:     true,
			expectedErr: nil,
		},
		{
			name: "failure - failed to fetch cas detailed statement",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:        nil,
			wantErr:     true,
			expectedErr: nil,
		},
		{
			name: "success - one folio skipped due to failure in phone number parsing",
			args: args{
				ctx:                            context.Background(),
				actorId:                        "actorId1",
				loanOfferEligibilityCriteriaId: "",
				conf: &config.Config{
					Lamf: &config.Lamf{
						ExperimentConfig: &config.ExperimentConfig{
							ProcessingFeesConfig: &config.ProcessingFeesConfig{
								FirstSegment: &config.ProcessingFeesSegment{
									Enabled:                  true,
									LoanAmountLowerLimit:     25000,
									LoanAmountUpperLimit:     10000000,
									IsPercentage:             true,
									PercentageProcessingFees: "1.25",
									MinProcessingFees:        999,
									MaxProcessingFees:        20000,
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1",
					preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&preapprovedloan.LoanApplicant{
					Id: "id1",
					PersonalDetails: &preapprovedloan.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().GetMFHoldingsSummary(gomock.Any(), &mfExternalPb.GetMFHoldingsSummaryRequest{
					ActorId: "actorId1",
				}).Return(&mfExternalPb.GetMFHoldingsSummaryResponse{
					Status: rpc.StatusOk(),
					MutualFundExternalHoldingsSummaries: []*mfExternalPb.MutualFundExternalHoldingsSummary{
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "1",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 120, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2400, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "P",
							SchemeCode:     "8044",
							Folio:          "2",
							RtaCode:        "Cams",
							ClosingBalance: &money.Money{Units: 300, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 6000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF209KB10I9",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "3",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 1000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "4",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 200, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 2000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "101",
							Folio:          "5",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 10000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "111",
							Folio:          "10",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 10000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
						{
							AmcCode:        "XYZ",
							SchemeCode:     "111",
							Folio:          "11",
							RtaCode:        "Kfintech",
							ClosingBalance: &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							MarketValue:    &money.Money{Units: 10000, Nanos: 0, CurrencyCode: "INR"},
							Nav:            &money.Money{Units: 100, Nanos: 0, CurrencyCode: "INR"},
							IsDemat:        false,
							Isin:           "INF084M01AG7",
							Email:          "<EMAIL>",
						},
					},
				}, nil)

				f.mfExternalOrderClient.EXPECT().FetchAdditionalCasStatement(gomock.Any(), &mfExternalPb.FetchAdditionalCasStatementRequest{
					ActorId: "actorId1",
					ContactIdentifier: &mfExternalPb.FetchAdditionalCasStatementRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
					},
					StatementType: mfExternalPb.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
				}).Return(&mfExternalPb.FetchAdditionalCasStatementResponse{
					Status: rpc.StatusOk(),
					Statement: &mfExternalPb.FetchAdditionalCasStatementResponse_CasSummaryStatement{
						CasSummaryStatement: &mutualfund.CASSummaryRes{
							Data: []*mfcentral.CASSummaryData{
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "P",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "+911234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "100",
												},
											},
											Folio: "1",
											Isin:  "INF209KB10I9",
										},
										{
											Mobile: "1234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_StringValue{
													StringValue: "200",
												},
											},
											Folio: "2",
											Isin:  "INF209KB10I9",
										},
									},
								},
								{
									Summary: []*mfcentral.Summary{
										{
											Amc: "XYZ",
										},
									},
									Schemes: []*mfcentral.Scheme{
										{
											Mobile: "01234567890",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_NumberValue{
													NumberValue: 100,
												},
											},
											Folio: "3",
											Isin:  "INF084M01AG7",
										},
										{
											Mobile: "(+91) 12390",
											Email:  "<EMAIL>",
											AvailableUnits: &structPb.Value{
												Kind: &structPb.Value_NumberValue{
													NumberValue: 100,
												},
											},
											Folio: "10",
											Isin:  "INF084M01AG7",
										},
									},
								},
							},
						},
					},
				}, nil)

				f.fiftyfinVgClient.EXPECT().LoanEligibilityCheck(gomock.Any(), &fiftyFinPb.LoanEligibilityCheckRequest{
					Portfolio: map[string]float64{
						"INF209KB10I9": 300,
						"INF084M01AG7": 100,
					},
				}).Return(&fiftyFinPb.LoanEligibilityCheckResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.LoanEligibilityCheckData{
						ApprovedIsin: map[string]*fiftyfin.LoanEligibilityCheckData_ApprovedIsinDetail{
							"INF209KB10I9": {
								Isin:             "INF209KB10I9",
								Price:            300,
								LoanToValueRatio: 0.5,
							},
							"INF084M01AG7": {
								Isin:             "INF084M01AG7",
								Price:            200,
								LoanToValueRatio: 1,
							},
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), "actorId1", []preapprovedloan.LoanProgram{
					preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}).Return(nil, nil)

				f.time.EXPECT().Now().Return(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC))
				f.uuidGenerator.EXPECT().GenerateUuid().Return("testUuid")

				offer := &preapprovedloan.LoanOffer{
					ActorId: "actorId1",
					OfferConstraints: &preapprovedloan.OfferConstraints{
						MaxLoanAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        59800,
							Nanos:        0,
						},
						MinLoanAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        25000,
							Nanos:        0,
						},
						MinTenureMonths: 12,
						MaxTenureMonths: 36,
						AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
							FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
								MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
									ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 30000},
											VendorLtv:             0.5,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.46,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 13800},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "2",
											Isin:                  "INF209KB10I9",
											Quantity:              200,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 60000},
											VendorLtv:             0.5,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.46,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 27600},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "3",
											Isin:                  "INF084M01AG7",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
											VendorLtv:             1,
											DiscountFactor:        0.92,
											LoanToValueRatio:      0.92,
											MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 18400},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
										},
									},
									LockedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "4",
											Isin:                  "INF084M01AG7",
											Quantity:              200,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "5",
											Isin:                  "INF084M01AG7",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
											AmcCode:               "XYZ",
											SchemeCode:            "101",
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											FolioNumber:           "11",
											Isin:                  "INF084M01AG7",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
											AmcCode:               "XYZ",
											SchemeCode:            "111",
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
										},
									},
									UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "1",
											Isin:                  "INF209KB10I9",
											Quantity:              20,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 4000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
										},
										{
											MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											FolioNumber:           "2",
											Isin:                  "INF209KB10I9",
											Quantity:              100,
											Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
											TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
											AmcCode:               "P",
											SchemeCode:            "8044",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
											Email:                 "<EMAIL>",
											ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
										},
									},
								},
								Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
							},
						},
					},
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						Gst: 18,
						InterestRate: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 10.5,
								},
							},
						},
						ProcessingFee: []*preapprovedloan.RangeData{
							{
								Start: 25000,
								End:   1_00_00_000,
								MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
									MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
								},
								MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
									MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
								},
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 1.25,
								},
							},
						},
					},
					LoanOfferEligibilityCriteriaId: "",
					VendorOfferId:                  "testUuid",
					LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
					ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
					Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
				}

				var loanOfferReq *preapprovedloan.LoanOffer
				f.loanOffersDao.EXPECT().Create(gomock.Any(), gomock.AssignableToTypeOf(loanOfferReq)).
					DoAndReturn(func(ctx context.Context, offerParams *preapprovedloan.LoanOffer) (*preapprovedloan.LoanOffer, error) {
						if diff := cmp.Diff(offerParams, offer, protocmp.Transform()); diff != "" {
							return nil, fmt.Errorf("loan offer does not match, got: %s\n want: %s\n diff: %s", offerParams, offer, diff)
						}
						return offer, nil
					})
			},
			want: &preapprovedloan.LoanOffer{
				ActorId: "actorId1",
				OfferConstraints: &preapprovedloan.OfferConstraints{
					MaxLoanAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        59800,
						Nanos:        0,
					},
					MinLoanAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        25000,
						Nanos:        0,
					},
					MinTenureMonths: 12,
					MaxTenureMonths: 36,
					AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
						FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
							MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
								ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 30000},
										VendorLtv:             0.5,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.46,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 13800},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "2",
										Isin:                  "INF209KB10I9",
										Quantity:              200,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 300},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 60000},
										VendorLtv:             0.5,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.46,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 27600},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "3",
										Isin:                  "INF084M01AG7",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
										VendorLtv:             1,
										DiscountFactor:        0.92,
										LoanToValueRatio:      0.92,
										MaxLoanAmount:         &money.Money{CurrencyCode: "INR", Units: 18400},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
									},
								},
								LockedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "4",
										Isin:                  "INF084M01AG7",
										Quantity:              200,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "5",
										Isin:                  "INF084M01AG7",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
										AmcCode:               "XYZ",
										SchemeCode:            "101",
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
										FolioNumber:           "11",
										Isin:                  "INF084M01AG7",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 100},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 10000},
										AmcCode:               "XYZ",
										SchemeCode:            "111",
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY,
									},
								},
								UnapprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "1",
										Isin:                  "INF209KB10I9",
										Quantity:              20,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 4000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
									},
									{
										MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
										FolioNumber:           "2",
										Isin:                  "INF209KB10I9",
										Quantity:              100,
										Price:                 &money.Money{CurrencyCode: "INR", Units: 200},
										TotalAmount:           &money.Money{CurrencyCode: "INR", Units: 20000},
										AmcCode:               "P",
										SchemeCode:            "8044",
										PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1234567890},
										Email:                 "<EMAIL>",
										ApprovalStatus:        preapprovedloan.MfOfferApprovalStatus_MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO,
									},
								},
							},
							Source: preapprovedloan.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
						},
					},
				},
				ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
					Gst: 18,
					InterestRate: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 10.5,
							},
						},
					},
					ProcessingFee: []*preapprovedloan.RangeData{
						{
							Start: 25000,
							End:   1_00_00_000,
							MaxValue: &preapprovedloan.RangeData_MaxAbsoluteValue{
								MaxAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 20000},
							},
							MinValue: &preapprovedloan.RangeData_MinAbsoluteValue{
								MinAbsoluteValue: &money.Money{CurrencyCode: "INR", Units: 999},
							},
							Value: &preapprovedloan.RangeData_Percentage{
								Percentage: 1.25,
							},
						},
					},
				},
				LoanOfferEligibilityCriteriaId: "",
				VendorOfferId:                  "testUuid",
				LoanProgram:                    preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				ValidSince:                     timestamp.New(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
				ValidTill:                      timestamp.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)),
				Vendor:                         preapprovedloan.Vendor_FIFTYFIN,
			},
			wantErr:     false,
			expectedErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			gconf := genconf.Config{}
			gconf.Init("")
			_ = gconf.Set(tt.args.conf, false, nil)

			s := lamf.NewMfcCasSummaryOfferManager(f.time, f.fiftyfinVgClient, f.loanOffersDao, f.loanApplicantDao, f.loanOfferEligibilityCriteriaDao,
				f.mfExternalOrderClient, f.txnExecutorProvider, f.uuidGenerator, &gconf)
			got, err := s.CreateOffer(tt.args.ctx, tt.args.actorId, tt.args.loanOfferEligibilityCriteriaId)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOffer() error got: %s, want: %s", err, tt.want)
			}
			if tt.wantErr {
				if tt.expectedErr != nil && !errors.Is(err, tt.expectedErr) {
					t.Errorf("CreateOffer() expected error, got= %v, want=%v", err, tt.expectedErr)
				}
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("CreateOffer() response got = %v\n want %v\n diff %v", got, tt.want, diff)
			}
		})
	}
}
