package lamf_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"reflect"
	"testing"

	"github.com/epifi/gamma/preapprovedloan/offer_manager/providers/fiftyfin/lamf"
)

func TestParsePhoneNumber(t *testing.T) {
	t.<PERSON>()
	type args struct {
		phoneNumberStr string
	}
	tests := []struct {
		name    string
		args    args
		want    *commontypes.PhoneNumber
		wantErr bool
	}{
		{
			name: "success - starts with (+91)",
			args: args{
				phoneNumberStr: "(+91)**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - starts with +91-",
			args: args{
				phoneNumberStr: "+91-**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - starts with 091",
			args: args{
				phoneNumberStr: "091**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - starts with +91",
			args: args{
				phoneNumberStr: "+91**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - starts with 0",
			args: args{
				phoneNumberStr: "0**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - starts with 91",
			args: args{
				phoneNumberStr: "91**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - does not have any prefix",
			args: args{
				phoneNumberStr: "**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - trims space and tabs",
			args: args{
				phoneNumberStr: "	  	(+91) 12345 67890 		 ",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			name: "success - phone number is empty",
			args: args{
				phoneNumberStr: "091**********",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
		{
			// This case will give wrong result, but with mfcentral we expect at least the national number will not be less than 10
			name: "starts with (+91) - national number length is less than 10",
			args: args{
				phoneNumberStr: "(+91)123456789",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: 1123456789,
			},
			wantErr: false,
		},
		{
			name: "success - ignore any non numeric character",
			args: args{
				phoneNumberStr: "(+91) 123a a45-67890aa",
			},
			want: &commontypes.PhoneNumber{
				NationalNumber: **********,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := lamf.ParsePhoneNumber(tt.args.phoneNumberStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParsePhoneNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParsePhoneNumber() got = %v, want %v", got, tt.want)
			}
		})
	}
}
