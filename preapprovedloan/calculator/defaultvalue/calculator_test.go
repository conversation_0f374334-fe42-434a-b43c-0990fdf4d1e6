package defaultvalue

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	commonConfig "github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators/provider/basecalculator"
)

//nolint:unparam
func getLoanOffer(vendor palPb.Vendor, maxLoanAmount *moneyPb.Money, minTenureMonths int32, maxTenureMonths int32, maxEmiAmt *moneyPb.Money) *palPb.LoanOffer {
	if maxEmiAmt == nil {
		maxEmiAmt = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        25000,
		}
	}

	return &palPb.LoanOffer{
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount:   maxLoanAmount,
			MaxEmiAmount:    maxEmiAmt,
			MaxTenureMonths: maxTenureMonths,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
			MinTenureMonths:       minTenureMonths,
			AdditionalConstraints: nil,
		},
		Vendor: vendor,
		ProcessingInfo: &palPb.OfferProcessingInfo{
			Gst: 0,
			InterestRate: []*palPb.RangeData{
				{
					Value: &palPb.RangeData_Percentage{Percentage: 13},
				},
			},
			ProcessingFee: nil,
			ApplicationId: "",
		},
	}
}

func getDefaultMaxLoanAmount() *moneyPb.Money {
	return &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        100000,
	}
}

func TestCalculator_GetDefaultPledgeDetails(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name      string
		loanOffer *palPb.LoanOffer
		want      *palPb.PledgeDetails
	}{
		{
			name:      "#1 pledge details should always come nil in base provider",
			loanOffer: getLoanOffer(palPb.Vendor_LIQUILOANS, getDefaultMaxLoanAmount(), 10, 30, nil),
			want:      nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			base := &basecalculator.Calculator{LoanOffer: tt.loanOffer}
			legacyCalculator := calculators.GetCalculator(context.Background(), calculators.GetCalculatorProviderRequest{
				Vendor:      tt.loanOffer.GetVendor(),
				LoanProgram: tt.loanOffer.GetLoanProgram(),
				Offer:       tt.loanOffer,
			}, base)

			c := &Calculator{
				loanOffer:          tt.loanOffer,
				originalCalculator: legacyCalculator,
			}

			opts := []cmp.Option{
				protocmp.Transform(),
			}

			got := c.GetDefaultPledgeDetails()
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetDefaultPledgeDetails() Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}

func TestCalculator_GetDefaultOfferTenureAndLoanAmount(t *testing.T) {
	t.Parallel()

	type fields struct {
		LoanOffer *palPb.LoanOffer
	}
	type args struct {
		tenureInMonths  int32
		loanAmount      *moneyPb.Money
		loanConstraints []*commonConfig.LoanSelectionConstraint
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int32
		want1  *moneyPb.Money
	}{
		{
			name: "#1 tenure and loan amount not selected",
			fields: fields{
				LoanOffer: getLoanOffer(palPb.Vendor_LIQUILOANS, getDefaultMaxLoanAmount(), 12, 60, nil),
			},
			args: args{
				tenureInMonths: 0,
				loanAmount:     nil,
			},
			want: 40,
			want1: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        70000,
			},
		},
		{
			name: "#2 tenure not selected and loan amount selected",
			fields: fields{
				LoanOffer: getLoanOffer(palPb.Vendor_LIQUILOANS, &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500000,
				}, 12, 60, nil),
			},
			args: args{
				tenureInMonths: 0,
				loanAmount:     getDefaultMaxLoanAmount(),
			},
			want:  40,
			want1: getDefaultMaxLoanAmount(),
		},
		{
			name: "#3 tenure not selected and loan amount selected, honouring max EMI as well",
			fields: fields{
				LoanOffer: getLoanOffer(palPb.Vendor_LIQUILOANS, &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500000,
				}, 12, 60, &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        5000,
				}),
			},
			args: args{
				tenureInMonths: 0,
				loanAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
			},
			want:  45,
			want1: getDefaultMaxLoanAmount(),
		},
		{
			name: "#4 tenure and loan amount selected",
			fields: fields{
				LoanOffer: getLoanOffer(palPb.Vendor_LIQUILOANS, &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500000,
				}, 12, 60, nil),
			},
			args: args{
				tenureInMonths: 12,
				loanAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
			},
			want:  12,
			want1: getDefaultMaxLoanAmount(),
		},
		{
			name: "#5 No check on EMI honouring if both contraints are already selected, relying on Slider",
			fields: fields{
				LoanOffer: getLoanOffer(palPb.Vendor_LIQUILOANS, &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500000,
				}, 12, 60, &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        5000,
				}),
			},
			args: args{
				tenureInMonths: 6,
				loanAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
			},
			want:  6,
			want1: getDefaultMaxLoanAmount(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			base := &basecalculator.Calculator{LoanOffer: tt.fields.LoanOffer}
			legacyCalculator := calculators.GetCalculator(context.Background(), calculators.GetCalculatorProviderRequest{
				Vendor:      tt.fields.LoanOffer.GetVendor(),
				LoanProgram: tt.fields.LoanOffer.GetLoanProgram(),
				Offer:       tt.fields.LoanOffer,
			}, base)

			f := &Calculator{
				loanOffer:          tt.fields.LoanOffer,
				originalCalculator: legacyCalculator,
			}
			got, got1, err := f.GetDefaultOfferTenureAndLoanAmount(tt.args.tenureInMonths, tt.args.loanAmount, tt.args.loanConstraints)
			if err != nil {
				t.Errorf("GetDefaultOfferTenureAndLoanAmount() error = %v, wantErr %v", err, nil)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetDefaultOfferTenureAndLoanAmount() Mismatch in tenure got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
			if diff := cmp.Diff(tt.want1, got1, opts...); diff != "" {
				t.Errorf("GetDefaultOfferTenureAndLoanAmount() Mismatch in amount got = %v,\n want = %v \n diff = %v ", got1, tt.want1, diff)
			}
		})
	}
}
