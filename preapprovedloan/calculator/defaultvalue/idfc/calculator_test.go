package idfc

import (
	"reflect"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	commonConfig "github.com/epifi/gamma/preapprovedloan/config/common"
)

func getLoanOffer() *palPb.LoanOffer {
	return &palPb.LoanOffer{
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			},
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        25000,
			},
			MaxTenureMonths: 58,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
			MinTenureMonths:       0,
			AdditionalConstraints: nil,
		},
		ProcessingInfo: &palPb.OfferProcessingInfo{
			Gst: 0,
			InterestRate: []*palPb.RangeData{
				{
					Value: &palPb.RangeData_Percentage{Percentage: 13},
				},
			},
			ProcessingFee: nil,
			ApplicationId: "",
		},
	}
}

func TestIdfcCalculator_GetDefaultOfferTenureAndLoanAmount(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name           string
		tenureInMonths int32
		loanAmount     *moneyPb.Money
		wantTenure     int32
		wantLoanAmount *moneyPb.Money
	}{
		{
			name:           "#1 tenureInMonths selected as 60, and amount not selected",
			tenureInMonths: 60,
			loanAmount:     nil,
			wantTenure:     58,
			wantLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        320000,
			},
		},
		{
			name:           "#2 tenureInMonths and amount not selected",
			tenureInMonths: 0,
			loanAmount:     nil,
			wantTenure:     42,
			wantLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        320000,
			},
		},
		{
			name:           "#3 amount selected and tenure not selected",
			tenureInMonths: 0,
			loanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        80000,
			},
			wantTenure: 16,
			wantLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        80000,
			},
		},
		{
			name:           "#4 amount and tenure selected, tenure came after honouring maxEmi amount",
			tenureInMonths: 12,
			loanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			},
			wantTenure: 23,
			wantLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			},
		},
		{
			name:           "#5 amount and tenure selected, tenure came after honouring constraint logic",
			tenureInMonths: 25,
			loanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
			wantTenure: 24,
			wantLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
		},
		{
			name:           "#5 amount and tenure selected, tenure came after honouring constraint logic",
			tenureInMonths: 60,
			loanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			},
			wantTenure: 58,
			wantLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &IdfcCalculator{
				LoanOffer:  getLoanOffer(),
				Calculator: nil,
			}
			got, got1, err := c.GetDefaultOfferTenureAndLoanAmount(tt.tenureInMonths, tt.loanAmount, []*commonConfig.LoanSelectionConstraint{
				{
					MinTenure:     6,
					MaxTenure:     24,
					MinLoanAmount: 50000,
					MaxLoanAmount: 100000,
				},
				{
					MinTenure:     12,
					MaxTenure:     48,
					MinLoanAmount: 100001,
					MaxLoanAmount: 200000,
				},
				{
					MinTenure:     18,
					MaxTenure:     60,
					MinLoanAmount: 200001,
					MaxLoanAmount: 500000,
				},
			})
			if err != nil {
				t.Errorf("GetDefaultOfferTenureAndLoanAmount() error = %v", err)
				return
			}
			if got != tt.wantTenure {
				t.Errorf("GetDefaultOfferTenureAndLoanAmount() got = %v, want %v", got, tt.wantTenure)
			}
			if !reflect.DeepEqual(got1, tt.wantLoanAmount) {
				t.Errorf("GetDefaultOfferTenureAndLoanAmount() got1 = %v, want %v", got1, tt.wantLoanAmount)
			}
		})
	}
}
