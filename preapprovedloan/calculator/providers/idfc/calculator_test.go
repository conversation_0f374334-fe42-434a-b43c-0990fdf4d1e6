package idfc

import (
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/calculator/types"
	commonConfig "github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators/provider/basecalculator"
)

func getLoanOffer() *palPb.LoanOffer {
	return &palPb.LoanOffer{
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			},
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        25000,
			},
			MaxTenureMonths: 58,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
			MinTenureMonths:       0,
			AdditionalConstraints: nil,
		},
		ProcessingInfo: &palPb.OfferProcessingInfo{
			Gst: 0,
			InterestRate: []*palPb.RangeData{
				{
					Value: &palPb.RangeData_Percentage{Percentage: 13},
				},
			},
			ProcessingFee: nil,
			ApplicationId: "",
		},
	}
}

func TestCalculator_GetSliderMinTenureInMonths(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		req  *types.Request
		want int32
	}{
		{
			name: "#1 contraint - 50k case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			}, 0, nil, getLoanOffer(), nil),
			want: 6,
		},
		{
			name: "#2 contraint - 1L case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100000,
			}, 0, nil, getLoanOffer(), nil),
			want: 6,
		},
		{
			name: "#3 contraint - 1L one rupee case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100001,
			}, 0, nil, getLoanOffer(), nil),
			want: 12,
		},
		{
			name: "#4 contraint - 2L case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        200000,
			}, 0, nil, getLoanOffer(), nil),
			want: 12,
		},
		{
			name: "#5 Contraint - 2L one rupee case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        200001,
			}, 0, nil, getLoanOffer(), nil),
			want: 18,
		},
		{
			name: "#6 Contraint - 5L case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			}, 0, nil, getLoanOffer(), nil),
			want: 23,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			loanOffer := getLoanOffer()
			c := &Calculator{
				req:                    tt.req,
				originalBaseCalculator: &basecalculator.Calculator{LoanOffer: loanOffer},
				loanConstraintConfig: []*commonConfig.LoanSelectionConstraint{
					{
						MinTenure:     6,
						MaxTenure:     24,
						MinLoanAmount: 50000,
						MaxLoanAmount: 100000,
					},
					{
						MinTenure:     12,
						MaxTenure:     48,
						MinLoanAmount: 100001,
						MaxLoanAmount: 200000,
					},
					{
						MinTenure:     18,
						MaxTenure:     60,
						MinLoanAmount: 200001,
						MaxLoanAmount: 500000,
					},
				},
			}
			if got := c.GetSliderMinTenureInMonths(); got != tt.want {
				t.Errorf("GetSliderMinTenureInMonths() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCalculator_GetSliderMaxTenureInMonths(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		req  *types.Request
		want int32
	}{
		{
			name: "#1 contraint - 50k case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			}, 0, nil, getLoanOffer(), nil),
			want: 24,
		},
		{
			name: "#2 contraint - 1L case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100000,
			}, 0, nil, getLoanOffer(), nil),
			want: 24,
		},
		{
			name: "#3 contraint - 1L one rupee case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100001,
			}, 0, nil, getLoanOffer(), nil),
			want: 48,
		},
		{
			name: "#4 contraint - 2L case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        200000,
			}, 0, nil, getLoanOffer(), nil),
			want: 48,
		},
		{
			name: "#5 Contraint - 2L one rupee case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        200001,
			}, 0, nil, getLoanOffer(), nil),
			want: 58,
		},
		{
			name: "#6 Contraint - 5L case",
			req: types.NewRequest(0, &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500000,
			}, 0, nil, getLoanOffer(), nil),
			want: 58,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			loanOffer := getLoanOffer()
			c := &Calculator{
				req:                    tt.req,
				originalBaseCalculator: &basecalculator.Calculator{LoanOffer: loanOffer},
				loanConstraintConfig: []*commonConfig.LoanSelectionConstraint{
					{
						MinTenure:     6,
						MaxTenure:     24,
						MinLoanAmount: 50000,
						MaxLoanAmount: 100000,
					},
					{
						MinTenure:     12,
						MaxTenure:     48,
						MinLoanAmount: 100001,
						MaxLoanAmount: 200000,
					},
					{
						MinTenure:     18,
						MaxTenure:     60,
						MinLoanAmount: 200001,
						MaxLoanAmount: 500000,
					},
				},
			}
			if got := c.GetSliderMaxTenureInMonths(); got != tt.want {
				t.Errorf("GetSliderMinTenureInMonths() = %v, want %v", got, tt.want)
			}
		})
	}
}
