package fiftyfin_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	activityPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans/activity"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

// nolint:funlen
func getConfirmPfAccountScreen(lrId string, lseId string, number *commontypes.PhoneNumber, email string, pan string) *deeplink.Deeplink {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOAN_ACCOUNT_DETAILS_UPDATE_USER_INPUT_SCREEN, &palTypesPb.LamfPfDetailsUpdateScreen{
		LoanHeader: &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      palFeEnumsPb.Vendor_FIFTYFIN,
		},
		PageTitle: &uiPb.VerticalKeyValuePair{
			Title: &uiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetPlainStringText("Fetch your portfolio").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#313234"),
				},
			},
			Value: &uiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetPlainStringText("Enter your phone number & email ID linked to your Mutual Fund investments").WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#929599"),
				},
			},
			HAlignment:                   uiPb.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
			VerticalPaddingBtwTitleValue: 12,
		},
		DisclosureTextV2: uiPb.NewITC().WithTexts(
			commontypes.GetPlainStringText("This does not impact your credit score").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#2D5E6E"),
		).WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/card.png", 16, 16).WithLeftImagePadding(4).WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
			BgColor:       "#E4F1F5",
			CornerRadius:  10,
			LeftPadding:   16,
			RightPadding:  16,
			TopPadding:    6,
			BottomPadding: 6,
		}),
		PanField: &palTypesPb.LamfPfDetailsUpdateScreen_TextField{
			Label:    commontypes.GetTextFromStringFontColourFontStyle("PAN", "#B2B5B9", commontypes.FontStyle_OVERLINE_XS_CAPS),
			Value:    commontypes.GetTextFromStringFontColourFontStyle(pan, "#313234", commontypes.FontStyle_HEADLINE_L),
			Editable: false,
		},
		PhoneNumber: &palTypesPb.LamfPfDetailsUpdateScreen_PhoneNumberField{
			Label:           commontypes.GetPlainStringText("MOBILE NUMBER").WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS).WithBgColor("#B2B5B9"),
			Value:           number,
			PlaceholderText: commontypes.GetPlainStringText("Your mobile number").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#B2B5B9"),
			HelperText:      commontypes.GetPlainStringText("Ensure your Mutual Funds are linked to this number").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#9DA1A4"),
		},
		Email: &palTypesPb.LamfPfDetailsUpdateScreen_EmailField{
			Label:           commontypes.GetPlainStringText("EMAIL ID").WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS).WithBgColor("#B2B5B9"),
			Value:           commontypes.GetPlainStringText(email).WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#313234"),
			PlaceholderText: commontypes.GetPlainStringText("Your email ID").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#B2B5B9"),
			HelperText:      commontypes.GetPlainStringText("Ensure your Mutual Funds are linked to this email ID").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#9DA1A4"),
		},
		Cta: &deeplink.Button{
			Text: helper.GetText("Continue", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			Padding: &deeplink.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Cta: &deeplink.Cta{
				Text:         "Continue",
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
			},
		},
		TermInfos: []*deeplink.TermInfo{
			{
				TermText:             helper.GetTextWithHtml(fmt.Sprintf("I agree to <a href=\"%s\" style=\"color: #00B899\">EpiFi Tech LAMF T&Cs</a>\n</font>", "https://dza2kd7rioahk.cloudfront.net/assets/pdf/lamf-tnc.pdf"), "#6A6D70", commontypes.FontStyle_BODY_4),
				IsTermClickable:      true,
				IsTermLinkUnderlined: false,
				TermCta: &deeplink.Cta{
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
						ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
							ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
								ExternalUrl: "https://dza2kd7rioahk.cloudfront.net/assets/pdf/lamf-tnc.pdf",
							},
						},
					},
				},
			},
			{
				TermText: commontypes.GetHtmlText(fmt.Sprintf(
					"I agree to <a href=\"%s\" style=\"color: #00B899\">MF Central T&Cs</a></font>", "https://app.mfcentral.com/links/terms-of-use",
				)).WithFontColor("#929599").WithFontStyle(commontypes.FontStyle_BODY_4),
				IsTermClickable: true,
				TermCta: &deeplink.Cta{
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
						ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
							ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
								ExternalUrl: "https://app.mfcentral.com/links/terms-of-use",
							},
						},
					},
				},
			},
			{
				TermText: commontypes.GetHtmlText(fmt.Sprintf(
					"I agree to <a href=\"%s\" style=\"color: #00B899\">Epifi Wealth T&Cs</a></font> and authorize it to fetch and share my portfolio data with Epifi Tech to provide a loan offer", "https://fi.money/wealth/TnC",
				)).WithFontColor("#929599").WithFontStyle(commontypes.FontStyle_BODY_4),
				IsTermClickable: true,
				TermCta: &deeplink.Cta{
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
						ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
							ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
								ExternalUrl: "https://fi.money/wealth/TnC",
							},
						},
					},
				},
			},
		},
		LoanStepExecutionId: lseId,
		LoanRequestId:       lrId,
	})
	if dlErr != nil {
		return &deeplink.Deeplink{Screen: deeplink.Screen_LOAN_ACCOUNT_DETAILS_UPDATE_USER_INPUT_SCREEN}
	}
	return dl
}

func TestProcessor_SetUserDetailsScreen(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *activityPb.ActivityRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *mockFields)
		want       *activityPb.ActivityResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "success - get user details from user service",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil).Times(2)

				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				f.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: nil,
						EmailId:     "",
					},
				}, nil)

				dl := getConfirmPfAccountScreen("loanReqId1", "lseId1", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, "<EMAIL>", "**********")
				f.loanRequestDao.EXPECT().Update(gomock.Any(), &palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					NextAction:  dl,
				}, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).Return(nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "success - get user details from loan applicant",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil).Times(2)

				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				f.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				dl := getConfirmPfAccountScreen("loanReqId1", "lseId1", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, "<EMAIL>", "**********")
				f.loanRequestDao.EXPECT().Update(gomock.Any(), &palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					NextAction:  dl,
				}, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).Return(nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "success - get user details from loan applicant and empty details from user service",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil).Times(2)

				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				f.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						EmailId: "",
					},
				}, nil)

				dl := getConfirmPfAccountScreen("loanReqId1", "lseId1", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, "<EMAIL>", "**********")
				f.loanRequestDao.EXPECT().Update(gomock.Any(), &palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					NextAction:  dl,
				}, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).Return(nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initMocks(ctrl)
			tt.setupMocks(f)

			p := NewActivityProcessorWithMocks(f)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *activityPb.ActivityResponse
			got, err := env.ExecuteActivity(palNs.SetUserDetailsScreen, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("SetUserDetailsScreen() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("SetUserDetailsScreen() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("SetUserDetailsScreen() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("SetUserDetailsScreen() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}

func TestProcessor_FiftyfinUpdateUserDetailsInLoanApplicant(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *activityPb.ActivityRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *mockFields)
		want       *activityPb.ActivityResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "success - update old user details",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
								Email:       "<EMAIL>",
							},
						},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)

				f.loanApplicantDao.EXPECT().Update(gomock.Any(), &palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, []palPb.LoanApplicantFieldMask{palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS}).Return(nil)

				f.loanOfferDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), "actorId1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_LAMF}).Return([]*palPb.LoanOffer{
					{
						Id:      "offerId1",
						ActorId: "actorId1",
						Vendor:  palPb.Vendor_FIFTYFIN,
					},
				}, nil)

				f.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), "offerId1").Return(nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "success - new details are same",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
								Email:       "<EMAIL>",
							},
						},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "success - email to be inferred - update only phone",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
								Email:       "",
							},
						},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					Details: &palPb.LoanRequestDetails{
						Details: &palPb.LoanRequestDetails_PortfolioFetchDetails{
							PortfolioFetchDetails: &palPb.PortfolioFetchDetails{
								Details: &palPb.PortfolioFetchDetails_FiftyfinLamfDetails{
									FiftyfinLamfDetails: &palPb.FiftyfinLamfPortfolioFetchDetails{
										SkipAccountDetailUpdate:     false,
										FetchMfcPortfolio:           true,
										FetchFiftyfinPortfolio:      false,
										FetchMfcCasSummaryPortfolio: true,
										UserEmailInputInferred:      true,
									},
								},
							},
						},
					},
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)

				f.loanApplicantDao.EXPECT().Update(gomock.Any(), &palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, []palPb.LoanApplicantFieldMask{palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS}).Return(nil)

				f.loanOfferDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), "actorId1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_LAMF}).Return([]*palPb.LoanOffer{
					{
						Id:      "offerId1",
						ActorId: "actorId1",
						Vendor:  palPb.Vendor_FIFTYFIN,
					},
				}, nil)

				f.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), "offerId1").Return(nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "success - email to be inferred and phone is same - no update",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
								Email:       "",
							},
						},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					Details: &palPb.LoanRequestDetails{
						Details: &palPb.LoanRequestDetails_PortfolioFetchDetails{
							PortfolioFetchDetails: &palPb.PortfolioFetchDetails{
								Details: &palPb.PortfolioFetchDetails_FiftyfinLamfDetails{
									FiftyfinLamfDetails: &palPb.FiftyfinLamfPortfolioFetchDetails{
										SkipAccountDetailUpdate:     false,
										FetchMfcPortfolio:           true,
										FetchFiftyfinPortfolio:      false,
										FetchMfcCasSummaryPortfolio: true,
										UserEmailInputInferred:      true,
									},
								},
							},
						},
					},
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
			},
			want:      &activityPb.ActivityResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - lse does not have user details",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								PhoneNumber: nil,
								Email:       "<EMAIL>",
							},
						},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failure - updating user details in loan applicant",
			args: args{
				ctx: context.Background(),
				req: &activityPb.ActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
						GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			setupMocks: func(f *mockFields) {
				f.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
								Email:       "<EMAIL>",
							},
						},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_UNSPECIFIED,
				}, nil)

				f.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), "orchId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)

				f.loanApplicantDao.EXPECT().Update(gomock.Any(), &palPb.LoanApplicant{
					Id: "loanApplicantId1",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, []palPb.LoanApplicantFieldMask{palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS}).Return(fmt.Errorf("failure"))
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initMocks(ctrl)
			tt.setupMocks(f)

			p := NewActivityProcessorWithMocks(f)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *activityPb.ActivityResponse
			got, err := env.ExecuteActivity(palNs.FiftyfinUpdateUserDetailsInLoanApplicant, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("FiftyfinUpdateUserDetailsInLoanApplicant() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("FiftyfinUpdateUserDetailsInLoanApplicant() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("FiftyfinUpdateUserDetailsInLoanApplicant() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("FiftyfinUpdateUserDetailsInLoanApplicant() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}
