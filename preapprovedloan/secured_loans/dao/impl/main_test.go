package impl_test

import (
	"flag"
	"os"
	"testing"

	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/preapprovedloan/test"

	"gorm.io/gorm"
)

type DaoTestSuite struct {
	dbNameToDb         map[string]*gorm.DB
	dbResourceProvider *storage.DBResourceProvider[*gorm.DB]
}

var (
	daoTestSuite DaoTestSuite
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	conf, _, teardown := test.InitTestServer()
	dbConnProvider, _, dbNameToDb, releaseDbFunc := test.InitDbConns(conf, true, false)

	daoTestSuite = DaoTestSuite{
		dbNameToDb:         dbNameToDb,
		dbResourceProvider: dbConnProvider,
	}
	exitCode := m.Run()

	releaseDbFunc()
	teardown()

	os.Exit(exitCode)
}
