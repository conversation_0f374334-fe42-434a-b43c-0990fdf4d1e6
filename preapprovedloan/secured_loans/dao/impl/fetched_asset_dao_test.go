package impl_test

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	test "github.com/epifi/be-common/pkg/test/v2"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/dao/filters"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/dao/impl"
)

var (
	sampleAsset1 = &preapprovedloanPb.FetchedAsset{
		Id:                  "b0666f90-61f1-11ee-8c99-0242ac120002",
		ActorId:             "actor-id-1",
		Vendor:              preapprovedloanPb.Vendor_FEDERAL,
		AssetType:           preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       "asset-id-1",
		UserAssetIdentifier: "PAN-2",
	}
	sampleAsset2 = &preapprovedloanPb.FetchedAsset{
		Id:                  "7c812554-6218-11ee-8c99-0242ac120002",
		ActorId:             "actor-id-1",
		Vendor:              preapprovedloanPb.Vendor_FEDERAL,
		AssetType:           preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       "asset-id-2",
		UserAssetIdentifier: "PAN-2",
	}
	sampleAsset3 = &preapprovedloanPb.FetchedAsset{
		Id:                  "7c812554-6218-11ee-8c99-0242ac120002",
		ActorId:             "actor-id-1",
		Vendor:              preapprovedloanPb.Vendor_FEDERAL,
		AssetType:           preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       "asset-id-1",
		UserAssetIdentifier: "PAN-1",
	}
	sampleAsset4 = &preapprovedloanPb.FetchedAsset{
		Id:                  "4de1a6c6-621b-11ee-8c99-0242ac120002",
		ActorId:             "actor-id-1",
		Vendor:              preapprovedloanPb.Vendor_FEDERAL,
		AssetType:           preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       "asset-id-10",
		UserAssetIdentifier: "PAN-1",
	}
	sampleAsset5 = &preapprovedloanPb.FetchedAsset{
		Id:                  "7c812554-6218-11ee-8c99-0242ac120002",
		ActorId:             "actor-id-1",
		Vendor:              preapprovedloanPb.Vendor_FEDERAL,
		AssetType:           preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       "asset-id-2",
		UserAssetIdentifier: "PAN-2",
		DeletedAt:           timestamppb.New(time.Now()),
	}
)

func TestFetchedAssetDao_Create(t *testing.T) {
	type args struct {
		ctx   context.Context
		asset *preapprovedloanPb.FetchedAsset
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.FetchedAsset
		wantErr bool
		err     error
	}{
		{
			name: "Successful Creation Flow",
			args: args{
				ctx:   context.Background(),
				asset: sampleAsset1,
			},
			want:    sampleAsset1,
			wantErr: false,
		},
		// todo (Shivansh) :  fix and uncomment the test
		// {
		// 	name: "Failed, duplicate entry",
		// 	args: args{
		// 		ctx:   context.Background(),
		// 		asset: sampleAsset4,
		// 	},
		// 	wantErr: false,
		// 	want:    sampleAsset4,
		// },
	}
	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, []string{"fetched_assets"})
	c := impl.NewFetchedAssetDao(daoTestSuite.dbResourceProvider)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.Create(tt.args.ctx, tt.args.asset, filters.WithOnConflictClause())
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.err)
				}
			}
			if err == nil {
				got.CreatedAt = nil
				got.UpdatedAt = nil
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Create() got = %v,\n want %v", got, tt.want)
				}
			}
		})
	}
}

func TestFetchedAssetDao_Update(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		asset       *preapprovedloanPb.FetchedAsset
		updateMasks []preapprovedloanPb.FetchedAssetFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		err     error
	}{
		{
			name: "Successful Update Flow",
			args: args{
				ctx:         context.Background(),
				asset:       sampleAsset2,
				updateMasks: []preapprovedloanPb.FetchedAssetFieldMask{preapprovedloanPb.FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_USER_ASSET_IDENTIFIER, preapprovedloanPb.FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_VENDOR_ASSET_ID},
			},
			wantErr: false,
		},
		{
			name: "Fail, primary id empty",
			args: args{
				ctx:         context.Background(),
				asset:       &preapprovedloanPb.FetchedAsset{Id: ""},
				updateMasks: []preapprovedloanPb.FetchedAssetFieldMask{preapprovedloanPb.FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_USER_ASSET_IDENTIFIER, preapprovedloanPb.FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_VENDOR_ASSET_ID},
			},
			wantErr: true,
		},
		{
			name: "Fail, field masks empty",
			args: args{
				ctx:         context.Background(),
				asset:       sampleAsset2,
				updateMasks: []preapprovedloanPb.FetchedAssetFieldMask{},
			},
			wantErr: true,
		},
		{
			name: "Fail, no rows affected",
			args: args{
				ctx:         context.Background(),
				asset:       sampleAsset4,
				updateMasks: []preapprovedloanPb.FetchedAssetFieldMask{},
			},
			wantErr: true,
		},
		{
			name: "success, mark asset deleted",
			args: args{
				ctx:         context.Background(),
				asset:       sampleAsset5,
				updateMasks: []preapprovedloanPb.FetchedAssetFieldMask{preapprovedloanPb.FetchedAssetFieldMask_FETCHED_ASSET_FIELD_MASK_DELETED_AT},
			},
			wantErr: false,
		},
	}
	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, []string{})
	c := impl.NewFetchedAssetDao(daoTestSuite.dbResourceProvider)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := c.Update(tt.args.ctx, tt.args.asset, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestFetchedAssetDao_GetById(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.FetchedAsset
		wantErr bool
		err     error
	}{
		{
			name: "Successful Flow",
			args: args{
				ctx: context.Background(),
				id:  sampleAsset3.GetId(),
			},
			wantErr: false,
			want:    sampleAsset3,
		},
		{
			name: "Failure flow",
			args: args{
				ctx: context.Background(),
				id:  sampleAsset4.GetId(),
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "Failure flow, empty id",
			args: args{
				ctx: context.Background(),
				id:  "",
			},
			wantErr: true,
		},
	}
	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, []string{})
	c := impl.NewFetchedAssetDao(daoTestSuite.dbResourceProvider)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil {
				got.CreatedAt = nil
				got.UpdatedAt = nil
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetById() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestFetchedAssetDao_GetByActorId(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
		filters []storageV2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.FetchedAsset
		wantErr bool
		err     error
	}{
		{
			name: "Successful Flow",
			args: args{
				ctx:     context.Background(),
				actorId: sampleAsset3.GetActorId(),
			},
			wantErr: false,
			want:    sampleAsset3,
		},
		{
			name: "successful flow, GetByActorIdAndAssetId",
			args: args{
				ctx:     context.Background(),
				actorId: sampleAsset3.GetActorId(),
				filters: []storageV2.FilterOption{filters.WithAssetType(preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS)},
			},
			wantErr: false,
			want:    sampleAsset3,
		},
		{
			name: "successful flow, GetByActorIdAndVendor",
			args: args{
				ctx:     context.Background(),
				actorId: sampleAsset3.GetActorId(),
				filters: []storageV2.FilterOption{filters.WithVendor(preapprovedloanPb.Vendor_FEDERAL)},
			},
			wantErr: false,
			want:    sampleAsset3,
		},
		{
			name: "successful flow, GetByActorIdVendorAndAssetType",
			args: args{
				ctx:     context.Background(),
				actorId: sampleAsset3.GetActorId(),
				filters: []storageV2.FilterOption{filters.WithVendor(preapprovedloanPb.Vendor_FEDERAL), filters.WithAssetType(preapprovedloanPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS)},
			},
			wantErr: false,
			want:    sampleAsset3,
		},
		{
			name: "Failure flow",
			args: args{
				ctx:     context.Background(),
				actorId: "invalid-actor-id",
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, []string{})
	c := impl.NewFetchedAssetDao(daoTestSuite.dbResourceProvider)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByActorId(tt.args.ctx, tt.args.actorId, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil {
				got.CreatedAt = nil
				got.UpdatedAt = nil
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByActorId() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}
