package finflux_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/preapprovedloan"
	finflux2 "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux/mocks"
	mock_dao "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/prepay/providers/finflux"
)

var loanAccount = preapprovedloan.LoanAccount{
	Id:               "la-id-1",
	LmsPartnerLoanId: "lms-id-1",
}

func TestFinfluxDataProvider_FetchLoanScheduleFromVendor(t *testing.T) {
	t.<PERSON>llel()
	var paymentAmount = money.Money{
		Units: 300,
	}
	var lessPayment = money.Money{
		Units: 200,
	}
	var greaterPayment = money.Money{
		Units: 400,
	}
	var eqPayment = money.Money{
		Units: 300,
	}
	type args struct {
		ctx           context.Context
		loanAccountId string
		paymentAmount *money.Money
	}
	tests := []struct {
		name         string
		mockVgClient func(client *mocks.MockFinfluxClient)
		mockLADao    func(mockDao *mock_dao.MockLoanAccountsDao)
		args         args
		wantRes      bool
	}{
		{
			name: "should return true ie we can foreclose loan account if payment amount is greater than net foreclose amount",
			args: args{
				ctx:           context.Background(),
				loanAccountId: loanAccount.GetId(),
				paymentAmount: &paymentAmount,
			},
			mockLADao: func(mockDao *mock_dao.MockLoanAccountsDao) {
				mockDao.EXPECT().GetById(gomock.Any(), loanAccount.GetId()).Return(&loanAccount, nil)
			},
			mockVgClient: func(client *mocks.MockFinfluxClient) {
				client.EXPECT().GetForeclosureDetails(gomock.Any(), gomock.Any()).Return(&finflux2.GetForeclosureDetailsResponse{
					Status: rpc.StatusOk(),
					Amount: &lessPayment,
				}, nil)
			},
			wantRes: true,
		},
		{
			name: "should return true ie we can foreclsoe loan account if payment amount is equal than net foreclose amount",
			args: args{
				ctx:           context.Background(),
				loanAccountId: loanAccount.GetId(),
				paymentAmount: &paymentAmount,
			},
			mockLADao: func(mockDao *mock_dao.MockLoanAccountsDao) {
				mockDao.EXPECT().GetById(gomock.Any(), loanAccount.GetId()).Return(&loanAccount, nil)
			},
			mockVgClient: func(client *mocks.MockFinfluxClient) {
				client.EXPECT().GetForeclosureDetails(gomock.Any(), gomock.Any()).Return(&finflux2.GetForeclosureDetailsResponse{
					Status: rpc.StatusOk(),
					Amount: &eqPayment,
				}, nil)
			},
			wantRes: true,
		},
		{
			name: "should return false ie we cant foreclsoe loan account if payment amount is less than net foreclose amount",
			args: args{
				ctx:           context.Background(),
				loanAccountId: loanAccount.GetId(),
				paymentAmount: &paymentAmount,
			},
			mockLADao: func(mockDao *mock_dao.MockLoanAccountsDao) {
				mockDao.EXPECT().GetById(gomock.Any(), loanAccount.GetId()).Return(&loanAccount, nil)
			},
			mockVgClient: func(client *mocks.MockFinfluxClient) {
				client.EXPECT().GetForeclosureDetails(gomock.Any(), gomock.Any()).Return(&finflux2.GetForeclosureDetailsResponse{
					Status: rpc.StatusOk(),
					Amount: &greaterPayment,
				}, nil)
			},
			wantRes: false,
		},
	}
	ctrl := gomock.NewController(t)
	vgFinfluxClient := mocks.NewMockFinfluxClient(ctrl)
	laDao := mock_dao.NewMockLoanAccountsDao(ctrl)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockVgClient != nil {
				tt.mockVgClient(vgFinfluxClient)
			}
			if tt.mockLADao != nil {
				tt.mockLADao(laDao)
			}
			s := finflux.NewFinfluxBaseProvider(vgFinfluxClient, laDao)
			res, err := s.ShouldPreCloseTheLoanOnPayment(tt.args.ctx, tt.args.loanAccountId, tt.args.paymentAmount)
			if err != nil {
				t.Errorf("unexpected error from lms interface method, %v", err)
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				t.Errorf("got: %v, want: %v", res, tt.wantRes)
			}
		})
	}
}
