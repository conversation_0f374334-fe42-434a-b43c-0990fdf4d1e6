package vendorapimodels

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	datePb "google.golang.org/genproto/googleapis/type/date"
	latLngPb "google.golang.org/genproto/googleapis/type/latlng"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"

	ldbtPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
)

func TestGetLoanAffinityRequest_Marshal(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name          string
		req           *ldbtPb.AffinityApiRequest
		expectLatLong bool // whether latitude/longitude should be present in output
		wantErr       bool
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: true,
		},
		{
			name: "request with non-zero lat/long",
			req: &ldbtPb.AffinityApiRequest{
				ActorId: "test-actor",
				UserDetails: &ldbtPb.AffinityApiRequest_UserDetails{
					Dob: &datePb.Date{
						Year:  1990,
						Month: 1,
						Day:   1,
					},
					LatLong: &latLngPb.LatLng{
						Latitude:  12.9716,
						Longitude: 77.5946,
					},
					Gender: commonTypes.Gender_MALE,
				},
				DeviceDetails: &ldbtPb.AffinityApiRequest_DeviceDetails{
					Model:        "iPhone",
					Manufacturer: "Apple",
				},
				EmploymentDetails: &ldbtPb.AffinityApiRequest_EmploymentDetails{
					EmploymentType: commonTypes.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
				},
			},
			expectLatLong: true,
			wantErr:       false,
		},
		{
			name: "request with zero lat/long",
			req: &ldbtPb.AffinityApiRequest{
				ActorId: "test-actor",
				UserDetails: &ldbtPb.AffinityApiRequest_UserDetails{
					Dob: &datePb.Date{
						Year:  1990,
						Month: 1,
						Day:   1,
					},
					LatLong: &latLngPb.LatLng{
						Latitude:  0.0,
						Longitude: 0.0,
					},
					Gender: commonTypes.Gender_MALE,
				},
				DeviceDetails: &ldbtPb.AffinityApiRequest_DeviceDetails{
					Model:        "iPhone",
					Manufacturer: "Apple",
				},
				EmploymentDetails: &ldbtPb.AffinityApiRequest_EmploymentDetails{
					EmploymentType: commonTypes.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
				},
			},
			expectLatLong: false,
			wantErr:       false,
		},
		{
			name: "request with nil lat/long",
			req: &ldbtPb.AffinityApiRequest{
				ActorId: "test-actor",
				UserDetails: &ldbtPb.AffinityApiRequest_UserDetails{
					Dob: &datePb.Date{
						Year:  1990,
						Month: 1,
						Day:   1,
					},
					LatLong: nil,
					Gender:  commonTypes.Gender_MALE,
				},
				DeviceDetails: &ldbtPb.AffinityApiRequest_DeviceDetails{
					Model:        "iPhone",
					Manufacturer: "Apple",
				},
				EmploymentDetails: &ldbtPb.AffinityApiRequest_EmploymentDetails{
					EmploymentType: commonTypes.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
				},
			},
			expectLatLong: false,
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &GetLoanAffinityRequest{
				Req:     tt.req,
				GenConf: &genconf.Config{},
			}

			got, err := r.Marshal()
			if tt.wantErr {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, got)

			// Unmarshal the JSON to verify the contents
			var result map[string]interface{}
			err = json.Unmarshal(got, &result)
			require.NoError(t, err)

			// Check if user_details exists
			userDetails, ok := result["user_details"].(map[string]interface{})
			require.True(t, ok)

			// Verify lat/long presence based on expectation
			_, hasLat := userDetails["latitude"]
			_, hasLong := userDetails["longitude"]
			assert.Equal(t, tt.expectLatLong, hasLat, "latitude presence mismatch")
			assert.Equal(t, tt.expectLatLong, hasLong, "longitude presence mismatch")

			// If lat/long should be present, verify the values
			// nolint:testifylint
			if tt.expectLatLong {
				assert.Equal(t, tt.req.GetUserDetails().GetLatLong().GetLatitude(), userDetails["latitude"])
				assert.Equal(t, tt.req.GetUserDetails().GetLatLong().GetLongitude(), userDetails["longitude"])
			}

			// Verify other required fields are present
			assert.NotEmpty(t, result["actor_id"])
			assert.NotNil(t, result["context"])
			assert.NotNil(t, result["device_details"])
			assert.NotNil(t, result["employment_details"])
		})
	}
}
