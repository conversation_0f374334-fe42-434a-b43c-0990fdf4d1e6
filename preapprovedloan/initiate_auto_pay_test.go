// nolint:gocritic
package preapprovedloan_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	mock_providers "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/mocks"
)

func TestService_InitiateAutoPayExecution(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx context.Context
		req *palPb.InitiateAutoPayExecutionRequest
	}
	ctr := gomock.NewController(t)
	autoPayProviderMock := mock_providers.NewMockIAutoPayExecutionAmountProvider(ctr)
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *mockedDependencies)
		wantRes    *palPb.InitiateAutoPayExecutionResponse
		wantErr    bool
	}{
		{
			name: "should give error if dao call to loan accounts(get by id) fails",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan account"),
			},
			wantErr: false,
		},
		{
			name: "should give error if dao call to loan accounts(get by id) gives account with status not active",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
				}, nil)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("loan account not active"),
			},
			wantErr: false,
		},
		{
			name: "should give error if dao call to lpr (GetByActorIdAndStatuses) returns error other tha err record not found",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:     "acc-id-1",
					Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return(nil, epifierrors.ErrPermanent)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan payment requests"),
			},
			wantErr: false,
		},
		{
			name: "should give error if dao call to lpr (GetByActorIdAndStatuses) returns error and it is not record not found",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
					},
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:         "acc-id-1",
					Status:     palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					LmsPartner: enums.LmsPartner_LMS_PARTNER_FINFLUX,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return(nil, epifierrors.ErrPermissionDenied)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan payment requests"),
			},
			wantErr: false,
		},
		{
			name: "should give error if dao call to GetByAccountIdAndStatuses gives record not found and get auto pay exec provider gives error",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
					},
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:         "acc-id-1",
					Status:     palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					LmsPartner: enums.LmsPartner_LMS_PARTNER_FINFLUX,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				f.mockLoanDataProvider.EXPECT().GetAutoPayExecutionAmountProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(autoPayProviderMock, nil)
				autoPayProviderMock.EXPECT().GetAutoPayExecutionAmount(gomock.Any(), &providers.GetAutoPayExecutionAmountRequest{
					LoanAccountId: "acc-id-1",
				}).Return(nil, epifierrors.ErrInvalidArgument)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should give error if dao call to GetByAccountIdAndStatuses gives record not found and dao call to lpr(create) fails",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
					},
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:         "acc-id-1",
					Status:     palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					LmsPartner: enums.LmsPartner_LMS_PARTNER_FINFLUX,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				f.mockLoanDataProvider.EXPECT().GetAutoPayExecutionAmountProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(autoPayProviderMock, nil)
				autoPayProviderMock.EXPECT().GetAutoPayExecutionAmount(gomock.Any(), &providers.GetAutoPayExecutionAmountRequest{
					LoanAccountId: "acc-id-1",
				}).Return(&providers.GetAutoPayExecutionAmountResponse{
					Amount: &money.Money{
						Units:        300,
						CurrencyCode: "INR",
					},
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrInvalidArgument)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should give error if dao call to GetByAccountIdAndStatuses gives record not found and new workflow initiation fails",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
					},
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:         "acc-id-1",
					Status:     palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					LmsPartner: enums.LmsPartner_LMS_PARTNER_FINFLUX,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				f.mockLoanDataProvider.EXPECT().GetAutoPayExecutionAmountProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(autoPayProviderMock, nil)
				autoPayProviderMock.EXPECT().GetAutoPayExecutionAmount(gomock.Any(), &providers.GetAutoPayExecutionAmountRequest{
					LoanAccountId: "acc-id-1",
				}).Return(&providers.GetAutoPayExecutionAmountResponse{
					Amount: &money.Money{
						Units:        300,
						CurrencyCode: "INR",
					},
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&palPb.LoanPaymentRequest{
					AccountId: "acc-id-1",
					OrchId:    "orch-id-1",
				}, nil)
				f.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should run successfully when no non-terminal recurring payment LPR exists",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
					},
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:         "acc-id-1",
					Status:     palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					LmsPartner: enums.LmsPartner_LMS_PARTNER_FINFLUX,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				f.mockLoanDataProvider.EXPECT().GetAutoPayExecutionAmountProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(autoPayProviderMock, nil)
				autoPayProviderMock.EXPECT().GetAutoPayExecutionAmount(gomock.Any(), &providers.GetAutoPayExecutionAmountRequest{
					LoanAccountId: "acc-id-1",
				}).Return(&providers.GetAutoPayExecutionAmountResponse{
					Amount: &money.Money{
						Units:        300,
						CurrencyCode: "INR",
					},
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&palPb.LoanPaymentRequest{
					AccountId: "acc-id-1",
					OrchId:    "orch-id-1",
				}, nil)
				f.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "should give error if there is already a lpr and dao call to mark it failed fails",
			args: args{
				ctx: context.Background(),
				req: &palPb.InitiateAutoPayExecutionRequest{
					LoanAccountId: "acc-id-1",
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
					},
				},
			},
			setupMocks: func(f *mockedDependencies) {
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), "acc-id-1").Return(&palPb.LoanAccount{
					Id:         "acc-id-1",
					Status:     palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					LmsPartner: enums.LmsPartner_LMS_PARTNER_FINFLUX,
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), "acc-id-1", []palPb.LoanPaymentRequestStatus{
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION,
					palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
				}, gomock.Any()).Return([]*palPb.LoanPaymentRequest{
					{
						Id:     "id-1",
						Type:   palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_RECURRING_PAYMENT_EXECUTION,
						OrchId: "orch-id-1",
						Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
					},
					{
						Id: "id-2",
					},
				}, nil)
				f.celestialClient.EXPECT().GetWorkflowStatus(gomock.Any(), &celestial.GetWorkflowStatusRequest{
					Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestial.ClientReqId{
							Id:     "orch-id-1",
							Client: workflow.Client_PRE_APPROVED_LOAN,
						},
					},
					Ownership: commontypes.Ownership_LIQUILOANS_PL,
				}).Return(&celestial.GetWorkflowStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				f.loanPaymentRequestsDao.EXPECT().Update(gomock.Any(), &palPb.LoanPaymentRequest{
					Id:     "id-1",
					Type:   palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_RECURRING_PAYMENT_EXECUTION,
					OrchId: "orch-id-1",
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_FAILED,
				}, []palPb.LoanPaymentRequestFieldMask{
					palPb.LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_STATUS,
				}).Return(epifierrors.ErrPermissionDenied)
			},
			wantRes: &palPb.InitiateAutoPayExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, f, deferFunc := newPalServiceWithMocks(t)
			defer deferFunc()

			tt.setupMocks(f)
			gotRes, err := s.InitiateAutoPayExecution(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateAutoPayExecution() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(gotRes, tt.wantRes, protocmp.Transform()); diff != "" {
				t.Errorf("InitiateAutoPayExecution gotRes = %v\n want = %v\n diff = %v", gotRes, tt.wantRes, diff)
			}
			if diff := cmp.Diff(gotRes, tt.wantRes, protocmp.Transform()); diff != "" {
				logger.Panic(fmt.Sprintf("InitiateAutoPayExecution - got = %v, want %v \n diff: %v", gotRes, tt.wantRes, diff))
			}
		})
	}
}
