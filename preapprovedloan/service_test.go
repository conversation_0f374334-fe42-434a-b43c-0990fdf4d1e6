// nolint
package preapprovedloan_test

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	datePb "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	moneyPb "github.com/epifi/be-common/pkg/money"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/liveness"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	esignPb "github.com/epifi/gamma/api/docs/esign"
	"github.com/epifi/gamma/api/frontend/deeplink"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	plEnumsPb "github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	typesESign "github.com/epifi/gamma/api/typesv2/esign"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/preapprovedloan"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	dataExistenceManager "github.com/epifi/gamma/preapprovedloan/data_existence_manager"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
	loanDataProviderProviders "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	mock_providers "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/mocks"
	"github.com/epifi/gamma/preapprovedloan/loanplans"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	mocks2 "github.com/epifi/gamma/preapprovedloan/prepay/providers/mocks"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux/converters"
)

var (
	dummyActorId           = "dummy-actor-1"
	dummyVendorApplicantId = "dummy-vendor-application-id"
	dummyOfferId           = "dummy-offer-id"
	dummyLoecId            = "dummy-loec-id"
	dummyUserId            = "dummy-user-id"

	loanOfferSample1 = &preApprovedLoanPb.LoanOffer{
		Id:            "loan-offer-id-1",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-1",
		Vendor:        preApprovedLoanPb.Vendor_FEDERAL,
		OfferConstraints: &preApprovedLoanPb.OfferConstraints{
			MaxLoanAmount:   moneyPb.FromPaisa(50000000),
			MaxEmiAmount:    moneyPb.FromPaisa(1500000),
			MaxTenureMonths: 48,
		},
		ProcessingInfo: &preApprovedLoanPb.OfferProcessingInfo{
			InterestRate: []*preApprovedLoanPb.RangeData{
				{
					Start: 1,
					End:   1000,
					Value: &preApprovedLoanPb.RangeData_Percentage{
						Percentage: 12,
					},
				},
			},
			ProcessingFee: nil,
			ApplicationId: "",
		},
		ValidSince: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		ValidTill: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		DeactivatedAt: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
	}

	loanOfferSample2 = &preApprovedLoanPb.LoanOffer{
		Id:            "loan-offer-id-2",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-2",
		Vendor:        preApprovedLoanPb.Vendor_STOCK_GUARDIAN_LSP,
		OfferConstraints: &preApprovedLoanPb.OfferConstraints{
			MaxLoanAmount:   moneyPb.FromPaisa(50000000),
			MaxEmiAmount:    moneyPb.FromPaisa(1500000),
			MaxTenureMonths: 48,
		},
		ProcessingInfo: &preApprovedLoanPb.OfferProcessingInfo{
			InterestRate: []*preApprovedLoanPb.RangeData{
				{
					Start: 1,
					End:   1000,
					Value: &preApprovedLoanPb.RangeData_Percentage{
						Percentage: 12,
					},
				},
			},
			ProcessingFee: nil,
			ApplicationId: "",
		},
		ValidSince: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		ValidTill: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		DeactivatedAt: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
	}

	loanRequestSample1 = &preApprovedLoanPb.LoanRequest{
		OfferId:         "loan-offer-id-1",
		VendorRequestId: "vreqId-1",
		Details: &preApprovedLoanPb.LoanRequestDetails{
			OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
				Otp:            "1234",
				MaxAttempts:    3,
				AttemptsCount:  1,
				LastEnteredOtp: "1234",
			},

			MaskedAccountNumber: "",
			LoanInfo:            nil,
			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: **********,
			},
		},
		SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_OTP_GENERATED,
	}
	loanRequestSample2 = &preApprovedLoanPb.LoanRequest{
		OfferId:         "loan-offer-id-1",
		VendorRequestId: "vreqId-1",
		Details: &preApprovedLoanPb.LoanRequestDetails{
			OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
				Otp:            "1234",
				MaxAttempts:    3,
				AttemptsCount:  1,
				LastEnteredOtp: "1234",
			},

			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: **********,
			},
		},
		Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
		SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
	}
	loanRequestSample3 = &preApprovedLoanPb.LoanRequest{
		OfferId:         "loan-offer-id-1",
		VendorRequestId: "vreqId-1",
		Details: &preApprovedLoanPb.LoanRequestDetails{
			OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
				Otp:            "1234",
				MaxAttempts:    3,
				AttemptsCount:  1,
				LastEnteredOtp: "1234",
			},

			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: **********,
			},
			LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
				Amount:          moneyPb.FromPaisa(1000),
				EmiAmount:       moneyPb.FromPaisa(1000),
				DisbursalAmount: moneyPb.FromPaisa(1000),
				Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
					Gst:             moneyPb.FromPaisa(0),
					ProcessingFee:   moneyPb.FromPaisa(0),
					AdvanceInterest: moneyPb.FromPaisa(0),
					TotalDeductions: moneyPb.FromPaisa(0),
				},
				TotalPayable: nil,
			},
		},
		CompletedAt: timestampPb.Now(),
		Status:      preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
		SubStatus:   preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
	}

	loanAccountSample1 = &preApprovedLoanPb.LoanAccount{
		Id:          "laccId-1",
		ActorId:     "act-1",
		Vendor:      preApprovedLoanPb.Vendor_FEDERAL,
		LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Status:      preApprovedLoanPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
		LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
			LoanAmount:         moneyPb.FromPaisa(1000),
			DisbursedAmount:    moneyPb.FromPaisa(1000),
			OutstandingAmount:  moneyPb.FromPaisa(1000),
			TotalPayableAmount: moneyPb.FromPaisa(1000),
		},
	}
	loanAccountSample2 = &preApprovedLoanPb.LoanAccount{
		Id:          "laccId-1",
		ActorId:     "act-1",
		Vendor:      preApprovedLoanPb.Vendor_FIFTYFIN,
		LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_LAMF,
		Status:      preApprovedLoanPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
		LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
			LoanAmount:         moneyPb.FromPaisa(1000),
			DisbursedAmount:    moneyPb.FromPaisa(1000),
			OutstandingAmount:  moneyPb.FromPaisa(1000),
			TotalPayableAmount: moneyPb.FromPaisa(1000),
		},
	}
	loanRequestSample4 = &preApprovedLoanPb.LoanRequest{
		Id:     "lr1",
		Type:   preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE,
		Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
	}
	loanRequestSample5 = &preApprovedLoanPb.LoanRequest{
		Id:     "lr1",
		Type:   preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE,
		Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
	}
)

func TestService_InitiateESign(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx context.Context
		req *palPb.InitiateESignRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.InitiateESignResponse
		wantErr    bool
	}{
		{
			name: "should return Status as Record Not Found when unable to fetch loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &preApprovedLoanPb.InitiateESignResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &preApprovedLoanPb.InitiateESignResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should return Status as Record Not Found when unable to fetch loan offer",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "lr-1",
					ActorId: "actor-1",
					OfferId: "offer-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &preApprovedLoanPb.InitiateESignResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan offer",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "lr-1",
					ActorId: "actor-1",
					OfferId: "offer-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-1").Return(nil, epifierrors.ErrTransient)
			},
			want: &preApprovedLoanPb.InitiateESignResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan step by ref id",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
					OfferId: "offer-1",
				}
				lo := &preApprovedLoanPb.LoanOffer{
					Id:      "offer-1",
					ActorId: "actor-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-1").Return(lo, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				).Return(nil, epifierrors.ErrTransient)
			},
			want: &preApprovedLoanPb.InitiateESignResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in updating loan step",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
					OfferId: "offer-1",
				}
				lo := &preApprovedLoanPb.LoanOffer{
					Id:      "offer-1",
					ActorId: "actor-1",
					ProcessingInfo: &preApprovedLoanPb.OfferProcessingInfo{
						Gst: 18,
						ProcessingFee: []*preApprovedLoanPb.RangeData{
							{
								Start: 1,
								End:   1000,
								Value: &preApprovedLoanPb.RangeData_Percentage{
									Percentage: 12,
								},
							},
						},
					},
				}
				lse := &preApprovedLoanPb.LoanStepExecution{
					Id:       "step-1",
					ActorId:  "actor-1",
					RefId:    "ref-1",
					OrchId:   "client-req-1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							Name: &commontypes.Name{
								FirstName: "first",
							},
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
							KycName: &commontypes.Name{
								FirstName: "first",
								LastName:  "last",
							},
						},
					},
					Status: rpc.StatusOk(),
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-1").Return(lo, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				).Return(lse, nil)

				// RPC HELPER begins
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)

				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
					FeatureInfo: &onbPb.FeatureInfo{
						FeatureStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE,
					},
				}, nil)

				// GetCustomerPermanentDetails() Calls:
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)

				addressMap := map[string]*postaladdress.PostalAddress{
					types.AddressType_PERMANENT.String(): {
						PostalCode: "260037",
					},
				}
				md.usersClient.EXPECT().GetCustomerDetails(gomock.Any(), &userPb.GetCustomerDetailsRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					UserId:     "user-1",
					ActorId:    "actor-1",
					Provenance: userPb.Provenance_APP,
				}).Return(&userPb.GetCustomerDetailsResponse{
					Status:    rpc.StatusOk(),
					Addresses: addressMap,
				}, nil)

				md.savingsClient.EXPECT().GetAccount(gomock.Any(), &savings.GetAccountRequest{
					Identifier: &savings.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savings.ActorUniqueAccountIdentifier{
							ActorId:                "actor-1",
							AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id:      "account-id-1",
						EmailId: "<EMAIL>",
						ActorId: "actor-1",
					},
				}, nil)

				md.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 17, 0, 0, 0, 0, datetime.IST)).Times(5)

				md.eSignClient.EXPECT().InitiateESign(gomock.Any(), &esignPb.InitiateESignRequest{
					ActorId:         "actor-1",
					ClientRequestId: "client-req-1",
					TemplateType:    typesESign.TemplateType_PRE_APPROVED_LOAN_KFS_TEMPLATE,
					TemplateOption: &typesESign.TemplateOptions{
						Options: &typesESign.TemplateOptions_KfsTemplateOptions{
							KfsTemplateOptions: &typesESign.KFSTemplateOptions{
								File: &typesESign.KFSTemplateOptions_File{
									Name: "First Last",
									Fields: map[string]string{
										palVgPb.KfsOptions_Field_Name_FIELD_ACCOUNT_NUMBER.String():                     "",
										palVgPb.KfsOptions_Field_Name_FIELD_ADDRESS.String():                            "260037",
										palVgPb.KfsOptions_Field_Name_FIELD_AGE.String():                                "2024",
										palVgPb.KfsOptions_Field_Name_FIELD_AMOUNT_IN_WORDS.String():                    "Zero",
										palVgPb.KfsOptions_Field_Name_FIELD_AMOUNT.String():                             "-",
										palVgPb.KfsOptions_Field_Name_FIELD_APR.String():                                "0.00",
										palVgPb.KfsOptions_Field_Name_FIELD_BPI.String():                                "-",
										palVgPb.KfsOptions_Field_Name_FIELD_DATE.String():                               "17-01-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_EMAIL.String():                              "<EMAIL>",
										palVgPb.KfsOptions_Field_Name_FIELD_EMI.String():                                "-",
										palVgPb.KfsOptions_Field_Name_FIELD_EXPIRY.String():                             "05-02-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_FATHER_HUSBAND_NAME.String():                " ",
										palVgPb.KfsOptions_Field_Name_FIELD_FIRST_EMI_DATE.String():                     "05-03-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_IFSC.String():                               "",
										palVgPb.KfsOptions_Field_Name_FIELD_MOBILE.String():                             "**********",
										palVgPb.KfsOptions_Field_Name_FIELD_NAME.String():                               "First Last",
										palVgPb.KfsOptions_Field_Name_FIELD_PERIOD.String():                             "0",
										palVgPb.KfsOptions_Field_Name_FIELD_PF_AMOUNT.String():                          "0",
										palVgPb.KfsOptions_Field_Name_FIELD_PF_RATE.String():                            "12.00",
										palVgPb.KfsOptions_Field_Name_FIELD_ROI.String():                                "0.00",
										palVgPb.KfsOptions_Field_Name_INTEREST_AMOUNT.String():                          "0",
										palVgPb.KfsOptions_Field_Name_NET_LOAN_AMOUNT.String():                          "-",
										palVgPb.KfsOptions_Field_Name_TOTAL_AMOUNT.String():                             "-",
										palVgPb.KfsOptions_Field_Name_FIELD_LENTRA_REF_ID.String():                      "FI_",
										palVgPb.KfsOptions_Field_Name_FIELD_SANCTION_AMOUNT.String():                    "-",
										palVgPb.KfsOptions_Field_Name_FIELD_TENURE.String():                             "0",
										palVgPb.KfsOptions_Field_Name_FIELD_FIRST_EMI.String():                          "05-03-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_EMI_NUMBER.String():                         "0",
										palVgPb.KfsOptions_Field_Name_FIELD_EMI_AMOUNT.String():                         "-",
										palVgPb.KfsOptions_Field_Name_FIELD_PROCESSING_FEE.String():                     "-",
										palVgPb.KfsOptions_Field_Name_FIELD_GST.String():                                "-",
										palVgPb.KfsOptions_Field_Name_FIELD_RATE_OF_INTEREST.String():                   "0.00",
										palVgPb.KfsOptions_Field_Name_FIELD_NET_LOAN_AMOUNT.String():                    "-",
										palVgPb.KfsOptions_Field_Name_FIELD_LOAN_AMOUNT_TO_BE_PAID_BY_BOROWWER.String(): "-",
										palVgPb.KfsOptions_Field_Name_FIELD_RATE_OF_INTEREST_CAPS.String():              "0.00",
									},
								},
								Invitee: &typesESign.KFSTemplateOptions_Invitee{
									Name:  "First Last",
									Email: "<EMAIL>",
									PhoneNumber: &commontypes.PhoneNumber{
										CountryCode:    91,
										NationalNumber: **********,
									},
								},
							},
						},
					},
					Vendor: esignPb.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
					Client: esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2,
				}).Return(&esignPb.InitiateESignResponse{
					Status:  rpc.StatusOk(),
					SignUrl: "signURL",
					ExpiryAt: &timestampPb.Timestamp{
						Seconds: 1735689600, // 01/01/2025
						Nanos:   0,
					},
				}, nil)
				// RPC HELPER ends

				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), lse, []preApprovedLoanPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				}).Return(epifierrors.ErrTransient)
			},
			want: &preApprovedLoanPb.InitiateESignResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Should return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateESignRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
					OfferId: "offer-1",
				}
				lo := &preApprovedLoanPb.LoanOffer{
					Id:      "offer-1",
					ActorId: "actor-1",
					ProcessingInfo: &preApprovedLoanPb.OfferProcessingInfo{
						Gst: 18,
						ProcessingFee: []*preApprovedLoanPb.RangeData{
							{
								Start: 1,
								End:   1000,
								Value: &preApprovedLoanPb.RangeData_Percentage{
									Percentage: 12,
								},
							},
						},
					},
				}
				lse := &preApprovedLoanPb.LoanStepExecution{
					Id:       "step-1",
					ActorId:  "actor-1",
					RefId:    "ref-1",
					OrchId:   "client-req-1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							Name: &commontypes.Name{
								FirstName: "first",
							},
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
							KycName: &commontypes.Name{
								FirstName: "first",
								LastName:  "last",
							},
						},
					},
					Status: rpc.StatusOk(),
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-1").Return(lo, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				).Return(lse, nil)

				// RPC HELPER begins
				// 1 :
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)

				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
					FeatureInfo: &onbPb.FeatureInfo{
						FeatureStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE,
					},
				}, nil)

				// 2 :
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				addressMap := map[string]*postaladdress.PostalAddress{
					types.AddressType_PERMANENT.String(): {
						PostalCode: "260037",
					},
				}
				md.usersClient.EXPECT().GetCustomerDetails(gomock.Any(), &userPb.GetCustomerDetailsRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					UserId:     "user-1",
					ActorId:    "actor-1",
					Provenance: userPb.Provenance_APP,
				}).Return(&userPb.GetCustomerDetailsResponse{
					Status:    rpc.StatusOk(),
					Addresses: addressMap,
				}, nil)

				md.savingsClient.EXPECT().GetAccount(gomock.Any(), &savings.GetAccountRequest{
					Identifier: &savings.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savings.ActorUniqueAccountIdentifier{
							ActorId:                "actor-1",
							AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id:      "account-id-1",
						EmailId: "<EMAIL>",
						ActorId: "actor-1",
					},
				}, nil)

				md.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 17, 0, 0, 0, 0, datetime.IST)).Times(5)

				md.eSignClient.EXPECT().InitiateESign(gomock.Any(), &esignPb.InitiateESignRequest{
					ActorId:         "actor-1",
					ClientRequestId: "client-req-1",
					TemplateType:    typesESign.TemplateType_PRE_APPROVED_LOAN_KFS_TEMPLATE,
					TemplateOption: &typesESign.TemplateOptions{
						Options: &typesESign.TemplateOptions_KfsTemplateOptions{
							KfsTemplateOptions: &typesESign.KFSTemplateOptions{
								File: &typesESign.KFSTemplateOptions_File{
									Name: "First Last",
									Fields: map[string]string{
										palVgPb.KfsOptions_Field_Name_FIELD_ACCOUNT_NUMBER.String():                     "",
										palVgPb.KfsOptions_Field_Name_FIELD_ADDRESS.String():                            "260037",
										palVgPb.KfsOptions_Field_Name_FIELD_AGE.String():                                "2024",
										palVgPb.KfsOptions_Field_Name_FIELD_AMOUNT_IN_WORDS.String():                    "Zero",
										palVgPb.KfsOptions_Field_Name_FIELD_AMOUNT.String():                             "-",
										palVgPb.KfsOptions_Field_Name_FIELD_APR.String():                                "0.00",
										palVgPb.KfsOptions_Field_Name_FIELD_BPI.String():                                "-",
										palVgPb.KfsOptions_Field_Name_FIELD_DATE.String():                               "17-01-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_EMAIL.String():                              "<EMAIL>",
										palVgPb.KfsOptions_Field_Name_FIELD_EMI.String():                                "-",
										palVgPb.KfsOptions_Field_Name_FIELD_EXPIRY.String():                             "05-02-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_FATHER_HUSBAND_NAME.String():                " ",
										palVgPb.KfsOptions_Field_Name_FIELD_FIRST_EMI_DATE.String():                     "05-03-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_IFSC.String():                               "",
										palVgPb.KfsOptions_Field_Name_FIELD_MOBILE.String():                             "**********",
										palVgPb.KfsOptions_Field_Name_FIELD_NAME.String():                               "First Last",
										palVgPb.KfsOptions_Field_Name_FIELD_PERIOD.String():                             "0",
										palVgPb.KfsOptions_Field_Name_FIELD_PF_AMOUNT.String():                          "0",
										palVgPb.KfsOptions_Field_Name_FIELD_PF_RATE.String():                            "12.00",
										palVgPb.KfsOptions_Field_Name_FIELD_ROI.String():                                "0.00",
										palVgPb.KfsOptions_Field_Name_INTEREST_AMOUNT.String():                          "0",
										palVgPb.KfsOptions_Field_Name_NET_LOAN_AMOUNT.String():                          "-",
										palVgPb.KfsOptions_Field_Name_TOTAL_AMOUNT.String():                             "-",
										palVgPb.KfsOptions_Field_Name_FIELD_LENTRA_REF_ID.String():                      "FI_",
										palVgPb.KfsOptions_Field_Name_FIELD_SANCTION_AMOUNT.String():                    "-",
										palVgPb.KfsOptions_Field_Name_FIELD_TENURE.String():                             "0",
										palVgPb.KfsOptions_Field_Name_FIELD_FIRST_EMI.String():                          "05-03-2024",
										palVgPb.KfsOptions_Field_Name_FIELD_EMI_NUMBER.String():                         "0",
										palVgPb.KfsOptions_Field_Name_FIELD_EMI_AMOUNT.String():                         "-",
										palVgPb.KfsOptions_Field_Name_FIELD_PROCESSING_FEE.String():                     "-",
										palVgPb.KfsOptions_Field_Name_FIELD_GST.String():                                "-",
										palVgPb.KfsOptions_Field_Name_FIELD_RATE_OF_INTEREST.String():                   "0.00",
										palVgPb.KfsOptions_Field_Name_FIELD_NET_LOAN_AMOUNT.String():                    "-",
										palVgPb.KfsOptions_Field_Name_FIELD_LOAN_AMOUNT_TO_BE_PAID_BY_BOROWWER.String(): "-",
										palVgPb.KfsOptions_Field_Name_FIELD_RATE_OF_INTEREST_CAPS.String():              "0.00",
									},
								},
								Invitee: &typesESign.KFSTemplateOptions_Invitee{
									Name:  "First Last",
									Email: "<EMAIL>",
									PhoneNumber: &commontypes.PhoneNumber{
										CountryCode:    91,
										NationalNumber: **********,
									},
								},
							},
						},
					},
					Vendor: esignPb.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
					Client: esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2,
				}).Return(&esignPb.InitiateESignResponse{
					Status:  rpc.StatusOk(),
					SignUrl: "signURL",
					ExpiryAt: &timestampPb.Timestamp{
						Seconds: 1735689600, // 01/01/2025
						Nanos:   0,
					},
				}, nil)
				// RPC HELPER ends

				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), lse, []preApprovedLoanPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				}).Return(nil)
			},
			want: &preApprovedLoanPb.InitiateESignResponse{
				Status:  rpc.StatusOk(),
				SignUrl: "signURL",
				ExitUrl: "https://fi.money/loans-callback",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.InitiateESign(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateESign() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("InitiateESign() Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}

func TestService_CancelApplication(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	mockLoanReqDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockPartnerLmsUserDao := daoMocks.NewMockPartnerLmsUserDao(ctr)
	eventMock := eventMock.NewMockBroker(ctr)
	mockMandateRequest := daoMocks.NewMockMandateRequestDao(ctr)
	eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	type mockGetLoanRequestById struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetLoanStepByRefId struct {
		res []*preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockUpdateLR struct {
		err error
	}
	type mockUpdateLS struct {
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.CancelApplicationRequest
	}
	tests := []struct {
		name                   string
		args                   args
		want                   *preApprovedLoanPb.CancelApplicationResponse
		wantErr                bool
		mockGetLoanRequestById *mockGetLoanRequestById
		mockGetLoanStepByRefId *mockGetLoanStepByRefId
		mockUpdateLR           *mockUpdateLR
		mockUpdateLS           *mockUpdateLS
	}{
		{
			name: "#1.1 Fail to fetch loan request by id",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.CancelApplicationRequest{
					LoanRequestId: "loan-req-id",
				},
			},
			want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: nil,
				err: epifierrors.ErrTransient,
			},
		},
		{
			name: "#1.2 Fail to fetch loan request record not found",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.CancelApplicationRequest{
					LoanRequestId: "loan-req-id",
				},
			},
			want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "#2.1 Cannot cancel as loan request already initiated with vendor",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.CancelApplicationRequest{
					LoanRequestId: "loan-req-id",
				},
			},
			want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusFailedPrecondition()},
			wantErr: false,
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
				},
				err: nil,
			},
		},
		{
			name: "#2.2 Cannot cancel as loan request already failed at vendor",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.CancelApplicationRequest{
					LoanRequestId: "loan-req-id",
				},
			},
			want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusFailedPrecondition()},
			wantErr: false,
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
				},
				err: nil,
			},
		},
		{
			name: "#3.1 fail to fetch loan step execution",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.CancelApplicationRequest{
					LoanRequestId: "loan-req-id",
				},
			},
			want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLoanStepByRefId: &mockGetLoanStepByRefId{
				res: nil,
				err: epifierrors.ErrTransient,
			},
		},
		{
			name: "#4.1 fail to update loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.CancelApplicationRequest{
					LoanRequestId: "loan-req-id",
				},
			},
			want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLoanStepByRefId: &mockGetLoanStepByRefId{
				res: nil,
				err: nil,
			},
			mockUpdateLR: &mockUpdateLR{
				err: epifierrors.ErrTransient,
			},
		},
		// todo (Harish) : fix and uncomment the unit test
		// {
		// 	name: "#5.1 should successfully cancel loan application",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.CancelApplicationRequest{
		// 			LoanRequestId: "loan-req-id",
		// 		},
		// 	},
		// 	want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusOk()},
		// 	wantErr: false,
		// 	mockGetLoanRequestById: &mockGetLoanRequestById{
		// 		res: &preApprovedLoanPb.LoanRequest{
		// 			Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		// 		},
		// 		err: nil,
		// 	},
		// 	mockGetLoanStepByRefId: &mockGetLoanStepByRefId{
		// 		res: []*preApprovedLoanPb.LoanStepExecution{{Id: "loan-step"}},
		// 		err: nil,
		// 	},
		// 	mockUpdateLR: &mockUpdateLR{
		// 		err: nil,
		// 	},
		// 	mockUpdateLS: &mockUpdateLS{
		// 		err: nil,
		// 	},
		// },
		// {
		// 	name: "#5.1 should successfully cancel loan application despite loan step update failure",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.CancelApplicationRequest{
		// 			LoanRequestId: "loan-req-id",
		// 		},
		// 	},
		// 	want:    &preApprovedLoanPb.CancelApplicationResponse{Status: rpc.StatusOk()},
		// 	wantErr: false,
		// 	mockGetLoanRequestById: &mockGetLoanRequestById{
		// 		res: &preApprovedLoanPb.LoanRequest{
		// 			Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		// 		},
		// 		err: nil,
		// 	},
		// 	mockGetLoanStepByRefId: &mockGetLoanStepByRefId{
		// 		res: []*preApprovedLoanPb.LoanStepExecution{{Id: "loan-step"},
		// 			{Id: "loan-step-2"}},
		// 		err: nil,
		// 	},
		// 	mockUpdateLR: &mockUpdateLR{
		// 		err: nil,
		// 	},
		// 	mockUpdateLS: &mockUpdateLS{
		// 		err: epifierrors.ErrTransient,
		// 	},
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoanRequestById != nil {
				mockLoanReqDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestById.res, tt.mockGetLoanRequestById.err)
			}
			if tt.mockGetLoanStepByRefId != nil {
				mockStepExecutionDao.EXPECT().GetByRefIdAndStatuses(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanStepByRefId.res, tt.mockGetLoanStepByRefId.err)
			}
			if tt.mockUpdateLR != nil {
				mockLoanReqDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockUpdateLR.err)
			}
			if tt.mockUpdateLS != nil {
				mockStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockUpdateLS.err).AnyTimes()
			}
			s := preapprovedloan.NewService(nil, nil, mockLoanReqDao, mockStepExecutionDao, nil, nil, nil, nil, nil,
				nil, nil, eventMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
				nil, nil, nil, mockPartnerLmsUserDao, nil, nil,
				nil, nil, nil, nil, nil, nil, nil,
				mockMandateRequest, nil, nil, nil, nil,
				nil, nil, nil, nil, nil,
				nil, nil, nil, loanplans.NewProviderFactory(nil), nil, nil, nil, nil)
			got, err := s.CancelApplication(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CancelApplication() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CancelApplication() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetOfferDetails(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()

	type mockGetOfferById struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
		FeatureInfo:  nil,
	}, nil).AnyTimes()

	type mockGetActorById bool

	type mockGetActiveOfferByActorIdVendorAndLoanProgram struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type mockGetLoanOfferVg struct {
		res *palVgPb.GetInstantLoanOfferResponse
		err error
	}
	type mockGetByActorIdAndVendorAndStatus struct {
		res []*preApprovedLoanPb.LoanOfferEligibilityCriteria
		err error
	}
	type mockTxnExecutor struct {
		err error
	}
	type mockLoGetActiveOfferByActorIdAndVendor struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}

	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetOfferDetailsRequest
	}
	tests := []struct {
		name                                            string
		args                                            args
		want                                            *preApprovedLoanPb.GetOfferDetailsResponse
		wantErr                                         bool
		mockGetOfferById                                *mockGetOfferById
		mockGetActorById                                mockGetActorById
		mockGetLoanOfferVg                              *mockGetLoanOfferVg
		mockTxnExecutor                                 *mockTxnExecutor
		mockGetByActorIdAndVendorAndStatus              *mockGetByActorIdAndVendorAndStatus
		mockLoGetActiveOfferByActorIdAndVendor          *mockLoGetActiveOfferByActorIdAndVendor
		mockGetActiveOfferByActorIdVendorAndLoanProgram *mockGetActiveOfferByActorIdVendorAndLoanProgram
		customCheck                                     func(*preApprovedLoanPb.GetOfferDetailsResponse, error) error
	}{
		{
			name: "Should fail to fetch offer by id",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetOfferDetailsRequest{
					OfferId: "random-offer-id",
				},
			},
			mockGetOfferById: &mockGetOfferById{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
			want: &preApprovedLoanPb.GetOfferDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Should fail to fetch loan offer from vendor",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetOfferDetailsRequest{
					ActorId: "actor-1",
				},
			},
			want: &preApprovedLoanPb.GetOfferDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr:            false,
			mockGetActorById:   false,
			mockGetLoanOfferVg: nil,
			mockGetActiveOfferByActorIdVendorAndLoanProgram: &mockGetActiveOfferByActorIdVendorAndLoanProgram{},
		},
		{
			name: "Successfully fetch loan offer from vendor but fail to create in db",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetOfferDetailsRequest{
					ActorId: "actor-1",
				},
			},
			want: &preApprovedLoanPb.GetOfferDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr:            false,
			mockGetActorById:   false,
			mockGetLoanOfferVg: nil,
			mockTxnExecutor:    nil,
			mockGetActiveOfferByActorIdVendorAndLoanProgram: &mockGetActiveOfferByActorIdVendorAndLoanProgram{},
		},
		// TODO(@prasoon): fix  the below test as it's timestamp based (due to interest rate)
		// {
		//	name: "Successfully fetch loan offer from vendor and create in db",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &preApprovedLoanPb.GetOfferDetailsRequest{
		//			ActorId:        "actor-1",
		//			LoanAmount:     moneyPb.FromPaisa(1000000),
		//			TenureInMonths: 10,
		//		},
		//	},
		//	want: &preApprovedLoanPb.GetOfferDetailsResponse{
		//		Status:  rpc.StatusOk(),
		//		OfferId: loanOfferSample1.Id,
		//		OfferInfo: &preApprovedLoanPb.GetOfferDetailsResponse_OfferInfo{
		//			MinLoanAmount:     moneyPb.FromPaisa(100),
		//			MaxLoanAmount:     moneyPb.FromPaisa(50000000),
		//			InterestRate:      10,
		//			MinTenureInMonths: 6,
		//			MaxTenureInMonths: 48,
		//			MaxEmiAmount:      moneyPb.FromPaisa(1500000),
		//			GstPercentage:     0,
		//		},
		//		LoanInfo: &preApprovedLoanPb.GetOfferDetailsResponse_LoanInfo{
		//			Amount:          moneyPb.FromPaisa(1000000),
		//			TenureInMonths:  10,
		//			DisbursalAmount: moneyPb.FromPaisa(997300),
		//			EmiAmount:       moneyPb.FromPaisa(104600),
		//			Deductions: &preApprovedLoanPb.GetOfferDetailsResponse_LoanInfo_Deductions{
		//				TotalDeductions: moneyPb.FromPaisa(2700),
		//				Gst:             moneyPb.FromPaisa(0),
		//				ProcessingFee:   moneyPb.FromPaisa(0),
		//				AdvanceInterest: moneyPb.FromPaisa(2700),
		//			},
		//			Constrains: &preApprovedLoanPb.GetOfferDetailsResponse_LoanInfo_Constrains{
		//				MinLoanAmount:     moneyPb.FromPaisa(100),
		//				MaxLoanAmount:     moneyPb.FromPaisa(14334800),
		//				MinTenureInMonths: 6,
		//				MaxTenureInMonths: 48,
		//			},
		//			TotalPayable: moneyPb.FromPaisa(1046400),
		//		},
		//	},
		//	wantErr:          false,
		//	mockGetActorById: true,
		//	mockGetLoanOfferVg: &mockGetLoanOfferVg{res: &palVgPb.GetInstantLoanOfferResponse{
		//		IsEligible:      false,
		//		OfferId:         loanOfferSample1.GetVendorOfferId(),
		//		MaxAmount:       loanOfferSample1.GetOfferConstraints().GetMaxLoanAmount(),
		//		MaxAllowedEmi:   loanOfferSample1.GetOfferConstraints().GetMaxEmiAmount(),
		//		MaxTenureMonths: loanOfferSample1.GetOfferConstraints().GetMaxTenureMonths(),
		//		InterestRates: []*palVgPb.GetInstantLoanOfferResponse_RangeToFrom{
		//			{
		//				RangeFrom: moneyPb.FromPaisa(100),
		//				RangeTo:   moneyPb.FromPaisa(********),
		//				Type:      palVgPb.GetInstantLoanOfferResponse_TYPE_PERCENTAGE,
		//				Value:     10,
		//			},
		//		},
		//		ProcessingFeePercentages: nil,
		//		ServiceTaxGst:            0,
		//		ExpiryTimestamp:          0,
		//		ApplicationId:            "",
		//		Status:                   rpc.StatusOk(),
		//		RawResponseCode:          "",
		//		RawResponseReason:        "",
		//	}, err: nil},
		//	mockCreateOffer: &mockCreateOffer{
		//		res: loanOfferSample1,
		//		err: nil,
		//	},
		// },

		{
			name: "Successfully fetch loan offer from vendor and fail the transcation bcz the offer is already present in db",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetOfferDetailsRequest{
					ActorId: "actor-1",
				},
			},
			mockGetActorById: true,
			mockGetLoanOfferVg: &mockGetLoanOfferVg{
				res: &palVgPb.GetInstantLoanOfferResponse{
					IsEligible:      false,
					OfferId:         "vendor-offer-id-1",
					MaxAmount:       loanOfferSample1.GetOfferConstraints().GetMaxLoanAmount(),
					MaxAllowedEmi:   loanOfferSample1.GetOfferConstraints().GetMaxEmiAmount(),
					MaxTenureMonths: loanOfferSample1.GetOfferConstraints().GetMaxTenureMonths(),
					InterestRates: []*palVgPb.GetInstantLoanOfferResponse_RangeToFrom{
						{
							RangeFrom: moneyPb.FromPaisa(100),
							RangeTo:   moneyPb.FromPaisa(********),
							Type:      palVgPb.GetInstantLoanOfferResponse_TYPE_PERCENTAGE,
							Value:     10,
						},
					},
					ApplicationId:     "",
					Status:            rpc.StatusOk(),
					RawResponseCode:   "",
					RawResponseReason: "",
				}, err: nil,
			},
			mockGetByActorIdAndVendorAndStatus: &mockGetByActorIdAndVendorAndStatus{
				res: make([]*preApprovedLoanPb.LoanOfferEligibilityCriteria, 0),
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: nil,
			},
			// mockLoGetActiveOfferByActorIdAndVendor: &mockLoGetActiveOfferByActorIdAndVendor{
			//	res: &preApprovedLoanPb.LoanOffer{
			//		Id:            "loan-offer-id-1",
			//		ActorId:       "actor-1",
			//		VendorOfferId: "vendor-offer-id-1",
			//		Vendor:        preApprovedLoanPb.Vendor_FEDERAL,
			//		LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			//		OfferConstraints: &preApprovedLoanPb.OfferConstraints{
			//			MaxLoanAmount:   moneyPb.FromPaisa(50000000),
			//			MaxEmiAmount:    moneyPb.FromPaisa(1500000),
			//			MaxTenureMonths: 48,
			//		},
			//		ProcessingInfo: &preApprovedLoanPb.OfferProcessingInfo{
			//			InterestRate: []*preApprovedLoanPb.RangeData{
			//				{
			//					Start: 1,
			//					End:   1000,
			//					Value: &preApprovedLoanPb.RangeData_Percentage{
			//						Percentage: 12,
			//					},
			//				},
			//			},
			//			ProcessingFee: nil,
			//			ApplicationId: "",
			//		},
			//	},
			//	err: nil,
			// },
			mockGetActiveOfferByActorIdVendorAndLoanProgram: &mockGetActiveOfferByActorIdVendorAndLoanProgram{
				res: &preApprovedLoanPb.LoanOffer{
					Id:            "loan-offer-id-0",
					ActorId:       "actor-1",
					VendorOfferId: "vendor-offer-id-0",
					Vendor:        preApprovedLoanPb.Vendor_FEDERAL,
					LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					OfferConstraints: &preApprovedLoanPb.OfferConstraints{
						MaxLoanAmount:   moneyPb.FromPaisa(50000000),
						MaxEmiAmount:    moneyPb.FromPaisa(1500000),
						MaxTenureMonths: 48,
					},
					ProcessingInfo: &preApprovedLoanPb.OfferProcessingInfo{
						InterestRate: []*preApprovedLoanPb.RangeData{
							{
								Start: 1,
								End:   1000,
								Value: &preApprovedLoanPb.RangeData_Percentage{
									Percentage: 12,
								},
							},
						},
						ProcessingFee: nil,
						ApplicationId: "",
					},
				},
				err: nil,
			},
			wantErr: false,
			customCheck: func(response *preApprovedLoanPb.GetOfferDetailsResponse, err error) error {
				if err != nil {
					return err
				}
				// if response.OfferId != "loan-offer-id-1" {
				//	return errors.New("offer Id mismatch")
				// }
				return nil
			},
			want: &preApprovedLoanPb.GetOfferDetailsResponse{
				Status:  rpc.StatusOk(),
				OfferId: "loan-offer-id-1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetOfferById != nil {
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(tt.mockGetOfferById.res, tt.mockGetOfferById.err)
			}
			if tt.mockGetByActorIdAndVendorAndStatus != nil {
				md.loecDao.EXPECT().GetByActorIdAndVendorAndStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockGetByActorIdAndVendorAndStatus.res, tt.mockGetByActorIdAndVendorAndStatus.err).Times(1)
			}
			if tt.mockGetActorById {
				md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{Status: rpc.StatusOk(), Actor: &types.Actor{}}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							PhoneNumber: &commontypes.PhoneNumber{},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
			}
			if tt.mockLoGetActiveOfferByActorIdAndVendor != nil {
				md.loanOfferDao.EXPECT().GetActiveOfferByActorIdAndVendor(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLoGetActiveOfferByActorIdAndVendor.res, tt.mockLoGetActiveOfferByActorIdAndVendor.err)
			}
			if tt.mockGetLoanOfferVg != nil {
				md.palVgClient.EXPECT().GetInstantLoanOffer(gomock.Any(), gomock.Any()).Return(tt.mockGetLoanOfferVg.res, tt.mockGetLoanOfferVg.err)
			}
			if tt.mockTxnExecutor != nil {
				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(tt.mockTxnExecutor.err)
			}
			if tt.mockGetActiveOfferByActorIdVendorAndLoanProgram != nil {
				md.loanOfferDao.EXPECT().GetActiveOfferByActorIdVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockGetActiveOfferByActorIdVendorAndLoanProgram.res, tt.mockGetActiveOfferByActorIdVendorAndLoanProgram.err)
			}

			got, err := s.GetOfferDetails(tt.args.ctx, tt.args.req)
			if tt.customCheck != nil {
				customCheckErr := tt.customCheck(got, err)
				if customCheckErr != nil {
					t.Errorf("GetOfferDetails() custom check failed = %v", customCheckErr)
				}
				return
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOfferDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOfferDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetApplicationStatus(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()

	type mockGetLrByID struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetLsByRefId struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetApplicationStatusRequest
	}
	tests := []struct {
		name             string
		args             args
		want             *preApprovedLoanPb.GetApplicationStatusResponse
		wantErr          bool
		mockGetLrByID    *mockGetLrByID
		mockGetLsByRefId *mockGetLsByRefId
	}{
		{
			name: "#1.1 Should fail to fetch loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetApplicationStatusRequest{LoanRequestId: "random-lr-1"},
			},
			want: &preApprovedLoanPb.GetApplicationStatusResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
			mockGetLrByID: &mockGetLrByID{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "#1.2 Should fail to fetch loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetApplicationStatusRequest{LoanRequestId: "random-lr-1"},
			},
			want: &preApprovedLoanPb.GetApplicationStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetLrByID: &mockGetLrByID{
				res: nil,
				err: epifierrors.ErrTransient,
			},
		},
		{
			name: "Should fail to fetch loan step by ref id",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetApplicationStatusRequest{LoanRequestId: "random-lr-1"},
			},
			want: &preApprovedLoanPb.GetApplicationStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetLsByRefId: &mockGetLsByRefId{
				res: nil,
				err: epifierrors.ErrTransient,
			},
			mockGetLrByID: &mockGetLrByID{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
		},
		{
			name: "No Loan step exists for loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetApplicationStatusRequest{LoanRequestId: "random-lr-1"},
			},
			want: &preApprovedLoanPb.GetApplicationStatusResponse{
				Status: rpc.StatusOk(),
				LoanRequest: &preApprovedLoanPb.LoanRequest{
					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							Amount:          moneyPb.FromPaisa(1000),
							EmiAmount:       moneyPb.FromPaisa(1000),
							DisbursalAmount: moneyPb.FromPaisa(1000),
							Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
								Gst:             moneyPb.FromPaisa(0),
								ProcessingFee:   moneyPb.FromPaisa(0),
								AdvanceInterest: moneyPb.FromPaisa(0),
								TotalDeductions: moneyPb.FromPaisa(0),
							},
							TotalPayable: nil,
						},
					},
				},
				CurrentExecution: nil,
			},
			wantErr: false,
			mockGetLsByRefId: &mockGetLsByRefId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
			mockGetLrByID: &mockGetLrByID{
				res: &preApprovedLoanPb.LoanRequest{

					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							Amount:    moneyPb.FromPaisa(1000),
							EmiAmount: moneyPb.FromPaisa(1000),
							Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
								TotalDeductions: moneyPb.FromPaisa(0),
								Gst:             moneyPb.FromPaisa(0),
								ProcessingFee:   moneyPb.FromPaisa(0),
								AdvanceInterest: moneyPb.FromPaisa(0),
							},
							TotalPayable: nil,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "successfully fetch application status",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetApplicationStatusRequest{LoanRequestId: "random-lr-1"},
			},
			want: &preApprovedLoanPb.GetApplicationStatusResponse{
				Status: rpc.StatusOk(),
				LoanRequest: &preApprovedLoanPb.LoanRequest{
					Id: "lr-1",
					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							Amount:          moneyPb.FromPaisa(1000),
							EmiAmount:       moneyPb.FromPaisa(1000),
							DisbursalAmount: moneyPb.FromPaisa(1000),
							Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
								Gst:             moneyPb.FromPaisa(0),
								ProcessingFee:   moneyPb.FromPaisa(0),
								AdvanceInterest: moneyPb.FromPaisa(0),
								TotalDeductions: moneyPb.FromPaisa(0),
							},
							TotalPayable: nil,
						},
					},
				},
				CurrentExecution: &preApprovedLoanPb.LoanStepExecution{Id: "ls-1"},
			},
			wantErr: false,
			mockGetLsByRefId: &mockGetLsByRefId{
				res: &preApprovedLoanPb.LoanStepExecution{Id: "ls-1"},
				err: nil,
			},
			mockGetLrByID: &mockGetLrByID{
				res: &preApprovedLoanPb.LoanRequest{
					Id: "lr-1",
					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							Amount:    moneyPb.FromPaisa(1000),
							EmiAmount: moneyPb.FromPaisa(1000),
							Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
								TotalDeductions: moneyPb.FromPaisa(0),
								Gst:             moneyPb.FromPaisa(0),
								ProcessingFee:   moneyPb.FromPaisa(0),
								AdvanceInterest: moneyPb.FromPaisa(0),
							},
							TotalPayable: nil,
						},
					},
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLsByRefId != nil {
				md.loanStepExecutionDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLsByRefId.res, tt.mockGetLsByRefId.err)
			}
			if tt.mockGetLrByID != nil {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLrByID.res, tt.mockGetLrByID.err)
			}
			got, err := s.GetApplicationStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetApplicationStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetApplicationStatus() \ngot = %v, \n want %v", got, tt.want)
			}
		})
	}
}

func TestService_ApplyForLoan(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockGetLoByID struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type mockGetLrByActorId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.ApplyForLoanRequest
	}
	type mockLrCreate struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetActorById struct {
		res *actorPb.GetActorByIdResponse
		err error
	}
	type mockGetUser struct {
		res *userPb.GetUserResponse
		err error
	}
	type mockGetAccount struct {
		res *savings.GetAccountResponse
		err error
	}
	type mockInitiateWorkflow struct {
		res *celestial.InitiateWorkflowResponse
		err error
	}
	tests := []struct {
		name                 string
		args                 args
		want                 *preApprovedLoanPb.ApplyForLoanResponse
		wantErr              bool
		mockGetLoByID        *mockGetLoByID
		mockGetLrByActorId   *mockGetLrByActorId
		mockLrCreate         *mockLrCreate
		mockGetActorById     *mockGetActorById
		mockGetUser          *mockGetUser
		mockGetAccount       *mockGetAccount
		mockInitiateWorkflow *mockInitiateWorkflow
	}{
		{
			name: "#1.1 Loan Offer does not exist",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.ApplyForLoanRequest{
					ActorId: "act-1",
					OfferId: "offer-1",
				},
			},
			want: &preApprovedLoanPb.ApplyForLoanResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
			mockGetLoByID: &mockGetLoByID{
				err: epifierrors.ErrRecordNotFound,
			},
		},
		// {
		//	name: "#1.2 failed to get non terminal by actorId and vendor",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &preApprovedLoanPb.ApplyForLoanRequest{
		//			ActorId: "act-1",
		//		},
		//	},
		//	want: &preApprovedLoanPb.ApplyForLoanResponse{
		//		Status: rpc.StatusInternal(),
		//	},
		//	wantErr: false,
		//	mockGetLoByID: &mockGetLoByID{
		//		res: &preApprovedLoanPb.LoanOffer{},
		//		err: nil,
		//	},
		//	mockGetLrByActorId: &mockGetLrByActorId{
		//		res: nil,
		//		err: epifierrors.ErrPermanent,
		//	},
		// },
		// {
		//	name: "#1.3 Success Case",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &preApprovedLoanPb.ApplyForLoanRequest{
		//			ActorId:        "act-1",
		//			OfferId:        "offerID-1",
		//			LoanAmount:     moneyPb.FromPaisa(1000),
		//			TenureInMonths: 12,
		//		},
		//	},
		//	want: &preApprovedLoanPb.ApplyForLoanResponse{
		//		Status:        rpc.StatusOk(),
		//		LoanRequestId: "reqID-1",
		//	},
		//	wantErr: false,
		//	mockGetLoByID: &mockGetLoByID{
		//		res: loanOfferSample1,
		//
		//		err: nil,
		//	},
		//	mockGetLrByActorId: &mockGetLrByActorId{
		//		res: nil,
		//		err: epifierrors.ErrRecordNotFound,
		//	},
		//	mockGetActorById: &mockGetActorById{
		//		res: &actorPb.GetActorByIdResponse{
		//			Status: rpc.StatusOk(),
		//			Actor: &types.Actor{
		//				EntityId: "entity-1",
		//			},
		//		},
		//		err: nil,
		//	},
		//	mockGetUser: &mockGetUser{
		//		res: &userPb.GetUserResponse{
		//			Status: rpc.StatusOk(),
		//			User: &userPb.User{
		//				Id: "userID-1",
		//			},
		//		},
		//		err: nil,
		//	},
		//	mockGetAccount: &mockGetAccount{
		//		res: &savings.GetAccountResponse{Account: &savings.Account{
		//			Id:        "saccID-1",
		//			AccountNo: "**********",
		//		}},
		//		err: nil,
		//	},
		//	mockLrCreate: &mockLrCreate{
		//		res: &preApprovedLoanPb.LoanRequest{
		//			Id:      "reqID-1",
		//			ActorId: "act-1",
		//		},
		//		err: nil,
		//	},
		//	mockInitiateWorkflow: &mockInitiateWorkflow{
		//		res: &celestial.InitiateWorkflowResponse{
		//			Status:            rpc.StatusOk(),
		//			WorkflowRequestId: "flow-id",
		//		},
		//		err: nil,
		//	},
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoByID != nil {
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(tt.mockGetLoByID.res, tt.mockGetLoByID.err)
			}

			if tt.mockGetLrByActorId != nil {
				md.loanRequestDao.EXPECT().GetNonTerminalByActorIdVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLrByActorId.res, tt.mockGetLrByActorId.err)
			}
			if tt.mockLrCreate != nil {
				md.loanRequestDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
					tt.mockLrCreate.res, tt.mockLrCreate.err)
			}
			if tt.mockGetActorById != nil {
				md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(
					tt.mockGetActorById.res, tt.mockGetActorById.err).AnyTimes()
			}
			if tt.mockGetAccount != nil {
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(
					tt.mockGetAccount.res, tt.mockGetAccount.err)
			}
			if tt.mockGetUser != nil {
				md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(
					tt.mockGetUser.res, tt.mockGetUser.err)
			}
			if tt.mockInitiateWorkflow != nil {
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(
					tt.mockInitiateWorkflow.res, tt.mockInitiateWorkflow.err)
			}
			got, err := s.ApplyForLoan(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyForLoan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ApplyForLoan() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLandingInfo(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockGetLrByID struct {
		res []*preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetLoByactorId struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetLandingInfoRequest
	}
	tests := []struct {
		name               string
		args               args
		want               *preApprovedLoanPb.GetLandingInfoResponse
		wantErr            bool
		mockGetLrByID      *mockGetLrByID
		mockGetLoByactorId *mockGetLoByactorId
	}{
		// todo (Divas) : fix and uncomment the unit test
		// {
		// 	name: "#1.1 failed to get loan request",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.GetLandingInfoRequest{
		// 			ActorId: "act-1",
		// 		},
		// 	},
		// 	want: &preApprovedLoanPb.GetLandingInfoResponse{
		// 		Status:             rpc.StatusInternal(),
		// 		EarlySalaryDetails: &preApprovedLoanPb.GetLandingInfoResponse_EarlySalaryDetails{},
		// 	},
		// 	wantErr: false,
		// 	mockGetLrByID: &mockGetLrByID{
		// 		res: nil,
		// 		err: epifierrors.ErrPermanent,
		// 	},
		// 	mockGetLoByactorId: nil,
		// },
		// todo (Divas) : fix and uncomment the unit test
		// {
		// 	name: "# 1.1 for success case",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.GetLandingInfoRequest{ActorId: "act-1"},
		// 	},
		// 	want: &preApprovedLoanPb.GetLandingInfoResponse{
		// 		Status: rpc.NewStatusWithoutDebug(uint32(preApprovedLoanPb.GetLandingInfoResponse_LOAN_APPLICATIONS), "Loan Management"),
		// 		LoanOffer: &preApprovedLoanPb.LoanOffer{
		// 			Id:      "offerID-1",
		// 			ActorId: "act-1",
		// 		},
		// 		ActiveLoanRequest: &preApprovedLoanPb.LoanRequest{
		// 			Id:      "reqID-1",
		// 			ActorId: "act-1",
		// 		},
		// 		EarlySalaryDetails: &preApprovedLoanPb.GetLandingInfoResponse_EarlySalaryDetails{},
		// 	},
		// 	wantErr: false,
		// 	mockGetLrByID: &mockGetLrByID{
		// 		res: []*preApprovedLoanPb.LoanRequest{
		// 			{
		// 				Id:      "reqID-1",
		// 				ActorId: "act-1",
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	mockGetLoByactorId: &mockGetLoByactorId{
		// 		res: &preApprovedLoanPb.LoanOffer{
		// 			Id:      "offerID-1",
		// 			ActorId: "act-1",
		// 		},
		// 		err: nil,
		// 	},
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLrByID != nil {
				md.loanRequestDao.EXPECT().GetByActorIdVendorStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLrByID.res, tt.mockGetLrByID.err)
			}
			if tt.mockGetLoByactorId != nil {
				md.loanOfferDao.EXPECT().GetActiveOfferByActorIdVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoByactorId.res, tt.mockGetLoByactorId.err)
			}
			got, err := s.GetLandingInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLandingInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got.GetEarlySalaryDetails().MinDaysPostSalaryCredit = 0
			got.GetEarlySalaryDetails().MinSalaryCredits = 0
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLandingInfo() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLandingInfoV2(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()

	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetLandingInfoV2Request
	}
	type mockGetLandingPageDataForActor struct {
		res *landing_provider.GetLandingPageDataForActorResponse
		err error
	}

	tests := []struct {
		name                           string
		args                           args
		want                           *preApprovedLoanPb.GetLandingInfoV2Response
		wantErr                        bool
		mockGetLandingPageDataForActor *mockGetLandingPageDataForActor
	}{
		{
			name: "Loan account exists",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLandingInfoV2Request{
					ActorId:    "actor-id-1",
					LoanHeader: nil,
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
					ActiveLoanAccount: loanAccountSample1,
				},
				err: nil,
			},
			want: &preApprovedLoanPb.GetLandingInfoV2Response{
				Status:            rpc.StatusOk(),
				ActiveLoanAccount: loanAccountSample1,
				LoanHeader: &preApprovedLoanPb.LoanHeader{
					LoanProgram: loanAccountSample1.GetLoanProgram(),
					Vendor:      loanAccountSample1.GetVendor(),
				},
				LandingStatus: rpc.NewStatusWithoutDebug(uint32(preApprovedLoanPb.GetLandingInfoV2Response_LANDING_STATUS_LOAN_APPLICATIONS), "Loan Management"),
			},
			wantErr: false,
		},
		{
			name: "Active loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLandingInfoV2Request{
					ActorId: loanRequestSample1.GetActorId(),
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanRequestSample1.GetLoanProgram(),
						Vendor:      loanRequestSample1.GetVendor(),
					},
					LoanRequests: []*preApprovedLoanPb.LoanRequest{loanRequestSample1},
				},
				err: nil,
			},
			want: &preApprovedLoanPb.GetLandingInfoV2Response{
				Status:            rpc.StatusOk(),
				ActiveLoanRequest: loanRequestSample1,
				LoanHeader: &preApprovedLoanPb.LoanHeader{
					LoanProgram: loanRequestSample1.GetLoanProgram(),
					Vendor:      loanRequestSample1.GetVendor(),
				},
				LandingStatus: rpc.NewStatusWithoutDebug(uint32(preApprovedLoanPb.GetLandingInfoResponse_LOAN_APPLICATIONS), "Loan Management"),
			},
			wantErr: false,
		},
		{
			name: "Active loan offer",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLandingInfoV2Request{
					ActorId: loanOfferSample1.GetActorId(),
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanOfferSample1.GetLoanProgram(),
						Vendor:      loanOfferSample1.GetVendor(),
					},
					LoanOptions: []*palPb.LoanOption{{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOfferSample1}}},
				},
				err: nil,
			},
			want: &preApprovedLoanPb.GetLandingInfoV2Response{
				Status:      rpc.StatusOk(),
				LoanOptions: []*palPb.LoanOption{{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOfferSample1}}},
				LoanHeader: &preApprovedLoanPb.LoanHeader{
					LoanProgram: loanOfferSample1.GetLoanProgram(),
					Vendor:      loanOfferSample1.GetVendor(),
				},
				LandingStatus: rpc.NewStatusWithoutDebug(uint32(preApprovedLoanPb.GetLandingInfoV2Response_LANDING_STATUS_LOAN_OFFER), "Loan Offer"),
			},
		},
		{
			name: "Multiple active loan offers",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLandingInfoV2Request{
					ActorId: loanOfferSample1.GetActorId(),
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanOfferSample1.GetLoanProgram(),
						Vendor:      loanOfferSample1.GetVendor(),
					},
					LoanOptions: []*palPb.LoanOption{
						{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOfferSample1}},
						{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOfferSample2}},
					},
				},
				err: nil,
			},
			want: &preApprovedLoanPb.GetLandingInfoV2Response{
				Status: rpc.StatusOk(),
				LoanOptions: []*palPb.LoanOption{
					{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOfferSample1}},
					{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOfferSample2}},
				},
				LoanHeader: &preApprovedLoanPb.LoanHeader{
					LoanProgram: loanOfferSample1.GetLoanProgram(),
					Vendor:      loanOfferSample1.GetVendor(),
				},
				LandingStatus: rpc.NewStatusWithoutDebug(uint32(preApprovedLoanPb.GetLandingInfoV2Response_LANDING_STATUS_LOAN_OFFER), "Loan Offer"),
			},
		},
		{
			name: "No loan offer exists for user",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLandingInfoV2Request{
					ActorId: "random-actor-id-1",
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					},
				},
				err: nil,
			},
			want: &preApprovedLoanPb.GetLandingInfoV2Response{
				Status: rpc.StatusPermissionDenied(),
				LoanHeader: &preApprovedLoanPb.LoanHeader{
					LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			wantErr: false,
		},
		{
			name: "Internal error in calling multiDB provider GetLandingPageDataForActor",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLandingInfoV2Request{
					ActorId: "actor-id-1",
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				err: epifierrors.ErrPermanent,
			},
			want: &preApprovedLoanPb.GetLandingInfoV2Response{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLandingPageDataForActor != nil {
				md.landingProvider.EXPECT().GetLandingPageDataForActor(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLandingPageDataForActor.res, tt.mockGetLandingPageDataForActor.err)
			}

			got, err := s.GetLandingInfoV2(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLandingInfoV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetLandingInfoV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanActivityStatus(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockGetPrByorchId struct {
		res *preApprovedLoanPb.LoanPaymentRequest
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetLoanActivityStatusRequest
	}
	tests := []struct {
		name              string
		args              args
		want              *preApprovedLoanPb.GetLoanActivityStatusResponse
		wantErr           bool
		mockGetPrByorchId *mockGetPrByorchId
	}{
		{
			name: "#1.1 failed to get payment request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanActivityStatusRequest{RefId: "refId-1"},
			},
			want: &preApprovedLoanPb.GetLoanActivityStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetPrByorchId: &mockGetPrByorchId{
				res: &preApprovedLoanPb.LoanPaymentRequest{},
				err: epifierrors.ErrTransient,
			},
		},
		{
			name: "#1.2 success case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanActivityStatusRequest{RefId: "refId-1"},
			},
			want: &preApprovedLoanPb.GetLoanActivityStatusResponse{
				Status:         rpc.StatusOk(),
				ActivityStatus: preApprovedLoanPb.GetLoanActivityStatusResponse_COMPLETED,
				LoanAccountId:  "accId-1",
			},
			wantErr: false,
			mockGetPrByorchId: &mockGetPrByorchId{
				res: &preApprovedLoanPb.LoanPaymentRequest{
					AccountId: "accId-1",
					Status:    preApprovedLoanPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS,
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPrByorchId != nil {
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetPrByorchId.res, tt.mockGetPrByorchId.err)
			}
			got, err := s.GetLoanActivityStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanActivityStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanActivityStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetDashboard(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockGetLrByID struct {
		res []*preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetLaByactorId struct {
		res []*preApprovedLoanPb.LoanAccount
		err error
	}
	type mockLrGetByLoanAccountIdAndType struct {
		res []*preApprovedLoanPb.LoanRequest
		err error
	}
	type mockLoGetById struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type mockLiiGetByAccountIdAndStatuses struct {
		res []*preApprovedLoanPb.LoanInstallmentInfo
		err error
	}
	type mockLipGetByLoanInstallmentInfoIds struct {
		res []*preApprovedLoanPb.LoanInstallmentPayout
		err error
	}
	type mockLoGetActiveOfferByActorIdAndVendor struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type mockLseGetByRefIdAndStatuses struct {
		res []*preApprovedLoanPb.LoanStepExecution
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetDashboardRequest
	}
	tests := []struct {
		name                                   string
		args                                   args
		want                                   *preApprovedLoanPb.GetDashboardResponse
		wantErr                                bool
		mockGetLrByID                          *mockGetLrByID
		mockGetLaByactorId                     *mockGetLaByactorId
		mockLrGetByLoanAccountIdAndType        *mockLrGetByLoanAccountIdAndType
		mockLoGetById                          *mockLoGetById
		mockLiiGetByAccountIdAndStatuses       *mockLiiGetByAccountIdAndStatuses
		mockLipGetByLoanInstallmentInfoIds     *mockLipGetByLoanInstallmentInfoIds
		mockLoGetActiveOfferByActorIdAndVendor *mockLoGetActiveOfferByActorIdAndVendor
		mockLseGetByRefIdAndStatuses           *mockLseGetByRefIdAndStatuses
	}{
		{
			name: "#1.1 Failed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetDashboardRequest{ActorId: "act-1"},
			},
			want: &preApprovedLoanPb.GetDashboardResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetLrByID: &mockGetLrByID{
				err: epifierrors.ErrPermanent,
			},
		},
		{
			name: "#1.2 failed to get loan account",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetDashboardRequest{ActorId: "act-1"},
			},
			want: &preApprovedLoanPb.GetDashboardResponse{
				Status:            rpc.StatusInternal(),
				RecentLoanRequest: nil,
				LoanInfoList:      nil,
			},
			wantErr: false,
			mockGetLrByID: &mockGetLrByID{
				res: []*preApprovedLoanPb.LoanRequest{
					{
						Id:     "reqID-1",
						Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
					},
				},
				err: nil,
			},
			mockGetLaByactorId: &mockGetLaByactorId{
				err: epifierrors.ErrPermanent,
			},
			mockLseGetByRefIdAndStatuses: &mockLseGetByRefIdAndStatuses{
				res: []*preApprovedLoanPb.LoanStepExecution{
					{
						Id: "id",
					},
				},
				err: nil,
			},
		},
		{
			name: "#1.3 success",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetDashboardRequest{ActorId: "act-1"},
			},
			want: &preApprovedLoanPb.GetDashboardResponse{
				Status: rpc.StatusOk(),
				RecentLoanRequest: &preApprovedLoanPb.LoanRequest{
					ActorId: "act-1",
					OfferId: "offerID-1",
					Status:  preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							Amount:          moneyPb.FromPaisa(1000),
							EmiAmount:       moneyPb.FromPaisa(1000),
							DisbursalAmount: moneyPb.FromPaisa(1000),
							Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
								Gst:             moneyPb.FromPaisa(0),
								ProcessingFee:   moneyPb.FromPaisa(0),
								AdvanceInterest: moneyPb.FromPaisa(0),
								TotalDeductions: moneyPb.FromPaisa(0),
							},
							TotalPayable: nil,
						},
					},
				},
				LoanInfoList: []*preApprovedLoanPb.LoanInfo{
					{
						LoanAccount: &preApprovedLoanPb.LoanAccount{
							Id:            "id-1",
							ActorId:       "act-1",
							Vendor:        0,
							AccountNumber: "",
							LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
								LoanAmount:         moneyPb.FromPaisa(1000),
								DisbursedAmount:    moneyPb.FromPaisa(1000),
								OutstandingAmount:  moneyPb.FromPaisa(1000),
								TotalPayableAmount: moneyPb.FromPaisa(1000),
							},
						},
						InstallmentInfoList: []*preApprovedLoanPb.InstallmentInfo{
							{
								LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
									AccountId:   "",
									TotalAmount: moneyPb.FromPaisa(1000),
									Details:     &preApprovedLoanPb.LoanInstallmentInfoDetails{NextEmiAmount: moneyPb.FromPaisa(1000)},
								},
								LoanInstallmentPayouts: []*preApprovedLoanPb.LoanInstallmentPayout{{Amount: moneyPb.FromPaisa(1000)}},
							},
						},
					},
				},
				LoanSteps: []*preApprovedLoanPb.LoanStepExecution{
					{
						Id: "id",
					},
				},
				CheckUserEligibility: true,
			},
			wantErr: false,
			mockGetLrByID: &mockGetLrByID{
				res: []*preApprovedLoanPb.LoanRequest{
					{
						ActorId: "act-1",
						OfferId: "offerID-1",
						Status:  preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
						Details: &preApprovedLoanPb.LoanRequestDetails{
							LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
								Amount:          moneyPb.FromPaisa(1000),
								EmiAmount:       moneyPb.FromPaisa(1000),
								DisbursalAmount: moneyPb.FromPaisa(1000),
								Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
									Gst:             moneyPb.FromPaisa(0),
									ProcessingFee:   moneyPb.FromPaisa(0),
									AdvanceInterest: moneyPb.FromPaisa(0),
									TotalDeductions: moneyPb.FromPaisa(0),
								},
								TotalPayable: nil,
							},
						},
					},
				},
				err: nil,
			},
			mockGetLaByactorId: &mockGetLaByactorId{
				res: []*preApprovedLoanPb.LoanAccount{
					{
						Id:            "id-1",
						ActorId:       "act-1",
						Vendor:        0,
						AccountNumber: "",
						LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
							LoanAmount:         moneyPb.FromPaisa(1000),
							DisbursedAmount:    moneyPb.FromPaisa(1000),
							OutstandingAmount:  moneyPb.FromPaisa(1000),
							TotalPayableAmount: moneyPb.FromPaisa(1000),
						},
					},
				},
				err: nil,
			},
			mockLrGetByLoanAccountIdAndType: &mockLrGetByLoanAccountIdAndType{
				res: []*preApprovedLoanPb.LoanRequest{
					{
						ActorId: "act-1",
						OfferId: "offerID-1",
						Status:  preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
						Details: &preApprovedLoanPb.LoanRequestDetails{
							LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
								Amount:          moneyPb.FromPaisa(1000),
								EmiAmount:       moneyPb.FromPaisa(1000),
								DisbursalAmount: moneyPb.FromPaisa(1000),
								Deductions: &preApprovedLoanPb.LoanRequestDetails_LoanInfo_Deductions{
									Gst:             moneyPb.FromPaisa(0),
									ProcessingFee:   moneyPb.FromPaisa(0),
									AdvanceInterest: moneyPb.FromPaisa(0),
									TotalDeductions: moneyPb.FromPaisa(0),
								},
								TotalPayable: nil,
							},
						},
					},
				},
				err: nil,
			},
			mockLoGetById: &mockLoGetById{
				res: &preApprovedLoanPb.LoanOffer{
					Id:               "offerID-1",
					ActorId:          "act-1",
					VendorOfferId:    "vend-off-1",
					Vendor:           preApprovedLoanPb.Vendor_FEDERAL,
					OfferConstraints: nil,
					ProcessingInfo:   nil,
					ValidSince:       timestampPb.Now(),
					ValidTill:        timestampPb.New(time.Now().AddDate(0, 0, 1)),
				},
				err: nil,
			},
			mockLoGetActiveOfferByActorIdAndVendor: &mockLoGetActiveOfferByActorIdAndVendor{
				res: &preApprovedLoanPb.LoanOffer{
					Id:            "offerID-1",
					ActorId:       "act-1",
					VendorOfferId: "vend-off-1",
					Vendor:        preApprovedLoanPb.Vendor_FEDERAL,
					OfferConstraints: &preApprovedLoanPb.OfferConstraints{
						MaxLoanAmount: moneyPb.FromPaisa(1000),
						MinLoanAmount: moneyPb.FromPaisa(1000),
					},
					ProcessingInfo: nil,
					ValidSince:     timestampPb.Now(),
					ValidTill:      timestampPb.New(time.Now().AddDate(0, 0, 1)),
				},
				err: nil,
			},
			mockLiiGetByAccountIdAndStatuses: &mockLiiGetByAccountIdAndStatuses{
				res: []*preApprovedLoanPb.LoanInstallmentInfo{
					{
						AccountId:   "",
						TotalAmount: moneyPb.FromPaisa(1000),
						Details:     &preApprovedLoanPb.LoanInstallmentInfoDetails{NextEmiAmount: moneyPb.FromPaisa(1000)},
					},
				},
				err: nil,
			},
			mockLipGetByLoanInstallmentInfoIds: &mockLipGetByLoanInstallmentInfoIds{
				res: []*preApprovedLoanPb.LoanInstallmentPayout{
					{
						LoanInstallmentInfoId: "",
						Amount:                moneyPb.FromPaisa(1000),
					},
				},
			},
			mockLseGetByRefIdAndStatuses: &mockLseGetByRefIdAndStatuses{
				res: []*preApprovedLoanPb.LoanStepExecution{
					{
						Id: "id",
					},
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLrByID != nil {
				md.loanRequestDao.EXPECT().GetByActorIdVendorStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLrByID.res, tt.mockGetLrByID.err)
			}
			if tt.mockGetLaByactorId != nil {
				md.loanAccountDao.EXPECT().GetByActorIdVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLaByactorId.res, tt.mockGetLaByactorId.err)
			}
			if tt.mockLiiGetByAccountIdAndStatuses != nil {
				md.loanInstallmentInfo.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLiiGetByAccountIdAndStatuses.res, tt.mockLiiGetByAccountIdAndStatuses.err)
			}
			if tt.mockLipGetByLoanInstallmentInfoIds != nil {
				md.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoIds(gomock.Any(), gomock.Any()).Return(
					tt.mockLipGetByLoanInstallmentInfoIds.res, tt.mockLipGetByLoanInstallmentInfoIds.err)
			}
			if tt.mockLoGetActiveOfferByActorIdAndVendor != nil {
				md.loanOfferDao.EXPECT().GetActiveOfferByActorIdVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLoGetActiveOfferByActorIdAndVendor.res, tt.mockLoGetActiveOfferByActorIdAndVendor.err)
			}
			if tt.mockLseGetByRefIdAndStatuses != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndStatuses(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLseGetByRefIdAndStatuses.res, tt.mockLseGetByRefIdAndStatuses.err)
			}

			got, err := s.GetDashboard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDashboard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetDashboard() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_GetDashboardV2(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()

	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetDashboardRequest
	}
	type mockGetLandingPageDataForActor struct {
		res *landing_provider.GetLandingPageDataForActorResponse
		err error
	}
	type mockLiiGetByAccountIdAndStatuses struct {
		res []*preApprovedLoanPb.LoanInstallmentInfo
		err error
	}
	type mockLipGetByLoanInstallmentInfoIds struct {
		res []*preApprovedLoanPb.LoanInstallmentPayout
		err error
	}
	type mockLseGetByRefIdAndStatuses struct {
		res []*preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockLoecGetByActorIdLoanProgramsAndStatuses struct {
		res []*preApprovedLoanPb.LoanOfferEligibilityCriteria
		err error
	}

	tests := []struct {
		name                                        string
		args                                        args
		want                                        *preApprovedLoanPb.GetDashboardResponse
		wantErr                                     bool
		mockGetLandingPageDataForActor              *mockGetLandingPageDataForActor
		mockLiiGetByAccountIdAndStatuses            *mockLiiGetByAccountIdAndStatuses
		mockLipGetByLoanInstallmentInfoIds          *mockLipGetByLoanInstallmentInfoIds
		mockLseGetByRefIdAndStatuses                *mockLseGetByRefIdAndStatuses
		mockLoecGetByActorIdLoanProgramsAndStatuses *mockLoecGetByActorIdLoanProgramsAndStatuses
	}{
		{
			name: "Loan account exists",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetDashboardRequest{
					ActorId: loanAccountSample1.GetActorId(),
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
					ActiveLoanAccount: loanAccountSample1,
				},
				err: nil,
			},
			mockLiiGetByAccountIdAndStatuses: &mockLiiGetByAccountIdAndStatuses{
				res: []*preApprovedLoanPb.LoanInstallmentInfo{
					{
						AccountId:   "",
						TotalAmount: moneyPb.FromPaisa(1000),
						Details:     &preApprovedLoanPb.LoanInstallmentInfoDetails{NextEmiAmount: moneyPb.FromPaisa(1000)},
					},
				},
				err: nil,
			},
			mockLipGetByLoanInstallmentInfoIds: &mockLipGetByLoanInstallmentInfoIds{
				res: []*preApprovedLoanPb.LoanInstallmentPayout{
					{
						LoanInstallmentInfoId: "",
						Amount:                moneyPb.FromPaisa(1000),
					},
				},
			},
			want: &preApprovedLoanPb.GetDashboardResponse{
				Status: rpc.StatusOk(),
				LoanInfoList: []*preApprovedLoanPb.LoanInfo{
					{
						LoanAccount: loanAccountSample1,
						InstallmentInfoList: []*preApprovedLoanPb.InstallmentInfo{
							{
								LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
									AccountId:   "",
									TotalAmount: moneyPb.FromPaisa(1000),
									Details:     &preApprovedLoanPb.LoanInstallmentInfoDetails{NextEmiAmount: moneyPb.FromPaisa(1000)},
								},
								LoanInstallmentPayouts: []*preApprovedLoanPb.LoanInstallmentPayout{{Amount: moneyPb.FromPaisa(1000)}},
							},
						},
					},
				},
				CheckUserEligibility: false,
			},
			wantErr: false,
		},
		// todo (lending-team) : fix and uncomment the unit test
		// {
		// 	name: "Loan request exists",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.GetDashboardRequest{
		// 			ActorId: loanRequestSample3.GetActorId(),
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: loanRequestSample3.GetLoanProgram(),
		// 				Vendor:      loanRequestSample3.GetVendor(),
		// 			},
		// 		},
		// 	},
		// 	mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
		// 		res: &multiDbProvider.GetLandingPageDataForActorResponse{
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: loanRequestSample3.GetLoanProgram(),
		// 				Vendor:      loanRequestSample3.GetVendor(),
		// 			},
		// 			LoanRequests: []*preApprovedLoanPb.LoanRequest{
		// 				loanRequestSample3,
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	mockLseGetByRefIdAndStatuses: &mockLseGetByRefIdAndStatuses{
		// 		res: []*preApprovedLoanPb.LoanStepExecution{
		// 			{
		// 				Id: "id",
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	want: &preApprovedLoanPb.GetDashboardResponse{
		// 		Status:               rpc.StatusOk(),
		// 		RecentLoanRequest:    loanRequestSample3,
		// 		CheckUserEligibility: false,
		// 		LoanSteps: []*preApprovedLoanPb.LoanStepExecution{
		// 			{
		// 				Id: "id",
		// 			},
		// 		},
		// 	},
		// 	wantErr: false,
		// },
		// todo (Divas) : fix and uncomment the unit test
		// {
		// 	name: "Loan offer exists",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.GetDashboardRequest{
		// 			ActorId: loanOfferSample1.GetActorId(),
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: loanOfferSample1.GetLoanProgram(),
		// 				Vendor:      loanOfferSample1.GetVendor(),
		// 			},
		// 		},
		// 	},
		// 	mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
		// 		res: &multiDbProvider.GetLandingPageDataForActorResponse{
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: loanOfferSample1.GetLoanProgram(),
		// 				Vendor:      loanOfferSample1.GetVendor(),
		// 			},
		// 			ActiveLoanOffer: loanOfferSample1,
		// 		},
		// 		err: nil,
		// 	},
		// 	want: &preApprovedLoanPb.GetDashboardResponse{
		// 		Status:               rpc.StatusOk(),
		// 		CheckUserEligibility: true,
		// 		ActiveLoanOffer:      loanOfferSample1,
		// 	},
		// 	wantErr: false,
		// },
		// {
		// 	name: "When loan request & loan offer exist, both are returned",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.GetDashboardRequest{
		// 			ActorId: loanRequestSample3.GetActorId(),
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		// 				Vendor:      preApprovedLoanPb.Vendor_FEDERAL,
		// 			},
		// 		},
		// 	},
		// 	mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
		// 		res: &multiDbProvider.GetLandingPageDataForActorResponse{
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		// 				Vendor:      preApprovedLoanPb.Vendor_FEDERAL,
		// 			},
		// 			LoanRequests:    []*preApprovedLoanPb.LoanRequest{loanRequestSample3},
		// 			ActiveLoanOffer: loanOfferSample1,
		// 		},
		// 		err: nil,
		// 	},
		// 	mockLseGetByRefIdAndStatuses: &mockLseGetByRefIdAndStatuses{
		// 		res: []*preApprovedLoanPb.LoanStepExecution{
		// 			{
		// 				Id: "id",
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	want: &preApprovedLoanPb.GetDashboardResponse{
		// 		Status:               rpc.StatusOk(),
		// 		RecentLoanRequest:    loanRequestSample3,
		// 		CheckUserEligibility: true,
		// 		LoanSteps: []*preApprovedLoanPb.LoanStepExecution{
		// 			{
		// 				Id: "id",
		// 			},
		// 		},
		// 		ActiveLoanOffer: loanOfferSample1,
		// 	},
		// 	wantErr: false,
		// },
		{
			name: "Error in calling multidb provider",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetDashboardRequest{
					ActorId:    "",
					LoanHeader: nil,
				},
			},
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: nil,
				err: epifierrors.ErrPermanent,
			},
			want: &preApprovedLoanPb.GetDashboardResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLandingPageDataForActor != nil {
				md.landingProvider.EXPECT().GetLandingPageDataForActor(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLandingPageDataForActor.res, tt.mockGetLandingPageDataForActor.err)
			}
			if tt.mockLiiGetByAccountIdAndStatuses != nil {
				md.loanInstallmentInfo.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLiiGetByAccountIdAndStatuses.res, tt.mockLiiGetByAccountIdAndStatuses.err)
			}
			if tt.mockLipGetByLoanInstallmentInfoIds != nil {
				md.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoIds(gomock.Any(), gomock.Any()).Return(
					tt.mockLipGetByLoanInstallmentInfoIds.res, tt.mockLipGetByLoanInstallmentInfoIds.err)
			}
			if tt.mockLseGetByRefIdAndStatuses != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndStatuses(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLseGetByRefIdAndStatuses.res, tt.mockLseGetByRefIdAndStatuses.err)
			}
			if tt.mockLoecGetByActorIdLoanProgramsAndStatuses != nil {
				md.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLoecGetByActorIdLoanProgramsAndStatuses.res, tt.mockLoecGetByActorIdLoanProgramsAndStatuses.err)
			}

			got, err := s.GetDashboardV2(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDashboardV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetDashboardV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GenerateConfirmationCode(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GenerateConfirmationCodeRequest
	}
	type mockLrById struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockLoById struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type mockGetInstantLoanOTP struct {
		res *palVgPb.GetInstantLoanOtpResponse
		err error
	}
	type mockLrUpdate struct {
		err error
	}
	tests := []struct {
		name                  string
		args                  args
		want                  *preApprovedLoanPb.GenerateConfirmationCodeResponse
		wantErr               bool
		mockLrById            *mockLrById
		mockLoById            *mockLoById
		mockGetInstantLoanOTP *mockGetInstantLoanOTP
		mockLrUpdate          *mockLrUpdate
	}{
		// {
		//	name: "Success case",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &preApprovedLoanPb.GenerateConfirmationCodeRequest{
		//			ActorId:       "actor-1",
		//			LoanRequestId: "reqID-1",
		//		},
		//	},
		//	mockLrById: &mockLrById{
		//		res: &preApprovedLoanPb.LoanRequest{
		//			VendorRequestId: "vreqId-1",
		//			OfferId:         loanRequestSample1.GetOfferId(),
		//			Details: &preApprovedLoanPb.LoanRequestDetails{
		//				OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
		//					Otp:            loanRequestSample1.GetDetails().GetOtpInfo().GetOtp(),
		//					MaxAttempts:    loanRequestSample1.GetDetails().GetOtpInfo().GetMaxAttempts(),
		//					AttemptsCount:  loanRequestSample1.GetDetails().GetOtpInfo().GetAttemptsCount(),
		//					LastEnteredOtp: loanRequestSample1.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
		//				},
		//				PhoneNumber: &commontypes.PhoneNumber{
		//					CountryCode:    91,
		//					NationalNumber: **********,
		//				},
		//			},
		//		},
		//		err: nil,
		//	},
		//	mockLoById: &mockLoById{
		//		res: &preApprovedLoanPb.LoanOffer{},
		//		err: nil,
		//	},
		//	mockGetInstantLoanOTP: &mockGetInstantLoanOTP{
		//		res: &palVgPb.GetInstantLoanOtpResponse{
		//			Otp:    1234,
		//			Status: rpc.StatusOk(),
		//		},
		//		err: nil,
		//	},
		//	mockLrUpdate: &mockLrUpdate{err: nil},
		//	want: &preApprovedLoanPb.GenerateConfirmationCodeResponse{
		//		Status:      rpc.StatusOk(),
		//		LoanRequest: loanRequestSample1,
		//	},
		//	wantErr: false,
		// },
		{
			name: "failed case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GenerateConfirmationCodeRequest{
					ActorId:       "actor-1",
					LoanRequestId: "reqID-1",
				},
			},

			want: &preApprovedLoanPb.GenerateConfirmationCodeResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockLrById: &mockLrById{
				res: &preApprovedLoanPb.LoanRequest{
					VendorRequestId: "vreqId-1",
					OfferId:         loanRequestSample1.GetOfferId(),
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:            loanRequestSample1.GetDetails().GetOtpInfo().GetOtp(),
							MaxAttempts:    loanRequestSample1.GetDetails().GetOtpInfo().GetMaxAttempts(),
							AttemptsCount:  loanRequestSample1.GetDetails().GetOtpInfo().GetAttemptsCount(),
							LastEnteredOtp: loanRequestSample1.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
						},
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
				},
				err: nil,
			},
			mockLoById: &mockLoById{
				res: nil,
				err: epifierrors.ErrPermanent,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockLrById != nil {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockLrById.res, tt.mockLrById.err)
			}
			if tt.mockLrUpdate != nil {
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLrUpdate.err)
			}
			if tt.mockLoById != nil {
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockLoById.res, tt.mockLoById.err)
			}
			if tt.mockGetInstantLoanOTP != nil {
				md.palVgClient.EXPECT().GetInstantLoanOTP(gomock.Any(), gomock.Any()).Return(
					tt.mockGetInstantLoanOTP.res, tt.mockGetInstantLoanOTP.err)
			}
			got, err := s.GenerateConfirmationCode(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateConfirmationCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GenerateConfirmationCode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_PrePayLoan(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockCreateloanPaymentRequestDao struct {
		res *preApprovedLoanPb.LoanPaymentRequest
		err error
	}
	type mockLaGetById struct {
		res *preApprovedLoanPb.LoanAccount
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.PrePayLoanRequest
	}

	type mockCreateFundTransferOrder struct {
		res *payPb.CreateFundTransferOrderResponse
		err error
	}
	type mockGetActorById struct {
		res *actorPb.GetActorByIdResponse
		err error
	}
	type mockGetPi struct {
		res *paymentinstrument.GetPiResponse
		err error
	}
	type mockGetAccount struct {
		res *savings.GetAccountResponse
		err error
	}
	type mockInitiateWorkflow struct {
		res *celestial.InitiateWorkflowResponse
		err error
	}

	tests := []struct {
		name                            string
		args                            args
		want                            *preApprovedLoanPb.PrePayLoanResponse
		wantErr                         bool
		mockCreateloanPaymentRequestDao *mockCreateloanPaymentRequestDao
		mockLaGetById                   *mockLaGetById
		mockCreateFundTransferOrder     *mockCreateFundTransferOrder
		mockGetActorById                *mockGetActorById
		mockGetPi                       *mockGetPi
		mockGetAccount                  *mockGetAccount
		mockInitiateWorkflow            *mockInitiateWorkflow
	}{
		// todo (Divas) : fix and uncomment the unit test
		// {
		// 	name: "success case",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.PrePayLoanRequest{
		// 			ActorId: "actor-1",
		// 			LoanId:  "loanID-1",
		// 			Amount:  moneyPb.FromPaisa(1000),
		// 		},
		// 	},
		// 	want: &preApprovedLoanPb.PrePayLoanResponse{
		// 		Status:      rpc.StatusOk(),
		// 		ReferenceId: "orchID-1",
		// 		TransactionAttribute: &attribute.TransactionAttribute{
		// 			PayerPaymentInstrument:   "pi-id",
		// 			PayeePaymentInstrument:   "pi-id",
		// 			PayerPaymentInstrumentId: "pi-id",
		// 			PayeePaymentInstrumentId: "pi-id",
		// 		},
		// 	},
		// 	wantErr: false,
		// 	mockCreateloanPaymentRequestDao: &mockCreateloanPaymentRequestDao{
		// 		res: &preApprovedLoanPb.LoanPaymentRequest{
		// 			OrchId: "orchID-1",
		// 		},
		// 		err: nil,
		// 	},
		// 	mockLaGetById: &mockLaGetById{
		// 		res: &preApprovedLoanPb.LoanAccount{
		//
		// 			AccountNumber: "loanacc-1",
		// 			IfscCode:      "FDRL",
		// 		},
		// 		err: nil,
		// 	},
		// 	mockCreateFundTransferOrder: &mockCreateFundTransferOrder{
		// 		res: &payPb.CreateFundTransferOrderResponse{
		// 			Status: rpc.StatusOk(),
		// 			TxnAttributes: []*attribute.TransactionAttribute{
		// 				{},
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	mockGetActorById: &mockGetActorById{
		// 		res: &actorPb.GetActorByIdResponse{
		// 			Status: rpc.StatusOk(),
		// 			Actor: &types.Actor{
		// 				Id:       "actor-1",
		// 				EntityId: "entity-1",
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	mockGetAccount: &mockGetAccount{
		// 		res: &savings.GetAccountResponse{
		// 			Account: &savings.Account{
		// 				AccountNo: "acc-1",
		// 				IfscCode:  "FDLR",
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	mockGetPi: &mockGetPi{
		// 		res: &paymentinstrument.GetPiResponse{
		// 			Status: rpc.StatusOk(),
		// 			PaymentInstrument: &paymentinstrument.PaymentInstrument{
		// 				Id: "pi-id",
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// 	mockInitiateWorkflow: &mockInitiateWorkflow{
		// 		res: &celestial.InitiateWorkflowResponse{
		// 			Status:            rpc.StatusOk(),
		// 			WorkflowRequestId: "flow-id",
		// 		},
		// 		err: nil,
		// 	},
		// },
		// todo (Divas) : fix and uncomment the unit test
		// {
		// 	name: "failed case",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.PrePayLoanRequest{
		// 			ActorId: "actor-1",
		// 			LoanId:  "loanID-1",
		// 			Amount:  moneyPb.FromPaisa(1000),
		// 		},
		// 	},
		// 	want: &preApprovedLoanPb.PrePayLoanResponse{
		// 		Status: rpc.StatusInternal(),
		// 	},
		// 	wantErr: false,
		// 	mockCreateloanPaymentRequestDao: &mockCreateloanPaymentRequestDao{
		// 		res: &preApprovedLoanPb.LoanPaymentRequest{
		// 			OrchId: "orchID-1",
		// 		},
		// 		err: nil,
		// 	},
		// 	mockLaGetById: &mockLaGetById{
		// 		res: &preApprovedLoanPb.LoanAccount{
		//
		// 			AccountNumber: "loanacc-1",
		// 			IfscCode:      "FDRL",
		// 		},
		// 		err: nil,
		// 	},
		// 	mockCreateFundTransferOrder: &mockCreateFundTransferOrder{
		//
		// 		err: epifierrors.ErrPermanent,
		// 	},
		// 	mockGetActorById:     nil,
		// 	mockGetAccount:       nil,
		// 	mockGetPi:            nil,
		// 	mockInitiateWorkflow: nil,
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockInitiateWorkflow != nil {
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(
					tt.mockInitiateWorkflow.res, tt.mockInitiateWorkflow.err).AnyTimes()
			}
			if tt.mockCreateloanPaymentRequestDao != nil {
				md.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
					tt.mockCreateloanPaymentRequestDao.res, tt.mockCreateloanPaymentRequestDao.err)
			}
			if tt.mockLaGetById != nil {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockLaGetById.res, tt.mockLaGetById.err)
			}
			if tt.mockCreateFundTransferOrder != nil {
				md.payClient.EXPECT().CreateFundTransferOrder(gomock.Any(), gomock.Any()).Return(
					tt.mockCreateFundTransferOrder.res, tt.mockCreateFundTransferOrder.err)
			}
			if tt.mockGetActorById != nil {
				md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(
					tt.mockGetActorById.res, tt.mockGetActorById.err)
			}
			if tt.mockGetAccount != nil {
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(
					tt.mockGetAccount.res, tt.mockGetAccount.err)
			}
			if tt.mockGetPi != nil {
				md.piClient.EXPECT().GetPi(gomock.Any(), gomock.Any()).Return(
					tt.mockGetPi.res, tt.mockGetPi.err).AnyTimes()
			}
			got, err := s.PrePayLoan(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PrePayLoan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PrePayLoan() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_GetAllTransactions(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockGetByAccountIdAndTypesAndCount struct {
		res []*preApprovedLoanPb.LoanActivity
		err error
	}
	type mockGetByLoanAccountId struct {
		res *preApprovedLoanPb.LoanAccount
		err error
	}
	type mockGetByAccountIdAndStatuses struct {
		res []*preApprovedLoanPb.LoanInstallmentInfo
		err error
	}
	type mockGetCountByInstallmentInfoIds struct {
		res int64
		err error
	}
	type mockGetTransaction struct {
		res *paymentPb.GetTransactionResponse
		err error
	}
	type mockGetOrderWithTransactions struct {
		res *orderPb.GetOrderWithTransactionsResponse
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetAllTransactionsRequest
	}
	tests := []struct {
		name                               string
		args                               args
		want                               *preApprovedLoanPb.GetAllTransactionsResponse
		wantErr                            bool
		mockGetByAccountIdAndTypesAndCount *mockGetByAccountIdAndTypesAndCount
		mockGetByAccountIdAndStatuses      *mockGetByAccountIdAndStatuses
		mockGetCountByInstallmentInfoIds   *mockGetCountByInstallmentInfoIds
		mockGetTransaction                 *mockGetTransaction
		mockGetOrderWithTransactions       *mockGetOrderWithTransactions
		mockGetByLoanAccountId             *mockGetByLoanAccountId
	}{
		{
			name: "success case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetAllTransactionsRequest{
					LoanAccountId: "lacc-1",
					ActorId:       "actorId1",
				},
			},
			want: &preApprovedLoanPb.GetAllTransactionsResponse{
				Status: rpc.StatusOk(),
				Transactions: []*preApprovedLoanPb.TransactionActivity{
					{
						PaymentTimestamp: timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
						Status:           preApprovedLoanPb.TransactionActivity_SUCCESS,
						Details: &preApprovedLoanPb.TransactionActivity_Details{
							Type:      preApprovedLoanPb.TransactionActivity_Details_EMI,
							Amount:    moneyPb.FromPaisa(1000),
							EmiNumber: 1,
						},
						Utr:        "456",
						LoanHeader: &preApprovedLoanPb.LoanHeader{},
					},
				},
			},
			wantErr: false,
			mockGetTransaction: &mockGetTransaction{
				res: &paymentPb.GetTransactionResponse{
					Transaction: &paymentPb.Transaction{CreatedAt: timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)), OrderId: "123"},
					Status:      rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetOrderWithTransactions: &mockGetOrderWithTransactions{
				res: &orderPb.GetOrderWithTransactionsResponse{OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{ExternalId: "456"}},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetByAccountIdAndTypesAndCount: &mockGetByAccountIdAndTypesAndCount{
				res: []*preApprovedLoanPb.LoanActivity{
					{
						LoanAccountId: "lacc-1",
						Type:          preApprovedLoanPb.LoanActivityType_LOAN_ACTIVITY_TYPE_EMI,
						Details: &preApprovedLoanPb.LoanActivityDetails{
							TransactionId: "txnID",
							Amount:        moneyPb.FromPaisa(1000),
						},
					},
				},
				err: nil,
			},
			mockGetByAccountIdAndStatuses: &mockGetByAccountIdAndStatuses{
				res: []*preApprovedLoanPb.LoanInstallmentInfo{
					{
						Id: "lii-id-1",
					},
				},
				err: nil,
			},
			mockGetCountByInstallmentInfoIds: &mockGetCountByInstallmentInfoIds{
				res: 1,
				err: nil,
			},
			mockGetByLoanAccountId: &mockGetByLoanAccountId{
				res: &preApprovedLoanPb.LoanAccount{
					Id:      "account-id-1",
					ActorId: "actorId1",
				},
				err: nil,
			},
		},
		{
			name: "failed case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetAllTransactionsRequest{LoanAccountId: "lacc-1"},
			},
			want: &preApprovedLoanPb.GetAllTransactionsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetByAccountIdAndTypesAndCount: &mockGetByAccountIdAndTypesAndCount{
				err: epifierrors.ErrPermanent,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByLoanAccountId != nil {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(tt.mockGetByLoanAccountId.res, tt.mockGetByLoanAccountId.err)
			}
			if tt.mockGetByAccountIdAndTypesAndCount != nil {
				md.loanActivityDao.EXPECT().GetByAccountIdAndTypesAndCount(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetByAccountIdAndTypesAndCount.res, tt.mockGetByAccountIdAndTypesAndCount.err)
			}
			if tt.mockGetByAccountIdAndStatuses != nil {
				md.loanInstallmentInfo.EXPECT().GetByAccountIdAndStatuses(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetByAccountIdAndStatuses.res, tt.mockGetByAccountIdAndStatuses.err)
			}
			if tt.mockGetCountByInstallmentInfoIds != nil {
				md.loanInstallmentPayoutDao.EXPECT().GetCountByInstallmentInfoIds(gomock.Any(), gomock.Any()).Return(
					tt.mockGetCountByInstallmentInfoIds.res, tt.mockGetCountByInstallmentInfoIds.err)
			}
			if tt.mockGetTransaction != nil {
				md.paymentClient.EXPECT().GetTransaction(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetTransaction.res, tt.mockGetTransaction.err)
			}
			if tt.mockGetOrderWithTransactions != nil {
				md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetOrderWithTransactions.res, tt.mockGetOrderWithTransactions.err)
			}
			got, err := s.GetAllTransactions(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllTransactions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllTransactions() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ConfirmApplication(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockLrById struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}

	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.ConfirmApplicationRequest
	}
	type mockLrUpdate struct {
		err error
	}
	type mockPublish struct {
		res string
		err error
	}
	type mockLseByRefIdAndFlowAndName struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockLoById struct {
		res *preApprovedLoanPb.LoanOffer
		err error
	}
	type mockTxnExecutor struct {
		err error
	}
	type mockLseUpdate struct {
		err error
	}
	tests := []struct {
		name                         string
		args                         args
		want                         *preApprovedLoanPb.ConfirmApplicationResponse
		wantErr                      bool
		mockLrById                   *mockLrById
		mockLrUpdate                 *mockLrUpdate
		mockPublish                  *mockPublish
		mockLseByRefIdAndFlowAndName *mockLseByRefIdAndFlowAndName
		mockLoById                   *mockLoById
		mockTxnExecutor              *mockTxnExecutor
		mockLseUpdate                *mockLseUpdate
	}{
		{
			name: "success case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.ConfirmApplicationRequest{
					ActorId:       "actor-1",
					LoanRequestId: "reqID-1",
					Otp:           "1234",
					OtpFlow:       palPb.ConfirmApplicationRequest_OTP_FLOW_E_SIGN,
				},
			},
			want: &preApprovedLoanPb.ConfirmApplicationResponse{
				Status: rpc.NewStatus(uint32(palPb.ConfirmApplicationResponse_INCORRECT_OTP), "Incorrect OTP", ""),
				LoanRequest: &preApprovedLoanPb.LoanRequest{
					OfferId:         "loan-offer-id-1",
					VendorRequestId: "vreqId-1",
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:            "1234",
							MaxAttempts:    3,
							AttemptsCount:  1,
							LastEnteredOtp: "1234",
						},

						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
					Vendor:    palPb.Vendor_FEDERAL,
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
				},
				LoanStep: &preApprovedLoanPb.LoanStepExecution{
					Details: &preApprovedLoanPb.LoanStepExecutionDetails{
						Details: &preApprovedLoanPb.LoanStepExecutionDetails_ESignStepData{ESignStepData: &preApprovedLoanPb.ESignStepData{
							OtpInfo: &preApprovedLoanPb.OtpInfo{
								MaxAttempts:   3,
								AttemptsCount: 2,
								Otp:           "1234",
							},
						}},
					},
					SubStatus: preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_OTP_FAILED,
				},
			},
			wantErr: false,
			mockLrById: &mockLrById{
				res: &preApprovedLoanPb.LoanRequest{
					VendorRequestId: loanRequestSample2.GetVendorRequestId(),
					OfferId:         loanRequestSample2.GetOfferId(),
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:            loanRequestSample2.GetDetails().GetOtpInfo().GetOtp(),
							MaxAttempts:    loanRequestSample2.GetDetails().GetOtpInfo().GetMaxAttempts(),
							AttemptsCount:  loanRequestSample2.GetDetails().GetOtpInfo().GetAttemptsCount(),
							LastEnteredOtp: loanRequestSample2.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
						},
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    loanRequestSample2.GetDetails().GetPhoneNumber().GetCountryCode(),
							NationalNumber: loanRequestSample2.GetDetails().GetPhoneNumber().GetNationalNumber(),
						},
					},
					Vendor:    preApprovedLoanPb.Vendor_FEDERAL,
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
				},

				err: nil,
			},
			mockLrUpdate: &mockLrUpdate{err: nil},
			mockPublish: &mockPublish{
				res: "",
				err: nil,
			},
			mockLseByRefIdAndFlowAndName: &mockLseByRefIdAndFlowAndName{
				res: &preApprovedLoanPb.LoanStepExecution{
					Details: &preApprovedLoanPb.LoanStepExecutionDetails{
						Details: &preApprovedLoanPb.LoanStepExecutionDetails_ESignStepData{ESignStepData: &preApprovedLoanPb.ESignStepData{
							OtpInfo: &preApprovedLoanPb.OtpInfo{
								MaxAttempts:   3,
								AttemptsCount: 1,
							},
						}},
					},
				},
				err: nil,
			},
			mockLoById: &mockLoById{
				res: &preApprovedLoanPb.LoanOffer{},
				err: nil,
			},
			mockLseUpdate: &mockLseUpdate{
				err: nil,
			},
		},
		{
			name: "failure case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.ConfirmApplicationRequest{
					ActorId:       "actor-1",
					LoanRequestId: "reqID-1",
					Otp:           "1234",
				},
			},

			mockLrById: &mockLrById{
				err: epifierrors.ErrPermanent,
			},
			want: &preApprovedLoanPb.ConfirmApplicationResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockLrById != nil {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockLrById.res, tt.mockLrById.err).Times(1)
			}
			if tt.mockLrUpdate != nil {
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLrUpdate.err).AnyTimes()
			}
			if tt.mockPublish != nil {
				md.publisherClient.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(
					tt.mockPublish.res, tt.mockPublish.err).AnyTimes()
			}
			if tt.mockLseByRefIdAndFlowAndName != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLseByRefIdAndFlowAndName.res, tt.mockLseByRefIdAndFlowAndName.err)
			}
			if tt.mockLoById != nil {
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockLoById.res, tt.mockLoById.err)
			}
			if tt.mockTxnExecutor != nil {
				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(tt.mockTxnExecutor.err)
			}
			if tt.mockLseUpdate != nil {
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLseUpdate.err)
			}
			got, err := s.ConfirmApplication(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfirmApplication() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ConfirmApplication() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLivenessStatus(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetLivenessStatusRequest
	}
	type mockLrById struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetLivenessAttempts struct {
		res *liveness.GetLivenessAttemptsResponse
		err error
	}
	md.livenessClient.EXPECT().GetLivenessStatus(gomock.Any(), gomock.Any()).Return(&liveness.GetLivenessStatusResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	tests := []struct {
		name                    string
		args                    args
		want                    *preApprovedLoanPb.GetLivenessStatusResponse
		wantErr                 bool
		mockLrById              *mockLrById
		mockGetLivenessAttempts *mockGetLivenessAttempts
	}{
		{
			name: "success case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLivenessStatusRequest{
					LoanRequestId: "reqID-1",
					ActorId:       "actor-1",
				},
			},
			want: &preApprovedLoanPb.GetLivenessStatusResponse{
				Status: rpc.StatusOk(),
				LivenessAttempt: &liveness.LivenessAttempt{
					ActorId:      "actor-1",
					AttemptId:    "attemptID-1",
					RequestId:    "reqID-1",
					LivenessFlow: liveness.LivenessFlow_PRE_APPROVED_LOANS,
				},
				LoanRequest: &preApprovedLoanPb.LoanRequest{
					ActorId:         "actor-1",
					VendorRequestId: "vreqId-1",
					OfferId:         loanRequestSample1.GetOfferId(),
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:            loanRequestSample1.GetDetails().GetOtpInfo().GetOtp(),
							MaxAttempts:    loanRequestSample1.GetDetails().GetOtpInfo().GetMaxAttempts(),
							AttemptsCount:  loanRequestSample1.GetDetails().GetOtpInfo().GetAttemptsCount(),
							LastEnteredOtp: loanRequestSample1.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
						},
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
				},
			},
			wantErr: false,
			mockLrById: &mockLrById{
				res: &preApprovedLoanPb.LoanRequest{
					ActorId:         "actor-1",
					VendorRequestId: "vreqId-1",
					OfferId:         loanRequestSample1.GetOfferId(),
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:            loanRequestSample1.GetDetails().GetOtpInfo().GetOtp(),
							MaxAttempts:    loanRequestSample1.GetDetails().GetOtpInfo().GetMaxAttempts(),
							AttemptsCount:  loanRequestSample1.GetDetails().GetOtpInfo().GetAttemptsCount(),
							LastEnteredOtp: loanRequestSample1.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
						},
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
				},
				err: nil,
			},
			mockGetLivenessAttempts: &mockGetLivenessAttempts{
				res: &liveness.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*liveness.LivenessAttempt{
						{
							ActorId:      "actor-1",
							AttemptId:    "attemptID-1",
							RequestId:    "reqID-1",
							LivenessFlow: liveness.LivenessFlow_PRE_APPROVED_LOANS,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "failed case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLivenessStatusRequest{LoanRequestId: "reqID-1"},
			},
			want: &preApprovedLoanPb.GetLivenessStatusResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
			mockLrById: &mockLrById{
				err: epifierrors.ErrRecordNotFound,
			},
			mockGetLivenessAttempts: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockLrById != nil {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockLrById.res, tt.mockLrById.err)
			}
			if tt.mockGetLivenessAttempts != nil {
				md.livenessClient.EXPECT().GetLivenessAttempts(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLivenessAttempts.res, tt.mockGetLivenessAttempts.err)
			}
			got, err := s.GetLivenessStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLivenessStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLivenessStatus() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanSummaryForHome(t *testing.T) {
	t.Parallel()
	s, md, assertTest := newPalServiceWithMocks(t)
	defer assertTest()
	type mockGetLandingPageDataForActor struct {
		res *landing_provider.GetLandingPageDataForActorResponse
		err error
	}
	type mockLiiGetByLoanAccountIdAndType struct {
		res *preApprovedLoanPb.LoanInstallmentInfo
		err error
	}
	type mockLrByAccountIdAndType struct {
		res []*preApprovedLoanPb.LoanRequest
		err error
	}
	type mockLseByRefIdFlowAndName struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockGetOrRefreshLoanDataExistenceCache struct {
		res *dataExistenceManager.DataExistenceDetails
	}
	md.savingsClient.EXPECT().GetAccount(gomock.Any(), &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savings.ActorUniqueAccountIdentifier{
				ActorId:                "act-1",
				AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
				PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	}).Return(&savings.GetAccountResponse{
		Account: &savings.Account{
			Id:      "account-id-1",
			EmailId: "<EMAIL>",
			ActorId: "actor-1",
		},
	}, nil)

	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetLoanSummaryForHomeRequest
	}
	tests := []struct {
		name                                   string
		args                                   args
		want                                   *preApprovedLoanPb.GetLoanSummaryForHomeResponse
		wantErr                                bool
		mockGetLandingPageDataForActor         *mockGetLandingPageDataForActor
		mockLiiGetByLoanAccountIdAndType       *mockLiiGetByLoanAccountIdAndType
		mockGetOrRefreshLoanDataExistenceCache *mockGetOrRefreshLoanDataExistenceCache
		mockLrByAccountIdAndType               *mockLrByAccountIdAndType
		mockLseByRefIdFlowAndName              *mockLseByRefIdFlowAndName
	}{
		{
			name: "Active loan account",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanSummaryForHomeRequest{ActorId: "actor-1"},
			},
			want: &preApprovedLoanPb.GetLoanSummaryForHomeResponse{
				Status:       rpc.StatusOk(),
				HomeCardType: 1,
				HomeCardData: &preApprovedLoanPb.GetLoanSummaryForHomeResponse_CardActiveLoanAccount_{CardActiveLoanAccount: &preApprovedLoanPb.GetLoanSummaryForHomeResponse_CardActiveLoanAccount{
					LoanAccountInfos: []*preApprovedLoanPb.GetLoanSummaryForHomeResponse_LoanAccountInfo{
						{
							LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
								Details: &preApprovedLoanPb.LoanInstallmentInfoDetails{
									NextEmiAmount: &money.Money{
										Units: 123,
										Nanos: 0,
									},
								},
							},
							LoanAccount: loanAccountSample1,
						},
					}},
				},
			},
			wantErr: false,
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
					ActiveLoanAccount: loanAccountSample1,
				},
				err: nil,
			},
			mockLrByAccountIdAndType: &mockLrByAccountIdAndType{
				res: []*preApprovedLoanPb.LoanRequest{
					{
						Id: "lr-id",
					},
				},
				err: nil,
			},
			mockLseByRefIdFlowAndName: &mockLseByRefIdFlowAndName{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockLiiGetByLoanAccountIdAndType: &mockLiiGetByLoanAccountIdAndType{
				res: &preApprovedLoanPb.LoanInstallmentInfo{
					Details: &preApprovedLoanPb.LoanInstallmentInfoDetails{
						NextEmiAmount: &money.Money{
							Units: 123,
							Nanos: 0,
						},
					},
				},
				err: nil,
			},
			mockGetOrRefreshLoanDataExistenceCache: &mockGetOrRefreshLoanDataExistenceCache{
				res: nil,
			},
		},
		// todo (Divas) : fix and uncomment the unit test
		// {
		// 	name: "Loan Application",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &preApprovedLoanPb.GetLoanSummaryForHomeRequest{ActorId: "actor-1"},
		// 	},
		// 	want: &preApprovedLoanPb.GetLoanSummaryForHomeResponse{
		// 		Status:       rpc.StatusOk(),
		// 		HomeCardType: 2,
		// 		HomeCardData: &preApprovedLoanPb.GetLoanSummaryForHomeResponse_CardLoanApplication_{CardLoanApplication: &preApprovedLoanPb.GetLoanSummaryForHomeResponse_CardLoanApplication{
		// 			LoanRequest: loanRequestSample2,
		// 		},
		// 		}},
		// 	wantErr: false,
		// 	mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
		// 		res: &multiDbProvider.GetLandingPageDataForActorResponse{
		// 			LoanHeader: &preApprovedLoanPb.LoanHeader{
		// 				LoanProgram: loanRequestSample2.GetLoanProgram(),
		// 				Vendor:      loanRequestSample2.GetVendor(),
		// 			},
		// 			LoanRequests: []*preApprovedLoanPb.LoanRequest{
		// 				loanRequestSample2,
		// 			},
		// 		},
		// 		err: nil,
		// 	},
		// },
		{
			name: "No active Loan Offer",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanSummaryForHomeRequest{ActorId: "actor-1"},
			},
			want: &preApprovedLoanPb.GetLoanSummaryForHomeResponse{
				Status:       rpc.StatusOk(),
				HomeCardData: &preApprovedLoanPb.GetLoanSummaryForHomeResponse_CardLoanOffer_{CardLoanOffer: nil},
				HomeCardType: preApprovedLoanPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_NO_OFFER,
			},
			wantErr: false,
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: &landing_provider.GetLandingPageDataForActorResponse{},
				err: nil,
			},
			mockGetOrRefreshLoanDataExistenceCache: &mockGetOrRefreshLoanDataExistenceCache{
				res: &dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: map[commontypes.Ownership]bool{
						commontypes.Ownership_MONEYVIEW_PL: true,
					},
					LoecDataExistenceMap: map[commontypes.Ownership]bool{
						commontypes.Ownership_MONEYVIEW_PL: true,
					},
				},
			},
		},
		{
			name: "failed to get loan account",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanSummaryForHomeRequest{ActorId: "actor-1"},
			},
			want: &preApprovedLoanPb.GetLoanSummaryForHomeResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetLandingPageDataForActor: &mockGetLandingPageDataForActor{
				res: nil,
				err: epifierrors.ErrPermanent,
			},
			mockGetOrRefreshLoanDataExistenceCache: &mockGetOrRefreshLoanDataExistenceCache{
				res: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLandingPageDataForActor != nil {
				md.landingProvider.EXPECT().GetLandingPageDataForActor(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLandingPageDataForActor.res, tt.mockGetLandingPageDataForActor.err)
			}
			if tt.mockLiiGetByLoanAccountIdAndType != nil {
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), gomock.Any()).Return(
					tt.mockLiiGetByLoanAccountIdAndType.res, tt.mockLiiGetByLoanAccountIdAndType.err)
			}
			if tt.mockLrByAccountIdAndType != nil {
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLrByAccountIdAndType.res, tt.mockLrByAccountIdAndType.err)
			}
			if tt.mockLseByRefIdFlowAndName != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLseByRefIdFlowAndName.res, tt.mockLseByRefIdFlowAndName.err)
			}

			if tt.mockGetOrRefreshLoanDataExistenceCache != nil {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(),
					gomock.Any()).Return(tt.mockGetOrRefreshLoanDataExistenceCache.res).Times(1)
			}

			got, err := s.GetLoanSummaryForHome(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanSummaryForHome() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetLoanSummaryForHome() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanDefaultDetails(t *testing.T) {
	t.Parallel()
	var (
		schedule1 = []*preApprovedLoanPb.LoanInstallmentPayout{
			{
				Id:                    "payout-id-1",
				LoanInstallmentInfoId: "installment-info-id-1",
				VendorInstallmentId:   "vendor-installment-id-1",
				DueDate:               &datePb.Date{Year: 2023, Month: 6, Day: 16},
				Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						BounceCharges: &money.Money{CurrencyCode: "INR", Units: 2000},
						OtherCharges:  &money.Money{CurrencyCode: "INR", Units: 2000},
					},
				},
				Principal: &money.Money{
					CurrencyCode: "INR",
					Units:        11000,
				},
				Interest: &money.Money{
					CurrencyCode: "INR",
					Units:        19000,
				},
				DueAmount: &money.Money{
					CurrencyCode: "INR",
					Units:        34000,
				},
				Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			},
			{
				Id:                    "payout-id-1",
				LoanInstallmentInfoId: "installment-info-id-1",
				VendorInstallmentId:   "vendor-installment-id-1",
				DueDate: &datePb.Date{
					Year:  2022,
					Month: 5,
					Day:   3,
				},
				Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{

					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						LatePaymentInterest: &money.Money{CurrencyCode: "INR", Units: 1500},
						BounceCharges:       &money.Money{CurrencyCode: "INR", Units: 1000},
						OtherCharges:        &money.Money{CurrencyCode: "INR", Units: 0},
					},
				},
				Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
				DueAmount: &money.Money{
					CurrencyCode: "INR",
					Units:        25500,
				},
				Principal: &money.Money{
					CurrencyCode: "INR",
					Units:        10000,
				},
				Interest: &money.Money{
					CurrencyCode: "INR",
					Units:        13000,
				},
			},
		}
		schedule2 = []*preApprovedLoanPb.LoanInstallmentPayout{
			{
				Id:                    "payout-id-1",
				LoanInstallmentInfoId: "installment-info-id-1",
				VendorInstallmentId:   "vendor-installment-id-1",
				DueDate:               &datePb.Date{Year: 2023, Month: 6, Day: 16},
				Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						BounceCharges: &money.Money{CurrencyCode: "INR", Units: 2000},
						OtherCharges:  &money.Money{CurrencyCode: "INR", Units: 2000},
					},
				},
				Principal: &money.Money{
					CurrencyCode: "INR",
					Units:        11000,
				},
				Interest: &money.Money{
					CurrencyCode: "INR",
					Units:        19000,
				},
				DueAmount: &money.Money{
					CurrencyCode: "INR",
					Units:        34000,
				},
				Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			},
			{
				Id:                    "payout-id-1",
				LoanInstallmentInfoId: "installment-info-id-1",
				VendorInstallmentId:   "vendor-installment-id-1",
				DueDate: &datePb.Date{
					Year:  2022,
					Month: 5,
					Day:   3,
				},
				Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						LatePaymentInterest: &money.Money{CurrencyCode: "INR", Units: 1500},
						BounceCharges:       &money.Money{CurrencyCode: "INR", Units: 1000},
						OtherCharges:        &money.Money{CurrencyCode: "INR", Units: 0},
					},
				},
				Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
				Principal: &money.Money{
					CurrencyCode: "INR",
					Units:        10000,
				},
				Interest: &money.Money{
					CurrencyCode: "INR",
					Units:        13000,
				},
				DueAmount: &money.Money{
					CurrencyCode: "INR",
					Units:        25500,
				},
			},
			{
				Id:                    "payout-id-1",
				LoanInstallmentInfoId: "installment-info-id-1",
				VendorInstallmentId:   "vendor-installment-id-1",
				DueDate:               &datePb.Date{Year: 2023, Month: 6, Day: 16},
				Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						BounceCharges: &money.Money{CurrencyCode: "INR", Units: 2000},
						OtherCharges:  &money.Money{CurrencyCode: "INR", Units: 2000},
					},
				},
				Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
				Principal: &money.Money{
					CurrencyCode: "INR",
					Units:        11000,
				},
				Interest: &money.Money{
					CurrencyCode: "INR",
					Units:        19000,
				},
				DueAmount: &money.Money{
					CurrencyCode: "INR",
					Units:        34000,
				},
			},
			{
				Id:                    "payout-id-1",
				LoanInstallmentInfoId: "installment-info-id-1",
				VendorInstallmentId:   "vendor-installment-id-1",
				DueDate:               &datePb.Date{Year: 2050, Month: 6, Day: 16},
				Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						BounceCharges: &money.Money{CurrencyCode: "INR", Units: 2000},
						OtherCharges:  &money.Money{CurrencyCode: "INR", Units: 2000},
					},
				},
				Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
				Principal: &money.Money{
					CurrencyCode: "INR",
					Units:        11000,
				},
				Interest: &money.Money{
					CurrencyCode: "INR",
					Units:        19000,
				},
				DueAmount: &money.Money{
					CurrencyCode: "INR",
					Units:        34000,
				},
			},
		}
		// schedule3 = []*preApprovedLoanPb.LoanInstallmentPayout{
		//	{
		//		Id:                    "payout-id-1",
		//		LoanInstallmentInfoId: "installment-info-id-1",
		//		VendorInstallmentId:   "vendor-installment-id-1",
		//		DueDate:               &datePb.Date{Year: 2023, Month: 4, Day: 3},
		//		Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
		//			ChargesApplied: &preApprovedLoanPb.ChargesApplied{
		//				BounceCharges: &money.Money{CurrencyCode: "INR", Units: 2000},
		//				OtherCharges:  &money.Money{CurrencyCode: "INR", Units: 2000},
		//			},
		//		},
		//		Principal: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        11000,
		//		},
		//		Interest: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        19000,
		//		},
		//		DueAmount: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        34000,
		//		},
		//		Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
		//	},
		//	{
		//		Id:                    "payout-id-1",
		//		LoanInstallmentInfoId: "installment-info-id-1",
		//		VendorInstallmentId:   "vendor-installment-id-1",
		//		DueDate: &datePb.Date{
		//			Year:  2022,
		//			Month: 5,
		//			Day:   3,
		//		},
		//		Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
		//
		//			ChargesApplied: &preApprovedLoanPb.ChargesApplied{
		//				LatePaymentInterest: &money.Money{CurrencyCode: "INR", Units: 10},
		//				BounceCharges:       &money.Money{CurrencyCode: "INR", Units: 10},
		//				OtherCharges:        &money.Money{CurrencyCode: "INR", Units: 0},
		//			},
		//		},
		//		Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
		//		DueAmount: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        2000,
		//		},
		//		Principal: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        1500,
		//		},
		//		Interest: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        500,
		//		},
		//		Amount: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        2000,
		//		},
		//		PayoutDate: &datePb.Date{
		//			Year:  2023,
		//			Month: 11,
		//			Day:   7,
		//		},
		//	},
		//	{
		//		Id:                    "payout-id-1",
		//		LoanInstallmentInfoId: "installment-info-id-1",
		//		VendorInstallmentId:   "vendor-installment-id-1",
		//		DueDate: &datePb.Date{
		//			Year:  2023,
		//			Month: 11,
		//			Day:   3,
		//		},
		//		Details: &preApprovedLoanPb.LoanInstallmentPayoutDetails{
		//
		//			ChargesApplied: &preApprovedLoanPb.ChargesApplied{
		//				LatePaymentInterest: &money.Money{CurrencyCode: "INR", Units: 10},
		//				BounceCharges:       &money.Money{CurrencyCode: "INR", Units: 10},
		//				OtherCharges:        &money.Money{CurrencyCode: "INR", Units: 0},
		//			},
		//		},
		//		Status: preApprovedLoanPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
		//		DueAmount: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        2000,
		//		},
		//		Principal: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        1500,
		//		},
		//		Interest: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        500,
		//		},
		//		Amount: &money.Money{
		//			CurrencyCode: "INR",
		//			Units:        2000,
		//		},
		//		PayoutDate: &datePb.Date{
		//			Year:  2023,
		//			Month: 10,
		//			Day:   7,
		//		},
		//	},
		// }
	)
	type args struct {
		ctx context.Context
		req *preApprovedLoanPb.GetLoanDefaultDetailsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *preApprovedLoanPb.GetLoanDefaultDetailsResponse
		wantErr bool
		prepare func(*args, *mockedDependencies)
	}{
		{
			name: "with multiple unpaid loan payouts",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanDefaultDetailsRequest{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						Vendor: preApprovedLoanPb.Vendor_LIQUILOANS,
					},
					LoanAccountId: "loan-account-id-1",
				},
			},
			want: &preApprovedLoanPb.GetLoanDefaultDetailsResponse{
				Status: rpc.StatusOk(),
				LoanAccount: &preApprovedLoanPb.LoanAccount{
					Id:      "account-id-1",
					ActorId: "AC221007eUwAIwoORGOc0uRRQznKPw==",
					MaturityDate: &datePb.Date{
						Year:  2024,
						Month: 6,
						Day:   16,
					},
					Details: &preApprovedLoanPb.LoanAccountDetails{
						TenureInMonths: 12,
						InterestRate:   13,
					},
					LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
						DisbursedAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
					},
					CreatedAt:     timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
					AccountNumber: "1234",
					LmsPartner:    plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX,
				},
				LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
					Id:        "installment-info-id-1",
					AccountId: "account-id-1",
				},
				DefaultDetails: &preApprovedLoanPb.LoanDefaultDetails{
					FirstDefaultDate: &datePb.Date{
						Year:  2022,
						Month: 5,
						Day:   3,
					},
					TotalDueAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        59500,
					},
					ExpectedEmiAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        59500,
					},
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						LatePaymentInterest: &money.Money{
							CurrencyCode: "INR",
							Units:        1500,
						},
						BounceCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        3000,
						},
						OtherCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        2000,
						},
					},
					ReceivedAmount: moneyPb.ZeroINR().GetPb(),
				},
				Schedule: schedule1,
				ForeclosureDetails: &preApprovedLoanPb.ForeclosureDetails{
					TotalOutstandingAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					PrincipalOutstandingAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					InterestOutstandingAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					PenaltyAmt: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					FeesAmt: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					OtherCharges: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
				},
				ContactDetails: &preApprovedLoanPb.ContactDetails{
					AlternateContactNumber: &commontypes.PhoneNumber{
						CountryCode:    converters.CountryCodeIndia,
						NationalNumber: **********,
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *mockedDependencies) {
				// f.mockCache.EXPECT().Get(gomock.Any(), gomock.Any()).Return("", errors.New("error")).Times(1)
				// f.mockCache.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), a.req.GetLoanAccountId()).Return(
					&preApprovedLoanPb.LoanAccount{
						Id:            "account-id-1",
						ActorId:       "AC221007eUwAIwoORGOc0uRRQznKPw==",
						AccountNumber: "1234",
						MaturityDate: &datePb.Date{
							Year:  2024,
							Month: 6,
							Day:   16,
						},
						Details: &preApprovedLoanPb.LoanAccountDetails{
							TenureInMonths: 12,
							InterestRate:   13,
						},
						LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
							DisbursedAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        123000,
							},
						},
						LmsPartner: plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX,
						CreatedAt:  timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
					}, nil).Times(1)

				f.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), a.req.GetLoanAccountId()).Return(
					&preApprovedLoanPb.LoanInstallmentInfo{
						Id:        "installment-info-id-1",
						AccountId: "account-id-1",
					}, nil).Times(1)

				f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "installment-info-id-1").Return(schedule1, nil).Times(1)

				f.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), "account-id-1", preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Return([]*preApprovedLoanPb.LoanRequest{}, nil).Times(2)

				f.mockLoanDataProvider.EXPECT().FetchLoanForeClosureDetailsProvider(gomock.Any(), gomock.Any(), plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX).Return(f.loanDataProvider, nil).Times(1)
				f.llVgMockClient.EXPECT().ForeClosureDetails(gomock.Any(), gomock.Any()).Return(&llVgPb.ForeClosureDetailsResponse{
					Status:  rpc.StatusOk(),
					Message: "",
					Data: &llVgPb.ForeClosureDetailsResponse_Data{
						ApplicationId: "",
						TotalOutstanding: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						PrincipalOutstanding: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						InterestOutstanding: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						PenaltyCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						FeesCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						OtherCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
					},
				}, nil).Times(1)
				f.loanApplicationDao.EXPECT().GetByActorId(gomock.Any(), "AC221007eUwAIwoORGOc0uRRQznKPw==").Return(&preApprovedLoanPb.LoanApplicant{
					PersonalDetails: &preApprovedLoanPb.PersonalDetails{
						AlternatePhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    converters.CountryCodeIndia,
							NationalNumber: **********,
						},
					},
				}, nil)
			},
		},
		{
			name: "with multiple unpaid loan payouts along with paid payouts",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanDefaultDetailsRequest{
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						Vendor: preApprovedLoanPb.Vendor_LIQUILOANS,
					},
					LoanAccountId: "loan-account-id-1",
				},
			},
			want: &preApprovedLoanPb.GetLoanDefaultDetailsResponse{
				Status: rpc.StatusOk(),
				LoanAccount: &preApprovedLoanPb.LoanAccount{
					Id:            "account-id-1",
					AccountNumber: "1234",
					ActorId:       "AC221007eUwAIwoORGOc0uRRQznKPw==",
					MaturityDate: &datePb.Date{
						Year:  2024,
						Month: 6,
						Day:   16,
					},
					Details: &preApprovedLoanPb.LoanAccountDetails{
						TenureInMonths: 12,
						InterestRate:   13,
					},
					LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
						DisbursedAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
					},
					CreatedAt:  timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
					LmsPartner: plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX,
				},
				LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
					Id:        "installment-info-id-1",
					AccountId: "account-id-1",
				},
				DefaultDetails: &preApprovedLoanPb.LoanDefaultDetails{
					FirstDefaultDate: &datePb.Date{
						Year:  2022,
						Month: 5,
						Day:   3,
					},
					TotalDueAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        59500,
					},
					ChargesApplied: &preApprovedLoanPb.ChargesApplied{
						LatePaymentInterest: &money.Money{
							CurrencyCode: "INR",
							Units:        1500,
						},
						BounceCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        3000,
						},
						OtherCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        2000,
						},
					},
					ReceivedAmount: moneyPb.ZeroINR().GetPb(),
					ExpectedEmiAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        59500,
					},
				},
				Schedule: schedule2,
				ForeclosureDetails: &preApprovedLoanPb.ForeclosureDetails{
					TotalOutstandingAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					PrincipalOutstandingAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					InterestOutstandingAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					PenaltyAmt: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					FeesAmt: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
					OtherCharges: &money.Money{
						CurrencyCode: "INR",
						Units:        123000,
					},
				},
				ContactDetails: &preApprovedLoanPb.ContactDetails{
					AlternateContactNumber: &commontypes.PhoneNumber{
						CountryCode:    converters.CountryCodeIndia,
						NationalNumber: **********,
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *mockedDependencies) {
				// f.mockCache.EXPECT().Get(gomock.Any(), gomock.Any()).Return("", errors.New("error")).Times(1)
				// f.mockCache.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				f.loanAccountDao.EXPECT().GetById(gomock.Any(), a.req.GetLoanAccountId()).Return(
					&preApprovedLoanPb.LoanAccount{
						Id:            "account-id-1",
						ActorId:       "AC221007eUwAIwoORGOc0uRRQznKPw==",
						AccountNumber: "1234",
						MaturityDate: &datePb.Date{
							Year:  2024,
							Month: 6,
							Day:   16,
						},
						Details: &preApprovedLoanPb.LoanAccountDetails{
							TenureInMonths: 12,
							InterestRate:   13,
						},
						LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
							DisbursedAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        123000,
							},
						},
						LmsPartner: plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX,
						CreatedAt:  timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
					}, nil).Times(1)

				f.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), a.req.GetLoanAccountId()).Return(
					&preApprovedLoanPb.LoanInstallmentInfo{
						Id:        "installment-info-id-1",
						AccountId: "account-id-1",
					}, nil).Times(1)

				f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "installment-info-id-1").Return(
					schedule2, nil).Times(1)

				f.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), "account-id-1", preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Return([]*preApprovedLoanPb.LoanRequest{}, nil).Times(2)
				f.mockLoanDataProvider.EXPECT().FetchLoanForeClosureDetailsProvider(gomock.Any(), gomock.Any(), plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX).Return(f.loanDataProvider, nil).Times(1)
				f.llVgMockClient.EXPECT().ForeClosureDetails(gomock.Any(), gomock.Any()).Return(&llVgPb.ForeClosureDetailsResponse{
					Status:  rpc.StatusOk(),
					Message: "",
					Data: &llVgPb.ForeClosureDetailsResponse_Data{
						ApplicationId: "",
						TotalOutstanding: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						PrincipalOutstanding: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						InterestOutstanding: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						PenaltyCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						FeesCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
						OtherCharges: &money.Money{
							CurrencyCode: "INR",
							Units:        123000,
						},
					},
				}, nil).Times(1)
				f.loanApplicationDao.EXPECT().GetByActorId(gomock.Any(), "AC221007eUwAIwoORGOc0uRRQznKPw==").Return(&preApprovedLoanPb.LoanApplicant{
					PersonalDetails: &preApprovedLoanPb.PersonalDetails{
						AlternatePhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    converters.CountryCodeIndia,
							NationalNumber: **********,
						},
					},
				}, nil)
			},
		},
		// {
		//	name: "2 payouts in allocation, one was paid after allocation",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &preApprovedLoanPb.GetLoanDefaultDetailsRequest{
		//			LoanHeader: &preApprovedLoanPb.LoanHeader{
		//				Vendor: preApprovedLoanPb.Vendor_LIQUILOANS,
		//			},
		//			LoanAccountId: "loan-account-id-1",
		//		},
		//	},
		//	want: &preApprovedLoanPb.GetLoanDefaultDetailsResponse{
		//		Status: rpc.StatusOk(),
		//		LoanAccount: &preApprovedLoanPb.LoanAccount{
		//			Id:      "account-id-1",
		//			ActorId: "AC221007eUwAIwoORGOc0uRRQznKPw==",
		//			MaturityDate: &datePb.Date{
		//				Year:  2024,
		//				Month: 6,
		//				Day:   16,
		//			},
		//			Details: &preApprovedLoanPb.LoanAccountDetails{
		//				TenureInMonths: 12,
		//				InterestRate:   13,
		//			},
		//			LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
		//				DisbursedAmount: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        123000,
		//				},
		//			},
		//			CreatedAt: timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
		//		},
		//		LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
		//			Id:        "installment-info-id-1",
		//			AccountId: "account-id-1",
		//		},
		//		DefaultDetails: &preApprovedLoanPb.LoanDefaultDetails{
		//			FirstDefaultDate: &datePb.Date{
		//				Year:  2022,
		//				Month: 5,
		//				Day:   3,
		//			},
		//			TotalDueAmount: &money.Money{
		//				CurrencyCode: "INR",
		//				Units:        2000,
		//			},
		//			ExpectedEmiAmount: &money.Money{
		//				CurrencyCode: "INR",
		//				Units:        2000,
		//			},
		//			ChargesApplied: &preApprovedLoanPb.ChargesApplied{
		//				LatePaymentInterest: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        10,
		//				},
		//				BounceCharges: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        10,
		//				},
		//				OtherCharges: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        0,
		//				},
		//			},
		//			ReceivedAmount: &money.Money{
		//				CurrencyCode: "INR",
		//				Units:        2000,
		//			},
		//		},
		//		Schedule: schedule3,
		//	},
		//	wantErr: false,
		//	prepare: func(a *args, f *mockedDependencies) {
		//		f.loanAccountDao.EXPECT().GetById(gomock.Any(), a.req.GetLoanAccountId()).Return(
		//			&preApprovedLoanPb.LoanAccount{
		//				Id:      "account-id-1",
		//				ActorId: "AC221007eUwAIwoORGOc0uRRQznKPw==",
		//				MaturityDate: &datePb.Date{
		//					Year:  2024,
		//					Month: 6,
		//					Day:   16,
		//				},
		//				Details: &preApprovedLoanPb.LoanAccountDetails{
		//					TenureInMonths: 12,
		//					InterestRate:   13,
		//				},
		//				LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
		//					DisbursedAmount: &money.Money{
		//						CurrencyCode: "INR",
		//						Units:        123000,
		//					},
		//				},
		//				CreatedAt: timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
		//			}, nil).Times(1)
		//
		//		f.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), a.req.GetLoanAccountId()).Return(
		//			&preApprovedLoanPb.LoanInstallmentInfo{
		//				Id:        "installment-info-id-1",
		//				AccountId: "account-id-1",
		//			}, nil).Times(1)
		//
		//		f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "installment-info-id-1").Return(schedule3, nil).Times(1)
		//
		//	},
		// },
		// {
		//	name: "2 payouts in allocation, but paid on 1st Nov",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &preApprovedLoanPb.GetLoanDefaultDetailsRequest{
		//			LoanHeader: &preApprovedLoanPb.LoanHeader{
		//				Vendor: preApprovedLoanPb.Vendor_LIQUILOANS,
		//			},
		//			LoanAccountId: "loan-account-id-1",
		//		},
		//	},
		//	want: &preApprovedLoanPb.GetLoanDefaultDetailsResponse{
		//		Status: rpc.StatusOk(),
		//		LoanAccount: &preApprovedLoanPb.LoanAccount{
		//			Id:      "account-id-1",
		//			ActorId: "AC221007eUwAIwoORGOc0uRRQznKPw==",
		//			MaturityDate: &datePb.Date{
		//				Year:  2024,
		//				Month: 6,
		//				Day:   16,
		//			},
		//			Details: &preApprovedLoanPb.LoanAccountDetails{
		//				TenureInMonths: 12,
		//				InterestRate:   13,
		//			},
		//			LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
		//				DisbursedAmount: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        123000,
		//				},
		//			},
		//			CreatedAt: timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
		//		},
		//		LoanInstallmentInfo: &preApprovedLoanPb.LoanInstallmentInfo{
		//			Id:        "installment-info-id-1",
		//			AccountId: "account-id-1",
		//		},
		//		DefaultDetails: &preApprovedLoanPb.LoanDefaultDetails{
		//			FirstDefaultDate: &datePb.Date{
		//				Year:  2022,
		//				Month: 5,
		//				Day:   3,
		//			},
		//			TotalDueAmount: &money.Money{
		//				CurrencyCode: "INR",
		//				Units:        2000,
		//			},
		//			ExpectedEmiAmount: &money.Money{
		//				CurrencyCode: "INR",
		//				Units:        2000,
		//			},
		//			ChargesApplied: &preApprovedLoanPb.ChargesApplied{
		//				LatePaymentInterest: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        10,
		//				},
		//				BounceCharges: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        10,
		//				},
		//				OtherCharges: &money.Money{
		//					CurrencyCode: "INR",
		//					Units:        0,
		//				},
		//			},
		//			ReceivedAmount: &money.Money{
		//				CurrencyCode: "INR",
		//				Units:        2000,
		//			},
		//		},
		//		Schedule: schedule3,
		//	},
		//	wantErr: false,
		//	prepare: func(a *args, f *mockedDependencies) {
		//		f.loanAccountDao.EXPECT().GetById(gomock.Any(), a.req.GetLoanAccountId()).Return(
		//			&preApprovedLoanPb.LoanAccount{
		//				Id:      "account-id-1",
		//				ActorId: "AC221007eUwAIwoORGOc0uRRQznKPw==",
		//				MaturityDate: &datePb.Date{
		//					Year:  2024,
		//					Month: 6,
		//					Day:   16,
		//				},
		//				Details: &preApprovedLoanPb.LoanAccountDetails{
		//					TenureInMonths: 12,
		//					InterestRate:   13,
		//				},
		//				LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
		//					DisbursedAmount: &money.Money{
		//						CurrencyCode: "INR",
		//						Units:        123000,
		//					},
		//				},
		//				CreatedAt: timestampPb.New(time.Date(2023, 6, 16, 0, 0, 0, 0, datetime.IST)),
		//			}, nil).Times(1)
		//
		//		f.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), a.req.GetLoanAccountId()).Return(
		//			&preApprovedLoanPb.LoanInstallmentInfo{
		//				Id:        "installment-info-id-1",
		//				AccountId: "account-id-1",
		//			}, nil).Times(1)
		//
		//		f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "installment-info-id-1").Return(schedule3, nil).Times(1)
		//
		//	},
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()
			if tt.prepare != nil {
				tt.prepare(&tt.args, md)
			}
			got, err := s.GetLoanDefaultDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanDefaultDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}

func TestService_GetVendorApplicantId(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	type args struct {
		req *palPb.GetVendorApplicantIdRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *palPb.GetVendorApplicantIdResponse
		wantErr    bool
		setupMocks func(md *mockedDependencies)
	}{
		{
			name: "#1 Successfully able to fetch vendor applicant id",
			args: args{
				req: &palPb.GetVendorApplicantIdRequest{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
					ActorId: dummyActorId,
				},
			},
			want: &palPb.GetVendorApplicantIdResponse{
				Status:            rpc.StatusInternal(),
				VendorApplicantId: dummyVendorApplicantId,
			},
			wantErr: false,
			setupMocks: func(md *mockedDependencies) {
				md.loanApplicationDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), dummyActorId, palPb.Vendor_LIQUILOANS, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, gomock.Any()).Return(&palPb.LoanApplicant{
					ActorId:           dummyActorId,
					VendorApplicantId: dummyVendorApplicantId,
					LoanProgram:       palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				}, nil).Times(1)
			},
		},
		{
			name: "#2 error, record not found",
			args: args{
				req: &palPb.GetVendorApplicantIdRequest{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
					ActorId: dummyActorId,
				},
			},
			want: &palPb.GetVendorApplicantIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
			setupMocks: func(md *mockedDependencies) {
				md.loanApplicationDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), dummyActorId, palPb.Vendor_LIQUILOANS, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
		},
		{
			name: "#3 error, internal error received from dao",
			args: args{
				req: &palPb.GetVendorApplicantIdRequest{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
					ActorId: dummyActorId,
				},
			},
			want: &palPb.GetVendorApplicantIdResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			setupMocks: func(md *mockedDependencies) {
				md.loanApplicationDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), dummyActorId, palPb.Vendor_LIQUILOANS, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, gomock.Any()).Return(nil, epifierrors.ErrInvalidArgument).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.GetVendorApplicantId(ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVendorApplicantId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}

func TestService_GetLoanDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.GetLoanDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetLoanDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return Status as Record Not Found when unable to fetch loan account",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan account",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "should return Status as Record Not Found when unable to fetch loan installment info",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan installment info",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "should return Status as Record Not Found when unable to fetch loan request by LoanAccountID and Type",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				lii := &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
					Status:    preApprovedLoanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
				}
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(lii, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), "loan-id", palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan request by LoanAccountID and Type",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				lii := &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
					Status:    preApprovedLoanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
				}
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(lii, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), "loan-id", palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "should return Status as Record Not Found when unable to fetch loan offer",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				lii := &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
					Status:    preApprovedLoanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
				}
				lr := []*palPb.LoanRequest{
					{
						Id:      "loan-req-1",
						ActorId: "actor-1",
						OfferId: "offer-id",
					},
				}
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(lii, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), "loan-id", palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan offer",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanDetailsRequest{
					LoanId:  "loan-id",
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				lii := &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
					Status:    preApprovedLoanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
				}
				lr := []*palPb.LoanRequest{
					{
						Id:      "loan-req-1",
						ActorId: "actor-1",
						OfferId: "offer-id",
					},
				}
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(lii, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(gomock.Any(), "loan-id", palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Return(lr, nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.GetLoanDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetTransactionReceipt(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.GetTransactionReceiptRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetTransactionReceiptResponse
		wantErr    bool
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan activity",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetTransactionReceiptRequest{
					ActorId:        "actor-1",
					LoanActivityId: "loan-activity-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanActivityDao.EXPECT().GetById(gomock.Any(), "loan-activity-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetTransactionReceiptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan account",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetTransactionReceiptRequest{
					ActorId:        "actor-1",
					LoanActivityId: "loan-activity-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanActivityDao.EXPECT().GetById(gomock.Any(), "loan-activity-id").Return(&palPb.LoanActivity{
					Id:            "loan-activity-id",
					LoanAccountId: "loan-account-id",
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetTransactionReceiptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetTransactionReceiptRequest{
					ActorId:        "actor-1",
					LoanActivityId: "loan-activity-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanActivityDao.EXPECT().GetById(gomock.Any(), "loan-activity-id").Return(&palPb.LoanActivity{
					Id:            "loan-activity-id",
					LoanAccountId: "loan-account-id",
					ReferenceId:   "transaction-id",
					Details: &palPb.LoanActivityDetails{
						TransactionId: "",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10000,
							Nanos:        0,
						},
						TxnParticulars: "txn-particular",
						TxnTime: &timestampPb.Timestamp{
							Seconds: ********,
							Nanos:   0,
						},
						ActivityTypeDetails: nil,
						Utr:                 "",
					},
					Type: palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM,
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id").Return(&palPb.LoanAccount{
					Id:      "loan-account-id",
					ActorId: "actor-1",
					Details: &palPb.LoanAccountDetails{
						LoanName: "Loan #0",
					},
				}, nil)
				md.paymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "transaction-id"},
					GetReqInfo: false,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Utr: "utr-1",
						CreatedAt: &timestampPb.Timestamp{
							Seconds: ********,
							Nanos:   0,
						},
					},
				}, nil)
			},
			want: &palPb.GetTransactionReceiptResponse{
				Status: rpc.StatusOk(),
				Transaction: &palPb.TransactionActivity{
					PaymentTimestamp: &timestampPb.Timestamp{
						Seconds: ********,
						Nanos:   0,
					},
					Status: palPb.TransactionActivity_SUCCESS,
					Details: &palPb.TransactionActivity_Details{
						Type: palPb.TransactionActivity_Details_LUMPSUM,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10000,
							Nanos:        0,
						},
					},
					Utr:            "utr-1",
					LoanActivityId: "loan-activity-id",
					TxnParticulars: "txn-particular",
					LoanHeader:     &palPb.LoanHeader{},
				},
				LoanName: "Loan #0",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.GetTransactionReceipt(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTransactionReceipt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTransactionReceipt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanIdByHeaderAndActorId(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.GetLoanIdByHeaderAndActorIdRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetLoanIdByHeaderAndActorIdResponse
		wantErr    bool
		err        error
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan account",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanIdByHeaderAndActorIdRequest{
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetByActorIdVendorAndLoanProgram(gomock.Any(), "actor-1", palPb.Vendor_LIQUILOANS, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN).Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanIdByHeaderAndActorIdResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: true,
			err:     epifierrors.ErrTransient,
		},

		{
			name: "Should return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanIdByHeaderAndActorIdRequest{
					ActorId: "actor-1",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetByActorIdVendorAndLoanProgram(gomock.Any(), "actor-1", palPb.Vendor_LIQUILOANS, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN).Return([]*palPb.LoanAccount{
					{
						Id:      "loan-id",
						ActorId: "actor-1",
					},
				}, nil)
			},
			want: &palPb.GetLoanIdByHeaderAndActorIdResponse{
				Status: rpc.StatusOk(),
				LoanId: "loan-id",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.GetLoanIdByHeaderAndActorId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanIdByHeaderAndActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanIdByHeaderAndActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanInstallmentPayoutDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.GetLoanInstallmentPayoutDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetLoanInstallmentPayoutDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return Status as Record Not Found when unable to fetch LIP",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanInstallmentPayoutDetailsRequest{
					LoanInstallmentPayoutId: "lip-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanInstallmentPayoutDao.EXPECT().GetById(gomock.Any(), "lip-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanInstallmentPayoutDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching LIP",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanInstallmentPayoutDetailsRequest{
					LoanInstallmentPayoutId: "lip-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanInstallmentPayoutDao.EXPECT().GetById(gomock.Any(), "lip-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanInstallmentPayoutDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching LII",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanInstallmentPayoutDetailsRequest{
					LoanInstallmentPayoutId: "lip-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanInstallmentPayoutDao.EXPECT().GetById(gomock.Any(), "lip-id").Return(&palPb.LoanInstallmentPayout{
					Id:                    "lip-id",
					LoanInstallmentInfoId: "lii-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetById(gomock.Any(), "lii-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanInstallmentPayoutDetailsResponse{
				Status: rpc.StatusInternal(),
				LoanInstallmentPayout: &palPb.LoanInstallmentPayout{
					Id:                    "lip-id",
					LoanInstallmentInfoId: "lii-id",
				},
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan account",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanInstallmentPayoutDetailsRequest{
					LoanInstallmentPayoutId: "lip-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanInstallmentPayoutDao.EXPECT().GetById(gomock.Any(), "lip-id").Return(&palPb.LoanInstallmentPayout{
					Id:                    "lip-id",
					LoanInstallmentInfoId: "lii-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetById(gomock.Any(), "lii-id").Return(&palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanInstallmentPayoutDetailsResponse{
				Status: rpc.StatusInternal(),
				LoanInstallmentPayout: &palPb.LoanInstallmentPayout{
					Id:                    "lip-id",
					LoanInstallmentInfoId: "lii-id",
				},
				LoanInstallmentInfo: &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				},
			},
			wantErr: false,
		},

		{
			name: "Should return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanInstallmentPayoutDetailsRequest{
					LoanInstallmentPayoutId: "lip-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lip := &palPb.LoanInstallmentPayout{
					Id:                    "lip-id",
					LoanInstallmentInfoId: "lii-id",
				}
				lii := &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				}
				la := &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				}
				md.loanInstallmentPayoutDao.EXPECT().GetById(gomock.Any(), "lip-id").Return(lip, nil)
				md.loanInstallmentInfo.EXPECT().GetById(gomock.Any(), "lii-id").Return(lii, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(la, nil)
			},
			want: &palPb.GetLoanInstallmentPayoutDetailsResponse{
				Status: rpc.StatusOk(),
				LoanInstallmentPayout: &palPb.LoanInstallmentPayout{
					Id:                    "lip-id",
					LoanInstallmentInfoId: "lii-id",
				},
				LoanInstallmentInfo: &palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				},
				LoanAccount: &palPb.LoanAccount{
					Id:      "loan-id",
					ActorId: "actor-1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.GetLoanInstallmentPayoutDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanInstallmentPayoutDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanInstallmentPayoutDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanSchedule(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.GetLoanScheduleRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetLoanScheduleResponse
		wantErr    bool
	}{
		{
			name: "should return Status as Record Not Found when unable to fetch Loan Account",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching Loan Accounts",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "should return Status as Record Not Found when unable to fetch Loan Installment Info",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(&palPb.LoanAccount{
					Id: "loan-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching Loan Installment Info",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(&palPb.LoanAccount{
					Id: "loan-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "should return Status as Record Not Found when unable to fetch Loan Installment Payout",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(&palPb.LoanAccount{
					Id: "loan-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(&palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				}, nil)
				md.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "lii-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching Loan Installment Payout",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(&palPb.LoanAccount{
					Id: "loan-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(&palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				}, nil)
				md.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "lii-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &palPb.GetLoanScheduleRequest{
					LoanId: "loan-id",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-id").Return(&palPb.LoanAccount{
					Id: "loan-id",
				}, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-id").Return(&palPb.LoanInstallmentInfo{
					Id:        "lii-id",
					AccountId: "loan-id",
				}, nil)
				md.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "lii-id").Return([]*palPb.LoanInstallmentPayout{
					{
						Id:                    "lip-id",
						LoanInstallmentInfoId: "lii-id",
					},
				}, nil)
			},
			want: &palPb.GetLoanScheduleResponse{
				Status: rpc.StatusOk(),
				Schedule: []*palPb.LoanInstallmentPayout{
					{
						Id:                    "lip-id",
						LoanInstallmentInfoId: "lii-id",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		s, md, assertTest := newPalServiceWithMocks(t)
		defer assertTest()

		tt.setupMocks(md)
		t.Run(tt.name, func(t *testing.T) {
			got, err := s.GetLoanSchedule(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanSchedule() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanSchedule() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_AddEmploymentDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.AddEmploymentDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.AddEmploymentDetailsResponse
		wantErr    bool
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan request",
			args: args{
				ctx: context.Background(),
				req: &palPb.AddEmploymentDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.AddEmploymentDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan step by ref id",
			args: args{
				ctx: context.Background(),
				req: &palPb.AddEmploymentDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
				).Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.AddEmploymentDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.AddEmploymentDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddEmploymentDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddEmploymentDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_AddAddressDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.AddAddressDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.AddAddressDetailsResponse
		wantErr    bool
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan request",
			args: args{
				ctx: context.Background(),
				req: &palPb.AddAddressDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan step by ref id",
			args: args{
				ctx: context.Background(),
				req: &palPb.AddAddressDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_ABFL,
					},
					Address: &typesPb.PostalAddress{PostalCode: "560037"},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:          "loan-req-id",
					ActorId:     "actor-1",
					Vendor:      palPb.Vendor_ABFL,
					LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS,
				).Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in updating loan step",
			args: args{
				ctx: context.Background(),
				req: &palPb.AddAddressDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_ABFL,
					},
					Address: &typesPb.PostalAddress{PostalCode: "560037"},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:          "loan-req-id",
					ActorId:     "actor-1",
					Vendor:      palPb.Vendor_ABFL,
					LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				}
				lse := &preApprovedLoanPb.LoanStepExecution{
					ActorId: "actor-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS,
				).Return(lse, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{Status: rpc.StatusOk()}, nil)
				md.usersClient.EXPECT().UpdateAddress(gomock.Any(), gomock.Any()).Return(&userPb.UpdateAddressResponse{Status: rpc.StatusOk()}, nil)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), lse,
					[]palPb.LoanStepExecutionFieldMask{
						palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
					},
				).Return(epifierrors.ErrTransient)
			},
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.AddAddressDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddAddressDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddAddressDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_VerifyDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.VerifyDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.VerifyDetailsResponse
		wantErr    bool
		err        error
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan request",
			args: args{
				ctx: context.Background(),
				req: &palPb.VerifyDetailsRequest{
					ReqId:   "loan-req-id",
					ActorId: "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							Name: &commontypes.Name{
								FirstName: "first",
							},
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
						},
					},
					Status: rpc.StatusOk(),
				}
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)

				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.VerifyDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Transient error in fetching loan step by ref id",
			args: args{
				ctx: context.Background(),
				req: &palPb.VerifyDetailsRequest{
					ReqId:   "loan-req-id",
					ActorId: "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							Name: &commontypes.Name{
								FirstName: "first",
							},
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
						},
					},
					Status: rpc.StatusOk(),
				}
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
				}
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)

				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION,
				).Return(nil, epifierrors.ErrTransient)
			},
			want:    &palPb.VerifyDetailsResponse{},
			wantErr: true,
			err:     epifierrors.ErrTransient,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.VerifyDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VerifyDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_VerifyCkycDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palPb.VerifyCkycDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.VerifyCkycDetailsResponse
		wantErr    bool
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan request",
			args: args{
				ctx: context.Background(),
				req: &palPb.VerifyCkycDetailsRequest{
					LoanReqId: "loan-req-id",
					ActorId:   "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.VerifyCkycDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan step by ref id",
			args: args{
				ctx: context.Background(),
				req: &palPb.VerifyCkycDetailsRequest{
					LoanReqId: "loan-req-id",
					ActorId:   "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
				).Return(nil, epifierrors.ErrTransient)
			},
			want: &palPb.VerifyCkycDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in updating loan step",
			args: args{
				ctx: context.Background(),
				req: &palPb.VerifyCkycDetailsRequest{
					LoanReqId: "loan-req-id",
					ActorId:   "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
				}
				lse := &preApprovedLoanPb.LoanStepExecution{
					ActorId: "actor-1",
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
				).Return(lse, nil)

				lse.SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED
				lse.Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), lse, []preApprovedLoanPb.LoanStepExecutionFieldMask{
					preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
					preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				}).Return(epifierrors.ErrTransient)
			},
			want: &palPb.VerifyCkycDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.VerifyCkycDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyCkycDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VerifyCkycDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanReviewDetails(t *testing.T) {
	t.Parallel()
	const LoecExpiryDuration = 30 * 24 * time.Hour
	type args struct {
		ctx context.Context
		req *palPb.GetLoanReviewDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetLoanReviewDetailsResponse
		wantErr    bool
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan request",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanReviewDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(nil, epifierrors.ErrTransient)
			},
			want: &preApprovedLoanPb.GetLoanReviewDetailsResponse{
				Status:          rpc.StatusInternal(),
				PersonalDetails: &palPb.GetLoanReviewDetailsResponse_PersonalDetails{},
				ContactDetails:  &palPb.GetLoanReviewDetailsResponse_ContactDetails{},
				OnboardingData:  &palPb.OnboardingData{},
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan step",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanReviewDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
					Type:    palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
					Vendor:  palPb.Vendor_LIQUILOANS,
				}
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(3)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							Name: &commontypes.Name{
								FirstName: "first",
							},
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email: "<EMAIL>",
							KycName: &commontypes.Name{
								FirstName: "first",
							},
						},
					},
					Status: rpc.StatusOk(),
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)

				// RPC HELPER begins
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)
				// RPC HELPER ends

				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB).Return(nil, epifierrors.ErrTransient)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, status.Error(codes.NotFound, "account not found"))
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoanReviewDetailsResponse{
				Status:          rpc.StatusInternal(),
				PersonalDetails: &palPb.GetLoanReviewDetailsResponse_PersonalDetails{},
				ContactDetails: &palPb.GetLoanReviewDetailsResponse_ContactDetails{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					EmailId: "<EMAIL>",
				},
				OnboardingData: &palPb.OnboardingData{},
			},
			wantErr: false,
		},

		{
			name: "Should return Status as Internal when getting error in fetching loan step",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanReviewDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
					Type:    palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
					Vendor:  palPb.Vendor_LIQUILOANS,
				}
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(3)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				applicantLse := &preApprovedLoanPb.LoanStepExecution{
					Id:       "step-1",
					ActorId:  "actor-1",
					RefId:    "ref-1",
					OrchId:   "client-req-1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								Pan: "**********",
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
								Dob: &datePb.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
							},
						},
						VendorData:            "",
						VendorSpecificDetails: nil,
					},
				}
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							GivenName: &commontypes.Name{
								FirstName: "first",
							},
							GivenGender: types.Gender_MALE,
							PAN:         "**********",
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							DateOfBirth: &datePb.Date{
								Year:  2000,
								Month: 1,
								Day:   1,
							},
							Email: "<EMAIL>",
							KycName: &commontypes.Name{
								FirstName: "first",
							},
						},
					},
					Status: rpc.StatusOk(),
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)

				// RPC HELPER begins
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)
				// RPC HELPER ends

				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB,
				).Return(applicantLse, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
				).Return(nil, epifierrors.ErrTransient)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, status.Error(codes.NotFound, "account not found"))
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoanReviewDetailsResponse{
				Status: rpc.StatusInternal(),
				PersonalDetails: &palPb.GetLoanReviewDetailsResponse_PersonalDetails{
					Name: &commontypes.Name{
						FirstName: "first",
					},
					Gender: types.Gender_MALE,
					Pan:    "**********",
					Dob: &datePb.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				},
				ContactDetails: &palPb.GetLoanReviewDetailsResponse_ContactDetails{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					EmailId: "<EMAIL>",
				},
				OnboardingData: &palPb.OnboardingData{},
			},
			wantErr: false,
		},

		{
			name: "Should return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoanReviewDetailsRequest{
					LoanRequestId: "loan-req-id",
					ActorId:       "actor-1",
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				lr := &preApprovedLoanPb.LoanRequest{
					Id:      "loan-req-id",
					ActorId: "actor-1",
					Type:    palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
					Vendor:  palPb.Vendor_LIQUILOANS,
				}
				applicantLse := &preApprovedLoanPb.LoanStepExecution{
					Id:       "step-1",
					ActorId:  "actor-1",
					RefId:    "ref-1",
					OrchId:   "client-req-1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{
							ApplicantData: &palPb.ApplicantData{
								Pan: "**********",
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
								Dob: &datePb.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
							},
						},
						VendorData:            "",
						VendorSpecificDetails: nil,
					},
				}
				addDetailsLse := &palPb.LoanStepExecution{
					Id:      "step-1",
					ActorId: "actor-1",
					RefId:   "ref-1",
					OrchId:  "client-req-1",
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_OnboardingData{
							OnboardingData: &palPb.OnboardingData{
								AddressDetails:    nil,
								EmploymentDetails: nil,
								BankingDetails:    nil,
							},
						},
					},
				}
				userRes := &userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							GivenName: &commontypes.Name{
								FirstName: "first",
							},
							GivenGender: types.Gender_MALE,
							PAN:         "**********",
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							DateOfBirth: &datePb.Date{
								Year:  2000,
								Month: 1,
								Day:   1,
							},
							Email: "<EMAIL>",
							KycName: &commontypes.Name{
								FirstName: "first",
							},
						},
					},
					Status: rpc.StatusOk(),
				}
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "loan-req-id").Return(lr, nil)

				// RPC HELPER begins
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "user-1"},
				}).Return(userRes, nil)
				// RPC HELPER ends

				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB,
				).Return(applicantLse, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), "loan-req-id",
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
				).Return(addDetailsLse, nil)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(3)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), "actor-1", []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, LoecExpiryDuration, true).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, status.Error(codes.NotFound, "account not found"))
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoanReviewDetailsResponse{
				Status: rpc.StatusOk(),
				PersonalDetails: &palPb.GetLoanReviewDetailsResponse_PersonalDetails{
					Name: &commontypes.Name{
						FirstName: "first",
					},
					Gender: types.Gender_MALE,
					Pan:    "**********",
					Dob: &datePb.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				},
				ContactDetails: &palPb.GetLoanReviewDetailsResponse_ContactDetails{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					EmailId: "<EMAIL>",
				},
				OnboardingData: &palPb.OnboardingData{
					AddressDetails:    nil,
					EmploymentDetails: nil,
					BankingDetails:    nil,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.GetLoanReviewDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanReviewDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanReviewDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoansUserStatus(t *testing.T) {
	t.Parallel()
	currentDate := time.Now()
	type args struct {
		ctx context.Context
		req *palPb.GetLoansUserStatusRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetLoansUserStatusResponse
		wantErr    bool
	}{
		{
			name: "Should return Status as Internal when getting error in fetching loan requests",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Should return not a loans user status when no requests are found",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpc.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_NOT_AN_ACTIVE_LOANS_USER,
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan account",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Should return active loan application status when loan request is pending",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
						},
					},
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_APPLICATION,
				Status:          rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "Should return Status as Internal when getting error in fetching loan installment info",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_LIQUILOANS_PL {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-1",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					}, nil
				})
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-1").Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "When actor is in NOT DPD - PRE_DUE state",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_LIQUILOANS_PL {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-1",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					}, nil
				})
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-1").Return(&palPb.LoanInstallmentInfo{
					Details: &palPb.LoanInstallmentInfoDetails{
						GracePeriod: 10,
					},
					NextInstallmentDate: &datePb.Date{
						Year:  int32(currentDate.AddDate(0, 0, 5).Year()),
						Month: int32(currentDate.AddDate(0, 0, 5).Month()),
						Day:   int32(currentDate.AddDate(0, 0, 5).Day()),
					},
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpc.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT,
				IsLoanOverdue:   false,
			},
			wantErr: false,
		},
		{
			name: "NOT DPD - EARLY_PRE_PAYMENT",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_LIQUILOANS_PL {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-1",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					}, nil
				})
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-1").Return(&palPb.LoanInstallmentInfo{
					Details: &palPb.LoanInstallmentInfoDetails{
						GracePeriod: 10,
					},
					NextInstallmentDate: &datePb.Date{
						Year:  int32(currentDate.AddDate(0, 0, 15).Year()),
						Month: int32(currentDate.AddDate(0, 0, 15).Month()),
						Day:   int32(currentDate.AddDate(0, 0, 15).Day()),
					},
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpc.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT,
				IsLoanOverdue:   false,
			},
			wantErr: false,
		},
		{
			name: "DPD - LATE_DPD",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_LIQUILOANS_PL {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-1",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					}, nil
				})
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-1").Return(&palPb.LoanInstallmentInfo{
					Details: &palPb.LoanInstallmentInfoDetails{
						GracePeriod: 10,
					},
					NextInstallmentDate: &datePb.Date{
						Year:  int32(currentDate.AddDate(0, 0, -12).Year()),
						Month: int32(currentDate.AddDate(0, 0, -12).Month()),
						Day:   int32(currentDate.AddDate(0, 0, -12).Day()),
					},
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpc.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT,
				IsLoanOverdue:   true,
			},
			wantErr: false,
		},
		{
			name: "DPD - EARLY_DPD",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_LIQUILOANS_PL {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-1",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
					}, nil
				})
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-1").Return(&palPb.LoanInstallmentInfo{
					Details: &palPb.LoanInstallmentInfoDetails{
						GracePeriod: 10,
					},
					NextInstallmentDate: &datePb.Date{
						Year:  int32(currentDate.AddDate(0, 0, -3).Year()),
						Month: int32(currentDate.AddDate(0, 0, -3).Month()),
						Day:   int32(currentDate.AddDate(0, 0, -3).Day()),
					},
				}, nil)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpc.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT,
				IsLoanOverdue:   true,
			},
			wantErr: false,
		},
		{
			name: "Should return loan account closed status when all accounts are closed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetLoansUserStatusRequest{
					ActorId: "actor-id-1",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.dataExistenceManager.EXPECT().GetOrRefreshLoanDataExistenceCache(gomock.Any(), "actor-id-1").Return(&dataExistenceManager.DataExistenceDetails{
					DataExistenceMap: nil,
				})
				md.multiDBProvider.EXPECT().CheckAndGetLoanRequestsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "actor-id-1",
					Statuses:     append(preapprovedloan.NonTerminalLrStatuses, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
					Types:        []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
					LoanPrograms: []palPb.LoanProgram{},
				}).Return(&multiDbProvider.CheckAndGetLoanRequestsForActorResponse{
					Requests: []*palPb.LoanRequest{
						{
							LoanAccountId: "loan-account-id-1",
							Vendor:        palPb.Vendor_LIQUILOANS,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
						{
							LoanAccountId: "loan-account-id-2",
							Vendor:        palPb.Vendor_FIFTYFIN,
							Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
						},
					},
				}, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-1").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_LIQUILOANS_PL {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-1",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
					}, nil
				}).Times(1)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "loan-account-id-2").DoAndReturn(func(ctx context.Context, Id string) (*palPb.LoanAccount, error) {
					if epificontext.OwnershipFromContext[context.Context](ctx) != commontypes.Ownership_FIFTYFIN_LAMF {
						return nil, epifierrors.ErrInvalidArgument
					}
					return &palPb.LoanAccount{
						Id:     "loan-account-id-2",
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
					}, nil
				}).Times(1)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-1").Return(&palPb.LoanInstallmentInfo{}, nil)
				md.loanInstallmentInfo.EXPECT().GetByActiveAccountId(gomock.Any(), "loan-account-id-2").Return(&palPb.LoanInstallmentInfo{}, nil)
			},
			want: &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpc.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_CLOSED_LOAN_ACCOUNT,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()

			tt.setupMocks(md)
			got, err := s.GetLoansUserStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoansUserStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoansUserStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ApplyForLoan1(t *testing.T) {
	ctx := context.Background()
	type args struct {
		req *palPb.ApplyForLoanRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *palPb.ApplyForLoanResponse
		wantErr    bool
		setupMocks func(md *mockedDependencies)
	}{
		{
			name: "user is eligible for federal V2 live",
			args: args{
				req: &palPb.ApplyForLoanRequest{
					ActorId:        dummyActorId,
					OfferId:        dummyOfferId,
					LoanAmount:     nil,
					TenureInMonths: 24,
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_FEDERAL,
					},
					PledgeDetails: nil,
				},
			},
			want: &palPb.ApplyForLoanResponse{
				Status:        rpc.StatusOk(),
				LoanRequestId: "lr-id",
				RespHeader: &palPb.LoanRespHeader{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					Vendor:      palPb.Vendor_FEDERAL,
				},
			},
			wantErr: false,
			setupMocks: func(md *mockedDependencies) {
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), dummyOfferId).Return(getLoanOffer(dummyOfferId, dummyActorId, "", palPb.Vendor_FEDERAL, dummyLoecId, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN), nil).Times(1)
				md.limitEstimatorClient.EXPECT().GetLoanConservativeLimit(gomock.Any(), gomock.Any()).Return(&limitEstimatorPb.GetLoanConservativeLimitResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: dummyActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: dummyUserId,
					},
				}, nil).Times(1)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: dummyUserId},
				}).Return(getUserRes(), nil).Times(1)

				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: dummyActorId,
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
					FeatureInfo:  nil,
				}, nil).Times(1)
				md.loanRequestDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&palPb.LoanRequest{
					Id:          "lr-id",
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					OrchId:      "orch-id",
					ActorId:     dummyActorId,
				}, nil).Times(1)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(&celestial.InitiateWorkflowResponse{Status: rpc.StatusOk()}, nil)
				md.multiDBProvider.EXPECT().CheckAndGetLoanAccountsForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:         dummyActorId,
					AccountStatuses: []palPb.LoanAccountStatus{palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE},
					LoanPrograms:    []palPb.LoanProgram{},
				}).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
				md.multiDBProvider.EXPECT().CheckAndGetNonTerminalLoanRequests(gomock.Any(), &multiDbProvider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId: dummyActorId,
					Types: []palPb.LoanRequestType{
						palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
						palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY,
					},
				}).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.ApplyForLoan(ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyForLoan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}

func TestService_GetForeclosureDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	foreClosureDetailsProvider := mock_providers.NewMockILoanForeClosureDetailsProvider(ctr)
	type args struct {
		ctx context.Context
		req *palPb.GetForeclosureDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.GetForeclosureDetailsResponse
		wantErr    bool
	}{
		{
			name: "loan account not found - pre condition",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetForeclosureDetailsRequest{
					LoanAccountId: "la101",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "la101").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &preApprovedLoanPb.GetForeclosureDetailsResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("loan account does not exists in db"),
			},
			wantErr: false,
		},
		{
			name: "fetching loan account - db error",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetForeclosureDetailsRequest{
					LoanAccountId: "la101",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "la101").Return(nil, errors.New("db error"))
			},
			want: &preApprovedLoanPb.GetForeclosureDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching loan account from db"),
			},
			wantErr: false,
		},
		{
			name: "err in fetching foreclosure provider",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetForeclosureDetailsRequest{
					LoanAccountId: "la101",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "la101").Return(loanAccountSample1, nil)
				md.mockLoanDataProvider.EXPECT().FetchLoanForeClosureDetailsProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("foreclosure provider not found"))
			},
			want: &preApprovedLoanPb.GetForeclosureDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("Error while fetching loan foreclosure provider"),
			},
			wantErr: false,
		},
		{
			name: "error in identifying pre-close",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetForeclosureDetailsRequest{
					LoanAccountId: "la101",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "la101").Return(loanAccountSample1, nil)
				md.mockLoanDataProvider.EXPECT().FetchLoanForeClosureDetailsProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(foreClosureDetailsProvider, nil)
				foreClosureDetailsProvider.EXPECT().FetchLoanPreClosureDetailsFromVendor(gomock.Any(), loanAccountSample1).Return(nil, errors.New("fetch err"))
			},
			want: &preApprovedLoanPb.GetForeclosureDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching foreclosure details from vendor"),
			},
			wantErr: false,
		},
		{
			name: "loan can be pre-closed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.GetForeclosureDetailsRequest{
					LoanAccountId: "la101",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "la101").Return(loanAccountSample1, nil)
				md.mockLoanDataProvider.EXPECT().FetchLoanForeClosureDetailsProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(foreClosureDetailsProvider, nil)
				foreClosureDetailsProvider.EXPECT().FetchLoanPreClosureDetailsFromVendor(gomock.Any(), loanAccountSample1).Return(&loanDataProviderProviders.FetchLoanPreClosureDetailsFromVendorResponse{
					LoanPreCloseAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
				}, nil)
			},
			want: &preApprovedLoanPb.GetForeclosureDetailsResponse{
				Status: rpc.StatusOk(),
				Details: &preApprovedLoanPb.GetForeclosureDetailsResponse_Details{
					ForeclosureAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.GetForeclosureDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetForeclosureDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetForeclosureDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_InitiateLoanClosure(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	type args struct {
		ctx context.Context
		req *palPb.InitiateLoanClosureRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.InitiateLoanClosureResponse
		wantErr    bool
	}{
		{
			name: "loan account not found - pre condition",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
				},
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), "la101").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("loan account not found in db"),
			},
			wantErr: false,
		},
		{
			name: "non terminal request found - initiating workflow failed - err already exists",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample1.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample1, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample1.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return([]*palPb.LoanRequest{loanRequestSample4}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status:        rpc.StatusOk(),
				LoanRequestId: loanRequestSample4.GetId(),
			},
			wantErr: false,
		},
		{
			name: "non terminal request found - initiating loan closure v3 workflow failed - err already exists",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample2.GetLoanProgram(),
						Vendor:      loanAccountSample2.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample2.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample2, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample2.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return([]*palPb.LoanRequest{loanRequestSample4}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status:        rpc.StatusOk(),
				LoanRequestId: loanRequestSample4.GetId(),
			},
			wantErr: false,
		},
		{
			name: "non terminal request found - initiating workflow failed - unknown err",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample1.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample1, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample1.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return([]*palPb.LoanRequest{loanRequestSample4}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while initiating loan closure flow"),
			},
			wantErr: false,
		},
		{
			name: "non terminal request found - initiating workflow - passed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample1.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample1, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample1.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return([]*palPb.LoanRequest{loanRequestSample4}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status:        rpc.StatusOk(),
				LoanRequestId: loanRequestSample4.GetId(),
			},
			wantErr: false,
		},
		{
			name: "non terminal request not found - req creation failed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample1.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample1, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample1.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return(nil, epifierrors.ErrRecordNotFound)
				md.loanRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(nil, errors.New("db error"))
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while initiating loan closure flow"),
			},
			wantErr: false,
		},
		{
			name: "non terminal request not found - lr req creation passed, wf initiation passed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample1.GetLoanProgram(),
						Vendor:      loanAccountSample1.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample1.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample1, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample1.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return(nil, epifierrors.ErrRecordNotFound)
				md.loanRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(loanRequestSample1, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status:        rpc.StatusOk(),
				LoanRequestId: loanRequestSample1.GetId(),
			},
			wantErr: false,
		},
		{
			name: "non terminal request not found - lr req creation passed, loan closure v3 wf initiation passed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedLoanPb.InitiateLoanClosureRequest{
					LoanAccountId: "la101",
					LoanHeader: &preApprovedLoanPb.LoanHeader{
						LoanProgram: loanAccountSample2.GetLoanProgram(),
						Vendor:      loanAccountSample2.GetVendor(),
					},
				},
			},
			setupMocks: func(md *mockedDependencies) {
				ctx := epificontext.WithOwnership(context.Background(), helper.GetPalOwnership(loanAccountSample2.GetVendor()))
				md.loanAccountDao.EXPECT().GetById(ctx, "la101").Return(loanAccountSample2, nil)
				md.loanRequestDao.EXPECT().GetByLoanAccountIdAndType(ctx, loanAccountSample2.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE).Return(nil, epifierrors.ErrRecordNotFound)
				md.loanRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(loanRequestSample1, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &preApprovedLoanPb.InitiateLoanClosureResponse{
				Status:        rpc.StatusOk(),
				LoanRequestId: loanRequestSample1.GetId(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.InitiateLoanClosure(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateLoanClosure() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateLoanClosure() got = %v, want %v", got, tt.want)
			}
		})
	}
}

var offAppLoanRepaymentRequest = &palPb.ProcessOffAppLoanRepaymentRequest{
	LoanAccountId: "l-id-1",
	RepaidAmount: &money.Money{
		Units: 300,
	},
	PaymentReferenceId: "ref-id-1",
	PaymentTime:        timestampPb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST)),
	PaymentProtocol:    paymentPb.PaymentProtocol_UPI,
	TxnSettlementTime:  timestampPb.New(time.Date(2024, 1, 2, 0, 0, 0, 0, datetime.IST)),
}

func TestService_ProcessOffAppLoanRepaymentV2(t *testing.T) {
	t.Parallel()
	loanAcc := &preApprovedLoanPb.LoanAccount{
		Id:         offAppLoanRepaymentRequest.GetLoanAccountId(),
		LmsPartner: plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX,
		Vendor:     preApprovedLoanPb.Vendor_EPIFI_TECH,
	}
	orchId := crypto.GetSHA256Hash("off-app-prepay", offAppLoanRepaymentRequest.GetPaymentReferenceId())
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	type args struct {
		ctx     context.Context
		request *palPb.ProcessOffAppLoanRepaymentRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider)
		want       *palPb.ProcessOffAppLoanRepaymentResponse
		wantErr    bool
	}{
		{
			name: "should fail if requested entry is not found in loan accounts",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "should fail if dao call to loan accounts fails",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(nil, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan account from db"),
			},
		},
		{
			name: "should fail if dao call to loan payment request fails with unhandled error",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan payment request"),
			},
		},
		{
			name: "should fail if call to loan payment request gives record not found and fetching preclose decider fails",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrRecordNotFound)
				md.prepay.EXPECT().GetLoanPreClosureDecider(gomock.Any(), gomock.Any(), loanAcc.GetLmsPartner()).Return(nil, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get precloseDecider"),
			},
		},
		{
			name: "should fail if call to loan payment request gives record not found preclose decider fails to give verdict on whether to preclose or not",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrRecordNotFound)
				md.prepay.EXPECT().GetLoanPreClosureDecider(gomock.Any(), gomock.Any(), loanAcc.GetLmsPartner()).Return(decider, nil)
				decider.EXPECT().ShouldPreCloseTheLoanOnPayment(gomock.Any(), loanAcc.GetId(), offAppLoanRepaymentRequest.GetRepaidAmount()).Return(false, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to check whether to preclose account or not"),
			},
		},
		{
			name: "should fail if dao call to loan payment requests to create new payment reuqest fails",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrRecordNotFound)
				md.prepay.EXPECT().GetLoanPreClosureDecider(gomock.Any(), gomock.Any(), loanAcc.GetLmsPartner()).Return(decider, nil)
				decider.EXPECT().ShouldPreCloseTheLoanOnPayment(gomock.Any(), loanAcc.GetId(), offAppLoanRepaymentRequest.GetRepaidAmount()).Return(true, nil)
				md.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), &palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}).Return(nil, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to create payment request entry"),
			},
		},
		{
			name: "should pass with status already exists if workflow already exists for given orch id",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrRecordNotFound)
				md.prepay.EXPECT().GetLoanPreClosureDecider(gomock.Any(), gomock.Any(), loanAcc.GetLmsPartner()).Return(decider, nil)
				decider.EXPECT().ShouldPreCloseTheLoanOnPayment(gomock.Any(), loanAcc.GetId(), offAppLoanRepaymentRequest.GetRepaidAmount()).Return(true, nil)
				md.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), &palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}).Return(&palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrAlreadyExists)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusAlreadyExists(),
			},
		},
		{
			name: "should fail if workflow doesnt exists and new workflow initiation also fails",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrRecordNotFound)
				md.prepay.EXPECT().GetLoanPreClosureDecider(gomock.Any(), gomock.Any(), loanAcc.GetLmsPartner()).Return(decider, nil)
				decider.EXPECT().ShouldPreCloseTheLoanOnPayment(gomock.Any(), loanAcc.GetId(), offAppLoanRepaymentRequest.GetRepaidAmount()).Return(true, nil)
				md.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), &palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}).Return(&palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("error initiating loan off app prepay workflow"),
			},
		},
		{
			name: "should pass with status ok",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies, decider *mocks2.MockILoanPreClosureDecider) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.loanPaymentRequestsDao.EXPECT().GetByOrchId(gomock.Any(), orchId).Return(nil, epifierrors.ErrRecordNotFound)
				md.prepay.EXPECT().GetLoanPreClosureDecider(gomock.Any(), gomock.Any(), loanAcc.GetLmsPartner()).Return(decider, nil)
				decider.EXPECT().ShouldPreCloseTheLoanOnPayment(gomock.Any(), loanAcc.GetId(), offAppLoanRepaymentRequest.GetRepaidAmount()).Return(true, nil)
				md.loanPaymentRequestsDao.EXPECT().Create(gomock.Any(), &palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}).Return(&palPb.LoanPaymentRequest{
					ActorId:   loanAcc.GetActorId(),
					AccountId: loanAcc.GetId(),
					OrchId:    orchId,
					Amount:    offAppLoanRepaymentRequest.GetRepaidAmount(),
					Details: &palPb.LoanPaymentRequestDetails{
						Utr:               offAppLoanRepaymentRequest.GetPaymentReferenceId(),
						TxnInitiationTime: offAppLoanRepaymentRequest.GetPaymentTime(),
						PaymentProtocol:   offAppLoanRepaymentRequest.GetPaymentProtocol(),
						TxnSettlementTime: offAppLoanRepaymentRequest.GetTxnSettlementTime(),
						TxnProvenance:     offAppLoanRepaymentRequest.GetPaymentProvenance(),
						VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
							LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
								SkipChargeCollection: offAppLoanRepaymentRequest.GetSkipChargeCollection(),
							},
						},
					},
					Type:   preApprovedLoanPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE,
					Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
				}, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusOk(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			flag := dynConf.Flags().IsOffAppPaymentV2Enabled()
			err := s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(true, true, nil)
			if err != nil {
				t.Errorf("failed to toggle dyn config")
				return
			}
			defer func() {
				err = s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(flag, true, nil)
				if err != nil {
					t.Error("failed to toggle dyn config")
				}
			}()
			defer assertTest()
			decider := mocks2.NewMockILoanPreClosureDecider(ctr)
			tt.setupMocks(md, decider)
			got, err := s.ProcessOffAppLoanRepayment(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessOffAppLoanRepaymentV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessOffAppLoanRepaymentV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ProcessOffAppLoanRepaymentV1(t *testing.T) {
	loanAcc := &preApprovedLoanPb.LoanAccount{
		Id:         offAppLoanRepaymentRequest.GetLoanAccountId(),
		LmsPartner: plEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX,
		Vendor:     preApprovedLoanPb.Vendor_EPIFI_TECH,
	}
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	type args struct {
		ctx     context.Context
		request *palPb.ProcessOffAppLoanRepaymentRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.ProcessOffAppLoanRepaymentResponse
		wantErr    bool
	}{
		{
			name: "should fail if requested entry is not found in loan accounts",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "should fail if workflow initiation fails with unhandled error",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrPermanent)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("error initiating off app repayment workflow"),
			},
		},
		{
			name: "should pass if workflow already exists",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrAlreadyExists)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusAlreadyExists(),
			},
		},
		{
			name: "should pass if workflow started successsfully",
			args: args{
				ctx:     context.Background(),
				request: offAppLoanRepaymentRequest,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), offAppLoanRepaymentRequest.GetLoanAccountId()).Return(loanAcc, nil)
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(&celestial.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			want: &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusOk(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			flag := dynConf.Flags().IsOffAppPaymentV2Enabled()
			err := s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(false, true, nil)
			if err != nil {
				t.Errorf("failed to toggle dyn config")
				return
			}
			defer func() {
				err = s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(flag, true, nil)
				if err != nil {
					t.Error("failed to toggle dyn config")
				}
			}()
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.ProcessOffAppLoanRepayment(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessOffAppLoanRepaymentV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessOffAppLoanRepaymentV1() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_AddEmploymentDetailsSync(t *testing.T) {
	addEmploymentDetailsReq := &palPb.AddEmploymentDetailsRequest{
		LoanRequestId:    "req-id-1",
		OccupationType:   types.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
		OrganizationName: "ABC pvt. ltd.",
		MonthlyIncome: &money.Money{
			CurrencyCode: "INR",
			Units:        50000,
			Nanos:        0,
		},
		ActorId:   "actor-id-1",
		WorkEmail: "<EMAIL>",
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		OfficeAddress: nil,
	}
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	type args struct {
		ctx     context.Context
		request *palPb.AddEmploymentDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.AddEmploymentDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return internal when loan request is not found",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
			wantErr: false,
			want: &palPb.AddEmploymentDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return permission denied when actor ids mismatch",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(&palPb.LoanRequest{
					Id:      "req-id-1",
					ActorId: "actor-id-2",
				}, nil).Times(1)
			},
			wantErr: false,
			want: &palPb.AddEmploymentDetailsResponse{
				Status: rpc.StatusPermissionDenied(),
			},
		},
		{
			name: "should return internal when fails to fetch workflow status",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(&palPb.LoanRequest{
					Id:      "req-id-1",
					ActorId: "actor-id-1",
					OrchId:  "orch-id-1",
					Vendor:  palPb.Vendor_LIQUILOANS,
				}, nil).Times(1)
				md.celestialClient.EXPECT().GetWorkflowStatus(gomock.Any(), &celestial.GetWorkflowStatusRequest{
					Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestial.ClientReqId{
							Id:     "orch-id-1",
							Client: workflow.Client_PRE_APPROVED_LOAN,
						},
					},
					Ownership: commontypes.Ownership_LIQUILOANS_PL,
				}).Return(&celestial.GetWorkflowStatusResponse{Status: rpc.StatusRecordNotFound()}, nil).Times(1)
			},
			wantErr: false,
			want: &palPb.AddEmploymentDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			flag := dynConf.Flags().IsOffAppPaymentV2Enabled()
			err := s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(false, true, nil)
			if err != nil {
				t.Errorf("failed to toggle dyn config")
				return
			}
			defer func() {
				err = s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(flag, true, nil)
				if err != nil {
					t.Error("failed to toggle dyn config")
				}
			}()
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.AddEmploymentDetailsSync(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddEmploymentDetailsSync() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AddEmploymentDetailsSync() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_SaveContactDetails(t *testing.T) {
	saveContactDetailsReq := &palPb.SaveContactDetailsRequest{
		LseId: "lse-id-1",
		LoanHeader: &preApprovedLoanPb.LoanHeader{
			LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
		},
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 9876543210,
		},
	}
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	type args struct {
		ctx     context.Context
		request *palPb.SaveContactDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.SaveContactDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return internal when loan step execution is not found",
			args: args{
				ctx:     context.Background(),
				request: saveContactDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: false,
			want: &palPb.SaveContactDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return internal error when failed to fetch lse",
			args: args{
				ctx:     context.Background(),
				request: saveContactDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").
					Return(&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
					}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: false,
			want: &palPb.SaveContactDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "user client rpc failed to fetch phone number",
			args: args{
				ctx:     context.Background(),
				request: saveContactDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").
					Return(&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
					}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(&preApprovedLoanPb.LoanRequest{
					Id:      "ref-id-1",
					ActorId: "actor-1",
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: "actor-1",
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: false,
			want: &palPb.SaveContactDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "alternate phone number is same as primary phone number",
			args: args{
				ctx:     context.Background(),
				request: saveContactDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").
					Return(&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
					}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(&preApprovedLoanPb.LoanRequest{
					Id:      "ref-id-1",
					ActorId: "actor-1",
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: "actor-1",
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 9876543210,
							},
						},
					},
				}, nil)
			},
			wantErr: false,
			want: &palPb.SaveContactDetailsResponse{
				Status: rpc.StatusFailedPrecondition(),
			},
		},
		{
			name: "should return internal when loan step execution update fails",
			args: args{
				ctx:     context.Background(),
				request: saveContactDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").
					Return(&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
					}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(&preApprovedLoanPb.LoanRequest{
					Id:      "ref-id-1",
					ActorId: "actor-1",
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: "actor-1",
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 9988776655,
							},
						},
					},
				}, nil)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(),
					&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
						Details: &preApprovedLoanPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ContactabilityDetailsData{ContactabilityDetailsData: &palPb.ContactabilityDetailsData{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 9876543210,
								},
								MaxPhoneAttemptAllowed:  3,
								PhoneNumberAttemptCount: 1,
							},
							},
						},
					}, []palPb.LoanStepExecutionFieldMask{
						palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS},
				).Return(epifierrors.ErrFailedPrecondition)
			},
			wantErr: false,
			want: &palPb.SaveContactDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "details gets added successfully and rpc returns success",
			args: args{
				ctx:     context.Background(),
				request: saveContactDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").
					Return(&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
					}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(&preApprovedLoanPb.LoanRequest{
					Id:      "ref-id-1",
					ActorId: "actor-1",
				}, nil)
				md.usersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: "actor-1",
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 9988776655,
							},
						},
					},
				}, nil)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(),
					&preApprovedLoanPb.LoanStepExecution{
						Id:      "lse-id-1",
						ActorId: "actor-1",
						RefId:   "ref-id-1",
						Details: &preApprovedLoanPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ContactabilityDetailsData{ContactabilityDetailsData: &palPb.ContactabilityDetailsData{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 9876543210,
								},
								MaxPhoneAttemptAllowed:  3,
								PhoneNumberAttemptCount: 1,
							},
							},
						},
					}, []palPb.LoanStepExecutionFieldMask{
						palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS},
				).Return(nil)
			},
			wantErr: false,
			want: &palPb.SaveContactDetailsResponse{
				Status: rpc.StatusOk(),
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_CONFIRMATION_VIA_OTP_SCREEN,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.SaveContactDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveContactDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil && tt.want.GetStatus() == rpc.StatusOk() {
				if tt.want.GetDeeplink().GetScreen() != got.GetDeeplink().GetScreen() {
					t.Errorf("SaveContactDetails() screen = %v, want screen %v", got.GetDeeplink().GetScreen(), tt.want.GetDeeplink().GetScreen())
					return
				}
			}
		})
	}
}

func TestService_AddAddressDetailsSync(t *testing.T) {
	addEmploymentDetailsReq := &palPb.AddAddressDetailsRequest{
		LoanRequestId: "req-id-1",
		Address: &types.PostalAddress{
			PostalCode:         "232323",
			AdministrativeArea: "india",
		},
		ActorId: "actor-id-1",
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_FEDERAL,
		},
	}
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	type args struct {
		ctx     context.Context
		request *palPb.AddAddressDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(md *mockedDependencies)
		want       *palPb.AddAddressDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return internal when loan request is not found",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
			wantErr: false,
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return internal when loan request is not found",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
			wantErr: false,
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return permission denied when actor ids mismatch",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(&palPb.LoanRequest{
					Id:          "req-id-1",
					ActorId:     "actor-id-2",
					Vendor:      preApprovedLoanPb.Vendor_FEDERAL,
					LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				}, nil).Times(1)
			},
			wantErr: false,
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusPermissionDenied(),
			},
		},
		{
			name: "should return internal when fails to fetch workflow status",
			args: args{
				ctx:     context.Background(),
				request: addEmploymentDetailsReq,
			},
			setupMocks: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), addEmploymentDetailsReq.GetLoanRequestId()).Return(&palPb.LoanRequest{
					Id:          "req-id-1",
					ActorId:     "actor-id-1",
					OrchId:      "orch-id-1",
					Vendor:      preApprovedLoanPb.Vendor_FEDERAL,
					LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				}, nil).Times(1)
				md.celestialClient.EXPECT().GetWorkflowStatus(gomock.Any(), &celestial.GetWorkflowStatusRequest{
					Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestial.ClientReqId{
							Id:     "orch-id-1",
							Client: workflow.Client_PRE_APPROVED_LOAN,
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				}).Return(&celestial.GetWorkflowStatusResponse{Status: rpc.StatusRecordNotFound()}, nil).Times(1)
			},
			wantErr: false,
			want: &palPb.AddAddressDetailsResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newPalServiceWithMocks(t)
			flag := dynConf.Flags().IsOffAppPaymentV2Enabled()
			err := s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(false, true, nil)
			if err != nil {
				t.Errorf("failed to toggle dyn config")
				return
			}
			defer func() {
				err = s.DynConf.Flags().SetIsOffAppPaymentV2Enabled(flag, true, nil)
				if err != nil {
					t.Error("failed to toggle dyn config")
				}
			}()
			defer assertTest()
			tt.setupMocks(md)
			got, err := s.AddAddressDetailsSync(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddAddressDetailsSync() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AddAddressDetailsSync() got = %v, want %v", got, tt.want)
			}
		})
	}
}
