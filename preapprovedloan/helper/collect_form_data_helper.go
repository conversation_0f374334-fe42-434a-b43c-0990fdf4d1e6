package helper

import (
	"strings"

	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/pkg/errors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

func ValidateCollectFormData(req *ValidateCollectFormDataRequest) error {
	switch {
	case req.Vendor == palPb.Vendor_FIFTYFIN:
		return nil
	case req.Name != "":
		if err := validateUserName(req.Name); err != nil {
			return err
		}
	case req.EmploymentType != "":
		switch req.EmploymentType {
		case typesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED.String():
			if req.EmployerName == "" {
				return errors.New("employer name is required")
			}
			// if req.WorkEmail == "" {
			// 	return errors.New("work email is required")
			// }
			if req.MonthlyIncome == nil {
				return errors.New("monthly income is required in salaried cases")
			}
		case typesPb.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED.String():
			if req.EmployerName == "" {
				return errors.New("organization name is required")
			}
			if req.AnnualRevenue == nil {
				return errors.New("annual revenue is required")
			}
		default:
			if req.MonthlyIncome == nil {
				return errors.New("monthly income is required for all other cases")
			}
		}
	case req.AddressType != "":
		if req.AddressType == typesPb.ResidenceType_RESIDENCE_TYPE_RENTED.String() || req.AddressType == typesPb.ResidenceType_RESIDENCE_TYPE_PAYING_GUEST.String() {
			if req.Rent == nil {
				return errors.New("rent is required")
			}
		}
	}

	return nil
}

type ValidateCollectFormDataRequest struct {
	PinCode           int32
	Name              string
	Dob               *date.Date
	Pan               string
	Gender            string
	MaritalStatus     string
	AddressType       string
	Rent              *moneyPb.Money
	LoanPurpose       string
	DesiredLoanAmount *moneyPb.Money
	EmploymentType    string
	EmployerName      string
	WorkEmail         string
	MonthlyIncome     *moneyPb.Money
	Gstin             string
	AnnualRevenue     *moneyPb.Money
	MotherName        string
	FatherName        string
	Vendor            palPb.Vendor
}

func validateUserName(name string) error {
	// Trim whitespaces from the name
	sanitizedName := strings.TrimSpace(name)

	// Adding this check to avoid cibil pull if either forename or surname is missing
	if !strings.Contains(sanitizedName, " ") {
		return errors.New("name validation failed")
	}

	return nil
}
