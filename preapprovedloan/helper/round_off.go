package helper

import (
	"google.golang.org/genproto/googleapis/type/money"

	money2 "github.com/epifi/be-common/pkg/money"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
)

// to remove precision form any money object
// have this preapproved specific for now due to ay new implementation from federal can be singly handled from here.
// TODO(@prasoon_: Move this to pkg or use decimal for calculation if no specific implementation needed
/*
5.55 -> 6
5.2 -> 5
6.5 -> 7
*/
func round(req *money.Money) *money.Money {
	if req == nil {
		return nil
	}
	half := money2.ParseFloat(0.5, "")
	if req.GetNanos() >= half.GetNanos() {
		req.Units += 1
		req.Nanos = 0
		return req
	} else {
		req.Nanos = 0
		return req
	}
}

func RoundLoanRequest(req *preApprovedLoanPb.LoanRequest) (*preApprovedLoanPb.LoanRequest, error) {
	if req == nil {
		return nil, nil
	}
	var err error
	if req.GetDetails().GetLoanInfo() != nil {
		req.GetDetails().GetLoanInfo().EmiAmount = round(req.GetDetails().GetLoanInfo().GetEmiAmount())
		req.GetDetails().GetLoanInfo().Amount = round(req.GetDetails().GetLoanInfo().GetAmount())
		req.GetDetails().GetLoanInfo().TotalPayable = round(req.GetDetails().GetLoanInfo().GetTotalPayable())
		if req.GetDetails().GetLoanInfo().GetDeductions() != nil {
			req.GetDetails().GetLoanInfo().GetDeductions().Gst = round(req.GetDetails().GetLoanInfo().GetDeductions().GetGst())
			req.GetDetails().GetLoanInfo().GetDeductions().AdvanceInterest = round(req.GetDetails().GetLoanInfo().GetDeductions().GetAdvanceInterest())
			req.GetDetails().GetLoanInfo().GetDeductions().ProcessingFee = round(req.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee())
			req.GetDetails().GetLoanInfo().GetDeductions().TotalDeductions, err = money2.Sum(req.GetDetails().GetLoanInfo().GetDeductions().GetGst(), req.GetDetails().GetLoanInfo().GetDeductions().GetAdvanceInterest())
			if err != nil {
				return nil, err
			}
			req.GetDetails().GetLoanInfo().GetDeductions().TotalDeductions, err = money2.Sum(req.GetDetails().GetLoanInfo().GetDeductions().GetTotalDeductions(), req.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee())
			if err != nil {
				return nil, err
			}
			req.GetDetails().GetLoanInfo().DisbursalAmount, err = money2.Subtract(req.GetDetails().GetLoanInfo().GetAmount(), req.GetDetails().GetLoanInfo().GetDeductions().GetTotalDeductions())
			if err != nil {
				return nil, err
			}
		}
	}
	return req, nil
}

func RoundLoanAccount(req *preApprovedLoanPb.LoanAccount) *preApprovedLoanPb.LoanAccount {
	if req == nil {
		return nil
	}
	req.GetLoanAmountInfo().OutstandingAmount = round(req.GetLoanAmountInfo().GetOutstandingAmount())
	req.GetLoanAmountInfo().LoanAmount = round(req.GetLoanAmountInfo().GetLoanAmount())
	req.GetLoanAmountInfo().TotalPayableAmount = round(req.GetLoanAmountInfo().GetTotalPayableAmount())
	req.GetLoanAmountInfo().DisbursedAmount = round(req.GetLoanAmountInfo().GetDisbursedAmount())
	return req
}

func RoundLoanInstallmentInfo(req *preApprovedLoanPb.LoanInstallmentInfo) *preApprovedLoanPb.LoanInstallmentInfo {
	if req == nil {
		return nil
	}
	req.TotalAmount = round(req.GetTotalAmount())
	req.GetDetails().NextEmiAmount = round(req.GetDetails().GetNextEmiAmount())
	return req
}

func RoundLoanInstallmentPayout(req *preApprovedLoanPb.LoanInstallmentPayout) {
	if req == nil {
		return
	}
	req.Amount = round(req.GetAmount())
	if req.GetDetails() != nil {
		req.GetDetails().ActualInstallmentAmount = round(req.GetDetails().GetActualInstallmentAmount())
		req.GetDetails().PenaltyAmount = round(req.GetDetails().GetPenaltyAmount())
	}
}
