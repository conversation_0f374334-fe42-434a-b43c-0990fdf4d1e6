package abfl_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/preapprovedloan"
	vgAbflPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl/mocks"
	mock_dao "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/abfl"
)

func TestABFLDataProvider_FetchLoanScheduleFromVendor(t *testing.T) {
	t.Parallel()

	var (
		accountNo     = "account-id"
		dealNo        = "deal-no"
		loanAccountId = "loan-account-id"
		la            = &preapprovedloan.LoanAccount{
			Id:            loanAccountId,
			AccountNumber: accountNo,
			Details: &preapprovedloan.LoanAccountDetails{
				LenderAccountDetails: &preapprovedloan.LenderAccountDetails{
					VendorSpecificDetails: &preapprovedloan.LoanAccountVendorSpecificDetails{
						Details: &preapprovedloan.LoanAccountVendorSpecificDetails_AbflDetails{
							AbflDetails: &preapprovedloan.AbflLoanAccountDetails{
								DealNumber: dealNo,
								AccountId:  accountNo,
							},
						},
					},
				},
			},
		}
		txnDetailsResAllPaid = &vgAbflPb.GetTransactionDetailsResponse{
			Status: rpc.StatusOk(),
			RepaymentDetailsList: []*vgAbflPb.GetTransactionDetailsResponse_RepaymentDetails{
				{
					RepaymentId: "1",
					DueDate: &date.Date{
						Year:  2023,
						Month: 10,
						Day:   5,
					},
					Principal:     moneyPb.ParseInt(2000, "INR"),
					Interest:      moneyPb.ParseInt(200, "INR"),
					PaymentStatus: vgAbflPb.GetTransactionDetailsResponse_RepaymentDetails_PAYMENT_STATUS_PAID,
				},
				{
					RepaymentId: "2",
					DueDate: &date.Date{
						Year:  2023,
						Month: 11,
						Day:   5,
					},
					Principal:     moneyPb.ParseInt(2000, "INR"),
					Interest:      moneyPb.ParseInt(200, "INR"),
					PaymentStatus: vgAbflPb.GetTransactionDetailsResponse_RepaymentDetails_PAYMENT_STATUS_PAID,
				},
			},
		}
		txnDetailsResPaidAndUnpaid = &vgAbflPb.GetTransactionDetailsResponse{
			Status: rpc.StatusOk(),
			RepaymentDetailsList: []*vgAbflPb.GetTransactionDetailsResponse_RepaymentDetails{
				{
					RepaymentId: "1",
					DueDate: &date.Date{
						Year:  2023,
						Month: 10,
						Day:   5,
					},
					Principal:     moneyPb.ParseInt(2000, "INR"),
					Interest:      moneyPb.ParseInt(200, "INR"),
					PaymentStatus: vgAbflPb.GetTransactionDetailsResponse_RepaymentDetails_PAYMENT_STATUS_PAID,
				},
				{
					RepaymentId: "2",
					DueDate: &date.Date{
						Year:  2023,
						Month: 11,
						Day:   5,
					},
					Principal:     moneyPb.ParseInt(2000, "INR"),
					Interest:      moneyPb.ParseInt(200, "INR"),
					PaymentStatus: vgAbflPb.GetTransactionDetailsResponse_RepaymentDetails_PAYMENT_STATUS_UNPAID,
				},
			},
		}
		lipAllPaid = &providers.FetchLoanScheduleResponse{LoanInstallments: []*preapprovedloan.LoanInstallmentPayout{
			{
				DueDate: &date.Date{
					Year:  2023,
					Month: 10,
					Day:   5,
				},
				Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
				VendorInstallmentId: "1",
				Principal:           moneyPb.ParseInt(2000, "INR"),
				Interest:            moneyPb.ParseInt(200, "INR"),
				Amount:              &money.Money{CurrencyCode: "INR"},
				PayoutDate: &date.Date{
					Year:  2023,
					Month: 10,
					Day:   5,
				},
				DueAmount:     moneyPb.ParseInt(2200, "INR"),
				LoanAccountId: loanAccountId,
			},
			{
				DueDate: &date.Date{
					Year:  2023,
					Month: 11,
					Day:   5,
				},
				Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
				VendorInstallmentId: "2",
				Principal:           moneyPb.ParseInt(2000, "INR"),
				Interest:            moneyPb.ParseInt(200, "INR"),
				Amount:              &money.Money{CurrencyCode: "INR"},
				PayoutDate: &date.Date{
					Year:  2023,
					Month: 11,
					Day:   5,
				},
				DueAmount:     moneyPb.ParseInt(2200, "INR"),
				LoanAccountId: loanAccountId,
			},
		},
			Status: preapprovedloan.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
		}
		lipPaidAndUnpaid = &providers.FetchLoanScheduleResponse{LoanInstallments: []*preapprovedloan.LoanInstallmentPayout{
			{
				DueDate: &date.Date{
					Year:  2023,
					Month: 10,
					Day:   5,
				},
				Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
				VendorInstallmentId: "1",
				Principal:           moneyPb.ParseInt(2000, "INR"),
				Interest:            moneyPb.ParseInt(200, "INR"),
				Amount:              &money.Money{CurrencyCode: "INR"},
				PayoutDate: &date.Date{
					Year:  2023,
					Month: 10,
					Day:   5,
				},
				DueAmount:     moneyPb.ParseInt(2200, "INR"),
				LoanAccountId: loanAccountId,
			},
			{
				DueDate: &date.Date{
					Year:  2023,
					Month: 11,
					Day:   5,
				},
				Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
				VendorInstallmentId: "2",
				Principal:           moneyPb.ParseInt(2000, "INR"),
				Interest:            moneyPb.ParseInt(200, "INR"),
				Amount:              &money.Money{CurrencyCode: "INR"},
				DueAmount:           moneyPb.ParseInt(2200, "INR"),
				LoanAccountId:       loanAccountId,
			},
		},
			Status: preapprovedloan.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
		}
		txnDetailsResEmptyTxnDetails = &vgAbflPb.GetTransactionDetailsResponse{
			Status:               rpc.StatusOk(),
			RepaymentDetailsList: nil,
		}
	)
	tests := []struct {
		name         string
		mockVgClient func(client *mocks.MockAbflClient)
		Req          *providers.FetchLoanScheduleRequest
		wantRes      *providers.FetchLoanScheduleResponse
		wantErr      bool
	}{
		{
			name: "fetch loan schedule all paid",
			mockVgClient: func(client *mocks.MockAbflClient) {
				client.EXPECT().GetTransactionDetails(gomock.Any(), &vgAbflPb.GetTransactionDetailsRequest{
					AccountId:     la.GetDetails().GetLenderAccountDetails().GetVendorSpecificDetails().GetAbflDetails().GetAccountId(),
					DealNo:        la.GetDetails().GetLenderAccountDetails().GetVendorSpecificDetails().GetAbflDetails().GetDealNumber(),
					RetrieveLevel: vgAbflPb.GetTransactionDetailsRequest_RETRIEVE_LEVEL_EXTENDED,
				}).Return(txnDetailsResAllPaid, nil)
			},
			Req:     &providers.FetchLoanScheduleRequest{LoanAccount: la},
			wantRes: lipAllPaid,
			wantErr: false,
		},
		{
			name: "fetch loan schedule few paid, few unpaid",
			mockVgClient: func(client *mocks.MockAbflClient) {
				client.EXPECT().GetTransactionDetails(gomock.Any(), &vgAbflPb.GetTransactionDetailsRequest{
					AccountId:     la.GetDetails().GetLenderAccountDetails().GetVendorSpecificDetails().GetAbflDetails().GetAccountId(),
					DealNo:        la.GetDetails().GetLenderAccountDetails().GetVendorSpecificDetails().GetAbflDetails().GetDealNumber(),
					RetrieveLevel: vgAbflPb.GetTransactionDetailsRequest_RETRIEVE_LEVEL_EXTENDED,
				}).Return(txnDetailsResPaidAndUnpaid, nil)
			},
			Req:     &providers.FetchLoanScheduleRequest{LoanAccount: la},
			wantRes: lipPaidAndUnpaid,
			wantErr: false,
		},
		{
			name: "if loan schedule is empty",
			mockVgClient: func(client *mocks.MockAbflClient) {
				client.EXPECT().GetTransactionDetails(gomock.Any(), &vgAbflPb.GetTransactionDetailsRequest{
					AccountId:     la.GetDetails().GetLenderAccountDetails().GetVendorSpecificDetails().GetAbflDetails().GetAccountId(),
					DealNo:        la.GetDetails().GetLenderAccountDetails().GetVendorSpecificDetails().GetAbflDetails().GetDealNumber(),
					RetrieveLevel: vgAbflPb.GetTransactionDetailsRequest_RETRIEVE_LEVEL_EXTENDED,
				}).Return(txnDetailsResEmptyTxnDetails, nil)
			},
			Req:     &providers.FetchLoanScheduleRequest{LoanAccount: la},
			wantErr: true,
		},
	}

	ctrl := gomock.NewController(t)
	vgAbflClient := mocks.NewMockAbflClient(ctrl)
	lrMock := mock_dao.NewMockLoanRequestsDao(ctrl)
	lseMock := mock_dao.NewMockLoanStepExecutionsDao(ctrl)
	laMock := mock_dao.NewMockLoanAccountsDao(ctrl)
	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockVgClient != nil {
				tt.mockVgClient(vgAbflClient)
			}
			s := abfl.NewAbflDataProvider(vgAbflClient, lseMock, lrMock, laMock)
			res, err := s.FetchLoanScheduleFromVendor(ctx, tt.Req)
			if (err != nil) != tt.wantErr {
				t.Errorf("unexpected error from lms interface method, %v", err)
				return
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				t.Errorf("got: %v, want: %v", res, tt.wantRes)
			}
		})
	}
}
