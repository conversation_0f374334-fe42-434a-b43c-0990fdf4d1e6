package finflux_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	pkgDateTime "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	finflux2 "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux/types"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
)

var loanAccount = &preapprovedloan.LoanAccount{
	Id:               "id-1",
	LmsPartnerLoanId: "l-id-1",
	LmsPartner:       enums.LmsPartner_LMS_PARTNER_FINFLUX,
}

func TestBaseProvider_FetchLoanPreClosureDetailsFromVendor(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		LoanAccount *preapprovedloan.LoanAccount
	}
	tests := []struct {
		name       string
		setupMocks func(f *BaseProviderMockFields)
		args       args
		wantRes    *providers.FetchLoanPreClosureDetailsFromVendorResponse
		wantErr    bool
	}{
		{
			name: "should return error if call to finflux vg client (get foreclosure details) fails",
			args: args{
				ctx:         context.Background(),
				LoanAccount: loanAccount,
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.vgClient.EXPECT().GetForeclosureDetails(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrTransient)
			},
			wantRes: nil,
			wantErr: true,
		},
		{
			name: "should run correct foreclosure details using finflux client",
			args: args{
				ctx:         context.Background(),
				LoanAccount: loanAccount,
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.vgClient.EXPECT().GetForeclosureDetails(gomock.Any(), gomock.Any()).Return(&finflux2.GetForeclosureDetailsResponse{
					Status:                rpc.StatusOk(),
					Amount:                pkgMoney.AmountINR(1500).GetPb(),
					NetForeclosureAmount:  pkgMoney.AmountINR(1400).GetPb(),
					ForeclosureCharges:    pkgMoney.AmountINR(200).GetPb(),
					PrincipalPortion:      pkgMoney.AmountINR(800).GetPb(),
					FeeChargesPortion:     pkgMoney.AmountINR(80).GetPb(),
					PenaltyChargesPortion: pkgMoney.AmountINR(120).GetPb(),
					InterestPortion:       pkgMoney.AmountINR(200).GetPb(),
				}, nil)
			},
			wantRes: &providers.FetchLoanPreClosureDetailsFromVendorResponse{
				LoanPreCloseAmount:             pkgMoney.AmountINR(1500).GetPb(),
				LoanPreCloseCharges:            pkgMoney.AmountINR(200).GetPb(),
				LoanPrincipalOutstandingAmount: pkgMoney.AmountINR(800).GetPb(),
				LoanOtherCharges:               nil,
				LoanFeesAmount:                 pkgMoney.AmountINR(80).GetPb(),
				LoanPenaltyAmount:              pkgMoney.AmountINR(120).GetPb(),
				LoanInterestOutstandingAmount:  pkgMoney.AmountINR(200).GetPb(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			res, err := p.FetchLoanPreClosureDetailsFromVendor(tt.args.ctx, tt.args.LoanAccount)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchLoanPreClosureDetailsFromVendor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				t.Errorf("got: %v, want: %v", res, tt.wantRes)
			}
		})
	}
}

func TestBaseProvider_FetchLoanScheduleFromVendor(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *providers.FetchLoanScheduleRequest
	}
	tests := []struct {
		name       string
		setupMocks func(f *BaseProviderMockFields)
		args       args
		wantRes    *providers.FetchLoanScheduleResponse
		wantErr    bool
	}{
		{
			name: "should return error if call to finflux vg client (fetch loan details) fails",
			args: args{
				ctx: context.Background(),
				request: &providers.FetchLoanScheduleRequest{
					LoanAccount: loanAccount,
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.vgClient.EXPECT().FetchLoanSchedule(gomock.Any(), &finflux2.FetchLoanScheduleRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX),
					},
					Identifier: &finflux2.FetchLoanScheduleRequest_LoanId{
						LoanId: loanAccount.GetLmsPartnerLoanId(),
					},
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: true,
			wantRes: nil,
		},
		{
			name: "should run correct loan schedule details using finflux client",
			args: args{
				ctx: context.Background(),
				request: &providers.FetchLoanScheduleRequest{
					LoanAccount: loanAccount,
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.vgClient.EXPECT().FetchLoanSchedule(gomock.Any(), &finflux2.FetchLoanScheduleRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX),
					},
					Identifier: &finflux2.FetchLoanScheduleRequest_LoanId{
						LoanId: loanAccount.GetLmsPartnerLoanId(),
					},
				}).Return(&finflux2.FetchLoanScheduleResponse{
					Loan: &types.LoanDetails{
						NumberOfRepayments: 3,
					},
					Status: rpc.StatusOk(),
					Periods: []*types.LoanRepaymentPeriod{
						{
							// garbage installment
						},
						{
							DueDate: &date.Date{
								Day:   1,
								Month: 2,
								Year:  2004,
							},
							TotalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(900).GetPb(),
								Due:         pkgMoney.AmountINR(900).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							PrincipalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(500).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(500).GetPb(),
							},
							InterestComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(200).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(200).GetPb(),
							},
							PenaltyChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(100).GetPb(),
								Due:         pkgMoney.AmountINR(100).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							FeeChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(100).GetPb(),
								Due:         pkgMoney.AmountINR(100).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							IsComplete: true,
						},
						{
							DueDate: &date.Date{
								Day:   1,
								Month: 2,
								Year:  2004,
							},
							TotalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(800).GetPb(),
								Due:         pkgMoney.AmountINR(900).GetPb(),
								Outstanding: pkgMoney.AmountINR(100).GetPb(),
							},
							PrincipalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(400).GetPb(),
								Outstanding: pkgMoney.AmountINR(100).GetPb(),
								Due:         pkgMoney.AmountINR(500).GetPb(),
							},
							InterestComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(200).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(200).GetPb(),
							},
							PenaltyChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(100).GetPb(),
								Due:         pkgMoney.AmountINR(100).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							FeeChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(100).GetPb(),
								Due:         pkgMoney.AmountINR(100).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							IsComplete: false,
						},
						{
							DueDate: &date.Date{
								Day:   1,
								Month: 2,
								Year:  2004,
							},
							TotalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(900).GetPb(),
								Outstanding: pkgMoney.AmountINR(900).GetPb(),
							},
							PrincipalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Outstanding: pkgMoney.AmountINR(500).GetPb(),
								Due:         pkgMoney.AmountINR(500).GetPb(),
							},
							InterestComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Outstanding: pkgMoney.AmountINR(200).GetPb(),
								Due:         pkgMoney.AmountINR(200).GetPb(),
							},
							PenaltyChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(100).GetPb(),
								Outstanding: pkgMoney.AmountINR(100).GetPb(),
							},
							FeeChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(100).GetPb(),
								Outstanding: pkgMoney.AmountINR(100).GetPb(),
							},
							IsComplete: false,
						},
						{
							SerialNumber: 4,
							DueDate: &date.Date{
								Day:   1,
								Month: 2,
								Year:  2004,
							},
							TotalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(20).GetPb(),
								Due:         pkgMoney.AmountINR(20).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							PrincipalComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(0).GetPb(),
							},
							InterestComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(0).GetPb(),
							},
							PenaltyChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(20).GetPb(),
								Due:         pkgMoney.AmountINR(20).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							FeeChargesComponent: &types.LoanComponentBreakdown{
								Paid:        pkgMoney.AmountINR(0).GetPb(),
								Due:         pkgMoney.AmountINR(0).GetPb(),
								Outstanding: pkgMoney.AmountINR(0).GetPb(),
							},
							IsComplete: true,
						},
					},
				}, nil)
			},
			wantErr: false,
			wantRes: &providers.FetchLoanScheduleResponse{
				LoanInstallments: []*preapprovedloan.LoanInstallmentPayout{
					{
						Amount: pkgMoney.AmountINR(900).GetPb(),
						DueDate: &date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						},
						Details: &preapprovedloan.LoanInstallmentPayoutDetails{
							PaidPrincipalAmt:        pkgMoney.AmountINR(500).GetPb(),
							PaidInterestAmt:         pkgMoney.AmountINR(200).GetPb(),
							PrincipalOutstandingAmt: pkgMoney.AmountINR(0).GetPb(),
							InterestOutstandingAmt:  pkgMoney.AmountINR(0).GetPb(),
							ChargesApplied: &preapprovedloan.ChargesApplied{
								LatePaymentInterest: pkgMoney.AmountINR(100).GetPb(),
								OtherCharges:        pkgMoney.AmountINR(100).GetPb(),
							},
						},
						Status:        preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
						LoanAccountId: loanAccount.GetId(),
						VendorInstallmentId: pkgDateTime.DateToString(&date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						}, pkgDateTime.DATE_LAYOUT_DDMMYYYY, pkgDateTime.IST),
						Principal: pkgMoney.AmountINR(500).GetPb(),
						Interest:  pkgMoney.AmountINR(200).GetPb(),
						DueAmount: pkgMoney.AmountINR(900).GetPb(),
					},
					{
						Amount: pkgMoney.AmountINR(800).GetPb(),
						DueDate: &date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						},
						Details: &preapprovedloan.LoanInstallmentPayoutDetails{
							PaidPrincipalAmt:        pkgMoney.AmountINR(400).GetPb(),
							PaidInterestAmt:         pkgMoney.AmountINR(200).GetPb(),
							PrincipalOutstandingAmt: pkgMoney.AmountINR(100).GetPb(),
							InterestOutstandingAmt:  pkgMoney.AmountINR(0).GetPb(),
							ChargesApplied: &preapprovedloan.ChargesApplied{
								LatePaymentInterest: pkgMoney.AmountINR(100).GetPb(),
								OtherCharges:        pkgMoney.AmountINR(100).GetPb(),
							},
						},
						Status:        preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
						LoanAccountId: loanAccount.GetId(),
						VendorInstallmentId: pkgDateTime.DateToString(&date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						}, pkgDateTime.DATE_LAYOUT_DDMMYYYY, pkgDateTime.IST),
						Principal: pkgMoney.AmountINR(500).GetPb(),
						Interest:  pkgMoney.AmountINR(200).GetPb(),
						DueAmount: pkgMoney.AmountINR(900).GetPb(),
					},
					{
						Amount: pkgMoney.AmountINR(0).GetPb(),
						DueDate: &date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						},
						Details: &preapprovedloan.LoanInstallmentPayoutDetails{
							PaidPrincipalAmt:        pkgMoney.AmountINR(0).GetPb(),
							PaidInterestAmt:         pkgMoney.AmountINR(0).GetPb(),
							PrincipalOutstandingAmt: pkgMoney.AmountINR(500).GetPb(),
							InterestOutstandingAmt:  pkgMoney.AmountINR(200).GetPb(),
							ChargesApplied: &preapprovedloan.ChargesApplied{
								LatePaymentInterest: pkgMoney.AmountINR(100).GetPb(),
								OtherCharges:        pkgMoney.AmountINR(100).GetPb(),
							},
						},
						Status:        preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						LoanAccountId: loanAccount.GetId(),
						VendorInstallmentId: pkgDateTime.DateToString(&date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						}, pkgDateTime.DATE_LAYOUT_DDMMYYYY, pkgDateTime.IST),
						Principal: pkgMoney.AmountINR(500).GetPb(),
						Interest:  pkgMoney.AmountINR(200).GetPb(),
						DueAmount: pkgMoney.AmountINR(900).GetPb(),
					},
					{

						Amount: pkgMoney.AmountINR(20).GetPb(),
						DueDate: &date.Date{
							Day:   1,
							Month: 2,
							Year:  2004,
						},
						Details: &preapprovedloan.LoanInstallmentPayoutDetails{
							PaidPrincipalAmt:        pkgMoney.AmountINR(0).GetPb(),
							PaidInterestAmt:         pkgMoney.AmountINR(0).GetPb(),
							PrincipalOutstandingAmt: pkgMoney.AmountINR(0).GetPb(),
							InterestOutstandingAmt:  pkgMoney.AmountINR(0).GetPb(),
							ChargesApplied: &preapprovedloan.ChargesApplied{
								LatePaymentInterest: pkgMoney.AmountINR(20).GetPb(),
								OtherCharges:        pkgMoney.AmountINR(0).GetPb(),
							},
						},
						Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
						LoanAccountId:       loanAccount.GetId(),
						VendorInstallmentId: "penalty-installment",
						Principal:           pkgMoney.AmountINR(0).GetPb(),
						Interest:            pkgMoney.AmountINR(0).GetPb(),
						DueAmount:           pkgMoney.AmountINR(20).GetPb(),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			res, err := p.FetchLoanScheduleFromVendor(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchLoanScheduleFromVendor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				t.Errorf("got: %v, want: %v", res, tt.wantRes)
			}
		})
	}
}

func TestBaseProvider_GetOutstandingLoanAmount(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *providers.GetOutstandingLoanAmountRequest
	}
	tests := []struct {
		name       string
		setupMocks func(f *BaseProviderMockFields)
		args       args
		wantRes    *providers.GetOutstandingLoanAmountResponse
		wantErr    bool
	}{
		{
			name: "should give error if dao call to loan accounts fail",
			args: args{
				ctx: context.Background(),
				request: &providers.GetOutstandingLoanAmountRequest{
					LoanAccountId: loanAccount.GetId(),
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanAccountsDao.EXPECT().GetById(gomock.Any(), loanAccount.GetId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: true,
			wantRes: nil,
		},
		{
			name: "should return error if call to finflux vg client(fetch lon details) fails",
			args: args{
				ctx: context.Background(),
				request: &providers.GetOutstandingLoanAmountRequest{
					LoanAccountId: loanAccount.GetId(),
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanAccountsDao.EXPECT().GetById(gomock.Any(), loanAccount.GetId()).Return(loanAccount, nil)
				f.vgClient.EXPECT().FetchLoanDetails(gomock.Any(), &finflux2.FetchLoanDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX),
					},
					Identifier: &finflux2.FetchLoanDetailsRequest_LoanId{
						LoanId: loanAccount.GetLmsPartnerLoanId(),
					},
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: true,
			wantRes: nil,
		},
		{
			name: "should run correct outstanding balance using finflux client",
			args: args{
				ctx: context.Background(),
				request: &providers.GetOutstandingLoanAmountRequest{
					LoanAccountId: loanAccount.GetId(),
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanAccountsDao.EXPECT().GetById(gomock.Any(), loanAccount.GetId()).Return(loanAccount, nil)
				f.vgClient.EXPECT().FetchLoanDetails(gomock.Any(), &finflux2.FetchLoanDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX),
					},
					Identifier: &finflux2.FetchLoanDetailsRequest_LoanId{
						LoanId: loanAccount.GetLmsPartnerLoanId(),
					},
				}).Return(&finflux2.FetchLoanDetailsResponse{
					Status: rpc.StatusOk(),
					Loan: &types.LoanDetails{
						Summary: &types.LoanSummary{
							TotalOutstanding: pkgMoney.AmountINR(200).GetPb(),
						},
					},
				}, nil)
			},
			wantErr: false,
			wantRes: &providers.GetOutstandingLoanAmountResponse{
				OutstandingLoanAmount: pkgMoney.AmountINR(200).GetPb(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			res, err := p.GetOutstandingLoanAmount(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOutstandingLoanAmount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				t.Errorf("got: %v, want: %v", res, tt.wantRes)
			}
		})
	}
}

func TestBaseProvider_GetPostPaymentEmiLevelAllocation(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *providers.GetPostPaymentEmiLevelAllocationRequest
	}
	tests := []struct {
		name       string
		setupMocks func(f *BaseProviderMockFields)
		args       args
		wantRes    *providers.GetPostPaymentEmiLevelAllocationResponse
		wantErr    bool
	}{
		{
			name: "should return error if loan header is not provided",
			args: args{
				ctx: context.Background(),
				request: &providers.GetPostPaymentEmiLevelAllocationRequest{
					LoanHeader:       nil,
					PaymentRequestId: "1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {},
			wantErr:    true,
			wantRes:    nil,
		},
		{
			name: "should return error if payment request fetch fails",
			args: args{
				ctx: context.Background(),
				request: &providers.GetPostPaymentEmiLevelAllocationRequest{
					LoanHeader: &preapprovedloan.LoanHeader{
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
						Vendor:      preapprovedloan.Vendor_LIQUILOANS,
					},
					PaymentRequestId: "1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanPaymentRequestDao.EXPECT().GetById(gomock.Any(), "1").Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
			wantErr: true,
			wantRes: nil,
		},
		{
			name: "should return error if finflux txn details api fails",
			args: args{
				ctx: context.Background(),
				request: &providers.GetPostPaymentEmiLevelAllocationRequest{
					LoanHeader: &preapprovedloan.LoanHeader{
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
						Vendor:      preapprovedloan.Vendor_LIQUILOANS,
					},
					PaymentRequestId: "1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanPaymentRequestDao.EXPECT().GetById(gomock.Any(), "1").Return(&preapprovedloan.LoanPaymentRequest{
					Id:        "1",
					OrchId:    "or-1",
					AccountId: "ac-1",
				}, nil).Times(1)
				f.vgClient.EXPECT().GetRepaymentAllocationDetails(gomock.Any(), &finflux2.GetRepaymentAllocationDetailsRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
				}).Return(&finflux2.GetRepaymentAllocationDetailsResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
			},
			wantErr: true,
			wantRes: nil,
		},
		{
			name: "should fail if overpayment and sum of emi allocations does not match the total amount",
			args: args{
				ctx: context.Background(),
				request: &providers.GetPostPaymentEmiLevelAllocationRequest{
					LoanHeader: &preapprovedloan.LoanHeader{
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
						Vendor:      preapprovedloan.Vendor_LIQUILOANS,
					},
					PaymentRequestId: "1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanPaymentRequestDao.EXPECT().GetById(gomock.Any(), "1").Return(&preapprovedloan.LoanPaymentRequest{
					Id:        "1",
					OrchId:    "or-1",
					AccountId: "ac-1",
					Amount:    pkgMoney.AmountINR(1900).GetPb(),
				}, nil).Times(1)
				f.vgClient.EXPECT().GetRepaymentAllocationDetails(gomock.Any(), &finflux2.GetRepaymentAllocationDetailsRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
				}).Return(&finflux2.GetRepaymentAllocationDetailsResponse{
					Status: rpc.StatusOk(),
					Items: []*finflux2.GetRepaymentAllocationDetailsResponse_EmiLevelPaymentAllocationInfo{
						{
							EmiNo:           1,
							PrincipalAmount: pkgMoney.AmountINR(500).GetPb(),
							InterestAmount:  pkgMoney.AmountINR(100).GetPb(),
							PenaltyAmount:   pkgMoney.AmountINR(50).GetPb(),
							DueDate:         &date.Date{Year: 2020, Month: 1, Day: 1},
						},
						{
							EmiNo:           2,
							PrincipalAmount: pkgMoney.AmountINR(500).GetPb(),
							InterestAmount:  pkgMoney.AmountINR(100).GetPb(),
							PenaltyAmount:   pkgMoney.AmountINR(20).GetPb(),
							DueDate:         &date.Date{Year: 2020, Month: 2, Day: 1},
						},
						{
							EmiNo:           3,
							PrincipalAmount: pkgMoney.AmountINR(500).GetPb(),
							InterestAmount:  pkgMoney.AmountINR(100).GetPb(),
							PenaltyAmount:   pkgMoney.AmountINR(0).GetPb(),
							DueDate:         &date.Date{Year: 2020, Month: 3, Day: 1},
						},
					},
				}, nil).Times(1)
				f.loanAccountsDao.EXPECT().GetById(gomock.Any(), "ac-1").Return(&preapprovedloan.LoanAccount{
					Id:               "ac-1",
					LmsPartnerLoanId: "lms-id-1",
				}, nil).Times(1)
				f.vgClient.EXPECT().GetTransactionSummary(gomock.Any(), &finflux2.GetTransactionSummaryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
					LoanId:        "lms-id-1",
				}).Return(&finflux2.GetTransactionSummaryResponse{
					Status:             rpc.StatusOk(),
					OverpaymentPortion: pkgMoney.ZeroINR().GetPb(),
				}, nil).Times(1)
			},
			wantErr: true,
			wantRes: nil,
		},
		{
			name: "should run successfully when amount is distributed to emis and overpayment",
			args: args{
				ctx: context.Background(),
				request: &providers.GetPostPaymentEmiLevelAllocationRequest{
					LoanHeader: &preapprovedloan.LoanHeader{
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
						Vendor:      preapprovedloan.Vendor_LIQUILOANS,
					},
					PaymentRequestId: "1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanPaymentRequestDao.EXPECT().GetById(gomock.Any(), "1").Return(&preapprovedloan.LoanPaymentRequest{
					Id:        "1",
					OrchId:    "or-1",
					AccountId: "ac-1",
					Amount:    pkgMoney.AmountINR(1900).GetPb(),
				}, nil).Times(1)
				f.vgClient.EXPECT().GetRepaymentAllocationDetails(gomock.Any(), &finflux2.GetRepaymentAllocationDetailsRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
				}).Return(&finflux2.GetRepaymentAllocationDetailsResponse{
					Status: rpc.StatusOk(),
					Items: []*finflux2.GetRepaymentAllocationDetailsResponse_EmiLevelPaymentAllocationInfo{
						{
							EmiNo:           1,
							PrincipalAmount: pkgMoney.AmountINR(500).GetPb(),
							InterestAmount:  pkgMoney.AmountINR(100).GetPb(),
							PenaltyAmount:   pkgMoney.AmountINR(50).GetPb(),
							DueDate:         &date.Date{Year: 2020, Month: 1, Day: 1},
						},
						{
							EmiNo:           2,
							PrincipalAmount: pkgMoney.AmountINR(500).GetPb(),
							InterestAmount:  pkgMoney.AmountINR(100).GetPb(),
							PenaltyAmount:   pkgMoney.AmountINR(20).GetPb(),
							DueDate:         &date.Date{Year: 2020, Month: 2, Day: 1},
						},
						{
							EmiNo:           3,
							PrincipalAmount: pkgMoney.AmountINR(500).GetPb(),
							InterestAmount:  pkgMoney.AmountINR(100).GetPb(),
							PenaltyAmount:   pkgMoney.AmountINR(0).GetPb(),
							DueDate:         &date.Date{Year: 2020, Month: 3, Day: 1},
						},
					},
				}, nil).Times(1)
				f.loanAccountsDao.EXPECT().GetById(gomock.Any(), "ac-1").Return(&preapprovedloan.LoanAccount{
					Id:               "ac-1",
					LmsPartnerLoanId: "lms-id-1",
				}, nil).Times(1)
				f.vgClient.EXPECT().GetTransactionSummary(gomock.Any(), &finflux2.GetTransactionSummaryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
					LoanId:        "lms-id-1",
				}).Return(&finflux2.GetTransactionSummaryResponse{
					Status:             rpc.StatusOk(),
					OverpaymentPortion: pkgMoney.AmountINR(30).GetPb(),
				}, nil).Times(1)
			},
			wantErr: false,
			wantRes: &providers.GetPostPaymentEmiLevelAllocationResponse{
				EmiLevelPaymentAllocations: []*providers.EmiLevelPaymentAllocation{
					{
						DueDate:               &date.Date{Year: 2020, Month: 1, Day: 1},
						EmiSerialNumber:       1,
						PrincipalComponent:    pkgMoney.AmountINR(500).GetPb(),
						InterestComponent:     pkgMoney.AmountINR(100).GetPb(),
						TotalPenaltyComponent: pkgMoney.AmountINR(50).GetPb(),
					},
					{
						DueDate:               &date.Date{Year: 2020, Month: 2, Day: 1},
						EmiSerialNumber:       2,
						PrincipalComponent:    pkgMoney.AmountINR(500).GetPb(),
						InterestComponent:     pkgMoney.AmountINR(100).GetPb(),
						TotalPenaltyComponent: pkgMoney.AmountINR(20).GetPb(),
					},
					{
						DueDate:               &date.Date{Year: 2020, Month: 3, Day: 1},
						EmiSerialNumber:       3,
						PrincipalComponent:    pkgMoney.AmountINR(500).GetPb(),
						InterestComponent:     pkgMoney.AmountINR(100).GetPb(),
						TotalPenaltyComponent: pkgMoney.AmountINR(0).GetPb(),
					},
				},
				OverPaidComponent: pkgMoney.AmountINR(30).GetPb(),
			},
		},
		{
			name: "should run successfully when amount is distributed only overpayment",
			args: args{
				ctx: context.Background(),
				request: &providers.GetPostPaymentEmiLevelAllocationRequest{
					LoanHeader: &preapprovedloan.LoanHeader{
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
						Vendor:      preapprovedloan.Vendor_LIQUILOANS,
					},
					PaymentRequestId: "1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.loanPaymentRequestDao.EXPECT().GetById(gomock.Any(), "1").Return(&preapprovedloan.LoanPaymentRequest{
					Id:        "1",
					OrchId:    "or-1",
					AccountId: "ac-1",
					Amount:    pkgMoney.AmountINR(100).GetPb(),
				}, nil).Times(1)
				f.vgClient.EXPECT().GetRepaymentAllocationDetails(gomock.Any(), &finflux2.GetRepaymentAllocationDetailsRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
				}).Return(&finflux2.GetRepaymentAllocationDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil).Times(1)
				f.loanAccountsDao.EXPECT().GetById(gomock.Any(), "ac-1").Return(&preapprovedloan.LoanAccount{
					Id:               "ac-1",
					LmsPartnerLoanId: "lms-id-1",
				}, nil).Times(1)
				f.vgClient.EXPECT().GetTransactionSummary(gomock.Any(), &finflux2.GetTransactionSummaryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: helper.ConvertLmsPartnerToVgVendor(enums.LmsPartner_LMS_PARTNER_FINFLUX)},
					TxnExternalId: "or-1",
					LoanId:        "lms-id-1",
				}).Return(&finflux2.GetTransactionSummaryResponse{
					Status:             rpc.StatusOk(),
					OverpaymentPortion: pkgMoney.AmountINR(100).GetPb(),
				}, nil).Times(1)
			},
			wantErr: false,
			wantRes: &providers.GetPostPaymentEmiLevelAllocationResponse{
				OverPaidComponent: pkgMoney.AmountINR(100).GetPb(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			res, err := p.GetPostPaymentEmiLevelAllocation(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPostPaymentEmiLevelAllocation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				got, _ := json.Marshal(res)
				want, _ := json.Marshal(tt.wantRes)
				t.Errorf("got: %v, want: %v", string(got), string(want))
			}
		})
	}
}
