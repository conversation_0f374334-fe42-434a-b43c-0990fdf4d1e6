// nolint: gosec
package idfc

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	vgIdfcPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
)

const (
	loanUtilityOutstandingRequestCode = "OUTSTANDINGS"
)

const forecloseAmtCacheKeyTemplate = "ForeClosurePalcache:"

type Provider struct {
	vgClient               vgIdfcPb.IdfcClient
	loanAccountsDao        dao.LoanAccountsDao
	loanApplicantDao       dao.LoanApplicantDao
	cache                  cache.CacheStorage
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao
	loanRequestDao         dao.LoanRequestsDao
	rpcHelper              *helper.RpcHelper
}

func NewProvider(
	vgClient vgIdfcPb.IdfcClient,
	loanAccountsDao dao.LoanAccountsDao,
	loanApplicantDao dao.LoanApplicantDao,
	cache cache.CacheStorage,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	loanRequestDao dao.LoanRequestsDao,
	rpcHelper *helper.RpcHelper,
) *Provider {
	return &Provider{
		vgClient:               vgClient,
		loanAccountsDao:        loanAccountsDao,
		loanApplicantDao:       loanApplicantDao,
		cache:                  cache,
		loanInstallmentInfoDao: loanInstallmentInfoDao,
		loanRequestDao:         loanRequestDao,
		rpcHelper:              rpcHelper,
	}
}

func (p *Provider) FetchLoanScheduleFromVendor(
	ctx context.Context,
	req *providers.FetchLoanScheduleRequest,
) (*providers.FetchLoanScheduleResponse, error) {
	res := &providers.FetchLoanScheduleResponse{
		LoanInstallments: []*palPb.LoanInstallmentPayout{},
	}

	repaymentRes, err := p.vgClient.Repayment(ctx, &vgIdfcPb.RepaymentRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IDFC},
		LoanId:        req.LoanAccount.GetAccountNumber(),
		CorrelationId: uuid.New().String(),
	})
	if err = epifigrpc.RPCError(repaymentRes, err); err != nil {
		return nil, fmt.Errorf("unable to fetch loan schedule from vendor: %w", err)
	}
	for _, vgInstallment := range repaymentRes.GetResourceData() {
		var beInstallment *palPb.LoanInstallmentPayout
		beInstallment, err = convertVgIdfcRepaymentToBe(vgInstallment)
		if err != nil {
			return nil, fmt.Errorf("unable to convert vg response to be for %s: %w", vgInstallment.GetLoanId(), err)
		}
		res.LoanInstallments = append(res.LoanInstallments, beInstallment)
	}
	return res, nil
}

func (p *Provider) FetchLoanPreClosureDetailsFromVendor(ctx context.Context, loanAccount *palPb.LoanAccount) (*providers.FetchLoanPreClosureDetailsFromVendorResponse, error) {
	res := &providers.FetchLoanPreClosureDetailsFromVendorResponse{}

	// key := fmt.Sprint(forecloseAmtCacheKeyTemplate, loanAccount.GetVendor().String(), loanAccount.GetAccountNumber())
	// val, err := p.cache.Get(ctx, key)
	// if err != nil {
	//	// intentionally not returning the error to fetch foreclosure amount on best effort basis
	//	logger.Error(ctx, "pair not present", zap.Any("key_val", key))
	// } else {
	//	if err2 := json.Unmarshal([]byte(val), &res); err2 != nil {
	//		// intentionally not returning the error to fetch foreclosure amount on best effort basis
	//		logger.Error(ctx, "failed to unmarshall value", zap.Error(err2))
	//	} else {
	//		return res, nil
	//	}
	// }

	loanInstallmentInfo, err := p.loanInstallmentInfoDao.GetByActiveAccountId(ctx, loanAccount.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching loan installment info from db")
	}

	// if loan account is created within grace days, user has to pay only the disbursed amount
	if time.Now().Before(loanAccount.GetCreatedAt().AsTime().AddDate(0, 0, int(loanInstallmentInfo.GetDetails().GetGracePeriod()))) {
		res.LoanPreCloseCharges = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        0,
			Nanos:        0,
		}
		res.LoanPreCloseAmount = loanAccount.GetLoanAmountInfo().GetDisbursedAmount()
	} else {
		vgRes, vgErr := p.vgClient.ForeClosure(ctx, &vgIdfcPb.ForeClosureRequest{
			Header:              &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IDFC},
			EntityReqId:         strconv.Itoa(************* + int(rand.Int63n(*************))),
			SearchType:          vgIdfcPb.SearchType_SEARCH_TYPE_AGREEMENT_ID,
			SearchValue:         loanAccount.GetAccountNumber(),
			ActivityType:        vgIdfcPb.ActivityType_ACTIVITY_TYPE_SIMULATION,
			PrepayPenaltyMatrix: vgIdfcPb.PrepayPenaltyMatrix_PREPAY_PENALTY_MATRIX_CONTRACT,
			CorrelationId:       uuid.New().String(),
			TransactionDate:     datetime.TimeToDateInLoc(time.Now(), datetime.IST),
		})
		te := epifigrpc.RPCError(vgRes, vgErr)
		if te != nil || len(vgRes.GetResourceData()) == 0 {
			return res, errors.Wrap(te, te.Error())
		}
		res.LoanPreCloseCharges = vgRes.GetResourceData()[0].GetPenaltyAmount()
		res.LoanPreCloseAmount = vgRes.GetResourceData()[0].GetForeclosureAmount()
	}

	// bytes, err := json.Marshal(res)
	// if err != nil {
	//	// not returning intentionally, so that the existing flow would not break
	//	logger.Error(ctx, "Error while marshaling FetchLoanPreClosureDetailsFromVendorResponse for caching", zap.Error(err))
	// } else {
	//	val := string(bytes)
	//	dayEndTime := time.Until(datetime.EndOfDay(time.Now().In(datetime.IST)))
	//
	//	err = p.cache.Set(ctx, key, val, dayEndTime)
	//	if err != nil {
	//		// not returning intentionally, so that the existing flow would not break
	//		logger.Error(ctx, "unable to set the value in the cache", zap.Error(err))
	//	}
	// }

	return res, nil
}

func (p *Provider) GetOutstandingLoanAmount(
	ctx context.Context,
	req *providers.GetOutstandingLoanAmountRequest,
) (*providers.GetOutstandingLoanAmountResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request is nil")
	}
	res := &providers.GetOutstandingLoanAmountResponse{}

	loanAccount, err := p.loanAccountsDao.GetById(ctx, req.LoanAccountId)
	if err != nil {
		return nil, fmt.Errorf("unable to get loan account: %w", err)
	}

	loanApplicant, err := p.loanApplicantDao.GetByActorId(ctx, loanAccount.GetActorId())
	if err != nil {
		return nil, fmt.Errorf("unable to get loan applicant: %w", err)
	}

	loanUtilitiesRes, err := p.vgClient.LoanUtilities(ctx, &vgIdfcPb.LoanUtilitiesRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IDFC},
		AgreementId:   loanAccount.GetAccountNumber(),
		CrnNo:         loanApplicant.GetVendorApplicantId(),
		RequestCode:   loanUtilityOutstandingRequestCode,
		CorrelationId: uuid.New().String(),
	})
	if err = epifigrpc.RPCError(loanUtilitiesRes, err); err != nil {
		return nil, fmt.Errorf("unable to get outstanding loan amount from vendor: %w", err)
	}

	loanUtilitiesData := loanUtilitiesRes.GetData()
	if len(loanUtilitiesData) == 0 {
		return nil, fmt.Errorf("data array is empty")
	}

	res.OutstandingLoanAmount = loanUtilitiesData[0].GetOutstandingDetails().GetTotal()
	return res, nil
}

func (p *Provider) GetVkycdata(ctx context.Context, req *providers.GetVkycDataRequest) (*providers.GetVkycDataResponse, error) {
	// fetching lr to fetch loan applicant
	lr, lrErr := p.loanRequestDao.GetById(ctx, req.LoanRequestId)
	if lrErr != nil {
		return nil, errors.Wrap(lrErr, "unable to fetch the lr by id")
	}

	// fetching loan applicant by actor id
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lr.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting loan applicant for the user")
	}

	// vg call to idfc to get the reference id needed to call the creation of Vkyc profile.
	statusRes, err := p.vgClient.CheckStatus(ctx, &vgIdfcPb.CheckStatusRequest{
		ReqId:         applicant.GetVendorRequestId(),
		CorrelationId: uuid.New().String(),
	})
	if te := epifigrpc.RPCError(statusRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get loan status")
	}

	if statusRes.GetVkycStatus() == vgIdfcPb.VKYCStatus_VKYC_STATUS_AUDITOR_STATUS_APPROVED {
		return &providers.GetVkycDataResponse{
			IsVkycOfUserAlreadyCompleted: true,
		}, nil
	}

	// if reference id needed to call create profile is empty then returning error
	if statusRes.GetReferenceId() == "" {
		return nil, errors.Wrap(err, "failed to get reference id for creating vkyc profile")
	}

	// vg call to create the vkyc profile
	createVkycRes, err := p.vgClient.CreateVkycProfile(ctx, &vgIdfcPb.CreateVkycProfileRequest{
		ReqId:         applicant.GetVendorRequestId(),
		ReferenceId:   statusRes.GetReferenceId(),
		CorrelationId: uuid.New().String(),
	})
	if te := epifigrpc.RPCError(createVkycRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get create vkyc profile")
	}

	isUserInIndia, isUserInIndiaErr := p.rpcHelper.IsUserInIndia(ctx, lr.GetActorId())
	if isUserInIndiaErr != nil {
		logger.Error(ctx, fmt.Sprintf("error in getting if user in india or not,err: %v", isUserInIndiaErr))
	}
	// if user is in india (or) we failed to get the location, we can skip this check
	// we are skipping this check if we failed to get the location as we already have one more check which comes
	// from IDFC
	if !isUserInIndia {
		return &providers.GetVkycDataResponse{
			IsUserOutsideIndia: true,
		}, nil
	}

	return &providers.GetVkycDataResponse{
		VkycUrl: createVkycRes.GetVkycUrl(),
	}, nil
}
