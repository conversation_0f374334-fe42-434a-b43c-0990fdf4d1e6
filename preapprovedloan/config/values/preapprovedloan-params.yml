Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/lending/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/lending/secure.log"
  MaxSizeInMBs: 100
  MaxBackups: 20

Flags:
  TrimDebugMessageFromStatus: true
  IsFldgLoanOverdueSherlockBannerEnabled: true
  IsOffAppPaymentV2Enabled: true
  IsRecommendationEngineEnabled: true
  ShowNewSecondLookScreen: true

Vendor:
  # TODO(<PERSON><PERSON><PERSON>): Update vendor sftp remote path whenever available
  SftpUploadRemotePath: ""

Tracing:
  Enable: false

EpifiDb:
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

KfsExitUrl: "https://fi.money/loans-callback"

VendorsSupported:
  - 1 # FEDERAL
  - 2 # LIQUILOANS

VendorUsed: 1

Notification:
  VkycSuccess:
    Title: "Yay, time to move forward ➡️"
    Desc: "Your video KYC was successful! Tap to continue your loan application"
    Icon: ""
  ManualReviewSuccess:
    Title: "You are verified on Fi ✅"
    Desc: "Our bank agents have given you the go-ahead! Tap to continue your loan application"
    Icon: ""
  VkycFailed:
    Title: "There seems to be an issue 🤔"
    Desc: "Our partner regulated entity cannot process your loan application due to a verification issue"
    Icon: ""
  ManualReviewFailed:
    Title: "Loan application cancelled"
    Desc: "According to our partner regulated entity, it was due to a verification issue"
    Icon: ""
  LoanAccountCreation:
    Title: "Yay, good news!"
    Desc: "Your loan account number #accNum is now ready for active use ✅"
    Icon: ""
  LoanAccountClosure:
    Title: "Congrats on wrapping up the loan 🚀"
    Desc: "Your loan account number %v is now closed"
    Icon: ""
  LoanPrePay:
    Title: "Money Received"
    Desc: "You have received %v amount in your loan account %v"
    Icon: ""
  DropOffComms:
    Enable: true
    BlackoutTimeStart: "00:00"
    BlackoutTimeStop: "06:00"
    # Waiting times in minutes
    VkycWaitingTimes: [ 5, 10, 20 ]
    LivenessWaitingTimes: [ 5, 10, 20 ]
    ESignWaitingTimes: [ 5, 10, 20 ]

EarlySalary:
  MinSalaryCredits: 1
  MinDaysPostSalaryCredit: 0

EnableDynamicElementForAllUsers: false

InstantCashSegmentId: ""

HomeWidgetV1Config:
  IsEnabled: false

Prepay:
  UseIDFCLoanCancellationV2: true
  PoolAccounts:
    LLPersonalLoanPoolAccountPI: "PILEcFKdcmQFWa8BfipSET/Q230915=="
    LLActorId: "actor-pl-liquiloans"
    IdfcPersonalLoanActorId: "actor-idfc-pl"
    IdfcPersonalLoanPI: "paymentinstrument-idfc-pl"
    IdfcPersonalLoanDisbursalPI: "paymentinstrument-idfc-pl-disbursal"
    LLEarlySalaryPoolAccountPI: "PAYMENT-INSTRUMENTS-LIQUILOANS-LOAN-REPAYMENT-ACCOUNT"
    StockGuardianPoolAccountPi: "PI61dnfacErpx5241114_5feFb8zhrk"
    StockGuardianPoolAccountActorId: "actor-stock-guardian-lsp"
  LenderToPrepayBlackOutConfig:
    LIQUILOANS:
      BlockDurationBeforeEmiDueDateInDays: 2
      BlockDurationAfterEmiDueDateInDays: 3
      BlockDurationBeforeEmiGraceEndDateInDays: 3
      BlockDurationAfterEmiGraceEndDateInDays: 1
    STOCK_GUARDIAN_LSP:
      BlockDurationBeforeEmiDueDateInDays: 0
      BlockDurationAfterEmiDueDateInDays: 1
      BlockDurationBeforeEmiGraceEndDateInDays: 0
      BlockDurationAfterEmiGraceEndDateInDays: 1
  LenderToPreClosureBlackOutConfig:
    FIFTYFIN:
      BlockStartHour: 17
      BlockEndHour: 10

Charges:
  FLDGCharges:
    LateFeeChargesPercent: 0.1

LamfConfig:
  FiftyFinConfig:
    LoanOfferConfig:
      MinLoanAmount: 25000
      MaxLoanAmount: 1000000
      MinTenureMonths: 12
      MaxTenureMonths: 36
      GstInPercentage: 18
      InterestRateInPercentage: 10.5
      MinimumProcessingFee: 999
      ValidityDuration: 3h
      ProcessingFeeInPercentage: 2.5
      DiscountFactor: 0.92

Downtime:
  Vendors:
    - "IDFC":
        Daily:
          IsEnable: true
          StartTime: "00:00"
          EndTime: "04:00"

DeeplinkConfig:
  IsInitiateMandateEnrichmentEnabled: false
  IsAlternateAccountFlowEnabled: false
  IsAlternateAccountFlowEnabledForLL: false
  OpenAbflMandateUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternalForIos: false
  OpenAbflMandateUrlViaExternalForIos: false
  ChangeButtonTextDetailsPage: false
  OpenIdfcVkycUrlViaExternalForAndroid: true
  OpenIdfcVkycUrlViaExternalForIos: false
  # this config is extended version of IsLoanDetailsSelectionV2FlowEnabled, make sure to change the value at both places whenever needed.
  LoanDetailsSelectionV2Flow:
    IsEnabled: true
    EnableLoanPrograms:
      - IDFC
      - FEDERAL_BANK
      - LOAN_PROGRAM_PRE_APPROVED_LOAN
      - LOAN_PROGRAM_FLDG
      - LOAN_PROGRAM_STPL
      - LOAN_PROGRAM_ACQ_TO_LEND
    DefaultAmountPercentage:
      - "LIQUILOANS": 0.95
      - "IDFC": 1
      - "FEDERAL": 1
  OfferDetailsV3Config:
    IsEnabled: true
    AppVersionConstraintConfig:
      MinAndroidVersion: 359
      MinIOSVersion: 506
    VendorLoanProgramMap:
      - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
      - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "LIQUILOANS:LOAN_PROGRAM_STPL": true
      - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB": true
  AbflReferencesAppVersionConstraintConfig:
    MinAndroidVersion: 363
    MinIOSVersion: 2317


QuestSdk:
  Disable: true # make this false in respective env yaml files to enable quest
CreditReportConfig:
  UseCreditReportV2: true
  StaleExperianReportThresholdDays: 1

CategoryToEmiComms:
  - "LIQUILOANS:LOAN_PROGRAM_FLDG":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_STPL":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
  - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
      LowBalComms:
        "0":
          ArrayElement:
            Position: 0
          StartDaysPastDueDate: -2
          EndDaysPastDueDate: -1
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "1":
          ArrayElement:
            Position: 1
          StartDaysPastDueDate: -1
          EndDaysPastDueDate: 0
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "2":
          ArrayElement:
            Position: 2
          StartDaysPastDueDate: 3
          EndDaysPastDueDate: 4
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#C0723D"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "You are in you in your penalty free period. Add before GRACE_END_DATE to avoid late fees."
                FontColor: "#C0723D"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-yellow.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#F6E1C1", "#F1CE9B" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"
        "3":
          ArrayElement:
            Position: 3
          StartDaysPastDueDate: 7
          EndDaysPastDueDate: 10
          CommsChannelDetails:
            GtmPopUp:
              IsEnabled: true
              Title:
                PlainString: "Add funds for your EMI of EMI_AMOUNT"
                FontColor: "#6D3149"
                StandardFontStyle: "HEADLINE_XL"
              Body:
                PlainString: "Failure to do so will incur you late payment fees."
                FontColor: "#A93D5B"
                StandardFontStyle: "BODY_S"
              VisualElement:
                ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/popup-warning-pink.png"
                Width: 160
                Height: 160
              RadialGradient:
                Colours: [ "#FAE7ED", "#E5A1B6" ]
              CtaList:
                "0":
                  ArrayElement:
                    Position: 0
                  Text:
                    PlainString: "Add funds and pay"
                    FontColor: "#00B899"
                    StandardFontStyle: "BUTTON_M"
                  BgColor: "#FFFFFF"
                  Deeplink:
                    Screen: "TRANSFER_IN"

Lms:
  EmiPaymentNudgeIdsToExit:
    - "LIQUILOANS:LOAN_PROGRAM_FLDG":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_STPL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"

# if the loan amount is between MinLoanAmount and MaxLoanAmount, tenure should be between MinTenure and MaxTenure or vice versa
LoanCalculator:
  LoanSelectionConstraint:
    "IDFC":
      - MinTenure: 6
        MaxTenure: 24
        MinLoanAmount: 50000
        MaxLoanAmount: 100000
      - MinTenure: 12
        MaxTenure: 48
        MinLoanAmount: 100001
        MaxLoanAmount: 200000
      - MinTenure: 18
        MaxTenure: 60
        MinLoanAmount: 200001
        MaxLoanAmount: 500000

MandateConfig:
  LiquiloansMandateConfig:
    IsMandateCoolOffCheckEnabled: true
    MinCoolOffMinutesBetweenMandateAttempts: 8
    IsMandateRequestBasedCountLogicEnabled: true
    IsPreviousMandateStatusCheckEnabled: true

WealthToTechDataSharingConsentConfig:
  FeatureConfig:
    DisableFeature: false
  ScreenConfig:
    - "NET_WORTH_HUB_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

    - "ANALYSER_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

    - "SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

    - "LOANS_DASHBOARD_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

SmsFetchingConsentConfig:
  FeatureConfig:
    DisableFeature: false
    UnsupportedPlatforms: [ 2 ]
  ScreenConfig:
    - "NET_WORTH_HUB_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

    - "ANALYSER_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

    - "SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 0s

VendorProgramLevelFeature:
  VendorProgramActiveMap:
    "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_FLDG":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_STPL":
      IsAllowed: true
    "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_FED_REAL_TIME":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
      IsAllowed: true
    "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
  NonFiCoreVendorProgramActiveMap:
    - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
        IsAllowed: true
    - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true

Lendability:
  ScoreCategoryDetails:
    Version: "1.0.0" # if any changes to below mapping, version should be updated
    PdScoreCategoryThresholdMap:
      - "PD_CATEGORY_LOW": 0.07829998
      - "PD_CATEGORY_MEDIUM": 0.10921082
      - "PD_CATEGORY_HIGH": 1
    AffinityScoreCategoryThresholdMap:
      - "LOAN_AFFINITY_CATEGORY_LOW": 0.09
      - "LOAN_AFFINITY_CATEGORY_MEDIUM": 0.15
      - "LOAN_AFFINITY_CATEGORY_HIGH": 1

LoanOfferPrioritisation:
  LoanHeaders:
    - LoanProgram: LOAN_PROGRAM_PRE_APPROVED_LOAN
      Vendor: FEDERAL

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB
      Vendor: FEDERAL

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: STOCK_GUARDIAN_LSP

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: ABFL

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: FEDERAL

    - LoanProgram: LOAN_PROGRAM_PRE_APPROVED_LOAN
      Vendor: ABFL

    - LoanProgram: LOAN_PROGRAM_PRE_APPROVED_LOAN
      Vendor: MONEYVIEW

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: MONEYVIEW

    - LoanProgram: LOAN_PROGRAM_PRE_APPROVED_LOAN
      Vendor: IDFC

    - LoanProgram: LOAN_PROGRAM_PRE_APPROVED_LOAN
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_FLDG
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_REALTIME_SUBVENTION
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_REALTIME_STPL
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_STPL
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_FED_REAL_TIME
      Vendor: FEDERAL

    - LoanProgram: LOAN_PROGRAM_FI_LITE_PL
      Vendor: LIQUILOANS

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: LENDEN

    - LoanProgram: LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
      Vendor: LENDEN

SgEtbNewEligibilityFlow:
  IsAllowed: true

LopeOverrideConfig:
  ShowSgEligibilityOverAbflSoftOffer: false
  ShowSgEligibilityOverMvSoftOffer: true
  #Vendor and LoanProgram are mandatory
  #One of IsOffer or IsEligibillity is mandatory
  #Offer type is optional
  LoanPriorityOrderNonFiCore:
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "STOCK_GUARDIAN_LSP"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "MONEYVIEW"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"

  LoanPriorityOrderFiCore:
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "MONEYVIEW"
      LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "STOCK_GUARDIAN_LSP"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "IDFC"
      LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_FLDG"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_REALTIME_SUBVENTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_REALTIME_STPL"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_STPL"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_FED_REAL_TIME"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LIQUILOANS"
      LoanProgram: "LOAN_PROGRAM_FI_LITE_PL"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LENDEN"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"

AutoCancelCurrentLrConfig:
  MinAndroidVersion: 100
  MinIOSVersion: 100
