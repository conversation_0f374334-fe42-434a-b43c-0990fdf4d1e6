Application:
  Environment: "qa"
  Name: "preapprovedloan"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_fiftyfin"
    EnableDebug: true
    SSLMode: "disable"
    #    Enable once ssl secret permissions are added
    #    SSLMode: "verify-full"
    #    SSLRootCert: "qa/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_fiftyfin_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_moneyview"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

FederalPgDb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 5s
  Name: "loans_federal"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FederalDb:
  Username: "federal_dev_user"
  Password: ""
  Name: "federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.federal_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.federal_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "preapprovedloan"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "federal_dev_user"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.federal_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.federal_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "qa/rudder/internal-writekey"
    # Federal
    EpifiFederalPgpPrivateKey: "qa/pgp/epifi-fed-api-private-key"
    EpifiFederalPgpPassphrase: "qa/pgp/epifi-fed-api-password"
    FederalPgpPublicKey: "qa/pgp/federal-pgp-public-key-for-epifi"
    FederalDbCredentials: "qa/rds/epifimetis/loans_federal_dev_user"
    TemporalCodecAesKey: "qa/temporal/codec-encryption-key"

ProcessLoanInboundTransactionSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-loan-inbound-transaction-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 5

OrderUpdatePreApprovedLoanSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-update-preapprovedloan-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ProcessLoansFiftyfinCallbackSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-vn-loans-fiftyfin-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

Tracing:
  Enable: true

SignalWorkflowPublisher:
  QueueName: "qa-celestial-signal-workflow-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

VendorUsed: 1

NudgeExitEventPublisher:
  QueueName: "qa-nudge-preapprovedloan-event-queue"

EnableDynamicElementForAllUsers: true

InstantCashSegmentId: "AWS_pal-test-qa-segment"

PgdbMigrationFlag: true

HomeWidgetV1Config:
  IsEnabled: true
  PersonalLoanDisplayConfig:
    ActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Get upto"
          "SubHeadingTemplate": "OFFER_MAX_AMOUNT"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Low interest from"
          "SubHeadingTemplate": "12% pa"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Get loan in"
          "SubHeadingTemplate": "5 mins"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Approval odds"
          "SubHeadingTemplate": "Excellent"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Get cash now"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            EntryPoint: 9
        BgColor: "#E4F1F5"
    NoActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Get upto"
          "SubHeadingTemplate": "₹5L"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Low interest from"
          "SubHeadingTemplate": "12% pa"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Get loan in"
          "SubHeadingTemplate": "5 mins"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Approval odds"
          "SubHeadingTemplate": "Excellent"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Check eligibility"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            EntryPoint: 9
        BgColor: "#E4F1F5"
  #    CampaignDisplayConfig:
  #      Heading:
  #        PlainString: "Get cash today"
  #        FontColor: "#313234"
  #        StandardFontStyle: "HEADLINE_M"
  #      HorizontalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
  #      VerticalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
  #      WidgetTiles :
  #        "1" :
  #          "ArrayElement":
  #            "Position": 1
  #          "HeadingTemplate" : "Get up to"
  #          "SubHeadingTemplate": "60L"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
  #          "BgColor" : "#FFFFFF"
  #        "2" :
  #          "ArrayElement":
  #            "Position": 2
  #          "HeadingTemplate" : "Interest rates"
  #          "SubHeadingTemplate": "1%/month"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
  #          "BgColor" : "#FFFFFF"
  #        "3" :
  #          "ArrayElement":
  #            "Position": 3
  #          "HeadingTemplate" : "Get loan in"
  #          "SubHeadingTemplate": "5 min"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
  #          "BgColor" : "#FFFFFF"
  #        "4" :
  #          "ArrayElement":
  #            "Position": 4
  #          "HeadingTemplate" : "Approval odds"
  #          "SubHeadingTemplate": "Excellent"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
  #          "BgColor" : "#FFFFFF"
  #      PrimaryCta:
  #        Text:
  #          PlainString: "Check eligibility"
  #          FontColor: "#6294A6"
  #          StandardFontStyle: "BUTTON_S"
  #        Deeplink:
  #          Screen: "PRE_APPROVED_LOAN_DASHBOARD_SCREEN"
  #        BgColor: "#E4F1F5"
  LAMFDisplayConfig:
    ActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Balance.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Balance.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Credit score"
          "SubHeadingTemplate": "NOT needed"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_smile.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_smile.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Interest rate"
          "SubHeadingTemplate": "Flat 10.50%"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Own your funds"
          "SubHeadingTemplate": "Earn returns"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_UpArrow.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_UpArrow.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Get up to"
          "SubHeadingTemplate": "₹20 Lakh"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Check loan amount"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            LoanProgram: 7
            EntryPoint: 9
        BgColor: "#E4F1F5"
    NoActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Balance.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Balance.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Credit score"
          "SubHeadingTemplate": "NOT needed"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_smile.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_smile.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Interest rate"
          "SubHeadingTemplate": "Flat 10.50%"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Own your funds"
          "SubHeadingTemplate": "Earn returns"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_UpArrow.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_UpArrow.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Get up to"
          "SubHeadingTemplate": "₹20 Lakh"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Check loan amount"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            LoanProgram: 7
            EntryPoint: 9
        BgColor: "#E4F1F5"
#    CampaignDisplayConfig:
#      Heading:
#        PlainString: "Get cash today"
#        FontColor: "#313234"
#        StandardFontStyle: "HEADLINE_M"
#      HorizontalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
#      VerticalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
#      WidgetTiles :
#        "1" :
#          "ArrayElement":
#            "Position": 1
#          "HeadingTemplate" : "Get up to"
#          "SubHeadingTemplate": "60L"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
#          "BgColor" : "#FFFFFF"
#        "2" :
#          "ArrayElement":
#            "Position": 2
#          "HeadingTemplate" : "Interest rates"
#          "SubHeadingTemplate": "1%/month"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
#          "BgColor" : "#FFFFFF"
#        "3" :
#          "ArrayElement":
#            "Position": 3
#          "HeadingTemplate" : "Get loan in"
#          "SubHeadingTemplate": "5 min"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
#          "BgColor" : "#FFFFFF"
#        "4" :
#          "ArrayElement":
#            "Position": 4
#          "HeadingTemplate" : "Approval odds"
#          "SubHeadingTemplate": "Excellent"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
#          "BgColor" : "#FFFFFF"
#      PrimaryCta:
#        Text:
#          PlainString: "Check eligibility"
#          FontColor: "#6294A6"
#          StandardFontStyle: "BUTTON_S"
#        Deeplink:
#          Screen: "PRE_APPROVED_LOAN_DASHBOARD_SCREEN"
#        BgColor: "#E4F1F5"


IsCampaignRelatedHomeDynamicElementEnabled: true

Flags:
  IsFldgLoanOverdueSherlockBannerEnabled: true
  ShowNewSecondLookScreen: true

DeeplinkConfig:
  IsInitiateMandateEnrichmentEnabled: false
  IsAlternateAccountFlowEnabled: false
  OpenAbflMandateUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternalForIos: false
  OpenAbflMandateUrlViaExternalForIos: false
  OpenIdfcVkycUrlViaExternalForAndroid: true
  OpenIdfcVkycUrlViaExternalForIos: false
  InfoItemV3MinVersion:
    MinAndroidVersion: 407
    MinIOSVersion: 2590

PreApprovedLoanBucketName: "epifi-qa-preapprovedloan"

QuestSdk:
  Disable: false

FeatureReleaseConfig:
  FeatureConstraints:
    LOANS_FEDERAL_V2:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    # INTERNAL    # string to be in format "REPEAT_LOANS_<VENDOR>_<LOAN_PROGRAM>" e.g. REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL
    # use the same string as in enums to add feature release config if needed for controlled rollout.
    REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNALFeatureReleaseConfig:
    FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 378
        MinIOSVersion: 2402


EsignLinkExpirationInSecs: 3600

EnableDataExistenceManager: true

Lendability:
  Url: "https://loan-default.data-dev.pointz.in"
  EvaluationRuleMatrix:
    - "PD_CATEGORY_LOW:LOAN_AFFINITY_CATEGORY_HIGH":
        Value: 100
    - "PD_CATEGORY_LOW:LOAN_AFFINITY_CATEGORY_MEDIUM":
        Value: 100
    - "PD_CATEGORY_LOW:LOAN_AFFINITY_CATEGORY_LOW":
        Value: 100
    - "PD_CATEGORY_MEDIUM:LOAN_AFFINITY_CATEGORY_HIGH":
        Value: 100
    - "PD_CATEGORY_MEDIUM:LOAN_AFFINITY_CATEGORY_MEDIUM":
        Value: 100
    - "PD_CATEGORY_MEDIUM:LOAN_AFFINITY_CATEGORY_LOW":
        Value: 10
    - "PD_CATEGORY_HIGH:LOAN_AFFINITY_CATEGORY_HIGH":
        Value: 10
    - "PD_CATEGORY_HIGH:LOAN_AFFINITY_CATEGORY_MEDIUM":
        Value: 10
    - "PD_CATEGORY_HIGH:LOAN_AFFINITY_CATEGORY_LOW":
        Value: 10

LopeOverrideConfig:
  ShowSgEligibilityOverAbflSoftOffer: false
  ShowSgEligibilityOverMvSoftOffer: true

AutoCancelCurrentLrConfig:
  MinAndroidVersion: 458
  MinIOSVersion: 2888
