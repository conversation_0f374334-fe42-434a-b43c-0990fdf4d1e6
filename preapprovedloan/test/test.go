package test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"log"

	"github.com/mohae/deepcopy"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	palWorkerConfig "github.com/epifi/gamma/preapprovedloan/config/worker"
	palWorkerGenConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
)

// InitTestServer instantiates necessary config needed for unit tests.
func InitTestServer() (*config.Config, *genconf.Config, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	// Init dynamic config
	genConf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	return conf, genConf, func() {
		_ = logger.Log.Sync()
	}
}

// InitTestWorker initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
// nolint: dupl
func InitTestWorker() (*palWorkerConfig.Config, *palWorkerGenConf.Config, func()) {
	var err error
	// Init config
	conf, err := palWorkerConfig.LoadConfig()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Init dynamic config
	dynConf, err := palWorkerGenConf.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)
	err = epifitemporal.InitWorkflowParams(conf.WorkflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		logger.Panic("failed to load workflow params", zap.Error(err))
	}

	err = epifitemporal.InitDefaultActivityParams(conf.DefaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		logger.Panic("failed to load default activity params", zap.Error(err))
	}

	return conf, dynConf, func() {
		_ = logger.Log.Sync()
	}
}

func InitDbConns(conf *config.Config, useRandomScopedDb bool, populateFixtures bool) (*storageV2.DBResourceProvider[*gorm.DB], *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor], map[string]*gorm.DB, func()) {
	var (
		teardownDbFuncs []func()
	)

	ownershipToDbConfigMap := deepcopy.Copy(conf.PgDbConfigMap.GetOwnershipToDbConfigMap()).(map[commontypes.Ownership]*cfg.DB)

	for _, dbConf := range ownershipToDbConfigMap {
		if useRandomScopedDb {
			dbConn, randomDbName, teardownDbFunc, err := pkgTest.PrepareRandomScopedRdsTestDb(dbConf, false)
			if err != nil {
				logger.Panic("failed to initiate db", zap.Error(err))
			}
			if populateFixtures {
				pkgTest.PopulateRdsFixtures(pkgTest.NewZapLogger(logger.Log), dbConn, dbConf.GetName())
			}
			dbConf.Name = randomDbName
			teardownDbFuncs = append(teardownDbFuncs, teardownDbFunc)
		} else {
			dbConn, err := pkgTest.PrepareRdsTestDB(dbConf)
			if err != nil {
				logger.Panic("failed to initiate db", zap.Error(err))
			}
			teardownDbFuncs = append(teardownDbFuncs, func() {
				sqlDB, _ := dbConn.DB()
				_ = sqlDB.Close()
			})

			if populateFixtures {
				pkgTest.PopulateRdsFixtures(pkgTest.NewZapLogger(logger.Log), dbConn, dbConf.GetName())
			}
		}
	}
	// Initializing dbUtilsProvider to get db connections and txn executers
	dbConnProvider, dbTxnExecutorProvider, dbConnTeardown, err := storageV2.NewDBResourceProvider(ownershipToDbConfigMap, false)
	if err != nil {
		logger.Panic("failed to initialise db resource provider", zap.Error(err))
	}

	dbNameToDb := map[string]*gorm.DB{}

	for ownership, dbConfig := range ownershipToDbConfigMap {
		dbConn, dbConnErr := dbConnProvider.GetResourceForOwnership(ownership)
		if dbConnErr != nil {
			logger.Panic("failed to get db resource", zap.Error(dbConnErr))
		}

		dbNameToDb[dbConfig.GetName()] = dbConn
	}

	return dbConnProvider, dbTxnExecutorProvider, dbNameToDb, func() {
		dbConnTeardown()
		for i := 0; i < len(teardownDbFuncs); i++ {
			teardownDbFuncs[i]()
		}
	}
}
