package liquiloans

import (
	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	money2 "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
)

type FldgProvider struct {
	*baseprovider.Provider
}

func NewFldgProvider(baseProvider *baseprovider.Provider) *FldgProvider {
	return &FldgProvider{Provider: baseProvider}
}

var _ provider.IDeeplinkProvider = &FldgProvider{}

func (p *FldgProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG,
		Vendor:      palFeEnumsPb.Vendor_LIQUILOANS,
	}
}

// nolint:dupl
func (p *FldgProvider) GetFailedCkycCheckDeeplink(lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplinkPb.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/profile_validation_failed.png",
				Details: []*deeplinkPb.InfoItem{
					{
						Title: "Your application\ncannot be taken forward",
						Desc:  "This is because some of the internal checks \nhave failed.",
					},
				},
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "Back to loans",
					Deeplink:     p.GetLoanLandingInfo(lh),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader: lh,
			},
		},
	}
}

// nolint:dupl
func (p *FldgProvider) GetInitiateESignScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, documentUrl string) *deeplinkPb.Deeplink {
	baseDl := p.Provider.GetInitiateESignScreen(lh, loanRequestId, documentUrl)
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().Title = "To complete application,\ne-Sign the loan document"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().SubTitle = "Our partner requires this for Instant Loan"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().BulletPoints = []string{
		"Review a document that has all your loan details",
		"Enter your OTP",
	}
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().PartnerIconUrl = ""
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().BottomSheetText = "Next: You’ll see your loan agreement"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().GetContinue().Deeplink = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
			PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
				DocumentUrl: documentUrl,
				Agree: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "I agree to all the details",
					Deeplink:     p.GetLoanApplicationConfirmationViaOtpScreen(lh, loanRequestId, ""),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader:    lh,
				LoanRequestId: loanRequestId,
			},
		},
	}
	return baseDl
}

func (p *FldgProvider) GetLoanApplicationConfirmationViaOtpScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_CONFIRMATION_VIA_OTP_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
			PreApprovedLoanApplicationConfirmationViaOtpScreenOptions: &deeplinkPb.PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
				LoanRequestId:       loanRequestId,
				LoanHeader:          lh,
				LoanStepExecutionId: lseId,
			},
		},
	}
}

// nolint:dupl
func (p *FldgProvider) GetPLMandateScreenDeeplink(lh *palFeEnumsPb.LoanHeader, loanReqId string, entryUrl string, exitUrl string, mandateId string, phNum *commontypes.PhoneNumber, nextScreen *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_MANDATE_INITIATE_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedMandateInitiateScreenOptions{
			PreApprovedMandateInitiateScreenOptions: &deeplinkPb.PreApprovedLoanInitiateMandateScreenOptions{
				HeaderInfoItem: &deeplinkPb.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/preapprovedloan/enachIcon.png",
					Title: "Let’s set auto-pay for EMI",
				},
				BulletPoints: []*deeplinkPb.InfoItem{
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/1.png",
						Title: "Review the mandate details",
					},
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/2.png",
						Title: "Choose ‘Debit Card’ as your preferred option",
					},
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/3.png",
						Title: "Keep your Fi-Federal Debit Card handy to complete the auto pay setup",
					},
				},
				Continue: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: "Continue",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_WEB_PAGE,
						ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{},
						},
					},
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanRequestId:   loanReqId,
				BottomSheetText: "Next, you’ll be taken to the e-NACH flow",
				EntryUrl:        entryUrl,
				ExitUrl:         exitUrl,
				MandateId:       mandateId,
				ContactNumber:   phNum,
				NextScreen:      nextScreen,
				LoanHeader:      lh,
				CtaAction:       deeplinkPb.PreApprovedLoanInitiateMandateScreenOptions_DIGIO_FLOW,
			}},
	}
}

// nolint:dupl
func (p *FldgProvider) GetEmploymentDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedEmploymentDetailsScreenOptions{
			PreApprovedEmploymentDetailsScreenOptions: &deeplinkPb.PreApprovedEmploymentDetailsScreenOptions{
				HeaderInfoItem: &deeplinkPb.InfoItem{
					Title: "Tell us more about you",
					Desc:  "This will help us evaluate your scope to repay the loan.",
				},
				ContinueCta: &deeplinkPb.Cta{
					Type:     deeplinkPb.Cta_CUSTOM,
					Text:     "Continue",
					Deeplink: p.GetLoanApplicationStatusPollScreenDeepLink(lh, loanRequestId),
				},
				LoanReqId:  loanRequestId,
				LoanHeader: lh,
			},
		},
	}
}

// nolint:dupl
func (p *FldgProvider) GetPlUpdatedRateScreenDeeplink(lh *palFeEnumsPb.LoanHeader, lr *palPb.LoanRequest) *deeplinkPb.Deeplink {
	dl := p.Provider.GetPlUpdatedRateScreenDeeplink(lh, lr)
	return dl
}

// nolint:dupl
func (p *FldgProvider) GetInformationDialogDeeplink(lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	dl := p.Provider.GetInformationDialogDeeplink(lh)
	return dl
}

// nolint:dupl
func (p *FldgProvider) GetLoanApplicationStatusScreenDeepLink(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanReq *palPb.LoanRequest) (*deeplinkPb.Deeplink, error) {
	emiAmountString := money2.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), 0, true)
	nextEmiDateString := getEmiStartDateStr(loanReq)
	isFeatureEnabled := baseprovider.IsFeatureEnabled(ctx, 251, 2000)
	dl, err := p.Provider.GetLoanApplicationStatusScreenDeepLink(ctx, lh, loanReq)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting loan application status screen deeplink, err")
	}
	if !isFeatureEnabled {
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetEmiInfo().Desc = fmt.Sprintf("%v • %v", nextEmiDateString, emiAmountString)
	} else {
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetEmiInfoV2().GetDesc().DisplayValue = &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%v • %v", nextEmiDateString, emiAmountString)}
	}
	return dl, nil
}

// nolint:dupl
func (p *FldgProvider) GetPollScreenDeepLink(lh *palFeEnumsPb.LoanHeader, lse *palPb.LoanStepExecution) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetPollScreenDeepLink(lh, lse)
}

func (p *FldgProvider) GetMandateFailureScreen(ctx context.Context, lrId string, lseId string, tryAgainDl *deeplinkPb.Deeplink, failedAccountDetails *palPb.MandateData_BankingDetails_AccountDetails) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetMandateFailureScreen(ctx, lrId, lseId, tryAgainDl, failedAccountDetails)
}

func (p *FldgProvider) GetLoansMandateSetupScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string, loanRequestId string, lseId string, params *provider.LoansMandateSetupScreenParams) (*deeplinkPb.Deeplink, error) {
	return getLoansMandateSetupScreenForPlAndFldg(ctx, lh, actorId, loanRequestId, lseId, params, p.Provider)
}

func (p *FldgProvider) GetLoansMandateInitiateScreenV2(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansMandateInitiateScreenV2Params) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetLoansMandateInitiateScreenV2(ctx, lh, loanRequestId, lseId, params)
}

func (p *FldgProvider) GetLoansAlternateAccountsScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansAlternateAccountsScreenParams) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetLoansAlternateAccountsScreen(ctx, lh, loanRequestId, lseId, params)
}
