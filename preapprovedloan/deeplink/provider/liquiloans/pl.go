//nolint:dupl
package liquiloans

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	money2 "github.com/epifi/be-common/pkg/money"

	analyticsPb "github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	pkgLoans "github.com/epifi/gamma/pkg/loans"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

type PlProvider struct {
	*baseprovider.Provider
}

func NewProvider(baseProvider *baseprovider.Provider) *PlProvider {
	return &PlProvider{Provider: baseProvider}
}

var _ provider.IDeeplinkProvider = &PlProvider{}

func (p *PlProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palFeEnumsPb.Vendor_LIQUILOANS,
	}
}

func (p *PlProvider) GetFailedCkycCheckDeeplink(lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplinkPb.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/profile_validation_failed.png",
				Details: []*deeplinkPb.InfoItem{
					{
						Title: "Your application\ncannot be taken forward",
						Desc:  "This is because some of the internal checks \nhave failed.",
					},
				},
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "Back to loans",
					Deeplink:     p.GetLoanLandingInfo(lh),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader: lh,
			},
		},
	}
}

func (p *PlProvider) GetInitiateESignScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, documentUrl string) *deeplinkPb.Deeplink {
	baseDl := p.Provider.GetInitiateESignScreen(lh, loanRequestId, documentUrl)
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().Title = "To complete application,\ne-Sign the loan document"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().SubTitle = "Our partner requires this for Instant Loan"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().BulletPoints = []string{
		"Review a document that has all your loan details",
		"Enter your OTP",
	}
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().PartnerIconUrl = ""
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().BottomSheetText = "Next: You’ll see your loan agreement"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().GetContinue().Deeplink = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
			PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
				DocumentUrl: documentUrl,
				Agree: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "I agree to all the details",
					Deeplink:     p.GetLoanApplicationConfirmationViaOtpScreen(lh, loanRequestId, ""),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader:    lh,
				LoanRequestId: loanRequestId,
			},
		},
	}
	return baseDl
}

func (p *PlProvider) GetPLMandateScreenDeeplink(ctx context.Context, actorId string, lh *palFeEnumsPb.LoanHeader, loanReqId string, entryUrl string, exitUrl string, mandateId string, phNum *commontypes.PhoneNumber, nextScreen *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_MANDATE_INITIATE_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedMandateInitiateScreenOptions{
			PreApprovedMandateInitiateScreenOptions: &deeplinkPb.PreApprovedLoanInitiateMandateScreenOptions{
				HeaderInfoItem: &deeplinkPb.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/preapprovedloan/enachIcon.png",
					Title: "Let’s set auto-pay for EMI",
				},
				BulletPoints: []*deeplinkPb.InfoItem{
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/1.png",
						Title: "Review the mandate details",
					},
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/2.png",
						Title: "Choose ‘Debit Card’ as your preferred option",
					},
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/3.png",
						Title: "Keep your Fi-Federal Debit Card handy to complete the auto pay setup",
					},
				},
				Continue: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: "Continue",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_WEB_PAGE,
						ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{},
						},
					},
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanRequestId:   loanReqId,
				BottomSheetText: "Next, you’ll be taken to the e-NACH flow",
				EntryUrl:        entryUrl,
				ExitUrl:         exitUrl,
				MandateId:       mandateId,
				ContactNumber:   phNum,
				NextScreen:      nextScreen,
				LoanHeader:      lh,
				CtaAction:       deeplinkPb.PreApprovedLoanInitiateMandateScreenOptions_DIGIO_FLOW,
				ToolbarTitle:    helper.GetText("Instant Loan", "#383838", "", commontypes.FontStyle_HEADLINE_2),
			}},
	}
	_ = p.Provider.EnrichInitiateMandateScreen(ctx, actorId, dl.GetPreApprovedMandateInitiateScreenOptions(), p.GetLoanDashboardScreenDeepLink(lh))
	return dl
}

func (p *PlProvider) GetEmploymentDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink {
	baseDl := p.Provider.GetEmploymentDetailsDeeplink(lh, loanRequestId)
	return baseDl
}

func (p *PlProvider) GetPlUpdatedRateScreenDeeplink(lh *palFeEnumsPb.LoanHeader, lr *palPb.LoanRequest) *deeplinkPb.Deeplink {
	dl := p.Provider.GetPlUpdatedRateScreenDeeplink(lh, lr)
	return dl
}

func (p *PlProvider) GetInformationDialogDeeplink(lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	dl := p.Provider.GetInformationDialogDeeplink(lh)
	return dl
}

func (p *PlProvider) GetLoanApplicationStatusScreenDeepLink(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanReq *palPb.LoanRequest) (*deeplinkPb.Deeplink, error) {
	emiAmountString := money2.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), 0, true)
	nextEmiDateString := getEmiStartDateStr(loanReq)
	disbursalAmountString := money2.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDisbursalAmount(), 0, true)
	isFeatureEnabled := baseprovider.IsFeatureEnabled(ctx, 251, 3000)
	dl, err := p.Provider.GetLoanApplicationStatusScreenDeepLink(ctx, lh, loanReq)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting loan application status deeplink")
	}
	if !isFeatureEnabled {
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetEmiInfo().Desc = fmt.Sprintf("%v • %v", nextEmiDateString, emiAmountString)
	} else {
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetEmiInfoV2().GetDesc().DisplayValue = &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%v • %v", nextEmiDateString, emiAmountString)}
	}

	if pkgLoans.LoanProgramEligibleForLLAltAccFlow[lh.GetLoanProgram()] {
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetLoanStatus().Desc = fmt.Sprintf("In a few minutes, %v will arrive in your chosen bank account. The loan document will reach your inbox.", disbursalAmountString)
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetLoanStatusV2().GetDesc().DisplayValue = &commontypes.Text_PlainString{PlainString: fmt.Sprintf("In a few minutes, %v will arrive in your chosen bank account. The loan document will reach your inbox.", disbursalAmountString)}
	}

	// populate entry point for "Ok" button to Loan Application Status screen
	ctaScreenOptions := dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetCta().GetDeeplink().GetPreApprovedLoanLandingScreenOptions()
	if ctaScreenOptions != nil {
		ctaScreenOptions.EntryPoint = analyticsPb.AnalyticsScreenName_PL_APPLICATION_STATUS.String()
	}
	buttonScreenOptions := dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetButton().GetCta().GetDeeplink().GetPreApprovedLoanLandingScreenOptions()
	if buttonScreenOptions != nil {
		buttonScreenOptions.EntryPoint = analyticsPb.AnalyticsScreenName_PL_APPLICATION_STATUS.String()
	}
	logger.Debug(ctx, "deeplink for loan application status screen", zap.Any("deeplink", dl))
	return dl, nil
}

// getEmiStartDateStr tries to fetch emi start date from lr, if not present uses the below logic based on what happens on LL side
// if current date is between 1st of month M till 25th of month M (both inclusive) then EMI date is 5th of month M+1
// else EMI date is 5th of month M+2
func getEmiStartDateStr(lr *palPb.LoanRequest) string {
	if lr.GetDetails().GetLoanInfo().GetEmiStartDate() != nil {
		return datetime.DateToTimeV2(lr.GetDetails().GetLoanInfo().GetEmiStartDate(), datetime.IST).Format("02 Jan 2006")
	}
	timeNowInIST := time.Now().In(datetime.IST)
	delayByMonth := 0
	if timeNowInIST.Day() > 25 {
		delayByMonth += 1
	}
	nextEmiDate := datetime.TimeToDateInLoc(datetime.StartOfMonth(time.Now().AddDate(0, 1+delayByMonth, 0).In(datetime.IST)).AddDate(0, 0, 4), datetime.IST)
	return datetime.DateToString(nextEmiDate, "02 Jan 2006", datetime.IST)
}

func (p *PlProvider) GetPollScreenDeepLink(lh *palFeEnumsPb.LoanHeader, lse *palPb.LoanStepExecution) (*deeplinkPb.Deeplink, error) {
	dl, err := p.Provider.GetPollScreenDeepLink(lh, lse)
	return dl, err
}

func (p *PlProvider) GetLoanActivityStatusPollScreenDeepLink(lh *palFeEnumsPb.LoanHeader, refId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ACTIVITY_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanActivityStatusPollScreenOptions{
			PreApprovedLoanActivityStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanActivityStatusPollScreenOptions{
				RetryAttemptNumber: 0,
				RetryDelay:         1000,
				RefId:              refId,
				LoanHeader:         lh,
			},
		},
	}
}

func (p *PlProvider) GetAddressConfirmationScreen(lh *palFeEnumsPb.LoanHeader, loanReqId string, customerName string) *deeplinkPb.Deeplink {
	baseDl := p.Provider.GetAddressConfirmationScreen(lh, loanReqId, customerName)
	return baseDl
}

func (p *PlProvider) GetLoansMandateSetupScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string, loanRequestId string, lseId string, params *provider.LoansMandateSetupScreenParams) (*deeplinkPb.Deeplink, error) {
	return getLoansMandateSetupScreenForPlAndFldg(ctx, lh, actorId, loanRequestId, lseId, params, p.Provider)
}

func getLoansMandateSetupScreenForPlAndFldg(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string, loanRequestId string, lseId string, params *provider.LoansMandateSetupScreenParams, baseProvider *baseprovider.Provider) (*deeplinkPb.Deeplink, error) {
	screenOption := &palTypesPb.LoansMandateSetupScreenOptions{
		LoanHeader:    lh,
		ScreenName:    analyticsPb.AnalyticsScreenName_PL_MANDATE_SETUP_SCREEN,
		LoanRequestId: loanRequestId,
		LseId:         lseId,
		MandateViewInfo: &palTypesPb.MandateViewInfo{
			MandateViewType: palTypesPb.MandateViewType_MANDATE_VIEW_TYPE_SDK,
			ViewInfo: &palTypesPb.MandateViewInfo_SdkInfo_{
				SdkInfo: &palTypesPb.MandateViewInfo_SdkInfo{
					MandateSdkVendor: palTypesPb.MandateSdkVendor_MANDATE_SDK_VENDOR_DIGIO,
					Info: &palTypesPb.MandateViewInfo_SdkInfo_DigioInfo{
						DigioInfo: &palTypesPb.MandateViewInfo_DigioInfo{
							MandateId:     params.SdkParams.DigioSdkParams.MandateId,
							ContactNumber: params.SdkParams.DigioSdkParams.PhoneNumber,
						},
					},
				},
			},
		},
	}
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)
	// we only need to enrich deeplink in case of iOS since android does not allow overlay in SDK view
	// For android, different screen has been defined for same use-case: Screen_LOANS_DEBIT_CARD_DETAILS_SCREEN
	if appPlatform == commontypes.Platform_IOS {
		_ = baseProvider.EnrichMandateSetupScreen(ctx, actorId, screenOption, &baseprovider.EnrichMandateSetupScreenParams{
			IsFiAccount:              params.MandateOverlayDetailsParams.IsFiAccount,
			IsDropOffDlEnrichmentReq: false,
			ExitNowDropOffDl:         baseProvider.GetLoanDashboardScreenDeepLink(lh),
		})
	}
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_MANDATE_SETUP_SCREEN, screenOption)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating loans mandate setup screen deeplink")
	}
	return dl, nil
}

func (p *PlProvider) GetMandateFailureScreen(ctx context.Context, lrId string, lseId string, tryAgainDl *deeplinkPb.Deeplink, failedAccountDetails *palPb.MandateData_BankingDetails_AccountDetails) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetMandateFailureScreen(ctx, lrId, lseId, tryAgainDl, failedAccountDetails)
}
func (p *PlProvider) GetLoansMandateInitiateScreenV2(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansMandateInitiateScreenV2Params) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetLoansMandateInitiateScreenV2(ctx, lh, loanRequestId, lseId, params)
}

func (p *PlProvider) GetLoansAlternateAccountsScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansAlternateAccountsScreenParams) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetLoansAlternateAccountsScreen(ctx, lh, loanRequestId, lseId, params)
}
