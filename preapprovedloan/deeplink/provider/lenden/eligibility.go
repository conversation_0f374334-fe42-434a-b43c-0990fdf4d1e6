package lenden

import (
	"errors"
	"fmt"

	moneypb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	lendenPkg "github.com/epifi/gamma/pkg/loans/lenden"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

type EligibilityProvider struct {
	*baseprovider.Provider
}

var _ provider.IDeeplinkProvider = &EligibilityProvider{}

func NewEligibilityProvider(baseDeeplinkProvider *baseprovider.Provider) *EligibilityProvider {
	return &EligibilityProvider{
		baseDeeplinkProvider,
	}
}

const (
	privacyPolicyUrl   = "https://www.lendenclub.com/privacy-policy/"
	termsOfServicesUrl = "https://www.lendenclub.com/terms-of-services/"
)

func (ldc *EligibilityProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
		Vendor:      palFeEnumsPb.Vendor_EPIFI_TECH,
		DataOwner:   palFeEnumsPb.Vendor_LENDEN,
	}
}

func (ldc *EligibilityProvider) GetPreBreLoanConsentScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error) {
	privacyPolicyAndToS := fmt.Sprintf("I have read and agreed to the  <a href=\"%s\" style=\"color:#00B899\">Privacy Policy</a> and <a href=\"%s\" style=\"color:#00B899\">Terms of Services</a>", privacyPolicyUrl, termsOfServicesUrl)
	return deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN, &palTypesPb.LoansConsentV2ScreenOptions{
		LoanHeader:    lh,
		Flow:          palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		LoanRequestId: loanRequestId,
		TopIcon:       commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/loans/lenden/innofin-solutions-name-and-logo.png", 32, 84),
		Title:         helper.GetText("Confirm the following to\n apply for a loan", colors.ColorNight, "", commontypes.FontStyle_HEADLINE_L),
		Subtitle:      helper.GetText("This is required by our lending partner", colors.ColorSlate, "", commontypes.FontStyle_HEADLINE_S),
		ConsentItems: []*widget.CheckboxItem{
			{
				Id: consent.ConsentType_CONSENT_LENDEN_CREDIT_REPORT_CHECK_AND_PAN_VALIDATION.String(),
				DisplayText: helper.GetText("I confirm that above address is my current address and agree to allow Innofin Solutions Pvt Ltd "+
					"to check my credit bureau and validate PAN from NSDL via third party scheme & also "+
					"agree to receive communication over WhatsApp from Innofin Solutions Pvt Ltd", colors.ColorNight, "", commontypes.FontStyle_BODY_4),
			},
			{
				Id:          consent.ConsentType_CONSENT_PRE_BRE_LENDEN_PRIVACY_POLICY_AND_TNC.String(),
				DisplayText: commontypes.GetTextFromHtmlStringFontColourFontStyle(privacyPolicyAndToS, "#333333", commontypes.FontStyle_BODY_4),
			},
			{
				Id: consent.ConsentType_CONSENT_LENDEN_DATA_SHARING.String(),
				DisplayText: helper.GetText("I hereby provide my consent to Innofin Solutions Private Limited for sharing my personal details, loan amount, interest rate, and credit score "+
					"with potential lenders on it's platform to process my loan application", colors.ColorNight, "", commontypes.FontStyle_BODY_4),
			},
			{
				Id:          consent.ConsentType_CONSENT_TYPE_LDC_CKYC_DATA_PULL.String(),
				DisplayText: commontypes.GetTextFromStringFontColourFontStyle("By proceeding, you declare that you grant consent to process and fetch your data from CKYC", colors.ColorNight, commontypes.FontStyle_BODY_4),
			},
		},
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "I agree to above consents",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		BgColor:        colors.ColorSnow,
		ConsentBgColor: colors.ColorOnDarkHighEmphasis,
		LoansCta: &palTypesPb.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "I agree to above consents",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
			CtaAction: &palTypesPb.LoansCtaAction{
				Action: &palTypesPb.LoansCtaAction_CallRpc_{
					CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
						RpcName: palTypesPb.LoansCtaAction_RPC_COLLECT_DATA,
						CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
							LoanHeader: lh,
							LrId:       loanRequestId,
							LseFlow:    palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
						},
					},
				},
			},
		},
	})
}

func (ldc *EligibilityProvider) GetPreBreLoanOfferDetailsScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error) {
	amtSelectionSection := ldc.getAmtSelectionSectionForOfferV3()
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OFFER_DETAILS_SCREEN, &palTypesPb.LoansOfferDetailsScreenOptions{
		LoanHeader:             lh,
		LoanRequestId:          loanRequestId,
		Flow:                   palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		LoecOwner:              palFeEnumsPb.Vendor_LENDEN,
		AmountSelectionSection: amtSelectionSection,
		SubmitCta: &deeplinkPb.Cta{
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			Text:         "Save & preview details",
		},
		PartnerVisualElementComponent: &palTypesPb.VisualElementComponent{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/loans/lenden/powered-by-innofin-solutions-logo.png").WithProperties(
				&commontypes.VisualElementProperties{Width: 138, Height: 16}),
			TopMargin: 16,
		},
		BgColor: "#EFF2F6",
		ToolbarRightCta: ui.NewITC().WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 16, 16).WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Get help", "#6A6D70", commontypes.FontStyle_HEADLINE_M)).WithDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HELP_MAIN,
		}).WithContainerPadding(6, 17, 6, 8).WithContainerBackgroundColor("#FFFFFF").WithContainerCornerRadius(16),
	}), nil
}

// pencil-url:https://epifi-icons.s3.ap-south-1.amazonaws.com/pencil.png
func (ldc *EligibilityProvider) getAmtSelectionSectionForOfferV3() *palTypesPb.LoansOfferDetailsScreenOptions_AmountSelectionSection {
	minAmt := &moneypb.Money{CurrencyCode: "INR", Units: lendenPkg.MinProductAmountINRValue}
	maxAmt := &moneypb.Money{CurrencyCode: "INR", Units: lendenPkg.MaxProductAmountINRValue}
	amtSelectionSection := &palTypesPb.LoansOfferDetailsScreenOptions_AmountSelectionSection{
		CardTitle: commontypes.GetTextFromStringFontColourFontStyle("What’s your desired loan amount?", "#313234", commontypes.FontStyle_HEADLINE_M),
		CardIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/money-with-bg.png").WithProperties(&commontypes.VisualElementProperties{
			Width:  380,
			Height: 172,
		}),
		CollapsedViewTitle:   commontypes.GetTextFromStringFontColourFontStyle("Amount you need", "#313234", commontypes.FontStyle_HEADLINE_M),
		CollapsedViewAmount:  typesPb.GetFromBeMoney(maxAmt),
		BgColor:              &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
		IsCollapsedView:      false,
		MinLoanAmount:        typesPb.GetFromBeMoney(minAmt),
		MaxLoanAmount:        typesPb.GetFromBeMoney(maxAmt),
		DefaultLoanAmount:    typesPb.GetFromBeMoney(maxAmt),
		AmountSliderMinLabel: commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormat(minAmt, 0, true), "#929599", commontypes.FontStyle_OVERLINE_XS_CAPS),
		AmountSliderMaxLabel: commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormat(maxAmt, 0, true), "#929599", commontypes.FontStyle_OVERLINE_XS_CAPS),
		StepSize:             lendenPkg.MinProductAmountINRStepSize,
		InterestRate: &ui.IconTextComponent{
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/arrow-down.png", 24, 24),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#F6F9FD",
				LeftPadding:   12,
				RightPadding:  12,
				TopPadding:    8,
				BottomPadding: 8,
				CornerRadius:  30,
			},
			Texts: []*commontypes.Text{
				GetText(fmt.Sprintf("@ %d%% p.a.", lendenPkg.MinAnnualInterestRatePercentageValue), "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			Deeplink: deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET, &palTypesPb.LoansOptionSelectionScreenOption{
				Title: GetText("Select interest rate", "#282828", commontypes.FontStyle_SUBTITLE_2),
				OptionView: &ui.OptionSelectionView{
					Items: getInterestRateSelectionItems(),
				},
				PrimaryCta: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: "Save",
				},
			}),
		},
		StepInterval: lendenPkg.MinProductAmountINRStepSize,
		StepCount:    lendenPkg.MaxNumberOfSteps,
	}
	amtSelectionSection.IsCollapsedView = false
	return amtSelectionSection
}

func getInterestRateSelectionItems() []*ui.OptionSelectionItem {
	var items []*ui.OptionSelectionItem
	for _, ratePercentageValue := range lendenPkg.GetAllowedInterestRatePercentageValues() {
		items = append(items, &ui.OptionSelectionItem{
			Id: int64(ratePercentageValue),
			OptionValue: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					GetText(fmt.Sprintf("@ %d%% p.a.", ratePercentageValue), "#646464", commontypes.FontStyle_SUBTITLE_3),
				},
			},
		})
	}
	return items
}

func (ldc *EligibilityProvider) GetPreBreLoansOfferIntroScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error) {
	loanOfferDetailsScreen, err := ldc.GetPreBreLoanOfferDetailsScreen(lh, loanRequestId)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("error getting pre bre loan offer details screen: %v", err))
	}
	return loanOfferDetailsScreen, nil
	// return ldc.getOfferIntroScreenWithOfferDetailsCta(lh, loanRequestId, loanOfferDetailsScreen), nil
}

// nolint: unused
func (ldc *EligibilityProvider) getOfferIntroScreenWithOfferDetailsCta(lh *palFeEnumsPb.LoanHeader, loanRequestId string, loanOfferDetailsScreen *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN, &palTypesPb.LoansOfferScreenOptions{
		BackgroundVisualElement: commontypes.GetVisualElementLottieFromUrl(uiFrontend.MoneyFallingLottie).WithRepeatCount(-1),
		ScreenBgColor:           widget.GetBlockBackgroundColour("#E6E9ED"),
		LoanHeader:              lh,
		LoanRequestId:           loanRequestId,
		Flow:                    palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		ToolbarRightCta: ui.NewITC().WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 16, 16).WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Get help", "#6A6D70", commontypes.FontStyle_HEADLINE_M)).WithDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HELP_MAIN,
		}).WithContainerPadding(4, 17, 4, 8).WithContainerBackgroundColor("#FFFFFF").WithContainerCornerRadius(16),
		Components: []*palTypesPb.LoansScreenUiComponents{
			{
				Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
					LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
						CardBgColor: "#EFF2F6",
						CardTitle:   GetText("You Have An Offer:", "#313234", commontypes.FontStyle_HEADLINE_2),
						OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
							OfferTitleV2: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									GetText("You Can Borrow Upto:", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
								},
							},
							OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
								OfferAmount: &ui.IconTextComponent{
									Texts: []*commontypes.Text{
										GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
										GetText(fmt.Sprintf("%d", lendenPkg.MaxProductAmountINRValue), "#313234", commontypes.FontStyle_NUMBER_3XL),
									},
								},
							},
							OfferDescriptionChips: []*ui.IconTextComponent{
								{
									LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.HighVoltageIcon, 20, 20),
									Texts: []*commontypes.Text{
										GetText("Takes less than 2 mins", "#007A56", commontypes.FontStyle_SUBTITLE_S),
									},
									LeftImgTxtPadding: 4,
									ContainerProperties: &ui.IconTextComponent_ContainerProperties{
										LeftPadding:   8,
										RightPadding:  8,
										TopPadding:    9,
										BottomPadding: 9,
										CornerRadius:  16,
										BgColor:       "#DCF3EE",
									},
								},
							},
						},
						OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
							BgColor: "#E4F1F5",
							StageProgress: &palTypesPb.SectionTypeProgress{
								ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
									{
										Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/el-choose-amt", 27, 27),
										Text:               GetText("Choose \namount", "#313234", commontypes.FontStyle_SUBTITLE_XS),
										IsCompleted:        true,
										FollowUpLineColour: "#BCDCE7",
									},
									{
										Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/el-confirm-details", 27, 27),
										Text:               GetText("Confirm \ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
										IsCompleted:        true,
										FollowUpLineColour: "#BCDCE7",
									},
									{
										Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/el-get-money", 27, 27),
										Text:               GetText("Get \nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
										IsCompleted:        true,
										FollowUpLineColour: "#BCDCE7",
									},
								},
							},
							PartnerLogo: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/loans/lenden/powered-by-innofin-solutions-logo.png", 16, 139),
						},
					},
				},
			},
			{
				Component: &palTypesPb.LoansScreenUiComponents_FaqsScrollableCardsViewComponent{
					FaqsScrollableCardsViewComponent: &palTypesPb.FaqsScrollableCardsViewComponent{
						TopMargin:      48,
						ComponentTitle: GetText("Popular Questions", "#313234", commontypes.FontStyle_HEADLINE_S),
						Faqs: []*deeplinkPb.InfoItemV3{
							{
								Title: GetText("Will I need any paperwork?", "#313234", commontypes.FontStyle_HEADLINE_S),
								Desc:  GetText("You can get your loan without any \npaperwork required.", "#929599", commontypes.FontStyle_BODY_XS),
							},
							{
								Title: GetText("Will there be any charges if \nI close my loan early?", "#313234", commontypes.FontStyle_HEADLINE_S),
								Desc:  GetText("Fii has a zero preclosure charge \npolicy. You can close the loan any \ntime no matter how long the tenure \nyou choose", "#929599", commontypes.FontStyle_BODY_XS),
							},
						},
					},
				},
			},
		},
		CtaBanner: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(" Fi is trusted by 3.5+ Lakh Users ", "#007A56", commontypes.FontStyle_BUTTON_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#DCF3EE",
				LeftPadding:   20,
				RightPadding:  20,
				TopPadding:    8,
				BottomPadding: 8,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/el-green-people", 14, 14),
		},
		PrimaryCta: &deeplinkPb.Cta{
			Type:     deeplinkPb.Cta_CUSTOM,
			Text:     "Continue",
			Deeplink: loanOfferDetailsScreen,
		},
	})
}

func GetText(text string, color string, style commontypes.FontStyle) *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: text,
		},
		FontColor: color,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: style,
		},
	}
}
