package idfc

// nolint: goimports
import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	money2 "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/names"

	"github.com/epifi/gamma/api/frontend/analytics"
	analyticsPb "github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	palPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	vkycScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	addresshelper "github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
	vkycFailureHandlers "github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc/vkyc/failure_handlers"
)

const (
	PAN_LENGTH = 10
)

type Provider struct {
	*baseprovider.Provider
	vkycFailureHandler vkycFailureHandlers.Factory
}

func NewProvider(
	baseProvider *baseprovider.Provider,
	vkycFailureHandler vkycFailureHandlers.Factory,
) *Provider {
	return &Provider{
		Provider:           baseProvider,
		vkycFailureHandler: vkycFailureHandler,
	}
}

var _ provider.IDeeplinkProvider = &Provider{}

func (p *Provider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palFeEnumsPb.Vendor_IDFC,
	}
}

func (p *Provider) GetFailedCkycCheckDeeplink(lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplinkPb.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/profile_validation_failed.png",
				Details: []*deeplinkPb.InfoItem{
					{
						Title: "Your application\ncannot be taken forward",
						Desc:  "This is because some of the internal checks \nhave failed.",
					},
				},
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "Back to loans",
					Deeplink:     p.GetLoanLandingInfo(lh),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader: lh,
			},
		},
	}
}

func (p *Provider) GetPLMandateScreenDeeplink(ctx context.Context, actorId string, lh *palFeEnumsPb.LoanHeader, loanReqId string, base64EncodedHtml string, exitUrl string, nextScreen *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	currMonth := datetime.StartOfMonth(time.Now())
	emiDate := time.Date(currMonth.Year(), currMonth.Month()+1, 3, 0, 0, 0, 0, datetime.IST)
	if time.Now().Day() > 18 {
		emiDate = emiDate.AddDate(0, 1, 0)
	}
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_MANDATE_INITIATE_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedMandateInitiateScreenOptions{
			PreApprovedMandateInitiateScreenOptions: &deeplinkPb.PreApprovedLoanInitiateMandateScreenOptions{
				HeaderInfoItem: &deeplinkPb.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/preapprovedloan/enachIcon.png",
					Title: "Let’s set auto-pay for EMI",
				},
				BulletPoints: []*deeplinkPb.InfoItem{
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/1.png",
						Title: "Review the mandate details",
					},
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/2.png",
						Title: "Choose ‘Debit Card’ as your preferred option",
					},
				},
				DisclaimerInfo: &deeplinkPb.InfoItemV2{
					Icon:  "https://epifi-icons.pointz.in/preapprovedloan/mandate_debit_card.png",
					Title: helper.GetText("Please keep your Fi-Federal Debit Card \nhandy for the next step", "#000000", "#F4E7BF", commontypes.FontStyle_SUBTITLE_S),
				},
				Continue: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: "Continue",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_WEB_PAGE,
						ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{},
						},
					},
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
				},
				TermInfos: []*deeplinkPb.TermInfo{
					{
						TermText:        helper.GetTextWithHtml(fmt.Sprintf("I authorise IDFC FIRST Bank to automatically deduct the payment every month <b>(starting on %v 3, %v)</b> from my Federal savings account", emiDate.Format("Jan"), emiDate.Year()), "#333333", commontypes.FontStyle_BODY_XS),
						IsTermClickable: false,
					},
				},
				LoanRequestId:     loanReqId,
				BottomSheetText:   "Next, you’ll be taken to the e-NACH flow",
				NextScreen:        nextScreen,
				LoanHeader:        lh,
				Base64EncodedHtml: base64EncodedHtml,
				ExitUrl:           exitUrl,
				CtaAction:         deeplinkPb.PreApprovedLoanInitiateMandateScreenOptions_HTML_WEBPAGE_FLOW,
			}},
	}
	_ = p.Provider.EnrichInitiateMandateScreen(ctx, actorId, dl.GetPreApprovedMandateInitiateScreenOptions(), p.GetLoanDashboardScreenDeepLink(lh))
	return dl
}

func (p *Provider) GetInitiateESignScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, documentUrl string) *deeplinkPb.Deeplink {
	baseDl := p.Provider.GetInitiateESignScreen(lh, loanRequestId, documentUrl)
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().Title = "To complete application,\ne-Sign the loan document"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().SubTitle = "Our partner requires this for Instant Loan"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().BulletPoints = []string{
		"Review a document that has all your loan details",
		"Enter your OTP",
	}
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().PartnerIconUrl = ""
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().BottomSheetText = "Next: You’ll see your loan agreement"
	baseDl.GetPreApprovedLoanInitiateEsignScreenOptions().GetContinue().Deeplink = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
			PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
				DocumentUrl: documentUrl,
				Agree: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "Continue",
					Deeplink:     p.GetLoanApplicationConfirmationViaOtpScreen(lh, loanRequestId, ""),
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader:    lh,
				LoanRequestId: loanRequestId,
				CheckBoxes: []*widget.CheckboxItem{
					{
						Id: "consent_to_agree_to_terms_and_conditions_of_loan_agreement_of_idfc_first_bank",
						DisplayText: helper.GetText(
							"By continuing, I agree to the above displayed Key Fact Statement and Terms and Conditions",
							"#333333", "", commontypes.FontStyle_BODY_4),
						IsChecked: false,
					},
				},
			},
		},
	}
	return baseDl
}

// nolint: funlen
func (p *Provider) GetAddDetailsScreen(lh *palFeEnumsPb.LoanHeader, detailType palPb.DetailsType, pan string, detailValue string, reqId string) (*deeplinkPb.Deeplink, error) {
	screenOptions := &palPb.PlEnterDetailsScreenOptions{
		LoanHeader:     lh,
		PartnershipUrl: "https://epifi-icons.pointz.in/preapprovedloan/offer-details-partnership-idfc.png",
		LoanRequestId:  reqId,
		PartnershipLogo: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/preapprovedloan/offer-details-partnership-idfc.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  74,
						Height: 34,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
	}
	switch detailType {
	case palPb.DetailsType_DETAILS_TYPE_PAN:
		if len(pan) != PAN_LENGTH {
			return nil, errors.New("invalid pan length")
		}
		screenOptions.Title = helper.GetText("Enter your PAN details", "#333333", "", commontypes.FontStyle_HEADLINE_2)
		screenOptions.Desc = helper.GetText("This is required to confirm the validity of your PAN", "#8D8D8D", "", commontypes.FontStyle_BODY_3_PARA)
		screenOptions.DetailsSectionList = []*palPb.PlEnterDetailsScreenOptions_DetailsSection{
			{
				LayoutType:  palPb.PlEnterDetailsScreenOptions_DetailsSection_LAYOUT_TYPE_CARD,
				DetailsType: palPb.DetailsType_DETAILS_TYPE_PAN,
				Details: &palPb.PlEnterDetailsScreenOptions_DetailsSection_CardDetails{
					CardDetails: &palPb.PlEnterDetailsScreenOptions_DetailsSection_Card{
						HeaderText: helper.GetText("YOUR PAN CARD", "#333333", "", commontypes.FontStyle_SUBTITLE_3),
						Colour:     "#DEEEF2",
					},
				},
				PreFilledValue: &palPb.PreFilledValue{
					Value:               pan[:6],
					RemainingCharacters: 4,
				},
			},
		}
		screenOptions.ContinueButton = &deeplinkPb.Button{
			Text:        helper.GetText("Next", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   152,
				RightPadding:  152,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   20,
				RightMargin:  20,
				BottomMargin: 12,
				TopMargin:    110,
			},
		}
	case palPb.DetailsType_DETAILS_TYPE_DOB:
		screenOptions.Title = helper.GetText("Enter your year of birth", "#333333", "", commontypes.FontStyle_HEADLINE_2)
		screenOptions.Desc = helper.GetText("This is required to verify your details for the loan", "#8D8D8D", "", commontypes.FontStyle_BODY_3_PARA)
		screenOptions.DetailsSectionList = []*palPb.PlEnterDetailsScreenOptions_DetailsSection{
			{
				LayoutType:  palPb.PlEnterDetailsScreenOptions_DetailsSection_LAYOUT_TYPE_PLAIN_TEXT,
				DetailsType: palPb.DetailsType_DETAILS_TYPE_DOB,
				Details: &palPb.PlEnterDetailsScreenOptions_DetailsSection_PlainTextDetails{
					PlainTextDetails: &palPb.PlEnterDetailsScreenOptions_DetailsSection_PlainText{Label: helper.GetText("BIRTH DATE", "#B9B9B9", "", commontypes.FontStyle_SUBTITLE_3)},
				},
				PreFilledValue: &palPb.PreFilledValue{
					Value:               detailValue,
					RemainingCharacters: 4,
				},
			},
		}
		screenOptions.TermInfos = []*deeplinkPb.TermInfo{
			{
				TermText:        helper.GetTextWithHtml(fmt.Sprintf("I consent to share my PAN (<b>%v</b>) and date of birth with IDFC FIRST Bank, and authorize it to obtain my KYC records from CERSAI.", pan), "#333333", commontypes.FontStyle_BODY_XS),
				IsTermClickable: false,
			},
		}
		screenOptions.ContinueButton = &deeplinkPb.Button{
			Text:        helper.GetText("Get KYC details", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   152,
				RightPadding:  152,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   20,
				RightMargin:  20,
				BottomMargin: 12,
				TopMargin:    110,
			},
		}
	case palPb.DetailsType_DETAILS_TYPE_OCCUPATION:
		screenOptions.Title = helper.GetText("Tell us your occupation", "#333333", "", commontypes.FontStyle_SUBTITLE_1)
		screenOptions.Desc = helper.GetText("This is required by our partner regulated entity", "#8D8D8D", "", commontypes.FontStyle_BODY_3_PARA)
		screenOptions.DetailsSectionList = []*palPb.PlEnterDetailsScreenOptions_DetailsSection{
			{
				LayoutType:  palPb.PlEnterDetailsScreenOptions_DetailsSection_LAYOUT_TYPE_BULLET_POINTS,
				DetailsType: palPb.DetailsType_DETAILS_TYPE_OCCUPATION,
				Details: &palPb.PlEnterDetailsScreenOptions_DetailsSection_OccupationList_{
					OccupationList: &palPb.PlEnterDetailsScreenOptions_DetailsSection_OccupationList{
						OccupationList: []*palPb.Occupation{
							{
								Employment:     typesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
								OccupationText: "Salaried",
							},
							{
								Employment:     typesPb.EmploymentType_EMPLOYMENT_TYPE_BUSINESS_OWNER,
								OccupationText: "Self employed",
							},
						},
					},
				},
			},
		}
		screenOptions.ContinueButton = &deeplinkPb.Button{
			Text:        helper.GetText("Confirm", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   152,
				RightPadding:  152,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   20,
				RightMargin:  20,
				BottomMargin: 12,
				TopMargin:    110,
			},
		}
	}

	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_ENTER_DETAILS_SCREEN, screenOptions)
	if err != nil {
		return nil, errors.New("error in getting deeplink v2 for add details screen")
	}
	return dl, nil
}

// nolint: funlen
func (p *Provider) GetReviewScreenDeeplink(lh *palFeEnumsPb.LoanHeader, name *commontypes.Name, pan string, address *types.PostalAddress, ckycId string, dob string, loanReqId string) (*deeplinkPb.Deeplink, error) {
	screenOptions := &palPb.PlReviewDetailsScreenOptions{
		LoanHeader:    lh,
		LoanRequestId: loanReqId,
		Title:         helper.GetText("Review your KYC details", "#313234", "", commontypes.FontStyle_SUBTITLE_1),
		Details: []*deeplinkPb.InfoItemBox{
			// "full name" box in the review screen
			{
				Title: &deeplinkPb.InfoItemV2{
					Title: helper.GetText("Full name", "#333333", "", commontypes.FontStyle_SUBTITLE_3),
				},
				Rows: []*deeplinkPb.InfoItemsRowV2{
					{
						Items: []*deeplinkPb.InfoItemV2{
							{
								Title: helper.GetText(names.ToString(name), "#333333", "", commontypes.FontStyle_SUBTITLE_2),
							},
						},
					},
				},
				BgColor: "",
			},
			// "KYC details" box in the review screen
			{
				Title: &deeplinkPb.InfoItemV2{
					Title: helper.GetText("KYC Details", "#333333", "", commontypes.FontStyle_SUBTITLE_3),
				},
				Rows: []*deeplinkPb.InfoItemsRowV2{
					{
						Items: []*deeplinkPb.InfoItemV2{
							{
								Title: helper.GetText("PAN", "#8D8D8D", "", commontypes.FontStyle_SUBTITLE_2),
								Desc:  helper.GetText(pan, "#333333", "", commontypes.FontStyle_SUBTITLE_2),
							},
						},
					},
					{
						Items: []*deeplinkPb.InfoItemV2{
							{
								Title: helper.GetText("DATE OF BIRTH", "#8D8D8D", "", commontypes.FontStyle_SUBTITLE_2),
								Desc:  helper.GetText(dob, "#333333", "", commontypes.FontStyle_SUBTITLE_2),
							},
						},
					},
					{
						Items: []*deeplinkPb.InfoItemV2{
							{
								Title: helper.GetText("ADDRESS", "#8D8D8D", "", commontypes.FontStyle_SUBTITLE_2),
								Desc:  helper.GetText(addresshelper.ConvertPostalAddressToString(address.GetBeAddress()), "#333333", "", commontypes.FontStyle_SUBTITLE_2),
							},
						},
					},
					{
						Items: []*deeplinkPb.InfoItemV2{
							{
								Title: helper.GetText("CKYC ID", "#8D8D8D", "", commontypes.FontStyle_SUBTITLE_2),
								Desc:  helper.GetText(ckycId, "#8D8D8D", "", commontypes.FontStyle_SUBTITLE_2),
							},
						},
					},
				},
				BgColor: "",
			},
		},
		PartnershipUrl: "https://epifi-icons.pointz.in/preapprovedloan/offer-details-partnership-idfc.png",
		Button: &deeplinkPb.Button{
			Text:        helper.GetText("Confirm", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   152,
				RightPadding:  152,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   20,
				RightMargin:  20,
				BottomMargin: 12,
				TopMargin:    110,
			},
		},
		TermInfos: []*deeplinkPb.TermInfo{
			{
				TermText:        helper.GetText("I confirm that above mentioned address is my current address", "#333333", "", commontypes.FontStyle_BODY_XS),
				IsTermClickable: false,
			},
		},
		PartnershipLogo: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/preapprovedloan/offer-details-partnership-idfc.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  74,
						Height: 34,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
	}
	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_REVIEW_DETAILS_SCREEN, screenOptions)
	if err != nil {
		return nil, errors.New("error in getting deeplink v2 for review screen")
	}
	return dl, nil
}

func (p *Provider) GetLoanApplicationStatusScreenDeepLink(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanReq *preapprovedloan.LoanRequest) (*deeplinkPb.Deeplink, error) {
	emiAmountString := money2.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), 0, true)
	disbursalAmountString := money2.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDisbursalAmount(), 0, true)
	nextEmiDateString, _ := getNextEmiDateAndEmiStartMonth()
	dl, err := p.Provider.GetLoanApplicationStatusScreenDeepLink(ctx, lh, loanReq)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting loan application status screen deeplink")
	}
	emiInfoV2 := dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetEmiInfoV2()
	if emiInfoV2 != nil {
		dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetEmiInfoV2().Desc = helper.GetText(fmt.Sprintf("%v • %v", nextEmiDateString, emiAmountString), "#8D8D8D", "#D1DAF1", commontypes.FontStyle_SUBTITLE_3)
	}
	dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetLoanStatus().Desc = fmt.Sprintf("In a few minutes, %v will arrive in your chosen bank account. The loan document will reach your inbox.", disbursalAmountString)
	dl.GetPreApprovedLoanApplicationStatusScreenOptions().GetLoanStatusV2().GetDesc().DisplayValue = &commontypes.Text_PlainString{PlainString: fmt.Sprintf("In a few minutes, %v will arrive in your chosen bank account. The loan document will reach your inbox.", disbursalAmountString)}
	return dl, nil
}

func (p *Provider) GetApplicationStatusPollScreenWithCustomMsgDeepLink(lh *palFeEnumsPb.LoanHeader, requestId string, pollingText string, fontColor string, iconUrl string) (*deeplinkPb.Deeplink, error) {
	dl, err := p.Provider.GetApplicationStatusPollScreenWithCustomMsgDeepLink(lh, requestId, pollingText, fontColor, iconUrl)
	return dl, err
}

func (p *Provider) GetLoanApplicationConfirmationViaOtpScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_CONFIRMATION_VIA_OTP_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
			PreApprovedLoanApplicationConfirmationViaOtpScreenOptions: &deeplinkPb.PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
				LoanRequestId:       loanRequestId,
				LoanHeader:          lh,
				LoanStepExecutionId: lseId,
			},
		},
	}
}

func getNextEmiDateAndEmiStartMonth() (string, string) {
	timeNowInIST := time.Now().In(datetime.IST)
	delayByMonth := 0
	if timeNowInIST.Day() > 18 {
		delayByMonth += 1
	}
	nextEmiDate := datetime.TimeToDateInLoc(datetime.StartOfMonth(time.Now().AddDate(0, 1+delayByMonth, 0).In(datetime.IST)).AddDate(0, 0, 2), datetime.IST)
	return datetime.DateToString(nextEmiDate, "02 Jan 2006", datetime.IST), cases.Title(language.Und).String(time.Now().In(datetime.IST).AddDate(0, 1+delayByMonth, 0).Month().String())
}

func (p *Provider) GetLoanActivityStatusPollScreenDeepLink(lh *palFeEnumsPb.LoanHeader, refId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ACTIVITY_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanActivityStatusPollScreenOptions{
			PreApprovedLoanActivityStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanActivityStatusPollScreenOptions{
				RetryAttemptNumber: 0,
				RetryDelay:         1000,
				RefId:              refId,
				LoanHeader:         lh,
			},
		},
	}
}

func (a *Provider) GetVkycWebview(lh *palFeEnumsPb.LoanHeader, loanRequestId string, entryUrl string, backOff uint32, platform commontypes.Platform) (*deeplinkPb.Deeplink, error) {
	shouldOpenInExternalTab := a.shouldVkycOpenInExternalTab(platform)
	logger.DebugNoCtx("value of vkyc shouldOpenInExternalTab", zap.Any("shouldOpenInExternalTab", shouldOpenInExternalTab))

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN, &palPb.LoansWebviewWithStatusPollScreen{
		LoanHeader:               lh,
		EntryUrl:                 entryUrl,
		RetryBackoff:             backOff,
		PageTitle:                commontypes.GetPlainStringText("Video KYC v2").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
		LoanRequestId:            loanRequestId,
		AnalyticsScreenName:      analytics.AnalyticsScreenName_VKYC_INFO, // change this
		ExitDelayInMilliseconds:  2000,
		CameraPermissionRequired: true,
		ShouldOpenInExternalTab:  shouldOpenInExternalTab,
	})
}

func (p *Provider) shouldVkycOpenInExternalTab(platform commontypes.Platform) bool {
	switch platform {
	case commontypes.Platform_ANDROID:
		return p.GetDeeplinkConfig().OpenIdfcVkycUrlViaExternalForAndroid()
	case commontypes.Platform_IOS:
		return p.GetDeeplinkConfig().OpenIdfcVkycUrlViaExternalForIos()
	default:
		return true
	}
}

func (p *Provider) GetLoansMandateInitiateScreenV2(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansMandateInitiateScreenV2Params) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetLoansMandateInitiateScreenV2(ctx, p.GetLoanHeader(), loanRequestId, lseId, params)
}

func (p *Provider) GetLoansAlternateAccountsScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansAlternateAccountsScreenParams) (*deeplinkPb.Deeplink, error) {
	return p.Provider.GetLoansAlternateAccountsScreen(ctx, p.GetLoanHeader(), loanRequestId, lseId, params)
}

func (p *Provider) GetLoansMandateSetupScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string, loanRequestId string, lseId string, params *provider.LoansMandateSetupScreenParams) (*deeplinkPb.Deeplink, error) {
	screenOption := &palPb.LoansMandateSetupScreenOptions{
		LoanHeader:    lh,
		ScreenName:    analyticsPb.AnalyticsScreenName_PL_MANDATE_SETUP_SCREEN,
		LoanRequestId: loanRequestId,
		LseId:         lseId,
		MandateViewInfo: &palPb.MandateViewInfo{
			MandateViewType: palPb.MandateViewType_MANDATE_VIEW_TYPE_WEBVIEW,
			ViewInfo: &palPb.MandateViewInfo_WebPageInfo_{
				WebPageInfo: &palPb.MandateViewInfo_WebPageInfo{
					ExitUrl:           params.WebViewParams.ExitUrl,
					Base64EncodedHtml: params.WebViewParams.EncodedHtml,
				},
			},
		},
	}
	_ = p.Provider.EnrichMandateSetupScreen(ctx, actorId, screenOption, &baseprovider.EnrichMandateSetupScreenParams{
		IsFiAccount:              params.MandateOverlayDetailsParams.IsFiAccount,
		IsDropOffDlEnrichmentReq: true,
		ExitNowDropOffDl:         p.GetLoanDashboardScreenDeepLink(lh),
	})
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_MANDATE_SETUP_SCREEN, screenOption)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating loans mandate setup screen deeplink")
	}
	return dl, nil
}

func (a *Provider) GetVkycMakerCallSuccessScreen(
	lh *palFeEnumsPb.LoanHeader,
	loanRequestId string,
) (*deeplinkPb.Deeplink, error) {

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
			PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanApplicationStatusPollScreenOptions{
				RetryAttemptNumber: 0,
				RetryDelay:         4_000,  // 4s
				RetryDuration:      30_000, // 30s
				LoanRequestId:      loanRequestId,
				PollingText: &deeplinkPb.InfoItemV2{
					Title: helper.GetText("Verifying your identity", "#000000", "", commontypes.FontStyle_HEADLINE_L),
					SubTitle: helper.GetText("This may take up to 15 minutes.\nOnce done, we'll let you know.",
						"#929599", "", commontypes.FontStyle_BODY_S),
				},
				LoanHeader: lh,
				CentreIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/lending/green-id-card.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  120,
					Height: 120,
				}),
				BackgroundColour: widget.GetBlockBackgroundColour("#FFFFFF"),
				HideLoader:       true,
				BottomCta: &deeplinkPb.Button{
					Text: helper.GetText("Back to Home", "#00B899", "#F6F9FD", commontypes.FontStyle_BUTTON_M),
					Cta: &deeplinkPb.Cta{
						Deeplink:     a.GetLoanLandingInfo(a.GetLoanHeader()),
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
						Type:         deeplinkPb.Cta_DONE,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					},
					Margin: &deeplinkPb.Button_Margin{
						LeftMargin:   15,
						RightMargin:  15,
						BottomMargin: 20,
						TopMargin:    0,
					},
				},
			},
		},
	}, nil
}

func (a *Provider) GetVkycAuditorSuccessScreen(
	ctx context.Context,
	lh *palFeEnumsPb.LoanHeader,
	loanRequestId string,
) (*deeplinkPb.Deeplink, error) {
	pollingDeeplink, err := a.GetLoansApplicationStatusPollDeeplink(ctx, lh, "", loanRequestId, &provider.ApplicationStatusPollDeeplinkParams{})
	if err != nil {
		return nil, errors.Wrap(err, "error while generating loans application status poll screen deeplink")
	}
	screenOptions := &palPb.LoansFailureScreen{
		LoanHeader: a.GetLoanHeader(),
		PageHeader: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/lending/tick-mark-green.png").WithProperties(&commontypes.
				VisualElementProperties{Height: 120, Width: 120}),
			TitleText: commontypes.GetPlainStringText("Video KYC completed").WithFontStyle(commontypes.FontStyle_HEADLINE_L).
				WithFontColor("#000000"),
			SubtitleText: commontypes.GetPlainStringText("You can now continue with your loan application").
				WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#929599"),
		},
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText("Continue to e-Sign", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			Cta: &deeplinkPb.Cta{
				Deeplink:     pollingDeeplink,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_TEXT,
			},
		},
		Components: []*palPb.LoansFailureScreenComponent{
			{
				Component: &palPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/lending/tick-mark-green.png").WithProperties(&commontypes.
								VisualElementProperties{Height: 120, Width: 120}),
							TitleText: commontypes.GetPlainStringText("Video KYC completed").WithFontStyle(commontypes.FontStyle_HEADLINE_L).
								WithFontColor("#000000"),
							SubtitleText: commontypes.GetPlainStringText("You can now continue with your loan application").
								WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#929599"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, screenOptions)
}

func (a *Provider) GetVkycNonRetryableFailureScreen() (*deeplinkPb.Deeplink, error) {
	screenOptions := &palPb.LoansFailureScreen{
		LoanHeader: a.GetLoanHeader(),
		PageHeader: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer.png").WithProperties(&commontypes.VisualElementProperties{Width: 120, Height: 120}),
			TitleText:     commontypes.GetPlainStringText("Couldn't get you a loan offer").WithFontStyle(commontypes.FontStyle_HEADLINE_XL).WithFontColor("#313234"),
			SubtitleText: commontypes.GetPlainStringText("We'll let you know once we have a loan offer for you").
				WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
		},
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText("Back to home", "#00B899", "", commontypes.FontStyle_BUTTON_S),
			Cta: &deeplinkPb.Cta{
				Deeplink:     a.GetLoanLandingInfo(a.GetLoanHeader()),
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_TEXT,
			},
		},
		// TODO(@Shivansh) add the correct analytics screen name
		// AnalyticsScreenName: analytics.AnalyticsScreenName_LAMF_NO_FUNDS_FOUND_FAILURE_SCREEN,
		Components: []*palPb.LoansFailureScreenComponent{
			{
				Component: &palPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer.png").WithProperties(&commontypes.VisualElementProperties{Width: 120, Height: 120}),
							TitleText:     commontypes.GetPlainStringText("Couldn't get you a loan offer").WithFontStyle(commontypes.FontStyle_HEADLINE_XL).WithFontColor("#313234"),
							SubtitleText: commontypes.GetPlainStringText("We'll let you know once we have a loan offer for you").
								WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, screenOptions)
}

// GetVkycFailureScreen returns the deeplink for vkyc failure screen based on the reject reason
// e.g: for network issue cases, we return "Looks like there was an internet issue" screen
func (a *Provider) GetVkycFailureScreen(
	rejectReason string,
	nextAction *deeplinkPb.Deeplink,
	ctaText string,
) (*deeplinkPb.Deeplink, error) {
	failureHandler := a.vkycFailureHandler.GetFailureHandler(rejectReason)
	screenOptions := &palPb.LoansFailureScreen{
		LoanHeader: a.GetLoanHeader(),
		PageHeader: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl(failureHandler.GetIconUrl()).WithProperties(&commontypes.
				VisualElementProperties{Height: 300, Width: 412}),
			TitleText: commontypes.GetPlainStringText(failureHandler.GetTitle()).WithFontStyle(commontypes.FontStyle_HEADLINE_XL).
				WithFontColor("#313234"),
			SubtitleText: commontypes.GetPlainStringText(failureHandler.GetDescription()).
				WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
		},
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText(ctaText, "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			Cta: &deeplinkPb.Cta{
				Deeplink:     nextAction,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_TEXT,
			},
		},
		Components: []*palPb.LoansFailureScreenComponent{
			{
				Component: &palPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement: commontypes.GetVisualElementImageFromUrl(failureHandler.GetIconUrl()).WithProperties(&commontypes.
								VisualElementProperties{Height: 300, Width: 412}),
							TitleText: commontypes.GetPlainStringText(failureHandler.GetTitle()).WithFontStyle(commontypes.FontStyle_HEADLINE_XL).
								WithFontColor("#313234"),
							SubtitleText: commontypes.GetPlainStringText(failureHandler.GetDescription()).
								WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, screenOptions)
}

// GetVkycInstructionScreen returns the deeplink for vkyc instruction screen (currently used in IDFC VKYC flow)
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=11980-39607&mode=design&t=TxD3E39PF03D9EHq-4
func (p *Provider) GetVkycInstructionScreen(
	nextAction *deeplinkPb.Deeplink,
) (*deeplinkPb.Deeplink, error) {

	screenOption := &vkycScreenOptionsPb.VkycInstructionsV2ScreenOptions{
		Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/lending/man-selfie-sitting.png").
			WithProperties(&commontypes.VisualElementProperties{Width: 280, Height: 280}),
		Title: commontypes.GetTextFromStringFontColourFontStyle("Complete video KYC to get a loan. It's a mandatory step as per RBI guidelines", "#313234", commontypes.FontStyle_SUBTITLE_1),
		Ctas: []*deeplinkPb.Cta{
			{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Continue",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Deeplink:     nextAction,
			},
		},
		BgColor: widget.GetBlockBackgroundColour("#FFFFFF"),
		Blocks: []*uiPb.IconTextComponent{
			uiPb.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/lending/video-icon-circle.png", 48, 48).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
					"An agent will verify your identity over the video call", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)).
				WithLeftImagePadding(10),
			uiPb.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/lending/bulb-icon-circle.png", 48, 48).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Sit in a well-lit and quiet place with stable connection", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)).
				WithLeftImagePadding(10),
		},
	}
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_VKYC_INSTRUCTIONS_V2, screenOption)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating vkyc instructions screen deeplink")
	}
	return dl, nil
}

// GetVkycOverlayScreen returns the deeplink for vkyc overlay screen (currently used in IDFC VKYC flow)
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=11980-40195&mode=design&t=TxD3E39PF03D9EHq-4
func (p *Provider) GetVkycOverlayScreen(
	vkycDeeplink *deeplinkPb.Deeplink,
) (*deeplinkPb.Deeplink, error) {
	screenOption := &vkycScreenOptionsPb.VkycInstructionsOverlayScreenOptions{
		Title: commontypes.GetTextFromStringFontColourFontStyle("Before we begin", "#929599",
			commontypes.FontStyle_HEADLINE_L),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle("Keep your physical\nPAN card ready", "#F6F9FD",
			commontypes.FontStyle_HEADLINE_XL),
		OverlayBackground: widget.GetBlockBackgroundColour("#18191BE6"),
		OverlayPageBlocks: []*vkycScreenOptionsPb.OverlayPageBlock{
			{
				Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/lending/pan-card-holding-white-bg.png").WithImageType(commontypes.ImageType_PNG).
					WithProperties(&commontypes.VisualElementProperties{Width: 232, Height: 268}),
				Ctas: []*deeplinkPb.Cta{
					{
						Type:         deeplinkPb.Cta_CUSTOM,
						Text:         "Yes, it’s ready",
						Deeplink:     vkycDeeplink,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					},
				},
				VisibleAfterSeconds: 2,
				BgColor:             widget.GetBlockBackgroundColour("#FFFFFF"),
			},
		},
	}
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_VKYC_INSTRUCTIONS_OVERLAY, screenOption)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating vkyc instructions overlay screen deeplink")
	}
	return dl, nil
}

func (p *Provider) GetVkyTimedLoaderScreen(
	nextAction *deeplinkPb.Deeplink,
) (*deeplinkPb.Deeplink, error) {
	screenOption := &palPb.LoansTimedLoaderScreen{
		BgColor: widget.GetBlockBackgroundColour("#18191B"),
		Title: commontypes.GetTextFromStringFontColourFontStyle("Redirecting to IDFC First Bank for your video KYC", "#F6F9FD",
			commontypes.FontStyle_HEADLINE_L),
		HideLoader:             false,
		DurationInMilliseconds: 2000,
		Deeplink:               nextAction,
		AnalyticsScreenName:    analytics.AnalyticsScreenName_LOANS_TIMED_LOADER_SCREEN,
	}
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_TIMED_LOADER_SCREEN, screenOption)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating vkyc timed loader screen deeplink")
	}
	return dl, nil
}

// GetVkycDlWhichCallsFe returns a deeplink which helps in redirection to the FE GetVkycDeeplink rpc
func (a *Provider) GetVkycRedirectionDl(lh *palFeEnumsPb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_VKYC_REDIRECTION_SCREEN, &palPb.LoansVkycRedirectionScreenOptions{
		LoanHeader:    lh,
		LoanRequestId: loanRequestId,
	})
}

func (a *Provider) GetVkycDeeplinkWithWebview(loanRequestId string, vkycUrl string, platform commontypes.Platform) (*deeplinkPb.Deeplink, error) {
	vkycWebviewDeeplink, err := a.GetVkycWebview(
		a.GetLoanHeader(),
		loanRequestId,
		vkycUrl,
		5000,
		platform,
	)
	if err != nil {
		return nil, fmt.Errorf("error getting deeplink for vkyc webview screen, err : %v", err.Error())
	}

	vkycTimedLoaderDeeplink, err := a.GetVkyTimedLoaderScreen(vkycWebviewDeeplink)
	if err != nil {
		return nil, fmt.Errorf("error getting deeplink for vkyc timed loader screen, err : %v", err.Error())
	}

	return vkycTimedLoaderDeeplink, nil
}
