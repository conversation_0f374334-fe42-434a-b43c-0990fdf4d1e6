// nolint:goimports
package preapprovedloan

import (
	"context"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
)

// GetLoanUserStatusV2 - This rpc will return the user status of the Loan user.
func (s *Service) GetLoanUserStatusV2(ctx context.Context, req *palPb.GetLoanUserStatusV2Request) (*palPb.GetLoanUserStatusV2Response, error) {
	// landing data tells the current user status what user is eligible for
	dataExistenceRes := s.dataExistenceManager.GetOrRefreshLoanDataExistenceCache(ctx, req.GetActorId())
	landingRes, reErr := s.landingProvider.GetLandingPageDataForActor(ctx, &landing_provider.GetLandingPageDataForActorRequest{
		ActorId:                    req.GetActorId(),
		OwnershipFilterMap:         dataExistenceRes.GetDataExistenceMap(),
		OwnershipFilterMapForLoecs: dataExistenceRes.GetLoecDataExistenceMap(),
	})
	if reErr != nil {
		logger.Error(ctx, "unable to get details from landing provider", zap.Error(reErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.GetLoanUserStatusV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Take out all the loan requests from the DB
	loanRequests, lrErr := s.getMultiDBLoanRequests(ctx, &SegregatedLoanRequestsRequest{
		actorId:        req.GetActorId(),
		updatedAtAfter: req.GetUpdatedAtAfter(),
	})
	if lrErr != nil {
		logger.Error(ctx, "unable to get loan requests", zap.Error(lrErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.GetLoanUserStatusV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// latest loan request of eligibility and application
	var latestApplicationLoanRequest, latestEligibilityLoanRequest *palPb.LoanRequest
	if len(loanRequests.GetEligibilityLoanRequests()) > 0 {
		latestEligibilityLoanRequest = loanRequests.GetEligibilityLoanRequests()[0]
	}
	if len(loanRequests.GetApplicationLoanRequests()) > 0 {
		latestApplicationLoanRequest = loanRequests.GetApplicationLoanRequests()[0]
	}

	// If user is having loan account active then return the status
	// Todo: Parallel Loans Support (Ticket - 97386)
	if landingRes.ActiveLoanAccount != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ACTIVE_LOAN,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
				LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
				LatestLoanDisbursedTimestamp:       landingRes.ActiveLoanAccount.GetCreatedAt(),
			},
		}, nil
	}

	// If user is having active application loan request then return the status
	if loanRequests.GetActiveApplicationLoanRequest() != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ACTIVE_APPLICATION,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
				LatestApplicationReqStartTimestamp: loanRequests.GetActiveApplicationLoanRequest().GetCreatedAt(),
			},
		}, nil
	}

	// If user is having active eligibility loan request then return the status
	if loanRequests.GetActiveEligibilityLoanRequest() != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ACTIVE_ELIGIBILITY,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: loanRequests.GetActiveEligibilityLoanRequest().GetCreatedAt(),
				LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
			},
		}, nil
	}

	userStatus := palPb.UserStatus_USER_STATUS_REJECTED
	for _, lo := range landingRes.LoanOptions {
		if lo.GetLoanOffer() != nil {
			userStatus = palPb.UserStatus_USER_STATUS_OFFER_AVAILABLE
			break
		}
		if lo.GetEligibilityHeader() != nil {
			userStatus = palPb.UserStatus_USER_STATUS_ACTIVE_ELIGIBILITY
			break
		}
	}
	var rejectionTs *timestamp.Timestamp
	if userStatus == palPb.UserStatus_USER_STATUS_REJECTED {
		rejectionTs = loanRequests.GetLatestFailedLoanRequest().GetCompletedAt()
	}

	return &palPb.GetLoanUserStatusV2Response{
		Status:     rpc.StatusOk(),
		UserStatus: userStatus,
		LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
			LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
			LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
			LatestRejectionTimestamp:           rejectionTs,
		},
	}, nil
}
