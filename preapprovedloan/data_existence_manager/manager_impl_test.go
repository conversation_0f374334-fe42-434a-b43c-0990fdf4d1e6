// nolint: goconst
package data_existence_manager

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	cacheMocks "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epificontext"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

func TestManagerImpl_GetLoanDataExistenceForActorIfExists(t *testing.T) {
	t.Parallel()
	type fields struct {
		cache           *cacheMocks.MockCacheStorage
		loanAccountsDao *daoMocks.MockLoanAccountsDao
		loanRequestsDao *daoMocks.MockLoanRequestsDao
		loanOffersDao   *daoMocks.MockLoanOffersDao
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    *DataExistenceDetails
		wantErr bool
		prepare func(*fields, *args)
	}{
		{
			name: "should return nil if cache does not have the data",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want:    nil,
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.cache.EXPECT().HashGetAll(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, key string) (map[string]string, error) {
					if key == "loan_data_existence:actor-id-1" || key == "loan_loec_data_existence:actor-id-1" {
						return nil, nil
					}
					return nil, errors.New("error")
				}).Times(2)
			},
		},
		{
			name: "should return error if cache returns error",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want:    nil,
			wantErr: true,
			prepare: func(f *fields, a *args) {
				f.cache.EXPECT().HashGetAll(gomock.Any(), "loan_data_existence:actor-id-1").Return(nil,
					errors.New("error")).Times(1)
			},
		},
		{
			name: "should return the data from cache",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want: &DataExistenceDetails{
				DataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:  true,
					commontypes.Ownership_LIQUILOANS_PL: false,
					commontypes.Ownership_IDFC_PL:       false,
					commontypes.Ownership_FIFTYFIN_LAMF: true,
					commontypes.Ownership_MONEYVIEW_PL:  true,
				},
			},
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.cache.EXPECT().HashGetAll(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, key string) (map[string]string, error) {
					if key == "loan_data_existence:actor-id-1" {
						return map[string]string{
							"2":  "t",
							"5":  "f",
							"7":  "f",
							"8":  "t",
							"10": "t",
						}, nil
					}
					return nil, nil
				}).Times(2)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			cache := cacheMocks.NewMockCacheStorage(ctr)
			loanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
			loanRequestDao := daoMocks.NewMockLoanRequestsDao(ctr)
			loanOfferDao := daoMocks.NewMockLoanOffersDao(ctr)

			f := fields{
				cache:           cache,
				loanAccountsDao: loanAccountDao,
				loanRequestsDao: loanRequestDao,
				loanOffersDao:   loanOfferDao,
			}

			if tt.prepare != nil {
				tt.prepare(&f, &tt.args)
			}

			m := &ManagerImpl{
				cache:           cache,
				loanAccountsDao: loanAccountDao,
				loanRequestsDao: loanRequestDao,
				loanOffersDao:   loanOfferDao,
			}
			got, err := m.getLoanDataExistenceForActorIfExists(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanDataExistenceForActorIfExists() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanDataExistenceForActorIfExists() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManagerImpl_RefreshLoanDataExistenceForActor(t *testing.T) {
	t.Parallel()
	type fields struct {
		cache           *cacheMocks.MockCacheStorage
		loanAccountsDao *daoMocks.MockLoanAccountsDao
		loanRequestsDao *daoMocks.MockLoanRequestsDao
		loanOffersDao   *daoMocks.MockLoanOffersDao
		loecDao         *daoMocks.MockLoanOfferEligibilityCriteriaDao
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    *DataExistenceDetails
		wantErr bool
		prepare func(*fields, *args)
	}{
		{
			name: "should return error if error in grp executions",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want:    nil,
			wantErr: true,
			prepare: func(f *fields, a *args) {
				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), a.actorId,
					nil).Return(nil, errors.New("error")).AnyTimes()

				f.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), a.actorId, nil, nil,
					time.Duration(0), false).
					Return(nil, errors.New("error")).AnyTimes()
			},
		},
		{
			name: "should set true in cache for all vendors in data existence map and false in loec data existence map",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want: &DataExistenceDetails{
				DataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             true,
					commontypes.Ownership_LIQUILOANS_PL:            true,
					commontypes.Ownership_IDFC_PL:                  true,
					commontypes.Ownership_FIFTYFIN_LAMF:            true,
					commontypes.Ownership_LOANS_ABFL:               true,
					commontypes.Ownership_EPIFI_TECH_V2:            true,
					commontypes.Ownership_MONEYVIEW_PL:             true,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: true,
					commontypes.Ownership_LOANS_LENDEN:             true,
				},
				LoecDataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            false,
					commontypes.Ownership_IDFC_PL:                  false,
					commontypes.Ownership_FIFTYFIN_LAMF:            false,
					commontypes.Ownership_LOANS_ABFL:               false,
					commontypes.Ownership_EPIFI_TECH_V2:            false,
					commontypes.Ownership_MONEYVIEW_PL:             false,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: false,
					commontypes.Ownership_LOANS_LENDEN:             false,
				},
			},
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), a.actorId,
					nil).Return([]*palPb.LoanOffer{{Id: "loan-offer-id-1"}}, nil).Times(len(helper.GetPalOwnerships()))

				f.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), a.actorId, nil, nil, time.Duration(0),
					false).Return(nil, nil).Times(len(helper.GetPalOwnerships()))

				f.cache.EXPECT().HashSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, key string, data map[string]string, expiration time.Duration) error {

					if key == "loan_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "t",
						"5":  "t",
						"7":  "t",
						"8":  "t",
						"12": "t",
						"10": "t",
						"11": "t",
						"14": "t",
						"17": "t",
					}) {
						return nil
					}

					if key == "loan_loec_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "f",
						"7":  "f",
						"8":  "f",
						"12": "f",
						"10": "f",
						"11": "f",
						"14": "f",
						"17": "f",
					}) {
						return nil
					}

					return errors.New("error")
				}).Times(2)
			},
		},
		{
			name: "should set true in cache for all vendors in data existence map and in loec data existence map",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want: &DataExistenceDetails{
				DataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             true,
					commontypes.Ownership_LIQUILOANS_PL:            true,
					commontypes.Ownership_IDFC_PL:                  true,
					commontypes.Ownership_FIFTYFIN_LAMF:            true,
					commontypes.Ownership_LOANS_ABFL:               true,
					commontypes.Ownership_EPIFI_TECH_V2:            true,
					commontypes.Ownership_MONEYVIEW_PL:             true,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: true,
					commontypes.Ownership_LOANS_LENDEN:             true,
				},
				LoecDataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             true,
					commontypes.Ownership_LIQUILOANS_PL:            true,
					commontypes.Ownership_IDFC_PL:                  true,
					commontypes.Ownership_FIFTYFIN_LAMF:            true,
					commontypes.Ownership_LOANS_ABFL:               true,
					commontypes.Ownership_EPIFI_TECH_V2:            true,
					commontypes.Ownership_MONEYVIEW_PL:             true,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: true,
					commontypes.Ownership_LOANS_LENDEN:             true,
				},
			},
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), a.actorId,
					nil).Return([]*palPb.LoanOffer{{Id: "loan-offer-id-1"}}, nil).Times(len(helper.GetPalOwnerships()))

				f.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), a.actorId, nil, nil, time.Duration(0),
					false).Return([]*palPb.LoanOfferEligibilityCriteria{{Id: "loec-id-1"}}, nil).Times(len(helper.GetPalOwnerships()))

				f.cache.EXPECT().HashSet(gomock.Any(), gomock.Any(), map[string]string{
					"2":  "t",
					"5":  "t",
					"7":  "t",
					"8":  "t",
					"12": "t",
					"10": "t",
					"11": "t",
					"14": "t",
					"17": "t",
				}, gomock.Any()).DoAndReturn(func(ctx context.Context, key string, data map[string]string, expiration time.Duration) error {
					if key == "loan_data_existence:actor-id-1" || key == "loan_loec_data_existence:actor-id-1" {
						return nil
					}
					return errors.New("error")
				}).Times(2)
			},
		},
		{
			name: "should set true in cache for specific vendors if offers or loec exists",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want: &DataExistenceDetails{
				DataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            true,
					commontypes.Ownership_IDFC_PL:                  false,
					commontypes.Ownership_FIFTYFIN_LAMF:            true,
					commontypes.Ownership_LOANS_ABFL:               false,
					commontypes.Ownership_EPIFI_TECH_V2:            true,
					commontypes.Ownership_MONEYVIEW_PL:             true,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: true,
					commontypes.Ownership_LOANS_LENDEN:             true,
				},
				LoecDataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            false,
					commontypes.Ownership_IDFC_PL:                  true,
					commontypes.Ownership_FIFTYFIN_LAMF:            false,
					commontypes.Ownership_LOANS_ABFL:               false,
					commontypes.Ownership_EPIFI_TECH_V2:            false,
					commontypes.Ownership_MONEYVIEW_PL:             false,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: false,
					commontypes.Ownership_LOANS_LENDEN:             false,
				},
			},
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), a.actorId,
					nil).DoAndReturn(func(ctx context.Context, actorId string, loanPrograms []palPb.LoanProgram) ([]*palPb.LoanOffer, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					switch ownership {
					case commontypes.Ownership_LOANS_ABFL, commontypes.Ownership_FEDERAL_BANK, commontypes.Ownership_IDFC_PL:
						return nil, nil
					default:
						return []*palPb.LoanOffer{{Id: "loan-offer-id-1"}}, nil
					}
				}).Times(len(helper.GetPalOwnerships()))

				f.loanAccountsDao.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), a.actorId, nil,
					nil).Return(nil, nil).Times(3)

				f.loanRequestsDao.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), a.actorId, nil, nil,
					nil, gomock.Any(), nil).Return(nil, nil).Times(3)

				f.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), a.actorId, nil, nil, time.Duration(0),
					false).DoAndReturn(func(ctx context.Context, actorId string, loanPrograms []palPb.LoanProgram, statuses []palPb.LoanOfferEligibilityCriteriaStatus, duration time.Duration, isExpired bool) ([]*palPb.LoanOfferEligibilityCriteria, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					if ownership == commontypes.Ownership_IDFC_PL {
						return []*palPb.LoanOfferEligibilityCriteria{{Id: "loec-id-1"}}, nil
					}
					return nil, nil
				}).Times(len(helper.GetPalOwnerships()))

				f.cache.EXPECT().HashSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, key string, data map[string]string, expiration time.Duration) error {
					if key == "loan_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "t",
						"7":  "f",
						"8":  "t",
						"12": "f",
						"10": "t",
						"11": "t",
						"14": "t",
						"17": "t",
					}) {
						return nil
					}

					if key == "loan_loec_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "f",
						"7":  "t",
						"8":  "f",
						"12": "f",
						"10": "f",
						"11": "f",
						"14": "f",
						"17": "f",
					}) {
						return nil
					}
					return errors.New("error")
				}).Times(2)
			},
		},
		{
			name: "should set true in cache for specific vendors if offer or account exists",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want: &DataExistenceDetails{
				DataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            true,
					commontypes.Ownership_IDFC_PL:                  true,
					commontypes.Ownership_FIFTYFIN_LAMF:            true,
					commontypes.Ownership_LOANS_ABFL:               false,
					commontypes.Ownership_EPIFI_TECH_V2:            true,
					commontypes.Ownership_MONEYVIEW_PL:             true,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: true,
					commontypes.Ownership_LOANS_LENDEN:             true,
				},
				LoecDataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            false,
					commontypes.Ownership_IDFC_PL:                  false,
					commontypes.Ownership_FIFTYFIN_LAMF:            false,
					commontypes.Ownership_LOANS_ABFL:               false,
					commontypes.Ownership_EPIFI_TECH_V2:            false,
					commontypes.Ownership_MONEYVIEW_PL:             false,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: false,
					commontypes.Ownership_LOANS_LENDEN:             false,
				},
			},
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), a.actorId,
					nil).DoAndReturn(func(ctx context.Context, actorId string, loanPrograms []palPb.LoanProgram) ([]*palPb.LoanOffer, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					switch ownership {
					case commontypes.Ownership_LOANS_ABFL, commontypes.Ownership_FEDERAL_BANK, commontypes.Ownership_IDFC_PL:
						return nil, nil
					default:
						return []*palPb.LoanOffer{{Id: "loan-offer-id-1"}}, nil
					}
				}).Times(len(helper.GetPalOwnerships()))

				f.loanAccountsDao.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), a.actorId, nil,
					nil).DoAndReturn(func(ctx context.Context, actorId string, statuses []palPb.LoanAccountStatus, loanPrograms []palPb.LoanProgram) ([]*palPb.LoanAccount, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					if ownership == commontypes.Ownership_IDFC_PL {
						return []*palPb.LoanAccount{{Id: "loan-account-id-1"}}, nil
					}
					return nil, nil
				}).Times(3)

				f.loanRequestsDao.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), a.actorId, nil, nil,
					nil, gomock.Any(), nil).Return(nil, nil).Times(2)

				f.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), a.actorId, nil, nil, time.Duration(0),
					false).Return(nil, nil).Times(len(helper.GetPalOwnerships()))

				f.cache.EXPECT().HashSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, key string, data map[string]string, expiration time.Duration) error {
					if key == "loan_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "t",
						"7":  "t",
						"8":  "t",
						"12": "f",
						"10": "t",
						"11": "t",
						"14": "t",
						"17": "t",
					}) {
						return nil
					}

					if key == "loan_loec_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "f",
						"7":  "f",
						"8":  "f",
						"12": "f",
						"10": "f",
						"11": "f",
						"14": "f",
						"17": "f",
					}) {
						return nil
					}
					return errors.New("error")
				}).Times(2)

			},
		},
		{
			name: "should set true in cache for specific vendors if offer or account or request exists",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want: &DataExistenceDetails{
				DataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            true,
					commontypes.Ownership_IDFC_PL:                  true,
					commontypes.Ownership_FIFTYFIN_LAMF:            true,
					commontypes.Ownership_LOANS_ABFL:               true,
					commontypes.Ownership_EPIFI_TECH_V2:            true,
					commontypes.Ownership_MONEYVIEW_PL:             true,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: true,
					commontypes.Ownership_LOANS_LENDEN:             true,
				},
				LoecDataExistenceMap: map[commontypes.Ownership]bool{
					commontypes.Ownership_FEDERAL_BANK:             false,
					commontypes.Ownership_LIQUILOANS_PL:            false,
					commontypes.Ownership_IDFC_PL:                  false,
					commontypes.Ownership_FIFTYFIN_LAMF:            false,
					commontypes.Ownership_LOANS_ABFL:               false,
					commontypes.Ownership_EPIFI_TECH_V2:            false,
					commontypes.Ownership_MONEYVIEW_PL:             false,
					commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP: false,
					commontypes.Ownership_LOANS_LENDEN:             false,
				},
			},
			wantErr: false,
			prepare: func(f *fields, a *args) {
				f.loanOffersDao.EXPECT().GetActiveOffersByActorIdAndLoanPrograms(gomock.Any(), a.actorId,
					nil).DoAndReturn(func(ctx context.Context, actorId string, loanPrograms []palPb.LoanProgram) ([]*palPb.LoanOffer, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					switch ownership {
					case commontypes.Ownership_LOANS_ABFL, commontypes.Ownership_FEDERAL_BANK, commontypes.Ownership_IDFC_PL:
						return nil, nil
					default:
						return []*palPb.LoanOffer{{Id: "loan-offer-id-1"}}, nil
					}
				}).Times(len(helper.GetPalOwnerships()))

				f.loanAccountsDao.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), a.actorId, nil,
					nil).DoAndReturn(func(ctx context.Context, actorId string, statuses []palPb.LoanAccountStatus, loanPrograms []palPb.LoanProgram) ([]*palPb.LoanAccount, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					if ownership == commontypes.Ownership_IDFC_PL {
						return []*palPb.LoanAccount{{Id: "loan-account-id-1"}}, nil
					}
					return nil, nil
				}).Times(3)

				f.loanRequestsDao.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), a.actorId, nil, nil,
					nil, gomock.Any(), nil).DoAndReturn(func(ctx context.Context, actorId string,
					types []palPb.LoanRequestType, statuses []palPb.LoanRequestStatus,
					loanPrograms []palPb.LoanProgram, duration, duration2 *time.Duration) ([]*palPb.LoanRequest, error) {
					ownership := epificontext.OwnershipFromContext(ctx)
					if ownership == commontypes.Ownership_LOANS_ABFL {
						return []*palPb.LoanRequest{{Id: "loan-request-id-1"}}, nil
					}
					return nil, nil
				}).Times(2)

				f.loecDao.EXPECT().GetByActorIdLoanProgramsAndStatuses(gomock.Any(), a.actorId, nil, nil, time.Duration(0),
					false).Return(nil, nil).Times(len(helper.GetPalOwnerships()))

				f.cache.EXPECT().HashSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, key string, data map[string]string, expiration time.Duration) error {
					if key == "loan_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "t",
						"7":  "t",
						"8":  "t",
						"12": "t",
						"10": "t",
						"11": "t",
						"14": "t",
						"17": "t",
					}) {
						return nil
					}

					if key == "loan_loec_data_existence:actor-id-1" && reflect.DeepEqual(data, map[string]string{
						"2":  "f",
						"5":  "f",
						"7":  "f",
						"8":  "f",
						"12": "f",
						"10": "f",
						"11": "f",
						"14": "f",
						"17": "f",
					}) {
						return nil
					}
					return errors.New("error")
				}).Times(2)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			cache := cacheMocks.NewMockCacheStorage(ctr)
			loanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
			loanRequestDao := daoMocks.NewMockLoanRequestsDao(ctr)
			loanOfferDao := daoMocks.NewMockLoanOffersDao(ctr)
			loecDao := daoMocks.NewMockLoanOfferEligibilityCriteriaDao(ctr)

			f := fields{
				cache:           cache,
				loanAccountsDao: loanAccountDao,
				loanRequestsDao: loanRequestDao,
				loanOffersDao:   loanOfferDao,
				loecDao:         loecDao,
			}

			if tt.prepare != nil {
				tt.prepare(&f, &tt.args)
			}

			m := &ManagerImpl{
				cache:           cache,
				loanAccountsDao: loanAccountDao,
				loanRequestsDao: loanRequestDao,
				loanOffersDao:   loanOfferDao,
				loecDao:         loecDao,
			}

			got, err := m.refreshLoanDataExistenceForActor(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("refreshLoanDataExistenceForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("refreshLoanDataExistenceForActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}
