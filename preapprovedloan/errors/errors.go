package errors

import "errors"

var (
	ErrInsufficientCollateral           = errors.New("loan amount issued against collateral is less than min loan amount required for given program and vendor")
	ErrBankNotSupported                 = errors.New("bank is not supported on EPIFI end")
	ErrFailedToParsePhoneNumber         = errors.New("failed to parse phone number")
	ErrOtpExpired                       = errors.New("otp has expired")
	ErrExpectedDelayInLmsUpdateAtLender = errors.New("expected delay in lms update at vendor")
)
