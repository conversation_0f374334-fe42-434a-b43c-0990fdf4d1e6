package providers

import (
	"context"
	"fmt"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers/postproc"
)

type NonFiCore struct {
	userGroupClient userGroupPb.GroupClient
	userClient      userPb.UsersClient
	Conf            *genconf.VendorProgramLevelFeature
	postProcessor   postproc.IPostProcessor
}

func NewNonFiCore(userGroupClient userGroupPb.GroupClient, userClient userPb.UsersClient, conf *genconf.VendorProgramLevelFeature, postProcessor postproc.IPostProcessor) *NonFiCore {
	return &NonFiCore{
		userGroupClient: userGroupClient,
		userClient:      userClient,
		Conf:            conf,
		postProcessor:   postProcessor,
	}
}

var _ IPriorityProvider = &NonFiCore{}

func (r *NonFiCore) GetLoanHeaderPrioritisation(ctx context.Context, loanHeaderPrioritisationRequest GetLoanHeaderPrioritisationRequest) ([]*palPb.LoanHeader, error) {
	prio := []*palPb.LoanHeader{
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
			Vendor:      palPb.Vendor_FEDERAL,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_MONEYVIEW,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_LENDEN,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_ABFL,
		},
	}
	if loanHeaderPrioritisationRequest.ShouldCheckAvailability == false {
		return prio, nil
	}
	conf := r.Conf
	usergroups, err := helper.GetUserGroupDetails(ctx, loanHeaderPrioritisationRequest.ActorId, r.userGroupClient, r.userClient)
	if err != nil {
		return nil, fmt.Errorf("failed to get groups to which user belong, err: %w", err)
	}
	var filteredPrio []*palPb.LoanHeader
	for _, lh := range prio {
		constraint := conf.NonFiCoreVendorProgramActiveMap().Get(helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram()))
		if constraint == nil {
			continue
		}
		allowedUserGroups := constraint.AllowedGroups()
		if constraint.IsAllowed() && (len(allowedUserGroups.ToStringArray()) == 0 || helper.IsUserAllowed(usergroups, allowedUserGroups)) {
			filteredPrio = append(filteredPrio, lh)
		}
	}

	// post process the filtered priority loan headers
	filteredPrio = r.postProcessor.PostProcess(ctx, false, filteredPrio)

	return filteredPrio, nil
}
