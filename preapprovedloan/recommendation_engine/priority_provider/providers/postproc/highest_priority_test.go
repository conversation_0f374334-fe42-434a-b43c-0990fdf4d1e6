package postproc

import (
	"context"
	"reflect"
	"testing"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

func TestHighestPriorityPostProcessor_PostProcess(t *testing.T) {
	t.<PERSON>()
	type args struct {
		ctx         context.Context
		isForFiCore bool
		loanHeaders []*palPb.LoanHeader
	}
	tests := []struct {
		name string
		args args
		want []*palPb.LoanHeader
	}{
		{
			name: "should move the abfl loan header to the second position of the list",
			args: args{
				ctx:         context.Background(),
				isForFiCore: false,
				loanHeaders: []*palPb.LoanHeader{
					{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
						Vendor:      palPb.Vendor_MONEYVIEW,
					},
					{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
						Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
					},
					{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
						Vendor:      palPb.Vendor_ABFL,
					},
				},
			},
			want: []*palPb.LoanHeader{
				{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Vendor:      palPb.Vendor_MONEYVIEW,
				},
				{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Vendor:      palPb.Vendor_ABFL,
				},
				{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &HighestPriorityPostProcessor{
				BasePostProcessor:  NewBasePostProcessor(),
				lopeOverrideConfig: gconf.LopeOverrideConfig(),
			}
			if got := h.PostProcess(tt.args.ctx, tt.args.isForFiCore, tt.args.loanHeaders); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PostProcess() = %v, want %v", got, tt.want)
			}
		})
	}
}
