package providers

import (
	"context"
	"fmt"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers/postproc"
)

type Common struct {
	userGroupClient userGroupPb.GroupClient
	userClient      userPb.UsersClient
	Conf            *genconf.VendorProgramLevelFeature
	postProcessor   postproc.IPostProcessor
}

func NewCommon(userGroupClient userGroupPb.GroupClient, userClient userPb.UsersClient, conf *genconf.VendorProgramLevelFeature, postProcessor postproc.IPostProcessor) *Common {
	return &Common{
		userGroupClient: userGroupClient,
		userClient:      userClient,
		Conf:            conf,
		postProcessor:   postProcessor,
	}
}

var _ IPriorityProvider = &Common{}

func (r *Common) GetLoanHeaderPrioritisation(ctx context.Context, loanHeaderPrioritisationRequest GetLoanHeaderPrioritisationRequest) ([]*palPb.LoanHeader, error) {
	prio := []*palPb.LoanHeader{
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_FEDERAL,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_MONEYVIEW,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_FEDERAL,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_IDFC,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_FLDG,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_STPL,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},

		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_FED_REAL_TIME,
			Vendor:      palPb.Vendor_FEDERAL,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL,
			Vendor:      palPb.Vendor_LIQUILOANS,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_LENDEN,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_ABFL,
		},
		{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_ABFL,
		},
	}

	if loanHeaderPrioritisationRequest.ShouldCheckAvailability == false {
		return prio, nil
	}
	conf := r.Conf
	usergroups, err := helper.GetUserGroupDetails(ctx, loanHeaderPrioritisationRequest.ActorId, r.userGroupClient, r.userClient)
	if err != nil {
		return nil, fmt.Errorf("failed to get groups to which user belong, err: %w", err)
	}
	var filteredPrio []*palPb.LoanHeader

	for _, lh := range prio {
		vendorProgramConf := conf.VendorProgramActiveMap().Get(helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram()))

		if vendorProgramConf != nil {
			isAllowed := vendorProgramConf.IsAllowed()
			allowedUserGroups := conf.VendorProgramActiveMap().Get(helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram())).AllowedGroups()
			if isAllowed && ((len(allowedUserGroups.ToStringArray()) == 0) || helper.IsUserAllowed(usergroups, allowedUserGroups)) {
				filteredPrio = append(filteredPrio, lh)
			}
		}
	}

	// post process the filtered priority loan headers
	filteredPrio = r.postProcessor.PostProcess(ctx, true, filteredPrio)

	return filteredPrio, nil
}
