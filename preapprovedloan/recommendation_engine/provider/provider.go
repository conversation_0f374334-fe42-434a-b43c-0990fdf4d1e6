package provider

import (
	"context"

	"github.com/google/wire"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

var (
	WireSet = wire.NewSet(NewLoanOptionRecommender, wire.Bind(new(ILoanRecommendationEngine), new(*LoanOptionRecommender)))
)

// ILoanRecommendationEngine interface provide abstract methods for loans recommendation engine
//
//go:generate mockgen -source=provider.go -destination=mocks/provider.go
type ILoanRecommendationEngine interface {
	// GetRecommendedLoanOption will return a loan offer for a user if available, otherwise
	// based on loec, will check eligibility if applicable for a particular loan program
	GetRecommendedLoanOption(context.Context, *GetRecommendedLoanOptionRequest) (*GetRecommendedLoanOptionResponse, error)
	// GetRankedLoanOptions will return the list of all loan offers for a user in ranked order
	// based on loec, will check eligibility if applicable for a particular loan program
	GetRankedLoanOptions(context.Context, *GetRankedLoanOptionsRequest) (*GetRankedLoanOptionsResponse, error)
	// GetCombinedRankedLoanOptions will return the list of all loan options for a user in ranked order
	// based on loan offer and loec. It checks for all loan offers and their types AND loecs with its data requirements
	GetCombinedRankedLoanOptions(context.Context, *GetCombinedRankedLoanOptionsRequest) (*GetCombinedRankedLoanOptionsResponse, error)
}

type GetRecommendedLoanOptionRequest struct {
	ActorId string
}

type Eligibility struct {
	CheckEligibility bool
	LoanHeader       *palPb.LoanHeader
}

type GetRecommendedLoanOptionResponse struct {
	ActiveOffer *palPb.LoanOffer
	Eligibility *Eligibility
}

type GetRankedLoanOptionsRequest struct {
	ActorId string
}

type GetRankedLoanOptionsResponse struct {
	ActiveOffers []*palPb.LoanOffer
	Eligibility  *Eligibility
}

type GetCombinedRankedLoanOptionsRequest struct {
	ActorId string
}

type GetCombinedRankedLoanOptionsResponse struct {
	LoanOptions []*palPb.LoanOption
}
