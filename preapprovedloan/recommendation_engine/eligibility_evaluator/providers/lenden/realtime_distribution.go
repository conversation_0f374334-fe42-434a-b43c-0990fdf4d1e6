package lenden

//nolint:goimports
import (
	"context"
	"errors"
	"time"

	errorsPkg "github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
)

type RealtimeDistEligibilityProvider struct {
	commonProvider *providers.CommonEligibilityProvider
	loecDao        dao.LoanOfferEligibilityCriteriaDao
}

func NewRealtimeDistEligibilityProvider(loecDao dao.LoanOfferEligibilityCriteriaDao, commonProvider *providers.CommonEligibilityProvider,
) *RealtimeDistEligibilityProvider {
	return &RealtimeDistEligibilityProvider{
		loecDao:        loecDao,
		commonProvider: commonProvider,
	}

}

func (c *RealtimeDistEligibilityProvider) EvaluateLoanEligibility(ctx context.Context, req *providers.EvaluateLoanEligibilityRequest, lh *palPb.LoanHeader) (*providers.EvaluateLoanEligibilityResponse, error) {
	res, err := c.commonProvider.EvaluateLoanEligibility(ctx, req, lh)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error in evaluating the common eligility of the offer")
	}
	if res.IsOfferAvailable {
		return res, nil
	}

	ctx2 := epificontext.WithOwnership(ctx, helper.GetPalOwnership(lh.GetVendor()))
	loecs, loecErr := c.loecDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx2, req.ActorId, []palPb.LoanProgram{lh.GetLoanProgram()}, nil, time.Hour*24*90, true)
	if loecErr != nil && !errors.Is(loecErr, epifierrors.ErrRecordNotFound) {
		return nil, errorsPkg.Wrap(loecErr, "error in getting the LOECs from the Db")
	}

	if len(loecs) > 0 {
		shouldNotCheckEligibility := loecs[0].GetStatus() != palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED

		res.CheckEligibility = &providers.CheckEligibility{
			ShouldCheckEligibility: !shouldNotCheckEligibility,
			LoanHeader:             lh,
		}
	}
	return res, nil
}
