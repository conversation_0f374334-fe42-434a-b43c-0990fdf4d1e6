package steps

import (
	"time"

	"github.com/epifi/be-common/pkg/events"

	"github.com/fatih/structs"

	"github.com/google/uuid"
)

//nolint:gosec
const (
	EventPLWebCreditReportPullInitiatedName = "PLWebCreditReportPullInitiated"
	EventPLWebCreditReportPullCompletedName = "PLWebCreditReportPullCompleted"
	EventPLFiWebBREName                     = "PLFiWebBRE"
	EventPLFiWebPreBREName                  = "PLFiWebPreBRE"
)

// PLWebCreditReportPullInitiated represents the event when credit report pull is initiated
type PLWebCreditReportPullInitiated struct {
	EventId    string
	ActorId    string
	EventType  string
	Timestamp  time.Time
	BureauName string
}

// NewPLWebCreditReportPullInitiated creates a new PLWebCreditReportPullInitiated event
func NewPLWebCreditReportPullInitiated(actorId string) *PLWebCreditReportPullInitiated {
	return &PLWebCreditReportPullInitiated{
		EventId:    uuid.New().String(),
		ActorId:    actorId,
		Timestamp:  time.Now(),
		EventType:  events.EventTrack,
		BureauName: "experian",
	}
}

func (e *PLWebCreditReportPullInitiated) GetEventType() string {
	return e.EventType
}

func (e *PLWebCreditReportPullInitiated) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *PLWebCreditReportPullInitiated) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *PLWebCreditReportPullInitiated) GetEventId() string {
	return e.EventId
}

func (e *PLWebCreditReportPullInitiated) GetUserId() string {
	return e.ActorId
}

func (e *PLWebCreditReportPullInitiated) GetProspectId() string {
	return ""
}

func (e *PLWebCreditReportPullInitiated) GetEventName() string {
	return EventPLWebCreditReportPullInitiatedName
}

// PLWebCreditReportPullCompleted represents the event when credit report pull is completed
type PLWebCreditReportPullCompleted struct {
	EventId    string
	ActorId    string
	EventType  string
	Timestamp  time.Time
	Status     string // found/not_found/failed
	BureauName string
}

// NewPLWebCreditReportPullCompleted creates a new PLWebCreditReportPullCompleted event
func NewPLWebCreditReportPullCompleted(actorId string, status string) *PLWebCreditReportPullCompleted {
	return &PLWebCreditReportPullCompleted{
		EventId:    uuid.New().String(),
		ActorId:    actorId,
		Timestamp:  time.Now(),
		EventType:  events.EventTrack,
		Status:     status,
		BureauName: "experian",
	}
}

func (e *PLWebCreditReportPullCompleted) GetEventType() string {
	return e.EventType
}

func (e *PLWebCreditReportPullCompleted) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *PLWebCreditReportPullCompleted) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *PLWebCreditReportPullCompleted) GetEventId() string {
	return e.EventId
}

func (e *PLWebCreditReportPullCompleted) GetUserId() string {
	return e.ActorId
}

func (e *PLWebCreditReportPullCompleted) GetProspectId() string {
	return ""
}

func (e *PLWebCreditReportPullCompleted) GetEventName() string {
	return EventPLWebCreditReportPullCompletedName
}

// PLFiWebBRE represents the event for BRE (Business Rule Engine) processing
type PLFiWebBRE struct {
	EventId         string
	ActorId         string
	EventType       string
	Timestamp       time.Time
	LoanProgram     string
	Vendor          string
	LendingPrograms string
	OfferValue      string
	OfferState      string // offered/waitlist
	BREStatus       string // success/failure
}

// NewPLFiWebBRE creates a new PLFiWebBRE event
func NewPLFiWebBRE(actorId, lendingPrograms, offerValue, offerState, breStatus string) *PLFiWebBRE {
	return &PLFiWebBRE{
		EventId:         uuid.New().String(),
		ActorId:         actorId,
		Timestamp:       time.Now(),
		EventType:       events.EventTrack,
		LendingPrograms: lendingPrograms,
		OfferValue:      offerValue,
		OfferState:      offerState,
		BREStatus:       breStatus,
	}
}

func (e *PLFiWebBRE) GetEventType() string {
	return e.EventType
}

func (e *PLFiWebBRE) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *PLFiWebBRE) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *PLFiWebBRE) GetEventId() string {
	return e.EventId
}

func (e *PLFiWebBRE) GetUserId() string {
	return e.ActorId
}

func (e *PLFiWebBRE) GetProspectId() string {
	return ""
}

func (e *PLFiWebBRE) GetEventName() string {
	return EventPLFiWebBREName
}

// PLFiWebPreBRE represents the event for Pre BRE (Business Rule Engine) processing
type PLFiWebPreBRE struct {
	EventId      string
	ActorId      string
	EventType    string
	Timestamp    time.Time
	OfferState   string // offered/waitlist
	PreBREStatus string // success/failure
}

// NewPLFiWebPreBRE creates a new PLFiWebPreBRE event
func NewPLFiWebPreBRE(actorId, offerState, preBreStatus string) *PLFiWebPreBRE {
	return &PLFiWebPreBRE{
		EventId:      uuid.New().String(),
		ActorId:      actorId,
		Timestamp:    time.Now(),
		EventType:    events.EventTrack,
		OfferState:   offerState,
		PreBREStatus: preBreStatus,
	}
}

func (e *PLFiWebPreBRE) GetEventType() string {
	return e.EventType
}

func (e *PLFiWebPreBRE) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *PLFiWebPreBRE) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *PLFiWebPreBRE) GetEventId() string {
	return e.EventId
}

func (e *PLFiWebPreBRE) GetUserId() string {
	return e.ActorId
}

func (e *PLFiWebPreBRE) GetProspectId() string {
	return ""
}

func (e *PLFiWebPreBRE) GetEventName() string {
	return EventPLFiWebPreBREName
}
