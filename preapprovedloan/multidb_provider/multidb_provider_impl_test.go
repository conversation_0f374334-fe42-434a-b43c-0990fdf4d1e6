package multidb_provider_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"sort"
	"testing"

	"github.com/google/go-cmp/cmp"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	test "github.com/epifi/be-common/pkg/test/v2"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/multidb_provider"
)

var (
	loanOfferSample1 = &palPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         palPb.Vendor_FEDERAL,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}

	loanOfferSample2 = &palPb.LoanOffer{
		Id:                             "loan-offer-id-3",
		ActorId:                        "actor-3",
		VendorOfferId:                  "vendor-offer-id-4",
		Vendor:                         palPb.Vendor_LIQUILOANS,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-4",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}

	loanOfferSample5 = &palPb.LoanOffer{
		Id:                             "loan-offer-id-5",
		ActorId:                        "actor-5",
		VendorOfferId:                  "vendor-offer-id-5",
		Vendor:                         palPb.Vendor_LIQUILOANS,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-4",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	loanOfferSample6 = &palPb.LoanOffer{
		Id:                             "loan-offer-id-6",
		ActorId:                        "actor-5",
		VendorOfferId:                  "vendor-offer-id-6",
		Vendor:                         palPb.Vendor_LIQUILOANS,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-4",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_FLDG,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}

	loanOfferMv = &palPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         palPb.Vendor_MONEYVIEW,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	loanOfferAbfl = &palPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         palPb.Vendor_ABFL,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	loanOfferLenden = &palPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         palPb.Vendor_LENDEN,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	loanOfferFiftyfin = &palPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         palPb.Vendor_FIFTYFIN,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
)

var (
	loanReqSample1 = &palPb.LoanRequest{
		Id:              "request-id-10",
		ClientReqId:     "cr-id-3",
		ActorId:         "act-10",
		OfferId:         "off-10",
		OrchId:          "orch-10",
		LoanAccountId:   "acc-10",
		VendorRequestId: "req-10",
		Details:         &palPb.LoanRequestDetails{},
		Vendor:          palPb.Vendor_FEDERAL,
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
	loanReqSample2 = &palPb.LoanRequest{
		Id:              "request-id-2",
		ClientReqId:     "cr-id-1",
		ActorId:         "act-2",
		OfferId:         "off-2",
		OrchId:          "orch-2",
		LoanAccountId:   "*********",
		VendorRequestId: "req-2",
		Vendor:          palPb.Vendor_FEDERAL,
		Details:         &palPb.LoanRequestDetails{},
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
	loanReqSampleFiftyFin1 = &palPb.LoanRequest{
		Id:              "request-id-10",
		ClientReqId:     "cr-id-3",
		ActorId:         "act-10",
		OfferId:         "off-10",
		OrchId:          "orch-10",
		LoanAccountId:   "acc-10",
		VendorRequestId: "req-10",
		Details:         &palPb.LoanRequestDetails{},
		Vendor:          palPb.Vendor_FIFTYFIN,
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
	loanReqSampleFiftyFin2 = &palPb.LoanRequest{
		Id:              "request-id-2",
		ClientReqId:     "cr-id-1",
		ActorId:         "act-2",
		OfferId:         "off-2",
		OrchId:          "orch-2",
		LoanAccountId:   "*********",
		VendorRequestId: "req-2",
		Vendor:          palPb.Vendor_FIFTYFIN,
		Details:         &palPb.LoanRequestDetails{},
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
	loanReqSampleMoneyview2 = &palPb.LoanRequest{
		Id:              "request-id-2",
		ClientReqId:     "cr-id-1",
		ActorId:         "act-2",
		OfferId:         "off-2",
		OrchId:          "orch-2",
		LoanAccountId:   "*********",
		VendorRequestId: "req-2",
		Vendor:          palPb.Vendor_MONEYVIEW,
		Details:         &palPb.LoanRequestDetails{},
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
)

var (
	loanAccountSample1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_FEDERAL,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSample2 = &palPb.LoanAccount{
		Id:            "account-id-1",
		ActorId:       "act-1",
		Vendor:        palPb.Vendor_FEDERAL,
		AccountNumber: "acc-1",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleFiftyFin1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_FIFTYFIN,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleMoneyview1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_MONEYVIEW,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleFiftyfin2 = &palPb.LoanAccount{
		Id:            "account-id-1",
		ActorId:       "act-1",
		Vendor:        palPb.Vendor_FIFTYFIN,
		AccountNumber: "acc-1",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleLiquiloans1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_LIQUILOANS,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleAbfl1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_ABFL,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleLenden1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_LENDEN,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanAccountSampleIdfc1 = &palPb.LoanAccount{
		Id:            "account-id-2",
		ActorId:       "act-2",
		Vendor:        palPb.Vendor_IDFC,
		AccountNumber: "acc-2",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-2",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4000,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        3000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
)

var ctxWithFederalAsOwner = epificontext.WithOwnership(context.Background(), commontypes.Ownership_FEDERAL_BANK)
var ctxWithLiquiloansAsOwner = epificontext.WithOwnership(context.Background(), commontypes.Ownership_LIQUILOANS_PL)

func TestMultiDbProviderImpl_CheckAndGetLoanAccountsForActor(t *testing.T) {
	t.Parallel()
	federalDb, _ := daoTestSuite.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_FEDERAL_BANK)

	type args struct {
		ctx context.Context
		req *multidb_provider.CheckAndGetLoanAccountsForActorRequest
	}

	tests := []struct {
		name    string
		args    args
		want    *multidb_provider.CheckAndGetLoanAccountsForActorResponse
		wantErr bool
		err     error
	}{
		{
			name: "context has federal ownership, no db object",
			args: args{
				ctx: ctxWithFederalAsOwner,
				req: &multidb_provider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:         loanAccountSample1.GetActorId(),
					AccountStatuses: []palPb.LoanAccountStatus{},
					LoanPrograms:    []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanAccountsForActorResponse{Accounts: []*palPb.LoanAccount{loanAccountSample1}},
			wantErr: false,
		},
		{
			name: "context has federal db object, no ownership, throw error",
			args: args{
				ctx: gormctxv2.New(context.Background(), federalDb),
				req: &multidb_provider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:      loanAccountSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has federal db object and liquiloans ownership",
			args: args{
				ctx: gormctxv2.New(ctxWithLiquiloansAsOwner, federalDb),
				req: &multidb_provider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:      loanAccountSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has no ownership & no db object, and all DBs have data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:      loanAccountSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{loanAccountSample1.GetLoanProgram()},
				},
			},
			want: &multidb_provider.CheckAndGetLoanAccountsForActorResponse{Accounts: []*palPb.LoanAccount{
				loanAccountSampleAbfl1,
				loanAccountSample1,
				loanAccountSampleFiftyFin1,
				loanAccountSampleIdfc1,
				loanAccountSampleLenden1,
				loanAccountSampleLiquiloans1,
				loanAccountSampleMoneyview1,
			}},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, only one DB has data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:      loanAccountSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{loanAccountSample2.GetLoanProgram(), palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED},
				},
			},
			want: &multidb_provider.CheckAndGetLoanAccountsForActorResponse{
				Accounts: []*palPb.LoanAccount{loanAccountSample2, loanAccountSampleFiftyfin2},
			},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, data does not exist",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanAccountsForActorRequest{
					ActorId:      "random-actor-id",
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_FLDG},
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	provider := getMultiDbProviderWithMockGrpcClient(daoTestSuite.dynConf)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := provider.CheckAndGetLoanAccountsForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAndGetLoanAccountsForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("CheckAndGetLoanAccountsForActor() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil && len(got.Accounts) > 0 {
				if len(tt.want.Accounts) != len(got.Accounts) {
					t.Errorf("CheckAndGetLoanAccountsForActor() got = %v, want %v", got, tt.want)
					return
				}
				sort.Slice(got.Accounts, func(i, j int) bool {
					return fmt.Sprintf("%s%s", got.Accounts[i].Id, got.Accounts[i].Vendor) < fmt.Sprintf("%s%s", got.Accounts[j].Id, got.Accounts[j].Vendor)
				})
				for idx := range got.Accounts {
					gotReq, wantReq := got.Accounts[idx], tt.want.Accounts[idx]
					gotReq.CreatedAt = nil
					gotReq.UpdatedAt = nil
					if diff := cmp.Diff(gotReq, wantReq, protocmp.Transform()); diff != "" {
						t.Errorf("CheckAndGetLoanAccountsForActor() got = %v,\n want %v at \n index = %v \n diff : %v", gotReq, wantReq, idx, diff)
					}
				}
			}
		})
	}
}

func TestMultiDdProviderImpl_CheckAndGetLoanOffer(t *testing.T) {
	t.Parallel()
	plLiquiloansDb, _ := daoTestSuite.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_LIQUILOANS_PL)

	type args struct {
		ctx context.Context
		req *multidb_provider.CheckAndGetLoanOfferForActorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *multidb_provider.CheckAndGetLoanOfferForActorResponse
		wantErr bool
		err     error
	}{
		{
			name: "context has federal ownership, no db object",
			args: args{
				ctx: ctxWithFederalAsOwner,
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOfferForActorResponse{Offer: loanOfferSample1},
			wantErr: false,
		},
		{
			name: "context has liquiloans db object, no ownership, throw error",
			args: args{
				ctx: gormctxv2.New(context.Background(), plLiquiloansDb),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has liquiloans db object and federal ownership",
			args: args{
				ctx: gormctxv2.New(ctxWithFederalAsOwner, plLiquiloansDb),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has no ownership & no db object, and both DBs have data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOfferForActorResponse{Offer: loanOfferSample1},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, only one DB has data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOfferForActorResponse{Offer: loanOfferSample2},
			wantErr: false,
		},
		{
			name: "federal & liquiloans offer exist for the actor, return federal offer",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOfferForActorResponse{Offer: loanOfferSample1},
			wantErr: false,
		},
		{
			name: "liquiloans pal & liquiloans fldg offer exist for the actor, return liquiloans pal offer",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId: loanOfferSample5.GetActorId(),
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOfferForActorResponse{Offer: loanOfferSample6},
			wantErr: false,
		},
		{
			name: "fldg loan offer exists for user, return fldg offer",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample6.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_FLDG},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOfferForActorResponse{Offer: loanOfferSample6},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, data does not exist",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOfferForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)
	provider := getMultiDbProviderWithMockGrpcClient(daoTestSuite.dynConf)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := provider.CheckAndGetLoanOfferForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAndGetLoanOfferForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("CheckAndGetLoanOfferForActor() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil {
				got.Offer.CreatedAt = nil
				got.Offer.UpdatedAt = nil
				got.Offer.ValidTill = nil
				got.Offer.ValidSince = nil

				if tt.want.Offer == nil || !proto.Equal(got.Offer, tt.want.Offer) {
					t.Errorf("CheckAndGetLoanOffer() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestMultiDbProviderImpl_CheckAndGetLoanRequestsForActor(t *testing.T) {
	federalDb, _ := daoTestSuite.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_FEDERAL_BANK)

	type args struct {
		ctx context.Context
		req *multidb_provider.CheckAndGetLoanRequestsForActorRequest
	}

	tests := []struct {
		name    string
		args    args
		want    *multidb_provider.CheckAndGetLoanRequestsForActorResponse
		wantErr bool
		err     error
	}{
		{
			name: "context has federal ownership, no db object",
			args: args{
				ctx: ctxWithFederalAsOwner,
				req: &multidb_provider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      loanReqSample2.GetActorId(),
					Statuses:     []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED},
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanRequestsForActorResponse{Requests: []*palPb.LoanRequest{loanReqSample2}},
			wantErr: false,
		},
		{
			name: "context has federal db object, no ownership, throw error",
			args: args{
				ctx: gormctxv2.New(context.Background(), federalDb),
				req: &multidb_provider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      loanReqSample1.GetActorId(),
					Statuses:     []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED},
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has federal db object and liquiloans ownership",
			args: args{
				ctx: gormctxv2.New(ctxWithLiquiloansAsOwner, federalDb),
				req: &multidb_provider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      loanReqSample1.GetActorId(),
					Statuses:     []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED},
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has no ownership & no db object, and multiple DBs have data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      loanReqSample2.GetActorId(),
					Statuses:     []palPb.LoanRequestStatus{loanReqSample2.GetStatus()},
					LoanPrograms: []palPb.LoanProgram{loanReqSample2.GetLoanProgram()},
				},
			},
			want: &multidb_provider.CheckAndGetLoanRequestsForActorResponse{Requests: []*palPb.LoanRequest{
				loanReqSample2,
				loanReqSample2,
				loanReqSample2,
				loanReqSampleFiftyFin2,
				loanReqSampleMoneyview2,
			}},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, only one DB has data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      loanReqSample1.GetActorId(),
					Statuses:     []palPb.LoanRequestStatus{loanReqSample1.GetStatus()},
					LoanPrograms: []palPb.LoanProgram{loanReqSample1.GetLoanProgram(), palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED},
				},
			},
			want: &multidb_provider.CheckAndGetLoanRequestsForActorResponse{
				Requests: []*palPb.LoanRequest{loanReqSample1, loanReqSampleFiftyFin1},
			},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, data does not exist",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanRequestsForActorRequest{
					ActorId:      "random-actor-id",
					Statuses:     []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED},
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_FLDG},
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)
	provider := getMultiDbProviderWithMockGrpcClient(daoTestSuite.dynConf)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := provider.CheckAndGetLoanRequestsForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAndGetLoanRequestsForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("CheckAndGetLoanRequestsForActor() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil && len(got.Requests) > 0 {
				sort.Slice(got.Requests, func(i, j int) bool {
					return fmt.Sprintf("%s%s", got.Requests[i].Id, got.Requests[i].Vendor) < fmt.Sprintf("%s%s", got.Requests[j].Id, got.Requests[j].Vendor)
				})
				if len(tt.want.Requests) != len(got.Requests) {
					t.Errorf("CheckAndGetLoanRequestsForActor() got = %v, want %v", got, tt.want)
				}
				for idx := range got.Requests {
					gotReq, wantReq := got.Requests[idx], tt.want.Requests[idx]
					gotReq.CreatedAt = nil
					gotReq.UpdatedAt = nil
					if diff := cmp.Diff(gotReq, wantReq, protocmp.Transform()); diff != "" {
						t.Errorf("CheckAndGetLoanRequestsForActor() got = %v,\n want %v at \n index = %v \n diff : %v", gotReq, wantReq, idx, diff)
					}
				}
			}
		})
	}
}

func TestMultiDbProviderImpl_CheckAndGetNonTerminalLoanRequests(t *testing.T) {
	federalDb, _ := daoTestSuite.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_FEDERAL_BANK)

	type args struct {
		ctx context.Context
		req *multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest
	}

	tests := []struct {
		name    string
		args    args
		want    *multidb_provider.CheckAndGetNonTerminalLoanRequestsResponse
		wantErr bool
		err     error
	}{
		{
			name: "context has federal ownership, no db object",
			args: args{
				ctx: ctxWithFederalAsOwner,
				req: &multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId:             loanReqSample2.GetActorId(),
					NonTerminalStatuses: []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED},
					LoanPrograms:        []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetNonTerminalLoanRequestsResponse{Requests: []*palPb.LoanRequest{loanReqSample2}},
			wantErr: false,
		},
		{
			name: "context has federal db object, no ownership, throw error",
			args: args{
				ctx: gormctxv2.New(context.Background(), federalDb),
				req: &multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId:             loanReqSample1.GetActorId(),
					NonTerminalStatuses: []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED},
					LoanPrograms:        []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has federal db object and liquiloans ownership",
			args: args{
				ctx: gormctxv2.New(ctxWithLiquiloansAsOwner, federalDb),
				req: &multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId:             loanReqSample1.GetActorId(),
					NonTerminalStatuses: []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED},
					LoanPrograms:        []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has no ownership & no db object, and multiple DBs have data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId:             loanReqSample2.GetActorId(),
					NonTerminalStatuses: []palPb.LoanRequestStatus{loanReqSample2.GetStatus()},
					LoanPrograms:        []palPb.LoanProgram{loanReqSample2.GetLoanProgram()},
				},
			},
			want: &multidb_provider.CheckAndGetNonTerminalLoanRequestsResponse{Requests: []*palPb.LoanRequest{
				loanReqSample2,
				loanReqSample2,
				loanReqSample2,
				loanReqSampleFiftyFin2,
				loanReqSampleMoneyview2,
			}},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, only one DB has data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId:             loanReqSample1.GetActorId(),
					NonTerminalStatuses: []palPb.LoanRequestStatus{loanReqSample1.GetStatus()},
					LoanPrograms:        []palPb.LoanProgram{loanReqSample1.GetLoanProgram(), palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED},
				},
			},
			want: &multidb_provider.CheckAndGetNonTerminalLoanRequestsResponse{
				Requests: []*palPb.LoanRequest{loanReqSample1, loanReqSampleFiftyFin1},
			},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, data does not exist",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetNonTerminalLoanRequestsRequest{
					ActorId:             "random-actor-id",
					NonTerminalStatuses: []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED},
					LoanPrograms:        []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_FLDG},
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)
	provider := getMultiDbProviderWithMockGrpcClient(daoTestSuite.dynConf)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := provider.CheckAndGetNonTerminalLoanRequests(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAndGetNonTerminalLoanRequests() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("CheckAndGetNonTerminalLoanRequests() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil && len(got.Requests) > 0 {
				sort.Slice(got.Requests, func(i, j int) bool {
					return fmt.Sprintf("%s%s", got.Requests[i].GetId(), got.Requests[i].GetVendor()) < fmt.Sprintf("%s%s", got.Requests[j].GetId(), got.Requests[j].GetVendor())
				})
				if len(tt.want.Requests) != len(got.Requests) {
					t.Errorf("CheckAndGetNonTerminalLoanRequests() got = %v, want %v", got, tt.want)
				}
				for idx := range got.Requests {
					gotReq, wantReq := got.Requests[idx], tt.want.Requests[idx]
					gotReq.CreatedAt = nil
					gotReq.UpdatedAt = nil
					if diff := cmp.Diff(gotReq, wantReq, protocmp.Transform()); diff != "" {
						t.Errorf("CheckAndGetNonTerminalLoanRequests() got = %v,\n want %v at \n index = %v \n diff : %v", gotReq, wantReq, idx, diff)
					}
				}
			}
		})
	}
}

func TestMultiDdProviderImpl_CheckAndGetLoanOffers(t *testing.T) {
	plLiquiloansDb, _ := daoTestSuite.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_LIQUILOANS_PL)

	type args struct {
		ctx context.Context
		req *multidb_provider.CheckAndGetLoanOffersForActorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *multidb_provider.CheckAndGetLoanOffersForActorResponse
		wantErr bool
		err     error
	}{
		{
			name: "context has federal ownership, no db object",
			args: args{
				ctx: ctxWithFederalAsOwner,
				req: &multidb_provider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      loanOfferSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOffersForActorResponse{Offers: []*palPb.LoanOffer{loanOfferSample1}},
			wantErr: false,
		},
		{
			name: "context has liquiloans db object, no ownership, throw error",
			args: args{
				ctx: gormctxv2.New(context.Background(), plLiquiloansDb),
				req: &multidb_provider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY, palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has liquiloans db object and federal ownership",
			args: args{
				ctx: gormctxv2.New(ctxWithFederalAsOwner, plLiquiloansDb),
				req: &multidb_provider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED},
				},
			},
			wantErr: true,
			err:     multidb_provider.ErrMethodCalledInsideTransaction,
		},
		{
			name: "context has no ownership & no db object, and both DBs have data",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      loanOfferSample1.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
				},
			},
			want: &multidb_provider.CheckAndGetLoanOffersForActorResponse{Offers: []*palPb.LoanOffer{
				loanOfferMv, loanOfferAbfl, loanOfferLenden, loanOfferSample1, loanOfferFiftyfin, loanOfferSample1, loanOfferSample1}},
			wantErr: false,
		},
		{
			name: "fldg loan offer exists for user, return fldg offer",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      loanOfferSample6.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_FLDG},
				},
			},
			want:    &multidb_provider.CheckAndGetLoanOffersForActorResponse{Offers: []*palPb.LoanOffer{loanOfferSample6}},
			wantErr: false,
		},
		{
			name: "context has no ownership & no db object, data does not exist",
			args: args{
				ctx: context.Background(),
				req: &multidb_provider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      loanOfferSample2.GetActorId(),
					LoanPrograms: []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)
	provider := getMultiDbProviderWithMockGrpcClient(daoTestSuite.dynConf)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := provider.CheckAndGetLoanOffersForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAndGetLoanOffersForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("CheckAndGetLoanOffersForActor() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != nil && len(got.Offers) > 0 {
				sort.Slice(got.Offers, func(i, j int) bool {
					return fmt.Sprintf("%s%s", got.Offers[i].GetId(), got.Offers[i].GetVendor()) < fmt.Sprintf("%s%s", got.Offers[j].GetId(), got.Offers[j].GetVendor())
				})
				sort.Slice(tt.want.Offers, func(i, j int) bool {
					return fmt.Sprintf("%s%s", tt.want.Offers[i].GetId(), tt.want.Offers[i].GetVendor()) < fmt.Sprintf("%s%s", tt.want.Offers[j].GetId(), tt.want.Offers[j].GetVendor())
				})
				if len(tt.want.Offers) != len(got.Offers) {
					t.Errorf("CheckAndGetLoanOffersForActor() got = %v, want %v", got, tt.want)
				}
				for idx := range got.Offers {
					gotReq, wantReq := got.Offers[idx], tt.want.Offers[idx]
					gotReq.CreatedAt = nil
					gotReq.UpdatedAt = nil
					opts := []cmp.Option{
						protocmp.Transform(),
						protocmp.IgnoreFields(&palPb.LoanOffer{}, "created_at", "updated_at", "valid_since", "valid_till"),
					}
					if diff := cmp.Diff(gotReq, wantReq, opts...); diff != "" {
						t.Errorf("CheckAndGetLoanOffersForActor() got = %v,\n want %v at \n index = %v \n diff : %v", gotReq, wantReq, idx, diff)
					}
				}
			}
		})
	}
}
