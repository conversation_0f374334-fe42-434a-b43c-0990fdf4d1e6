package multidb_provider_test

import (
	"context"
	"flag"
	"os"
	"testing"

	"gorm.io/gorm"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	defaultValueCalculator "github.com/epifi/gamma/preapprovedloan/calculator/defaultvalue"
	calculatorProvidersWrapper "github.com/epifi/gamma/preapprovedloan/calculator/providers/wrapper"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/multidb_provider"
	de "github.com/epifi/gamma/preapprovedloan/multidb_provider/decision_engine"
	"github.com/epifi/gamma/preapprovedloan/test"
)

type DaoTestSuite struct {
	dbNameToDb         map[string]*gorm.DB
	dynConf            *genconf.Config
	dbResourceProvider *storage.DBResourceProvider[*gorm.DB]
}

var (
	affectedTestTables = []string{
		"loan_offers", "collection_allocations", "collection_leads", "loan_requests", "loan_accounts", "loan_installment_info", "loan_step_executions", "loan_activities", "loan_payment_requests", "loan_offer_eligibility_criteria",
		"loan_installment_payout", "loan_applicants", "fetched_assets",
	}
	daoTestSuite DaoTestSuite
)

type mockCalculatorProvider struct {
}

func (p *mockCalculatorProvider) GetDefaultValueCalculator(
	ctx context.Context,
	loanOffer *palPb.LoanOffer,
) calculatorTypes.DefaultValueCalculator {
	return defaultValueCalculator.NewCalculator(ctx, loanOffer)
}

func (p *mockCalculatorProvider) GetCalculator(
	ctx context.Context,
	req *calculatorTypes.Request,
) (calculatorTypes.Calculator, error) {
	return calculatorProvidersWrapper.NewProvider().GetCalculator(ctx, req)
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, dynConf, teardown := test.InitTestServer()
	dbConnProvider, _, dbNameToDb, releaseDbConn := test.InitDbConns(conf, true, true)

	daoTestSuite = DaoTestSuite{
		dbNameToDb:         dbNameToDb,
		dynConf:            dynConf,
		dbResourceProvider: dbConnProvider,
	}

	exitCode := m.Run()

	releaseDbConn()
	teardown()

	os.Exit(exitCode)
}

func getMultiDbProviderWithMockGrpcClient(
	dynConf *genconf.Config,
) multidb_provider.IMultiDbProvider {
	loanAccountsDao := impl.NewCrdbLoanAccountsDao(daoTestSuite.dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	loanOfferDao := impl.NewCrdbLoanOfferDao(daoTestSuite.dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	loanRequestDao := impl.NewCrdbLoanRequestsDao(daoTestSuite.dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	loecDao := impl.NewCrdbLoanOfferEligibilityCriteriaDao(daoTestSuite.dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	provider := multidb_provider.NewMultiDBProvider(de.NewSimpleOffersDecisionEngine(dynConf.Flags()), loanAccountsDao,
		loanOfferDao, loanRequestDao, loecDao)
	return provider
}
