package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palEnums "github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	palDeeplinkHelper "github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

func (p *Processor) WaitForDataCollection(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		userData, userDataErr := p.userDataPrpvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{
			ActorId: lse.GetActorId(),
		})
		if userDataErr != nil {
			lg.Error("failed to fetch user by actor ID", zap.Error(userDataErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID, err: %v", userDataErr))
		}

		lh := &palFeEnumsPb.LoanHeader{
			LoanProgram: helper.GetFeLoanProgramFromBe(req.GetLoanProgram()),
			Vendor:      helper.GetPalFeVendorFromBe(req.GetVendor()),
			EventData: &palFeEnumsPb.EventData{
				ComponentIdentifier: lse.GetStepName().String(),
			},
		}

		var dataCollectionScreen *deeplinkPb.Deeplink
		switch lse.GetStepName() {
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE:
			if userData.GetBestName() != nil && userData.GetGivenDateOfBirth() != nil && userData.GetPan() != "" {
				MarkLoanStepSuccess(lse)
				// note: name is not stored under lse
				if lse.GetDetails().GetApplicantData().GetPan() == "" && lse.GetDetails().GetApplicantData().GetDob() == nil {
					lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
					res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
				}
				return res, nil
			}
			var showFields []palEnums.FieldId
			if userData.GetBestName() == nil {
				showFields = append(showFields, palEnums.FieldId_NAME)
			}
			if userData.GetGivenDateOfBirth() == nil {
				showFields = append(showFields, palEnums.FieldId_DOB)
			}
			if userData.GetPan() == "" {
				showFields = append(showFields, palEnums.FieldId_PAN)
			}
			dataCollectionScreen = palDeeplinkHelper.GetPanDetailsDeeplink(lh, lse.GetRefId(), showFields)
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE:
			if userData.GetGivenGender() != typesPb.Gender_GENDER_UNSPECIFIED && userData.GetMaritalStatus() != typesPb.MaritalStatus_UNSPECIFIED {
				MarkLoanStepSuccess(lse)
				// note: gender is not stored in lse
				if lse.GetDetails().GetApplicantData().GetMaritalStatus() == "" {
					lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
					res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
				}
				return res, nil
			}

			var showFields []palEnums.FieldId
			if userData.GetGivenGender() == typesPb.Gender_GENDER_UNSPECIFIED {
				showFields = append(showFields, palEnums.FieldId_GENDER)
			}
			if userData.GetMaritalStatus() == typesPb.MaritalStatus_UNSPECIFIED {
				showFields = append(showFields, palEnums.FieldId_MARITAL_STATUS)
			}
			dataCollectionScreen = palDeeplinkHelper.GetOtherDetailsDeeplink(lh, lse.GetRefId(), showFields)
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE:
			if lse.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails().GetPostalCode() != "" {
				MarkLoanStepSuccess(lse)
				return res, nil
			}
			dataCollectionScreen = palDeeplinkHelper.GetAddressDetailsDeeplink(lh, lse.GetRefId())
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE:
			if lse.GetDetails().GetApplicantData().GetPurposeOfLoan() != "" || lse.GetDetails().GetApplicantData().GetDesiredLoanAmount() != nil {
				MarkLoanStepSuccess(lse)
				return res, nil
			}
			dataCollectionScreen = palDeeplinkHelper.GetLoanRequirementDeeplink(lh, lse.GetRefId())
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT:
			if lse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetOccupation() != typesv2.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
				MarkLoanStepSuccess(lse)
				return res, nil
			}
			dataCollectionScreen = palDeeplinkHelper.GetEmploymentDetailsDeeplink(lh, lse.GetRefId())
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE:
			if lse.GetDetails().GetApplicantData().GetFatherName() != "" && lse.GetDetails().GetApplicantData().GetMotherName() != "" {
				MarkLoanStepSuccess(lse)
				return res, nil
			}
			dataCollectionScreen = palDeeplinkHelper.GetParentalDetailsDeeplink(lh, lse.GetRefId())
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE:
			if lse.GetDetails().GetApplicantData().GetResidenceType() != "" &&
				lse.GetDetails().GetApplicantData().GetEmploymentType() != "" &&
				lse.GetDetails().GetApplicantData().GetMaritalStatus() != "" {
				MarkLoanStepSuccess(lse)
				return res, nil
			}
			dataCollectionScreen = palDeeplinkHelper.GetPersonalDetailsDeeplink(lh, lse.GetRefId())
		}

		if lse.GetStatus() != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
			lg.Error("step is in progress", zap.String("step_name", lse.GetStepName().String()), zap.String("lse_status", lse.GetStatus().String()))
			res.NextAction = dataCollectionScreen
			return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("received transient failure for lse, step_name: %v, status: %v", lse.GetStepName().String(), lse.GetStatus().String()))
		}

		MarkLoanStepSuccess(lse)
		return res, nil
	})
	return actRes, actErr
}
