package activity_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto" // nolint:depguard

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
)

func TestProcessor_CheckLivenessEligibility(t *testing.T) {
	t.<PERSON>llel()
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type mockGetLoanRequestBOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGenerateOtp struct {
		res *livenessPb.GenerateOTPResponse
		err error
	}
	type mockTxnExecutor struct {
		err error
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                      string
		args                      args
		assertErr                 func(err error) bool
		want                      *preApprovedActivityPb.CheckEligibilityActivityResponse
		wantErr                   bool
		mockGetLoanRequestBOrchId *mockGetLoanRequestBOrchId
		mockGenerateOtp           *mockGenerateOtp
		mockTxnExecutor           *mockTxnExecutor
	}{
		{
			name: "should fail to fetch loan request",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			want:    nil,
			wantErr: true,
			mockGetLoanRequestBOrchId: &mockGetLoanRequestBOrchId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "should fail to generate OTP for liveness",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			assertErr: epifitemporal.IsRetryableError,
			want:      nil,
			wantErr:   true,
			mockGetLoanRequestBOrchId: &mockGetLoanRequestBOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockGenerateOtp: &mockGenerateOtp{
				res: &livenessPb.GenerateOTPResponse{
					Status: rpc.StatusInternal(),
					Otp:    "",
				},
				err: nil,
			},
		},
		{
			name: "should fail to create a loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			assertErr: epifitemporal.IsRetryableError,
			want:      nil,
			wantErr:   true,
			mockGetLoanRequestBOrchId: &mockGetLoanRequestBOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockGenerateOtp: &mockGenerateOtp{
				res: &livenessPb.GenerateOTPResponse{
					Status: rpc.StatusOk(),
					Otp:    "",
				},
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: fmt.Errorf("failed to create loan step execution"),
			},
		},
		{
			name: "should successfully create a loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: false,
			mockGetLoanRequestBOrchId: &mockGetLoanRequestBOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockGenerateOtp: &mockGenerateOtp{
				res: &livenessPb.GenerateOTPResponse{
					Status: rpc.StatusOk(),
					Otp:    "",
				},
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoanRequestBOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestBOrchId.res, tt.mockGetLoanRequestBOrchId.err)
			}
			if tt.mockGenerateOtp != nil {
				md.livenessClient.EXPECT().GenerateOTP(gomock.Any(), gomock.Any()).Return(
					tt.mockGenerateOtp.res, tt.mockGenerateOtp.err)
			}
			if tt.mockTxnExecutor != nil {
				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(
					tt.mockTxnExecutor.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.CheckLivenessEligibility, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckLivenessEligibility() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckLivenessEligibility() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckLivenessEligibility() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckLivenessEligibility() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_ProcessLivenessCheck(t *testing.T) {
	t.Parallel()
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
		&preApprovedLoanPb.LoanRequest{}, nil).AnyTimes()
	md.livenessClient.EXPECT().GetLivenessStatus(gomock.Any(), gomock.Any()).Return(&livenessPb.GetLivenessStatusResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	type mockGetByOrchId struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockGetLivenessAttempt struct {
		res *livenessPb.GetLivenessAttemptsResponse
		err error
	}
	type mockTxnExecutor struct {
		err error
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                   string
		args                   args
		want                   *activityPb.Response
		wantErr                bool
		assertErr              func(err error) bool
		mockGetByOrchId        *mockGetByOrchId
		mockGetLivenessAttempt *mockGetLivenessAttempt
		mockTxnExecutor        *mockTxnExecutor
	}{
		{
			name: "Should fail to fetch loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByOrchId: &mockGetByOrchId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "Should permanently fail as loan step execution marked failed",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
				err: nil,
			},
		},
		{
			name: "Should succeed as loan step execution already marked success",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:    nil,
			wantErr: false,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
		},
		{
			name: "#1.1 Should check liveness attempt status as loan step in created state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLivenessAttempt: &mockGetLivenessAttempt{
				res: &livenessPb.GetLivenessAttemptsResponse{
					Status:           rpc.StatusInternal(),
					LivenessAttempts: nil,
				},
				err: nil,
			},
		},
		{
			name: "#1.2 Should check liveness attempt status as loan step in created state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLivenessAttempt: &mockGetLivenessAttempt{
				res: &livenessPb.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*livenessPb.LivenessAttempt{
						{
							LivenessFlow: livenessPb.LivenessFlow_PRE_APPROVED_LOANS,
							Status:       livenessPb.LivenessStatus_LIVENESS_VIDEO_RECEIVED,
						},
					},
				},
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: nil,
			},
		},
		{
			name: "#1.3 Should check liveness attempt status as loan step in created state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLivenessAttempt: &mockGetLivenessAttempt{
				res: &livenessPb.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*livenessPb.LivenessAttempt{
						{
							LivenessFlow: livenessPb.LivenessFlow_PRE_APPROVED_LOANS,
							Status:       livenessPb.LivenessStatus_LIVENESS_VIDEO_RECEIVED,
						},
					},
				},
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: fmt.Errorf("failed to update loans step execution"),
			},
		},
		{
			name: "#1.4 Liveness returned unexpected status",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLivenessAttempt: &mockGetLivenessAttempt{
				res: &livenessPb.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*livenessPb.LivenessAttempt{
						{
							LivenessFlow: livenessPb.LivenessFlow_PRE_APPROVED_LOANS,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "#1.5 Liveness failed",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLivenessAttempt: &mockGetLivenessAttempt{
				res: &livenessPb.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*livenessPb.LivenessAttempt{
						{
							LivenessFlow: livenessPb.LivenessFlow_PRE_APPROVED_LOANS,
							Status:       livenessPb.LivenessStatus_LIVENESS_FAILED,
						},
					},
				},
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: nil,
			},
		},
		{
			name: "#1.6 Liveness passed",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-step-execution-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLivenessAttempt: &mockGetLivenessAttempt{
				res: &livenessPb.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*livenessPb.LivenessAttempt{
						{
							LivenessFlow: livenessPb.LivenessFlow_PRE_APPROVED_LOANS,
							Status:       livenessPb.LivenessStatus_LIVENESS_PASSED,
						},
					},
				},
				err: nil,
			},
			mockTxnExecutor: &mockTxnExecutor{
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByOrchId != nil {
				md.loanStepExecutionDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetByOrchId.res, tt.mockGetByOrchId.err)
			}
			if tt.mockGetLivenessAttempt != nil {
				md.livenessClient.EXPECT().GetLivenessAttempts(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLivenessAttempt.res, tt.mockGetLivenessAttempt.err)
			}
			if tt.mockTxnExecutor != nil {
				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(
					tt.mockTxnExecutor.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.ProcessLivenessCheck, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessLivenessCheck() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessLivenessCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessLivenessCheck() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessLivenessCheck() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
