package abfl

import (
	"context"
	"fmt"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"

	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/events"

	abflVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

const (
	partnerDataPushed       = "partner_data_pushed"
	offerAccepted           = "offer_accepted"
	offerGenerated          = "offer_generated"
	bankDetailsAdded        = "bank_details_added"
	personalInfoPwaScreen   = "PersonalInformation"
	kycPwaScreen            = "KYC"
	bankDetailsScreen       = "BankDetails"
	pwaDeeplinkRetryBackoff = 5000 // 5 seconds
	userOnboardingStarted   = "user_onboarding_started"
)

func (p *Processor) FetchAndSetAbflPwaJourneyLink(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		var isLoanApplicationStatusUpdated bool
		switch lr.GetSubStatus() {
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS:
			isLoanApplicationStatusUpdated = true
			palActivity.MarkLoanStepSuccess(lse)
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED:
			isLoanApplicationStatusUpdated = true
			// TODO: add more granular sub status with reason if needed
			palActivity.MarkLoanStepFailWithSubStatus(res, palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL)
		}

		if isLoanApplicationStatusUpdated {
			if err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			}); err != nil {
				lg.Error("error in updating lse status", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse status, err: %v", err))
			}
			res.NextAction = deeplink.GetApplicationStatusPollDeeplinkWithParams(&palFeEnumsPb.LoanHeader{
				LoanProgram: deeplink.GetFeLoanProgramFromBe(req.GetLoanProgram()),
				Vendor:      deeplink.GetPalFeVendorFromBe(req.GetVendor()),
			}, req.GetLoanStep().GetRefId(), nil)
			return res, nil
		}

		userType, err := getUserType(ctx, lr.GetActorId(), p.onbClient, p.savingsClient)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getUserType, err: %v", err))
		}

		if lse.GetDetails().GetVendorPwaStagesStepData().GetPwaUrl() == "" || time.Now().After(lse.GetDetails().GetVendorPwaStagesStepData().GetPwaUrlExpiryTime().AsTime()) {
			vgRes, vgErr := p.abflVgClient.CreateJourneySession(ctx, &abflVgPb.CreateJourneySessionRequest{
				Header:         &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
				CustomerId:     lr.GetVendorRequestId(),
				Program:        "",
				RedirectUrl:    "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu",
				Source:         userType,
				CampaignParams: nil,
			})
			if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
				lg.Error("unable to get the correct response from the abfl vg pwa url", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to get the correct response from the abfl vg pwa url, err: %v", te))
			}

			// update in LSE
			if lse.GetDetails() == nil {
				lse.Details = &palPb.LoanStepExecutionDetails{
					Details: &palPb.LoanStepExecutionDetails_VendorPwaStagesStepData{
						VendorPwaStagesStepData: &palPb.VendorPWAStagesStepData{},
					},
				}
			}
			if lse.GetDetails().GetVendorPwaStagesStepData() == nil {
				lse.Details.Details = &palPb.LoanStepExecutionDetails_VendorPwaStagesStepData{
					VendorPwaStagesStepData: &palPb.VendorPWAStagesStepData{},
				}
			}

			lse.GetDetails().GetVendorPwaStagesStepData().PwaUrl = vgRes.GetJourneyUrl()
			lse.GetDetails().GetVendorPwaStagesStepData().PwaUrlExpiryTime = timestamp.New(time.Now().Add(10 * time.Minute))
			if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
			}); updateErr != nil {
				lg.Error("error in updating lse details", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse details, err: %v", updateErr))
			}
		}
		pwaUrl := lse.GetDetails().GetVendorPwaStagesStepData().GetPwaUrl()

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})

		nudgeData := p.getNudgeData(ctx, lse)
		pwaScreenDl, dlErr := deeplinkProvider.GetPwaScreenDeeplinkWithWebview(ctx, &provider.PwaScreenDeeplinkRequest{
			LoanRequestId: lr.GetId(),
			EntryUrl:      pwaUrl,
			LoanHeader:    deeplinkProvider.GetLoanHeader(),
			RetryBackOff:  pwaDeeplinkRetryBackoff,
			NudgeData:     nudgeData,
		})
		if dlErr != nil {
			lg.Error("failed to get Pwa Screen Deeplink", zap.Error(dlErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get Pwa Screen Deeplink, err: %v", dlErr))
		}

		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		if err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); err != nil {
			lg.Error("error in updating lse status", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse status, err: %v", err))
		}

		res.NextAction = pwaScreenDl
		return res, errors.Wrap(epifierrors.ErrTransient, "pwa journey is not come to an end.")
	})
	return actRes, actErr
}

func (p *Processor) getNudgeData(ctx context.Context, lse *palPb.LoanStepExecution) *preapprovedloans.LoansWebviewWithStatusPollScreen_InstructionView {
	pwaStageInfos := lse.GetDetails().GetVendorPwaStagesStepData().GetStageInfos()
	length := len(pwaStageInfos)

	if length == 0 {
		return nil
	}

	latestStage := pwaStageInfos[length-1]

	var pwaScreen string
	var nudgeMessages []string
	nudgeNeeded := false
	switch latestStage.GetVendorStageName() {
	case userOnboardingStarted:
		nudgeMessage := "Please enter name exactly as mentioned in your PAN card"
		nudgeMessages = append(nudgeMessages, nudgeMessage)
		pwaScreen = personalInfoPwaScreen
		nudgeNeeded = true
	case offerAccepted:
		nudgeMessage := "Try being close to your KYC location to improve chances of approval"
		nudgeMessages = append(nudgeMessages, nudgeMessage)
		pwaScreen = kycPwaScreen
		nudgeNeeded = true
	case offerGenerated:
		nudgeMessage := "You can see the rate of interest of the offer in next screen"
		nudgeMessages = append(nudgeMessages, nudgeMessage)
		pwaScreen = personalInfoPwaScreen
		nudgeNeeded = true
	case bankDetailsAdded:
		nudgeMessage1 := "Please remove any pre-filled bank details and enter them again"
		nudgeMessage2 := "Enter only your own bank account details"
		nudgeMessages = append(nudgeMessages, nudgeMessage1, nudgeMessage2)
		pwaScreen = bankDetailsScreen
		nudgeNeeded = true
	}

	if nudgeNeeded {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewPwaNudges(lse.GetActorId(), "ABFL", "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION", pwaScreen, latestStage.GetStageName().String(), latestStage.GetVendorStageName(), nudgeMessages, latestStage.GetVendorUpdatedAt().AsTime()))
		})
		return baseprovider.GetPwaNudgeComponent(nudgeMessages, pwaScreen)
	}

	return nil
}
