package activity_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto" // nolint:depguard
	"google.golang.org/protobuf/types/known/anypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	actorPb "github.com/epifi/gamma/api/actor"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	commsPb "github.com/epifi/gamma/api/comms"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedLoanActPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func TestProcessor_CheckManualReviewStatus(t *testing.T) {
	t.Parallel()
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	incorrectPayload, _ := anypb.New(&preApprovedLoanPb.LoanRequest{})
	emptyPayload, _ := anypb.New(&preApprovedLoanActPb.CheckManualReviewStatusRequest{
		StepExecutionLivenessOrchId:  "",
		StepExecutionFaceMatchOrchId: "",
	})
	faceMatchPayload, _ := anypb.New(&preApprovedLoanActPb.CheckManualReviewStatusRequest{
		StepExecutionFaceMatchOrchId: "face-match-step-orch-id",
	})
	livenessPayload, _ := anypb.New(&preApprovedLoanActPb.CheckManualReviewStatusRequest{
		StepExecutionFaceMatchOrchId: "face-match-step-orch-id",
	})
	faceMatchLivenessPayload, _ := anypb.New(&preApprovedLoanActPb.CheckManualReviewStatusRequest{
		StepExecutionFaceMatchOrchId: "face-match-step-orch-id",
		StepExecutionLivenessOrchId:  "liveness-step-orch-id",
	})

	md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
		&preApprovedLoanPb.LoanRequest{}, nil).AnyTimes()
	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanStepExecution{
		Details: &preApprovedLoanPb.LoanStepExecutionDetails{
			Details: &preApprovedLoanPb.LoanStepExecutionDetails_ManualReviewStepData{
				ManualReviewStepData: &preApprovedLoanPb.ManualReviewStepData{
					NotificationId: "",
				},
			},
		},
	}, nil).AnyTimes()
	md.commsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any(), gomock.Any()).Return(&commsPb.SendMessageResponse{
		Status:    rpc.StatusOk(),
		MessageId: "12345678",
	}, nil).AnyTimes()
	md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
		FeatureInfo:  nil,
	}, nil).AnyTimes()

	type mockGetByOrchId struct {
		res   *preApprovedLoanPb.LoanStepExecution
		err   error
		times int
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name            string
		args            args
		want            *activityPb.Response
		wantErr         bool
		assertErr       func(err error) bool
		mockGetByOrchId *mockGetByOrchId
	}{
		{
			name: "failed to unmarshall request",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   incorrectPayload,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "no review needed for empty payload",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   emptyPayload,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "#1.1 fail permanently as fail to fetch loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   faceMatchPayload,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByOrchId: &mockGetByOrchId{
				res:   nil,
				err:   epifierrors.ErrRecordNotFound,
				times: 1,
			},
		},
		{
			name: "#1.2 perform manual review check for facematch as liveness id empty",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   faceMatchPayload,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
				},
				err:   nil,
				times: 1,
			},
		},
		{
			name: "#1.3 perform manual review check for facematch as liveness id empty",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   faceMatchPayload,
				},
			},
			want:    nil,
			wantErr: false,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err:   nil,
				times: 1,
			},
		},
		{
			name: "#2.1 fail permanently as fail to fetch loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   livenessPayload,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByOrchId: &mockGetByOrchId{
				res:   nil,
				err:   epifierrors.ErrRecordNotFound,
				times: 1,
			},
		},
		{
			name: "#2.2 perform manual review check for liveness as facematch id empty",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   livenessPayload,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
				},
				err:   nil,
				times: 1,
			},
		},
		{
			name: "#2.3 perform manual review check for liveness as face match id empty",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   livenessPayload,
				},
			},
			want:    nil,
			wantErr: false,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err:   nil,
				times: 1,
			},
		},
		{
			name: "#3.1 perform face match for liveness and facematch",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   faceMatchLivenessPayload,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
				},
				err:   nil,
				times: 2,
			},
		},
		{
			name: "#3.2 perform face match for liveness and facematch when facematch failed",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   faceMatchLivenessPayload,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
				err:   nil,
				times: 2,
			},
		},
		{
			name: "#3.3 perform face match for liveness and facematch when both passed",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
					PayloadV1:   faceMatchLivenessPayload,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByOrchId: &mockGetByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err:   nil,
				times: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByOrchId != nil {
				md.loanStepExecutionDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetByOrchId.res, tt.mockGetByOrchId.err).Times(tt.mockGetByOrchId.times)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.CheckManualReviewStatus, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckManualReviewStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckManualReviewStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckManualReviewStatus() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckManualReviewStatus() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_CheckManualReviewForLivenessAndFaceMatch(t *testing.T) {
	t.Parallel()
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
		&preApprovedLoanPb.LoanRequest{}, nil).AnyTimes()
	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(
		nil).AnyTimes()
	md.pQueue.EXPECT().InsertElement(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
		User: &userPb.User{
			Profile: &userPb.Profile{
				Photo: &commontypes.Image{
					ImageUrl: "",
				},
			},
		},
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{Status: rpc.StatusOk(), Actor: &types.Actor{}}, nil).AnyTimes()

	type mockGetByRefIdAndStepName struct {
		res   *preApprovedLoanPb.LoanStepExecution
		err   error
		times int
	}
	type mockGetLivenessStatus struct {
		res *livenessPb.GetLivenessStatusResponse
		err error
	}
	type mockGetFaceMatchStatus struct {
		res *livenessPb.GetFaceMatchStatusResponse
		err error
	}
	type mockCreateLs struct {
		res   *preApprovedLoanPb.LoanStepExecution
		err   error
		times int
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                      string
		args                      args
		want                      *preApprovedLoanActPb.CheckManualReviewForLivenessAndFaceMatchResponse
		wantErr                   bool
		assertErr                 func(err error) bool
		mockGetByRefIdAndStepName *mockGetByRefIdAndStepName
		mockGetLivenessStatus     *mockGetLivenessStatus
		mockGetFaceMatchStatus    *mockGetFaceMatchStatus
		mockCreateLs              *mockCreateLs
	}{
		{
			name: "Failed to fetch liveness loan step",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetByRefIdAndStepName: &mockGetByRefIdAndStepName{
				res:   nil,
				err:   epifierrors.ErrRecordNotFound,
				times: 1,
			},
		},
		{
			name: "Failed to fetch liveness status",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-orch-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetByRefIdAndStepName: &mockGetByRefIdAndStepName{
				res: &preApprovedLoanPb.LoanStepExecution{
					Details: &preApprovedLoanPb.LoanStepExecutionDetails{Details: &preApprovedLoanPb.LoanStepExecutionDetails_LivenessStepData{
						LivenessStepData: &preApprovedLoanPb.LivenessStepData{
							AttemptId: "",
						},
					}},
				},
				err:   nil,
				times: 1,
			},
			mockGetLivenessStatus: &mockGetLivenessStatus{
				res: &livenessPb.GetLivenessStatusResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
		},
		// {
		//	name: "trigger manual review for liveness and not face match",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &activityPb.Request{
		//			ClientReqId: "loan-req-orch-id",
		//		},
		//	},
		//	want:    nil,
		//	wantErr: false,
		//	mockGetByRefIdAndStepName: &mockGetByRefIdAndStepName{
		//		res: &preApprovedLoanPb.LoanStepExecution{
		//			Details: &preApprovedLoanPb.LoanStepExecutionDetails{Details: &preApprovedLoanPb.LoanStepExecutionDetails_LivenessStepData{
		//				LivenessStepData: &preApprovedLoanPb.LivenessStepData{
		//					AttemptId: "",
		//				},
		//			}},
		//		},
		//		err:   nil,
		//		times: 3,
		//	},
		//	mockGetLivenessStatus: &mockGetLivenessStatus{
		//		res: &livenessPb.GetLivenessStatusResponse{
		//			Status:         rpc.StatusOk(),
		//			LivenessStatus: livenessPb.LivenessStatus_LIVENESS_PASSED,
		//			LivenessScore:  85,
		//		},
		//		err: nil,
		//	},
		//	mockGetFaceMatchStatus: &mockGetFaceMatchStatus{
		//		res: &livenessPb.GetFaceMatchStatusResponse{
		//			Status:         rpc.StatusOk(),
		//			FaceMatchScore: 95,
		//		},
		//	},
		//	// mockCreateLs: &mockCreateLs{
		//	//	res:   &preApprovedLoanPb.LoanStepExecution{},
		//	//	err:   nil,
		//	//	times: 1,
		//	// },
		// },
		// {
		//	name: "trigger manual review for both liveness and face match",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &activityPb.Request{
		//			ClientReqId: "loan-req-orch-id",
		//		},
		//	},
		//	want:    nil,
		//	wantErr: false,
		//	mockGetByRefIdAndStepName: &mockGetByRefIdAndStepName{
		//		res: &preApprovedLoanPb.LoanStepExecution{
		//			Details: &preApprovedLoanPb.LoanStepExecutionDetails{Details: &preApprovedLoanPb.LoanStepExecutionDetails_LivenessStepData{
		//				LivenessStepData: &preApprovedLoanPb.LivenessStepData{
		//					AttemptId: "",
		//				},
		//			}},
		//		},
		//		err:   nil,
		//		times: 4,
		//	},
		//	mockGetLivenessStatus: &mockGetLivenessStatus{
		//		res: &livenessPb.GetLivenessStatusResponse{
		//			Status:         rpc.StatusOk(),
		//			LivenessStatus: livenessPb.LivenessStatus_LIVENESS_PASSED,
		//			LivenessScore:  85,
		//		},
		//		err: nil,
		//	},
		//	mockGetFaceMatchStatus: &mockGetFaceMatchStatus{
		//		res: &livenessPb.GetFaceMatchStatusResponse{
		//			Status:         rpc.StatusOk(),
		//			FaceMatchScore: 85,
		//		},
		//	},
		//	// mockCreateLs: &mockCreateLs{
		//	//	res:   &preApprovedLoanPb.LoanStepExecution{},
		//	//	err:   nil,
		//	//	times: 2,
		//	// },
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByRefIdAndStepName != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetByRefIdAndStepName.res, tt.mockGetByRefIdAndStepName.err).Times(tt.mockGetByRefIdAndStepName.times)
			}
			if tt.mockGetLivenessStatus != nil {
				md.livenessClient.EXPECT().GetLivenessStatus(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLivenessStatus.res, tt.mockGetLivenessStatus.err)
			}
			if tt.mockGetFaceMatchStatus != nil {
				md.livenessClient.EXPECT().GetFaceMatchStatus(gomock.Any(), gomock.Any()).Return(
					tt.mockGetFaceMatchStatus.res, tt.mockGetFaceMatchStatus.err)
			}
			if tt.mockCreateLs != nil {
				md.loanStepExecutionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(tt.mockCreateLs.res, tt.mockCreateLs.err).Times(tt.mockCreateLs.times)
			}
			var result *preApprovedLoanActPb.CheckManualReviewForLivenessAndFaceMatchResponse
			got, err := env.ExecuteActivity(palNs.CheckManualReviewForLivenessAndFaceMatch, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckManualReviewForLivenessAndFaceMatch() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckManualReviewForLivenessAndFaceMatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckManualReviewForLivenessAndFaceMatch() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckManualReviewForLivenessAndFaceMatch() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
