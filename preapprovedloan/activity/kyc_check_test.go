package activity_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	onbPb "github.com/epifi/gamma/api/user/onboarding"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	actorPb "github.com/epifi/gamma/api/actor"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	userPb "github.com/epifi/gamma/api/user"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto" // nolint:depguard
)

func TestProcessor_CheckKycEligibility(t *testing.T) {
	t.Parallel()
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanRequest{}, nil).AnyTimes()
	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	type mockGetBankCustomer struct {
		res *bankcust.GetBankCustomerResponse
		err error
	}
	type mockRunTxn struct {
		err error
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                string
		args                args
		want                *preApprovedActivityPb.CheckEligibilityActivityResponse
		wantErr             bool
		assertErr           func(err error) bool
		mockGetBankCustomer *mockGetBankCustomer
		mockRunTxn          *mockRunTxn
	}{
		{
			name: "#1.1 Should successfully return as actor is full-kyc",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   false,
			assertErr: nil,
			mockGetBankCustomer: &mockGetBankCustomer{
				res: &bankcust.GetBankCustomerResponse{
					BankCustomer: &bankcust.BankCustomer{
						DedupeInfo: &bankcust.DedupeInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
		},
		{
			name: "#2.1 Should fail as loan step execution creation failed for min-kyc",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetBankCustomer: &mockGetBankCustomer{
				res: &bankcust.GetBankCustomerResponse{
					BankCustomer: &bankcust.BankCustomer{
						DedupeInfo: &bankcust.DedupeInfo{
							KycLevel: kycPb.KYCLevel_MIN_KYC,
						},
					},
					Status: rpc.StatusOk(),
				},
			},
			mockRunTxn: &mockRunTxn{
				err: fmt.Errorf("failed to create loan step execution"),
			},
		},
		{
			name: "#2.2 Should successfully return loan step execution id for kyc check",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: false,
			mockGetBankCustomer: &mockGetBankCustomer{
				res: &bankcust.GetBankCustomerResponse{
					BankCustomer: &bankcust.BankCustomer{
						DedupeInfo: &bankcust.DedupeInfo{
							KycLevel: kycPb.KYCLevel_MIN_KYC,
						},
					},
					Status: rpc.StatusOk(),
				},
			},
			mockRunTxn: &mockRunTxn{
				err: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetBankCustomer != nil {
				md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(
					tt.mockGetBankCustomer.res, tt.mockGetBankCustomer.err)
			}
			if tt.mockRunTxn != nil {
				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(tt.mockRunTxn.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.CheckKycEligibility, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckKycEligibility() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckKycEligibility() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckKycEligibility() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckKycEligibility() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_ProcessKycCheck(t *testing.T) {
	t.Parallel()
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()
	md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanStepExecution{
		Details: &preApprovedLoanPb.LoanStepExecutionDetails{
			Details: &preApprovedLoanPb.LoanStepExecutionDetails_VkycStepData{
				VkycStepData: &preApprovedLoanPb.VkycStepData{
					NotificationId: "",
				},
			},
		},
	}, nil).AnyTimes()
	md.commsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any(), gomock.Any()).Return(&commsPb.SendMessageResponse{
		Status:    rpc.StatusOk(),
		MessageId: "12345678",
	}, nil).AnyTimes()
	md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
		FeatureInfo:  nil,
	}, nil).AnyTimes()
	md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	type mockGetLoanStepByOrchId struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockGetLoanRequestById struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetVkycSummary struct {
		res *vkycPb.GetVKYCSummaryResponse
		err error
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                    string
		args                    args
		want                    *activityPb.Response
		wantErr                 bool
		assertErr               func(err error) bool
		mockGetLoanStepByOrchId *mockGetLoanStepByOrchId
		mockGetLoanRequestById  *mockGetLoanRequestById
		mockGetVkycSummary      *mockGetVkycSummary
	}{
		{
			name: "Should fail to fetch loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "Should fail to fetch loan step execution",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: nil,
				err: fmt.Errorf("temp error"),
			},
		},
		{
			name: "#1.1 Should return success as loan step execution is in success state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    &activityPb.Response{},
			wantErr: false,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
		},
		{
			name: "#1.2 Should return permanent failure as loan step execution is in failed state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
		},
		{
			name: "#2.1 vkyc not started yet",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				res: &vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
		},
		{
			name: "#2.2 vkyc started and in registered state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				res: &vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkycPb.VKYCRecord{
						VkycSummary: &vkycPb.VKYCSummary{
							Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "#2.3 vkyc started and in review state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				res: &vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkycPb.VKYCRecord{
						VkycSummary: &vkycPb.VKYCSummary{
							Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "#2.4 vkyc started and in rejected state",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS,
				},
				err: nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				res: &vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkycPb.VKYCRecord{
						VkycSummary: &vkycPb.VKYCSummary{
							Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "#2.5 vkyc started and in rejected state and step failed",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS,
				},
				err: nil,
			},
		},
		{
			name: "#3.1 vkyc approved",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanStepByOrchId: &mockGetLoanStepByOrchId{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
				},
				err: nil,
			},
			mockGetLoanRequestById: &mockGetLoanRequestById{
				res: &preApprovedLoanPb.LoanRequest{
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS,
				},
				err: nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				res: &vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkycPb.VKYCRecord{
						VkycSummary: &vkycPb.VKYCSummary{
							Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED,
						},
					},
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoanStepByOrchId != nil {
				md.loanStepExecutionDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanStepByOrchId.res, tt.mockGetLoanStepByOrchId.err)
			}
			if tt.mockGetLoanRequestById != nil {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestById.res, tt.mockGetLoanRequestById.err)
			}
			if tt.mockGetVkycSummary != nil {
				md.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(
					tt.mockGetVkycSummary.res, tt.mockGetVkycSummary.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.ProcessKycCheck, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessKycCheck() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessKycCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessKycCheck() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessKycCheck() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
