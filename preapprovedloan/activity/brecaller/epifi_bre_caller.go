package brecaller

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	brePb "github.com/epifi/gamma/api/bre"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

type EpifiBreCaller struct {
	rpcHelper              *helper.RpcHelper
	breClient              brePb.BreClient
	txnExecutorProvider    *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	userDataProvider       userdata.IUserDataProvider
	config                 *genconf.Config
	dataDevS3Client        types2.DataDevS3Client
	onbClient              onbPb.OnboardingClient
	savingsClient          savings.SavingsClient
	salaryEstimationClient salaryestimation.SalaryEstimationClient
	eventBroker            events.Broker
}

func NewEpifiBreCaller(
	rpcHelper *helper.RpcHelper,
	breClient brePb.BreClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	userDataProvider userdata.IUserDataProvider,
	config *genconf.Config,
	dataDevS3Client types2.DataDevS3Client,
	onbClient onbPb.OnboardingClient,
	savingsClient savings.SavingsClient,
	salaryEstimationClient salaryestimation.SalaryEstimationClient,
	eventBroker events.Broker,
) *EpifiBreCaller {
	return &EpifiBreCaller{
		rpcHelper:              rpcHelper,
		breClient:              breClient,
		txnExecutorProvider:    txnExecutorProvider,
		userDataProvider:       userDataProvider,
		config:                 config,
		dataDevS3Client:        dataDevS3Client,
		onbClient:              onbClient,
		savingsClient:          savingsClient,
		salaryEstimationClient: salaryEstimationClient,
		eventBroker:            eventBroker,
	}
}

// nolint:funlen
func (s *EpifiBreCaller) CheckAndGetLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {
	userData, err := s.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.Loec.GetActorId()})
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting user data: %v", err))
	}
	var epfo, aa, cibil *brePb.DataAvailability
	for _, d := range req.Loec.GetDataRequirementDetails().GetDataRequirements() {
		if !d.GetIsCollected() {
			continue
		}
		switch d.GetDataRequirementType() {
		case palpb.DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO:
			epfo = &brePb.DataAvailability{
				IsAvailable: true,
			}
		case palpb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL:
			cibil = &brePb.DataAvailability{
				IsAvailable: true,
			}
		case palpb.DataRequirementType_DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME:
			salaryEstResp, salaryEstErr := s.salaryEstimationClient.GetSalary(ctx, &salaryestimation.GetSalaryRequest{
				ActorId: req.Loec.GetActorId(),
			})
			if te := epifigrpc.RPCError(salaryEstResp, salaryEstErr); te != nil {
				return nil, errors.Wrap(te, "failed to get the salary data")
			}

			breRequiredAaData, breRequiredAaDataErr := helper.GetBreRequiredAaData(ctx, salaryEstResp.GetL1AnalysisSignedUrl(), req.Loec.GetVendor())
			if breRequiredAaDataErr != nil {
				if errors.Is(breRequiredAaDataErr, epifierrors.ErrRecordNotFound) {
					return nil, errors.New("BRE required AA data is not available")
				}
				return nil, errors.Wrap(breRequiredAaDataErr, "error in bre required data")
			}
			req.Loec.PolicyParams.DataInfo = &palpb.DataInfo{
				AaData: breRequiredAaData,
			}
			aa = &brePb.DataAvailability{
				IsAvailable: true,
			}
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), palEvents.NewAADataReceivedForBRE(req.Loec.GetActorId(), req.Loec.GetVendor().String(), req.Loec.GetLoanProgram().String()))
			})
		default:
			// do nothing for now
		}
	}
	address := userData.GetAddress()
	// only basic address details are collected from user in eligibility flow, use them if present
	if userData.GetResidenceDetails().GetResidentialAddress().GetAddress().GetPostalCode() != "" {
		address = typesv2.GetFromBeAddress(userData.GetResidenceDetails().GetResidentialAddress().GetAddress())
	}

	userFeatProp, err := helper.GetUserFeatureProperty(ctx, req.Loec.GetActorId(), s.onbClient, s.savingsClient)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in GetUserFeatureProperty err: %v", err))
	}

	breReq := &brePb.GetFinalBreEligibilityDetailsRequest{
		ActorId:   req.Loec.GetActorId(),
		RequestId: uuid.NewString(),
		CustomerDetails: &brePb.CustomerDetails{
			PersonalDetails: &brePb.PersonalDetails{
				Dob:    userData.GivenDateOfBirth,
				Name:   userData.GivenName,
				Gender: userData.GivenGender,
				Pan:    userData.PAN,
			},
			ResidentialAddress: address,
			RequestedLoanDetails: &brePb.RequestedLoanDetails{
				DesiredLoanAmount: req.Loec.GetDataRequirementDetails().GetDesiredLoanAmount(),
			},
		},
		Vendor:       req.Loec.GetVendor(),
		Epfo:         epfo,
		Aa:           aa,
		Cibil:        cibil,
		PolicyParams: req.Loec.GetPolicyParams(),
	}

	if breReq.GetPolicyParams() == nil {
		breReq.PolicyParams = &palpb.PolicyParams{}
	}
	if breReq.GetPolicyParams().GetDataInfo() == nil {
		breReq.GetPolicyParams().DataInfo = &palpb.DataInfo{}
	}
	breReq.GetPolicyParams().GetDataInfo().IsEtbUser = userFeatProp.IsFiSAHolder

	// todo(Sharath/Anupam): Placing this condition because monthly income not
	// available for the user in self employed case, so till the time that checks
	// goes in we will be using annual revenue.
	monthlyIncome := userData.GetEmploymentDetails().GetMonthlyIncome()
	if monthlyIncome == nil && userData.GetEmploymentDetails().GetAnnualRevenue() != nil {
		monthlyIncome = userData.GetEmploymentDetails().GetAnnualRevenue()
		monthlyIncome.Units = monthlyIncome.GetUnits() / 12
	}

	if userData.EmploymentDetails != nil {
		breReq.GetCustomerDetails().EmploymentDetails = &brePb.EmploymentDetails{
			EmploymentType: userData.EmploymentDetails.EmploymentType,
			MonthlyIncome:  monthlyIncome,
			EmployerName:   userData.EmploymentDetails.OrganizationName,
			WorkEmail:      userData.EmploymentDetails.WorkEmail,
			WorkAddress:    userData.EmploymentDetails.WorkAddress,
		}
	}

	res, err := s.breClient.GetFinalBreEligibilityDetails(ctx, breReq)
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to call GetFinalBreEligibilityDetails: %v", err))
	}
	if res.GetRawBreResponse() != nil {
		csvRow := &helper.CsvRow{
			ActorId:       req.Loec.GetActorId(),
			LoecId:        req.Loec.GetId(),
			ReqId:         breReq.GetRequestId(),
			BreRes:        string(res.GetRawBreResponse()),
			BreIdentifier: "FINAL",
		}
		writeErr := helper.WriteRawResponseToS3(ctx, csvRow, s.config, s.dataDevS3Client, true)
		if writeErr != nil {
			return nil, errors.Wrap(writeErr, "failed to write final BRE res to S3")
		}
	}

	updatedLoec, loecFieldMasks, updatedLoecErr := getModifiedLoecByBreResponse(req.Loec, res, breReq.GetRequestId())
	if updatedLoecErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get modified loec by bre response: %v", updatedLoecErr))
	}
	var loanOffer *palpb.LoanOffer
	if res.GetPrioritizedDecision().GetDecision() == brePb.Decision_DECISION_APPROVED {
		if res.GetPrioritizedDecision().GetOfferDetails() == nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("offer details not found in bre response for approved decision"))
		}
		loanOffer = makeLoanOfferFromBreOutput(req.Loec, res.GetPrioritizedDecision().GetOfferDetails())
	}

	return &CheckAndGetLoanOfferResponse{
		UpdatedLoec:     updatedLoec,
		UpdateFieldMask: loecFieldMasks,
		LoanOffer:       loanOffer,
	}, nil
}

func getModifiedLoecByBreResponse(loec *palpb.LoanOfferEligibilityCriteria, breResp *brePb.GetFinalBreEligibilityDetailsResponse, finalBreReqId string) (*palpb.LoanOfferEligibilityCriteria, []palpb.LoanOfferEligibilityCriteriaFieldMask, error) {
	if loec.GetDataRequirementDetails() == nil {
		loec.DataRequirementDetails = &palpb.DataRequirementDetails{}
	}
	loec.GetDataRequirementDetails().FinalBreRequestId = finalBreReqId
	loecFieldMasks := []palpb.LoanOfferEligibilityCriteriaFieldMask{
		palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
		palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
		palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS,
		palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
	}
	loec.PolicyParams = breResp.GetPolicyParams()
	switch {
	case breResp.GetPrioritizedDecision().GetDecision() == brePb.Decision_DECISION_APPROVED:
		loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED
		loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI
		loecFieldMasks = append(loecFieldMasks, palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT)
		loec.ExpiredAt = breResp.GetPrioritizedDecision().GetValidTill()
	case !breResp.GetSubsequentCallAllowed():
		loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
		loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI
		loec.ExpiredAt = breResp.GetPrioritizedDecision().GetValidTill()
		loecFieldMasks = append(loecFieldMasks, palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT)
	case breResp.GetSubsequentCallAllowed() && breResp.GetPrioritizedDecision() == nil:
		// if subsequent call is allowed without any prioritised decision that means a subsequent call can be made with more data to get an offer
		// ideally this case should have been cleared in the data completeness checker, that is why throwing an error from here
		return nil, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("subsequent call allowed without any prioritized decision"))
	default:
		return nil, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unexpected case in bre response: %s", breResp.String()))
	}
	return loec, loecFieldMasks, nil
}

func makeLoanOfferFromBreOutput(loec *palpb.LoanOfferEligibilityCriteria, od *brePb.OfferDetails) *palpb.LoanOffer {
	return &palpb.LoanOffer{
		ActorId:       loec.GetActorId(),
		VendorOfferId: uuid.New().String(),
		Vendor:        loec.GetVendor(),
		OfferConstraints: &palpb.OfferConstraints{
			MinLoanAmount:   od.GetMinAmount(),
			MaxLoanAmount:   od.GetMaxAmount(),
			MaxEmiAmount:    od.GetMaxEmiAmount(),
			MinTenureMonths: od.GetMinTenureInMonths(),
			MaxTenureMonths: od.GetMaxTenureInMonths(),
		},
		ProcessingInfo: &palpb.OfferProcessingInfo{
			Gst: od.GetGstPercentage(),
			InterestRate: []*palpb.RangeData{
				{
					Start: 0,
					End:   1000000,
					Value: &palpb.RangeData_Percentage{
						Percentage: od.GetInterestPercentage(),
					},
				},
			},
			ProcessingFee: []*palpb.RangeData{
				{
					Start: 0,
					End:   1000000,
					Value: &palpb.RangeData_Percentage{
						Percentage: od.GetProcessingFeePercentage(),
					},
				},
			},
		},
		ValidSince:                     timestampPb.New(time.Now().In(datetime.IST)),
		ValidTill:                      od.GetValidTill(),
		LoanOfferEligibilityCriteriaId: loec.GetId(),
		LoanProgram:                    loec.GetLoanProgram(),
	}
}

func (s *EpifiBreCaller) FetchHardVendorLoanOffer(_ context.Context, _ *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {
	return nil, errors.New("not implemented")
}
