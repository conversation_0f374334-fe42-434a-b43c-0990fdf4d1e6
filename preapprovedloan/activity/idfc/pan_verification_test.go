package idfc_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	actorPb "github.com/epifi/gamma/api/actor"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
)

func TestProcessor_IdfcPanVerification(t *testing.T) {
	t.<PERSON>llel()

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		assertErr func(err error) bool
		prepare   func(*args, *mockedDependencies)
	}{
		{
			name: "should return transient error if unable to get user by actor id",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:      "lse-id-1",
						RefId:   "loan-request-id-1",
						ActorId: "actor-id-1",
						Details: &palPb.LoanStepExecutionDetails{},
						Status:  palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					},
				},
			},
			assertErr: epifitemporal.IsRetryableError,
			prepare: func(args *args, md *mockedDependencies) {
				lse := args.req.GetLoanStep()

				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse.GetId()).Return(lse, nil).Times(1)

				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: lse.GetActorId(),
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)

			},
		},
		{
			name: "should update the next action in loan request to PL_ENTER_DETAILS_SCREEN",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:      "lse-id-1",
						RefId:   "loan-request-id-1",
						ActorId: "actor-id-1",
						Details: &palPb.LoanStepExecutionDetails{},
						Status:  palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					},
				},
			},
			prepare: func(args *args, md *mockedDependencies) {
				lse := args.req.GetLoanStep()
				loanRequest := &palPb.LoanRequest{
					Id: "loan-request-id-1",
				}

				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse.GetId()).Return(lse, nil).Times(1)

				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: lse.GetActorId(),
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						EntityId: "user-id-1",
					},
				}, nil).Times(1)

				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: "user-id-1",
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Id: "user-id-1",
						Profile: &userPb.Profile{
							PAN: "**********",
						},
					},
				}, nil).Times(1)

				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse.GetRefId()).Return(loanRequest, nil).Times(1)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), loanRequest, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).DoAndReturn(
					func(ctx context.Context, lr *palPb.LoanRequest, fieldMask []palPb.LoanRequestFieldMask) error {
						if lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PL_ENTER_DETAILS_SCREEN {
							t.Errorf("IdfcPanVerification() - loanRequestDao.Update() got = %v, want %v",
								lr.GetNextAction().GetScreen(), deeplinkPb.Screen_PL_ENTER_DETAILS_SCREEN)
							return nil
						}
						return nil
					}).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newIdfcActProcessorWithMocks(t)
			defer assertTest()
			if tt.prepare != nil {
				tt.prepare(&tt.args, md)
			}
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var res *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.IdfcPanVerification, tt.args.req)
			if got != nil {
				getErr := got.Get(&res)
				if getErr != nil {
					t.Errorf("IdfcPanVerification() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != (tt.assertErr != nil):
				t.Errorf("IdfcPanVerification() error = %v, assertion failed", err)
				return
			case tt.assertErr != nil && !tt.assertErr(err):
				t.Errorf("IdfcPanVerification() error = %v assertion failed", err)
				return
			case tt.want != nil:
				if !proto.Equal(res, tt.want) {
					t.Errorf("IdfcPanVerification() got = %v, want %v", res, tt.want)
					return
				}
			}
		})
	}
}
