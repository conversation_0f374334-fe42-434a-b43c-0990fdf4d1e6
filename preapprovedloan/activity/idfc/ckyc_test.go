package idfc_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/api/actor"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
)

func TestCkyc_IdfcCheckCkyc(t *testing.T) {
	t.<PERSON>()
	// TODO:
	// We need to complete all workflows on priority and there are changes in
	// ckyc workflow as well. so skipping tests...
	t.Skip("Skipping CKYC tests...")

	p, md, assertTest := newIdfcActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanStepExecution{
		Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED,
	}, nil).AnyTimes()
	md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanRequest{}, nil).AnyTimes()
	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	type mockGetActorById struct {
		res *actor.GetActorByIdResponse
		err error
	}
	type mockGetUser struct {
		res *user.GetUserResponse
		err error
	}
	type mockSearchCkyc struct {
		res *idfc.SearchCkycResponse
		err error
	}
	type mockDownloadCkyc struct {
		res *idfc.DownloadCkycResponse
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedActivityPb.PalActivityRequest
	}
	// TODO: Only critical code paths testing have been added, there are more
	// corner cases that need to be covered,
	// eg: updateNextActionInLoanRequest failures test cases are not written
	tests := []struct {
		name             string
		args             args
		want             *preApprovedActivityPb.PalActivityResponse
		wantErr          bool
		assertErr        func(err error) bool
		mockGetActorById *mockGetActorById
		mockGetUser      *mockGetUser
		mockSearchCkyc   *mockSearchCkyc
		mockDownloadCkyc *mockDownloadCkyc
	}{
		{
			name: "Success case",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
				},
			},
			wantErr:   false,
			assertErr: nil,
			mockGetActorById: &mockGetActorById{
				res: &actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "123",
					},
				},
				err: nil,
			},
			mockGetUser: &mockGetUser{
				res: &user.GetUserResponse{
					User: &user.User{
						Id: "123",
						Profile: &user.Profile{
							DateOfBirth: &date.Date{
								Year:  2000,
								Month: 01,
								Day:   01,
							},
							PAN: "xxx",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSearchCkyc: &mockSearchCkyc{
				res: &idfc.SearchCkycResponse{
					Status: rpc.StatusOk(),
					CkycResponse: &idfc.SearchCkycResponse_CkycResponse{
						Details: []*idfc.SearchCkycResponse_CkycResponse_CkycDetails{
							{CkycId: ""},
						},
					},
				},
				err: nil,
			},
			mockDownloadCkyc: &mockDownloadCkyc{
				res: &idfc.DownloadCkycResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			want: &preApprovedActivityPb.PalActivityResponse{
				LoanStep: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
			},
		},
		{
			name: "Failure case - Download ckyc not found response",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
				},
			},
			wantErr:   false,
			assertErr: nil,
			mockGetActorById: &mockGetActorById{
				res: &actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "123",
					},
				},
				err: nil,
			},
			mockGetUser: &mockGetUser{
				res: &user.GetUserResponse{
					User: &user.User{
						Id: "123",
						Profile: &user.Profile{
							DateOfBirth: &date.Date{
								Year:  2000,
								Month: 01,
								Day:   01,
							},
							PAN: "xxx",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSearchCkyc: &mockSearchCkyc{
				res: &idfc.SearchCkycResponse{
					Status: rpc.StatusOk(),
					CkycResponse: &idfc.SearchCkycResponse_CkycResponse{
						Details: []*idfc.SearchCkycResponse_CkycResponse_CkycDetails{
							{CkycId: ""},
						},
					},
				},
				err: nil,
			},
			mockDownloadCkyc: &mockDownloadCkyc{
				res: &idfc.DownloadCkycResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			want: &preApprovedActivityPb.PalActivityResponse{
				LoanStep: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
			},
		},
		{
			name: "Failure case - Search ckyc not found response",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
				},
			},
			wantErr:   false,
			assertErr: nil,
			mockGetActorById: &mockGetActorById{
				res: &actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "123",
					},
				},
				err: nil,
			},
			mockGetUser: &mockGetUser{
				res: &user.GetUserResponse{
					User: &user.User{
						Id: "123",
						Profile: &user.Profile{
							DateOfBirth: &date.Date{
								Year:  2000,
								Month: 01,
								Day:   01,
							},
							PAN: "xxx",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSearchCkyc: &mockSearchCkyc{
				res: &idfc.SearchCkycResponse{
					Status:       rpc.StatusRecordNotFound(),
					CkycResponse: nil,
				},
				err: nil,
			},
			want: &preApprovedActivityPb.PalActivityResponse{
				LoanStep: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
			},
		},
		{
			name: "Failure case - Get user failed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetActorById: &mockGetActorById{
				res: &actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "123",
					},
				},
				err: nil,
			},
			mockGetUser: &mockGetUser{
				res: &user.GetUserResponse{
					User:   nil,
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: nil,
		},
		{
			name: "Failure case - Get actor failed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetActorById: &mockGetActorById{
				res: &actor.GetActorByIdResponse{
					Status: rpc.StatusInternal(),
					Actor:  nil,
				},
				err: nil,
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetActorById != nil {
				md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(
					tt.mockGetActorById.res, tt.mockGetActorById.err)
			}
			if tt.mockGetUser != nil {
				md.userClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(
					tt.mockGetUser.res, tt.mockGetUser.err)
			}
			if tt.mockSearchCkyc != nil {
				md.idfcPalVgClient.EXPECT().SearchCkyc(gomock.Any(), gomock.Any()).Return(
					tt.mockSearchCkyc.res, tt.mockSearchCkyc.err)
			}
			if tt.mockDownloadCkyc != nil {
				md.idfcPalVgClient.EXPECT().DownloadCkyc(gomock.Any(), gomock.Any()).Return(
					tt.mockDownloadCkyc.res, tt.mockDownloadCkyc.err)
			}
			var result *preApprovedActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.IdfcCheckCkyc, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("IdfcCheckCkyc() error = %v", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("IdfcCheckCkyc() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("IdfcCheckCkyc() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("IdfcCheckCkyc() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
