package idfc_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/mock"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	vgIdfcPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
)

func TestProcessor_IdfcDisbursal(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		assertErr func(err error) bool
		prepare   func(*args, *mockedDependencies)
	}{
		{
			name: "should return non-retryable error when vendor gives disbursal error",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:      "lse-id-1",
						RefId:   "loan-request-id-1",
						ActorId: "actor-id-1",
						Details: &palPb.LoanStepExecutionDetails{},
					},
				},
			},
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			prepare: func(args *args, md *mockedDependencies) {
				loanRequest := &palPb.LoanRequest{
					Id:              "loan-request-id-1",
					VendorRequestId: "loan-request-vendor-request-id-1",
				}
				lse := args.req.GetLoanStep()
				loanApplicant := &palPb.LoanApplicant{
					ActorId:         lse.GetActorId(),
					VendorRequestId: "loan-applicant-vendor-request-id-1",
				}

				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse.GetId()).Return(lse, nil).Times(1)
				md.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), lse.GetActorId()).Return(loanApplicant, nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse.GetRefId()).Return(loanRequest, nil).Times(2)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).DoAndReturn(func(ctx context.Context, lr *palPb.LoanRequest, fieldMask []palPb.LoanRequestFieldMask) error {
					if lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN {
						t.Errorf("IdfcDisbursal() - loanRequestDao.Update() got = %v, want %v",
							lr.GetNextAction().GetScreen(), deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN)
						return nil
					}
					return nil
				}).Times(1)

				md.idfcPalVgClient.EXPECT().AutoDisbursal(gomock.Any(),
					mock.NewProtoMatcherWithIgnoreFields(&vgIdfcPb.AutoDisbursalRequest{
						PreAuthId: loanApplicant.GetVendorRequestId(),
						EntityId:  loanApplicant.GetVendorRequestId(),
						LoanId:    loanRequest.GetVendorRequestId(),
						ReqType:   vgIdfcPb.AutoDisbursalRequestType_AUTO_DISBURSAL_REQUEST_TYPE_AUTH,
					}, "correlation_id"),
				).Return(&vgIdfcPb.AutoDisbursalResponse{
					Status:              rpc.StatusOk(),
					AutoDisbursalStatus: vgIdfcPb.AutoDisbursalStatus_AUTO_DISBURSAL_STATUS_ERROR,
				}, nil).Times(1)
			},
		},
		{
			name: "should successfully disburse when vendor gives disbursal success",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:      "lse-id-1",
						RefId:   "loan-request-id-1",
						ActorId: "actor-id-1",
						Details: &palPb.LoanStepExecutionDetails{},
					},
				},
			},
			prepare: func(args *args, md *mockedDependencies) {
				loanRequest := &palPb.LoanRequest{
					Id:              "loan-request-id-1",
					VendorRequestId: "loan-request-vendor-request-id-1",
					OfferId:         "loan-offer-id-1",
					Details: &palPb.LoanRequestDetails{
						LoanInfo: &palPb.LoanRequestDetails_LoanInfo{
							TenureInMonths: 3,
							Deductions:     &palPb.LoanRequestDetails_LoanInfo_Deductions{},
						},
					},
				}
				lse := args.req.GetLoanStep()
				loanApplicant := &palPb.LoanApplicant{
					ActorId:         lse.GetActorId(),
					VendorRequestId: "loan-applicant-vendor-request-id-1",
				}
				loanAccount := &palPb.LoanAccount{
					Id: "loan-account-id-1",
					LoanAmountInfo: &palPb.LoanAmountInfo{
						LoanAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1_23_000,
						},
						DisbursedAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1_21_000,
						},
						OutstandingAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1_28_000,
						},
						TotalPayableAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1_28_000,
						},
					},
					Details: &palPb.LoanAccountDetails{
						LoanName: "Loan #1",
					},
					MaturityDate: &datePb.Date{Day: 18, Month: 10, Year: 2021},
					Status:       palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
				}
				loanInstallmentInfo := &palPb.LoanInstallmentInfo{
					Id:                  "loan-installment-info-id-1",
					AccountId:           loanAccount.GetId(),
					StartDate:           &datePb.Date{Day: 18, Month: 7, Year: 2021},
					EndDate:             &datePb.Date{Day: 3, Month: 10, Year: 2021},
					NextInstallmentDate: &datePb.Date{Day: 3, Month: 8, Year: 2021},
					Details: &palPb.LoanInstallmentInfoDetails{
						GracePeriod: 3,
					},
					TotalAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1_28_000,
					},
					Status: palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
				}
				loanActivity := &palPb.LoanActivity{
					LoanAccountId: loanAccount.GetId(),
					Type:          palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT,
					Details: &palPb.LoanActivityDetails{
						Amount: loanAccount.GetLoanAmountInfo().GetDisbursedAmount(),
					},
				}

				md.time.EXPECT().Now().Return(time.Date(2021, 7, 18, 0, 0, 0, 0, datetimePkg.IST)).Times(4)

				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse.GetId()).Return(lse, nil).Times(1)
				md.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), lse.GetActorId()).Return(loanApplicant, nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse.GetRefId()).Return(loanRequest, nil).Times(3)

				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, status.Error(codes.NotFound, "account not found")).Times(1)
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
					Feature: onboardingPb.Feature_FEATURE_SA,
				}).Return(&onboardingPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil).Times(1)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(
					gomock.Any(),
					gomock.Any(),
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE,
				).Return(&palPb.LoanStepExecution{
					Id:      "mandate-lse-id-1",
					ActorId: "actor-id-1",
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_MandateData{
							MandateData: &palPb.MandateData{
								BankingDetails: &palPb.MandateData_BankingDetails{
									FinalAccDetailsUsed: &palPb.MandateData_BankingDetails_AccountDetails{
										AccountNumber: "**********",
										IfscCode:      "HDFC0001234",
									},
								},
							},
						},
					},
				}, nil).Times(1)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).DoAndReturn(func(ctx context.Context, lr *palPb.LoanRequest, fieldMask []palPb.LoanRequestFieldMask) error {
					if lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN &&
						lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_SCREEN {
						t.Errorf("IdfcDisbursal() - loanRequestDao.Update() got = %v, want %v or %v",
							lr.GetNextAction().GetScreen(),
							deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
							deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_SCREEN,
						)
						return nil
					}
					return nil
				}).Times(2)

				md.idfcPalVgClient.EXPECT().AutoDisbursal(gomock.Any(),
					mock.NewProtoMatcherWithIgnoreFields(&vgIdfcPb.AutoDisbursalRequest{
						PreAuthId: loanApplicant.GetVendorRequestId(),
						EntityId:  loanApplicant.GetVendorRequestId(),
						LoanId:    loanRequest.GetVendorRequestId(),
						ReqType:   vgIdfcPb.AutoDisbursalRequestType_AUTO_DISBURSAL_REQUEST_TYPE_AUTH,
					}, "correlation_id"),
				).Return(&vgIdfcPb.AutoDisbursalResponse{
					Status:              rpc.StatusOk(),
					AutoDisbursalStatus: vgIdfcPb.AutoDisbursalStatus_AUTO_DISBURSAL_STATUS_SUCCESS,
					ResourceData: []*vgIdfcPb.AutoDisbursalResponse_ResourceData{
						{
							KfsDetails: []*vgIdfcPb.KfsDetails{
								{
									LoanAmount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1_23_000,
									},
									NetDisbursedAmount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1_21_000,
									},
									TotalPayableAmount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1_28_000,
									},
								},
							},
						},
					},
				}, nil).Times(1)

				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, txnFunc func(context.Context) error) error {
					return txnFunc(ctx)
				}).Times(1)

				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(),
					mock.NewProtoMatcherWithIgnoreFields(loanAccount, "id"),
				).Return(loanAccount, nil).Times(1)

				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(),
					mock.NewProtoMatcherWithIgnoreFields(loanInstallmentInfo, "id"),
				).Return(loanInstallmentInfo, nil).Times(1)

				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER,
				}).Return(nil).Times(1)

				md.loanOfferDao.EXPECT().Update(gomock.Any(),
					mock.NewProtoMatcherWithIgnoreFields(&palPb.LoanOffer{
						Id: loanRequest.GetOfferId(),
					}, "deactivated_at"),
					[]palPb.LoanOfferFieldMask{
						palPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT,
					}).
					Return(nil).Times(1)

				md.loanActivityDao.EXPECT().Create(
					gomock.Any(),
					mock.NewProtoMatcherWithIgnoreFields(loanActivity, "reference_id"),
				).Return(loanActivity, nil).Times(1)

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newIdfcActProcessorWithMocks(t)
			defer assertTest()
			if tt.prepare != nil {
				tt.prepare(&tt.args, md)
			}
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var res *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.IdfcDisbursal, tt.args.req)
			if got != nil {
				getErr := got.Get(&res)
				if getErr != nil {
					t.Errorf("IdfcDisbursal() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != (tt.assertErr != nil):
				t.Errorf("IdfcDisbursal() error = %v, assertion failed", err)
				return
			case tt.assertErr != nil && !tt.assertErr(err):
				t.Errorf("IdfcDisbursal() error = %v assertion failed", err)
				return
			case tt.want != nil:
				if !proto.Equal(res, tt.want) {
					t.Errorf("IdfcDisbursal() got = %v, want %v", res, tt.want)
					return
				}
			}
		})
	}
}

func TestProcessor_IdfcCheckLoanStatus(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		wantErr   bool
		assertErr func(err error) bool
		prepare   func(*args, *mockedDependencies)
	}{
		{
			name: "should return retryable error when vendor gives ISE",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:      "lse-id-1",
						RefId:   "loan-request-id-1",
						ActorId: "actor-id-1",
						Details: &palPb.LoanStepExecutionDetails{},
					},
				},
			},
			assertErr: epifitemporal.IsRetryableError,
			prepare: func(args *args, md *mockedDependencies) {
				loanRequest := &palPb.LoanRequest{
					Id:              "loan-request-id-1",
					VendorRequestId: "loan-request-vendor-request-id-1",
				}
				lse := args.req.GetLoanStep()
				loanApplicant := &palPb.LoanApplicant{
					ActorId:           lse.GetActorId(),
					VendorRequestId:   "loan-applicant-vendor-request-id-1",
					VendorApplicantId: "loan-applicant-vendor-applicant-id-1",
				}

				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse.GetId()).Return(lse, nil).Times(1)
				md.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), lse.GetActorId()).Return(loanApplicant, nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse.GetRefId()).Return(loanRequest, nil).Times(1)

				md.idfcPalVgClient.EXPECT().LoanUtilities(gomock.Any(),
					&vgIdfcPb.LoanUtilitiesRequest{
						AgreementId: loanRequest.GetVendorRequestId(),
						CrnNo:       loanApplicant.GetVendorApplicantId(),
						RequestCode: "STATUS",
					},
				).Return(&vgIdfcPb.LoanUtilitiesResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
			},
		},
		{
			name: "should successfully check loan status",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:      "lse-id-1",
						RefId:   "loan-request-id-1",
						ActorId: "actor-id-1",
						Details: &palPb.LoanStepExecutionDetails{},
					},
				},
			},
			prepare: func(args *args, md *mockedDependencies) {
				loanRequest := &palPb.LoanRequest{
					Id:              "loan-request-id-1",
					VendorRequestId: "loan-request-vendor-request-id-1",
				}
				lse := args.req.GetLoanStep()
				loanApplicant := &palPb.LoanApplicant{
					ActorId:           lse.GetActorId(),
					VendorRequestId:   "loan-applicant-vendor-request-id-1",
					VendorApplicantId: "loan-applicant-vendor-applicant-id-1",
				}

				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse.GetId()).Return(lse, nil).Times(1)
				md.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), lse.GetActorId()).Return(loanApplicant, nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse.GetRefId()).Return(loanRequest, nil).Times(1)

				md.idfcPalVgClient.EXPECT().LoanUtilities(gomock.Any(),
					&vgIdfcPb.LoanUtilitiesRequest{
						AgreementId: loanRequest.GetVendorRequestId(),
						CrnNo:       loanApplicant.GetVendorApplicantId(),
						RequestCode: "STATUS",
					},
				).Return(&vgIdfcPb.LoanUtilitiesResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newIdfcActProcessorWithMocks(t)
			defer assertTest()
			if tt.prepare != nil {
				tt.prepare(&tt.args, md)
			}
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var res *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.IdfcCheckLoanStatus, tt.args.req)
			if got != nil {
				getErr := got.Get(&res)
				if getErr != nil {
					t.Errorf("IdfcCheckLoanStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != (tt.assertErr != nil):
				t.Errorf("IdfcCheckLoanStatus() error = %v, assertion failed", err)
				return
			case tt.assertErr != nil && !tt.assertErr(err):
				t.Errorf("IdfcCheckLoanStatus() error = %v assertion failed", err)
				return
			case tt.want != nil:
				if !proto.Equal(res, tt.want) {
					t.Errorf("IdfcCheckLoanStatus() got = %v, want %v", res, tt.want)
					return
				}
			}
		})
	}
}
