// nolint: gocritic
package idfc_test

import (
	"context"
	"errors"
	"testing"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	kycPb "github.com/epifi/gamma/api/kyc"
	kycV2Pb "github.com/epifi/gamma/api/kyc/v2/kyc"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
)

func TestProcessor_IdfcVkyc(t *testing.T) {
	t.<PERSON>()

	type mockGetLseByRefIdAndFlow struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}
	type mockGetKycStatus struct {
		res *kycPb.GetKYCStatusResponse
		err error
	}
	type mockInitKyc struct {
		res *kycPb.InitiateKYCResponse
		err error
	}
	type args struct {
		ctx context.Context
		req *preApprovedActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *preApprovedActivityPb.PalActivityResponse
		wantErr   bool
		assertErr func(err error) bool
		// setup mocks
		mockGetLseByRefIdAndFlow *mockGetLseByRefIdAndFlow
		mockGetKycStatus         *mockGetKycStatus
		mockInitKyc              *mockInitKyc
	}{
		{
			name: "success : positive confirmation yes, no vkyc required",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want: &preApprovedActivityPb.PalActivityResponse{
				LoanStep: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
			},
			wantErr:   false,
			assertErr: nil,
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{
					Details: &preApprovedLoanPb.LoanStepExecutionDetails{
						Details: &preApprovedLoanPb.LoanStepExecutionDetails_CkycStepData{
							CkycStepData: &preApprovedLoanPb.CkycStepData{
								PositiveConfirmation: preApprovedLoanPb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_YES,
							},
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "success : completed vkyc",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want: &preApprovedActivityPb.PalActivityResponse{
				LoanStep: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
			},
			wantErr:   false,
			assertErr: nil,
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_COMPLETED,
				},
				err: nil,
			},
		},
		{
			name: "permanent error, vkyc rejected",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_FAILED,
				},
				err: nil,
			},
		},
		{
			name: "transient error, vkyc in progress",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_IN_PROGRESS,
				},
				err: nil,
			},
		},
		{
			name: "permanent error, vkyc expired",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_EXPIRED,
				},
				err: nil,
			},
		},
		{
			name: "initiate vkyc successfully for first time, record not created",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockInitKyc: &mockInitKyc{
				res: &kycPb.InitiateKYCResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_IN_PROGRESS,
				},
				err: nil,
			},
		},
		{
			name: "initiate vkyc successfully, earlier attempt got created but failed due to vendor error",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_CREATED,
				},
				err: nil,
			},
			mockInitKyc: &mockInitKyc{
				res: &kycPb.InitiateKYCResponse{
					Status:    rpcPb.StatusOk(),
					KYCStatus: kycV2Pb.Status_STATUS_IN_PROGRESS,
				},
				err: nil,
			},
		},
		{
			name: "kyc client GetKycStatus failed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{Status: rpcPb.StatusInternal()},
				err: nil,
			},
		},
		{
			name: "kyc client InitiateKYC failed",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: &preApprovedLoanPb.LoanStepExecution{},
				err: nil,
			},
			mockGetKycStatus: &mockGetKycStatus{
				res: &kycPb.GetKYCStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockInitKyc: &mockInitKyc{
				res: &kycPb.InitiateKYCResponse{Status: rpcPb.StatusInternal()},
				err: nil,
			},
		},
		{
			name: "failed to fetch CKYC LSE for positive confirmation check",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preApprovedLoanPb.LoanStepExecution{},
					Vendor:        preApprovedLoanPb.Vendor_IDFC,
					LoanProgram:   preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLseByRefIdAndFlow: &mockGetLseByRefIdAndFlow{
				res: nil,
				err: errors.New("failed to fetch LSE"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newIdfcActProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanRequest{}, nil).AnyTimes()
			md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preApprovedLoanPb.LoanStepExecution{}, nil).AnyTimes()
			md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			if tt.mockGetLseByRefIdAndFlow != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(tt.mockGetLseByRefIdAndFlow.res, tt.mockGetLseByRefIdAndFlow.err)
			}
			if tt.mockGetKycStatus != nil {
				md.kycClient.EXPECT().GetKYCStatus(gomock.Any(), gomock.Any()).Return(tt.mockGetKycStatus.res, tt.mockGetKycStatus.err)
			}
			if tt.mockInitKyc != nil {
				md.kycClient.EXPECT().InitiateKYC(gomock.Any(), gomock.Any()).Return(tt.mockInitKyc.res, tt.mockInitKyc.err)
			}

			var result *preApprovedActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.IdfcVkyc, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("IdfcVkyc() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("IdfcVkyc() \nerror = %v, \nwantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("IdfcVkyc() \nerror = %v \nassertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("IdfcVkyc() \ngot = %v, \nwant %v", result, tt.want)
				return
			}
		})
	}
}
