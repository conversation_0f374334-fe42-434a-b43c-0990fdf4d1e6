package activity_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	moneyPb "github.com/epifi/be-common/pkg/money"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
)

var (
	currentTime      = time.Now()
	time10MinsBefore = time.Now().Add(-10 * time.Minute)
)

func TestProcessor_ExecuteRecurringPayment(t *testing.T) {
	t.Parallel()
	loanAcc1 := &palPb.LoanAccount{
		Id: "acc-id-1",
		Details: &palPb.LoanAccountDetails{
			RecurringPaymentDetails: &palPb.RecurringPaymentDetails{
				Id: "recurring-payment-1",
			},
		},
	}
	loanPaymentRequest1 := &palPb.LoanPaymentRequest{
		Id:        "lpr-1",
		ActorId:   "actor-1",
		AccountId: loanAcc1.GetId(),
		OrchId:    "orch-id-1",
		Amount:    moneyPb.AmountINR(500).GetPb(),
	}
	lse1 := &palPb.LoanStepExecution{
		Id:      "lse-id-1",
		RefId:   loanPaymentRequest1.GetId(),
		ActorId: loanPaymentRequest1.GetActorId(),
	}

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *mockedDependencies)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should fail with transient error when dao call to fetch loan payment request fails",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			want: nil,
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(nil, epifierrors.ErrTransient)
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to fetch loan account fails",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			want: nil,
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(nil, errors.New("error fetching loan account from db"))
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when rpc call to fetch recurring payment execution status fails with a transient error",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			want: nil,
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)
				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when rpc call to initiate recurring payment execution fails",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			want: nil,
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return LSE status as FAILED when rpc call to initiate recurring payment execution fails with a permanent error",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.NewStatus(uint32(rpPb.ExecuteRecurringPaymentResponse_MANDATE_REVOKED), "", ""),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:        lse1.GetId(),
					ActorId:   lse1.GetActorId(),
					RefId:     lse1.GetRefId(),
					Status:    palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus: palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_INITIATION_FAILED,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
			},
			wantErr: false,
		},
		{
			name: "should fail with transient error when rpc call to fetch execution status (after initiation the recurring payment execution) fails",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			want: nil,
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return LSE status as FAILED when fetch recurring execution status rpc returns execution state as FAILED",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_FAILURE,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:      lse1.GetId(),
					ActorId: lse1.GetActorId(),
					RefId:   lse1.GetRefId(),
					Status:  palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
			},
			wantErr: false,
		},
		{
			name: "should return transient error when fetch recurring execution status rpc returns a non terminal state",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_IN_PROGRESS,
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient error when no successful transaction is returned by get transaction details rpc",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				}, nil)

				md.orderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: clonedLoanPaymentRequest1.GetOrchId(),
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-1"},
				}, nil)

				md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-1",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1"},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Utr:             "utr-1",
								Status:          paymentPb.TransactionStatus_FAILED,
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								CreatedAt:       timestampPb.New(time10MinsBefore),
								DebitedAt:       timestampPb.New(currentTime),
							},
						},
					},
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient error when dao call to update loan payment request fails",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				}, nil)

				md.orderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: clonedLoanPaymentRequest1.GetOrchId(),
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-1"},
				}, nil)

				md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-1",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1"},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Utr:             "utr-1",
								Status:          paymentPb.TransactionStatus_SUCCESS,
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								CreatedAt:       timestampPb.New(time10MinsBefore),
								DebitedAt:       timestampPb.New(currentTime),
							},
						},
					},
				}, nil)

				md.loanPaymentRequestsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error"))
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return success response when recurring payment execution is successful",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.recurringPaymentClient.EXPECT().ExecuteRecurringPayment(gomock.Any(), &rpPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Amount:             clonedLoanPaymentRequest1.GetAmount(),
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
				}).Return(&rpPb.ExecuteRecurringPaymentResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				}, nil)

				md.orderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: clonedLoanPaymentRequest1.GetOrchId(),
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-1"},
				}, nil)

				md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-1",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1"},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Utr:             "utr-1",
								Status:          paymentPb.TransactionStatus_SUCCESS,
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								CreatedAt:       timestampPb.New(time10MinsBefore),
								DebitedAt:       timestampPb.New(currentTime),
							},
						},
					},
				}, nil)

				md.loanPaymentRequestsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: lse1,
			},
			wantErr: false,
		},
		{
			name: "should return success response when an already initiated recurring payment execution was successful",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				}, nil)

				md.orderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: clonedLoanPaymentRequest1.GetOrchId(),
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-1"},
				}, nil)

				md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-1",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1"},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Utr:             "utr-1",
								Status:          paymentPb.TransactionStatus_SUCCESS,
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								CreatedAt:       timestampPb.New(time10MinsBefore),
								DebitedAt:       timestampPb.New(currentTime),
							},
						},
					},
				}, nil)

				md.loanPaymentRequestsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: lse1,
			},
			wantErr: false,
		},
		{
			name: "should return LSE status as FAILED when fetch recurring execution status rpc returns execution state as FAILED for an already initiated execution",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: lse1,
					Vendor:   palPb.Vendor_EPIFI_TECH,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				clonedLse1 := cloneLoanStepExecution(lse1)
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), lse1.GetId()).Return(clonedLse1, nil).Times(1)

				clonedLoanPaymentRequest1 := cloneLoanPaymentRequest(loanPaymentRequest1)
				md.loanPaymentRequestsDao.EXPECT().GetById(gomock.Any(), clonedLse1.GetRefId()).Return(clonedLoanPaymentRequest1, nil)

				md.loanAccountDao.EXPECT().GetById(gomock.Any(), clonedLoanPaymentRequest1.GetAccountId()).Return(loanAcc1, nil)
				md.recurringPaymentClient.EXPECT().GetRecurringPaymentActionStatus(gomock.Any(), &rpPb.GetRecurringPaymentActionStatusRequest{
					RecurringPaymentId: loanAcc.GetDetails().GetRecurringPaymentDetails().GetId(),
					Action:             rpPb.Action_EXECUTE,
					ClientRequestId:    clonedLoanPaymentRequest1.GetOrchId(),
					CurrentActorId:     clonedLoanPaymentRequest1.GetActorId(),
				}).Return(&rpPb.GetRecurringPaymentActionStatusResponse{
					Status:      rpc.StatusOk(),
					ActionState: rpPb.GetRecurringPaymentActionStatusResponse_FAILURE,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:      lse1.GetId(),
					ActorId: lse1.GetActorId(),
					RefId:   lse1.GetRefId(),
					Status:  palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newPalActProcessorWithMocks(t)
			defer assertTest()
			tt.mockFunc(md)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.ExecuteRecurringPayment, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ExecuteRecurringPayment() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ExecuteRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ExecuteRecurringPayment() error = %v assertion failed", err)
				return
			case !proto.Equal(result, tt.want):
				t.Errorf("ExecuteRecurringPayment() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func cloneLoanStepExecution(lse *palPb.LoanStepExecution) *palPb.LoanStepExecution {
	return proto.Clone(lse).(*palPb.LoanStepExecution)
}

func cloneLoanPaymentRequest(lpr *palPb.LoanPaymentRequest) *palPb.LoanPaymentRequest {
	return proto.Clone(lpr).(*palPb.LoanPaymentRequest)
}
