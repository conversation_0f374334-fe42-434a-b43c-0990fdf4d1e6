package fiftyfin_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	typesPb "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	usersPb "github.com/epifi/gamma/api/user"
	vgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

func GetLoanOfferExpiryErrorScreen() *deeplinkPb.Deeplink {
	dl, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, &palTypesPb.LoansFailureScreen{
		LoanHeader: &pal_enums.LoanHeader{
			LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      pal_enums.Vendor_FIFTYFIN,
		},
		PageHeader: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/filled_triangle_warning.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
			TitleText:     commontypes.GetPlainStringText("Uh-oh! We encountered an issue").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
			SubtitleText:  commontypes.GetPlainStringText("Please refresh your portfolio to continue").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
		},
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText("Refresh portfolio", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			Cta: &deeplinkPb.Cta{
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
						PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
							LoanHeader: &pal_enums.LoanHeader{
								LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
								Vendor:      pal_enums.Vendor_FIFTYFIN,
							},
						},
					},
				},
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Text:         "Refresh portfolio",
			},
			Padding: helper.GetButtonPadding(12, 24, 12, 24),
			Margin:  helper.GetButtonMargin(16, 24, 24, 24),
		},
		AnalyticsScreenName: analytics.AnalyticsScreenName_LAMF_PERMANENT_FAILURE_FULL_SCREEN,
		Components: []*palTypesPb.LoansFailureScreenComponent{
			{
				Component: &palTypesPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palTypesPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement:   commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/filled_triangle_warning.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
							TitleText:       commontypes.GetPlainStringText("Uh-oh! We encountered an issue").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
							SubtitleText:    commontypes.GetPlainStringText("Please refresh your portfolio to continue").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	})
	return dl
}

func getKycAdditionalDetailsDl(fatherName, motherName, loanReqId string) *deeplinkPb.Deeplink {
	getLabelText := func(label string) *commontypes.Text {
		return helper.GetText(label, "#B9B9B9", "#FFFFFF", commontypes.FontStyle_OVERLINE_1)
	}
	getValueText := func(val string) *commontypes.Text {
		return helper.GetText(val, "#313234", "#FFFFFF", commontypes.FontStyle_SUBTITLE_1)
	}
	getDropdownTitle := func(val string) *commontypes.Text {
		return helper.GetText(val, "#282828", "", commontypes.FontStyle_SUBTITLE_2)
	}
	getDropdownOption := func(label, value string) *palTypesPb.DropdownSelectionOptionComponent_Option {
		return &palTypesPb.DropdownSelectionOptionComponent_Option{
			DisplayText: helper.GetText(label, "#646464", "", commontypes.FontStyle_SUBTITLE_3),
			Value:       value,
		}
	}

	dl, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOAN_APPLICATION_ADDITIONAL_DETAILS_USER_INPUT_SCREEN, &palTypesPb.LamfApplicationAdditionalDetailsInputScreen{
		LoanHeader: &pal_enums.LoanHeader{
			LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      pal_enums.Vendor_FIFTYFIN,
		},
		LoanApplicationId: loanReqId,
		Title: &uiPb.VerticalKeyValuePair{
			Title:                        uiPb.NewITC().WithTexts(helper.GetText("Complete your KYC", "#313234", "#FFFFFF", commontypes.FontStyle_HEADLINE_L)),
			Value:                        uiPb.NewITC().WithTexts(helper.GetText("Fill in these details to verify your identity", "#929599", "#FFFFFF", commontypes.FontStyle_BODY_S)),
			VerticalPaddingBtwTitleValue: 12,
		},
		FatherName: &palTypesPb.LamfApplicationAdditionalDetailsInputScreen_TextInputOption{
			Label:      getLabelText("FATHER'S NAME"),
			Value:      getLabelText(fatherName),
			HelperText: helper.GetText("Please make sure this matches your PAN", "#A4A4A4", "#FFFFFF", commontypes.FontStyle_BODY_4),
		},
		MotherName: &palTypesPb.LamfApplicationAdditionalDetailsInputScreen_TextInputOption{
			Label: getLabelText("MOTHER'S NAME"),
			Value: getValueText(motherName),
		},
		MaritalStatus: &palTypesPb.LamfApplicationAdditionalDetailsInputScreen_DropdownSelectionInputOption{
			Label: getLabelText("MARITAL STATUS"),
			Dropdown: &palTypesPb.DropdownSelectionOptionComponent{
				Title: getDropdownTitle("Marital status"),
				Options: []*palTypesPb.DropdownSelectionOptionComponent_Option{
					getDropdownOption("Single", typesPb.MaritalStatus_UNMARRIED.String()),
					getDropdownOption("Married", typesPb.MaritalStatus_MARRIED.String()),
				},
			},
		},
		EmploymentType: &palTypesPb.LamfApplicationAdditionalDetailsInputScreen_DropdownSelectionInputOption{
			Label: getLabelText("EMPLOYMENT TYPE"),
			Dropdown: &palTypesPb.DropdownSelectionOptionComponent{
				Title: getDropdownTitle("Select employment type"),
				Options: []*palTypesPb.DropdownSelectionOptionComponent_Option{
					getDropdownOption("Professional", typesPb.EmploymentType_EMPLOYMENT_TYPE_WORKING_PROFESSIONAL.String()),
					getDropdownOption("Self Employed", typesPb.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED.String()),
					getDropdownOption("Retired", typesPb.EmploymentType_EMPLOYMENT_TYPE_RETIRED.String()),
					getDropdownOption("Housewife", typesPb.EmploymentType_EMPLOYMENT_TYPE_HOUSEWIFE.String()),
					getDropdownOption("Other", typesPb.EmploymentType_EMPLOYMENT_TYPE_OTHERS.String()),
				},
			},
		},
		ResidenceType: &palTypesPb.LamfApplicationAdditionalDetailsInputScreen_DropdownSelectionInputOption{
			Label: getLabelText("RESIDENCE TYPE"),
			Dropdown: &palTypesPb.DropdownSelectionOptionComponent{
				Title: getDropdownTitle("Residence type"),
				Options: []*palTypesPb.DropdownSelectionOptionComponent_Option{
					getDropdownOption("Owned", vgPb.ResidenceType_RESIDENCE_TYPE_OWNED.String()),
					getDropdownOption("Rented", vgPb.ResidenceType_RESIDENCE_TYPE_LEASED.String()),
					getDropdownOption("Other", vgPb.ResidenceType_RESIDENCE_TYPE_OTHERS.String()),
				},
			},
		},
		Cta: &deeplinkPb.Button{
			Text:        helper.GetText("Continue", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   24,
				RightMargin:  24,
				BottomMargin: 16,
			},
			Cta: &deeplinkPb.Cta{
				Text:         "Continue",
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
	})
	return dl
}

func TestProcessor_FiftyfinGetAdditionalKycDetailScreen(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func(md *processorMocks)
		want      *palActivityPb.PalActivityResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "success - additional kyc details screen is shown",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil).Times(2)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actorId1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actorId1",
						EntityId: "userId1",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_Id{
						Id: "userId1",
					},
				}).Return(&usersPb.GetUserResponse{
					User: &usersPb.User{
						Id: "userId1",
						Profile: &usersPb.Profile{
							MotherName: &commontypes.Name{
								FirstName:  "Mother",
								MiddleName: "Test",
								LastName:   "Name",
								Honorific:  "",
							},
							FatherName: &commontypes.Name{
								FirstName:  "Father",
								MiddleName: "Test",
								LastName:   "Name",
								Honorific:  "",
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.loanRequestDao.EXPECT().Update(gomock.Any(), &palPb.LoanRequest{
					Id:         "loanReqId1",
					ActorId:    "actorId1",
					OfferId:    "offerId1",
					NextAction: getKycAdditionalDetailsDl("Father Test Name", "Mother Test Name", "loanReqId1"),
				}, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}).Return(nil)

				m.loanStepExecutionDao.EXPECT().Update(gomock.Any(), &palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				}).Return(nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				},
			},
			wantErr: false,
		},
		{
			name: "failure - failed to get user details",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actorId1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actorId1",
						EntityId: "userId1",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_Id{
						Id: "userId1",
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed - offer expired",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 29, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: timestamppb.New(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST)),
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 24, 0, 0, 0, 0, datetime.IST))
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_EXPIRED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				},
				NextAction: GetLoanOfferExpiryErrorScreen(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initProcessorMocks(ctrl)
			tt.mockFunc(f)

			p := newActivityProcessorWithMocks(f)

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.FiftyfinGetAdditionalKycDetailScreen, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("FiftyfinGetAdditionalKycDetailScreen() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("FiftyfinGetAdditionalKycDetailScreen() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("FiftyfinGetAdditionalKycDetailScreen() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("FiftyfinGetAdditionalKycDetailScreen() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}

func TestProcessor_CheckFiftyfinAdditionalKycDetailsInputStatus(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func(md *processorMocks)
		want      *palActivityPb.PalActivityResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "success - kyc details added",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
					LoanStep: &palPb.LoanStepExecution{
						Id:       "lseId1",
						ActorId:  "actorId1",
						RefId:    "loanReqId1",
						Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:   "orchId1",
						StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details: &palPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
						},
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:        "loanReqId1",
					OrchId:    "orchId1",
					Status:    palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				},
			},
			wantErr: false,
		},
		{
			name: "failure - kyc details input pending",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
					LoanStep: &palPb.LoanStepExecution{
						Id:       "lseId1",
						ActorId:  "actorId1",
						RefId:    "loanReqId1",
						Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:   "orchId1",
						StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details: &palPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
						},
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:        "loanReqId1",
					OrchId:    "orchId1",
					Status:    palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
					SubStatus: palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED,
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure - failed to get loan request status",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
					LoanStep: &palPb.LoanStepExecution{
						Id:       "lseId1",
						ActorId:  "actorId1",
						RefId:    "loanReqId1",
						Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:   "orchId1",
						StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details: &palPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
						},
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(nil, fmt.Errorf("failure"))
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initProcessorMocks(ctrl)
			tt.mockFunc(f)

			p := newActivityProcessorWithMocks(f)

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.CheckFiftyfinAdditionalKycDetailsInputStatus, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckFiftyfinAdditionalKycDetailsInputStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckFiftyfinAdditionalKycDetailsInputStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckFiftyfinAdditionalKycDetailsInputStatus() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("CheckFiftyfinAdditionalKycDetailsInputStatus() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}

func TestProcessor_FiftyfinSubmitAdditionalKycDetail(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func(md *processorMocks)
		want      *palActivityPb.PalActivityResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "success - successfully submit additional kyc details",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
					LoanStep: &palPb.LoanStepExecution{
						Id:       "lseId1",
						ActorId:  "actorId1",
						RefId:    "loanReqId1",
						Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:   "orchId1",
						StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details: &palPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{
								MotherName:     "Mother Name",
								FatherName:     "Father Name",
								MaritalStatus:  "UNMARRIED",
								EmploymentType: "EMPLOYMENT_TYPE_SALARIED",
								ResidenceType:  "RESIDENCE_TYPE_OWNED",
							}},
						},
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{
							MotherName:     "Mother Name",
							FatherName:     "Father Name",
							MaritalStatus:  "UNMARRIED",
							EmploymentType: "EMPLOYMENT_TYPE_SALARIED",
							ResidenceType:  "RESIDENCE_TYPE_OWNED",
						}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), "actorId1").Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					VendorApplicantId: "123456",
				}, nil)

				m.ffVgClient.EXPECT().AddAdditionalKycDetails(gomock.Any(), &vgPb.AddAdditionalKycDetailsRequest{
					EmploymentType: typesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
					MaritalStatus:  typesPb.MaritalStatus_UNMARRIED,
					RelationType:   vgPb.RelationType_RELATION_TYPE_FATHER,
					RelativeName: &commontypes.Name{
						FirstName:  "Father",
						MiddleName: "",
						LastName:   "Name",
						Honorific:  "",
					},
					MotherName: &commontypes.Name{
						FirstName:  "Mother",
						MiddleName: "",
						LastName:   "Name",
						Honorific:  "",
					},
					ResidenceType: vgPb.ResidenceType_RESIDENCE_TYPE_OWNED,
					UserId:        "123456",
				}).Return(&vgPb.AddAdditionalKycDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{
							MotherName:     "Mother Name",
							FatherName:     "Father Name",
							MaritalStatus:  "UNMARRIED",
							EmploymentType: "EMPLOYMENT_TYPE_SALARIED",
							ResidenceType:  "RESIDENCE_TYPE_OWNED",
						}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				},
			},
			wantErr: false,
		},
		{
			name: "success - failed to submit additional kyc details",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "orchId1",
					},
					LoanStep: &palPb.LoanStepExecution{
						Id:       "lseId1",
						ActorId:  "actorId1",
						RefId:    "loanReqId1",
						Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:   "orchId1",
						StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
						Details: &palPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{
								MotherName:     "Mother Name",
								FatherName:     "Father Name",
								MaritalStatus:  "UNMARRIED",
								EmploymentType: "EMPLOYMENT_TYPE_SALARIED",
								ResidenceType:  "RESIDENCE_TYPE_OWNED",
							}},
						},
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "loanReqId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId1",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{
							MotherName:     "Mother Name",
							FatherName:     "Father Name",
							MaritalStatus:  "UNMARRIED",
							EmploymentType: "EMPLOYMENT_TYPE_SALARIED",
							ResidenceType:  "RESIDENCE_TYPE_OWNED",
						}},
					},
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_KYC,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:      "loanReqId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
				}, nil)

				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestamppb.New(time.Date(2024, 1, 22, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
				}, nil)

				m.time.EXPECT().Now().Return(time.Date(2024, 1, 18, 0, 0, 0, 0, datetime.IST))

				m.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), "actorId1").Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					VendorApplicantId: "123456",
				}, nil)

				m.ffVgClient.EXPECT().AddAdditionalKycDetails(gomock.Any(), &vgPb.AddAdditionalKycDetailsRequest{
					EmploymentType: typesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
					MaritalStatus:  typesPb.MaritalStatus_UNMARRIED,
					RelationType:   vgPb.RelationType_RELATION_TYPE_FATHER,
					RelativeName: &commontypes.Name{
						FirstName:  "Father",
						MiddleName: "",
						LastName:   "Name",
						Honorific:  "",
					},
					MotherName: &commontypes.Name{
						FirstName:  "Mother",
						MiddleName: "",
						LastName:   "Name",
						Honorific:  "",
					},
					ResidenceType: vgPb.ResidenceType_RESIDENCE_TYPE_OWNED,
					UserId:        "123456",
				}).Return(&vgPb.AddAdditionalKycDetailsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initProcessorMocks(ctrl)
			tt.mockFunc(f)

			p := newActivityProcessorWithMocks(f)

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.FiftyfinSubmitAdditionalKycDetail, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("FiftyfinSubmitAdditionalKycDetail() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("FiftyfinSubmitAdditionalKycDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("FiftyfinSubmitAdditionalKycDetail() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("FiftyfinSubmitAdditionalKycDetail() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}
