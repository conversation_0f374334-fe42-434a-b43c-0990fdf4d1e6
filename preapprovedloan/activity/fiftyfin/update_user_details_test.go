package fiftyfin_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

func GetLAMFPermanentFailureScreen(subtitle string) *deeplinkPb.Deeplink {
	loanLandingScreen := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
			PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
				LoanHeader: &pal_enums.LoanHeader{
					LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
					Vendor:      pal_enums.Vendor_FIFTYFIN,
				},
			},
		},
	}
	dl, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, &palTypesPb.LoansFailureScreen{
		LoanHeader: &pal_enums.LoanHeader{
			LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      pal_enums.Vendor_FIFTYFIN,
		},
		PageHeader: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/filled_triangle_warning.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
			TitleText:     commontypes.GetPlainStringText("Looks like there was an issue").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
			SubtitleText:  commontypes.GetPlainStringText(subtitle).WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
		},
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText("Back", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			Cta: &deeplinkPb.Cta{
				Deeplink:     loanLandingScreen,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Text:         "Back",
			},
			Padding: helper.GetButtonPadding(12, 24, 12, 24),
			Margin:  helper.GetButtonMargin(16, 24, 24, 24),
		},
		AnalyticsScreenName: analytics.AnalyticsScreenName_LAMF_PERMANENT_FAILURE_FULL_SCREEN,
		Components: []*palTypesPb.LoansFailureScreenComponent{
			{
				Component: &palTypesPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palTypesPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement:   commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/filled_triangle_warning.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
							TitleText:       commontypes.GetPlainStringText("Looks like there was an issue").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
							SubtitleText:    commontypes.GetPlainStringText(subtitle).WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	})
	return dl
}

func TestProcessor_FiftyfinUpdateUserDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func(md *processorMocks)
		want      *palActivityPb.PalActivityResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "success - signup a new user",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.UserSignupResponse_Data{
						UserId: 123456,
					},
				}, nil)

				m.ffVgClient.EXPECT().LinkPan(gomock.Any(), &fiftyfin.LinkPanRequest{
					UserId:  123456,
					Pan:     "**********",
					PanName: "Test User Name",
					Dob:     &date.Date{Year: 1997, Month: 2, Day: 9},
				}).Return(&fiftyfin.LinkPanResponse{
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)

				m.loanApplicantDao.EXPECT().Update(gomock.Any(), &palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, []palPb.LoanApplicantFieldMask{
					palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_SUB_STATUS,
					palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID,
				}).Return(nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - error in user signup vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure - phone number already exists error in user sign up vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.NewStatusWithoutDebug(uint32(fiftyfin.UserSignupResponse_PHONE_NUMBER_ALREADY_EXISTS), "phone number already exists"),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				NextAction:    GetLAMFPermanentFailureScreen("User with the registered phone number already exists"),
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - email already exists error in user sign up vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.NewStatusWithoutDebug(uint32(fiftyfin.UserSignupResponse_EMAIl_ALREADY_ALREADY_EXISTS), "email already exists"),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				NextAction:    GetLAMFPermanentFailureScreen("User with the registered email id already exists"),
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - internal error in link pan vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.UserSignupResponse_Data{
						UserId: 123456,
					},
				}, nil)

				m.ffVgClient.EXPECT().LinkPan(gomock.Any(), &fiftyfin.LinkPanRequest{
					UserId:  123456,
					Pan:     "**********",
					PanName: "Test User Name",
					Dob:     &date.Date{Year: 1997, Month: 2, Day: 9},
				}).Return(&fiftyfin.LinkPanResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure - dob mismatch in pan - link pan vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.UserSignupResponse_Data{
						UserId: 123456,
					},
				}, nil)

				m.ffVgClient.EXPECT().LinkPan(gomock.Any(), &fiftyfin.LinkPanRequest{
					UserId:  123456,
					Pan:     "**********",
					PanName: "Test User Name",
					Dob:     &date.Date{Year: 1997, Month: 2, Day: 9},
				}).Return(&fiftyfin.LinkPanResponse{
					Status: &rpc.Status{
						Code: uint32(fiftyfin.LinkPanResponse_DOB_MISMATCH_WITH_PAN),
					},
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DOB_DOES_NOT_MATCH_PAN_DATA,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				NextAction:    GetLAMFPermanentFailureScreen("Date of birth does not match with PAN data"),
			},
			wantErr: false,
		},
		{
			name: "success - update user details for existing user",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)

				m.ffVgClient.EXPECT().UpdateUserBulk(gomock.Any(), &fiftyfin.UpdateUserBulkRequest{
					UserId: "123456",
					Email:  "<EMAIL>",
					Phone: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				}).Return(&fiftyfin.UpdateUserBulkResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "success - phone number and email don't exist in loan applicant for existing user",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: nil,
						EmailId:     "",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - email doesn't exist in loan applicant for existing user",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: nil,
						EmailId:     "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - phone number doesn't exist in loan applicant for existing user",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						EmailId: "",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - error in fetch user vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure - internal error in update user details vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)

				m.ffVgClient.EXPECT().UpdateUserBulk(gomock.Any(), &fiftyfin.UpdateUserBulkRequest{
					UserId: "123456",
					Email:  "<EMAIL>",
					Phone: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				}).Return(&fiftyfin.UpdateUserBulkResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure - email already exists error in update user details vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)

				m.ffVgClient.EXPECT().UpdateUserBulk(gomock.Any(), &fiftyfin.UpdateUserBulkRequest{
					UserId: "123456",
					Email:  "<EMAIL>",
					Phone: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				}).Return(&fiftyfin.UpdateUserBulkResponse{
					Status: rpc.NewStatusWithoutDebug(uint32(fiftyfin.UpdateUserBulkResponse_EMAIL_ALREADY_EXISTS), "email already exists"),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				NextAction:    GetLAMFPermanentFailureScreen("User with the registered email id already exists"),
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - phone number already exists error in update user details vg api",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    false,
					IsActiveLoanFiftyfin: false,
				}, nil)

				m.ffVgClient.EXPECT().UpdateUserBulk(gomock.Any(), &fiftyfin.UpdateUserBulkRequest{
					UserId: "123456",
					Email:  "<EMAIL>",
					Phone: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				}).Return(&fiftyfin.UpdateUserBulkResponse{
					Status: rpc.NewStatusWithoutDebug(uint32(fiftyfin.UpdateUserBulkResponse_PHONE_NUMBER_ALREADY_EXISTS), "phone already exists"),
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				NextAction:    GetLAMFPermanentFailureScreen("User with the registered phone number already exists"),
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - has active loan with bajaj - user had already signed up",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    true,
					IsActiveLoanFiftyfin: false,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ACTIVE_LOAN_WITH_VENDOR,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				NextAction:    GetLAMFPermanentFailureScreen("There is an active loan with bajaj."),
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - has active loan with bajaj - user sign up",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:        "loanApplicantId1",
					Status:    palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus: palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: "actorId1",
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actorId1",
						EntityId: "userId",
					},
				}, nil)

				m.usersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_Id{
						Id: "userId",
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						Profile: &user.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
							PAN:   "**********",
							PanName: &commontypes.Name{
								FirstName:  "Test",
								MiddleName: "User",
								LastName:   "Name",
							},
							DateOfBirth: &date.Date{
								Year:  1997,
								Month: 2,
								Day:   9,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)

				m.ffVgClient.EXPECT().UserSignup(gomock.Any(), &fiftyfin.UserSignupRequest{
					Name: &commontypes.Name{
						FirstName:  "Test",
						MiddleName: "User",
						LastName:   "Name",
					},
					Email: "<EMAIL>",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					Dob: &date.Date{
						Year:  1997,
						Month: 2,
						Day:   9,
					},
				}).Return(&fiftyfin.UserSignupResponse{
					Status: rpc.StatusOk(),
					Data: &fiftyfin.UserSignupResponse_Data{
						UserId: 123456,
					},
				}, nil)

				m.ffVgClient.EXPECT().LinkPan(gomock.Any(), &fiftyfin.LinkPanRequest{
					UserId:  123456,
					Pan:     "**********",
					PanName: "Test User Name",
					Dob:     &date.Date{Year: 1997, Month: 2, Day: 9},
				}).Return(&fiftyfin.LinkPanResponse{
					Status: rpc.StatusOk(),
				}, nil)

				m.loanApplicantDao.EXPECT().Update(gomock.Any(), &palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, []palPb.LoanApplicantFieldMask{
					palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_SUB_STATUS,
					palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID,
				}).Return(nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status:               rpc.StatusOk(),
					IsActiveLoanBajaj:    true,
					IsActiveLoanFiftyfin: false,
				}, nil)
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus:  palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ACTIVE_LOAN_WITH_VENDOR,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				},
				NextAction: GetLAMFPermanentFailureScreen("There is an active loan with bajaj."),
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "failure - dedupe api failed - user already signed up",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: &palPb.LoanStepExecution{
						Id:         "lseId1",
						ActorId:    "actorId1",
						RefId:      "loanReqId1",
						Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:     "orchId1",
						StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
						Details:    nil,
						Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						SubStatus:  0,
						GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
					},
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "orchId1",
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:         "lseId1",
					ActorId:    "actorId1",
					RefId:      "loanReqId1",
					Flow:       palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:     "orchId1",
					StepName:   palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION,
					Details:    nil,
					Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					SubStatus:  0,
					GroupStage: palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "loanReqId1").Return(&palPb.LoanRequest{
					Id:          "loanReqId1",
					ActorId:     "actorId1",
					OfferId:     "offerId1",
					OrchId:      "orchId1",
					Vendor:      palPb.Vendor_FIFTYFIN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN,
					palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{
					Id:                "loanApplicantId1",
					Status:            palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
					SubStatus:         palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR,
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId: "<EMAIL>",
					},
				}, nil)

				m.ffVgClient.EXPECT().FetchUser(gomock.Any(), &fiftyfin.FetchUserRequest{
					IdType: fiftyfin.IdType_ID_TYPE_USER_ID,
					UserId: 123456,
				}).Return(&fiftyfin.FetchUserResponse{
					Status: rpc.StatusOk(),
					UserData: &fiftyfin.FetchUserResponse_UserData{
						Email: "<EMAIL>",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678909,
						},
					},
				}, nil)

				m.ffVgClient.EXPECT().DedupeCheck(gomock.Any(), &fiftyfin.DedupeCheckRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					UserId: 123456,
				}).Return(&fiftyfin.DedupeCheckResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initProcessorMocks(ctrl)
			tt.mockFunc(f)

			p := newActivityProcessorWithMocks(f)

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.FiftyfinUpdateUserDetails, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("FiftyfinUpdateUserDetails() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("FiftyfinUpdateUserDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("FiftyfinUpdateUserDetails() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("FiftyfinUpdateUserDetails() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}
