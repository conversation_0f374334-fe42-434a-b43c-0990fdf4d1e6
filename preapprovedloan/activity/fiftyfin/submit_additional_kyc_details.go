package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	typesPb "github.com/epifi/gamma/api/typesv2"
	fiftyfinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

// FiftyfinSubmitAdditionalKycDetail fetches additional kyc data to be submitted and performs the VG call
func (p *Processor) FiftyfinSubmitAdditionalKycDetailV2(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	return palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{LoanStep: lse}

		// handle loan offer expiry
		offerExpiredRes, err := p.handleLoanOfferExpiry(ctx, req.GetVendor(), req.GetLoanProgram(), lse, res)
		if err != nil {
			if errors.Is(err, ErrorLoanOfferExpired) {
				lg.Error("offer has expired")
				return offerExpiredRes, nil
			}
			lg.Error("error in handleLoanOfferExpiry", zap.Error(err))
			return nil, err
		}

		// Get user submitted details from lse and convert to vg compliant enum
		// Get parental details from LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE step
		parentalLse, parentalErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(), lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE)
		if parentalErr != nil {
			lg.Error("error while getting parental details lse", zap.Error(parentalErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, parentalErr.Error())
		}
		parentalData := parentalLse.GetDetails().GetApplicantData()
		if parentalData == nil {
			lg.Error("got empty applicant data from parental details lse")
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "got empty applicant data from parental details lse")
		}

		// Get personal details from LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE step
		personalLse, personalErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(), lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE)
		if personalErr != nil {
			lg.Error("error while getting personal details lse", zap.Error(personalErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, personalErr.Error())
		}
		personalData := personalLse.GetDetails().GetApplicantData()
		if personalData == nil {
			lg.Error("got empty onboarding data from personal details lse")
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "got empty onboarding data from personal details lse")
		}

		additionalKycDetReq := &fiftyfinPb.AddAdditionalKycDetailsRequest{
			RelationType:   fiftyfinPb.RelationType_RELATION_TYPE_FATHER,
			RelativeName:   (&commontypes.Name{}).Parse(parentalData.GetFatherName()),
			MotherName:     (&commontypes.Name{}).Parse(parentalData.GetMotherName()),
			EmploymentType: Enum(personalData.GetEmploymentType(), typesPb.EmploymentType_value, typesPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED),
			MaritalStatus:  Enum(personalData.GetMaritalStatus(), typesPb.MaritalStatus_value, typesPb.MaritalStatus_UNSPECIFIED),
			ResidenceType:  fiftyfinPb.ResidenceType(Enum(personalData.GetResidenceType(), typesPb.ResidenceType_value, typesPb.ResidenceType_RESIDENCE_TYPE_UNSPECIFIED)),
		}
		// Get vendor applicant id
		loanApplicantRes, loanApplicantErr := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
		if loanApplicantErr != nil {
			lg.Error("error while getting loan applicant for the user", zap.Error(loanApplicantErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, loanApplicantErr.Error())
		}
		additionalKycDetReq.UserId = loanApplicantRes.GetVendorApplicantId()

		// Perform vendor call
		additionalKycDetailResp, additionalKycDetailErr := p.ffVgClient.AddAdditionalKycDetails(ctx, additionalKycDetReq)
		if rpcErr := epifigrpc.RPCError(additionalKycDetailResp, additionalKycDetailErr); rpcErr != nil {
			lg.Error("failed to initiate kyc", zap.Error(rpcErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, rpcErr.Error())
		}

		return res, nil
	})
}
