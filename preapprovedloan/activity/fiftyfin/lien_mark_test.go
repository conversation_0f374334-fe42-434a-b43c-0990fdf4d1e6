package fiftyfin_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/money"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	ffVgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	fiftyfinPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/gamma/preapprovedloan/activity/fiftyfin"
)

func TestAddAllUnitsToMap(t *testing.T) {
	t.Parallel()

	type args struct {
		otpTypeToMfDetailsMap map[palPb.OtpType]map[string]float64
		fiftyfinPortfolio     *fiftyfinPb.MfPortfolio
		isin                  string
	}
	tests := []struct {
		name string
		args args
		want map[palPb.OtpType]map[string]float64
	}{
		{
			args: args{
				isin: "i1",
				otpTypeToMfDetailsMap: map[palPb.OtpType]map[string]float64{
					palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK:  {},
					palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {},
				},
				fiftyfinPortfolio: &fiftyfinPb.MfPortfolio{
					CamsApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          1,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    10,
							Price:       20,
						},
						{
							Id:          2,
							Isin:        "i1",
							FolioNumber: "2",
							Quantity:    20,
							Price:       20,
						},
						{
							Id:          3,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    100,
							Price:       1,
						},
					},
					KarvyApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          4,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    30,
							Price:       20,
						},
						{
							Id:          5,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    50,
							Price:       1,
						},
						{
							Id:          6,
							Isin:        "isin4",
							FolioNumber: "4",
						},
					},
					CamsUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          7,
							Isin:        "i4",
							FolioNumber: "2",
							Quantity:    100,
							Price:       50,
						},
						{
							Id:          8,
							Isin:        "i1",
							FolioNumber: "40",
							Quantity:    20,
							Price:       20,
						},
					},
					KarvyUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          9,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    1,
							Price:       20,
						},
					},
				},
			},
			want: map[palPb.OtpType]map[string]float64{
				palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK: {
					"1": 10,
					"2": 20,
				},
				palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {
					"4": 30,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fiftyfin.AddAllUnitsToMap(tt.args.otpTypeToMfDetailsMap, tt.args.fiftyfinPortfolio, tt.args.isin)
			if !reflect.DeepEqual(tt.args.otpTypeToMfDetailsMap, tt.want) {
				t.Errorf("AddAllUnitsToMap() got = %v, want %v", tt.args.otpTypeToMfDetailsMap, tt.want)
			}
		})
	}
}

func TestAddMaxNUnitsToMap(t *testing.T) {
	t.Parallel()
	type args struct {
		otpTypeToMfDetailsMap map[palPb.OtpType]map[string]float64
		fiftyfinPortfolio     *fiftyfinPb.MfPortfolio
		isin                  string
		maxUnits              float64
	}
	tests := []struct {
		name string
		args args
		want map[palPb.OtpType]map[string]float64
	}{
		{
			name: "partial units of a single facilitator included",
			args: args{
				isin:     "i1",
				maxUnits: 24.34,
				otpTypeToMfDetailsMap: map[palPb.OtpType]map[string]float64{
					palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK:  {},
					palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {},
				},
				fiftyfinPortfolio: &fiftyfinPb.MfPortfolio{
					CamsApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          1,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    10,
							Price:       20,
						},
						{
							Id:          2,
							Isin:        "i1",
							FolioNumber: "2",
							Quantity:    20,
							Price:       20,
						},
						{
							Id:          3,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    100,
							Price:       1,
						},
					},
					KarvyApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          4,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    30,
							Price:       20,
						},
						{
							Id:          5,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    50,
							Price:       1,
						},
						{
							Id:          6,
							Isin:        "isin4",
							FolioNumber: "4",
						},
					},
					CamsUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          7,
							Isin:        "i4",
							FolioNumber: "2",
							Quantity:    100,
							Price:       50,
						},
						{
							Id:          8,
							Isin:        "i1",
							FolioNumber: "40",
							Quantity:    20,
							Price:       20,
						},
					},
					KarvyUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          9,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    1,
							Price:       20,
						},
					},
				},
			},
			want: map[palPb.OtpType]map[string]float64{
				palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK: {
					"1": 10,
					"2": 14.34,
				},
				palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {},
			},
		},
		{
			name: "all units of one facilitator and partial of other facilitator included",
			args: args{
				isin:     "i1",
				maxUnits: 31.548,
				otpTypeToMfDetailsMap: map[palPb.OtpType]map[string]float64{
					palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK:  {},
					palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {},
				},
				fiftyfinPortfolio: &fiftyfinPb.MfPortfolio{
					CamsApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          1,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    10,
							Price:       20,
						},
						{
							Id:          2,
							Isin:        "i1",
							FolioNumber: "2",
							Quantity:    20,
							Price:       20,
						},
						{
							Id:          3,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    100,
							Price:       1,
						},
					},
					KarvyApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          4,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    30,
							Price:       20,
						},
						{
							Id:          5,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    50,
							Price:       1,
						},
						{
							Id:          6,
							Isin:        "isin4",
							FolioNumber: "4",
						},
					},
					CamsUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          7,
							Isin:        "i4",
							FolioNumber: "2",
							Quantity:    100,
							Price:       50,
						},
						{
							Id:          8,
							Isin:        "i1",
							FolioNumber: "40",
							Quantity:    20,
							Price:       20,
						},
					},
					KarvyUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          9,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    1,
							Price:       20,
						},
					},
				},
			},
			want: map[palPb.OtpType]map[string]float64{
				palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK: {
					"1": 10,
					"2": 20,
				},
				palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {
					"4": 1.548,
				},
			},
		},
		{
			name: "all units of both facilitator included",
			args: args{
				isin:     "i1",
				maxUnits: 80,
				otpTypeToMfDetailsMap: map[palPb.OtpType]map[string]float64{
					palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK:  {},
					palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {},
				},
				fiftyfinPortfolio: &fiftyfinPb.MfPortfolio{
					CamsApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          1,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    10,
							Price:       20,
						},
						{
							Id:          2,
							Isin:        "i1",
							FolioNumber: "2",
							Quantity:    20,
							Price:       20,
						},
						{
							Id:          3,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    100,
							Price:       1,
						},
					},
					KarvyApproved: []*fiftyfinPb.MfHolding{
						{
							Id:          4,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    30,
							Price:       20,
						},
						{
							Id:          5,
							Isin:        "i2",
							FolioNumber: "2",
							Quantity:    50,
							Price:       1,
						},
						{
							Id:          6,
							Isin:        "isin4",
							FolioNumber: "4",
						},
					},
					CamsUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          7,
							Isin:        "i4",
							FolioNumber: "2",
							Quantity:    100,
							Price:       50,
						},
						{
							Id:          8,
							Isin:        "i1",
							FolioNumber: "40",
							Quantity:    20,
							Price:       20,
						},
					},
					KarvyUnapproved: []*fiftyfinPb.MfHolding{
						{
							Id:          9,
							Isin:        "i1",
							FolioNumber: "1",
							Quantity:    1,
							Price:       20,
						},
					},
				},
			},
			want: map[palPb.OtpType]map[string]float64{
				palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK: {
					"1": 10,
					"2": 20,
				},
				palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK: {
					"4": 30,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fiftyfin.AddMaxNUnitsToMap(tt.args.otpTypeToMfDetailsMap, tt.args.fiftyfinPortfolio, tt.args.isin, tt.args.maxUnits)
			if !reflect.DeepEqual(tt.args.otpTypeToMfDetailsMap, tt.want) {
				t.Errorf("AddMaxNUnitsToMap() got = %v, want %v", tt.args.otpTypeToMfDetailsMap, tt.want)
			}
		})
	}
}

func TestProcessor_ValidateLoanAmount(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.LamfValidateLoanAmountResponse
		mockFunc  func(md *processorMocks)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "transient failure, error in getting loan applicant",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure, error in converting user id to int",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "non-int-applicant-id"}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure, error fetching loan portfolio from vendor",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure, error in getting loan request",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{
					Status: rpc.StatusOk(),
					Portfolio: &fiftyfinPb.MfLoanPortfolio{
						Aggregate: &fiftyfinPb.MfPortfolioAggregate{
							MaxLoanAmount: 1000,
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure, error in subtracting money values",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{
					Status: rpc.StatusOk(),
					Portfolio: &fiftyfinPb.MfLoanPortfolio{
						Aggregate: &fiftyfinPb.MfPortfolioAggregate{
							MaxLoanAmount: 1000,
						},
					},
				}, nil)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), gomock.Any()).Return(nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanRequest{Details: &palPb.LoanRequestDetails{
					LoanInfo: &palPb.LoanRequestDetails_LoanInfo{
						Amount: money.ParseFloat(2000.0, "random_code"),
					},
				}}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "zero loan value lien approved",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{
					Status: rpc.StatusOk(),
					Portfolio: &fiftyfinPb.MfLoanPortfolio{
						Aggregate: &fiftyfinPb.MfPortfolioAggregate{
							MaxLoanAmount: 0,
						},
					},
				}, nil)
			},
			wantErr: false,
			want: &palActivityPb.LamfValidateLoanAmountResponse{
				LoanStep: &palPb.LoanStepExecution{
					Status:    palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
					SubStatus: palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_LIEN_MARKED_PORTFOLIO,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				},
				NextAction:          nil,
				PartialLienApproved: false,
			},
		},
		{
			name: "failure, applied loan amount is more than eligible amount",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusOk(), Portfolio: &fiftyfinPb.MfLoanPortfolio{
					Aggregate: &fiftyfinPb.MfPortfolioAggregate{
						MaxLoanAmount: 1000.0,
						ApprovedValue: 2000.0,
					},
				}}, nil)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), gomock.Any()).Return(nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanRequest1, nil).AnyTimes()
			},
			wantErr: false,
			want: &palActivityPb.LamfValidateLoanAmountResponse{
				LoanStep: &palPb.LoanStepExecution{
					Status:    palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION,
					SubStatus: palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LIEN_MARKED_PORTFOLIO,
				},
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				},
				NextAction:          nil,
				PartialLienApproved: false,
			},
		},
		{
			name: "success case, application with valid loan amount",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &palPb.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&palPb.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusOk(), Portfolio: &fiftyfinPb.MfLoanPortfolio{
					Aggregate: &fiftyfinPb.MfPortfolioAggregate{
						MaxLoanAmount: 5000.0,
					},
				}}, nil)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), gomock.Any()).Return(nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanRequest1, nil)
			},
			wantErr: false,
			want: &palActivityPb.LamfValidateLoanAmountResponse{
				LoanStep: &palPb.LoanStepExecution{
					Status: palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
				},
				NextAction:          nil,
				PartialLienApproved: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockFields := initProcessorMocks(ctrl)
			tt.mockFunc(mockFields)
			p := newActivityProcessorWithMocks(mockFields)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.LamfValidateLoanAmountResponse
			got, err := env.ExecuteActivity(palNs.ValidateLoanAmount, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ValidateLoanAmount() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ValidateLoanAmount() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ValidateLoanAmount() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("ValidateLoanAmount() got = %v, want %v, diff %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}
