package fiftyfin_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/base64"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/investment/mutualfund"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	ffVgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	ffVendorsPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func getVerificationV2IntermediateScreenDl(lrId, lseId string) *deeplinkPb.Deeplink {
	marshalledPayload, _ := protojson.Marshal(&palTypesPb.RecordUserActionDetails{
		Details: &palTypesPb.RecordUserActionDetails_LamfLinkMfDetails{
			LamfLinkMfDetails: &palTypesPb.LamfLinkMfDetails{
				ActionIdentifier: palPb.RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF.String(),
			},
		},
	})
	encodedPayload := base64.StdEncoding.EncodeToString(marshalledPayload)
	verifyMfRecordActionDl, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_RECORD_USER_ACTION_SCREEN, &palTypesPb.LoansRecordUserActionScreenOptions{
		LoanHeader: &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      palFeEnumsPb.Vendor_FIFTYFIN,
		},
		Screen:        deeplinkPb.Screen_LAMF_LINK_MF_SCREEN,
		ActionDetails: encodedPayload,
		LoanRequestId: lrId,
		LoanStepId:    lseId,
	})

	mfVerificationFaqDl, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LAMF_FUND_VERIFICATION_BOTTOM_SHEET, &palTypesPb.LamfFundVerificationBottomSheet{
		Title: &widget.VisualElementTitleSubtitleElement{
			VisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/lamf/verify_mutual_funds.png", 80, 80),
			TitleText:       commontypes.GetTextFromStringFontColourFontStyle("Why is this needed?", "#313234", commontypes.FontStyle_HEADLINE_L),
			SubtitleText:    nil,
			BackgroundColor: "#FFFFFF",
		},
		Questions: []*ui.VerticalKeyValuePair{
			{
				Value:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Some of your mutual funds are not linked to the phone number or email ID that you have provided in the loan application.", "#6A6D70", commontypes.FontStyle_BODY_S)),
				VerticalPaddingBtwTitleValue: 8,
			},
			{
				Value:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Your mutual funds need to be linked to the same phone number and email ID in order to take this loan", "#6A6D70", commontypes.FontStyle_BODY_S)),
				VerticalPaddingBtwTitleValue: 8,
			},
		},
		Cta: &deeplinkPb.Button{
			Text:        commontypes.GetTextFromStringFontColourFontStyle("Okay", "#FFFFFF", commontypes.FontStyle_BUTTON_M).WithBgColor("#00B899"),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   32,
				RightPadding:  32,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Cta: &deeplinkPb.Cta{
				Text:         "Okay",
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
	})

	dl, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LAMF_LINK_MF_SCREEN, &palTypesPb.LamfLinkScreenOptions{
		LoanHeader: &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      palFeEnumsPb.Vendor_FIFTYFIN,
		},
		Title:    commontypes.GetPlainStringText("Link Mutual Funds").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#313234"),
		Subtitle: commontypes.GetPlainStringText("To make your Mutual Funds eligible for a loan, link them to your phone number & email ID").WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#929599"),
		BeforeComponents: []*palTypesPb.LamfLinkScreenOptions_Component{
			{
				Component: &palTypesPb.LamfLinkScreenOptions_Component_LinkedMfComponent{
					LinkedMfComponent: &palTypesPb.LinkedMFComponent{
						Icon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/link_failed.png", 48, 48),
						Title: ui.NewITC().WithTexts(
							commontypes.GetPlainStringText("Linking 1 fund to").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#6A6D70"),
							commontypes.GetPlainStringText(fmt.Sprintf(" +91 98765 43210")).WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
						),
						Subtitle:  commontypes.GetPlainStringText("It will take up to 2 hours for MFCentral to process").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#929599"),
						BgColor:   widget.GetBlockBackgroundColour("#F6F9FD"),
						TopMargin: 64,
					},
				},
			},
		},
		AfterComponents: []*palTypesPb.LamfLinkScreenOptions_Component{
			{
				Component: &palTypesPb.LamfLinkScreenOptions_Component_LinkedMfComponent{
					LinkedMfComponent: &palTypesPb.LinkedMFComponent{
						Icon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/link_in_progress_orange.png", 48, 48),
						Title: ui.NewITC().WithTexts(
							commontypes.GetPlainStringText("Linking 1 fund to").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#6A6D70"),
							commontypes.GetPlainStringText(fmt.Sprintf(" +91 98765 43210")).WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
						),
						Subtitle:  commontypes.GetPlainStringText("Request submitted to partner.").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#929599"),
						BgColor:   widget.GetBlockBackgroundColour("#F6F9FD"),
						TopMargin: 64,
					},
				},
			},
		},
		PartnerLogo: ui.NewITC().WithRightVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/usstocks_images/epifi-wealth-powered.png", 15, 150),
		CtaLink: &deeplinkPb.Button{
			Text:        commontypes.GetTextFromStringFontColourFontStyle("Check link status", "#FFFFFF", commontypes.FontStyle_BUTTON_M).WithBgColor("#00B899"),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Cta: &deeplinkPb.Cta{
				Text:         "Check link status",
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Deeplink:     verifyMfRecordActionDl,
			},
		},
		BeforeErrorBanner: &palTypesPb.ErrorBanner{
			Id: "uuid1",
			Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/waiting_icon_orange.png").WithProperties(&commontypes.VisualElementProperties{
				Height: 24,
				Width:  24,
			}),
			Title:       commontypes.GetPlainStringText("Some of your funds have not been linked yet").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#6D3149"),
			Description: commontypes.GetPlainStringText(fmt.Sprintf("Please try again at 12:01 am. We will notify you.")).WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#C0723D"),
			BgColor:     widget.GetBlockBackgroundColour("#FBF3E6"),
			ShowOnce:    false,
		},
		AfterErrorBanner: &palTypesPb.ErrorBanner{
			Id: "uuid1",
			Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/done_icon_green.png").WithProperties(&commontypes.VisualElementProperties{
				Height: 24,
				Width:  24,
			}),
			Title:       commontypes.GetPlainStringText("Your funds would have been veirfied by now.").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#37522A"),
			Description: commontypes.GetPlainStringText("Please try again to check the link status.").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#37522A"),
			BgColor:     widget.GetBlockBackgroundColour("#EDF5EB"),
			ShowOnce:    false,
		},
		IsCtaLinkEnabled: false,
		LoanReqId:        lrId,
		LoanStepId:       lseId,
		HelpText: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Why is this needed?", "#00B899", commontypes.FontStyle_HEADLINE_S)).
			WithDeeplink(mfVerificationFaqDl),
		ComponentChangeTime: timestampPb.New(time.Date(2024, 5, 7, 0, 1, 0, 0, datetime.IST)),
		AnalyticsScreenName: "LAMF_MF_INTERMEDIATE_LINK_SCREEN",
	})
	return dl
}

func TestProcessor_FiftyfinFundVerificationV2(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.FiftyfinFundVerificationRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func(md *processorMocks)
		want      *palActivityPb.FiftyfinFundVerificationResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "success - some funds not linked - fiftyfin portfolio fetched once",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.FiftyfinFundVerificationRequest{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
					LoanStep: &palPb.LoanStepExecution{
						Id:       "lseId1",
						ActorId:  "actorId1",
						RefId:    "lrId1",
						Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						OrchId:   "orchId2",
						StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS,
						Status:   palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
						Details: &palPb.LoanStepExecutionDetails{
							Details: &palPb.LoanStepExecutionDetails_LoanDetailsVerificationData{
								LoanDetailsVerificationData: &palPb.LoanDetailsVerificationData{
									Data: &palPb.LoanDetailsVerificationData_Lamf{
										Lamf: &palPb.LamfLoanDetailsVerificationData{
											UserActionTaken: false,
											PfFetch_Data: []*palPb.LamfLoanDetailsVerificationData_PfFetchData{
												{
													ReqId:          "reqId1",
													FetchCompleted: true,
													IsFetchSuccess: false,
													CompletionTime: nil,
												},
												{
													ReqId:          "reqId2",
													FetchCompleted: true,
													IsFetchSuccess: true,
													CompletionTime: timestampPb.New(time.Date(2024, 5, 6, 0, 0, 0, 0, datetime.IST)),
												},
											},
											MobileLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfMobileLinkDetails{
												NewMobile:   &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
												LinkedEmail: "<EMAIL>",
												Folios: []*palPb.LamfLoanDetailsVerificationData_FolioData{
													{
														FolioNumber: "1",
														Isin:        "isin1",
														Quantity:    10,
														Amc:         "P",
													},
													{
														FolioNumber: "2",
														Isin:        "isin2",
														Quantity:    10,
														Amc:         "101",
													},
												},
												NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
													{
														ClientReqId: "nftClientReqId1",
														CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
													},
												},
											},
											EmailLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfEmailLinkDetails{
												NewEmail:     "<EMAIL>",
												LinkedMobile: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
												Folios: []*palPb.LamfLoanDetailsVerificationData_FolioData{
													{
														FolioNumber: "3",
														Isin:        "isin1",
														Quantity:    10,
														Amc:         "P",
													},
												},
												NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
													{
														ClientReqId: "nftClientReqId1",
														CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
													},
												},
											},
											UserAction: &palPb.LamfLoanDetailsVerificationData_UserAction{
												Identifier: palPb.RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF,
											},
										},
									},
								},
							},
						},
					},
				},
			},
			mockFunc: func(m *processorMocks) {
				m.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lseId1").Return(&palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "lrId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId2",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS,
					Status:   palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_LoanDetailsVerificationData{
							LoanDetailsVerificationData: &palPb.LoanDetailsVerificationData{
								Data: &palPb.LoanDetailsVerificationData_Lamf{
									Lamf: &palPb.LamfLoanDetailsVerificationData{
										UserActionTaken: false,
										PfFetch_Data: []*palPb.LamfLoanDetailsVerificationData_PfFetchData{
											{
												ReqId:          "reqId1",
												FetchCompleted: true,
												IsFetchSuccess: false,
												CompletionTime: nil,
											},
											{
												ReqId:          "reqId2",
												FetchCompleted: true,
												IsFetchSuccess: true,
												CompletionTime: timestampPb.New(time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST)),
											},
										},
										MobileLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfMobileLinkDetails{
											NewMobile:   &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
											LinkedEmail: "<EMAIL>",
											Folios: []*palPb.LamfLoanDetailsVerificationData_FolioData{
												{
													FolioNumber: "1",
													Isin:        "isin1",
													Quantity:    10,
													Amc:         "P",
												},
												{
													FolioNumber: "2",
													Isin:        "isin1",
													Quantity:    100,
													Amc:         "P",
												},
												{
													FolioNumber: "3",
													Isin:        "isin1",
													Quantity:    100,
													Amc:         "P",
												},
											},
											NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
												{
													ClientReqId: "nftClientReqId1",
													CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
												},
											},
										},
										EmailLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfEmailLinkDetails{
											NewEmail:     "<EMAIL>",
											LinkedMobile: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
											Folios: []*palPb.LamfLoanDetailsVerificationData_FolioData{
												{
													FolioNumber: "1",
													Isin:        "isin2",
													Quantity:    100,
													Amc:         "101",
												},
												{
													FolioNumber: "3",
													Isin:        "isin2",
													Quantity:    100,
													Amc:         "101",
												},
											},
											NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
												{
													ClientReqId: "nftClientReqId2",
													CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
												},
											},
										},
										UserAction: &palPb.LamfLoanDetailsVerificationData_UserAction{
											Identifier: palPb.RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF,
										},
									},
								},
							},
						},
					},
				}, nil)

				m.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), "actorId1", palPb.Vendor_FIFTYFIN, palPb.LoanProgram_LOAN_PROGRAM_LAMF, gomock.Any()).Return(&palPb.LoanApplicant{
					Id:                "loanApplicant1",
					ActorId:           "actorId1",
					VendorApplicantId: "123456",
					PersonalDetails: &palPb.PersonalDetails{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
						EmailId:     "<EMAIL>",
					},
					LoanProgramVersion: enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1,
				}, nil)

				m.loanRequestDao.EXPECT().GetById(gomock.Any(), "lrId1").Return(&palPb.LoanRequest{
					Id:      "lrId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
					Details: &palPb.LoanRequestDetails{
						LoanInfo: &palPb.LoanRequestDetails_LoanInfo{
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100000,
								Nanos:        0,
							},
							PledgeDetails: &palPb.PledgeDetails{
								MutualFunds: &palPb.PledgeDetails_MutualFunds{
									Schemes: []*palPb.PledgeDetails_MutualFunds_Scheme{
										{
											Isin:     "isin1",
											Quantity: 300,
										},
									},
								},
							},
						},
					},
				}, nil).Times(2)

				m.time.EXPECT().Now().Return(time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST))
				m.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId1").Return(&palPb.LoanOffer{
					Id:            "offerId1",
					ActorId:       "actorId1",
					ValidTill:     timestampPb.New(time.Date(2024, 5, 10, 0, 0, 0, 0, datetime.IST)),
					CreatedAt:     timestampPb.New(time.Date(2024, 5, 3, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt:     timestampPb.New(time.Date(2024, 5, 3, 0, 0, 0, 0, datetime.IST)),
					DeactivatedAt: nil,
					OfferConstraints: &palPb.OfferConstraints{
						MaxLoanAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 100000},
						AdditionalConstraints: &palPb.OfferConstraints_FiftyfinLamfConstraintInfo{
							FiftyfinLamfConstraintInfo: &palPb.FiftyFinLamfConstraintInfo{
								MfPortfolioConstraint: &palPb.MfPortfolioConstraint{
									ApprovedHoldings: []*palPb.MfPortfolioConstraint_Holding{
										{
											MutualFundFacilitator: palPb.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											Isin:                  "isin1",
											FolioNumber:           "1",
											AmcCode:               "P",
											Quantity:              100,
											VendorLtv:             0.9,
											TotalAmount:           &moneyPb.Money{CurrencyCode: "INR", Units: 40000},
											MaxLoanAmount:         &moneyPb.Money{CurrencyCode: "INR", Units: 36000},
											Email:                 "<EMAIL>",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
										},
										{
											MutualFundFacilitator: palPb.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											Isin:                  "isin1",
											FolioNumber:           "2",
											AmcCode:               "P",
											Quantity:              100,
											VendorLtv:             0.9,
											TotalAmount:           &moneyPb.Money{CurrencyCode: "INR", Units: 40000},
											MaxLoanAmount:         &moneyPb.Money{CurrencyCode: "INR", Units: 36000},
											Email:                 "<EMAIL>",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
										},
										{
											MutualFundFacilitator: palPb.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
											Isin:                  "isin1",
											FolioNumber:           "3",
											AmcCode:               "P",
											Quantity:              100,
											VendorLtv:             0.9,
											TotalAmount:           &moneyPb.Money{CurrencyCode: "INR", Units: 40000},
											MaxLoanAmount:         &moneyPb.Money{CurrencyCode: "INR", Units: 36000},
											Email:                 "<EMAIL>",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1987654321},
										},
										{
											MutualFundFacilitator: palPb.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											Isin:                  "isin2",
											FolioNumber:           "1",
											AmcCode:               "101",
											Quantity:              100,
											VendorLtv:             0.9,
											TotalAmount:           &moneyPb.Money{CurrencyCode: "INR", Units: 40000},
											MaxLoanAmount:         &moneyPb.Money{CurrencyCode: "INR", Units: 36000},
											Email:                 "<EMAIL>",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
										},
										{
											MutualFundFacilitator: palPb.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
											Isin:                  "isin2",
											FolioNumber:           "3",
											AmcCode:               "101",
											Quantity:              100,
											VendorLtv:             0.9,
											TotalAmount:           &moneyPb.Money{CurrencyCode: "INR", Units: 40000},
											MaxLoanAmount:         &moneyPb.Money{CurrencyCode: "INR", Units: 36000},
											Email:                 "<EMAIL>",
											PhoneNumber:           &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
										},
									},
								},
								Source: palPb.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED,
							},
						},
					},
				}, nil).Times(2)

				m.fetchedAssetDao.EXPECT().GetByActorId(gomock.Any(), "actorId1", gomock.Any()).Return(&palPb.FetchedAsset{
					Id:                  "faId1",
					ActorId:             "actorId1",
					Vendor:              palPb.Vendor_FIFTYFIN,
					AssetType:           palPb.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
					VendorAssetId:       "12345",
					UserAssetIdentifier: "",
					FetchedAt:           timestampPb.New(time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST)),
				}, nil)

				m.ffVgClient.EXPECT().FetchMfPortfolio(gomock.Any(), &ffVgPb.FetchMfPortfolioRequest{
					UserId: 123456,
				}).Return(&ffVgPb.FetchMfPortfolioResponse{
					Status: rpc.StatusOk(),
					Portfolio: &ffVendorsPb.MfPortfolio{
						CamsApproved: []*ffVendorsPb.MfHolding{
							{
								Id:          1,
								Isin:        "isin1",
								Quantity:    100,
								Price:       400,
								FolioNumber: "1",
							},
							{
								Id:          2,
								Isin:        "isin1",
								Quantity:    100,
								Price:       400,
								FolioNumber: "2",
							},
						},
						KarvyApproved: []*ffVendorsPb.MfHolding{
							{
								Id:          3,
								Isin:        "isin2",
								Quantity:    100,
								Price:       400,
								FolioNumber: "3",
							},
						},
					},
				}, nil)

				var getMutualFundsReq *catalogManagerPb.GetMutualFundsRequest
				m.mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), gomock.AssignableToTypeOf(getMutualFundsReq)).DoAndReturn(
					func(ctx context.Context, req *catalogManagerPb.GetMutualFundsRequest, opts ...interface{}) (*catalogManagerPb.GetMutualFundsResponse, error) {
						return &catalogManagerPb.GetMutualFundsResponse{
							Status: rpc.StatusOk(),
							MutualFunds: map[string]*mutualfund.MutualFund{
								"isin1": {
									IsinNumber: "isin1",
									Amc:        mutualfund.Amc_ICICI_PRUDENTIAL,
									NameData: &mutualfund.FundNameMetadata{
										ShortName: "Icici Prudential",
									},
								},
							},
						}, nil
					}).Times(2)

				m.uuidGenerator.EXPECT().GenerateUuid().Return("uuid1").Times(2)

				m.eventsBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()

				m.loanStepExecutionDao.EXPECT().Update(gomock.Any(), &palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "lrId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId2",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS,
					Status:   palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_LoanDetailsVerificationData{
							LoanDetailsVerificationData: &palPb.LoanDetailsVerificationData{
								Data: &palPb.LoanDetailsVerificationData_Lamf{
									Lamf: &palPb.LamfLoanDetailsVerificationData{
										UserActionTaken: false,
										PfFetch_Data: []*palPb.LamfLoanDetailsVerificationData_PfFetchData{
											{
												ReqId:          "reqId1",
												FetchCompleted: true,
												IsFetchSuccess: false,
												CompletionTime: nil,
											},
											{
												ReqId:          "reqId2",
												FetchCompleted: true,
												IsFetchSuccess: true,
												CompletionTime: timestampPb.New(time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST)),
											},
										},
										MobileLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfMobileLinkDetails{
											NewMobile:   &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
											LinkedEmail: "<EMAIL>",
											Folios: []*palPb.LamfLoanDetailsVerificationData_FolioData{
												{
													FolioNumber: "3",
													Isin:        "isin1",
													Quantity:    100,
													Amc:         "P",
												},
											},
											NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
												{
													ClientReqId: "nftClientReqId1",
													CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
												},
											},
										},
										EmailLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfEmailLinkDetails{
											NewEmail:     "<EMAIL>",
											LinkedMobile: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
											NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
												{
													ClientReqId: "nftClientReqId2",
													CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
												},
											},
										},
										UserAction: nil,
									},
								},
							},
						},
					},
				}, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}).Return(nil)

				m.loanRequestDao.EXPECT().Update(gomock.Any(), &palPb.LoanRequest{
					Id:      "lrId1",
					ActorId: "actorId1",
					OfferId: "offerId1",
					Details: &palPb.LoanRequestDetails{
						LoanInfo: &palPb.LoanRequestDetails_LoanInfo{
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100000,
								Nanos:        0,
							},
							PledgeDetails: &palPb.PledgeDetails{
								MutualFunds: &palPb.PledgeDetails_MutualFunds{
									Schemes: []*palPb.PledgeDetails_MutualFunds_Scheme{
										{
											Isin:     "isin1",
											Quantity: 300,
										},
									},
								},
							},
						},
					},
					NextAction: getVerificationV2IntermediateScreenDl("lrId1", "lseId1"),
				}, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}).Return(nil)
			},
			want: &palActivityPb.FiftyfinFundVerificationResponse{
				LoanStep: &palPb.LoanStepExecution{
					Id:       "lseId1",
					ActorId:  "actorId1",
					RefId:    "lrId1",
					Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
					OrchId:   "orchId2",
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS,
					Status:   palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &palPb.LoanStepExecutionDetails{
						Details: &palPb.LoanStepExecutionDetails_LoanDetailsVerificationData{
							LoanDetailsVerificationData: &palPb.LoanDetailsVerificationData{
								Data: &palPb.LoanDetailsVerificationData_Lamf{
									Lamf: &palPb.LamfLoanDetailsVerificationData{
										UserActionTaken: false,
										PfFetch_Data: []*palPb.LamfLoanDetailsVerificationData_PfFetchData{
											{
												ReqId:          "reqId1",
												FetchCompleted: true,
												IsFetchSuccess: false,
												CompletionTime: nil,
											},
											{
												ReqId:          "reqId2",
												FetchCompleted: true,
												IsFetchSuccess: true,
												CompletionTime: timestampPb.New(time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST)),
											},
										},
										MobileLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfMobileLinkDetails{
											NewMobile:   &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
											LinkedEmail: "<EMAIL>",
											Folios: []*palPb.LamfLoanDetailsVerificationData_FolioData{
												{
													FolioNumber: "3",
													Isin:        "isin1",
													Quantity:    100,
													Amc:         "P",
												},
											},
											NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
												{
													ClientReqId: "nftClientReqId1",
													CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
												},
											},
										},
										EmailLinkDetails: &palPb.LamfLoanDetailsVerificationData_MfEmailLinkDetails{
											NewEmail:     "<EMAIL>",
											LinkedMobile: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
											NftReqDetails: []*palPb.LamfLoanDetailsVerificationData_NftRequestDetails{
												{
													ClientReqId: "nftClientReqId2",
													CreatedAt:   timestampPb.New(time.Date(2024, 5, 5, 23, 55, 0, 0, datetime.IST)),
												},
											},
										},
										UserAction: nil,
									},
								},
							},
						},
					},
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initProcessorMocks(ctrl)
			tt.mockFunc(f)

			p := newActivityProcessorWithMocks(f)

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			var result *palActivityPb.FiftyfinFundVerificationResponse
			got, err := env.ExecuteActivity(palNs.FiftyfinFundVerificationV2, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("FiftyfinFundVerificationV2() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("FiftyfinFundVerificationV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("FiftyfinFundVerificationV2() error = %v assertion failed", err)
				return
			case cmp.Diff(result, tt.want, protocmp.Transform()) != "":
				t.Errorf("FiftyfinFundVerificationV2() got = %v,\n want %v \n diff : %v", result, tt.want, cmp.Diff(result, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}
