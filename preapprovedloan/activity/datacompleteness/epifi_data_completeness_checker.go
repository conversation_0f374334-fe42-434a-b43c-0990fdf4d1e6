package datacompleteness

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	brePb "github.com/epifi/gamma/api/bre"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/preapprovedloan/activity/datacompleteness/helper"
	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	palHelper "github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

// static map reflecting tha mapping maintained in the BRE - https://docs.google.com/spreadsheets/d/1GCxAkYHupDqWelL3GcXJpOht9SwG4oVkWS9YrOOmdIQ/edit?usp=sharing
var dataReqMap = map[string][]palPb.DataRequirementType{
	"PL_SALARIED_LP02": {
		// disabling EPFO for now to allow SG offer generation along with MV and ABFL
		// EPFO data was anyway not being used in the BRE for SG
		// palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO,
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
	},
	"ABFL_PL_ALL_LP01": {
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
	},
	"FEDERAL_PL_SALARIED_LP01": {
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
	},
	"LENDEN_PL_ALL_LP01": {
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
	},
	"MONEYVIEW_PL_ALL_LP01": {
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
	},
	"PL_SALARIED_LP03": {
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO,
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
		palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME,
	},
}

var fedEligibilityDataRequirementTypes = []palPb.DataRequirementType{
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_FEDERAL_BRE_CONSENT,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_FEDERAL_PAN_VERIFICATION,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_FULL_ADDRESS,
}

const moreDataNeededDecisionForEvent = "MORE_DATA_NEEDED"

type EpifiDataCompletenessChecker struct {
	userDataProvider       userdata.IUserDataProvider
	breClient              brePb.BreClient
	eventBroker            events.Broker
	config                 *genconf.Config
	dataDevS3Client        types2.DataDevS3Client
	salaryEstimationClient salaryestimation.SalaryEstimationClient
	usersClient            userPb.UsersClient
	userGroupClient        userGroupPb.GroupClient
	onbClient              onbPb.OnboardingClient
	savingsClient          savings.SavingsClient
	releaseEvaluator       release.IEvaluator
}

var _ IDataCompletenessChecker = &EpifiDataCompletenessChecker{}

func NewEpifiDataCompletenessChecker(salaryEstimationClient salaryestimation.SalaryEstimationClient, userDataProvider userdata.IUserDataProvider, breClient brePb.BreClient, eventBroker events.Broker, config *genconf.Config, dataDevS3Client types2.DataDevS3Client, usersClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient, onbClient onbPb.OnboardingClient,
	savingsClient savings.SavingsClient, releaseEvaluator release.IEvaluator) *EpifiDataCompletenessChecker {
	return &EpifiDataCompletenessChecker{
		userDataProvider:       userDataProvider,
		breClient:              breClient,
		eventBroker:            eventBroker,
		config:                 config,
		dataDevS3Client:        dataDevS3Client,
		salaryEstimationClient: salaryEstimationClient,
		usersClient:            usersClient,
		userGroupClient:        userGroupClient,
		onbClient:              onbClient,
		savingsClient:          savingsClient,
		releaseEvaluator:       releaseEvaluator,
	}
}

// nolint:funlen
func (s *EpifiDataCompletenessChecker) CheckAndUpdateDataCompleteness(ctx context.Context, req *CheckDataCompletenessRequest) (*CheckDataCompletenessResponse, error) {
	if !helper.IsDataCollected(req.Loec, palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON) {
		req.Loec.DataRequirementDetails = &palPb.DataRequirementDetails{
			DataRequirements: []*palPb.DataRequirement{
				{
					DataRequirementType: palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON,
				},
			},
		}
		return &CheckDataCompletenessResponse{
			UpdatedLoec: req.Loec,
			UpdateFieldMask: []palPb.LoanOfferEligibilityCriteriaFieldMask{
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
			},
		}, nil
	}

	allDataCollected := true
	for _, d := range req.Loec.GetDataRequirementDetails().GetDataRequirements() {
		if !d.GetIsCollected() {
			allDataCollected = false
			break
		}
	}
	if !allDataCollected {
		return &CheckDataCompletenessResponse{
			UpdatedLoec: req.Loec,
		}, nil
	}

	user, err := s.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.Loec.GetActorId()})
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID, err: %v", err))
	}
	var epfo, aa, cibil *brePb.DataAvailability
	for _, d := range req.Loec.GetDataRequirementDetails().GetDataRequirements() {
		if !d.GetIsCollected() {
			continue
		}
		switch d.GetDataRequirementType() {
		case palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO:
			epfo = &brePb.DataAvailability{
				IsAvailable: true,
			}
		case palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL:
			cibil = &brePb.DataAvailability{
				IsAvailable: true,
			}
		case palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME:
			salaryEstResp, salaryEstErr := s.salaryEstimationClient.GetSalary(ctx, &salaryestimation.GetSalaryRequest{
				ActorId: req.Loec.GetActorId(),
			})
			if te := epifigrpc.RPCError(salaryEstResp, salaryEstErr); te != nil {
				return nil, errors.Wrap(te, "failed to get the salary data")
			}

			breRequiredAaData, breRequiredAaDataErr := palHelper.GetBreRequiredAaData(ctx, salaryEstResp.GetL1AnalysisSignedUrl(), req.Loec.GetVendor())
			if breRequiredAaDataErr != nil {
				if errors.Is(breRequiredAaDataErr, epifierrors.ErrRecordNotFound) {
					return nil, errors.New("BRE required AA data is not available")
				}
				return nil, errors.Wrap(breRequiredAaDataErr, "error in bre required data")
			}
			req.Loec.PolicyParams.DataInfo = &palPb.DataInfo{
				AaData: breRequiredAaData,
			}
			aa = &brePb.DataAvailability{
				IsAvailable: true,
			}
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), palEvents.NewAADataReceivedForBRE(req.Loec.GetActorId(), req.Loec.GetVendor().String(), req.Loec.GetLoanProgram().String()))
			})
		default:
			// do nothing for now
		}
	}
	address := user.GetAddress()
	// only basic address details are collected from user in eligibility flow, use them if present
	if user.GetResidenceDetails().GetResidentialAddress().GetAddress().GetPostalCode() != "" {
		address = typesv2.GetFromBeAddress(user.GetResidenceDetails().GetResidentialAddress().GetAddress())
	}

	userFeatProp, err := palHelper.GetUserFeatureProperty(ctx, req.Loec.GetActorId(), s.onbClient, s.savingsClient)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in GetUserFeatureProperty err: %v", err))
	}

	// todo(Sharath/Anupam): Placing this condition because monthly income not
	// available for the user in self employed case, so till the time that checks
	// goes in we will be using annual revenue.
	monthlyIncome := user.GetEmploymentDetails().GetMonthlyIncome()
	if monthlyIncome == nil && user.GetEmploymentDetails().GetAnnualRevenue() != nil {
		monthlyIncome = user.GetEmploymentDetails().GetAnnualRevenue()
		monthlyIncome.Units = monthlyIncome.GetUnits() / 12
	}

	breReq := &brePb.GetFinalBreEligibilityDetailsRequest{
		ActorId:   req.Loec.GetActorId(),
		RequestId: uuid.NewString(),
		CustomerDetails: &brePb.CustomerDetails{
			PersonalDetails: &brePb.PersonalDetails{
				Dob:    user.GetGivenDateOfBirth(),
				Name:   user.GetBestName(),
				Gender: user.GetGivenGender(),
				Pan:    user.GetPan(),
			},
			EmploymentDetails: &brePb.EmploymentDetails{
				EmploymentType: user.GetEmploymentDetails().GetEmploymentType(),
				MonthlyIncome:  monthlyIncome,
				EmployerName:   user.GetEmploymentDetails().GetOrganizationName(),
				WorkEmail:      user.GetEmploymentDetails().GetWorkEmail(),
				WorkAddress:    user.GetEmploymentDetails().GetWorkAddress(),
			},
			ResidentialAddress: address,
			RequestedLoanDetails: &brePb.RequestedLoanDetails{
				DesiredLoanAmount: req.Loec.GetDataRequirementDetails().GetDesiredLoanAmount(),
			},
		},
		Vendor:       req.Loec.GetVendor(),
		Epfo:         epfo,
		Aa:           aa,
		Cibil:        cibil,
		PolicyParams: req.Loec.GetPolicyParams(),
	}

	if breReq.GetPolicyParams() == nil {
		breReq.PolicyParams = &palPb.PolicyParams{}
	}
	if breReq.GetPolicyParams().GetDataInfo() == nil {
		breReq.GetPolicyParams().DataInfo = &palPb.DataInfo{}
	}
	breReq.GetPolicyParams().GetDataInfo().IsEtbUser = userFeatProp.IsFiSAHolder

	breResp, breErr := s.breClient.GetFinalBreEligibilityDetails(ctx, breReq)
	if te := epifigrpc.RPCError(breResp, breErr); te != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to call GetFinalBreEligibilityDetails: %v", te))
	}
	if breResp.GetRawBreResponse() != nil {
		csvRow := &palHelper.CsvRow{
			ActorId:       req.Loec.GetActorId(),
			LoecId:        req.Loec.GetId(),
			ReqId:         breReq.GetRequestId(),
			BreRes:        string(breResp.GetRawBreResponse()),
			BreIdentifier: "FINAL",
		}
		writeErr := palHelper.WriteRawResponseToS3(ctx, csvRow, s.config, s.dataDevS3Client, true)
		if writeErr != nil {
			return nil, errors.Wrap(writeErr, "failed to write final BRE res to S3.")
		}
	}

	updatedLoec, loecFieldMasks, updatedLoecErr := s.getModifiedLoecByBreResponse(ctx, req.Loec, breResp, breReq.GetRequestId(), req.LoanRequestId)
	if updatedLoecErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed in getting modified loec from bre response: %v", updatedLoecErr))
	}

	return &CheckDataCompletenessResponse{
		UpdatedLoec:     updatedLoec,
		UpdateFieldMask: loecFieldMasks,
	}, nil
}

func (s *EpifiDataCompletenessChecker) getModifiedLoecByBreResponse(ctx context.Context, loec *palPb.LoanOfferEligibilityCriteria, breResp *brePb.GetFinalBreEligibilityDetailsResponse, finalBreReqId, lrId string) (*palPb.LoanOfferEligibilityCriteria, []palPb.LoanOfferEligibilityCriteriaFieldMask, error) {
	if loec.GetDataRequirementDetails() == nil {
		loec.DataRequirementDetails = &palPb.DataRequirementDetails{}
	}
	loec.GetDataRequirementDetails().FinalBreRequestId = finalBreReqId
	loec.PolicyParams = breResp.GetPolicyParams()
	loecFieldMasks := []palPb.LoanOfferEligibilityCriteriaFieldMask{
		palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
		palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
		palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS,
		palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
	}
	var decisionForEvent string
	var dataRequirementsForEvent []palPb.DataRequirementType
	switch {
	case breResp.GetPrioritizedDecision().GetDecision() == brePb.Decision_DECISION_APPROVED:
		fm := s.modifyLoecForFiBreApproval(ctx, loec, breResp)
		loecFieldMasks = append(loecFieldMasks, fm...)
		decisionForEvent = brePb.Decision_DECISION_APPROVED.String()
	case !breResp.GetSubsequentCallAllowed():
		loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
		loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI
		if len(breResp.GetDecisions()) > 0 {
			loec.ExpiredAt = breResp.GetDecisions()[0].GetValidTill()
			loecFieldMasks = append(loecFieldMasks, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT)
		}
		decisionForEvent = brePb.Decision_DECISION_REJECTED.String()
	case breResp.GetSubsequentCallAllowed():
		dataReqTypes, err := getDataRequirements(breResp)
		if err != nil {
			return nil, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to modify loec with data requirements: %v", err))
		}
		if loec.GetDataRequirementDetails() == nil {
			loec.DataRequirementDetails = &palPb.DataRequirementDetails{}
		}
		for _, req := range dataReqTypes {
			loec.GetDataRequirementDetails().DataRequirements = append(loec.GetDataRequirementDetails().DataRequirements, &palPb.DataRequirement{
				DataRequirementType: req,
			})
		}
		decisionForEvent = moreDataNeededDecisionForEvent
		dataRequirementsForEvent = dataReqTypes
	default:
		return nil, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unexpected case in bre response: %s", breResp.String()))
	}

	goroutine.RunWithDefaultTimeout(context.Background(), func(ctx context.Context) {
		s.eventBroker.AddToBatch(ctx, palEvents.NewFiFinalBre(loec.GetActorId(), lrId, loec.GetVendor().String(), loec.GetLoanProgram().String(), decisionForEvent, finalBreReqId, dataRequirementsForEvent))
	})

	return loec, loecFieldMasks, nil
}

func (s *EpifiDataCompletenessChecker) modifyLoecForFiBreApproval(_ context.Context, loec *palPb.LoanOfferEligibilityCriteria, breResp *brePb.GetFinalBreEligibilityDetailsResponse) (loecFieldMasks []palPb.LoanOfferEligibilityCriteriaFieldMask) {
	// APPROVED_BY_FI_PRE_BRE means we can proceed for offer generation
	loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE
	loecFieldMasks = append(loecFieldMasks, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT)
	loec.ExpiredAt = breResp.GetPrioritizedDecision().GetValidTill()
	return loecFieldMasks
}

func getDataRequirements(breResp *brePb.GetFinalBreEligibilityDetailsResponse) ([]palPb.DataRequirementType, error) {
	if len(breResp.GetDataRequirements()) == 0 {
		return nil, errors.New("no data requirements found in bre response")
	}
	var dataReqs []palPb.DataRequirementType
	// TODO: this will only work for one loan program, need to add logic to handle multiple loan programs
	for lp, required := range breResp.GetDataRequirements() {
		reqs, ok := dataReqMap[lp]
		if !ok {
			return nil, fmt.Errorf("unknown loan program: %v", lp)
		}
		if required {
			dataReqs = append(dataReqs, reqs...)
		}
	}
	return dataReqs, nil
}

// if all eligibility stages completed, return true, else false
// if entry not present in data requirements, add it and return false
func checkExtraFedStagesCompleteness(dataRequirements *palPb.DataRequirementDetails) bool {
	// find if all required data requirements are present in LOEC and add them all at once if not
	var newDataRequirements []*palPb.DataRequirement
	for _, drt := range fedEligibilityDataRequirementTypes {
		foundFlag := false
		for _, dataReq := range dataRequirements.GetDataRequirements() {
			if dataReq.GetDataRequirementType() == drt {
				foundFlag = true
				break
			}
		}
		if !foundFlag {
			newDataRequirements = append(newDataRequirements, &palPb.DataRequirement{
				DataRequirementType: drt,
				IsCollected:         false,
			})
		}
	}
	// if any new data requirements are needed, return false
	if len(newDataRequirements) > 0 {
		dataRequirements.DataRequirements = append(dataRequirements.GetDataRequirements(), newDataRequirements...)
		return false
	}

	// loop through all the fed required data requirements and check if they are collected
	for _, drt := range fedEligibilityDataRequirementTypes {
		for _, dataReq := range dataRequirements.GetDataRequirements() {
			if dataReq.GetDataRequirementType() == drt {
				if !dataReq.GetIsCollected() {
					return false
				}
			}
		}
	}
	return true
}

func (r *EpifiDataCompletenessChecker) isInternalUser(ctx context.Context, actorId string) (bool, error) {
	userResp, userErr := r.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, userErr); rpcErr != nil {
		return false, fmt.Errorf("usersClient.GetUser call failed, err: %w", rpcErr)
	}

	// get user groups
	groupResp, groupErr := r.userGroupClient.GetGroupsMappedToEmail(ctx, &userGroupPb.GetGroupsMappedToEmailRequest{
		Email: userResp.GetUser().GetProfile().GetEmail(),
	})
	if rpcErr := epifigrpc.RPCError(groupResp, groupErr); rpcErr != nil {
		return false, fmt.Errorf("userGroupClient.GetGroupsMappedToEmail call failed, err: %w", rpcErr)
	}
	return lo.Contains(groupResp.GetGroups(), commontypes.UserGroup_INTERNAL), nil
}
