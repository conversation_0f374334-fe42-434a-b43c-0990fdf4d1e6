// nolint
package activity_test

import (
	"context"
	"fmt"
	"testing"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	authOrchPb "github.com/epifi/gamma/api/auth/orchestrator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/kyc/vkyc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	preApprovedActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/gamma/api/risk/profile"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgProfileValidationPb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
)

func TestProcessor_UpdateLoanRequest(t *testing.T) {
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	testIncorrectPayload, _ := protojson.Marshal(&preApprovedLoanPb.LoanRequest{Id: "abcd", ActorId: "dummyactor"})
	testCorrectPayload, _ := protojson.Marshal(&preApprovedActivityPb.UpdateLoanRequestActivityRequest{
		LoanRequestStatus:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		LoanRequestSubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED,
	})

	type mockGetLoanRequestByOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}

	type mockUpdate struct {
		err error
	}

	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                       string
		args                       args
		want                       *activityPb.Response
		wantErr                    bool
		assertErr                  func(err error) bool
		mockGetLoanRequestByOrchId *mockGetLoanRequestByOrchId
		mockUpdate                 *mockUpdate
	}{
		{
			name: "Should fail to update loan request as fail to unmarshall payload",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					Payload:     testIncorrectPayload,
					ClientReqId: "random-loan-request-orch-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Should fail as no such loan request exists",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "random-loan-request-orch-id",
					Payload:     testCorrectPayload,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "Should fail with transient error",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-request-orch-id",
					Payload:     testCorrectPayload,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: nil,
				err: fmt.Errorf("random-error"),
			},
		},
		{
			name: "Should fail to update",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-request-orch-id",
					Payload:     testCorrectPayload,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockUpdate: &mockUpdate{err: fmt.Errorf("update error")},
		},
		{
			name: "Should successfully update loan request",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-request-orch-id",
					Payload:     testCorrectPayload,
				},
			},
			want:    nil,
			wantErr: false,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockUpdate: &mockUpdate{err: nil},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoanRequestByOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
			}
			if tt.mockUpdate != nil {
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockUpdate.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.UpdateLoanRequest, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UpdateLoanRequest() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("UpdateLoanRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("UpdateLoanRequest() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("UpdateLoanRequest() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

// func TestProcessor_ProcessLoanApplication(t *testing.T) {
//	p, md, assertTest := newPalActProcessorWithMocks(t)
//	defer assertTest()
//
//	env := wts.NewTestActivityEnvironment()
//	env.RegisterActivity(p)
//
//	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//
//	md.authClient.EXPECT().GetDeviceAuth(gomock.Any(), gomock.Any()).Return(&authPb.GetDeviceAuthResponse{
//		Status: rpc.StatusOk(),
//		Device: &commontypes.Device{
//			DeviceId: "",
//		},
//	}, nil).AnyTimes()
//
//	type mockGetLoanRequestByOrchId struct {
//		res *preApprovedLoanPb.LoanRequest
//		err error
//	}
//
//	type mockGetOfferById struct {
//		res *preApprovedLoanPb.LoanOffer
//		err error
//	}
//
//	type mockVgGetInstantLoanInfo struct {
//		res *palVgPb.GetInstantLoanInfoResponse
//		err error
//	}
//	type mockVgGetInstantLoan struct {
//		res *palVgPb.GetInstantLoanApplicationResponse
//		err error
//	}
//	type mockVgGetStatusEnquiry struct {
//		res *palVgPb.GetInstantLoanStatusEnquiryResponse
//		err error
//	}
//
//	type args struct {
//		ctx context.Context
//		req *activityPb.Request
//	}
//
//	tests := []struct {
//		name                       string
//		args                       args
//		want                       *activityPb.Response
//		wantErr                    bool
//		assertErr                  func(err error) bool
//		mockGetLoanRequestByOrchId *mockGetLoanRequestByOrchId
//		mockGetOfferById           *mockGetOfferById
//		mockVgGetInstantLoanInfo   *mockVgGetInstantLoanInfo
//		mockVgGetInstantLoan       *mockVgGetInstantLoan
//		mockVgGetStatusEnquiry     *mockVgGetStatusEnquiry
//	}{
//		{
//			name: "Should fail as no such loan request exists",
//			args: args{
//				ctx: context.Background(),
//				req: &activityPb.Request{
//					ClientReqId: "loan-request-orch-id-1",
//				},
//			},
//			want:    nil,
//			wantErr: true,
//			assertErr: func(err error) bool {
//				return !epifitemporal.IsRetryableError(err)
//			},
//			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//				res: nil,
//				err: epifierrors.ErrRecordNotFound,
//			},
//		},
//		{
//			name: "Should fail as no loan offer exists",
//			args: args{
//				ctx: context.Background(),
//				req: &activityPb.Request{
//					ClientReqId: "loan-request-orch-id-1",
//				},
//			},
//			want:      nil,
//			wantErr:   true,
//			assertErr: epifitemporal.IsRetryableError,
//			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//				res: &preApprovedLoanPb.LoanRequest{},
//				err: nil,
//			},
//			mockGetOfferById: &mockGetOfferById{
//				res: nil,
//				err: fmt.Errorf("test error"),
//			},
//		},
//		{
//			name: "Should fail to get status from vendor due to transient failure",
//			args: args{
//				ctx: context.Background(),
//				req: &activityPb.Request{
//					ClientReqId: "loan-request-orch-id-1",
//				},
//			},
//			want:      nil,
//			wantErr:   true,
//			assertErr: epifitemporal.IsRetryableError,
//			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//				res: &preApprovedLoanPb.LoanRequest{
//					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
//					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_KFS,
//				},
//				err: nil,
//			},
//			mockGetOfferById: &mockGetOfferById{
//				res: &preApprovedLoanPb.LoanOffer{},
//				err: nil,
//			},
//			mockVgGetStatusEnquiry: &mockVgGetStatusEnquiry{
//				res: &palVgPb.GetInstantLoanStatusEnquiryResponse{
//					Status: rpc.StatusInternal(),
//				},
//				err: nil,
//			},
//		},
//		{
//			name: "Should fail to initiate a new request with vendor",
//			args: args{
//				ctx: context.Background(),
//				req: &activityPb.Request{
//					ClientReqId: "loan-request-orch-id-1",
//				},
//			},
//			want:      nil,
//			wantErr:   true,
//			assertErr: epifitemporal.IsRetryableError,
//			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//				res: &preApprovedLoanPb.LoanRequest{
//					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
//					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
//				},
//				err: nil,
//			},
//			mockGetOfferById: &mockGetOfferById{
//				res: &preApprovedLoanPb.LoanOffer{},
//				err: nil,
//			},
//			mockVgGetStatusEnquiry: &mockVgGetStatusEnquiry{
//				res: &palVgPb.GetInstantLoanStatusEnquiryResponse{
//					Status: rpc.StatusRecordNotFound(),
//				},
//				err: nil,
//			},
//			mockVgGetInstantLoan: &mockVgGetInstantLoan{
//				res: &palVgPb.GetInstantLoanApplicationResponse{
//					Status: rpc.StatusInternal(),
//				},
//				err: nil,
//			},
//		},
//		{
//			name: "Should initiate a new request with vendor",
//			args: args{
//				ctx: context.Background(),
//				req: &activityPb.Request{
//					ClientReqId: "loan-request-orch-id-1",
//				},
//			},
//			want:      nil,
//			wantErr:   true,
//			assertErr: epifitemporal.IsRetryableError,
//			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//				res: &preApprovedLoanPb.LoanRequest{
//					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
//					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
//				},
//				err: nil,
//			},
//			mockGetOfferById: &mockGetOfferById{
//				res: &preApprovedLoanPb.LoanOffer{},
//				err: nil,
//			},
//			mockVgGetStatusEnquiry: &mockVgGetStatusEnquiry{
//				res: &palVgPb.GetInstantLoanStatusEnquiryResponse{
//					Status: rpc.StatusRecordNotFound(),
//				},
//				err: nil,
//			},
//			mockVgGetInstantLoan: &mockVgGetInstantLoan{
//				res: &palVgPb.GetInstantLoanApplicationResponse{
//					Status: rpc.StatusOk(),
//				},
//				err: nil,
//			},
//			mockVgGetInstantLoanInfo: &mockVgGetInstantLoanInfo{
//				res: &palVgPb.GetInstantLoanInfoResponse{
//					Status: rpc.StatusInternal(),
//				},
//				err: nil,
//			},
//		},
//		// {
//		//	name: "Should not initiate a new request with vendor as one already raised with same application id",
//		//	args: args{
//		//		ctx: context.Background(),
//		//		req: &activityPb.Request{
//		//			ClientReqId: "loan-request-orch-id-1",
//		//		},
//		//	},
//		//	want:      nil,
//		//	wantErr:   true,
//		//	assertErr: epifitemporal.IsRetryableError,
//		//	mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//		//		res: &preApprovedLoanPb.LoanRequest{
//		//			Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
//		//			SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
//		//		},
//		//		err: nil,
//		//	},
//		//	mockGetOfferById: &mockGetOfferById{
//		//		res: &preApprovedLoanPb.LoanOffer{},
//		//		err: nil,
//		//	},
//		//	mockVgGetStatusEnquiry: &mockVgGetStatusEnquiry{
//		//		res: &palVgPb.GetInstantLoanStatusEnquiryResponse{
//		//			Status: rpc.StatusOk(),
//		//		},
//		//		err: nil,
//		//	},
//		//	mockVgGetInstantLoanInfo: &mockVgGetInstantLoanInfo{
//		//		res: &palVgPb.GetInstantLoanInfoResponse{
//		//			Status: rpc.StatusInternal(),
//		//		},
//		//		err: nil,
//		//	},
//		// },
//		// {
//		//	name: "Should fail to initiate a new request with vendor and vendor replying with invalid otp",
//		//	args: args{
//		//		ctx: context.Background(),
//		//		req: &activityPb.Request{
//		//			ClientReqId: "loan-request-orch-id-1",
//		//		},
//		//	},
//		//	want:      nil,
//		//	wantErr:   true,
//		//	assertErr: epifitemporal.IsRetryableError,
//		//	mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//		//		res: &preApprovedLoanPb.LoanRequest{
//		//			Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
//		//			SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
//		//		},
//		//		err: nil,
//		//	},
//		//	mockGetOfferById: &mockGetOfferById{
//		//		res: &preApprovedLoanPb.LoanOffer{},
//		//		err: nil,
//		//	},
//		//	mockVgGetStatusEnquiry: &mockVgGetStatusEnquiry{
//		//		res: &palVgPb.GetInstantLoanStatusEnquiryResponse{
//		//			Status: rpc.StatusRecordNotFound(),
//		//		},
//		//		err: nil,
//		//	},
//		//	mockVgGetInstantLoan: &mockVgGetInstantLoan{
//		//		res: &palVgPb.GetInstantLoanApplicationResponse{
//		//			Status: rpc.StatusPermissionDeniedWithDebugMsg("ILE025"),
//		//		},
//		//		err: nil,
//		//	},
//		// },
//		// {
//		//	name: "Should successfully do a status check at vendor",
//		//	args: args{
//		//		ctx: context.Background(),
//		//		req: &activityPb.Request{
//		//			ClientReqId: "loan-request-orch-id-1",
//		//		},
//		//	},
//		//	want:      nil,
//		//	wantErr:   true,
//		//	assertErr: epifitemporal.IsRetryableError,
//		//	mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//		//		res: &preApprovedLoanPb.LoanRequest{
//		//			Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
//		//			SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_IN_PROGRESS,
//		//		},
//		//		err: nil,
//		//	},
//		//	mockGetOfferById: &mockGetOfferById{
//		//		res: &preApprovedLoanPb.LoanOffer{},
//		//		err: nil,
//		//	},
//		//	mockVgGetInstantLoanInfo: &mockVgGetInstantLoanInfo{
//		//		res: &palVgPb.GetInstantLoanInfoResponse{
//		//			Status:    rpc.StatusOk(),
//		//			LoanState: palVgPb.LoanState_LOAN_STATE_APPROVED_PENDING_DISBURSAL,
//		//		},
//		//		err: nil,
//		//	},
//		// },
//		// {
//		//	name: "Should successfully do a status check at vendor and fail the request",
//		//	args: args{
//		//		ctx: context.Background(),
//		//		req: &activityPb.Request{
//		//			ClientReqId: "loan-request-orch-id-1",
//		//		},
//		//	},
//		//	want:    nil,
//		//	wantErr: true,
//		//	assertErr: func(err error) bool {
//		//		return !epifitemporal.IsRetryableError(err)
//		//	},
//		//	mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//		//		res: &preApprovedLoanPb.LoanRequest{
//		//			Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
//		//			SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR,
//		//		},
//		//		err: nil,
//		//	},
//		//	mockGetOfferById: &mockGetOfferById{
//		//		res: &preApprovedLoanPb.LoanOffer{},
//		//		err: nil,
//		//	},
//		//	mockVgGetInstantLoanInfo: &mockVgGetInstantLoanInfo{
//		//		res: &palVgPb.GetInstantLoanInfoResponse{
//		//			Status:    rpc.StatusOk(),
//		//			LoanState: palVgPb.LoanState_LOAN_STATE_FAILED,
//		//		},
//		//		err: nil,
//		//	},
//		// },
//		// {
//		//	name: "Should fail with transient error on receiving unknown status from vendor",
//		//	args: args{
//		//		ctx: context.Background(),
//		//		req: &activityPb.Request{
//		//			ClientReqId: "loan-request-orch-id-1",
//		//		},
//		//	},
//		//	want:      nil,
//		//	wantErr:   true,
//		//	assertErr: epifitemporal.IsRetryableError,
//		//	mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//		//		res: &preApprovedLoanPb.LoanRequest{
//		//			Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
//		//			SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR,
//		//		},
//		//		err: nil,
//		//	},
//		//	mockGetOfferById: &mockGetOfferById{
//		//		res: &preApprovedLoanPb.LoanOffer{},
//		//		err: nil,
//		//	},
//		//	mockVgGetInstantLoanInfo: &mockVgGetInstantLoanInfo{
//		//		res: &palVgPb.GetInstantLoanInfoResponse{
//		//			Status:    rpc.StatusOk(),
//		//			LoanState: palVgPb.LoanState_LOAN_STATE_TYPE_UNSPECIFIED,
//		//		},
//		//		err: nil,
//		//	},
//		// },
//		// {
//		//	name: "Should successfully do a status check at vendor and succeed the activity",
//		//	args: args{
//		//		ctx: context.Background(),
//		//		req: &activityPb.Request{
//		//			ClientReqId: "loan-request-orch-id-1",
//		//		},
//		//	},
//		//	want:    nil,
//		//	wantErr: false,
//		//	mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
//		//		res: &preApprovedLoanPb.LoanRequest{
//		//			Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
//		//			SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR,
//		//		},
//		//		err: nil,
//		//	},
//		//	mockGetOfferById: &mockGetOfferById{
//		//		res: &preApprovedLoanPb.LoanOffer{},
//		//		err: nil,
//		//	},
//		//	mockVgGetInstantLoanInfo: &mockVgGetInstantLoanInfo{
//		//		res: &palVgPb.GetInstantLoanInfoResponse{
//		//			Status:    rpc.StatusOk(),
//		//			LoanState: palVgPb.LoanState_LOAN_STATE_DISBURSED,
//		//		},
//		//		err: nil,
//		//	},
//		// },
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if tt.mockGetLoanRequestByOrchId != nil {
//				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
//					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
//			}
//			if tt.mockGetOfferById != nil {
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(
//					tt.mockGetOfferById.res, tt.mockGetOfferById.err)
//			}
//			if tt.mockVgGetInstantLoanInfo != nil {
//				md.palVgClient.EXPECT().GetInstantLoanInfo(gomock.Any(), gomock.Any()).Return(
//					tt.mockVgGetInstantLoanInfo.res, tt.mockVgGetInstantLoanInfo.err)
//			}
//			if tt.mockVgGetInstantLoan != nil {
//				md.palVgClient.EXPECT().GetInstantLoanApplication(gomock.Any(), gomock.Any()).Return(
//					tt.mockVgGetInstantLoan.res, tt.mockVgGetInstantLoan.err)
//			}
//			if tt.mockVgGetStatusEnquiry != nil {
//				md.palVgClient.EXPECT().GetInstantLoanStatusEnquiry(gomock.Any(), gomock.Any()).Return(
//					tt.mockVgGetStatusEnquiry.res, tt.mockVgGetStatusEnquiry.err)
//			}
//			var result *activityPb.Response
//			got, err := env.ExecuteActivity(palNs.ProcessLoanApplication, tt.args.req)
//			if got != nil {
//				getErr := got.Get(&result)
//				if getErr != nil {
//					t.Errorf("ProcessLoanApplication() error = %v failed to fetch type value from convertible", err)
//					return
//				}
//			}
//
//			switch {
//			case (err != nil) != tt.wantErr:
//				t.Errorf("ProcessLoanApplication() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			case tt.wantErr && !tt.assertErr(err):
//				t.Errorf("ProcessLoanApplication() error = %v assertion failed", err)
//				return
//			case tt.want != nil && !proto.Equal(result, tt.want):
//				t.Errorf("ProcessLoanApplication() got = %v, want %v", result, tt.want)
//				return
//			}
//		})
//	}
// }

func TestProcessor_GetOtpVerificationStatus(t *testing.T) {
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type mockGetLoanRequestByOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}

	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                       string
		args                       args
		want                       *activityPb.Response
		wantErr                    bool
		assertErr                  func(err error) bool
		mockGetLoanRequestByOrchId *mockGetLoanRequestByOrchId
	}{
		{
			name: "Should return success if otp already verified",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: false,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP,
				},
				err: nil,
			},
		},
		{
			name: "Should exhaust OTP retries on receiving failure and on attempt count equals max count",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP,
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:           "12345",
							MaxAttempts:   3,
							AttemptsCount: 3,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "Should fail with perm error on receiving failure",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:           "12345",
							MaxAttempts:   3,
							AttemptsCount: 3,
						},
					},
				},
				err: nil,
			},
		},
		{
			name: "Should fail with perm error on receiving max otp failure",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "loan-req-id",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status:    preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
					SubStatus: preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED,
					Details: &preApprovedLoanPb.LoanRequestDetails{
						OtpInfo: &preApprovedLoanPb.LoanRequestDetails_OtpInfo{
							Otp:           "12345",
							MaxAttempts:   3,
							AttemptsCount: 3,
						},
					},
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoanRequestByOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
			}

			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.GetOtpVerificationStatus, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetOtpVerificationStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetOtpVerificationStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetOtpVerificationStatus() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetOtpVerificationStatus() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_CheckProfileValidation(t *testing.T) {
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor:  &types.Actor{},
	}, nil).AnyTimes()

	md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
		User: &userPb.User{
			Profile: &userPb.Profile{PhoneNumber: &commontypes.PhoneNumber{}},
		},
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()

	md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{
		Status:       rpc.StatusOk(),
		BankCustomer: &bankcust.BankCustomer{},
	}, nil).AnyTimes()

	md.authClient.EXPECT().GetDeviceAuth(gomock.Any(), gomock.Any()).Return(&authPb.GetDeviceAuthResponse{
		Status: rpc.StatusOk(),
		Device: &commontypes.Device{},
	}, nil).AnyTimes()

	md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
		FeatureInfo:  nil,
	}, nil).AnyTimes()

	md.eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	type mockVgCustomerClient struct {
		res *vgCustomerPb.FetchCustomerDetailsResponse
		err error
	}
	type mockGetLoanRequestByOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockValidateProfile struct {
		res *vgProfileValidationPb.CheckProfileValidationResponse
		err error
	}

	type mockCreateLse struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}

	type mockGetLseByRefIdandFlow struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}

	type args struct {
		ctx context.Context
		req *preApprovedActivityPb.PalActivityRequest
	}
	tests := []struct {
		name                       string
		args                       args
		want                       *activityPb.Response
		wantErr                    bool
		assertErr                  func(err error) bool
		mockGetLoanRequestByOrchId *mockGetLoanRequestByOrchId
		mockVgCustomerClient       *mockVgCustomerClient
		mockValidateProfile        *mockValidateProfile
		mockCreateLse              *mockCreateLse
		mockGetLseByRefIdandFlow   *mockGetLseByRefIdandFlow
	}{
		{
			name: "Should fail as no such loan request exists",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "Should fail as loan request in failed state",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
				},
				err: nil,
			},
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
		},
		{
			name: "Should fail to fetch customer details from VG",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
				},
				err: nil,
			},
			mockVgCustomerClient: &mockVgCustomerClient{
				res: &vgCustomerPb.FetchCustomerDetailsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
		},
		{
			name: "Fetch customer details gave unknown gender",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
				},
				err: nil,
			},
			mockVgCustomerClient: &mockVgCustomerClient{
				res: &vgCustomerPb.FetchCustomerDetailsResponse{
					Status: rpc.StatusOk(),
					Gender: types.Gender_GENDER_UNSPECIFIED,
				},
				err: nil,
			},
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
		},
		{
			name: "successfully fetch customer details but fail to validate profile",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
				},
				err: nil,
			},
			mockVgCustomerClient: &mockVgCustomerClient{
				res: &vgCustomerPb.FetchCustomerDetailsResponse{
					Status: rpc.StatusOk(),
					Gender: types.Gender_MALE,
				},
				err: nil,
			},
			mockValidateProfile: &mockValidateProfile{
				res: &vgProfileValidationPb.CheckProfileValidationResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
		},
		{
			name: "successfully validate profile",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: false,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
				},
				err: nil,
			},
			mockVgCustomerClient: &mockVgCustomerClient{
				res: &vgCustomerPb.FetchCustomerDetailsResponse{
					Status: rpc.StatusOk(),
					Gender: types.Gender_MALE,
				},
				err: nil,
			},
			mockValidateProfile: &mockValidateProfile{
				res: &vgProfileValidationPb.CheckProfileValidationResponse{
					Status:           rpc.StatusOk(),
					ValidationStatus: vgProfileValidationPb.ValidationStatus_VALIDATION_STATUS_VERIFIED,
				},
				err: nil,
			},
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
		},
		{
			name: "validate profile gave rejected response",
			args: args{
				ctx: context.Background(),
				req: &preApprovedActivityPb.PalActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
					},
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Status: preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
				},
				err: nil,
			},
			mockVgCustomerClient: &mockVgCustomerClient{
				res: &vgCustomerPb.FetchCustomerDetailsResponse{
					Status: rpc.StatusOk(),
					Gender: types.Gender_MALE,
				},
				err: nil,
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockValidateProfile: &mockValidateProfile{
				res: &vgProfileValidationPb.CheckProfileValidationResponse{
					Status:           rpc.StatusOk(),
					ValidationStatus: vgProfileValidationPb.ValidationStatus_VALIDATION_STATUS_REJECTED,
				},
				err: nil,
			},
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result *activityPb.Response
			if tt.mockGetLoanRequestByOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
			}
			if tt.mockVgCustomerClient != nil {
				md.vgCustomerClient.EXPECT().FetchCustomerDetails(gomock.Any(), gomock.Any()).Return(
					tt.mockVgCustomerClient.res, tt.mockVgCustomerClient.err)
			}
			if tt.mockValidateProfile != nil {
				md.vgProfileValidationClient.EXPECT().CheckProfileValidation(gomock.Any(), gomock.Any()).Return(
					tt.mockValidateProfile.res, tt.mockValidateProfile.err)
			}
			if tt.mockCreateLse != nil {
				md.loanStepExecutionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
					tt.mockCreateLse.res, tt.mockCreateLse.err).AnyTimes()
			}
			if tt.mockGetLseByRefIdandFlow != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLseByRefIdandFlow.res, tt.mockGetLseByRefIdandFlow.err)
			}
			got, err := env.ExecuteActivity(palNs.CheckProfileValidation, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckProfileValidation() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckProfileValidation() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckProfileValidation() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckProfileValidation() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_ProcessLoanAccountCreation(t *testing.T) {
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor:  &types.Actor{},
	}, nil).AnyTimes()

	md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
		User: &userPb.User{
			Profile: &userPb.Profile{PhoneNumber: &commontypes.PhoneNumber{}},
		},
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()

	md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{
		Status:       rpc.StatusOk(),
		BankCustomer: &bankcust.BankCustomer{},
	}, nil).AnyTimes()

	md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), gomock.Any()).Return(
		&orderPb.GetOrderWithTransactionsResponse{
			Status: rpc.StatusOk(),
			OrderWithTransactions: &orderPb.OrderWithTransactions{
				Transactions: []*paymentPb.Transaction{
					{},
				},
			},
		}, nil).AnyTimes()

	md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
		FeatureInfo:  nil,
	}, nil).AnyTimes()

	md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	type mockFetchLoanDetails struct {
		res *palVgPb.FetchLoanDetailsResponse
		err error
	}
	type mockGetLoanRequestByOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}
	type mockGetOrdersForActor struct {
		res   *orderPb.GetOrdersForActorResponse
		err   error
		times int
	}
	type mockLoanAccountsGetByActorAndVendor struct {
		res []*preApprovedLoanPb.LoanAccount
		err error
	}
	type mockCreatePi struct {
		res *piPb.CreatePiResponse
		err error
	}
	type args struct {
		ctx context.Context
		req *activityPb.Request
	}
	tests := []struct {
		name                                string
		args                                args
		want                                *activityPb.Response
		wantErr                             bool
		assertErr                           func(err error) bool
		mockGetLoanRequestByOrchId          *mockGetLoanRequestByOrchId
		mockFetchLoanDetails                *mockFetchLoanDetails
		mockGetOrdersForActor               *mockGetOrdersForActor
		mockLoanAccountsGetByActorAndVendor *mockLoanAccountsGetByActorAndVendor
		mockCreatePi                        *mockCreatePi
	}{
		{
			name: "Should fail as no such loan request exists",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "Should fail to fetch loan details",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockFetchLoanDetails: &mockFetchLoanDetails{
				res: &palVgPb.FetchLoanDetailsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
		},
		{
			name: "fetch loan details gave empty account list",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockFetchLoanDetails: &mockFetchLoanDetails{
				res: &palVgPb.FetchLoanDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
		},
		{
			name: "successfully fetch loan account from fetch loan details but fail to fetch order",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockFetchLoanDetails: &mockFetchLoanDetails{
				res: &palVgPb.FetchLoanDetailsResponse{
					Status: rpc.StatusOk(),
					LoanDetails: []*palVgPb.FetchLoanDetailsResponse_LoanDetails{
						{
							AccountNumber: "",
							AccountType:   palVgPb.FetchLoanDetailsResponse_ACCOUNT_TYPE_LOANS,
						},
					},
				},
				err: nil,
			},
			mockGetOrdersForActor: &mockGetOrdersForActor{
				res: &orderPb.GetOrdersForActorResponse{
					Status: rpc.StatusInternal(),
				},
				err:   nil,
				times: 1,
			},
		},
		{
			name: "successfully fetch loan account from fetch loan details but no disbursal order exists",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{},
				err: nil,
			},
			mockFetchLoanDetails: &mockFetchLoanDetails{
				res: &palVgPb.FetchLoanDetailsResponse{
					Status: rpc.StatusOk(),
					LoanDetails: []*palVgPb.FetchLoanDetailsResponse_LoanDetails{
						{
							AccountNumber: "",
							AccountType:   palVgPb.FetchLoanDetailsResponse_ACCOUNT_TYPE_LOANS,
						},
					},
				},
				err: nil,
			},
			mockGetOrdersForActor: &mockGetOrdersForActor{
				res: &orderPb.GetOrdersForActorResponse{
					Status: rpc.StatusOk(),
					Orders: []*orderPb.Order{
						{},
					},
				},
				err:   nil,
				times: 8,
			},
			mockLoanAccountsGetByActorAndVendor: &mockLoanAccountsGetByActorAndVendor{
				res: nil,
				err: epifierrors.ErrTransient,
			},
		},
		{
			name: "successfully fetch disbursal order",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: false,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							TenureInMonths: 12,
						},
					},
				},
				err: nil,
			},
			mockFetchLoanDetails: &mockFetchLoanDetails{
				res: &palVgPb.FetchLoanDetailsResponse{
					Status: rpc.StatusOk(),
					LoanDetails: []*palVgPb.FetchLoanDetailsResponse_LoanDetails{
						{
							AccountNumber: "",
							AccountType:   palVgPb.FetchLoanDetailsResponse_ACCOUNT_TYPE_LOANS,
						},
					},
				},
				err: nil,
			},
			mockGetOrdersForActor: &mockGetOrdersForActor{
				res: &orderPb.GetOrdersForActorResponse{
					Status: rpc.StatusOk(),
					Orders: []*orderPb.Order{
						{Tags: []orderPb.OrderTag{orderPb.OrderTag_LOAN}},
					},
				},
				err:   nil,
				times: 1,
			},
			mockLoanAccountsGetByActorAndVendor: &mockLoanAccountsGetByActorAndVendor{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "successfully process loan account creation",
			args: args{
				ctx: context.Background(),
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			want:    nil,
			wantErr: false,
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					Details: &preApprovedLoanPb.LoanRequestDetails{
						LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
							TenureInMonths: 12,
						},
					},
				},
				err: nil,
			},
			mockFetchLoanDetails: &mockFetchLoanDetails{
				res: &palVgPb.FetchLoanDetailsResponse{
					Status: rpc.StatusOk(),
					LoanDetails: []*palVgPb.FetchLoanDetailsResponse_LoanDetails{
						{
							AccountNumber: "",
							AccountType:   palVgPb.FetchLoanDetailsResponse_ACCOUNT_TYPE_LOANS,
						},
					},
				},
				err: nil,
			},
			mockGetOrdersForActor: &mockGetOrdersForActor{
				res: &orderPb.GetOrdersForActorResponse{
					Status: rpc.StatusOk(),
					Orders: []*orderPb.Order{
						{Tags: []orderPb.OrderTag{orderPb.OrderTag_LOAN}},
					},
				},
				err:   nil,
				times: 1,
			},
			mockLoanAccountsGetByActorAndVendor: &mockLoanAccountsGetByActorAndVendor{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := &activityPb.Response{}

			if tt.mockGetLoanRequestByOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
			}
			if tt.mockFetchLoanDetails != nil {
				md.palVgClient.EXPECT().FetchLoanDetails(gomock.Any(), gomock.Any()).Return(
					tt.mockFetchLoanDetails.res, tt.mockFetchLoanDetails.err)
			}
			if tt.mockGetOrdersForActor != nil {
				md.orderClient.EXPECT().GetOrdersForActor(gomock.Any(), gomock.Any()).Return(
					tt.mockGetOrdersForActor.res, tt.mockGetOrdersForActor.err).Times(tt.mockGetOrdersForActor.times)
			}
			if tt.mockLoanAccountsGetByActorAndVendor != nil {
				md.loanAccountDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockLoanAccountsGetByActorAndVendor.res, tt.mockLoanAccountsGetByActorAndVendor.err)
			}
			if tt.mockCreatePi != nil {
				md.piClient.EXPECT().CreatePi(gomock.Any(), gomock.Any()).Return(tt.mockCreatePi.res, tt.mockCreatePi.err)
			}
			got, err := env.ExecuteActivity(palNs.ProcessLoanAccountCreation, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessLoanAccountCreation() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessLoanAccountCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessLoanAccountCreation() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessLoanAccountCreation() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_ProcessLivenessVendorReview(t *testing.T) {
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil)

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}

	type mockGetVkycSummary struct {
		res *vkyc.GetVKYCSummaryResponse
		err error
	}
	type mockGetLivenessStatus struct {
		res *liveness.GetLivenessStatusResponse
		err error
	}
	type mockGetLoanRequestByOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}

	type mockGetByRefIdAndFlowAndName struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}

	type mockGetAuthStageRefId struct {
		res *authOrchPb.GetAuthStageRefIdResponse
		err error
	}

	type mockGetLivenessSummary struct {
		res *liveness.GetLivenessSummaryResponse
		err error
	}

	tests := []struct {
		name                         string
		args                         args
		want                         *palActivityPb.PalActivityResponse
		wantErr                      bool
		assertErr                    func(err error) bool
		mockVkycSummary              *mockGetVkycSummary
		mockGetLivenessStatus        *mockGetLivenessStatus
		mockGetLoanRequestByOrchId   *mockGetLoanRequestByOrchId
		mockGetByRefIdAndFlowAndName *mockGetByRefIdAndFlowAndName
		mockGetAuthStageRefId        *mockGetAuthStageRefId
		mockGetLivenessSummary       *mockGetLivenessSummary
	}{
		{
			name: "Success Case",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{},
			},
			want:    nil,
			wantErr: false,

			mockGetLivenessStatus: &mockGetLivenessStatus{
				res: &liveness.GetLivenessStatusResponse{
					VideoLocation: "videolacation",
					Status:        rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					ActorId: "actor-1",
				},
				err: nil,
			},
			mockVkycSummary: &mockGetVkycSummary{
				res: &vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNREGISTERED,
						},
					},
				},
				err: nil,
			},

			mockGetByRefIdAndFlowAndName: &mockGetByRefIdAndFlowAndName{
				res: &preApprovedLoanPb.LoanStepExecution{
					ActorId: "actor-1",
					OrchId:  "loan-step-orch-id",
					Details: &preApprovedLoanPb.LoanStepExecutionDetails{Details: &preApprovedLoanPb.LoanStepExecutionDetails_LivenessStepData{&preApprovedLoanPb.LivenessStepData{
						AttemptId: "attemptId",
					}}},
				},
				err: nil,
			},

			mockGetAuthStageRefId: &mockGetAuthStageRefId{
				res: &authOrchPb.GetAuthStageRefIdResponse{
					Status:         rpc.StatusOk(),
					AuthStageRefId: "auth-stage-ref-id",
				},
				err: nil,
			},
			mockGetLivenessSummary: &mockGetLivenessSummary{
				res: &liveness.GetLivenessSummaryResponse{
					Status: rpc.StatusOk(),
					Summary: &liveness.LivenessSummary{
						LivenessAttemptId: "attemptId",
					},
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockVkycSummary != nil {
				md.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(
					tt.mockVkycSummary.res, tt.mockVkycSummary.err)
			}
			if tt.mockGetLivenessStatus != nil {
				md.livenessClient.EXPECT().GetLivenessStatus(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLivenessStatus.res, tt.mockGetLivenessStatus.err)
			}
			if tt.mockGetLoanRequestByOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
			}
			if tt.mockGetByRefIdAndFlowAndName != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetByRefIdAndFlowAndName.res, tt.mockGetByRefIdAndFlowAndName.err)
			}
			if tt.mockGetLivenessSummary != nil {
				md.livenessClient.EXPECT().GetLivenessSummary(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLivenessSummary.res, tt.mockGetLivenessSummary.err)
			}
			if tt.mockGetAuthStageRefId != nil {
				md.authOrchClient.EXPECT().GetAuthStageRefId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetAuthStageRefId.res, tt.mockGetAuthStageRefId.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.ProcessLivenessVendorReview, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessLivenessVendorReview() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessLivenessVendorReview() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessLivenessVendorReview() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessLivenessVendorReview() got = %v, want %v", result, tt.want)
				return
			}

		})
	}
}

func TestProcessor_CheckForRisk(t *testing.T) {
	p, md, assertTest := newPalActProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}

	type mockGetLoanRequestByOrchId struct {
		res *preApprovedLoanPb.LoanRequest
		err error
	}

	type mockUpdate struct {
		err error
	}

	type mockGetUserProfile struct {
		res *profile.GetUserProfileResponse
		err error
	}

	type mockCreateLse struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}

	type mockGetLseByRefIdandFlow struct {
		res *preApprovedLoanPb.LoanStepExecution
		err error
	}

	tests := []struct {
		name                       string
		args                       args
		want                       *palActivityPb.PalActivityResponse
		wantErr                    bool
		assertErr                  func(err error) bool
		mockGetLoanRequestByOrchId *mockGetLoanRequestByOrchId
		mockUpdate                 *mockUpdate
		mockGetUserProfile         *mockGetUserProfile
		mockCreateLse              *mockCreateLse
		mockGetLseByRefIdandFlow   *mockGetLseByRefIdandFlow
	}{
		{
			name: "Non risky case",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{},
			},
			want:    nil,
			wantErr: false,

			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					ActorId: "actor-1",
				},
				err: nil,
			},

			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},

			mockGetUserProfile: &mockGetUserProfile{res: &profile.GetUserProfileResponse{
				Status: rpc.StatusOk(),
				AccountsInfo: []*profile.AccountInfo{
					{
						PresentFreezeStatus: enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN,
					},
				},
			}},

			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
		},
		{
			name: "risky case",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{},
			},
			want:    nil,
			wantErr: true,
			mockGetLseByRefIdandFlow: &mockGetLseByRefIdandFlow{
				res: &preApprovedLoanPb.LoanStepExecution{

					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
				err: nil,
			},
			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					ActorId: "actor-1",
				},
				err: nil,
			},
			mockCreateLse: &mockCreateLse{
				res: &preApprovedLoanPb.LoanStepExecution{
					Status: preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				},
				err: nil,
			},
			mockGetUserProfile: &mockGetUserProfile{res: &profile.GetUserProfileResponse{
				Status: rpc.StatusOk(),
				AccountsInfo: []*profile.AccountInfo{
					{
						PresentFreezeStatus: enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE,
					},
				},
			}},
			mockUpdate: &mockUpdate{err: nil},
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to get loan request",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{},
			},
			want:    nil,
			wantErr: true,

			mockGetLoanRequestByOrchId: &mockGetLoanRequestByOrchId{
				res: &preApprovedLoanPb.LoanRequest{
					ActorId: "actor-1",
				},
				err: epifierrors.ErrTransient,
			},
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLoanRequestByOrchId != nil {
				md.loanRequestDao.EXPECT().GetByOrchId(gomock.Any(), gomock.Any()).Return(
					tt.mockGetLoanRequestByOrchId.res, tt.mockGetLoanRequestByOrchId.err)
			}

			if tt.mockUpdate != nil {
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockUpdate.err)
			}

			if tt.mockGetUserProfile != nil {
				md.profileClient.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(
					tt.mockGetUserProfile.res, tt.mockGetUserProfile.err)
			}

			if tt.mockCreateLse != nil {
				md.loanStepExecutionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
					tt.mockCreateLse.res, tt.mockCreateLse.err).AnyTimes()
			}

			if tt.mockGetLseByRefIdandFlow != nil {
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					tt.mockGetLseByRefIdandFlow.res, tt.mockGetLseByRefIdandFlow.err)
			}

			var result *activityPb.Response
			got, err := env.ExecuteActivity(palNs.CheckForRisk, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckForRisk() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckForRisk() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckForRisk() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckForRisk() got = %v, want %v", result, tt.want)
				return
			}

		})
	}
}

func TestProcessor_DeactivateLoanOffer(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.DeactivateLoanOfferRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.DeactivateLoanOfferResponse
		mockFunc  func(md *mockedDependencies)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return transient error when loan request is not found",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.DeactivateLoanOfferRequest{
					RequestHeader: nil,
					LoanStep: &preApprovedLoanPb.LoanStepExecution{
						Id:    "lse-id-1",
						RefId: "ref-id-1",
					},
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockFunc: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
		},
		{
			name: "success response if vendor:loan_program key not found in the config map",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.DeactivateLoanOfferRequest{
					RequestHeader: nil,
					LoanStep: &preApprovedLoanPb.LoanStepExecution{
						Id:    "lse-id-1",
						RefId: "ref-id-1",
					},
				},
			},
			want:    nil,
			wantErr: false,
			mockFunc: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(
					&preApprovedLoanPb.LoanRequest{
						Id:          "red-id-1",
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_FED_REAL_TIME,
					}, nil).Times(1)
				md.eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			},
		},
		{
			name: "success response if lse value not found in the list",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.DeactivateLoanOfferRequest{
					RequestHeader: nil,
					LoanStep: &preApprovedLoanPb.LoanStepExecution{
						Id:        "lse-id-1",
						RefId:     "ref-id-1",
						StepName:  preApprovedLoanPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE,
						Status:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
						SubStatus: preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					},
				},
			},
			want:    nil,
			wantErr: false,
			mockFunc: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(
					&preApprovedLoanPb.LoanRequest{
						Id:          "red-id-1",
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					}, nil).Times(1)
				md.eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			},
		},
		{
			name: "should return transient failure when loan offer not found",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.DeactivateLoanOfferRequest{
					RequestHeader: nil,
					LoanStep: &preApprovedLoanPb.LoanStepExecution{
						Id:        "lse-id-1",
						RefId:     "ref-id-1",
						StepName:  preApprovedLoanPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE,
						Status:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
						SubStatus: preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION,
					},
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockFunc: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(
					&preApprovedLoanPb.LoanRequest{
						Id:          "red-id-1",
						OfferId:     "offer-id-1",
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					}, nil).Times(1)
				md.eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-id-1").Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
		},
		{
			name: "success response",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.DeactivateLoanOfferRequest{
					RequestHeader: nil,
					LoanStep: &preApprovedLoanPb.LoanStepExecution{
						Id:        "lse-id-1",
						RefId:     "ref-id-1",
						StepName:  preApprovedLoanPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE,
						Status:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
						SubStatus: preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION,
					},
				},
			},
			want:    nil,
			wantErr: false,
			mockFunc: func(md *mockedDependencies) {
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-id-1").Return(
					&preApprovedLoanPb.LoanRequest{
						Id:          "red-id-1",
						OfferId:     "offer-id-1",
						Vendor:      preApprovedLoanPb.Vendor_LIQUILOANS,
						LoanProgram: preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					}, nil).Times(1)
				md.eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-id-1").Return(&preApprovedLoanPb.LoanOffer{
					Id:                             "offer-id-1",
					LoanOfferEligibilityCriteriaId: "loec-id-1",
				}, nil).Times(1)
				md.loecDao.EXPECT().GetById(gomock.Any(), "loec-id-1").Return(&preApprovedLoanPb.LoanOfferEligibilityCriteria{Id: "loec-id-1"}, nil).Times(1)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), "offer-id-1").Return(nil).Times(1)
				md.loecDao.EXPECT().Update(gomock.Any(), &preApprovedLoanPb.LoanOfferEligibilityCriteria{
					Id:        "loec-id-1",
					Status:    preApprovedLoanPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED,
					SubStatus: preApprovedLoanPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR,
				}, []preApprovedLoanPb.LoanOfferEligibilityCriteriaFieldMask{
					preApprovedLoanPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
					preApprovedLoanPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
				}).Return(nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newPalActProcessorWithMocks(t)
			defer assertTest()
			tt.mockFunc(md)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.DeactivateLoanOffer, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("DeactivateLoanOffer() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("DeactivateLoanOffer() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("DeactivateLoanOffer() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("DeactivateLoanOffer() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func TestProcessor_GetExpiredPropertyForStage(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.GetExpiredPropertyForStageRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.GetExpiredPropertyForStageResponse
		mockFunc  func(md *mockedDependencies)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return transient error when loan step is not found",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.GetExpiredPropertyForStageRequest{
					LseId: "lse-id-1",
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			mockFunc: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
		},
		{
			name: "should return false for mark non-retrybale as expired response required status and sub-statuses matches",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.GetExpiredPropertyForStageRequest{
					LseId:        "lse-id-1",
					LseStatus:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					LseSubStatus: []preApprovedLoanPb.LoanStepExecutionSubStatus{preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED},
				},
			},
			want: &palActivityPb.GetExpiredPropertyForStageResponse{
				MarkNonRetryableAsExpired: false,
			},
			wantErr: false,
			mockFunc: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(
					&preApprovedLoanPb.LoanStepExecution{
						Id:        "lse-id-1",
						Status:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus: preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW,
					}, nil).Times(1)
			},
		},
		{
			name: "should return true for mark non-retrybale as expired response required status and sub-statuses matches",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.GetExpiredPropertyForStageRequest{
					LseId:        "lse-id-1",
					LseStatus:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					LseSubStatus: []preApprovedLoanPb.LoanStepExecutionSubStatus{preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED},
				},
			},
			want: &palActivityPb.GetExpiredPropertyForStageResponse{
				MarkNonRetryableAsExpired: true,
			},
			wantErr: false,
			mockFunc: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(
					&preApprovedLoanPb.LoanStepExecution{
						Id:        "lse-id-1",
						Status:    preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						SubStatus: preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
					}, nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newPalActProcessorWithMocks(t)
			defer assertTest()
			tt.mockFunc(md)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.GetExpiredPropertyForStageResponse
			got, err := env.ExecuteActivity(palNs.GetExpiredPropertyForStage, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetExpiredPropertyForStage() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetExpiredPropertyForStage() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetExpiredPropertyForStage() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetExpiredPropertyForStage() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
