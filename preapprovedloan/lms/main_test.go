package lms

import (
	"os"
	"testing"

	"gorm.io/gorm"

	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	lmsConf              *common.Lms
	dbResourceProvider   *storageV2.DBResourceProvider[*gorm.DB]
	txnExecutorsProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	dbNameToDb           = map[string]*gorm.DB{}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	// nolint : dogsled
	conf, _, teardown := test.InitTestServer()
	lmsConf = conf.Lms

	var releaseDbConns func()

	dbResourceProvider, txnExecutorsProvider, dbNameToDb, releaseDbConns = test.InitDbConns(conf, true, true)
	exitCode := m.Run()

	releaseDbConns()
	teardown()
	os.Exit(exitCode)
}
