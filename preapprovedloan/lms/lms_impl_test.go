package lms

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	datetimeMocksPkg "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epificontext"
	moneyPb "github.com/epifi/be-common/pkg/money"
	test "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/api/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
)

var (
	vendorLoanSchedule1 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Amount:              moneyPb.ParseInt(746, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 12, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "3",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Amount:              moneyPb.ParseInt(946, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 10, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Amount:              moneyPb.ParseInt(746, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 11, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	vendorLoanSchedule2 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Amount:              moneyPb.ParseInt(500, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 10, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	vendorLoanSchedule3 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Amount:              moneyPb.ParseInt(946, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 10, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Amount:              moneyPb.ParseInt(946, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	accountId11 = "account-id-11"
	liInfo11    = &preapprovedloan.LoanInstallmentInfo{
		Id:                    "Lii-11",
		AccountId:             accountId11,
		TotalAmount:           moneyPb.ParseInt(50000, "INR"),
		StartDate:             &date.Date{Year: 2023, Month: 8, Day: 15},
		EndDate:               &date.Date{Year: 2024, Month: 6, Day: 15},
		TotalInstallmentCount: 10,
		NextInstallmentDate:   &date.Date{Year: 2023, Month: 10, Day: 5},
		Details:               nil,
		Status:                palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
		DeactivatedAt:         nil,
	}
	liInfoUpdated11 = &preapprovedloan.LoanInstallmentInfo{
		Id:                    "Lii-11",
		AccountId:             accountId11,
		TotalAmount:           moneyPb.ParseInt(50000, "INR"),
		StartDate:             &date.Date{Year: 2023, Month: 8, Day: 15},
		EndDate:               &date.Date{Year: 2024, Month: 6, Day: 15},
		TotalInstallmentCount: 10,
		NextInstallmentDate:   &date.Date{Year: 2023, Month: 11, Day: 5},
		Details:               nil,
		Status:                palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
		DeactivatedAt:         nil,
	}
	lip11 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Id:                  "lip11-3",
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 12, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "3",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip11-1",
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip11-2",
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	lipUpdated11 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Id:                  "lip11-1",
			Amount:              moneyPb.ParseInt(946, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 10, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip11-2",
			Amount:              moneyPb.ParseInt(746, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 11, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip11-3",
			Amount:              moneyPb.ParseInt(746, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 12, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "3",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	accountId12 = "account-id-12"
	// ('inst-info-id-12', 'account-id-12', '{"currency_code": "INR", "units": 50000}', '2023-08-15', '2024-06-15', 10, '2023-10-05', '{}', 'LOAN_INSTALLMENT_INFO_STATUS_ACTIVE', NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL) ON CONFLICT (id) DO NOTHING;
	liInfo12 = &preapprovedloan.LoanInstallmentInfo{
		Id:                    "Lii-12",
		AccountId:             accountId12,
		TotalAmount:           moneyPb.ParseInt(50000, "INR"),
		StartDate:             &date.Date{Year: 2023, Month: 8, Day: 15},
		EndDate:               &date.Date{Year: 2024, Month: 6, Day: 15},
		TotalInstallmentCount: 10,
		NextInstallmentDate:   &date.Date{Year: 2023, Month: 10, Day: 5},
		Details:               nil,
		Status:                palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
		DeactivatedAt:         nil,
	}
	lip12 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Id:                  "lip-12-1",
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip-12-2",
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	lipUpdated12 = []*preapprovedloan.LoanInstallmentPayout{
		{
			Id:                  "lip-12-1",
			Amount:              moneyPb.ParseInt(500, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 10, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip-12-2",
			Amount:              moneyPb.ParseInt(0, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	lipUpdatedFullyPaid = []*preapprovedloan.LoanInstallmentPayout{
		{
			Id:                  "lip-12-1",
			Amount:              moneyPb.ParseInt(946, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 10, Day: 5},
			PayoutDate:          &date.Date{Year: 2023, Month: 10, Day: 5},
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			VendorInstallmentId: "1",
			Principal:           moneyPb.ParseInt(746, "INR"),
			Interest:            moneyPb.ParseInt(200, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
		{
			Id:                  "lip-12-2",
			Amount:              moneyPb.ParseInt(946, "INR"),
			DueDate:             &date.Date{Year: 2023, Month: 11, Day: 5},
			PayoutDate:          nil,
			Status:              preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			VendorInstallmentId: "2",
			Principal:           moneyPb.ParseInt(646, "INR"),
			Interest:            moneyPb.ParseInt(300, "INR"),
			DueAmount:           moneyPb.ParseInt(946, "INR"),
		},
	}
	loanAccount11SyncUpdate = &preapprovedloan.LoanAccount{
		Id: accountId11,
		Details: &preapprovedloan.LoanAccountDetails{
			LmsSyncedAt: timestampPb.New(time.Date(2022, 4, 1, 11, 48, 50, 0, datetimePkg.IST)),
		},
	}
	loanAccount12SyncUpdate = &preapprovedloan.LoanAccount{
		Id: accountId12,
		Details: &preapprovedloan.LoanAccountDetails{
			LmsSyncedAt: timestampPb.New(time.Date(2022, 4, 1, 11, 48, 50, 0, datetimePkg.IST)),
		},
	}
	loanAccountPaidUpdate = &preapprovedloan.LoanAccount{
		Id:     accountId12,
		Status: preapprovedloan.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
		Details: &preapprovedloan.LoanAccountDetails{
			LmsSyncedAt: timestampPb.New(time.Date(2022, 4, 1, 11, 48, 50, 0, datetimePkg.IST)),
		},
	}
	affectedTestTables = []string{
		"loan_requests",
		"loan_accounts",
		"loan_offers",
		"loan_step_executions",
		"loan_offer_eligibility_criteria",
		"loan_installment_info",
		"loan_installment_payout",
		"loan_applicants",
		"loan_activities",
		"loan_payment_requests",
		"collection_leads",
		"collection_allocations",
		"collection_communications",
		"fetched_assets",
	}
	lipUpdateFilters = []palPb.LoanInstallmentPayoutFieldMask{
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_AMOUNT,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PRINCIPAL_AMT,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_INTEREST_AMT,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_STATUS,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_DATE,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_AMT,
		palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PAYOUT_DATE,
	}
)

func TestNewLms_updateLoanSchedule(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	loanAccountDaoMock := daoMocks.NewMockLoanAccountsDao(ctrl)
	liiMock := daoMocks.NewMockLoanInstallmentInfoDao(ctrl)
	lipMock := daoMocks.NewMockLoanInstallmentPayoutDao(ctrl)
	loanActivityMock := daoMocks.NewMockLoanActivityDao(ctrl)
	timeMock := datetimeMocksPkg.NewMockTime(ctrl)

	lms := Lms{
		txnExecutorProvider: txnExecutorsProvider,
		loanAccountDao:      loanAccountDaoMock,
		installmentInfoDao:  liiMock,
		payoutDao:           lipMock,
		loanActivityDao:     loanActivityMock,
		conf:                lmsConf,
		time:                timeMock,
	}

	type args struct {
		loanInstallments []*preapprovedloan.LoanInstallmentPayout
		loanAccount      *palPb.LoanAccount
		loanHeader       *palPb.LoanHeader
	}

	tests := []struct {
		name        string
		args        args
		mockExecute func()
	}{
		{
			name: "user paid first installment, second installment unpaid",
			args: args{
				loanInstallments: vendorLoanSchedule1,
				loanAccount: &palPb.LoanAccount{
					Id:      accountId11,
					Details: &palPb.LoanAccountDetails{},
				},
			},
			mockExecute: func() {
				liiMock.EXPECT().GetByActiveAccountId(gomock.Any(), accountId11).Return(liInfo11, nil)
				lipMock.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), liInfo11.GetId()).Return(lip11, nil)
				for i, _ := range lip11 {
					lipMock.EXPECT().Update(gomock.Any(), lipUpdated11[i], lipUpdateFilters).Return(nil)
				}
				loanActivityMock.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				liiMock.EXPECT().Update(gomock.Any(), liInfoUpdated11, []palPb.LoanInstallmentInfoFieldMask{
					palPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE,
				}).Return(nil).Times(1)
				timeMock.EXPECT().Now().Return(time.Date(2022, 4, 1, 11, 48, 50, 0, datetimePkg.IST))
				loanAccountDaoMock.EXPECT().Update(gomock.Any(), loanAccount11SyncUpdate,
					[]preapprovedloan.LoanAccountFieldMask{preapprovedloan.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DETAILS}).
					Return(nil)
			},
		},
		{
			name: "after first installment is partially paid",
			args: args{
				loanInstallments: vendorLoanSchedule2,
				loanAccount: &palPb.LoanAccount{
					Id:      accountId12,
					Details: &palPb.LoanAccountDetails{},
				},
			},
			mockExecute: func() {
				liiMock.EXPECT().GetByActiveAccountId(gomock.Any(), accountId12).Return(liInfo12, nil)
				lipMock.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), liInfo12.GetId()).Return(lip12, nil)
				for i, _ := range lip12 {
					lipMock.EXPECT().Update(gomock.Any(), lipUpdated12[i], lipUpdateFilters).Return(nil)
				}
				loanActivityMock.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				timeMock.EXPECT().Now().Return(time.Date(2022, 4, 1, 11, 48, 50, 0, datetimePkg.IST))
				loanAccountDaoMock.EXPECT().Update(gomock.Any(),
					loanAccount12SyncUpdate,
					[]preapprovedloan.LoanAccountFieldMask{preapprovedloan.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DETAILS}).
					Return(nil)
			},
		},

		{
			name: "after installment is fully paid, account should be closed",
			args: args{
				loanInstallments: vendorLoanSchedule3,
				loanAccount: &palPb.LoanAccount{
					Id:      accountId12,
					Details: &palPb.LoanAccountDetails{},
				},
			},
			mockExecute: func() {
				liiMock.EXPECT().GetByActiveAccountId(gomock.Any(), accountId12).Return(liInfo12, nil)
				lipMock.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), liInfo12.GetId()).Return(lip12, nil)
				for i, _ := range lip12 {
					lipMock.EXPECT().Update(gomock.Any(), lipUpdatedFullyPaid[i], lipUpdateFilters).Return(nil)
				}
				loanActivityMock.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				timeMock.EXPECT().Now().Return(time.Date(2022, 4, 1, 11, 48, 50, 0, datetimePkg.IST))
				loanAccountDaoMock.EXPECT().Update(gomock.Any(),
					loanAccountPaidUpdate,
					[]preapprovedloan.LoanAccountFieldMask{preapprovedloan.
						LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS, preapprovedloan.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DETAILS}).
					Return(nil)
			},
		},
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
	defer cancel()
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LIQUILOANS_PL)
	test.TruncateAndPopulateRdsFixturesForMultipleDBs(t, dbNameToDb, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockExecute != nil {
				tt.mockExecute()
			}
			err := lms.updateLoanSchedules(ctx, tt.args.loanInstallments, tt.args.loanAccount, nil, nil)
			if err != nil {
				t.Errorf("failed to update loan schedule, %v", err)
			}
		})
	}
}

func TestLms_GetOverdueAmountForAccount(t *testing.T) {
	t.Parallel()
	var (
		loanAccountId1         = "loan-account-id-1"
		loanInstallmentInfoId1 = "loan-installment-info-id-1"
		loanHeader1            = &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_FLDG,
			Vendor:      palPb.Vendor_LIQUILOANS,
		}
		loanInstallmentInfo1 = &palPb.LoanInstallmentInfo{Id: loanInstallmentInfoId1}

		currentTime = time.Now()
	)

	type args struct {
		ctx context.Context
		req *GetOverdueAmountForAccountRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockLoanInstallmentInfoDao *daoMocks.MockLoanInstallmentInfoDao, mockPayoutsDao *daoMocks.MockLoanInstallmentPayoutDao)
		want       *GetOverdueAmountForAccountResponse
		wantErr    bool
	}{
		{
			name: "should return error when dao call to fetch installment info returns error",
			args: args{
				ctx: context.Background(),
				req: &GetOverdueAmountForAccountRequest{
					LoanHeader:    loanHeader1,
					LoanAccountId: loanAccountId1,
				},
			},
			setupMocks: func(mockLoanInstallmentInfoDao *daoMocks.MockLoanInstallmentInfoDao, mockPayoutsDao *daoMocks.MockLoanInstallmentPayoutDao) {
				mockLoanInstallmentInfoDao.EXPECT().GetByActiveAccountId(context.Background(), loanAccountId1).Return(nil, errors.New("error fetching installment info from db"))
			},
			wantErr: true,
		},
		{
			name: "should return error when dao call to fetch loan payouts returns error",
			args: args{
				ctx: context.Background(),
				req: &GetOverdueAmountForAccountRequest{
					LoanHeader:    loanHeader1,
					LoanAccountId: loanAccountId1,
				},
			},
			setupMocks: func(mockLoanInstallmentInfoDao *daoMocks.MockLoanInstallmentInfoDao, mockPayoutsDao *daoMocks.MockLoanInstallmentPayoutDao) {
				mockLoanInstallmentInfoDao.EXPECT().GetByActiveAccountId(context.Background(), loanAccountId1).Return(loanInstallmentInfo1, nil)
				mockPayoutsDao.EXPECT().GetByLoanInstallmentInfoId(context.Background(), loanInstallmentInfo1.GetId()).Return(nil, errors.New("error fetching payouts from db"))
			},
			wantErr: true,
		},
		{
			name: "should return the correct overdue amount for the given loan account",
			args: args{
				ctx: context.Background(),
				req: &GetOverdueAmountForAccountRequest{
					LoanHeader:    loanHeader1,
					LoanAccountId: loanAccountId1,
				},
			},
			setupMocks: func(mockLoanInstallmentInfoDao *daoMocks.MockLoanInstallmentInfoDao, mockPayoutsDao *daoMocks.MockLoanInstallmentPayoutDao) {
				mockLoanInstallmentInfoDao.EXPECT().GetByActiveAccountId(context.Background(), loanAccountId1).Return(loanInstallmentInfo1, nil)
				mockPayoutsDao.EXPECT().GetByLoanInstallmentInfoId(context.Background(), loanInstallmentInfo1.GetId()).Return([]*palPb.LoanInstallmentPayout{
					{
						DueAmount: moneyPb.AmountINR(100).GetPb(),
						Amount:    moneyPb.AmountINR(100).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime.AddDate(0, 0, -10), datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
					},
					{
						DueAmount: moneyPb.AmountINR(200).GetPb(),
						Amount:    moneyPb.AmountINR(200).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime.AddDate(0, 0, -9), datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
					},
					{
						DueAmount: moneyPb.AmountINR(400).GetPb(),
						Amount:    moneyPb.AmountINR(400).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime.AddDate(0, 0, -3), datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
					},
					{
						DueAmount: moneyPb.AmountINR(800).GetPb(),
						Amount:    moneyPb.AmountINR(400).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime.AddDate(0, 0, -2), datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
					},
					{
						DueAmount: moneyPb.AmountINR(1600).GetPb(),
						Amount:    moneyPb.AmountINR(0).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime.AddDate(0, 0, -1), datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					},
					{
						DueAmount: moneyPb.AmountINR(3200).GetPb(),
						Amount:    moneyPb.AmountINR(0).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime, datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					},
					{
						DueAmount: moneyPb.AmountINR(6400).GetPb(),
						Amount:    moneyPb.AmountINR(0).GetPb(),
						DueDate:   datetimePkg.TimeToDateInLoc(currentTime.AddDate(0, 0, 1), datetimePkg.IST),
						Status:    palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					},
				}, nil)
			},
			want:    &GetOverdueAmountForAccountResponse{OverdueAmount: moneyPb.AmountINR(2000).GetPb()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockLoanInstallmentInfoDao := daoMocks.NewMockLoanInstallmentInfoDao(ctr)
			mockLoanPayoutDao := daoMocks.NewMockLoanInstallmentPayoutDao(ctr)
			tt.setupMocks(mockLoanInstallmentInfoDao, mockLoanPayoutDao)

			l := &Lms{
				installmentInfoDao: mockLoanInstallmentInfoDao,
				payoutDao:          mockLoanPayoutDao,
			}
			got, err := l.GetOverdueAmountForAccount(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOverdueAmountForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOverdueAmountForAccount() got = %v, want %v", got, tt.want)
			}
		})
	}
}
