package workflow_test

import (
	"fmt"
	"testing"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	palWorkflow "github.com/epifi/gamma/preapprovedloan/workflow"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/workflow"
)

func TestPreApprovedLoanApplication(t *testing.T) {
	t.Parallel()
	const defaultWorkflowID = "default-test-workflow-id"

	type mockGetWorkflowProcessingParams struct {
		res *celestialPb.WorkflowProcessingParams
		err error
	}
	type mockInitiateWorkflowStage struct {
		stage  workflowPb.Stage
		status stagePb.Status
		err    error
	}
	type mockCheckKycEligibility struct {
		req *activityPb.Request
		res *palActivityPb.CheckEligibilityActivityResponse
		err error
	}
	type mockUpdateWorkflowStageStatus struct {
		times int
	}
	type mockPublishWorkflowUpdateEvent struct {
		times int
	}
	type mockUpdateLoanRequest struct {
		err error
		res *activityPb.Response
	}
	type mockKycChildWorkflow struct {
		err error
	}
	type args struct {
		ctx workflow.Context
		req *workflowPb.Request
	}
	tests := []struct {
		name                            string
		args                            args
		wantErr                         bool
		mockGetWorkflowProcessingParams *mockGetWorkflowProcessingParams
		mockInitiateWorkflowStage       *mockInitiateWorkflowStage
		mockCheckKycEligibility         *mockCheckKycEligibility
		mockUpdateWorkflowStageStatus   *mockUpdateWorkflowStageStatus
		mockPublishWorkflowUpdateEvent  *mockPublishWorkflowUpdateEvent
		mockUpdateLoanRequest           *mockUpdateLoanRequest
		mockKycChildWorkflow            *mockKycChildWorkflow
	}{
		{
			name: "Failed to fetch workflow processing params",
			mockGetWorkflowProcessingParams: &mockGetWorkflowProcessingParams{
				err: fmt.Errorf("failed to fetch processing params"),
			},
			wantErr: true,
		},
		{
			name:    "Failed to initiate KYC workflow stage",
			wantErr: true,
			mockGetWorkflowProcessingParams: &mockGetWorkflowProcessingParams{
				res: &celestialPb.WorkflowProcessingParams{
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_PRE_APPROVED_LOAN,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStage: &mockInitiateWorkflowStage{
				stage:  workflowPb.Stage_KYC,
				status: stagePb.Status_INITIATED,
				err:    fmt.Errorf("failed to initiate workflow stage"),
			},
		},
		{
			name:    "Fail to check KYC eligibility",
			wantErr: true,
			mockGetWorkflowProcessingParams: &mockGetWorkflowProcessingParams{
				res: &celestialPb.WorkflowProcessingParams{
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_PRE_APPROVED_LOAN,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStage: &mockInitiateWorkflowStage{
				stage:  workflowPb.Stage_KYC,
				status: stagePb.Status_INITIATED,
				err:    nil,
			},
			mockCheckKycEligibility: &mockCheckKycEligibility{
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
				},
				res: nil,
				err: epifitemporal.NewPermanentError(epifierrors.ErrPermanent),
			},
			mockPublishWorkflowUpdateEvent: &mockPublishWorkflowUpdateEvent{times: 1},
			mockUpdateWorkflowStageStatus:  &mockUpdateWorkflowStageStatus{times: 1},
		},
		{
			name:    "KYC stage goes to manual intervention",
			wantErr: true,
			mockGetWorkflowProcessingParams: &mockGetWorkflowProcessingParams{
				res: &celestialPb.WorkflowProcessingParams{
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_PRE_APPROVED_LOAN,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStage: &mockInitiateWorkflowStage{
				stage:  workflowPb.Stage_KYC,
				status: stagePb.Status_INITIATED,
				err:    nil,
			},
			mockCheckKycEligibility: &mockCheckKycEligibility{
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
				},
				res: nil,
				err: epifitemporal.NewTransientError(fmt.Errorf("kyc retries exhausted")),
			},
			mockPublishWorkflowUpdateEvent: &mockPublishWorkflowUpdateEvent{times: 1},
			mockUpdateWorkflowStageStatus:  &mockUpdateWorkflowStageStatus{times: 1},
			mockUpdateLoanRequest: &mockUpdateLoanRequest{
				err: nil,
				res: nil,
			},
		},
		{
			name:    "Fail child workflow for KYC",
			wantErr: true,
			mockGetWorkflowProcessingParams: &mockGetWorkflowProcessingParams{
				res: &celestialPb.WorkflowProcessingParams{
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_PRE_APPROVED_LOAN,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStage: &mockInitiateWorkflowStage{
				stage:  workflowPb.Stage_KYC,
				status: stagePb.Status_INITIATED,
				err:    nil,
			},
			mockKycChildWorkflow: &mockKycChildWorkflow{err: epifitemporal.NewPermanentError(epifierrors.ErrPermanent)},
			mockCheckKycEligibility: &mockCheckKycEligibility{
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
				},
				res: &palActivityPb.CheckEligibilityActivityResponse{LoanStepExecutionOrchId: "loan-step-id"},
				err: nil,
			},
			mockPublishWorkflowUpdateEvent: &mockPublishWorkflowUpdateEvent{times: 1},
			mockUpdateWorkflowStageStatus:  &mockUpdateWorkflowStageStatus{times: 2},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&palActivity.Processor{})
			env.RegisterActivity(&celestialActivity.Processor{})

			if tt.mockGetWorkflowProcessingParams != nil {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParams), mock.Anything, defaultWorkflowID).
					Return(tt.mockGetWorkflowProcessingParams.res, tt.mockGetWorkflowProcessingParams.err)
			}
			if tt.mockInitiateWorkflowStage != nil {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStage), mock.Anything, defaultWorkflowID, tt.mockInitiateWorkflowStage.stage, tt.mockInitiateWorkflowStage.status).
					Return(tt.mockInitiateWorkflowStage.err)
			}
			if tt.mockCheckKycEligibility != nil {
				env.OnActivity(string(palNs.CheckKycEligibility), mock.Anything, tt.mockCheckKycEligibility.req).Return(
					tt.mockCheckKycEligibility.res, tt.mockCheckKycEligibility.err)
			}
			if tt.mockUpdateWorkflowStageStatus != nil {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStageStatus), mock.Anything, defaultWorkflowID, mock.Anything, mock.Anything).
					Return(nil).Times(tt.mockUpdateWorkflowStageStatus.times)
			}
			if tt.mockPublishWorkflowUpdateEvent != nil {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEvent), mock.Anything, defaultWorkflowID).
					Return(nil).Times(tt.mockPublishWorkflowUpdateEvent.times)
			}
			if tt.mockUpdateLoanRequest != nil {
				env.OnActivity(string(palNs.UpdateLoanRequest), mock.Anything, mock.Anything).Return(
					tt.mockUpdateLoanRequest.res, tt.mockUpdateLoanRequest.err)
			}
			if tt.mockKycChildWorkflow != nil {
				env.RegisterWorkflow(palWorkflow.KycCheck)
				env.OnWorkflow(string(palNs.KycCheck), mock.Anything, mock.Anything).Return(tt.mockKycChildWorkflow.err)
			}

			env.ExecuteWorkflow(palWorkflow.PreApprovedLoanApplication, tt.args.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("PreApprovedLoanApplication() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
