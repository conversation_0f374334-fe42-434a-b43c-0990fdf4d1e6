package lamf

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"github.com/samber/lo"
	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/epifitech"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

var (
	groupStageRunOrder = []palPb.GroupStage{
		palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
		palPb.GroupStage_GROUP_STAGE_KYC,
		palPb.GroupStage_GROUP_STAGE_VERIFY_LOAN_DETAILS,
		palPb.GroupStage_GROUP_STAGE_LIEN_MARK,
		palPb.GroupStage_GROUP_STAGE_INIT_LOAN,
		palPb.GroupStage_GROUP_STAGE_KFS,
		palPb.GroupStage_GROUP_STAGE_E_SIGN,
		palPb.GroupStage_GROUP_STAGE_MANDATE,
		palPb.GroupStage_GROUP_STAGE_DRAWDOWN,
		palPb.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION,
	}
)

type Provider struct{}

var _ providers.IVendorProvider = &Provider{}

func NewProvider() *Provider {
	return &Provider{}
}

func (p *Provider) GetStages(ctx workflow.Context, req *providers.GetStagesRequest) (*providers.GetStagesResponse, error) {
	res := &providers.GetStagesResponse{
		Stages: make([]stages.IStage, 0),
	}

	switch req.GroupStage {
	case palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION:
		res.Stages = append(res.Stages, NewFiftyfinUserRegistration())
	case palPb.GroupStage_GROUP_STAGE_KYC:
		kycV2Flag := workflow.GetVersion(ctx, "kyc-v2", workflow.DefaultVersion, 1)
		if kycV2Flag == 1 {
			res.Stages = append(res.Stages,
				epifitech.NewCollectFormData(palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PARENTAL_DETAILS_CAPTURE, preApprovedLoanNs.StageAddDetails),
				epifitech.NewCollectFormData(palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PERSONAL_DETAILS_CAPTURE, preApprovedLoanNs.StageAddDetails),
				NewFiftyfinSubmitAdditionalKycDetail(),
				NewFiftyfinKyc(),
			)
		} else {
			res.Stages = append(res.Stages, NewFiftyfinAdditionalKycDetails(), NewFiftyfinKyc())
		}
	case palPb.GroupStage_GROUP_STAGE_INIT_LOAN:
		res.Stages = append(res.Stages, NewFiftyfinInitLoan())
	case palPb.GroupStage_GROUP_STAGE_LIEN_MARK:
		res.Stages = append(res.Stages, NewFiftyfinLienMark())
	case palPb.GroupStage_GROUP_STAGE_KFS:
		res.Stages = append(res.Stages, NewFiftyfinKfs())
	case palPb.GroupStage_GROUP_STAGE_MANDATE:
		res.Stages = append(res.Stages, NewFiftyfinMandate())
	case palPb.GroupStage_GROUP_STAGE_E_SIGN:
		res.Stages = append(res.Stages, NewFiftyfinEsign())
	case palPb.GroupStage_GROUP_STAGE_DRAWDOWN:
		res.Stages = append(res.Stages, NewFiftyfinDrawdown())
	case palPb.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION:
		res.Stages = append(res.Stages, NewFiftyfinLoanAccountCreation())
	case palPb.GroupStage_GROUP_STAGE_VERIFY_LOAN_DETAILS:
		loanVerificationV2Flag := workflow.GetVersion(ctx, "loan-verification-v2", workflow.DefaultVersion, 1)
		if loanVerificationV2Flag == 1 {
			stage, err := p.getVerifyLoanStage(ctx, req.ClientRefId)
			if err != nil {
				return nil, fmt.Errorf("failed to get verify loan stage: %w", err)
			}
			res.Stages = append(res.Stages, stage)
		} else {
			res.Stages = append(res.Stages, NewLoanDetailsVerification())
		}
	case palPb.GroupStage_GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION:
		res.Stages = append(res.Stages, NewFiftyfinResetVendorLoanApplication())
	default:
		return nil, fmt.Errorf("group stage not implemented by vendor provider, groupStage: %v", req.GroupStage)
	}

	return res, nil
}

func (p *Provider) GetGroupStages(_ workflow.Context, _ *providers.GetGroupStagesRequest) (*providers.GetGroupStagesResponse, error) {
	res := &providers.GetGroupStagesResponse{}
	res.GroupStages = []palPb.GroupStage{
		palPb.GroupStage_GROUP_STAGE_USER_REGISTRATION,
		palPb.GroupStage_GROUP_STAGE_KYC,
		palPb.GroupStage_GROUP_STAGE_VERIFY_LOAN_DETAILS,
		palPb.GroupStage_GROUP_STAGE_LIEN_MARK,
		palPb.GroupStage_GROUP_STAGE_INIT_LOAN,
		palPb.GroupStage_GROUP_STAGE_KFS,
		palPb.GroupStage_GROUP_STAGE_E_SIGN,
		palPb.GroupStage_GROUP_STAGE_MANDATE,
		palPb.GroupStage_GROUP_STAGE_DRAWDOWN,
		palPb.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION,
	}
	return res, nil
}

func (p *Provider) GetNextGroupStage(_ workflow.Context, request *providers.GetNextGroupStageRequest) (*providers.GetNextGroupStageResponse, error) {
	res := &providers.GetNextGroupStageResponse{}
	currentStage := request.CurrentGroupStage
	if currentStage == palPb.GroupStage_GROUP_STAGE_UNSPECIFIED {
		res.NextStage = groupStageRunOrder[0]
		return res, nil
	}
	_, ind, present := lo.FindIndexOf(groupStageRunOrder, func(item palPb.GroupStage) bool {
		return item == currentStage
	})
	if !present {
		return nil, fmt.Errorf("current group stage not part of workflow")
	}
	if ind+1 == len(groupStageRunOrder) {
		return res, nil
	}
	res.NextStage = groupStageRunOrder[ind+1]
	return res, nil
}

func (p *Provider) getVerifyLoanStage(ctx workflow.Context, clientRefId string) (stages.IStage, error) {
	actReq := &palActivityPb.GetLoanRequestActivityRequest{
		OrchId: clientRefId,
	}
	actRes := &palActivityPb.GetLoanRequestActivityResponse{}
	lrEr := activityPkg.Execute(ctx, preApprovedLoanNs.GetLoanRequest, actRes, actReq)
	if lrEr != nil {
		return nil, fmt.Errorf("GetLoanRequest activity failed: %w", lrEr)
	}

	loanOfferId := actRes.GetLoanRequest().GetOfferId()
	loanOfferActReq := &palActivityPb.GetLoanOfferActivityRequest{
		OfferId: loanOfferId,
	}
	loanOfferActRes := &palActivityPb.GetLoanOfferActivityResponse{}
	offerErr := activityPkg.Execute(ctx, preApprovedLoanNs.GetLoanOffer, loanOfferActRes, loanOfferActReq)
	if offerErr != nil {
		return nil, fmt.Errorf("GetLoanOffer activity failed: %w", offerErr)
	}

	if loanOfferActRes.GetLoanOffer().GetOfferConstraints().GetFiftyfinLamfConstraintInfo().GetSource() == palPb.FiftyFinLamfOfferSource_FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED {
		return NewLoanDetailsVerificationV2(), nil
	} else {
		return NewLoanDetailsVerification(), nil
	}
}

type commonStageHelper struct {
}

func (_ *commonStageHelper) GetLoanHeader() *palPb.LoanHeader {
	return &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
		Vendor:      palPb.Vendor_FIFTYFIN,
	}
}

func (_ *commonStageHelper) getActivityRequest(req *stages.PerformRequest) *palActivityPb.PalActivityRequest {
	return &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
}

func (_ *commonStageHelper) newFiftyfinFetchMfPortfolioRequest(req *stages.PerformRequest, skipAccountDetailUpdate, fetchMfcPf, fetchFiftyfinPf, skipApplicationProgressScreen bool) *palActivityPb.FiftyfinFetchMfPortfolioRequest {
	return &palActivityPb.FiftyfinFetchMfPortfolioRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:                      req.Request.GetLoanStep(),
		Vendor:                        req.Vendor,
		LoanProgram:                   req.LoanProgram,
		SkipAccountDetailUpdate:       skipAccountDetailUpdate,
		FetchMfcPf:                    fetchMfcPf,
		FetchFiftyfinPf:               fetchFiftyfinPf,
		SkipApplicationProgressScreen: skipApplicationProgressScreen,
	}
}

func (_ *commonStageHelper) newFiftyfinFundVerificationRequest(req *stages.PerformRequest) *palActivityPb.FiftyfinFundVerificationRequest {
	return &palActivityPb.FiftyfinFundVerificationRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
}

func (_ *commonStageHelper) newFiftyfinWaitForUserActionRequest(req *stages.PerformRequest) *palActivityPb.FiftyfinWaitForUserActionRequest {
	return &palActivityPb.FiftyfinWaitForUserActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
}

func (_ *commonStageHelper) newFiftyfinVoidLoanRequest(req *stages.PerformRequest, restartLoan,
	unpledgeFunds commontypes.BooleanEnum) *palActivityPb.FiftyfinVoidLoanRequest {
	return &palActivityPb.FiftyfinVoidLoanRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:      req.Request.GetLoanStep(),
		Vendor:        req.Vendor,
		LoanProgram:   req.LoanProgram,
		RestartLoan:   restartLoan,
		UnpledgeFunds: unpledgeFunds,
	}
}
