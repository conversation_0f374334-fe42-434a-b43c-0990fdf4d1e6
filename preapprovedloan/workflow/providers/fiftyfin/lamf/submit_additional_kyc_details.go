// nolint: dupl
package lamf

import (
	"go.temporal.io/sdk/workflow"

	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palNsGamma "github.com/epifi/gamma/preapprovedloan/workflow/namespace"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

// FiftyfinSubmitAdditionalKycDetail represents the stage which submits collected KYC details to vendor
type FiftyfinSubmitAdditionalKycDetail struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*commonStageHelper
}

func NewFiftyfinSubmitAdditionalKycDetail() *FiftyfinSubmitAdditionalKycDetail {
	return &FiftyfinSubmitAdditionalKycDetail{}
}

var _ stages.IStage = &FiftyfinSubmitAdditionalKycDetail{}

func (f *FiftyfinSubmitAdditionalKycDetail) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	// Submit additional kyc details to vendor
	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNsGamma.FiftyfinSubmitAdditionalKycDetailV2, actRes, actReq)
	if providers.IsActivityErrorV2(ctx, res, actErr, providers.PalActivityResponseErrParams(actRes).WithMarkRetryableErrsAsFailed(true)) {
		return res, actErr
	}

	res.LseFieldMasks = []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (f *FiftyfinSubmitAdditionalKycDetail) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SUBMIT_ADDITIONAL_KYC_DETAILS
}

func (f *FiftyfinSubmitAdditionalKycDetail) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAddDetails
}
