// nolint:funlen,unparam
package webfe

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	pkgErr "github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/comms"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/employment"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffBeEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	authHeader "github.com/epifi/gamma/api/frontend/header"
	feHeader "github.com/epifi/gamma/api/frontend/header"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/screener"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/webfe"
	"github.com/epifi/gamma/pkg/frontend/header"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/webfe/userproc"
)

var (
	webFeConsentTypeToBeConsentTypes = map[webfe.ConsentType]consent.ConsentType{
		webfe.ConsentType_CONSENT_TYPE_CREDIT_REPORT_DATA_PULL: consent.ConsentType_CREDIT_REPORT_DATA_PULL,
		webfe.ConsentType_CONSENT_TYPE_FI_TNC:                  consent.ConsentType_FI_TNC,
	}
	webFeGenerateOtpFlowToBeGenerateOtpFlow = map[webfe.GenerateOTPFlow]auth.GenerateOTPFlow{
		webfe.GenerateOTPFlow_GENERATE_OTP_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK: auth.GenerateOTPFlow_GENERATE_OTP_FLOW_CREDIT_CARD_WEB_ELIGIBILITY,
	}
)

type Service struct {
	webfe.UnimplementedWebfeServer
	actorClient         actor.ActorClient
	authClient          auth.AuthClient
	employmentClient    employment.EmploymentClient
	commsClient         comms.CommsClient
	usersClient         user.UsersClient
	userCommsPrefClient upPb.UserPreferenceClient
	consentClient       consent.ConsentClient
	screenerClient      screener.ScreenerClient
	onbClient           onboarding.OnboardingClient
	fireflyClient       ffPb.FireflyClient
	userProcessor       userproc.UserProcessor
}

func NewService(actorClient actor.ActorClient, authClient auth.AuthClient, employmentClient employment.EmploymentClient, commsClient comms.CommsClient,
	userClient user.UsersClient, userCommsPrefClient upPb.UserPreferenceClient, consentClient consent.ConsentClient,
	screenerClient screener.ScreenerClient, onbClient onboarding.OnboardingClient, fireflyClient ffPb.FireflyClient,
	userProcessor userproc.UserProcessor) *Service {
	return &Service{
		actorClient:         actorClient,
		authClient:          authClient,
		employmentClient:    employmentClient,
		commsClient:         commsClient,
		usersClient:         userClient,
		userCommsPrefClient: userCommsPrefClient,
		consentClient:       consentClient,
		screenerClient:      screenerClient,
		onbClient:           onbClient,
		fireflyClient:       fireflyClient,
		userProcessor:       userProcessor,
	}
}

var _ webfe.WebfeServer = &Service{}

func (s *Service) ValidateLogin(ctx context.Context, req *webfe.ValidateLoginRequest) (*webfe.ValidateLoginResponse, error) {
	res := &webfe.ValidateLoginResponse{RespHeader: &headerPb.ResponseHeader{}}

	verifyOtpRes, err := s.authClient.VerifyOtp(ctx, &auth.VerifyOtpRequest{
		Device:      req.GetReq().GetAuth().GetDevice(),
		PhoneNumber: req.GetPhoneNumber(),
		Token:       req.GetToken(),
		Otp:         req.GetOtp(),
		Mediums: []comms.Medium{
			comms.Medium_SMS,
		},
	})
	if rpcErr := epifigrpc.RPCError(verifyOtpRes, err); rpcErr != nil {
		logger.Error(ctx, "error in otp verification", zap.Error(rpcErr))
		return &webfe.ValidateLoginResponse{
			RespHeader: header.InlineErrResp(verifyOtpRes.GetStatus(), fmt.Sprint(verifyOtpRes.GetStatus().GetCode()), verifyOtpRes.GetStatus().GetShortMessage()),
		}, nil
	}

	resp, err := s.userProcessor.GetOrCreateUserAndActor(ctx, &userproc.GetOrCreateUserAndActorRequest{
		PhoneNumber: req.GetPhoneNumber(),
		Name:        nil,
		Url:         "",
		Pan:         req.GetPan(),
		EmailId:     req.GetEmailId(),
		Dob:         req.GetDob(),
	})
	if err != nil {
		logger.Error(ctx, "error in creating user and actor",
			zap.Error(err),
			zap.Any(logger.PHONE_NUMBER, req.GetPhoneNumber()))
		res.RespHeader = header.InlineErrResp(rpc.StatusInternal(), fmt.Sprintf("%d", rpc.StatusInternal().GetCode()), rpc.StatusInternal().GetDebugMessage())
		return res, nil
	}

	actorDetails := resp.Actor
	consentErr := s.recordUserProvidedConsents(ctx, actorDetails, req.GetConsents(), req.GetReq().GetAuth().GetDevice())
	if consentErr != nil {
		logger.Error(ctx, "error in recording consent", zap.Error(consentErr), zap.String(logger.ACTOR_ID_V2, actorDetails.GetId()))
		res.RespHeader = header.InlineErrResp(rpc.StatusInternal(), fmt.Sprintf("%d", rpc.StatusInternal().GetCode()), rpc.StatusInternal().GetDebugMessage())
		return res, nil
	}
	clientReqId, err := s.initiateFlow(ctx, actorDetails, req.GetWebFlow(), req.GetEmailId(), req.GetToken(), req.GetName(), nil)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in initiating flow for %s", req.GetWebFlow().String()))
		res.RespHeader = header.InlineErrResp(rpc.StatusInternal(), fmt.Sprintf("%d", rpc.StatusInternal().GetCode()), rpc.StatusInternal().GetDebugMessage())
		return res, nil
	}
	res.RespHeader = header.SuccessRespHeader()
	res.ActorId = actorDetails.GetId()
	res.ClientReqId = clientReqId
	res.AccessToken = req.GetToken()
	return res, nil
}

// recordUserProvidedConsents will record a list of user entered consents
func (s *Service) recordUserProvidedConsents(ctx context.Context, actorDetails *types.Actor, consents []webfe.ConsentType, device *commontypes.Device) error {
	beConsents := make([]*consent.ConsentRequestInfo, 0)
	for _, cons := range consents {
		beConsent, ok := webFeConsentTypeToBeConsentTypes[cons]
		if !ok {
			return fmt.Errorf("error in fetching consent [%s] for the actor", cons.String())
		}
		beConsents = append(beConsents, &consent.ConsentRequestInfo{
			ConsentType: beConsent,
			ClientReqId: uuid.NewString(),
		})
	}
	consentRes, err := s.consentClient.RecordConsents(ctx, &consent.RecordConsentsRequest{
		Consents: beConsents,
		ActorId:  actorDetails.GetId(),
		Device:   device,
		Owner:    commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(consentRes, err); te != nil {
		return pkgErr.Wrap(te, "error in recording consents")
	}
	return nil
}

// initiateFlow will initiate a flow based on the web flow given . It will return a client req id which
// can be used to poll the status of the flow
func (s *Service) initiateFlow(ctx context.Context,
	actorDetails *types.Actor,
	webFlow webfe.WebFlow,
	emailId string,
	token string,
	name *commontypes.Name,
	details *webfe.AdditionalDetails,
) (string, error) {
	switch webFlow {
	case webfe.WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK:
		// checking if a request already exists
		webEligCr, err := s.fireflyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffPb.GetCardRequestByActorIdAndWorkflowRequest{
			ActorId:             actorDetails.GetId(),
			CardRequestWorkFlow: ffBeEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK,
		})
		if te := epifigrpc.RPCError(webEligCr, err); te != nil {
			// ignoring error here and initiating the flow again in case we are not able to fetch the card requests
			logger.Error(ctx, "error in fetching web eligibility card req", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorDetails.GetId()))
		} else {
			return webEligCr.GetCardRequest().GetId(), nil
		}
		initiateCardReqRes, err := s.fireflyClient.InitiateCardReq(ctx, &ffPb.InitiateCardReqRequest{
			CardRequestWorkFlow: ffBeEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK,
			RequestData: &ffPb.InitiateCardReqRequest_RealTimeProfileValidationCheckData{
				RealTimeProfileValidationCheckData: &ffPb.RealTimeProfileValidationCheckData{
					EmailId:           emailId,
					AuthClientReqId:   token,
					Name:              name,
					CardProgramType:   details.GetCreditCardEligibilityCheckData().GetCardProgramType(),
					CardProgramVendor: details.GetCreditCardEligibilityCheckData().GetCardProgramVendor(),
				}},
			ActorId: actorDetails.GetId(),
		})
		if te := epifigrpc.RPCError(initiateCardReqRes, err); te != nil {
			return "", pkgErr.Wrap(te, "error in initialising credit card request")
		}
		return initiateCardReqRes.GetCardRequestId(), nil
	default:
		return "", fmt.Errorf("no flow in place for the given waitlist flow : %s", webFlow.String())
	}
}

func (s *Service) GetRequestStatus(ctx context.Context, req *webfe.GetRequestStatusRequest) (*webfe.GetRequestStatusResponse, error) {
	switch req.GetWebFlow() {
	case webfe.WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK:
		return s.checkOffAppCreditReportPullWorkflowStatus(ctx, req.GetClientReqId(), req.GetActorId())
	default:
		logger.Error(ctx, fmt.Sprintf("no mapping for the given web flow : %s", req.GetWebFlow().String()))
		return &webfe.GetRequestStatusResponse{
			RespHeader: &feHeader.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
}

func (s *Service) checkOffAppCreditReportPullWorkflowStatus(ctx context.Context, clientReqId, actorId string) (*webfe.GetRequestStatusResponse, error) {
	ffPollingRes, err := s.fireflyClient.GetRequestStatus(ctx, &ffPb.GetRequestStatusRequest{CardRequestId: clientReqId})
	if te := epifigrpc.RPCError(ffPollingRes, err); te != nil {
		logger.Error(ctx, "error in fetching card request status", zap.Error(te), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.InlineErrResp(rpc.StatusInternal(), fmt.Sprintf("%d", rpc.StatusInternal().GetCode()), rpc.StatusInternal().GetShortMessage()),
			RequestStatus: 0,
			DisplayInfo:   nil,
		}, nil
	}
	switch ffPollingRes.GetRequestStatus() {
	case ffBeEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		ffBeEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED:
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.SuccessRespHeader(),
			RequestStatus: webfe.GetRequestStatusResponse_STATUS_IN_PROGRESS,
			DisplayInfo: &webfe.DisplayInfo{
				ScreenTitle: "We're checking it for you",
			},
		}, nil
	case ffBeEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		cardRequestDetails, err := s.fireflyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffPb.GetCardRequestByActorIdAndWorkflowRequest{
			ActorId:             actorId,
			CardRequestWorkFlow: ffBeEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK,
		})
		if te := epifigrpc.RPCError(cardRequestDetails, err); te != nil {
			return &webfe.GetRequestStatusResponse{
				RespHeader:    header.InlineErrResp(rpc.StatusInternal(), fmt.Sprintf("%d", rpc.StatusInternal().GetCode()), rpc.StatusInternal().GetShortMessage()),
				RequestStatus: 0,
				DisplayInfo:   nil,
			}, nil
		}
		return getSuccessResponse(cardRequestDetails.GetCardRequest().GetRequestDetails().GetRealTimeProfileValidationCheckDetails().GetCardProgramType())

	case ffBeEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		return getFailureResponse(ffPollingRes.GetCardRequestDetails().GetRealTimeProfileValidationCheckDetails().GetCardProgramType())
	default:
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.SuccessRespHeader(),
			RequestStatus: webfe.GetRequestStatusResponse_STATUS_IN_PROGRESS,
			DisplayInfo: &webfe.DisplayInfo{
				ScreenTitle: "We're checking it for you",
			},
		}, nil
	}
}

func getSuccessResponse(cardProgramType types.CardProgramType) (*webfe.GetRequestStatusResponse, error) {
	if cardProgramType == types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED {
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.SuccessRespHeader(),
			RequestStatus: webfe.GetRequestStatusResponse_STATUS_SUCCESSFUL,
			DisplayInfo: &webfe.DisplayInfo{
				ScreenTitle:    "Congrats, you're eligible!",
				ScreenMessage:  "Now, the only thing left is to complete your application on the app. ",
				ScreenImage:    "https://epifi-icons.pointz.in/credit_card_images/web_flow_magnifi_benefits.png",
				CtaText:        "DOWNLOAD THE APP",
				AdditionalText: "Enjoy these benefits with the Magnifi Fi-Federal Credit Card",
				BottomText:     "Approval of card application shall be at issuer discretion",
			},
		}, nil
	} else {
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.SuccessRespHeader(),
			RequestStatus: webfe.GetRequestStatusResponse_STATUS_SUCCESSFUL,
			DisplayInfo: &webfe.DisplayInfo{
				ScreenTitle:    "Congrats, you're eligible!",
				ScreenMessage:  "Now, the only thing left is to complete your application on the app. ",
				ScreenImage:    "https://epifi-icons.pointz.in/credit_card_images/benefits.png",
				CtaText:        "DOWNLOAD THE APP",
				AdditionalText: "Enjoy these benefits with the Amplifi Fi-Federal Credit Card",
				BottomText:     "Approval of card application shall be at issuer discretion",
			},
		}, nil
	}
}

func getFailureResponse(cardProgramType types.CardProgramType) (*webfe.GetRequestStatusResponse, error) {
	if cardProgramType == types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED {
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.SuccessRespHeader(),
			RequestStatus: webfe.GetRequestStatusResponse_STATUS_FAILED,
			DisplayInfo: &webfe.DisplayInfo{
				ScreenTitle:   "Thank you for showing interest",
				ScreenMessage: "You don't meet the eligibility criteria right now. However, you can still check out the other products we offer.",
				ScreenImage:   "https://epifi-icons.pointz.in/credit_card_images/web_flow_magnifi_benefits.png",
				CtaText:       "DOWNLOAD THE APP",
			},
		}, nil
	} else {
		return &webfe.GetRequestStatusResponse{
			RespHeader:    header.SuccessRespHeader(),
			RequestStatus: webfe.GetRequestStatusResponse_STATUS_FAILED,
			DisplayInfo: &webfe.DisplayInfo{
				ScreenTitle:   "Thank you for showing interest",
				ScreenMessage: "You don't meet the eligibility criteria right now. However, you can still check out the other products we offer.",
				ScreenImage:   "https://epifi-icons.pointz.in/credit_card_images/benefits.png",
				CtaText:       "DOWNLOAD THE APP",
			},
		}, nil
	}
}

// Generates phone OTP and sends SMS to phone number
func (s *Service) GeneratePhoneOtp(ctx context.Context, req *webfe.GeneratePhoneOtpRequest) (*webfe.GeneratePhoneOtpResponse, error) {
	ctx = epificontext.CtxWithPhoneNumToken(ctx, obfuscator.HashedPhoneNum(req.GetPhoneNumber()))
	logger.Info(ctx, "GeneratePhoneOtp request received for webfe service", zap.String("otpToken", req.GetToken()))

	// generateOtp rpc call to the auth service
	authRes, err := s.authClient.GenerateOtp(ctx, &auth.GenerateOtpRequest{
		Token:           req.GetToken(),
		PhoneNumber:     req.GetPhoneNumber(),
		Device:          req.GetReq().GetAuth().GetDevice(),
		Mediums:         []comms.Medium{comms.Medium_SMS},
		GenerateOtpFlow: webFeGenerateOtpFlowToBeGenerateOtpFlow[req.GetGenerateOtpFlow()],
	})
	if rpcErr := epifigrpc.RPCError(authRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in invoking GenerateOtp of Auth server", zap.Error(rpcErr))
		return &webfe.GeneratePhoneOtpResponse{
			RespHeader: header.InlineErrResp(authRes.GetStatus(), fmt.Sprint(authRes.GetStatus().GetCode()), authRes.GetStatus().GetShortMessage()),
			Token:      authRes.GetToken(),
		}, nil
	}
	retryTimerSeconds := authRes.GetRetryTimerSeconds()
	if retryTimerSeconds == 0 {
		retryTimerSeconds = 30
	}
	return &webfe.GeneratePhoneOtpResponse{
		RespHeader:        header.SuccessRespHeader(),
		Token:             authRes.GetToken(),
		RetryTimerSeconds: retryTimerSeconds,
	}, nil
}

// VerifyPhoneOTP verifies the OTP against a GeneratePhoneOTP request
// On a successful verification, it returns an access token
func (s *Service) VerifyPhoneOtp(ctx context.Context, req *webfe.VerifyPhoneOtpRequest) (*webfe.VerifyPhoneOtpResponse, error) {
	ctx = epificontext.CtxWithPhoneNumToken(ctx, obfuscator.HashedPhoneNum(req.GetPhoneNumber()))
	logger.Info(ctx, "VerifyPhoneOtp request received for webfe service", zap.String("otpToken", req.GetToken()))

	// verifyOtp rpc call to the auth service
	authRes, err := s.authClient.VerifyOtp(ctx, &auth.VerifyOtpRequest{
		Token:       req.GetToken(),
		PhoneNumber: req.GetPhoneNumber(),
		Otp:         req.GetOtp(),
		Device:      req.GetReq().GetAuth().GetDevice(),
		Mediums:     []comms.Medium{comms.Medium_SMS},
	})
	if rpcErr := epifigrpc.RPCError(authRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in invoking VerifyOtp of Auth server", zap.Error(rpcErr))
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader: header.InlineErrResp(authRes.GetStatus(), fmt.Sprint(authRes.GetStatus().GetCode()), authRes.GetStatus().GetShortMessage()),
		}, nil
	}

	// create user row with phone number and actor row based on user id
	userDetails, actorDetails, err := s.createUserAndActor(ctx, req.GetPhoneNumber(), nil, req.GetWebUrl(), "", "", nil)
	if err != nil {
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader: header.InlineErrResp(rpc.StatusInternal(), fmt.Sprint(rpc.StatusInternal().GetCode()), constants.InternalErrTitle),
		}, nil
	}

	// If success, pass user identity and generate an access token from the auth server
	// if token is successfully generated, return the response
	accessToken, err := s.newToken(ctx, actorDetails, req.GetReq().GetAuth().GetDevice(), req.GetPhoneNumber(), "", auth.TokenType_WEB_LITE_ACCESS_TOKEN)
	if err != nil {
		logger.Error(ctx, "error in generating access token", zap.Error(err))
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader: header.InlineErrResp(rpc.StatusInternal(), fmt.Sprint(rpc.StatusInternal().GetCode()), constants.InternalErrTitle),
		}, nil
	}

	// early return for onboarding completed actors
	if s.isOnboardingComplete(ctx, actorDetails.GetId()) {
		if req.GetWebFlow() == webfe.WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK {
			clientReqId, initiateFlowErr := s.initiateFlow(ctx, actorDetails, req.GetWebFlow(),
				userDetails.GetProfile().GetEmail(), req.GetToken(), userDetails.GetProfile().GetKycName(),
				req.GetAdditionalDetails())
			if initiateFlowErr != nil {
				logger.Error(ctx, "error in initiating web eligibility workflow", zap.Error(initiateFlowErr))
				return &webfe.VerifyPhoneOtpResponse{
					RespHeader: header.InlineErrResp(rpc.StatusInternal(), fmt.Sprint(rpc.StatusInternal().GetCode()), constants.InternalErrTitle),
				}, nil
			}
			return &webfe.VerifyPhoneOtpResponse{
				RespHeader:  header.SuccessRespHeader(),
				AccessToken: accessToken,
				NextScreen: &dlPb.Deeplink{
					Screen: dlPb.Screen_CC_WEB_ELIGIBILITY_CHECK_STATUS_SCREEN,
					ScreenOptions: &dlPb.Deeplink_CreditCardWebEligibilityCheckStatusScreenOptions{
						CreditCardWebEligibilityCheckStatusScreenOptions: &dlPb.CreditCardWebEligibilityCheckStatusScreenOptions{
							ClientReqId: clientReqId,
						},
					},
				},
				ActorId: actorDetails.GetId(),
			}, nil

		}
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader:  header.SuccessRespHeader(),
			AccessToken: accessToken,
			NextScreen: &dlPb.Deeplink{
				Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
			},
			ActorId: actorDetails.GetId(),
		}, nil
	}

	// record whatsapp consent
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		_ = s.recordWAConsent(ctx, actorDetails.GetId(), req.GetHasWhatsappConsent())
	})

	// record validConsents for actor
	err = s.recordConsents(ctx, actorDetails.GetId(), req.GetConsentTypes())
	if err != nil {
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader: header.InlineErrResp(rpc.StatusInternal(), fmt.Sprint(rpc.StatusInternal().GetCode()), constants.InternalErrTitle),
		}, nil
	}

	if req.GetWebFlow() == webfe.WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK {
		nextAction, nextActionErr := s.getCCEligibilityNextAction(ctx, actorDetails.GetId())
		if nextActionErr != nil {
			logger.Error(ctx, "error in fetching next action for cc web eligibility", zap.Error(nextActionErr),
				zap.String(logger.ACTOR_ID_V2, actorDetails.GetId()))
			return &webfe.VerifyPhoneOtpResponse{
				RespHeader: header.InlineErrResp(rpc.StatusInternal(), fmt.Sprint(rpc.StatusInternal().GetCode()), constants.InternalErrTitle),
			}, nil
		}
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader:  header.SuccessRespHeader(),
			AccessToken: accessToken,
			NextScreen:  nextAction,
			ActorId:     actorDetails.GetId(),
		}, nil
	}
	// record screener attempt
	nextAction, err := s.recordScreenerAttempt(ctx, actorDetails.GetId())
	if err != nil {
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader: header.InlineErrResp(rpc.StatusInternal(), fmt.Sprint(rpc.StatusInternal().GetCode()), constants.InternalErrTitle),
		}, nil
	}
	// return gracefully for invalid screens
	if _, ok := lo.Find(validScreens, func(screen dlPb.Screen) bool {
		return screen == nextAction.GetScreen()
	}); !ok {
		logger.Error(ctx, "unexpected deep link from screener run check", zap.String("flow", nextAction.GetScreen().String()))
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader:  header.SuccessRespHeader(),
			AccessToken: accessToken,
			NextScreen: &dlPb.Deeplink{
				Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
			},
			ActorId: actorDetails.GetId(),
		}, nil
	}
	// return gracefully for invalid screens
	if _, ok := lo.Find(validScreens, func(screen dlPb.Screen) bool {
		return screen == nextAction.GetScreen()
	}); !ok {
		logger.Error(ctx, "unexpected deep link from screener run check", zap.String("flow", nextAction.GetScreen().String()))
		return &webfe.VerifyPhoneOtpResponse{
			RespHeader:  header.SuccessRespHeader(),
			AccessToken: accessToken,
			NextScreen: &dlPb.Deeplink{
				Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
			},
			ActorId: actorDetails.GetId(),
		}, nil
	}

	return &webfe.VerifyPhoneOtpResponse{
		RespHeader:  header.SuccessRespHeader(),
		AccessToken: accessToken,
		NextScreen:  nextAction,
		ActorId:     actorDetails.GetId(),
	}, nil
}

func (s *Service) getCCEligibilityNextAction(ctx context.Context, actorId string) (*dlPb.Deeplink, error) {
	// checking if a request already exists
	webEligCr, err := s.fireflyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffPb.GetCardRequestByActorIdAndWorkflowRequest{
		ActorId:             actorId,
		CardRequestWorkFlow: ffBeEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_PROFILE_VALIDATION_CHECK,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching realtime profile validation check request", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, err
	case webEligCr.GetStatus().IsRecordNotFound():
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_CC_WEB_ELIGIBILITY_USER_DETAILS_SCREEN,
		}, nil
	case !webEligCr.GetStatus().IsSuccess():
		logger.Error(ctx, "non success status in fetching realtime profile validation check request", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, fmt.Errorf("non success status in fetching realtime profile validation check request")
	default:
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_CC_WEB_ELIGIBILITY_CHECK_STATUS_SCREEN,
			ScreenOptions: &dlPb.Deeplink_CreditCardWebEligibilityCheckStatusScreenOptions{
				CreditCardWebEligibilityCheckStatusScreenOptions: &dlPb.CreditCardWebEligibilityCheckStatusScreenOptions{
					ClientReqId: webEligCr.GetCardRequest().GetId(),
				},
			},
			ScreenOptionsV2: nil,
		}, nil
	}
}

// Generates OTP and sends it to email
func (s *Service) GenerateEmailOtp(ctx context.Context, req *webfe.GenerateEmailOtpRequest) (*webfe.GenerateEmailOtpResponse, error) {
	logger.Info(ctx, "GenerateEmailOtp request received for webfe service", zap.String("otpToken", req.GetToken()))

	// generateWorkEmailOtp rpc call to the employment service
	empRes, err := s.employmentClient.SendWorkEmailOTP(ctx, &employment.SendWorkEmailOTPRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		Email:       req.GetEmail(),
		Token:       req.GetToken(),
		ClientReqId: req.GetClientReqId(),
	})
	if rpcErr := epifigrpc.RPCError(empRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in invoking GenerateWorkEmailOtp of employment server", zap.Error(rpcErr))
		return &webfe.GenerateEmailOtpResponse{
			RespHeader: header.InlineErrResp(empRes.GetStatus(), fmt.Sprint(empRes.GetStatus().GetCode()), getSendEmailOtpInlineString(empRes.GetStatus().GetCode())),
			Token:      empRes.GetToken(),
		}, nil
	}

	return &webfe.GenerateEmailOtpResponse{
		RespHeader:        header.SuccessRespHeader(),
		Token:             empRes.GetToken(),
		RetryTimerSeconds: empRes.GetRetryAfter(),
	}, nil
}

// VerifyEmailOTP verifies the OTP against a GenerateEmailOTP request
func (s *Service) VerifyEmailOtp(ctx context.Context, req *webfe.VerifyEmailOtpRequest) (*webfe.VerifyEmailOtpResponse, error) {
	logger.Info(ctx, "VerifyEmailOtp request received for webfe service", zap.String("otpToken", req.GetToken()))

	// verifyEmailOtp rpc call to the employment service
	empRes, err := s.employmentClient.VerifyWorkEmailOTP(ctx, &employment.VerifyWorkEmailOTPRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		Email:       req.GetEmail(),
		Token:       req.GetToken(),
		Otp:         req.GetOtp(),
		ClientReqId: req.GetClientReqId(),
	})

	if rpcErr := epifigrpc.RPCError(empRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in invoking VerifyWorkEmailOTP of employment server", zap.Error(rpcErr))
		return &webfe.VerifyEmailOtpResponse{
			RespHeader: header.InlineErrResp(empRes.GetStatus(), fmt.Sprint(empRes.GetStatus().GetCode()), getVerifyEmailOtpInlineString(empRes.GetStatus().GetCode())),
		}, nil
	}

	return &webfe.VerifyEmailOtpResponse{
		RespHeader: header.SuccessRespHeader(),
	}, nil
}

func (s *Service) SendAppLinkToUser(ctx context.Context, req *webfe.SendAppLinkToUserRequest) (*webfe.SendAppLinkToUserResponse, error) {
	logger.Info(ctx, "SendAppLinkToUser request received")

	userRes, err := s.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		},
	})

	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in getting user details", zap.Error(rpcErr))
		return &webfe.SendAppLinkToUserResponse{
			RespHeader: header.InlineErrResp(userRes.GetStatus(), fmt.Sprint(userRes.GetStatus().GetCode()), constants.InternalErrTitle),
		}, nil
	}

	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		commsRes, err := s.commsClient.SendMessage(ctx, &comms.SendMessageRequest{
			Type:   comms.QoS_GUARANTEED,
			Medium: comms.Medium_WHATSAPP,
			UserIdentifier: &comms.SendMessageRequest_UserId{
				UserId: userRes.GetUser().GetId(),
			},
			Message: getWebOnbWhatsappMessage(),
		})

		if rpcErr := epifigrpc.RPCError(commsRes, err); rpcErr != nil {
			logger.Error(ctx, "Error in invoking send message of comms server", zap.Error(rpcErr))
		}
	})

	return &webfe.SendAppLinkToUserResponse{
		RespHeader: header.SuccessRespHeader(),
	}, nil
}

func getWebOnbWhatsappMessage() *comms.SendMessageRequest_Whatsapp {
	return &comms.SendMessageRequest_Whatsapp{Whatsapp: &comms.WhatsappMessage{
		WhatsappOption: &comms.WhatsappOption{
			Option: &comms.WhatsappOption_WebOnboardingCompletedWhatsappOption{
				WebOnboardingCompletedWhatsappOption: &comms.WebOnboardingCompletedWhatsappOption{
					WhatsappType: comms.WhatsappType_WEB_ONBOARDING_COMPLETED,
					Option: &comms.WebOnboardingCompletedWhatsappOption_WebOnboardingCompletedWhatsappOptionV1{
						WebOnboardingCompletedWhatsappOptionV1: &comms.WebOnboardingCompletedWhatsappOptionV1{
							TemplateVersion: comms.TemplateVersion_VERSION_V1,
							RedirectLink:    "https://go.fi.money/40Se02E",
						},
					},
				},
			},
		},
	}}
}

func (s *Service) updateUser(
	ctx context.Context,
	actorId string,
	name *commontypes.Name,
	pan string,
	dob *date.Date) (*user.User, *types.Actor, error) {

	getUserResp, getUserErr := s.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(getUserResp, getUserErr); err != nil {
		return nil, nil, err
	}

	updateUserResp, updateUserErr := s.usersClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User: &user.User{
			Id: getUserResp.GetUser().GetId(),
			Profile: &user.Profile{
				Name:        name,
				PAN:         pan,
				DateOfBirth: dob,
			},
			ActorId: actorId,
		},
		UpdateMask: []user.UserFieldMask{user.UserFieldMask_PAN,
			user.UserFieldMask_DOB, user.UserFieldMask_GIVEN_NAME},
	})
	if err := epifigrpc.RPCError(updateUserResp, updateUserErr); err != nil {
		return nil, nil, err
	}

	actorDetails, actorErr := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: actorId,
	})
	if err := epifigrpc.RPCError(actorDetails, actorErr); err != nil {
		return nil, nil, err
	}

	return updateUserResp.GetUser(), actorDetails.GetActor(), nil
}

// todo teja ,move this to a common place , same code repeated in frontend service
func (s *Service) createUserAndActor(ctx context.Context, phoneNumber *commontypes.PhoneNumber,
	name *commontypes.Name, url, pan, emailId string, dob *date.Date) (*user.User, *types.Actor, error) {
	var aUser *user.User
	// check if user already created
	userRes, err := s.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "failed to get user by phone number", zap.Error(err))
		return nil, nil, err

	case userRes.GetStatus().IsRecordNotFound():
		// user does not exist, we can go ahead with user creation

	case userRes.GetStatus().IsSuccess():
		logger.Info(ctx, fmt.Sprintf("user already exists, skipping user creation: %v", userRes.GetUser().GetId()))
		aUser = userRes.GetUser()

		actorRes, _ := s.actorClient.GetActorByEntityId(ctx, &actor.GetActorByEntityIdRequest{
			EntityId: aUser.GetId(),
			Type:     types.Actor_USER,
		})

		// if actor already exists return
		if actorRes.GetStatus().IsSuccess() {
			return aUser, actorRes.GetActor(), nil
		}

	default:
		logger.Error(ctx, "failed to get user by phone number", zap.Error(err))
		return nil, nil, epifigrpc.RPCError(userRes, err)
	}

	// create user if not created
	if aUser == nil {
		aUser, err = beCreateUser(ctx, s, phoneNumber, name, dob, url, emailId, pan)
		if err != nil {
			return nil, nil, err
		}
		logger.Info(ctx, fmt.Sprintf("created new user: %v", aUser.GetId()))
	}

	// create actor
	newActor, err := beCreateActor(ctx, s, aUser.Id, types.Actor_USER)
	if err != nil {
		return nil, nil, err
	}

	logger.DebugNoCtx(fmt.Sprintf("created user with phone number %v", phoneNumber.NationalNumber),
		zap.String(logger.ACTOR_ID_V2, newActor.Id), zap.String(logger.USER_ID, aUser.Id),
	)
	logger.Info(ctx, fmt.Sprintf("created new actor: %v", newActor.GetId()),
		zap.String(logger.ACTOR_ID_V2, newActor.GetId()), zap.String(logger.USER_ID, aUser.GetId()),
	)
	return aUser, newActor, nil
}

func beCreateUser(ctx context.Context, s *Service, phoneNumber *commontypes.PhoneNumber, name *commontypes.Name, dob *date.Date, url, emailId, pan string) (*user.User, error) {
	profile := &user.Profile{
		DateOfBirth: dob,
		PhoneNumber: phoneNumber,
		Email:       emailId,
		PAN:         pan,
		GivenName:   name,
	}
	userRes, err := s.usersClient.CreateUser(ctx, &user.CreateUserRequest{
		User: &user.User{
			Profile: profile,
			AcquisitionInfo: &user.AcquisitionInfo{
				Platform:             commontypes.Platform_WEB,
				WebUrl:               url,
				AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
				AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
				AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
				AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
			},
		},
	})

	if err != nil {
		logger.Error(ctx, "failed to create new user", zap.Error(err))
		return nil, err
	}

	return userRes.User, nil
}

func beCreateActor(ctx context.Context, s *Service, entityId string, actorType types.Actor_Type) (*types.Actor, error) {
	createActorResponse, err := s.actorClient.CreateActor(ctx, &actor.CreateActorRequest{
		Type:     actorType,
		EntityId: entityId,
	})
	if rpcErr := epifigrpc.RPCError(createActorResponse, err); rpcErr != nil {
		logger.Error(ctx, "failed to create new actor", zap.Error(rpcErr))
		return nil, rpcErr
	}

	return createActorResponse.Actor, nil
}

// Record Whatsapp consent
func (s *Service) recordWAConsent(ctx context.Context, actorId string, hasWhatsappConsent commontypes.BooleanEnum) error {

	if hasWhatsappConsent == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		logger.Info(ctx, "no WA consent decision found in request")
		return fmt.Errorf("no WA consent decision found in request")
	}

	var pref upPb.Preference
	if hasWhatsappConsent == commontypes.BooleanEnum_TRUE {
		pref = upPb.Preference_ON
	} else {
		pref = upPb.Preference_OFF
	}

	res, err := s.userCommsPrefClient.CreatePreference(ctx, &upPb.CreatePreferenceRequest{
		ActorId:    actorId,
		Medium:     comms.Medium_WHATSAPP,
		Category:   comms.Category_CATEGORY_PROMOTIONAL,
		Preference: pref,
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to create preference for category promotion", zap.Error(rpcErr))
		return rpcErr
	}

	res, err = s.userCommsPrefClient.CreatePreference(ctx, &upPb.CreatePreferenceRequest{
		ActorId:    actorId,
		Medium:     comms.Medium_WHATSAPP,
		Category:   comms.Category_CATEGORY_TRANSACTIONAL,
		Preference: pref,
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to create preference for category transactional", zap.Error(rpcErr))
		return rpcErr
	}

	return nil
}

// record validConsents for actor
func (s *Service) recordConsents(ctx context.Context, actorId string, consentTypes []webfe.ConsentType) error {
	checkConsentRes, err := s.consentClient.CheckConsentRequirement(ctx, &consent.CheckConsentRequirementRequest{
		ActorId: actorId,
		ConsentTypes: []consent.ConsentType{
			consent.ConsentType_FED_TNC,
			consent.ConsentType_FI_PRIVACY_POLICY,
			consent.ConsentType_FI_TNC,
			consent.ConsentType_FI_WEALTH_TNC,
		},
		Owner: commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if err != nil {
		logger.Error(ctx, "error in checking consent requirement", zap.Error(err))
		return err
	}

	var webfeConsentToConsentMapping = getConsentMapping()
	var wg sync.WaitGroup
	var errorChannel = make(chan error, len(checkConsentRes.ConsentTypeUrls))
	if checkConsentRes.IsConsentRequired {
		var validConsentUrls []*consent.ConsentTypeUrl
		for _, consentUrl := range checkConsentRes.ConsentTypeUrls {
			if _, ok := lo.Find(validConsents, func(consentType consent.ConsentType) bool {
				return consentUrl.GetConsentType() == consentType
			}); ok {
				validConsentUrls = append(validConsentUrls, consentUrl)
			}
		}
		wg.Add(len(validConsentUrls))

		// for all the missing consents, record user permitted ones
		for _, consentUrl := range validConsentUrls {
			if _, ok := lo.Find(consentTypes, func(webfeConsentType webfe.ConsentType) bool {
				return webfeConsentToConsentMapping[webfeConsentType] == consentUrl.GetConsentType()
			}); !ok {
				consentsNotReceived := errors.New("mandatory consents not received")
				logger.Error(ctx, "mandatory consents not received", zap.Error(consentsNotReceived))
				return consentsNotReceived
			}
			gConsentUrl := consentUrl
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				consentRes, err := s.consentClient.RecordConsent(ctx, &consent.RecordConsentRequest{
					ActorId:     actorId,
					ConsentType: gConsentUrl.GetConsentType(),
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				})
				if rpcErr := epifigrpc.RPCError(consentRes, err); rpcErr != nil {
					logger.Error(ctx, fmt.Sprintf("error in recording consent for consent type: %v", consentUrl.GetConsentType()), zap.Error(rpcErr))
					errorChannel <- rpcErr
				}
				wg.Done()
			})
		}
	}
	waitgroup.SafeWaitWithDefaultTimeout(&wg)
	close(errorChannel)
	rpcErr, _ := <-errorChannel
	return rpcErr
}

func getConsentMapping() map[webfe.ConsentType]consent.ConsentType {
	return map[webfe.ConsentType]consent.ConsentType{
		webfe.ConsentType_CONSENT_TYPE_UNSPECIFIED:       consent.ConsentType_ConsentType_UNSPECIFIED,
		webfe.ConsentType_CONSENT_TYPE_FI_TNC:            consent.ConsentType_FI_TNC,
		webfe.ConsentType_CONSENT_TYPE_FI_PRIVACY_POLICY: consent.ConsentType_FI_PRIVACY_POLICY,
		webfe.ConsentType_CONSENT_TYPE_FED_TNC:           consent.ConsentType_FED_TNC,
		webfe.ConsentType_CONSENT_TYPE_FI_WEALTH_TNC:     consent.ConsentType_FI_WEALTH_TNC,
	}
}

func (s *Service) recordScreenerAttempt(ctx context.Context, actorId string) (*dlPb.Deeplink, error) {
	var nextAction *dlPb.Deeplink
	var err error

	empInfo, err := s.employmentClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId: actorId,
		ProcessNames: []employment.ProcessName{
			employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION,
		},
	})
	if rpcErr := epifigrpc.RPCError(empInfo, err); rpcErr != nil && !empInfo.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "Error in invoking ProcessEmploymentData of employment server", zap.Error(rpcErr))
		return nil, rpcErr
	}

	// process employment data, creates employment data if not exists
	if empInfo.GetStatus().IsRecordNotFound() {
		processEmpDataRes, processErr := s.employmentClient.ProcessEmploymentData(ctx, &employment.ProcessEmploymentDataRequest{
			ActorId:        actorId,
			EmploymentType: employment.EmploymentType_SALARIED,
			UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_B2B_ONBOARDING,
		})
		if rpcErr := epifigrpc.RPCError(processEmpDataRes, processErr); rpcErr != nil {
			logger.Error(ctx, "Error in invoking ProcessEmploymentData of employment server", zap.Error(rpcErr))
			return nil, rpcErr
		}
	}

	getAttemptRes, errGet := s.screenerClient.GetScreenerAttemptsByActorId(ctx, &screener.GetScreenerAttemptsByActorIdRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getAttemptRes, errGet); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		logger.Error(ctx, "failed to get screener attempts by actor id", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// If no screener attempt is present, start the check to create an attempt
	if getAttemptRes.GetStatus().IsRecordNotFound() {
		if nextAction, err = s.runScreenerCheck(ctx, actorId); err != nil {
			return nil, err
		}
		return nextAction, nil
	}

	weCheckDetails, ok := lo.Find[*screener.CheckDetails](getAttemptRes.GetChecksMap(), func(check *screener.CheckDetails) bool {
		return check.GetCheckType() == screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION
	})
	if !ok {
		// if work email verification stage is not a part of screener, create a row for this stage and proceed with work email verification
		if nextAction, err = s.runScreenerCheck(ctx, actorId); err != nil {
			return nil, err
		}
		return nextAction, nil
	}

	switch weCheckDetails.GetCheckResult() {
	case screener.CheckResult_CHECK_RESULT_PASSED,
		screener.CheckResult_CHECK_RESULT_SKIPPED,
		screener.CheckResult_CHECK_RESULT_FAILED,
		screener.CheckResult_CHECK_RESULT_DISABLED:
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
		}, nil
	case screener.CheckResult_CHECK_RESULT_UNSCPECIFIED, screener.CheckResult_CHECK_RESULT_IN_PROGRESS:
		// Need to start a new check or check current check status
		if nextAction, err = s.runScreenerCheck(ctx, actorId); err != nil {
			return nil, err
		}
		return nextAction, nil
	default:
		logger.Error(ctx, "unexpected checkResult", zap.String("status", weCheckDetails.GetCheckResult().String()))
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
		}, nil
	}
}

func (s *Service) runScreenerCheck(ctx context.Context, actorId string) (*dlPb.Deeplink, error) {
	runCheckResp, errRun := s.screenerClient.RunCheck(ctx, &screener.RunCheckRequest{
		ActorId:   actorId,
		CheckType: screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION,
	})
	if rpcErr := epifigrpc.RPCError(runCheckResp, errRun); rpcErr != nil {
		logger.Error(ctx, "failed to run check", zap.Error(rpcErr))
		return nil, rpcErr
	}

	logger.Info(ctx, "RunCheck response", zap.String("Resp", fmt.Sprintf("%+v", runCheckResp)),
		zap.String("status", runCheckResp.GetCheckDetails().GetCheckResult().String()),
		zap.String("stage", runCheckResp.GetCheckDetails().GetCheckType().String()),
	)

	return runCheckResp.GetNextAction(), nil
}

func getVerifyEmailOtpInlineString(statusCode uint32) string {
	switch statusCode {
	case uint32(auth.VerifyOtpResponse_OTP_INCORRECT):
		return constants.IncorrectOtpErrTitle
	case uint32(auth.VerifyOtpResponse_OTP_INCORRECT_LAST_ATTEMPT):
		return constants.IncorrectOtpLastAttemptErrTitle
	default:
		return constants.VerifyOtpErrTitle
	}
}

func getSendEmailOtpInlineString(statusCode uint32) string {

	switch statusCode {
	// In case work email contains keywords/not a valid work email
	case uint32(employment.SendWorkEmailOTPResponse_INVALID_EMAIL):
		return constants.InvalidEmailErrTitle
	case uint32(employment.SendWorkEmailOTPResponse_DOMAIN_NOT_WHITELISTED):
		return constants.DomainNotFoundErrTitle
	case uint32(employment.SendWorkEmailOTPResponse_EMAIL_ALREADY_EXISTS):
		return constants.WorkEmailAlreadyExistsErrTitle
	default:
		return constants.SendOtpErrTitle
	}

}

func (s *Service) isOnboardingComplete(ctx context.Context, actorId string) bool {
	onbRes, err := s.onbClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
		ActorId:    actorId,
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})

	if grpcErr := epifigrpc.RPCError(onbRes, err); grpcErr != nil {
		if onbRes.GetStatus().IsRecordNotFound() {
			return false
		}
		logger.Error(ctx, "unable to fetch onboarded user details", zap.Error(grpcErr))
		return false
	}

	// if onboarding is completed or screening is passed, skip work email verification for that user
	if IsScreenerCompletedOrSkipped(onbRes.GetDetails()) {
		return true
	}

	return false
}

func IsScreenerCompletedOrSkipped(onb *onboarding.OnboardingDetails) bool {
	stageStatus := getStageStatus(onb, onboarding.OnboardingStage_ONBOARDING_COMPLETE)
	return stageStatus == onboarding.OnboardingState_SUCCESS || stageStatus == onboarding.OnboardingState_SKIPPED ||
		onb.GetStageMetadata().GetAppScreeningData().GetScreeningPassed() == commontypes.BooleanEnum_TRUE
}

// getStageStatus extracts OnboardingState of input OnboardingStage from OnboardingDetails
func getStageStatus(onb *onboarding.OnboardingDetails, stage onboarding.OnboardingStage) onboarding.OnboardingState {
	return onb.GetStageDetails().GetStageMapping()[stage.String()].GetState()
}

func (s *Service) CheckCreditCardEligibility(ctx context.Context, req *webfe.CheckCreditCardEligibilityRequest) (*webfe.CheckCreditCardEligibilityResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	_, actorDetails, updateUserDetailsErr := s.updateUser(ctx, actorId, req.GetName(), req.GetPan(), req.GetDob())
	if updateUserDetailsErr != nil {
		logger.Error(ctx, "error while updating user", zap.Error(updateUserDetailsErr))
		return &webfe.CheckCreditCardEligibilityResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	recordConsentErr := s.recordConsents(ctx, actorId, req.GetConsents())
	if recordConsentErr != nil {
		logger.Error(ctx, "error while recording consent", zap.Error(recordConsentErr))
		return &webfe.CheckCreditCardEligibilityResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	clientRequestId, err := s.initiateFlow(ctx,
		actorDetails,
		webfe.WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK,
		req.GetEmailId(),
		req.GetToken(),
		req.GetName(),
		&webfe.AdditionalDetails{
			WebFlowData: &webfe.AdditionalDetails_CreditCardEligibilityCheckData{
				CreditCardEligibilityCheckData: &webfe.CreditCardEligibilityCheckData{
					CardProgramType:   req.GetCardProgramType(),
					CardProgramVendor: req.GetCardProgramVendor(),
				},
			},
		},
	)

	if err != nil {
		logger.Error(ctx, "error while initiating cc flow", zap.Error(err))
		return &webfe.CheckCreditCardEligibilityResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	return &webfe.CheckCreditCardEligibilityResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ClientReqId: clientRequestId,
	}, nil
}

func (s *Service) AuthFuncOverride(ctx context.Context, authClient auth.AuthClient, authH *authHeader.AuthHeader) (*auth.ValidateTokenResponse, error) {
	return ValidateAccessToken(ctx, authClient, authH)
}
