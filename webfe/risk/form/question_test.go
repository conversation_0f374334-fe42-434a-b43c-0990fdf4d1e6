package form

import (
	"testing"

	"github.com/stretchr/testify/assert"

	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	webUiPb "github.com/epifi/gamma/api/typesv2/webui"
)

func Test_formFieldFromBEQuestion(t *testing.T) {
	testTextTypeQuestion := &formPb.ExtendedQuestion{
		Question: &formPb.Question{
			Id:          "id",
			Type:        formPb.QuestionType_QUESTION_TYPE_TEXT,
			Text:        "Sample Question",
			Tip:         "Tip",
			Placeholder: "Placeholder",
			Options: &formPb.QuestionOptions{
				Options: &formPb.QuestionOptions_TextOptions{
					TextOptions: &formPb.TextOptions{
						MaxCharsLimit: 10,
					},
				},
			},
		},
		IsMandatory: true,
	}
	testFileTypeQuestion := &formPb.ExtendedQuestion{
		Question: &formPb.Question{
			Type: formPb.QuestionType_QUESTION_TYPE_FILE,
			Options: &formPb.QuestionOptions{
				Options: &formPb.QuestionOptions_FileOptions{
					FileOptions: &formPb.FileOptions{
						AllowedContentTypes: []formPb.FileContentType{formPb.FileContentType_FILE_CONTENT_TYPE_JPEG},
					},
				},
			},
		},
		IsMandatory: true,
	}
	testMultiChoiceTypeQuestion := &formPb.ExtendedQuestion{
		Question: &formPb.Question{
			Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
			Options: &formPb.QuestionOptions{
				Options: &formPb.QuestionOptions_MultiChoice{
					MultiChoice: &formPb.MultiChoiceOptions{
						IsMultiSelect: false,
						ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
							"Yes": nil,
							"No":  nil,
						},
					},
				},
			},
		},
		IsMandatory: true,
		ConditionalQuestions: map[string]*formPb.ExtendedQuestion_ConditionalQuestions{
			"Yes": {Questions: []*formPb.ExtendedQuestion{
				testTextTypeQuestion,
				testFileTypeQuestion,
			}},
			"No": nil,
		},
	}
	textTypeFormField := &webUiPb.FormField{
		Label:       testTextTypeQuestion.GetQuestion().GetText(),
		Key:         testTextTypeQuestion.GetQuestion().GetId(),
		FieldType:   webUiPb.FieldType_FIELD_TYPE_TEXT_BOX,
		IsMandatory: testTextTypeQuestion.GetIsMandatory(),
		Tip:         testTextTypeQuestion.GetQuestion().GetTip(),
		Placeholder: testTextTypeQuestion.GetQuestion().GetPlaceholder(),
		FieldOptions: &webUiPb.FieldOptions{
			Options: &webUiPb.FieldOptions_TextBoxFieldOptions{
				TextBoxFieldOptions: &webUiPb.TextBoxFieldOptions{
					MaxCharsLimit: 10,
				},
			},
		},
	}
	testFileTypeFormField := &webUiPb.FormField{
		FieldType: webUiPb.FieldType_FIELD_TYPE_FILE,
		FieldOptions: &webUiPb.FieldOptions{
			Options: &webUiPb.FieldOptions_FileFieldOptions{
				FileFieldOptions: &webUiPb.FileFieldOptions{
					AllowedTypes: []webUiPb.FileContentType{webUiPb.FileContentType_FILE_CONTENT_TYPE_JPEG},
					MaxSizeLimit: maxFileSize,
				},
			},
		},
		IsMandatory: true,
	}
	testMultiChoiceTypeFormField := &webUiPb.FormField{
		FieldType: webUiPb.FieldType_FIELD_TYPE_CHECK_BOX,
		FieldOptions: &webUiPb.FieldOptions{
			Options: &webUiPb.FieldOptions_MultiChoiceFieldOptions{
				MultiChoiceFieldOptions: &webUiPb.MultiChoiceFieldOptions{
					IsMultiSelect: false,
					ChoiceConditionalFieldsMap: map[string]*webUiPb.MultiChoiceFieldOptions_FormFields{
						"Yes": {
							FormFields: []*webUiPb.FormField{
								textTypeFormField,
								testFileTypeFormField,
							},
						},
						"No": {},
					},
				},
			},
		},
		IsMandatory: true,
	}
	type args struct {
		extendedQuestion *formPb.ExtendedQuestion
	}
	tests := []struct {
		name    string
		args    args
		want    *webUiPb.FormField
		wantErr bool
	}{
		{
			name:    "invalid question type",
			args:    args{extendedQuestion: &formPb.ExtendedQuestion{}},
			wantErr: true,
		},
		{
			name: "failed for empty choices",
			args: args{extendedQuestion: &formPb.ExtendedQuestion{
				Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE},
			}},
			wantErr: true,
		},
		{
			name:    "success for text type question",
			args:    args{extendedQuestion: testTextTypeQuestion},
			want:    textTypeFormField,
			wantErr: false,
		},
		{
			name:    "success for file type question",
			args:    args{extendedQuestion: testFileTypeQuestion},
			want:    testFileTypeFormField,
			wantErr: false,
		},
		{
			name:    "success for multi choice type question",
			args:    args{extendedQuestion: testMultiChoiceTypeQuestion},
			want:    testMultiChoiceTypeFormField,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := formFieldFromBEQuestion(tt.args.extendedQuestion)
			if (err != nil) != tt.wantErr {
				t.Errorf("formFieldFromBEQuestion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !assert.Equal(t, tt.want, got) {
				t.Errorf("formFieldFromBEQuestion() got = %v,\n                                             want %v", got, tt.want)
			}
		})
	}
}
