// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire3 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	namespace "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	actor "github.com/epifi/gamma/api/actor"
	authpb "github.com/epifi/gamma/api/auth"
	biometrics "github.com/epifi/gamma/api/auth/biometrics"
	authlocationpb "github.com/epifi/gamma/api/auth/location"
	bankcust "github.com/epifi/gamma/api/bankcust"
	brepb "github.com/epifi/gamma/api/bre"
	ccpb "github.com/epifi/gamma/api/card/control"
	cardcipb "github.com/epifi/gamma/api/card/currencyinsights"
	cardcxpb "github.com/epifi/gamma/api/card/cx"
	dcmandatepb "github.com/epifi/gamma/api/card/debitcardmandate"
	developer2 "github.com/epifi/gamma/api/card/developer"
	notificationpb "github.com/epifi/gamma/api/card/notification"
	provisioning2 "github.com/epifi/gamma/api/card/provisioning"
	tokenizerproxypb "github.com/epifi/gamma/api/card/tokenizer_proxy"
	redemptionpb "github.com/epifi/gamma/api/casper/redemption"
	categorizerpb "github.com/epifi/gamma/api/categorizer"
	comms "github.com/epifi/gamma/api/comms"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	consentpb "github.com/epifi/gamma/api/consent"
	limitestimatorpb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditreportv2pb "github.com/epifi/gamma/api/creditreportv2"
	deposit "github.com/epifi/gamma/api/deposit"
	docspb "github.com/epifi/gamma/api/docs"
	firefly "github.com/epifi/gamma/api/firefly"
	ffaccountspb "github.com/epifi/gamma/api/firefly/accounting"
	ffaccconsumerpb "github.com/epifi/gamma/api/firefly/accounting/consumer"
	billpb "github.com/epifi/gamma/api/firefly/billing"
	ffbillingconsumerpb "github.com/epifi/gamma/api/firefly/billing/consumer"
	ffrepb "github.com/epifi/gamma/api/firefly/card_recommendation"
	ffconsumerpb "github.com/epifi/gamma/api/firefly/consumer"
	cccxpb "github.com/epifi/gamma/api/firefly/cx"
	ccdevpb "github.com/epifi/gamma/api/firefly/developer"
	lms "github.com/epifi/gamma/api/firefly/lms"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	ffbev2pb "github.com/epifi/gamma/api/firefly/v2"
	ffconsumerv2pb "github.com/epifi/gamma/api/firefly/v2/consumer"
	healthenginepb "github.com/epifi/gamma/api/health_engine"
	kyc "github.com/epifi/gamma/api/kyc"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	merchantpb "github.com/epifi/gamma/api/merchant"
	nudgepb "github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	paypb "github.com/epifi/gamma/api/pay"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloanpb "github.com/epifi/gamma/api/preapprovedloan"
	product "github.com/epifi/gamma/api/product"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	rewardspb "github.com/epifi/gamma/api/rewards"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	rewardsprojectionpb "github.com/epifi/gamma/api/rewards/projector"
	riskpb "github.com/epifi/gamma/api/risk"
	profilepb "github.com/epifi/gamma/api/risk/profile"
	salaryprogrampb "github.com/epifi/gamma/api/salaryprogram"
	savings "github.com/epifi/gamma/api/savings"
	segmentpb "github.com/epifi/gamma/api/segment"
	tieringpb "github.com/epifi/gamma/api/tiering"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	userlocationpb "github.com/epifi/gamma/api/user/location"
	onboardingpb "github.com/epifi/gamma/api/user/onboarding"
	ccvgpb2 "github.com/epifi/gamma/api/vendorgateway/creditcard"
	currencyinsightsvgpb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	ccvgpb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	creditlinevgpb "github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	vgpb2 "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vgshipwaypb "github.com/epifi/gamma/api/vendorgateway/shipway"
	cardconf "github.com/epifi/gamma/card/config"
	genconf2 "github.com/epifi/gamma/card/config/genconf"
	dao "github.com/epifi/gamma/card/dao"
	provisioning "github.com/epifi/gamma/card/provisioning"
	wire "github.com/epifi/gamma/card/wire"
	types2 "github.com/epifi/gamma/card/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/staging/card/hook"
	creditlimitestimatorconf "github.com/epifi/gamma/creditlimitestimator/config"
	wire2 "github.com/epifi/gamma/creditlimitestimator/wire"
	firefly2 "github.com/epifi/gamma/firefly"
	wire4 "github.com/epifi/gamma/firefly/accounting/wire"
	wire5 "github.com/epifi/gamma/firefly/billing/wire"
	fireflyconf "github.com/epifi/gamma/firefly/config"
	genconf3 "github.com/epifi/gamma/firefly/config/genconf"
	servergenwire2 "github.com/epifi/gamma/firefly/interceptor/servergen_wire"
	wire6 "github.com/epifi/gamma/firefly/lms/wire"
	wire7 "github.com/epifi/gamma/firefly/v2/wire"
	wirev2types "github.com/epifi/gamma/firefly/v2/wire/types"
	wire3 "github.com/epifi/gamma/firefly/wire"
	types3 "github.com/epifi/gamma/firefly/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.CARD_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.CARD_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.CARD_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.CARD_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()

	creditCardPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CreditCardPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CreditCardPGDB"))
		return err
	}
	creditCardPGDBSqlDb, err := creditCardPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CreditCardPGDB"))
		return err
	}
	defer func() { _ = creditCardPGDBSqlDb.Close() }()
	debitCardPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["DebitCardPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "DebitCardPGDB"))
		return err
	}
	debitCardPGDBSqlDb, err := debitCardPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "DebitCardPGDB"))
		return err
	}
	defer func() { _ = debitCardPGDBSqlDb.Close() }()
	creditCardFederalPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CreditCardFederalPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CreditCardFederalPGDB"))
		return err
	}
	creditCardFederalPGDBSqlDb, err := creditCardFederalPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CreditCardFederalPGDB"))
		return err
	}
	defer func() { _ = creditCardFederalPGDBSqlDb.Close() }()

	cardConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	accountingClient := ffaccountspb.NewAccountingClient(cardConn)
	fireflyClient := firefly.NewFireflyClient(cardConn)

	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	groupClient := usergrouppb.NewGroupClient(onboardingConn)
	usersClient := user.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actor.NewActorClient(payConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	serviceClient := currencyinsightsvgpb.NewServiceClient(vendorgatewayConn)
	locationClient := authlocationpb.NewLocationClient(onboardingConn)
	cardsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CardsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = cardsRedisStore.Close() }()
	cardProvisioningClient := vgpb2.NewCardProvisioningClient(vendorgatewayConn)
	authClient := authpb.NewAuthClient(onboardingConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savings.NewSavingsClient(centralgrowthConn)
	kycClient := kyc.NewKycClient(onboardingConn)
	cardControlClient := ccpb.NewCardControlClient(cardConn)
	growthinfraConnVar3ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewCardRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnVar3ClientInterceptors = append(growthinfraConnVar3ClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConnVar3 := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar3ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar3)
	commsClient := comms.NewCommsClient(growthinfraConnVar3)
	onboardingClient := onboardingpb.NewOnboardingClient(onboardingConn)
	piClient := pipb.NewPiClient(payConn)
	vendorgatewaypciConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.VENDORGATEWAY_PCI_SERVER)
	defer epifigrpc.CloseConn(vendorgatewaypciConn)
	cardProvisioningClientVar2 := vgpb2.NewCardProvisioningClient(vendorgatewaypciConn)
	salaryProgramClient := salaryprogrampb.NewSalaryProgramClient(centralgrowthConn)
	payClient := paypb.NewPayClient(payConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	orderServiceClient := orderpb.NewOrderServiceClient(payConn)
	bankCustomerServiceClient := bankcust.NewBankCustomerServiceClient(onboardingConn)
	balanceClient := accountbalancepb.NewBalanceClient(payConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(growthinfraConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(payConn)
	cardProvisioningClientVar7 := provisioning2.NewCardProvisioningClient(cardConn)
	shipwayClient := vgshipwaypb.NewShipwayClient(vendorgatewayConn)
	healthEngineServiceClient := healthenginepb.NewHealthEngineServiceClient(payConn)
	nudgeServiceClient := nudgepb.NewNudgeServiceClient(growthinfraConn)
	locationClientVar3 := userlocationpb.NewLocationClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	preApprovedLoanClient := preapprovedloanpb.NewPreApprovedLoanClient(lendingConn)
	creditLineClient := creditlinevgpb.NewCreditLineClient(vendorgatewayConn)
	vendorgatewayConnVar8ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire2.NewVgTenantClientInterceptor(accountingClient, fireflyClient)
	if unaryClientInterceptorVar3 != nil {
		vendorgatewayConnVar8ClientInterceptors = append(vendorgatewayConnVar8ClientInterceptors, unaryClientInterceptorVar3)
	}
	vendorgatewayConnVar8 := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar8ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar8)
	creditCardClient := ccvgpb.NewCreditCardClient(vendorgatewayConnVar8)
	billingClient := billpb.NewBillingClient(cardConn)
	docsClient := docspb.NewDocsClient(payConn)
	rewardsGeneratorClient := rewardspb.NewRewardsGeneratorClient(growthinfraConn)
	creditLimitEstimatorClient := limitestimatorpb.NewCreditLimitEstimatorClient(cardConn)
	txnAggregatesClient := ffpinotpb.NewTxnAggregatesClient(cardConn)
	loanManagementSystemClient := lms.NewLoanManagementSystemClient(cardConn)
	vendorgatewaypciConnVar5ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire2.NewVgTenantClientInterceptor(accountingClient, fireflyClient)
	if unaryClientInterceptorVar2 != nil {
		vendorgatewaypciConnVar5ClientInterceptors = append(vendorgatewaypciConnVar5ClientInterceptors, unaryClientInterceptorVar2)
	}
	vendorgatewaypciConnVar5 := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.VENDORGATEWAY_PCI_SERVER, vendorgatewaypciConnVar5ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewaypciConnVar5)
	creditCardClientVar2 := ccvgpb.NewCreditCardClient(vendorgatewaypciConnVar5)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	depositClient := deposit.NewDepositClient(wealthdmfConn)
	biometricsServiceClient := biometrics.NewBiometricsServiceClient(onboardingConn)
	projectorServiceClient := rewardsprojectionpb.NewProjectorServiceClient(growthinfraConn)
	rewardsAggregatesClient := rewardspinotpb.NewRewardsAggregatesClient(growthinfraConn)
	userriskConn := epifigrpc.NewServerConn(cfg.CARD_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	profileClient := profilepb.NewProfileClient(userriskConn)
	offerRedemptionServiceClient := redemptionpb.NewOfferRedemptionServiceClient(growthinfraConn)
	cardRecommendationServiceClient := ffrepb.NewCardRecommendationServiceClient(cardConn)
	fireflyRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["FireflyRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = fireflyRedisStore.Close() }()
	commsClientVar6 := comms.NewCommsClient(growthinfraConn)
	consentClient := consentpb.NewConsentClient(onboardingConn)
	fireflyV2Client := ffbev2pb.NewFireflyV2Client(cardConn)
	txnCategorizerClient := categorizerpb.NewTxnCategorizerClient(wealthdmfConn)
	breClient := brepb.NewBreClient(lendingConn)
	productClient := product.NewProductClient(onboardingConn)
	creditReportManagerClient := creditreportv2pb.NewCreditReportManagerClient(lendingConn)
	riskClient := riskpb.NewRiskClient(userriskConn)
	merchantServiceClient := merchantpb.NewMerchantServiceClient(payConn)
	creditCardClientVar8 := ccvgpb.NewCreditCardClient(vendorgatewayConn)
	creditCardClientVar10 := ccvgpb2.NewCreditCardClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor, err := servergenwire.AddRudderEventsUnaryInterceptor(broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire2.NewUnaryCreditCardRequestValidatorInterceptor(creditCardPGDB)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3, err := servergenwire3.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupCard(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, serviceClient, locationClient, cardsRedisStore, cardProvisioningClient, epifiCRDB, debitCardPGDB, authClient, savingsClient, kycClient, cardControlClient, commsClient, onboardingClient, rateLimiterRedisStore, piClient, cardProvisioningClientVar2, salaryProgramClient, payClient, celestialClient, tieringClient, orderServiceClient, bankCustomerServiceClient, balanceClient, vKYCClient, inAppTargetedCommsClient, accountPIRelationClient, cardProvisioningClientVar7, shipwayClient, healthEngineServiceClient, nudgeServiceClient, locationClientVar3)
	if err != nil {
		return err
	}
	err = setupCreditlimitestimator(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, preApprovedLoanClient, fireflyClient, creditLineClient, usersClient, actorClient, groupClient, bankCustomerServiceClient)
	if err != nil {
		return err
	}
	err = setupFirefly(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, creditCardPGDB, celestialClient, actorClient, accountingClient, creditCardClient, usersClient, bankCustomerServiceClient, billingClient, docsClient, orderServiceClient, payClient, rewardsGeneratorClient, creditLimitEstimatorClient, txnAggregatesClient, loanManagementSystemClient, creditCardClientVar2, groupClient, depositClient, fireflyClient, savingsClient, biometricsServiceClient, projectorServiceClient, rewardsAggregatesClient, rateLimiterRedisStore, profileClient, offerRedemptionServiceClient, cardRecommendationServiceClient, fireflyRedisStore, commsClientVar6, consentClient, cardProvisioningClientVar7, segmentationServiceClient, piClient, inAppTargetedCommsClient, fireflyV2Client, txnCategorizerClient, breClient, productClient, creditReportManagerClient, riskClient, merchantServiceClient, creditCardClientVar8, creditCardFederalPGDB, creditCardClientVar10, authClient, onboardingClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.BeforeCardServerStart(s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "BeforeCardServerStart"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := servergenwire3.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupCard(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	serviceClient currencyinsightsvgpb.ServiceClient,
	locationClient authlocationpb.LocationClient,
	cardsRedisStore types2.CardsRedisStore,
	cardProvisioningClient vgpb2.CardProvisioningClient,
	epifiCRDB types.EpifiCRDB,
	debitCardPGDB dao.DebitCardPGDB,
	authClient authpb.AuthClient,
	savingsClient savings.SavingsClient,
	kycClient kyc.KycClient,
	cardControlClient ccpb.CardControlClient,
	commsClient types2.CardCommsClientWithInterceptors,
	onboardingClient onboardingpb.OnboardingClient,
	rateLimiterRedisStore types.RateLimiterRedisStore,
	piClient pipb.PiClient,
	cardProvisioningClientVar2 provisioning.CardProvisioningClientToVendorGatewayPCIServer,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	payClient paypb.PayClient,
	celestialClient celestialpb.CelestialClient,
	tieringClient tieringpb.TieringClient,
	orderServiceClient orderpb.OrderServiceClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	balanceClient accountbalancepb.BalanceClient,
	vKYCClient vkycpb.VKYCClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	cardProvisioningClientVar7 provisioning2.CardProvisioningClient,
	shipwayClient vgshipwaypb.ShipwayClient,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	locationClientVar3 userlocationpb.LocationClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	cardConf, err := cardconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CARD_SERVICE))
		return err
	}
	_ = cardConf

	cardGenConf, err := dynconf.LoadConfigWithQuestConfig(cardconf.Load, genconf2.NewConfigWithQuest, cfg.CARD_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CARD_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		cardGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: cardGenConf, SdkConfig: cardGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{cardGenConfAppConfig}, string(cfg.CARD_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = cardGenConf

	creationPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	pinSetEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.PinSetEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	renewCardPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.RenewCardPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	shipmentRegisterEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.ShipmentRegisterEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardDispatchRequestPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CardDispatchRequestPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderPhysicalCardCriticalNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.OrderPhysicalCardCriticalNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	initDispatchWithAddressUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.InitDispatchWithAddressUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	creationDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	piCreationPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.PiCreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveryDelayEventDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveryDelayEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveryDelayEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveryDelayEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveredEventDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveredEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardDispatchRequestDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CardDispatchRequestPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveredEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveredEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	debitCardUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cardGenConf.DebitCardUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	cardCreationEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cardGenConf.CardCreationEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	cardSwitchNotificationRewardsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cardGenConf.CardSwitchNotificationRewardsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	dcDocS3Client := s3pkg.NewClient(awsConf, cardGenConf.Buckets().DebitCardDocs)
	dcDocS3ClientVar2 := s3pkg.NewClient(awsConf, cardGenConf.Buckets().DebitCardDocs)
	rawDataS3Client := s3pkg.NewClient(awsConf, cardGenConf.CardSwitchNotificationsRawDataStore().BucketName)

	service := wire.InitializeCurrencyInsightsService(serviceClient, locationClient, cardConf, cardsRedisStore)

	cardcipb.RegisterCurrencyInsightsServer(s, service)

	serviceVar2 := wire.InitializeService(cardProvisioningClient, epifiCRDB, debitCardPGDB, creationPublisher, pinSetEventPublisher, actorClient, authClient, usersClient, broker, savingsClient, renewCardPublisher, kycClient, cardControlClient, cardConf, commsClient, groupClient, onboardingClient, shipmentRegisterEventPublisher, rateLimiterRedisStore, piClient, cardProvisioningClientVar2, cardGenConf, cardDispatchRequestPublisher, orderPhysicalCardCriticalNotificationPublisher, initDispatchWithAddressUpdatePublisher, salaryProgramClient, payClient, celestialClient, tieringClient, orderServiceClient, bankCustomerServiceClient, debitCardUpdateEventPublisher, balanceClient, segmentationServiceClient, cardsRedisStore, vKYCClient, serviceClient, inAppTargetedCommsClient)

	provisioning2.RegisterCardProvisioningServer(s, serviceVar2)

	cardDbStatesService := wire.InitializeDevCardService(epifiCRDB, debitCardPGDB, cardsRedisStore, cardConf)

	developer2.RegisterCardDbStatesServer(s, cardDbStatesService)

	serviceVar3 := wire.InitializeCardControlService(epifiCRDB, debitCardPGDB, cardProvisioningClient, authClient, actorClient, usersClient, savingsClient, broker, cardConf, piClient, commsClient, cardGenConf, cardProvisioningClientVar2, groupClient, bankCustomerServiceClient, cardsRedisStore)

	ccpb.RegisterCardControlServer(s, serviceVar3)

	serviceVar4 := wire.InitializeCxService(debitCardPGDB, epifiCRDB, cardsRedisStore, cardConf)

	cardcxpb.RegisterCxServer(s, serviceVar4)

	serviceVar5 := wire.InitializeTokenizerProxyService(cardProvisioningClientVar2)

	tokenizerproxypb.RegisterTokenizerProxyServer(s, serviceVar5)

	serviceVar6, err := wire.InitializeDebitCardMandateService(debitCardPGDB)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	dcmandatepb.RegisterDebitCardMandateServiceServer(s, serviceVar6)

	serviceVar7 := wire.InitializeConsumerService(cardProvisioningClient, epifiCRDB, debitCardPGDB, creationDelayPublisher, piClient, accountPIRelationClient, actorClient, piCreationPublisher, authClient, usersClient, savingsClient, cardControlClient, broker, cardProvisioningClientVar7, commsClient, cardConf, renewCardPublisher, creationPublisher, shipmentRegisterEventPublisher, shipwayClient, deliveryDelayEventDelayPublisher, deliveryDelayEventPublisher, groupClient, deliveredEventDelayPublisher, cardProvisioningClientVar2, cardGenConf, cardDispatchRequestDelayPublisher, cardDispatchRequestPublisher, orderPhysicalCardCriticalNotificationPublisher, bankCustomerServiceClient, cardCreationEventPublisher, healthEngineServiceClient, celestialClient, nudgeServiceClient, dcDocS3Client, cardsRedisStore, locationClient, locationClientVar3)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		provisioning2.RegisterCardConsumerServer(s, serviceVar7)
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardCreationMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.PiCreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardPiCreationMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.PinSetEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardPinSetEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.RenewCardSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardRenewalEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardOnboardingEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardOnboardingEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardAuthFactorUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.ShipmentRegisterEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardShipmentRegisterEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.DeliveryDelayEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardDeliveryDelayEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.DeliveredEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardDeliveredEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.FetchTrackingDetailsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterGetTrackingDetailsMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardDispatchRequestSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardDispatchMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.InitDispatchWithAddressUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessShippingAddressUpdateAndDispatchPhysicalCardMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardsDispatchedCsvFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardsDispatchedCsvFileMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.DcAmcChargesEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardAmcChargesEligibleUserFileMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.OrderPhysicalCardCriticalNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterOrderPhysicalCardCriticalNotificationMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.UserDevicePropertiesUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber(subscriber, serviceVar7)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	callbackService := wire.InitializeCallbackConsumerService(epifiCRDB, debitCardPGDB, piCreationPublisher, broker, cardConf, actorClient, commsClient, savingsClient, usersClient, deliveryDelayEventPublisher, deliveredEventPublisher, groupClient, bankCustomerServiceClient, cardCreationEventPublisher, cardGenConf, celestialClient, cardsRedisStore, orderPhysicalCardCriticalNotificationPublisher, cardControlClient, nudgeServiceClient)

	provisioning2.RegisterCallBackConsumerServer(s, callbackService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CreationCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		provisioning2.RegisterProcessCardCreationCallBackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.TrackingCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		provisioning2.RegisterProcessCardTrackingCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardDispatchRequestCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		provisioning2.RegisterProcessDispatchPhysicalCardCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar8 := wire.InitializeNotificationConsumerService(debitCardPGDB, cardProvisioningClientVar7, cardControlClient, savingsClient, payClient, celestialClient, orderServiceClient, commsClient, actorClient, cardConf, rateLimiterRedisStore, groupClient, usersClient, cardGenConf, dcDocS3ClientVar2, broker, rawDataS3Client, cardsRedisStore, cardSwitchNotificationRewardsPublisher)

	notificationpb.RegisterNotificationConsumerServiceServer(s, serviceVar8)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardSwitchFinancialNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessCardSwitchFinancialNotificationsMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardSwitchNonFinancialNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessCardSwitchNonFinancialNotificationsMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardForexTxnNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessForexTransactionsRefundMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardTxnNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessCardTransactionsMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.CARD_SERVICE)] = &commonexplorer.Config{StaticConf: &cardconf.Config{}, QuestIntegratedConfig: cardGenConf}

	return nil

}

// nolint: funlen
func setupCreditlimitestimator(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	fireflyClient firefly.FireflyClient,
	creditLineClient creditlinevgpb.CreditLineClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	groupClient usergrouppb.GroupClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	creditlimitestimatorConf, err := creditlimitestimatorconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.LIMIT_ESTIMATOR_SERVICE))
		return err
	}
	_ = creditlimitestimatorConf

	serviceVar9 := wire2.InitialiseCreditLimitEstimatorSvc(creditlimitestimatorConf, preApprovedLoanClient, fireflyClient, creditLineClient, usersClient, actorClient, groupClient, bankCustomerServiceClient)

	limitestimatorpb.RegisterCreditLimitEstimatorServer(s, serviceVar9)

	configNameToConfMap[cfg.ConfigName(cfg.LIMIT_ESTIMATOR_SERVICE)] = &commonexplorer.Config{StaticConf: &creditlimitestimatorconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupFirefly(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	creditCardPGDB types.CreditCardPGDB,
	celestialClient celestialpb.CelestialClient,
	actorClient actor.ActorClient,
	accountingClient ffaccountspb.AccountingClient,
	creditCardClient types3.CreditCardVgClientWithInterceptors,
	usersClient user.UsersClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	billingClient billpb.BillingClient,
	docsClient docspb.DocsClient,
	orderServiceClient orderpb.OrderServiceClient,
	payClient paypb.PayClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	creditLimitEstimatorClient limitestimatorpb.CreditLimitEstimatorClient,
	txnAggregatesClient ffpinotpb.TxnAggregatesClient,
	loanManagementSystemClient lms.LoanManagementSystemClient,
	creditCardClientVar2 firefly2.CreditCardClientWithInterceptorsToVendorGatewayPCIServer,
	groupClient usergrouppb.GroupClient,
	depositClient deposit.DepositClient,
	fireflyClient firefly.FireflyClient,
	savingsClient savings.SavingsClient,
	biometricsServiceClient biometrics.BiometricsServiceClient,
	projectorServiceClient rewardsprojectionpb.ProjectorServiceClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	rateLimiterRedisStore types.RateLimiterRedisStore,
	profileClient profilepb.ProfileClient,
	offerRedemptionServiceClient redemptionpb.OfferRedemptionServiceClient,
	cardRecommendationServiceClient ffrepb.CardRecommendationServiceClient,
	fireflyRedisStore types3.FireflyRedisStore,
	commsClientVar6 comms.CommsClient,
	consentClient consentpb.ConsentClient,
	cardProvisioningClientVar7 provisioning2.CardProvisioningClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	piClient pipb.PiClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	fireflyV2Client ffbev2pb.FireflyV2Client,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	breClient brepb.BreClient,
	productClient product.ProductClient,
	creditReportManagerClient creditreportv2pb.CreditReportManagerClient,
	riskClient riskpb.RiskClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	creditCardClientVar8 ccvgpb.CreditCardClient,
	creditCardFederalPGDB wirev2types.CreditCardFederalPGDB,
	creditCardClientVar10 ccvgpb2.CreditCardClient,
	authClient authpb.AuthClient,
	onboardingClient onboardingpb.OnboardingClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	fireflyConf, err := fireflyconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.FIREFLY_SERVICE))
		return err
	}
	_ = fireflyConf

	fireflyGenConf, err := dynconf.LoadConfig(fireflyconf.Load, genconf3.NewConfig, cfg.FIREFLY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.FIREFLY_SERVICE))
		return err
	}

	_ = fireflyGenConf

	signalWorkflowPublisher, err := sqs.NewPublisherWithConfig(ctx, fireflyGenConf.SignalWorkflowPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	ccOnboardingStateUpdateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, fireflyGenConf.CcOnboardingStateUpdateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	cCTransactionEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, fireflyGenConf.CCTransactionEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	acsS3Client := s3pkg.NewClient(awsConf, fireflyGenConf.AcsBucketName())
	rawDataS3ClientVar2 := s3pkg.NewClient(awsConf, fireflyGenConf.AcsNotificationRawDataStore().BucketName)
	cardsSentForPrintingS3Client := s3pkg.NewClient(awsConf, fireflyGenConf.CardsSentForPrintingBucketName())
	cardsDispatchedS3Client := s3pkg.NewClient(awsConf, fireflyGenConf.CardsDispatchedBucketName())

	fireflyClientVar5, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Firefly, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	serviceVar10 := wire3.InitialiseFireflySvc(creditCardPGDB, celestialClient, signalWorkflowPublisher, fireflyConf, actorClient, accountingClient, creditCardClient, usersClient, bankCustomerServiceClient, billingClient, docsClient, orderServiceClient, payClient, rewardsGeneratorClient, creditLimitEstimatorClient, txnAggregatesClient, loanManagementSystemClient, creditCardClientVar2, fireflyGenConf, groupClient, broker, depositClient, fireflyClient, savingsClient, biometricsServiceClient, fireflyClientVar5, projectorServiceClient, rewardsAggregatesClient, rateLimiterRedisStore, profileClient, offerRedemptionServiceClient, cardRecommendationServiceClient, fireflyRedisStore, commsClientVar6, consentClient, cardProvisioningClientVar7, segmentationServiceClient, piClient, inAppTargetedCommsClient, fireflyV2Client)

	firefly.RegisterFireflyServer(s, serviceVar10)

	serviceVar11 := wire4.InitialiseAccountingSvc(creditCardPGDB, piClient, actorClient, txnCategorizerClient, billingClient, creditCardClient, bankCustomerServiceClient, fireflyClient, depositClient)

	ffaccountspb.RegisterAccountingServer(s, serviceVar11)

	serviceVar12 := wire4.InitialiseAccountingConsumerSvc(fireflyClient, creditCardPGDB, creditCardClient, actorClient, piClient, cCTransactionEventPublisher, celestialClient, billingClient, acsS3Client, rawDataS3ClientVar2, fireflyConf)

	ffaccconsumerpb.RegisterConsumerServer(s, serviceVar12)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCTransactionNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffaccconsumerpb.RegisterProcessCardTransactionNotificationMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCAcsNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffaccconsumerpb.RegisterProcessAcsNotificationMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar13, err := wire3.InitializeConsumerService(creditCardPGDB, awsConf, fireflyConf, usersClient, bankCustomerServiceClient, celestialClient, actorClient, creditCardClient, accountingClient, billingClient, docsClient, orderServiceClient, payClient, rewardsGeneratorClient, cardsSentForPrintingS3Client, cardsDispatchedS3Client, txnAggregatesClient, loanManagementSystemClient, commsClientVar6, fireflyClient, depositClient, savingsClient, projectorServiceClient, profileClient, fireflyRedisStore, broker, breClient, productClient, creditReportManagerClient, cardRecommendationServiceClient, riskClient, segmentationServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	ffconsumerpb.RegisterCreditCardConsumerServer(s, serviceVar13)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CardsSentForPrintingCsvFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffconsumerpb.RegisterProcessCardsSentForPrintingCsvFileMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CardsDispatchedCsvFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffconsumerpb.RegisterProcessCardsDispatchedCsvFileMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCAuthFactorUpdateNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffconsumerpb.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCNonFinancialNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffconsumerpb.RegisterProcessNonFinancialNotificationEventMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	consumer, err := wire3.InitialiseConsumerServiceForPinot(creditCardPGDB, merchantServiceClient, txnCategorizerClient, awsConf, fireflyConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	ffpinotpb.RegisterConsumerServer(s, consumer)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCTransactionsForPinotSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffpinotpb.RegisterProcessCCTransactionEventMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CategorizerUpdateForPinotSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffpinotpb.RegisterProcessCategoryUpdateEventMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar14 := wire3.InitialiseTxnAggregatesService(fireflyConf, txnCategorizerClient, accountingClient, fireflyClient)

	ffpinotpb.RegisterTxnAggregatesServer(s, serviceVar14)

	serviceVar15 := wire5.InitialiseBillingSvc(creditCardPGDB, fireflyClient, accountingClient, creditCardClient, bankCustomerServiceClient, broker, fireflyConf)

	billpb.RegisterBillingServer(s, serviceVar15)

	consumerService := wire5.InitialiseBillingConsumerSvc(fireflyClient)

	ffbillingconsumerpb.RegisterConsumerServer(s, consumerService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCStatementNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffbillingconsumerpb.RegisterProcessStatementGeneratedNotificationMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar16 := wire3.InitializeFireflyCxService(creditCardPGDB, fireflyClient, creditCardClient, fireflyConf, fireflyRedisStore, broker, commsClientVar6, depositClient, billingClient, accountingClient, usersClient, actorClient)

	cccxpb.RegisterCxServer(s, serviceVar16)

	fireflyDevEntity := wire3.InitializeFireflyDevEntityService(creditCardPGDB, fireflyConf, fireflyRedisStore, commsClientVar6, depositClient, billingClient, accountingClient, usersClient, creditCardClientVar8, fireflyClient, actorClient, broker, creditCardFederalPGDB)

	ccdevpb.RegisterDevFireflyServer(s, fireflyDevEntity)

	serviceVar17 := wire6.InitialiseLoanManagementSystemService(creditCardPGDB, accountingClient, creditCardClient, fireflyClient, fireflyConf, fireflyGenConf, groupClient, usersClient, actorClient, billingClient, commsClientVar6, broker, depositClient)

	lms.RegisterLoanManagementSystemServer(s, serviceVar17)

	serviceVar18 := wire3.InitializeRecommendationSvc(creditCardPGDB, fireflyConf, fireflyGenConf, fireflyClient, segmentationServiceClient, broker, fireflyRedisStore, groupClient, usersClient, actorClient)

	ffrepb.RegisterCardRecommendationServiceServer(s, serviceVar18)

	serviceVar19 := wire7.InitialiseFireflyV2Svc(fireflyGenConf, creditCardFederalPGDB, usersClient, creditCardClientVar10, authClient, bankCustomerServiceClient, onboardingClient, ccOnboardingStateUpdateEventPublisher)

	ffbev2pb.RegisterFireflyV2Server(s, serviceVar19)

	serviceVar20 := wire7.InitialiseConsumerV2Service(creditCardFederalPGDB)

	ffconsumerv2pb.RegisterCreditCardConsumerServer(s, serviceVar20)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fireflyGenConf.CCOnboardingStateUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ffconsumerv2pb.RegisterProcessCreditCardOnboardingStateUpdateEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.FIREFLY_SERVICE)] = &commonexplorer.Config{StaticConf: &fireflyconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.CARD_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
