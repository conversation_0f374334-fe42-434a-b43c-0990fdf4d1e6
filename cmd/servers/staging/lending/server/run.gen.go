// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	http "net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire3 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	namespace "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	actor "github.com/epifi/gamma/api/actor"
	authpb "github.com/epifi/gamma/api/auth"
	livenesspb "github.com/epifi/gamma/api/auth/liveness"
	locationpb "github.com/epifi/gamma/api/auth/location"
	orchpb "github.com/epifi/gamma/api/auth/orchestrator"
	bankcustpb "github.com/epifi/gamma/api/bankcust"
	brepb2 "github.com/epifi/gamma/api/bre"
	cardpb "github.com/epifi/gamma/api/card/provisioning"
	collection "github.com/epifi/gamma/api/collection"
	collectiondeveloper "github.com/epifi/gamma/api/collection/developer"
	commspb "github.com/epifi/gamma/api/comms"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	consentpb "github.com/epifi/gamma/api/consent"
	limitestimatorpb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditreport "github.com/epifi/gamma/api/creditreportv2"
	crpb "github.com/epifi/gamma/api/creditreportv2/consumer"
	crdapb "github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	crdaconsumerpb "github.com/epifi/gamma/api/creditreportv2/derivedattributes/consumer"
	developer3 "github.com/epifi/gamma/api/creditreportv2/developer"
	docspb "github.com/epifi/gamma/api/docs"
	esignpb "github.com/epifi/gamma/api/docs/esign"
	beemploymentpb "github.com/epifi/gamma/api/employment"
	epfopb "github.com/epifi/gamma/api/epfo"
	inhousebrepb "github.com/epifi/gamma/api/inhousebre"
	catalogmanagerpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfexternalorderpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycpb "github.com/epifi/gamma/api/kyc"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	leadspb "github.com/epifi/gamma/api/leads"
	leadsdevpb "github.com/epifi/gamma/api/leads/developer"
	nudge "github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	paypb "github.com/epifi/gamma/api/pay"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	consumer2 "github.com/epifi/gamma/api/preapprovedloan/consumer"
	palcxpb "github.com/epifi/gamma/api/preapprovedloan/cx"
	paldevpb "github.com/epifi/gamma/api/preapprovedloan/developer"
	inboundnotification "github.com/epifi/gamma/api/preapprovedloan/inbound_notification"
	lendabilitypb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	preeligibilitypb "github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	securedloanspb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	sherlockbannerspb "github.com/epifi/gamma/api/preapprovedloan/sherlock_banners"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	profilepb "github.com/epifi/gamma/api/risk/profile"
	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
	savings "github.com/epifi/gamma/api/savings"
	segmentpb "github.com/epifi/gamma/api/segment"
	sgesignapigatewaypb "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	sgkycapigatewaypb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sglmspb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	user "github.com/epifi/gamma/api/user"
	creditreportpb "github.com/epifi/gamma/api/user/credit_report"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	userlocationpb "github.com/epifi/gamma/api/user/location"
	obfuscator "github.com/epifi/gamma/api/user/obfuscator"
	onbpb "github.com/epifi/gamma/api/user/onboarding"
	userintelpb "github.com/epifi/gamma/api/userintel"
	creditreportvgpb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	fennelvgpb "github.com/epifi/gamma/api/vendorgateway/fennel"
	brevgpb "github.com/epifi/gamma/api/vendorgateway/lending/bre"
	digitapvgpb "github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	finflux "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	palvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	abflvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	idfcvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	ldcvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	llvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	mvvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	ffvgpb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	location "github.com/epifi/gamma/api/vendorgateway/location"
	accountvgpb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgcustomerpb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	savingsvgpb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	panvgpb "github.com/epifi/gamma/api/vendorgateway/pan"
	vgscienapticpb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	vendormapping "github.com/epifi/gamma/api/vendormapping"
	breconf "github.com/epifi/gamma/bre/config"
	wire "github.com/epifi/gamma/bre/wire"
	hook "github.com/epifi/gamma/cmd/servers/staging/lending/hook"
	collectionconf "github.com/epifi/gamma/collection/config"
	wire6 "github.com/epifi/gamma/collection/wire"
	creditreportv2conf "github.com/epifi/gamma/creditreportv2/config"
	genconf3 "github.com/epifi/gamma/creditreportv2/config/genconf"
	wire5 "github.com/epifi/gamma/creditreportv2/derivedattributes/wire"
	wire4 "github.com/epifi/gamma/creditreportv2/wire"
	epfoconf "github.com/epifi/gamma/epfo/config"
	genconf4 "github.com/epifi/gamma/epfo/config/genconf"
	wire7 "github.com/epifi/gamma/epfo/wire"
	inhousebreconf "github.com/epifi/gamma/inhousebre/config"
	wire2 "github.com/epifi/gamma/inhousebre/wire"
	leadsconf "github.com/epifi/gamma/leads/config"
	wire8 "github.com/epifi/gamma/leads/wire"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire2 "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	preapprovedloanconf "github.com/epifi/gamma/preapprovedloan/config"
	genconf2 "github.com/epifi/gamma/preapprovedloan/config/genconf"
	servergenwire "github.com/epifi/gamma/preapprovedloan/interceptors/servergen_wire"
	wire3 "github.com/epifi/gamma/preapprovedloan/wire"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.LENDING_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.LENDING_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.LENDING_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.LENDING_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()

	loansFederalPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["LoansFederalPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "LoansFederalPGDB"))
		return err
	}
	loansFederalPGDBSqlDb, err := loansFederalPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "LoansFederalPGDB"))
		return err
	}
	defer func() { _ = loansFederalPGDBSqlDb.Close() }()
	featureEngineeringPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["FeatureEngineeringPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FeatureEngineeringPGDB"))
		return err
	}
	featureEngineeringPGDBSqlDb, err := featureEngineeringPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FeatureEngineeringPGDB"))
		return err
	}
	defer func() { _ = featureEngineeringPGDBSqlDb.Close() }()
	creditRiskPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CreditRiskPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CreditRiskPGDB"))
		return err
	}
	creditRiskPGDBSqlDb, err := creditRiskPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CreditRiskPGDB"))
		return err
	}
	defer func() { _ = creditRiskPGDBSqlDb.Close() }()

	lendingConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	breClient := inhousebrepb.NewBreClient(lendingConn)
	preApprovedLoanClient := palpb.NewPreApprovedLoanClient(lendingConn)
	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	groupClient := usergrouppb.NewGroupClient(onboardingConn)
	usersClient := user.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actor.NewActorClient(payConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	businessRuleEngineClient := brevgpb.NewBusinessRuleEngineClient(vendorgatewayConn)
	fennelFeatureStoreClient := fennelvgpb.NewFennelFeatureStoreClient(vendorgatewayConn)
	scienapticClient := vgscienapticpb.NewScienapticClient(vendorgatewayConn)
	epfoClient := epfopb.NewEpfoClient(lendingConn)
	creditReportManagerClient := creditreport.NewCreditReportManagerClient(lendingConn)
	fireflyRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["FireflyRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = fireflyRedisStore.Close() }()
	centralgrowthConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savings.NewSavingsClient(centralgrowthConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	mFExternalOrdersClient := mfexternalorderpb.NewMFExternalOrdersClient(wealthdmfConn)
	preApprovedLoanClientVar2 := palvgpb.NewPreApprovedLoanClient(vendorgatewayConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	livenessClient := livenesspb.NewLivenessClient(onboardingConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	kycClient := kycpb.NewKycClient(onboardingConn)
	authClient := authpb.NewAuthClient(onboardingConn)
	eSignClient := esignpb.NewESignClient(payConn)
	customerClient := vgcustomerpb.NewCustomerClient(vendorgatewayConn)
	commsClient := commspb.NewCommsClient(growthinfraConn)
	orderServiceClient := orderpb.NewOrderServiceClient(payConn)
	payClient := paypb.NewPayClient(payConn)
	piClient := pipb.NewPiClient(payConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(payConn)
	paymentClient := paymentpb.NewPaymentClient(payConn)
	savingsClientVar3 := savingsvgpb.NewSavingsClient(vendorgatewayConn)
	liquiloansClient := llvgpb.NewLiquiloansClient(vendorgatewayConn)
	userriskConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	profileClient := profilepb.NewProfileClient(userriskConn)
	bankCustomerServiceClient := bankcustpb.NewBankCustomerServiceClient(onboardingConn)
	salaryProgramClient := salaryprogram.NewSalaryProgramClient(centralgrowthConn)
	employmentClient := beemploymentpb.NewEmploymentClient(onboardingConn)
	idfcClient := idfcvgpb.NewIdfcClient(vendorgatewayConn)
	accountsClient := accountvgpb.NewAccountsClient(vendorgatewayConn)
	creditReportManagerClientVar2 := creditreportpb.NewCreditReportManagerClient(onboardingConn)
	onboardingClient := onbpb.NewOnboardingClient(onboardingConn)
	obfuscatorClient := obfuscator.NewObfuscatorClient(onboardingConn)
	balanceClient := accountbalancepb.NewBalanceClient(payConn)
	collectionClient := collection.NewCollectionClient(lendingConn)
	pANClient := panvgpb.NewPANClient(vendorgatewayConn)
	fiftyFinClient := ffvgpb.NewFiftyFinClient(vendorgatewayConn)
	cardConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	cardProvisioningClient := cardpb.NewCardProvisioningClient(cardConn)
	creditLimitEstimatorClient := limitestimatorpb.NewCreditLimitEstimatorClient(cardConn)
	abflClient := abflvgpb.NewAbflClient(vendorgatewayConn)
	connectedAccountClient := connectedaccountpb.NewConnectedAccountClient(centralgrowthConn)
	moneyviewClient := mvvgpb.NewMoneyviewClient(vendorgatewayConn)
	locationClient := userlocationpb.NewLocationClient(onboardingConn)
	catalogManagerClient := catalogmanagerpb.NewCatalogManagerClient(wealthdmfConn)
	securedLoansClient := securedloanspb.NewSecuredLoansClient(lendingConn)
	nudgeServiceClient := nudge.NewNudgeServiceClient(growthinfraConn)
	finfluxClient := finflux.NewFinfluxClient(vendorgatewayConn)
	userIntelServiceClient := userintelpb.NewUserIntelServiceClient(onboardingConn)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(growthinfraConn)
	consentClient := consentpb.NewConsentClient(onboardingConn)
	goblinConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.GOBLIN_SERVER)
	defer epifigrpc.CloseConn(goblinConn)
	esignClient := sgesignapigatewaypb.NewEsignClient(goblinConn)
	lmsClient := sglmspb.NewLmsClient(goblinConn)
	lendenClient := ldcvgpb.NewLendenClient(vendorgatewayConn)
	orchestratorClient := orchpb.NewOrchestratorClient(onboardingConn)
	recurringPaymentServiceClient := recurringpaymentpb.NewRecurringPaymentServiceClient(payConn)
	locationClientVar2 := locationpb.NewLocationClient(onboardingConn)
	kYCClient := sgkycapigatewaypb.NewKYCClient(goblinConn)
	docsClient := docspb.NewDocsClient(payConn)
	locationClientVar14 := location.NewLocationClient(vendorgatewayConn)
	breClientVar2 := brepb2.NewBreClient(lendingConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(payConn)
	creditReportClient := creditreportvgpb.NewCreditReportClient(vendorgatewayConn)
	var analyticsDBConnTeardown func()
	analyticsDBResourceProvider, analyticsDBConnTeardown, err := storage2.NewAnalyticsDBResourceProvider(gconf.AnalyticsDBConfigMap().GetOwnershipToDbConfigMap())
	if err != nil {
		logger.Error(ctx, "failed to get db conn provider", zap.Error(err))
		return err
	}
	defer analyticsDBConnTeardown()
	vendormappingConn := epifigrpc.NewServerConn(cfg.LENDING_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vendormapping.NewVendorMappingServiceClient(vendormappingConn)
	digitapServiceClient := digitapvgpb.NewDigitapServiceClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.NewUnaryLoanHeaderInterceptor(preApprovedLoanClient)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire2.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3, err := servergenwire3.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupBre(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, businessRuleEngineClient, usersClient, actorClient, groupClient)
	if err != nil {
		return err
	}
	err = setupInhousebre(ctx, s, httpMux, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, fennelFeatureStoreClient, scienapticClient, usersClient, epfoClient, creditReportManagerClient)
	if err != nil {
		return err
	}
	err = setupPreapprovedloan(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, loansFederalPGDB, fireflyRedisStore, savingsClient, mFExternalOrdersClient, preApprovedLoanClientVar2, preApprovedLoanClient, celestialClient, livenessClient, vKYCClient, kycClient, authClient, eSignClient, customerClient, commsClient, orderServiceClient, payClient, piClient, accountPIRelationClient, paymentClient, savingsClientVar3, liquiloansClient, profileClient, bankCustomerServiceClient, salaryProgramClient, employmentClient, idfcClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, onboardingClient, obfuscatorClient, balanceClient, collectionClient, pANClient, fiftyFinClient, cardProvisioningClient, creditLimitEstimatorClient, abflClient, connectedAccountClient, moneyviewClient, locationClient, catalogManagerClient, securedLoansClient, nudgeServiceClient, finfluxClient, userIntelServiceClient, inAppTargetedCommsClient, consentClient, esignClient, lmsClient, lendenClient, orchestratorClient, recurringPaymentServiceClient, locationClientVar2, kYCClient, epifiCRDB, docsClient, locationClientVar14, breClientVar2, operationalStatusServiceClient)
	if err != nil {
		return err
	}
	err = setupCreditreportv2(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, featureEngineeringPGDB, celestialClient, consentClient, onboardingClient, usersClient, creditReportClient, fennelFeatureStoreClient, scienapticClient, analyticsDBResourceProvider, vendorMappingServiceClient, creditReportManagerClient)
	if err != nil {
		return err
	}
	err = setupCollection(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, celestialClient)
	if err != nil {
		return err
	}
	err = setupEpfo(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, creditRiskPGDB, pANClient, usersClient, actorClient, digitapServiceClient)
	if err != nil {
		return err
	}
	err = setupLeads(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, usersClient, preApprovedLoanClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitLendingServer(gconf, s, httpMux, breClient) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitLendingServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := servergenwire3.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupBre(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	businessRuleEngineClient brevgpb.BusinessRuleEngineClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	groupClient usergrouppb.GroupClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	breConf, err := breconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BRE_SERVICE))
		return err
	}
	_ = breConf

	inhouseBreS3Client := s3pkg.NewClient(awsConf, breConf.InhouseBreBucketName)
	rawInhouseBreS3Client := s3pkg.NewClient(awsConf, breConf.RawInhouseBreBucketName)

	service := wire.InitialiseBreService(breConf, businessRuleEngineClient, inhouseBreS3Client, rawInhouseBreS3Client, usersClient, broker, actorClient, groupClient)

	brepb2.RegisterBreServer(s, service)

	configNameToConfMap[cfg.ConfigName(cfg.BRE_SERVICE)] = &commonexplorer.Config{StaticConf: &breconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupInhousebre(ctx context.Context, s *grpc.Server, httpMux *http.ServeMux, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	fennelFeatureStoreClient fennelvgpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	usersClient user.UsersClient,
	epfoClient epfopb.EpfoClient,
	creditReportManagerClient creditreport.CreditReportManagerClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	inhousebreConf, err := inhousebreconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INHOUSE_BRE_SERVICE))
		return err
	}
	_ = inhousebreConf

	serviceVar2 := wire2.InitialiseInhouseBreService(fennelFeatureStoreClient, scienapticClient, usersClient, epfoClient, creditReportManagerClient)

	inhousebrepb.RegisterBreServer(s, serviceVar2)

	httpMux.HandleFunc("/inhouse/getEpfoData", serviceVar2.GetEpfoDataV1)

	configNameToConfMap[cfg.ConfigName(cfg.INHOUSE_BRE_SERVICE)] = &commonexplorer.Config{StaticConf: &inhousebreconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupPreapprovedloan(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	loansFederalPGDB types.LoansFederalPGDB,
	fireflyRedisStore types2.FireflyRedisStore,
	savingsClient savings.SavingsClient,
	mFExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient,
	preApprovedLoanClientVar2 palvgpb.PreApprovedLoanClient,
	preApprovedLoanClient palpb.PreApprovedLoanClient,
	celestialClient celestialpb.CelestialClient,
	livenessClient livenesspb.LivenessClient,
	vKYCClient vkycpb.VKYCClient,
	kycClient kycpb.KycClient,
	authClient authpb.AuthClient,
	eSignClient esignpb.ESignClient,
	customerClient vgcustomerpb.CustomerClient,
	commsClient commspb.CommsClient,
	orderServiceClient orderpb.OrderServiceClient,
	payClient paypb.PayClient,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	paymentClient paymentpb.PaymentClient,
	savingsClientVar3 savingsvgpb.SavingsClient,
	liquiloansClient llvgpb.LiquiloansClient,
	profileClient profilepb.ProfileClient,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beemploymentpb.EmploymentClient,
	idfcClient idfcvgpb.IdfcClient,
	accountsClient accountvgpb.AccountsClient,
	creditReportManagerClientVar2 creditreportpb.CreditReportManagerClient,
	creditReportManagerClient creditreport.CreditReportManagerClient,
	onboardingClient onbpb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	balanceClient accountbalancepb.BalanceClient,
	collectionClient collection.CollectionClient,
	pANClient panvgpb.PANClient,
	fiftyFinClient ffvgpb.FiftyFinClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	creditLimitEstimatorClient limitestimatorpb.CreditLimitEstimatorClient,
	abflClient abflvgpb.AbflClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	moneyviewClient mvvgpb.MoneyviewClient,
	locationClient userlocationpb.LocationClient,
	catalogManagerClient catalogmanagerpb.CatalogManagerClient,
	securedLoansClient securedloanspb.SecuredLoansClient,
	nudgeServiceClient nudge.NudgeServiceClient,
	finfluxClient finflux.FinfluxClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	consentClient consentpb.ConsentClient,
	esignClient sgesignapigatewaypb.EsignClient,
	lmsClient sglmspb.LmsClient,
	lendenClient ldcvgpb.LendenClient,
	orchestratorClient orchpb.OrchestratorClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	locationClientVar2 locationpb.LocationClient,
	kYCClient sgkycapigatewaypb.KYCClient,
	epifiCRDB types.EpifiCRDB,
	docsClient docspb.DocsClient,
	locationClientVar14 location.LocationClient,
	breClientVar2 brepb2.BreClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	preapprovedloanConf, err := preapprovedloanconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PRE_APPROVED_LOAN_SERVICE))
		return err
	}
	_ = preapprovedloanConf

	preapprovedloanGenConf, err := dynconf.LoadConfigWithQuestConfig(preapprovedloanconf.Load, genconf2.NewConfigWithQuest, cfg.PRE_APPROVED_LOAN_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PRE_APPROVED_LOAN_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		preapprovedloanGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: preapprovedloanGenConf, SdkConfig: preapprovedloanGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{preapprovedloanGenConfAppConfig}, string(cfg.LENDING_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = preapprovedloanGenConf

	signalWorkflowPublisher, err := sqs.NewPublisherWithConfig(ctx, preapprovedloanGenConf.SignalWorkflowPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	nudgeExitEventPublisher, err := sqs.NewPublisherWithConfig(ctx, preapprovedloanGenConf.NudgeExitEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	preApprovedLoanS3Client := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())
	preApprovedLoanS3ClientVar2 := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())
	preApprovedLoanS3ClientVar3 := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())
	preApprovedLoanS3ClientVar4 := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())
	preApprovedLoanS3ClientVar5 := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())
	preApprovedLoanS3ClientVar6 := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())
	preApprovedLoanS3ClientVar7 := s3pkg.NewClient(awsConf, preapprovedloanGenConf.PreApprovedLoanBucketName())

	preApprovedLoanClientVar4, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.PreApprovedLoan, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	preApprovedLoanClientVar13, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.PreApprovedLoan, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	preApprovedLoanDevEntity := wire3.InitializePreApprovedLoanDevEntityService(loansFederalPGDB, crdbResourceMap, fireflyRedisStore, broker, savingsClient, mFExternalOrdersClient)

	paldevpb.RegisterDevPreApprovedLoanServer(s, preApprovedLoanDevEntity)

	serviceVar3, err := wire3.InitialisePreApprovedLoanSvc(loansFederalPGDB, crdbResourceMap, preApprovedLoanClientVar2, preApprovedLoanClient, usersClient, actorClient, savingsClient, celestialClient, broker, livenessClient, vKYCClient, kycClient, signalWorkflowPublisher, authClient, eSignClient, customerClient, preapprovedloanConf, commsClient, orderServiceClient, payClient, piClient, accountPIRelationClient, paymentClient, savingsClientVar3, liquiloansClient, crdbTxnResourceMap, nudgeExitEventPublisher, profileClient, bankCustomerServiceClient, salaryProgramClient, employmentClient, idfcClient, segmentationServiceClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, onboardingClient, obfuscatorClient, balanceClient, collectionClient, pANClient, fiftyFinClient, preapprovedloanGenConf, awsConf, cardProvisioningClient, creditLimitEstimatorClient, preApprovedLoanS3Client, abflClient, connectedAccountClient, moneyviewClient, mFExternalOrdersClient, locationClient, catalogManagerClient, securedLoansClient, groupClient, nudgeServiceClient, finfluxClient, userIntelServiceClient, preApprovedLoanClientVar4, fireflyRedisStore, inAppTargetedCommsClient, consentClient, esignClient, lmsClient, lendenClient, orchestratorClient, recurringPaymentServiceClient, locationClientVar2, kYCClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	palpb.RegisterPreApprovedLoanServer(s, serviceVar3)

	serviceVar4 := wire3.InitializePreApprovedLoanCxService(loansFederalPGDB, crdbResourceMap, paymentClient, preApprovedLoanClientVar2, preApprovedLoanClient, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vKYCClient, kycClient, authClient, customerClient, eSignClient, commsClient, preapprovedloanConf, orderServiceClient, payClient, piClient, accountPIRelationClient, savingsClientVar3, liquiloansClient, profileClient, bankCustomerServiceClient, salaryProgramClient, employmentClient, idfcClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, preapprovedloanGenConf, onboardingClient, obfuscatorClient, balanceClient, pANClient, fiftyFinClient, epifiCRDB, segmentationServiceClient, awsConf, creditLimitEstimatorClient, preApprovedLoanS3ClientVar2, abflClient, mFExternalOrdersClient, locationClient, finfluxClient, fireflyRedisStore, broker, esignClient, lmsClient, lendenClient, moneyviewClient, locationClientVar2, groupClient)

	palcxpb.RegisterCxServer(s, serviceVar4)

	serviceVar5, err := wire3.InitializeConsumerService(loansFederalPGDB, crdbResourceMap, actorClient, preApprovedLoanClientVar2, awsConf, usersClient, savingsClient, bankCustomerServiceClient, celestialClient, livenessClient, vKYCClient, kycClient, authClient, eSignClient, customerClient, preapprovedloanConf, commsClient, orderServiceClient, payClient, piClient, accountPIRelationClient, broker, paymentClient, savingsClientVar3, liquiloansClient, profileClient, salaryProgramClient, employmentClient, idfcClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, preapprovedloanGenConf, onboardingClient, obfuscatorClient, balanceClient, pANClient, fiftyFinClient, abflClient, segmentationServiceClient, creditLimitEstimatorClient, preApprovedLoanS3ClientVar3, mFExternalOrdersClient, locationClient, fireflyRedisStore, esignClient, locationClientVar2)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, preapprovedloanGenConf.OrderUpdatePreApprovedLoanSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer2.RegisterProcessOrderUpdatePacketMethodToSubscriber(subscriber, serviceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, preapprovedloanGenConf.ProcessLoansFiftyfinCallbackSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer2.RegisterProcessLoansFiftyfinCallbackEventMethodToSubscriber(subscriber, serviceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar6 := wire3.InitializeInboundNotificationService(loansFederalPGDB, crdbResourceMap, preApprovedLoanClientVar2, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vKYCClient, kycClient, authClient, eSignClient, customerClient, commsClient, preapprovedloanConf, piClient, accountPIRelationClient, orderServiceClient, payClient, paymentClient, savingsClientVar3, liquiloansClient, profileClient, bankCustomerServiceClient, salaryProgramClient, employmentClient, idfcClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, preapprovedloanGenConf, onboardingClient, obfuscatorClient, balanceClient, pANClient, fiftyFinClient, segmentationServiceClient, awsConf, creditLimitEstimatorClient, preApprovedLoanS3ClientVar4, abflClient, mFExternalOrdersClient, locationClient, fireflyRedisStore, broker, esignClient, locationClientVar2)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, preapprovedloanGenConf.ProcessLoanInboundTransactionSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inboundnotification.RegisterProcessLoanInboundTransactionMethodToSubscriber(subscriber, serviceVar6)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar7 := wire3.InitializeSherlockBannersService(preapprovedloanConf, preapprovedloanGenConf, loansFederalPGDB, preApprovedLoanClient, crdbTxnResourceMap, crdbResourceMap, broker, preApprovedLoanClientVar2, liquiloansClient, idfcClient, fiftyFinClient, abflClient, nudgeServiceClient, finfluxClient, fireflyRedisStore, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vKYCClient, kycClient, authClient, customerClient, eSignClient, commsClient, orderServiceClient, payClient, piClient, accountPIRelationClient, paymentClient, savingsClientVar3, profileClient, bankCustomerServiceClient, salaryProgramClient, employmentClient, docsClient, awsConf, orchestratorClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, onboardingClient, obfuscatorClient, consentClient, balanceClient, pANClient, connectedAccountClient, mFExternalOrdersClient, locationClientVar2, locationClient, segmentationServiceClient, creditLimitEstimatorClient, preApprovedLoanS3ClientVar5, esignClient, lmsClient, lendenClient, moneyviewClient)

	sherlockbannerspb.RegisterPreApprovedLoanSherlockBannersServer(s, serviceVar7)

	serviceVar8 := wire3.InitialiseSecuredLoansService(crdbResourceMap, celestialClient, crdbTxnResourceMap, cardProvisioningClient, preapprovedloanConf, preapprovedloanGenConf, connectedAccountClient, catalogManagerClient, fireflyRedisStore, preApprovedLoanClientVar2, actorClient, usersClient, savingsClient, livenessClient, kycClient, customerClient, authClient, eSignClient, commsClient, orderServiceClient, payClient, piClient, accountPIRelationClient, paymentClient, savingsClientVar3, liquiloansClient, profileClient, bankCustomerServiceClient, salaryProgramClient, idfcClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, onboardingClient, obfuscatorClient, balanceClient, pANClient, fiftyFinClient, segmentationServiceClient, preApprovedLoanS3ClientVar6, creditLimitEstimatorClient, mFExternalOrdersClient, locationClient, broker, esignClient, locationClientVar2)

	securedloanspb.RegisterSecuredLoansServer(s, serviceVar8)

	serviceVar9 := wire3.InitiateLendabilityService(preapprovedloanGenConf, creditReportManagerClient, usersClient, employmentClient, locationClient, locationClientVar14, broker, gconf.VendorApiConf())

	lendabilitypb.RegisterLendabilityServer(s, serviceVar9)

	serviceVar10 := wire3.InitiatePreEligibilityService(preapprovedloanGenConf, preapprovedloanConf, preApprovedLoanClientVar2, crdbResourceMap, fireflyRedisStore, actorClient, usersClient, savingsClient, celestialClient, livenessClient, kycClient, customerClient, authClient, eSignClient, commsClient, orderServiceClient, payClient, piClient, accountPIRelationClient, paymentClient, savingsClientVar3, liquiloansClient, profileClient, accountsClient, creditReportManagerClientVar2, creditReportManagerClient, onboardingClient, obfuscatorClient, balanceClient, pANClient, fiftyFinClient, preApprovedLoanS3ClientVar7, creditLimitEstimatorClient, mFExternalOrdersClient, locationClient, broker, esignClient, bankCustomerServiceClient, salaryProgramClient, idfcClient, segmentationServiceClient, preApprovedLoanClientVar13, consentClient, locationClientVar2, breClientVar2, operationalStatusServiceClient, preApprovedLoanClient, moneyviewClient)

	preeligibilitypb.RegisterPreEligibilityServer(s, serviceVar10)

	configNameToConfMap[cfg.ConfigName(cfg.PRE_APPROVED_LOAN_SERVICE)] = &commonexplorer.Config{StaticConf: &preapprovedloanconf.Config{}, QuestIntegratedConfig: preapprovedloanGenConf}

	return nil

}

// nolint: funlen
func setupCreditreportv2(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	featureEngineeringPGDB types.FeatureEngineeringPGDB,
	celestialClient celestialpb.CelestialClient,
	consentClient consentpb.ConsentClient,
	onboardingClient onbpb.OnboardingClient,
	usersClient user.UsersClient,
	creditReportClient creditreportvgpb.CreditReportClient,
	fennelFeatureStoreClient fennelvgpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	analyticsDBResourceProvider storage2.AnalyticsDBResourceProvider,
	vendorMappingServiceClient vendormapping.VendorMappingServiceClient,
	creditReportManagerClient creditreport.CreditReportManagerClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	creditreportv2Conf, err := creditreportv2conf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CREDIT_REPORT_SERVICE))
		return err
	}
	_ = creditreportv2Conf

	creditreportv2GenConf, err := dynconf.LoadConfig(creditreportv2conf.Load, genconf3.NewConfig, cfg.CREDIT_REPORT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CREDIT_REPORT_SERVICE))
		return err
	}

	_ = creditreportv2GenConf

	creditReportVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, creditreportv2GenConf.CreditReportVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	creditReportPresencePublisher, err := sqs.NewPublisherWithConfig(ctx, creditreportv2GenConf.CreditReportPresencePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	creditReportDerivedAttributesPublisher, err := sqs.NewPublisherWithConfig(ctx, creditreportv2GenConf.CreditReportDerivedAttributesPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	creditReportDownloadEventsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, creditreportv2GenConf.CreditReportDownloadEventsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	creditReportsS3Client := s3pkg.NewClient(awsConf, creditreportv2GenConf.AWS().S3.CreditReportsBucketName)

	serviceVar11 := wire4.InitializeCreditReportService(epifiCRDB, featureEngineeringPGDB, creditreportv2GenConf, creditReportVerificationPublisher, creditReportPresencePublisher, celestialClient, consentClient, onboardingClient, usersClient, creditReportClient, fennelFeatureStoreClient, scienapticClient, analyticsDBResourceProvider, vendorMappingServiceClient)

	creditreport.RegisterCreditReportManagerServer(s, serviceVar11)

	derivedAttributesService := wire5.InitializeCreditReportDerivedAttributesService(featureEngineeringPGDB, epifiCRDB)

	crdapb.RegisterDerivedAttributesManagerServer(s, derivedAttributesService)

	derivedAttributesConsumerService := wire5.InitializeCreditReportDerivedAttributesConsumerService(featureEngineeringPGDB, epifiCRDB, creditReportManagerClient)

	crdaconsumerpb.RegisterConsumerServer(s, derivedAttributesConsumerService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, creditreportv2GenConf.CreditReportDerivedAttributesSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		crdaconsumerpb.RegisterProcessGenerateDerivedAttributesMessageMethodToSubscriber(subscriber, derivedAttributesConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	creditReportConsumerService := wire4.InitializeCreditReportConsumerService(epifiCRDB, creditreportv2Conf, creditReportClient, broker, creditReportsS3Client, usersClient, creditReportDerivedAttributesPublisher, creditReportDownloadEventsPublisher, featureEngineeringPGDB)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, creditreportv2GenConf.CreditReportPresenceSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		crpb.RegisterProcessReportPresenceCheckMethodToSubscriber(subscriber, creditReportConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, creditreportv2GenConf.CreditReportVerificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		crpb.RegisterProcessReportVerificationMethodToSubscriber(subscriber, creditReportConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, creditreportv2GenConf.CreditReportFlatteningSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		crpb.RegisterProcessCibilReportFlatteningMethodToSubscriber(subscriber, creditReportConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	creditReportEntity := wire4.InitializeCreditReportDevEntityService(epifiCRDB, featureEngineeringPGDB)

	developer3.RegisterDevCreditReportServer(s, creditReportEntity)

	configNameToConfMap[cfg.ConfigName(cfg.CREDIT_REPORT_SERVICE)] = &commonexplorer.Config{StaticConf: &creditreportv2conf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupCollection(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	celestialClient celestialpb.CelestialClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	collectionConf, err := collectionconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.COLLECTION_SERVICE))
		return err
	}
	_ = collectionConf

	serviceVar12 := wire6.InitializeCollectionService(celestialClient, crdbResourceMap)

	collection.RegisterCollectionServer(s, serviceVar12)

	serviceVar13 := wire6.InitializeDeveloperService(crdbResourceMap)

	collectiondeveloper.RegisterDeveloperServer(s, serviceVar13)

	configNameToConfMap[cfg.ConfigName(cfg.COLLECTION_SERVICE)] = &commonexplorer.Config{StaticConf: &collectionconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupEpfo(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	creditRiskPGDB types.CreditRiskPGDB,
	pANClient panvgpb.PANClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	digitapServiceClient digitapvgpb.DigitapServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	epfoConf, err := epfoconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.EPFO_SERVICE))
		return err
	}
	_ = epfoConf

	epfoGenConf, err := dynconf.LoadConfig(epfoconf.Load, genconf4.NewConfig, cfg.EPFO_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.EPFO_SERVICE))
		return err
	}

	_ = epfoGenConf

	serviceVar14 := wire7.InitialiseEpfoService(epfoConf, creditRiskPGDB, pANClient, usersClient, actorClient, digitapServiceClient, epfoGenConf)

	epfopb.RegisterEpfoServer(s, serviceVar14)

	configNameToConfMap[cfg.ConfigName(cfg.EPFO_SERVICE)] = &commonexplorer.Config{StaticConf: &epfoconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupLeads(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	usersClient user.UsersClient,
	preApprovedLoanClient palpb.PreApprovedLoanClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	leadsConf, err := leadsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.LEADS_SERVICE))
		return err
	}
	_ = leadsConf

	serviceVar15 := wire8.InitializeUserLeadService(usersClient, preApprovedLoanClient, crdbResourceMap, broker)

	leadspb.RegisterUserLeadSvcServer(s, serviceVar15)

	leadsDbStateService := wire8.InitializrDevLeadsService(crdbResourceMap)

	leadsdevpb.RegisterDevLeadServer(s, leadsDbStateService)

	configNameToConfMap[cfg.ConfigName(cfg.LEADS_SERVICE)] = &commonexplorer.Config{StaticConf: &leadsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.LENDING_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
