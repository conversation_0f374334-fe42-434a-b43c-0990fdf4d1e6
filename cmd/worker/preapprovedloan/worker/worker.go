package worker

import (
	"github.com/aws/aws-sdk-go-v2/aws"

	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	awsconfigpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cmd/config"
	pkgcmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	rudderLogger "github.com/epifi/be-common/pkg/logger/rudder_logger"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	operationalstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	omegle "github.com/epifi/gamma/api/omegle"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/pkg/epifitemporal/interceptor/quest"

	// nolint: depguard
	awsSqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/rudderlabs/analytics-go"
	"go.temporal.io/sdk/worker"
	"go.uber.org/zap"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/aws/v2/sns"
	"github.com/epifi/be-common/pkg/cfg"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/epifigrpc"
	epifigrpcresolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	epifiTemporalClient "github.com/epifi/be-common/pkg/epifitemporal/client"
	epifiTemporalWorker "github.com/epifi/be-common/pkg/epifitemporal/worker"

	celestialPb "github.com/epifi/be-common/api/celestial"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	authLocationPb "github.com/epifi/gamma/api/auth/location"
	orchPb "github.com/epifi/gamma/api/auth/orchestrator"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/compliance"
	brePb "github.com/epifi/gamma/api/bre"
	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/collection"
	commPb "github.com/epifi/gamma/api/comms"
	connectedaccountPb "github.com/epifi/gamma/api/connected_account"
	beConsentPb "github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	docsPb "github.com/epifi/gamma/api/docs"
	eSignPb "github.com/epifi/gamma/api/docs/esign"
	employmentPb "github.com/epifi/gamma/api/employment"
	epfoPb "github.com/epifi/gamma/api/epfo"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfexternalorderpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	securedLoansPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	"github.com/epifi/gamma/api/product"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/risk"
	riskPb "github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/profile"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	sgApplicantPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/applicant"
	sgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sgCustomerPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/customer"
	sgEsignPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	sgKycPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sglmsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	sgMatrixrPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	userPb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	userObfuscator "github.com/epifi/gamma/api/user/obfuscator"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	incomeEstimatorPb "github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	vgCredgenicsPb "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	abflVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	idfcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	mvVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	fiftyFinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/api/vendorgateway/lending/setu"
	vgAccountPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	profileValidationPb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	celestialWire "github.com/epifi/gamma/celestial/wire"
	collectionWorkflow "github.com/epifi/gamma/collection/workflow"
	"github.com/epifi/gamma/pkg/persistentqueue"
	palWorkerConf "github.com/epifi/gamma/preapprovedloan/config/worker"
	palWorkerGConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	securedLoansWf "github.com/epifi/gamma/preapprovedloan/secured_loans/workflow"
	"github.com/epifi/gamma/preapprovedloan/wire"
	preApprovedWorkflow "github.com/epifi/gamma/preapprovedloan/workflow"
	collectionsWorkflow "github.com/epifi/gamma/preapprovedloan/workflow/collection_providers"
	plWorkflowV2 "github.com/epifi/gamma/preapprovedloan/workflow/v2"
	questSDKInit "github.com/epifi/gamma/quest/sdk/init"
)

const (
	httpPrefix = "http://"
)

//nolint:funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	epifiserver.HandlePanic()
	epifigrpcresolver.RegisterBuilder(epifigrpcresolver.ConsulSchemeName)

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	conf, err := palWorkerConf.LoadConfig()
	if err != nil {
		panic(fmt.Errorf("failed to load config: %w", err))
	}

	logger.InitV2(env, conf.Application.Logging)

	c, err := epifiTemporalClient.NewWorkflowClient(conf.Application.GetNamespace(), true, conf.Secrets.Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Panic("unable to create client", zap.Error(err))
	}

	defer c.Close()

	palWorkerGenConf, err := palWorkerGConf.Load()
	if err != nil {
		panic(err)
	}

	if err = epifiTemporalWorker.InitWorkerPrerequisites(conf.WorkflowParamsList, conf.DefaultActivityParamsList); err != nil {
		logger.Panic("failed to init worker prerequisites", zap.Error(err))
	}

	wo := conf.Application.GetWorkerOptions()
	w, err := epifiTemporalWorker.GetWorker(
		c,
		conf.Application.TaskQueue,
		conf.Application.GetNamespace(),
		&wo,
		conf.Application.RedisConfig,
		conf.PausedWorkflowList,
		quest.NewQuestContextInterceptor(),
	)

	if err != nil {
		logger.Panic("failed to get worker", zap.Error(err))
	}

	var (
		dbConnProvider, pgDbConnProvider             *storageV2.DBResourceProvider[*gormV2.DB]
		txnExecutorProvider, pgDbTxnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
		dbConnTeardown, pgDbConnTeardown             func()
	)

	if palWorkerGenConf.PgdbMigrationFlag() {
		pgDbConnProvider, pgDbTxnExecutorProvider, pgDbConnTeardown, err = storageV2.NewDBResourceProvider(conf.PgDbConfigMap.GetOwnershipToDbConfigMap(), false)
		if err != nil {
			logger.Panic("failed to get db resource provider", zap.Error(err))
		}

		// After migration, all the services that earlier used CRDB using DbConfigMap config, will use the PGDB with PgDbConfigMap
		dbConnProvider, txnExecutorProvider, dbConnTeardown = pgDbConnProvider, pgDbTxnExecutorProvider, pgDbConnTeardown
	} else {
		pgDbConnProvider, pgDbTxnExecutorProvider, pgDbConnTeardown, err = storageV2.NewDBResourceProvider(conf.PgDbConfigMap.GetOwnershipToDbConfigMap(), false)
		if err != nil {
			logger.Panic("failed to get db resource provider", zap.Error(err))
		}

		dbConnProvider, txnExecutorProvider, dbConnTeardown, err = storageV2.NewDBResourceProvider(conf.DbConfigMap.GetOwnershipToDbConfigMap(), false)
		if err != nil {
			logger.Panic("failed to get db resource provider", zap.Error(err))
		}
	}

	// These are Celestial common activities db conn. These will be migrated along with other Celestial used tables
	commonDbConnProvider, commonTxnExecutorProvider, commonDbConnTeardown, err := usecase.NewDBResourceProvider(conf.CommonDbConfigMap, false, nil)
	if err != nil {
		logger.Panic("failed to get common db resource provider", zap.Error(err))
	}

	defer func() {
		dbConnTeardown()
		pgDbConnTeardown()
		commonDbConnTeardown()
	}()

	epifiDb, err := commonDbConnProvider.GetResource(commontypes.Ownership_EPIFI_TECH, commontypes.UseCase_USE_CASE_UNSPECIFIED)
	if err != nil {
		logger.Panic("failed to get epifi db conn", zap.Error(err))
	}
	storageV2.InitDefaultCRDBTransactionExecutor(epifiDb)

	db, err := dbConnProvider.GetResourceForOwnership(commontypes.Ownership_FEDERAL_BANK)
	if err != nil {
		logger.Panic("failed to get federal conn", zap.Error(err))
	}

	federalTxnExecutor := storageV2.NewGormTxnExecutor(db)

	llTxnExecutor, err := txnExecutorProvider.GetResourceForOwnership(commontypes.Ownership_LIQUILOANS_PL)
	if err != nil {
		logger.Panic("failed to get txn executor for LL")
	}

	abflTxnExecutor, err := txnExecutorProvider.GetResourceForOwnership(commontypes.Ownership_LOANS_ABFL)
	if err != nil {
		logger.Panic("failed to get txn executor for ABFL")
	}

	sgTxnExecutor, err := txnExecutorProvider.GetResourceForOwnership(commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP)
	if err != nil {
		logger.Error(ctx, "failed to get txn executor for Stock Guardian")
		logger.Panic("failed to get txn executor for Stock Guardian")
	}

	idfcTxnExecutor, err := txnExecutorProvider.GetResourceForOwnership(commontypes.Ownership_IDFC_PL)
	if err != nil {
		logger.Panic("failed to get txn executor for IDFC")
	}

	awsConf, err := awsconfigpkg.NewAWSConfig(ctx, conf.AWS.Region, true)
	if err != nil {
		logger.Panic("failed to initialise aws config", zap.Error(err))
	}

	pQueue := persistentqueue.NewPersistentQueue(db)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	usersClient := userPb.NewUsersClient(userConn)
	creditReportClient := creditReportPb.NewCreditReportManagerClient(userConn)
	userGroupClient := userGroupPb.NewGroupClient(userConn)
	userLocationClient := userLocationPb.NewLocationClient(userConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	palVgClient := palVgPb.NewPreApprovedLoanClient(vgConn)
	palLLVgClient := llVgPb.NewLiquiloansClient(vgConn)
	palIdfcVgClient := idfcVgPb.NewIdfcClient(vgConn)
	palAbflVgClient := abflVgPb.NewAbflClient(vgConn)
	vgCustomerClient := vgCustomerPb.NewCustomerClient(vgConn)
	profileValidationVgClient := profileValidationPb.NewProfileValidationClient(vgConn)
	vgSavingsClient := vgSavingsPb.NewSavingsClient(vgConn)
	vgAccountClient := vgAccountPb.NewAccountsClient(vgConn)
	vgCredgenicsClient := vgCredgenicsPb.NewCredgenicsClient(vgConn)
	panVgClient := panVgPb.NewPANClient(vgConn)
	fiftyFinVgClient := fiftyFinPb.NewFiftyFinClient(vgConn)
	abflVgClient := abflVgPb.NewAbflClient(vgConn)
	ldcClient := lenden.NewLendenClient(vgConn)
	ieClient := incomeEstimatorPb.NewIncomeEstimatorClient(vgConn)
	mvVgClient := mvVgPb.NewMoneyviewClient(vgConn)
	setuClient := setu.NewSetuClient(vgConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)
	operationalStatusServiceClient := operationalstatuspb.NewOperationalStatusServiceClient(savingsConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	defer epifigrpc.CloseConn(celestialConn)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	palConn := epifigrpc.NewConnByService(cfg.PRE_APPROVED_LOAN_SERVICE)
	defer epifigrpc.CloseConn(palConn)
	palClient := palPb.NewPreApprovedLoanClient(palConn)
	creditReportV2Client := creditReportV2Pb.NewCreditReportManagerClient(palConn)

	securedLoanConn := epifigrpc.NewConnByService(cfg.PRE_APPROVED_LOAN_SERVICE)
	defer epifigrpc.CloseConn(palConn)
	secureLoanClient := securedLoansPb.NewSecuredLoansClient(securedLoanConn)

	epfoConn := epifigrpc.NewConnByService(cfg.EPFO_SERVICE)
	defer epifigrpc.CloseConn(epfoConn)
	epfoClient := epfoPb.NewEpfoClient(epfoConn)

	breConn := epifigrpc.NewConnByService(cfg.BRE_SERVICE)
	defer epifigrpc.CloseConn(breConn)
	breClient := brePb.NewBreClient(breConn)

	bankCustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bankCustConn)
	bankCustClient := bankCustPb.NewBankCustomerServiceClient(bankCustConn)
	compClient := compliance.NewComplianceClient(bankCustConn)

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	livenessConn := epifigrpc.NewConnByService(cfg.LIVENESS_SERVICE)
	defer epifigrpc.CloseConn(livenessConn)
	livenessClient := livenessPb.NewLivenessClient(livenessConn)
	authClient := authPb.NewAuthClient(authConn)
	orchClient := orchPb.NewOrchestratorClient(authConn)
	authLocationClient := authLocationPb.NewLocationClient(authConn)
	consentClient := beConsentPb.NewConsentClient(authConn)

	vkycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	defer epifigrpc.CloseConn(vkycConn)
	vkycClient := vkycPb.NewVKYCClient(vkycConn)
	kycClient := kycPb.NewKycClient(vkycConn)

	eSignConn := epifigrpc.NewConnByService(cfg.DOCS_SERVICE)
	defer epifigrpc.CloseConn(eSignConn)
	eSignClient := eSignPb.NewESignClient(eSignConn)

	orderConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	defer epifigrpc.CloseConn(orderConn)
	orderClient := orderPb.NewOrderServiceClient(orderConn)

	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	defer epifigrpc.CloseConn(payConn)
	payClient := payPb.NewPayClient(payConn)

	piConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	defer epifigrpc.CloseConn(piConn)
	piClient := piPb.NewPiClient(piConn)

	accountPiConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	defer epifigrpc.CloseConn(accountPiConn)
	accountPiClient := accountPiPb.NewAccountPIRelationClient(accountPiConn)

	paymentConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	defer epifigrpc.CloseConn(paymentConn)
	paymentClient := paymentPb.NewPaymentClient(paymentConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commPb.NewCommsClient(commsConn)

	cleConn := epifigrpc.NewConnByService(cfg.LIMIT_ESTIMATOR_SERVICE)
	defer epifigrpc.CloseConn(cleConn)
	limitEstimatorClient := limitEstimatorPb.NewCreditLimitEstimatorClient(cleConn)

	salaryProgramConn := epifigrpc.NewConnByService(cfg.SALARY_PROGRAM_SERVICE)
	defer epifigrpc.CloseConn(salaryProgramConn)
	salaryProgramSvcClient := salaryprogramPb.NewSalaryProgramClient(salaryProgramConn)

	riskConn := epifigrpc.NewConnByService(cfg.RISK_SERVICE)
	defer epifigrpc.CloseConn(riskConn)
	profileClient := profile.NewProfileClient(riskConn)
	riskClient := risk.NewRiskClient(riskConn)

	employmentConn := epifigrpc.NewConnByService(cfg.EMPLOYMENT_SERVICE)
	defer epifigrpc.CloseConn(employmentConn)
	employmentClient := employmentPb.NewEmploymentClient(employmentConn)

	onboardingConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(onboardingConn)
	onboardingClient := beOnbPb.NewOnboardingClient(onboardingConn)

	recurringPaymentConn := epifigrpc.NewConnByService(cfg.RECURRING_PAYMENT_SERVICE)
	recurringPaymentClient := recurringPaymentPb.NewRecurringPaymentServiceClient(recurringPaymentConn)
	defer epifigrpc.CloseConn(recurringPaymentConn)
	enachClient := enachPb.NewEnachServiceClient(recurringPaymentConn)
	defer epifigrpc.CloseConn(recurringPaymentConn)
	docsConn := epifigrpc.NewConnByService(cfg.DOCS_SERVICE)
	docsClient := docsPb.NewDocsClient(docsConn)
	defer epifigrpc.CloseConn(docsConn)

	obfuscatorClient := userObfuscator.NewObfuscatorClient(userConn)

	accountConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accountConn)
	accountBalanceClient := accountBalancePb.NewBalanceClient(accountConn)

	collConn := epifigrpc.NewConnByService(cfg.COLLECTION_SERVICE)
	defer epifigrpc.CloseConn(collConn)
	collClient := collection.NewCollectionClient(collConn)

	productConn := epifigrpc.NewConnByService(cfg.PRODUCT_SERVICE)
	defer epifigrpc.CloseConn(productConn)
	productClient := product.NewProductClient(productConn)

	segmentationSvcConn := epifigrpc.NewConnByService(cfg.SEGMENT_SERVICE)
	defer epifigrpc.CloseConn(segmentationSvcConn)
	segmentationClient := segmentPb.NewSegmentationServiceClient(segmentationSvcConn)

	questConn := epifigrpc.NewConnByService(cfg.QUEST_SERVICE)
	defer epifigrpc.CloseConn(questConn)
	questManagerClient := questManagerPb.NewManagerClient(questConn)

	cardSvcConn := epifigrpc.NewConnByService(cfg.CARD_SERVICE)
	defer epifigrpc.CloseConn(cardSvcConn)
	cardProvClient := cardProvisioningPb.NewCardProvisioningClient(cardSvcConn)

	cxConn := epifigrpc.NewConnByService(cfg.CX_SERVICE)
	defer epifigrpc.CloseConn(cxConn)
	cxTicketClient := cxTicketPb.NewTicketClient(cxConn)

	caConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	defer epifigrpc.CloseConn(caConn)
	caClient := connectedaccountPb.NewConnectedAccountClient(caConn)

	palS3Client := s3.NewClient(awsConf, conf.PreApprovedLoanBucketName)

	dataDevS3Client := s3.NewClient(awsConf, conf.RawDataDevBucketName)

	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	mfExternalCleint := mfexternalorderpb.NewMFExternalOrdersClient(investmentConn)
	mfCatalogClient := catalogManagerPb.NewCatalogManagerClient(investmentConn)

	nudgeConn := epifigrpc.NewConnByService(cfg.NUDGE_SERVICE)
	nudgeClient := nudge.NewNudgeServiceClient(nudgeConn)
	defer epifigrpc.CloseConn(nudgeConn)

	userIntelConn := epifigrpc.NewConnByService(cfg.USER_INTEL_SERVICE)
	userIntelClient := userIntelPb.NewUserIntelServiceClient(userIntelConn)
	defer epifigrpc.CloseConn(userIntelConn)

	goblinConn := epifigrpc.NewServerConn(cfg.PRE_APPROVED_LOAN_WORKER_SERVER, cfg.SG_API_GATEWAY_SERVER)
	sgEsignClient := sgEsignPb.NewEsignClient(goblinConn)
	sgApplicationClient := sgApplicationPb.NewApplicationClient(goblinConn)
	sgMatrixClient := sgMatrixrPb.NewMatrixClient(goblinConn)
	sgApplicantClient := sgApplicantPb.NewApplicantServiceClient(goblinConn)
	sgCustomerClient := sgCustomerPb.NewCustomerServiceClient(goblinConn)
	sgKycClient := sgKycPb.NewKYCClient(goblinConn)
	sgLmsClient := sglmsPb.NewLmsClient(goblinConn)
	OmegleClient := omegle.NewOmegleClient(goblinConn)
	defer epifigrpc.CloseConn(goblinConn)
	salaryEstimationConn := epifigrpc.NewConnByService(cfg.SALARY_ESTIMATION_SERVICE)
	defer epifigrpc.CloseConn(salaryEstimationConn)
	salaryEstimationClient := salaryestimation.NewSalaryEstimationClient(salaryEstimationConn)

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		conf.Secrets.Ids[palWorkerConf.RudderWriteKey], httpPrefix+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
			Logger:    rudderLogger.NewRudderLogger(),
		})
	if err != nil {
		panic(err)
	}
	broker := events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	// Register activities
	closure, err := registerCommonActivities(ctx, w, conf, awsConf, commonTxnExecutorProvider, commonDbConnProvider)
	if err != nil {
		logger.Panic("failed to register common activities", zap.Error(err))
	}

	// avoid initiating quest sdk if it is disabled in given env
	if !palWorkerGenConf.QuestSdk().Disable() {
		authSdkAppConfig := questSDKInit.QuestSdkAppConfig{AppConfig: palWorkerGenConf, SdkConfig: palWorkerGenConf.QuestSdk()}
		questSDKInit.SetupQuestSDK([]questSDKInit.QuestSdkAppConfig{authSdkAppConfig}, string(cfg.PRE_APPROVED_LOAN_WORKER_SERVER), questManagerClient, userGroupClient, usersClient, actorClient, segmentationClient, broker)
	}

	redisClient := storage.NewRedisClientFromConfig(conf.RedisStore, false)
	defer func() {
		_ = redisClient.Close()
	}()

	finfluxClient := finflux.NewFinfluxClient(vgConn)

	defer closure()
	err = registerDomainActivities(w, db, dbConnProvider, llTxnExecutor, palVgClient, usersClient, actorClient, savingsClient, celestialClient,
		livenessClient, vkycClient, kycClient, federalTxnExecutor, pQueue, authClient, vgCustomerClient, eSignClient, commsClient,
		profileValidationVgClient, orderClient, payClient, piClient, accountPiClient, paymentClient, vgSavingsClient, conf,
		broker, palLLVgClient, palIdfcVgClient, orchClient, awsConf, limitEstimatorClient, txnExecutorProvider,
		profileClient, bankCustClient, recurringPaymentClient, salaryProgramSvcClient, employmentClient, docsClient, idfcTxnExecutor,
		vgAccountClient, creditReportClient, creditReportV2Client, onboardingClient, palClient, obfuscatorClient, consentClient, accountBalanceClient,
		riskClient, breClient, collClient, panVgClient, fiftyFinVgClient, epfoClient, productClient, secureLoanClient, segmentationClient,
		abflVgClient, cardProvClient, palWorkerGenConf, palS3Client, dataDevS3Client, caClient, ieClient, palAbflVgClient,
		mvVgClient, mfExternalCleint, authLocationClient, userLocationClient, mfCatalogClient, nudgeClient, userGroupClient, finfluxClient, userIntelClient,
		compClient, redisClient, setuClient, operationalStatusServiceClient, abflTxnExecutor, sgTxnExecutor, sgCustomerClient, sgApplicantClient, sgApplicationClient, sgMatrixClient, sgEsignClient, sgKycClient, sgLmsClient, OmegleClient, ldcClient, salaryEstimationClient, enachClient)
	if err != nil {
		logger.Panic("failed to register domain activities", zap.Error(err))
	}

	registerCollectionActivities(
		w,
		pgDbConnProvider,
		pgDbTxnExecutorProvider,
		usersClient,
		conf,
		palClient,
		vgCredgenicsClient,
		broker,
		salaryProgramSvcClient,
		creditReportV2Client,
		profileClient,
		employmentClient,
		authClient,
		cxTicketClient,
	)

	// Register workflows
	w.RegisterWorkflow(preApprovedWorkflow.PreApprovedLoanApplication)
	w.RegisterWorkflow(preApprovedWorkflow.KycCheck)
	w.RegisterWorkflow(preApprovedWorkflow.LivenessCheck)
	w.RegisterWorkflow(preApprovedWorkflow.FaceMatchCheck)
	w.RegisterWorkflow(preApprovedWorkflow.ManualReviewCheck)
	w.RegisterWorkflow(preApprovedWorkflow.PreApprovedLoanPrePay)
	w.RegisterWorkflow(preApprovedWorkflow.PreCloseLoanAccount)
	w.RegisterWorkflow(plWorkflowV2.LoanApplication)
	w.RegisterWorkflow(preApprovedWorkflow.ProcessFiEligibleBase)
	w.RegisterWorkflow(preApprovedWorkflow.ProcessChildFiEligibleBase)
	w.RegisterWorkflow(plWorkflowV2.LoanCollection)
	w.RegisterWorkflow(plWorkflowV2.LoanPrePay)
	w.RegisterWorkflow(plWorkflowV2.LoanOffAppPrepay)
	w.RegisterWorkflow(plWorkflowV2.LoanAutoPay)
	w.RegisterWorkflow(collectionsWorkflow.LoanCollectionPaymentAndReconcile)
	w.RegisterWorkflow(plWorkflowV2.LoanEligibility)
	w.RegisterWorkflow(plWorkflowV2.LoanPreClose)
	w.RegisterWorkflow(securedLoansWf.AssetPortfolioFetch)
	w.RegisterWorkflow(plWorkflowV2.SetupSi)
	w.RegisterWorkflow(preApprovedWorkflow.VendorCallbackProcessor)
	w.RegisterWorkflow(plWorkflowV2.ProcessVendorNotification)
	w.RegisterWorkflow(preApprovedWorkflow.ProcessOffAppRepayment)
	w.RegisterWorkflow(preApprovedWorkflow.PlSyncProxy)
	// register collection workflows
	w.RegisterWorkflow(collectionWorkflow.CreateAllocation)
	w.RegisterWorkflow(collectionWorkflow.SyncAllActiveCollectionDetails)
	w.RegisterWorkflow(collectionWorkflow.SyncLeadDetails)
	w.RegisterWorkflow(collectionWorkflow.UpdatePayment)
	w.RegisterWorkflow(plWorkflowV2.MutualFundNft)
	w.RegisterWorkflow(plWorkflowV2.LoanPreCloseV3)
	w.RegisterWorkflow(plWorkflowV2.LoanDataCollection)
	initNotifier <- cfg.PRE_APPROVED_LOAN_WORKER_SERVER
	epifiTemporalWorker.StartWorker(c, w, conf.Server, conf.Application.TaskQueue)
	return nil
}

func registerCommonActivities(ctx context.Context, w worker.Worker, conf *palWorkerConf.Config, awsConf aws.Config, txnExecutorProvider *usecase.DBResourceProvider[storageV2.IdempotentTxnExecutor], dbConnProviderWithUseCase *usecase.DBResourceProvider[*gormV2.DB]) (func(), error) {
	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	commsClient := commPb.NewCommsClient(commsConn)

	epifiTechTxnExecutor, err := txnExecutorProvider.GetResource(commontypes.Ownership_EPIFI_TECH, commontypes.UseCase_USE_CASE_UNSPECIFIED)
	if err != nil {
		return nil, fmt.Errorf("failed to get txn executor for: %s: %w", commontypes.Ownership_EPIFI_TECH.String(), err)
	}

	// TODO(Sundeep): Remove this once, dynamic config is setup for workers & pass that directly to InitializeActivityProcessor.
	// Using this hacky way to initialise enableConnectToPgdb for now.
	enableConnectToPgdb, _ := gencfg.NewPgdbConns()
	_ = enableConnectToPgdb.Set(conf.Application.PgdbConns, false, nil)

	celestialActProcessor := celestialWire.InitializeActivityProcessor(enableConnectToPgdb, dbConnProviderWithUseCase, epifiTechTxnExecutor, initSNSPublisher(ctx, conf.WorkflowUpdatePublisher, awsConf), commsClient)
	w.RegisterActivity(celestialActProcessor)

	celestialActProcessorv2 := celestialWire.InitializeActivityProcessorV2(enableConnectToPgdb, dbConnProviderWithUseCase, txnExecutorProvider, initSNSPublisher(ctx, conf.WorkflowUpdatePublisher, awsConf), commsClient, pkgcmdtypes.CelestialCodecAesKey(conf.Secrets.Ids[config.TemporalCodecAesKey]))
	w.RegisterActivity(celestialActProcessorv2)

	procrastinatorActProcessor := celestialWire.InitializeProcrastinatorActivityProcessor(awsSqs.NewFromConfig(awsConf), pkgcmdtypes.CelestialCodecAesKey(conf.Secrets.Ids[config.TemporalCodecAesKey]))
	w.RegisterActivity(procrastinatorActProcessor)

	return func() {
		epifigrpc.CloseConn(commsConn)
	}, nil
}

// nolint: funlen
func registerDomainActivities(w worker.Worker, db *gormV2.DB, dbConnProvider *storageV2.DBResourceProvider[*gormV2.DB],
	llTxnExecutor storageV2.TxnExecutor, palVgClient palVgPb.PreApprovedLoanClient, usersClient userPb.UsersClient,
	actorClient actorPb.ActorClient, savingsClient savingsPb.SavingsClient, celestialClient celestialPb.CelestialClient,
	livenessClient livenessPb.LivenessClient, vkycClient vkycPb.VKYCClient, kycClient kycPb.KycClient, txnExecutor storageV2.TxnExecutor,
	pQueue persistentqueue.PersistentQueue, authClient authPb.AuthClient, vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient eSignPb.ESignClient, commClient commPb.CommsClient, profileValidationVgClient profileValidationPb.ProfileValidationClient,
	orderClient orderPb.OrderServiceClient, payClient payPb.PayClient, piClient piPb.PiClient, accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient, vgSavingsClient vgSavingsPb.SavingsClient, config *palWorkerConf.Config, broker events.Broker,
	palLlVgClient llVgPb.LiquiloansClient, palIdfcVgClient idfcVgPb.IdfcClient, authOrchClient orchPb.OrchestratorClient,
	awsConf aws.Config, limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient, txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	profileClient profile.ProfileClient, bankCustClient bankCustPb.BankCustomerServiceClient, recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	salaryProgramSvcClient salaryprogramPb.SalaryProgramClient, employmentClient employmentPb.EmploymentClient, docsClient docsPb.DocsClient,
	idfcTxnExecutor storageV2.TxnExecutor, vgAccountClient vgAccountPb.AccountsClient, creditReportManagerClient creditReportPb.CreditReportManagerClient,
	creditReportManagerV2Client creditReportV2Pb.CreditReportManagerClient, onbClient beOnbPb.OnboardingClient, palClient preApprovedLoanPb.PreApprovedLoanClient,
	obfuscatorClient obfuscator.ObfuscatorClient, consentClient beConsentPb.ConsentClient, accountBalanceClient accountBalancePb.BalanceClient,
	riskClient riskPb.RiskClient, breClient brePb.BreClient, collClient collection.CollectionClient, panVgClient panVgPb.PANClient,
	fiftyFinVgClient fiftyFinPb.FiftyFinClient, epfoClient epfoPb.EpfoClient, productClient product.ProductClient, securedLoansClient securedLoansPb.SecuredLoansClient,
	segmentationClient segmentPb.SegmentationServiceClient, abflClient abflVgPb.AbflClient, cardProvClient cardProvisioningPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config, palS3Client s3.S3Client, dataDevS3Client s3.S3Client, caClient connectedaccountPb.ConnectedAccountClient,
	ieClient incomeEstimatorPb.IncomeEstimatorClient, palAbflVgClient abflVgPb.AbflClient, mvVgClient mvVgPb.MoneyviewClient,
	mfExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient, authLocationClient authLocationPb.LocationClient,
	userLocationClient userLocationPb.LocationClient, mfCatalogClient catalogManagerPb.CatalogManagerClient, nudgeClient nudge.NudgeServiceClient, userGrpClient userGroupPb.GroupClient, finfluxClient finflux.FinfluxClient, userIntelClient userIntelPb.UserIntelServiceClient, compClient compliance.ComplianceClient,
	redisClient *redis.Client, setuClient setu.SetuClient, operationalStatusClient operationalstatuspb.OperationalStatusServiceClient, abflTxnExecutor storageV2.TxnExecutor, sgTxnExecutor storageV2.TxnExecutor,
	sgCustomerClient sgCustomerPb.CustomerServiceClient, sgApplicantClient sgApplicantPb.ApplicantServiceClient, sgApplicationClient sgApplicationPb.ApplicationClient, sgMatrixClient sgMatrixrPb.MatrixClient, sgEsignClient sgEsignPb.EsignClient, sgKycClient sgKycPb.KYCClient,
	sgLmsClient sglmsPb.LmsClient, omegleClient omegle.OmegleClient, ldcClient lenden.LendenClient, salaryEstimationClient salaryestimation.SalaryEstimationClient, enachClient enachPb.EnachServiceClient) error {

	actProcessor, err := wire.InitialiseActivityProcessor(db, dbConnProvider, palVgClient, usersClient, actorClient, savingsClient,
		celestialClient, livenessClient, vkycClient, kycClient, txnExecutor, pQueue, authClient, vgCustomerClient, eSignClient,
		config.Notification, commClient, profileValidationVgClient, orderClient, payClient, piClient, accountPiClient, paymentClient,
		vgSavingsClient, broker, palLlVgClient, limitEstimatorClient, authOrchClient, txnExecutorProvider, config,
		profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, palIdfcVgClient, docsClient, userGrpClient, vgAccountClient,
		creditReportManagerClient, creditReportManagerV2Client, onbClient, obfuscatorClient, accountBalanceClient, riskClient,
		collClient, panVgClient, fiftyFinVgClient, epfoClient, productClient, segmentationClient, cardProvClient, workerConf,
		palS3Client, abflClient, caClient, mfExternalOrdersClient, userLocationClient, nudgeClient, mfCatalogClient, palClient,
		finfluxClient, userIntelClient, recurringPaymentClient, redisClient, breClient, dataDevS3Client, sgEsignClient,
		sgLmsClient, ldcClient, mvVgClient, authLocationClient, salaryEstimationClient)
	w.RegisterActivity(actProcessor)

	if err != nil {
		return err
	}

	fedActProcessor := wire.InitialiseFederalActivityProcessor(db, dbConnProvider, palVgClient, palClient, usersClient, actorClient, savingsClient,
		celestialClient, livenessClient, vkycClient, kycClient, txnExecutor, pQueue, authClient, vgCustomerClient, eSignClient,
		config.Notification, commClient, profileValidationVgClient, orderClient, payClient, piClient, accountPiClient, paymentClient,
		vgSavingsClient, broker, palLlVgClient, limitEstimatorClient, authOrchClient, txnExecutorProvider, awsConf, config,
		profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, palIdfcVgClient, docsClient, vgAccountClient,
		creditReportManagerClient, creditReportManagerV2Client, onbClient, obfuscatorClient, accountBalanceClient, panVgClient,
		fiftyFinVgClient, segmentationClient, cardProvClient, workerConf, palS3Client, abflClient, caClient,
		mfExternalOrdersClient, userLocationClient, nudgeClient, finfluxClient, mfCatalogClient, redisClient, setuClient,
		sgEsignClient, sgLmsClient, ldcClient, mvVgClient, enachClient, recurringPaymentClient, authLocationClient)
	w.RegisterActivity(fedActProcessor)

	llActProcessor := wire.InitialiseLlActivityProcessor(db, dbConnProvider, palLlVgClient, palVgClient, usersClient, actorClient,
		savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient, vgCustomerClient, eSignClient,
		config.Notification, commClient, profileValidationVgClient, orderClient, payClient, piClient, accountPiClient, llTxnExecutor,
		paymentClient, vgSavingsClient, broker, authOrchClient, awsConf, config, profileClient, bankCustClient, recurringPaymentClient,
		salaryProgramSvcClient, employmentClient, txnExecutorProvider, palIdfcVgClient, vgAccountClient, creditReportManagerClient,
		creditReportManagerV2Client, onbClient, palClient, obfuscatorClient, accountBalanceClient, breClient, panVgClient,
		fiftyFinVgClient, segmentationClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, dataDevS3Client,
		abflClient, caClient, ieClient, mfExternalOrdersClient, userLocationClient, mfCatalogClient, nudgeClient, userGrpClient,
		finfluxClient, userIntelClient, compClient, redisClient, riskClient, operationalStatusClient, sgEsignClient,
		nil, ldcClient, mvVgClient, authLocationClient)
	w.RegisterActivity(llActProcessor)

	idfcActProcessor := wire.InitialiseIdfcActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient, palVgClient,
		usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient, vgCustomerClient,
		eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient, paymentClient, vgSavingsClient,
		profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient, awsConf, config, authOrchClient,
		idfcTxnExecutor, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client, onbClient, obfuscatorClient,
		consentClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, cardProvClient, workerConf,
		limitEstimatorClient, palS3Client, palAbflVgClient, caClient, mfExternalOrdersClient, userLocationClient, mfCatalogClient,
		redisClient, userGrpClient, sgEsignClient, authLocationClient)
	w.RegisterActivity(idfcActProcessor)

	commonActProcessor := wire.InitialiseCommonActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, palClient, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient,
		vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient,
		paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient,
		awsConf, config, authOrchClient, idfcTxnExecutor, vgAccountClient, recurringPaymentClient, creditReportManagerClient,
		creditReportManagerV2Client, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient,
		cardProvClient, workerConf, limitEstimatorClient, palS3Client, abflClient, caClient, mfExternalOrdersClient, userLocationClient,
		mfCatalogClient, userIntelClient, txnExecutorProvider, redisClient, sgEsignClient, salaryEstimationClient, authLocationClient,
		userGrpClient, finfluxClient, sgLmsClient, mvVgClient, ldcClient)
	w.RegisterActivity(commonActProcessor)

	fiftyfinActProcessor := wire.InitialiseFiftyfinActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, palClient, usersClient, userGrpClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient,
		vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient,
		paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient,
		awsConf, config, authOrchClient, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client,
		onbClient, obfuscatorClient, consentClient, accountBalanceClient, panVgClient, fiftyFinVgClient, securedLoansClient, txnExecutorProvider,
		segmentationClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, abflClient, caClient,
		mfExternalOrdersClient, userLocationClient, mfCatalogClient, finfluxClient, redisClient, sgEsignClient, sgLmsClient, ldcClient,
		mvVgClient, authLocationClient)
	w.RegisterActivity(fiftyfinActProcessor)

	fiftyfinSecureloansProcessor := wire.InitializeSecuredLoansFiftyfinActivityProvider(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient,
		vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient,
		paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient,
		awsConf, config, authOrchClient, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client,
		onbClient, obfuscatorClient, consentClient, accountBalanceClient, panVgClient, fiftyFinVgClient, securedLoansClient,
		segmentationClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, palAbflVgClient, caClient,
		mfExternalOrdersClient, userLocationClient, txnExecutorProvider, mfCatalogClient, redisClient, sgEsignClient, authLocationClient)
	w.RegisterActivity(fiftyfinSecureloansProcessor)

	abflActProcessor := wire.InitialiseAbflActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient,
		vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient,
		paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient,
		awsConf, config, authOrchClient, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client,
		onbClient, obfuscatorClient, consentClient, accountBalanceClient, panVgClient, txnExecutorProvider, fiftyFinVgClient,
		segmentationClient, abflClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, caClient,
		mfExternalOrdersClient, authLocationClient, userLocationClient, mfCatalogClient, redisClient, abflTxnExecutor,
		finfluxClient, sgEsignClient, nil, ldcClient)
	w.RegisterActivity(abflActProcessor)

	mvActProcessor := wire.InitialiseMvActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient,
		vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient,
		paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient,
		awsConf, config, authOrchClient, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client,
		onbClient, obfuscatorClient, consentClient, accountBalanceClient, panVgClient, txnExecutorProvider, fiftyFinVgClient,
		segmentationClient, abflClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, mvVgClient,
		mfExternalOrdersClient, userLocationClient, redisClient, sgEsignClient, nil, authLocationClient)
	w.RegisterActivity(mvActProcessor)

	lendenActProcessor := wire.InitialiseLendenActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient, kycClient, authClient,
		vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient, accountPiClient,
		paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient, docsClient,
		awsConf, config, authOrchClient, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client,
		onbClient, obfuscatorClient, consentClient, accountBalanceClient, panVgClient, txnExecutorProvider, fiftyFinVgClient,
		segmentationClient, abflClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, mvVgClient,
		mfExternalOrdersClient, userLocationClient, redisClient, sgEsignClient, sgLmsClient, caClient, mfCatalogClient, ldcClient, authLocationClient)
	w.RegisterActivity(lendenActProcessor)

	sgActProcessor := wire.InitialiseStockGuardianActivityProcessor(dbConnProvider, broker, palLlVgClient, palIdfcVgClient,
		palVgClient, txnExecutorProvider, usersClient, actorClient, savingsClient, celestialClient, livenessClient, vkycClient,
		kycClient, authClient, vgCustomerClient, eSignClient, config.Notification, commClient, orderClient, payClient, piClient,
		accountPiClient, paymentClient, vgSavingsClient, profileClient, bankCustClient, salaryProgramSvcClient, employmentClient,
		docsClient, awsConf, config, authOrchClient, vgAccountClient, creditReportManagerClient, creditReportManagerV2Client,
		onbClient, obfuscatorClient, consentClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient,
		abflClient, cardProvClient, workerConf, limitEstimatorClient, palS3Client, caClient, mfExternalOrdersClient,
		userLocationClient, mfCatalogClient, redisClient, sgTxnExecutor, finfluxClient, breClient, dataDevS3Client, sgEsignClient,
		sgApplicantClient, sgApplicationClient, sgMatrixClient, sgCustomerClient, sgKycClient, recurringPaymentClient, sgLmsClient,
		riskClient, omegleClient, palClient, ldcClient, mvVgClient, authLocationClient)
	w.RegisterActivity(sgActProcessor)

	syncProxyProcessor := wire.InitialiseSyncActivityProcessor(sgActProcessor, lendenActProcessor, abflActProcessor, commonActProcessor,
		fedActProcessor, actProcessor)
	w.RegisterActivity(syncProxyProcessor)

	return nil
}

func initSNSPublisher(ctx context.Context, conf *cfg.SnsPublisher, awsConf aws.Config) queue.Publisher {
	publisher, err := sns.NewSnsPublisherWithConfig(ctx, conf, awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialise SNS publisher", zap.Error(err))
	}

	return publisher
}
