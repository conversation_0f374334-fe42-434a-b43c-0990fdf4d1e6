package variablegenerator

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher"
	"github.com/epifi/gamma/analyser/variablegenerator/variableprocessor"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
)

type Service struct {
	*analyserVariablePb.UnimplementedVariableGeneratorServer
	rawDetailsFetcher       rawdetailsfetcher.IRawDetailsFetcher
	analysisVariableFactory IAnalysisVariableFactory
}

func NewService(
	rawDetailsFetcher rawdetailsfetcher.IRawDetailsFetcher,
	analysisVariableFactory IAnalysisVariableFactory,
) *Service {
	return &Service{
		rawDetailsFetcher:       rawDetailsFetcher,
		analysisVariableFactory: analysisVariableFactory,
	}
}

var AnalysisVariableNameToRawDetailsNameMap = map[analyserVariablePb.AnalysisVariableName][]analyserVariablePb.RawDetailsName{
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_INCOME:              {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS:          {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME, analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS:             {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DOB:                                  {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DOB},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS:                       {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO: {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS, analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY:                         {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_PORTFOLIO_SUMMARY, analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_ASSETS_DAY_CHANGE},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES:                  {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS:                 {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS:                          {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_NETWORTH_DETAILS},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION:         {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_ASSETS_DAY_CHANGE},
	analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION:                    {analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_WEEKLY_CHANGE},
}

func (s *Service) GetAnalysisVariables(ctx context.Context, req *analyserVariablePb.GetAnalysisVariablesRequest) (*analyserVariablePb.GetAnalysisVariablesResponse, error) {
	unqAnalysisVariable := lo.Uniq(req.GetAnalysisVariableNames())
	unqRawDetailsNames := s.findUniqueRawDetailsNames(unqAnalysisVariable)
	getRawDetailsResp, err := s.rawDetailsFetcher.GetRawDetailsFetcher(ctx, &rawdetailsfetcher.GetRawDetailsFetcherRequest{
		ActorId:         req.GetActorId(),
		RawDetailsNames: unqRawDetailsNames,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while getting raw details")
	}
	analysisVariableChan := make(chan *analyserVariablePb.AnalysisVariable, len(unqAnalysisVariable))
	grp, grpCtx := errgroup.WithContext(ctx)
	for _, _analysisVariableName := range unqAnalysisVariable {
		analysisVariableName := _analysisVariableName
		rawDetailsMapCopy := getRawDetailsResp.RORawDetailsMap.GetRawDetails()
		grp.Go(func() error {
			analysisVariable, err := s.generateAnalysisVariable(grpCtx, &generateAnalysisVariableRequest{
				actorId:              req.GetActorId(),
				analysisVariableName: analysisVariableName,
				rawDetailsMap:        rawDetailsMapCopy,
			})
			if err != nil {
				return errors.Wrap(err, "error while generating analysis variable")
			}
			if analysisVariable != nil {
				analysisVariableChan <- analysisVariable
			}
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, errors.Wrap(err, "error while error groups")
	}
	close(analysisVariableChan)
	analysisVariableMap := make(map[string]*analyserVariablePb.AnalysisVariable)
	for analysisVariable := range analysisVariableChan {
		analysisVariableMap[analysisVariable.GetAnalysisVariableName().String()] = analysisVariable
	}
	return &analyserVariablePb.GetAnalysisVariablesResponse{
		Status:              rpcPb.StatusOk(),
		AnalysisVariableMap: analysisVariableMap,
	}, nil
}

func (s *Service) findUniqueRawDetailsNames(analysisVariableNames []analyserVariablePb.AnalysisVariableName) []analyserVariablePb.RawDetailsName {
	rawDetailsNames := make([]analyserVariablePb.RawDetailsName, 0)
	for _, analysisVariableName := range analysisVariableNames {
		rawDetailsNames = append(rawDetailsNames, AnalysisVariableNameToRawDetailsNameMap[analysisVariableName]...)
	}
	return lo.Uniq(rawDetailsNames)
}

type generateAnalysisVariableRequest struct {
	actorId              string
	analysisVariableName analyserVariablePb.AnalysisVariableName
	rawDetailsMap        map[analyserVariablePb.RawDetailsName]*analyserVariablePb.RawDetails
}

func (s *Service) generateAnalysisVariable(ctx context.Context, req *generateAnalysisVariableRequest) (*analyserVariablePb.AnalysisVariable, error) {
	missRawDetails := s.findMissingRawDetailsForAnalysisVariable(req.analysisVariableName, req.rawDetailsMap)
	if len(missRawDetails) > 0 {
		return &analyserVariablePb.AnalysisVariable{
			AnalysisVariableName:      req.analysisVariableName,
			UnavailableRawDetailsName: missRawDetails,
			AnalysisVariableState:     analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_DATA_MISSING,
		}, nil
	}
	analysisVariable, err := s.analysisVariableFactory.GenerateAnalysisVariable(ctx, &variableprocessor.GenerateAnalysisVariableRequest{
		ActorId:          req.actorId,
		AnalysisVariable: req.analysisVariableName,
		RawDetailsMap:    req.rawDetailsMap,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while generating analysis variable from factory")
	}
	return analysisVariable, nil
}

func (s *Service) findMissingRawDetailsForAnalysisVariable(analysisVariableName analyserVariablePb.AnalysisVariableName, rawDetailsMap map[analyserVariablePb.RawDetailsName]*analyserVariablePb.RawDetails) []analyserVariablePb.RawDetailsName {
	missRawDetails := make([]analyserVariablePb.RawDetailsName, 0)
	for _, rawDetailsName := range AnalysisVariableNameToRawDetailsNameMap[analysisVariableName] {
		rawDetails, ok := rawDetailsMap[rawDetailsName]
		if !ok {
			missRawDetails = append(missRawDetails, rawDetailsName)
		}
		if rawDetails.GetRawDetailsDataStatus() == analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE {
			missRawDetails = append(missRawDetails, rawDetailsName)
		}
	}
	return missRawDetails
}
