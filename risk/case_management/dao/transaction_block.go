package dao

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/risk/case_management/dao/model"
)

const (
	// TransactionBlockMaxRecord is the maximum number of transaction blocks that can be returned in a single query
	// This limit is applied to prevent excessive memory usage and improve performance
	TransactionBlockMaxRecord = 1000
)

type TransactionBlockDaoPGDB struct {
	db cmdtypes.FRMPGDB
}

func NewTransactionBlockDao(db cmdtypes.FRMPGDB) *TransactionBlockDaoPGDB {
	return &TransactionBlockDaoPGDB{
		db: db,
	}
}

var _ TransactionBlockDao = &TransactionBlockDaoPGDB{}

// validate validates the transaction block for required fields
func validateTransactionBlock(transactionBlock *cmPb.TransactionBlock) error {
	if transactionBlock == nil {
		return fmt.Errorf("transaction block cannot be nil: %w", epifierrors.ErrInvalidArgument)
	}

	if transactionBlock.GetActorId() == "" {
		return fmt.Errorf("actor ID cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}

	return nil
}

func (t *TransactionBlockDaoPGDB) Create(ctx context.Context, transactionBlock *cmPb.TransactionBlock) (*cmPb.TransactionBlock, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "TransactionBlockDaoPGDB", "Create", time.Now())

	if err := validateTransactionBlock(transactionBlock); err != nil {
		return nil, err
	}

	entity := model.NewTransactionBlockModel(transactionBlock)
	db := gormctxv2.FromContextOrDefault(ctx, t.db)

	if createErr := db.Create(entity).Error; createErr != nil {
		// Handle potential database compatibility issues
		if strings.Contains(createErr.Error(), "syntax error at or near \"NOT\"") {
			return nil, fmt.Errorf("database compatibility issue with CREATE statement: %w", createErr)
		}
		return nil, fmt.Errorf("error creating new transaction block in db: %w", createErr)
	}

	return entity.ToProto(), nil
}

func (t *TransactionBlockDaoPGDB) Update(ctx context.Context, transactionBlock *cmPb.TransactionBlock) (*cmPb.TransactionBlock, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "TransactionBlockDaoPGDB", "Update", time.Now())

	if err := validateTransactionBlock(transactionBlock); err != nil {
		return nil, err
	}

	if transactionBlock.GetId() == "" {
		return nil, fmt.Errorf("transaction block ID cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}

	entity := model.NewTransactionBlockModel(transactionBlock)
	db := gormctxv2.FromContextOrDefault(ctx, t.db)

	// Update the record
	result := db.Model(&model.TransactionBlock{}).Where("id = ?", transactionBlock.GetId()).Updates(entity)
	if result.Error != nil {
		return nil, fmt.Errorf("error updating transaction block: %w", result.Error)
	}

	// Check if any rows were affected
	if result.RowsAffected == 0 {
		return nil, epifierrors.ErrNoRowsAffected
	}

	// Fetch the updated record
	var updatedEntity model.TransactionBlock
	if err := db.Where("id = ?", transactionBlock.GetId()).First(&updatedEntity).Error; err != nil {
		return nil, fmt.Errorf("error fetching updated transaction block: %w", err)
	}

	return updatedEntity.ToProto(), nil
}

func (t *TransactionBlockDaoPGDB) GetById(ctx context.Context, id string) (*cmPb.TransactionBlock, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "TransactionBlockDaoPGDB", "GetById", time.Now())

	if id == "" {
		return nil, fmt.Errorf("transaction block ID cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}

	var entity model.TransactionBlock
	db := gormctxv2.FromContextOrDefault(ctx, t.db)

	if err := db.Where("id = ?", id).First(&entity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error fetching transaction block by ID: %w", err)
	}

	return entity.ToProto(), nil
}

// getTransactionBlocks is a helper function to retrieve transaction blocks by a specific field and value
func (t *TransactionBlockDaoPGDB) getTransactionBlocks(ctx context.Context, fieldName string, fieldValue string, blockType string, limit int, metricName string, options ...storagev2.FilterOption) ([]*cmPb.TransactionBlock, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "TransactionBlockDaoPGDB", metricName, time.Now())

	if fieldValue == "" {
		return nil, fmt.Errorf("%s cannot be empty: %w", fieldName, epifierrors.ErrInvalidArgument)
	}

	if limit > TransactionBlockMaxRecord {
		return nil, fmt.Errorf("limit value passed is greater than max supported limit: %w", epifierrors.ErrInvalidArgument)
	}

	// If limit is not specified, apply default limit
	if limit <= 0 {
		limit = TransactionBlockMaxRecord
	}

	var entities []*model.TransactionBlock
	db := gormctxv2.FromContextOrDefault(ctx, t.db)

	// Apply filter options
	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}

	// Add the base filter for the specified field
	query := db.Where(fieldName+" = ?", fieldValue)

	// Apply block_type filter if provided and not unspecified
	if blockType != "" {
		// Convert enum representation to string representation if it's a numeric value
		// Otherwise, assume it's already the correct string format
		var blockTypeStr string
		if blockTypeInt, err := convertToBlockTypeInt(blockType); err == nil {
			// Skip filtering if the block type is UNSPECIFIED (0) to avoid UTF-8 encoding issues
			if blockTypeInt == int32(cmPb.TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED) {
				// Don't apply block_type filter for UNSPECIFIED - return all block types
			} else {
				// Convert to the enum string representation for valid enum values
				blockTypeStr = cmPb.TransactionBlockType(blockTypeInt).String()
				query = query.Where("block_type = ?", blockTypeStr)
			}
		} else {
			// Handle string representations - check if it's the unspecified string
			if blockType == "TRANSACTION_BLOCK_TYPE_UNSPECIFIED" {
				// Don't apply block_type filter for UNSPECIFIED
			} else {
				// Use the original string for other enum names
				query = query.Where("block_type = ?", blockType)
			}
		}
	}

	if err := query.Order("created_at desc").Limit(limit).Find(&entities).Error; err != nil {
		return nil, fmt.Errorf("error fetching transaction blocks by %s: %w", fieldName, err)
	}

	if len(entities) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return model.TransactionBlocks(entities).ToProto(), nil
}

// convertToBlockTypeInt attempts to convert a string to an int representing the block type enum
func convertToBlockTypeInt(blockType string) (int32, error) {
	// First try to parse the string as an integer
	var i int32
	_, err := fmt.Sscanf(blockType, "%d", &i)
	if err == nil {
		return i, nil
	}

	// If that fails, try to look up the enum name in the proto enum map
	if val, ok := cmPb.TransactionBlockType_value[blockType]; ok {
		return val, nil
	}

	return 0, fmt.Errorf("cannot convert to block type value")
}

func (t *TransactionBlockDaoPGDB) GetByActorId(ctx context.Context, actorId string, blockType string, limit int, options ...storagev2.FilterOption) ([]*cmPb.TransactionBlock, error) {
	return t.getTransactionBlocks(ctx, "actor_id", actorId, blockType, limit, "GetByActorId", options...)
}

func (t *TransactionBlockDaoPGDB) GetByAlertId(ctx context.Context, alertId string, blockType string, limit int, options ...storagev2.FilterOption) ([]*cmPb.TransactionBlock, error) {
	return t.getTransactionBlocks(ctx, "alert_id", alertId, blockType, limit, "GetByAlertId", options...)
}
