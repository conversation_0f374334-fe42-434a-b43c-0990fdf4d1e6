package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	pb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/risk/case_management/dao/model"
)

type RuleDaoCRDB struct {
	db cmdtypes.FRMCRDB
}

func NewRuleDao(db cmdtypes.FRMCRDB) *RuleDaoCRDB {
	return &RuleDaoCRDB{
		db: db,
	}
}

var _ RuleDao = &RuleDaoCRDB{}

const (
	ruleColumnNameId                 = "id"
	ruleColumnNameAssessedEntityType = "assessed_entity_type"
	ruleColumnNameConfidenceScore    = "confidence_score"
	ruleColumnNameDescription        = "description"
	ruleColumnNameName               = "name"
	ruleColumnNameEvaluationMethod   = "evaluation_method"
	ruleColumnNameVersion            = "version"
	ruleColumnNameExternalId         = "external_id"
	ruleColumnNameProvenance         = "provenance"
	ruleColumnNameState              = "state"
	ruleColumnNameSuspectEntity      = "suspect_entity"
	ruleColumnNameCreatedAt          = "created_at"
	ruleColumnNameUpdatedAt          = "updated_at"
	ruleColumnNameDeletedAt          = "deleted_at"
	ruleColumnNameRuleGroup          = "rule_group"
	ruleColumnNameAddedByEmail       = "added_by_email"
	ruleColumnSeedPrecision          = "seed_precision"
	ruleColumnForceUseSeedPrecision  = "force_use_seed_precision"
	ruleColumnTags                   = "tags"
)

var (
	// RuleSelectColumnNameMap maps Rule field mask to column name

	RuleSelectColumnNameMap = map[pb.RuleFieldMask]string{
		pb.RuleFieldMask_RULE_FIELD_MASK_ID:                       ruleColumnNameId,
		pb.RuleFieldMask_RULE_FIELD_MASK_ASSESSED_ENTITY_TYPE:     ruleColumnNameAssessedEntityType,
		pb.RuleFieldMask_RULE_FIELD_MASK_CONFIDENCE_SCORE:         ruleColumnNameConfidenceScore,
		pb.RuleFieldMask_RULE_FIELD_MASK_DESCRIPTION:              ruleColumnNameDescription,
		pb.RuleFieldMask_RULE_FIELD_MASK_NAME:                     ruleColumnNameName,
		pb.RuleFieldMask_RULE_FIELD_MASK_EVALUATION_METHOD:        ruleColumnNameEvaluationMethod,
		pb.RuleFieldMask_RULE_FIELD_MASK_VERSION:                  ruleColumnNameVersion,
		pb.RuleFieldMask_RULE_FIELD_MASK_EXTERNAL_ID:              ruleColumnNameExternalId,
		pb.RuleFieldMask_RULE_FIELD_MASK_PROVENANCE:               ruleColumnNameProvenance,
		pb.RuleFieldMask_RULE_FIELD_MASK_STATE:                    ruleColumnNameState,
		pb.RuleFieldMask_RULE_FIELD_MASK_SUSPECT_ENTITY:           ruleColumnNameSuspectEntity,
		pb.RuleFieldMask_RULE_FIELD_MASK_CREATED_AT:               ruleColumnNameCreatedAt,
		pb.RuleFieldMask_RULE_FIELD_MASK_UPDATED_AT:               ruleColumnNameUpdatedAt,
		pb.RuleFieldMask_RULE_FIELD_MASK_DELETED_AT:               ruleColumnNameDeletedAt,
		pb.RuleFieldMask_RULE_FIELD_MASK_RULE_GROUP:               ruleColumnNameRuleGroup,
		pb.RuleFieldMask_RULE_FIELD_MASK_ADDED_BY_EMAIL:           ruleColumnNameAddedByEmail,
		pb.RuleFieldMask_RULE_FIELD_MASK_SEED_PRECISION:           ruleColumnSeedPrecision,
		pb.RuleFieldMask_RULE_FIELD_MASK_FORCE_USE_SEED_PRECISION: ruleColumnForceUseSeedPrecision,
		pb.RuleFieldMask_RULE_FIELD_MASK_TAGS:                     ruleColumnTags,
	}

	// RuleUpdateColumnNameMap maps Rule field mask to column name
	RuleUpdateColumnNameMap = map[pb.RuleFieldMask]string{
		pb.RuleFieldMask_RULE_FIELD_MASK_CONFIDENCE_SCORE:         ruleColumnNameConfidenceScore,
		pb.RuleFieldMask_RULE_FIELD_MASK_DESCRIPTION:              ruleColumnNameDescription,
		pb.RuleFieldMask_RULE_FIELD_MASK_STATE:                    ruleColumnNameState,
		pb.RuleFieldMask_RULE_FIELD_MASK_RULE_GROUP:               ruleColumnNameRuleGroup,
		pb.RuleFieldMask_RULE_FIELD_MASK_SUSPECT_ENTITY:           ruleColumnNameSuspectEntity,
		pb.RuleFieldMask_RULE_FIELD_MASK_ADDED_BY_EMAIL:           ruleColumnNameAddedByEmail,
		pb.RuleFieldMask_RULE_FIELD_MASK_SEED_PRECISION:           ruleColumnSeedPrecision,
		pb.RuleFieldMask_RULE_FIELD_MASK_FORCE_USE_SEED_PRECISION: ruleColumnForceUseSeedPrecision,
		pb.RuleFieldMask_RULE_FIELD_MASK_TAGS:                     ruleColumnTags,
		pb.RuleFieldMask_RULE_FIELD_MASK_ASSESSED_ENTITY_TYPE:     ruleColumnNameAssessedEntityType,
	}
)

func (r *RuleDaoCRDB) CreateWithVersionUpgrade(ctx context.Context, rule *pb.Rule) (*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "CreateWithVersionUpgrade", time.Now())

	if err := rule.Validate(); err != nil {
		return nil, fmt.Errorf("parameter error while creating the rule: %w", err)
	}

	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var ruleModel, createRuleModel *model.Rule
	txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		if getErr := db.Where("name = ?", rule.GetName()).Order("version desc").Take(&ruleModel).Error; getErr != nil {
			if !errors.Is(getErr, gorm.ErrRecordNotFound) {
				return errors.Wrap(getErr, "unable to fetch the rule")
			}
		}
		rule.Version = ruleModel.Version + 1
		createRuleModel = model.NewRuleModel(rule)

		if createErr := db.Create(createRuleModel).Error; createErr != nil {
			return errors.Wrap(createErr, "error creating new rule in db: %w")
		}
		return nil
	})

	if txnErr != nil {
		return nil, errors.Wrap(txnErr, "error while creating the rule in transaction block")
	}

	return createRuleModel.ToProto(), nil
}

func (r *RuleDaoCRDB) Update(ctx context.Context, rule *pb.Rule,
	updateMask []pb.RuleFieldMask) (*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "Update", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	ruleModel := model.NewRuleModel(rule)
	updateColumns, err := getUpdateColumns(updateMask, lo.Keys(RuleUpdateColumnNameMap), RuleSelectColumnNameMap)

	if err != nil {
		return nil, err
	}

	if contains(updateColumns, ruleColumnNameState) && rule.GetState() == pb.RuleState_RULE_STATE_UNSPECIFIED {
		return nil, fmt.Errorf("rule state can't be updated as unspecified: %w", epifierrors.ErrInvalidArgument)
	}

	if contains(updateColumns, ruleColumnNameConfidenceScore) && rule.GetConfidenceScore() < 0 && rule.GetConfidenceScore() > 100 {
		return nil, fmt.Errorf("confidence score should be between 0 and 100: %w", epifierrors.ErrInvalidArgument)
	}

	if contains(updateColumns, ruleColumnSeedPrecision) && (rule.GetSeedPrecision() < 0 || rule.GetSeedPrecision() > 1) {
		return nil, fmt.Errorf("seed precision should be between 0 and 1: %w", epifierrors.ErrInvalidArgument)
	}

	if updateErr := db.Select(updateColumns).Updates(ruleModel).Error; updateErr != nil {
		return nil, fmt.Errorf("error updating rule: %w", updateErr)
	}

	return ruleModel.ToProto(), nil
}

func (r *RuleDaoCRDB) GetBulkByExternalId(ctx context.Context, externalIdList []string) ([]*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "GetBulkByExternalId", time.Now())

	if len(externalIdList) == 0 {
		return nil, fmt.Errorf("could not pass the empty list for external while fetching the rules: %w", epifierrors.ErrInvalidArgument)
	}
	var (
		rules []*model.Rule
	)
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	if err := db.Where("external_id in (?)", externalIdList).Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("error fetching rules by external ids: %w", err)
	}

	if len(rules) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return convertToRulesProto(rules), nil
}

func (r *RuleDaoCRDB) GetBulkById(ctx context.Context, idList []string) ([]*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "GetBulkById", time.Now())
	if len(idList) == 0 {
		return nil, fmt.Errorf("could not pass the empty list for ids while fetching the rules: %w", epifierrors.ErrInvalidArgument)
	}
	var (
		rules []*model.Rule
	)
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	if err := db.Where("id in (?)", idList).Order("updated_at desc").Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("error fetching rules by ids: %w", err)
	}

	if len(rules) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return convertToRulesProto(rules), nil
}

func (r *RuleDaoCRDB) GetLatestByName(ctx context.Context, name string) (*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "GetLatestByName", time.Now())
	var ruleModel *model.Rule
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	if err := db.Where("name = ?", name).Order("version desc").Take(&ruleModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error fetching rules by name: %v", err)
	}

	return ruleModel.ToProto(), nil
}

func convertToRulesProto(rules []*model.Rule) []*pb.Rule {
	var rulesProto []*pb.Rule
	for _, rule := range rules {
		rulesProto = append(rulesProto, rule.ToProto())
	}
	return rulesProto
}

func contains(columns []string, updateColumn string) bool {
	for _, a := range columns {
		if a == updateColumn {
			return true
		}
	}
	return false
}

func (r *RuleDaoCRDB) GetByRuleGroup(ctx context.Context, group pb.RuleGroup) ([]*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "GetByRuleGroup", time.Now())

	var (
		rules []*model.Rule
	)
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	if err := db.Where("rule_group in (?)", group.String()).Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("error fetching rules by rule group: %w", err)
	}

	if len(rules) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return convertToRulesProto(rules), nil
}

func (r *RuleDaoCRDB) GetAll(ctx context.Context) ([]*pb.Rule, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RuleDaoCRDB", "GetAll", time.Now())
	var (
		rules []*model.Rule
	)
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	if err := db.Find(&rules).Order("updated_at desc").Error; err != nil {
		return nil, fmt.Errorf("error fetching All rules: %w", err)
	}

	if len(rules) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return convertToRulesProto(rules), nil
}
