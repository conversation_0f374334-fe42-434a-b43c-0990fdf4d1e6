package model

import (
	"time"

	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/pkg/nulltypes"

	"github.com/lib/pq"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
)

// TransactionBlock represents the transaction_blocks table in the database
type TransactionBlock struct {
	Id               string `gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	ActorId          string
	AlertId          nulltypes.NullString
	AggregatedCredit float64
	AggregatedDebit  float64
	Duration         time.Duration
	BlockType        string
	TransactionIds   pq.StringArray `gorm:"type:text[]"`
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAtUnix    int64
}

// TransactionBlocks is a slice of TransactionBlock
type TransactionBlocks []*TransactionBlock

// ToProto converts the model to proto
func (t *TransactionBlock) ToProto() *cmPb.TransactionBlock {
	if t == nil {
		return nil
	}

	// Convert BlockType string to enum value with proper validation
	var blockType = cmPb.TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED // Default to UNSPECIFIED

	if t.BlockType != "" {
		// Handle explicit UNSPECIFIED case
		if t.BlockType == "TRANSACTION_BLOCK_TYPE_UNSPECIFIED" {
			blockType = cmPb.TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED
		} else if val, ok := cmPb.TransactionBlockType_value[t.BlockType]; ok {
			// Convert valid enum name to enum value
			blockType = cmPb.TransactionBlockType(val)
		}
		// If the string doesn't match any known enum value, keep the default UNSPECIFIED
	}

	proto := &cmPb.TransactionBlock{
		Id:               t.Id,
		ActorId:          t.ActorId,
		AlertId:          t.AlertId.GetValue(),
		AggregatedCredit: t.AggregatedCredit,
		AggregatedDebit:  t.AggregatedDebit,
		BlockType:        blockType,
		TransactionIds:   t.TransactionIds,
		Duration: &durationpb.Duration{
			Seconds: int64(t.Duration.Seconds()),
			Nanos:   int32(t.Duration.Nanoseconds() % 1000000000), //nolint:gosec // G115: This is safe as the result of modulo 1e9 is always < 2^31
		},
	}

	return proto
}

// ToProtos converts the models to protos
func (t TransactionBlocks) ToProto() []*cmPb.TransactionBlock {
	var protos []*cmPb.TransactionBlock
	for _, model := range t {
		protos = append(protos, model.ToProto())
	}
	return protos
}

// NewTransactionBlockModel converts proto to model
func NewTransactionBlockModel(proto *cmPb.TransactionBlock) *TransactionBlock {
	if proto == nil {
		return nil
	}

	// Convert enum value to string with proper handling of UNSPECIFIED
	var blockType string
	if proto.BlockType == cmPb.TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED {
		// For UNSPECIFIED, use the explicit enum name to avoid null character issues
		blockType = "TRANSACTION_BLOCK_TYPE_UNSPECIFIED"
	} else {
		// For other valid enum values, use the string representation
		blockType = proto.BlockType.String()
	}

	model := &TransactionBlock{
		Id:               proto.Id,
		ActorId:          proto.ActorId,
		AlertId:          nulltypes.NewNullString(proto.AlertId),
		AggregatedCredit: proto.AggregatedCredit,
		AggregatedDebit:  proto.AggregatedDebit,
		BlockType:        blockType,
		TransactionIds:   proto.TransactionIds,
		DeletedAtUnix:    proto.DeletedAtUnix,
	}

	// Handle nil Duration
	if proto.Duration != nil {
		model.Duration = time.Duration(proto.Duration.GetSeconds()) * time.Second
	}

	return model
}
