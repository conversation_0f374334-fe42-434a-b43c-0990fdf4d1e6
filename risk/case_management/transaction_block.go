package case_management

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
)

// GetTransactionBlocks retrieves transaction blocks based on the provided actor_id or alert_id
func (s *Service) GetTransactionBlocks(ctx context.Context, req *cmPb.GetTransactionBlocksRequest) (*cmPb.GetTransactionBlocksResponse, error) {
	var (
		blocks []*cmPb.TransactionBlock
		err    error
	)

	// Validate the request
	if req.GetActorId() == "" && req.GetAlertId() == "" {
		logger.Error(ctx, "no identifier provided for GetTransactionBlocks request")
		return &cmPb.GetTransactionBlocksResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	// Set a default limit if not provided
	limit := req.GetLimit()
	if limit <= 0 {
		limit = 100 // Default limit
	}

	// Convert block type enum to proper string representation
	var blockTypeStr string
	if req.GetBlockType() == cmPb.TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED {
		// For UNSPECIFIED, pass empty string to indicate no filter should be applied
		blockTypeStr = ""
	} else {
		// For other enum values, use the proper string representation
		blockTypeStr = req.GetBlockType().String()
	}

	// Get transaction blocks based on identifier type
	switch {
	case req.GetActorId() != "":
		// Fetch transaction blocks by actor ID
		blocks, err = s.transactionBlockDao.GetByActorId(ctx, req.GetActorId(), blockTypeStr, int(limit))
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &cmPb.GetTransactionBlocksResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "error fetching transaction blocks by actor ID",
				zap.String("actor_id", req.GetActorId()),
				zap.Error(err))
			return &cmPb.GetTransactionBlocksResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

	case req.GetAlertId() != "":
		// Fetch transaction blocks by alert ID
		blocks, err = s.transactionBlockDao.GetByAlertId(ctx, req.GetAlertId(), blockTypeStr, int(limit))
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &cmPb.GetTransactionBlocksResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "error fetching transaction blocks by alert ID",
				zap.String("alert_id", req.GetAlertId()),
				zap.Error(err))
			return &cmPb.GetTransactionBlocksResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

	default:
		logger.Error(ctx, "invalid identifier provided for GetTransactionBlocks request")
		return &cmPb.GetTransactionBlocksResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	return &cmPb.GetTransactionBlocksResponse{
		Status:            rpcPb.StatusOk(),
		TransactionBlocks: blocks,
	}, nil
}

// CreateTransactionBlock creates new transaction blocks
func (s *Service) CreateTransactionBlock(ctx context.Context, req *cmPb.CreateTransactionBlockRequest) (*cmPb.CreateTransactionBlockResponse, error) {
	if len(req.GetTransactionBlocks()) == 0 {
		logger.Error(ctx, "no transaction blocks provided for CreateTransactionBlock request")
		return &cmPb.CreateTransactionBlockResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	createdBlocks := make([]*cmPb.TransactionBlock, 0, len(req.GetTransactionBlocks()))

	for _, block := range req.GetTransactionBlocks() {
		// Add unique ID if not provided
		if block.GetId() == "" {
			block.Id = uuid.New().String()
		}

		// Set created/updated timestamps
		now := time.Now()
		block.CreatedAt = timestamppb.New(now)
		block.UpdatedAt = timestamppb.New(now)

		// Create the transaction block in the database
		createdBlock, err := s.transactionBlockDao.Create(ctx, block)
		if err != nil {
			logger.Error(ctx, "error creating transaction block",
				zap.String("actor_id", block.GetActorId()),
				zap.Error(err))
			return &cmPb.CreateTransactionBlockResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

		createdBlocks = append(createdBlocks, createdBlock)
	}

	return &cmPb.CreateTransactionBlockResponse{
		Status:            rpcPb.StatusOk(),
		TransactionBlocks: createdBlocks,
	}, nil
}
