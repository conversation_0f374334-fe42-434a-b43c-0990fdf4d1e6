package liquiloans

import (
	"context"
	"net/http"

	"github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	liquiloansPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/vendorgateway/config"

	"go.uber.org/zap"
)

type Service struct {
	liquiloansPb.UnimplementedLiquiloansServer
	Handler    *vendorapi.HTTPRequestHandler
	conf       *config.PreApprovedLoan
	httpClient *http.Client
}

func NewService(h *vendorapi.HTTPRequestHandler, httpClient *http.Client, conf *config.PreApprovedLoan) *Service {
	return &Service{
		Handler:    h,
		conf:       conf,
		httpClient: httpClient,
	}
}

func (s *Service) GetCreditLineSchemes(ctx context.Context, req *liquiloansPb.GetCreditLineSchemesRequest) (*liquiloansPb.GetCreditLineSchemesResponse, error) {
	vendorReq := &GetCreditLineDetailsRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetCreditLineSchemes request", zap.Error(err))
		return &liquiloansPb.GetCreditLineSchemesResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetCreditLineSchemesResponse), nil
}

func (s *Service) AddPersonalDetails(ctx context.Context, req *liquiloansPb.AddPersonalDetailsRequest) (*liquiloansPb.AddPersonalDetailsResponse, error) {
	vendorReq := &AddPersonalDetailsRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing AddPersonalDetails request", zap.Error(err))
		return &liquiloansPb.AddPersonalDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.AddPersonalDetailsResponse), nil
}

func (s *Service) AddEmploymentDetails(ctx context.Context, req *liquiloansPb.AddEmploymentDetailsRequest) (*liquiloansPb.AddDetailsResponse, error) {
	vendorReq := &AddEmploymentDetailsRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing AddEmploymentDetails request", zap.Error(err))
		return &liquiloansPb.AddDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.AddDetailsResponse), nil
}

func (s *Service) AddBankingDetails(ctx context.Context, req *liquiloansPb.AddBankingDetailsRequest) (*liquiloansPb.AddDetailsResponse, error) {
	vendorReq := &AddBankingDetailsRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing AddBankingDetails request", zap.Error(err))
		return &liquiloansPb.AddDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.AddDetailsResponse), nil
}

func (s *Service) AddAddressDetails(ctx context.Context, req *liquiloansPb.AddAddressDetailsRequest) (*liquiloansPb.AddDetailsResponse, error) {
	vendorReq := &AddAddressDetailsRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing AddAddressDetails request", zap.Error(err))
		return &liquiloansPb.AddDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.AddDetailsResponse), nil
}

func (s *Service) ApplicantLookup(ctx context.Context, req *liquiloansPb.ApplicantLookupRequest) (*liquiloansPb.ApplicantLookupResponse, error) {
	vendorReq := &ApplicantLookupRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing Applicant Lookup request", zap.Error(err))
		return &liquiloansPb.ApplicantLookupResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.ApplicantLookupResponse), nil
}

func (s *Service) GetMandateLink(ctx context.Context, req *liquiloansPb.GetMandateLinkRequest) (*liquiloansPb.GetMandateLinkResponse, error) {
	vendorReq := &GetMandateLinkRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetMandateLink request", zap.Error(err))
		return &liquiloansPb.GetMandateLinkResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetMandateLinkResponse), nil
}

func (s *Service) GetMandateStatus(ctx context.Context, req *liquiloansPb.GetMandateStatusRequest) (*liquiloansPb.GetMandateStatusResponse, error) {
	vendorReq := &GetMandateStatusRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetMandateStatus request", zap.Error(err))
		return &liquiloansPb.GetMandateStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetMandateStatusResponse), nil
}

func (s *Service) MakeDrawdown(ctx context.Context, req *liquiloansPb.MakeDrawdownRequest) (*liquiloansPb.MakeDrawdownResponse, error) {
	vendorReq := &MakeDrawdownRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing make Drawdown request", zap.Error(err))
		return &liquiloansPb.MakeDrawdownResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.MakeDrawdownResponse), nil
}

func (s *Service) GetPdfAgreement(ctx context.Context, req *liquiloansPb.GetPdfAgreementRequest) (*liquiloansPb.GetPdfAgreementResponse, error) {
	vendorReq := &GetPdfAgreementRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing get pdf agreement request", zap.Error(err))
		return &liquiloansPb.GetPdfAgreementResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetPdfAgreementResponse), nil
}

func (s *Service) SendBorrowerAgreementOtp(ctx context.Context, req *liquiloansPb.SendBorrowerAgreementOtpRequest) (*liquiloansPb.SendBorrowerAgreementOtpResponse, error) {
	vendorReq := &SendBorrowerAgreementOtpRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing send borrower agreement otp request", zap.Error(err))
		return &liquiloansPb.SendBorrowerAgreementOtpResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.SendBorrowerAgreementOtpResponse), nil
}

func (s *Service) VerifyBorrowerAgreementOtp(ctx context.Context, req *liquiloansPb.VerifyBorrowerAgreementOtpRequest) (*liquiloansPb.VerifyBorrowerAgreementOtpResponse, error) {
	vendorReq := &VerifyBorrowerAgreementOtpRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing verify borrower agreement otp request", zap.Error(err))
		return &liquiloansPb.VerifyBorrowerAgreementOtpResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.VerifyBorrowerAgreementOtpResponse), nil
}

func (s *Service) GetLoanStatus(ctx context.Context, req *liquiloansPb.GetLoanStatusRequest) (*liquiloansPb.GetLoanStatusResponse, error) {
	vendorReq := &GetLoanStatusRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetLoanStatus request", zap.Error(err))
		return &liquiloansPb.GetLoanStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetLoanStatusResponse), nil
}

func (s *Service) VerifyAndDownloadCkyc(ctx context.Context, req *liquiloansPb.VerifyAndDownloadCkycRequest) (*liquiloansPb.VerifyAndDownloadCkycResponse, error) {
	vendorReq := &VerifyAndDownloadCkycRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing VerifyAndDownloadCkyc request", zap.Error(err))
		return &liquiloansPb.VerifyAndDownloadCkycResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.VerifyAndDownloadCkycResponse), nil
}

func (s *Service) GetApplicantStatus(ctx context.Context, req *liquiloansPb.GetApplicantStatusRequest) (*liquiloansPb.GetApplicantStatusResponse, error) {
	vendorReq := &GetApplicantStatusRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetApplicantStatus request", zap.Error(err))
		return &liquiloansPb.GetApplicantStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetApplicantStatusResponse), nil
}

func (s *Service) GetRepaymentSchedule(ctx context.Context, req *liquiloansPb.GetRepaymentScheduleRequest) (*liquiloansPb.GetRepaymentScheduleResponse, error) {
	vendorReq := &GetRepaymentScheduleRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetRepaymentSchedule request", zap.Error(err))
		return &liquiloansPb.GetRepaymentScheduleResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetRepaymentScheduleResponse), nil
}

func (s *Service) UploadDocument(ctx context.Context, req *liquiloansPb.UploadDocumentRequest) (*liquiloansPb.UploadDocumentResponse, error) {
	vendorReq := &UploadDocumentRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing UploadDocument request", zap.Error(err))
		return &liquiloansPb.UploadDocumentResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.UploadDocumentResponse), nil
}

func (s *Service) SaveCollection(ctx context.Context, req *liquiloansPb.SaveCollectionRequest) (*liquiloansPb.SaveCollectionResponse, error) {
	vendorReq := &SaveCollectionRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing SaveCollection request", zap.Error(err))
		return &liquiloansPb.SaveCollectionResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.SaveCollectionResponse), nil
}

func (s *Service) HashGenerationForOkyc(ctx context.Context, req *liquiloansPb.HashGenerationForOkycRequest) (*liquiloansPb.HashGenerationForOkycResponse, error) {
	vendorReq := &HashGenerationForOkycRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetUniqueHashFldg request", zap.Error(err))
		return &liquiloansPb.HashGenerationForOkycResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.HashGenerationForOkycResponse), nil
}

func (s *Service) CaptchaGenerationForOkyc(ctx context.Context, req *liquiloansPb.CaptchaGenerationForOkycRequest) (*liquiloansPb.CaptchaGenerationForOkycResponse, error) {
	vendorReq := &CaptchaGenerationForOkycRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetUniqueHashWithInitialisingHashFldg request", zap.Error(err))
		return &liquiloansPb.CaptchaGenerationForOkycResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.CaptchaGenerationForOkycResponse), nil
}

func (s *Service) GenerateOtpForOkyc(ctx context.Context, req *liquiloansPb.GenerateOtpForOkycRequest) (*liquiloansPb.GenerateOtpForOkycResponse, error) {
	vendorReq := &GenerateOtpForOkycRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetUniqueHashFldg request", zap.Error(err))
		return &liquiloansPb.GenerateOtpForOkycResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GenerateOtpForOkycResponse), nil
}

func (s *Service) ValidateOtpForOkyc(ctx context.Context, req *liquiloansPb.ValidateOtpForOkycRequest) (*liquiloansPb.ValidateOtpForOkycResponse, error) {
	vendorReq := &ValidateOtpForOkycRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetUniqueHashWithInitialisingHashFldg request", zap.Error(err))
		return &liquiloansPb.ValidateOtpForOkycResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.ValidateOtpForOkycResponse), nil
}

func (s *Service) UpdateLead(ctx context.Context, req *liquiloansPb.UpdateLeadRequest) (*liquiloansPb.UpdateLeadResponse, error) {
	vendorReq := &UpdateLeadRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing UpdateLead request", zap.Error(err))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			return &liquiloansPb.UpdateLeadResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error())}, nil
		}
		return &liquiloansPb.UpdateLeadResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.UpdateLeadResponse), nil
}

func (s *Service) CancelLead(ctx context.Context, req *liquiloansPb.CancelLeadRequest) (*liquiloansPb.CancelLeadResponse, error) {
	vendorReq := &CancelLeadRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing CancelLead request", zap.Error(err))
		return &liquiloansPb.CancelLeadResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.CancelLeadResponse), nil
}

func (s *Service) ForeClosureDetails(ctx context.Context, req *liquiloansPb.ForeClosureDetailsRequest) (*liquiloansPb.ForeClosureDetailsResponse, error) {
	vendorReq := &ForeClosureDetailsRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing ForeClosureDetails request", zap.Error(err))
		return &liquiloansPb.ForeClosureDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.ForeClosureDetailsResponse), nil
}

func (s *Service) UpdateApplicantUdf(ctx context.Context, req *liquiloansPb.UpdateApplicantUdfRequest) (*liquiloansPb.UpdateApplicantUdfResponse, error) {
	vendorReq := &UpdateApplicantUdfRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing UpdateApplicantUdf request", zap.Error(err))
		return &liquiloansPb.UpdateApplicantUdfResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.UpdateApplicantUdfResponse), nil
}

func (s *Service) CreateRepaymentSchedule(ctx context.Context, req *liquiloansPb.CreateRepaymentScheduleRequest) (*liquiloansPb.CreateRepaymentScheduleResponse, error) {
	vendorReq := &CreateRepaymentScheduleRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing CreateRepaymentSchedule request", zap.Error(err))
		return &liquiloansPb.CreateRepaymentScheduleResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.CreateRepaymentScheduleResponse), nil
}

func (s *Service) SaveCharges(ctx context.Context, req *liquiloansPb.SaveChargesRequest) (*liquiloansPb.SaveChargesResponse, error) {
	vendorReq := &SaveChargesRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing SaveCharges request", zap.Error(err))
		return &liquiloansPb.SaveChargesResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.SaveChargesResponse), nil
}

func (s *Service) GetApplicationSoa(ctx context.Context, req *liquiloansPb.GetApplicationSoaRequest) (*liquiloansPb.GetApplicationSoaResponse, error) {
	vendorReq := &GetApplicationSoaRequest{
		Method: http.MethodGet,
		Req:    req,
		Conf:   s.conf.Liquiloans,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetApplicationSoa request", zap.Error(err))
		return &liquiloansPb.GetApplicationSoaResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*liquiloansPb.GetApplicationSoaResponse), nil
}
