package liquiloans

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	vgLlPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	vendorLlPb "github.com/epifi/gamma/api/vendors/liquiloans/lending"
	"github.com/epifi/gamma/vendorgateway/config"
)

type GetApplicationSoaRequest struct {
	Method string
	Req    *vgLlPb.GetApplicationSoaRequest
	Conf   *config.LiquiloansPreapprovedLoan
}

func (c *GetApplicationSoaRequest) HTTPMethod() string {
	return http.MethodGet
}

func (c *GetApplicationSoaRequest) URL() string {
	return c.Conf.HttpUrl + "/api/apiintegration/v4/getApplicationSoa"
}

func (c *GetApplicationSoaRequest) GetResponse() vendorapi.Response {
	return &GetApplicationSoaResponse{}
}

func (c *GetApplicationSoaRequest) Marshal() ([]byte, error) {
	sid, key, _ := getSidKeyAndSchemeCode(c.Req.GetLoanProgram(), c.Conf, c.Req.GetSchemeVersion())
	checksumDataString := fmt.Sprintf("%v||%v", c.Req.GetApplicationId(), sid)
	checksum := calculateChecksum(checksumDataString, key)
	requestPayload := &vendorLlPb.GetApplicationSoaRequest{
		Sid:           sid,
		ApplicationId: c.Req.GetApplicationId(),
		Checksum:      checksum,
	}
	return protojson.Marshal(requestPayload)
}

type GetApplicationSoaResponse struct{}

func (c *GetApplicationSoaResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := &vendorLlPb.GetApplicationSoaResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to liquiloans proto message")
	}

	if !res.GetStatus() {
		return &vgLlPb.GetApplicationSoaResponse{
			Status: rpc.StatusInternalWithDebugMsg(res.GetMessage()),
		}, nil
	}

	vgResponse := &vgLlPb.GetApplicationSoaResponse{
		Status: rpc.StatusOkWithDebugMsg(res.GetMessage()),
		Data: &vgLlPb.GetApplicationSoaResponse_Data{
			Link:          res.GetData().GetLink(),
			ApplicationId: res.GetData().GetApplicationId(),
		},
	}
	return vgResponse, nil
}

func (c *GetApplicationSoaResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in GetApplicationSoa API", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
