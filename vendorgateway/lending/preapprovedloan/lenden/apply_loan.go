package lenden

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"

	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

// ApplyLoanRequest represents the request structure for applying for a loan by the user.
type ApplyLoanRequest struct {
	Req  *lendenPb.ApplyForLoanRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

type ApplyLoanResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *ApplyLoanRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *ApplyLoanRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *ApplyLoanRequest) GetResponse() vendorapi.Response {
	return &ApplyLoanResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

// nolint:funlen
func (c *ApplyLoanRequest) Marshal() ([]byte, error) {
	interestType, err := getLendenInterestType(c.Req.GetInterest().GetType())
	if err != nil {
		return nil, err
	}

	interestFrequency, err := getLendenInterestFrequency(c.Req.GetInterest().GetFrequency())
	if err != nil {
		return nil, err
	}

	addressType, err := getLendenAddressType(c.Req.GetAddressType())
	if err != nil {
		return nil, err
	}

	pincode, err := strconv.ParseInt(c.Req.GetAddress().GetPostalCode(), 10, 32)
	if err != nil {
		return nil, fmt.Errorf("failed to convert postal code from string to int: %w", err)
	}

	stateCode, err := getLendenStateCode(c.Req.GetAddress().GetAdministrativeArea())
	if err != nil {
		return nil, err
	}

	if c.Req.GetRequestedAmount().GetCurrencyCode() != pkgMoney.RupeeCurrencyCode {
		return nil, fmt.Errorf("currency code is not INR")
	}
	// TODO(Brijesh): Check if we should add max-min or other validations on requested amount value and interest value
	// TODO(Brijesh): Check what if we should validate the interest rate value against the values allowed
	// TODO(Brijesh): Check if we should have interest rate value as decimal in case floats are accepted, else migrate to int
	// TODO(Brijesh): Check if we should validate the address details like non-emptiness of pin-code and state
	requestPayload := &vendorLendenPb.ApplyLoanRequest{
		Params: &vendorLendenPb.Params{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Fields: &vendorLendenPb.Fields{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Json: &vendorLendenPb.ApplyLoanRequestPayload{
			ProductId:       c.Conf.ProductId,
			UserId:          c.Req.GetUserId(),
			ReferenceId:     c.Req.GetReferenceId(),
			RequestedAmount: int32(c.Req.GetRequestedAmount().GetUnits()),
			Interest: &vendorLendenPb.Interest{
				Type:      interestType,
				Value:     c.Req.GetInterest().GetValue(),
				Frequency: interestFrequency,
			},
			AddressDetails: &vendorLendenPb.AddressDetails{
				Type:        addressType,
				AddressLine: c.Req.GetAddress().GetAddressString(),
				Pincode:     int32(pincode),
				StateCode:   stateCode,
			},
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeCreateLoan),
	}
	// Step 2: Marshal the VendorRequest into JSON
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling vendor request: %w", err)
	}

	// Step 3: Encrypt the JSON request
	encryptedReqBytes, err := c.Cryptor.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, fmt.Errorf("error encrypting vendor request: %w", err)
	}

	// Step 4: Return the encrypted bytes
	return encryptedReqBytes, nil
}

// nolint:dupl
func (c *ApplyLoanResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}

	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}

	decryptedBytes, err := c.Cryptor.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, fmt.Errorf("error decrypting response: %w", err)
	}

	responseWithWrapper := vendorLendenPb.ApplyLoanResponseWrapper{}
	if err := unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	vendorResponse := responseWithWrapper.GetResponse().GetResponseData()
	if vendorResponse.GetIsDuplicate() {
		return &lendenPb.ApplyForLoanResponse{
			Status: rpc.StatusAlreadyExists(),
			LoanId: vendorResponse.GetLoanId(),
		}, nil
	}
	return &lendenPb.ApplyForLoanResponse{
		Status: rpc.StatusOk(),
		LoanId: vendorResponse.GetLoanId(),
	}, nil
}

func (c *ApplyLoanResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden apply loan API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))

	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}

	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}

	decryptedBytes, err := c.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, fmt.Errorf("error decrypting response: %w", err)
	}

	responseWithWrapper := vendorLendenPb.ApplyLoanResponseWrapper{}
	if err := unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	if responseWithWrapper.GetResponse().GetMessageCode() == MessageCodeMaxActiveLoanCountExceeded.String() {
		return &lendenPb.ApplyForLoanResponse{Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.ApplyForLoanResponse_MAX_LOAN_ACCOUNT_REACHED_AT_VENDOR), responseWithWrapper.GetResponse().GetMessage())}, nil
	}

	return &lendenPb.ApplyForLoanResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
