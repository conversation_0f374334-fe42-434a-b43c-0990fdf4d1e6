package lenden

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"

	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

const InternalApiCodeGetAmortizationSchedule InternalApiCode = "AMORTIZATION_SCHEDULE_DETAILS_API"

// GetAmortizationScheduleRequest represents the request structure for fetching amortization schedule.
type GetAmortizationScheduleRequest struct {
	Req  *lendenPb.GetAmortizationScheduleRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

// GetAmortizationScheduleResponse represents the response structure for fetching amortization schedule.
type GetAmortizationScheduleResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *GetAmortizationScheduleRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *GetAmortizationScheduleRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *GetAmortizationScheduleRequest) GetResponse() vendorapi.Response {
	return &GetAmortizationScheduleResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

func (c *GetAmortizationScheduleRequest) Marshal() ([]byte, error) {
	if c.Req.GetLoanId() == "" {
		return nil, errors.New("loan_id is mandatory and must be a non-empty string")
	}
	requestPayload := &vendorLendenPb.AmortizationScheduleRequest{
		Params: &vendorLendenPb.Params{},
		Fields: &vendorLendenPb.Fields{},
		Json: &vendorLendenPb.AmortizationScheduleRequestPayload{
			LoanId:       c.Req.GetLoanId(),
			ProductId:    c.Conf.ProductId,
			OriginSystem: OriginSystemPartner,
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeGetAmortizationSchedule),
	}
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, errors.Wrap(err, "error marshaling vendor request")
	}
	encryptedReqBytes, err := c.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, errors.Wrap(err, "error encrypting vendor request")
	}
	return encryptedReqBytes, nil
}

func (c *GetAmortizationScheduleResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}
	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response body")
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}
	decryptedBytes, err := c.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting response")
	}
	responseWithWrapper := vendorLendenPb.AmortizationScheduleResponseWrapper{}
	if err = unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response")
	}
	vendorResponse := responseWithWrapper.GetResponse().GetResponseData()
	var schedule []*lendenPb.AmortizationScheduleItem
	for _, vendorScheduleItem := range vendorResponse.GetAmortizationSchedule() {
		scheduleItem, convErr := getAmortizationScheduleItem(vendorScheduleItem)
		if convErr != nil {
			return nil, errors.Wrap(convErr, "error converting amortization schedule item")
		}
		schedule = append(schedule, scheduleItem)
	}
	return &lendenPb.GetAmortizationScheduleResponse{
		Status:               rpc.StatusOk(),
		AmortizationSchedule: schedule,
	}, nil
}

func getAmortizationScheduleItem(item *vendorLendenPb.AmortizationScheduleItem) (*lendenPb.AmortizationScheduleItem, error) {
	var breakup []*lendenPb.AmortizationAmountBreakupComponent
	for _, d := range item.GetDetails() {
		purpose, err := getAmortizationComponentPurpose(d.GetPurpose())
		if err != nil {
			return nil, errors.Wrap(err, "error getting amortization component purpose")
		}
		breakup = append(breakup, &lendenPb.AmortizationAmountBreakupComponent{
			Purpose: purpose,
			Amount:  moneyPkg.ParseFloat(d.GetAmount(), moneyPkg.RupeeCurrencyCode),
		})
	}
	status, err := getAmortizationScheduleItemStatus(item.GetStatus())
	if err != nil {
		return nil, errors.Wrap(err, "error getting amortization schedule item status")
	}
	dueDate, err := datetime.ParseStringToDateInLocation(datetime.DATE_LAYOUT_DDMMYYYY, item.GetDueDate(), datetime.IST)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing due date")
	}
	return &lendenPb.AmortizationScheduleItem{
		DueDate:           dueDate,
		DueAmount:         moneyPkg.ParseFloat(item.GetDueAmount(), moneyPkg.RupeeCurrencyCode),
		Breakup:           breakup,
		Status:            status,
		OutstandingAmount: moneyPkg.ParseFloat(item.GetOutstandingAmount(), moneyPkg.RupeeCurrencyCode),
	}, nil
}

func getAmortizationComponentPurpose(purpose string) (lendenPb.AmortizationComponentPurpose, error) {
	purpose = strings.ToUpper(strings.TrimSpace(purpose))
	switch purpose {
	case "PRINCIPAL":
		return lendenPb.AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL, nil
	case "INTEREST":
		return lendenPb.AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_INTEREST, nil
	default:
		return 0, errors.Errorf("unexpected purpose: %s", purpose)
	}
}

func getAmortizationScheduleItemStatus(status string) (lendenPb.AmortizationScheduleItemStatus, error) {
	status = strings.ToUpper(strings.TrimSpace(status))
	switch status {
	case "UPCOMING":
		return lendenPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING, nil
	case "PAID", "PAID_ON_TIME":
		return lendenPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID, nil
	case "PAID_IN_ADVANCE":
		return lendenPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE, nil
	case "DUE":
		return lendenPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE, nil
	case "OVERDUE":
		return lendenPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE, nil
	case "LATE_PAYMENT":
		return lendenPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT, nil
	default:
		return 0, errors.Errorf("unexpected status: %s", status)
	}
}

func (c *GetAmortizationScheduleResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden get amortization schedule API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return &lendenPb.GetAmortizationScheduleResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
