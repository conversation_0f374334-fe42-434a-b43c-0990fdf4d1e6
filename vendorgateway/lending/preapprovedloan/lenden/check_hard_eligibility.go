package lenden

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/text/currency"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	structPb "google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"

	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

// CheckHardEligibilityRequest represents the request structure for checking hard eligibility.
type CheckHardEligibilityRequest struct {
	Req  *lendenPb.CheckHardEligibilityRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

// CheckHardEligibilityResponse represents the response structure for checking hard eligibility.
type CheckHardEligibilityResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *CheckHardEligibilityRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *CheckHardEligibilityRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *CheckHardEligibilityRequest) GetResponse() vendorapi.Response {
	return &CheckHardEligibilityResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

// nolint:dupl
func (c *CheckHardEligibilityRequest) Marshal() ([]byte, error) {
	requestPayload := &vendorLendenPb.CheckHardEligibilityRequest{
		Params: &vendorLendenPb.Params{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Fields: &vendorLendenPb.Fields{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Json: &vendorLendenPb.CheckHardEligibilityRequestPayload{
			ProductId: c.Conf.ProductId,
			LoanId:    c.Req.GetLoanId(),
			UserId:    c.Req.GetUserId(),
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeCheckHardEligibility),
	}

	// Step 2: Marshal the VendorRequest into JSON
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling vendor request: %w", err)
	}

	// Step 3: Encrypt the JSON request
	encryptedReqBytes, err := c.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, fmt.Errorf("error encrypting vendor request: %w", err)
	}

	// Step 4: Return the encrypted bytes
	return encryptedReqBytes, nil
}

func (c *CheckHardEligibilityResponse) Unmarshal(b []byte) (proto.Message, error) {
	encryptedRes := vendorLendenPb.LendenEncryptedResponse{}
	if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, &encryptedRes); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling encrypted response")
	}
	if encryptedRes.GetResponse().GetPayload() == "" {
		return nil, errors.Errorf("empty response received from vendor")
	}
	decryptedResJson, err := c.Decrypt([]byte(encryptedRes.GetResponse().GetPayload()))
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting response")
	}
	wrappedRes := &vendorLendenPb.CheckHardEligibilityResponseWrapper{}
	err = (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(decryptedResJson, wrappedRes)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response")
	}
	responseData := wrappedRes.GetResponse().GetResponseData()
	eligibilityStatus, err := getEligibilityStatusFromResponseString(responseData.GetAction())
	if err != nil {
		return nil, errors.Wrap(err, "error getting eligibility status from action")
	}
	var offers []*lendenPb.P2POfferDetails
	for _, vendorOffer := range responseData.GetOfferData().GetOffers() {
		var allowedRoiValues []float64
		for _, roi := range vendorOffer.GetModifyRoiList() {
			allowedRoiValues = append(allowedRoiValues, roi)
		}
		offer := &lendenPb.P2POfferDetails{
			OfferCode:                vendorOffer.GetOfferCode(),
			Roi:                      vendorOffer.GetRoi(),
			FundingProbability:       vendorOffer.GetFundingProbability(),
			ExpectedTimeToGetFunding: vendorOffer.GetExpectedTimeToGetFunding(),
			IsRecommended:            vendorOffer.GetIsRecommended(),
			ModifyRoiList:            allowedRoiValues,
		}
		offers = append(offers, offer)
	}
	var applicableAmountToTenures []*lendenPb.OfferData_ApplicableAmountToTenure
	for amountRange, tenureList := range responseData.GetOfferData().GetApplicableTenures() {
		values := strings.Split(amountRange, "-")
		var minAmount, maxAmount *moneyPb.Money
		var minAmountErr, maxAmountErr error
		if len(values) == 0 {
			return nil, errors.New("there is no range present in applicable tenures")
		} else if len(values) > 0 {
			minAmount, minAmountErr = parseStringToMoney(values[0])
			if minAmountErr != nil {
				return nil, errors.Wrap(minAmountErr, "minAmount could not be parsed to float32")
			}
			maxAmount = minAmount
		}
		if len(values) > 1 {
			maxAmount, maxAmountErr = parseStringToMoney(values[1])
			if maxAmountErr != nil {
				return nil, errors.Wrap(maxAmountErr, "maxAmountErr could not be parsed to float32")
			}
		}
		isTenureListContinuous, minTenure, maxTenure := getMaxAndMinTenureAndIsTenureContinuous(tenureList.GetValues())
		if !isTenureListContinuous {
			return nil, errors.New("tenure list corresponding to amount range is not continuous")
		}
		applicableAmountToTenure := &lendenPb.OfferData_ApplicableAmountToTenure{
			MinAmount: minAmount,
			MaxAmount: maxAmount,
			MinTenure: minTenure,
			MaxTenure: maxTenure,
		}
		applicableAmountToTenures = append(applicableAmountToTenures, applicableAmountToTenure)
	}
	return &lendenPb.CheckHardEligibilityResponse{
		Status:            rpc.StatusOk(),
		EligibilityStatus: eligibilityStatus,
		OfferData: &lendenPb.OfferData{
			Offers:                 offers,
			OfferSelectionMultiple: responseData.GetOfferData().GetOfferSelectionMultiple(),
			MinAmount:              floatToMoney(responseData.GetOfferData().GetMinAmount()),
			MaxAmount:              floatToMoney(responseData.GetOfferData().GetMaxAmount()),
			ApplicableTenures:      applicableAmountToTenures,
		},
	}, nil
}

func getEligibilityStatusFromResponseString(action string) (lendenPb.EligibilityStatus, error) {
	statusMap := map[string]lendenPb.EligibilityStatus{
		"APPROVED": lendenPb.EligibilityStatus_ELIGIBILITY_STATUS_APPROVED,
		"REJECTED": lendenPb.EligibilityStatus_ELIGIBILITY_STATUS_REJECTED,
		"REJECT":   lendenPb.EligibilityStatus_ELIGIBILITY_STATUS_REJECTED,
		"FAILED":   lendenPb.EligibilityStatus_ELIGIBILITY_STATUS_NEED_MORE_DETAILS_FOR_EVALUATION,
	}
	status, exists := statusMap[action]
	if !exists {
		return lendenPb.EligibilityStatus_ELIGIBILITY_STATUS_UNSPECIFIED, fmt.Errorf("unknown action status: %s", action)
	}
	return status, nil
}

func (c *CheckHardEligibilityResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden check hard eligibility API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	if httpStatus >= 400 && httpStatus < 500 {
		encryptedRes := vendorLendenPb.LendenEncryptedResponse{}
		encryptedResUnmarshallErr := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, &encryptedRes)
		if encryptedResUnmarshallErr != nil {
			logger.Error(ctx, "error unmarshalling encrypted res", zap.Error(encryptedResUnmarshallErr))
		}
		wrappedRes := &vendorLendenPb.CreateUserResponseWrapper{}
		wrappedResUnmarshallErr := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, wrappedRes)
		if wrappedResUnmarshallErr != nil {
			logger.Error(ctx, "error unmarshalling wrapped res", zap.Error(wrappedResUnmarshallErr))
		}
		if encryptedRes.GetResponse().GetPayload() != "" {
			decryptedResJson, err := c.Decrypt([]byte(encryptedRes.GetResponse().GetPayload()))
			if err != nil {
				return nil, errors.Wrap(err, "error decrypting response")
			}
			err = (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(decryptedResJson, wrappedRes)
			if err != nil {
				return nil, errors.Wrap(err, "error unmarshalling response")
			}
		}
		switch wrappedRes.GetResponse().GetMessage() {
		case "Mandatory event, BANK_STATEMENT is missing":
			return &lendenPb.CheckHardEligibilityResponse{
				Status:            rpc.StatusOk(),
				EligibilityStatus: lendenPb.EligibilityStatus_ELIGIBILITY_STATUS_NEED_MORE_DETAILS_FOR_EVALUATION,
			}, nil
		case MessageCodeFetchBureauFailed.String():
			return &lendenPb.CheckHardEligibilityResponse{
				Status: rpc.StatusInternalWithDebugMsg(wrappedRes.GetResponse().GetMessage()),
			}, nil
		default:
			return &lendenPb.CheckHardEligibilityResponse{Status: rpc.StatusInternalWithDebugMsg(wrappedRes.GetResponse().GetMessage())}, nil
		}
	}
	return &lendenPb.CheckHardEligibilityResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}

func floatToMoney(moneyFloat float64) *moneyPb.Money {
	return moneyPkg.ParseFloat(moneyFloat, currency.INR.String())
}

func getMaxAndMinTenureAndIsTenureContinuous(tenureList []*structPb.Value) (bool, int32, int32) {
	n := int32(len(tenureList))
	if n == 0 {
		return false, 0, 0
	}

	minTenure := int32(tenureList[0].GetNumberValue())
	maxTenure := int32(tenureList[n-1].GetNumberValue())

	if maxTenure-minTenure != n-1 {
		return false, minTenure, maxTenure
	}
	return true, minTenure, maxTenure
}

func parseStringToMoney(amountStr string) (*moneyPb.Money, error) {
	// Remove any whitespace
	amountStr = strings.TrimSpace(amountStr)

	// Convert string to float64
	amountFloat, parseErr := strconv.ParseFloat(amountStr, 64)
	if parseErr != nil {
		return nil, errors.Wrapf(parseErr, "error parsing string to float: %s", amountStr)
	}

	// Separate the integer and fractional parts
	integerPart := int64(amountFloat)
	fractionalPart := int32(math.Round((amountFloat - float64(integerPart)) * 1000000000)) // Since 1 nano is 10^-9

	// Create and return the Google Money object
	return &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        integerPart,
		Nanos:        fractionalPart,
	}, nil
}
