package lenden

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"

	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

type GetForeclosureDetailsRequest struct {
	Req  *lendenPb.GetForeclosureDetailsRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

type GetForeclosureDetailsResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *GetForeclosureDetailsRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *GetForeclosureDetailsRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *GetForeclosureDetailsRequest) GetResponse() vendorapi.Response {
	return &GetForeclosureDetailsResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

func (c *GetForeclosureDetailsRequest) Marshal() ([]byte, error) {
	if c.Req.GetLoanId() == "" {
		return nil, errors.New("loan_id is mandatory and must be a non-empty string")
	}
	purposeValue, err := getPurposeValue(c.Req.GetPurpose())
	if err != nil {
		return nil, errors.Wrap(err, "error getting purpose value")
	}
	requestPayload := &vendorLendenPb.GetForeclosureDetailsRequest{
		Params: &vendorLendenPb.Params{},
		Fields: &vendorLendenPb.Fields{},
		Json: &vendorLendenPb.GetForeclosureDetailsRequestPayload{
			LoanId:       c.Req.GetLoanId(),
			ProductId:    c.Conf.ProductId,
			Purpose:      purposeValue,
			OriginSystem: OriginSystemPartner,
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeGetForeclosureDetails),
	}
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, errors.Wrap(err, "error marshaling vendor request")
	}
	encryptedReqBytes, err := c.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, errors.Wrap(err, "error encrypting vendor request")
	}
	return encryptedReqBytes, nil
}

func getPurposeValue(purpose lendenPb.GetForeclosureDetailsRequest_Purpose) (string, error) {
	switch purpose {
	case lendenPb.GetForeclosureDetailsRequest_PURPOSE_FORECLOSURE:
		return "FORECLOSURE", nil
	case lendenPb.GetForeclosureDetailsRequest_PURPOSE_COOL_OFF:
		return "COOL-OFF", nil
	default:
		return "", errors.Errorf("unexpected purpose: %s", purpose)
	}
}

func (c *GetForeclosureDetailsResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}
	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response body")
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}
	decryptedBytes, err := c.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting response")
	}
	responseWithWrapper := vendorLendenPb.GetForeclosureDetailsResponseWrapper{}
	if err = unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response")
	}

	if responseWithWrapper.GetResponse().GetMessageCode() == MessageCodeLoanCoolOffNotAvailable.String() {
		return &lendenPb.GetForeclosureDetailsResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("loan cool off not available"),
		}, nil
	}

	return &lendenPb.GetForeclosureDetailsResponse{
		Status:             rpc.StatusOk(),
		ForeclosureDetails: getForeclosureDetails(responseWithWrapper.GetResponse().GetResponseData()),
	}, nil
}

func getForeclosureDetails(details *vendorLendenPb.ForeclosureDetails) *lendenPb.ForeclosureDetails {
	return &lendenPb.ForeclosureDetails{
		PrincipalOutstanding:    moneyPkg.ParseFloat(details.GetPrincipalOutstanding(), moneyPkg.RupeeCurrencyCode),
		InterestOutstanding:     moneyPkg.ParseFloat(details.GetInterestOutstanding(), moneyPkg.RupeeCurrencyCode),
		DelayChargesOutstanding: moneyPkg.ParseFloat(details.GetDelayOutstanding(), moneyPkg.RupeeCurrencyCode),
		LateFeeOutstanding:      moneyPkg.ParseFloat(details.GetLateFeeOutstanding(), moneyPkg.RupeeCurrencyCode),
		ForeclosureAmount:       moneyPkg.ParseFloat(details.GetForeclosureAmount(), moneyPkg.RupeeCurrencyCode),
		ForeclosureCharges:      moneyPkg.ParseFloat(details.GetForeclosureCharges(), moneyPkg.RupeeCurrencyCode),
		RepaymentReceived:       moneyPkg.ParseFloat(details.GetRepaymentReceived(), moneyPkg.RupeeCurrencyCode),
	}
}

func (c *GetForeclosureDetailsResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden get foreclosure details API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	if httpStatus >= 400 && httpStatus < 500 {
		wrappedRes := &vendorLendenPb.GetForeclosureDetailsResponseWrapper{}
		err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, wrappedRes)
		if err != nil {
			return nil, errors.Wrap(err, "error unmarshalling response")
		}
		switch wrappedRes.GetResponse().GetMessageCode() {
		case MessageCodeForeclosureNotAllowedMessage.String():
			return &lendenPb.GetForeclosureDetailsResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg(wrappedRes.GetResponse().GetMessage())}, nil
		case MessageInvalidLoanStatus.String():
			return &lendenPb.GetForeclosureDetailsResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.GetForeclosureDetailsResponse_INVALID_LOAN_STATUS), wrappedRes.GetResponse().GetMessage()),
			}, nil
		default:
			return &lendenPb.GetForeclosureDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(wrappedRes.GetResponse().GetMessage())}, nil
		}
	}
	return &lendenPb.GetForeclosureDetailsResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
