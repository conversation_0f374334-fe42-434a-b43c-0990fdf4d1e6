// nolint: gocritic
package deposit

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	pkgDeposit "github.com/epifi/gamma/pkg/deposit"
	"github.com/epifi/gamma/pkg/deposit/interest_rates"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/jonboulle/clockwork"
	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	accountsPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/frontend/fcm"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	panPb "github.com/epifi/gamma/api/pan"
	types "github.com/epifi/gamma/api/typesv2"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/deposit/config"
	"github.com/epifi/gamma/pkg/downtime"
)

const (
	locationIndia              = "Asia/Calcutta"
	pageSize                   = 30
	bonusTemplateExtraInterest = "3"
	depositAccountId           = "depositAccountId"
)

var (
	// TODO(harish): come up with some concrete way of fetching requestID prefix
	depositTypeToRequestPrefixMap = map[accountsPb.Type]string{
		accountsPb.Type_FIXED_DEPOSIT:     "NEOLIFDO",
		accountsPb.Type_RECURRING_DEPOSIT: "NEOLIRDO",
		accountsPb.Type_SMART_DEPOSIT:     "NEOLIFRD",
	}

	depositTypeToOrderTagMap = map[accountsPb.Type]orderPb.OrderTag{
		accountsPb.Type_FIXED_DEPOSIT:     orderPb.OrderTag_FD,
		accountsPb.Type_RECURRING_DEPOSIT: orderPb.OrderTag_RD,
		accountsPb.Type_SMART_DEPOSIT:     orderPb.OrderTag_SD,
	}

	TaxSavingSchemes = map[commonvgpb.Vendor][]depositPb.DepositScheme{
		commonvgpb.Vendor_FEDERAL_BANK: {
			depositPb.DepositScheme_FD_TAX_SAVING_ON_MATURITY,
			depositPb.DepositScheme_FD_TAX_SAVING,
			depositPb.DepositScheme_FD_TAX_SAVING_QUARTERLY_INTEREST,
			depositPb.DepositScheme_FD_TAX_SAVING_MONTHLY_INTEREST,
		},
	}

	depositToVgRenewOptionsMap = map[depositPb.RenewOption]vgDepositPb.RenewOption{
		depositPb.RenewOption_FIXED_AMOUNT:             vgDepositPb.RenewOption_FIXED_AMOUNT,
		depositPb.RenewOption_PRINCIPLE_EXTRA:          vgDepositPb.RenewOption_PRINCIPLE_EXTRA,
		depositPb.RenewOption_PRINCIPLE_ONLY:           vgDepositPb.RenewOption_PRINCIPLE_ONLY,
		depositPb.RenewOption_MATURITY_ONLY:            vgDepositPb.RenewOption_MATURITY_ONLY,
		depositPb.RenewOption_RENEW_OPTION_UNSPECIFIED: vgDepositPb.RenewOption_RENEW_OPTION_UNSPECIFIED,
	}

	// Commented out successive interest rates which are same to avoid showing redundant tile in the app
	//nolint:dupl
	newNonSeniorCitizenInterestRates = map[commonvgpb.Vendor]map[accountsPb.Type][]*types.DepositInterestDetails{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_SMART_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.00", Term: &types.DepositTerm{Days: 28, Months: 0}}, // original is 7 days (federal)
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "4.50", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 91, Months: 0}},
				{InterestRate: "6.00", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.25", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "6.60", Term: &types.DepositTerm{Days: 0, Months: 12}},
			},
			accountsPb.Type_FIXED_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.00", Term: &types.DepositTerm{Days: 7, Months: 0}},
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "4.50", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 91, Months: 0}},
				{InterestRate: "6.00", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.25", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "6.60", Term: &types.DepositTerm{Days: 0, Months: 12}},
				{InterestRate: "6.80", Term: &types.DepositTerm{Days: 1, Months: 12}},
				//{InterestRate: "7.20", Term: &types.DepositTerm{Days: 0, Months: 13}},
				{InterestRate: "7.00", Term: &types.DepositTerm{Days: 444, Months: 0}},
				{InterestRate: "6.80", Term: &types.DepositTerm{Days: 445, Months: 0}},
				{InterestRate: "7.00", Term: &types.DepositTerm{Days: 710, Months: 0}},
				{InterestRate: "6.80", Term: &types.DepositTerm{Days: 711, Months: 0}},
				{InterestRate: "6.90", Term: &types.DepositTerm{Days: 1, Months: 24}},
				{InterestRate: "6.50", Term: &types.DepositTerm{Days: 0, Months: 60}},
			},
		},
	}

	//nolint:dupl
	nonSeniorCitizenInterestRates = map[commonvgpb.Vendor]map[accountsPb.Type][]*types.DepositInterestDetails{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_SMART_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.00", Term: &types.DepositTerm{Days: 28, Months: 0}}, // original is 7 days (federal)
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "4.50", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 91, Months: 0}},
				{InterestRate: "6.00", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.25", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "6.75", Term: &types.DepositTerm{Days: 0, Months: 12}},
			},
			accountsPb.Type_FIXED_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.00", Term: &types.DepositTerm{Days: 7, Months: 0}},
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "4.50", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 91, Months: 0}},
				{InterestRate: "6.00", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.25", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "6.75", Term: &types.DepositTerm{Days: 0, Months: 12}},
				{InterestRate: "6.80", Term: &types.DepositTerm{Days: 1, Months: 12}},
				//{InterestRate: "7.20", Term: &types.DepositTerm{Days: 0, Months: 13}},
				{InterestRate: "7.15", Term: &types.DepositTerm{Days: 444, Months: 0}},
				{InterestRate: "6.80", Term: &types.DepositTerm{Days: 445, Months: 0}},
				{InterestRate: "7.10", Term: &types.DepositTerm{Days: 710, Months: 0}},
				{InterestRate: "6.80", Term: &types.DepositTerm{Days: 711, Months: 0}},
				{InterestRate: "6.90", Term: &types.DepositTerm{Days: 1, Months: 24}},
				{InterestRate: "6.50", Term: &types.DepositTerm{Days: 0, Months: 60}},
			},
		},
	}

	// Commented out successive interest rates which are same to avoid showing redundant tile in the app
	//nolint:dupl
	newSeniorCitizenInterestRates = map[commonvgpb.Vendor]map[accountsPb.Type][]*types.DepositInterestDetails{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_SMART_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 28, Months: 0}}, // original is 7 days (federal)
				{InterestRate: "4.00", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "5.50", Term: &types.DepositTerm{Days: 91, Months: 0}},
				{InterestRate: "6.50", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.75", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "7.10", Term: &types.DepositTerm{Days: 0, Months: 12}},
			},
			accountsPb.Type_FIXED_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 7, Months: 0}},
				{InterestRate: "4.00", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "7.00", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.75", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "7.25", Term: &types.DepositTerm{Days: 0, Months: 12}},
				{InterestRate: "7.30", Term: &types.DepositTerm{Days: 1, Months: 12}},
				{InterestRate: "7.50", Term: &types.DepositTerm{Days: 444, Months: 0}},
				{InterestRate: "7.30", Term: &types.DepositTerm{Days: 445, Months: 0}},
				{InterestRate: "7.50", Term: &types.DepositTerm{Days: 710, Months: 0}},
				{InterestRate: "7.30", Term: &types.DepositTerm{Days: 711, Months: 0}},
				{InterestRate: "7.40", Term: &types.DepositTerm{Days: 1, Months: 24}},
				{InterestRate: "7.00", Term: &types.DepositTerm{Days: 0, Months: 60}},
			},
		},
	}

	//nolint:dupl
	seniorCitizenInterestRates = map[commonvgpb.Vendor]map[accountsPb.Type][]*types.DepositInterestDetails{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_SMART_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 28, Months: 0}}, // original is 7 days (federal)
				{InterestRate: "4.00", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "5.50", Term: &types.DepositTerm{Days: 91, Months: 0}},
				{InterestRate: "6.50", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.75", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "7.25", Term: &types.DepositTerm{Days: 0, Months: 12}},
			},
			accountsPb.Type_FIXED_DEPOSIT: []*types.DepositInterestDetails{
				{InterestRate: "3.50", Term: &types.DepositTerm{Days: 7, Months: 0}},
				{InterestRate: "4.00", Term: &types.DepositTerm{Days: 30, Months: 0}},
				{InterestRate: "5.00", Term: &types.DepositTerm{Days: 46, Months: 0}},
				{InterestRate: "7.00", Term: &types.DepositTerm{Days: 181, Months: 0}},
				{InterestRate: "6.75", Term: &types.DepositTerm{Days: 271, Months: 0}},
				{InterestRate: "7.25", Term: &types.DepositTerm{Days: 0, Months: 12}},
				{InterestRate: "7.30", Term: &types.DepositTerm{Days: 1, Months: 12}},
				//{InterestRate: "7.70", Term: &types.DepositTerm{Days: 0, Months: 13}},
				{InterestRate: "7.65", Term: &types.DepositTerm{Days: 444, Months: 0}},
				{InterestRate: "7.30", Term: &types.DepositTerm{Days: 445, Months: 0}},
				{InterestRate: "7.60", Term: &types.DepositTerm{Days: 710, Months: 0}},
				{InterestRate: "7.30", Term: &types.DepositTerm{Days: 711, Months: 0}},
				{InterestRate: "7.40", Term: &types.DepositTerm{Days: 1, Months: 24}},
				{InterestRate: "7.00", Term: &types.DepositTerm{Days: 0, Months: 60}},
			},
		},
	}
	minCreationAmountMap = map[commonvgpb.Vendor]map[accountsPb.Type]map[depositPb.DepositAccountProvenance]*moneyPb.Money{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_FIXED_DEPOSIT: {
				depositPb.DepositAccountProvenance_REWARDS_APP: {CurrencyCode: "INR", Units: 1000, Nanos: 0},
				depositPb.DepositAccountProvenance_USER_APP:    {CurrencyCode: "INR", Units: 1000, Nanos: 0},
				// add as of now for 1000 as minimum limit, will update when product comes with clarity
				depositPb.DepositAccountProvenance_CREDIT_CARD: {CurrencyCode: "INR", Units: 1000, Nanos: 0},
			},
			accountsPb.Type_SMART_DEPOSIT: {
				depositPb.DepositAccountProvenance_REWARDS_APP: {CurrencyCode: "INR", Units: 100, Nanos: 0},
				depositPb.DepositAccountProvenance_USER_APP:    {CurrencyCode: "INR", Units: 300, Nanos: 0},
			},
		},
	}
	maxCreationAmountMap50K = map[commonvgpb.Vendor]map[accountsPb.Type]*moneyPb.Money{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_FIXED_DEPOSIT: {CurrencyCode: "INR", Units: 1000000, Nanos: 0},
			accountsPb.Type_SMART_DEPOSIT: {CurrencyCode: "INR", Units: 50000, Nanos: 0},
		},
	}

	maxCreationAmountMap = map[commonvgpb.Vendor]map[accountsPb.Type]*moneyPb.Money{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_FIXED_DEPOSIT: {CurrencyCode: "INR", Units: 1000000, Nanos: 0},
			accountsPb.Type_SMART_DEPOSIT: {CurrencyCode: "INR", Units: 200000, Nanos: 0},
		},
	}

	defaultDepositTerm = map[commonvgpb.Vendor]map[accountsPb.Type]*types.DepositTerm{
		commonvgpb.Vendor_FEDERAL_BANK: {
			accountsPb.Type_FIXED_DEPOSIT: &types.DepositTerm{Days: 0, Months: 15},
			accountsPb.Type_SMART_DEPOSIT: &types.DepositTerm{Days: 0, Months: 12},
		},
	}

	activeSDNumberUpperLimit = 10

	minKycMaxSavingsBalance = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        100000,
		Nanos:        0,
	}
)

func getInterestRatesFromCode(isSeniorCitizen bool, depositType accountsPb.Type, vendor commonvgpb.Vendor) []*types.DepositInterestDetails {
	// TODO(mounish): move to config
	// TODO(mounish): This approach doesn't work if we have multiple vendors with different interest update dates
	newTime := time.Date(2025, 06, 02, 0, 0, 0, 0, datetime.IST)
	if time.Now().In(datetime.IST).After(newTime) {
		if !isSeniorCitizen {
			return newNonSeniorCitizenInterestRates[vendor][depositType]
		} else {
			return newSeniorCitizenInterestRates[vendor][depositType]
		}
	} else {
		if !isSeniorCitizen {
			return nonSeniorCitizenInterestRates[vendor][depositType]
		} else {
			return seniorCitizenInterestRates[vendor][depositType]
		}
	}
}

func convertDepositToVGRenewOptions(renewOptions depositPb.RenewOption) (vgDepositPb.RenewOption, error) {
	if vgRenewInfo, ok := depositToVgRenewOptionsMap[renewOptions]; ok {
		return vgRenewInfo, nil
	}
	return vgDepositPb.RenewOption_RENEW_OPTION_UNSPECIFIED,
		fmt.Errorf("failed to map renew info from deposit to VG, deposit RenewOptions: %v", renewOptions)
}

// getInterestRateForTerm gets the interest rate applicable on the deposit account for a vendor, deposit type, age, and
// deposit term
func (s *Service) getInterestRateForTerm(ctx context.Context, vendor commonvgpb.Vendor, accountType accountsPb.Type,
	term *types.DepositTerm, isSeniorCitizen bool) (string, error) {
	var (
		interestRates []*types.DepositInterestDetails
	)
	interestRates, err := s.getInterestRates(ctx, isSeniorCitizen, accountType, vendor, nil)
	if err != nil {
		return "", errors.New("failed to get interest rates")
	}
	return interest_rates.GetInterestRateForTerm(ctx, term, interestRates)
}

// getCurrentMonthStartingTime helper function which returns the starting timestamp for the current month
func getCurrentMonthStartingTime() (*timestamp.Timestamp, error) {
	year, month, _ := time.Now().Date()
	// Location being set as India
	loc, err := time.LoadLocation(locationIndia)
	if err != nil {
		return nil, fmt.Errorf("failed to get Indian Standard Time location")
	}

	currMonthStartingTime, err := ptypes.TimestampProto(time.Date(year, month, 1, 0, 0, 0, 0, loc))
	if err != nil {
		return nil, fmt.Errorf("failed to convert time to Proto time: %w", err)
	}
	return currMonthStartingTime, nil
}

func generateRequestId(vendor commonvgpb.Vendor, prefix string) (string, error) {
	switch vendor {
	case commonvgpb.Vendor_FEDERAL_BANK:
		return idgen.FederalRandomSequence(prefix, 5), nil
	default:
		return "", fmt.Errorf("invalid vendor: %v", vendor)
	}
}

// getDefaultMinDepositAmount returns min deposit amount needed for a given vendor and accountType
func getDefaultMinDepositAmount(vendor commonvgpb.Vendor, accountType accountsPb.Type, depositProvenance depositPb.DepositAccountProvenance, templateDetails *config.DepositTemplate) (*moneyPb.Money, error) {
	vendorMinCreationAmountMap, ok := minCreationAmountMap[vendor]
	if !ok {
		return nil, fmt.Errorf("no min deposit creation amount found for vendor %s", vendor)
	}

	minAmount, ok := vendorMinCreationAmountMap[accountType][depositProvenance]
	if !ok {
		return nil, fmt.Errorf("no min deposit creation amount found for vendor %s and accountType %s", vendor, accountType)
	}

	if templateDetails != nil && len(templateDetails.MinAmount) > 0 {
		logger.DebugNoCtx(fmt.Sprintf("templatedetails min amount %v", templateDetails.MinAmount))
		templateMinAmount, err := money.ParseString(templateDetails.MinAmount, money.RupeeCurrencyCode)
		if err != nil {
			return nil, err
		}
		// if template has a min amount set, use that.
		return templateMinAmount, nil
	}

	return minAmount, nil
}

func (s *Service) getIncrementalPurchaseAmount(templateDetails *config.DepositTemplate) (*moneyPb.Money, error) {
	if templateDetails == nil || len(templateDetails.IncrementalPurchaseAmount) == 0 {
		return nil, nil
	}
	logger.DebugNoCtx(fmt.Sprintf("incrementalPurchaseAmount :%v", templateDetails.IncrementalPurchaseAmount))
	incrementalPurchaseAmount, err := money.ParseString(templateDetails.IncrementalPurchaseAmount, money.RupeeCurrencyCode)
	if err != nil {
		return nil, err
	}
	return incrementalPurchaseAmount, nil
}

func (s *Service) getTemplateDetailsFromTemplateID(ctx context.Context, accountType accountsPb.Type, templateID string) *config.DepositTemplate {
	if len(templateID) == 0 {
		return nil
	}
	switch accountType {
	case accountsPb.Type_SMART_DEPOSIT:
		for _, sdTemplate := range s.sdTemplates {
			if strings.Compare(templateID, sdTemplate.Id) == 0 {
				return sdTemplate
			}
		}
	case accountsPb.Type_FIXED_DEPOSIT:
		for _, fdTemplate := range s.fdTemplates {
			if strings.Compare(templateID, fdTemplate.Id) == 0 {
				return fdTemplate
			}
		}
	}
	return nil
}

func (s *Service) getDefaultMaturityAction(accountType accountsPb.Type, templateDetails *config.DepositTemplate) depositPb.MaturityAction {
	// maturity action is only applicable for fixed deposits
	if accountType != accountsPb.Type_FIXED_DEPOSIT {
		return depositPb.MaturityAction_MATURITY_ACTION_UNSPECIFIED
	}

	// default to auto-renew if the template doesn't have a default maturity action set
	if templateDetails == nil || templateDetails.DefaultMaturityAction.ToEnum() == depositPb.MaturityAction_MATURITY_ACTION_UNSPECIFIED {
		return depositPb.MaturityAction_MATURITY_ACTION_AUTO_RENEW
	}

	return templateDetails.DefaultMaturityAction.ToEnum()
}

// getDefaultMaxDepositAmount returns max deposit amount for a given vendor and accountType
func getDefaultMaxDepositAmount(ctx context.Context, vendor commonvgpb.Vendor, accountType accountsPb.Type, s *Service, actorId string) (*moneyPb.Money, error) {
	vendorMaxCreationAmountMap, ok := maxCreationAmountMap50K[vendor]

	isAllowedGroup, err := s.isFeatureEnabledUserGroup(ctx, s.config.SdTwoLakhLimit().AllowedUserGroups(), actorId)

	if err == nil {
		if s.config.SdTwoLakhLimit().Enable() && isAllowedGroup {
			vendorMaxCreationAmountMap, ok = maxCreationAmountMap[vendor]
		}
	}

	if !ok {
		return nil, fmt.Errorf("no max deposit creation amount found for vendor %s", vendor)
	}

	maxAmount, ok := vendorMaxCreationAmountMap[accountType]
	if !ok {
		return nil, fmt.Errorf("no max deposit creation amount found for vendor %s and accountType %s", vendor, accountType)
	}

	return maxAmount, nil
}

// getDetailedStatusForSystemErr returns detailed status for epifi system errors while processing the transactions
func getDetailedStatusForSystemErr(err error) *depositPb.DepositRequestDetailedStatus_DetailedStatus {
	return &depositPb.DepositRequestDetailedStatus_DetailedStatus{
		SystemErrorDescription: err.Error(),
		ErrorCategory:          depositPb.DepositRequestDetailedStatus_DetailedStatus_SYSTEM_ERROR,
	}
}

// AppendDetailedStatus appends detailed status to deposit request detailed status list.
// If the list is nil, will create a new list and append the detailed status to that list.
// If the list already contains statuses, will append the new detailed status to the list.
func AppendDetailedStatus(
	depositRequestDetailedStatus *depositPb.DepositRequestDetailedStatus,
	detailedStatus *depositPb.DepositRequestDetailedStatus_DetailedStatus,
) *depositPb.DepositRequestDetailedStatus {
	if detailedStatus == nil {
		return depositRequestDetailedStatus
	}

	if depositRequestDetailedStatus == nil {
		return &depositPb.DepositRequestDetailedStatus{
			DetailedStatusList: []*depositPb.DepositRequestDetailedStatus_DetailedStatus{
				detailedStatus,
			},
		}
	}

	if depositRequestDetailedStatus.DetailedStatusList != nil {
		detailedStatusList := append(depositRequestDetailedStatus.DetailedStatusList, detailedStatus)
		return &depositPb.DepositRequestDetailedStatus{
			DetailedStatusList: detailedStatusList,
		}
	} else {
		return &depositPb.DepositRequestDetailedStatus{
			DetailedStatusList: []*depositPb.DepositRequestDetailedStatus_DetailedStatus{
				detailedStatus,
			},
		}
	}
}

func (s *Service) convertToVGNomineeDetails(ctx context.Context, nomineeDetails *depositPb.DepositNomineeDetails, actorId string) (*vgDepositPb.DepositNomineeDetails, error) {
	var guardianCityCode, guardianStateCode string

	if nomineeDetails == nil || nomineeDetails.NomineeInfoList == nil {
		return nil, nil
	}

	vgNomineeDetails := &vgDepositPb.DepositNomineeDetails{
		NomineeInfoList: make([]*vgDepositPb.DepositNomineeDetails_DepositNomineeInfo, 0),
	}
	for idx := range nomineeDetails.NomineeInfoList {
		nominee, err := s.getNomineeById(ctx, nomineeDetails.GetNomineeInfoList()[idx].GetNomineeId(), actorId)
		if err != nil {
			return nil, fmt.Errorf("cannot convert to vg nominee details as failed to fetch nominee, %w", err)
		}
		nomineeCityCode, nomineeStateCode, err := s.getVendorGeoAreaCodes(ctx, commonvgpb.Vendor_FEDERAL_BANK, nominee.GetContactInfo().GetAddress().GetLocality(),
			nominee.GetContactInfo().GetAddress().GetAdministrativeArea())
		if err != nil {
			return nil, fmt.Errorf("failed to get vendor specific city state code %w", err)
		}

		if nominee.GetGuardianInfo() != nil && nominee.GetGuardianInfo().GetContactInfo() != nil && nominee.GetGuardianInfo().GetContactInfo().GetAddress() != nil {
			guardianCityCode, guardianStateCode, err = s.getVendorGeoAreaCodes(ctx, commonvgpb.Vendor_FEDERAL_BANK, nominee.GetGuardianInfo().GetContactInfo().GetAddress().GetLocality(),
				nominee.GetGuardianInfo().GetContactInfo().GetAddress().GetAdministrativeArea())
			if err != nil {
				return nil, fmt.Errorf("failed to get vendor specific city state code for guardian, %w", err)
			}
		}

		vgNominee := &vgDepositPb.DepositNomineeDetails_DepositNomineeInfo{
			Nominee:         nominee,
			PercentageShare: nomineeDetails.GetNomineeInfoList()[idx].GetPercentageShare(),
			NomineeCityStateCode: &vgDepositPb.DepositNomineeDetails_DepositNomineeInfo_CityStateCode{
				CityCode:  nomineeCityCode,
				StateCode: nomineeStateCode,
			},
		}
		if nominee.GetGuardianInfo() != nil {
			vgNominee.GuardianCityStateCode = &vgDepositPb.DepositNomineeDetails_DepositNomineeInfo_CityStateCode{
				CityCode:  guardianCityCode,
				StateCode: guardianStateCode,
			}
		}

		vgNomineeDetails.NomineeInfoList = append(vgNomineeDetails.NomineeInfoList, vgNominee)
	}
	return vgNomineeDetails, nil
}

func convertAccountTypeToString(accountType accountsPb.Type) string {
	switch accountType {
	case accountsPb.Type_SMART_DEPOSIT:
		return "SD"
	case accountsPb.Type_FIXED_DEPOSIT:
		return "FD"
	default:
		return accountType.String()
	}
}

// setPageTokens sets before and after page token value in the response.
// it calculates before and after token after traversing the slice of deposit requests
// nolint: funlen
func setPageTokens(ctx context.Context, res *depositPb.GetDepositRequestsForActorResponse, depositRequests []*depositPb.DepositRequest,
	prevBeforeToken *beforePageToken, prevAfterToken *afterPageToken) error {
	var (
		err         error
		beforeToken = &beforePageToken{}
		before      string
		afterToken  = &afterPageToken{}
		after       string
	)

	// reversed deposit requests
	reverseDepositRequests := funk.Reverse(depositRequests).([]*depositPb.DepositRequest)

	switch {
	// In case prevAfterToken is not nil, the deposit requests are in ASCENDING time-ordered sequence.
	case prevAfterToken != nil:
		// assigning prev token ensures carry forward in case of no activities in current page.
		*afterToken = *prevAfterToken
		beforeToken.FirstDepositRequestTimeStamp, beforeToken.Offset = getFirstOccurrenceWithOffset(depositRequests)
		tm, offset := getFirstOccurrenceWithOffset(reverseDepositRequests)
		// set only if non-nil as nil represents no activities present
		if tm != nil {
			afterToken.LastDepositRequestTimestamp = tm
			afterToken.Offset = offset
			// ensures offset is carry forwarded
			// e.g. prev page had 5 deposit requests with timestamp tm and now current page had 15 deposit requests
			// in the same tm timestamp so for next query offset should be 20
			if tm.AsTime().Equal(prevAfterToken.LastDepositRequestTimestamp.AsTime()) {
				afterToken.Offset += prevAfterToken.Offset
			}
		}

	// In case prevBeforeToken is not nil, the activities are in DESCENDING time-ordered sequence.
	case prevBeforeToken != nil:
		// assigning prev token ensures carry forward in case of no activities in current page.
		*beforeToken = *prevBeforeToken
		afterToken.LastDepositRequestTimestamp, afterToken.Offset = getFirstOccurrenceWithOffset(depositRequests)
		tm, offset := getFirstOccurrenceWithOffset(reverseDepositRequests)
		// set only if non-nil as nil represents no activities found
		if tm != nil {
			beforeToken.FirstDepositRequestTimeStamp = tm
			beforeToken.Offset = offset
			// ensures offset is carry forwarded
			// e.g. prev page had 5 activities with timestamp tm and now current page had 15 activities in the same tm timestamp
			// so for next query offset should be 20
			if tm.AsTime().Equal(prevBeforeToken.FirstDepositRequestTimeStamp.AsTime()) {
				beforeToken.Offset += prevBeforeToken.Offset
			}
		}

	// If both are nil then FetchLatestPage boolean flag must be true
	// Hence activities contains latest activities in DESCENDING time-ordered sequence from current timestamp
	default:
		afterToken.LastDepositRequestTimestamp, afterToken.Offset = getFirstOccurrenceWithOffset(depositRequests)
		beforeToken.FirstDepositRequestTimeStamp, beforeToken.Offset = getFirstOccurrenceWithOffset(reverseDepositRequests)
	}

	before, err = beforeToken.MarshalToken()
	if err != nil {
		return fmt.Errorf("failed to generate before token for current page: %w", err)
	}

	after, err = afterToken.MarshalToken()
	if err != nil {
		return fmt.Errorf("failed to generate after token for current page: %w", err)
	}

	res.BeforePageToken = before
	res.AfterPageToken = after
	return nil
}

// getFirstOccurrenceWithOffset returns first deposit request timestamp and count of deposit requests whose
// timestamp == first event timestamp
func getFirstOccurrenceWithOffset(depositRequests []*depositPb.DepositRequest) (*timestamp.Timestamp, int32) {
	var (
		firstFound          bool
		firstTimeStamp      *timestamp.Timestamp
		firstTimeStampCount int32
	)

	for _, depositRequest := range depositRequests {
		if !firstFound {
			firstFound = true
			firstTimeStamp = depositRequest.GetCreatedAt()
			firstTimeStampCount++
			continue
		}

		if depositRequest.GetCreatedAt().AsTime().Equal(firstTimeStamp.AsTime()) {
			firstTimeStampCount++
		} else {
			break
		}
	}
	return firstTimeStamp, firstTimeStampCount
}

func convertToFCMIconAttribute(iconAttr *config.IconAttribute) *fcm.IconAttributes {
	if iconAttr == nil {
		return nil
	}

	return &fcm.IconAttributes{
		IconUrl:         iconAttr.IconURL,
		IconName:        iconAttr.IconName,
		BackgroundColor: iconAttr.ColourCode,
	}
}

// initFcmNotificationWithCommonFields initializes fcm notification with common fields based on notification type
func initFcmNotificationWithCommonFields(commonFields *fcm.CommonTemplateFields, notificationType fcm.NotificationType) *fcm.Notification {
	notification := &fcm.Notification{}

	switch notificationType { //nolint:exhaustive
	case fcm.NotificationType_IN_APP:
		notification.NotificationTemplates = &fcm.Notification_InAppTemplate{InAppTemplate: &fcm.InAppTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_FULL_SCREEN:
		notification.NotificationTemplates = &fcm.Notification_FullscreenTemplate{FullscreenTemplate: &fcm.FullScreenTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_BACKGROUND:
		notification.NotificationTemplates = &fcm.Notification_BackgroundTemplate{BackgroundTemplate: &fcm.BackgroundTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_SYSTEM_TRAY:
		notification.NotificationTemplates = &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{CommonTemplateFields: commonFields}}
	}

	notification.NotificationType = notificationType
	return notification
}

// ConvertToValidDepositTerm converts the maturity date given into a valid deposit term
// Deposit duration is calculated assuming deposit is made on current date.
// A valid deposit term will always have days less than equal to 30 and any number of months
// nolint: unused
func ConvertToValidDepositTerm(depositMaturityDate time.Time) (*types.DepositTerm, error) {
	timezone := depositMaturityDate.Location()
	currDate := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, timezone)

	if depositMaturityDate.Before(currDate) {
		return nil, fmt.Errorf("deposit term has negative values, cannot be converted")
	}

	validMonths := 0
	justPrevDate := currDate
	nextDate := justPrevDate.AddDate(0, 1, 0)
	// looping over to get the date with the same day as today, just before the maturity date
	// eg: today: 20 Oct 2020, maturityDate: 24 Oct 2022, -> justPrevDate: 20 Oct 2022
	// eg: today: 20 Oct 2020, maturityDate: 14 Oct 2022, -> justPrevDate: 20 Sep 2022
	// this will help in identifying the number of months needed to reach maturity date from today
	for !nextDate.After(depositMaturityDate) {
		validMonths++
		justPrevDate = nextDate
		nextDate = justPrevDate.AddDate(0, 1, 0)
	}

	// getting remaining number of days between the maturityDate and the same day as today, but just before maturity date
	validDays := int32(depositMaturityDate.Sub(justPrevDate).Hours() / 24)

	res := &types.DepositTerm{
		Days:   validDays,
		Months: int32(validMonths),
	}
	return res, nil
}

// getB2CPayload creates a B2C order payload for transferring given amount from epifi's business account to given savings account.
func (s *Service) getB2CPayload(ctx context.Context, savingsAccountPiId string, amount *moneyPb.Money, remarks string) (*paymentPb.B2CFundTransfer, error) {
	// businessAccPiId, err := s.getPiForActor(ctx, epifiBusinessAccountActorId, accountsPb.Type_CURRENT)
	// if err != nil {
	// 	return nil, fmt.Errorf("unable to get business account pi id, err %w", err)
	// }

	return &paymentPb.B2CFundTransfer{
		PiFrom:            epifiBusinessAccountPiId,
		PiTo:              savingsAccountPiId,
		Amount:            amount,
		Remarks:           remarks,
		PreferredProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		Partner:           commonvgpb.Vendor_FEDERAL_BANK,
	}, nil
}

func (s *Service) fetchBonusTemplates(accountType accountsPb.Type) []*config.DepositTemplate {
	bonusTemplates := make([]*config.DepositTemplate, 0)
	switch accountType {
	case accountsPb.Type_SMART_DEPOSIT:
		if s.sdTemplates != nil {
			for _, sdTemplate := range s.sdTemplates {
				if sdTemplate.IsEnabled && sdTemplate.DepositTemplateType.ToDepositTempalteType() == depositPb.DepositTemplateType_BONUS {
					bonusTemplates = append(bonusTemplates, sdTemplate)
				}
			}
		}
	case accountsPb.Type_FIXED_DEPOSIT:
		if s.fdTemplates != nil {
			for _, fdTemplate := range s.fdTemplates {
				if fdTemplate.IsEnabled && fdTemplate.DepositTemplateType.ToDepositTempalteType() == depositPb.DepositTemplateType_BONUS {
					bonusTemplates = append(bonusTemplates, fdTemplate)
				}
			}
		}
	default:
		return nil
	}
	return bonusTemplates
}

func (s *Service) getBonusDepositTemplatesForActor(ctx context.Context, actorId string, accountType accountsPb.Type) ([]*depositPb.DepositTemplate, error) {
	// fetch bonus templates for deposit account type
	bonusTemplates := s.fetchBonusTemplates(accountType)
	bonusTemplateIds := make([]string, 0)
	IdTemplateMap := make(map[string]*config.DepositTemplate)
	for _, bonusTemplate := range bonusTemplates {
		bonusTemplateIds = append(bonusTemplateIds, bonusTemplate.Id)
		IdTemplateMap[bonusTemplate.Id] = bonusTemplate
	}

	bonusDepositAccounts, err := s.DepositAccountDao.GetByActorIdAndTemplateIDs(ctx, actorId, bonusTemplateIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get bonus deposit account for actorId, %w", err)
	}

	// remove all templates for whom a deposit account already exists
	for _, bonusDepositAccount := range bonusDepositAccounts {
		delete(IdTemplateMap, bonusDepositAccount.GetDepositTemplateId())
	}

	finalBonusTemplates := make([]*depositPb.DepositTemplate, 0)
	for _, val := range IdTemplateMap {
		depositTemplate, err := convertToDepositTemplate(val, accountType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert deposit tempalte, %w", err)
		}
		finalBonusTemplates = append(finalBonusTemplates, depositTemplate)
	}

	return finalBonusTemplates, nil
}

func convertToDepositTemplate(configDepositTemplate *config.DepositTemplate, templateAccType accountsPb.Type) (*depositPb.DepositTemplate, error) {
	depositTemplate := &depositPb.DepositTemplate{
		Name:                configDepositTemplate.Name,
		IsAmountEditable:    configDepositTemplate.IsAmountEditable,
		Type:                templateAccType,
		Description:         configDepositTemplate.Description,
		Term:                configDepositTemplate.Term.ToDepositTerm(),
		IsTermEditable:      configDepositTemplate.IsTermEditable,
		Tag:                 configDepositTemplate.Tag,
		IconUrl:             configDepositTemplate.IconAttribute.IconURL,
		DepositTemplateType: configDepositTemplate.DepositTemplateType.ToDepositTempalteType(),
		Id:                  configDepositTemplate.Id,
		IsNameEditable:      configDepositTemplate.IsNameEditable,
		BonusTemplateFields: &depositPb.DepositTemplate_BonusTemplateFields{ExtraInterest: bonusTemplateExtraInterest},
	}
	amount, err := money.ParseString(configDepositTemplate.Amount, "INR")
	if err != nil {
		return nil, fmt.Errorf("failed to parse amount from string, %w", err)
	}
	depositTemplate.Amount = amount

	return depositTemplate, nil
}

// Note: fetchBonusTemplates returns only enabled bonus templates.
// Use this method only when active bonus templates need to be fetched.
func (s *Service) isBoosterJar(templateId string, accountType accountsPb.Type) bool {
	if templateId == "" {
		return false
	}
	bonusTemplates := s.fetchBonusTemplates(accountType)
	for _, bonusTemplate := range bonusTemplates {
		if bonusTemplate.Id == templateId {
			return true
		}
	}
	return false
}

func (s *Service) checkBonusTemplateEligibility(ctx context.Context, depositType accountsPb.Type, actorId string) error {
	bonusTemplates := s.fetchBonusTemplates(depositType)
	bonusTemplateIds := make([]string, 0)
	for _, bonusTemplate := range bonusTemplates {
		bonusTemplateIds = append(bonusTemplateIds, bonusTemplate.Id)
	}
	bonusTemplateDeposits, err := s.DepositAccountDao.GetByActorIdAndTemplateIDs(ctx, actorId, bonusTemplateIds)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("failed to fetch if actor has bonus template deposits, %w", err)
	}

	if len(bonusTemplateDeposits) > 0 {
		return DepositTemplateLimitExceededError
	}

	// check if min-kyc user, they aren't allowed to create booster jar
	isFullKyc, err := s.isFullKYCUser(ctx, actorId)
	if err != nil {
		return fmt.Errorf("failed to get kyc level, %w", err)
	}
	if !isFullKyc {
		return DepositEligibilityMinKYCError
	}

	return nil
}

// getBonusPayoutAmount gets the bonus payout amount for a payout record. The Bonus payout amount is calculated using the below logic:
//  1. Fetch the balance on the start date of payout, and calculate the bonus interest amount on it for a month
//  2. For each transaction between start date and end date calculate the interest amount based on the number of days
//     remaining between the transaction date and the end date.
//
// nolint: funlen
func (s *Service) getBonusPayoutAmount(ctx context.Context, bonusPayout *depositPb.DepositAccountBonusPayout) (*moneyPb.Money, error) {
	durationInDays := bonusPayout.GetEndDate().AsTime().Sub(bonusPayout.GetStartDate().AsTime()).Hours() / 24

	depositAccount, err := s.DepositAccountDao.GetById(ctx, bonusPayout.GetDepositAccountId())
	if err != nil {
		return nil, fmt.Errorf("no deposit account exists with id %s, err %w", bonusPayout.GetDepositAccountId(), err)
	}

	depositPi, err := s.getPiByAccountNumberAndIfsc(ctx, depositAccount.GetAccountNumber(), depositAccount.GetIfscCode())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch deposit PI, id %s, err %w", depositAccount.GetId(), err)
	}

	startDayDepositBalance, err := s.getDepositBalanceOnADay(ctx, depositAccount, depositPi.GetId(), bonusPayout.GetStartDate())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch deposit balance on a day %s, err %w", depositAccount.GetId(), err)
	}
	if money.Compare(startDayDepositBalance, maxEligibleAmountForBonusPayout) == 1 {
		// call bonus payout interest calculation from here as max limit has reached, no point of calculating average
		return calculateInterestOnAmount(durationInDays, maxEligibleAmountForBonusPayout)
	}

	totalDepositBalance := &moneyPb.Money{
		CurrencyCode: startDayDepositBalance.GetCurrencyCode(),
		Units:        startDayDepositBalance.GetUnits(),
		Nanos:        startDayDepositBalance.GetNanos(),
	}
	eligibleAverageBalance := &moneyPb.Money{
		CurrencyCode: startDayDepositBalance.GetCurrencyCode(),
		Units:        startDayDepositBalance.GetUnits(),
		Nanos:        startDayDepositBalance.GetNanos(),
	}

	allTxns, err := s.getTxnsBetweenDates(ctx, bonusPayout.GetStartDate(), bonusPayout.GetEndDate(), "", depositPi.GetId(),
		paymentPb.TransactionFieldMask_DEBITED_AT)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch deposit txns between dates, %w", err)
	}

	for _, txn := range allTxns {
		averageExceededEligibleAmount := false
		txnAmount := txn.GetAmount()
		totalDepositBalance, err = money.Sum(totalDepositBalance, txn.GetAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to add money fields, err %w", err)
		}
		if money.Compare(totalDepositBalance, maxEligibleAmountForBonusPayout) == 1 {
			moneyDiff, subErr := money.Subtract(totalDepositBalance, maxEligibleAmountForBonusPayout)
			if subErr != nil {
				return nil, fmt.Errorf("failed to subtract money fields, err %w", subErr)
			}
			txnAmount, subErr = money.Subtract(txnAmount, moneyDiff)
			if subErr != nil {
				return nil, fmt.Errorf("failed to subtract money fields, err %w", subErr)
			}
			averageExceededEligibleAmount = true
		}
		daysToEndDate := bonusPayout.GetEndDate().AsTime().Sub(txn.GetDebitedAt().AsTime()).Hours() / 24
		averageAmount := (float64(txnAmount.GetUnits()) * daysToEndDate) / durationInDays
		eligibleAverageBalance, err = money.Sum(eligibleAverageBalance, &moneyPb.Money{
			CurrencyCode: eligibleAverageBalance.GetCurrencyCode(),
			Units:        int64(averageAmount),
			Nanos:        0,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to add money fields, err %w", err)
		}
		if averageExceededEligibleAmount {
			break
		}
	}

	return calculateInterestOnAmount(durationInDays, eligibleAverageBalance)
}

func calculateInterestOnAmount(durationInDays float64, amount *moneyPb.Money) (*moneyPb.Money, error) {
	bonusPayoutExtraInterest, err := strconv.ParseFloat(bonusTemplateExtraInterest, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse bonus interest from string to float %s, err %w", bonusTemplateExtraInterest, err)
	}
	daysInYear := time.Date(time.Now().Year(), 12, 31, 0, 0, 0, 0, time.UTC).YearDay()

	interestAmount := (float64(amount.GetUnits()) * bonusPayoutExtraInterest * durationInDays) / float64(daysInYear)
	return &moneyPb.Money{
		CurrencyCode: amount.GetCurrencyCode(),
		Units:        int64(interestAmount),
		Nanos:        0,
	}, nil
}

func (s *Service) getDepositBalanceOnADay(ctx context.Context, depositAccount *depositPb.DepositAccount, depositPiId string, balanceDate *timestamp.Timestamp) (*moneyPb.Money, error) {
	depositCurrentBalance := depositAccount.GetPrincipalAmount()
	txnAmountFromBalanceDate := money.ZeroINR().GetPb()

	allTxns, err := s.getTxnsBetweenDates(ctx, balanceDate, ptypes.TimestampNow(), "", depositPiId, paymentPb.TransactionFieldMask_CREATED_AT)
	if err != nil {
		return nil, fmt.Errorf("failed to get deposit txns between dates, %w", err)
	}
	for _, txn := range allTxns {
		txnAmountFromBalanceDate, err = money.Sum(txnAmountFromBalanceDate, txn.GetAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to caclualte money on a date, err %w", err)
		}
	}
	return money.Subtract(depositCurrentBalance, txnAmountFromBalanceDate)
}

// getTxnsBetweenDates gets all txns between two timestamps for a given set of fromPI and toPi
func (s *Service) getTxnsBetweenDates(ctx context.Context, startDate, endDate *timestamp.Timestamp, fromPiId, toPiId string,
	sortBy paymentPb.TransactionFieldMask) ([]*paymentPb.Transaction, error) {
	offset := int32(0)
	pageSize := int32(40)
	fetchedAllTxns := false

	allTxns := make([]*paymentPb.Transaction, 0)
	for !fetchedAllTxns {
		txnsRes, err := s.paymentClient.GetTxnsByPi(ctx, &paymentPb.GetTxnsByPiRequest{
			PiFrom:        fromPiId,
			PiTo:          toPiId,
			FromTimestamp: startDate,
			ToTimestamp:   endDate,
			SortBy:        sortBy,
			SortDesc:      false,
			PageSize:      pageSize,
			Offset:        offset,
			Statuses:      []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
		})
		switch {
		case err != nil:
			return nil, fmt.Errorf("failed to get transactions for deposit account, %w", err)
		case txnsRes.GetStatus().IsRecordNotFound():
			return nil, nil
		case !txnsRes.GetStatus().IsSuccess():
			return nil, fmt.Errorf("paymentClient.GetSuccessfulTxnsByPi() returned unsuccessful status %v", txnsRes.GetStatus())
		case txnsRes.GetStatus().IsSuccess():
			if len(txnsRes.GetTransactions()) < int(pageSize) {
				fetchedAllTxns = true
			} else {
				offset = int32(len(txnsRes.GetTransactions()))
			}
			allTxns = append(allTxns, txnsRes.GetTransactions()...)
		}
	}
	return allTxns, nil
}

// shouldRetryForRecordNotFound returns a boolean value depending on if a deposit request is to be retried or not
// Retry logic: If deposit request created withing a time range, retry and do a status check. Else mark the deposit
// request as failed, and create new one.
func shouldRetryForRecordNotFound(enquiryParams *config.DepositEnquiryParams, creationTime *timestamp.Timestamp, vendor commonvgpb.Vendor) bool {
	val, ok := enquiryParams.NotFoundMaxRetryDurationVendorMap[vendor.String()]
	if !ok {
		return false
	}
	return time.Since(creationTime.AsTime()) < val
}

// isBankDowntimeApplicable will check whether a bank downtime or CSIS downtime is applicable or not
// It returns a boolean value to determine if downtime is applicable or not.
// It returns an error when there's some issue checking bank downtime
func (s *Service) isBankDowntimeApplicable(ctx context.Context, payeeActorId string) (bool, error) {
	bankDownTime, _, bankDowntimeErr := downtime.NewBankDowntimeCheck(clockwork.NewRealClock()).IsDowntime(ctx, nil)
	switch {
	case bankDowntimeErr != nil:
		return false, fmt.Errorf("error in evaluating bank downtime, %w", bankDowntimeErr)
	case bankDownTime:
		logger.Info(ctx, "bank downtime is applicable")
		return true, nil
	}

	csisDownTimeCheck := downtime.NewCsisDownTimeCheck(s.healthEngineClient, clockwork.NewRealClock())
	params := map[downtime.Params]string{
		downtime.ParamsActorId: payeeActorId,
	}
	csisDownTime, _, csisDownTimeErr := csisDownTimeCheck.IsDowntime(ctx, params)
	switch {
	case csisDownTimeErr != nil:
		return false, fmt.Errorf("error in evaluating CSIS downtime, %w", csisDownTimeErr)
	case csisDownTime:
		logger.Info(ctx, "CSIS downtime applicable")
		return true, nil
	}
	return false, nil
}

func (s *Service) filterDepositTemplates(ctx context.Context, vendor commonvgpb.Vendor, depositTemplates []*depositPb.DepositTemplate, actorID string) ([]*depositPb.DepositTemplate, error) {
	filteredTemplates, err := s.filterTaxSavingTemplatesForNonFullKYCUser(ctx, vendor, depositTemplates, actorID)
	if err != nil {
		return nil, err
	}
	return filteredTemplates, nil
}

func (s *Service) filterTaxSavingTemplatesForNonFullKYCUser(ctx context.Context, vendor commonvgpb.Vendor, depositTemplates []*depositPb.DepositTemplate, actorID string) ([]*depositPb.DepositTemplate, error) {

	isTaxSavingSchemeEnabled := false
	isFiLiteUser, _, err := s.onboardingAccessor.IsFiLiteActor(ctx, actorID)
	if err != nil {
		return nil, err
	}

	// If user is not a filite user, check for kyc status as tax saving schemes are only enabled for full kyc users
	if !isFiLiteUser {
		isFullKYC, err := s.isFullKYCUser(ctx, actorID)
		if err != nil {
			return nil, err
		}
		if isFullKYC {
			isTaxSavingSchemeEnabled = true
		}
	}

	if isTaxSavingSchemeEnabled {
		return depositTemplates, nil
	}

	var filteredDepositTemplates []*depositPb.DepositTemplate
	for _, depositTemplate := range depositTemplates {
		shouldAppend := true
		for _, val := range pkgDeposit.TaxSavingSchemes[vendor] {
			if val == depositTemplate.GetSchemeCode() {
				shouldAppend = false
				break
			}
		}
		if shouldAppend {
			filteredDepositTemplates = append(filteredDepositTemplates, depositTemplate)
		}
	}
	return filteredDepositTemplates, nil
}

// checkPanAadhaarLinkEligibility returns a boolean value to determine if the user is eligible for the deposit
// creation/add funds or not based on the pan aadhaar link status.
// depositAmount is the amount for which the deposit is being created or funds are being added.
// maxAllowedAmount is the maximum amount allowed for the deposit creation/add funds if the user is not pan aadhaar linked.
func (s *Service) checkPanAadhaarLinkEligibility(
	ctx context.Context,
	actorId string,
	depositAmount *moneyPb.Money,
	maxAllowedAmount *moneyPb.Money,
) (bool,
	error) {

	// if pan aadhaar link eligibility check is disabled, return true
	if !s.config.DepositLimits().AadhaarPanLinkEligibilityCheckEnable {
		return true, nil
	}

	getPANAadhaarLinkStatusRes, err := s.panClient.GetPANAadharLinkStatus(ctx, &panPb.GetPANAadharLinkStatusRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(getPANAadhaarLinkStatusRes, err); err != nil {
		switch {
		case getPANAadhaarLinkStatusRes.GetStatus().IsRecordNotFound() || getPANAadhaarLinkStatusRes.GetStatus().GetCode() == uint32(panPb.GetPANAadharLinkStatusResponse_INVALID_PAN):
			logger.Info(ctx, "non-success status received from GetPANAadharLinkStatus RPC",
				zap.String(logger.STATUS, getPANAadhaarLinkStatusRes.GetStatus().String()))
			return false, nil
		case getPANAadhaarLinkStatusRes.GetStatus().IsCancelled() || getPANAadhaarLinkStatusRes.GetStatus().IsDeadlineExceeded():
			logger.Info(ctx, "GetPANAadharLinkStatus called because of deadline-exceeded/context-cancellation", zap.Error(err))
			return false, epifierrors.ErrContextCanceled
		default:
			return false, fmt.Errorf("failed to get pan aadhaar link status, %w", err)
		}
	}

	isPanAadhaarLinked := getPANAadhaarLinkStatusRes.GetPanAadharLinkStatus() == panPb.
		PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED

	// if pan aadhaar is not linked and amount is more than max allowed amount
	if !isPanAadhaarLinked && money.Compare(depositAmount, maxAllowedAmount) == 1 {
		return false, nil
	}

	// if pan aadhaar is linked or amount is less than max allowed amount
	return true, nil
}
