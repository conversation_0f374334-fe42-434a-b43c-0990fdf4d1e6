package accessrevoke

import (
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
)

// RiskReasonToAccessRevokeReasonMap maps risk reason to access revoke reason (for full freeze)
var RiskReasonToAccessRevokeReasonMap = map[riskEnumsPb.RequestReason]userPb.AccessRevokeReason{
	riskEnumsPb.RequestReason_REQUEST_REASON_UNSPECIFIED:                    userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED,
	riskEnumsPb.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT:             userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT,
	riskEnumsPb.RequestReason_REQUEST_REASON_OTHER:                          userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_OTHER,
	riskEnumsPb.RequestReason_REQUEST_REASON_CORE_KYC_ISSUE:                 userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_CORE_KYC_ISSUE,
	riskEnumsPb.RequestReason_REQUEST_REASON_PROFILE_INDICATORS:             userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_PROFILE_INDICATORS,
	riskEnumsPb.RequestReason_REQUEST_REASON_PENNY_DROP_ABUSE:               userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_PENNY_DROP_ABUSE,
	riskEnumsPb.RequestReason_REQUEST_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS: userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS,
	riskEnumsPb.RequestReason_REQUEST_REASON_HIGH_ATM_WITHDRAWAL_COUNT:      userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_HIGH_ATM_WITHDRAWAL_COUNT,
	riskEnumsPb.RequestReason_REQUEST_REASON_TM_ALERT:                       userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_TM_ALERT,
	riskEnumsPb.RequestReason_REQUEST_REASON_TM_PROFILE_MISMATCH:            userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_TM_PROFILE_MISMATCH,
	riskEnumsPb.RequestReason_REQUEST_REASON_ACCOUNT_DELETION_REQUEST:       userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST,
	riskEnumsPb.RequestReason_REQUEST_REASON_MIN_KYC_ACCOUNT_EXPIRY:         userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY,
	riskEnumsPb.RequestReason_REQUEST_REASON_LEA_COMPLAINT:                  userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_LEA_COMPLAINT,
	riskEnumsPb.RequestReason_REQUEST_REASON_LEA_ENQUIRY:                    userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_LEA_ENQUIRY,
	riskEnumsPb.RequestReason_REQUEST_REASON_NPCI_COMPLAINT:                 userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_NPCI_COMPLAINT,
	riskEnumsPb.RequestReason_REQUEST_REASON_FEDERAL_RULES:                  userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_FEDERAL_RULES,
	riskEnumsPb.RequestReason_REQUEST_REASON_LSO_USER_VKYC_PENDING:          userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING,
	riskEnumsPb.RequestReason_REQUEST_REASON_BLOCK_ONBOARDING:               userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_BLOCK_ONBOARDING,
	riskEnumsPb.RequestReason_REQUEST_REASON_RE_VKYC_NOT_COMPLETED:          userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED,
}

// RiskReasonToAccessRestoreReasonMap maps risk reason to access restore reason (for un-freeze)
var RiskReasonToAccessRestoreReasonMap = map[riskEnumsPb.RequestReason]userPb.AccessRestoreReason{
	riskEnumsPb.RequestReason_REQUEST_REASON_UNSPECIFIED:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_UNSCPECIFIED,
	riskEnumsPb.RequestReason_REQUEST_REASON_OTHER:            userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_OTHERS,
	riskEnumsPb.RequestReason_REQUEST_REASON_DUE_DILIGENCE:    userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_DUE_DILIGENCE,
	riskEnumsPb.RequestReason_REQUEST_REASON_CUSTOMER_OUTCALL: userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_CUSTOMER_OUTCALL,
	riskEnumsPb.RequestReason_REQUEST_REASON_LEA_UNFREEZE:     userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_LEA_UNFREEZE,
	riskEnumsPb.RequestReason_REQUEST_REASON_CC_OR_PL_USER:    userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_REASON_CC_OR_PL_USER,
}

// RiskReasonToConstraintsUpdateReasonMap maps risk reason to access revoke reason (for full freeze)
var RiskReasonToConstraintsUpdateReasonMap = map[riskEnumsPb.RequestReason]savingsPb.ConstraintsUpdateReason{
	riskEnumsPb.RequestReason_REQUEST_REASON_UNSPECIFIED:                    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_UNSPECIFIED,
	riskEnumsPb.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT:             savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_FRAUDULENT_ACCOUNT,
	riskEnumsPb.RequestReason_REQUEST_REASON_OTHER:                          savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_OTHER,
	riskEnumsPb.RequestReason_REQUEST_REASON_CORE_KYC_ISSUE:                 savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_CORE_KYC_ISSUE,
	riskEnumsPb.RequestReason_REQUEST_REASON_PROFILE_INDICATORS:             savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_PROFILE_INDICATORS,
	riskEnumsPb.RequestReason_REQUEST_REASON_PENNY_DROP_ABUSE:               savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_PENNY_DROP_ABUSE,
	riskEnumsPb.RequestReason_REQUEST_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS: savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS,
	riskEnumsPb.RequestReason_REQUEST_REASON_HIGH_ATM_WITHDRAWAL_COUNT:      savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_HIGH_ATM_WITHDRAWAL_COUNT,
	riskEnumsPb.RequestReason_REQUEST_REASON_TM_ALERT:                       savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_TM_ALERT,
	riskEnumsPb.RequestReason_REQUEST_REASON_TM_PROFILE_MISMATCH:            savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_TM_PROFILE_MISMATCH,
	riskEnumsPb.RequestReason_REQUEST_REASON_DUE_DILIGENCE:                  savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_DUE_DILIGENCE,
	riskEnumsPb.RequestReason_REQUEST_REASON_CUSTOMER_OUTCALL:               savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_CUSTOMER_OUTCALL,
	riskEnumsPb.RequestReason_REQUEST_REASON_ACCOUNT_DELETION_REQUEST:       savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_ACCOUNT_DELETION_REQUEST,
	riskEnumsPb.RequestReason_REQUEST_REASON_MIN_KYC_ACCOUNT_EXPIRY:         savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_MIN_KYC_ACCOUNT_EXPIRY,
	riskEnumsPb.RequestReason_REQUEST_REASON_LEA_COMPLAINT:                  savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_COMPLAINT,
	riskEnumsPb.RequestReason_REQUEST_REASON_LEA_ENQUIRY:                    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_ENQUIRY,
	riskEnumsPb.RequestReason_REQUEST_REASON_NPCI_COMPLAINT:                 savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_NPCI_COMPLAINT,
	riskEnumsPb.RequestReason_REQUEST_REASON_FEDERAL_RULES:                  savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_FEDERAL_RULES,
	riskEnumsPb.RequestReason_REQUEST_REASON_LEA_UNFREEZE:                   savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_UNFREEZE,
	riskEnumsPb.RequestReason_REQUEST_REASON_LSO_USER_VKYC_PENDING:          savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LSO_USER_VKYC_PENDING,
	riskEnumsPb.RequestReason_REQUEST_REASON_BLOCK_ONBOARDING:               savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_BLOCK_ONBOARDING,
	riskEnumsPb.RequestReason_REQUEST_REASON_CC_OR_PL_USER:                  savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_CC_OR_PL_USER,
	riskEnumsPb.RequestReason_REQUEST_REASON_RE_VKYC_NOT_COMPLETED:          savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_RE_VKYC_NOT_COMPLETED,
}
