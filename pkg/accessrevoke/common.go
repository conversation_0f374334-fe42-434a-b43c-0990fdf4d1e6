package accessrevoke

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	authPb "github.com/epifi/gamma/api/auth"
	riskPb "github.com/epifi/gamma/api/risk"
	riskEnums "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// RiskActionToAccessRevokeDetails fills access revoke details using risk action and enums
func RiskActionToAccessRevokeDetails(ctx context.Context, action riskEnums.Action, reason *riskPb.RequestReason, updatedBy string) (*userPb.AccessRevokeDetails, error) {
	var (
		accessRevokeStatus  userPb.AccessRevokeStatus
		accessRevokeReason  userPb.AccessRevokeReason
		accessRestoreReason userPb.AccessRestoreReason
		ok                  bool
	)

	switch action {
	case riskEnums.Action_ACTION_FULL_FREEZE:
		// unblocked - to enable user app access after total freeze
		accessRevokeStatus = userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED
		accessRestoreReason = userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_TOTAL_FREEZE
	case riskEnums.Action_ACTION_UNFREEZE:
		accessRevokeStatus = userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED
		accessRestoreReason, ok = RiskReasonToAccessRestoreReasonMap[reason.GetReason()]
		if !ok {
			logger.Error(ctx, "could not map request reason to access restore reason", zap.String("risk_reason", reason.String()))
			return nil, fmt.Errorf("could not map request reason to access restore reason")
		}
		if accessRestoreReason == userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE {
			logger.Info(ctx, "access restore reason can not be credit freeze for unfreeze action",
				zap.String("risk_reason", reason.String()))
			return nil, fmt.Errorf("access restore reason can not be credit freeze for unfreeze action")
		}
	case riskEnums.Action_ACTION_CREDIT_FREEZE:
		accessRevokeStatus = userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED
		accessRestoreReason = userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE
	case riskEnums.Action_ACTION_DEBIT_FREEZE:
		accessRevokeStatus = userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED
		accessRestoreReason = userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_DEBIT_FREEZE
	default:
		logger.Info(ctx, "no revoke status associated to risk action", zap.String("risk_action", action.String()))
		return nil, fmt.Errorf("no revoke status associated to risk action: %v", action.String())
	}

	return &userPb.AccessRevokeDetails{
		AccessRevokeStatus: accessRevokeStatus,
		Reason:             accessRevokeReason,
		RestoreReason:      accessRestoreReason,
		Remarks:            reason.GetRemarks(),
		UpdatedBy:          updatedBy,
		UpdatedAt:          timestampPb.Now(),
	}, nil
}

// RiskActionToSavingsAccountConstraints fills savings account constraints using risk action and enums
func RiskActionToSavingsAccountConstraints(ctx context.Context, action riskEnums.Action, reason *riskPb.RequestReason, updatedBy string) (*savingsPb.AccountConstraints, error) {
	constraints := &savingsPb.AccountConstraints{}
	switch action {
	case riskEnums.Action_ACTION_FULL_FREEZE:
		constraints.AccessLevel = savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS
	case riskEnums.Action_ACTION_CREDIT_FREEZE:
		constraints.AccessLevel = savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS
		constraints.Restrictions = []savingsPb.Restriction{savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE}
	case riskEnums.Action_ACTION_DEBIT_FREEZE:
		constraints.AccessLevel = savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS
		constraints.Restrictions = []savingsPb.Restriction{savingsPb.Restriction_RESTRICTION_DEBIT_FREEZE}
	case riskEnums.Action_ACTION_UNFREEZE:
		constraints.AccessLevel = savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS
	default:
		logger.Info(ctx, "no savings constraint associated to risk action", zap.String("risk_action", action.String()))
		return nil, fmt.Errorf("no savings constraint associated to risk action: %v", action.String())
	}

	updateReason, ok := RiskReasonToConstraintsUpdateReasonMap[reason.GetReason()]
	if !ok {
		logger.Error(ctx, "could not map risk reason to constraints update reason", zap.String("risk_reason", reason.String()))
		return nil, fmt.Errorf("could not map risk reason to constraints update reason")
	}
	constraints.UpdateDetails = &savingsPb.ConstraintsUpdateDetails{
		Reason:    updateReason,
		Remarks:   reason.GetRemarks(),
		UpdatedBy: updatedBy,
		UpdatedAt: timestampPb.Now(),
	}
	return constraints, nil
}

// UpdateSavingsAccountConstraints updates savings account constraints and also sets in-app banner if account is credit frozen.
// It does nothing if no savings account was found associated to the given user id
func UpdateSavingsAccountConstraints(ctx context.Context, savClient savingsPb.SavingsClient, userId string, constraints *savingsPb.AccountConstraints) error {
	updateAcctResp, err := savClient.UpdateAccount(ctx, &savingsPb.UpdateAccountRequest{
		Identifier:  &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: userId},
		Constraints: constraints,
		UpdateMask:  []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
	})
	if te := epifigrpc.RPCError(updateAcctResp, err); te != nil && !updateAcctResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while updating savings account constraints", zap.Error(te))
		return te
	}
	return nil
}

// UpdateAppAccessRevokeDetails updates app access revoke details, savings account constraints and sets in app comms
func UpdateAppAccessRevokeDetails(ctx context.Context, userClient userPb.UsersClient, userIdentifier *userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier, accessRevokeDetails *userPb.AccessRevokeDetails, triggerComms bool) error {
	userResp, err := userClient.UpdateAccessRevokeDetails(ctx, &userPb.UpdateAccessRevokeDetailsRequest{
		UserIdentifier:      userIdentifier,
		AccessRevokeDetails: accessRevokeDetails,
		TriggerUserComms:    triggerComms,
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		logger.Error(ctx, "error in revoking app access", zap.Error(te))
		return te
	}
	return nil
}

// SignOutUser deletes all the auth tokens linked to the user and logs him out of the application.
// We need to do this as access tokens can be abused to hit APIs directly circumventing the UI
func SignOutUser(ctx context.Context, authClient authPb.AuthClient, actorId, userId string, accessRevokeStatus userPb.AccessRevokeStatus) error {
	// do not proceed if access is not be revoked
	if accessRevokeStatus != userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED {
		return nil
	}
	signOutResp, err := authClient.SignOut(ctx, &authPb.SignOutRequest{
		Actor: &types.Actor{
			Id:       actorId,
			EntityId: userId,
		},
	})
	if te := epifigrpc.RPCError(signOutResp, err); te != nil {
		logger.Error(ctx, "error while signing out user", zap.Error(err))
		return te
	}
	return nil
}
