package tiering

import (
	"testing"

	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/pkg/tiering/errors"

	"github.com/stretchr/testify/assert"
)

func TestGetAllCriteriaMinValuesFromOptions(t *testing.T) {
	testCases := []struct {
		name        string
		options     []*tieringCriteriaPb.Option
		expected    []*CriteriaMinValue
		expectedErr error
	}{
		{
			name:        "Empty options",
			options:     []*tieringCriteriaPb.Option{},
			expected:    nil,
			expectedErr: errors.ErrTierHasNoMinBalanceCriteria,
		},
		{
			name: "Options with criteria",
			options: []*tieringCriteriaPb.Option{
				{
					Actions: []*tieringCriteriaPb.Action{
						{
							ActionDetails: &tieringCriteriaPb.QualifyingCriteria{
								Criteria: &tieringCriteriaPb.QualifyingCriteria_UsStocksSip{
									UsStocksSip: &tieringCriteriaPb.UsStocksSip{
										MinWalletAddFunds: &gmoney.Money{
											CurrencyCode: "INR",
											Units:        1000,
										},
									},
								},
							},
						},
					},
				},
			},
			expected: []*CriteriaMinValue{
				{
					Criteria: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
					MinValue: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
				},
			},
			expectedErr: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := GetAllCriteriaMinValuesFromOptions(tc.options)

			if tc.expectedErr != nil {
				assert.Equal(t, tc.expectedErr, err)
			} else {
				require.NoError(t, err)
				assert.Len(t, tc.expected, len(result))

				if len(tc.expected) > 0 {
					assert.Equal(t, tc.expected[0].Criteria, result[0].Criteria)
					assert.Equal(t, tc.expected[0].MinValue.CurrencyCode, result[0].MinValue.CurrencyCode)
					assert.Equal(t, tc.expected[0].MinValue.Units, result[0].MinValue.Units)
				}
			}
		})
	}
}

func TestTieringConsumer_DetermineBalanceBucket(t *testing.T) {
	type fields struct {
	}
	type args struct {
		amount int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name:   "bucketing 1",
			fields: fields{},
			args: args{
				amount: 0,
			},
			want: "0-500",
		},
		{
			name:   "bucketing 2",
			fields: fields{},
			args: args{
				amount: 500,
			},
			want: "0-500",
		},
		{
			name:   "bucketing 3",
			fields: fields{},
			args: args{
				amount: 501,
			},
			want: "501-1000",
		},
		{
			name:   "bucketing 4",
			fields: fields{},
			args: args{
				amount: 1000,
			},
			want: "501-1000",
		},
		{
			name:   "bucketing 5",
			fields: fields{},
			args: args{
				amount: 1001,
			},
			want: "1001-3000",
		},
		{
			name:   "bucketing 6",
			fields: fields{},
			args: args{
				amount: 1501,
			},
			want: "1001-3000",
		},
		{
			name:   "bucketing 7",
			fields: fields{},
			args: args{
				amount: 50000,
			},
			want: "45001-50000",
		},
		{
			name:   "bucketing 8",
			fields: fields{},
			args: args{
				amount: 100001,
			},
			want: "100000+",
		},
		{
			name:   "bucketing 9",
			fields: fields{},
			args: args{
				amount: -1,
			},
			want: "0",
		},
	}
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := DetermineBalanceBucket(tc.args.amount)
			assert.Equal(t, tc.want, got)
		})
	}
}
