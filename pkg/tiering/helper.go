package tiering

import (
	"fmt"

	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
)

// GetAllCriteriaMinValuesFromOptions extracts all criteria minimum values from a list of tiering options
func GetAllCriteriaMinValuesFromOptions(tierOptions []*tieringCriteriaPb.Option) ([]*CriteriaMinValue, error) {
	var criteriaMinValues []*CriteriaMinValue

	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			ac := action.GetActionDetails().GetCriteria()
			switch actionCriteria := ac.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_UsStocksSip:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
						MinValue: actionCriteria.UsStocksSip.GetMinWalletAddFunds(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_Deposits:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
						MinValue: actionCriteria.Deposits.GetMinDepositsAmount(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
						MinValue: actionCriteria.BalanceV2.GetMinBalanceForUpgrade(),
					})
			}
		}
	}

	if len(criteriaMinValues) > 0 {
		return criteriaMinValues, nil
	}

	return nil, pkgErrors.ErrTierHasNoMinBalanceCriteria
}

// DetermineBalanceBucket determines the balance bucket the amount falls into
// bucketRanges sorted in ascending order and define the lower bounds of each bucket.
// The returned string is of the form "lower-upper" (both inclusive).
// Eg. if the amount is 3001, we consider the amount to be in 3001 - 5001 because it checks if the amount is between 3001 and 5000 (both inclusive) and returns '3001-5000'
func DetermineBalanceBucket(amount int64) string {
	var bucketRanges = []int64{
		0, 501, 1001, 3001, 5001, 7501, 10001, 20001, 25001, 30001, 40001, 45001, 50001, 100001,
	}
	for bucketIdx, upperBound := range bucketRanges {
		if amount < upperBound {
			if bucketIdx == 0 {
				return fmt.Sprintf("%d", bucketRanges[0])
			}
			lowerBound := bucketRanges[bucketIdx-1]
			return fmt.Sprintf("%d-%d", lowerBound, upperBound-1)
		}
	}
	return fmt.Sprintf("%d+", bucketRanges[len(bucketRanges)-1]-1)
}
