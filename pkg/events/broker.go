package events

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"regexp"
	"time"

	"github.com/epifi/be-common/pkg/cfg"

	"github.com/rudderlabs/analytics-go"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events/metrics"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	EventTrack    = "track"
	EventIdentify = "identify"
	SessionIdKey  = "session_id"
	AttemptIdKey  = "attempt_id"
	TracedIdKey   = "trace_id"
	EventId       = "event_id"
	EventName     = "event_name"
	TimeStamp     = "timestamp"
	AnonymousId   = "anonymous_id"
	// UserLayerBucket is an attribute discussed in this document [https://docs.google.com/document/d/1vkLCtDvirU_jiYM0SmGBNu3r6ibEqy7-jSPl29RlJkY]
	// UserId [ActorId] will be hashed using FNV-1 algorithm and converted into a bucket ranging from 0..99.
	// The bucket size can be increased in the future.
	UserLayerBucket = "user_layer"
	// UserLayerBucketSize is expected to be a small value and hence represented as int16, and stored as SMALLINT in DBs
	UserLayerBucketSize  = 100
	EventNameUnspecified = "EVENT_NAME_UNSPECIFIED"
)

// Regexp definitions
var keyMatchRegex = regexp.MustCompile(`\"(\w+)\":`)
var wordBarrierRegex = regexp.MustCompile(`(\w)([A-Z])`)

// List of sensitive properties
var propertiesToRemove = []string{"UserId", "ActorId", "DeviceId", "ProspectId", "FlowName", "EventType"}

var successEventsLogsList = []string{
	"OpenedFiAccountServer",
	"CompletedTnCServer",
	"EventCompletedAddFundsServer",
	"ScreenerCompleted",
	"VKYCCallCustomerArrived",
}

// event interface, used for adding an event in event-batch for rudder
type Event interface {
	// returns event-id set by event producer
	GetEventId() string
	// returns user-id set by event producer
	GetUserId() string
	// returns prospect-id set by event producer
	GetProspectId() string
	// returns event-name set by event producer
	GetEventName() string
	// returns event-type set by event producer
	GetEventType() string
	// returns event-properties set by event producer
	// event-properties are event-data is sent by producer
	// In rudder, "track" event need properties, and "properties" will only be set for event-type "track"
	GetEventProperties() map[string]interface{}
	// returns event-traits set by event producer
	// event-traits is required by event-type "identify",
	// producer sets "device-id", "os", "network" etc
	// "traits" will only be set for event-type "identify"
	GetEventTraits() map[string]interface{}
}

func getMessageFromEvent(event Event, ctx context.Context) analytics.Message {
	switch event.GetEventType() {
	case EventTrack:
		return analytics.Track{
			UserId:      event.GetUserId(),
			AnonymousId: getProspectFromCtxOrEvent(event.GetProspectId(), ctx),
			Event:       event.GetEventName(),
			Timestamp:   time.Now(),
			Context: &analytics.Context{
				Library: analytics.LibraryInfo{Name: "http"},
			},
			Properties: getProperties(event.GetEventProperties(), event.GetUserId(), ctx),
		}
	case EventIdentify:
		return analytics.Identify{
			UserId:      event.GetUserId(),
			AnonymousId: getProspectFromCtxOrEvent(event.GetProspectId(), ctx),
			Timestamp:   time.Now(),
			Context: &analytics.Context{
				Library: analytics.LibraryInfo{Name: "http"},
			},
			Traits: getProperties(event.GetEventTraits(), event.GetUserId(), ctx),
		}
	default:
		logger.ErrorNoCtx("unrecognised event type", zap.String("event-type", event.GetEventType()))
		return nil
	}
}

func getProspectFromCtxOrEvent(id string, ctx context.Context) string {
	prospectFromCtx := epificontext.ProspectIdFromContext(ctx)
	if prospectFromCtx != epificontext.UnknownId && prospectFromCtx != "" {
		return prospectFromCtx
	}
	return id
}

func getProperties(attr map[string]interface{}, userId string, ctx context.Context) map[string]interface{} {
	if attr == nil {
		return nil
	}
	snakeCaseJsonBytes, err := replaceAttributesToSnakeCase(attr)
	if err != nil {
		logger.ErrorNoCtx("error in constructing properties", zap.Error(err))
		return nil
	}
	attr, err = fillMap(snakeCaseJsonBytes)
	if err != nil {
		logger.ErrorNoCtx("error in constructing properties", zap.Error(err))
	}
	enrichEventAttributes(attr, userId, ctx)
	return attr
}

func enrichEventAttributes(attr map[string]interface{}, userId string, ctx context.Context) {
	sessionIdFromContext := epificontext.SessionIdFromContext(ctx)
	attemptIdFromContext := epificontext.AttemptIdFromContext(ctx)
	traceIdFromContext := epificontext.TraceIdFromContext(ctx)
	// checking if the session id, attempt id and trace id is present in the context
	if sessionIdFromContext != epificontext.UnknownId && sessionIdFromContext != "" {
		attr[SessionIdKey] = sessionIdFromContext
	}
	if attemptIdFromContext != epificontext.UnknownId && attemptIdFromContext != "" {
		attr[AttemptIdKey] = attemptIdFromContext
	}
	if traceIdFromContext != epificontext.UnknownId && traceIdFromContext != "" {
		attr[TracedIdKey] = traceIdFromContext
	}
	// overriding time stamp value to current epoch time in milliseconds
	// we need to manually divide from nanoseconds since there is no UnixMillis.
	millis := time.Now().UnixNano() / 1e6
	attr[TimeStamp] = millis
	attr[UserLayerBucket] = GetUserLayerBucket(userId)
}

// GetUserLayerBucket returns a number from the range 0..UserLayerBucketSize[exclusive] by hashing
// the given userId using FNV-1 algorithm
func GetUserLayerBucket(userId string) int16 {
	h := fnv.New64()
	if _, err := h.Write([]byte(userId)); err != nil {
		logger.ErrorNoCtx("error while generating uint64 hash as user group bucket", zap.Error(err))
		return 0
	}
	return int16(h.Sum64() % UserLayerBucketSize)
}

// This methods returns a Map from []byte
func fillMap(b []byte) (map[string]interface{}, error) {
	var result map[string]interface{}
	// Unmarshal or Decode the JSON to the interface.
	err := json.Unmarshal(b, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

/*This method converts all attributes in event properties, identify traits to snake case from camel case.
  alternative approach: To add `json:"attribute"` in every event struct*/
/*This method converts the attributes in a struct to snake case*/
func replaceAttributesToSnakeCase(c map[string]interface{}) ([]byte, error) {
	err := removeSensitiveProperties(c)
	if err != nil {
		return nil, err
	}
	marshalled, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	converted := keyMatchRegex.ReplaceAllFunc(
		marshalled,
		func(match []byte) []byte {
			return bytes.ToLower(wordBarrierRegex.ReplaceAll(
				match,
				[]byte(`${1}_${2}`),
			))
		},
	)
	return converted, nil
}

// This method is used to filter out all the keys which are not supposed to be present inside the properties of an emitted event.
// Removes the key from the map only if it is present.
func removeSensitiveProperties(mp map[string]interface{}) error {
	if mp == nil {
		return fmt.Errorf("remove failed due to nil map")
	}

	for _, element := range propertiesToRemove {
		if mp[element] != nil {
			delete(mp, element)
		}
	}
	return nil
}

/*
broker interface used for different event-stream brokers.
exports a method `AddToBatch` -
  - events are added in batch and batch is emitted as soon as `batchlimit` exceeds, or `maxTime` exceeds
  - `batchlimit` and `maxTime` can be configured in the client

Use `AddToBatch` and `AddProspectEventsToBatch` method in go routine
*/
type Broker interface {
	AddToBatch(ctx context.Context, event Event)
	Close()
}

/*
RudderStackBroker implements `Broker` interface.
`AddToBatch` - will add an event to batch, it does not return error, just logs it
-- rudder analytics client retries failed events (~default 10 times)
-- client has an error channel where it notifies for the errors
*/
type RudderStackBroker struct {
	client analytics.Client
}

func NewRudderStackBroker(client analytics.Client) *RudderStackBroker {
	b := &RudderStackBroker{
		client: client,
	}
	return b
}

func (b *RudderStackBroker) AddToBatch(ctx context.Context, event Event) {
	// this function is mostly called in a separate go routine and any panic shouldn't crash the entire server.
	defer logger.RecoverPanicAndError(ctx)
	if cfg.IsTestTenantEnabled() {
		// skip events for test tenant
		return
	}

	msg := getMessageFromEvent(event, ctx)
	if msg == nil {
		logger.Error(ctx, "building message from event failed", zap.String("event-name", event.GetEventName()))
		return
	}
	if event.GetEventType() == EventTrack && event.GetEventProperties() != nil {
		err := b.client.Enqueue(msg)
		if err != nil {
			logger.Error(ctx, "error in enqueue msg", zap.Error(err))
		}
	} else if event.GetEventType() == EventIdentify && event.GetEventTraits() != nil {
		err := b.client.Enqueue(msg)
		if err != nil {
			logger.Error(ctx, "error in enqueue msg", zap.Error(err))
		}
	} else {
		logger.Error(ctx, "not enqueuing event to Rudder as Properties Or Traits have not been set", zap.String("event-name", event.GetEventName()))
	}

	return
}

func (b *RudderStackBroker) Close() {
	err := b.client.Close()
	if err != nil {
		logger.ErrorNoCtx("error in closing rudder client", zap.Error(err))
	}
}

type callback struct {
}

func NewCallback() analytics.Callback {
	return &callback{}
}

// Success is callback invoked by rudderstack when event is uploaded/emitted successfully
func (c *callback) Success(msg analytics.Message) {
	attributes := getEventAttributes(msg)
	logMsg := fmt.Sprintf("Event emitted successfully with event_id: %v event_name: %v trace_id: %v", attributes[EventId], attributes[EventName], attributes[TracedIdKey])
	eventName := attributes[EventName].(string)
	for _, event := range successEventsLogsList {
		if event == eventName {
			logger.InfoNoCtx(logMsg, zap.Any("event_attributes", attributes))
		} else {
			logger.DebugNoCtx(logMsg, zap.Any("event_attributes", attributes))
		}
	}
	if eventName == "" {
		eventName = EventNameUnspecified
	}
	metrics.RecordEventCount(metrics.EventSuccess, eventName)
}

// Failure is callback invoked by rudderstack when event is dropped after max attempts
func (c *callback) Failure(msg analytics.Message, err error) {
	attributes := getEventAttributes(msg)
	logMsg := fmt.Sprintf("Error in uploading event with event_id: %v event_name: %v trace_id: %v", attributes[EventId], attributes[EventName], attributes[TracedIdKey])
	logger.ErrorNoCtx(logMsg, zap.Any("event_attributes", attributes), zap.Error(err))
	eventName := attributes[EventName].(string)
	if eventName == "" {
		eventName = EventNameUnspecified
	}
	metrics.RecordEventCount(metrics.EventFailure, eventName)
}

// getEventAttributes extracts the event name, event id, anonymous id and timestamp from the msg for better debugging
// currently only above attributes are extracted, could add more attributes in future if required
func getEventAttributes(msg analytics.Message) map[string]interface{} {
	attributeMap := make(map[string]interface{})
	switch m := msg.(type) {
	case analytics.Track:
		attributes := m.Properties
		eventName := m.Event
		eventId := ""
		if _, ok := attributes[EventId]; ok {
			eventId = fmt.Sprint(attributes[EventId])
		}
		anonymousId := m.AnonymousId
		timestamp := m.Timestamp
		attributeMap[EventName] = eventName
		attributeMap[EventId] = eventId
		attributeMap[AnonymousId] = anonymousId
		attributeMap[TimeStamp] = timestamp.String()
		attributeMap[TracedIdKey] = attributes[TracedIdKey]
		return attributeMap
	case analytics.Identify:
		attributes := m.Traits
		eventName := "Identify"
		eventId := ""
		if _, ok := attributes[EventId]; ok {
			eventId = fmt.Sprint(attributes[EventId])
		}
		anonymousId := m.AnonymousId
		timestamp := m.Timestamp
		attributeMap[EventName] = eventName
		attributeMap[EventId] = eventId
		attributeMap[AnonymousId] = anonymousId
		attributeMap[TimeStamp] = timestamp.String()
		attributeMap[TracedIdKey] = attributes[TracedIdKey]
		return attributeMap
	default:
		logger.ErrorNoCtx("msg type not supported", zap.Any("message_type", m))
		return attributeMap
	}
}
