package salaryestimation

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	caxPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/typesv2"
)

const duration180Days = 6 * 30 * 24 * time.Hour

func GetAccountsToBeAnalysedForActor(ctx context.Context, connectedAccountClient caPb.ConnectedAccountClient, actorId string) ([]*caxPb.AccountDetails, error) {
	connAccountsRes, err := connectedAccountClient.GetAccounts(ctx, &caPb.GetAccountsRequest{
		ActorId: actorId,
		AccountFilterList: []caxPb.AccountFilter{
			// TODO(Brijesh): Discuss if we need Fi Federal Bank account filter
			caxPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
		},
		AccInstrumentTypeList: []caEnumsPb.AccInstrumentType{
			caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
		},
	})
	if err = epifigrpc.RPCError(connAccountsRes, err); err != nil {
		if connAccountsRes.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no connected accounts found")
		}
		return nil, errors.Wrap(err, "error getting connected accounts")
	}
	// TODO(Brijesh): Remove accounts already analysed recently but with no salary detected
	if len(connAccountsRes.GetAccountDetailsList()) == 0 {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no connected accounts found")
	}
	return connAccountsRes.GetAccountDetailsList(), nil
}

// AADataPullStatus is only for consent active accounts
type AADataPullStatus struct {
	totalAccounts      []*caxPb.AccountDetails
	failedAccounts     []*caxPb.AccountDetails
	successAccounts    []*caxPb.AccountDetails
	inProgressAccounts []*caxPb.AccountDetails
}

func (a *AADataPullStatus) GetSuccessAccount() []*caxPb.AccountDetails {
	return a.successAccounts
}

// IsDataPullInProgress if total active accounts is equal to total success + failed accounts pull then we can say that data pull is done
func (a *AADataPullStatus) IsDataPullInProgress() bool {
	return len(a.successAccounts)+len(a.failedAccounts) < len(a.totalAccounts)
}

func (a *AADataPullStatus) IsDataPullFailed() bool {
	return len(a.failedAccounts) == len(a.totalAccounts)
}

func (a *AADataPullStatus) IsDataPullSuccess() bool {
	return !a.IsDataPullInProgress() && !a.IsDataPullFailed()
}

func GetAADataPullStatus(ctx context.Context, connectedAccountClient caPb.ConnectedAccountClient, actorId string) (*AADataPullStatus, error) {
	res, err := connectedAccountClient.GetAccounts(ctx, &caPb.GetAccountsRequest{
		ActorId: actorId,
		AccountFilterList: []caxPb.AccountFilter{
			caxPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
		},
		AccInstrumentTypeList: []caEnumsPb.AccInstrumentType{caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to get account details for actor : %w", rpcErr)
	}
	if len(res.GetAccountDetailsList()) == 0 || res.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "no active connected accounts found")
		return nil, epifierrors.ErrRecordNotFound
	}
	dps := &AADataPullStatus{}
	for _, account := range res.GetAccountDetailsList() {
		dps.totalAccounts = append(dps.totalAccounts, account)
		var dataFetchRes *caPb.GetDataFetchAttemptsResponse
		dataFetchRes, err = connectedAccountClient.GetDataFetchAttempts(ctx, &caPb.GetDataFetchAttemptsRequest{
			PageContext: &rpc.PageContextRequest{PageSize: 500},
			Filter:      &caPb.GetDataFetchAttemptsRequest_AccountId{AccountId: account.GetAccountId()},
		})
		if err = epifigrpc.RPCError(dataFetchRes, err); err != nil {
			if dataFetchRes.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, fmt.Sprintf("no data fetch attempts found for account id: %s", account.GetAccountId()))
				continue
			}
			return nil, errors.Wrapf(err, "error getting data fetch attempts for account id: %s", account.GetAccountId())
		}
		dataFetchIntervals := mergeDataFetchIntervals(dataFetchRes.GetDataFetchAttemptDetailsList())
		accountDataPresent := false
		for _, interval := range dataFetchIntervals {
			dataFetchDuration := interval.GetEndTime().AsTime().Sub(interval.GetStartTime().AsTime())
			if dataFetchDuration >= duration180Days {
				accountDataPresent = true
				break
			}
		}
		if accountDataPresent {
			dps.successAccounts = append(dps.successAccounts, account)
			continue
		}
		if len(dataFetchRes.GetDataFetchAttemptDetailsList()) > 0 &&
			(dataFetchRes.GetDataFetchAttemptDetailsList()[0].GetFetchStatus() == caEnumsPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION ||
				dataFetchRes.GetDataFetchAttemptDetailsList()[0].GetFetchStatus() == caEnumsPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED) {
			dps.failedAccounts = append(dps.failedAccounts, account)
			continue
		}
		dps.inProgressAccounts = append(dps.inProgressAccounts, account)
	}
	logger.Info(ctx, "data pull status",
		zap.Strings("success_accounts", lo.Map(dps.successAccounts, func(a *caxPb.AccountDetails, _ int) string { return a.GetAccountId() })),
		zap.Strings("in_progress_accounts", lo.Map(dps.inProgressAccounts, func(a *caxPb.AccountDetails, _ int) string { return a.GetAccountId() })),
		zap.Strings("failed_accounts", lo.Map(dps.failedAccounts, func(a *caxPb.AccountDetails, _ int) string { return a.GetAccountId() })),
	)
	return dps, nil
}

func mergeDataFetchIntervals(dataFetchAttempts []*caxPb.DataFetchAttemptDetails) []*typesv2.Interval {
	var rawIntervals []*typesv2.Interval
	for _, attempt := range dataFetchAttempts {
		rawIntervals = append(rawIntervals, &typesv2.Interval{
			StartTime: attempt.GetDataRangeFrom(),
			EndTime:   attempt.GetDataRangeTo(),
		})
	}
	if len(rawIntervals) == 0 {
		return nil
	}
	sort.Slice(rawIntervals, func(i, j int) bool {
		return rawIntervals[i].GetStartTime().AsTime().Before(rawIntervals[j].GetStartTime().AsTime())
	})
	mergedIntervals := []*typesv2.Interval{rawIntervals[0]}
	for i := 1; i < len(rawIntervals); i++ {
		current := mergedIntervals[len(mergedIntervals)-1]
		next := rawIntervals[i]
		// If the current interval overlaps with the next interval, merge them
		if !current.GetEndTime().AsTime().Before(next.GetStartTime().AsTime()) {
			if next.GetEndTime().AsTime().After(current.GetEndTime().AsTime()) {
				current.EndTime = next.GetEndTime()
			}
		} else {
			// Add as a new interval when no overlap found
			mergedIntervals = append(mergedIntervals, next)
		}
	}
	return mergedIntervals
}
