package loans

import "strconv"

func ConvertTenureFromMonthsToYearsAndMonths(totalMonths int32) string {
	var periodText string
	years := totalMonths / 12
	months := totalMonths % 12

	if years > 0 {
		periodText += strconv.Itoa(int(years)) + " " + "year"
		if years > 1 {
			periodText += "s"
		}
	}
	if months > 0 {
		if years > 0 {
			periodText += " "
		}
		periodText += strconv.Itoa(int(months)) + " " + "month"
		if months > 1 {
			periodText += "s"
		}
	}
	return periodText
}
