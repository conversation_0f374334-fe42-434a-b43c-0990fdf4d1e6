package networth

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/logger"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"

	"github.com/epifi/be-common/pkg/money"
	mfAnalyserVariablePb "github.com/epifi/gamma/api/analyser/variables/mutualfund"
)

type DailyChangeDetails struct {
	DailyNavPercentageChange float64
	CurrentValue             float64
	SchemeAnalytics          *mfAnalyserVariablePb.SchemeAnalytics
}

func CalculateDailyChange(details *DailyChangeDetails) float64 {
	return (details.CurrentValue * details.DailyNavPercentageChange) / (100 + details.DailyNavPercentageChange)
}

// CalculateAggregatedMfValues returns aggregated currentValue, aggregated previous value and aggregated daily change
func CalculateAggregatedMfValues(dailyChangeList []*DailyChangeDetails) (float64, float64, float64) {
	aggregatedDailyChange := 0.0
	aggregatedCurrentValue := 0.0
	aggregatedPreviousValue := 0.0
	for _, dailyChange := range dailyChangeList {
		currentSchemeChangeDiff := CalculateDailyChange(dailyChange)
		aggregatedDailyChange += currentSchemeChangeDiff
		aggregatedCurrentValue += dailyChange.CurrentValue
		aggregatedPreviousValue += dailyChange.CurrentValue - currentSchemeChangeDiff
	}
	return aggregatedCurrentValue, aggregatedPreviousValue, aggregatedDailyChange
}

// GetMfAggregatedValues aggregates values from mf scheme analytics response
// and returns totalCurrentValue, totalPreviousValue, totalDailyChange and percentageChange(0 if previous value is not present)
func GetMfAggregatedValues(ctx context.Context, mfSchemeAnalytics *mfAnalyserVariablePb.MfSchemeAnalytics) (float64, float64, float64, float64) {
	sortedMfDailyChangeList := GetSchemeAnalyticsBySortedDayChange(ctx, mfSchemeAnalytics)
	mfAggCurVal, mfAggPrevVal, mfAggDailyChange := CalculateAggregatedMfValues(sortedMfDailyChangeList)
	if mfAggPrevVal == 0 {
		return 0, 0, 0, 0
	}
	percentageChange := (mfAggDailyChange / mfAggPrevVal) * 100

	return mfAggCurVal, mfAggPrevVal, mfAggDailyChange, percentageChange
}

func GetSchemeAnalyticsBySortedDayChange(ctx context.Context, schemeDetails *mfAnalyserVariablePb.MfSchemeAnalytics) []*DailyChangeDetails {
	var mfDailyChangeList []*DailyChangeDetails
	for _, scheme := range schemeDetails.GetSchemeAnalytics() {
		if !validateCurrentScheme(scheme) {
			continue
		}
		percentageChange := CalculateNavChangePercentage(ctx, scheme.GetSchemeDetail())
		if percentageChange == 0 {
			continue
		}
		mfDailyChangeList = append(mfDailyChangeList, &DailyChangeDetails{
			DailyNavPercentageChange: percentageChange,
			CurrentValue:             money.ToFloat(scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails().GetCurrentValue()),
			SchemeAnalytics:          scheme,
		})
	}
	sort.Slice(mfDailyChangeList, func(i, j int) bool {
		valI := mfDailyChangeList[i].CurrentValue
		valJ := mfDailyChangeList[j].CurrentValue
		return valI > valJ
	})
	return mfDailyChangeList
}

func validateCurrentScheme(scheme *mfAnalyserVariablePb.SchemeAnalytics) bool {
	enrichedSchemeDetails := scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails()
	if money.IsZero(enrichedSchemeDetails.GetInvestedValue()) || money.IsZero(enrichedSchemeDetails.GetCurrentValue()) {
		return false
	}
	return true
}

func WeeklyChangeMapCreation(details []*networthPb.AssetValueChange) map[string]*networthPb.AssetValueChange {
	weeklyChangeMap := make(map[string]*networthPb.AssetValueChange)
	for _, detail := range details {
		weeklyChangeMap[detail.GetAssetId()] = detail
	}
	return weeklyChangeMap
}

func CalculateWeeklyPercentageChange(details *networthPb.AssetValueChange) float64 {
	return (details.GetChange() * 100) / money.ToFloat(details.GetInitialDateValue())
}

func CalculateNavChangePercentage(ctx context.Context, fund *mfPb.MutualFund) float64 {
	navAmt, err := GetAmountFromMoney(ctx, fund.GetNav())
	if err != nil {
		return 0
	}
	delta := fund.GetDailyNavChange() * 100 / float32(math.Max(1, navAmt-float64(fund.GetDailyNavChange())))
	val, err := strconv.ParseFloat(fmt.Sprintf("%.2f", delta), 64)
	if err != nil {
		return 0
	}
	return val
}

func GetAmountFromMoney(ctx context.Context, nav *moneyPb.Money) (float64, error) {
	amt, err := money.ToString(nav, 2)
	if err != nil {
		logger.Error(ctx, "error getting amount from NAV", zap.Any("NAV", nav), zap.Error(err))
		return 0, err
	}
	val, err := strconv.ParseFloat(amt, 64)
	if err != nil {
		return 0, err
	}
	return val, nil
}
