package rewards

import (
	rewardsPb "github.com/epifi/gamma/api/rewards"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
)

const (
	ChequeBookFeesRefundRewardOfferId = "3432aa71-2b8d-4d5b-8d5c-2e0f0262a0b4"
	FiCoinsToCashConversionRatio      = 0.02
	// RewardsSecondaryRefIdDefaultValue is default value of SecondaryRefId column in rewards table
	RewardsSecondaryRefIdDefaultValue = "NA"
)

var (
	BeTierToMaxFiCoinsAnchorRewardsForCardSpendsCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            1000,
		beTieringExtPb.Tier_TIER_FI_PLUS:             4000,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         8000,
		beTieringExtPb.Tier_TIER_FI_SALARY:           8000,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      8000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 4000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 4000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 4000,
	}
	BeTierToMaxFiCoinsAnchorRewardsForUpiSpendsCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            500,
		beTieringExtPb.Tier_TIER_FI_PLUS:             1000,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         2000,
		beTieringExtPb.Tier_TIER_FI_SALARY:           2000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 1000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 1000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 1000,
	}
	BeTierToCardSpendsToRewardUnitsFactorMap = map[beTieringExtPb.Tier]float64{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            0.05,
		beTieringExtPb.Tier_TIER_FI_PLUS:             0.1,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         0.2,
		beTieringExtPb.Tier_TIER_FI_SALARY:           0.2,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      0.2,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 0.1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 0.1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 0.1,
	}
	BeTierToUpiSpendsToRewardUnitsFactorMap = map[beTieringExtPb.Tier]float64{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            0.025,
		beTieringExtPb.Tier_TIER_FI_PLUS:             0.05,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         0.1,
		beTieringExtPb.Tier_TIER_FI_SALARY:           0.1,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      0.1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 0.1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 0.1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 0.1,
	}
	BeTierToCashbackFactorMap = map[beTieringExtPb.Tier]float64{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            0,
		beTieringExtPb.Tier_TIER_FI_PLUS:             0.01,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         0.02,
		beTieringExtPb.Tier_TIER_FI_SALARY:           0.02,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      0.01,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 0.01,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 0.02,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 0.03,
	}
	BeTierToCashbackMonthlyCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            0,
		beTieringExtPb.Tier_TIER_FI_PLUS:             100,
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     100,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         500,
		beTieringExtPb.Tier_TIER_FI_SALARY:           500,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      300,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 100,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 250,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 1000,
	}
	BeTierToCashbackDailyCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_UNSPECIFIED:         0,
		beTieringExtPb.Tier_TIER_FI_BASIC:            0,
		beTieringExtPb.Tier_TIER_FI_PLUS:             20,
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     20,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         100,
		beTieringExtPb.Tier_TIER_FI_SALARY:           100,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      100,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: 100,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: 100,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 100,
	}

	BeTierToFiCoinsDailyCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_FI_PLUS:             1000,
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     1000,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         5000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 5000,
		beTieringExtPb.Tier_TIER_FI_SALARY:           5000,
	}
	BeTierToFiCoinsMonthlyCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_FI_PLUS:             5000,
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     5000,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         25000,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 50000,
		beTieringExtPb.Tier_TIER_FI_SALARY:           50000,
	}
	BeTierToFiCoinsTxnCapMap = map[beTieringExtPb.Tier]uint32{
		beTieringExtPb.Tier_TIER_FI_PLUS:             500,
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     500,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         1500,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: 1500,
		beTieringExtPb.Tier_TIER_FI_SALARY:           1500,
	}
	BeTierToRewardsUserTierMap = map[beTieringExtPb.Tier]rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_UserTier{
		beTieringExtPb.Tier_TIER_FI_REGULAR:          rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_REGULAR,
		beTieringExtPb.Tier_TIER_FI_BASIC:            rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_BASIC,
		beTieringExtPb.Tier_TIER_FI_PLUS:             rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_PLUS,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_INFINITE,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_2,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_3,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY_LITE,
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY_BASIC,
		beTieringExtPb.Tier_TIER_FI_SALARY:           rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY,
	}
	RewardsUserTierToBETierMap = map[rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_UserTier]beTieringExtPb.Tier{
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_REGULAR:          beTieringExtPb.Tier_TIER_FI_REGULAR,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_BASIC:            beTieringExtPb.Tier_TIER_FI_BASIC,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_PLUS:             beTieringExtPb.Tier_TIER_FI_PLUS,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_INFINITE:         beTieringExtPb.Tier_TIER_FI_INFINITE,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_1: beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_2: beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_3: beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY_LITE:      beTieringExtPb.Tier_TIER_FI_SALARY_LITE,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY_BASIC:     beTieringExtPb.Tier_TIER_FI_SALARY_BASIC,
		rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY:           beTieringExtPb.Tier_TIER_FI_SALARY,
	}
	// Map of tiers to their corresponding offer types
	BeTierToRewardOfferTypeMap = map[beTieringExtPb.Tier][]rewardsPb.RewardOfferType{
		beTieringExtPb.Tier_TIER_FI_PLUS:             {rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: {rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: {rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: {rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      {rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_INFINITE:         {rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER, rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_SALARY:           {rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER, rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER},
		beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:     {rewardsPb.RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER},
	}

	// ForexRefundEligibleBeTierList : List of tiers that are eligible for Forex refunds
	ForexRefundEligibleBeTierList = []beTieringExtPb.Tier{
		beTieringExtPb.Tier_TIER_FI_PLUS,
		beTieringExtPb.Tier_TIER_FI_INFINITE,
		beTieringExtPb.Tier_TIER_FI_SALARY,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
	}
)

func GetRewardsTierEnum(tier beTieringExtPb.Tier) rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest_UserTier {
	return BeTierToRewardsUserTierMap[tier]
}
