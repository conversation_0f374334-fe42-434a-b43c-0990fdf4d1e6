package savings

import (
	"fmt"

	"github.com/epifi/be-common/pkg/constants"

	typesAccountPb "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/user"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

const (
	ReVkycClosureTitle    = "Your savings account will be closed soon"
	ReVkycClosureSubTitle = "As part of our partner bank’s periodic check-in, starting November, we had reached out multiple times to complete your Video KYC by 03 May 2025. Since it has not been completed within the required time, as communicated to you earlier, your Federal Bank Savings Account linked to Fi will be permanently closed soon.\n\nTo transfer any pending balance out of your Federal Bank Savings Account, please share your alternate bank account details with us."
)

var (
	AccessReasonToKnowMoreSubtl = map[user.AccessRevokeReason]string{
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED:            constants.KnowMoreAccClosedDetails,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY: fmt.Sprintf(constants.KnowMoreMinKycAccClosedDetails, vkycPkg.AccountClosureDaysLimit),
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING:  constants.KnowMoreLSOUserDetails,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED:  ReVkycClosureSubTitle,
	}

	AccessReasonToBalTxnSubtl = map[user.AccessRevokeReason]string{
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY: fmt.Sprintf(constants.AcctClosureTxSubtitle, vkycPkg.AccountClosureDaysLimit),
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING:  constants.AcctClosureLSOUserBalTxSubtitle,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED:            constants.KnowMoreAccClosedDetails,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED:  ReVkycClosureSubTitle,
	}

	AccessReasonToBalTxnTitle = map[user.AccessRevokeReason]string{
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY: constants.AcctClosureBalTxTitle,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING:  constants.AcctClosureBalTxTitle,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED:            constants.AcctClosureBalTxTitle,
		user.AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED:  ReVkycClosureTitle,
	}

	// SupportedSavingsAccountProductOfferings list of all the supported Savings Account Product offerings
	// NOTE: ordering of items in the list doesn't mean anything, if there is any order specific requirement then new list should be maintained.
	SupportedSavingsAccountProductOfferings = []typesAccountPb.AccountProductOffering{
		typesAccountPb.AccountProductOffering_APO_REGULAR,
		typesAccountPb.AccountProductOffering_APO_NRO,
		typesAccountPb.AccountProductOffering_APO_NRE,
	}
)
