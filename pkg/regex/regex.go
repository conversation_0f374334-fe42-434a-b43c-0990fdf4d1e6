package regex

import "regexp"

const (
	PersonalPan    = `^[A-Za-z]{3}P[A-Za-z]{1}[0-9]{4}[A-Za-z]{1}$`
	Aadhaar4Digit  = `^[0-9]{4}$`
	DrivingLicense = `^[A-Za-z]{2}[/ -]?[0-9]{2}[/ -]?([0-9]{4,12})$`
)

var (
	PersonalPanRegex    = regexp.MustCompile(PersonalPan)
	Aadhaar4DigitRegex  = regexp.MustCompile(Aadhaar4Digit)
	DrivingLicenseRegex = regexp.MustCompile(DrivingLicense)
)

// IsValidPersonalPAN checks whether given PAN is a personal PAN
func IsValidPersonalPAN(pan string) bool {
	return PersonalPanRegex.MatchString(pan)
}

// IsValidAadhaar4Digit checks whether given aadhaar is valid 4 digit aadhaar or not
func IsValidAadhaar4Digit(aadhaar4Digit string) bool {
	return Aadhaar4DigitRegex.MatchString(aadhaar4Digit)
}

// IsValidDrivingLicense checks whether given license is a valid driving license or not
func IsValidDrivingLicense(license string) bool {
	return DrivingLicenseRegex.MatchString(license)
}
