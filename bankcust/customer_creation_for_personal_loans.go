package bankcust

import (
	"context"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/kyc"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"

	"go.uber.org/zap"
)

type PLCustomerCreationGenerator struct {
	fp *FederalProc
}

var _ CustomerCreationGenerator = &PLCustomerCreationGenerator{}

func (pl *PLCustomerCreationGenerator) CreateCustomer(ctx context.Context, request *CreateCustomerRequest) (*CreateCustomerResponse, error) {
	req, err := pl.generateCreateCustomerRequest(ctx, request.GetActorId(), request.GetRequestId(), request.GetBankCustomer())
	if err != nil {
		return nil, err
	}
	res, err := pl.fp.vgCustomerClient.CreateLoanCustomer(ctx, req)
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
		return nil, grpcErr
	}
	return &CreateCustomerResponse{
		Status:       res.GetStatus(),
		VendorStatus: res.GetVendorStatus(),
	}, nil
}

func (pl *PLCustomerCreationGenerator) CheckCustomerCreationStatus(ctx context.Context, req *CheckCustomerCreationStatusRequest) (*CheckCustomerCreationStatusResponse, error) {
	res, err := pl.fp.vgCustomerClient.LoanCustomerCreationStatus(ctx, &customer.LoanCustomerCreationStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		RequestId:    idgen.RandAlphaNumericString(25),
		CifRequestId: req.GetRequestId(),
	})
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
		return nil, grpcErr
	}
	return &CheckCustomerCreationStatusResponse{
		Status:         res.GetStatus(),
		VendorStatus:   res.GetVendorStatus(),
		BankCustomerId: res.GetCustomerId(),
		CifCreatedTime: res.GetCifCreatedTime(),
	}, nil
}

func (pl *PLCustomerCreationGenerator) generateCreateCustomerRequest(ctx context.Context, actorId string, reqId string, bankCustomer *bankcust.BankCustomer) (*customer.CreateLoanCustomerRequest, error) {
	userResp, err := pl.fp.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_Id{
			Id: bankCustomer.GetUserId(),
		},
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		logger.Error(ctx, "error in fetching user", zap.Error(err))
		return nil, err
	}

	var (
		profile           = userResp.GetUser().GetProfile()
		employmentDetails *usersPb.DataVerificationDetail_EmploymentDetail
	)
	for _, verificationDetails := range userResp.GetUser().GetDataVerificationDetails().GetDataVerificationDetails() {
		if verificationDetails.GetDataType() == usersPb.DataType_DATA_TYPE_EMPLOYMENT_DETAIL && verificationDetails.GetVerificationMethod() == usersPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA {
			employmentDetails = verificationDetails.GetEmploymentDetail()
			// NOTE: We are not breaking here as we want the latest employment details from the list.
		}
	}

	kycRes, err := pl.fp.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId:   actorId,
		SignImage: true,
	})
	if err = epifigrpc.RPCError(kycRes, err); err != nil {
		logger.Error(ctx, "error while fetching signed kyc record", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	permanentAddress, communicationAddress, err := pl.fp.methodAbstraction.GetAddresses(ctx, actorId, bankCustomer.GetUserId(), kycRes.GetKycType(), kycRes.GetKycRecord(), bankCustomer.GetSource())
	if err != nil {
		logger.Error(ctx, "error in fetching addresses", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	identityProof, addressProof, err := fetchPOIandPOAFromKYCRecord(kycRes.GetKycRecord(), kycRes.GetKycType())
	if err != nil {
		logger.Error(ctx, "failed to fetch identification proof/ address proof", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	customerDetails, err := pl.fp.preApprovedLoansClient.GetFederalLoanCustomerDetails(ctx, &preApprovedLoanPb.GetFederalLoanCustomerDetailsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(customerDetails, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching customer details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return nil, rpcErr
	}

	// TODO: The following fields are not captured by us, so they are not included in the request:
	// 1. MaritalStatus
	// 2. SignImage
	// If these fields are mandatory for customer creation as per Federal requirements,
	// we need to determine how to collect and populate them in the request.

	return &customer.CreateLoanCustomerRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		RequestId:            reqId, // Set request ID in the vendor request
		SolId:                bankCustomer.GetVendorMetadata().GetFederalMetadata().GetSolId(),
		BreRefNumber:         customerDetails.GetBreRefNumber(),
		UidNo:                bankCustomer.GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo(),
		Name:                 profile.GetKycName(),
		FatherName:           profile.GetFatherName().ToString(),
		MotherName:           profile.GetMotherName().ToString(),
		Email:                profile.GetEmail(),
		DateOfBirth:          profile.GetDateOfBirth(),
		Gender:               profile.GetKycGender(),
		PhoneNumber:          profile.GetPhoneNumber(),
		CommunicationAddress: communicationAddress,
		PermanentAddress:     permanentAddress,
		IdentityProof:        identityProof,
		AddressProof:         addressProof,
		PanNumber:            profile.GetPAN(),
		EmploymentType:       employment.GetEmploymentType(employmentDetails.GetEmploymentType()),
		AnnualIncome:         float32(employmentDetails.GetMonthlyIncome().GetUnits() * 12),
		OccupationType:       employmentDetails.GetOccupationType(),
		Community:            profile.GetCommunity(),
		Qualification:        profile.GetQualification(),
		Designation:          profile.GetDesignation(),
		Category:             profile.GetCategory(),
		DisabilityType:       profile.GetDisabilityType(),
	}, nil
}
