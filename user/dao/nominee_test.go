package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/runtime/protoimpl"
	"gorm.io/gorm"

	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/user/config"
)

const nomineesTable = "nominees"

var (
	nomineeTs *nomineeTestSuite

	nomineesTables = []string{nomineesTable}
)

// ContactInfos
var (
	contactInfo = &types.ContactInfo{
		PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999900000},
		EmailId:     "<EMAIL>",
		Address:     &types.PostalAddress{RegionCode: "Region", Locality: "Locality"},
		AddressType: types.AddressType_PERMANENT,
	}
)

// Guardians
var (
	guardianInfo = &types.GuardianInfo{
		Relationship: types.RelationType_DE_FACTO_GUARDIAN,
		Name:         "Voldemort",
		ContactInfo:  contactInfo,
	}
)

// Nominees
var (
	adultNominee = &types.Nominee{
		ActorId:      "actor-000",
		Relationship: types.RelationType_BROTHER,
		Name:         "Severus Snape",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
	}

	minorNominee = &types.Nominee{
		ActorId:      "actor-001",
		Relationship: types.RelationType_BROTHER,
		Name:         "Tom Riddle",
		Dob:          &timestamp.Timestamp{Seconds: 1420070400},
		ContactInfo:  contactInfo,
		GuardianInfo: guardianInfo,
	}

	nomineeWithDocument = &types.Nominee{
		ActorId:      "actor-002",
		Relationship: types.RelationType_BROTHER,
		Name:         "Top Snape",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
		NomineeDocument: &types.NomineeDocument{
			DocumentNumber: "**********",
			DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
		},
	}

	nomineeWithPan = &types.Nominee{
		ActorId:      "actor-003",
		Relationship: types.RelationType_FATHER,
		Name:         "Top Payn",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
		Pan:          "**********",
	}

	nomineeWithPanReturn = &types.Nominee{
		ActorId:      "actor-003",
		Relationship: types.RelationType_FATHER,
		Name:         "Top Payn",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
		NomineeDocument: &types.NomineeDocument{
			DocumentNumber: "**********",
			DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
		},
	}

	nomineeWithId1 = &types.Nominee{
		Id:           "NOM001",
		ActorId:      "actor-001",
		Relationship: types.RelationType_BROTHER,
		Name:         "Tom Riddle",
		Dob:          &timestamp.Timestamp{Seconds: 1420070400},
		ContactInfo:  contactInfo,
		GuardianInfo: guardianInfo,
	}

	nomineeWithId2 = &types.Nominee{
		Id:           "NOM002",
		ActorId:      "actor-001",
		Relationship: types.RelationType_SISTER,
		Name:         "Rowena Ravenclaw",
		Dob:          &timestamp.Timestamp{Seconds: 1420070400},
		ContactInfo:  contactInfo,
		GuardianInfo: guardianInfo,
	}

	adultNomineeWithId = &types.Nominee{
		Id:           "NOM003",
		ActorId:      "actor-002",
		Relationship: types.RelationType_BROTHER,
		Name:         "Severus Snape",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
	}

	nomineeWithDocumentWithId = &types.Nominee{
		Id:           "NOM004",
		ActorId:      "actor-003",
		Relationship: types.RelationType_BROTHER,
		Name:         "Top Snape",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
		NomineeDocument: &types.NomineeDocument{
			DocumentNumber: "**********",
			DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
		},
	}

	nomineeWithExistingPan = &types.Nominee{
		Id:           "NOM005",
		ActorId:      "actor-004",
		Relationship: types.RelationType_BROTHER,
		Name:         "Snape Top",
		Dob:          &timestamp.Timestamp{Seconds: 654727808},
		ContactInfo:  contactInfo,
		NomineeDocument: &types.NomineeDocument{
			DocumentNumber: "**********",
			DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
		},
	}
)

// Tests for storage
type nomineeTestSuite struct {
	db   *gorm.DB
	conf *config.Config
	dao  NomineeDao
}

func NewNomineeTestSuite(db *gorm.DB, conf *config.Config, dao NomineeDao) *nomineeTestSuite {
	return &nomineeTestSuite{
		db:   db,
		conf: conf,
		dao:  dao,
	}
}

func TestNomineesCrdb_Create(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, nomineeTs.conf.EpifiDb.GetName(), nomineeTs.db, nomineesTables)

	tests := []struct {
		name     string
		nominee  *types.Nominee
		response *types.Nominee
	}{
		{
			name:     "Successful with adult nominee",
			nominee:  adultNominee,
			response: adultNominee,
		},
		{
			name:     "Successful with minor nominee",
			nominee:  minorNominee,
			response: minorNominee,
		},
		{
			name:     "Successful with document",
			nominee:  nomineeWithDocument,
			response: nomineeWithDocument,
		},
		{
			name:     "Successful with pan",
			nominee:  nomineeWithPan,
			response: nomineeWithPanReturn,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			resp, err := nomineeTs.dao.Create(context.Background(), test.nominee)

			assert.Nil(t, err)
			assert.NotNil(t, resp)
			assert.NotNil(t, resp.Id)
			assert.NotNil(t, test.response, resp)
		})
	}
}

func TestNomineesCrdb_GetNominee(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, nomineeTs.conf.EpifiDb.GetName(), nomineeTs.db, nomineesTables)

	tests := []struct {
		name          string
		actorId       string
		nomineeId     string
		errorExpected bool
		nominee       *types.Nominee
	}{
		{
			name:          "no nominee for this actor",
			actorId:       "actor-000",
			nomineeId:     "NOM001",
			errorExpected: true,
			nominee:       nil,
		},
		{
			name:          "Successful nominee fetch - minor",
			actorId:       "actor-001",
			nomineeId:     "NOM001",
			errorExpected: false,
			nominee:       nomineeWithId1,
		},
		{
			name:          "Successful nominee fetch - adult",
			actorId:       "actor-002",
			nomineeId:     "NOM003",
			errorExpected: false,
			nominee:       adultNomineeWithId,
		},
		{
			name:          "Successful nominee fetch - with document",
			actorId:       "actor-003",
			nomineeId:     "NOM004",
			errorExpected: false,
			nominee:       nomineeWithDocumentWithId,
		},
		{
			name:          "Successful nominee fetch - with existing pan",
			actorId:       "actor-004",
			nomineeId:     "NOM005",
			errorExpected: false,
			nominee:       nomineeWithExistingPan,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			resp, err := nomineeTs.dao.GetNominee(context.Background(), test.actorId, test.nomineeId)

			assert.Equal(t, test.errorExpected, err != nil)
			assert.Equal(t, IsEqualValue(test.nominee, resp), true)
		})
	}
}

func TestNomineesCrdb_GetNominees(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, nomineeTs.conf.EpifiDb.GetName(), nomineeTs.db, nomineesTables)

	tests := []struct {
		name     string
		actorId  string
		nominees []*types.Nominee
	}{

		{
			name:     "No nominees for this actor",
			actorId:  "actor-000",
			nominees: nil,
		},
		{
			name:     "Successful fetch of nominees",
			actorId:  "actor-001",
			nominees: []*types.Nominee{nomineeWithId2, nomineeWithId1},
		},
		{
			name:     "Successful fetch of nominees with document",
			actorId:  "actor-003",
			nominees: []*types.Nominee{nomineeWithDocumentWithId},
		},
		{
			name:     "Successful fetch of nominee with existing pan",
			actorId:  "actor-004",
			nominees: []*types.Nominee{nomineeWithExistingPan},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			resp, err := nomineeTs.dao.GetNominees(context.Background(), test.actorId)

			assert.Nil(t, err)
			assert.Equal(t, IsEqualValue(test.nominees, resp), true)
		})
	}
}

// Compares the values in struct objects and not the values of the addresses that struct members point to
// While comparing each field in the struct, we ignore the entities added by proto
func IsEqualValue(x, y interface{}) bool {
	return cmp.Equal(x, y, cmpopts.IgnoreTypes(protoimpl.MessageState{}, protoimpl.SizeCache(0), protoimpl.UnknownFields{}))
}
