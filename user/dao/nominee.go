package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/user/dao/model"

	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type NomineeCrdb struct {
	db    dbTypes.EpifiCRDB
	idGen idgen.IdGenerator
}

var _ NomineeDao = &NomineeCrdb{}

func NewNomineesCrdb(db dbTypes.EpifiCRDB, idGen idgen.IdGenerator) *NomineeCrdb {
	return &NomineeCrdb{db: db, idGen: idGen}
}

func (crdb *NomineeCrdb) Create(ctx context.Context, nominee *types.Nominee) (*types.Nominee, error) {
	defer metric_util.TrackDuration("user/dao", "NomineeCrdb", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)

	nomineeModel, err := convertToModel(nominee)
	if err != nil {
		logger.Error(ctx, "failed to convert to nominee model", zap.Any(logger.NOMINEE, nominee), zap.Error(err))
		return nil, err
	}

	nomineeModel.Id = getIdForNominee(12)

	if err = db.Create(nomineeModel).Error; err != nil {
		logger.Error(ctx, "failed to create nominee entry in DB", zap.Any("nominee entry", nomineeModel), zap.Error(err))
		return nil, err
	}

	nomineeProto, err := convertToProto(nomineeModel)
	if err != nil {
		logger.Error(ctx, "failed to convert nominee model to nominee proto", zap.Any("nominee model", nomineeModel), zap.Error(err))
		return nil, err
	}
	return nomineeProto, nil
}

func (crdb *NomineeCrdb) GetNominee(ctx context.Context, actorId string, nomineeId string) (*types.Nominee, error) {
	defer metric_util.TrackDuration("user/dao", "NomineeCrdb", "GetNominee", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("failed to call GetNominee: Empty actor ID")
	}
	if nomineeId == "" {
		return nil, fmt.Errorf("failed to call GetNominee: Empty nominee ID")
	}

	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)

	result := model.Nominee{}
	res := db.Where(&model.Nominee{ActorId: actorId, Id: nomineeId}).First(&result)

	if errors.Is(res.Error, gorm.ErrRecordNotFound) {
		logger.Error(ctx, fmt.Sprintf("no nominee found for actorID: %v and nomineeID: %v", actorId, nomineeId))
		return nil, res.Error
	}

	if res.Error != nil {
		logger.Error(ctx, "failed to fetch nominee", zap.String("actor id", actorId), zap.String("nominee id", nomineeId), zap.Error(res.Error))
		return nil, res.Error
	}

	nomineeProto, err := convertToProto(&result)
	if err != nil {
		logger.Error(ctx, "failed to convert nominee model to nominee proto", zap.Any("nominee model", result), zap.Error(err))
		return nil, err
	}
	return nomineeProto, nil
}

func (crdb *NomineeCrdb) GetNominees(ctx context.Context, actorId string) ([]*types.Nominee, error) {
	defer metric_util.TrackDuration("user/dao", "NomineeCrdb", "GetNominees", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("failed to call GetNominees: Empty actor ID")
	}

	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)

	var nominees []*types.Nominee
	var result []*model.Nominee
	res := db.Where(&model.Nominee{ActorId: actorId}).Order(datetime.CreatedAt + " " + datetime.Descending).Find(&result)

	if res.Error != nil {
		logger.Error(ctx, "failed to fetch nominees for Actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(res.Error))
		return nil, res.Error
	}

	if len(result) == 0 {
		logger.Debug(ctx, "No nominees for actor", zap.String(logger.ACTOR_ID_V2, actorId))
	}

	for _, record := range result {
		nomineeProto, err := convertToProto(record)
		if err != nil {
			logger.Error(ctx, "failed to convert nominee model to nominee proto", zap.Any("nominee model", record), zap.Error(err))
			return nil, err
		}
		nominees = append(nominees, nomineeProto)
	}
	return nominees, nil
}

func convertToModel(nominee *types.Nominee) (_ *model.Nominee, er error) {
	defer func() {
		if err := recover(); err != nil {
			er = fmt.Errorf("panic while converting nominee to model")
		}
	}()

	dob, err := ptypes.Timestamp(nominee.Dob)
	if err != nil {
		return nil, fmt.Errorf("failed to convert Nominee DOB from proto to model: %w", err)
	}

	document := nominee.NomineeDocument

	// For older clients where document is nil and pan is provided, then create a nominee document and store it.
	if document == nil && nominee.Pan != "" {
		document = &types.NomineeDocument{
			DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
			DocumentNumber: nominee.Pan,
		}
	}

	nomineeModel := &model.Nominee{
		Id:           nominee.Id,
		ActorId:      nominee.ActorId,
		Relationship: nominee.Relationship,
		Name:         nominee.Name,
		Dob:          dob,
		Address:      nominee.ContactInfo.Address,
		AddressType:  nominee.ContactInfo.AddressType,
		PhoneNumber:  nominee.ContactInfo.PhoneNumber,
		EmailId:      nominee.ContactInfo.EmailId,
		GuardianInfo: nominee.GuardianInfo,
		Document:     document,
	}
	return nomineeModel, nil
}

func convertToProto(nominee *model.Nominee) (*types.Nominee, error) {
	relationship := nominee.Relationship
	addressType := nominee.AddressType
	contactInfo := &types.ContactInfo{
		PhoneNumber: nominee.PhoneNumber,
		EmailId:     nominee.EmailId,
		Address:     nominee.Address,
		AddressType: addressType,
	}
	dob, err := ptypes.TimestampProto(nominee.Dob)
	if err != nil {
		return nil, fmt.Errorf("failed to convert DOB from model to proto: %w", err)
	}
	guardianInfo := nominee.GuardianInfo
	if guardianInfo != nil && guardianInfo.Name == "" && guardianInfo.ContactInfo == nil && guardianInfo.Relationship == types.RelationType_RELATION_TYPE_UNSPECIFIED {
		guardianInfo = nil
	}

	document := nominee.Document

	// For older users where Pan is there in DB, convert that to nominee document
	if nominee.Pan != "" {
		document = &types.NomineeDocument{
			DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
			DocumentNumber: nominee.Pan,
		}
	}

	nomineeProto := &types.Nominee{
		Id:              nominee.Id,
		ActorId:         nominee.ActorId,
		Relationship:    relationship,
		Name:            nominee.Name,
		Dob:             dob,
		ContactInfo:     contactInfo,
		GuardianInfo:    guardianInfo,
		NomineeDocument: document,
	}
	return nomineeProto, nil
}

func getIdForNominee(n int) string {
	id := fmt.Sprintf("%v%v", "NOM", idgen.Base64URLSafeRandAlphaNumericString(n))
	return id
}
