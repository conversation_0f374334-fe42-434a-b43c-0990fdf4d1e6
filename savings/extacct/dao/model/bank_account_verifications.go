package model

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"github.com/epifi/gamma/api/savings/extacct"
)

// BankAccountVerification struct representation of bank account verification data model
type BankAccountVerification struct {
	Id      string `gorm:"type:uuid;primary_key;column:id;default:gen_random_uuid()"`
	ActorId string `gorm:"primary_key"`
	// OverallStatus describes the state of the bank account verification entry
	OverallStatus extacct.OverallStatus `gorm:"primary_key"`

	AccountNumber string
	Ifsc          string
	NameAtBank    string

	VendorStatus *commonvgpb.VendorStatus
	VendorReqId  string
	// Vendor (used to perform acc validation) enum as string
	Vendor extacct.Vendor
	// FailureReason (if any) enum as string
	FailureReason extacct.FailureReason

	NameMatchData *extacct.NameMatchData
	// Caller has data of the person (sherlock/user)
	// making the bank account verification
	Caller *extacct.Caller

	CreatedAt time.Time
	UpdatedAt time.Time
	// DeletedAtUnix signifies the unix timestamp of deletion of the bank account verification entry.
	DeletedAtUnix int64
}

func (bav *BankAccountVerification) ConvertToProto() *extacct.BankAccountVerification {
	return &extacct.BankAccountVerification{
		Id:            bav.Id,
		ActorId:       bav.ActorId,
		AccountNumber: bav.AccountNumber,
		Ifsc:          bav.Ifsc,
		NameAtBank:    bav.NameAtBank,
		OverallStatus: bav.OverallStatus,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        bav.VendorStatus.GetCode(),
			Description: bav.VendorStatus.GetDescription(),
		},
		VendorReqId:   bav.VendorReqId,
		Vendor:        bav.Vendor,
		FailureReason: bav.FailureReason,
		NameMatchData: &extacct.NameMatchData{
			KycName:                 bav.NameMatchData.KycName,
			UserGivenName:           bav.NameMatchData.GetUserGivenName(),
			UserGivenNameMatchScore: bav.NameMatchData.GetUserGivenNameMatchScore(),
			NameAtBank:              bav.NameMatchData.GetNameAtBank(),
			NameAtBankMatchScore:    bav.NameMatchData.GetNameAtBankMatchScore(),
		},
		Caller: &extacct.Caller{
			Source: bav.Caller.GetSource(),
			Email:  bav.Caller.GetEmail(),
		},
		CreatedAt:     timestampPb.New(bav.CreatedAt),
		UpdatedAt:     timestampPb.New(bav.UpdatedAt),
		DeletedAtUnix: bav.DeletedAtUnix,
	}
}
