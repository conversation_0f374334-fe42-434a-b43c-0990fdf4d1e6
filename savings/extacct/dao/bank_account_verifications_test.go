package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/savings/config"
	"github.com/epifi/gamma/savings/extacct/dao/model"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"
)

const bankAccountVerificationsTable = "bank_account_verifications"

var (
	bankAccountVerificationsTs *bankAccountVerificationsTestSuite

	bankAccountVerificationsTables = []string{bankAccountVerificationsTable}
)

// Bank Account Verifications
var (
	bankAccountVerification1 = &extacct.BankAccountVerification{
		Id:            "2b3f4d78-d970-47ee-ab3e-bbc35a633470",
		ActorId:       "actor--01",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "John Doe",
		},
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	bankAccountVerification2 = &extacct.BankAccountVerification{
		Id:            "2b3f4d78-d970-47ee-ab3e-bbc35a633470",
		Ifsc:          "ABCD0001234",
		AccountNumber: "**************",
		NameAtBank:    "John Doe",
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        "101",
			Description: "Transaction Successful",
		},
		VendorReqId: "abcd-1234",
		Vendor:      extacct.Vendor_KARZA,
		NameMatchData: &extacct.NameMatchData{
			NameAtBank: "John Doe",
		},
	}
	bankAccountVerification3 = &extacct.BankAccountVerification{
		Id:            "68f64034-9cd1-46fa-88e0-b9be729782f3",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
		ActorId:       "actor--02",
		Ifsc:          "ABCD0001234",
		AccountNumber: "**************",
		NameAtBank:    "John Doe",
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        "101",
			Description: "Transaction Successful",
		},
		VendorReqId: "abcd-1234",
		Vendor:      extacct.Vendor_KARZA,
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "John Doe",
			NameAtBank:    "John Doe",
			KycName: &commontypes.Name{
				FirstName: "John",
				LastName:  "Doe",
			},
		},
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	bankAccountVerification4 = &extacct.BankAccountVerification{
		Id:            "68f64034-9cd1-46fa-88e0-b9be729782f3",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
		NameMatchData: &extacct.NameMatchData{
			UserGivenNameMatchScore: &kyc.NameMatchScore{
				Score: 85,
			},
			NameAtBankMatchScore: &kyc.NameMatchScore{
				Score: 60,
			},
		},
		FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH,
	}
	bankAccountVerification5 = &extacct.BankAccountVerification{
		Id:            "e4f219b9-7b6a-43e6-94b9-491fbb60a6b5",
		ActorId:       "actor--05",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "John Doe",
		},
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	bankAccountVerification6 = &extacct.BankAccountVerification{
		ActorId:       "actor--06",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "John Doe",
		},
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	bankAccountVerification7 = &extacct.BankAccountVerification{
		ActorId: "actor--07",
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "John Doe",
		},
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	bankAccountVerification8 = &extacct.BankAccountVerification{
		Id:            "e4f219b9-7b6a-43e6-94b9-491fbb60a6b5",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
		ActorId:       "actor--03",
		Ifsc:          "ABCD0001234",
		AccountNumber: "**************",
		NameAtBank:    "Jane Doe",
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        "101",
			Description: "IMPS Service not available for the selected bank",
		},
		VendorReqId: "abcd-1234",
		Vendor:      extacct.Vendor_KARZA,
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "Jane Doe",
			NameAtBank:    "Jane Doe",
			KycName: &commontypes.Name{
				FirstName: "Jane",
				LastName:  "Doe",
			},
		},
		FailureReason: extacct.FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	bankAccountVerification9 = &extacct.BankAccountVerification{
		Id:            "e4f219b9-7b6a-43e6-94b9-491fbb60a6b5",
		ActorId:       "actor--01",
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
		NameMatchData: &extacct.NameMatchData{
			UserGivenName: "Johnny",
			UserGivenNameMatchScore: &kyc.NameMatchScore{
				Score: 0.5,
			},
		},
		FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
)

// Tests for storage
type bankAccountVerificationsTestSuite struct {
	db   *gorm.DB
	conf *config.Config
	dao  BankAccountVerificationsDao
}

func NewBankAccountVerificationsTestSuite(db *gorm.DB, conf *config.Config, dao BankAccountVerificationsDao) *bankAccountVerificationsTestSuite {
	return &bankAccountVerificationsTestSuite{
		db:   db,
		conf: conf,
		dao:  dao,
	}
}

func TestBankAccountVerificationsCrdb_Create(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, bankAccountVerificationsTs.conf.EpifiDb.GetName(), bankAccountVerificationsTs.db, bankAccountVerificationsTables)

	type args struct {
		bav *extacct.BankAccountVerification
	}
	tests := []struct {
		name        string
		args        args
		want        *extacct.BankAccountVerification
		expectedErr error
	}{
		{
			name:        "successful creation",
			args:        args{bav: bankAccountVerification6},
			want:        bankAccountVerification6,
			expectedErr: nil,
		},
		{
			name:        "failed creation missing primary key",
			args:        args{bav: bankAccountVerification7},
			want:        &extacct.BankAccountVerification{},
			expectedErr: fmt.Errorf("can not update without primary key"),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			resp, err := bankAccountVerificationsTs.dao.Create(context.Background(), test.args.bav)
			actualRecord := &extacct.BankAccountVerification{}
			if test.expectedErr == nil {
				actualRecord, err = fetchRecord(resp.GetId())
				if err != nil {
					t.Errorf("error in fetching bank account verification %v", err)
					return
				}
			}
			assert.Equal(t, test.expectedErr, err)
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&extacct.BankAccountVerification{}, "created_at", "updated_at", "id", "vendor_status"),
			}
			if difference := cmp.Diff(test.want, actualRecord, opts...); difference != "" {
				t.Errorf("difference: %v", difference)
			}
		})
	}
}

func TestBankAccountVerificationsCrdb_UpdateByFieldMask(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, bankAccountVerificationsTs.conf.EpifiDb.GetName(), bankAccountVerificationsTs.db, bankAccountVerificationsTables)
	_, err := addRecords([]*extacct.BankAccountVerification{bankAccountVerification1, bankAccountVerification3})
	if err != nil {
		t.Fatalf("failed to initalise db %v", err)
	}

	type args struct {
		bav   *extacct.BankAccountVerification
		masks []extacct.BankAccountVerificationFieldMask
	}
	tests := []struct {
		name        string
		args        args
		want        *extacct.BankAccountVerification
		expectedErr error
	}{
		{
			name: "successful update of vendor response",
			args: args{
				bav: bankAccountVerification2,
				masks: []extacct.BankAccountVerificationFieldMask{
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_OVERALL_STATUS,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_ACCOUNT_NUMBER,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_IFSC,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_VENDOR,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_VENDOR_STATUS,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_VENDOR_REQ_ID,
				},
			},
			want: &extacct.BankAccountVerification{
				Id:            "2b3f4d78-d970-47ee-ab3e-bbc35a633470",
				ActorId:       "actor--01",
				OverallStatus: extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
				NameMatchData: &extacct.NameMatchData{
					UserGivenName: "John Doe",
					NameAtBank:    "John Doe",
				},
				Caller: &extacct.Caller{
					Source: extacct.Source_SOURCE_USER,
				},
				Ifsc:          "ABCD0001234",
				AccountNumber: "**************",
				NameAtBank:    "John Doe",
				VendorStatus: &commonvgpb.VendorStatus{
					Code:        "101",
					Description: "Transaction Successful",
				},
				VendorReqId: "abcd-1234",
				Vendor:      extacct.Vendor_KARZA,
			},
			expectedErr: nil,
		},
		{
			name: "successful update of name match data",
			args: args{
				bav: bankAccountVerification4,
				masks: []extacct.BankAccountVerificationFieldMask{
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK_MATCH_SCORE,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_FAILURE_REASON,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_OVERALL_STATUS,
				},
			},
			want: &extacct.BankAccountVerification{
				Id:            "68f64034-9cd1-46fa-88e0-b9be729782f3",
				OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
				ActorId:       "actor--02",
				Ifsc:          "ABCD0001234",
				AccountNumber: "**************",
				NameAtBank:    "John Doe",
				VendorStatus: &commonvgpb.VendorStatus{
					Code:        "101",
					Description: "Transaction Successful",
				},
				VendorReqId: "abcd-1234",
				Vendor:      extacct.Vendor_KARZA,
				NameMatchData: &extacct.NameMatchData{
					UserGivenName: "John Doe",
					NameAtBank:    "John Doe",
					KycName: &commontypes.Name{
						FirstName: "John",
						LastName:  "Doe",
					},
					UserGivenNameMatchScore: &kyc.NameMatchScore{
						Score: 85,
					},
					NameAtBankMatchScore: &kyc.NameMatchScore{
						Score: 60,
					},
				},
				Caller: &extacct.Caller{
					Source: extacct.Source_SOURCE_USER,
				},
				FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH,
			},
			expectedErr: nil,
		},
		{
			name: "failed update record does not exist",
			args: args{
				bav: bankAccountVerification5,
				masks: []extacct.BankAccountVerificationFieldMask{
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK_MATCH_SCORE,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_FAILURE_REASON,
					extacct.BankAccountVerificationFieldMask_FIELD_MASK_OVERALL_STATUS,
				},
			},
			want:        &extacct.BankAccountVerification{},
			expectedErr: epifierrors.ErrRecordNotFound,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err2 := bankAccountVerificationsTs.dao.UpdateByFieldMask(context.Background(), test.args.bav, test.args.masks)
			actualRecord := &extacct.BankAccountVerification{}
			if test.expectedErr == nil {
				actualRecord, err = fetchRecord(test.args.bav.GetId())
				if err != nil {
					t.Errorf("error in fetching bank account verification %v", err)
					return
				}
			}
			assert.Equal(t, test.expectedErr, err2)
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&extacct.BankAccountVerification{}, "created_at", "updated_at"),
			}
			if difference := cmp.Diff(test.want, actualRecord, opts...); difference != "" {
				t.Errorf("difference: %v", difference)
			}
		})
	}
}

func TestBankAccountVerificationsCrdb_GetByActorIdAndOverallStatus(t *testing.T) {
	// Clean database, run migrations and load fixtures
	// This will ensure all DB tables are setup as desired.
	pkgTest.PrepareScopedDatabase(t, bankAccountVerificationsTs.conf.EpifiDb.GetName(), bankAccountVerificationsTs.db, bankAccountVerificationsTables)
	createdRecords, err := addRecords([]*extacct.BankAccountVerification{bankAccountVerification1, bankAccountVerification9, bankAccountVerification3})
	if err != nil {
		t.Fatalf("failed to initalise db %v", err)
	}

	type args struct {
		actorId         string
		overallStatuses []extacct.OverallStatus
	}

	tests := []struct {
		name        string
		args        args
		want        []*extacct.BankAccountVerification
		expectedErr error
	}{
		{
			name: "successful fetch with single status",
			args: args{
				actorId: "actor--02",
				overallStatuses: []extacct.OverallStatus{
					extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
				},
			},
			want:        []*extacct.BankAccountVerification{createdRecords[2]},
			expectedErr: nil,
		},
		{
			name: "successful fetch with multiple statuses",
			args: args{
				actorId: "actor--01",
				overallStatuses: []extacct.OverallStatus{
					extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
					extacct.OverallStatus_OVERALL_STATUS_FAILURE,
				},
			},
			want:        []*extacct.BankAccountVerification{createdRecords[1], createdRecords[0]},
			expectedErr: nil,
		},
		{
			name: "successful fetch with just actor id",
			args: args{
				actorId: "actor--01",
			},
			want:        []*extacct.BankAccountVerification{createdRecords[1], createdRecords[0]},
			expectedErr: nil,
		},
		{
			name: "failed fetch actor id not provided",
			args: args{
				overallStatuses: []extacct.OverallStatus{
					extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
				},
			},
			want:        nil,
			expectedErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed fetch record not found",
			args: args{
				actorId: "actor--random",
				overallStatuses: []extacct.OverallStatus{
					extacct.OverallStatus_OVERALL_STATUS_SUCCESS,
				},
			},
			want:        nil,
			expectedErr: epifierrors.ErrRecordNotFound,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			resp, err := bankAccountVerificationsTs.dao.GetByActorIdAndOverallStatus(context.Background(), test.args.actorId, test.args.overallStatuses)
			assert.Equal(t, test.expectedErr, err)
			assert.True(t, reflect.DeepEqual(test.want, resp))
		})
	}
}

func TestBankAccountVerificationsCrdb_GetByAcctNoIfscAndOverallStatus(t *testing.T) {
	// Clean database, run migrations and load fixtures
	// This will ensure all DB tables are setup as desired.
	pkgTest.PrepareScopedDatabase(t, bankAccountVerificationsTs.conf.EpifiDb.GetName(), bankAccountVerificationsTs.db, bankAccountVerificationsTables)
	createdRecords, err := addRecords([]*extacct.BankAccountVerification{bankAccountVerification3, bankAccountVerification8})
	if err != nil {
		t.Fatalf("failed to initalise db %v", err)
	}

	type args struct {
		acctNo        string
		Ifsc          string
		overallStatus []extacct.OverallStatus
	}

	tests := []struct {
		name        string
		args        args
		want        []*extacct.BankAccountVerification
		expectedErr error
	}{
		{
			name:        "successful fetch",
			args:        args{acctNo: "**************", Ifsc: "ABCD0001234", overallStatus: []extacct.OverallStatus{extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS, extacct.OverallStatus_OVERALL_STATUS_FAILURE}},
			want:        []*extacct.BankAccountVerification{createdRecords[1], createdRecords[0]},
			expectedErr: nil,
		},
		{
			name:        "failed fetch ifsc not provided",
			args:        args{acctNo: "**************", overallStatus: []extacct.OverallStatus{extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS}},
			want:        nil,
			expectedErr: epifierrors.ErrInvalidArgument,
		},
		{
			name:        "failed fetch record not found",
			args:        args{acctNo: "**************", Ifsc: "ABCD0001234", overallStatus: []extacct.OverallStatus{extacct.OverallStatus_OVERALL_STATUS_SUCCESS}},
			want:        nil,
			expectedErr: epifierrors.ErrRecordNotFound,
		},
		{
			name:        "failed fetch unspecified overall status",
			args:        args{acctNo: "**************", Ifsc: "ABCD0001234", overallStatus: []extacct.OverallStatus{extacct.OverallStatus_OVERALL_STATUS_UNSPECIFIED}},
			want:        nil,
			expectedErr: epifierrors.ErrInvalidArgument,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			resp, err := bankAccountVerificationsTs.dao.GetByAcctNoIfscAndOverallStatus(context.Background(), test.args.acctNo, test.args.Ifsc, test.args.overallStatus)
			assert.Equal(t, test.expectedErr, err)
			assert.Equal(t, len(test.want), len(resp))
			for i := range resp {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&extacct.BankAccountVerification{}, "created_at", "updated_at"),
				}
				if diff := cmp.Diff(resp[i], test.want[i], opts...); diff != "" {
					t.Errorf("extacct.BankAccountVerification value is mismatch (-got +want):%s\n", diff)
				}
			}
		})
	}
}

func addRecords(bavs []*extacct.BankAccountVerification) ([]*extacct.BankAccountVerification, error) {
	var createdRecords []*extacct.BankAccountVerification
	for _, bav := range bavs {
		res := bankAccountVerificationsTs.db.Create(convertToModel(bav))
		if res.Error != nil {
			return nil, res.Error
		}
		createdRecord, err := fetchRecord(bav.GetId())
		if err != nil {
			return nil, err
		}
		createdRecords = append(createdRecords, createdRecord)
	}
	return createdRecords, nil
}

func fetchRecord(id string) (*extacct.BankAccountVerification, error) {
	bav := &model.BankAccountVerification{}
	if err := bankAccountVerificationsTs.db.Model(&model.BankAccountVerification{}).Where(&model.BankAccountVerification{Id: id}).
		Where("deleted_at_unix = ?", 0).
		Take(bav).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			return nil, err
		}
		return nil, err
	}
	return bav.ConvertToProto(), nil
}
