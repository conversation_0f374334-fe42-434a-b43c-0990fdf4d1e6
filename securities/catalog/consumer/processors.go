package consumer

import (
	"fmt"
	"strings"

	"google.golang.org/genproto/googleapis/type/money"

	moneyPb "github.com/epifi/be-common/pkg/money"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/ingester"
)

func (s *Service) processCompanyDescriptionFromCompanyDetails(companyDetails *vendorPb.CompanyDetails, companyFundamentalParagraphs []*vendorPb.CompanyFundamentalParagraph) (*catalogPb.StockDetails, error) {
	var stockDescriptionStr string
	gicsSectorType, ok := ingester.MapGICSSectorTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsSectorName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS sector type to enum: %s", companyDetails.GetGicsSectorName())
	}
	gicsIndustryGroupType, ok := ingester.MapGICSIndustryGroupTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsIndustryGroupName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS industry group type to enum: %s", companyDetails.GetGicsIndustryGroupName())
	}
	gicsIndustryType, ok := ingester.MapGICSIndustryTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsIndustryName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS industry type to enum: %s", companyDetails.GetGicsIndustryName())
	}
	for _, paragraph := range companyFundamentalParagraphs {
		if paragraph.GetParagraphType() == "company_description" {
			if paragraph.GetParagraph() == "" {
				return nil, fmt.Errorf("company description is empty")
			}
			stockDescriptionStr = paragraph.GetParagraph()
		}
	}
	return &catalogPb.StockDetails{
		StockName:                companyDetails.GetCompanyName(),
		StockShortName:           companyDetails.GetCompanyNameShort(),
		WebsiteUrl:               companyDetails.GetWebsite(),
		RegionName:               companyDetails.GetRegionName(),
		IncorporationCountryName: companyDetails.GetIncorporationCountryName(),
		GicsSectorType:           gicsSectorType,
		GicsIndustryGroupType:    gicsIndustryGroupType,
		GicsIndustryType:         gicsIndustryType,
		StockDescription:         stockDescriptionStr,
	}, nil
}

func (s *Service) processFinancialInfoFromFundamentalParameters(companyFundamentalParameters []*vendorPb.CompanyFundamentalParameters) *catalogPb.FinancialInfo {
	var bookValuePerShare *money.Money
	var dividendYield, peRatio, pbRatio, returnOnEquity, sharesOutstanding float64
	for _, parameter := range companyFundamentalParameters {
		// Bridgewise Ref: https://docs.google.com/document/d/1awDL9m1xfR0azidSC1kvJBOydwEFQYgOc7rVIHeGMAA/edit?usp=sharing
		switch parameter.GetParameterId() {
		// Book Value Per Share
		case 4020:
			bookValuePerShare = moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3())
		// Dividend Yield
		case 4038:
			dividendYield = parameter.GetParameterValue()
		// Price to Earnings Ratio
		case 100003:
			peRatio = parameter.GetParameterValue()
		// Price to Book Ratio
		case 100067:
			pbRatio = parameter.GetParameterValue()
		// Return on Equity
		case 4128:
			returnOnEquity = parameter.GetParameterValue()
		// Shares Outstanding
		case 1072:
			sharesOutstanding = parameter.GetParameterValue()
		}
	}
	return &catalogPb.FinancialInfo{
		TtmFundamentalParameters: &catalogPb.FundamentalParameters{
			BookValuePerShare: bookValuePerShare,
			DividendYield:     dividendYield,
			PeRatio:           peRatio,
			PbRatio:           pbRatio,
			ReturnOnEquity:    returnOnEquity,
			SharesOutstanding: sharesOutstanding,
		},
	}
}

func (s *Service) processMarketCapFromMarketData(companyMarketData []*vendorPb.CompanyMarketData) *money.Money {
	lastReportedMarketData := companyMarketData[len(companyMarketData)-1]
	return moneyPb.ParseFloat(lastReportedMarketData.GetMarketCap(), lastReportedMarketData.GetMarketCapCurrencyIso3())
}
