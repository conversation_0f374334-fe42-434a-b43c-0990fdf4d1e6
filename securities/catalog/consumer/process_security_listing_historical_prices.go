package consumer

import (
	"context"
	"fmt"
	"sort"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/securities/catalog"
	catalogConsumerPb "github.com/epifi/gamma/api/securities/catalog/consumer"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
)

func (s *Service) ProcessSecurityListingHistoricalPrices(ctx context.Context, req *catalogConsumerPb.ProcessSecurityListingHistoricalPricesRequest) (*catalogConsumerPb.ProcessSecurityListingHistoricalPricesResponse, error) {
	logger.Info(ctx, "starting process security historical prices", zap.String("securityListingId", req.GetSecurityListingId()))

	historicalPrices, err := s.getHistoricalPrices(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to fetch historical prices from vendor", zap.Error(err), zap.String("securityListingId", req.GetSecurityListingId()))
		return &catalogConsumerPb.ProcessSecurityListingHistoricalPricesResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}

	if len(historicalPrices.GetMarketData()) == 0 {
		logger.Info(ctx, "no historical prices found for security listing",
			zap.String("securityListingId", req.GetSecurityListingId()),
			zap.String("startDate", req.GetStartDate().AsTime().String()),
			zap.String("endDate", req.GetEndDate().AsTime().String()))
		return &catalogConsumerPb.ProcessSecurityListingHistoricalPricesResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}, nil
	}

	historicalPriceData := getHistoricalPricesForDateRange(req.GetSecurityListingId(), req.GetEndDate(), historicalPrices.GetMarketData())

	err = s.historicalPricesDao.BatchUpsert(ctx, historicalPriceData)
	if err != nil {
		logger.Error(ctx, "failed to upsert historical prices", zap.Error(err), zap.String("securityListingExternalId", req.GetSecurityListingId()))
		return &catalogConsumerPb.ProcessSecurityListingHistoricalPricesResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}

	logger.Info(ctx, "successfully inserted historical prices",
		zap.String("securityListingExternalId", req.GetSecurityListingId()), zap.Int("count", len(historicalPriceData)))

	return &catalogConsumerPb.ProcessSecurityListingHistoricalPricesResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

func (s *Service) getHistoricalPrices(ctx context.Context, req *catalogConsumerPb.ProcessSecurityListingHistoricalPricesRequest) (*vgCatalogPb.GetCompanyMarketDataResponse, error) {
	startTime := req.GetStartDate().AsTime()
	endTime := req.GetEndDate().AsTime()

	historicalPricesResp, err := s.vgCatalogClient.GetCompanyMarketData(ctx, &vgCatalogPb.GetCompanyMarketDataRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_BRIDGEWISE,
		},
		CompanyId:     req.GetCompanyId(),
		TradingItemId: req.GetTradingItemId(),
		FromDate:      datetime.TimeToDateInLoc(startTime, datetime.IST),
		ToDate:        datetime.TimeToDateInLoc(endTime, datetime.IST),
	})
	if rpcErr := epifigrpc.RPCError(historicalPricesResp, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get historical prices from vendor: %w", rpcErr)
	}

	return historicalPricesResp, nil
}

func getHistoricalPricesForDateRange(securityListingId string, endDate *timestampPb.Timestamp, historicalPrices []*vendorPb.CompanyMarketData) []*catalog.HistoricalPrice {
	var res []*catalog.HistoricalPrice

	// Sort prices by date
	sort.Slice(historicalPrices, func(i, j int) bool {
		dateI, _ := time.Parse("2006-01-02", historicalPrices[i].GetDate())
		dateJ, _ := time.Parse("2006-01-02", historicalPrices[j].GetDate())
		return dateI.Before(dateJ)
	})

	if len(historicalPrices) == 0 {
		return res
	}

	idx := 0
	curDate, _ := time.Parse("2006-01-02", historicalPrices[0].GetDate())
	endTime := endDate.AsTime()

	// Process each date in the range
	for !curDate.After(endTime) {
		matched := false
		if idx < len(historicalPrices) {
			if priceDate, err := time.Parse("2006-01-02", historicalPrices[idx].GetDate()); err == nil && curDate.Equal(priceDate) {
				// Use actual price data
				priceDate := datetime.TimeToDateInLoc(curDate, datetime.IST)
				closePrice := historicalPrices[idx].GetClosePrice()
				currencyCode := historicalPrices[idx].GetPriceCurrencyIso3()
				moneyValue := money.ParseFloat(closePrice, currencyCode)
				res = append(res, &catalog.HistoricalPrice{
					SecurityListingId: securityListingId,
					PriceDate:         priceDate,
					ClosePrice:        moneyValue,
					Vendor:            commonvgpb.Vendor_BRIDGEWISE,
					PriceDerivedDate:  priceDate,
				})
				idx++
				matched = true
			}
		}

		// Create derived price entry for non-matching days (e.g., holidays)
		if !matched && len(res) > 0 {
			lastPriceEntry := res[len(res)-1]
			priceDate := datetime.TimeToDateInLoc(curDate, datetime.IST)
			res = append(res, &catalog.HistoricalPrice{
				SecurityListingId: securityListingId,
				PriceDate:         priceDate,
				ClosePrice:        lastPriceEntry.GetClosePrice(),
				Vendor:            lastPriceEntry.GetVendor(),
				PriceDerivedDate:  lastPriceEntry.GetPriceDerivedDate(),
			})
		}

		curDate = curDate.AddDate(0, 0, 1)
	}

	return res
}
