package historicalpricesfetcher

import (
	"context"
	"fmt"

	"github.com/google/wire"

	"google.golang.org/genproto/googleapis/type/date"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/securities/catalog/dao"
)

var PGDBHistoricalPricesDAOWireSet = wire.NewSet(
	NewPGDBHistoricalPricesDAO,
	wire.Bind(new(HistoricalPricesFetcher), new(*PGDBHistoricalPricesDAO)))

// PGDBHistoricalPricesDAO implements the HistoricalPricesFetcher interface using the DAO
// It uses security listing external ID (secListExtId)
type PGDBHistoricalPricesDAO struct {
	historicalPricesDao dao.HistoricalPricesDao
}

// NewPGDBHistoricalPricesDAO creates a new instance of PGDBHistoricalPricesDAO
func NewPGDBHistoricalPricesDAO(historicalPricesDao dao.HistoricalPricesDao) *PGDBHistoricalPricesDAO {
	return &PGDBHistoricalPricesDAO{
		historicalPricesDao: historicalPricesDao,
	}
}

// GetPrices retrieves prices directly from the DAO
func (f *PGDBHistoricalPricesDAO) GetPrices(ctx context.Context, securityListingExtIDs []string, priceDate *date.Date) ([]*catalogPb.HistoricalPrice, error) {
	fieldMask := []catalogPb.HistoricalPriceFieldMask{
		catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_SECURITY_LISTING_ID,
		catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_CLOSE_PRICE,
		catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_PRICE_DATE,
	}

	prices, err := f.historicalPricesDao.GetBySecuritiesAndDates(ctx, securityListingExtIDs, []*date.Date{priceDate}, fieldMask)
	if err != nil {
		return nil, fmt.Errorf("dao error in GetBySecuritiesAndDates: %w", err)
	}

	return prices, nil
}
