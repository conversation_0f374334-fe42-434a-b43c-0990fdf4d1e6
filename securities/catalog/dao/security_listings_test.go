package dao

import (
	"context"
	"testing"

	vendorgatewayPb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
)

var (
	listingAAPL = &catalogPb.SecurityListing{
		InternalId:       "SLX8K2",
		ExternalId:       "USSX1A2B3",
		SecurityId:       "SECA1B2",
		Exchange:         catalogPb.Exchange_EXCHANGE_USA_NYSE,
		Symbol:           "AAPL",
		IsPrimaryListing: true,
		Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
		FinancialInfo: &catalogPb.FinancialInfo{
			MarketCap:                &money.Money{CurrencyCode: "USD", Units: 3000000000, Nanos: 0},
			TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 30.5, PbRatio: 10.2},
		},
		Isin:            "US0378331005",
		Vendor:          vendorgatewayPb.Vendor_ALPACA,
		VendorListingId: "VENDL001",
	}
	listingAAPLWithFieldMask = &catalogPb.SecurityListing{
		InternalId: "SLX8K2",
		ExternalId: "USSX1A2B3",
		Isin:       "US0378331005",
	}
)

func TestSecurityListingDaoPGDB_BatchUpsert(t *testing.T) {
	type args struct {
		ctx        context.Context
		listings   []*catalogPb.SecurityListing
		fieldMasks []catalogPb.SecurityListingFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    []*catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Insert a valid security listing should succeed",
			args: args{
				ctx: context.Background(),
				listings: []*catalogPb.SecurityListing{{
					InternalId:       "batch-id",
					ExternalId:       "batch-ext-id",
					SecurityId:       "SECA1C3",
					Exchange:         catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:           "BATCH",
					IsPrimaryListing: true,
					Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
					},
					Isin:            "US9876543210",
					Vendor:          vendorgatewayPb.Vendor_ALPACA,
					VendorListingId: "batch-vendor-listing-id",
				}},
			},
			want: []*catalogPb.SecurityListing{{
				InternalId:       "batch-id",
				ExternalId:       "batch-ext-id",
				SecurityId:       "SECA1C3",
				Exchange:         catalogPb.Exchange_EXCHANGE_USA_NYSE,
				Symbol:           "BATCH",
				IsPrimaryListing: true,
				Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
				FinancialInfo: &catalogPb.FinancialInfo{
					MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
					TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
				},
				Isin:            "US9876543210",
				Vendor:          vendorgatewayPb.Vendor_ALPACA,
				VendorListingId: "batch-vendor-listing-id",
			}},
			wantErr: false,
		},
		{
			name: "Update existing security listing SLX8K2 should succeed",
			args: args{
				ctx: context.Background(),
				listings: []*catalogPb.SecurityListing{{
					ExternalId:       "USSX1A2B3",
					SecurityId:       "SECA1B2",
					Exchange:         catalogPb.Exchange_EXCHANGE_USA_NASDAQ, // changed exchange
					Symbol:           "AAPL",
					IsPrimaryListing: true,
					Status:           catalogPb.ListingStatus_LISTING_STATUS_INACTIVE, // changed status
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 5000000000, Nanos: 0}, // changed market cap
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 25.0, PbRatio: 8.0},  // changed params
					},
					Isin:            "US0378331005",
					Vendor:          vendorgatewayPb.Vendor_ALPACA,
					VendorListingId: "VENDL001",
				}},
				fieldMasks: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_STATUS,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_FINANCIAL_INFO,
				},
			},
			want: []*catalogPb.SecurityListing{{
				InternalId:       "SLX8K2",
				ExternalId:       "USSX1A2B3",
				SecurityId:       "SECA1B2",
				Exchange:         catalogPb.Exchange_EXCHANGE_USA_NASDAQ, // should reflect update
				Symbol:           "AAPL",
				IsPrimaryListing: true,
				Status:           catalogPb.ListingStatus_LISTING_STATUS_INACTIVE, // should reflect update
				FinancialInfo: &catalogPb.FinancialInfo{
					MarketCap:                &money.Money{CurrencyCode: "USD", Units: 5000000000, Nanos: 0},
					TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 25.0, PbRatio: 8.0},
				},
				Isin:            "US0378331005",
				Vendor:          vendorgatewayPb.Vendor_ALPACA,
				VendorListingId: "VENDL001",
			}},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()
			err := dao.BatchUpsert(tt.args.ctx, tt.args.listings, tt.args.fieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				cmpOpts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
				}
				for i := range len(tt.want) {
					got, err := dao.GetByExternalId(tt.args.ctx, tt.args.listings[i].ExternalId, nil)
					if err != nil {
						t.Errorf("BatchUpsert() error = %v", err)
						return
					}
					if diff := cmp.Diff(tt.want[i], got, cmpOpts...); diff != "" {
						t.Errorf("BatchUpsert() got[%d] = %v,\n want[%d] %v\n diff %s", i, got, i, tt.want[i], diff)
					}
				}
			}
		})
	}
}

func TestSecurityListingDaoPGDB_GetBySymbolExchange(t *testing.T) {
	type args struct {
		ctx       context.Context
		symbol    string
		exchange  catalogPb.Exchange
		fieldMask []catalogPb.SecurityListingFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Valid symbol and exchange should return matching security listing",
			args: args{
				ctx:       context.Background(),
				symbol:    "AAPL",
				exchange:  catalogPb.Exchange_EXCHANGE_USA_NYSE,
				fieldMask: nil,
			},
			want:    listingAAPL,
			wantErr: false,
		},
		{
			name: "Valid symbol and exchange with fieldMask InternalId should return only internalId field",
			args: args{
				ctx:       context.Background(),
				symbol:    "AAPL",
				exchange:  catalogPb.Exchange_EXCHANGE_USA_NYSE,
				fieldMask: []catalogPb.SecurityListingFieldMask{catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID},
			},
			want: &catalogPb.SecurityListing{
				InternalId: "SLX8K2",
			},
			wantErr: false,
		},
		{
			name: "Non-existent symbol and exchange should return error",
			args: args{
				ctx:       context.Background(),
				symbol:    "FAKE",
				exchange:  catalogPb.Exchange_EXCHANGE_USA_NYSE,
				fieldMask: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty symbol or exchange should return error",
			args: args{
				ctx:       context.Background(),
				symbol:    "",
				exchange:  catalogPb.Exchange_EXCHANGE_UNSPECIFIED,
				fieldMask: nil,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()
			got, err := dao.GetBySymbolExchange(tt.args.ctx, tt.args.symbol, tt.args.exchange, tt.args.fieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBySymbolExchange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetBySymbolExchange() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecurityListingDaoPGDB_GetByExternalId(t *testing.T) {
	type args struct {
		ctx        context.Context
		externalId string
		fieldMask  []catalogPb.SecurityListingFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Valid externalId should return matching security listing",
			args: args{
				ctx:        context.Background(),
				externalId: "USSX1A2B3",
				fieldMask:  nil,
			},
			want:    listingAAPL,
			wantErr: false,
		},
		{
			name: "Valid externalId with fieldMask InternalId should return only internalId field",
			args: args{
				ctx:        context.Background(),
				externalId: "USSX1A2B3",
				fieldMask:  []catalogPb.SecurityListingFieldMask{catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID},
			},
			want: &catalogPb.SecurityListing{
				InternalId: "SLX8K2",
			},
			wantErr: false,
		},
		{
			name: "Non-existent externalId should return error",
			args: args{
				ctx:        context.Background(),
				externalId: "NON_EXISTENT",
				fieldMask:  nil,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()
			got, err := dao.GetByExternalId(tt.args.ctx, tt.args.externalId, tt.args.fieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByExternalId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByExternalId() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecurityListingDaoPGDB_GetByISINAndExchange(t *testing.T) {
	type args struct {
		ctx       context.Context
		pairs     []*catalogPb.ISINExchangePair
		fieldMask []catalogPb.SecurityListingFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    []*catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Valid ISIN and exchange pair should return matching security listing",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want: []*catalogPb.SecurityListing{
				{
					InternalId: "SLX8K2",
					Isin:       "US0378331005",
					Exchange:   catalogPb.Exchange_EXCHANGE_USA_NYSE,
				},
			},
			wantErr: false,
		},
		{
			name: "Multiple valid ISIN and exchange pairs should return matching security listings",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NASDAQ,
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want: []*catalogPb.SecurityListing{
				{
					InternalId: "SLX8K2",
					Isin:       "US0378331005",
					Exchange:   catalogPb.Exchange_EXCHANGE_USA_NYSE,
				},
			},
			wantErr: false,
		},
		{
			name: "Mixed valid and invalid pairs should return results for valid pairs",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
					{
						Isin:     "", // invalid
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED, // invalid
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want: []*catalogPb.SecurityListing{
				{
					InternalId: "SLX8K2",
					Isin:       "US0378331005",
					Exchange:   catalogPb.Exchange_EXCHANGE_USA_NYSE,
				},
			},
			wantErr: false,
		},
		{
			name: "Mixed valid and invalid pairs with non-existent valid pairs should return empty result",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "NON_EXISTENT_ISIN",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
					{
						Isin:     "", // invalid
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED, // invalid
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want:    []*catalogPb.SecurityListing{},
			wantErr: false,
		},
		{
			name: "Non-existent ISIN should return empty result",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "NON_EXISTENT_ISIN",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want:    []*catalogPb.SecurityListing{},
			wantErr: false,
		},
		{
			name: "Empty ISIN should be filtered out",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "",
						Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Unspecified exchange should be filtered out",
			args: args{
				ctx: context.Background(),
				pairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "US0378331005",
						Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED,
					},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty pairs should return error",
			args: args{
				ctx:   context.Background(),
				pairs: []*catalogPb.ISINExchangePair{},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()
			got, err := dao.GetByISINAndExchange(tt.args.ctx, tt.args.pairs, tt.args.fieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByISINAndExchange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("GetByISINAndExchange() got = %v,\n want %v\n diff %s", got, tt.want, diff)
				}
			}
		})
	}
}

func TestSecurityListingDaoPGDB_GetByExternalIds(t *testing.T) {
	type args struct {
		ctx         context.Context
		externalIds []string
		fieldMask   []catalogPb.SecurityListingFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    []*catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Valid external IDs should return matching security listings",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"USSX1A2B3"},
				fieldMask:   nil,
			},
			want:    []*catalogPb.SecurityListing{{ExternalId: "USSX1A2B3"}},
			wantErr: false,
		},
		{
			name: "Multiple valid external IDs should return matching security listings",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"USSX1A2B3", "batch-ext-id"},
				fieldMask:   nil,
			},
			want: []*catalogPb.SecurityListing{
				{ExternalId: "USSX1A2B3"},
				{ExternalId: "batch-ext-id"},
			},
			wantErr: false,
		},
		{
			name: "Valid external IDs with fieldMask should return only requested fields",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"USSX1A2B3"},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
				},
			},
			want: []*catalogPb.SecurityListing{
				{
					ExternalId: "USSX1A2B3",
					Symbol:     "AAPL",
				},
			},
			wantErr: false,
		},
		{
			name: "Non-existent external IDs should return empty slice",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"NON_EXISTENT"},
				fieldMask:   nil,
			},
			want:    []*catalogPb.SecurityListing{},
			wantErr: false,
		},
		{
			name: "Mixed valid and invalid external IDs should return only valid ones",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"USSX1A2B3", "NON_EXISTENT"},
				fieldMask:   nil,
			},
			want:    []*catalogPb.SecurityListing{{ExternalId: "USSX1A2B3"}},
			wantErr: false,
		},
		{
			name: "Empty external IDs should return error",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{},
				fieldMask:   nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "External IDs with empty strings should filter them out",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"USSX1A2B3", "", "NON_EXISTENT"},
				fieldMask:   nil,
			},
			want:    []*catalogPb.SecurityListing{{ExternalId: "USSX1A2B3"}},
			wantErr: false,
		},
		{
			name: "All empty external IDs should return error",
			args: args{
				ctx:         context.Background(),
				externalIds: []string{"", "", ""},
				fieldMask:   nil,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()

			// Setup test data if needed
			if len(tt.want) > 1 {
				// Insert the batch test data
				batchListing := &catalogPb.SecurityListing{
					InternalId:       "batch-id",
					ExternalId:       "batch-ext-id",
					SecurityId:       "SECA1C3",
					Exchange:         catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:           "BATCH",
					IsPrimaryListing: true,
					Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
					},
					Isin:            "US9876543210",
					Vendor:          vendorgatewayPb.Vendor_ALPACA,
					VendorListingId: "batch-vendor-listing-id",
				}
				err := dao.BatchUpsert(context.Background(), []*catalogPb.SecurityListing{batchListing}, nil)
				if err != nil {
					t.Fatalf("Failed to setup test data: %v", err)
				}
			}

			got, err := dao.GetByExternalIds(tt.args.ctx, tt.args.externalIds, tt.args.fieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByExternalIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByExternalIds() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecurityListingDaoPGDB_GetByExchangeSymbols(t *testing.T) {
	type args struct {
		ctx             context.Context
		exchangeSymbols []*catalogPb.ExchangeSymbol
		fieldMask       []catalogPb.SecurityListingFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    []*catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Valid exchange symbols should return matching security listings",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "AAPL", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
				},
				fieldMask: nil,
			},
			want: []*catalogPb.SecurityListing{
				{
					Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:   "AAPL",
				},
			},
			wantErr: false,
		},
		{
			name: "Multiple valid exchange symbols should return matching security listings",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "AAPL", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
					{Symbol: "BATCH", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
				},
				fieldMask: nil,
			},
			want: []*catalogPb.SecurityListing{
				{
					Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:   "AAPL",
				},
				{
					Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:   "BATCH",
				},
			},
			wantErr: false,
		},
		{
			name: "Valid exchange symbols with fieldMask should return only requested fields",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "AAPL", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
				},
				fieldMask: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
				},
			},
			want: []*catalogPb.SecurityListing{
				{
					ExternalId: "USSX1A2B3",
					Exchange:   catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:     "AAPL",
				},
			},
			wantErr: false,
		},
		{
			name: "Non-existent exchange symbols should return empty slice",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "NON_EXISTENT", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
				},
				fieldMask: nil,
			},
			want:    []*catalogPb.SecurityListing{},
			wantErr: false,
		},
		{
			name: "Mixed valid and invalid exchange symbols should return only valid ones",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "AAPL", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
					{Symbol: "NON_EXISTENT", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
				},
				fieldMask: nil,
			},
			want: []*catalogPb.SecurityListing{
				{
					Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:   "AAPL",
				},
			},
			wantErr: false,
		},
		{
			name: "Empty exchange symbols should return error",
			args: args{
				ctx:             context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{},
				fieldMask:       nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Exchange symbols with invalid data should filter them out",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "AAPL", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
					{Symbol: "", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
					{Symbol: "VALID", Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED},
				},
				fieldMask: nil,
			},
			want: []*catalogPb.SecurityListing{
				{
					Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:   "AAPL",
				},
			},
			wantErr: false,
		},
		{
			name: "All invalid exchange symbols should return error",
			args: args{
				ctx: context.Background(),
				exchangeSymbols: []*catalogPb.ExchangeSymbol{
					{Symbol: "", Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE},
					{Symbol: "VALID", Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED},
				},
				fieldMask: nil,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()

			// Setup test data if needed
			if len(tt.want) > 1 {
				// Insert the batch test data
				batchListing := &catalogPb.SecurityListing{
					InternalId:       "batch-id",
					ExternalId:       "batch-ext-id",
					SecurityId:       "SECA1C3",
					Exchange:         catalogPb.Exchange_EXCHANGE_USA_NYSE,
					Symbol:           "BATCH",
					IsPrimaryListing: true,
					Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
					},
					Isin:            "US9876543210",
					Vendor:          vendorgatewayPb.Vendor_ALPACA,
					VendorListingId: "batch-vendor-listing-id",
				}
				err := dao.BatchUpsert(context.Background(), []*catalogPb.SecurityListing{batchListing}, nil)
				if err != nil {
					t.Fatalf("Failed to setup test data: %v", err)
				}
			}

			got, err := dao.GetByExchangeSymbols(tt.args.ctx, tt.args.exchangeSymbols, tt.args.fieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByExchangeSymbols() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByExchangeSymbols() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecurityListingDaoPGDB_UpdateByVendorListingId(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		listing     *catalogPb.SecurityListing
		updateMasks []catalogPb.SecurityListingFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr string
	}{
		{
			name: "Updating a valid security listing should succeed",
			args: args{
				ctx: context.Background(),
				listing: &catalogPb.SecurityListing{
					ExternalId: "USSX1A2B3",
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
					},
					Vendor:          vendorgatewayPb.Vendor_ALPACA,
					VendorListingId: "VENDL001",
				},
				updateMasks: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_FINANCIAL_INFO,
				},
			},
			wantErr: "",
		},
		{
			name: "vendor is unspecified",
			args: args{
				ctx: context.Background(),
				listing: &catalogPb.SecurityListing{
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
					},
					Vendor:          vendorgatewayPb.Vendor_VENDOR_UNSPECIFIED,
					VendorListingId: "VENDL001",
				},
			},
			wantErr: "vendor cannot be unspecified",
		},
		{
			name: "vendor listing id is empty",
			args: args{
				ctx: context.Background(),
				listing: &catalogPb.SecurityListing{
					FinancialInfo: &catalogPb.FinancialInfo{
						MarketCap:                &money.Money{CurrencyCode: "USD", Units: 1234567890, Nanos: 0},
						TtmFundamentalParameters: &catalogPb.FundamentalParameters{PeRatio: 15.2, PbRatio: 3.4},
					},
					Vendor:          vendorgatewayPb.Vendor_ALPACA,
					VendorListingId: "",
				},
			},
			wantErr: "vendor listing id cannot be empty for security listing update operation",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			dao, cleanup := getSecurityListingsDao(t)
			defer cleanup()
			err := dao.UpdateByVendorListingId(tt.args.ctx, tt.args.listing, tt.args.updateMasks)
			if tt.wantErr != "" {
				if err.Error() != tt.wantErr {
					t.Errorf("UpdateByVendorListingId() error = %v, wantErr %v", err, tt.wantErr)
				}
				return
			}
			cmpOpts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at"),
			}
			got, err := dao.GetByExternalId(tt.args.ctx, tt.args.listing.ExternalId, []catalogPb.SecurityListingFieldMask{
				catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
				catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_FINANCIAL_INFO,
				catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR,
				catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID,
			})
			if err != nil {
				t.Errorf("UpdateByVendorListingId() error = %v", err)
				return
			}
			if diff := cmp.Diff(tt.args.listing, got, cmpOpts...); diff != "" {
				t.Errorf("UpdateByVendorListingId() got = %v,\n want %v\n diff %s", got, tt.args.listing, diff)
			}
		})
	}
}

func TestSecuritiesDaoPGDB_GetByVendorListingId(t *testing.T) {
	type args struct {
		ctx             context.Context
		vendor          vendorgatewayPb.Vendor
		vendorListingId string
		fieldMasks      []catalogPb.SecurityListingFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.SecurityListing
		wantErr bool
	}{
		{
			name: "Valid ID",
			args: args{
				ctx:             context.Background(),
				vendor:          vendorgatewayPb.Vendor_ALPACA,
				vendorListingId: "VENDL001",
			},
			want:    listingAAPL,
			wantErr: false,
		},
		{
			name: "Valid ID with field masks",
			args: args{
				ctx:             context.Background(),
				vendor:          vendorgatewayPb.Vendor_ALPACA,
				vendorListingId: "VENDL001",
				fieldMasks: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
				},
			},
			want:    listingAAPLWithFieldMask,
			wantErr: false,
		},
		{
			name: "Invalid ID",
			args: args{
				ctx:             context.Background(),
				vendor:          vendorgatewayPb.Vendor_BRIDGEWISE,
				vendorListingId: "InvalidId",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty ID",
			args: args{
				ctx:             context.Background(),
				vendor:          vendorgatewayPb.Vendor_MORNINGSTAR,
				vendorListingId: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Invalid Vendor",
			args: args{
				ctx:             context.Background(),
				vendor:          vendorgatewayPb.Vendor_VENDOR_UNSPECIFIED,
				vendorListingId: "validId",
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			securityListingsDao, cleanup := getSecurityListingsDao(t)
			defer cleanup()

			got, err := securityListingsDao.GetByVendorListingId(tt.args.ctx, tt.args.vendor, tt.args.vendorListingId, tt.args.fieldMasks)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetByVendorListingId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.SecurityListing{}, "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByVendorListingId() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}
