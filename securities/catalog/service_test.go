package catalog

import (
	"context"
	"errors"
	"testing"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vgCatalogMocks "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise/mocks"
	"github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	daoMocks "github.com/epifi/gamma/securities/catalog/dao/mocks"
	"github.com/epifi/gamma/securities/catalog/historicalpricesfetcher"
	ingesterMocks "github.com/epifi/gamma/securities/catalog/ingester/mocks"
	genConf "github.com/epifi/gamma/securities/config/genconf"
	"github.com/epifi/gamma/securities/test"
)

func TestGetSecListingIdByISIN(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	test.InitTestServerWithoutDBConn()
	defer ctrl.Finish()

	type testMocks struct {
		dao                 *daoMocks.MockSecurityListingsDao
		vgCatalogClient     *vgCatalogMocks.MockCatalogClient
		catalogDataIngester *ingesterMocks.MockCatalogDataIngester
	}

	tests := []struct {
		name      string
		request   *catalogPb.GetSecListingIdsByISINsRequest
		mockSetup func(*testMocks)
		want      *catalogPb.GetSecListingIdsByISINsResponse
	}{
		{
			name: "Empty request should return invalid argument",
			request: &catalogPb.GetSecListingIdsByISINsRequest{
				IsinExchangePairs: []*catalogPb.ISINExchangePair{},
			},
			mockSetup: func(mocks *testMocks) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("ISIN exchange pairs are empty"),
			},
		},
		{
			name: "Successful retrieval for BSE listing",
			request: &catalogPb.GetSecListingIdsByISINsRequest{
				IsinExchangePairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "INE848E01016",
						Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
					},
				},
			},
			mockSetup: func(mocks *testMocks) {
				mocks.dao.EXPECT().
					GetByISINAndExchange(
						gomock.Any(),
						[]*catalogPb.ISINExchangePair{
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "BSE123",
							Isin:       "INE848E01016",
							Exchange:   catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
					}, nil)
			},
			want: &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpc.StatusOk(),
				SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
					{
						IsinExchangePair: &catalogPb.ISINExchangePair{
							Isin:     "INE848E01016",
							Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
						SecurityListingExternalId: "BSE123",
					},
				},
			},
		},
		{
			name: "Record not found should return not found status",
			request: &catalogPb.GetSecListingIdsByISINsRequest{
				IsinExchangePairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "INE848E01016",
						Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
					},
				},
			},
			mockSetup: func(mocks *testMocks) {
				mocks.dao.EXPECT().
					GetByISINAndExchange(
						gomock.Any(),
						[]*catalogPb.ISINExchangePair{
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
						},
					).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "Unspecified exchange should try BSE then NSE",
			request: &catalogPb.GetSecListingIdsByISINsRequest{
				IsinExchangePairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "INE848E01016",
						Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED,
					},
				},
			},
			mockSetup: func(mocks *testMocks) {
				mocks.dao.EXPECT().
					GetByISINAndExchange(
						gomock.Any(),
						[]*catalogPb.ISINExchangePair{
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "NSE123",
							Isin:       "INE848E01016",
							Exchange:   catalogPb.Exchange_EXCHANGE_INDIA_NSE,
						},
					}, nil)
			},
			want: &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpc.StatusOk(),
				SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
					{
						IsinExchangePair: &catalogPb.ISINExchangePair{
							Isin:     "INE848E01016",
							Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
						},
						SecurityListingExternalId: "NSE123",
					},
				},
			},
		},
		{
			name: "Unspecified exchange should not try NSE when BSE is found",
			request: &catalogPb.GetSecListingIdsByISINsRequest{
				IsinExchangePairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "INE848E01016",
						Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED,
					},
				},
			},
			mockSetup: func(mocks *testMocks) {
				mocks.dao.EXPECT().
					GetByISINAndExchange(
						gomock.Any(),
						[]*catalogPb.ISINExchangePair{
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "BSE123",
							Isin:       "INE848E01016",
							Exchange:   catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
					}, nil)
			},
			want: &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpc.StatusOk(),
				SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
					{
						IsinExchangePair: &catalogPb.ISINExchangePair{
							Isin:     "INE848E01016",
							Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
						SecurityListingExternalId: "BSE123",
					},
				},
			},
		},
		{
			name: "Partial success - some ISINs found, others not found",
			request: &catalogPb.GetSecListingIdsByISINsRequest{
				IsinExchangePairs: []*catalogPb.ISINExchangePair{
					{
						Isin:     "INE848E01016",
						Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
					},
					{
						Isin:     "INE848E01017",
						Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
					},
					{
						Isin:     "INE848E01018",
						Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
					},
				},
			},
			mockSetup: func(mocks *testMocks) {
				// First call to GetByISINAndExchange - returns partial results
				mocks.dao.EXPECT().
					GetByISINAndExchange(
						gomock.Any(),
						[]*catalogPb.ISINExchangePair{
							{
								Isin:     "INE848E01016",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							{
								Isin:     "INE848E01017",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							{
								Isin:     "INE848E01018",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "BSE123",
							Isin:       "INE848E01016",
							Exchange:   catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
						{
							ExternalId: "NSE456",
							Isin:       "INE848E01018",
							Exchange:   catalogPb.Exchange_EXCHANGE_INDIA_NSE,
						},
					}, nil)

				// Mock getAssetDetails call for the missing ISIN
				mocks.vgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE848E01017",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 12345,
								CompanyId:     67890,
							},
						},
					}, nil)

				// Mock GetByVendorListingId call - listing not found for new ISIN
				mocks.dao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"12345",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock catalogDataIngester call - successful ingestion
				mocks.catalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{
						{
							Isin:              "INE848E01017",
							SecurityListingId: "BSE789",
						},
					}, []string{}, nil)

				// Mock second GetByISINAndExchange call after ingestion
				mocks.dao.EXPECT().
					GetByISINAndExchange(
						gomock.Any(),
						[]*catalogPb.ISINExchangePair{
							{
								Isin:     "INE848E01017",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
							},
							{
								Isin:     "INE848E01017",
								Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "BSE789",
							Isin:       "INE848E01017",
							Exchange:   catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
					}, nil)
			},
			want: &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpc.StatusOk(),
				SecListingIdsWithIsins: []*catalogPb.SecListingIdWithISIN{
					{
						IsinExchangePair: &catalogPb.ISINExchangePair{
							Isin:     "INE848E01016",
							Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
						SecurityListingExternalId: "BSE123",
					},
					{
						IsinExchangePair: &catalogPb.ISINExchangePair{
							Isin:     "INE848E01017",
							Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
						},
						SecurityListingExternalId: "BSE789",
					},
					{
						IsinExchangePair: &catalogPb.ISINExchangePair{
							Isin:     "INE848E01018",
							Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
						},
						SecurityListingExternalId: "NSE456",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks inside t.Run
			mocks := &testMocks{
				dao:                 daoMocks.NewMockSecurityListingsDao(ctrl),
				vgCatalogClient:     vgCatalogMocks.NewMockCatalogClient(ctrl),
				catalogDataIngester: ingesterMocks.NewMockCatalogDataIngester(ctrl),
			}

			// Setup expectations
			tt.mockSetup(mocks)

			// Create service instance
			service := NewService(
				&genConf.Config{},
				mocks.vgCatalogClient,
				nil, // stockCatalogRefreshPublisher not needed for this test
				nil, // idempotentTxnExecutor not needed for this test
				mocks.dao,
				nil, nil, nil,
				mocks.catalogDataIngester,
			)

			// Execute test
			response, err := service.GetSecListingIdsByISINs(context.Background(), tt.request)

			// Assertions
			require.NoError(t, err)
			if tt.want != nil && !proto.Equal(response, tt.want) {
				t.Errorf(" \n got: %v\nwant: %v", response, tt.want)
				return
			}
		})
	}
}

func TestGetPriceByDateAndSecListingIDs(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	test.InitTestServerWithoutDBConn()
	defer ctrl.Finish()

	tests := []struct {
		name      string
		request   *catalogPb.GetPriceByDateAndSecListingIDsRequest
		mockSetup func(*daoMocks.MockHistoricalPricesDao)
		want      *catalogPb.GetPriceByDateAndSecListingIDsResponse
	}{
		{
			name: "Empty security listing IDs should return invalid argument",
			request: &catalogPb.GetPriceByDateAndSecListingIDsRequest{
				SecurityListingIds: []string{},
				PriceDate: &date.Date{
					Year:  2024,
					Month: 3,
					Day:   15,
				},
			},
			mockSetup: func(m *daoMocks.MockHistoricalPricesDao) {},
			want: &catalogPb.GetPriceByDateAndSecListingIDsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("Security listing IDs are empty"),
			},
		},
		{
			name: "Nil price date should return invalid argument",
			request: &catalogPb.GetPriceByDateAndSecListingIDsRequest{
				SecurityListingIds: []string{"SEC123"},
				PriceDate:          nil,
			},
			mockSetup: func(m *daoMocks.MockHistoricalPricesDao) {},
			want: &catalogPb.GetPriceByDateAndSecListingIDsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("price date is nil"),
			},
		},
		{
			name: "Successful price retrieval",
			request: &catalogPb.GetPriceByDateAndSecListingIDsRequest{
				SecurityListingIds: []string{"SEC123", "SEC456"},
				PriceDate: &date.Date{
					Year:  2024,
					Month: 3,
					Day:   15,
				},
			},
			mockSetup: func(m *daoMocks.MockHistoricalPricesDao) {
				m.EXPECT().
					GetBySecuritiesAndDates(
						gomock.Any(),
						[]string{"SEC123", "SEC456"},
						[]*date.Date{{
							Year:  2024,
							Month: 3,
							Day:   15,
						}},
						[]catalogPb.HistoricalPriceFieldMask{
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_SECURITY_LISTING_ID,
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_CLOSE_PRICE,
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_PRICE_DATE,
						},
					).
					Return([]*catalogPb.HistoricalPrice{
						{
							SecurityListingId: "SEC123",
							ClosePrice: &money.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        500000000, // 0.5
							},
							PriceDate: &date.Date{
								Year:  2024,
								Month: 3,
								Day:   15,
							},
						},
						{
							SecurityListingId: "SEC456",
							ClosePrice: &money.Money{
								CurrencyCode: "INR",
								Units:        2000,
								Nanos:        750000000, // 0.75
							},
							PriceDate: &date.Date{
								Year:  2024,
								Month: 3,
								Day:   15,
							},
						},
					}, nil)
			},
			want: &catalogPb.GetPriceByDateAndSecListingIDsResponse{
				Status: rpc.StatusOk(),
				Prices: map[string]*money.Money{
					"SEC123": {
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        500000000,
					},
					"SEC456": {
						CurrencyCode: "INR",
						Units:        2000,
						Nanos:        750000000,
					},
				},
			},
		},
		{
			name: "DAO error should return internal error",
			request: &catalogPb.GetPriceByDateAndSecListingIDsRequest{
				SecurityListingIds: []string{"SEC123"},
				PriceDate: &date.Date{
					Year:  2024,
					Month: 3,
					Day:   15,
				},
			},
			mockSetup: func(m *daoMocks.MockHistoricalPricesDao) {
				m.EXPECT().
					GetBySecuritiesAndDates(
						gomock.Any(),
						[]string{"SEC123"},
						[]*date.Date{{
							Year:  2024,
							Month: 3,
							Day:   15,
						}},
						[]catalogPb.HistoricalPriceFieldMask{
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_SECURITY_LISTING_ID,
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_CLOSE_PRICE,
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_PRICE_DATE,
						},
					).
					Return(nil, errors.New("database error"))
			},
			want: &catalogPb.GetPriceByDateAndSecListingIDsResponse{
				Status: rpc.StatusInternalWithDebugMsg("dao error in GetBySecuritiesAndDates: database error"),
			},
		},
		{
			name: "Partial price data - some securities have prices, others don't",
			request: &catalogPb.GetPriceByDateAndSecListingIDsRequest{
				SecurityListingIds: []string{"SEC123", "SEC456", "SEC789"},
				PriceDate: &date.Date{
					Year:  2024,
					Month: 3,
					Day:   15,
				},
			},
			mockSetup: func(m *daoMocks.MockHistoricalPricesDao) {
				m.EXPECT().
					GetBySecuritiesAndDates(
						gomock.Any(),
						[]string{"SEC123", "SEC456", "SEC789"},
						[]*date.Date{{
							Year:  2024,
							Month: 3,
							Day:   15,
						}},
						[]catalogPb.HistoricalPriceFieldMask{
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_SECURITY_LISTING_ID,
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_CLOSE_PRICE,
							catalogPb.HistoricalPriceFieldMask_HISTORICAL_PRICE_FIELD_MASK_PRICE_DATE,
						},
					).
					Return([]*catalogPb.HistoricalPrice{
						{
							SecurityListingId: "SEC123",
							ClosePrice: &money.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        500000000,
							},
							PriceDate: &date.Date{
								Year:  2024,
								Month: 3,
								Day:   15,
							},
						},
						{
							SecurityListingId: "SEC789",
							ClosePrice: &money.Money{
								CurrencyCode: "INR",
								Units:        3000,
								Nanos:        250000000,
							},
							PriceDate: &date.Date{
								Year:  2024,
								Month: 3,
								Day:   15,
							},
						},
					}, nil)
			},
			want: &catalogPb.GetPriceByDateAndSecListingIDsResponse{
				Status: rpc.StatusOk(),
				Prices: map[string]*money.Money{
					"SEC123": {
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        500000000,
					},
					"SEC789": {
						CurrencyCode: "INR",
						Units:        3000,
						Nanos:        250000000,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockHistoricalPricesDao := daoMocks.NewMockHistoricalPricesDao(ctrl)
			tt.mockSetup(mockHistoricalPricesDao)
			// Set fallback to DAO for Redis cache
			pgdbhistoricalpricesfetcher := historicalpricesfetcher.NewPGDBHistoricalPricesDAO(mockHistoricalPricesDao)

			// Create service instance
			service := NewService(
				&genConf.Config{},
				nil, // vgCatalogClient not needed for these tests
				nil, // stockCatalogRefreshPublisher not needed for these tests
				nil, // idempotentTxnExecutor not needed for these tests
				nil, // securityListingsDao not needed for these tests
				nil,
				pgdbhistoricalpricesfetcher, nil, nil,
			)

			// Execute test
			response, err := service.GetPriceByDateAndSecListingIDs(context.Background(), tt.request)

			// Assertions
			require.NoError(t, err)
			if tt.want != nil && !proto.Equal(response, tt.want) {
				t.Errorf(" \n got: %v\nwant: %v", response, tt.want)
				return
			}
		})
	}
}

func TestGetSecurityListings(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	test.InitTestServerWithoutDBConn()
	defer ctrl.Finish()

	tests := []struct {
		name                string
		request             *catalogPb.GetSecurityListingsRequest
		mockSecurityListDao func(*daoMocks.MockSecurityListingsDao)
		mockSecuritiesDao   func(*daoMocks.MockSecuritiesDao)
		want                *catalogPb.GetSecurityListingsResponse
	}{
		{
			name: "Empty identifiers should return invalid argument",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: nil,
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				// No mock setup needed
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("identifiers are required"),
			},
		},
		{
			name: "Successful retrieval by external IDs without security fields",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
					ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
						ExternalIds: []string{"EXT001", "EXT002"},
					},
				},
				SecurityListingFields: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				m.EXPECT().
					GetByExternalIds(
						gomock.Any(),
						[]string{"EXT001", "EXT002"},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "EXT001",
							Symbol:     "AAPL",
							SecurityId: "SEC001",
						},
						{
							ExternalId: "EXT002",
							Symbol:     "GOOGL",
							SecurityId: "SEC002",
						},
					}, nil)
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed since no security fields requested
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusOk(),
				SecurityAndSecurityListings: []*catalogPb.SecurityAndSecurityListing{
					{
						SecurityListing: &catalogPb.SecurityListing{
							ExternalId: "EXT001",
							Symbol:     "AAPL",
							SecurityId: "SEC001",
						},
					},
					{
						SecurityListing: &catalogPb.SecurityListing{
							ExternalId: "EXT002",
							Symbol:     "GOOGL",
							SecurityId: "SEC002",
						},
					},
				},
			},
		},
		{
			name: "Successful retrieval by external IDs with security fields",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
					ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
						ExternalIds: []string{"EXT001"},
					},
				},
				SecurityListingFields: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
				},
				SecurityFields: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				m.EXPECT().
					GetByExternalIds(
						gomock.Any(),
						[]string{"EXT001"},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "EXT001",
							Symbol:     "AAPL",
							SecurityId: "SEC001",
						},
					}, nil)
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				m.EXPECT().
					BulkGet(
						gomock.Any(),
						[]string{"SEC001"},
						[]catalogPb.SecurityFieldMask{
							catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
							catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
						},
					).
					Return(map[string]*catalogPb.Security{
						"SEC001": {
							Id:           "SEC001",
							SecurityName: "Apple Inc.",
						},
					}, nil)
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusOk(),
				SecurityAndSecurityListings: []*catalogPb.SecurityAndSecurityListing{
					{
						SecurityListing: &catalogPb.SecurityListing{
							ExternalId: "EXT001",
							Symbol:     "AAPL",
							SecurityId: "SEC001",
						},
						Security: &catalogPb.Security{
							Id:           "SEC001",
							SecurityName: "Apple Inc.",
						},
					},
				},
			},
		},
		{
			name: "Successful retrieval by exchange symbols",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExchangeSymbols_{
					ExchangeSymbols: &catalogPb.GetSecurityListingsRequest_ExchangeSymbols{
						ExchangeSymbols: []*catalogPb.ExchangeSymbol{
							{
								Symbol:   "AAPL",
								Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
							},
							{
								Symbol:   "GOOGL",
								Exchange: catalogPb.Exchange_EXCHANGE_USA_NASDAQ,
							},
						},
					},
				},
				SecurityListingFields: []catalogPb.SecurityListingFieldMask{
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
					catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				m.EXPECT().
					GetByExchangeSymbols(
						gomock.Any(),
						[]*catalogPb.ExchangeSymbol{
							{
								Symbol:   "AAPL",
								Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
							},
							{
								Symbol:   "GOOGL",
								Exchange: catalogPb.Exchange_EXCHANGE_USA_NASDAQ,
							},
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
						},
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "EXT001",
							Symbol:     "AAPL",
							SecurityId: "SEC001",
						},
						{
							ExternalId: "EXT002",
							Symbol:     "GOOGL",
							SecurityId: "SEC002",
						},
					}, nil)
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed since no security fields requested
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusOk(),
				SecurityAndSecurityListings: []*catalogPb.SecurityAndSecurityListing{
					{
						SecurityListing: &catalogPb.SecurityListing{
							ExternalId: "EXT001",
							Symbol:     "AAPL",
							SecurityId: "SEC001",
						},
					},
					{
						SecurityListing: &catalogPb.SecurityListing{
							ExternalId: "EXT002",
							Symbol:     "GOOGL",
							SecurityId: "SEC002",
						},
					},
				},
			},
		},
		{
			name: "Empty external IDs should return invalid argument",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
					ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
						ExternalIds: []string{},
					},
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				// No mock setup needed
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("external_ids cannot be empty"),
			},
		},
		{
			name: "Empty exchange symbols should return invalid argument",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExchangeSymbols_{
					ExchangeSymbols: &catalogPb.GetSecurityListingsRequest_ExchangeSymbols{
						ExchangeSymbols: []*catalogPb.ExchangeSymbol{},
					},
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				// No mock setup needed
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("exchange_symbols cannot be empty"),
			},
		},
		{
			name: "Invalid exchange symbols should return invalid argument",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExchangeSymbols_{
					ExchangeSymbols: &catalogPb.GetSecurityListingsRequest_ExchangeSymbols{
						ExchangeSymbols: []*catalogPb.ExchangeSymbol{
							{
								Symbol:   "",
								Exchange: catalogPb.Exchange_EXCHANGE_USA_NYSE,
							},
						},
					},
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				// No mock setup needed
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("all exchange_symbols must have valid symbol and exchange"),
			},
		},
		{
			name: "No security listings found should return record not found",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
					ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
						ExternalIds: []string{"NON_EXISTENT"},
					},
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				m.EXPECT().
					GetByExternalIds(
						gomock.Any(),
						[]string{"NON_EXISTENT"},
						gomock.Any(),
					).
					Return([]*catalogPb.SecurityListing{}, nil)
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "Security fetch failure should return error",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
					ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
						ExternalIds: []string{"EXT001"},
					},
				},
				SecurityFields: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				m.EXPECT().
					GetByExternalIds(
						gomock.Any(),
						[]string{"EXT001"},
						gomock.Any(),
					).
					Return([]*catalogPb.SecurityListing{
						{
							ExternalId: "EXT001",
							SecurityId: "SEC001",
						},
					}, nil)
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				m.EXPECT().
					BulkGet(
						gomock.Any(),
						[]string{"SEC001"},
						[]catalogPb.SecurityFieldMask{
							catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
						},
					).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "Security listing DAO error should return internal error",
			request: &catalogPb.GetSecurityListingsRequest{
				Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
					ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
						ExternalIds: []string{"EXT001"},
					},
				},
			},
			mockSecurityListDao: func(m *daoMocks.MockSecurityListingsDao) {
				m.EXPECT().
					GetByExternalIds(
						gomock.Any(),
						[]string{"EXT001"},
						gomock.Any(),
					).
					Return(nil, errors.New("database error"))
			},
			mockSecuritiesDao: func(m *daoMocks.MockSecuritiesDao) {
				// No mock setup needed
			},
			want: &catalogPb.GetSecurityListingsResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(errors.New("database error")),
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockSecurityListingsDao := daoMocks.NewMockSecurityListingsDao(ctrl)
			mockSecuritiesDao := daoMocks.NewMockSecuritiesDao(ctrl)

			tt.mockSecurityListDao(mockSecurityListingsDao)
			tt.mockSecuritiesDao(mockSecuritiesDao)

			service := &Service{
				conf:                &genConf.Config{},
				securityListingsDao: mockSecurityListingsDao,
				securitiesDao:       mockSecuritiesDao,
			}

			got, err := service.GetSecurityListings(context.Background(), tt.request)
			require.NoError(t, err)

			// Compare the responses
			if !proto.Equal(got.Status, tt.want.Status) {
				t.Errorf("GetSecurityListings() status = %v, want %v", got.Status, tt.want.Status)
			}

			if len(got.SecurityAndSecurityListings) != len(tt.want.SecurityAndSecurityListings) {
				t.Errorf("GetSecurityListings() length = %d, want %d", len(got.SecurityAndSecurityListings), len(tt.want.SecurityAndSecurityListings))
				return
			}

			for i, gotItem := range got.SecurityAndSecurityListings {
				wantItem := tt.want.SecurityAndSecurityListings[i]
				if !proto.Equal(gotItem.SecurityListing, wantItem.SecurityListing) {
					t.Errorf("GetSecurityListings() SecurityListing[%d] = %v, want %v", i, gotItem.SecurityListing, wantItem.SecurityListing)
				}
				if !proto.Equal(gotItem.Security, wantItem.Security) {
					t.Errorf("GetSecurityListings() Security[%d] = %v, want %v", i, gotItem.Security, wantItem.Security)
				}
			}
		})
	}
}
