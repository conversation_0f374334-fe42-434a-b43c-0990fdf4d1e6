package catalog

import (
	"context"
	"errors"
	"strconv"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/ingester"
)

func (s *Service) AddSecurityWithISINs(ctx context.Context, req *catalogPb.AddSecurityWithISINsRequest) (*catalogPb.AddSecurityWithISINsResponse, error) {
	var missingListings []*ingester.IsinVendorIdMapping
	var failedISINs []string
	var isinSecurityListingPairs []*catalogPb.IsinSecurityListingPair
	for _, isin := range req.GetIsins() {
		resp, err := s.getAssetDetails(ctx, isin)
		if err != nil {
			logger.Error(ctx, "error getting asset details for isin", zapIsin(isin), zap.Error(err))
			failedISINs = append(failedISINs, isin)
			continue
		}

		// Check if the listing is present in our catalog by fetching the record for the vendor listing id
		// If the record is not found, add it missingListings for ingesting the isin
		for _, listing := range resp.GetAssetDetails() {
			isinSecurityListingPair, missingListing, err := s.getListingFromDb(ctx, listing, isin)
			if err != nil {
				failedISINs = append(failedISINs, isin)
				continue
			}
			if missingListing != nil {
				missingListings = append(missingListings, missingListing)
			}
			if isinSecurityListingPair != nil {
				isinSecurityListingPairs = append(isinSecurityListingPairs, isinSecurityListingPair)
			}
		}
	}

	if len(missingListings) > 0 {
		logger.Info(ctx, "ISINs without a mapping in security listing", zap.Any("missingListings", missingListings))
	}
	// If there are ISINs which have not been mapped to a listing, ingest them and publish to consumers to fill listing and historical price details
	ingestedIsinSecurityListingPairs, failedISINs, err := s.catalogDataIngester.IngestByISINs(ctx, missingListings, failedISINs)
	if err != nil {
		logger.Error(ctx, "Error when ingesting and publishing non mapped ISINs", zap.Error(err))
		return &catalogPb.AddSecurityWithISINsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if len(failedISINs) > 0 {
		logger.Error(ctx, "error in ingesting following ISINs", zap.Strings("failedISINs", failedISINs))
	}

	if len(ingestedIsinSecurityListingPairs) > 0 {
		isinSecurityListingPairs = append(isinSecurityListingPairs, ingestedIsinSecurityListingPairs...)
	}

	return &catalogPb.AddSecurityWithISINsResponse{
		Status:                   rpcPb.StatusOk(),
		IsinSecurityListingPairs: isinSecurityListingPairs,
	}, nil
}

func (s *Service) getListingFromDb(ctx context.Context, listing *vendorPb.AssetDetails, isin string) (*catalogPb.IsinSecurityListingPair, *ingester.IsinVendorIdMapping, error) {
	vendorListingId := strconv.Itoa(int(listing.GetTradingItemId()))
	secListing, listingErr := s.securityListingsDao.GetByVendorListingId(ctx, vendorgateway.Vendor_BRIDGEWISE, vendorListingId, []catalogPb.SecurityListingFieldMask{
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
	})
	if listingErr != nil {
		if errors.Is(listingErr, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no listing found for vendorListingId", zapVLId(vendorListingId), zapIsin(isin))
			return nil, &ingester.IsinVendorIdMapping{
				Isin:             isin,
				VendorSecurityId: strconv.Itoa(int(listing.GetCompanyId())),
				VendorListingId:  strconv.Itoa(int(listing.GetTradingItemId())),
			}, nil
		}
		logger.Error(ctx, "error in fetching security listing", zap.Error(listingErr), zapVLId(vendorListingId), zapIsin(isin))
		return nil, nil, listingErr
	}

	updateErr := s.updateIsinInDb(ctx, isin, vendorListingId)
	if updateErr != nil {
		logger.Error(ctx, "error in updating isin", zap.Error(updateErr), zapIsin(isin), zapVLId(vendorListingId))
		return nil, nil, updateErr
	}

	return &catalogPb.IsinSecurityListingPair{
		Isin:              isin,
		SecurityListingId: secListing.GetExternalId(),
	}, nil, nil
}

func (s *Service) updateIsinInDb(ctx context.Context, isin, vendorListingId string) error {
	listing := &catalogPb.SecurityListing{
		Isin:            isin,
		Vendor:          vendorgateway.Vendor_BRIDGEWISE,
		VendorListingId: vendorListingId,
	}
	if updateErr := s.securityListingsDao.UpdateByVendorListingId(ctx, listing, []catalogPb.SecurityListingFieldMask{
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
	}); updateErr != nil {
		logger.Error(ctx, "Error updating security listing with ISIN", zap.Error(updateErr), zapIsin(isin), zapVLId(listing.GetVendorListingId()))
		return updateErr
	}
	return nil
}

func (s *Service) getAssetDetails(ctx context.Context, isin string) (*vgCatalogPb.GetAssetIdentifierDetailsResponse, error) {
	resp, err := s.vgCatalogClient.GetAssetIdentifierDetails(ctx, &vgCatalogPb.GetAssetIdentifierDetailsRequest{
		Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		IdentifierValue: isin,
		IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "Error fetching security with ISIN", zap.Error(rpcErr), zapIsin(isin))
		return nil, err
	}
	if len(resp.GetAssetDetails()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp, nil
}

func zapVLId(id string) zap.Field {
	return zap.String("vendorListingId", id)
}

func zapIsin(isin string) zap.Field {
	return zap.String("ISIN", isin)
}
