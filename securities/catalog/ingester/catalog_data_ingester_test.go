package ingester

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	securitiesPb "github.com/epifi/gamma/api/securities/catalog"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	mockUssStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog/mocks"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vgCatalogMocks "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise/mocks"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	daoMocks "github.com/epifi/gamma/securities/catalog/dao/mocks"
	securitiesconf "github.com/epifi/gamma/securities/config"
	secconfig "github.com/epifi/gamma/securities/config/genconf"
)

type mockStruct struct {
	securityDao         *daoMocks.MockSecuritiesDao
	securityListingsDao *daoMocks.MockSecurityListingsDao
	vgCatalogClient     *vgCatalogMocks.MockCatalogClient
	ussCatalogClient    *mockUssStocksCatalogPb.MockCatalogManagerClient
}

func TestCatalogDataIngesterImpl_IngestByPage(t *testing.T) {
	logger.Init(cfg.TestEnv)

	genConf, err := dynconf.LoadConfig(securitiesconf.Load, secconfig.NewConfig, cfg.SECURITIES_SERVICE)
	if err != nil {
		t.Errorf("failed to load securities conf: %v", err)
	}

	tests := []struct {
		name        string
		pageNum     int32
		setup       func(*mockStruct)
		expectedErr error
	}{
		{
			name:    "success_with_valid_page_new_security",
			pageNum: 1,
			setup: func(mocks *mockStruct) {
				// Mock successful GetCompanies response
				companies := &vendorPb.CompaniesData{
					Data: []*vendorPb.CompanyDetails{
						{
							CompanyId:                123,
							CompanyName:              "Test Company",
							CompanyNameShort:         "TEST",
							Website:                  "https://test.com",
							RegionName:               "US",
							IncorporationCountryName: "United States",
							GicsSectorName:           "Information Technology",
							GicsIndustryGroupName:    "Software & Services",
							GicsIndustryName:         "Software",
						},
					},
				}
				resp := &vgCatalogPb.GetCompaniesResponse{
					Status:    rpc.StatusOk(),
					Companies: companies,
				}
				mocks.vgCatalogClient.EXPECT().GetCompanies(gomock.Any(), gomock.Any()).Return(resp, nil)

				// Mock security dao - company doesn't exist, so create new
				mocks.securityDao.EXPECT().GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "123", gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)

				createdSecurity := &securitiesPb.Security{
					Id:               "sec_123",
					VendorSecurityId: "123",
				}
				mocks.securityDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdSecurity, nil)

				// Mock trading items response
				tradingItemsResp := &vgCatalogPb.GetCompanyTradingItemsResponse{
					Status: rpc.StatusOk(),
					AssetDetails: []*vendorPb.AssetDetails{
						{
							TradingItemId:  456,
							TickerSymbol:   "TEST",
							ExchangeSymbol: "NYSE",
							PrimaryFlag:    true,
						},
					},
				}
				mocks.vgCatalogClient.EXPECT().GetCompanyTradingItems(gomock.Any(), gomock.Any()).Return(tradingItemsResp, nil)

				// Mock USS catalog client for US stocks
				ussResp := &ussCatalogPb.GetStocksResponse{
					Status: rpc.StatusRecordNotFound(),
				}
				mocks.ussCatalogClient.EXPECT().GetStocks(gomock.Any(), gomock.Any()).Return(ussResp, nil)

				// Mock batch upsert
				mocks.securityListingsDao.EXPECT().BatchUpsert(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
		{
			name:        "error_max_pages_exceeded",
			pageNum:     1500,
			setup:       func(mocks *mockStruct) {}, // No mocks needed as it fails early
			expectedErr: ErrMaxPagesExceeded,
		},
		{
			name:    "success_with_existing_security",
			pageNum: 1,
			setup: func(mocks *mockStruct) {
				// Mock successful GetCompanies response
				companies := &vendorPb.CompaniesData{
					Data: []*vendorPb.CompanyDetails{
						{
							CompanyId:   123,
							CompanyName: "Existing Company",
						},
					},
				}
				resp := &vgCatalogPb.GetCompaniesResponse{
					Status:    rpc.StatusOk(),
					Companies: companies,
				}
				mocks.vgCatalogClient.EXPECT().GetCompanies(gomock.Any(), gomock.Any()).Return(resp, nil)

				// Mock security dao - company exists
				existingSecurity := &securitiesPb.Security{
					Id:               "sec_123",
					VendorSecurityId: "123",
				}
				mocks.securityDao.EXPECT().GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "123", gomock.Any()).
					Return(existingSecurity, nil)

				// Mock trading items response with Indian exchange
				tradingItemsResp := &vgCatalogPb.GetCompanyTradingItemsResponse{
					Status: rpc.StatusOk(),
					AssetDetails: []*vendorPb.AssetDetails{
						{
							TradingItemId:  456,
							TickerSymbol:   "TESTINDIA",
							ExchangeSymbol: "NSEI",
							PrimaryFlag:    true,
						},
					},
				}
				mocks.vgCatalogClient.EXPECT().GetCompanyTradingItems(gomock.Any(), gomock.Any()).Return(tradingItemsResp, nil)

				// Mock batch upsert
				mocks.securityListingsDao.EXPECT().BatchUpsert(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mocks
			mocks := &mockStruct{
				securityDao:         daoMocks.NewMockSecuritiesDao(ctrl),
				securityListingsDao: daoMocks.NewMockSecurityListingsDao(ctrl),
				vgCatalogClient:     vgCatalogMocks.NewMockCatalogClient(ctrl),
				ussCatalogClient:    mockUssStocksCatalogPb.NewMockCatalogManagerClient(ctrl),
			}

			// Setup mocks
			tt.setup(mocks)

			// Create ingester
			ingester := &CatalogDataIngesterImpl{
				securityDao:         mocks.securityDao,
				securityListingsDao: mocks.securityListingsDao,
				vgCatalogClient:     mocks.vgCatalogClient,
				ussCatalogClient:    mocks.ussCatalogClient,
				conf:                genConf,
			}

			// Execute test
			err := ingester.IngestByPage(context.Background(), tt.pageNum)

			// Assertions
			if tt.expectedErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expectedErr.Error(), err.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}
