// Code generated by MockGen. DO NOT EDIT.
// Source: catalog_data_ingester.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	catalog "github.com/epifi/gamma/api/securities/catalog"
	ingester "github.com/epifi/gamma/securities/catalog/ingester"
	gomock "github.com/golang/mock/gomock"
)

// MockCatalogDataIngester is a mock of CatalogDataIngester interface.
type MockCatalogDataIngester struct {
	ctrl     *gomock.Controller
	recorder *MockCatalogDataIngesterMockRecorder
}

// MockCatalogDataIngesterMockRecorder is the mock recorder for MockCatalogDataIngester.
type MockCatalogDataIngesterMockRecorder struct {
	mock *MockCatalogDataIngester
}

// NewMockCatalogDataIngester creates a new mock instance.
func NewMockCatalogDataIngester(ctrl *gomock.Controller) *MockCatalogDataIngester {
	mock := &MockCatalogDataIngester{ctrl: ctrl}
	mock.recorder = &MockCatalogDataIngesterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCatalogDataIngester) EXPECT() *MockCatalogDataIngesterMockRecorder {
	return m.recorder
}

// IngestByISINs mocks base method.
func (m *MockCatalogDataIngester) IngestByISINs(ctx context.Context, isinMappings []*ingester.IsinVendorIdMapping, failedISINs []string) ([]*catalog.IsinSecurityListingPair, []string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IngestByISINs", ctx, isinMappings, failedISINs)
	ret0, _ := ret[0].([]*catalog.IsinSecurityListingPair)
	ret1, _ := ret[1].([]string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// IngestByISINs indicates an expected call of IngestByISINs.
func (mr *MockCatalogDataIngesterMockRecorder) IngestByISINs(ctx, isinMappings, failedISINs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IngestByISINs", reflect.TypeOf((*MockCatalogDataIngester)(nil).IngestByISINs), ctx, isinMappings, failedISINs)
}

// IngestByPage mocks base method.
func (m *MockCatalogDataIngester) IngestByPage(ctx context.Context, pageNum int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IngestByPage", ctx, pageNum)
	ret0, _ := ret[0].(error)
	return ret0
}

// IngestByPage indicates an expected call of IngestByPage.
func (mr *MockCatalogDataIngesterMockRecorder) IngestByPage(ctx, pageNum interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IngestByPage", reflect.TypeOf((*MockCatalogDataIngester)(nil).IngestByPage), ctx, pageNum)
}
