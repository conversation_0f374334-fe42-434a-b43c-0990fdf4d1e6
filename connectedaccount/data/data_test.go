package data

import (
	"context"
	"encoding/base64"
	"encoding/xml"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	mockDateTime "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	aaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	vgAaMock "github.com/epifi/gamma/api/vendorgateway/aa/mocks"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/crypto"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/data/fi_types/xml_models"
	caError "github.com/epifi/gamma/connectedaccount/error"
	"github.com/epifi/gamma/connectedaccount/notification"
	account_processor "github.com/epifi/gamma/connectedaccount/test/mocks/mock_account_processor"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_crypto"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_dao"
	mock_fi_types "github.com/epifi/gamma/connectedaccount/test/mocks/mock_fi"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_fi_factory"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_notification"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"
)

var (
	dfaFetched = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId:          "consent-reference-id-1",
		CreatedAt:                   timestampPb.New(time.Now().AddDate(0, -1, 0)),
		UpdatedAt:                   timestampPb.Now(),
		DataFetchAttemptInitiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
	}

	dfaFetchedWithCurrentTime = func(currentTime time.Time) *caPb.DataFetchAttempt {
		return &caPb.DataFetchAttempt{
			Id:            "attempt-id-1",
			ActorId:       "actor-id-1",
			ConsentId:     "consent-id-1",
			TransactionId: "txn-id-1",
			SessionId:     "session-id-1",
			FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED,
			DataRangeFrom: timestampPb.New(currentTime.AddDate(-1, 0, 0)),
			DataRangeTo:   timestampPb.New(currentTime.AddDate(0, -1, 0)),
			KeyMaterial: &caPb.KeyMaterial{
				CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
				Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
				KeyExpiry:  timestampPb.New(currentTime.AddDate(0, 0, 1)),
				PublicKey:  "test-public-key",
				Nonce:      "test-nonce",
				PrivateKey: "test-private-key",
			},
			ConsentReferenceId:          "consent-reference-id-1",
			CreatedAt:                   timestampPb.New(time.Now().AddDate(0, -1, 0)),
			UpdatedAt:                   timestampPb.Now(),
			DataFetchAttemptInitiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
		}
	}

	dfaFetched2WithCurrentTime = func(currentTime time.Time) *caPb.DataFetchAttempt {
		return &caPb.DataFetchAttempt{
			Id:            "attempt-id-1",
			ActorId:       "actor-id-1",
			ConsentId:     "consent-id-1",
			TransactionId: "txn-id-1",
			SessionId:     "session-id-1",
			FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED,
			DataRangeFrom: timestampPb.New(currentTime.AddDate(-2, 0, 0)),
			DataRangeTo:   timestampPb.New(currentTime.AddDate(-1, -1, 0)),
			KeyMaterial: &caPb.KeyMaterial{
				CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
				Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
				KeyExpiry:  timestampPb.New(currentTime.AddDate(0, 0, 1)),
				PublicKey:  "test-public-key",
				Nonce:      "test-nonce",
				PrivateKey: "test-private-key",
			},
			ConsentReferenceId:          "consent-reference-id-1",
			CreatedAt:                   timestampPb.New(time.Now().AddDate(0, -1, 0)),
			UpdatedAt:                   timestampPb.Now(),
			DataFetchAttemptInitiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
		}
	}

	dfaFetchedInitiatedByUser = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId:          "consent-reference-id-1",
		CreatedAt:                   timestampPb.New(time.Now().AddDate(0, -1, 0)),
		UpdatedAt:                   timestampPb.Now(),
		DataFetchAttemptInitiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
	}

	dfaDecrypted = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_DECRYPTED,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId: "consent-reference-id-1",
	}
	dfaNonTerminal = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_PENDING,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId: "consent-reference-id-1",
		UpdatedAt:          timestampPb.New(time.Now()),
	}
	dfaNonTerminal2 = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_PENDING,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId: "consent-reference-id-1",
		UpdatedAt:          timestampPb.New(time.Now()),
		FiNotificationInfo: &caPb.FiNotificationInfo{
			AccountNotificationStatusList: []*caPb.AccountNotificationStatus{
				{
					LinkRefNumber:        "BLinkRef",
					ReceivedNotification: true,
					FiNotificationStatus: caEnumPb.FINotificationStatus_FI_NOTIFICATION_STATUS_READY,
				},
				{
					LinkRefNumber:        "ALinkRef",
					ReceivedNotification: true,
					FiNotificationStatus: caEnumPb.FINotificationStatus_FI_NOTIFICATION_STATUS_READY,
				},
			},
		},
	}
	dfaNonTerminal3 = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_PENDING,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId: "consent-reference-id-1",
		UpdatedAt:          timestampPb.New(time.Now()),
		FiNotificationInfo: &caPb.FiNotificationInfo{
			AccountNotificationStatusList: []*caPb.AccountNotificationStatus{
				{
					LinkRefNumber:        "BLinkRef",
					ReceivedNotification: false,
					FiNotificationStatus: caEnumPb.FINotificationStatus_FI_NOTIFICATION_STATUS_UNSPECIFIED,
				},
				{
					LinkRefNumber:        "ALinkRef",
					ReceivedNotification: true,
					FiNotificationStatus: caEnumPb.FINotificationStatus_FI_NOTIFICATION_STATUS_READY,
				},
			},
		},
	}
	dfaTerminal = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId: "consent-reference-id-1",
	}
	encryptedData = []*caPb.FI{
		{
			FipId: "HDFC-FIP",
			Data: []*caPb.Data{
				{
					LinkRefNumber: "link-reference-number-hdfc",
					EncryptedFi:   "test",
					DecryptedFi:   encodedRawFiDataXml,
				},
			},
			KeyMaterial: &caPb.KeyMaterial{
				CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
				Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
				KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
				PublicKey:  "test",
				Nonce:      "test",
				PrivateKey: "test",
			},
		},
	}

	dfaStuckedInNonTerminalState = &caPb.DataFetchAttempt{
		Id:            "attempt-id-1",
		ActorId:       "actor-id-1",
		ConsentId:     "consent-id-1",
		TransactionId: "txn-id-1",
		SessionId:     "session-id-1",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_PENDING,
		DataRangeFrom: timestampPb.New(time.Now().AddDate(0, -1, 0)),
		DataRangeTo:   timestampPb.Now(),
		KeyMaterial: &caPb.KeyMaterial{
			CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
			Curve:      caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
			KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
			PublicKey:  "test-public-key",
			Nonce:      "test-nonce",
			PrivateKey: "test-private-key",
		},
		ConsentReferenceId: "consent-reference-id-1",
	}
)

var rawFiDataXml = xml_models.RawFiDataXmlStruct{
	LinkedAccRef:    "linked-acc-ref-1",
	MaskedAccNumber: "masked-acc-numer-1",
	Type:            caPkg.Deposit,
}

// b64EncodedAccData is encoded base 64 data for below xml data
// <?xml version="1.0" encoding="UTF-8" standalone="yes"?><Account linkedAccRef="linked-acc-ref-1" maskedAccNumber="masked-acc-numer-1"><Profile><Holders type="SINGLE"><Holder address="ABC&!@#$%^*(){}|:,./;”**********\" /></Holders></Profile></Account>
var b64EncodedAccData = "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/PjxBY2NvdW50IGxpbmtlZEFjY1JlZj0ibGlua2VkLWFjYy1yZWYtMSIgbWFza2VkQWNjTnVtYmVyPSJtYXNrZWQtYWNjLW51bWVyLTEiPjxQcm9maWxlPjxIb2xkZXJzIHR5cGU9IlNJTkdMRSI+PEhvbGRlciBhZGRyZXNzPSJBQkMmIUAjJCVeKigpe318OiwuLzvigJ0xMjM0NTY3ODkwXCIgLz48L0hvbGRlcnM+PC9Qcm9maWxlPjwvQWNjb3VudD4="

var marshalledFiDataXml, _ = xml.Marshal(rawFiDataXml)
var encodedRawFiDataXml = base64.StdEncoding.EncodeToString(marshalledFiDataXml)

func TestProcessorService_CreateAttempt(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCDao := mock_dao.NewMockConsentDao(ctr)
	mockVgAaClient := vgAaMock.NewMockAccountAggregatorClient(ctr)
	mockDfaDao := mock_dao.NewMockDataFetchAttemptDao(ctr)
	mockDpaDao := mock_dao.NewMockDataProcessAttemptDao(ctr)
	mockEde := mock_crypto.NewMockEde(ctr)
	mockBpDao := mock_dao.NewMockAaBatchProcessDao(ctr)
	mockFiFactory := mock_fi_factory.NewMockIFIFactory(ctr)
	mockAccDao := mock_dao.NewMockAaAccountDao(ctr)
	mockTxnDao := mock_dao.NewMockAaTransactionDao(ctr)
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctr)
	mockTime := mockDateTime.NewMockTime(ctr)
	type fields struct {
		vgAaClient        aaPb.AccountAggregatorClient
		consentDao        dao.ConsentDao
		attemptDao        dao.DataFetchAttemptDao
		attemptProcessDao dao.DataProcessAttemptDao
		conf              *genconf.Config
		edeService        crypto.Ede
		batchProcessDao   dao.AaBatchProcessDao
		fiFactory         IFIFactory
		accDao            dao.AaAccountDao
		txnDao            dao.AaTransactionDao
		consentRequestDao dao.ConsentRequestDao
	}
	type args struct {
		ctx         context.Context
		consent     *caPb.Consent
		initiatedBy caEnumPb.DataFetchAttemptInitiatedBy
		mocks       []interface{}
		purpose     caEnumPb.DataFetchAttemptPurpose
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.DataFetchAttempt
		wantErr bool
	}{
		{
			name: "error getting latest attempt by consent id",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx: context.Background(),
				consent: &caPb.Consent{ConsentId: "test-consent-id", NextFetchAt: timestampPb.New(
					time.Now().AddDate(0, 0, -1))},
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "attempt already exists in non terminal state",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx: context.Background(),
				consent: &caPb.Consent{ConsentId: "test-consent-id", NextFetchAt: timestampPb.New(
					time.Now().AddDate(0, 0, -1))},
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
				},
			},
			want:    dfaNonTerminal,
			wantErr: true,
		},
		{
			name: "attempt already exists in non terminal state",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
				},
			},
			want:    dfaNonTerminal,
			wantErr: true,
		},
		{
			name: "attempt does not exist : error in getting consent request",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error getting fi data range : error in getting latest success attempt",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "record not found in getting latest success attempt, create attempt error",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
					mockTime.EXPECT().Now().Return(time.Now()).AnyTimes(),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error generating key material",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
					mockEde.EXPECT().GenerateKeyPairWithNonce(gomock.Any()).Return(nil, errors.New("err")),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error requesting data call to AA",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
					mockEde.EXPECT().GenerateKeyPairWithNonce(gomock.Any()).Return(&caPb.KeyMaterial{
						CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH, Curve: caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
						KeyExpiry: timestampPb.New(time.Now().AddDate(0, 0, 1)), PublicKey: "test",
						Nonce: "test", PrivateKey: "test"}, nil),
					mockVgAaClient.EXPECT().RequestData(gomock.Any(), gomock.Any()).Return(&aaPb.RequestDataResponse{Status: rpcPb.StatusInternal()}, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error updating attempt in DB",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
					mockEde.EXPECT().GenerateKeyPairWithNonce(gomock.Any()).Return(&caPb.KeyMaterial{
						CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH, Curve: caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
						KeyExpiry: timestampPb.New(time.Now().AddDate(0, 0, 1)), PublicKey: "test",
						Nonce: "test", PrivateKey: "test"}, nil),
					mockVgAaClient.EXPECT().RequestData(gomock.Any(), gomock.Any()).Return(&aaPb.RequestDataResponse{Status: rpcPb.StatusOk(), SessionId: "sess"}, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("err")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successfully create attempt in DB - initiated by job",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     consent1,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
					mockEde.EXPECT().GenerateKeyPairWithNonce(gomock.Any()).Return(&caPb.KeyMaterial{
						CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH, Curve: caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
						KeyExpiry: timestampPb.New(time.Now().AddDate(0, 0, 1)), PublicKey: "test",
						Nonce: "test", PrivateKey: "test"}, nil),
					mockVgAaClient.EXPECT().RequestData(gomock.Any(), gomock.Any()).Return(&aaPb.RequestDataResponse{
						Status: rpcPb.StatusOk(), SessionId: "sess"}, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "successfully create attempt in DB - initiated by user",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     consent1,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil).Times(2),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
					mockEde.EXPECT().GenerateKeyPairWithNonce(gomock.Any()).Return(&caPb.KeyMaterial{
						CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH, Curve: caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
						KeyExpiry: timestampPb.New(time.Now().AddDate(0, 0, 1)), PublicKey: "test",
						Nonce: "test", PrivateKey: "test"}, nil),
					mockVgAaClient.EXPECT().RequestData(gomock.Any(), gomock.Any()).Return(&aaPb.RequestDataResponse{
						Status: rpcPb.StatusOk(), SessionId: "sess"}, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "attempt already exists and stucked in non terminal state, error while updating attempt in case of reconciliation",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(&caPb.DataFetchAttempt{
						Id:          "attempt-id-1",
						FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_PENDING,
					}, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error while updating attempt")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "attempt already exists and stucked in non terminal state, no error while updating attempt, successfully created attempt",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: consent1,
				mocks: []interface{}{
					mockDfaDao.EXPECT().GetLatestConsentAttemptByConsentId(gomock.Any(), gomock.Any()).Return(dfaStuckedInNonTerminalState, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockDfaDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockDfaDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
					mockEde.EXPECT().GenerateKeyPairWithNonce(gomock.Any()).Return(&caPb.KeyMaterial{
						CryptoAlgo: caEnumPb.CryptoAlgo_CRYPTO_ALGO_ECDH, Curve: caEnumPb.CryptoCurve_CRYPTO_CURVE_25519,
						KeyExpiry: timestampPb.New(time.Now().AddDate(0, 0, 1)), PublicKey: "test",
						Nonce: "test", PrivateKey: "test"}, nil),
					mockVgAaClient.EXPECT().RequestData(gomock.Any(), gomock.Any()).Return(&aaPb.RequestDataResponse{
						Status: rpcPb.StatusOk(), SessionId: "sess"}, nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				vgAaClient:        tt.fields.vgAaClient,
				consentDao:        tt.fields.consentDao,
				attemptDao:        tt.fields.attemptDao,
				attemptProcessDao: tt.fields.attemptProcessDao,
				conf:              tt.fields.conf,
				edeService:        tt.fields.edeService,
				batchProcessDao:   tt.fields.batchProcessDao,
				fiFactory:         tt.fields.fiFactory,
				accDao:            tt.fields.accDao,
				txnDao:            tt.fields.txnDao,
				consentRequestDao: tt.fields.consentRequestDao,
				txnExecutor:       mockTxnExecutor,
				datetime:          mockTime,
			}
			got, err := p.CreateAttempt(tt.args.ctx, tt.args.consent, tt.args.initiatedBy, tt.args.purpose, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestProcessorService_FetchData(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCDao := mock_dao.NewMockConsentDao(ctr)
	mockVgAaClient := vgAaMock.NewMockAccountAggregatorClient(ctr)
	mockDfaDao := mock_dao.NewMockDataFetchAttemptDao(ctr)
	mockDpaDao := mock_dao.NewMockDataProcessAttemptDao(ctr)
	mockEde := mock_crypto.NewMockEde(ctr)
	mockBpDao := mock_dao.NewMockAaBatchProcessDao(ctr)
	mockFiFactory := mock_fi_factory.NewMockIFIFactory(ctr)
	mockAccDao := mock_dao.NewMockAaAccountDao(ctr)
	mockTxnDao := mock_dao.NewMockAaTransactionDao(ctr)
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctr)
	mockReleaseEval := mock_release.NewMockIEvaluator(ctr)
	type fields struct {
		vgAaClient        aaPb.AccountAggregatorClient
		consentDao        dao.ConsentDao
		attemptDao        dao.DataFetchAttemptDao
		attemptProcessDao dao.DataProcessAttemptDao
		conf              *genconf.Config
		edeService        crypto.Ede
		batchProcessDao   dao.AaBatchProcessDao
		fiFactory         IFIFactory
		accDao            dao.AaAccountDao
		txnDao            dao.AaTransactionDao
		consentRequestDao dao.ConsentRequestDao
	}
	type args struct {
		ctx         context.Context
		dataAttempt *caPb.DataFetchAttempt
		mocks       []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.FI
		wantErr bool
	}{
		{
			name: "error getting consent - attempt in terminal status",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error fetching consent from DB",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error fetching consent request from DB",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error fetching data from VG",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success in fetching data from VG",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusOk(),
						FiList: []*aaPb.FI{
							{
								FipId: "HDFC-FIP",
								Data: []*aaPb.Data{
									{
										LinkRefNumber: "link-ref-number-hdfc",
										EncryptedFi:   "encrypted-data",
									},
								},
								KeyMaterial: &aaPb.KeyMaterial{
									CryptoAlgo: aaPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
									Curve:      aaPb.CryptoCurve_CRYPTO_CURVE_25519,
									KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
									PublicKey:  "test",
									Nonce:      "test",
								},
							},
						},
					}, nil),
					mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "failure in fetching FIP data from VG",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusOk(),
						FiList: []*aaPb.FI{
							{
								FipId: "YESB-FIP",
								Data: []*aaPb.Data{
									{
										LinkRefNumber: "link-ref-number-yes",
										EncryptedFi:   "encrypted-data",
									},
								},
								KeyMaterial: &aaPb.KeyMaterial{
									CryptoAlgo: aaPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
									Curve:      aaPb.CryptoCurve_CRYPTO_CURVE_25519,
									KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
									PublicKey:  "test",
									Nonce:      "test",
								},
							},
						},
					}, nil),
					mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure in fetching all accounts accross all FIP data from VG",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusOk(),
						FiList: []*aaPb.FI{
							{
								FipId: "HDFC-FIP",
								Data:  []*aaPb.Data{},
								KeyMaterial: &aaPb.KeyMaterial{
									CryptoAlgo: aaPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
									Curve:      aaPb.CryptoCurve_CRYPTO_CURVE_25519,
									KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
									PublicKey:  "test",
									Nonce:      "test",
								},
							},
						},
					}, nil),
					mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure in fetching all accounts accross all FIP data from VG, unsorted linkref case",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent2, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusOk(),
						FiList: []*aaPb.FI{
							{
								FipId: "HDFC-FIP",
								Data: []*aaPb.Data{
									{
										LinkRefNumber: "BLinkRef",
										EncryptedFi:   "encrypted-data",
									}, {
										LinkRefNumber: "ALinkRef",
										EncryptedFi:   "encrypted-data",
									},
								},
								KeyMaterial: &aaPb.KeyMaterial{
									CryptoAlgo: aaPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
									Curve:      aaPb.CryptoCurve_CRYPTO_CURVE_25519,
									KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
									PublicKey:  "test",
									Nonce:      "test",
								},
							},
						},
					}, nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "failure in fetching all accounts accross all FIP data from VG, unsorted linkref case",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent2, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(nil, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure in fetching all accounts accross all FIP data from VG, unsorted linkref case",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent2, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(nil, errors.New("error fetching data")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure in fetching all accounts across all FIP data from VG after getting notification for all accounts with ready status",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal2,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent2, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusOk(),
						FiList: []*aaPb.FI{
							{
								FipId: "HDFC-FIP",
								Data: []*aaPb.Data{
									{
										LinkRefNumber: "ALinkRef",
										EncryptedFi:   "encrypted-data",
									},
								},
								KeyMaterial: &aaPb.KeyMaterial{
									CryptoAlgo: aaPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
									Curve:      aaPb.CryptoCurve_CRYPTO_CURVE_25519,
									KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
									PublicKey:  "test",
									Nonce:      "test",
								},
							},
						},
					}, nil),
					mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no error when fetching for not ready notification",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaNonTerminal3,
				mocks: []interface{}{
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent2, nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consentRequest1, nil),
					mockVgAaClient.EXPECT().FetchData(gomock.Any(), gomock.Any()).Return(&aaPb.FetchDataResponse{
						Status: rpcPb.StatusOk(),
						FiList: []*aaPb.FI{
							{
								FipId: "HDFC-FIP",
								Data: []*aaPb.Data{
									{
										LinkRefNumber: "BLinkRef",
										EncryptedFi:   "encrypted-data",
									},
									{
										LinkRefNumber: "ALinkRef",
										EncryptedFi:   "encrypted-data",
									},
								},
								KeyMaterial: &aaPb.KeyMaterial{
									CryptoAlgo: aaPb.CryptoAlgo_CRYPTO_ALGO_ECDH,
									Curve:      aaPb.CryptoCurve_CRYPTO_CURVE_25519,
									KeyExpiry:  timestampPb.New(time.Now().AddDate(0, 0, 1)),
									PublicKey:  "test",
									Nonce:      "test",
								},
							},
						},
					}, nil),
					mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				vgAaClient:        tt.fields.vgAaClient,
				consentDao:        tt.fields.consentDao,
				attemptDao:        tt.fields.attemptDao,
				attemptProcessDao: tt.fields.attemptProcessDao,
				conf:              tt.fields.conf,
				edeService:        tt.fields.edeService,
				batchProcessDao:   tt.fields.batchProcessDao,
				fiFactory:         tt.fields.fiFactory,
				accDao:            tt.fields.accDao,
				txnDao:            tt.fields.txnDao,
				consentRequestDao: tt.fields.consentRequestDao,
				txnExecutor:       mockTxnExecutor,
				releaseEvaluator:  mockReleaseEval,
			}
			got, err := p.FetchData(tt.args.ctx, tt.args.dataAttempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestProcessorService_GetFiDataRangeFrom(t *testing.T) {
	currentTime := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	salaryConsentReqId := "salary-consent-request-id"
	salaryEstimationConsentRequest := &caPb.ConsentRequest{
		Id:         salaryConsentReqId,
		CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
	}
	regularConsentReqId := "regular-consent-request-id"
	regularConsentRequest := &caPb.ConsentRequest{
		Id:         regularConsentReqId,
		CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_UNSPECIFIED,
	}
	salaryEstimationConsent := &caPb.Consent{
		ConsentRequestId: salaryConsentReqId,
		Accounts: &caPb.Accounts{
			AccountList: []*caPb.Account{
				{
					FipId: "HDFC-FIP",
				},
			},
		},
	}
	regularConsent := &caPb.Consent{
		ConsentRequestId: regularConsentReqId,
		Accounts: &caPb.Accounts{
			AccountList: []*caPb.Account{
				{
					FipId: "HDFC-FIP",
				},
			},
		},
	}
	type args struct {
		ctx         context.Context
		dfa         *caPb.DataFetchAttempt
		initiatedBy caEnumPb.DataFetchAttemptInitiatedBy
		consent     *caPb.Consent
	}
	type processorMocks struct {
		mockCrDao *mock_dao.MockConsentRequestDao
		mockTime  *mockDateTime.MockTime
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(p *processorMocks)
		want       *timestampPb.Timestamp
		wantErr    bool
	}{
		{
			name: "salary estimation flow - should return 6 months from now",
			args: args{
				ctx:         context.Background(),
				dfa:         nil, // No existing data fetch attempt
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				consent:     salaryEstimationConsent,
			},
			setupMocks: func(p *processorMocks) {
				p.mockCrDao.EXPECT().Get(gomock.Any(), salaryConsentReqId, caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_CA_FLOW_NAME).Return(salaryEstimationConsentRequest, nil)
				p.mockTime.EXPECT().Now().Return(currentTime).AnyTimes()
			},
			want:    timestampPb.New(currentTime.AddDate(0, -6, 0)), // 6 months ago
			wantErr: false,
		},
		{
			name: "regular flow - should return 7 days from now",
			args: args{
				ctx:         context.Background(),
				dfa:         nil, // No existing data fetch attempt
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				consent:     regularConsent,
			},
			setupMocks: func(p *processorMocks) {
				p.mockCrDao.EXPECT().Get(gomock.Any(), regularConsentReqId, caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_CA_FLOW_NAME).Return(regularConsentRequest, nil)
				p.mockTime.EXPECT().Now().Return(currentTime).AnyTimes()
			},
			want:    timestampPb.New(currentTime.AddDate(0, 0, -7)), // 7 days ago (FirstDataPullDurationInDays)
			wantErr: false,
		},
		{
			name: "existing data fetch attempt - should return DataRangeTo from existing attempt",
			args: args{
				ctx: context.Background(),
				dfa: &caPb.DataFetchAttempt{
					DataRangeTo: timestampPb.New(currentTime.AddDate(0, -2, 0)), // 2 months ago
				},
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				consent:     salaryEstimationConsent,
			},
			want:    timestampPb.New(currentTime.AddDate(0, -2, 0)), // Should return DataRangeTo from existing attempt
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockConsentReqDao := mock_dao.NewMockConsentRequestDao(ctr)
			mockTime := mockDateTime.NewMockTime(ctr)
			if tt.setupMocks != nil {
				tt.setupMocks(&processorMocks{
					mockCrDao: mockConsentReqDao,
					mockTime:  mockTime,
				})
			}
			p := &ProcessorService{
				consentRequestDao: mockConsentReqDao,
				conf:              dynconf,
				datetime:          mockTime,
			}
			got, err := p.getFiDataRangeFrom(tt.args.ctx, tt.args.dfa, tt.args.initiatedBy, tt.args.consent)
			if (err != nil) != tt.wantErr {
				t.Errorf("getFiDataRangeFrom() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getFiDataRangeFrom() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestProcessorService_GetDecryptedData(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCDao := mock_dao.NewMockConsentDao(ctr)
	mockVgAaClient := vgAaMock.NewMockAccountAggregatorClient(ctr)
	mockDfaDao := mock_dao.NewMockDataFetchAttemptDao(ctr)
	mockDpaDao := mock_dao.NewMockDataProcessAttemptDao(ctr)
	mockEde := mock_crypto.NewMockEde(ctr)
	mockBpDao := mock_dao.NewMockAaBatchProcessDao(ctr)
	mockFiFactory := mock_fi_factory.NewMockIFIFactory(ctr)
	mockAccDao := mock_dao.NewMockAaAccountDao(ctr)
	mockTxnDao := mock_dao.NewMockAaTransactionDao(ctr)
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctr)
	type fields struct {
		vgAaClient        aaPb.AccountAggregatorClient
		consentDao        dao.ConsentDao
		attemptDao        dao.DataFetchAttemptDao
		attemptProcessDao dao.DataProcessAttemptDao
		conf              *genconf.Config
		edeService        crypto.Ede
		batchProcessDao   dao.AaBatchProcessDao
		fiFactory         IFIFactory
		accDao            dao.AaAccountDao
		txnDao            dao.AaTransactionDao
		consentRequestDao dao.ConsentRequestDao
	}
	type args struct {
		ctx       context.Context
		attempt   *caPb.DataFetchAttempt
		inputData []*caPb.FI
		mocks     []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.FI
		wantErr bool
	}{
		{
			name: "data already decrypted",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:       context.Background(),
				attempt:   dfaDecrypted,
				inputData: encryptedData,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error in decrypting data",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:       context.Background(),
				attempt:   dfaFetched,
				inputData: encryptedData,
				mocks: []interface{}{
					mockEde.EXPECT().DecryptData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						"", errors.New("err"),
					),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success in decrypting data",
			fields: fields{
				vgAaClient:        mockVgAaClient,
				consentDao:        mockCDao,
				attemptDao:        mockDfaDao,
				attemptProcessDao: mockDpaDao,
				conf:              dynconf,
				edeService:        mockEde,
				batchProcessDao:   mockBpDao,
				fiFactory:         mockFiFactory,
				accDao:            mockAccDao,
				txnDao:            mockTxnDao,
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:       context.Background(),
				attempt:   dfaFetched,
				inputData: encryptedData,
				mocks: []interface{}{
					mockEde.EXPECT().DecryptData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("test", nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				vgAaClient:        tt.fields.vgAaClient,
				consentDao:        tt.fields.consentDao,
				attemptDao:        tt.fields.attemptDao,
				attemptProcessDao: tt.fields.attemptProcessDao,
				conf:              tt.fields.conf,
				edeService:        tt.fields.edeService,
				batchProcessDao:   tt.fields.batchProcessDao,
				fiFactory:         tt.fields.fiFactory,
				accDao:            tt.fields.accDao,
				txnDao:            tt.fields.txnDao,
				consentRequestDao: tt.fields.consentRequestDao,
				txnExecutor:       mockTxnExecutor,
			}
			got, err := p.GetDecryptedData(tt.args.ctx, tt.args.attempt, tt.args.inputData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDecryptedData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestProcessorService_ProcessFetchedData(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockApDao := mock_dao.NewMockDataProcessAttemptDao(ctrl)
	mockAttemptDao := mock_dao.NewMockDataFetchAttemptDao(ctrl)
	mockConsentDao := mock_dao.NewMockConsentDao(ctrl)
	mockProcessor := account_processor.NewMockProcessor(ctrl)

	type fields struct {
		consentDao         dao.ConsentDao
		attemptDao         dao.DataFetchAttemptDao
		attemptProcessDao  dao.DataProcessAttemptDao
		storeDecryptedData StoreDecryptedData
	}
	type args struct {
		ctx         context.Context
		dataAttempt *caPb.DataFetchAttempt
		data        []*caPb.FI
		mocks       []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "#1 error in fetching from db - update failed",
			fields: fields{
				attemptProcessDao: mockApDao,
				attemptDao:        mockAttemptDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("udpate failed")),
				},
			},
			wantErr: true,
		},
		{
			name: "#2 error in fetching from db - update success",
			fields: fields{
				attemptProcessDao: mockApDao,
				attemptDao:        mockAttemptDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: true,
		},
		{
			name: "#3 data process status success",
			fields: fields{
				attemptProcessDao: mockApDao,
				attemptDao:        mockAttemptDao,
				consentDao:        mockConsentDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS}, nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#4 data process status success - error determining first attempt for consent",
			fields: fields{
				attemptProcessDao: mockApDao,
				attemptDao:        mockAttemptDao,
				consentDao:        mockConsentDao,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS}, nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#5 store data successful",
			fields: fields{
				attemptProcessDao:  mockApDao,
				attemptDao:         mockAttemptDao,
				consentDao:         mockConsentDao,
				storeDecryptedData: mockProcessor.Store,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{
						Id:                "id-1",
						DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_UNSPECIFIED,
					}, nil),
					mockProcessor.EXPECT().Store(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error storing data")),
					mockApDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: true,
		},
		{
			name: "#6 store data successful - update unsuccessful",
			fields: fields{
				attemptProcessDao:  mockApDao,
				attemptDao:         mockAttemptDao,
				consentDao:         mockConsentDao,
				storeDecryptedData: mockProcessor.Store,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{
						Id:                "id-1",
						DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_UNSPECIFIED,
					}, nil),
					mockProcessor.EXPECT().Store(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error storing data")),
					mockApDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error updating")),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: true,
		},
		{
			name: "#7 empty batched txns",
			fields: fields{
				attemptProcessDao:  mockApDao,
				attemptDao:         mockAttemptDao,
				consentDao:         mockConsentDao,
				storeDecryptedData: mockProcessor.Store,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{
						Id:                "id-1",
						DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_UNSPECIFIED,
					}, nil),
					mockProcessor.EXPECT().Store(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(caError.ErrEmptyBatchedTxns),
					mockApDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#8 empty batched txns - error updating entities",
			fields: fields{
				attemptProcessDao:  mockApDao,
				attemptDao:         mockAttemptDao,
				consentDao:         mockConsentDao,
				storeDecryptedData: mockProcessor.Store,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{
						Id:                "id-1",
						DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_UNSPECIFIED,
					}, nil),
					mockProcessor.EXPECT().Store(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(caError.ErrEmptyBatchedTxns),
					mockApDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New("error fetching consent")),
				},
			},
			wantErr: true,
		},
		{
			name: "#9 empty batched txns - update error",
			fields: fields{
				attemptProcessDao:  mockApDao,
				attemptDao:         mockAttemptDao,
				consentDao:         mockConsentDao,
				storeDecryptedData: mockProcessor.Store,
			},
			args: args{
				ctx:         context.Background(),
				dataAttempt: dfaTerminal,
				data:        encryptedData,
				mocks: []interface{}{
					mockApDao.EXPECT().CreateOrGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.DataProcessAttempt{
						Id:                "id-1",
						DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_UNSPECIFIED,
					}, nil),
					mockProcessor.EXPECT().Store(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(caError.ErrEmptyBatchedTxns),
					mockApDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("update error")),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				consentDao:         tt.fields.consentDao,
				attemptDao:         tt.fields.attemptDao,
				attemptProcessDao:  tt.fields.attemptProcessDao,
				conf:               dynconf,
				storeDecryptedData: tt.fields.storeDecryptedData,
				txnExecutor:        mockTxnExecutor,
			}
			err := p.ProcessFetchedData(tt.args.ctx, tt.args.dataAttempt, tt.args.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessFetchedData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessorService_UpdateNextFetchAt(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAttemptDao := mock_dao.NewMockDataFetchAttemptDao(ctrl)
	mockConsentDao := mock_dao.NewMockConsentDao(ctrl)
	type fields struct {
		consentDao dao.ConsentDao
		attemptDao dao.DataFetchAttemptDao
	}
	type args struct {
		ctx       context.Context
		attemptId string
		mocks     []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "#1 failed to get attempt",
			fields: fields{
				attemptDao: mockAttemptDao,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "attempt-id-1",
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(nil, errors.New("error getting attempt")),
				},
			},
			wantErr: true,
		},
		{
			name: "#2 data fetch status not completed",
			fields: fields{
				attemptDao: mockAttemptDao,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "attempt-id-1",
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaNonTerminal, nil),
				},
			},
			wantErr: true,
		},
		{
			name: "#3 error getting consent",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "attempt-id-1",
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaTerminal, nil),
					mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), gomock.Any()).Return(nil, errors.New("error getting consent")),
				},
			},
			wantErr: true,
		},
		{
			name: "#3 error updating consent",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "attempt-id-1",
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaTerminal, nil),
					mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error updating consent")),
				},
			},
			wantErr: true,
		},
		{
			name: "#4 successful",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "attempt-id-1",
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaTerminal, nil),
					mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				consentDao:  tt.fields.consentDao,
				attemptDao:  tt.fields.attemptDao,
				conf:        dynconf,
				txnExecutor: mockTxnExecutor,
			}
			err := p.UpdateNextFetchAt(tt.args.ctx, tt.args.attemptId)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateNextFetchAt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessorService_notifyUserOnAttemptCompletion(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAttemptDao := mock_dao.NewMockDataFetchAttemptDao(ctrl)
	mockConsentDao := mock_dao.NewMockConsentDao(ctrl)
	mockNotifier := mock_notification.NewMockSender(ctrl)
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctrl)

	type fields struct {
		consentDao dao.ConsentDao
		attemptDao dao.DataFetchAttemptDao
		notifier   notification.Sender
		crDao      dao.ConsentRequestDao
	}
	type args struct {
		ctx     context.Context
		attempt *caPb.DataFetchAttempt
		mocks   []interface{}
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "#1 error in attempt dao",
			fields: fields{
				attemptDao: mockAttemptDao,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetched,
				mocks:   []interface{}{},
			},
		},
		{
			name: "#2 not first attempt",
			fields: fields{
				attemptDao: mockAttemptDao,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetched,
				mocks:   []interface{}{},
			},
		},
		{
			name: "#3 error getting attempt by id",
			fields: fields{
				attemptDao: mockAttemptDao,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetchedInitiatedByUser,
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in db")),
				},
			},
		},
		{
			name: "#4 error getting consent",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetchedInitiatedByUser,
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaFetched, nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
		},
		{
			name: "#5 error sending notification",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
				notifier:   mockNotifier,
				crDao:      mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetchedInitiatedByUser,
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaFetched, nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE}, nil),
					mockNotifier.EXPECT().Send(gomock.Any(), gomock.Any()).Return(errors.New("error sending notification")),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.ConsentRequest{
							Id:         "consent-req-id-1",
							CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
						}, nil,
					),
				},
			},
		},
		{
			name: "#6 successfully sent notifications",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
				notifier:   mockNotifier,
				crDao:      mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetchedInitiatedByUser,
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaFetched, nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE}, nil),
					mockNotifier.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil),
					mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.ConsentRequest{
							Id:         "consent-req-id-1",
							CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
						}, nil,
					),
				},
			},
		},
		{
			name: "#7 not sending notification - consent not active",
			fields: fields{
				attemptDao: mockAttemptDao,
				consentDao: mockConsentDao,
				notifier:   mockNotifier,
			},
			args: args{
				ctx:     context.Background(),
				attempt: dfaFetchedInitiatedByUser,
				mocks: []interface{}{
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaFetched, nil),
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_DELETED_EPIFI}, nil),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				consentDao:        tt.fields.consentDao,
				attemptDao:        tt.fields.attemptDao,
				conf:              dynconf,
				notifier:          tt.fields.notifier,
				txnExecutor:       mockTxnExecutor,
				consentRequestDao: tt.fields.crDao,
			}
			p.notifyUserOnAttemptCompletion(tt.args.ctx, tt.args.attempt)
		})
	}
}

func TestProcessorService_updateBatchAttemptAndNextFetch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockBpDao := mock_dao.NewMockAaBatchProcessDao(ctrl)
	mockAttemptDao := mock_dao.NewMockDataFetchAttemptDao(ctrl)
	mockConsentDao := mock_dao.NewMockConsentDao(ctrl)
	type fields struct {
		consentDao      dao.ConsentDao
		attemptDao      dao.DataFetchAttemptDao
		batchProcessDao dao.AaBatchProcessDao
	}
	type args struct {
		ctx   context.Context
		batch *caPb.BatchProcessTransaction
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "#1 error updating bp dao",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx:   context.Background(),
				batch: &caPb.BatchProcessTransaction{Id: "id-1"},
				mocks: []interface{}{
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#2 error getting from bp dao",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Id:               "id-1",
					FetchAttemptId:   "fetch-attempt-id-1",
					ProcessAttemptId: "process-attempt-id-1",
					BatchNumber:      1,
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#4 error updating attempt",
			fields: fields{
				batchProcessDao: mockBpDao,
				attemptDao:      mockAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Id:               "id-1",
					FetchAttemptId:   "fetch-attempt-id-1",
					ProcessAttemptId: "process-attempt-id-1",
					BatchNumber:      1,
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error updating")),
				},
			},
			wantErr: true,
		},
		{
			name: "#5 error updating next fetch at",
			fields: fields{
				batchProcessDao: mockBpDao,
				attemptDao:      mockAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Id:               "id-1",
					FetchAttemptId:   "fetch-attempt-id-1",
					ProcessAttemptId: "process-attempt-id-1",
					BatchNumber:      1,
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#6 successful",
			fields: fields{
				batchProcessDao: mockBpDao,
				attemptDao:      mockAttemptDao,
				consentDao:      mockConsentDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Id:               "id-1",
					FetchAttemptId:   "fetch-attempt-id-1",
					ProcessAttemptId: "process-attempt-id-1",
					BatchNumber:      1,
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockAttemptDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockAttemptDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(dfaTerminal, nil),
					mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(consent1, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				consentDao:      tt.fields.consentDao,
				attemptDao:      tt.fields.attemptDao,
				conf:            dynconf,
				batchProcessDao: tt.fields.batchProcessDao,
				txnExecutor:     mockTxnExecutor,
			}
			err := p.updateBatchAttemptAndNextFetch(tt.args.ctx, tt.args.batch)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateNextFetchAt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessorService_isBatchEligibleForProcessing(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockBpDao := mock_dao.NewMockAaBatchProcessDao(ctrl)
	type fields struct {
		batchProcessDao dao.AaBatchProcessDao
	}
	type args struct {
		ctx      context.Context
		curBatch *caPb.BatchProcessTransaction
		mocks    []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:   "#1",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				curBatch: &caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "#2",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx:      context.Background(),
				curBatch: &caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#3",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx:      context.Background(),
				curBatch: &caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			wantErr: false,
		},
		{
			name: "#4",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx:      context.Background(),
				curBatch: &caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS}, nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#5",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx:      context.Background(),
				curBatch: &caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				conf:            dynconf,
				batchProcessDao: tt.fields.batchProcessDao,
				txnExecutor:     mockTxnExecutor,
			}
			got, err := p.isBatchEligibleForProcessing(tt.args.ctx, tt.args.curBatch)
			if (err != nil) != tt.wantErr {
				t.Errorf("isBatchEligibleForProcessing() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestProcessorService_GetAccountDetails(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockAccDao := mock_dao.NewMockAaAccountDao(ctrl)
	mockFIFactory := mock_fi_factory.NewMockIFIFactory(ctrl)
	mockFIDepProcessor := mock_fi_types.NewMockFIDepositProcessor(ctrl)
	type fields struct {
		fiFactory IFIFactory
		accDao    dao.AaAccountDao
	}
	type args struct {
		ctx            context.Context
		accountId      string
		accDetailsMask []caExtPb.AccountDetailsMask
		mocks          []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caExtPb.AccountDetails
		want1   *caExtPb.ProfileDetails
		want2   interface{}
		wantErr bool
	}{
		{
			name: "#1 success",
			fields: fields{
				accDao:    mockAccDao,
				fiFactory: mockFIFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountId:      "acc-id-1",
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_UNSPECIFIED},
				mocks: []interface{}{
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{Id: "id-1"}, nil),
					mockFIFactory.EXPECT().GetSummaryDetailsImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().GetSummaryDetails(gomock.Any(), gomock.Any()).Return(&caPb.AaDepositAccount{Id: "id-1"}, nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#2 error getting account",
			fields: fields{
				accDao:    mockAccDao,
				fiFactory: mockFIFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountId:      "acc-id-1",
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_UNSPECIFIED},
				mocks: []interface{}{
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#3 error getting summary details implementation",
			fields: fields{
				accDao:    mockAccDao,
				fiFactory: mockFIFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountId:      "acc-id-1",
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE},
				mocks: []interface{}{
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{Id: "id-1", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT}, nil),
					mockFIFactory.EXPECT().GetSummaryDetailsImpl(gomock.Any()).Return(nil, errors.New("error getting summary details")),
					mockFIFactory.EXPECT().GetProfileDetailsImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().GetProfileDetails(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			wantErr: true,
		},
		{
			name: "#4 error getting summary details",
			fields: fields{
				accDao:    mockAccDao,
				fiFactory: mockFIFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountId:      "acc-id-1",
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE},
				mocks: []interface{}{
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{Id: "id-1"}, nil),
					mockFIFactory.EXPECT().GetSummaryDetailsImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().GetSummaryDetails(gomock.Any(), gomock.Any()).Return(nil, errors.New("error getting summary details")),
					mockFIFactory.EXPECT().GetProfileDetailsImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().GetProfileDetails(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			wantErr: true,
		},
		{
			name: "#5 not found summary details",
			fields: fields{
				accDao:    mockAccDao,
				fiFactory: mockFIFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountId:      "acc-id-1",
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE},
				mocks: []interface{}{
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{Id: "id-1"}, nil),
					mockFIFactory.EXPECT().GetSummaryDetailsImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().GetSummaryDetails(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockFIFactory.EXPECT().GetProfileDetailsImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().GetProfileDetails(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				conf:        dynconf,
				fiFactory:   tt.fields.fiFactory,
				accDao:      tt.fields.accDao,
				txnExecutor: mockTxnExecutor,
			}
			_, _, _, err := p.GetAccountDetails(tt.args.ctx, tt.args.accountId, tt.args.accDetailsMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessorService_StoreBatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockBpDao := mock_dao.NewMockAaBatchProcessDao(ctrl)
	mockFIFactory := mock_fi_factory.NewMockIFIFactory(ctrl)
	mockFIDepProcessor := mock_fi_types.NewMockFIDepositProcessor(ctrl)
	mockAccDao := mock_dao.NewMockAaAccountDao(ctrl)

	type fields struct {
		batchProcessDao dao.AaBatchProcessDao
		fiFactory       IFIFactory
		accDao          dao.AaAccountDao
	}
	type args struct {
		ctx   context.Context
		batch *caPb.BatchProcessTransaction
		req   *caCoPb.ProcessTransactionsBatchRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "#1 process success",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
			},
			wantErr: true,
		},
		{
			name: "#2 error in batch process dao",
			fields: fields{
				batchProcessDao: mockBpDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#3 error in getting implementation",
			fields: fields{
				batchProcessDao: mockBpDao,
				fiFactory:       mockFIFactory,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, nil),
					mockFIFactory.EXPECT().GetStoreTxnBatchImpl(gomock.Any()).Return(nil, errors.New("error getting implementation")),
				},
			},
			wantErr: true,
		},
		{
			name: "#4 error getting account by id",
			fields: fields{
				batchProcessDao: mockBpDao,
				fiFactory:       mockFIFactory,
				accDao:          mockAccDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, nil),
					mockFIFactory.EXPECT().GetStoreTxnBatchImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#5 error storing txn batch",
			fields: fields{
				batchProcessDao: mockBpDao,
				fiFactory:       mockFIFactory,
				accDao:          mockAccDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, nil),
					mockFIFactory.EXPECT().GetStoreTxnBatchImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(acc1, nil),
					mockFIDepProcessor.EXPECT().StoreTxnBatch(gomock.Any(), gomock.Any(), gomock.Any(),
						caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS).Return(errors.New("error storing txn batch")),
				},
			},
			wantErr: true,
		},
		{
			name: "#6 error updating batch by id",
			fields: fields{
				batchProcessDao: mockBpDao,
				fiFactory:       mockFIFactory,
				accDao:          mockAccDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, nil),
					mockFIFactory.EXPECT().GetStoreTxnBatchImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(acc1, nil),
					mockFIDepProcessor.EXPECT().StoreTxnBatch(gomock.Any(), gomock.Any(), gomock.Any(),
						caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS).Return(nil),
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error updating batch by id")),
				},
			},
			wantErr: true,
		},
		{
			name: "#7 success",
			fields: fields{
				batchProcessDao: mockBpDao,
				fiFactory:       mockFIFactory,
				accDao:          mockAccDao,
			},
			args: args{
				ctx: context.Background(),
				batch: &caPb.BatchProcessTransaction{
					Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_IN_PROGRESS,
				},
				req: &caCoPb.ProcessTransactionsBatchRequest{
					BatchId: "batch-id-1",
				},
				mocks: []interface{}{
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.BatchProcessTransaction{Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, nil),
					mockFIFactory.EXPECT().GetStoreTxnBatchImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockAccDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(acc1, nil),
					mockFIDepProcessor.EXPECT().StoreTxnBatch(gomock.Any(), gomock.Any(), gomock.Any(),
						caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS).Return(nil),
					mockBpDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockBpDao.EXPECT().GetByFetchAttemptProcessAttemptBatchNum(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				conf:            dynconf,
				batchProcessDao: tt.fields.batchProcessDao,
				fiFactory:       tt.fields.fiFactory,
				accDao:          tt.fields.accDao,
				txnExecutor:     mockTxnExecutor,
			}
			err := p.StoreBatch(tt.args.ctx, tt.args.batch, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("StoreBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessorService_Store(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockFiFactory := mock_fi_factory.NewMockIFIFactory(ctrl)
	mockFIDepProcessor := mock_fi_types.NewMockFIDepositProcessor(ctrl)

	type fields struct {
		vgAaClient         aaPb.AccountAggregatorClient
		consentDao         dao.ConsentDao
		attemptDao         dao.DataFetchAttemptDao
		attemptProcessDao  dao.DataProcessAttemptDao
		conf               *genconf.Config
		edeService         crypto.Ede
		batchProcessDao    dao.AaBatchProcessDao
		fiFactory          IFIFactory
		accDao             dao.AaAccountDao
		txnDao             dao.AaTransactionDao
		consentRequestDao  dao.ConsentRequestDao
		notifier           notification.Sender
		storeDecryptedData StoreDecryptedData
	}
	type args struct {
		ctx           context.Context
		dpa           *caPb.DataProcessAttempt
		decryptedData string
		dataAttempt   *caPb.DataFetchAttempt
		mocks         []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "#1 successful",
			fields: fields{
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:           context.Background(),
				dpa:           &caPb.DataProcessAttempt{Id: "id-1"},
				decryptedData: encryptedData[0].GetData()[0].GetDecryptedFi(),
				dataAttempt:   dfaNonTerminal,
				mocks: []interface{}{
					mockFiFactory.EXPECT().GetStoreAccountImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().StoreAccount(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#2 successful, fields in account entity have special characters",
			fields: fields{
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:           context.Background(),
				dpa:           &caPb.DataProcessAttempt{Id: "id-2"},
				decryptedData: b64EncodedAccData,
				dataAttempt:   dfaNonTerminal,
				mocks: []interface{}{
					mockFiFactory.EXPECT().GetStoreAccountImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().StoreAccount(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			wantErr: false,
		},
		{
			name: "#2 error decoding string",
			fields: fields{
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:         context.Background(),
				dpa:         &caPb.DataProcessAttempt{Id: "id-1"},
				dataAttempt: dfaNonTerminal,
				mocks:       []interface{}{},
			},
			wantErr: true,
		},
		{
			name: "#3 error getting store account impl",
			fields: fields{
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:           context.Background(),
				dpa:           &caPb.DataProcessAttempt{Id: "id-1"},
				dataAttempt:   dfaNonTerminal,
				decryptedData: encryptedData[0].GetData()[0].GetDecryptedFi(),
				mocks: []interface{}{
					mockFiFactory.EXPECT().GetStoreAccountImpl(gomock.Any()).Return(nil, errors.New("error getting impl")),
				},
			},
			wantErr: true,
		},
		{
			name: "#4 error storing account",
			fields: fields{
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:           context.Background(),
				dpa:           &caPb.DataProcessAttempt{Id: "id-1"},
				dataAttempt:   dfaNonTerminal,
				decryptedData: encryptedData[0].GetData()[0].GetDecryptedFi(),
				mocks: []interface{}{
					mockFiFactory.EXPECT().GetStoreAccountImpl(gomock.Any()).Return(mockFIDepProcessor, nil),
					mockFIDepProcessor.EXPECT().StoreAccount(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error storing account")),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				vgAaClient:         tt.fields.vgAaClient,
				consentDao:         tt.fields.consentDao,
				attemptDao:         tt.fields.attemptDao,
				attemptProcessDao:  tt.fields.attemptProcessDao,
				conf:               tt.fields.conf,
				edeService:         tt.fields.edeService,
				batchProcessDao:    tt.fields.batchProcessDao,
				fiFactory:          tt.fields.fiFactory,
				accDao:             tt.fields.accDao,
				txnDao:             tt.fields.txnDao,
				consentRequestDao:  tt.fields.consentRequestDao,
				notifier:           tt.fields.notifier,
				storeDecryptedData: tt.fields.storeDecryptedData,
				txnExecutor:        mockTxnExecutor,
			}
			err := p.Store(tt.args.ctx, tt.args.dpa, tt.args.decryptedData, tt.args.dataAttempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Store() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessorService_GetAccountDetailsBulk(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAccountDao := mock_dao.NewMockAaAccountDao(ctrl)
	mockFiFactory := mock_fi_factory.NewMockIFIFactory(ctrl)
	mockDepositType := mock_fi_types.NewMockFITermDepositProcessor(ctrl)
	mockRDepositType := mock_fi_types.NewMockFIRecurringDepositProcessor(ctrl)
	mockTDepositType := mock_fi_types.NewMockFITermDepositProcessor(ctrl)

	type fields struct {
		fiFactory IFIFactory
		accDao    dao.AaAccountDao
	}
	type args struct {
		ctx            context.Context
		accountIdList  []string
		accDetailsMask []caExtPb.AccountDetailsMask
		mocks          []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]*caPb.AccountProfileSummaryDetails
		wantErr bool
	}{
		{
			name: "#1 error in account dao",
			fields: fields{
				accDao: mockAccountDao,
			},
			args: args{
				ctx:            context.Background(),
				accountIdList:  []string{"acc-id-1"},
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
				mocks: []interface{}{
					mockAccountDao.EXPECT().GetBulkById(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in account dao")),
				},
			},
			wantErr: true,
		},
		{
			name: "#2 error in getting summary details implementation",
			fields: fields{
				accDao:    mockAccountDao,
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountIdList:  []string{"acc-id-d-1", "acc-id-rd-1", "acc-id-td-1"},
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
				mocks: []interface{}{
					mockAccountDao.EXPECT().GetBulkById(gomock.Any(), gomock.Any()).Return([]*caPb.AaAccount{
						{Id: "acc-id-d-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						{Id: "acc-id-rd-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT},
						{Id: "acc-id-td-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT},
					}, nil),
					mockFiFactory.EXPECT().GetSummaryDetailsImpl(gomock.Any()).Return(nil, errors.New("factory error")).Times(3),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(gomock.Any()).Return(nil, errors.New("factory error while getting processor for fetching profile details")).Times(3),
				},
			},
			wantErr: false,
		},
		{
			name: "#3 error in getting summary details implementation for td, error getting bulk summary for rd",
			fields: fields{
				accDao:    mockAccountDao,
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountIdList:  []string{"acc-id-d-1", "acc-id-rd-1", "acc-id-td-1"},
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
				mocks: []interface{}{
					mockAccountDao.EXPECT().GetBulkById(gomock.Any(), gomock.Any()).Return([]*caPb.AaAccount{
						{Id: "acc-id-d-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						{Id: "acc-id-rd-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT},
						{Id: "acc-id-td-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT},
					}, nil),
					mockFiFactory.EXPECT().GetSummaryDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT).Return(mockDepositType, nil),
					mockFiFactory.EXPECT().GetSummaryDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT).Return(mockRDepositType, nil),
					mockFiFactory.EXPECT().GetSummaryDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT).Return(nil, errors.New("error getting td implementation")),
					mockDepositType.EXPECT().GetProfileDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockRDepositType.EXPECT().GetProfileDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockTDepositType.EXPECT().GetProfileDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT).Return(mockDepositType, nil),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT).Return(mockRDepositType, nil),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT).Return(mockTDepositType, nil),
					mockDepositType.EXPECT().GetSummaryDetailsBulk(gomock.Any(), gomock.Any()).Return(map[string]interface{}{
						"acc-id-d-1": &caExtPb.DepositSummary{
							AccountId: "acc-id-d-1",
							IfscCode:  "dep-ifsc-code",
						},
					}, nil).Times(1),
					mockRDepositType.EXPECT().GetSummaryDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, errors.New("error getting summary")).Times(1),
				},
			},
			wantErr: false,
		},
		{
			name: "#4 successful",
			fields: fields{
				accDao:    mockAccountDao,
				fiFactory: mockFiFactory,
			},
			args: args{
				ctx:            context.Background(),
				accountIdList:  []string{"acc-id-d-1", "acc-id-rd-1", "acc-id-td-1"},
				accDetailsMask: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
				mocks: []interface{}{
					mockAccountDao.EXPECT().GetBulkById(gomock.Any(), gomock.Any()).Return([]*caPb.AaAccount{
						{Id: "acc-id-d-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						{Id: "acc-id-rd-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT},
						{Id: "acc-id-td-1", FipId: "HDFC-FIP", AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT},
					}, nil),

					mockFiFactory.EXPECT().GetSummaryDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT).Return(mockDepositType, nil),
					mockFiFactory.EXPECT().GetSummaryDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT).Return(mockRDepositType, nil),
					mockFiFactory.EXPECT().GetSummaryDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT).Return(mockTDepositType, nil),
					mockDepositType.EXPECT().GetProfileDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockRDepositType.EXPECT().GetProfileDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockTDepositType.EXPECT().GetProfileDetailsBulk(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT).Return(mockDepositType, nil),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT).Return(mockRDepositType, nil),
					mockFiFactory.EXPECT().GetProfileDetailsImpl(caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT).Return(mockTDepositType, nil),
					mockDepositType.EXPECT().GetSummaryDetailsBulk(gomock.Any(), gomock.Any()).Return(map[string]interface{}{
						"acc-id-d-1": &caExtPb.DepositSummary{
							AccountId: "acc-id-d-1",
							IfscCode:  "dep-ifsc-code",
						},
					}, nil).Times(1),
					mockRDepositType.EXPECT().GetSummaryDetailsBulk(gomock.Any(), gomock.Any()).Return(map[string]interface{}{
						"acc-id-rd-1": &caExtPb.RecurringDepositSummary{
							AccountId: "acc-id-rd-1",
							IfscCode:  "rdep-ifsc-code",
						},
					}, nil).Times(1),
					mockTDepositType.EXPECT().GetSummaryDetailsBulk(gomock.Any(), gomock.Any()).Return(map[string]interface{}{
						"acc-id-td-1": &caExtPb.TermDepositSummary{
							AccountId: "acc-id-td-1",
							IfscCode:  "tdep-ifsc-code",
						},
					}, nil).Times(1),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProcessorService{
				conf:        dynconf,
				fiFactory:   tt.fields.fiFactory,
				accDao:      tt.fields.accDao,
				txnExecutor: mockTxnExecutor,
			}
			_, err := p.GetAccountDetailsBulk(tt.args.ctx, tt.args.accountIdList, tt.args.accDetailsMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountDetailsBulk(%v, %v, %v)", tt.args.ctx, tt.args.accountIdList, tt.args.accDetailsMask)
			}
		})
	}
}

func TestProcessorService_getFiDataRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Set up mocks
	mockAttemptDao := mock_dao.NewMockDataFetchAttemptDao(ctrl)
	mockTime := mockDateTime.NewMockTime(ctrl)
	mockConsentReqDao := mock_dao.NewMockConsentRequestDao(ctrl)

	// Fixed test time
	now := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)

	// Test data
	testConsent := &caPb.Consent{
		Id:            "consent-id-1",
		DataRangeFrom: timestampPb.New(now.AddDate(-1, 0, 0)), // 1 Year ago
	}

	testConsentWithBounds := &caPb.Consent{
		Id:            "test-consent-bounds",
		DataRangeFrom: timestampPb.New(now.AddDate(0, -1, 0)), // 1 month ago
	}
	regularConsentRequest := &caPb.ConsentRequest{
		Id:         "regular-consent-request-id",
		CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_UNSPECIFIED,
	}

	type fields struct {
		attemptDao        dao.DataFetchAttemptDao
		conf              *genconf.Config
		datetime          datetime.Time
		consentRequestDao dao.ConsentRequestDao
	}
	type args struct {
		ctx                    context.Context
		consent                *caPb.Consent
		initiatedBy            caEnumPb.DataFetchAttemptInitiatedBy
		fetchDataForCustomUser bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mocks   func()
		want    *aaPb.FIDataRange
		wantErr bool
	}{
		{
			name: "error getting latest success attempt",
			fields: fields{
				attemptDao:        mockAttemptDao,
				conf:              dynconf,
				datetime:          mockTime,
				consentRequestDao: mockConsentReqDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     testConsent,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
			},
			mocks: func() {
				mockTime.EXPECT().Now().Return(now).AnyTimes()
				mockAttemptDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(
					gomock.Any(),
					testConsent.GetId(),
					caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
					[]caEnumPb.DataFetchAttemptPurpose{
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
					},
				).Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success - first data pull by user",
			fields: fields{
				attemptDao:        mockAttemptDao,
				conf:              dynconf,
				datetime:          mockTime,
				consentRequestDao: mockConsentReqDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     testConsent,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
			},
			mocks: func() {
				mockTime.EXPECT().Now().Return(now).AnyTimes()
				mockAttemptDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(
					gomock.Any(),
					testConsent.GetId(),
					caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
					[]caEnumPb.DataFetchAttemptPurpose{
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
					},
				).Return(nil, epifierrors.ErrRecordNotFound)
				mockConsentReqDao.EXPECT().Get(gomock.Any(), gomock.Any(), caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_CA_FLOW_NAME).Return(regularConsentRequest, nil)
			},
			want: &aaPb.FIDataRange{
				From: timestampPb.New(now.AddDate(0, 0, -dynconf.FirstDataPullDurationInDays())),
				To:   timestampPb.New(now),
			},
			wantErr: false,
		},
		{
			name: "success - subsequent data pull with previous success attempt",
			fields: fields{
				attemptDao:        mockAttemptDao,
				conf:              dynconf,
				datetime:          mockTime,
				consentRequestDao: mockConsentReqDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     testConsent,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
			},
			mocks: func() {
				mockTime.EXPECT().Now().Return(now).AnyTimes()
				mockAttemptDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(
					gomock.Any(),
					testConsent.GetId(),
					caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
					[]caEnumPb.DataFetchAttemptPurpose{
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
					},
				).Return(dfaFetchedWithCurrentTime(now), nil)
			},
			want: &aaPb.FIDataRange{
				From: timestampPb.New(dfaFetchedWithCurrentTime(now).GetDataRangeTo().AsTime().Add(-dynconf.FiDataRangeOverlapInterval())),
				To:   timestampPb.New(now),
			},
			wantErr: false,
		},
		{
			name: "success - subsequent data pull with previous success attempt but toData is more than 1 year",
			fields: fields{
				attemptDao:        mockAttemptDao,
				conf:              dynconf,
				datetime:          mockTime,
				consentRequestDao: mockConsentReqDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     testConsent,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
			},
			mocks: func() {
				mockTime.EXPECT().Now().Return(now).AnyTimes()
				mockAttemptDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(
					gomock.Any(),
					testConsent.GetId(),
					caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
					[]caEnumPb.DataFetchAttemptPurpose{
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
					},
				).Return(dfaFetched2WithCurrentTime(now), nil)
			},
			want: &aaPb.FIDataRange{
				From: timestampPb.New(now.AddDate(dynconf.Consent().DataRangeStartYears(), 0, 0)),
				To:   timestampPb.New(now),
			},
			wantErr: false,
		},
		{
			name: "success - custom fetch with overlap interval",
			fields: fields{
				attemptDao:        mockAttemptDao,
				conf:              dynconf,
				datetime:          mockTime,
				consentRequestDao: mockConsentReqDao,
			},
			args: args{
				ctx:                    context.Background(),
				consent:                testConsent,
				initiatedBy:            caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
				fetchDataForCustomUser: true,
			},
			mocks: func() {
				mockTime.EXPECT().Now().Return(now).AnyTimes()
				mockAttemptDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(
					gomock.Any(),
					testConsent.GetId(),
					caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
					[]caEnumPb.DataFetchAttemptPurpose{
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
					},
				).Return(dfaFetched, nil)
			},
			want: &aaPb.FIDataRange{
				From: timestampPb.New(dfaFetched.GetDataRangeTo().AsTime().Add(-dynconf.FiDataRangeOverlapIntervalForCustomfetch())),
				To:   timestampPb.New(now),
			},
			wantErr: false,
		},
		{
			name: "success - data range respects consent bounds",
			fields: fields{
				attemptDao:        mockAttemptDao,
				conf:              dynconf,
				datetime:          mockTime,
				consentRequestDao: mockConsentReqDao,
			},
			args: args{
				ctx:         context.Background(),
				consent:     testConsentWithBounds,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
			},
			mocks: func() {
				mockTime.EXPECT().Now().Return(now).AnyTimes()
				mockAttemptDao.EXPECT().GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(
					gomock.Any(),
					testConsentWithBounds.GetId(),
					caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
					[]caEnumPb.DataFetchAttemptPurpose{
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
						caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
					},
				).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &aaPb.FIDataRange{
				From: testConsentWithBounds.GetDataRangeFrom(),
				To:   timestampPb.New(now),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mocks != nil {
				tt.mocks()
			}

			p := &ProcessorService{
				attemptDao:        tt.fields.attemptDao,
				conf:              tt.fields.conf,
				datetime:          tt.fields.datetime,
				consentRequestDao: tt.fields.consentRequestDao,
			}

			got, err := p.getFiDataRange(tt.args.ctx, tt.args.consent, tt.args.initiatedBy, tt.args.fetchDataForCustomUser)
			if (err != nil) != tt.wantErr {
				t.Errorf("getFiDataRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getFiDataRange() got = %v, want %v", got, tt.want)
			}
		})
	}
}
