package processor

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/proto/json"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/employment/developer"
	"github.com/epifi/gamma/employment/dao"
)

const (
	PiId       = "pi_id"
	EmployerId = "employer_id"
)

type DevEmployerPiMapping struct {
	employerPiMappingDao dao.EmployerPiMappingDao
}

func NewDevEmployerPiMapping(employerPiMappingDao dao.EmployerPiMappingDao) *DevEmployerPiMapping {
	return &DevEmployerPiMapping{
		employerPiMappingDao: employerPiMappingDao,
	}
}

func (d *DevEmployerPiMapping) FetchParamList(ctx context.Context, entity developer.EmploymentEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            PiId,
			Label:           "PI ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            EmployerId,
			Label:           "Employer ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevEmployerPiMapping) FetchData(ctx context.Context, entity developer.EmploymentEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil to fetch employer pi mapping")
	}
	var (
		piId       string
		employerId string
		data       []byte
		err        error
		marshalErr error
		mapping    *employmentPb.EmployerPiMapping
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case PiId:
			piId = filter.GetStringValue()
		case EmployerId:
			employerId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unsupported filter %s", filter.GetParameterName())
		}
	}
	// nolint:dupl
	switch {
	case piId != "":
		mapping, err = d.employerPiMappingDao.GetByPiId(ctx, piId)
		if err != nil {
			logger.Error(ctx, "failed to get employer pi mapping by pi id", zap.Error(err))
			return "", err
		}
		data, marshalErr = json.Marshal(mapping)
	case employerId != "":
		mappings, err := d.employerPiMappingDao.GetByEmployerId(ctx, employerId)
		if err != nil {
			logger.Error(ctx, "failed to get employer pi mappings by employer id", zap.Error(err))
			return "", err
		}
		data, marshalErr = json.Marshal(mappings)
	default:
		logger.Error(ctx, "invalid parameters passed for fetching employer pi mapping", zap.Error(err))
		return "", fmt.Errorf("invalid parameters passed for fetching employer pi mapping: %w", epifierrors.ErrInvalidArgument)
	}
	if marshalErr != nil {
		return "", fmt.Errorf("cannot marshal struct to json: %w", err)
	}
	return string(data), nil
}
