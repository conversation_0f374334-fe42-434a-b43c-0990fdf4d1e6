package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/employment/developer"
	"github.com/epifi/gamma/employment/developer/processor"
)

type DevFactory struct {
	devEmploymentData    *processor.DevEmploymentData
	devEmployer          *processor.DevEmployer
	devEmployerPiMapping *processor.DevEmployerPiMapping
}

func NewDevFactory(devEmplData *processor.DevEmploymentData, devEmployer *processor.DevEmployer, devEmployerPiMapping *processor.DevEmployerPiMapping) *DevFactory {
	return &DevFactory{
		devEmploymentData:    devEmplData,
		devEmployer:          devEmployer,
		devEmployerPiMapping: devEmployerPiMapping,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.EmploymentEntity) (IParameterFetcher, error) {
	if entity == developer.EmploymentEntity_EMPLOYMENT_DATA {
		return d.devEmploymentData, nil
	}
	if entity == developer.EmploymentEntity_EMPLOYER {
		return d.devEmployer, nil
	}
	if entity == developer.EmploymentEntity_EMPLOYER_PI_MAPPING {
		return d.devEmployerPiMapping, nil
	}
	return nil, fmt.Errorf("no valid implementation found")
}

func (d *DevFactory) getDataImpl(entity developer.EmploymentEntity) (IDataFetcher, error) {
	if entity == developer.EmploymentEntity_EMPLOYMENT_DATA {
		return d.devEmploymentData, nil
	}
	if entity == developer.EmploymentEntity_EMPLOYER {
		return d.devEmployer, nil
	}
	if entity == developer.EmploymentEntity_EMPLOYER_PI_MAPPING {
		return d.devEmployerPiMapping, nil
	}
	return nil, fmt.Errorf("no valid implementation found")
}
