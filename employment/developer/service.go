package developer

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/employment/developer"
)

type EmploymentDevService struct {
	fac *DevFactory
}

func NewEmploymentDevService(fac *DevFactory) *EmploymentDevService {
	return &EmploymentDevService{
		fac: fac,
	}
}

func (c *EmploymentDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			developer.EmploymentEntity_EMPLOYMENT_DATA.String(),
			developer.EmploymentEntity_EMPLOYER.String(),
			developer.EmploymentEntity_EMPLOYER_PI_MAPPING.String(),
		},
	}, nil
}

func (c *EmploymentDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.EmploymentEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in employment"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.EmploymentEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in employment")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.EmploymentEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *EmploymentDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.EmploymentEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in employment"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.EmploymentEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in employment")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.EmploymentEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if storageV2.IsRecordNotFoundError(err) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
