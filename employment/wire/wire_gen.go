// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/appscreener/seon"
	"github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	employment2 "github.com/epifi/gamma/employment"
	"github.com/epifi/gamma/employment/config/genconf"
	"github.com/epifi/gamma/employment/dao"
	"github.com/epifi/gamma/employment/developer"
	"github.com/epifi/gamma/employment/developer/processor"
	types2 "github.com/epifi/gamma/employment/wire/types"
)

// Injectors from wire.go:

func InitializeService(dbV2 types.EpifiCRDB, vgEmploymentClient employment.EmploymentClient, conf *genconf.Config, actorClient actor.ActorClient, userClient user.UsersClient, empVerificationPublisher types2.EmploymentVerificationPublisher, broker events.Broker, searchClient search.ActionBarClient, onboardingClient onboarding.OnboardingClient, authClient auth.AuthClient, seonClient seon.SeonClient, ncClient namecheck.UNNameCheckClient, linkedinPublisher types2.LinkedinVerificationPublisher, updateEmploymentPublisher types2.UpdateEmploymentPublisher, incomeUpdatePublisher types2.IncomeUpdatePublisher, employerPiMappingUpdationEventSqsPublisher types2.EmployerPiMappingUpdateEventSqsPublisher, UserGroupClient group.GroupClient, bcClient bankcust.BankCustomerServiceClient, employmentDataRueidisRedisStore types2.EmploymentDataRueidisCacheStorage) *employment2.Service {
	employmentDataDaoCrdb := dao.NewEmploymentDataDaoCrdb(dbV2)
	cacheStorage := EmploymentDataCacheStorageProvider(employmentDataRueidisRedisStore)
	employmentDataCacheConfig := EmploymentDataCacheConfigProvider(conf)
	employmentDataCache := dao.NewEmployerDataCache(employmentDataDaoCrdb, cacheStorage, employmentDataCacheConfig)
	employmentDataDao := dao.ProvideEmploymentDataDao(employmentDataCache)
	emplVerificationCheckDaoCrdb := dao.NewEmplVerificationCheckDaoCrdb(dbV2)
	employmentVerificationProcessDaoCrdb := dao.NewEmploymentVerificationProcessDaoCrdb(dbV2)
	domainDetailsDaoCrdb := dao.NewDomainDetailsDaoCrdb(dbV2)
	employerDataDaoCrdb := dao.NewEmployerDataDaoCrdb(dbV2)
	cacheConfig := EmployerDataCacheConfigProvider(conf)
	employerDataCache := dao.NewEmployerDataCacheConstructor(employerDataDaoCrdb, cacheStorage, cacheConfig)
	checkHandlerImpl := employment2.NewCheckHandlerImpl(employmentDataDao, vgEmploymentClient, conf, userClient, actorClient, broker, emplVerificationCheckDaoCrdb, employmentVerificationProcessDaoCrdb, searchClient, authClient, linkedinPublisher, domainDetailsDaoCrdb, employerDataCache)
	employerPiMappingDaoCrdb := dao.NewEmployerPiMappingDaoCrdb(dbV2)
	service := employment2.NewService(employmentDataDao, vgEmploymentClient, conf, userClient, actorClient, empVerificationPublisher, broker, searchClient, onboardingClient, emplVerificationCheckDaoCrdb, employmentVerificationProcessDaoCrdb, checkHandlerImpl, seonClient, ncClient, employerDataCache, updateEmploymentPublisher, domainDetailsDaoCrdb, employerPiMappingDaoCrdb, incomeUpdatePublisher, employerPiMappingUpdationEventSqsPublisher, UserGroupClient, bcClient)
	return service
}

func InitializeDevEmploymentService(db types.EpifiCRDB, genConf *genconf.Config, employmentDataRueidisRedisStore types2.EmploymentDataRueidisCacheStorage) *developer.EmploymentDevService {
	employmentDataDaoCrdb := dao.NewEmploymentDataDaoCrdb(db)
	cacheStorage := EmploymentDataCacheStorageProvider(employmentDataRueidisRedisStore)
	employmentDataCacheConfig := EmploymentDataCacheConfigProvider(genConf)
	employmentDataCache := dao.NewEmployerDataCache(employmentDataDaoCrdb, cacheStorage, employmentDataCacheConfig)
	employmentDataDao := dao.ProvideEmploymentDataDao(employmentDataCache)
	employmentVerificationProcessDaoCrdb := dao.NewEmploymentVerificationProcessDaoCrdb(db)
	emplVerificationCheckDaoCrdb := dao.NewEmplVerificationCheckDaoCrdb(db)
	devEmploymentData := processor.NewDevEmploymentData(employmentDataDao, employmentVerificationProcessDaoCrdb, emplVerificationCheckDaoCrdb)
	employerDataDaoCrdb := dao.NewEmployerDataDaoCrdb(db)
	devEmployer := processor.NewDevEmployer(employerDataDaoCrdb)
	employerPiMappingDaoCrdb := dao.NewEmployerPiMappingDaoCrdb(db)
	devEmployerPiMapping := processor.NewDevEmployerPiMapping(employerPiMappingDaoCrdb)
	devFactory := developer.NewDevFactory(devEmploymentData, devEmployer, devEmployerPiMapping)
	employmentDevService := developer.NewEmploymentDevService(devFactory)
	return employmentDevService
}

// wire.go:

func EmploymentDataCacheStorageProvider(rueidisStore types2.EmploymentDataRueidisCacheStorage) cache.CacheStorage {
	return rueidisStore
}

func EmploymentDataCacheConfigProvider(conf *genconf.Config) *genconf.EmploymentDataCacheConfig {
	return conf.EmploymentDataCacheConfig()
}

func EmployerDataCacheConfigProvider(conf *genconf.Config) *genconf.CacheConfig {
	return conf.EmployerDataCacheConfig()
}
