//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	events2 "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/search"
	usersPb "github.com/epifi/gamma/api/user"
	userGroup "github.com/epifi/gamma/api/user/group"
	onboarding2 "github.com/epifi/gamma/api/user/onboarding"
	seonPb "github.com/epifi/gamma/api/vendorgateway/appscreener/seon"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/employment"
	"github.com/epifi/gamma/employment/config/genconf"
	"github.com/epifi/gamma/employment/dao"
	"github.com/epifi/gamma/employment/developer"
	"github.com/epifi/gamma/employment/developer/processor"
	wireTypes "github.com/epifi/gamma/employment/wire/types"
)

func InitializeService(dbV2 types.EpifiCRDB, vgEmploymentClient vgEmploymentPb.EmploymentClient,
	conf *genconf.Config, actorClient actor.ActorClient, userClient usersPb.UsersClient,
	empVerificationPublisher wireTypes.EmploymentVerificationPublisher, broker events2.Broker, searchClient search.ActionBarClient,
	onboardingClient onboarding2.OnboardingClient, authClient authPb.AuthClient,
	seonClient seonPb.SeonClient, ncClient namecheck.UNNameCheckClient, linkedinPublisher wireTypes.LinkedinVerificationPublisher, updateEmploymentPublisher wireTypes.UpdateEmploymentPublisher,
	incomeUpdatePublisher wireTypes.IncomeUpdatePublisher, employerPiMappingUpdationEventSqsPublisher wireTypes.EmployerPiMappingUpdateEventSqsPublisher, UserGroupClient userGroup.GroupClient, bcClient bankCustPb.BankCustomerServiceClient,
	employmentDataRueidisRedisStore wireTypes.EmploymentDataRueidisCacheStorage) *employment.Service {
	wire.Build(employment.NewService, dao.EmploymentDataDaoWireSet, dao.EmplVerificationCheckDaoWireSet,
		dao.EmploymentVerificationProcessDaoWireSet, employment.CheckHandlersImplWireSet, dao.DomainDaoWireSet, dao.EmployerPiMappingDaoWireSet, dao.EmployerDataCacheWireSet,
		EmploymentDataCacheStorageProvider, EmploymentDataCacheConfigProvider, EmployerDataCacheConfigProvider)
	return &employment.Service{}
}

func EmploymentDataCacheStorageProvider(rueidisStore wireTypes.EmploymentDataRueidisCacheStorage) cache.CacheStorage {
	return rueidisStore
}

func EmploymentDataCacheConfigProvider(conf *genconf.Config) *genconf.EmploymentDataCacheConfig {
	return conf.EmploymentDataCacheConfig()
}

func EmployerDataCacheConfigProvider(conf *genconf.Config) *genconf.CacheConfig {
	return conf.EmployerDataCacheConfig()
}

func InitializeDevEmploymentService(
	db types.EpifiCRDB,
	genConf *genconf.Config,
	employmentDataRueidisRedisStore wireTypes.EmploymentDataRueidisCacheStorage,
) *developer.EmploymentDevService {
	wire.Build(
		developer.NewEmploymentDevService,
		processor.NewDevEmploymentData,
		processor.NewDevEmployer,
		processor.NewDevEmployerPiMapping,
		dao.EmploymentDataDaoWireSet,
		dao.EmployerDaoWireSet,
		dao.EmployerPiMappingDaoWireSet,
		EmploymentDataCacheConfigProvider,
		dao.EmploymentVerificationProcessDaoWireSet,
		dao.EmplVerificationCheckDaoWireSet,
		developer.NewDevFactory,
		EmploymentDataCacheStorageProvider,
	)
	return &developer.EmploymentDevService{}
}
