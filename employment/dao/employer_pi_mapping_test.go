package dao_test

import (
	"context"
	"errors"
	"sort"
	"testing"

	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/employment/dao"
	userConfig "github.com/epifi/gamma/user/config"
)

type EmployerPiMappingTestSuite struct {
	conf *userConfig.Config
	db   *gorm.DB
	dao  dao.EmployerPiMappingDao
}

var (
	epmts                   *EmployerPiMappingTestSuite
	employerPiMappingTables = []string{"employer_pi_mapping"}
)

func TestEmployerPiMappingDaoCrdb_GetByPiId(t *testing.T) {
	pkgTestV2.PrepareScopedDatabase(t, epmts.conf.EpifiDb.GetName(), epmts.db, employerPiMappingTables)

	employerPiMappingData1 := &employmentPb.EmployerPiMapping{
		Id:         "f41dbdba-a30e-41ae-a336-80e05411c163",
		EmployerId: "a5004f18-5d52-4991-82a9-2a1e5010e991",
		PiId:       "pi-id-1",
		Source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
	}

	type args struct {
		piId string
	}
	tests := []struct {
		name        string
		args        args
		want        *employmentPb.EmployerPiMapping
		wantErr     bool
		wantErrType error
	}{
		{
			name:    "should return error when piId is passed empty",
			wantErr: true,
		},
		{
			name: "should return ErrRecordNotFound when no entry present in db for passed piId",
			args: args{
				piId: "pi-id-3",
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrRecordNotFound,
		},
		{
			name: "should return correct employerPiMappingData entry when entry exists for given pi-id",
			args: args{
				piId: "pi-id-1",
			},
			want: employerPiMappingData1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := epmts.dao.GetByPiId(context.Background(), tt.args.piId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("GetByPiId() gotErrType = %v\nwantErrType %v", err, tt.wantErrType)
				return
			}
			if !isEmployerPiMappingEqual(got, tt.want) {
				t.Errorf("GetByPiId() got = %v\nwant %v", got, tt.want)
			}
		})
	}
}

func isEmployerPiMappingEqual(got, want *employmentPb.EmployerPiMapping) bool {
	if got != nil && want != nil {
		want.CreatedAt = got.CreatedAt
		want.UpdatedAt = got.UpdatedAt
	}
	return proto.Equal(got, want)
}

func TestEmployerPiMappingDaoCrdb_CreateMapping(t *testing.T) {
	pkgTestV2.PrepareScopedDatabase(t, epmts.conf.EpifiDb.GetName(), epmts.db, employerPiMappingTables)
	type args struct {
		ctx        context.Context
		piId       string
		employerId string
		source     employmentPb.EmployerPiMappingSource
	}
	tests := []struct {
		name        string
		args        args
		want        *employmentPb.EmployerPiMapping
		wantErr     bool
		wantErrType error
	}{
		{
			name: "should successfully create mapping",
			args: args{
				ctx:        context.Background(),
				piId:       "pi-id-3",
				employerId: "a5004f18-5d52-4991-82a9-2a1e5010e991",
				source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Case when piId is empty",
			args: args{
				ctx:        context.Background(),
				piId:       "",
				employerId: "emp-id-1",
				source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Case when employerId is empty",
			args: args{
				ctx:        context.Background(),
				piId:       "pi-id-1",
				employerId: "",
				source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Case when piId is already present in table",
			args: args{
				ctx:        context.Background(),
				piId:       "pi-id-2",
				employerId: "a5004f18-5d52-4991-82a9-2a1e5010e991",
				source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should return error when source is passed as unspecified",
			args: args{
				ctx:        context.Background(),
				piId:       "pi-id-1",
				employerId: "emp-id-1",
				source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_UNSPECIFIED,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := epmts.dao.CreateMapping(tt.args.ctx, &employmentPb.EmployerPiMapping{PiId: tt.args.piId, EmployerId: tt.args.employerId, Source: tt.args.source})
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("GetByPiId() gotErrType = %v\nwantErrType %v", err, tt.wantErrType)
				return
			}
		})
	}
}

func TestEmployerPiMappingDaoCrdb_GetByEmployerId(t *testing.T) {
	pkgTestV2.PrepareScopedDatabase(t, epmts.conf.EpifiDb.GetName(), epmts.db, employerPiMappingTables)

	employerId := "a5004f18-5d52-4991-82a9-2a1e5010e991"

	mapping1 := &employmentPb.EmployerPiMapping{
		EmployerId: employerId,
		PiId:       "pi-id-1",
		Source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
	}
	mapping2 := &employmentPb.EmployerPiMapping{
		EmployerId: employerId,
		PiId:       "pi-id-2",
		Source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
	}

	tests := []struct {
		name        string
		employerId  string
		want        []*employmentPb.EmployerPiMapping
		wantErr     bool
		wantErrType error
	}{
		{
			name:       "should return all mappings for valid employerId",
			employerId: employerId,
			want:       []*employmentPb.EmployerPiMapping{mapping1, mapping2},
			wantErr:    false,
		},
		{
			name:        "should return error for employerId with no mappings",
			employerId:  "non-existent-employer-id",
			want:        nil,
			wantErr:     true,
			wantErrType: epifierrors.ErrRecordNotFound,
		},
		{
			name:        "should return error for empty employerId",
			employerId:  "",
			want:        nil,
			wantErr:     true,
			wantErrType: errors.New("employerId cannot be empty"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := epmts.dao.GetByEmployerId(context.Background(), tt.employerId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByEmployerId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErrType != nil {
				if !errors.Is(err, tt.wantErrType) && err.Error() != tt.wantErrType.Error() {
					t.Errorf("GetByEmployerId() gotErrType = %v\nwantErrType %v", err, tt.wantErrType)
					return
				}
			}
			if !tt.wantErr {
				// Compare slices ignoring CreatedAt/UpdatedAt
				if len(got) != len(tt.want) {
					t.Errorf("GetByEmployerId() got = %v, want %v", got, tt.want)
					return
				}
				sort.Slice(got, func(i, j int) bool { return got[i].PiId < got[j].PiId })
				sort.Slice(tt.want, func(i, j int) bool { return tt.want[i].PiId < tt.want[j].PiId })

				for i := range got {
					want := tt.want[i]
					want.CreatedAt = got[i].CreatedAt
					want.UpdatedAt = got[i].UpdatedAt
					want.Id = got[i].Id
					if !proto.Equal(got[i], want) {
						t.Errorf("GetByEmployerId() got = %v, want %v", got[i], want)
					}
				}
			}
		})
	}
}
