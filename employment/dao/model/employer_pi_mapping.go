package model

import (
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	employmentPb "github.com/epifi/gamma/api/employment"
)

// EmployerPiMapping is mapping of employer to details of payment instrument from which salary credits are done by employer
type EmployerPiMapping struct {
	// unique identifier for an Employment data entry
	Id string `gorm:"primary_key;type:uuid;default:gen_random_uuid()"`

	// EmployerId is the foreign key to employer in internal DB
	EmployerId string

	// PiId is the payment instrument id from which salary credits are performed by the employer
	PiId string

	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt

	// Source of creation indicates the source from where the employer id - pi id mapping is created
	Source employmentPb.EmployerPiMappingSource
}

func (EmployerPiMapping) TableName() string {
	return "employer_pi_mapping"
}

func (e *EmployerPiMapping) GetProto() *employmentPb.EmployerPiMapping {
	employerPiMappingProto := &employmentPb.EmployerPiMapping{
		Id:         e.Id,
		EmployerId: e.EmployerId,
		PiId:       e.PiId,
		Source:     e.Source,
		CreatedAt:  timestamp.New(e.CreatedAt),
		UpdatedAt:  timestamp.New(e.UpdatedAt),
	}

	if e.DeletedAt.Valid {
		employerPiMappingProto.DeletedAt = timestamp.New(e.DeletedAt.Time)
	}
	return employerPiMappingProto
}

func CreateObjectForEmployerPiMap(employerPiMappingParams *employmentPb.EmployerPiMapping) *EmployerPiMapping {
	employerPiMappingDbModel := &EmployerPiMapping{}
	employerPiMappingDbModel.EmployerId = employerPiMappingParams.GetEmployerId()
	employerPiMappingDbModel.PiId = employerPiMappingParams.GetPiId()
	employerPiMappingDbModel.Source = employerPiMappingParams.GetSource()
	return employerPiMappingDbModel
}
