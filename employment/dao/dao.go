//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/google/wire"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/be-common/pkg/pagination"
)

var (
	EmploymentDataDaoWireSet = wire.NewSet(
		ProvideEmploymentDataDao,
		NewEmployerDataCache,
		NewEmploymentDataDaoCrdb)
	EmplVerificationCheckDaoWireSet         = wire.NewSet(NewEmplVerificationCheckDaoCrdb, wire.Bind(new(EmplVerificationCheckDao), new(*EmplVerificationCheckDaoCrdb)))
	EmploymentVerificationProcessDaoWireSet = wire.NewSet(NewEmploymentVerificationProcessDaoCrdb, wire.Bind(new(EmploymentVerificationProcessDao), new(*EmploymentVerificationProcessDaoCrdb)))
	EmployerDaoWireSet                      = wire.NewSet(NewEmployerDataDaoCrdb, wire.Bind(new(EmployerDao), new(*EmployerDataDaoCrdb)))
	DomainDaoWireSet                        = wire.NewSet(NewDomainDetailsDaoCrdb, wire.Bind(new(DomainDetailsDao), new(*DomainDetailsDaoCrdb)))
	EmployerPiMappingDaoWireSet             = wire.NewSet(NewEmployerPiMappingDaoCrdb, wire.Bind(new(EmployerPiMappingDao), new(*EmployerPiMappingDaoCrdb)))
	EmployerDataCacheWireSet                = wire.NewSet(NewEmployerDataCacheConstructor, wire.Bind(new(EmployerDao), new(*EmployerDataCache)), NewEmployerDataDaoCrdb)
)

//go:generate mockgen -source=dao.go -destination=mocks/mock_dao.go -package=mocks
type EmploymentDataDao interface {
	GetByActorId(ctx context.Context, actorId string) (*employmentPb.EmploymentData, error)
	CreateEmploymentData(ctx context.Context, empData *employmentPb.EmploymentData) (*employmentPb.EmploymentData, error)
	Update(ctx context.Context, empData *employmentPb.EmploymentData, updateMask []employmentPb.EmploymentDataFieldMask) error
	DeleteByActorId(ctx context.Context, actorId string) error
}

type EmplVerificationCheckDao interface {
	Create(ctx context.Context, empData *employmentPb.EmploymentVerificationCheck) (*employmentPb.EmploymentVerificationCheck, error)
	GetByProcessIdAndName(ctx context.Context, processId string, name employmentPb.CheckName) (*employmentPb.EmploymentVerificationCheck, error)
	Update(ctx context.Context, empData *employmentPb.EmploymentVerificationCheck, updateMask []employmentPb.EmploymentVerificationCheckFieldMask) error
	DeleteCheckOfProcessId(ctx context.Context, processId string) error
	DeleteByActorId(ctx context.Context, actorId string) error
}

type EmploymentVerificationProcessDao interface {
	Create(ctx context.Context, empData *employmentPb.EmploymentVerificationProcess) (*employmentPb.EmploymentVerificationProcess, error)
	// GetByActorIdAndProcessName fetches the latest process entry that matches the conditions
	GetByActorIdAndProcessName(ctx context.Context, actorId string, name employmentPb.ProcessName) (*employmentPb.EmploymentVerificationProcess, error)
	GetByClientReqIdAndProcessName(ctx context.Context, clientReqId string, name employmentPb.ProcessName) (*employmentPb.EmploymentVerificationProcess, error)
	Update(ctx context.Context, empData *employmentPb.EmploymentVerificationProcess, updateMask []employmentPb.EmploymentVerificationProcessFieldMask) error
	DeleteAllProcessOfEmploymentId(ctx context.Context, empId string) error
	DeleteByActorId(ctx context.Context, actorId string) error
	GetByWorkEmailAndVerificationResult(ctx context.Context, emailId string, result employmentPb.EmploymentVerificationResult) (*employmentPb.EmploymentVerificationProcess, error)
	GetByWorkEmailClientsAndVerificationResult(ctx context.Context, emailId string, clients []employmentPb.VerificationProcessClient, result employmentPb.EmploymentVerificationResult) (*employmentPb.EmploymentVerificationProcess, error)
}

type EmployerDao interface {
	Create(ctx context.Context, employerData *employmentPb.EmployerData) (*employmentPb.EmployerData, error)
	GetByEmployerId(ctx context.Context, employerId string) (*employmentPb.EmployerData, error)
	Update(ctx context.Context, employerData *employmentPb.EmployerData, updateMasks []employmentPb.EmployerDataFieldMask) (*employmentPb.EmployerData, error)
	GetByEmployerName(ctx context.Context, employerName string) (*employmentPb.EmployerData, error)
	GetByGSTIN(ctx context.Context, gstin string) (*employmentPb.EmployerData, error)
	BulkCreate(ctx context.Context, employerDataEntries []*employmentPb.EmployerData, batchSize int) error
	GetPaginated(ctx context.Context, updatedFrom *timestampPb.Timestamp, pageToken *pagination.PageToken, pageSize uint32) ([]*employmentPb.EmployerData, *rpc.PageContextResponse, error)
}

type DomainDetailsDao interface {
	Create(ctx context.Context, domainDetails *employmentPb.DomainDetails) (*employmentPb.DomainDetails, error)
	// TODO(eswar): depracate GetByDomainName once GetByDomainNameAndType is live
	GetByDomainName(ctx context.Context, domainName string) (*employmentPb.DomainDetails, error)
	Update(ctx context.Context, domainDetails *employmentPb.DomainDetails, updateMasks []employmentPb.DomainDetailsFieldMask) error
	// GetByDomainNameAndType returns data if domain exists in DB. Search is done after converting domain name to lower case
	GetByDomainNameAndType(ctx context.Context, domainName string, domainType []employmentPb.DomainType) (*employmentPb.DomainDetails, error)
}

type EmployerPiMappingDao interface {
	GetByPiId(ctx context.Context, piId string) (*employmentPb.EmployerPiMapping, error)
	CreateMapping(ctx context.Context, employerPiMappingParams *employmentPb.EmployerPiMapping) error
	GetByEmployerId(ctx context.Context, employerId string) ([]*employmentPb.EmployerPiMapping, error)
}

func ProvideEmploymentDataDao(dao *EmploymentDataCache) EmploymentDataDao {
	return dao
}
