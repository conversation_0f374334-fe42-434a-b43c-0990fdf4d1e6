// nolint: gosec
package entity

import (
	"github.com/epifi/gamma/search/aggregations"
)

type actorSummaryParams struct {
	TxnDirection           string                   `json:"txn_direction,omitempty"`
	IsZeroResult           bool                     `json:"is_zero_result,omitempty"`
	ShowAnd                bool                     `json:"show_and,omitempty"`
	SecondActors           []string                 `json:"second_actors,omitempty"`
	SecondActorsToShow     string                   `json:"second_actors_to_show,omitempty"`
	ComparatorToShow       string                   `json:"comparator_to_show,omitempty"`
	AggrFuncToShow         string                   `json:"aggr_func_to_show,omitempty"`
	TagsToShow             string                   `json:"tags_to_show,omitempty"`
	RemarksToShow          string                   `json:"remarks_to_show,omitempty"`
	TxnCategory            []string                 `json:"txn_category,omitempty"`
	TxnCount               int                      `json:"txn_count,omitempty"`
	TxnCountToShow         string                   `json:"txn_count_to_show,omitempty"`
	TxnType                string                   `json:"txn_type,omitempty"`
	TimeToShow             string                   `json:"time_to_show,omitempty"`
	TimeToShowNew          string                   `json:"time_to_show_new,omitempty"`
	TimeToShowDebit        string                   `json:"time_to_show_debit"`
	TimeToShowCredit       string                   `json:"time_to_show_credit"`
	Amount                 float64                  `json:"amount,omitempty"`
	CrSummary              *aggregations.AggSummary `json:"cr_summary,omitempty"`
	DrSummary              *aggregations.AggSummary `json:"dr_summary,omitempty"`
	CrSummaryToShow        string                   `json:"cr_summary_to_show,omitempty"`
	DrSummaryToShow        string                   `json:"dr_summary_to_show,omitempty"`
	IsAtmSummary           bool                     `json:"is_atm_summary,omitempty"`
	Name                   string                   `json:"name,omitempty"`
	IsMerchant             bool                     `json:"is_merchant,omitempty"`
	CardOfferName          string                   `json:"card_offer_name,omitempty"`
	CardOfferDesc          string                   `json:"card_offer_desc,omitempty"`
	CardOfferCouponCode    string                   `json:"card_offer_coupon_code,omitempty"`
	CardOfferTnC           string                   `json:"card_offer_tn_c,omitempty"`
	MerchantCardOffersList string                   `json:"merchant_card_offers_list,omitempty"`
	LenOfCardOffersList    int                      `json:"card_offers_list_len,omitempty"`
}

type pISummaryFields struct {
	AccountNumber          []string                 `json:"account_number,omitempty"`
	PaymentProtocolsToShow string                   `json:"payment_protocols_to_show,omitempty"`
	UpiIntent              bool                     `json:"upi_intent,omitempty"`
	UpiIdToShow            string                   `json:"upi_id_to_show,omitempty"`
	DebitCardIntent        bool                     `json:"debit_card_intent,omitempty"`
	DebitCardNumberToShow  string                   `json:"debit_card_number_to_show,omitempty"`
	CreditCardIntent       bool                     `json:"credit_card_intent,omitempty"`
	CreditCardNumberToShow string                   `json:"credit_card_number_to_show,omitempty"`
	AccountNames           []string                 `json:"account_names,omitempty"`
	InstrumentTypes        string                   `json:"instrument_types,omitempty"`
	IsZeroResult           bool                     `json:"is_zero_result"`
	ShowAnd                bool                     `json:"show_and,omitempty"`
	TxnCount               int                      `json:"txn_count,omitempty"`
	TxnCountToShow         string                   `json:"txn_count_to_show,omitempty"`
	ComparatorToShow       string                   `json:"comparator_to_show"`
	Amount                 float64                  `json:"amount,omitempty"`
	TimeToShow             string                   `json:"time_to_show,omitempty"`
	CrSummary              *aggregations.AggSummary `json:"cr_summary,omitempty"`
	DrSummary              *aggregations.AggSummary `json:"dr_summary,omitempty"`
	CrSummaryToShow        string                   `json:"cr_summary_to_show,omitempty"`
	DrSummaryToShow        string                   `json:"dr_summary_to_show,omitempty"`
	IsErr                  bool                     `json:"is_err,omitempty"`
	ErrText                string                   `json:"err_text,omitempty"`
	Awb                    string                   `json:"awb,omitempty"`
	Carrier                string                   `json:"carrier,omitempty"`
	DeliveryState          string                   `json:"DeliveryState,omitempty"`
	DeliveryDate           string                   `json:"DeliveryDate,omitempty"`
	PosTxnLimit            int64                    `json:"pos_txn_limit,omitempty"`
	OnlineTxnLimit         int64                    `json:"online_txn_limit,omitempty"`
	ContactlessTxnLimit    int64                    `json:"contactless_txn_limit,omitempty"`
	AtmWithdrawalLimit     int64                    `json:"atm_withdrawal_limit,omitempty"`
	PosTxnCurrCode         string                   `json:"pos_txn_curr_code,omitempty"`
	OnlineTxnCurrCode      string                   `json:"online_txn_curr_code,omitempty"`
	ContactlessTxnCurrCode string                   `json:"contactless_txn_curr_code,omitempty"`
	AtmWithdrawalCurrCode  string                   `json:"atm_withdrawal_curr_code,omitempty"`
	// Might need below-mentioned fields in the future.
	AllowedCardMaxLimit     int64 `json:"allowed_card_max_limit,omitempty"`
	AllowedPurchaseMaxLimit int64 `json:"allowed_purchase_max_limit,omitempty"`
	AllowedAtmMaxLimit      int64 `json:"allowed_atm_max_limit,omitempty"`
}

type AllBanksTabSummaryParams struct {
	TotalDebitValue  string `json:"total_debit_value,omitempty"`
	TotalCreditValue string `json:"total_credit_value,omitempty"`
	QueryIntent      string `json:"query_intent,omitempty"`
	FromTime         string `json:"from_time,omitempty"`
	ToTime           string `json:"to_time,omitempty"`
	TimeToShow       string `json:"time_to_show,omitempty"`
	MinCreditValue   string `json:"min_credit_value,omitempty"`
	MaxCreditValue   string `json:"max_credit_value,omitempty"`
	MinDebitValue    string `json:"min_debit_value,omitempty"`
	MaxDebitValue    string `json:"max_debit_value,omitempty"`
	AvgDebits        string `json:"avg_debits,omitempty"`
	AvgCredits       string `json:"avg_credits,omitempty"`
	AggregationFunc  string `json:"aggregation_func,omitempty"`
	CaBankName       string `json:"ca_bank_name,omitempty"`
	TxnDirection     string `json:"TxnDirection,omitempty"`
}

type GlossarySummaryParams struct {
	FiniteCode string `json:"finite_code,omitempty"`
}

/*
   All Banks tab summary templates
*/

const AllBanksTabSummary = `{{if and (eq .TotalDebitValue "₹0.00") (eq .TotalCreditValue "₹0.00")}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?
{{else}}Across all bank accounts
{{if eq .TxnDirection "debit"}}Total spent: {{.TotalDebitValue}}
{{- else if eq .TxnDirection "credit"}}Total received: {{.TotalCreditValue}}
{{- else}}Total spent:      {{.TotalDebitValue}}
Total received: {{.TotalCreditValue}}{{end}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}`

const AllBanksTabAggregationSummary = `{{ if or (eq .QueryIntent "expensedetails") (eq .QueryIntent "transactiondetails")}}
{{- if eq .AggregationFunc "biggest"}}{{if eq .MaxDebitValue "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Maximum spent: {{.MaxDebitValue}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}
{{- else if eq .AggregationFunc "smallest"}}{{if eq .MinDebitValue "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Minimum spent: {{.MinDebitValue}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}
{{- else if eq .AggregationFunc "average"}}{{if eq .AvgDebits "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Average spent: {{.AvgDebits}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}
{{- else}}{{if eq .TotalDebitValue "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Total spent: {{.TotalDebitValue}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}{{end}}
{{- else if eq .QueryIntent "incomedetails"}}
{{- if eq .AggregationFunc "biggest"}}{{if eq .MaxCreditValue "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Maximum received: {{.MaxCreditValue}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}
{{- else if eq .AggregationFunc "smallest"}}{{if eq .MinCreditValue "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Minimum received: {{.MinCreditValue}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}
{{- else if eq .AggregationFunc "average"}}{{if eq .AvgCredits "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Average received: {{.AvgCredits}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}
{{- else}}{{if eq .TotalCreditValue "₹0.00"}}No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?{{- else}}Across all bank accounts
Total received: {{.TotalCreditValue}}
{{if eq .FromTime .ToTime}}On {{.FromTime}}
{{- else}}From: {{.FromTime}} To: {{.ToTime}}{{end}}{{end}}{{end -}}{{end -}}`

const AllBanksTabAggregationZeroStateSummary = `No transactions found across all the bank accounts{{if eq .FromTime .ToTime}} on {{.FromTime}}{{else}}
From: {{.FromTime}} To: {{.ToTime}}{{end}}
Try for a different duration instead?`

const AllBanksTabBankTxnQueryZeroStateSummary = `You have not yet connected your account on {{.CaBankName}}.
Click on the "Connect Account" button to connect your bank account.`

// International Transactions Skill summary
const SkillInternationalTxnsSummary = `To enable/disable International Transactions:

*Tap on Card usage below.
*Then select/deselect 'Enable International Transactions'.

Now, you can use your Fi card wherever Visa is accepted 💳`

// Hardcoded query summaries
const (
	GreetQuerySummary = `Hello!
Checkout what all can you explore by just Asking Fi! 😬

💰 Where is your money going? Just ask:
• Spends on 14th Feb
• My spends this month
• Swiggy spends this year
• debit card transactions
• upi transactions

🏦 Getting your account information! Just ask:
• Account Balance
• my upi id
• my ifsc code

🔍 In-app Navigation when you can’t seem to find a feature! Just ask:
• Mutual Funds
• Jump
• Smart Deposit`
)

// minimum balance response
const fiAccountMinBalanceSummary = `Your Fi account has no minimum balance requirement!
It is a zero balance savings account 😎`

// help query summary
const HelpQuerySummary = `We are always around to help!
If you have any app-specific issues:

*Tap on 'Help' below 🔍
You will find answers in our Frequently Asked Questions
*Ping us via the in-app chat
*Or e-mail us at: <EMAIL>`

// chat query summary
const ChatQuerySummary = `We are easy to reach & eager to help 😇
*To chat with our Fi Care team about app-specific issues 💬
Tap on 'Chat With Us' below

*If our in-app chat specialists appear busy 📩
Tell us your concerns over e-mail at: <EMAIL>`

const debitCardOffersSummary = `Special discounts on Myntra, Spotify, Amazon and other top brands 🛍 Tap on 'Card Offers' below for more!`

const actorConnectWithGmailSummary = "On Fi, we found zero transactions relevant to your query. Try granting permissions to your Gmail account for getting better insights into your finances!"

const cardPinUpdateSummary = `It's easy to change your ATM PIN on Fi 🔒
Just tap on 'Card Settings' below and go to 'Reset ATM PIN'.

Pro Tip: Consider these safety factors while setting a new ATM PIN:
*Don't set identical ATM PINs (like 1111)
*Avoid repeating the last 2 digits (like 4588)
*Skip sequential ATM PINs (like 1234)`

const piDetailSummaryTmpl = `{{ if .UpiIntent }} Your UPI ID is {{ .UpiIdToShow }} {{ end -}}
{{- if .DebitCardIntent }}Your Debit Card number is {{ .DebitCardNumberToShow }}💳{{ end -}}
{{- if .CreditCardIntent }}Your Credit Card number is {{ .CreditCardNumberToShow }}💳{{ end -}}`

const trackingDetailTmpl = `{{ if eq .DeliveryState "IN_TRANSIT" }} Your card is {{.DeliveryState}}. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}
{{- if eq .DeliveryState "OUT_FOR_DELIVERY"}} Your card is {{.DeliveryState}}. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}
{{- if eq .DeliveryState "RETURNED_TO_ORIGIN"}} Your card is {{.DeliveryState}}. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}
{{- if eq .DeliveryState "SHIPPED"}} Your card has been dispatched on {{.DeliveryDate}}. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}
{{- if eq .DeliveryState "DELIVERED"}} Your card has been delivered on {{.DeliveryDate}} {{end -}}
{{- if eq .DeliveryState "CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED" }} Your card will be dispatched soon. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}`

const piErrSummaryTmpl = `{{ .ErrText }}`

// below summary is used for writing unit tests.
const cardLimitDetailsSummary = `You remain in control of your Fi-Federal Debit Card's limits 💳 By default, the maximum daily limit for a Fi Card is ₹6,00,000.

Fi lets you set the domestic/international usage limits for:
Online usage, ATM Withdrawal, POS & Contactless transactions.

Your current Domestic POS transaction limit is: INR 200000
Your current Domestic Online transaction limit is: INR 250000
Your current Domestic Contactless transaction limit is: INR 29999
Your current Domestic ATM withdrawal limit is: INR 50000

👉 Tap on 'Card Limits' below for more.`

const fetchCardLimitDetailsTmpl = `You remain in control of your Fi-Federal Debit Card's limits 💳 By default, the maximum daily limit for a Fi Card is ₹6,00,000.

Fi lets you set the domestic/international usage limits for:
Online usage, ATM Withdrawal, POS & Contactless transactions.

Your current Domestic POS transaction limit is: {{.PosTxnCurrCode}} {{.PosTxnLimit}}
Your current Domestic Online transaction limit is: {{.OnlineTxnCurrCode}} {{.OnlineTxnLimit}}
Your current Domestic Contactless transaction limit is: {{.ContactlessTxnCurrCode}} {{.ContactlessTxnLimit}}
Your current Domestic ATM withdrawal limit is: {{.AtmWithdrawalCurrCode}} {{.AtmWithdrawalLimit}}

👉 Tap on 'Card Limits' below for more.`

const updateCardLimitSummary = `You remain in control of your Fi-Federal Debit Card's limits 💳 By default, the maximum daily limit for a Fi Card is ₹6,00,000

Fi lets you set the domestic/international usage limits for:
Online usage, ATM Withdrawal, POS & Contactless transactions.

 👉 Tap on 'Card Limits' below for more.`

/*
templates for txn entity
NOTE : add test in transactionEntity_test.TestTransactionEntity_getSummaryTmpl to cover tests for these templates
*/
var emptyFinGlossaryRes = [3]string{"Ah! It looks like we’ve not defined this...yet",
	"We’re stumped...maybe our team will have a description for this soon 🔎",
	"Sorry! Guess we don’t have an explainer for this term right now 🤷‍️"}

const KycStatusTmpl = `{{if eq .KycStatus "FULL_KYC" }}Good news! You are a fully-verified Fi user 😎
So, there is no need for you to complete a video KYC call.{{end -}}
{{if eq .KycStatus "MIN_KYC"}}Currently, you are a minimum-KYC user.
Want to gain access to a full-fledged Fi account?

Spare 3-minutes & complete a video KYC verification call 📹
- You get a Fi account with no deposit/transaction limits 🚀
- Your savings account gains lifetime validity ✅️️️{{end -}}
{{if eq .KycStatus ""}}We could not retrieve KYC detail at the moment. Please check again soon{{end -}}`

const MutualFundsTemplate = `{{if .IsErr}}Mutual Funds related details could not be retrieved! Please try again later.
{{else if eq .TotalInvestedAmt "₹0.00"}}Not made any investments on Fi yet? Tap on the quicklink below to explore commission-free Mutual Funds on Fi.
{{else if eq .QuerySkill "MutualFund"}}As of Today,
Total investments: {{.TotalInvestedAmt}}
Current value: {{.CurrValOfInvestedAmt}}
Portfolio growth: {{.GrowthPercentage}}%
{{else if eq .QuerySkill "MFCurrentValueSkill"}}Till now you have invested a total of {{.TotalInvestedAmt}} in Mutual Funds.
{{else if eq .QuerySkill "MFPortfolioGrowthSkill"}}Your portfolio's growth percentage is {{.GrowthPercentage}}%
{{else if eq .QuerySkill "MFTotalInvestedSkill"}}The current value of your Mutual Funds Investments is {{.CurrValOfInvestedAmt}}{{end -}}`

const fiAccountInterestRateSummary = `The interest rate for your Federal Bank Savings Account on Fi is 3.00%`

const accountBalanceErrorSummary = `We were not able to retrieve Account balance`

const accountNumberErrorSummary = `We were not able to retrieve your Fi account number`
