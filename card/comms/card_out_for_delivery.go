// nolint: dupl
package comms

import (
	"fmt"

	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"golang.org/x/net/context"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type CardOutForDeliveryRule struct {
	helper *helper.CommsHelper
}

func NewCardOutForDeliveryRule(helper *helper.CommsHelper) *CardOutForDeliveryRule {
	return &CardOutForDeliveryRule{
		helper: helper,
	}
}

func (c *CardOutForDeliveryRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if data.GetDeliveryTrackingState() == cardProvPb.CardTrackingDeliveryState_OUT_FOR_DELIVERY && data.GetAwb() != "" && data.GetCarrier() != "" {
		// get tracking url for carrier
		trackUrl, err := c.helper.GetTrackingUrlByCarrier(data.GetCarrier())
		if err != nil {
			logger.Error(ctx, "error getting track url by carrier", zap.Error(err))
			return nil, err
		}

		res = append(res, &commsPb.SendMessageRequest_Whatsapp{
			Whatsapp: &commsPb.WhatsappMessage{
				WhatsappOption: &commsPb.WhatsappOption{
					Option: &commsPb.WhatsappOption_CardOutForDeliveryWhatsappOption{
						CardOutForDeliveryWhatsappOption: &commsPb.CardOutForDeliveryWhatsappOption{
							WhatsappType: commsPb.WhatsappType_WHATSAPP_TYPE_CARD_OUT_FOR_DELIVERY,
							Option: &commsPb.CardOutForDeliveryWhatsappOption_CardOutForDeliveryWhatsappOptionV1{
								CardOutForDeliveryWhatsappOptionV1: &commsPb.CardOutForDeliveryWhatsappOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									CardName_1:      "Federal Bank Debit",
									CardHolderName:  data.GetCardHolderName(),
									CardName_2:      "Federal Bank Debit",
									LastFourDigit:   data.GetCardLastFourDigits(),
									TrackingLink:    fmt.Sprintf("%s and enter AWB: %s", trackUrl, data.GetAwb()),
								},
							},
						},
					},
				},
			},
		}, helper.GetPnWithCommonFields("Your Fi-Federal Debit Card arrives today.",
			"As long as the white tamper-proof seal on the box is intact, the card remains secure. If the seal is broken, don’t accept it.",
			helper.NewCardTrackingScreenDl(data.GetCardId()),
		))
	}
	return
}

func (c *CardOutForDeliveryRule) GetDoOnceTaskId(_ context.Context, data *cardCommsPb.ActionData, medium commsPb.Medium) string {
	if data.GetDeliveryTrackingState() != cardProvPb.CardTrackingDeliveryState_OUT_FOR_DELIVERY {
		return ""
	}
	return data.GetCardId() + "CARD_OUT_FOR_DELIVERY" + medium.String()
}
