package firefly

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	v2 "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/config/genconf"
	fireflyPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	getIntroScreenV2InternalErrorResponse = func() *ffPb.GetCCIntroScreenV2Response {
		return &ffPb.GetCCIntroScreenV2Response{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}
	}
)

func (s *Service) GetCCIntroScreenV2(ctx context.Context, req *ffPb.GetCCIntroScreenV2Request) (*ffPb.GetCCIntroScreenV2Response, error) {
	data, err := s.gatherIntroScreenV2Data(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to gather intro screen v2 data", zap.Error(err))
		return getIntroScreenV2InternalErrorResponse(), nil
	}

	section, floatingSection, backgroundImage, buildErr := s.ccIntroScrenBuilder.BuildScreen(data)

	if buildErr != nil {
		logger.Error(ctx, "failed to build intro screen v2 data", zap.Error(buildErr))
		return getIntroScreenV2InternalErrorResponse(), nil
	}

	return &ffPb.GetCCIntroScreenV2Response{
		RespHeader:      &header.ResponseHeader{Status: rpc.StatusOk()},
		Section:         section,
		FloatingSection: floatingSection,
		BackgroundImage: backgroundImage,
	}, nil
}

func (s *Service) gatherIntroScreenV2Data(ctx context.Context, req *ffPb.GetCCIntroScreenV2Request) (*introScreenV2Data, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	cardProgram := fireflyPkg.GetCardProgramFromCardProgramString(req.GetCreditCardRequestHeader().GetCardProgram())
	metadata, getMetadataErr := fireflyPkg.GetCardMetadataFromString(req.GetMetadata())
	if getMetadataErr != nil {
		return nil, fmt.Errorf("failed to get metadata from string: %w", getMetadataErr)
	}

	introScreenMetadata := metadata.GetIntroScreenV2Metadata()
	if cardProgram.GetCardProgramType() == typesV2.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED {
		cardProgram.CardProgramType = typesV2.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED
	}

	cardStateVal, ok := ccEnumsV2Pb.CardState_value[introScreenMetadata.GetCardState()]
	if !ok {
		return nil, fmt.Errorf("failed to get card state from card-metadata: %s", cardProgram.GetCardProgramType())
	}
	cardState := ccEnumsV2Pb.CardState(cardStateVal)

	cardOnbReqStatusVal, ok := ccEnumsV2Pb.CardRequestStatus_value[introScreenMetadata.GetOnboardingRequestStatus()]
	if !ok {
		return nil, fmt.Errorf("failed to get card request status from card-metadata: %s", cardProgram.GetCardProgramType())
	}
	cardOnbReqStatus := ccEnumsV2Pb.CardRequestStatus(cardOnbReqStatusVal)

	data := &introScreenV2Data{
		cardProgram:      cardProgram,
		cardState:        cardState,
		cardOnbReqStatus: cardOnbReqStatus,
		screenOptions:    s.dynamicConf.CreditCard().CCIntroScreenV2Options(),
		baseAnalyticsProperties: map[string]string{
			"card_name":                      cardProgram.GetCardProgramType().String(),
			"bank_name":                      cardProgram.GetCardProgramVendor().String(),
			"card_state":                     cardState.String(),
			"card_onboarding_request_status": cardOnbReqStatus.String(),
		},
	}

	rewardOfferResp, rewardOfferErr := s.fireflyV2Client.GetCreditCardOffers(ctx, &v2.GetCreditCardOffersRequest{
		ActorId:     actorId,
		Vendor:      fireflyPkg.GetVgVendorForCcVendor(cardProgram.GetCardProgramVendor()),
		CardProgram: cardProgram,
	})
	if rpcErr := epifigrpc.RPCError(rewardOfferResp, rewardOfferErr); rpcErr != nil && !rewardOfferResp.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to get cc offers, %w", rpcErr)
	}

	if len(rewardOfferResp.GetOffers()) == 0 {
		data.preApprovedOfferValue = moneyPkg.ZeroINR().GetPb()
	} else {
		data.preApprovedOfferValue = rewardOfferResp.GetOffers()[0].GetOfferConstraints().GetLimit()
	}

	if data.cardOnbReqStatus != ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
		data.primaryCtaDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CREDIT_CARD_SDK_SCREEN,
		}
	}

	return data, nil
}

type introScreenV2Data struct {
	cardProgram             *typesV2.CardProgram
	cardState               ccEnumsV2Pb.CardState // state of existing card, unspecified if no card is available for user
	cardOnbReqStatus        ccEnumsV2Pb.CardRequestStatus
	screenOptions           *genconf.CCIntroScreenV2Options
	preApprovedOfferValue   *gmoney.Money
	primaryCtaDeeplink      *deeplinkPb.Deeplink
	baseAnalyticsProperties map[string]string
}

type AnalyticsComponentContext struct {
	entryPoint string
	component  string
	index      string
}

// GetAnalyticsProperties combines base analytics properties with component-specific context
func (d *introScreenV2Data) GetAnalyticsProperties(context AnalyticsComponentContext) map[string]string {
	props := make(map[string]string)
	for k, v := range d.baseAnalyticsProperties {
		props[k] = v
	}

	// Add component context to properties
	if context.entryPoint != "" {
		props["entry_point"] = context.entryPoint
	}

	// Add component context to properties
	if context.component != "" {
		props["component"] = context.component
	}

	// Add component context to properties
	if context.index != "" {
		props["index"] = context.index
	}

	return props
}
