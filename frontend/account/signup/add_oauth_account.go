// nolint: goimports
package signup

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/frontend/app"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"

	beActorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	"github.com/epifi/gamma/api/comms"
	commsDeviceTokenPb "github.com/epifi/gamma/api/comms/device_token"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/account/signup"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	afuDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/afu"
	"github.com/epifi/gamma/api/typesv2/form"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vendorMappingPb "github.com/epifi/gamma/api/vendormapping"
	metrics2 "github.com/epifi/gamma/frontend/account/metrics"
	events2 "github.com/epifi/gamma/frontend/account/signup/events"
	"github.com/epifi/gamma/frontend/account/signup/metrics"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/events"
	pkgSourceAndIntent "github.com/epifi/gamma/pkg/acquisition/sourceandintent"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	pkgDeviceIntegrity "github.com/epifi/gamma/pkg/device_integrity"
	gammaepifigrpc "github.com/epifi/gamma/pkg/epifigrpc"
	"github.com/epifi/gamma/pkg/epifigrpc/interceptors/device_integrity"
	feErr "github.com/epifi/gamma/pkg/frontend/errors"
	header2 "github.com/epifi/gamma/pkg/frontend/header"
	"github.com/epifi/gamma/pkg/obfuscator"
	pkgSavings "github.com/epifi/gamma/pkg/savings"
)

var (
	// AddOAuthConfirmAFUStatus custom rpc status
	AddOAuthConfirmAFUStatus = rpc.NewStatus(
		uint32(signup.AddOAuthResponse_AFU_FLAG_REQUIRED), "Confirm Auth Factor Update", "",
	)

	addOAuthInvalidOAuthStatus = rpc.NewStatus(
		uint32(signup.AddOAuthResponse_INVALID_OAUTH_TOKEN), "Invalid OAuth token", "",
	)

	emailSilentLoginStatus = rpc.NewStatus(
		uint32(signup.LoginWithOAuthResponse_EMAIL_SILENT_LOGIN), "Email silent login", "",
	)

	// deeplinks
	actionForAccountCreationAfterKYC = &deeplinkPb.Deeplink{
		Screen:        deeplinkPb.Screen_CREATE_ACCOUNT,
		ScreenOptions: nil,
	}

	// errors
	errNewUser         = errors.New("new user in addoauth req")
	errResetNotAllowed = errors.New("reset not allowed")

	errCheckingDeviceIntegrity  = errors.New("error while checking device integrity")
	errIntegrityTokenAbsent     = errors.New("integrity token absent")
	errDeviceIntegrityRetryable = errors.New("device integrity retryable")

	errInvalidEmail = errors.New("invalid email string")
	// zap fields
	zapAddOAuth = zap.String("flow", "AddOAuth")
)

type AddOAuthAccountMetadata struct {
	ActorID      string
	ErrorCode    string
	ErrorMessage string
}

// getIsDeviceVerified checks the result of device integrity verification and returns if device is verified or not
func (s *Service) getIsDeviceVerified(ctx context.Context, actorId string) (bool, error) {
	deviceVerificationRes := device_integrity.GetDeviceVerificationResFromContext(ctx)
	switch deviceVerificationRes {
	case types.DeviceVerificationResult_DEVICE_VERIFICATION_RESULT_PASSED:
		return true, nil
	case types.DeviceVerificationResult_DEVICE_VERIFICATION_RESULT_HIGH_RISK:
		break
	default:
		return false, nil
	}
	if actorId == "" {
		return false, nil
	}
	// for a risk device, check if user has already given consent
	consentType := device_integrity.GetConsentTypeNeededForDevice(deviceVerificationRes)
	res, err := s.ConsentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
		ActorId:     actorId,
		ConsentType: consentType,
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		// if consent not found, return consent required error
		if res.GetStatus().IsRecordNotFound() {
			return false, device_integrity.CreateConsentNotFoundErr(consentType)
		}
		return false, fmt.Errorf("error in FetchConsent rpc: %w", rpcErr)
	}
	// check if consent has expired
	expiryTime := res.GetExpiresAt().AsTime()
	if !expiryTime.After(time.Now()) {
		return false, device_integrity.CreateConsentNotFoundErr(consentType)
	}
	return true, nil
}

//nolint:funlen,errorlint
func (s *Service) AddOAuthAccount(ctx context.Context, req *signup.AddOAuthRequest) (*signup.AddOAuthResponse, error) {
	authHeader := gammaepifigrpc.GetAuthHeader(req)
	device := authHeader.GetDevice()

	// add hashed deviceID to ctx for log mapping
	ctx = epificontext.CtxWithDeviceId(ctx, obfuscator.Hashed(device.GetDeviceId()))
	logWithAppDetails(ctx, "app details with device", req.GetReq(), zap.String(logger.APP_ID, req.GetAppId()), zap.String("SimIds", fmt.Sprintf("%v", req.GetDeviceMetadata().GetAndroidSimSubIds())))

	phoneNum, err := ValidateRefreshToken(ctx, s.AuthClient, authHeader.GetRefreshToken(), device)
	if err != nil {
		if st, ok := status.FromError(err); ok && st.Code() == codes.Unauthenticated {
			// hotfix: unhandled code in IOS
			if epificontext.AppPlatformFromContext(ctx) == commontypes.Platform_IOS {
				return nil, err
			}
			ce := feErr.NewClientError(feErr.REFRESH_TOKEN_EXPIRY)
			return &signup.AddOAuthResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusUnauthenticated(),
				},
				Status:     rpc.StatusUnauthenticated(),
				NextAction: clientErrorAsDeeplink(ce),
			}, nil
		}
		return &signup.AddOAuthResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in ValidateRefreshToken :%v", err)),
		}, nil
	}

	// add phone num token to ctx for logging
	ctx = epificontext.CtxWithPhoneNumToken(ctx, obfuscator.HashedPhoneNum(phoneNum))
	logWithAppDetails(ctx, "app details with  phone num token", req.GetReq(), zap.String(logger.APP_ID, req.GetAppId()), zap.String("SimIds", fmt.Sprintf("%v", req.GetDeviceMetadata().GetAndroidSimSubIds())),
		zap.String("flow", "addOAuth"))

	result, metadata, err := s.continueAddOAuthAccount(ctx, req, phoneNum, nil)
	if isLoginFailed(metadata.ErrorCode, getAddOAuthStatus(result.GetRespHeader(), result.GetStatus()), err) {
		s.storeAuthAttempt(ctx, phoneNum, result.GetRespHeader(), result.GetNextAction(), metadata)
		metrics.RecordAddOAuthErrorMetric(metrics.AddOAuth, metadata.ErrorCode, device.GetPlatform())
		return result, err
	}
	return result, nil
}

func getAddOAuthStatus(respHeader *header.ResponseHeader, status *rpc.Status) *rpc.Status {
	if respHeader.GetStatus() != nil {
		return respHeader.GetStatus()
	}
	return status
}

//nolint:funlen,errorlint
func (s *Service) continueAddOAuthAccount(ctx context.Context, req *signup.AddOAuthRequest, phoneNum *commontypes.PhoneNumber, loginWithOAuthReq *signup.LoginWithOAuthRequest) (res *signup.AddOAuthResponse, metadata *AddOAuthAccountMetadata, err error) {
	var (
		resMetadata                                                     = &AddOAuthAccountMetadata{}
		afuResult                                                       *authResult
		authHeader                                                      = gammaepifigrpc.GetAuthHeader(req)
		device                                                          = authHeader.GetDevice()
		actorToMinUserMap                                               = make(map[string]*user.MinimalUser)
		minUserFromPhoneNum, minUserFromEmail                           *user.MinimalUser
		actorIdFromPhoneNum, actorIdFromEmail, actorIdFromDevice, simId string
	)
	res = &signup.AddOAuthResponse{}
	res.Status = &rpc.Status{}
	res.UserProfile = &signup.UserProfile{}

	// validate the oAuth token
	// TODO(pruthvi): Store oAuthUserProfile
	oAuthUserProfile, rpcStatus := s.beValidateOAuthToken(ctx, req.GetOauthProvider(), req.GetOauthToken(), phoneNum.ToString(), authHeader.GetDevice())
	if !rpcStatus.IsSuccess() {
		switch rpcStatus.GetCode() {
		case uint32(auth.ValidateOAuthTokenResponse_GOOGLE_OIDC_TOKEN_EXPIRED):
			// No sign out option is shown to user in android if we send emailSilentLoginStatus, showing sign out option instead until client side handling is done
			if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConfig.Flags().ReturnRetryStatusForOIDCExpiry()) {
				return s.afuErrorResponse(ctx, req.GetReq(), "", feErr.NewClientError(feErr.INVALID_OAUTH_TOKEN))
			}
			return s.addOAuthErrorResponseWithMetadata(emailSilentLoginStatus, errors.New(emailSilentLoginStatus.GetShortMessage()), "")
		case uint32(auth.ValidateOAuthTokenResponse_APPLE_REFRESH_TOKEN_EXPIRED), uint32(auth.ValidateOAuthTokenResponse_APPLE_INVALID_OAUTH_LOGIN_REQUIRED):
			return s.addOAuthErrorResponseWithMetadata(addOAuthInvalidOAuthStatus, errors.New(addOAuthInvalidOAuthStatus.GetShortMessage()), "")
		case uint32(auth.ValidateOAuthTokenResponse_OAUTH_TOKEN_INVALID):
			// todo: (Vineet) need refactoring for methods returning errors
			return s.afuErrorResponse(ctx, req.GetReq(), "", feErr.NewClientError(feErr.INVALID_OAUTH_TOKEN))
		}
		return s.addOAuthErrorResponseWithMetadata(rpcStatus, errors.New(rpcStatus.GetDebugMessage()), "")
	}
	// get user registered details for ph & email & device
	if minUserFromPhoneNum, minUserFromEmail, actorIdFromDevice, simId, err = s.getUserDetailsFromAuthFactors(ctx, phoneNum, oAuthUserProfile.GetEmail(), device); err != nil {
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, "")
	}
	actorIdFromPhoneNum, actorIdFromEmail = minUserFromPhoneNum.GetActorId(), minUserFromEmail.GetActorId()

	// updating email for new web onboarding users
	// In web onboarding, user is created without capturing their email, so we update their profile details with
	// their personal email here when they sign up using the app
	if minUserFromPhoneNum != nil && minUserFromPhoneNum.GetProfileEssentials().GetEmail() == "" && minUserFromPhoneNum.GetAcquisitionPlatform() == commontypes.Platform_WEB {
		if webRes, webMetadata, webErr := s.updateEmailForWebFlow(ctx, minUserFromPhoneNum,
			oAuthUserProfile.GetEmail(), actorIdFromPhoneNum); webRes != nil {
			return webRes, webMetadata, webErr
		}
		minUserFromEmail = minUserFromPhoneNum
		actorIdFromEmail = actorIdFromPhoneNum
	}

	// update actorToUserMap
	if actorIdFromEmail != "" {
		actorToMinUserMap[actorIdFromEmail] = minUserFromEmail
	}
	if actorIdFromPhoneNum != "" {
		actorToMinUserMap[actorIdFromPhoneNum] = minUserFromPhoneNum
	}

	// trigger verify email event
	s.logVerifyEmailSuccessEvent(ctx, actorIdFromPhoneNum, actorIdFromEmail, actorIdFromDevice, device.GetDeviceId())

	// block users from performing AFU using invalid email domain
	if req.GetIsEmailUpdate() && s.isInvalidDomain(oAuthUserProfile.GetEmail()) {
		return s.getInvalidEmailDomainResult(ctx)
	}

	// evaluate the user identifiers and flags passed
	// processAuthRequest checks if it's a new user, or old user attempting AFU, or simple login.
	// And returns appropriate nextAction DL with details if available like actorId, afuId, revoke status
	afuResult, err = s.processAuthRequest(ctx, &AFURequest{
		ActorEmail:                 actorIdFromEmail,
		ActorPhone:                 actorIdFromPhoneNum,
		ActorDevice:                actorIdFromDevice,
		SimId:                      simId,
		AndroidSimSubIds:           req.GetDeviceMetadata().GetAndroidSimSubIds(),
		IsPhonePermissionGranted:   req.GetDeviceMetadata().GetIsPhonePermissionGranted(),
		Email:                      oAuthUserProfile.GetEmail(),
		Phone:                      phoneNum,
		Device:                     device,
		IsEmailUpdate:              req.GetIsEmailUpdate(),
		PhoneNumberUpdateSource:    req.GetPhoneNumberUpdateSource(),
		Pan:                        loginWithOAuthReq.GetUserInput().GetPan(),
		DeleteClosedAccountConsent: loginWithOAuthReq.GetConsentInfo().GetDeleteClosedAccountConsent(),
	}, actorToMinUserMap)
	deviceIntegrityToken := req.GetReq().GetAuth().GetDeviceIntegrity().GetDeviceIntegrityToken()
	if afuResult != nil {
		ctx = epificontext.CtxWithActorId(ctx, afuResult.ActorId)
		logWithAppDetails(ctx, "app details with actor", req.GetReq(), logger.ZapAFUId(afuResult.GetAfuId()), zap.String("SimIds", fmt.Sprintf("%v", req.GetDeviceMetadata().GetAndroidSimSubIds())))
	}
	var isNewUser, isDeviceVerified bool

	// checking device integrity status to validate for user action eligibility
	if s.isV2DeviceIntegrityVerifierEnabled(ctx, device.GetDeviceId(), req.GetReq().GetAuth().GetSafetyNetToken()) {
		nonce, errInt, errStatus := s.checkDeviceIntegrityResult(ctx, err, deviceIntegrityToken, device)
		// Client errors from device integrity can be treated as terminal errors
		if feErr.IsClientError(errInt) && s.genConfig.Signup().EnableDeviceIntegrityScreen() {
			return s.afuErrorResponse(ctx, req.GetReq(), afuResult.GetActorId(), errInt)
		}
		// returning early when checkDeviceIntegrityResult returns error Status
		if errStatus != nil && !s.canBypassDeviceIntegrity(phoneNum) {
			return addOAuthErrResponseWithNonce(nonce, errInt, errStatus)
		}
		// passing nonce when device integrity status is not eligible for user action
		if errInt != nil {
			res.DeviceIntegrityNonce = nonce
			resMetadata.ErrorMessage = errInt.Error()
		}
	}
	var (
		derivedActorID    string
		isNonResidentUser commontypes.BooleanEnum
	)
	switch {
	case err == nil:
		derivedActorID = afuResult.GetActorId()
		if afuResult.GetAfuId() == "" {
			// checking if user is non-resident by phone number only in case of non AFU
			// As in AFU, we may have a new number which will not help with identifying user type as non-resident
			isNonResidentUser, _ = s.isNonResidentByPhoneNumber(ctx, phoneNum)
		}

	case errors.Is(err, errNewUser):
		if s.isInvalidDomain(oAuthUserProfile.GetEmail()) {
			return s.getInvalidEmailDomainResult(ctx)
		}
		isNewUser = true
		if errSW := checkSignupDeviceOsApiVersion(ctx, s.genConfig.Signup(), device); errSW != nil && !errors.Is(errSW, epifierrors.ErrInvalidArgument) {
			return s.afuErrorResponse(ctx, req.GetReq(), "", errSW)
		}
		isNonResidentUser, err = s.isNonResidentByPhoneNumber(ctx, phoneNum)
		if err != nil {
			return nil, nil, err
		}
		if s.genConfig.Flags().BlockNrOnboarding() && isNonResidentUser.ToBool() {
			logger.Info(ctx, "non-resident user blocked from onboarding", zap.Any("country_code", phoneNum.GetCountryCode()))
			return s.afuErrorResponse(ctx, req.GetReq(), "", feErr.NewClientError(feErr.BLOCKED_NR_ONBOARDING))
		}

		// If new user, we expect device integrity to pass
		if s.genConfig.Application().EnableDeviceIntegrityCheck {
			// managing backward compatibility for device integrity check:
			if !s.isV2DeviceIntegrityVerifierEnabled(ctx, device.GetDeviceId(), req.GetReq().GetAuth().GetSafetyNetToken()) {
				isDeviceVerified, err = s.getIsDeviceVerified(ctx, "")
				if err != nil && !device_integrity.IsConsentNotFoundErr(err) {
					logger.Error(ctx, "error while checking device verification status", zap.Error(err))
					return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, "")
				}
				if !isDeviceVerified {
					logger.Info(ctx, "device verification check failed for new onboarding user")
					return nil, &AddOAuthAccountMetadata{
						ActorID:      derivedActorID,
						ErrorCode:    strconv.Itoa(int(codes.PermissionDenied)),
						ErrorMessage: device_integrity.DeviceVerificationRequiredErr.Error(),
					}, device_integrity.DeviceVerificationRequiredErr
				}
			}
		}

		// check and stop ongoing AFU attempts with the onboarding identifiers
		err = s.blockExistingAFUs(ctx, phoneNum, device.GetDeviceId(), oAuthUserProfile.GetEmail())
		if err != nil {
			return s.afuErrorResponse(ctx, req.GetReq(), derivedActorID, err)
		}

		acqInfo := s.getUserAcquisitionInfo(ctx, phoneNum, req.GetAcquisitionInfo())
		// create new user
		var newActor *types.Actor
		_, newActor, err = s.createUserAndActor(ctx, phoneNum, oAuthUserProfile.Email, req.GmailName, req.GetLegalName(), acqInfo)
		derivedActorID = newActor.GetId()
		ctx = epificontext.CtxWithActorId(ctx, newActor.GetId())
		if err != nil {
			return nil, &AddOAuthAccountMetadata{
				ActorID:      derivedActorID,
				ErrorMessage: err.Error(),
			}, err
		}
		// Storing appName before starting onboarding as it is used to decide stage order based on if user is normal or non-resident type
		if err = s.storeDeviceAppName(ctx, derivedActorID, req.GetReq().GetAppName()); err != nil {
			return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternalWithDebugMsg(err.Error()), err, derivedActorID)
		}

		// Storing pre-login consent recorded for new user
		if err = s.recordConsent(ctx, derivedActorID, device, loginWithOAuthReq.GetConsentInfo().GetConsentIdWithOwners()); err != nil {
			return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternalWithDebugMsg(err.Error()), err, derivedActorID)
		}

		if err = s.startOnboarding(ctx, derivedActorID); err != nil {
			logger.Error(ctx, "Error in starting onboarding", zap.Error(err), zap.String(logger.ACTOR_ID_V2, derivedActorID))
			return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, derivedActorID)
		}

	case errors.Is(err, errAFUEmailFlagReqd):
		popUp := deeplinkPb.AFUConfirmStartOptions_EMAIL_UPDATE_CONFIRMATION
		return s.confirmAFUPopUpResponse(ctx, minUserFromPhoneNum.GetProfileEssentials(), popUp, derivedActorID,
			oAuthUserProfile.GetEmail(), phoneNum)

	case errors.Is(err, errAFUPhoneNumFlagReqd):
		popUp := deeplinkPb.AFUConfirmStartOptions_PHONE_UPDATE_CONFIRMATION
		return s.confirmAFUPopUpResponse(ctx, minUserFromEmail.GetProfileEssentials(), popUp, derivedActorID,
			oAuthUserProfile.GetEmail(), phoneNum)

	default:
		return s.afuErrorResponse(ctx, req.GetReq(), derivedActorID, err)
	}
	ctx = epificontext.CtxWithActorId(ctx, derivedActorID) // for new user flow

	// copy user profile
	if err = copier.Copy(res.UserProfile, minUserFromEmail.GetProfileEssentials()); err != nil {
		logger.Error(ctx, "error in copying user profile", zap.Error(err))
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), errors.New("error in copy user profile"), derivedActorID)
	}
	res.UserProfile.ActorId = derivedActorID

	// check if device integrity checks have passed for an existing user.
	// By pass if `SkipDeviceIntegrityCheckForExistingActors` is set
	if s.genConfig.Application().EnableDeviceIntegrityCheck &&
		!s.genConfig.Application().SkipDeviceIntegrityCheckForExistingActors && !isNewUser {
		// managing backward compatibility for device integrity check:
		if !s.isV2DeviceIntegrityVerifierEnabled(ctx, device.GetDeviceId(), req.GetReq().GetAuth().GetSafetyNetToken()) {
			isDeviceVerified, err = s.getIsDeviceVerified(ctx, derivedActorID)
			if err != nil {
				if device_integrity.IsConsentNotFoundErr(err) {
					return nil, &AddOAuthAccountMetadata{
						ActorID:      derivedActorID,
						ErrorCode:    strconv.Itoa(int(codes.PermissionDenied)),
						ErrorMessage: err.Error(),
					}, status.Error(codes.PermissionDenied, err.Error())
				} else {
					return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, derivedActorID)
				}
			}
			if !isDeviceVerified {
				return nil, &AddOAuthAccountMetadata{
					ActorID:      derivedActorID,
					ErrorMessage: device_integrity.DeviceVerificationRequiredErr.Error(),
				}, device_integrity.DeviceVerificationRequiredErr
			}
		}
	}

	// Create access token
	e := oAuthUserProfile.GetEmail()
	afuId := afuResult.GetAfuId()
	var deviceReg bool
	// set app version from header as last known app version
	device.AppVersion = req.GetReq().GetAppVersionCode()
	res.AccessToken, deviceReg, err = s.newToken(ctx, device, derivedActorID, phoneNum, e, auth.TokenType_ACCESS_TOKEN, afuId, afuResult.GetAccessLevelFlags(), isNonResidentUser)
	if err != nil {
		logger.Error(ctx, "Error in creating access token", zap.Error(err))
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, derivedActorID)
	}

	// Get next action
	authHeader.ActorId = derivedActorID
	authHeader.IsDeviceRegistered = deviceReg
	res.NextAction, err = s.nextActionAfterAddOAuthAccount(ctx, authHeader, afuResult)
	if err != nil {
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, derivedActorID)
	}
	res.Status = rpc.StatusOk()
	res.RespHeader = header2.SuccessRespHeader()
	// account is accessible means all auth factors verified.
	// Client can access APIs that require device validation too.
	res.IsAccountAccessible = deviceReg
	if afuId != "" {
		res.IsProfileUpdate = true
	} else {
		appVersionInfo := &types.AppVersionInfo{
			Platform:       authHeader.GetDevice().GetPlatform(),
			AppVersionName: req.GetReq().GetAppVersionName(),
			AppVersionCode: req.GetReq().GetAppVersionCode(),
		}
		s.storeDevicePropsAsync(ctx, derivedActorID, req.GetDefaultDeviceLanguage(), req.GetReq().GetAuth().GetDevice(), appVersionInfo, req.GetDeviceMetadata(), req.GetReq().GetAppName())
	}
	resMetadata.ActorID = derivedActorID
	s.reconVendorIds(ctx, req.GetReq().GetProspectId(), derivedActorID)
	if s.genConfig.Flags().EnableRedListedAppsCheck() && len(req.GetDeviceMetadata().GetRedListedApps()) > 0 {
		logger.Info(ctx, "red listed apps found", zap.String(logger.ACTOR_ID_V2, derivedActorID))
		ce := feErr.NewClientError(feErr.RED_LISTED_APPS_FOUND, strings.Join(req.GetDeviceMetadata().GetRedListedApps(), ","))
		resMetadata = &AddOAuthAccountMetadata{
			ActorID:      derivedActorID,
			ErrorCode:    strconv.Itoa(int(ce.Code)),
			ErrorMessage: ce.Error(),
		}

		return &signup.AddOAuthResponse{
			Status:     feErr.ErrViewStatus,
			RespHeader: feErr.RespHeaderFromClientErr(ce.Code),
			NextAction: clientErrorAsDeeplink(ce),
		}, resMetadata, nil
	}
	return res, resMetadata, nil
}

func (s *Service) recordConsent(ctx context.Context, actorId string, device *commontypes.Device, consents []*form.ConsentIdWithOwner) error {
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConfig.Flags().AllowConsentCollectionOnLogin()) {
		return nil
	}
	logger.Info(ctx, "recording consent for new user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Int("consent_count", len(consents)))
	if len(consents) == 0 {
		logger.Error(ctx, "empty consent list send by client")
		return errors.New("empty consent list send by client")
	}

	consentOwnerToConsentInfo := make(map[commontypes.Owner][]*consentPb.ConsentRequestInfo)
	for _, consent := range consents {
		consentEnum := goutils.Enum(consent.GetConsentId(), consentPb.ConsentType_value, consentPb.ConsentType_ConsentType_UNSPECIFIED)
		consentOwner := goutils.Enum(consent.GetConsentOwner(), commontypes.Owner_value, commontypes.Owner_OWNER_UNSPECIFIED)
		if consentOwner == commontypes.Owner_OWNER_UNSPECIFIED || consentEnum == consentPb.ConsentType_ConsentType_UNSPECIFIED {
			logger.Error(ctx, "invalid consent owner or consent type", zap.String("consent_id", consent.GetConsentId()), zap.String("consent_owner", consent.GetConsentOwner()))
			return errors.New("invalid consent owner or consent type")
		}
		consentOwnerToConsentInfo[consentOwner] = append(consentOwnerToConsentInfo[consentOwner], &consentPb.ConsentRequestInfo{
			ConsentType: consentEnum,
			Provenance:  consentPb.ConsentProvenance_CONSENT_PROVENANCE_IN_APP,
		})
	}

	for owner, consentInfo := range consentOwnerToConsentInfo {
		res, err := s.ConsentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
			Consents: consentInfo,
			Device:   device,
			ActorId:  actorId,
			Owner:    owner,
		})
		if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
			logger.Error(ctx, "failed to record consents", zap.Error(rpcErr))
			return rpcErr
		}
	}

	// record whatsapp consent
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		s.recordWAConsent(ctx, actorId)
	})
	return nil
}

func (s *Service) recordWAConsent(ctx context.Context, actorId string) {
	// Adding whatsapp comms preference for new user
	res, err := s.userCommsPrefClient.CreatePreference(ctx, &upPb.CreatePreferenceRequest{
		ActorId:    actorId,
		Medium:     comms.Medium_WHATSAPP,
		Category:   comms.Category_CATEGORY_PROMOTIONAL,
		Preference: upPb.Preference_ON,
	})

	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "failed to create preference for category promotion", zap.Error(err))
		// Handling comms preference creation failure gracefully as it's a non-critical API in a critical funnel
	}

	res, err = s.userCommsPrefClient.CreatePreference(ctx, &upPb.CreatePreferenceRequest{
		ActorId:    actorId,
		Medium:     comms.Medium_WHATSAPP,
		Category:   comms.Category_CATEGORY_TRANSACTIONAL,
		Preference: upPb.Preference_ON,
	})

	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "failed to create preference for category transactional", zap.Error(err))
		// Handling comms preference creation failure gracefully as it's a non-critical API in a critical funnel
	}
}

// checkDeviceIntegrityResult checks device integrity result using the deviceIntegrityToken, and returns nonce
// for fresh validation of device integrity if required.
//
// For a new user if the device integrity needs to be re-done,
// it returns nonce, reason error and rpc.Status Permission Denied
//
// For an existing user, if the device integrity result is to be re-done,
// it returns nonce and a reason error without rpc.Status
//
// If device integrity doesn't need to be re-done, it returns nil
func (s *Service) checkDeviceIntegrityResult(ctx context.Context, authReqErr error, deviceIntegrityToken string, device *commontypes.Device) (nonce string, err error, errStatus *rpc.Status) {
	// emergency kill switch check for skipping device integrity checks
	if s.genConfig.DeviceIntegrity().SkipAsyncDeviceIntegrityChecks() {
		logger.Info(ctx, "emergency switch to skip device check is enabled, skipping device check in addOAuth")
		return "", nil, nil
	}

	var isDeviceIntegrityStatusEligible = true
	var isNewUser bool
	var errInt error
	// for new user:
	if errors.Is(authReqErr, errNewUser) {
		// check for integrity result from device integrity token
		isNewUser = true
		isDeviceIntegrityStatusEligible, errInt = s.isDeviceIntegrityStatusEligible(ctx, deviceIntegrityToken, device.GetDeviceId(), true)
	}
	if authReqErr == nil {
		isDeviceIntegrityStatusEligible, errInt = s.isDeviceIntegrityStatusEligible(ctx, deviceIntegrityToken, device.GetDeviceId(), false)
	}
	if !isDeviceIntegrityStatusEligible {
		nonceResp, errNon := s.genDeviceIntegrityNonce(ctx, device)
		if errNon != nil {
			logger.Error(ctx, "error while getting device integrity nonce", zap.Error(errNon))
			return "", errNon, rpc.StatusInternal()
		}
		if isNewUser {
			return nonceResp, errInt, rpc.StatusPermissionDenied()
		}
		return nonceResp, errInt, nil
	}
	return "", nil, nil
}

func (s *Service) isV2DeviceIntegrityVerifierEnabled(ctx context.Context, deviceId string, attestation string) bool {
	devIntegrityCfg := s.genConfig.DeviceIntegrity()
	platform := epificontext.AppPlatformFromContext(ctx)
	if platform == commontypes.Platform_ANDROID {
		return true
	}
	// always fallback to V1 if safety net token is not empty
	// client fetches version flag at the time of app open, this is to handle for those users where the flag has changed when app is still using V1
	if attestation != "" {
		logger.Debug(ctx, "safetynet token is non empty")
		return false
	}
	return (s.SafetyNetV2EnabledDeviceIds[deviceId] || app.IsFeatureRolledOut(deviceId, devIntegrityCfg.AsyncDeviceIntegrityRolloutPercentage())) &&
		apputils.IsFeatureEnabledFromCtxDynamic(ctx, devIntegrityCfg.AsyncDeviceIntegrityCheck())
}

func (s *Service) genDeviceIntegrityNonce(ctx context.Context, device *commontypes.Device) (string, error) {
	resp, err := s.AuthClient.GetDeviceIntegrityNonce(ctx, &auth.GetDeviceIntegrityNonceRequest{
		Device: device,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return "", err
	}
	return resp.GetNonce(), nil
}

func addOAuthErrResponseWithNonce(nonce string, errInt error, errStatus *rpc.Status) (*signup.AddOAuthResponse, *AddOAuthAccountMetadata, error) {
	return &signup.AddOAuthResponse{
			Status:               errStatus,
			DeviceIntegrityNonce: nonce,
		}, &AddOAuthAccountMetadata{
			ErrorMessage: errInt.Error(),
		}, nil
}

func (s *Service) isDeviceIntegrityStatusEligible(ctx context.Context, deviceIntegrityToken string, deviceId string, isNewUser bool) (isDeviceIntegrityResultEligible bool, err error) {
	// get integrity status for the device integrity token
	integrityStatus, err := s.getDeviceIntegrityStatus(ctx, deviceIntegrityToken, deviceId)
	if err != nil {
		logger.Error(ctx, "error in getting device integrity status", zap.Error(err))
		return false, errCheckingDeviceIntegrity
	}

	// Below logic handles all terminal device integrity results
	// interpret eligibility based on the user status and the integrity status
	if isNewUser {
		switch integrityStatus {
		case auth.Result_RESULT_PASSED:
			return true, nil
		default:
			logger.Info(ctx, "device integrity result does not meet eligibility for new user")
			return false, feErr.NewClientError(feErr.BLOCK_NEW_USER_DEVICE_INTEGRITY_FAILED)
		}
	} else {
		switch integrityStatus {
		case auth.Result_RESULT_PASSED, auth.Result_RESULT_PASSED_WITH_CONSENT:
			return true, nil
		default:
			logger.Info(ctx, "device integrity result does not meet eligibility for returning user")
			return false, feErr.NewClientError(feErr.BLOCK_EXISTNG_USER_DEVICE_INTEGRITY_FAILED)
		}
	}
}

func (s *Service) getDeviceIntegrityStatus(ctx context.Context, deviceIntegrityToken string, deviceId string) (auth.Result, error) {
	if deviceIntegrityToken == "" {
		return auth.Result_RESULT_UNSPECIFIED, errIntegrityTokenAbsent
	}
	logger.Debug(ctx, "device integrity token in GetStatus", zap.String(logger.TOKEN, deviceIntegrityToken), zap.String(logger.DEVICE_ID, deviceId))
	resp, errResp := s.AuthClient.GetDeviceIntegrityStatus(ctx, &auth.GetDeviceIntegrityStatusRequest{
		DeviceIntegrityToken: deviceIntegrityToken,
		DeviceId:             deviceId,
	})
	err := epifigrpc.RPCError(resp, errResp)
	result := resp.GetDeviceIntegrityStatus()
	switch {
	case resp.GetStatus().IsSuccess():
		result = resp.GetDeviceIntegrityStatus()
		err = nil
	case resp.GetStatus().GetCode() == uint32(auth.GetDeviceIntegrityStatusResponse_INTEGRITY_TOKEN_DEVICE_ID_MISMATCH),
		resp.GetStatus().GetCode() == uint32(auth.GetDeviceIntegrityStatusResponse_INTEGRITY_TOKEN_EXPIRED),
		resp.GetStatus().GetCode() == uint32(auth.GetDeviceIntegrityStatusResponse_INTEGRITY_TOKEN_INVALID):
		result = resp.GetDeviceIntegrityStatus()
		err = errDeviceIntegrityRetryable
	case err != nil:
		logger.Error(ctx, "error in fetching device integrity status", zap.Error(err))
		result = auth.Result_RESULT_UNSPECIFIED
		err = fmt.Errorf("unexpected device integrtiy status")
	}
	logger.Info(ctx, "result for device integrity check", zap.String(logger.RESULT, result.String()))
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		recordAddOAuthTimeTakenMetrics(ctx, s.genConfig.Secrets().Ids[config.ActiveDeviceIntegrityTokenSigningKey], deviceIntegrityToken)
	})
	return result, err
}

func recordAddOAuthTimeTakenMetrics(ctx context.Context, key, token string) {
	claims, err := pkgDeviceIntegrity.GetClaimsFromJWT(key, token)
	if err != nil {
		return
	}

	iatInterface := claims["iat"]
	if iatInterface == nil {
		return
	}
	iatVal, ok := iatInterface.(float64)
	if !ok {
		return
	}

	iatTime := time.Unix(int64(iatVal), 0)
	platform := epificontext.AppPlatformFromContext(ctx)
	metrics2.RecordAddOAuthTimeTaken(platform, iatTime)
}

// processAuthRequest encapsulates all the logic to evaluate the auth request
// parameters. It then calls AFU processor for auth factor update candidate requests.
//
//nolint:funlen,dupl
func (s *Service) processAuthRequest(ctx context.Context, req *AFURequest,
	actorToMinUserMap map[string]*user.MinimalUser) (*authResult, error) {
	var aUser *user.User
	// Deduce actor id from phone/email/device actor ids.
	// New user condition is also handled here where there is no actor id validated.
	actorId, err := s.validateActorId(ctx, req)
	if err != nil {
		logger.Info(ctx, "validate actor id action error", zap.Error(err), zapAddOAuth)
		return nil, err
	}
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	logger.Debug(ctx, fmt.Sprintf("process auth request received with phone: %v email: %v",
		req.Phone.GetNationalNumber(), req.Email),
		zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String("ActorPhone", req.ActorPhone),
		zap.String("ActorEmail", req.ActorEmail),
		zap.String("ActorDevice", req.ActorDevice),
		zap.String("phoneUpdate", req.PhoneNumberUpdateSource.String()),
		zap.Bool("emailUpdate", req.IsEmailUpdate),
		zap.Bool("onbDeviceReg", req.IsOnbDeviceRegistered),
		zap.Bool("onbAccountCreated", req.IsOnbAccountCreated),
		zap.Bool("onbCardCreated", req.IsOnbCardCreated),
		zap.Bool("onbCardPinSet", req.IsOnbAtmPinSet),
		zap.String("ActorSimId", req.SimId),
		zap.String("AndroidSimSubIds", fmt.Sprintf("%v", req.AndroidSimSubIds)),
	)
	minUserInfo, ok := actorToMinUserMap[actorId]
	if !ok {
		aUser, err = s.beGetUserByActorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error finding user for actor id: %s", actorId), zap.Error(err))
			return nil, err
		}
		minUserInfo = user.ToMinimalUser(aUser)
	}

	// Get onboarding status for device registration & card pin set.
	onbStatus, err := s.onbStatus(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in validate onb status", zap.Error(err), zapAddOAuth)
		return nil, err
	}
	/*
		We don't capture phone permissions in wealth onboarding for which we do not get AndroidSimSubIds in FetchAccessToken request
		Reordered existing checks as per below to allow users without giving phone permission
		Order of checks:
		1. Access revoke checks
		2. Resume onboarding for users whose device registration is not yet completed
		3. Empty SIM check with relevant changes to phone update source
		4. Old user with registered device
	*/
	if s.genConfig.Flags().EnableCheckForAccessRevoke() {
		// checking if app access is revoked for the user
		accessRevoked := isUserAccessRevoked(ctx, minUserInfo)
		// fetch access revoke error screen based on reason
		if accessRevoked {
			// fetching user for access revoke details
			aUser, err = s.beGetUserByActorId(ctx, actorId)
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("error finding user for actor id: %s", actorId), zap.Error(err))
				return nil, err
			}
			if afuResult, checkErr := s.checkClosedAccountReopenEligibility(ctx, req, onbStatus, actorId); checkErr == nil || errors.Is(checkErr, errNewUser) {
				return afuResult, checkErr
			}
			dl, errRevoke := s.getAccessRevokeErrorScreen(ctx, actorId, aUser.GetAccessRevokeDetails(), isAcctCloseBalTransferAllowed(req))
			if errRevoke != nil {
				return nil, errRevoke
			}
			return &authResult{
				ActorId:    actorId,
				NextAction: dl,
				AccessLevelFlags: &AccessLevelFlags{
					IsAccessRevoked:  true,
					IsAccountCreated: onbStatus.IsOnbAccountCreated,
				},
			}, nil
		}
	}

	// check for old user who has not completed the onboarding
	// device registration step. the user can resume onboarding.
	if isOldUnregisteredUser(req, onbStatus.IsOnbDeviceRegistered) {
		logger.Info(ctx, "old unregistered user", zapAddOAuth)
		return &authResult{
			ActorId: actorId,
			AccessLevelFlags: &AccessLevelFlags{
				IsAccessRevoked:  false,
				IsAccountCreated: onbStatus.IsOnbAccountCreated,
			},
		}, nil
	}
	if s.genConfig.AFU().AllowSimUpdateAFU() {
		platform, version := epificontext.AppPlatformAndVersion(ctx)
		if len(req.AndroidSimSubIds) == 0 && platform == commontypes.Platform_ANDROID {
			logger.Info(ctx, "user with empty android sim sub id",
				logger.ZapPlatformAndAppVersion(platform, version)...)
			if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConfig.Flags().EnablePhonePermissionCheck()) && !req.IsPhonePermissionGranted {
				logger.Info(ctx, "phone permission is not granted in auth", logger.ZapPlatformAndAppVersion(platform, version)...)
				return nil, rpc.StatusAsError(rpc.NewStatus(uint32(signup.LoginWithOAuthResponse_PHONE_PERMISSION_REQUIRED), "phone permission required", ""))
			}
			if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConfig.Flags().EnableSimID()) {
				logger.Info(ctx, "returning no sim inserted error screen", logger.ZapPlatformAndAppVersion(platform, version)...)
				return nil, feErr.NewClientError(feErr.AFU_NO_SIM_INSERTED)
			}
			if s.genConfig.AFU().EnableForceUpdateSimUpdate() {
				logger.Info(ctx, "forcing user through force update for empty sim id", logger.ZapPlatformAndAppVersion(platform, version)...)
				return &authResult{
					NextAction: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
					},
					ActorId: actorId,
					AccessLevelFlags: &AccessLevelFlags{
						IsAccountCreated: onbStatus.IsOnbAccountCreated,
					},
				}, nil
			}
		}
		req.PhoneNumberUpdateSource = s.validatePhoneNumberUpdateSource(ctx, req, actorId)
	}

	// Return if old user request for refreshing access token.
	// This will be the majority use case. Optimising with early return.
	if isOldRegisteredUser(req) {
		logger.Info(ctx, "old user with registered device", zapAddOAuth)
		return &authResult{
			ActorId: actorId,
			AccessLevelFlags: &AccessLevelFlags{
				IsAccessRevoked:  false,
				IsAccountCreated: onbStatus.IsOnbAccountCreated,
			},
		}, nil
	}

	activeProducts, err := s.getProductDetails(ctx, actorId)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "product details", zap.Any("activeProducts", activeProducts))
	isNonResidentUser, nrErr := s.isNonResidentUser(ctx, actorId)
	if nrErr != nil {
		return nil, nrErr
	}
	req.IsOnbDeviceRegistered = onbStatus.IsOnbDeviceRegistered
	req.IsOnbCustomerCreated = onbStatus.IsOnbCustomerCreated
	req.IsOnbAccountCreated = onbStatus.IsOnbAccountCreated
	req.IsOnbCardCreated = onbStatus.IsOnbCardCreated
	req.IsOnbAtmPinSet = onbStatus.IsOnbAtmPinSet
	req.CurrStage = onbStatus.CurrStage
	req.ActiveProducts = activeProducts
	req.IsNonResidentUser = isNonResidentUser

	res, err := s.processAFU(ctx, actorId, req)
	// fill access level flags
	if res != nil {
		res.AccessLevelFlags = &AccessLevelFlags{
			IsAccessRevoked:  false,
			IsAccountCreated: onbStatus.IsOnbAccountCreated,
		}
	}
	logger.Info(ctx, "recording afuID from processAFU", zap.String(logger.AFU_ID, res.GetAfuId()))
	return res, err
}

func isOldUnregisteredUser(req *AFURequest, userCompletedDeviceRegStep bool) bool {
	return req.ActorEmail != "" &&
		req.ActorEmail == req.ActorPhone &&
		req.ActorDevice == "" &&
		!userCompletedDeviceRegStep
}

func isOldRegisteredUser(req *AFURequest) bool {
	return !isUpdateFlagSet(req) &&
		req.ActorEmail != "" &&
		req.ActorEmail == req.ActorPhone &&
		req.ActorEmail == req.ActorDevice
}

// nextActionAfterAddOAuthAccount returns the next action to be taken after addOAuthAccount.
//  1. If any afu action is applicable, and returns that.
//  2. If none of the above is applicable, it returns Onboarding next action
func (s *Service) nextActionAfterAddOAuthAccount(ctx context.Context, auth *header.AuthHeader, authResult *authResult) (nextAction *deeplinkPb.Deeplink, err error) {
	var afuNextAction *deeplinkPb.Deeplink
	if authResult != nil && authResult.NextAction != nil {
		afuNextAction = authResult.NextAction
	}

	// Return if any afu action is applicable
	if afuNextAction != nil {
		logger.Info(ctx, fmt.Sprintf("afu action after addoauthaccount: %v", afuNextAction))
		return afuNextAction, nil
	}

	// if no afu action, return next onboarding action
	return s.getNextOnboardingAction(ctx, auth, onbPb.Feature_FEATURE_FI_LITE)
}

func (s *Service) getNextOnboardingAction(ctx context.Context, auth *header.AuthHeader, feature onbPb.Feature) (nextAction *deeplinkPb.Deeplink, err error) {
	nextActionRes, err := s.GetNextOnboardingAction(ctx, &signup.GetNextOnboardingActionRequest{
		Req: &header.RequestHeader{
			Auth: auth,
		},
		OnboardingFeature: feature.String(),
	})
	if err = epifigrpc.RPCError(nextActionRes, err); err != nil {
		logger.Error(ctx, "error in get next onboarding action", zap.Error(err))
		return nil, fmt.Errorf("error in GetNextOnboardingAction: %v", err)
	}
	return nextActionRes.GetNextAction(), nil
}

func (s *Service) getNextOnboardingActionV2(ctx context.Context, actorId string) (nextAction *deeplinkPb.Deeplink, err error) {
	nextActionRes, err := s.OnboardingClient.GetNextAction(ctx, &onbPb.GetNextActionRequest{
		ActorId:    actorId,
		ForceCache: commontypes.BooleanEnum_TRUE,
	})
	if err = epifigrpc.RPCError(nextActionRes, err); err != nil {
		logger.Error(ctx, "error in get next action", zap.Error(err))
		return nil, fmt.Errorf("error in GetNextAction: %w", err)
	}
	return nextActionRes.GetNextAction(), nil
}

// nolint:unparam
func (s *Service) createUserAndActor(ctx context.Context, phoneNum *commontypes.PhoneNumber, email string, gmailName, givenName *commontypes.Name, acquisitionInfo *user.AcquisitionInfo) (*user.User, *types.Actor, error) {
	var aUser *user.User
	// check if user already created
	userRes, err := s.UsersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_PhoneNumber{
			PhoneNumber: phoneNum,
		},
	})
	switch {
	case err != nil:
		return nil, nil, err

	case userRes.GetStatus().IsRecordNotFound():
		// user does not exist, we can go ahead with user creation

	case userRes.GetStatus().IsSuccess():
		logger.Info(ctx, fmt.Sprintf("user already exists, skipping user creation: %v", userRes.GetUser().GetId()))
		aUser = userRes.GetUser()

	default:
		return nil, nil, epifigrpc.RPCError(userRes, err)
	}

	// create user if not created
	if aUser == nil {
		if aUser, err = s.beCreateUser(ctx, phoneNum, email, gmailName, givenName, acquisitionInfo); err != nil {
			return nil, nil, err
		}
		logger.Info(ctx, fmt.Sprintf("created new user: %v", aUser.GetId()))
	}

	// create actor
	newActor, err := s.beCreateActor(ctx, aUser.Id, types.Actor_USER)
	if err != nil {
		return nil, nil, err
	}

	// update the created actorId in user table
	if err = s.updateActorIdInUser(ctx, aUser, newActor.GetId()); err != nil {
		return nil, nil, err
	}

	logger.Debug(ctx, fmt.Sprintf("created user with phone number %v", phoneNum.NationalNumber),
		zap.String(logger.ACTOR_ID_V2, newActor.Id), zap.String(logger.USER_ID, aUser.Id),
	)
	logger.Info(ctx, fmt.Sprintf("created new actor: %v", newActor.GetId()),
		zap.String(logger.ACTOR_ID_V2, newActor.GetId()), zap.String(logger.USER_ID, aUser.GetId()),
	)

	// temp long line for student program visibility
	if acquisitionInfo != nil {
		logger.Info(ctx, "actor created with acquisition info", zap.String(logger.ACTOR_ID_V2, newActor.GetId()),
			zap.String("acquisitionIntent", acquisitionInfo.GetAcquisitionIntent().String()),
			zap.String("acquisitionChannel", acquisitionInfo.GetAcquisitionChannel().String()))
	}

	return aUser, newActor, nil
}

func (s *Service) reconVendorIds(ctx context.Context, prospectIdFromClient string, actorId string) {
	if !s.genConfig.Flags().ReconVendorIds() {
		return
	}
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		if actorId == "" || prospectIdFromClient == "" {
			logger.Error(ctx, "reconVendorIds: actorId/prospectId is empty")
			return
		}
		// get vendor mapping entry by actorId
		vmByActorRes, err := s.vendorMappingClient.GetDPMappingById(ctx, &vendorMappingPb.GetDPMappingByIdRequest{
			Identifier: &vendorMappingPb.GetDPMappingByIdRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(vmByActorRes, err); rpcErr != nil {
			logger.Error(ctx, "reconVendorIds: error fetching vm by actor", zap.Error(rpcErr))
			return
		}

		if vmByActorRes.GetProspectId() == prospectIdFromClient {
			return
		}

		// get vendor mapping entry by prospect id from client
		vmByProspectRes, err := s.vendorMappingClient.GetDPMappingById(ctx, &vendorMappingPb.GetDPMappingByIdRequest{
			Identifier: &vendorMappingPb.GetDPMappingByIdRequest_ProspectId{
				ProspectId: prospectIdFromClient,
			},
		})
		if rpcErr := epifigrpc.RPCError(vmByProspectRes, err); rpcErr != nil {
			logger.Error(ctx, "reconVendorIds: error fetching vm by prospect", zap.Error(rpcErr))
			return
		}

		s.checkAndUpsertVendorIds(ctx, actorId, vmByActorRes, vmByProspectRes)
	})
}

func (s *Service) checkAndUpsertVendorIds(ctx context.Context, actorId string, vmByActor, vmByProspect *vendorMappingPb.GetDPMappingByIdResponse) {
	// check if vendor ids in the vendor mapping entry by actor is empty or not
	// and the corresponding entry for prospect is non-empty or not
	// if that is the case sync has happened for vendor ids with the prospect id but not with the actor id

	// currently the only vendor generated id is client af id
	// replace that
	if vmByActor.GetClientAppsflyerId() != "" {
		return
	}
	if vmByProspect.GetClientAppsflyerId() == "" {
		return
	}

	logger.Info(ctx, "checkAndUpsertVendorIds: upserting client af id for actor", zap.String(logger.CLIENT_APPSFLYER_ID, vmByProspect.GetClientAppsflyerId()))
	upsertRes, err := s.vendorMappingClient.UpsertClientAfId(ctx, &vendorMappingPb.UpsertClientAfIdRequest{
		ClientAfId: vmByProspect.GetClientAppsflyerId(),
		UpsertIdentifier: &vendorMappingPb.UpsertClientAfIdRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(upsertRes, err); rpcErr != nil {
		logger.Error(ctx, "checkAndUpsertVendorIds: error in upserting client af id", zap.Error(rpcErr))
		return
	}
	logger.Info(ctx, "checkAndUpsertVendorIds: successfully upserted client af id for actor", zap.String(logger.CLIENT_APPSFLYER_ID, vmByProspect.GetClientAppsflyerId()))
}

func (s *Service) updateActorIdInUser(ctx context.Context, u *user.User, actorId string) error {
	if u.GetActorId() != "" {
		return nil
	}
	u.ActorId = actorId
	uResp, uErr := s.UsersClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User: u,
		UpdateMask: []user.UserFieldMask{
			user.UserFieldMask_ACTOR_ID,
		},
	})
	if uErr = epifigrpc.RPCError(uResp, uErr); uErr != nil {
		logger.Error(ctx, "error updating actorId for user", zap.Error(uErr), zap.String(logger.ACTOR_ID_V2,
			u.GetActorId()))
		return uErr
	}
	return nil
}

func (s *Service) blockExistingAFUs(ctx context.Context, phone *commontypes.PhoneNumber, deviceId, email string) error {
	blockAFUsRequest := &auth.BlockExistingAuthFactorUpdatesRequest{
		PhoneNumber: phone,
		Email:       email,
		Device:      deviceId,
	}
	res, errResp := s.AuthClient.BlockExistingAuthFactorUpdates(ctx, blockAFUsRequest)
	switch res.GetStatus().GetCode() {
	case rpc.StatusOk().GetCode():
		return nil
	case rpc.StatusPermissionDenied().GetCode():
		return getOnboardingConflictClientError(res.GetAuthFactors())
	}
	if err := epifigrpc.RPCError(res, errResp); err != nil {
		logger.Error(ctx, "error in BlockExistingAuthFactorUpdates", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) getUserDetailsFromAuthFactors(ctx context.Context, ph *commontypes.PhoneNumber, email string,
	device *commontypes.Device) (mUserFromPh, mUserFromEmail *user.MinimalUser, actorIdFromDevice, simId string, err error) {
	g, gctx := errgroup.WithContext(ctx)

	// getting actorIdFromPhone
	g.Go(func() error {
		if mUserFromPh, err = s.getMinUserFromPhone(gctx, ph); err != nil {
			return fmt.Errorf("error in getting mUser for ph: %w", err)
		}
		return nil
	})

	// getting actorIdFromDevice and simId
	g.Go(func() error {
		if actorIdFromDevice, simId, err = s.registeredActorAndSimIdForDevice(gctx, device); err != nil {
			return fmt.Errorf("error in getting registered actor for device: %w", err)
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, nil, "", "", err
	}
	// early return if user for Ph has same email, saving redundant call
	if mUserFromPh.GetProfileEssentials().GetEmail() == email {
		return mUserFromPh, mUserFromPh, actorIdFromDevice, simId, nil
	}

	if mUserFromEmail, err = s.getMinUserFromEmail(ctx, email); err != nil {
		return nil, nil, "", "", fmt.Errorf("error in getting mUser for email: %w", err)
	}
	return mUserFromPh, mUserFromEmail, actorIdFromDevice, simId, nil
}

func (s *Service) getMinUserFromPhone(ctx context.Context, ph *commontypes.PhoneNumber) (mUser *user.MinimalUser, err error) {
	uResp, phErr := s.UsersClient.GetMinimalUser(ctx, &user.GetMinimalUserRequest{
		Identifier: &user.GetMinimalUserRequest_PhoneNumber{
			PhoneNumber: ph,
		},
	})
	if phErr = epifigrpc.RPCError(uResp, phErr); phErr != nil && !uResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error getting actor from ph", zap.Error(phErr))
		return nil, fmt.Errorf("error getting actor from ph: %w", phErr)
	}
	return uResp.GetMinimalUser(), nil
}

func (s *Service) getMinUserFromEmail(ctx context.Context, email string) (mUser *user.MinimalUser, err error) {
	uResp, eErr := s.UsersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_EmailId{
			EmailId: email,
		},
	})
	if eErr = epifigrpc.RPCError(uResp, eErr); eErr != nil && !uResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error getting actor from email", zap.Error(eErr))
		return nil, fmt.Errorf("error getting actor from email: %w", eErr)
	}
	return user.ToMinimalUser(uResp.GetUser()), nil
}

func (s *Service) beGetUserByActorId(ctx context.Context, actorId string) (*user.User, error) {
	resp, err := s.UsersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in get user by actor id", zap.Error(err))
		return nil, fmt.Errorf("error in invoking GetUser of User server: %w", err)
	}
	return resp.GetUser(), nil
}

func maskEmail(i string) (string, error) {
	s := strings.Split(i, "@")
	if len(s) != 2 {
		return "", errInvalidEmail
	}
	s[0] = mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, s[0])
	s[1] = mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, s[1])
	return strings.Join(s, "@"), nil
}

// confirmAFUPopUpResponse is a util method to return AddOAuthResponse with Pop up as next action.
// This response is sent when there is mismatch in phone & email. The users confirm if they want to
// actually start the update process for either of the auth factors.
//
//nolint:funlen
func (s *Service) confirmAFUPopUpResponse(ctx context.Context, profile *user.ProfileEssentials,
	popUp deeplinkPb.AFUConfirmStartOptions_PopUpType, actorId string, selectedEmail string,
	selectedPh *commontypes.PhoneNumber,
) (*signup.AddOAuthResponse, *AddOAuthAccountMetadata, error) {
	var (
		screenOptions *anyPb.Any
		err           error
		maskedEmail   string
		feProfile     = &signup.UserProfile{}
		warnMsg       string
		confirmUpdate string
	)
	if err := copier.Copy(feProfile, profile); err != nil {
		logger.Error(ctx, "error in copying user profiles", zap.Error(err))
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, actorId)
	}
	switch popUp {
	case deeplinkPb.AFUConfirmStartOptions_EMAIL_UPDATE_CONFIRMATION:
		maskedEmail, err = maskEmail(profile.GetEmail())
		if err != nil {
			logger.Error(ctx, "error in masking email", zap.Error(err))
			return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, actorId)
		}
		warnMsg = fmt.Sprintf("Are you sure you want to continue with <br>"+
			"<b>%v</b>?<br>"+
			"Your account is linked to another email id<br>"+
			"%s <br>"+
			"You won't be able to go back to your old email address or change it again for the next 7 days.", selectedEmail, maskedEmail)
		confirmUpdate = fmt.Sprintf(constants.ConfirmEmailUpdate, selectedEmail)

		screenOptions, err = deeplinkV3.GetScreenOptionV2(&afuDlPb.GetAfuWarningBottomUpSheet{
			PopUpType: afuDlPb.GetAfuWarningBottomUpSheet_EMAIL_UPDATE_CONFIRMATION,
			Title:     commontypes.GetPlainStringText("Update email on account?"),
			Subtitle:  commontypes.GetHtmlText(warnMsg),
			Ctas: []*deeplinkPb.Cta{
				{
					Text:         constants.SelectEmailAgain,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Type:         deeplinkPb.Cta_LOGOUT,
				},
				{
					Text:         confirmUpdate,
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
				},
			},
		})
	case deeplinkPb.AFUConfirmStartOptions_PHONE_UPDATE_CONFIRMATION:
		maskedPhone := mask.GetMaskedString(mask.DontMaskLastFourChars, strconv.FormatUint(profile.GetPhoneNumber().GetNationalNumber(), 10))

		warnMsg = fmt.Sprintf("Are you sure you want to continue with<br>"+
			"<b>%v</b>?<br>"+
			"Your account is linked to another phone number<br>"+
			"%s <br>"+
			"You won't be able to go back to your old number or change it again for the next 6 months. ", selectedPh.GetNationalNumber(), maskedPhone)
		confirmUpdate = fmt.Sprintf(constants.ConfirmPhoneUpdate, selectedPh.GetNationalNumber())

		screenOptions, err = deeplinkV3.GetScreenOptionV2(&afuDlPb.GetAfuWarningBottomUpSheet{
			PopUpType: afuDlPb.GetAfuWarningBottomUpSheet_PHONE_UPDATE_CONFIRMATION,
			// todo(Vineet): refactor copies
			Title:    commontypes.GetPlainStringText("Update phone number on account?"),
			Subtitle: commontypes.GetHtmlText(warnMsg),
			Ctas: []*deeplinkPb.Cta{
				{
					Text:         constants.SelectPhoneAgain,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Type:         deeplinkPb.Cta_LOGOUT,
				},
				{
					Text:         confirmUpdate,
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
				},
			},
		})
	}

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConfig.Flags().ReOOBECooldownBottomSheetScreenOptionsV2()) {
		screenOptions, err = s.getAFUScreenOptionsDetailsV2(ctx, profile, popUp, actorId, selectedEmail, selectedPh)
	}

	if err != nil || screenOptions == nil {
		logger.Error(ctx, "error creating screen options", zap.Error(err))
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, actorId)
	}

	return &signup.AddOAuthResponse{
			Status:      AddOAuthConfirmAFUStatus,
			UserProfile: feProfile,
			NextAction: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_AFU_CONFIRM_START_POPUP,
				ScreenOptionsV2: screenOptions,
				ScreenOptions: &deeplinkPb.Deeplink_AfuConfirmStart{
					AfuConfirmStart: &deeplinkPb.AFUConfirmStartOptions{
						PopUpType: popUp,
					},
				},
			},
		}, &AddOAuthAccountMetadata{
			ActorID: actorId,
		}, nil
}

func (s *Service) addOAuthErrorResponseWithMetadata(status *rpc.Status, err error, actorId string) (*signup.AddOAuthResponse, *AddOAuthAccountMetadata, error) {
	if feErr.IsClientError(err) {
		var clientErr *feErr.ClientError
		errors.As(err, &clientErr)
		return &signup.AddOAuthResponse{
				Status: status,
			},
			&AddOAuthAccountMetadata{
				ActorID:      actorId,
				ErrorCode:    strconv.Itoa(int(clientErr.Code)),
				ErrorMessage: clientErr.Error(),
			}, nil
	}
	return &signup.AddOAuthResponse{
			Status: status,
		},
		&AddOAuthAccountMetadata{
			ActorID:      actorId,
			ErrorCode:    constants.UnknownError,
			ErrorMessage: err.Error(),
		}, nil
}

func (s *Service) actorById(ctx context.Context, actorId string) (*types.Actor, error) {
	if actorId == "" {
		return nil, fmt.Errorf("empty actorId in GetActorById")
	}
	resp, err := s.ActorClient.GetActorById(ctx, &beActorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, fmt.Errorf("error in GetActorById: %v", err)
	}
	return resp.GetActor(), nil
}

// nolint: unparam
func (s *Service) afuErrorResponse(ctx context.Context, reqH *header.RequestHeader, actorId string, err error) (*signup.AddOAuthResponse, *AddOAuthAccountMetadata, error) {
	if feErr.IsClientError(err) {
		logger.Info(ctx, "afu client error in addoauth",
			zap.Error(err),
			zap.Uint32(logger.APP_VERSION_CODE, reqH.GetAuth().GetDevice().GetAppVersion()),
			zap.String(logger.APP_PLATFORM, reqH.GetAuth().GetDevice().GetPlatform().String()))

		var clientErr *feErr.ClientError
		errors.As(err, &clientErr)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events2.NewEventSignupError(actorId, clientErr.CodeStr))
		})
		metadata := &AddOAuthAccountMetadata{
			ActorID:      actorId,
			ErrorCode:    strconv.Itoa(int(clientErr.Code)),
			ErrorMessage: clientErr.Error(),
		}
		logger.Info(ctx, fmt.Sprintf("error view from AddOAuthAccount: %v (%v)", clientErr.Code, clientErr.Title))
		return &signup.AddOAuthResponse{
			Status:     feErr.ErrViewStatus,
			RespHeader: feErr.RespHeaderFromClientErr(clientErr.Code),
			NextAction: clientErrorAsDeeplink(clientErr),
		}, metadata, nil
	}
	logger.Error(ctx, "unexpected error in process afu", zap.Error(err))
	return s.addOAuthErrorResponseWithMetadata(rpc.StatusFromErrorWithDefaultInternal(err), err, actorId)
}

func (s *Service) startOnboarding(ctx context.Context, actorId string) error {
	res, err := s.OnboardingClient.StartOnboarding(ctx, &onbPb.StartOnboardingRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "error in StartOnboarding", zap.Error(err))
		return fmt.Errorf("error in StartOnboarding: %w", err)
	}

	return nil
}

func (s *Service) SignOut(ctx context.Context, req *signup.SignOutRequest) (*signup.SignOutResponse, error) {
	res := &signup.SignOutResponse{Status: rpc.StatusOk()}

	// Get actor
	actorId := req.GetReq().GetAuth().GetActorId()
	actor, err := s.getActor(ctx, actorId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return res, nil
		}
		logger.Error(ctx, "error in finding actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	authResponse, errResp := s.AuthClient.SignOut(ctx,
		&auth.SignOutRequest{Actor: actor})

	if err := epifigrpc.RPCError(authResponse, errResp); err != nil {
		logger.Error(ctx, "failed to sign out user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// In case a user signs out of the app, we need to de register the fcm device token at backend to
	// ensure that we do not hit firebase API for device tokens which are no longer valid if the user
	// has signed out.
	resp, err := s.deviceTokenClient.DeregisterFCMDeviceToken(ctx, &commsDeviceTokenPb.DeregisterFCMDeviceTokenRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		// since this error is just a side effect at our end and does not affect the user
		// we will not throw RPC error and just log it
		logger.Error(ctx, "error in de register fcm device token for actor", zap.Error(err))
	}
	res.Status = authResponse.Status
	return res, nil
}

func getOnboardingConflictClientError(list []afu.AuthFactor) error {
	const (
		phoneNumber = "phone number"
		email       = "email"
		device      = "device"
	)
	authFactors := make([]string, 0)
	for _, authFactor := range list {
		switch authFactor {
		case afu.AuthFactor_PHONE_NUM:
			authFactors = append(authFactors, phoneNumber)
		case afu.AuthFactor_EMAIL:
			authFactors = append(authFactors, email)
		case afu.AuthFactor_DEVICE:
			authFactors = append(authFactors, device)
		default:
			continue
		}
	}

	authFactorsString := strings.Join(authFactors, ", ")
	code := feErr.ONB_AUTH_FACTOR_CONFLICT_ERR

	e := feErr.NewClientError(code)
	return &feErr.ClientError{
		Code:        code,
		Title:       e.Title,
		Subtitle:    fmt.Sprintf(e.Subtitle, authFactorsString, authFactorsString),
		HasLogout:   e.HasLogout,
		ShowContact: e.ShowContact,
	}
}

func isUserAccessRevoked(ctx context.Context, userInfo *user.MinimalUser) bool {
	switch userInfo.GetAccessRevokeStatus() {
	case user.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED:
		logger.Info(ctx, "user blocked", zap.String(logger.USER_ID, userInfo.GetId()))
		return true
	default:
		return false
	}
}

func (s *Service) getAccessRevokeErrorScreen(ctx context.Context, actorId string, accessRevokedDetails *user.AccessRevokeDetails,
	isBalTransferAllowed bool) (*deeplinkPb.Deeplink, error) {
	reason := accessRevokedDetails.GetReason()
	switch {
	case reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST &&
		accessRevokedDetails.GetUpdatedBy() == "frontend.account.signup.ConfirmAccountClosure":
		return nil, feErr.NewClientError(feErr.ACCESS_REVOKED_ACCOUNT_CLOSED_IOS_MANDATE)
	case reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST:
		return nil, feErr.NewClientError(feErr.ACCESS_REVOKED_ACCOUNT_CLOSED)
	case reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY ||
		reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING ||
		reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED:
		if isBalTransferAllowed && app.IsFeatureEnabledFromCtx(ctx, s.genConfig.Signup().AcctClosureBalTransfer()) {
			dl, clientErr := pkgSavings.AcctClosureNextAction(ctx, actorId, reason, s.savingsClient, s.extacctClient)
			if clientErr != nil {
				return dl, clientErr
			}
			return dl, nil
		}
		return nil, feErr.NewClientError(feErr.ACCESS_REVOKED_MIN_KYC_ACCOUNT_EXPIRY)
	case reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_BLOCK_ONBOARDING:
		return nil, feErr.NewClientError(feErr.ACCESS_REVOKED_BLOCK_ONBOARDING)
	case reason == user.AccessRevokeReason_ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU:
		return getAccountFrozenForIncorrectUPIAttemptsScreen(), nil
	default:
		return s.getAccountFrozenScreen(ctx)
	}
}

func (s *Service) storeDevicePropsAsync(ctx context.Context, actorId string, defaultDeviceLang string, device *commontypes.Device, appVersionInfo *types.AppVersionInfo, deviceMetadata *signup.DeviceMetadata, appName commontypes.AppName) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		_ = s.storeDeviceProps(ctx, actorId, defaultDeviceLang, device, appVersionInfo, deviceMetadata, appName)
	})
}

func (s *Service) storeDeviceAppName(ctx context.Context, actorId string, appName commontypes.AppName) error {
	res, err := s.UsersClient.UpsertUserDeviceProperties(ctx, &user.UpsertUserDevicePropertyRequest{
		ActorId: actorId,
		DevicePropertyValueToUpdate: []*types.DevicePropertyKeyValuePair{
			{
				DeviceProperty: types.DeviceProperty_DEVICE_PROP_APP_NAME,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_AppName{
						AppName: appName,
					},
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "update dev prop: error in upserting device property value", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

//nolint:funlen
func (s *Service) storeDeviceProps(ctx context.Context, actorId string, defaultDeviceLang string, device *commontypes.Device, appVersionInfo *types.AppVersionInfo, deviceMetadata *signup.DeviceMetadata, appName commontypes.AppName) error {
	// properties to be stored
	props := []types.DeviceProperty{
		types.DeviceProperty_DEVICE_PROP_DEVICE_LANGUAGE,
		types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
		types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
		types.DeviceProperty_DEVICE_PROP_GOOGLE_ADVERTISING_ID,
		types.DeviceProperty_DEVICE_PROP_ANDROID_ID,
		types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN,
		types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
		types.DeviceProperty_DEVICE_PROP_APP_INSTALL_ID,
		types.DeviceProperty_DEVICE_PROP_MEMORY_INFO,
		types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		types.DeviceProperty_DEVICE_PROP_APP_NAME,
	}

	// validate data and add to pairs
	var pairs []*types.DevicePropertyKeyValuePair
	for _, prop := range props {
		switch prop {
		case types.DeviceProperty_DEVICE_PROP_APP_NAME:
			if appName == commontypes.AppName_APP_NAME_UNSPECIFIED {
				logger.Info(ctx, "update dev prop: empty or invalid appName")
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_AppName{
						AppName: appName,
					},
				},
			})
		case types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO:
			if appVersionInfo.GetAppVersionCode() == 0 || appVersionInfo.GetAppVersionName() == "" || appVersionInfo.GetPlatform() == commontypes.Platform_PLATFORM_UNSPECIFIED {
				logger.Info(ctx, "update dev prop: empty or invalid app version info")
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_AppVersionInfo{
						AppVersionInfo: &types.AppVersionInfo{
							Platform:       appVersionInfo.GetPlatform(),
							AppVersionName: appVersionInfo.GetAppVersionName(),
							AppVersionCode: appVersionInfo.GetAppVersionCode(),
						},
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_DEVICE_LANGUAGE:
			lang := strings.TrimSpace(deviceMetadata.GetDefaultDeviceLanguage())
			if lang == "" {
				lang = strings.TrimSpace(defaultDeviceLang)
			}
			if lang == "" {
				logger.Info(ctx, "update dev prop: default device language is empty")
				continue
			}
			languageEnum := LangCodeToEnumMap[strings.ToLower(lang)]
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_LANGUAGE,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_DeviceLanguage{
						DeviceLanguage: &types.LanguageInfo{
							Language: languageEnum,
							Raw:      lang,
						},
					},
				},
			})
		case types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO:
			if device.GetModel() == "" {
				logger.Info(ctx, "update dev prop: device model is empty")
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_DeviceModelInfo{
						DeviceModelInfo: &commontypes.DeviceModelInfo{
							Manufacturer: device.GetManufacturer(),
							Model:        device.GetModel(),
							HwVersion:    device.GetHwVersion(),
							SwVersion:    device.GetSwVersion(),
							OsApiVersion: device.GetOsApiVersion(),
							Platform:     device.GetPlatform(),
						},
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_GOOGLE_ADVERTISING_ID:
			if deviceMetadata.GetGoogleAdvertisingId() == "" {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_GoogleAdvertisingId{
						GoogleAdvertisingId: deviceMetadata.GetGoogleAdvertisingId(),
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_ANDROID_ID:
			if deviceMetadata.GetAndroidId() == "" {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_AndroidId{
						AndroidId: deviceMetadata.GetAndroidId(),
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN:
			ipToken, ipErr := obfuscator.GenIPAddressTokenFromCtx(ctx, s.obfuscatorClient)
			if ipErr != nil {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_IpAddressToken{
						IpAddressToken: ipToken,
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN:
			locToken := epificontext.DeviceLocationTokenFromContext(ctx)
			if locToken == "" || locToken == epificontext.UnknownToken ||
				// ignoring location token for zero coordinates (0, 0)
				locToken == "ec5077fc-e35b-4ef9-889c-14de55996a80" {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_LocationToken{
						LocationToken: locToken,
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_APP_INSTALL_ID:
			installId := deviceMetadata.GetInstallId()
			if installId == "" {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_AppInstallId{
						AppInstallId: installId,
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_MEMORY_INFO:
			if deviceMetadata.GetTotalRamInMb() == 0 && deviceMetadata.GetTotalInternalStorageInMb() == 0 {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_DeviceMemoryInfo{
						DeviceMemoryInfo: &types.DeviceMemoryInfo{
							TotalRamInMb:             deviceMetadata.GetTotalRamInMb(),
							TotalInternalStorageInMb: deviceMetadata.GetTotalInternalStorageInMb(),
						},
					},
				},
			})

		case types.DeviceProperty_DEVICE_PROP_DEVICE_ID:
			if strings.TrimSpace(device.GetDeviceId()) == "" {
				continue
			}
			pairs = append(pairs, &types.DevicePropertyKeyValuePair{
				DeviceProperty: prop,
				PropertyValue: &types.PropertyValue{
					PropValue: &types.PropertyValue_DeviceId{
						DeviceId: device.GetDeviceId(),
					},
				},
			})

		default:
			logger.Error(ctx, "update dev prop: unexpected property", zap.String(logger.STATE, prop.String()))
		}
	}

	if len(pairs) == 0 {
		logger.Info(ctx, "update dev prop: nothing to store")
		return nil
	}

	// storing properties
	resp, err := s.UsersClient.UpsertUserDeviceProperties(ctx, &user.UpsertUserDevicePropertyRequest{
		ActorId:                     actorId,
		DevicePropertyValueToUpdate: pairs,
	})
	if er := epifigrpc.RPCError(resp, err); er != nil {
		logger.Error(ctx, "update dev prop: error in upserting device property value", zap.Error(er))
		return er
	}
	return nil
}

func (s *Service) getAccountFrozenScreen(ctx context.Context) (*deeplinkPb.Deeplink, error) {
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	switch {
	// version check to send screenV2
	case app.IsFeatureEnabledFromCtx(ctx, s.genConfig.Signup().ShowAccountFrozenScreenV2()):
		return getAccountFrozenDeeplink(), nil
	case platform == commontypes.Platform_IOS:
		return nil, feErr.NewClientError(feErr.ACCESS_REVOKED_ACCOUNT_FREEZED_IOS)
		// case platform == commontypes.Platform_ANDROID:
	}
	return nil, feErr.NewClientError(feErr.ACCESS_REVOKED_ACCOUNT_FREEZED_ANDROID)
}

// registeredActorAndSimIdForDevice returns the actorID which is registered on the given device
func (s *Service) registeredActorAndSimIdForDevice(ctx context.Context, device *commontypes.Device) (string, string, error) {
	res, err := s.AuthClient.GetDeviceAuth(ctx, &auth.GetDeviceAuthRequest{Device: device})
	if err != nil {
		logger.Error(ctx, "error in get device auth", zap.Error(err))
		return "", "", fmt.Errorf("error in GetRegisteredDevice: %v", err)
	}
	if res.GetStatus().IsRecordNotFound() {
		return "", "", nil
	}
	if !res.GetStatus().IsSuccess() {
		logger.Error(ctx, fmt.Sprintf("non-ok status in get device auth: %v", res.GetStatus()))
		return "", "", fmt.Errorf("invalid response from RegisteredDevice: %v", res.GetStatus())
	}
	if res.DeviceRegStatus != auth.DeviceRegistrationStatus_REGISTERED {
		return "", "", nil
	}
	return res.GetActorId(), res.GetSimId(), nil
}

func getAccountFrozenForIncorrectUPIAttemptsScreen() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INSTRUCTIONS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_InstructionsScreenOptions{
			InstructionsScreenOptions: &deeplinkPb.InstructionsScreenOptions{
				Title: constants.AccountFrozenMainTitle,
				Instructions: []*deeplinkPb.Instruction{
					{
						Title:       constants.AccountFrozenMoneySafeTitle,
						Description: constants.AccountFrozenMoneySafeDescription,
						IconUrl:     constants.AccountFrozenMoneySafeImageUrl,
					},
					{
						Title:       "How do I unfreeze account?",
						Description: "You have exceeded your UPI pin attempts. Please wait for 24 hours and try again.",
						IconUrl:     constants.AccountFrozenUnfreezeImageUrl,
					},
				},
				ImageUrl: constants.AccountFrozenMainImageUrl,
			},
		},
	}
}

func getAccountFrozenDeeplink() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INSTRUCTIONS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_InstructionsScreenOptions{
			InstructionsScreenOptions: &deeplinkPb.InstructionsScreenOptions{
				Title: constants.AccountFrozenMainTitle,
				Instructions: []*deeplinkPb.Instruction{
					{
						Title:       constants.AccountFrozenMoneySafeTitle,
						Description: constants.AccountFrozenMoneySafeDescription,
						IconUrl:     constants.AccountFrozenMoneySafeImageUrl,
					},
					{
						Title:       constants.AccountFrozenUnfreezeTitle,
						Description: constants.AccountFrozenUnfreezeDescription,
						IconUrl:     constants.AccountFrozenUnfreezeImageUrl,
					},
					{
						Title:       constants.AccountFrozenEmailTitle,
						Description: constants.AccountFrozenEmailDescription,
						IconUrl:     constants.AccountFrozenEmailImageUrl,
					},
				},
				ImageUrl: constants.AccountFrozenMainImageUrl,
			},
		},
	}
}

// checkSignupDeviceOsApiVersion validates Android client's app version. It returns ClientError if the device
// has unsupported api version. As per NPCI regulations, we can't allow users below a certain api version.
func checkSignupDeviceOsApiVersion(ctx context.Context, cfg *genconf.Signup, d *commontypes.Device) error {
	// check if flag is on
	if !cfg.EnableAndroidAPIVersionCheckForSignup() {
		logger.Info(ctx, "android version check not enabled")
		return nil
	}

	// convert version from string to float
	osApiVersionFloat, err := strconv.ParseFloat(d.GetOsApiVersion(), 64)
	if err != nil {
		logger.Info(ctx, fmt.Sprintf("invalid sw version for android validation: '%s'", d.GetOsApiVersion()))
		return epifierrors.ErrInvalidArgument
	}

	// validate version & return result
	isInvalidVersion := d.GetPlatform() == commontypes.Platform_ANDROID && osApiVersionFloat < cfg.MinAndroidAPIVersionForSignup()
	if isInvalidVersion {
		return feErr.NewClientError(feErr.INVALID_ANDROID_VERSION)
	}
	return nil
}

func (s *Service) canBypassDeviceIntegrity(phoneNumber *commontypes.PhoneNumber) bool {

	if _, ok := lo.Find(s.genConfig.DeviceIntegrity().BypassPhoneNumbers().ToStringArray(), func(ph string) bool {
		return strings.EqualFold(ph, phoneNumber.ToString())
	}); ok {
		return true
	}
	return false
}

// getUserAcquisitionInfo returns the acquisition info by deriving it from multiple sources. Currently, the following are covered:
// 1. From B2B Salary Program user-group whitelisting
// 2. From Attribution details received from client, i.e. Appsflyer & Install Referral payloads
// nolint:funlen
func (s *Service) getUserAcquisitionInfo(ctx context.Context, userPhoneNum *commontypes.PhoneNumber, acqInfo *signup.AcquisitionInfo) *user.AcquisitionInfo {
	var (
		afOnConversionDataSuccessMap = acqInfo.GetAppsflyerOnConversionDataSuccessPayload().AsMap()
		installReferrerMap           = acqInfo.GetInstallReferrerPayload().AsMap()
		userPartOfSalaryB2BProgram   bool
		acquisitionInfo              *user.AcquisitionInfo
		identifiedSourceRaw          string
		identifiedIntentRaw          string
	)

	marshalledAfConversionData, err := json.Marshal(afOnConversionDataSuccessMap)
	if err != nil {
		logger.WarnWithCtx(ctx, "error json marshalling the afOnConversionDataSuccessMap", zap.Error(err))
	}
	marshalledInstallReferrerData, err := json.Marshal(installReferrerMap)
	if err != nil {
		logger.WarnWithCtx(ctx, "error json marshalling the installReferrerMap", zap.Error(err))
	}

	attributionDetails := &user.AttributionDetails{
		AppsflyerAttributionData: acqInfo.GetAppsflyerOnConversionDataSuccessPayload(),
		InstallReferrerData:      acqInfo.GetInstallReferrerPayload(),
	}

	/*
		Check if user is part of B2B Salary Program whitelisting.
		If yes, then consider the source & intent as B2B_SALARY_PROGRAM + Banking respectively
	*/
	b2bSalaryResp, err := s.UsersClient.GetB2BSalaryProgramVerificationStatus(ctx, &user.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &user.GetB2BSalaryProgramVerificationStatusRequest_PhoneNumber{PhoneNumber: userPhoneNum},
	})
	if rpcErr := epifigrpc.RPCError(b2bSalaryResp, err); rpcErr != nil {
		logger.Error(ctx, "error while checking for b2b salary verification status via phone number. not proceeding further", zap.Error(rpcErr))
	}

	userPartOfSalaryB2BProgram = b2bSalaryResp.GetIsVerified()

	if userPartOfSalaryB2BProgram {
		identifiedSourceRaw = user.AcquisitionChannel_B2B_SALARY_PROGRAM.String()
		identifiedIntentRaw = user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String()
	} else {
		/*
			Derive the source and intent from the attribution details received from the client
		*/
		acqSource, acqIntent := s.acqSourceAndIntentIdentifier.GetSourceAndIntentFromAttributionDetails(ctx, afOnConversionDataSuccessMap, installReferrerMap)
		identifiedSourceRaw = string(acqSource)
		identifiedIntentRaw = string(acqIntent)
	}

	s.logSourceAndIntentEvent(ctx, marshalledAfConversionData, marshalledInstallReferrerData, identifiedSourceRaw, identifiedIntentRaw, afOnConversionDataSuccessMap, installReferrerMap)

	if !s.genConfig.Flags().EnableAcqSourceAndIntentPropagation() {
		return nil
	}

	// assigning the raw values received by default to begin with
	acquisitionInfo = &user.AcquisitionInfo{
		AcquisitionSource:    identifiedSourceRaw,
		AcquisitionIntentRaw: identifiedIntentRaw,
		AttributionDetails:   attributionDetails,
	}

	// if the identified intent has its own mapping for enum, use that instead of the raw values received
	if acqIntentEnum, ok := pkgSourceAndIntent.AcquisitionIntentToEnumMap[identifiedIntentRaw]; ok {
		acquisitionInfo.AcquisitionIntent = acqIntentEnum
		acquisitionInfo.AcquisitionIntentRaw = acqIntentEnum.String()
	} else {
		logger.WarnWithCtx(ctx, "identified acquisition intent raw couldn't be mapped to enum", zap.String("acquisitionIntent", identifiedIntentRaw))
	}
	// if the identified source has its own mapping for enum, use that instead of the raw values received
	if acqSourceEnum, ok := pkgSourceAndIntent.AcquisitionSourceToEnumMap[identifiedSourceRaw]; ok {
		acquisitionInfo.AcquisitionChannel = acqSourceEnum
		acquisitionInfo.AcquisitionSource = acqSourceEnum.String()
	} else {
		logger.WarnWithCtx(ctx, "identified acquisition source raw couldn't be mapped to enum", zap.String("acquisitionSource", identifiedSourceRaw))
	}

	return acquisitionInfo
}

func (s *Service) logSourceAndIntentEvent(ctx context.Context, marshalledAfConversionData, marshalledInstallReferrerData []byte,
	identifiedSourceRaw, identifiedIntentRaw string,
	afOnConversionDataSuccessMap, installReferrerMap map[string]interface{}) {
	logger.Info(ctx, "acquisition intent and source during auth", zap.String("afOnConversionDataSuccessMap", string(marshalledAfConversionData)),
		zap.String("installReferrerMap", string(marshalledInstallReferrerData)), zap.String("acquisitionSource", identifiedSourceRaw),
		zap.String("acquisitionIntent", identifiedIntentRaw), zap.String(logger.PROSPECT_ID, epificontext.ProspectIdFromContext(ctx)),
	)

	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(
			epificontext.WithEventAttributes(ctx),
			events.NewIntentAndSourceIdentificationDuringAuth("", afOnConversionDataSuccessMap, installReferrerMap, identifiedSourceRaw, identifiedIntentRaw),
		)
	})
}

func (s *Service) updateEmailForWebFlow(ctx context.Context, userFromPhone *user.MinimalUser, email string, actorId string) (*signup.AddOAuthResponse, *AddOAuthAccountMetadata, error) {
	logger.Info(ctx, "updating email in AddOAuth flow for web users", zap.String(logger.USER_ID, userFromPhone.GetId()))
	if s.isInvalidDomain(email) {
		return s.getInvalidEmailDomainResult(ctx)
	}
	updateUserRes, err2 := s.UsersClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User: &user.User{
			Id: userFromPhone.GetId(),
			Profile: &user.Profile{
				Email: email,
			},
		},
		UpdateMask: []user.UserFieldMask{user.UserFieldMask_EMAIL},
	})

	if rpcErr := epifigrpc.RPCError(updateUserRes, err2); rpcErr != nil {
		logger.Error(ctx, "error while updating user with email", zap.Error(rpcErr))
		return s.addOAuthErrorResponseWithMetadata(rpc.StatusInternalWithDebugMsg(rpcErr.Error()), rpcErr, actorId)
	}
	return nil, nil, nil
}
