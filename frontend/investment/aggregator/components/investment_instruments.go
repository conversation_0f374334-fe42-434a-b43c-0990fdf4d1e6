package components

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"context"
	"fmt"
	"sort"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/investment/aggregator"
	"github.com/epifi/gamma/api/frontend/investment/aggregator/experiments"
	"github.com/epifi/gamma/api/frontend/investment/mutualfund/clientstates"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	beSvc "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	usStocksScreenOpts "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/screen_options_v2"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	InstrumentsTitle       = "Explore Investments & Savings"
	MFCardTitle            = "Mutual Funds"
	MFCardSubTitle         = "0 commissions"
	P2PCardTitle           = "Jump"
	P2PCardSubTitle        = "Up to 10% p.a."
	SDCardTitle            = "Smart deposit"
	SDCardSubTitle         = "Save flexibly"
	FDCardTitle            = "Fixed deposit"
	FDCardSubTitle         = "7.00% p.a. returns"
	USStocksCardSubTitle   = "0 Commissions"
	USFundsCardTitle       = "US Funds"
	USFundsCardSubTitle    = "Smart diversification"
	USStocksCardSubTitleV2 = "Global exposure"

	USStocksIconURL = "https://epifi-icons.pointz.in/investments/landing/us_stocks_color.png"
	SDIconURL       = "https://epifi-icons.pointz.in/investments/landing/sd_color.png"
	FDIconURL       = "https://epifi-icons.pointz.in/investments/landing/fd_color.png"
	MFIconURL       = "https://epifi-icons.pointz.in/investments/landing/mutual_funds_color.png"
	P2PIconURL      = "https://epifi-icons.pointz.in/investments/landing/p2p_color.png"

	USStocksLottieString = "{\"v\":\"4.8.0\",\"meta\":{\"g\":\"LottieFiles AE 3.3.6\",\"a\":\"\",\"k\":\"\",\"d\":\"\",\"tc\":\"\"},\"fr\":30,\"ip\":0,\"op\":150,\"w\":32,\"h\":32,\"nm\":\"US\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":1,\"h\":1,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC\",\"e\":1}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":2,\"nm\":\"US\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[15.5,15.5,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":4,\"nm\":\"Layer 30 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.4],\"y\":[1]},\"o\":{\"x\":[0.45],\"y\":[0]},\"t\":4,\"s\":[0]},{\"i\":{\"x\":[0.15],\"y\":[1]},\"o\":{\"x\":[0.45],\"y\":[0]},\"t\":10.34,\"s\":[-41.76]},{\"t\":24,\"s\":[360]}],\"ix\":10},\"p\":{\"a\":0,\"k\":[0.125,0.469,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[5.125,8.969,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.67,0.67,0.67],\"y\":[1,1,1]},\"o\":{\"x\":[0.557,0.557,0.557],\"y\":[0,0,0]},\"t\":4,\"s\":[0,0,100]},{\"i\":{\"x\":[0.15,0.15,0.15],\"y\":[1,1,1]},\"o\":{\"x\":[0.315,0.315,0.315],\"y\":[0,0,0]},\"t\":8.7,\"s\":[-6.7,-6.7,100]},{\"t\":24,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,-0.673],[0,0],[0,0],[0,-0.673],[0.674,0],[0,0],[0,-0.785],[-0.786,0],[0,0],[0,-2.132],[2.037,-0.106],[0,0],[0.673,0],[0,0.673],[0,0],[0,0],[0,0.673],[-0.673,0],[0,0],[0,0.785],[0.785,0],[0,0],[0,2.132],[-2.037,0.106],[0,0],[-0.673,0]],\"o\":[[0,0],[0,0],[0.674,0],[0,0.673],[0,0],[-0.786,0],[0,0.786],[0,0],[2.131,0],[0,2.063],[0,0],[0,0.673],[-0.673,0],[0,0],[0,0],[-0.673,0],[0,-0.673],[0,0],[0.785,0],[0,-0.786],[0,0],[-2.132,0],[0,-2.063],[0,0],[0,-0.673],[0.673,0]],\"v\":[[1.219,-7.5],[1.219,-6.5],[3.656,-6.5],[4.875,-5.281],[3.656,-4.062],[-1.015,-4.062],[-2.437,-2.641],[-1.015,-1.219],[1.016,-1.219],[4.875,2.641],[1.219,6.495],[1.219,7.5],[0,8.719],[-1.219,7.5],[-1.219,6.5],[-3.656,6.5],[-4.875,5.281],[-3.656,4.062],[1.016,4.062],[2.438,2.641],[1.016,1.219],[-1.015,1.219],[-4.875,-2.641],[-1.219,-6.495],[-1.219,-7.5],[0,-8.719]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.381590809542,0.494215841854,0.69743197572,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[5.125,8.969],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":4,\"op\":154.15015015015,\"st\":4,\"bm\":0},{\"ddd\":0,\"ind\":5,\"ty\":4,\"nm\":\"Layer 29 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[0.25,0.25,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.25,13.25,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.67,0.67,0.67],\"y\":[1,1,1]},\"o\":{\"x\":[0.557,0.557,0.557],\"y\":[0,0,0]},\"t\":0,\"s\":[0,0,100]},{\"i\":{\"x\":[0.15,0.15,0.15],\"y\":[1,1,1]},\"o\":{\"x\":[0.315,0.315,0.315],\"y\":[0,0,0]},\"t\":4.7,\"s\":[-6.7,-6.7,100]},{\"t\":20,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,5.834],[-5.834,0],[0,-5.834],[5.833,0]],\"o\":[[0,-5.834],[5.833,0],[0,5.834],[-5.834,0]],\"v\":[[-10.562,0],[0.001,-10.562],[10.563,0],[0.001,10.562]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ind\":1,\"ty\":\"sh\",\"ix\":2,\"ks\":{\"a\":0,\"k\":{\"i\":[[7.179,0],[0,-7.18],[-7.18,0],[0,7.18]],\"o\":[[-7.18,0],[0,7.18],[7.179,0],[0,-7.18]],\"v\":[[0.001,-13],[-13,0],[0.001,13],[13,0]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.381590809542,0.494215841854,0.69743197572,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.25,13.25],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":4,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0}],\"markers\":[]}"
	SDLottieString       = "{\"v\":\"4.8.0\",\"meta\":{\"g\":\"LottieFiles AE 3.3.6\",\"a\":\"\",\"k\":\"\",\"d\":\"\",\"tc\":\"\"},\"fr\":30,\"ip\":0,\"op\":150,\"w\":32,\"h\":32,\"nm\":\"SD\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":1,\"h\":1,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC\",\"e\":1}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":2,\"nm\":\"SD\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[15.5,15.5,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"ip\":0,\"op\":150,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":4,\"nm\":\"Layer 7 Outlines 4\",\"parent\":8,\"td\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.179,0.178],[0.252,0],[0,0],[0.693,1.032],[0.875,0.567],[2.138,0.157],[0.248,0.01],[1.279,-0.373],[0.049,-0.018],[1.313,0.325],[0.208,-0.12],[0.071,-0.23],[-0.01,-1.392],[0.319,-0.937],[0,0],[0.147,-0.175],[-0.003,-0.228],[0,0],[-0.116,-0.162],[-0.189,-0.064],[0,0],[-0.388,-0.596],[0,0],[-0.13,-0.485],[-0.434,-0.252],[0,0],[-0.335,0],[-0.161,0.045],[-0.201,0.15],[-0.128,0.216],[0,0],[-1.155,0.005],[0,0],[-1.29,0.627],[0,0],[-0.199,-0.154],[-0.243,-0.066],[-0.25,0.034],[-0.217,0.128],[0,0],[-0.153,0.199],[-0.065,0.242],[0.033,0.249],[0.127,0.217],[0,0],[-0.006,1.571],[0,0],[-0.179,0.178],[0,0.252]],\"o\":[[-0.179,-0.179],[0,0],[-0.244,-1.219],[-0.577,-0.859],[-1.823,-1.182],[-0.248,-0.019],[-1.331,-0.052],[-0.051,0.015],[-1.167,-0.684],[-0.235,-0.053],[-0.209,0.12],[-0.394,1.334],[-0.595,0.791],[0,0],[-0.225,0.037],[-0.146,0.174],[0,0],[0,0.2],[0.116,0.162],[0,0],[0.244,0.668],[0,0],[-0.249,0.436],[0.13,0.485],[0,0],[0.289,0.168],[0.167,0.002],[0.244,-0.062],[0.201,-0.151],[0,0],[1.079,0.414],[0,0],[1.434,0.001],[0,0],[0.124,0.219],[0.2,0.155],[0.244,0.065],[0.25,-0.033],[0,0],[0.218,-0.126],[0.153,-0.199],[0.065,-0.242],[-0.033,-0.248],[0,0],[0.744,-1.385],[0,0],[0.252,0],[0.179,-0.179],[0,-0.253]],\"v\":[[13.049,-1.613],[12.376,-1.892],[11.215,-1.892],[9.796,-5.302],[7.67,-7.501],[1.488,-9.295],[0.743,-9.339],[-3.215,-8.923],[-3.443,-8.843],[-7.812,-10.459],[-8.502,-10.356],[-8.937,-9.811],[-9.517,-5.699],[-10.896,-3.091],[-12.525,-2.844],[-13.102,-2.516],[-13.325,-1.892],[-13.325,1.915],[-13.146,2.472],[-12.676,2.82],[-10.773,3.448],[-9.822,5.351],[-10.564,6.674],[-10.749,8.112],[-9.868,9.263],[-8.251,10.215],[-7.299,10.472],[-6.805,10.406],[-6.132,10.085],[-5.633,9.53],[-5.282,8.911],[-1.903,9.53],[1.906,9.53],[6.046,8.578],[6.589,9.53],[7.079,10.096],[7.749,10.43],[8.497,10.478],[9.206,10.234],[10.824,9.283],[11.385,8.791],[11.714,8.123],[11.76,7.379],[11.519,6.674],[10.282,4.514],[11.424,0.012],[12.376,0.012],[13.049,-0.267],[13.327,-0.94]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.577,10.762],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 2\",\"np\":5,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":38,\"op\":177,\"st\":27,\"bm\":0},{\"ddd\":0,\"ind\":3,\"ty\":4,\"nm\":\"Layer 6 Outlines 3\",\"parent\":1,\"tt\":2,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.1,\"y\":1},\"o\":{\"x\":0.3,\"y\":0},\"t\":38,\"s\":[2.103,-13.34,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":50,\"s\":[2.103,-6.971,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[6.424,6.424,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.1,0.1,0.1],\"y\":[1,1,1]},\"o\":{\"x\":[0.3,0.3,0.3],\"y\":[0,0,0]},\"t\":38,\"s\":[20,20,100]},{\"t\":50,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-1.622,0],[-1.006,0.672],[-0.463,1.117],[0.236,1.186],[0.855,0.855],[1.186,0.236],[1.117,-0.463],[0.672,-1.005],[0,-1.209],[-1.147,-1.146]],\"o\":[[1.209,0],[1.005,-0.672],[0.463,-1.117],[-0.236,-1.186],[-0.856,-0.856],[-1.186,-0.236],[-1.117,0.463],[-0.672,1.006],[0,1.622],[1.146,1.147]],\"v\":[[-0.06,6.174],[3.338,5.144],[5.59,2.4],[5.938,-1.133],[4.264,-4.264],[1.133,-5.938],[-2.399,-5.59],[-5.144,-3.338],[-6.174,0.059],[-4.383,4.383]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[6.424,6.424],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":38,\"op\":177,\"st\":27,\"bm\":0},{\"ddd\":0,\"ind\":4,\"ty\":4,\"nm\":\"Layer 7 Outlines 3\",\"parent\":8,\"td\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.179,0.178],[0.252,0],[0,0],[0.693,1.032],[0.875,0.567],[2.138,0.157],[0.248,0.01],[1.279,-0.373],[0.049,-0.018],[1.313,0.325],[0.208,-0.12],[0.071,-0.23],[-0.01,-1.392],[0.319,-0.937],[0,0],[0.147,-0.175],[-0.003,-0.228],[0,0],[-0.116,-0.162],[-0.189,-0.064],[0,0],[-0.388,-0.596],[0,0],[-0.13,-0.485],[-0.434,-0.252],[0,0],[-0.335,0],[-0.161,0.045],[-0.201,0.15],[-0.128,0.216],[0,0],[-1.155,0.005],[0,0],[-1.29,0.627],[0,0],[-0.199,-0.154],[-0.243,-0.066],[-0.25,0.034],[-0.217,0.128],[0,0],[-0.153,0.199],[-0.065,0.242],[0.033,0.249],[0.127,0.217],[0,0],[-0.006,1.571],[0,0],[-0.179,0.178],[0,0.252]],\"o\":[[-0.179,-0.179],[0,0],[-0.244,-1.219],[-0.577,-0.859],[-1.823,-1.182],[-0.248,-0.019],[-1.331,-0.052],[-0.051,0.015],[-1.167,-0.684],[-0.235,-0.053],[-0.209,0.12],[-0.394,1.334],[-0.595,0.791],[0,0],[-0.225,0.037],[-0.146,0.174],[0,0],[0,0.2],[0.116,0.162],[0,0],[0.244,0.668],[0,0],[-0.249,0.436],[0.13,0.485],[0,0],[0.289,0.168],[0.167,0.002],[0.244,-0.062],[0.201,-0.151],[0,0],[1.079,0.414],[0,0],[1.434,0.001],[0,0],[0.124,0.219],[0.2,0.155],[0.244,0.065],[0.25,-0.033],[0,0],[0.218,-0.126],[0.153,-0.199],[0.065,-0.242],[-0.033,-0.248],[0,0],[0.744,-1.385],[0,0],[0.252,0],[0.179,-0.179],[0,-0.253]],\"v\":[[13.049,-1.613],[12.376,-1.892],[11.215,-1.892],[9.796,-5.302],[7.67,-7.501],[1.488,-9.295],[0.743,-9.339],[-3.215,-8.923],[-3.443,-8.843],[-7.812,-10.459],[-8.502,-10.356],[-8.937,-9.811],[-9.517,-5.699],[-10.896,-3.091],[-12.525,-2.844],[-13.102,-2.516],[-13.325,-1.892],[-13.325,1.915],[-13.146,2.472],[-12.676,2.82],[-10.773,3.448],[-9.822,5.351],[-10.564,6.674],[-10.749,8.112],[-9.868,9.263],[-8.251,10.215],[-7.299,10.472],[-6.805,10.406],[-6.132,10.085],[-5.633,9.53],[-5.282,8.911],[-1.903,9.53],[1.906,9.53],[6.046,8.578],[6.589,9.53],[7.079,10.096],[7.749,10.43],[8.497,10.478],[9.206,10.234],[10.824,9.283],[11.385,8.791],[11.714,8.123],[11.76,7.379],[11.519,6.674],[10.282,4.514],[11.424,0.012],[12.376,0.012],[13.049,-0.267],[13.327,-0.94]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.577,10.762],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 2\",\"np\":5,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":26,\"op\":165,\"st\":15,\"bm\":0},{\"ddd\":0,\"ind\":5,\"ty\":4,\"nm\":\"Layer 6 Outlines 2\",\"parent\":1,\"tt\":2,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.1,\"y\":1},\"o\":{\"x\":0.3,\"y\":0},\"t\":26,\"s\":[2.103,-13.34,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":41,\"s\":[2.103,3.029,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[6.424,6.424,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.1,0.1,0.1],\"y\":[1,1,1]},\"o\":{\"x\":[0.3,0.3,0.3],\"y\":[0,0,0]},\"t\":26,\"s\":[20,20,100]},{\"t\":41,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-1.622,0],[-1.006,0.672],[-0.463,1.117],[0.236,1.186],[0.855,0.855],[1.186,0.236],[1.117,-0.463],[0.672,-1.005],[0,-1.209],[-1.147,-1.146]],\"o\":[[1.209,0],[1.005,-0.672],[0.463,-1.117],[-0.236,-1.186],[-0.856,-0.856],[-1.186,-0.236],[-1.117,0.463],[-0.672,1.006],[0,1.622],[1.146,1.147]],\"v\":[[-0.06,6.174],[3.338,5.144],[5.59,2.4],[5.938,-1.133],[4.264,-4.264],[1.133,-5.938],[-2.399,-5.59],[-5.144,-3.338],[-6.174,0.059],[-4.383,4.383]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[6.424,6.424],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":26,\"op\":165,\"st\":15,\"bm\":0},{\"ddd\":0,\"ind\":6,\"ty\":4,\"nm\":\"Layer 7 Outlines 2\",\"parent\":8,\"td\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.179,0.178],[0.252,0],[0,0],[0.693,1.032],[0.875,0.567],[2.138,0.157],[0.248,0.01],[1.279,-0.373],[0.049,-0.018],[1.313,0.325],[0.208,-0.12],[0.071,-0.23],[-0.01,-1.392],[0.319,-0.937],[0,0],[0.147,-0.175],[-0.003,-0.228],[0,0],[-0.116,-0.162],[-0.189,-0.064],[0,0],[-0.388,-0.596],[0,0],[-0.13,-0.485],[-0.434,-0.252],[0,0],[-0.335,0],[-0.161,0.045],[-0.201,0.15],[-0.128,0.216],[0,0],[-1.155,0.005],[0,0],[-1.29,0.627],[0,0],[-0.199,-0.154],[-0.243,-0.066],[-0.25,0.034],[-0.217,0.128],[0,0],[-0.153,0.199],[-0.065,0.242],[0.033,0.249],[0.127,0.217],[0,0],[-0.006,1.571],[0,0],[-0.179,0.178],[0,0.252]],\"o\":[[-0.179,-0.179],[0,0],[-0.244,-1.219],[-0.577,-0.859],[-1.823,-1.182],[-0.248,-0.019],[-1.331,-0.052],[-0.051,0.015],[-1.167,-0.684],[-0.235,-0.053],[-0.209,0.12],[-0.394,1.334],[-0.595,0.791],[0,0],[-0.225,0.037],[-0.146,0.174],[0,0],[0,0.2],[0.116,0.162],[0,0],[0.244,0.668],[0,0],[-0.249,0.436],[0.13,0.485],[0,0],[0.289,0.168],[0.167,0.002],[0.244,-0.062],[0.201,-0.151],[0,0],[1.079,0.414],[0,0],[1.434,0.001],[0,0],[0.124,0.219],[0.2,0.155],[0.244,0.065],[0.25,-0.033],[0,0],[0.218,-0.126],[0.153,-0.199],[0.065,-0.242],[-0.033,-0.248],[0,0],[0.744,-1.385],[0,0],[0.252,0],[0.179,-0.179],[0,-0.253]],\"v\":[[13.049,-1.613],[12.376,-1.892],[11.215,-1.892],[9.796,-5.302],[7.67,-7.501],[1.488,-9.295],[0.743,-9.339],[-3.215,-8.923],[-3.443,-8.843],[-7.812,-10.459],[-8.502,-10.356],[-8.937,-9.811],[-9.517,-5.699],[-10.896,-3.091],[-12.525,-2.844],[-13.102,-2.516],[-13.325,-1.892],[-13.325,1.915],[-13.146,2.472],[-12.676,2.82],[-10.773,3.448],[-9.822,5.351],[-10.564,6.674],[-10.749,8.112],[-9.868,9.263],[-8.251,10.215],[-7.299,10.472],[-6.805,10.406],[-6.132,10.085],[-5.633,9.53],[-5.282,8.911],[-1.903,9.53],[1.906,9.53],[6.046,8.578],[6.589,9.53],[7.079,10.096],[7.749,10.43],[8.497,10.478],[9.206,10.234],[10.824,9.283],[11.385,8.791],[11.714,8.123],[11.76,7.379],[11.519,6.674],[10.282,4.514],[11.424,0.012],[12.376,0.012],[13.049,-0.267],[13.327,-0.94]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.577,10.762],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 2\",\"np\":5,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":15,\"op\":154,\"st\":4,\"bm\":0},{\"ddd\":0,\"ind\":7,\"ty\":4,\"nm\":\"Layer 6 Outlines\",\"parent\":1,\"tt\":2,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.1,\"y\":1},\"o\":{\"x\":0.3,\"y\":0},\"t\":15,\"s\":[2.103,-13.34,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":30,\"s\":[2.103,3.029,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[6.424,6.424,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.1,0.1,0.1],\"y\":[1,1,1]},\"o\":{\"x\":[0.3,0.3,0.3],\"y\":[0,0,0]},\"t\":15,\"s\":[20,20,100]},{\"t\":30,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-1.622,0],[-1.006,0.672],[-0.463,1.117],[0.236,1.186],[0.855,0.855],[1.186,0.236],[1.117,-0.463],[0.672,-1.005],[0,-1.209],[-1.147,-1.146]],\"o\":[[1.209,0],[1.005,-0.672],[0.463,-1.117],[-0.236,-1.186],[-0.856,-0.856],[-1.186,-0.236],[-1.117,0.463],[-0.672,1.006],[0,1.622],[1.146,1.147]],\"v\":[[-0.06,6.174],[3.338,5.144],[5.59,2.4],[5.938,-1.133],[4.264,-4.264],[1.133,-5.938],[-2.399,-5.59],[-5.144,-3.338],[-6.174,0.059],[-4.383,4.383]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[6.424,6.424],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":15,\"op\":154,\"st\":4,\"bm\":0},{\"ddd\":0,\"ind\":8,\"ty\":4,\"nm\":\"Layer 7 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.35],\"y\":[1]},\"o\":{\"x\":[0.001],\"y\":[0]},\"t\":0,\"s\":[10]},{\"i\":{\"x\":[0.1],\"y\":[1]},\"o\":{\"x\":[0.001],\"y\":[0]},\"t\":11,\"s\":[0]},{\"i\":{\"x\":[0.999],\"y\":[1.002]},\"o\":{\"x\":[0.9],\"y\":[0]},\"t\":15,\"s\":[0]},{\"i\":{\"x\":[0.1],\"y\":[1]},\"o\":{\"x\":[0.001],\"y\":[-0.003]},\"t\":18,\"s\":[4]},{\"i\":{\"x\":[0.1],\"y\":[1]},\"o\":{\"x\":[0.167],\"y\":[0]},\"t\":22,\"s\":[0]},{\"i\":{\"x\":[0.999],\"y\":[1.002]},\"o\":{\"x\":[0.9],\"y\":[0]},\"t\":26,\"s\":[0]},{\"i\":{\"x\":[0.1],\"y\":[1]},\"o\":{\"x\":[0.001],\"y\":[-0.003]},\"t\":29,\"s\":[4]},{\"t\":33,\"s\":[0]}],\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.188,\"y\":1},\"o\":{\"x\":0.001,\"y\":0},\"t\":0,\"s\":[1.025,-0.364,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.35,\"y\":1},\"o\":{\"x\":0.192,\"y\":0},\"t\":7,\"s\":[1.025,12.215,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":20,\"s\":[1.025,10.215,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.577,18.973,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.188,0.188,0.188],\"y\":[1,1,1]},\"o\":{\"x\":[0.001,0.001,0.001],\"y\":[0,0,0]},\"t\":0,\"s\":[0,0,100]},{\"i\":{\"x\":[0.35,0.35,0.35],\"y\":[1,1,1]},\"o\":{\"x\":[0.192,0.192,0.192],\"y\":[0,0,0]},\"t\":7,\"s\":[110,110,100]},{\"t\":20,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.526,0],[0,-0.526],[-0.526,0],[0,0.525]],\"o\":[[-0.526,0],[0,0.525],[0.526,0],[0,-0.526]],\"v\":[[0,-0.952],[-0.952,0.001],[0,0.952],[0.952,0.001]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[7.868,7.918],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false},{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0],[0.103,0.077],[0.125,0.03],[0.126,-0.02],[0.11,-0.07],[1.192,0],[0,0],[0.982,0.6],[0.259,-0.079],[0.12,-0.21],[0,0],[0,0],[0,0],[0.011,0.188],[0.114,0.15],[0.273,0.827],[0.106,0.106],[0.142,0.046],[0,0],[0,0],[0,0],[-0.135,0.121],[-0.049,0.175],[-0.684,0.836],[0.021,0.231],[-0.21,0.92],[-0.727,-0.421],[0,0],[-0.097,0.012],[-1.747,-0.145],[-1.536,-1.677],[-0.288,-1.327],[1.04,-1.679],[0,-0.167],[-0.084,-0.145],[0,0]],\"o\":[[0,0],[-0.064,-0.111],[-0.103,-0.077],[-0.123,-0.031],[-0.129,0.019],[-1.007,0.638],[0,0],[-1.151,0],[-0.233,-0.143],[-0.232,0.07],[0,0],[0,0],[0,0],[0.094,-0.163],[-0.012,-0.188],[-0.539,-0.684],[-0.046,-0.143],[-0.106,-0.106],[0,0],[0,0],[0,0],[0.179,-0.03],[0.135,-0.122],[0.27,-1.046],[0.137,-0.186],[-0.052,-0.942],[0.804,0.244],[0,0],[0.229,-0.038],[1.738,-0.216],[2.23,0.186],[0.931,1.016],[0.419,1.931],[-0.084,0.145],[0,0.167],[0,0],[0,0]],\"v\":[[8.245,8.578],[7.236,6.817],[6.982,6.532],[6.635,6.37],[6.258,6.353],[5.895,6.489],[2.609,7.626],[-1.859,7.626],[-5.187,6.878],[-5.961,6.78],[-6.508,7.217],[-7.29,8.578],[-8.908,7.626],[-7.831,5.723],[-7.704,5.184],[-7.898,4.666],[-9.127,2.382],[-9.359,2.004],[-9.736,1.773],[-11.421,1.23],[-11.421,-1.083],[-10.003,-1.321],[-9.521,-1.552],[-9.241,-2.006],[-7.794,-4.862],[-7.614,-5.509],[-7.376,-8.317],[-4.094,-7.029],[-3.892,-7.148],[-3.229,-7.148],[2.062,-7.325],[7.93,-4.69],[9.338,-1.609],[8.368,4.019],[8.241,4.495],[8.368,4.971],[9.863,7.626]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ind\":1,\"ty\":\"sh\",\"ix\":2,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.179,0.178],[0.252,0],[0,0],[0.693,1.032],[0.875,0.567],[2.138,0.157],[0.248,0.01],[1.279,-0.373],[0.049,-0.018],[1.313,0.325],[0.208,-0.12],[0.071,-0.23],[-0.01,-1.392],[0.319,-0.937],[0,0],[0.147,-0.175],[-0.003,-0.228],[0,0],[-0.116,-0.162],[-0.189,-0.064],[0,0],[-0.388,-0.596],[0,0],[-0.13,-0.485],[-0.434,-0.252],[0,0],[-0.335,0],[-0.161,0.045],[-0.201,0.15],[-0.128,0.216],[0,0],[-1.155,0.005],[0,0],[-1.29,0.627],[0,0],[-0.199,-0.154],[-0.243,-0.066],[-0.25,0.034],[-0.217,0.128],[0,0],[-0.153,0.199],[-0.065,0.242],[0.033,0.249],[0.127,0.217],[0,0],[-0.006,1.571],[0,0],[-0.179,0.178],[0,0.252]],\"o\":[[-0.179,-0.179],[0,0],[-0.244,-1.219],[-0.577,-0.859],[-1.823,-1.182],[-0.248,-0.019],[-1.331,-0.052],[-0.051,0.015],[-1.167,-0.684],[-0.235,-0.053],[-0.209,0.12],[-0.394,1.334],[-0.595,0.791],[0,0],[-0.225,0.037],[-0.146,0.174],[0,0],[0,0.2],[0.116,0.162],[0,0],[0.244,0.668],[0,0],[-0.249,0.436],[0.13,0.485],[0,0],[0.289,0.168],[0.167,0.002],[0.244,-0.062],[0.201,-0.151],[0,0],[1.079,0.414],[0,0],[1.434,0.001],[0,0],[0.124,0.219],[0.2,0.155],[0.244,0.065],[0.25,-0.033],[0,0],[0.218,-0.126],[0.153,-0.199],[0.065,-0.242],[-0.033,-0.248],[0,0],[0.744,-1.385],[0,0],[0.252,0],[0.179,-0.179],[0,-0.253]],\"v\":[[13.049,-1.613],[12.376,-1.892],[11.215,-1.892],[9.796,-5.302],[7.67,-7.501],[1.488,-9.295],[0.743,-9.339],[-3.215,-8.923],[-3.443,-8.843],[-7.812,-10.459],[-8.502,-10.356],[-8.937,-9.811],[-9.517,-5.699],[-10.896,-3.091],[-12.525,-2.844],[-13.102,-2.516],[-13.325,-1.892],[-13.325,1.915],[-13.146,2.472],[-12.676,2.82],[-10.773,3.448],[-9.822,5.351],[-10.564,6.674],[-10.749,8.112],[-9.868,9.263],[-8.251,10.215],[-7.299,10.472],[-6.805,10.406],[-6.132,10.085],[-5.633,9.53],[-5.282,8.911],[-1.903,9.53],[1.906,9.53],[6.046,8.578],[6.589,9.53],[7.079,10.096],[7.749,10.43],[8.497,10.478],[9.206,10.234],[10.824,9.283],[11.385,8.791],[11.714,8.123],[11.76,7.379],[11.519,6.674],[10.282,4.514],[11.424,0.012],[12.376,0.012],[13.049,-0.267],[13.327,-0.94]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.431042869418,0.644514016544,0.439860175638,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.577,10.762],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 2\",\"np\":6,\"cix\":2,\"bm\":0,\"ix\":2,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150,\"st\":0,\"bm\":0}],\"markers\":[]}"
	FDLottieString       = "{\"v\":\"4.8.0\",\"meta\":{\"g\":\"LottieFiles AE 3.3.6\",\"a\":\"\",\"k\":\"\",\"d\":\"\",\"tc\":\"\"},\"fr\":30,\"ip\":0,\"op\":150,\"w\":32,\"h\":32,\"nm\":\"FD\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":1,\"h\":1,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC\",\"e\":1}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":2,\"nm\":\"FD\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[15.5,15.125,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":4,\"nm\":\"Layer 21 Outlines\",\"parent\":6,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[1.946,9.291,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[2.23,2.28,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0]],\"o\":[[0,0],[0,0]],\"v\":[[-1.23,1.28],[1.23,-1.28]],\"c\":false},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[2.23,2.28],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":3,\"ty\":4,\"nm\":\"Layer 22 Outlines\",\"parent\":6,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[9.255,1.482,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[2.231,2.281,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0]],\"o\":[[0,0],[0,0]],\"v\":[[-1.231,1.281],[1.231,-1.281]],\"c\":false},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[2.231,2.281],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":4,\"ty\":4,\"nm\":\"Layer 23 Outlines\",\"parent\":6,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[1.942,1.476,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[2.226,2.275,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0]],\"o\":[[0,0],[0,0]],\"v\":[[-1.225,-1.275],[1.225,1.275]],\"c\":false},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[2.225,2.276],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":5,\"ty\":4,\"nm\":\"Layer 24 Outlines\",\"parent\":6,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[9.258,9.293,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[2.228,2.278,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0]],\"o\":[[0,0],[0,0]],\"v\":[[-1.228,-1.278],[1.228,1.278]],\"c\":false},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[2.228,2.278],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":6,\"ty\":4,\"nm\":\"Layer 25 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.67],\"y\":[1]},\"o\":{\"x\":[0.33],\"y\":[0]},\"t\":10,\"s\":[0]},{\"i\":{\"x\":[0.4],\"y\":[1]},\"o\":{\"x\":[0.4],\"y\":[0]},\"t\":17.098,\"s\":[-55.44]},{\"i\":{\"x\":[0.377],\"y\":[1]},\"o\":{\"x\":[0.292],\"y\":[0]},\"t\":35.311,\"s\":[770.4]},{\"t\":49,\"s\":[720]}],\"ix\":10},\"p\":{\"a\":0,\"k\":[0.454,0.465,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[5.6,5.429,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.4,0.4,0.4],\"y\":[1,1,1]},\"o\":{\"x\":[0.45,0.45,0.45],\"y\":[0,0,0]},\"t\":10,\"s\":[0,0,100]},{\"i\":{\"x\":[0.15,0.15,0.15],\"y\":[1,1,1]},\"o\":{\"x\":[0.45,0.45,0.45],\"y\":[0,0,0]},\"t\":12.853,\"s\":[-11.6,-11.6,100]},{\"t\":19,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-1.894,0],[0,0],[0,1.893],[1.893,0],[0,0],[0,-1.894]],\"o\":[[0,0],[1.893,0],[0,-1.894],[0,0],[-1.894,0],[0,1.893]],\"v\":[[-0.171,3.428],[0.172,3.428],[3.6,0.001],[0.172,-3.428],[-0.171,-3.428],[-3.6,0.001]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[5.6,5.429],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.819607853889,0.537254929543,0.35686275363,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":7,\"ty\":4,\"nm\":\"Layer 26 Outlines\",\"parent\":9,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[20.6,24.858,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1,2.143,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0]],\"o\":[[0,0],[0,0]],\"v\":[[1,1],[1,3.286]],\"c\":false},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[0,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":8,\"ty\":4,\"nm\":\"Layer 27 Outlines\",\"parent\":9,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[7.401,24.858,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1,2.143,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0]],\"o\":[[0,0],[0,0]],\"v\":[[1,1],[1,3.286]],\"c\":false},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[0,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":9,\"ty\":4,\"nm\":\"Layer 28 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.188,\"y\":1},\"o\":{\"x\":0.001,\"y\":0},\"t\":0,\"s\":[0.541,-2.232,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.35,\"y\":1},\"o\":{\"x\":0.192,\"y\":0},\"t\":7,\"s\":[0.541,11.144,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":20,\"s\":[0.541,9.018,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[14,21.982,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.188,0.188,0.188],\"y\":[1,1,1]},\"o\":{\"x\":[0.001,0.001,0.001],\"y\":[0,0,0]},\"t\":0,\"s\":[0,0,100]},{\"i\":{\"x\":[0.35,0.35,0.35],\"y\":[1,1,1]},\"o\":{\"x\":[0.192,0.192,0.192],\"y\":[0,0,0]},\"t\":7,\"s\":[108,108,100]},{\"t\":20,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[1.665,0],[0,0],[0,-1.664],[0,0],[-1.665,0],[0,0],[0,1.665],[0,0]],\"o\":[[0,0],[-1.665,0],[0,0],[0,1.665],[0,0],[1.665,0],[0,0],[0,-1.664]],\"v\":[[8.986,-10.857],[-8.985,-10.857],[-12,-7.843],[-12,7.843],[-8.985,10.857],[8.986,10.857],[12,7.843],[12,-7.843]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"st\",\"c\":{\"a\":0,\"k\":[0.819100772633,0.536736522001,0.358735028435,1],\"ix\":3},\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":2,\"ix\":5},\"lc\":2,\"lj\":2,\"bm\":0,\"nm\":\"Stroke 1\",\"mn\":\"ADBE Vector Graphic - Stroke\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[14,12.857],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0}],\"markers\":[]}"
	MFLottieString       = "{\"v\":\"4.8.0\",\"meta\":{\"g\":\"LottieFiles AE 3.3.6\",\"a\":\"\",\"k\":\"\",\"d\":\"\",\"tc\":\"\"},\"fr\":30,\"ip\":0,\"op\":150,\"w\":32,\"h\":32,\"nm\":\"MF\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":1,\"h\":1,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC\",\"e\":1}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":2,\"nm\":\"MF\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[15.5,15.5,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":4,\"nm\":\"Layer 10 Outlines\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[14.016,5.155,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.558,1.558,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0.363,-0.361],[-0.363,-0.362],[0,0],[-0.248,0],[0,0],[-0.174,0.175],[0,0.249],[0.176,0.175]],\"o\":[[-0.363,-0.362],[-0.363,0.362],[0,0],[0.175,0.176],[0,0],[0.248,0],[0.176,-0.176],[0,-0.248],[0,0]],\"v\":[[0.37,-0.946],[-0.945,-0.946],[-0.945,0.369],[-0.278,1.036],[0.379,1.308],[0.381,1.308],[1.036,1.036],[1.308,0.378],[1.036,-0.279]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.558,1.558],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":38,\"op\":188.15015015015,\"st\":38,\"bm\":0},{\"ddd\":0,\"ind\":3,\"ty\":4,\"nm\":\"Layer 9 Outlines\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[24.045,5.157,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.558,1.558,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.362,0.364],[0.361,-0.362],[0,0],[0,-0.248],[-0.176,-0.176],[-0.248,0],[-0.176,0.176],[0,0]],\"o\":[[-0.363,-0.361],[0,0],[-0.176,0.176],[0,0.249],[0.175,0.176],[0.249,0],[0,0],[0.36,-0.362]],\"v\":[[0.946,-0.947],[-0.368,-0.946],[-1.035,-0.28],[-1.308,0.378],[-1.035,1.036],[-0.378,1.308],[0.28,1.036],[0.948,0.368]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.558,1.558],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":35,\"op\":185.15015015015,\"st\":35,\"bm\":0},{\"ddd\":0,\"ind\":4,\"ty\":4,\"nm\":\"Layer 11 Outlines\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[19.032,3.197,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.18,1.651,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.513,0],[0,-0.513],[0,0],[-0.513,0],[0,0.513],[0,0]],\"o\":[[-0.513,0],[0,0],[0,0.513],[0.513,0],[0,0],[0,-0.513]],\"v\":[[0,-1.402],[-0.93,-0.472],[-0.93,0.472],[0,1.402],[0.93,0.472],[0.93,-0.472]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.18,1.651],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":32,\"op\":182.15015015015,\"st\":32,\"bm\":0},{\"ddd\":0,\"ind\":5,\"ty\":4,\"nm\":\"Layer 8 Outlines 3\",\"parent\":15,\"td\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[13.716,5.682,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.716,5.682,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.791,0],[0,0],[0,-0.813],[-0.155,-0.685],[-0.706,-0.903],[-2.218,-0.683],[-2.582,0],[-2.009,0.713],[-1.099,1.604],[-0.151,0.308],[0,1.389]],\"o\":[[0,0],[-0.791,0],[0,0.777],[0.259,1.168],[1.131,1.452],[1.882,0.578],[2.65,0],[2.261,-0.801],[0.208,-0.304],[0.56,-1.129],[0,-0.813]],\"v\":[[12.056,-5.432],[-12.056,-5.432],[-13.467,-3.983],[-13.236,3.936],[-11.781,7.059],[-6.734,10.276],[-0.007,11.147],[7.014,10.072],[12.079,6.449],[12.621,5.526],[13.467,-3.983]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.716,5.682],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":3,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":25,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":6,\"ty\":4,\"nm\":\"Layer 12 Outlines\",\"tt\":2,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.188,\"y\":1},\"o\":{\"x\":0.001,\"y\":0},\"t\":25,\"s\":[10.077,23.572,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.35,\"y\":1},\"o\":{\"x\":0.192,\"y\":0},\"t\":31.42,\"s\":[10.077,11.682,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":45,\"s\":[10.077,13.572,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[5.758,7.123,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.188,0.188,0.188],\"y\":[1,1,1]},\"o\":{\"x\":[0.001,0.001,0.001],\"y\":[0,0,0]},\"t\":25,\"s\":[64,64,100]},{\"i\":{\"x\":[0.35,0.35,0.35],\"y\":[1,1,1]},\"o\":{\"x\":[0.192,0.192,0.192],\"y\":[0,0,0]},\"t\":31.42,\"s\":[106.804,106.804,100]},{\"t\":45,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-0.009,-0.189],[0.121,-0.126],[0.182,0],[0,0],[0.011,0.235],[-0.001,0.16],[-1.049,1.745],[-1.232,0],[-0.549,-0.375],[0,-2.619]],\"o\":[[0.008,0.178],[-0.073,0.077],[0,0],[-0.208,0],[-0.007,-0.151],[0,-2.006],[1.033,-1.72],[0.553,0],[-1.359,2.259],[0,0.193]],\"v\":[[1.098,5.27],[0.915,6.968],[0.534,7.136],[-3.203,7.136],[-3.644,6.761],[-3.643,4.172],[-1.968,-1.817],[1.596,-4.525],[3.256,-3.96],[1.09,3.794]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ind\":1,\"ty\":\"sh\",\"ix\":2,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.336,0.314],[0,0],[2.06,-1.562],[0.645,-1.066],[0,-2.331],[-0.124,-0.605],[-0.44,0],[0,0],[-0.177,0.224],[0.064,0.276],[0,0.699],[-1.268,2.004],[-0.095,0.133]],\"o\":[[0,0],[-1.994,-1.865],[-0.813,0.617],[-1.214,2.019],[0,0.692],[0.089,0.431],[0,0],[0.285,0],[0.178,-0.223],[-0.134,-0.591],[0,-2.378],[0.09,-0.142],[0.264,-0.378]],\"v\":[[5.12,-4.833],[5.12,-4.834],[-1.373,-5.311],[-3.569,-2.773],[-5.508,4.175],[-5.326,8.098],[-4.413,8.841],[2.234,8.841],[2.962,8.489],[3.141,7.703],[2.944,3.791],[4.97,-3.207],[5.244,-3.618]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[5.758,7.123],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":4,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":25,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":7,\"ty\":4,\"nm\":\"Layer 8 Outlines 2\",\"parent\":15,\"td\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[13.716,5.682,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.716,5.682,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.791,0],[0,0],[0,-0.813],[-0.155,-0.685],[-0.706,-0.903],[-2.218,-0.683],[-2.582,0],[-2.009,0.713],[-1.099,1.604],[-0.151,0.308],[0,1.389]],\"o\":[[0,0],[-0.791,0],[0,0.777],[0.259,1.168],[1.131,1.452],[1.882,0.578],[2.65,0],[2.261,-0.801],[0.208,-0.304],[0.56,-1.129],[0,-0.813]],\"v\":[[12.056,-5.432],[-12.056,-5.432],[-13.467,-3.983],[-13.234,-1.779],[-11.779,1.344],[-6.732,4.561],[-0.005,5.432],[7.017,4.357],[12.082,0.734],[12.624,-0.189],[13.467,-3.983]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.716,5.682],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":3,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":20,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":8,\"ty\":4,\"nm\":\"Layer 13 Outlines\",\"tt\":2,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.188,\"y\":1},\"o\":{\"x\":0.001,\"y\":0},\"t\":20,\"s\":[19.031,23.029,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.35,\"y\":1},\"o\":{\"x\":0.192,\"y\":0},\"t\":26.42,\"s\":[19.031,11.139,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":40,\"s\":[19.031,13.029,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[8.114,7.665,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.188,0.188,0.188],\"y\":[1,1,1]},\"o\":{\"x\":[0.001,0.001,0.001],\"y\":[0,0,0]},\"t\":20,\"s\":[64,64,100]},{\"i\":{\"x\":[0.35,0.35,0.35],\"y\":[1,1,1]},\"o\":{\"x\":[0.192,0.192,0.192],\"y\":[0,0,0]},\"t\":26.42,\"s\":[106.804,106.804,100]},{\"t\":40,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0,0],[0.032,0.069],[-0.012,0.075],[0,0],[-0.005,0.056],[-0.036,0.047],[-0.07,0.025],[-0.07,-0.017],[0,0],[-0.233,0.203],[-0.074,0.309],[0,0],[0.006,0.325],[-0.311,0],[0,0],[0.235,0.215],[0.31,0.017],[0,0],[0.006,0.325],[-0.311,0],[0,0],[-0.106,-0.112],[0,-0.158],[0.105,-0.112],[0.15,0],[0,0],[-0.06,-0.426],[0,0],[-0.106,-0.112],[0,-0.158],[0.106,-0.111],[0.149,0],[0,0],[0.448,-0.423],[0.6,-0.025],[0,0],[0,0]],\"o\":[[0,0],[-0.05,-0.056],[-0.012,-0.075],[0,0],[-0.005,-0.056],[0.023,-0.055],[0.048,-0.06],[0.071,-0.017],[0,0],[0.302,-0.018],[0.232,-0.203],[0,0],[-0.308,-0.007],[0,-0.328],[0,0],[-0.06,-0.322],[-0.235,-0.215],[0,0],[-0.308,-0.006],[0,-0.328],[0,0],[0.15,0],[0.105,0.111],[0,0.157],[-0.106,0.111],[0,0],[0.229,0.356],[0,0],[0.149,0],[0.106,0.111],[0,0.157],[-0.106,0.111],[0,0],[-0.091,0.626],[-0.448,0.422],[0,0],[0,0],[0,0]],\"v\":[[-1.592,5.555],[-3.185,3.764],[-3.309,3.574],[-3.309,3.348],[-3.309,3.289],[-3.309,3.122],[-3.219,2.968],[-3.039,2.837],[-2.825,2.837],[-0.865,2.837],[-0.041,2.497],[0.431,1.71],[-2.825,1.71],[-3.388,1.115],[-2.825,0.521],[0.487,0.521],[0.033,-0.308],[-0.808,-0.667],[-2.825,-0.667],[-3.388,-1.26],[-2.825,-1.855],[2.413,-1.855],[2.812,-1.68],[2.977,-1.26],[2.812,-0.84],[2.413,-0.667],[1.208,-0.773],[1.647,0.414],[2.47,0.414],[2.868,0.589],[3.033,1.008],[2.868,1.428],[2.47,1.602],[1.647,1.602],[0.814,3.225],[-0.808,3.919],[-1.417,3.919],[0.101,5.555]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ind\":1,\"ty\":\"sh\",\"ix\":2,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0.832],[1.356,2.255],[0.915,0.691],[1.142,0],[0.927,-0.62],[0.683,-0.972],[0,0],[0.102,-0.159],[0,0],[0,-2.717],[-0.163,-0.73],[0,0],[-0.435,0],[0,0],[-0.097,0.424],[0,0]],\"o\":[[0,-2.606],[-0.708,-1.174],[-1.02,-0.771],[-1.018,0],[-0.838,0.56],[0,0],[-0.112,0.159],[0,0],[-1.452,2.296],[0,0.833],[0,0],[0.095,0.424],[0,0],[0.435,0],[0,0],[0.164,-0.731]],\"v\":[[7.864,4.335],[5.7,-3.415],[3.26,-6.234],[0,-7.415],[-2.935,-6.465],[-5.223,-4.146],[-5.225,-4.145],[-5.547,-3.661],[-5.548,-3.66],[-7.864,4.335],[-7.617,6.688],[-7.617,6.689],[-6.711,7.415],[6.707,7.415],[7.616,6.691],[7.617,6.689]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[8.114,7.665],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":4,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":20,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":15,\"ty\":4,\"nm\":\"Layer 8 Outlines\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.67,\"y\":1},\"o\":{\"x\":0.33,\"y\":0},\"t\":0,\"s\":[16.106,8.764,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.4,\"y\":1},\"o\":{\"x\":0.4,\"y\":0},\"t\":3.644,\"s\":[16.106,7.609,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.377,\"y\":1},\"o\":{\"x\":0.292,\"y\":0},\"t\":12.993,\"s\":[16.106,24.814,0],\"to\":[0,0,0],\"ti\":[0,0,0]},{\"t\":20.0205078125,\"s\":[16.106,23.764,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[13.716,5.682,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.67,0.67,0.67],\"y\":[1,1,1]},\"o\":{\"x\":[0.33,0.33,0.33],\"y\":[0,0,0]},\"t\":0,\"s\":[0,0,100]},{\"i\":{\"x\":[0.4,0.4,0.4],\"y\":[1,1,1]},\"o\":{\"x\":[0.4,0.4,0.4],\"y\":[0,0,0]},\"t\":3.644,\"s\":[-7.7,-7.7,100]},{\"i\":{\"x\":[0.377,0.377,0.377],\"y\":[1,1,1]},\"o\":{\"x\":[0.292,0.292,0.292],\"y\":[0,0,0]},\"t\":12.993,\"s\":[107,107,100]},{\"t\":20.0205078125,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0],[0.377,-0.759],[0.154,-0.225],[5.088,0],[1.724,2.205],[0.202,0.898],[0.021,0.484]],\"o\":[[-0.048,0.94],[-0.118,0.239],[-1.712,2.504],[-5.021,0],[-0.534,-0.684],[-0.097,-0.437],[0,0]],\"v\":[[11.59,-3.569],[10.95,-1.011],[10.541,-0.312],[-0.005,3.572],[-10.317,0.201],[-11.424,-2.183],[-11.604,-3.569]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ind\":1,\"ty\":\"sh\",\"ix\":2,\"ks\":{\"a\":0,\"k\":{\"i\":[[0.791,0],[0,0],[0,-0.813],[-0.155,-0.685],[-0.706,-0.903],[-2.218,-0.683],[-2.582,0],[-2.009,0.713],[-1.099,1.604],[-0.151,0.308],[0,1.389]],\"o\":[[0,0],[-0.791,0],[0,0.777],[0.259,1.168],[1.131,1.452],[1.882,0.578],[2.65,0],[2.261,-0.801],[0.208,-0.304],[0.56,-1.129],[0,-0.813]],\"v\":[[12.056,-5.432],[-12.056,-5.432],[-13.467,-3.983],[-13.234,-1.779],[-11.779,1.344],[-6.732,4.561],[-0.005,5.432],[7.017,4.357],[12.082,0.734],[12.624,-0.189],[13.467,-3.983]],\"c\":true},\"ix\":2},\"nm\":\"Path 2\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"mm\",\"mm\":1,\"nm\":\"Merge Paths 1\",\"mn\":\"ADBE Vector Filter - Merge\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.488990394742,0.444265956505,0.671807083429,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[13.716,5.682],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":4,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0}],\"markers\":[]}"
	P2pLottieString      = "{\"v\":\"4.8.0\",\"meta\":{\"g\":\"LottieFiles AE 3.3.6\",\"a\":\"\",\"k\":\"\",\"d\":\"\",\"tc\":\"\"},\"fr\":30,\"ip\":0,\"op\":150,\"w\":32,\"h\":32,\"nm\":\"Jump\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":1,\"h\":1,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC\",\"e\":1}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":2,\"nm\":\"Jump\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[15.5,15.5,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":4,\"nm\":\"Ball\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":1,\"k\":[{\"i\":{\"x\":0.67,\"y\":1},\"o\":{\"x\":0.33,\"y\":0},\"t\":0,\"s\":[-11.478,8.101,0],\"to\":[3.55,-0.212,0],\"ti\":[0,0,0]},{\"i\":{\"x\":0.1,\"y\":1},\"o\":{\"x\":0.33,\"y\":0},\"t\":8,\"s\":[-4.509,9.828,0],\"to\":[1.077,-4.243,0],\"ti\":[-3.236,1.101,0]},{\"t\":23,\"s\":[9.673,-4.695,0]}],\"ix\":2},\"a\":{\"a\":0,\"k\":[5.681,5.669,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.728,0.728,0.728],\"y\":[0.706,0.706,-17.828]},\"o\":{\"x\":[0.453,0.453,0.453],\"y\":[0,0,0]},\"t\":0,\"s\":[0,0,100]},{\"i\":{\"x\":[0.443,0.443,0.443],\"y\":[1,1,1]},\"o\":{\"x\":[0.223,0.223,0.223],\"y\":[0.804,0.804,28.934]},\"t\":8,\"s\":[64,64,100]},{\"t\":23,\"s\":[100,100,100]}],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-3,0],[0,-2.993],[3,0],[0,2.993]],\"o\":[[3,0],[0,2.993],[-3,0],[0,-2.993]],\"v\":[[0,-5.419],[5.431,0],[0,5.419],[-5.431,0]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[5.681,5.669],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":0,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":3,\"ty\":4,\"nm\":\"Layer 15 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[-12.279,8.995,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.625,1.623,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-0.759,0],[0,-0.758],[0.759,0],[0,0.758]],\"o\":[[0.759,0],[0,0.758],[-0.759,0],[0,-0.758]],\"v\":[[0,-1.372],[1.375,0.001],[0,1.372],[-1.375,0.001]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.625,1.622],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":1,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":4,\"ty\":4,\"nm\":\"Layer 17 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[-8.624,7.674,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.09,0.746,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[0,0.376],[-0.375,0],[-0.367,-0.084],[0.084,-0.366],[0.366,0.084],[0.076,0]],\"o\":[[0,-0.376],[0.245,0],[0.366,0.084],[-0.084,0.367],[-0.366,-0.084],[-0.375,0]],\"v\":[[-1.09,-0.066],[-0.411,-0.747],[0.561,-0.598],[1.072,0.217],[0.258,0.729],[-0.411,0.615]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.09,0.746],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":5,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":5,\"ty\":4,\"nm\":\"Layer 16 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[-4.32,9.262,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.148,0.966,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-0.194,0.322],[-0.321,-0.195],[-0.034,-0.022],[-0.267,-0.122],[0.155,-0.343],[0.085,-0.191],[0.048,-0.053],[0.157,-0.02],[0.151,0.098],[0.049,0.064],[0.254,0.155]],\"o\":[[0.194,-0.321],[0.034,0.021],[0.194,-0.198],[0.341,0.155],[-0.081,0.178],[-0.03,0.068],[-0.113,0.128],[-0.165,0.03],[-0.067,-0.044],[-0.061,-0.079],[-0.321,-0.195]],\"v\":[[-1.05,-0.598],[-0.117,-0.827],[-0.015,-0.763],[0.75,-0.905],[1.088,-0.004],[0.84,0.549],[0.722,0.732],[0.301,0.955],[-0.19,0.857],[-0.365,0.695],[-0.821,0.337]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.148,0.966],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":10,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":6,\"ty\":4,\"nm\":\"Layer 18 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[-2.362,6.048,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0.99,1.204,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-0.315,-0.204],[0.204,-0.316],[0.196,-0.358],[0.329,0.181],[-0.181,0.33],[-0.223,0.346]],\"o\":[[0.315,0.204],[-0.204,0.317],[-0.181,0.329],[-0.328,-0.181],[0.21,-0.382],[0.204,-0.316]],\"v\":[[0.679,-1.096],[0.881,-0.155],[0.285,0.852],[-0.638,1.12],[-0.905,0.195],[-0.26,-0.893]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[0.99,1.204],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":14,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":7,\"ty\":4,\"nm\":\"Layer 19 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[-0.003,2.963,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[1.095,1.125,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-0.269,-0.262],[0.262,-0.27],[0.247,-0.276],[0.279,0.252],[-0.251,0.28],[-0.307,0.315]],\"o\":[[0.269,0.262],[-0.296,0.305],[-0.251,0.28],[-0.279,-0.251],[0.262,-0.292],[0.261,-0.269]],\"v\":[[0.889,-0.932],[0.902,0.031],[0.089,0.899],[-0.87,0.95],[-0.921,-0.011],[-0.071,-0.919]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[1.095,1.125],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":16,\"op\":150.15015015015,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":8,\"ty\":4,\"nm\":\"Layer 20 Outlines\",\"parent\":1,\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[2.54,0.429,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0.9,0.892,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"ty\":\"gr\",\"it\":[{\"ind\":0,\"ty\":\"sh\",\"ix\":1,\"ks\":{\"a\":0,\"k\":{\"i\":[[-0.259,-0.272],[0.271,-0.259],[0.142,-0.136],[0.259,0.272],[-0.271,0.26],[-0.152,0.144]],\"o\":[[0.259,0.272],[-0.151,0.145],[-0.271,0.26],[-0.259,-0.271],[0.143,-0.138],[0.271,-0.26]],\"v\":[[0.712,-0.681],[0.689,0.282],[0.249,0.702],[-0.711,0.682],[-0.69,-0.281],[-0.248,-0.704]],\"c\":true},\"ix\":2},\"nm\":\"Path 1\",\"mn\":\"ADBE Vector Shape - Group\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":0,\"k\":[0.380834811342,0.60441236309,0.632818603516,1],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false},{\"ty\":\"tr\",\"p\":{\"a\":0,\"k\":[0.9,0.892],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"o\":{\"a\":0,\"k\":100,\"ix\":7},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"nm\":\"Transform\"}],\"nm\":\"Group 1\",\"np\":2,\"cix\":2,\"bm\":0,\"ix\":1,\"mn\":\"ADBE Vector Group\",\"hd\":false}],\"ip\":18,\"op\":150.15015015015,\"st\":0,\"bm\":0}],\"markers\":[]}"
)

type InvestmentInstruments struct {
	catalogClient                  catalog.CatalogManagerClient
	userGroupClient                usergroupPb.GroupClient
	actorClient                    actorPb.ActorClient
	userClient                     userPb.UsersClient
	config                         *genconf.Config
	releaseEvaluator               *release.Evaluator
	mutualFundsDeeplinkABEvaluator *release.ABEvaluator[experiments.InvestmentLandingMutualFundsInstrumentDeeplink]
}

func NewInvestmentInstruments(catalogClient catalog.CatalogManagerClient, config *genconf.Config, userGroupClient usergroupPb.GroupClient, actorClient actorPb.ActorClient, userClient userPb.UsersClient, releaseEvaluator *release.Evaluator,
	mutualFundsDeeplinkABEvaluator *release.ABEvaluator[experiments.InvestmentLandingMutualFundsInstrumentDeeplink]) *InvestmentInstruments {
	return &InvestmentInstruments{catalogClient: catalogClient, config: config, userClient: userClient, actorClient: actorClient, userGroupClient: userGroupClient, releaseEvaluator: releaseEvaluator, mutualFundsDeeplinkABEvaluator: mutualFundsDeeplinkABEvaluator}
}

// nolint:funlen
func (i *InvestmentInstruments) GetInvestmentLandingComponent(ctx context.Context, actorID string) (*fePb.InvestmentLandingComponent, error) {
	MFInstrumentCardFlag := i.config.Flags().EnableMFInstrumentCardFlag()
	USStocksInstrumentCardFlag := i.config.Flags().EnableUSStocksInstrumentCardFlag()
	JumpInstrumentCardFlag := i.config.Flags().EnableJumpInstrumentCardFlag()
	SDInstrumentCardFlag := i.config.Flags().EnableSDInstrumentCardFlag()
	FDInstrumentCardFlag := i.config.Flags().EnableFDInstrumentCardFlag()

	var cards []*fePb.InstrumentCard
	isUSFundsEnabled, err := i.isUSFundsEnabled(ctx, actorID)
	if err != nil {
		logger.Error(ctx, "error in isUSFundsEnabled", zap.Error(err))
		return nil, err
	}
	if USStocksInstrumentCardFlag {
		usStocksCardSubtitle := GetUSStocksCardSubtitle(isUSFundsEnabled)
		usStocksCard, err := i.getUSStocksCard(ctx, actorID, usStocksCardSubtitle)
		if err != nil {
			return nil, fmt.Errorf("error in getUSStocksCard :%w", err)
		}
		if usStocksCard != nil {
			cards = append(cards, usStocksCard)
		}

		if isUSFundsEnabled {
			usFundsCard, err := i.getUSFundsCard(ctx, actorID)
			if err != nil {
				return nil, fmt.Errorf("error in getUSFundsCard :%w", err)
			}
			if usFundsCard != nil {
				cards = append(cards, usFundsCard)
			}
		}
	}

	if JumpInstrumentCardFlag {
		cards = append(cards, i.getP2PCard())
	}

	if FDInstrumentCardFlag {
		cards = append(cards, i.getFDCard())
	}

	if SDInstrumentCardFlag {
		cards = append(cards, i.getSDCard())
	}

	if MFInstrumentCardFlag {
		mfCard, err := i.getMutualFundCard(ctx, actorID)
		if err != nil {
			return nil, fmt.Errorf("error in getMutualFundCard :%w", err)
		}
		if mfCard != nil {
			cards = append(cards, mfCard)
		}
	}

	return &fePb.InvestmentLandingComponent{
		Component: &fePb.InvestmentLandingComponent_Instruments{
			Instruments: &fePb.InvestmentInstruments{
				Title: &commontypes.Text{
					FontColor:    "#333333",
					DisplayValue: &commontypes.Text_PlainString{PlainString: InstrumentsTitle},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
				},
				Cards: cards,
				// ToDo(Junaid): Fill details needed for compare
				Compare: nil,
				Shadows: []*ui.Shadow{
					{
						Height: 2,
						Blur:   0,
						Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
					},
				},
			},
		},
	}, nil
}

func (i *InvestmentInstruments) isUSFundsEnabled(ctx context.Context, actorId string) (bool, error) {
	enableUSFunds, err := i.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in US_FUNDS_IN_INVEST_LANDING_PAGE releaseEvaluator", zap.Error(err))
		return false, err
	}
	return enableUSFunds, nil
}

func (i *InvestmentInstruments) getMutualFundCard(ctx context.Context, actorId string) (*fePb.InstrumentCard, error) {
	homeScreenCollections, err := i.getHomeScreenCollections(ctx, actorId)
	if err != nil {
		return nil, err
	}

	return &fePb.InstrumentCard{
		IconUrl: MFIconURL,
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: MFCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: MFCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Tag:      nil,
		Deeplink: i.getMutualFundsCardDeeplink(ctx, actorId, homeScreenCollections[0].GetCollection().GetId()),
		Shadows: []*ui.Shadow{
			{
				Height: 4,
				Blur:   0,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
			},
		},
		BgColor:       "#FFFFFF",
		StoryDeeplink: nil,
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: MFIconURL}}}},
	}, nil
}

func (i *InvestmentInstruments) getMutualFundsCardDeeplink(ctx context.Context, actorID string, collectionID string) *deeplinkPb.Deeplink {

	defaultDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_MutualFundCollectionsLandingScreenOptions{
			MutualFundCollectionsLandingScreenOptions: &deeplinkPb.MutualFundCollectionsLandingScreenOptions{
				CollectionId:   collectionID,
				CollectionType: clientstates.CollectionType_MF_COLLECTION,
				EntryPoint:     clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_Investment_LANDING,
			},
		},
	}
	isEnabled, variant, err := i.mutualFundsDeeplinkABEvaluator.Evaluate(ctx, &release.CommonConstraintData{
		ActorId: actorID,
		Feature: types.Feature_INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK,
	})
	if err != nil || !isEnabled {
		logger.Error(ctx, fmt.Sprintf("error in invoking or feature not enabled for mutualFundsDeeplinkABEvaluator.Evaluate. isEnabled: %v . Falling back to default deeplink", isEnabled), zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorID))
		return defaultDeeplink
	}

	switch variant {
	case experiments.InvestmentLandingMutualFundsInstrumentDeeplink_INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_DEFAULT:
		logger.Info(ctx, "Default state for mutualFundsDeeplinkABEvaluator", zap.String(logger.ACTOR_ID_V2, actorID))
		return defaultDeeplink
	case experiments.InvestmentLandingMutualFundsInstrumentDeeplink_INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_CONTROL_1:
		logger.Info(ctx, "Control state for mutualFundsDeeplinkABEvaluator", zap.String(logger.ACTOR_ID_V2, actorID))
		return &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN}
	default:
		logger.Error(ctx, fmt.Sprintf("unsupported variant, :%s , Falling back to default order", variant.String()), zap.String(logger.ACTOR_ID_V2, actorID))
		return defaultDeeplink
	}

}

func (i *InvestmentInstruments) getP2PCard() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl: P2PIconURL,
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: P2PCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: P2PCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		BgColor:  "#FFFFFF",
		Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN},
		Shadows: []*ui.Shadow{
			{
				Height: 4,
				Blur:   0,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
			},
		},
		StoryDeeplink: nil,
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: P2PIconURL}}}},
	}
}

// nolint:dupl
func (i *InvestmentInstruments) getSDCard() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl: SDIconURL,
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: SDCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: SDCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		},
		Shadows: []*ui.Shadow{
			{
				Height: 4,
				Blur:   0,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
			},
		},
		BgColor:       "#FFFFFF",
		StoryDeeplink: nil,
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: SDIconURL}}}},
	}
}

// nolint:dupl
func (i *InvestmentInstruments) getFDCard() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl: FDIconURL,
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: FDCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: FDCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		},
		Shadows: []*ui.Shadow{
			{
				Height: 4,
				Blur:   0,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
			},
		},
		BgColor:       "#FFFFFF",
		StoryDeeplink: nil,
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: FDIconURL}}}},
	}

}

func (i *InvestmentInstruments) getHomeScreenCollections(ctx context.Context, actorID string) ([]*beSvc.CollectionWithFundFilterCount, error) {
	res, err := i.catalogClient.GetCollections(ctx, &beSvc.GetCollectionsRequest{
		Screen:  beSvc.CollectionScreen_COLLECTIONS_HOME_SCREEN,
		ActorId: actorID,
	})

	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, te
	}

	var homeScreenCollections []*beSvc.CollectionWithFundFilterCount

	for _, value := range res.GetCollectionsWithFilterCounts() {
		homeScreenCollections = append(homeScreenCollections, value)
	}

	sort.SliceStable(homeScreenCollections, func(i, j int) bool {
		return homeScreenCollections[i].GetCollection().GetWeight() < homeScreenCollections[j].GetCollection().GetWeight()
	})
	return homeScreenCollections, nil
}

// nolint
func (i *InvestmentInstruments) getUSFundsCard(ctx context.Context, actorId string) (*fePb.InstrumentCard, error) {
	return &fePb.InstrumentCard{
		IconUrl: USStocksIconURL,
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: USFundsCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: USFundsCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_USSTOCKS_LANDING_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&usStocksScreenOpts.LandingPageScreenOptions{
				EntryPoint: usstocksFePb.LandingScreenEntryPoint_LANDING_SCREEN_ENTRY_POINT_FUNDS.String(),
			}),
		},
		Shadows: []*ui.Shadow{
			{
				Height: 4,
				Blur:   0,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
			},
		},
		BgColor:       "#FFFFFF",
		StoryDeeplink: nil,
		Tag:           commontypes.GetPlainStringText("NEW").WithBgColor("#D9F2CC").WithFontColor("#5D7D4C").WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS),
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Lottie_{
			Lottie: &commontypes.VisualElement_Lottie{
				Source: &commontypes.VisualElement_Lottie_Url{Url: "https://epifi-icons.pointz.in/usstocks_images/Us-Stock-Icon-Animation.json"},
				Properties: &commontypes.VisualElementProperties{
					Width:  32,
					Height: 32,
				},
			}}},
	}, nil
}

// nolint:funlen
func (i *InvestmentInstruments) getUSStocksCard(ctx context.Context, actorId string, usStocksCardSubtitle string) (*fePb.InstrumentCard, error) {

	actorRes, err := i.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})

	err = epifigrpc.RPCError(actorRes, err)
	if err != nil {
		return nil, err
	}

	userRes, err := i.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: actorRes.GetActor().GetEntityId(),
		}})

	err = epifigrpc.RPCError(userRes, err)
	if err != nil {
		return nil, err
	}

	groupResp, err := i.userGroupClient.GetGroupsMappedToEmail(ctx, &usergroupPb.GetGroupsMappedToEmailRequest{
		Email: userRes.GetUser().GetProfile().GetEmail(),
	})
	err = epifigrpc.RPCError(groupResp, err)
	if err != nil {
		return nil, err
	}

	isUSStocksEnabled, err := i.isUSStocksInstrumentEnabled(ctx, actorId, groupResp.GetGroups())
	if err != nil {
		return nil, err
	}

	if !isUSStocksEnabled {
		return nil, nil
	}

	return &fePb.InstrumentCard{
		IconUrl: USStocksIconURL,
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "US Stocks"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: usStocksCardSubtitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_USSTOCKS_LANDING_SCREEN,
		},
		Shadows: []*ui.Shadow{
			{
				Height: 4,
				Blur:   0,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#DDDFE1"}},
			},
		},
		BgColor:       "#FFFFFF",
		StoryDeeplink: nil,
		Tag:           commontypes.GetPlainStringText("NEW").WithBgColor("#D9F2CC").WithFontColor("#5D7D4C").WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS),
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Lottie_{
			Lottie: &commontypes.VisualElement_Lottie{
				Source: &commontypes.VisualElement_Lottie_Url{Url: "https://epifi-icons.pointz.in/usstocks_images/Us-Stock-Icon-Animation.json"},
				Properties: &commontypes.VisualElementProperties{
					Width:  32,
					Height: 32,
				},
			}}},
	}, nil
}

func GetUSStocksCardSubtitle(isUSFundsEnabled bool) string {
	if isUSFundsEnabled {
		return USStocksCardSubTitleV2
	}
	return USStocksCardSubTitle
}

// nolint: unused
func (s *InvestmentInstruments) isUSStocksInstrumentEnabled(ctx context.Context, actorId string,
	userGroups []commontypes.UserGroup) (bool, error) {
	enableUSStocks, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_US_STOCK_UI).WithActorId(actorId).WithUserGroup(userGroups))
	if err != nil {
		return false, err
	}
	return enableUSStocks, nil
}

func (i *InvestmentInstruments) GetMultipleInvestmentLandingComponents(ctx context.Context, actorID string) ([]*fePb.InvestmentLandingComponent, error) {
	return nil, fmt.Errorf("unimplemmented method")
}
