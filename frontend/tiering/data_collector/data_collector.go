//go:generate mockgen -source=$PWD/data_collector.go -destination=$PWD/mocks/mock_data_collector.go -package=mocks
package data_collector

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/protobuf/field_mask"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	enums "github.com/epifi/gamma/api/accounts/balance/enums"
	beActorPb "github.com/epifi/gamma/api/actor"
	alfredPb "github.com/epifi/gamma/api/alfred"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	beCardPb "github.com/epifi/gamma/api/card"
	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/employment"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	beKycPb "github.com/epifi/gamma/api/kyc"
	orderPb "github.com/epifi/gamma/api/order"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	ussAccountPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/frontend/tiering/helper"
	pkgCard "github.com/epifi/gamma/pkg/card"
	chkBookHelper "github.com/epifi/gamma/pkg/chequebook/helper"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
)

// DataCollector collects data from various backend services
type DataCollector interface {
	// GetBalance fetches current balance for user
	GetBalance(ctx context.Context, actorId string) (*gmoney.Money, error)
	// GetKycLevel returns current kyc level for actor
	GetKycLevel(ctx context.Context, actorId string) (beKycPb.KYCLevel, error)
	// GetSalaryStatus returns whether actor is salaried or not
	GetSalaryStatus(ctx context.Context, actorId string) (bool, error)
	// GetTieringEssentials fetches tiering essentials information to be shown to the user
	GetTieringEssentials(ctx context.Context, actorId string) (*helper.TieringFeEssentials, error)
	// GetTierTimeRange fetches list of time ranges when user was in a particular tier
	GetTierTimeRange(ctx context.Context, actorId string, tier beTieringExtPb.Tier) ([]*tieringPb.TimeRange, error)
	// GetDebitCardOrderCharges fetches debit card order charge details to be used in tiering screens
	GetDebitCardOrderCharges(ctx context.Context, actorId string) (*DebitCardOrderChargesData, error)
	// GetForexRefundAtTier fetches forex refund received by the user while at a tier
	GetForexRefundAtTier(ctx context.Context, actorId string, tier beTieringExtPb.Tier, fromTime, toTime time.Time) (*gmoney.Money, error)
	// GetMonthlyForexRefundAtTier fetches forex refund received by the user while at a tier month wise
	GetMonthlyForexRefundAtTier(ctx context.Context, actorId string, tier beTieringExtPb.Tier, monthsToFetch []*MonthYear) ([]*MonthlyForexData, error)
	// GetChequeBookRefundDetails fetches cheque book order details to be used in tiering screens
	GetChequeBookRefundDetails(ctx context.Context, actorId string) (*ChequeBookOrderData, error)
	// GetRewardsDataForEarnedBenefits fetches the rewards data to be used in tiering earned benefits screen
	GetRewardsDataForEarnedBenefits(ctx context.Context, actorId string, tier, higherTierToPitch beTieringExtPb.Tier, timeRangesInTier []*tieringPb.TimeRange) (*RewardsDataForEarnedBenefits, error)
	// GetRewardHistoryForEarnedBenefits fetches rewards data for the months specified in the request
	GetRewardHistoryForEarnedBenefits(ctx context.Context, actorId string, tier beTieringExtPb.Tier, monthsToFetch []*MonthYear) ([]*MonthlyRewardsData, error)

	GetAaSalaryData(ctx context.Context, actorId string) (*AaSalaryData, error)

	GetFiCoinsRewardAggregate(ctx context.Context, actorId string, tier beTieringExtPb.Tier, fromTime, toTime time.Time) (float32, error)
	GetCashRewardAggregate(ctx context.Context, actorId string, tier beTieringExtPb.Tier, fromTime, toTime time.Time) (float32, error)
	GetRewardsByActorId(ctx context.Context, actorId string, tier beTieringExtPb.Tier) (*rewardsPb.RewardsResponse, error)
	GetEmploymentInfo(ctx context.Context, actorId string) (*employment.EmployerInfo, error)
	GetHealthInsuranceDetails(ctx context.Context, actorId string) (*HealthInsuranceDetails, error)
}

var DataCollectorWireSet = wire.NewSet(NewDataCollectorService, wire.Bind(new(DataCollector), new(*DataCollectorService)))

// DataCollectorService implements DataCollector
type DataCollectorService struct {
	gconf                   *genconf.Config
	beSavingsClient         beSavingsPb.SavingsClient
	beActorClient           beActorPb.ActorClient
	beBankCustClient        bankCustomerPb.BankCustomerServiceClient
	beSalaryClient          beSalaryPb.SalaryProgramClient
	accountBalanceClient    accountBalancePb.BalanceClient
	tieringClient           tieringPb.TieringClient
	cardProvisioningClient  cardProvisioningPb.CardProvisioningClient
	alfredClient            alfredPb.AlfredClient
	rewardsClient           rewardsPb.RewardsGeneratorClient
	rewardAggregateClient   rewardsPinotPb.RewardsAggregatesClient
	projectorClient         projectorPb.ProjectorServiceClient
	txnAggregateClient      txnAggregatesPb.TxnAggregatesClient
	orderClient             orderPb.OrderServiceClient
	ussAccountManagerClient ussAccountPb.AccountManagerClient
	beEmploymentClient      employment.EmploymentClient
	beHealthInsuranceClient healthinsurance.HealthInsuranceClient
	rewardOffersClient      rewardOfferPb.RewardOffersClient
	userClient              userPb.UsersClient      // Added for user group check
	userGroupClient         userGroupPb.GroupClient // Added for user group check
}

// NewDataCollectorService to be used for dependency injection
func NewDataCollectorService(
	gconf *genconf.Config,
	beSavingsClient beSavingsPb.SavingsClient,
	beActorClient beActorPb.ActorClient,
	beBankCustClient bankCustomerPb.BankCustomerServiceClient,
	beSalaryClient beSalaryPb.SalaryProgramClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	tieringClient tieringPb.TieringClient,
	cardProvisioningClient cardProvisioningPb.CardProvisioningClient,
	alfredClient alfredPb.AlfredClient,
	rewardsClient rewardsPb.RewardsGeneratorClient,
	rewardAggregateClient rewardsPinotPb.RewardsAggregatesClient,
	projectorClient projectorPb.ProjectorServiceClient,
	txnAggregateClient txnAggregatesPb.TxnAggregatesClient,
	orderClient orderPb.OrderServiceClient,
	ussAccountManagerClient ussAccountPb.AccountManagerClient,
	beEmploymentClient employment.EmploymentClient,
	beHealthInsuranceClient healthinsurance.HealthInsuranceClient,
	rewardOffersClient rewardOfferPb.RewardOffersClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
) *DataCollectorService {
	return &DataCollectorService{
		gconf:                   gconf,
		beSavingsClient:         beSavingsClient,
		beActorClient:           beActorClient,
		beBankCustClient:        beBankCustClient,
		beSalaryClient:          beSalaryClient,
		accountBalanceClient:    accountBalanceClient,
		tieringClient:           tieringClient,
		cardProvisioningClient:  cardProvisioningClient,
		alfredClient:            alfredClient,
		rewardsClient:           rewardsClient,
		rewardAggregateClient:   rewardAggregateClient,
		projectorClient:         projectorClient,
		txnAggregateClient:      txnAggregateClient,
		orderClient:             orderClient,
		ussAccountManagerClient: ussAccountManagerClient,
		beEmploymentClient:      beEmploymentClient,
		beHealthInsuranceClient: beHealthInsuranceClient,
		rewardOffersClient:      rewardOffersClient,
		userClient:              userClient,
		userGroupClient:         userGroupClient,
	}
}

var _ DataCollector = &DataCollectorService{}

// GetBalance fetches current balance for user
func (d *DataCollectorService) GetBalance(ctx context.Context, actorId string) (*gmoney.Money, error) {
	// Get savings account by actor id
	savingsAccResp, savingsAccErr := d.beSavingsClient.GetAccount(ctx, &beSavingsPb.GetAccountRequest{
		Identifier: &beSavingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		}})
	if savingsAccErr != nil || savingsAccResp == nil || savingsAccResp.GetAccount() == nil {
		return nil, errors.Wrap(savingsAccErr, "error fetching savings account by user id")
	}
	historicalBalResp, rpcErr := d.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccResp.GetAccount().GetId()},
		ActorId:       actorId,
		DataFreshness: enums.DataFreshness_HISTORICAL,
	})
	if accBalErr := epifigrpc.RPCError(historicalBalResp, rpcErr); accBalErr != nil {
		return nil, fmt.Errorf("%w, %w", feTieringErrors.ErrAccountBalanceFetch, accBalErr)
	}
	return historicalBalResp.GetAvailableBalance(), nil
}

// GetKycLevel returns current kyc level for actor
func (d *DataCollectorService) GetKycLevel(ctx context.Context, actorId string) (beKycPb.KYCLevel, error) {
	// fetch kyc status for an actor
	bankCustResp, err := d.beBankCustClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if kycStatusErr := epifigrpc.RPCError(bankCustResp, err); kycStatusErr != nil {
		return 0, errors.Wrap(kycStatusErr, "error in collecting kyc data")
	}
	return bankCustResp.GetBankCustomer().GetDedupeInfo().GetKycLevel(), nil
}

// GetSalaryStatus returns whether actor is salaried or not
func (d *DataCollectorService) GetSalaryStatus(ctx context.Context, actorId string) (bool, error) {
	// Get salary program registration status
	salaryRegResp, err := d.beSalaryClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if salaryRegErr := epifigrpc.RPCError(salaryRegResp, err); salaryRegErr != nil {
		return false, errors.Wrap(salaryRegErr, "error fetching salary registration status")
	}

	if salaryRegResp.GetRegistrationStatus() != beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return false, nil
	}

	salaryStatusResp, err := d.beSalaryClient.GetLatestActivationDetailsActiveAtTime(ctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: salaryRegResp.GetRegistrationId(),
		ActiveAtTime:   timestampPb.Now(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
		ActivationKind: beSalaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if salaryStatusErr := epifigrpc.RPCError(salaryStatusResp, err); salaryStatusErr != nil {
		if salaryStatusResp.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		return false, errors.Wrap(salaryStatusErr, "error fetching salary activation details")
	}
	return true, nil
}

func (d *DataCollectorService) GetTierTimeRange(ctx context.Context, actorId string, tier beTieringExtPb.Tier) ([]*tieringPb.TimeRange, error) {
	getTimeRangeResp, getTimeRangeErr := d.tieringClient.GetTierTimeRangesForActor(ctx, &tieringPb.GetTierTimeRangesForActorRequest{
		ActorId:    actorId,
		Tiers:      []beTieringExtPb.Tier{tier},
		FilterFrom: timestampPb.New(time.Now().Add(-d.gconf.Tiering().TierTimeRangeFilterDepth())),
	})
	if rpcErr := epifigrpc.RPCError(getTimeRangeResp, getTimeRangeErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to fetch tier time ranges for actor, %w", rpcErr)
	}

	return getTimeRangeResp.GetTierTimeRangesMap()[tier.String()].GetTimeRanges(), nil
}

func (d *DataCollectorService) GetTieringEssentials(ctx context.Context, actorId string) (*helper.TieringFeEssentials, error) {
	errGrp, gCtx := errgroup.WithContext(ctx)

	var tieringPitchV2Resp *tieringPb.GetTieringPitchV2Response
	errGrp.Go(func() error {
		var tieringPitchV2Err error
		tieringPitchV2Resp, tieringPitchV2Err = d.tieringClient.GetTieringPitchV2(gCtx, &tieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(tieringPitchV2Resp, tieringPitchV2Err); rpcErr != nil {
			return fmt.Errorf("GetTieringPitchV2 rpc failed, %w", rpcErr)
		}

		return nil
	})

	var configParamsResp *tieringPb.GetConfigParamsResponse
	errGrp.Go(func() error {
		var configParamsErr error
		configParamsResp, configParamsErr = d.tieringClient.GetConfigParams(gCtx, &tieringPb.GetConfigParamsRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(configParamsResp, configParamsErr); rpcErr != nil {
			return fmt.Errorf("GetConfigParams rpc failed, %w", rpcErr)
		}

		return nil
	})

	var isUSStocksAccountActive bool
	errGrp.Go(func() error {
		getAccResp, getAccErr := d.ussAccountManagerClient.GetAccount(gCtx, &ussAccountPb.GetAccountRequest{
			Vendor:    commonvgpb.Vendor_ALPACA,
			ActorId:   actorId,
			FieldMask: &field_mask.FieldMask{Paths: []string{(&ussAccountPb.Account{}).GetAccountAccountStatusPath()}},
		})
		if rpcErr := epifigrpc.RPCError(getAccResp, getAccErr); rpcErr != nil {
			if getAccResp.GetStatus().IsRecordNotFound() {
				isUSStocksAccountActive = false
			} else {
				logger.Error(context.Background(), "error fetching USStocks account for Tier All plans V2", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId))
				return rpcErr
			}
		}
		if getAccResp.GetAccount().GetAccountStatus() == ussAccountPb.AccountStatus_ACTIVE {
			isUSStocksAccountActive = true
		} else {
			isUSStocksAccountActive = false
		}
		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("tiering essentials error group failed, %w", err)
	}

	tieringFeEssentials, tieringFeEssentialsErr := helper.GetTieringFeEssentialsFromResp(tieringPitchV2Resp, configParamsResp)
	tieringFeEssentials.IsUSStocksAccountActive = isUSStocksAccountActive

	return tieringFeEssentials, tieringFeEssentialsErr
}

func (d *DataCollectorService) getTierAtTime(ctx context.Context, actorId string, timestamp time.Time) (beTieringExtPb.Tier, error) {
	tierAtOrderTimeResp, getTierAtTimeErr := d.tieringClient.GetTierAtTime(ctx, &tieringPb.GetTierAtTimeRequest{
		ActorId:       actorId,
		TierTimestamp: timestampPb.New(timestamp),
	})
	if rpcErr := epifigrpc.RPCError(tierAtOrderTimeResp, getTierAtTimeErr); rpcErr != nil {
		return beTieringExtPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("GetTierAtTime rpc failed, %w", rpcErr)
	}

	return tierAtOrderTimeResp.GetTierInfo().GetTier(), nil
}

func (d *DataCollectorService) GetDebitCardOrderCharges(ctx context.Context, actorId string) (*DebitCardOrderChargesData, error) {
	hasOrderedDc, orderedAt, chargePaidByUser, orderedDcErr := d.fetchActorOrderedDc(ctx, actorId)
	if orderedDcErr != nil {
		return nil, fmt.Errorf("failed to check if actor ordered dc, %w", orderedDcErr)
	}

	if !hasOrderedDc {
		debitCardDeeplinkResp, debitCardDeeplinkErr := d.cardProvisioningClient.FetchPhysicalCardChargesForUser(ctx, &cardProvisioningPb.FetchPhysicalCardChargesForUserRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(debitCardDeeplinkResp, debitCardDeeplinkErr); rpcErr != nil {
			if debitCardDeeplinkResp.GetStatus().IsFailedPrecondition() {
				// todo[chandresh]: add handling to show renew card screen for this set of users
				return nil, nil
			}

			return nil, errors.Wrap(rpcErr, "FetchPhysicalCardChargesForUser rpc failed")
		}

		return &DebitCardOrderChargesData{
			HasOrderedDebitCard: hasOrderedDc,
			DeeplinkToOrderCard: debitCardDeeplinkResp.GetNextAction(),
		}, nil
	}

	tierAtOrderedTime, getTierErr := d.getTierAtTime(ctx, actorId, orderedAt)
	if getTierErr != nil {
		return nil, fmt.Errorf("failed to get tier at time, %w", getTierErr)
	}

	actualCardCharge := pkgCard.ActualPhysicalDCCharges
	discountedAmount := money.ZeroINR().GetPb()

	if chargePaidByUser == nil {
		chargePaidByUser = money.ZeroINR().GetPb()
	}

	var subErr error
	discountedAmount, subErr = money.Subtract(actualCardCharge, chargePaidByUser)
	if subErr != nil {
		return nil, fmt.Errorf("failed to sub charge paid by user from actual card charge, %w", subErr)
	}

	return &DebitCardOrderChargesData{
		HasOrderedDebitCard: hasOrderedDc,
		OrderedAt:           orderedAt,
		ActualCardCharge:    actualCardCharge,
		UserPaidCharge:      chargePaidByUser,
		DiscountedAmount:    discountedAmount,
		TierAtOrderedTime:   tierAtOrderedTime,
	}, nil
}

func (d *DataCollectorService) fetchActorOrderedDc(ctx context.Context, actorId string) (hasOrdered bool, orderedAt time.Time, chargePaidByUser *gmoney.Money, err error) {
	dispatchRequests, dispatchReqErr := d.cardProvisioningClient.FetchPhysicalCardDispatchRequests(ctx, &cardProvisioningPb.FetchPhysicalCardDispatchRequestsRequest{
		CardIdentifier: &cardProvisioningPb.CardIdentifier{
			Identifier: &cardProvisioningPb.CardIdentifier_ActorId{
				ActorId: actorId,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(dispatchRequests, dispatchReqErr); rpcErr != nil && !dispatchRequests.GetStatus().IsRecordNotFound() {
		return false, time.Time{}, nil, errors.Wrap(rpcErr, "FetchPhysicalCardDispatchRequests rpc failed")
	}

	if !dispatchRequests.GetStatus().IsRecordNotFound() {
		orderedAt, chargePaidByUser, err = d.getDebitCardOrderedAtAndChargesPaid(ctx, dispatchRequests.GetPhysicalCardDispatchRequests())
		if err != nil {
			return false, orderedAt, nil, fmt.Errorf("failed to get dc ordered at and charges paid, %w", err)
		}

		return true, orderedAt, chargePaidByUser, nil
	}

	if dispatchRequests.GetStatus().IsRecordNotFound() {
		// some old users might be having a physical debit card since before physical charges flow was enabled, added a card form check to exclude such users
		cards, cardFetchErr := d.cardProvisioningClient.FetchCards(ctx, &cardProvisioningPb.FetchCardsRequest{
			Actor:            &typesPb.Actor{Id: actorId},
			IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
			CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
			CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
			CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
			CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
			SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
		})
		if te := epifigrpc.RPCError(cards, cardFetchErr); te != nil {
			if cards.GetStatus().IsRecordNotFound() {
				return false, time.Time{}, nil, nil
			}
			return false, time.Time{}, nil, errors.Wrap(te, "error in FetchCards rpc")
		}
		if cards.GetCards()[0].GetForm() == beCardPb.CardForm_PHYSICAL {
			orderedAt = cards.GetCards()[0].GetCreatedAt().AsTime() // approximate time of ordering
			return true, orderedAt, nil, nil
		}
	}

	return false, time.Time{}, nil, nil
}

func (d *DataCollectorService) getDebitCardOrderedAtAndChargesPaid(ctx context.Context, dispatchRequests []*cardProvisioningPb.PhysicalCardDispatchRequest) (orderedAt time.Time, chargePaidByUser *gmoney.Money, err error) {
	for _, dispatchRequest := range dispatchRequests {
		if dispatchRequest.GetState() == cardProvisioningPb.RequestState_FAILED || dispatchRequest.GetState() == cardProvisioningPb.RequestState_REQUEST_STATE_UNSPECIFIED {
			continue
		}

		orderedAt = dispatchRequest.GetCreatedAt().AsTime() // approximate ordered at

		// skip fetching order charges if user ordered dc before time window
		if time.Since(orderedAt) > d.gconf.Tiering().FetchDebitCardOrderChargesTimeWindow() {
			return orderedAt, nil, nil
		}

		chargePaidByUser = money.ZeroINR().GetPb()
		if dispatchRequest.GetDetails().GetAmount() != nil {
			chargePaidByUser = dispatchRequest.GetDetails().GetAmount()
		}
		if money.IsZero(chargePaidByUser) && dispatchRequest.GetDetails().GetFundTransferClientReqId() != "" {
			getOrderResp, getOrderErr := d.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{
				ClientReqId: dispatchRequest.GetDetails().GetFundTransferClientReqId(),
			}})
			if rpcErr := epifigrpc.RPCError(getOrderResp, getOrderErr); rpcErr != nil {
				return time.Time{}, nil, fmt.Errorf("GetOrder rpc failed, %w", rpcErr)
			}

			chargePaidByUser = getOrderResp.GetOrder().GetAmount()
		}

		return orderedAt, chargePaidByUser, nil
	}

	return time.Time{}, nil, nil
}

func (d *DataCollectorService) GetForexRefundAtTier(ctx context.Context, actorId string, tier beTieringExtPb.Tier, fromTime, toTime time.Time) (*gmoney.Money, error) {
	forexRefundAggregateResp, forexRefundErr := d.cardProvisioningClient.FetchForexRefundAggregates(ctx, &cardProvisioningPb.FetchForexRefundAggregatesRequest{
		ActorId:   actorId,
		StartTime: timestampPb.New(fromTime),
		EndTime:   timestampPb.New(toTime),
		Tiers:     []beTieringExtPb.Tier{tier},
	})
	if rpcErr := epifigrpc.RPCError(forexRefundAggregateResp, forexRefundErr); rpcErr != nil {
		return nil, fmt.Errorf("FetchForexRefundAggregates rpc failed, %w", rpcErr)
	}

	forexRefund := forexRefundAggregateResp.GetRefundAggregates()[tier.String()]
	if forexRefund == nil || money.IsNegative(forexRefund) {
		forexRefund = money.ZeroINR().GetPb()
	}

	return forexRefund, nil
}

func (d *DataCollectorService) GetMonthlyForexRefundAtTier(ctx context.Context, actorId string, tier beTieringExtPb.Tier, monthsToFetch []*MonthYear) ([]*MonthlyForexData, error) {
	monthlyForexDataChannel := make(chan *MonthlyForexData, len(monthsToFetch))

	errGrp, gCtx := errgroup.WithContext(ctx)
	for _, monthToFetch := range monthsToFetch {
		monthToFetch := monthToFetch
		errGrp.Go(func() error {

			fromTime := monthToFetch.GetStartOfMonth()
			endTime := monthToFetch.GetEndOfMonth()

			forexRefund, getForexErr := d.GetForexRefundAtTier(gCtx, actorId, tier, fromTime, endTime)
			if getForexErr != nil {
				return fmt.Errorf("failed to get forex refund at tier, %w", getForexErr)
			}

			monthlyForexDataChannel <- &MonthlyForexData{
				MonthYear:   monthToFetch,
				ForexRefund: forexRefund,
			}

			return nil
		})
	}

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to get monthly forex refund data in err group, %w", err)
	}

	close(monthlyForexDataChannel)
	var monthlyForexRefundList []*MonthlyForexData
	for monthlyForexRefund := range monthlyForexDataChannel {
		monthlyForexRefundList = append(monthlyForexRefundList, monthlyForexRefund)
	}

	sort.Slice(monthlyForexRefundList, func(i, j int) bool {
		return monthlyForexRefundList[i].GetMonthYear().After(monthlyForexRefundList[j].GetMonthYear())
	})

	return monthlyForexRefundList, nil
}

// nolint: funlen
func (d *DataCollectorService) GetChequeBookRefundDetails(ctx context.Context, actorId string) (*ChequeBookOrderData, error) {
	errGrp, gCtx := errgroup.WithContext(ctx)

	var getChqBkReqRes *alfredPb.GetAllRequestStatusDetailsResponse
	errGrp.Go(func() error {
		var getChqBkErr error
		getChqBkReqRes, getChqBkErr = d.alfredClient.GetAllRequestStatusDetails(gCtx, &alfredPb.GetAllRequestStatusDetailsRequest{
			Filters: &alfredPb.Filters{
				ActorId:      actorId,
				RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
				StatusList:   []alfredPb.Status{alfredPb.Status_STATUS_SUCCESS, alfredPb.Status_STATUS_IN_PROGRESS},
			},
			PageContext: &rpc.PageContextRequest{PageSize: 30},
			SortOrder:   0,
		})
		if rpcErr := epifigrpc.RPCError(getChqBkReqRes, getChqBkErr); rpcErr != nil && !getChqBkReqRes.GetStatus().IsRecordNotFound() {
			return errors.Wrap(rpcErr, "GetAllRequestStatusDetails rpc failed")
		}

		return nil
	})

	var getRewardsResp *rewardsPb.RewardsResponse
	errGrp.Go(func() error {
		var getRewardsErr error
		getRewardsResp, getRewardsErr = d.rewardsClient.GetRewardsByActorId(gCtx, &rewardsPb.RewardsByActorIdRequest{
			ActorId:     actorId,
			PageContext: &rpc.PageContextRequest{PageSize: 30},
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					RewardOfferId: rewardsPkg.ChequeBookFeesRefundRewardOfferId,
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(getRewardsResp, getRewardsErr); rpcErr != nil {
			return errors.Wrap(rpcErr, "GetRewardsByActorId rpc failed")
		}

		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch data, %w", err)
	}

	chequeBookOrderData := &ChequeBookOrderData{}

	chequeBookOrderData.HasOrderedChequeBook = d.hasOrderedChequeBook(getChqBkReqRes)
	if !chequeBookOrderData.HasOrderedChequeBook {
		chequeBookOrderData.DeeplinkToOrderChequebook = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_ALFRED_REQUEST_CHOICE}
		return chequeBookOrderData, nil
	}

	chequeBookOrderData.HasGotRefund = d.hasGotChequeBookRefund(getRewardsResp)
	if !chequeBookOrderData.GetHasGotRefund() {
		return chequeBookOrderData, nil
	}

	eligibleForRefund, orderedAt := d.getRefundEligibleChequebookOrder(getChqBkReqRes)
	chequeBookOrderData.EligibleForRefund = eligibleForRefund

	if eligibleForRefund {
		chequeBookOrderData.OrderedAt = orderedAt
		chequeBookOrderData.ChequeBookChargesAtOrderTime = chkBookHelper.GetBalRequiredToOrderChequeBook().GetBeMoney()

		tierAtTimeResp, tierAtTimeErr := d.tieringClient.GetTierAtTime(ctx, &tieringPb.GetTierAtTimeRequest{
			ActorId:       actorId,
			TierTimestamp: timestampPb.New(orderedAt),
		})
		if rpcErr := epifigrpc.RPCError(tierAtTimeResp, tierAtTimeErr); rpcErr != nil {
			return nil, fmt.Errorf("GetTierAtTime rpc failed, %w", rpcErr)
		}

		chequeBookOrderData.TierAtOrderTime = tierAtTimeResp.GetTierInfo().GetTier()
	}

	return chequeBookOrderData, nil
}

func (d *DataCollectorService) hasOrderedChequeBook(getChqBkReqResp *alfredPb.GetAllRequestStatusDetailsResponse) bool {
	if getChqBkReqResp.GetStatus().IsRecordNotFound() {
		return false
	}

	for _, sr := range getChqBkReqResp.GetServiceRequestList() {
		if sr.GetStatus() == alfredPb.Status_STATUS_SUCCESS {
			return true
		}

		if sr.GetStatus() == alfredPb.Status_STATUS_IN_PROGRESS && sr.GetDetails().GetChequebookMetadata().GetOrderedAt() != nil {
			return true
		}
	}

	return false
}

func (d *DataCollectorService) getRefundEligibleChequebookOrder(getChqBkReqResp *alfredPb.GetAllRequestStatusDetailsResponse) (eligibleForRefund bool, orderedAt time.Time) {
	if getChqBkReqResp.GetStatus().IsRecordNotFound() {
		return false, time.Time{}
	}

	for _, sr := range getChqBkReqResp.GetServiceRequestList() {
		if sr.GetStatus() == alfredPb.Status_STATUS_SUCCESS && money.IsZero(sr.GetDetails().GetChequebookMetadata().GetCharges().GetBeMoney()) {
			return true, sr.GetDetails().GetChequebookMetadata().GetOrderedAt().AsTime()
		}

		if sr.GetStatus() == alfredPb.Status_STATUS_IN_PROGRESS &&
			sr.GetDetails().GetChequebookMetadata().GetOrderedAt() != nil &&
			money.IsZero(sr.GetDetails().GetChequebookMetadata().GetCharges().GetBeMoney()) {
			return true, sr.GetDetails().GetChequebookMetadata().GetOrderedAt().AsTime()
		}
	}

	return false, time.Time{}
}

func (d *DataCollectorService) hasGotChequeBookRefund(getRewardsResp *rewardsPb.RewardsResponse) bool {
	return len(getRewardsResp.GetRewards()) > 0
}

// nolint: funlen,dupl
func (d *DataCollectorService) GetRewardsDataForEarnedBenefits(ctx context.Context, actorId string, tier, higherTierToPitch beTieringExtPb.Tier, timeRangesInTier []*tieringPb.TimeRange) (*RewardsDataForEarnedBenefits, error) {
	currMonth := GetMonthYear(time.Now())
	prevMonth := GetMonthYear(datetime.PreviousMonth(time.Now()))
	timeRangeInTierForCurrDay := getTimeRangesForDay(timeRangesInTier, time.Now().Day(), time.Now().Month(), time.Now().Year())

	errGrp, gCtx := errgroup.WithContext(ctx)
	var currMonthProjectedFiCoins, currMonthProjectedCash float32
	currMonthProjectedCash = 0 // marking this as 0 since cashback rewards are stopped
	errGrp.Go(func() error {
		var fiCoinsErr error
		currMonthProjectedFiCoins, fiCoinsErr = d.getCustomRewardProjectionsAggregate(gCtx, actorId, rewardsPb.RewardType_FI_COINS, tier, currMonth)
		if fiCoinsErr != nil {
			return fmt.Errorf("failed to get custom projection aggregate for fi coins, %w", fiCoinsErr)
		}

		return nil
	})

	var currDayProjectedCash float32
	errGrp.Go(func() error {
		var currDayProjectionErr error
		_, currDayProjectedCash, currDayProjectionErr = d.getRewardProjectionAggregate(gCtx, actorId, timeRangeInTierForCurrDay)
		if currDayProjectionErr != nil {
			return fmt.Errorf("failed to get reward curr day projection aggregate, %w", currDayProjectionErr)
		}

		return nil
	})

	var currMonthRealisedFiCoins float32
	errGrp.Go(func() error {
		var currMonthRealisedFiCoinsErr error
		currMonthRealisedFiCoins, currMonthRealisedFiCoinsErr = d.GetFiCoinsRewardAggregate(gCtx, actorId, tier, currMonth.GetStartOfMonth(), currMonth.GetEndOfMonth())
		if currMonthRealisedFiCoinsErr != nil {
			return fmt.Errorf("failed to get curr month fi coins reward aggregate, %w", currMonthRealisedFiCoinsErr)
		}

		return nil
	})

	var currMonthRealisedCash float32
	errGrp.Go(func() error {
		var currMonthRealisedCashErr error
		currMonthRealisedCash, currMonthRealisedCashErr = d.GetCashRewardAggregate(gCtx, actorId, tier, currMonth.GetStartOfMonth(), currMonth.GetEndOfMonth())
		if currMonthRealisedCashErr != nil {
			return fmt.Errorf("failed to get curr month cash reward aggregate, %w", currMonthRealisedCashErr)
		}

		return nil
	})

	var lifeTimeRealisedFiCoins float32
	errGrp.Go(func() error {
		var lifetimeFiCoinsErr error
		lifeTimeRealisedFiCoins, lifetimeFiCoinsErr = d.GetFiCoinsRewardAggregate(gCtx, actorId, tier, time.Now().Add(-d.gconf.Tiering().EarnedBenefitsHistoryDepth()), time.Now())
		if lifetimeFiCoinsErr != nil {
			return fmt.Errorf("failed to get lifetime fi coins reward aggregate, %w", lifetimeFiCoinsErr)
		}

		return nil
	})

	var lifeTimeRealisedCash float32
	errGrp.Go(func() error {
		var lifetimeCashErr error
		lifeTimeRealisedCash, lifetimeCashErr = d.GetCashRewardAggregate(gCtx, actorId, tier, time.Now().Add(-d.gconf.Tiering().EarnedBenefitsHistoryDepth()), time.Now())
		if lifetimeCashErr != nil {
			return fmt.Errorf("failed to get lifetime cash reward aggregate, %w", lifetimeCashErr)
		}

		return nil
	})

	var prevMonthRealisedFiCoins float32
	errGrp.Go(func() error {
		var prevMonthRealisedFiCoinsErr error
		prevMonthRealisedFiCoins, prevMonthRealisedFiCoinsErr = d.GetFiCoinsRewardAggregate(gCtx, actorId, tier, prevMonth.GetStartOfMonth(), prevMonth.GetEndOfMonth())
		if prevMonthRealisedFiCoinsErr != nil {
			return fmt.Errorf("failed to get prev month fi coins reward aggregate, %w", prevMonthRealisedFiCoinsErr)
		}

		return nil
	})

	var prevMonthRealisedCash float32
	errGrp.Go(func() error {
		var prevMonthRealisedErr error
		prevMonthRealisedCash, prevMonthRealisedErr = d.GetCashRewardAggregate(gCtx, actorId, tier, prevMonth.GetStartOfMonth(), prevMonth.GetEndOfMonth())
		if prevMonthRealisedErr != nil {
			return fmt.Errorf("failed to get prev month cash reward aggregate, %w", prevMonthRealisedErr)
		}

		return nil
	})

	var currMonthEstFiCoinsInHigherTier float32
	var currMonthEstCashInHigherTier *gmoney.Money
	errGrp.Go(func() error {
		estRewardsResp, estRewardsErr := d.rewardsClient.GetEstimatedRewardsInTimeDurationForTier(gCtx, &rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest{
			ActorId:  actorId,
			UserTier: rewardsPkg.GetRewardsTierEnum(higherTierToPitch),
			TimeWindow: &rewardsPb.TimeWindow{
				FromTime: timestampPb.New(datetime.StartOfMonth(time.Now())),
				TillTime: timestampPb.New(datetime.EndOfMonth(time.Now())),
			},
		})
		if rpcErr := epifigrpc.RPCError(estRewardsResp, estRewardsErr); rpcErr != nil {
			return fmt.Errorf("GetEstimatedRewardsInTimeDurationForTier rpc failed, %w", rpcErr)
		}

		currMonthEstFiCoinsInHigherTier = float32(estRewardsResp.GetAnchorFiCoinRewards())
		currMonthEstCashInHigherTier = &gmoney.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        int64(math.Ceil(float64(estRewardsResp.GetCashbackReward()))),
		}

		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch data in err group, %w", err)
	}

	lifetimeCashRewards := currMonthProjectedCash + lifeTimeRealisedCash

	lifeTimeFiCoinsToCashUnitsConverted := lifeTimeRealisedFiCoins * rewardsPkg.FiCoinsToCashConversionRatio
	lifeTimeRewardUnits := lifeTimeFiCoinsToCashUnitsConverted + lifeTimeRealisedCash + currMonthProjectedCash

	monthlyCashRewardUpperCap := getMoneyForCashUnits(float32(rewardsPkg.BeTierToCashbackMonthlyCapMap[tier]))
	dailyCashRewardUpperCap := getMoneyForCashUnits(float32(rewardsPkg.BeTierToCashbackDailyCapMap[tier]))
	monthlyFiCoinsUpperCap := rewardsPkg.BeTierToMaxFiCoinsAnchorRewardsForCardSpendsCapMap[tier] +
		rewardsPkg.BeTierToMaxFiCoinsAnchorRewardsForUpiSpendsCapMap[tier]

	return &RewardsDataForEarnedBenefits{
		CurrMonthProjectedCash:          getMoneyForCashUnits(currMonthProjectedCash),
		CurrMonthProjectedFiCoins:       currMonthProjectedFiCoins,
		CurrMonthRealisedCash:           getMoneyForCashUnits(currMonthRealisedCash),
		CurrDayProjectedCash:            getMoneyForCashUnits(currDayProjectedCash),
		CurrMonthFiCoins:                currMonthRealisedFiCoins,
		PrevMonthCash:                   getMoneyForCashUnits(prevMonthRealisedCash),
		PrevMonthFiCoins:                prevMonthRealisedFiCoins,
		LifetimeFiCoins:                 lifeTimeRealisedFiCoins,
		LifetimeRealisedCash:            getMoneyForCashUnits(lifeTimeRealisedCash),
		LifetimeCash:                    getMoneyForCashUnits(lifetimeCashRewards),
		LifetimeTotalRewards:            getMoneyForCashUnits(lifeTimeRewardUnits),
		CurrMonthEstFiCoinsInHigherTier: currMonthEstFiCoinsInHigherTier,
		CurrMonthEstCashInHigherTier:    currMonthEstCashInHigherTier,
		MonthlyFiCoinsUpperCap:          int32(monthlyFiCoinsUpperCap),
		MonthlyCashRewardUpperCap:       monthlyCashRewardUpperCap,
		DailyCashRewardUpperCap:         dailyCashRewardUpperCap,
	}, nil
}

func (d *DataCollectorService) GetRewardHistoryForEarnedBenefits(ctx context.Context, actorId string, tier beTieringExtPb.Tier, monthsToFetch []*MonthYear) ([]*MonthlyRewardsData, error) {
	monthlyRewardsDataChannel := make(chan *MonthlyRewardsData, len(monthsToFetch))

	errGrp, gCtx := errgroup.WithContext(ctx)
	for _, monthToFetch := range monthsToFetch {
		monthToFetch := monthToFetch
		errGrp.Go(func() error {
			monthlyRewardsData, getRewardsErr := d.getRewardsDataForMonth(gCtx, actorId, tier, monthToFetch)
			if getRewardsErr != nil {
				return fmt.Errorf("failed to get rewards data for month, %w", getRewardsErr)
			}

			monthlyRewardsDataChannel <- monthlyRewardsData
			return nil
		})
	}

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to get rewards data for month in err group, %w", err)
	}

	close(monthlyRewardsDataChannel)
	var monthlyRewardsList []*MonthlyRewardsData
	for monthlyReward := range monthlyRewardsDataChannel {
		monthlyRewardsList = append(monthlyRewardsList, monthlyReward)
	}

	sort.Slice(monthlyRewardsList, func(i, j int) bool {
		return monthlyRewardsList[i].GetMonthYear().After(monthlyRewardsList[j].GetMonthYear())
	})

	return monthlyRewardsList, nil
}

func (d *DataCollectorService) getRewardsDataForMonth(ctx context.Context, actorId string, tier beTieringExtPb.Tier, monthToFetch *MonthYear) (*MonthlyRewardsData, error) {
	monthlyRewardsData := &MonthlyRewardsData{
		MonthYear: monthToFetch,
	}

	errGrp, gCtx := errgroup.WithContext(ctx)
	var rewardedFiCoins float32
	errGrp.Go(func() error {
		var rewardedFiCoinsErr error
		rewardedFiCoins, rewardedFiCoinsErr = d.GetFiCoinsRewardAggregate(gCtx, actorId, tier, monthToFetch.GetStartOfMonth(), monthToFetch.GetEndOfMonth())
		if rewardedFiCoinsErr != nil {
			return fmt.Errorf("failed to getfi coins reward aggregate for month, %w", rewardedFiCoinsErr)
		}

		return nil
	})

	var rewardedCash float32
	errGrp.Go(func() error {
		var currMonthRealisedCashErr error
		rewardedCash, currMonthRealisedCashErr = d.GetCashRewardAggregate(gCtx, actorId, tier, monthToFetch.GetStartOfMonth(), monthToFetch.GetEndOfMonth())
		if currMonthRealisedCashErr != nil {
			return fmt.Errorf("failed to get curr month cash reward aggregate, %w", currMonthRealisedCashErr)
		}

		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed get monthly reward in err group, %w", err)
	}

	monthlyRewardsData.FiCoins = rewardedFiCoins
	monthlyRewardsData.Cash = getMoneyForCashUnits(rewardedCash)

	fiCoinsConvertedCash := monthlyRewardsData.GetFiCoins() * rewardsPkg.FiCoinsToCashConversionRatio
	monthlyRewardsData.TotalRewards = getMoneyForCashUnits(fiCoinsConvertedCash + rewardedCash)

	return monthlyRewardsData, nil
}

func getMoneyForCashUnits(cashUnits float32) *gmoney.Money {
	units := int64(math.Ceil(float64(cashUnits)))
	return &gmoney.Money{
		CurrencyCode: money.RupeeCurrencyCode,
		Units:        units,
		Nanos:        0,
	}
}

func getTimeRangesForDay(timeRangesInTier []*tieringPb.TimeRange, day int, month time.Month, year int) []*tieringPb.TimeRange {
	var filteredTimeRange []*tieringPb.TimeRange

	// Create start and end of the requested day
	startOfDay := time.Date(year, month, day, 0, 0, 0, 0, datetime.IST)
	endOfDay := time.Date(year, month, day, 23, 59, 59, 999999999, datetime.IST)

	for _, timeRange := range timeRangesInTier {
		fromTime := timeRange.GetFromTime().AsTime().In(datetime.IST)
		toTime := timeRange.GetToTime().AsTime().In(datetime.IST)

		// Case 1: Time range completely encompasses the requested day
		if fromTime.Before(startOfDay) && toTime.After(endOfDay) {
			filteredTimeRange = append(filteredTimeRange, &tieringPb.TimeRange{
				FromTime: timestampPb.New(startOfDay),
				ToTime:   timestampPb.New(endOfDay),
			})
			continue
		}

		// Case 2: Time range is completely outside the requested day
		if !isSameDay(fromTime, startOfDay) && !isSameDay(toTime, startOfDay) {
			continue
		}

		// Case 3: Time range starts before the day but ends within the day
		if !isSameDay(fromTime, startOfDay) && isSameDay(toTime, startOfDay) {
			filteredTimeRange = append(filteredTimeRange, &tieringPb.TimeRange{
				FromTime: timestampPb.New(startOfDay),
				ToTime:   timeRange.GetToTime(),
			})
			continue
		}

		// Case 4: Time range starts within the day but ends after the day
		if isSameDay(fromTime, startOfDay) && !isSameDay(toTime, startOfDay) {
			filteredTimeRange = append(filteredTimeRange, &tieringPb.TimeRange{
				FromTime: timeRange.GetFromTime(),
				ToTime:   timestampPb.New(endOfDay),
			})
			continue
		}

		// Case 5: Time range is completely within the day
		if isSameDay(fromTime, startOfDay) && isSameDay(toTime, startOfDay) {
			filteredTimeRange = append(filteredTimeRange, timeRange)
			continue
		}
	}

	return filteredTimeRange
}

// Helper function to check if two times are on the same day
func isSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// filters time range for the requested month
func getTimeRangesForMonth(timeRangeInTier []*tieringPb.TimeRange, reqMonthYear *MonthYear) []*tieringPb.TimeRange {
	var filteredTimeRange []*tieringPb.TimeRange

	startOfReqMonth := reqMonthYear.GetStartOfMonth()
	endOfReqMonth := reqMonthYear.GetEndOfMonth()

	for _, timeRange := range timeRangeInTier {
		fromTime := timeRange.GetFromTime().AsTime().In(datetime.IST)
		toTime := timeRange.GetToTime().AsTime().In(datetime.IST)

		if fromTime.Before(startOfReqMonth) && toTime.After(endOfReqMonth) {
			filteredTimeRange = append(filteredTimeRange, &tieringPb.TimeRange{
				FromTime: timestampPb.New(startOfReqMonth),
				ToTime:   timestampPb.New(endOfReqMonth),
			})

			continue
		}

		if !IsMonthYearEqual(GetMonthYear(fromTime), reqMonthYear) && !IsMonthYearEqual(GetMonthYear(toTime), reqMonthYear) {
			continue
		}

		if !IsMonthYearEqual(GetMonthYear(fromTime), reqMonthYear) && IsMonthYearEqual(GetMonthYear(toTime), reqMonthYear) {
			startOfMonth := reqMonthYear.GetStartOfMonth()
			filteredTimeRange = append(filteredTimeRange, &tieringPb.TimeRange{
				FromTime: timestampPb.New(startOfMonth),
				ToTime:   timeRange.GetToTime(),
			})

			continue
		}

		if IsMonthYearEqual(GetMonthYear(fromTime), reqMonthYear) && !IsMonthYearEqual(GetMonthYear(toTime), reqMonthYear) {
			endOfMonth := reqMonthYear.GetEndOfMonth()
			filteredTimeRange = append(filteredTimeRange, &tieringPb.TimeRange{
				FromTime: timeRange.GetFromTime(),
				ToTime:   timestampPb.New(endOfMonth),
			})

			continue
		}

		if IsMonthYearEqual(GetMonthYear(fromTime), reqMonthYear) && IsMonthYearEqual(GetMonthYear(toTime), reqMonthYear) {
			filteredTimeRange = append(filteredTimeRange, timeRange)
			continue
		}
	}

	return filteredTimeRange
}

func getTimeWindowFromTimeRanges(timeRanges []*tieringPb.TimeRange) []*rewardsPb.TimeWindow {
	var timeWindows []*rewardsPb.TimeWindow
	for _, timeRange := range timeRanges {
		timeWindows = append(timeWindows, &rewardsPb.TimeWindow{
			FromTime: timeRange.GetFromTime(),
			TillTime: timeRange.GetToTime(),
		})
	}

	return timeWindows
}

// getTimeRangesForWholeMonth takes converts time ranges into full month for months present in timeRanges
// refer unit tests for documentation
func getTimeRangesForWholeMonth(timeRanges []*tieringPb.TimeRange) []*tieringPb.TimeRange {
	if len(timeRanges) == 0 {
		return nil
	}

	var wholeMonthTimeRanges []*tieringPb.TimeRange
	for _, timeRange := range timeRanges {
		fromTime := datetime.StartOfMonth(timeRange.GetFromTime().AsTime().In(datetime.IST))
		toTime := datetime.EndOfMonth(timeRange.GetToTime().AsTime().In(datetime.IST))

		for iter := fromTime; iter.Before(toTime); iter = iter.AddDate(0, 1, 0) {
			wholeMonthTimeRanges = append(wholeMonthTimeRanges, &tieringPb.TimeRange{
				FromTime: timestampPb.New(datetime.StartOfMonth(iter)),
				ToTime:   timestampPb.New(datetime.EndOfMonth(iter)),
			})
		}
	}

	// remove duplicates
	sort.Slice(wholeMonthTimeRanges, func(i, j int) bool {
		return wholeMonthTimeRanges[i].GetFromTime().AsTime().Before(wholeMonthTimeRanges[j].GetFromTime().AsTime())
	})

	var filtered []*tieringPb.TimeRange
	filtered = append(filtered, wholeMonthTimeRanges[0])
	for idx := 1; idx < len(wholeMonthTimeRanges); idx++ {
		if wholeMonthTimeRanges[idx].GetFromTime().AsTime().Equal(wholeMonthTimeRanges[idx-1].GetFromTime().AsTime()) {
			continue
		}

		filtered = append(filtered, wholeMonthTimeRanges[idx])
	}

	return filtered
}

// getRewardProjectionAggregate does not apply daily cap on rewards being earned,
// use getCustomRewardProjectionsAggregate if daily and monthly cap needs to be applied while fetching
func (d *DataCollectorService) getRewardProjectionAggregate(ctx context.Context, actorId string, timeRanges []*tieringPb.TimeRange) (projectedFiCoins, projectedCash float32, err error) {
	if len(timeRanges) == 0 {
		return 0, 0, nil
	}

	getProjectionResp, getProjectionErr := d.projectorClient.GetProjectionAggregates(ctx, &projectorPb.GetProjectionAggregatesRequest{
		ActorId: actorId,
		Filters: &projectorPb.GetProjectionAggregatesRequest_Filters{
			ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER},
			TimeWindows: getTimeWindowFromTimeRanges(timeRanges),
		},
	})
	if rpcErr := epifigrpc.RPCError(getProjectionResp, getProjectionErr); rpcErr != nil {
		return 0, 0, fmt.Errorf("GetProjectionAggregates rpc failed, %w", rpcErr)
	}

	for _, aggregate := range getProjectionResp.GetAggregates() {
		switch aggregate.GetRewardType() {
		case rewardsPb.RewardType_CASH:
			projectedCash = aggregate.GetProjectedRewardUnits()
		case rewardsPb.RewardType_FI_COINS:
			projectedFiCoins = aggregate.GetProjectedRewardUnits()
		default:
		}
	}

	if projectedCash < 0 {
		projectedCash = 0
	}

	if projectedFiCoins < 0 {
		projectedFiCoins = 0
	}

	return projectedFiCoins, projectedCash, nil
}

func (d *DataCollectorService) GetFiCoinsRewardAggregate(ctx context.Context, actorId string, tier beTieringExtPb.Tier, fromTime, toTime time.Time) (rewardedFiCoins float32, err error) {
	// for generated fi coin rewards (and power up rewards)
	rewardAggregateResp, rewardAggregateErr := d.rewardAggregateClient.GetRewardsAggregates(ctx, &rewardsPinotPb.GetRewardsAggregatesRequest{
		ActorId: actorId,
		Filters: &rewardsPinotPb.Filters{
			RewardOfferTypes:   append(rewardsPkg.BeTierToRewardOfferTypeMap[tier], rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE),
			IncludeActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER, rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT},
			AccountTier:        tier,
		},
		FromCreatedAt: timestampPb.New(fromTime),
		ToCreatedAt:   timestampPb.New(toTime),
	})
	if rpcErr := epifigrpc.RPCError(rewardAggregateResp, rewardAggregateErr); rpcErr != nil {
		if cfg.IsStagingEnv(d.gconf.Application().Environment) {
			logger.Debug(ctx, "GetCashRewardAggregate rpc failed, gracefully handling in staging", zap.Error(rpcErr))
			return 0, nil
		}
		return 0, fmt.Errorf("GetRewardAggregates rpc failed, %w", rpcErr)
	}

	for _, aggregate := range rewardAggregateResp.GetRewardOptionAggregates() {
		if aggregate.GetRewardType() == rewardsPb.RewardType_FI_COINS {
			rewardedFiCoins += float32(aggregate.GetRewardUnits())
		}
	}

	if rewardedFiCoins < 0 {
		rewardedFiCoins = 0
	}

	return rewardedFiCoins, nil
}

func (d *DataCollectorService) GetCashRewardAggregate(ctx context.Context, actorId string, tier beTieringExtPb.Tier, fromTime, toTime time.Time) (rewardedCash float32, err error) {
	// for generated cashback reward
	rewardAggregateResp, rewardAggregateErr := d.rewardAggregateClient.GetRewardsAggregates(ctx, &rewardsPinotPb.GetRewardsAggregatesRequest{
		ActorId: actorId,
		Filters: &rewardsPinotPb.Filters{
			IncludeActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER, rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT},
			RewardOfferTypes:   rewardsPkg.BeTierToRewardOfferTypeMap[tier],
			AccountTier:        tier,
		},
		FromCreatedAt: timestampPb.New(fromTime),
		ToCreatedAt:   timestampPb.New(toTime),
	})
	if rpcErr := epifigrpc.RPCError(rewardAggregateResp, rewardAggregateErr); rpcErr != nil {
		if cfg.IsStagingEnv(d.gconf.Application().Environment) {
			logger.Debug(ctx, "GetCashRewardAggregate rpc failed, gracefully handling in staging", zap.Error(rpcErr))
			return 0, nil
		}
		return 0, fmt.Errorf("GetRewardAggregates rpc failed, %w", rpcErr)
	}

	for _, aggregate := range rewardAggregateResp.GetRewardOptionAggregates() {
		if aggregate.GetRewardType() == rewardsPb.RewardType_CASH {
			rewardedCash += float32(aggregate.GetRewardUnits())
		}
	}

	if rewardedCash < 0 {
		rewardedCash = 0
	}

	return rewardedCash, nil
}

func (d *DataCollectorService) GetAaSalaryData(ctx context.Context, actorId string) (*AaSalaryData, error) {
	data := &AaSalaryData{}
	aaSalaryDetails, getAaSalaryDetailsErr := d.beSalaryClient.GetAaSalaryDetails(ctx, &beSalaryPb.GetAaSalaryDetailsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(aaSalaryDetails, getAaSalaryDetailsErr); rpcErr != nil {
		return nil, fmt.Errorf("GetAaSalaryDetails rpc failed, %w", rpcErr)
	}

	data.upcomingTransferCriteria = aaSalaryDetails.GetUpcomingTransferCriteria()
	data.latestActivation = aaSalaryDetails.GetLatestActivation()
	data.getDetailsResp = aaSalaryDetails
	data.latestSalTxnVerificationReq = aaSalaryDetails.GetLatestSalaryTxnVerificationRequest()
	return data, nil
}

func (d *DataCollectorService) GetRewardsByActorId(ctx context.Context, actorId string, tier beTieringExtPb.Tier) (*rewardsPb.RewardsResponse, error) {
	endDate := timestampPb.New(time.Now())
	startDate := timestampPb.New(time.Date(endDate.AsTime().Year(), endDate.AsTime().Month(), 1, 0, 0, 0, 0, endDate.AsTime().Location()).AddDate(0, -1, 0))
	rewardsFilter := &rewardsPb.RewardsByActorIdRequest_FiltersV2{
		AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
			StartDate: startDate,
			EndDate:   endDate,
			ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
			RewardOfferTypes: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER,
				rewardsPb.RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER,
			},
			Statuses:    []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED, rewardsPb.RewardStatus_EXPIRED},
			ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT},
		},
	}

	rewardResp, err := d.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId:   actorId,
		FiltersV2: rewardsFilter,
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})

	if rpcErr := epifigrpc.RPCError(rewardResp, err); rpcErr != nil {
		logger.Error(ctx, "error in GetRewardsByActorId rpc", zap.Error(rpcErr))
		return nil, fmt.Errorf("error in GetRewardsByActorId rpc, err: %w", rpcErr)
	}

	return rewardResp, nil
}

func (d *DataCollectorService) GetEmploymentInfo(ctx context.Context, actorId string) (*employment.EmployerInfo, error) {
	empResp, empErr := d.beEmploymentClient.GetEmployerOfUser(ctx, &employment.GetEmployerOfUserRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(empResp, empErr); rpcErr != nil {
		if empResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("GetEmployerOfUser rpc failed, %w", rpcErr)
	}
	return empResp.GetEmployerInfo(), nil
}

type HealthInsuranceDetails struct {
	EligibleForHealthInsurance bool
	HealthInsuranceTitle       string
	NextActionDeeplink         *deeplinkPb.Deeplink
	HasActiveHealthInsurance   bool
}

func (h *HealthInsuranceDetails) GetEligibleForHealthInsurance() bool {
	if h != nil {
		return h.EligibleForHealthInsurance
	}
	return false
}

func (h *HealthInsuranceDetails) GetHealthInsuranceTitle() string {
	if h != nil {
		return h.HealthInsuranceTitle
	}
	return ""
}

func (h *HealthInsuranceDetails) GetNextActionDeeplink() *deeplinkPb.Deeplink {
	if h != nil {
		return h.NextActionDeeplink
	}
	return nil
}

func (h *HealthInsuranceDetails) GetHasActiveHealthInsurance() bool {
	if h != nil {
		return h.HasActiveHealthInsurance
	}
	return false
}

func (d *DataCollectorService) GetHealthInsuranceDetails(ctx context.Context, actorId string) (*HealthInsuranceDetails, error) {
	rewardOffersRes, err := d.rewardOffersClient.GetRewardOffersForScreen(ctx, &rewardOfferPb.GetRewardOffersForScreenRequest{
		ActorId: actorId,
		Filter: &rewardOfferPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
			Tags:       []rewardOfferPb.RewardOfferTag{rewardOfferPb.RewardOfferTag_FULL_SALARY_ACCOUNT_OFFER, rewardOfferPb.RewardOfferTag_SALARY_PROGRAM_HEALTH_INSURANCE_OFFER},
		},
	})
	if rpcErr := epifigrpc.RPCError(rewardOffersRes, err); rpcErr != nil && !rewardOffersRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching reward offers for salary program", zap.Error(rpcErr),
			zap.Any(logger.RPC_STATUS, rewardOffersRes.GetStatus()),
		)
		return nil, fmt.Errorf("error fetching reward offers for salary prorgram, %w", rpcErr)
	}

	rewardOffers := rewardOffersRes.GetRewardOffers()

	if len(rewardOffers) == 0 {
		return &HealthInsuranceDetails{
			EligibleForHealthInsurance: false,
		}, nil
	}

	if len(rewardOffers) > 1 {
		// system allows only health insurance reward offer to be setup for a user
		logger.Error(ctx, "multiple reward offers found with SALARY_PROGRAM_HEALTH_INSURANCE_OFFER tag for user", zap.Int("count", len(rewardOffers)))
	}

	resp := &HealthInsuranceDetails{
		EligibleForHealthInsurance: true,
		HealthInsuranceTitle:       rewardOffers[0].GetDisplayMeta().GetTitle(),
		// temporarily land the user to salary benefits deeplink where they can purchase / view the health insurance
		// todo: refactor the code in frontend/salaryprogram - getBenefitsCardsAndCashEquivalentForActor method
		//  and use the same logic here to handle custom deeplink
		NextActionDeeplink: helper.SalaryBenefitsDeeplink(),
	}

	if resp.GetEligibleForHealthInsurance() {
		healthInsuranceResp, healthInsuranceErr := d.beHealthInsuranceClient.GetIssuedPoliciesForActor(ctx, &healthinsurance.GetIssuedPoliciesForActorRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(healthInsuranceResp, healthInsuranceErr); rpcErr != nil {
			return nil, fmt.Errorf("GetIssuedPoliciesForActor rpc failed, %w", rpcErr)
		}

		isSomePolicyActive := false
		for _, policy := range healthInsuranceResp.GetPolicies() {
			if policy.GetPolicyActiveFrom().AsTime().Before(time.Now()) && (policy.GetPolicyActiveTill() == nil || policy.GetPolicyActiveTill().AsTime().After(time.Now())) {
				isSomePolicyActive = true
				break
			}
		}

		resp.HasActiveHealthInsurance = isSomePolicyActive
	}

	return resp, nil
}

// isInternalUser checks if the actor belongs to the internal user group.
// can move this to be common utils package
func (d *DataCollectorService) isInternalUser(ctx context.Context, actorId string) (bool, error) {
	// Get user entity using actor id to retrieve email
	userResp, userErr := d.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, userErr); rpcErr != nil {
		if userResp.GetStatus().IsRecordNotFound() {
			// Handle case where user might not be found
			return false, nil
		}
		return false, fmt.Errorf("userClient.GetUser call failed for actor %s: %w", actorId, rpcErr)
	}

	if userResp.GetUser() == nil || userResp.GetUser().GetProfile() == nil || userResp.GetUser().GetProfile().GetEmail() == "" {
		// Should ideally not happen if GetUser succeeds, but good to check
		return false, fmt.Errorf("user profile or email is empty for actor %s", actorId)
	}

	// Get user groups using email
	userGrpResp, userGrpErr := d.userGroupClient.GetGroupsMappedToIdentifier(ctx, &userGroupPb.GetGroupsMappedToIdentifierRequest{
		IdentifierValue: &userGroupPb.IdentifierValue{
			Identifier: &userGroupPb.IdentifierValue_Email{
				Email: userResp.GetUser().GetProfile().GetEmail(),
			}},
	})
	if rpcErr := epifigrpc.RPCError(userGrpResp, userGrpErr); rpcErr != nil {
		// If no groups found, treat as not internal
		if userGrpResp.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		return false, fmt.Errorf("userGroupClient.GetGroupsMappedToEmail call failed for email %s: %w", userResp.GetUser().GetProfile().GetEmail(), rpcErr)
	}

	// Check if INTERNAL group is present
	return lo.Contains(userGrpResp.GetGroups(), commontypes.UserGroup_INTERNAL), nil
}

// getCustomRewardProjectionsAggregate calls the GetCustomUnActualizedRewardProjectionsAggregates RPC for a specific reward type.
func (d *DataCollectorService) getCustomRewardProjectionsAggregate(ctx context.Context, actorId string, rewardType rewardsPb.RewardType, tier beTieringExtPb.Tier, month *MonthYear) (float32, error) {
	var dailyLimit, monthlyLimit int64

	switch rewardType {
	case rewardsPb.RewardType_FI_COINS:
		dailyLimit = int64(rewardsPkg.BeTierToFiCoinsDailyCapMap[tier])
		monthlyLimit = int64(rewardsPkg.BeTierToFiCoinsMonthlyCapMap[tier])
		// OfferTypes are typically cashback related, using the map directly might be incorrect for FI_COINS.
		// If FI_COINS projection needs specific offer types, this needs adjustment.
		// For now, passing the tier's cashback offers; consider if UNSPECIFIED is better.
	case rewardsPb.RewardType_CASH:
		dailyLimit = int64(rewardsPkg.BeTierToCashbackDailyCapMap[tier])
		monthlyLimit = int64(rewardsPkg.BeTierToCashbackMonthlyCapMap[tier])
	default:
		return 0, fmt.Errorf("unsupported reward type for custom projection: %v", rewardType)
	}

	req := &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
		ActorId:    actorId,
		RewardType: rewardType,
		TimeRange: &rewardsPinotPb.TimeRangeFilter{
			Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
			Range: &rewardsPinotPb.TimeRange{
				From: timestampPb.New(month.GetStartOfMonth()),
				To:   timestampPb.New(month.GetEndOfMonth()),
			},
		},
		ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
			Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
			Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
				TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
					ActionType:   rewardsPb.CollectedDataType_ORDER,
					OfferTypes:   rewardsPkg.BeTierToRewardOfferTypeMap[tier],
					DailyLimit:   dailyLimit,
					MonthlyLimit: monthlyLimit,
				},
			},
		},
	}

	resp, err := d.rewardAggregateClient.GetCustomUnActualizedRewardProjectionsAggregates(ctx, req)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		// pinot is not setup in staging env, and RPCs dependant on pinot tables fail in staging env
		//- gracefully handling pinot rpc failures instaging
		if cfg.IsStagingEnv(d.gconf.Application().Environment) {
			logger.Debug(ctx, "GetCustomUnActualizedRewardProjectionsAggregates rpc failed, gracefully handling in staging", zap.Error(rpcErr))
			return 0, nil
		}
		return 0, fmt.Errorf("GetCustomUnActualizedRewardProjectionsAggregates (%v) failed: %w", rewardType, rpcErr)
	}

	return float32(resp.GetRewardProjectionsAggregates().GetRewardUnits()), nil
}
