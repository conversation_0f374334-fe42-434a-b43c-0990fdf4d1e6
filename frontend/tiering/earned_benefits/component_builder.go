package earned_benefits

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/samber/lo"

	alfredPb "github.com/epifi/gamma/api/alfred"
	alfred2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"

	durationPb "google.golang.org/protobuf/types/known/durationpb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	dynamicElementsPb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	feTieringPb "github.com/epifi/gamma/api/frontend/tiering"
	rewardPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/tiering/external"
	popupPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/popup"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/tiering/cta"
	displayNames "github.com/epifi/gamma/frontend/tiering/display_names"
	events2 "github.com/epifi/gamma/frontend/tiering/events"
	"github.com/epifi/gamma/frontend/tiering/helper"
	tieringAaSalHelper "github.com/epifi/gamma/frontend/tiering/helper/aasalary"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type IComponentBuilder interface {
	// LifetimeEarnedBenefitValue - component to show lifetime earned benefit for the user
	LifetimeEarnedBenefitValue(data *EarnedBenefitData) (*uiPb.IconTextComponent, error)
	// WarningView - component to warn user when they are in grace / got downgraded recently
	WarningView(data *EarnedBenefitData) (*feTieringPb.WarningView, error)
	// MonthlyBenefits - progress bar component to track the monthly rewards earned by the user
	MonthlyBenefits(data *EarnedBenefitData) (*feTieringPb.MonthlyBenefitView, error)
	// DebitCardBenefits component for debit card benefits
	DebitCardBenefits(ctx context.Context, data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error)
	// OtherBenefits component for other benefits like cheque book
	OtherBenefits(data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error)
	// HigherRewards component to pitch higher tier to the user
	HigherRewards(ctx context.Context, data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error)
	// MorePlansInfo component to display list of other info
	// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6453&mode=design&t=RTrLcSVIWjbgXE0W-4
	MorePlansInfo(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error)
	// RewardRockstarPopup to show M1 earnings at the beginning of M2
	// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6750&mode=design&t=C8MVAZ1k7PtG8bnN-4
	RewardRockstarPopup(data *EarnedBenefitData) (popup *deeplinkPb.Deeplink, coolOffDuration *durationPb.Duration, clientCacheKey string, err error)
	// GetHeaderForEarnedBenefitsHistory header component for earned benefit history screen
	GetHeaderForEarnedBenefitsHistory(data *EarnedBenefitHistoriesData) (*feTieringPb.GetEarnedBenefitsHistoryResponse_HeaderView, error)
	// GetMonthlyBenefitsList list of monthly benefit components
	// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3849-133392&mode=design&t=tOhKF6IhsihBmJZx-4
	GetMonthlyBenefitsList(data *EarnedBenefitHistoriesData) ([]*feTieringPb.MonthlyRewardEarnedView, error)
	// MonthlyBenefitsV2 [Benefit Options list] - progress bar component to track the monthly rewards earned by the user
	MonthlyBenefitsV2(data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error)
	// TransferSalaryView Transfer salary view - Displays user to transfer amount for the current month.
	TransferSalaryView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error)
	// TransferSalaryViewV2 Transfer salary view - Displays user to transfer amount for the current month (Enabled for Multiple ways to enter Tiering).
	TransferSalaryViewV2(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error)
	// MonthlyRewardWithMoneyPlant Monthly reward with money plant - Displays when user has earned benefits for the month.
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=14065-35254&t=p5Vvxy0pIEi6IVNV-4
	MonthlyRewardWithMoneyPlant(data *EarnedBenefitData) ([]*feTieringPb.BenefitsOptions, error)
	// RewardWarningView Reward warning view - Displays warning view for user when current date is between 1-5, and they are yet to get money plant for the previous month.
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=14163-136566&t=p5Vvxy0pIEi6IVNV-4
	RewardWarningView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error)
	// HealthInsuranceView Health insurance view - Displays health insurance benefits for the user.
	HealthInsuranceView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error)
	// CurrentEmployerView Current employer view - Displays current employer benefits for the user.
	CurrentEmployerView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error)
}

type ComponentBuilder struct {
	gconf             *genconf.Config
	tieringCtaManager cta.IManager
	eventBroker       events.Broker
}

func NewComponentBuilder(gconf *genconf.Config, tieringCtaManager cta.IManager, eventBroker events.Broker) *ComponentBuilder {
	return &ComponentBuilder{
		gconf:             gconf,
		tieringCtaManager: tieringCtaManager,
		eventBroker:       eventBroker,
	}
}

const (
	bottomSheetTitle        = "Which one would you like?"
	cancelledChequeTitle    = "Cancelled cheque"
	cancelledChequeSubtitle = "Download instantly. Share as \nproof of account ownership"
	chequebookTitle         = "10-leaf chequebook"
	chequebookSubtitle      = "Takes up-to 10 working days for delivery"
	continueCtaText         = "Continue"

	blackFontColor1       = "#333333"
	blackFontColorNeutral = "#313234"
	blackFontColorLead    = "#646464"
	blackFontColorNight   = "#313234"
)

var (
	downloadCancelledChequeDl = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DOWNLOAD_DIGITAL_CANCELLED_CHEQUE,
		ScreenOptions: &deeplinkPb.Deeplink_DownloadDigitalCancelledChequeScreenOptions{
			DownloadDigitalCancelledChequeScreenOptions: &deeplinkPb.DownloadDigitalCancelledChequeScreenOptions{
				Title: &commontypes.Text{
					FontColor: "#333333",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Download a digital cancelled cheque on your device",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
					},
				},
				CtaText: &commontypes.Text{
					FontColor: "#FFFFFF",
					BgColor:   "#00B899",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Preview and Download",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BUTTON_M,
					},
				},
				Description: &commontypes.Text{
					FontColor: "#646464",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "You can share a cancelled cheque with your employer as a proof of your account details.",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_S,
					},
				},
			},
		},
	}

	freeChequebookDl = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_REQUEST_CHOICE_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred2.RequestChoiceBottomSheetScreenOptions{
			Title: commontypes.GetTextFromStringFontColourFontStyle(bottomSheetTitle, blackFontColor1, commontypes.FontStyle_SUBTITLE_1),
			RequestChoices: []*alfred2.RequestChoice{
				getRequestChoice(cancelledChequeTitle, cancelledChequeSubtitle, "₹0", alfredPb.RequestType_REQUEST_TYPE_DOWNLOAD_DIGITAL_CANCELLED_CHEQUEBOOK.String()),
				getRequestChoice(chequebookTitle, chequebookSubtitle, "₹0", alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK.String()),
			},
		}),
	}

	savingsAccSummary = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN,
	}
)

func getRequestChoice(title, subtitle, price, requestType string) *alfred2.RequestChoice {
	return &alfred2.RequestChoice{
		Title:    commontypes.GetTextFromStringFontColourFontStyle(title, blackFontColorNeutral, commontypes.FontStyle_HEADLINE_M),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle(subtitle, blackFontColorLead, commontypes.FontStyle_BODY_S),
		Price:    commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%v", price), blackFontColorNight, commontypes.FontStyle_HEADLINE_L),
		Ctas: []*deeplinkPb.Cta{
			{
				Type: deeplinkPb.Cta_CUSTOM,
				Text: continueCtaText,
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PROVISION_NEW_REQUEST,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred2.ProvisionNewRequestScreenOptions{
						RequestType: requestType,
					}),
				},
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
	}
}

func (c *ComponentBuilder) CurrentEmployerView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	if !data.GetTierToLoad().IsSalaryTier() {
		return nil, nil
	}

	currentEmployerView := &feTieringPb.BenefitsOptions{
		Title:           commontypes.GetTextFromStringFontColourFontStyle(currEmployer, blackCat, commontypes.FontStyle_SUBTITLE_M),
		ShouldGrayscale: false,
		BenefitType:     feTieringPb.BenefitsType_BENEFITS_TYPE_MORE_INFO_VIEW.String(),
	}

	currentEmployerView.Option = &feTieringPb.BenefitsOptions_MoreBenefits{MoreBenefits: &feTieringPb.MoreInfoView{
		IconTextComponents: []*uiPb.IconTextComponent{
			uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(data.employerData.GetNameBySource(), black, commontypes.FontStyle_SUBTITLE_S)).
				WithContainer(64, 0, 16, white).
				WithContainerPadding(12, 16, 12, 16).
				WithLeftImagePadding(11).
				WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(grayPencilIcon, 24, 24)).
				WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_EMPLOYER_CONFIRMATION_SCREEN}),
		},
	}}

	return currentEmployerView, nil
}

func (c *ComponentBuilder) LifetimeEarnedBenefitValue(data *EarnedBenefitData) (*uiPb.IconTextComponent, error) {
	totalBenefitsValue := data.GetTotalBenefitsEarned()
	displayBenefitsValue := money.ToDisplayStringWithPrecisionV2(totalBenefitsValue, 0)

	var iconUrl string
	var getChevronErr error
	iconUrl, getChevronErr = getRightChevronUrl(data.GetTierToLoad())
	if getChevronErr != nil {
		return nil, fmt.Errorf("failed to get right chevron url, %w", getChevronErr)
	}

	displayBenefitsColor, containerColor, displayBenefitsColorErr := getTotalBenefitTextColor(data.GetTierToLoad())
	if displayBenefitsColorErr != nil {
		return nil, fmt.Errorf("failed to get total benefit color, %w", displayBenefitsColorErr)
	}

	historyScreenOptions, screenOptionErr := deeplinkv3.GetScreenOptionV2(&tiering.EarnedBenefitsHistoryScreenOptions{
		PageBackgroundColour: widgetPb.GetBlockBackgroundColour(neutralFog200),
		TierIdentifier:       data.GetTierToLoad().String(),
	})
	if screenOptionErr != nil {
		return nil, fmt.Errorf("failed to get screen options for earned b enefit history screen")
	}

	return uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(displayBenefitsValue, displayBenefitsColor, commontypes.FontStyle_NUMBER_2XL)).
		WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 24, 24)).
		WithContainerCornerRadius(24).
		WithContainerBackgroundColor(containerColor).
		WithContainerPaddingSymmetrical(12, 8).
		WithRightImagePadding(4).
		WithDeeplink(&deeplinkPb.Deeplink{
			Screen:          deeplinkPb.Screen_TIERING_EARNED_BENEFIT_HISTORY_SCREEN,
			ScreenOptionsV2: historyScreenOptions,
		}), nil
}

func (c *ComponentBuilder) WarningView(data *EarnedBenefitData) (*feTieringPb.WarningView, error) {
	if data.GetTieringEssentials().GetIsMultipleWaysToEnterTieringEnabledForActor() {
		return c.warningViewV2(data)
	}

	warningView := &feTieringPb.WarningView{
		BackgroundColour: widget.GetBlockBackgroundColour(charCoal90Alpha),
		CornerRadius:     20,
	}

	warningView.RightIcon = commontypes.GetVisualElementFromUrlHeightAndWidth(whiteChevron, 24, 24)

	if !data.GetTieringEssentials().GetIsUserInDowngradedWindow() && !data.GetTieringEssentials().GetIsUserInGrace() && !data.GetTierToLoad().IsAaSalaryTier() {
		return nil, nil
	}

	tierDispName, getDispNameErr := displayNames.GetTitleCaseDisplayString(data.GetTierToLoad())
	if getDispNameErr != nil {
		return nil, fmt.Errorf("failed to get tier display name, %w", getDispNameErr)
	}

	if data.GetTierToLoad().IsAaSalaryTier() {
		return getWarningViewForAASalary(data, warningView)
	}

	if data.GetTieringEssentials().GetIsUserInDowngradedWindow() {
		return getWarningViewForDowngradedUser(data, tierDispName, warningView)
	}

	if data.GetTieringEssentials().GetIsUserInGrace() {
		return getWarningViewForGraceUser(data, warningView, tierDispName)
	}

	return nil, nil
}

func (c *ComponentBuilder) warningViewV2(data *EarnedBenefitData) (*feTieringPb.WarningView, error) {
	tierToLoad := data.GetTierToLoad()
	if !IsTierAllowedForEarnedBenefitsScreen(tierToLoad) {
		return nil, nil
	}

	warningView := &feTieringPb.WarningView{
		CornerRadius: 20,
	}

	if data.GetTieringEssentials().GetIsUserInGrace() {
		daysRemainingForDowngrade := int(math.Floor(time.Until(data.GetTieringEssentials().GetGracePeriodExpiry()).Hours() / 24))
		return getWarningViewForGraceUserV2(data, warningView, daysRemainingForDowngrade)
	}

	if data.GetTieringEssentials().GetIsUserInDowngradedWindow() {
		return getWarningViewForDowngradedUserV2(data, warningView)
	}

	return getWarningViewForUpgradedUserV2(data, warningView)
}

func getWarningViewForGraceUser(data *EarnedBenefitData, warningView *feTieringPb.WarningView, tierDispName string) (*feTieringPb.WarningView, error) {
	balanceToAdd, subErr := money.Subtract(data.GetTieringEssentials().GetCurrTierMinBal(), data.GetUserBalance())
	if subErr != nil {
		return nil, fmt.Errorf("failed to subtract curr balance with criteria balance, %w", subErr)
	}

	// if user satisfies balance condition already, don't show warning view
	// user will exit grace in next day's daily job
	if money.IsNegative(balanceToAdd) {
		return nil, nil
	}

	balanceToAddStr := money.ToDisplayStringWithPrecision(balanceToAdd, 0)
	currTierMinBalStr := money.ToDisplayStringWithPrecision(data.GetTieringEssentials().GetCurrTierMinBal(), 0)
	graceExpiry := data.GetTieringEssentials().GetGracePeriodExpiry()

	if time.Until(graceExpiry) > data.GetTieringEssentials().GetGraceWindowDuration() {
		warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(pinkWarningTriangle, 40, 40)}
		title := fmt.Sprintf(balBelowThresholdText, currTierMinBalStr, tierDispName)
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(title, white, commontypes.FontStyle_SUBTITLE_S)
		warningView.Deeplink = helper.GetDeeplinkForAddFunds(data.GetTierToLoad())
		return warningView, nil
	}

	daysLeft := int32(math.Floor(time.Until(graceExpiry).Hours() / 24))
	title := fmt.Sprintf(planExpiresSoonText, tierDispName, balanceToAddStr)
	warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(title, white, commontypes.FontStyle_SUBTITLE_S)
	warningView.LeftView = &feTieringPb.WarningView_WarningCounterView_{WarningCounterView: &feTieringPb.WarningView_WarningCounterView{
		BackgroundColour: widget.GetBlockBackgroundColour(jetBlack),
		Title:            commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%d", daysLeft), crimsonPink, commontypes.FontStyle_NUMBER_3XL),
		SubTitle:         commontypes.GetTextFromStringFontColourFontStyle(daysLeftText, crimsonPink, commontypes.FontStyle_OVERLINE_2XS_CAPS),
	}}
	warningView.Deeplink = helper.GetDeeplinkForAddFunds(data.GetTierToLoad())
	return warningView, nil
}

func getWarningViewForDowngradedUser(data *EarnedBenefitData, tierDispName string, warningView *feTieringPb.WarningView) (*feTieringPb.WarningView, error) {
	balanceToAdd, subErr := money.Subtract(data.GetTieringEssentials().GetDowngradedTierMinBal(), data.GetUserBalance())
	if subErr != nil {
		return nil, fmt.Errorf("failed to subtract curr balance with criteria balance, %w", subErr)
	}

	// if user satisfies balance condition already, don't show warning view
	// user will exit grace in next day's daily job
	if money.IsNegative(balanceToAdd) {
		return nil, nil
	}

	balanceToAddStr := money.ToDisplayStringWithPrecision(balanceToAdd, 0)
	title := fmt.Sprintf(planExpiredText, tierDispName, balanceToAddStr)
	warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(pinkLock, 40, 40)}
	warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(title, white, commontypes.FontStyle_SUBTITLE_S)
	warningView.Deeplink = helper.GetDeeplinkForAddFunds(data.GetTierToLoad())
	return warningView, nil
}

func getWarningViewForAASalary(data *EarnedBenefitData, warningView *feTieringPb.WarningView) (*feTieringPb.WarningView, error) {
	upcomingTransferMonthString := time.Month(data.GetAaSalaryData().GetUpcomingTransferCriteria().GetRewardActivationMonth()).String()
	dueDaysForRewardActivation, getDueDaysErr := tieringAaSalHelper.GetAaSalDueDaysForRewardActivation(data.GetAaSalaryData().GetUpcomingTransferCriteria(), data.GetAaSalaryData().GetLatestActivation())
	if getDueDaysErr != nil {
		return nil, fmt.Errorf("failed to get due days for reward activation, %w", getDueDaysErr)
	}
	cashbackPercent, ok := tieringAaSalHelper.TierToCashbackPercent[data.GetTierToLoad()]
	if !ok {
		return nil, fmt.Errorf("cashback percent not available for %s", data.GetTierToLoad().String())
	}
	switch {
	case dueDaysForRewardActivation < 0:
		title := fmt.Sprintf(addMoneyText, cashbackPercent)
		warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(dangerTrianglePink, 40, 40)}
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(title, white, commontypes.FontStyle_SUBTITLE_S)
		warningView.Deeplink = aaSalaryLandingPage()
		return warningView, nil
	case dueDaysForRewardActivation <= 5:
		title := fmt.Sprintf(doNotMissOutXDaysLeft, cashbackPercent, upcomingTransferMonthString)
		var daysLeftMsg string
		if dueDaysForRewardActivation > 1 {
			daysLeftMsg = daysLeftText
		} else {
			daysLeftMsg = dayLeftText
		}
		warningView.LeftView = &feTieringPb.WarningView_WarningCounterView_{WarningCounterView: &feTieringPb.WarningView_WarningCounterView{
			BackgroundColour: widget.GetBlockBackgroundColour(jetBlack),
			Title:            commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%d", dueDaysForRewardActivation), crimsonPink, commontypes.FontStyle_NUMBER_3XL),
			SubTitle:         commontypes.GetTextFromStringFontColourFontStyle(daysLeftMsg, crimsonPink, commontypes.FontStyle_OVERLINE_2XS_CAPS),
		}}
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(title, white, commontypes.FontStyle_SUBTITLE_S)
		warningView.Deeplink = aaSalaryLandingPage()
		return warningView, nil
	default:
		return nil, nil
	}
	return nil, nil
}

func getUpgradeViewForInfiniteAndPlusBoost(warningView *feTieringPb.WarningView, title, leftIcon string) (*feTieringPb.WarningView, error) {
	warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(leftIcon, 40, 40)}
	warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(title, white, commontypes.FontStyle_SUBTITLE_S)
	return warningView, nil
}

func (c *ComponentBuilder) MonthlyBenefits(data *EarnedBenefitData) (*feTieringPb.MonthlyBenefitView, error) {
	month := time.Now().In(datetime.IST).Month().String()
	title := fmt.Sprintf(monthlyRewardTitleText, month)
	headerDeeplink := c.getMonthlyRewardsInfoPopup(data)
	headerView := &feTieringPb.MonthlyBenefitView_MonthlyBenefits_TitleView{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(title, blackCat, commontypes.FontStyle_SUBTITLE_M),
		BackgroundColour: widget.GetBlockBackgroundColour(aliceBlue),
		RightIcon: uiPb.NewITC().
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 24, 24)).
			WithDeeplink(headerDeeplink),
	}
	headerViewV2 := headerTitleView(title, headerDeeplink)

	var progressBarTiles []*feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile

	fiCoinsRewardTile, fiCoinsProgressBarErr := c.getProgressBarForFiCoinsReward(data)
	if fiCoinsProgressBarErr != nil {
		return nil, fmt.Errorf("failed to get fi coins progress bar, %w", fiCoinsProgressBarErr)
	}

	if fiCoinsRewardTile != nil {
		progressBarTiles = append(progressBarTiles, fiCoinsRewardTile)
	}

	// Get AMB entry point using pre-fetched data
	ambEntryPoint := c.getAMBEntryPoint(data)

	return &feTieringPb.MonthlyBenefitView{
		MonthlyBenefits: &feTieringPb.MonthlyBenefitView_MonthlyBenefits{
			HeaderView:          headerView,
			MonthlyBenefitTiles: progressBarTiles,
			InfoView:            c.getMonthlyRewardsInfoView(data),
			CornerRadius:        20,
			BackgroundColour:    widget.GetBlockBackgroundColour(white),
			ShouldGrayscale:     shouldGrayscaleComponent(data),
			HeaderViewV2:        headerViewV2,
			OfferSection:        c.getOfferSection(data),
			AmbEntryPoint:       ambEntryPoint,
		},
		BenefitType: feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_VIEW.String(),
	}, nil
}

func (c *ComponentBuilder) MonthlyBenefitsV2(data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error) {
	if !componentsToLoad[feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_VIEW] {
		return nil, nil
	}

	benefitView, benefitError := c.MonthlyBenefits(data)
	if benefitError != nil {
		return nil, benefitError
	}
	monthlyBenefitOptions := &feTieringPb.BenefitsOptions{
		Priority:         90,
		Title:            commontypes.GetTextFromStringFontColourFontStyle(monthlyRewards, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		ShouldGrayscale:  shouldGrayscaleComponent(data),
		BackgroundColour: widget.GetBlockBackgroundColour(white),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_VIEW.String(),
	}
	monthlyBenefitOptions.Option = &feTieringPb.BenefitsOptions_MonthlyBenefitView{
		MonthlyBenefitView: benefitView,
	}
	return monthlyBenefitOptions, nil
}

func headerTitleView(title string, deeplink *deeplinkPb.Deeplink) *feTieringPb.TitleView {
	return &feTieringPb.TitleView{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(title, blackCat, commontypes.FontStyle_SUBTITLE_M),
		BackgroundColour: widget.GetBlockBackgroundColour(aliceBlue),
		RightIcon: uiPb.NewITC().
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 24, 24)).
			WithDeeplink(deeplink),
	}
}

func shouldGrayscaleComponent(data *EarnedBenefitData) bool {
	if data.GetTieringEssentials().GetIsMultipleWaysToEnterTieringEnabledForActor() {
		return data.GetTieringEssentials().GetIsUserInDowngradedWindow()
	}

	if data.GetTierToLoad().IsAaSalaryTier() {
		dueDaysForRewardActivation, getDueDaysErr := tieringAaSalHelper.GetAaSalDueDaysForRewardActivation(data.GetAaSalaryData().GetUpcomingTransferCriteria(), data.GetAaSalaryData().GetLatestActivation())
		if getDueDaysErr != nil {
			return false
		}
		if dueDaysForRewardActivation < 0 {
			return true
		}
		return false
	}
	return data.GetTieringEssentials().GetIsUserInDowngradedWindow()
}

// nolint:dupl
func (c *ComponentBuilder) DebitCardBenefits(ctx context.Context, data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error) {
	if !componentsToLoad[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] {
		return nil, nil
	}

	if data.GetDebitCardOrderChargesData() == nil {
		return nil, nil
	}

	dcBenefitOptions := &feTieringPb.BenefitsOptions{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(dcComponentTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		ShouldGrayscale:  shouldGrayscaleComponent(data),
		BackgroundColour: widget.GetBlockBackgroundColour(white),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD.String(),
	}

	if data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] {
		dcBenefitOptions.Option = getRetryViewComponent()
		return dcBenefitOptions, nil
	}

	if !data.GetDebitCardOrderChargesData().GetHasOrderedDebitCard() {
		opt, err := getInactiveDebitCardComponent(data)
		if err != nil {
			return nil, fmt.Errorf("failed to get inactive dc component, %w", err)
		}
		dcBenefitOptions.Option = opt
		return dcBenefitOptions, nil
	}

	var err error
	dcBenefitOptions.Option, err = getActiveDebitCardComponent(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("failed to get active dc component, %w", err)
	}

	return dcBenefitOptions, nil
}

// nolint:dupl
func (c *ComponentBuilder) OtherBenefits(data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error) {
	if !componentsToLoad[feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER] {
		return nil, nil
	}

	if data.GetTierToLoad().IsSalaryTier() {
		return nil, nil
	}

	if data.GetChequeBookOrderData() == nil {
		return nil, nil
	}

	otherBenefitOptions := &feTieringPb.BenefitsOptions{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(otherBenefitsComponentTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		ShouldGrayscale:  shouldGrayscaleComponent(data),
		BackgroundColour: widget.GetBlockBackgroundColour(white),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER.String(),
	}

	if data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER] {
		otherBenefitOptions.Option = getRetryViewComponent()
		return otherBenefitOptions, nil
	}

	if !data.GetChequeBookOrderData().GetHasOrderedChequeBook() {
		otherBenefitOptions.Option = getInactiveOtherBenefitsComponent(data)
		return otherBenefitOptions, nil
	}

	otherBenefitOptions.Option = getActiveOtherBenefitsComponent(data)
	return otherBenefitOptions, nil
}

func (c *ComponentBuilder) HigherRewards(ctx context.Context, data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.BenefitsOptions, error) {
	if !shouldShowHigherRewardsComponent(data, componentsToLoad) {
		return nil, nil
	}

	higherRewardsOption := &feTieringPb.BenefitsOptions{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(higherRewardsComponentTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		ShouldGrayscale:  false,
		BackgroundColour: widget.GetBlockBackgroundColour(white),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW.String(),
	}

	leftCard, rightCard, getCardErr := getCardsForPitchingHigherTier(data)
	if getCardErr != nil {
		return nil, fmt.Errorf("failed to get cards for pitching higher tier, %w", getCardErr)
	}

	upgradeCta, getCtaErr := c.getItcCtaForUpgrade(data)
	if getCtaErr != nil {
		return nil, fmt.Errorf("failed to get cta for upgrade, %w", getCtaErr)
	}

	higherRewardsOption.Option = &feTieringPb.BenefitsOptions_UpgradeBenefits{UpgradeBenefits: &feTieringPb.UpgradeBenefitsView{
		ComponentTitle: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(higherRewardsHeaderText, blackCat, commontypes.FontStyle_SUBTITLE_M)),
		LeftCard:       leftCard,
		RightCard:      rightCard,
		VsItc:          uiPb.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(vsIcon, 50, 50)),
		Cta:            upgradeCta,
		TierIdentifier: data.GetTierToPitch().String(),
	}}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		c.eventBroker.AddToBatch(ctx, events2.NewMissedRewardsTiering(
			data.GetActorId(),
			time.Now().In(datetime.IST),
			data.GetTierToLoad(),
			data.GetTierToPitch(),
			data.GetRewardsData().GetCurrMonthEstCashInHigherTier().GetUnits(),
			int64(data.GetRewardsData().GetCurrMonthEstFiCoinsInHigherTier())))
	})

	return higherRewardsOption, nil
}

func (c *ComponentBuilder) MorePlansInfo(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	morePlansInfoOption := &feTieringPb.BenefitsOptions{
		Title:           commontypes.GetTextFromStringFontColourFontStyle(morePlansComponentTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
		ShouldGrayscale: false,
		BenefitType:     feTieringPb.BenefitsType_BENEFITS_TYPE_MORE_INFO_VIEW.String(),
	}

	tierBenefitsTitle, tierIcon, getIconTitleErr := getTierIconAndTitleForMoreInfo(data)
	if getIconTitleErr != nil {
		return nil, fmt.Errorf("failed to get icon and title for more info, %w", getIconTitleErr)
	}

	allPlansDeeplink := tiering.AllPlansDeeplink(data.GetTierToLoad(), data.GetTieringEssentials().GetGetConfigParamsResp().GetIsMultipleWaysToEnterTieringEnabledForActor())

	if data.GetTierToLoad().IsSalaryTier() {
		morePlansInfoOption.Option = &feTieringPb.BenefitsOptions_MoreBenefits{MoreBenefits: &feTieringPb.MoreInfoView{
			IconTextComponents: []*uiPb.IconTextComponent{
				getPlanInfo(tierIcon, tierBenefitsTitle, allPlansDeeplink),
				getPlanInfo(tncIcon, cancelledChequeBookText, downloadCancelledChequeDl),
				getPlanInfo(tncIcon, chequeBookText, freeChequebookDl),
				getPlanInfo(tncIcon, downloadAccStatementText, savingsAccSummary),
			},
		}}
	} else {
		morePlansInfoOption.Option = &feTieringPb.BenefitsOptions_MoreBenefits{MoreBenefits: &feTieringPb.MoreInfoView{
			IconTextComponents: []*uiPb.IconTextComponent{
				getPlanInfo(tierIcon, tierBenefitsTitle, allPlansDeeplink),
				getPlanInfo(faqIconGreen, faqs, faqDeeplink()),
				getPlanInfo(tncIcon, tncs, termsAndConditionsDeeplink()),
			},
		}}
	}

	return morePlansInfoOption, nil
}

func (c *ComponentBuilder) RewardRockstarPopup(data *EarnedBenefitData) (popup *deeplinkPb.Deeplink, coolOffDuration *durationPb.Duration, cacheKey string, err error) {
	if !c.shouldShowRewardRockstarPopup(data) {
		return nil, nil, "", nil
	}

	var infoComponents []*popupPb.CelebrationPopupScreenOptions_InfoComponent

	if data.GetTierToLoad().IsCashbackEligibleTier() {
		// using curr month realised cash since we show popup only after 5th of every month
		// and last month's reward is credited to account on 5th of curr month
		cashbackEarnedText := fmt.Sprintf(cashbackEarned, money.ToDisplayStringWithPrecision(data.GetRewardsData().GetCurrMonthRealisedCash(), 0))
		infoComponents = append(infoComponents, getCelebPopupInfoComponent(cashbackEarnedText, cashIcon, tierToIconContainerColor[data.GetTierToLoad()]))
	}

	fiCoinsEarnedText := fmt.Sprintf(fiCoinsEarned, int32(data.rewardsData.GetPrevMonthFiCoins()))
	infoComponents = append(infoComponents, getCelebPopupInfoComponent(fiCoinsEarnedText, fiCoinsIcon, tierToIconContainerColor[data.GetTierToLoad()]))

	_, gradientColor, getIconColorErr := getIconAndGradientTier(data.GetTierToLoad())
	if getIconColorErr != nil {
		return nil, nil, "", fmt.Errorf("failed to get icon and gradient for tier, %w", getIconColorErr)
	}

	popupScreenOption := &popupPb.CelebrationPopupScreenOptions{
		HeaderIcon:     commontypes.GetVisualElementFromUrlHeightAndWidth(popperEmojiBig, 160, 160),
		Title:          commontypes.GetTextFromStringFontColourFontStyle(rewardRockstarPopupTitle, white, commontypes.FontStyle_HEADLINE_XL),
		InfoComponents: infoComponents,
		Ctas: []*dynamicElementsPb.DynamicElementCta{
			{
				Type:            dynamicElementsPb.DynamicElementCta_TYPE_DISMISSED,
				Text:            ctaText,
				BackgroundColor: deepGraphite,
				TextColor:       fiGreen,
				ShadowColour:    black,
			},
		},
		BackgroundColour: gradientColor,
		DismissIcon:      commontypes.GetVisualElementFromUrlHeightAndWidth(tierToDismissIconMap[data.GetTierToLoad()], 24, 24),
		Identifier:       deeplinkPb.Screen_TIERING_EARNED_BENEFIT_SCREEN.String(),
	}

	screenOption, screenOptionErr := deeplinkv3.GetScreenOptionV2(popupScreenOption)
	if screenOptionErr != nil {
		return nil, nil, "", fmt.Errorf("failed to get screen option v2, %w", screenOptionErr)
	}

	popupCoolOffDuration := durationPb.New(time.Until(datetime.EndOfMonth(time.Now().In(datetime.IST))))
	if cfg.IsNonProdEnv(c.gconf.Application().Environment) {
		popupCoolOffDuration = durationPb.New(time.Minute)
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_CELEBRATION_POPUP,
		ScreenOptionsV2: screenOption,
	}, popupCoolOffDuration, data.GetTierToLoad().String(), nil
}

func (c *ComponentBuilder) GetHeaderForEarnedBenefitsHistory(data *EarnedBenefitHistoriesData) (*feTieringPb.GetEarnedBenefitsHistoryResponse_HeaderView, error) {
	resp := &feTieringPb.GetEarnedBenefitsHistoryResponse_HeaderView{}

	var planIconError error
	planIcon, planIconError := getHeaderIconForHistoryScreen(data.GetEarnedBenefitsData().GetTierToLoad(), 80, 116)
	if planIconError != nil {
		return nil, fmt.Errorf("failed to get plan icon, %w", planIconError)
	}

	resp.PlanIcon = uiPb.NewITC().WithLeftVisualElement(planIcon)
	if time.Now().In(datetime.IST).Before(time.Date(2025, 3, 1, 0, 0, 0, 0, datetime.IST)) && !cfg.IsNonProdEnv(c.gconf.Application().Environment) {
		resp.TotalBenefitsEarned = commontypes.GetTextFromStringFontColourFontStyle(TotalBenefitsEarnedTitle, platinumGrey, commontypes.FontStyle_OVERLINE_XS_CAPS)
	} else {
		resp.TotalBenefitsEarned = commontypes.GetTextFromStringFontColourFontStyle(EarnedBenefitsWorthTitle, platinumGrey, commontypes.FontStyle_OVERLINE_XS_CAPS)
	}
	resp.TotalBenefitsEarnedValue = commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithPrecision(data.GetEarnedBenefitsData().GetTotalBenefitsEarned(), 0), blackCat, commontypes.FontStyle_NUMBER_2XL)
	resp.BottomText = uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(question, fiGreen, commontypes.FontStyle_BUTTON_XS)).
		WithDeeplink(c.getTotalBenefitsValueInfoPopup(data.GetEarnedBenefitsData().GetTierToLoad()))

	return resp, nil
}

func (c *ComponentBuilder) GetMonthlyBenefitsList(data *EarnedBenefitHistoriesData) ([]*feTieringPb.MonthlyRewardEarnedView, error) {
	var monthlyRewardsList []*feTieringPb.MonthlyRewardEarnedView

	for _, monthlyBenefit := range data.GetMonthlyBenefitsDisplayData() {
		monthlyRewardView := &feTieringPb.MonthlyRewardEarnedView{
			HeaderView:               getHeaderOfMonthlyEarnedView(monthlyBenefit.GetMonthYear()),
			CardBackgroundColour:     widgetPb.GetBlockBackgroundColour(white),
			TotalBenefitsEarned:      commontypes.GetTextFromStringFontColourFontStyle(benefitsEarned, platinumGrey, commontypes.FontStyle_OVERLINE_XS_CAPS),
			TotalBenefitsEarnedValue: commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithPrecision(monthlyBenefit.GetTotalBenefitsEarned(), 0), blackCat, commontypes.FontStyle_NUMBER_XL),
			BenefitBackgroundColour:  widgetPb.GetBlockBackgroundColour(aliceBlue),
			BenefitCardItems:         getBenefitCardItemsForMonthlyReward(data.GetEarnedBenefitsData().GetTierToLoad(), monthlyBenefit),
		}

		monthlyRewardsList = append(monthlyRewardsList, monthlyRewardView)
	}

	return monthlyRewardsList, nil
}

// deprecated : use TransferSalaryViewV2, this is old implementation, kept for backward compatibility
func (c *ComponentBuilder) TransferSalaryView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	if data.GetTieringEssentials().GetIsMultipleWaysToEnterTieringEnabledForActor() {
		return c.TransferSalaryViewV2(data)
	}

	if !data.GetTierToLoad().IsAaSalaryTier() || data.GetAaSalaryData() == nil {
		return nil, nil
	}

	committedSalary := money.ToDisplayStringWithPrecision(data.GetAaSalaryData().GetLatestSalTxnVerificationReq().GetSalaryAmountCommitted(), 0)

	// show edit icon only after upcoming transfer window start time
	var editIcon *uiPb.IconTextComponent
	if time.Now().In(datetime.IST).After(data.GetAaSalaryData().GetUpcomingTransferCriteria().GetTransferWindowStartTime().AsTime()) {
		editIcon = uiPb.NewITC().
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(editIconUrl, 24, 24)).
			WithDeeplink(aaSalaryLandingPage())
	}

	rightCta, infoView, priority, getAaSalRightCtaAndInfoErr := c.getAaSalaryRightCtaAndInfoView(data)
	if getAaSalRightCtaAndInfoErr != nil {
		return nil, fmt.Errorf("failed to get aa salary right cta and info view, %w", getAaSalRightCtaAndInfoErr)
	}

	cashbackPercent, ok := tieringAaSalHelper.TierToCashbackPercent[data.GetTierToLoad()]
	if !ok {
		return nil, fmt.Errorf("cashback percent not available for %s", data.GetTierToLoad().String())
	}
	popupBody := fmt.Sprintf(transferSalaryPopupBody, cashbackPercent)
	amount := commontypes.GetTextFromStringFontColourFontStyle(committedSalary, blackCat, commontypes.FontStyle_SUBTITLE_L)

	return &feTieringPb.BenefitsOptions{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(monthlyTransferAmount, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		ShouldGrayscale:  false,
		BackgroundColour: widget.GetBlockBackgroundColour(white),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW.String(),
		Priority:         priority,
		Option: &feTieringPb.BenefitsOptions_TransferSalaryView{
			TransferSalaryView: &feTieringPb.TransferSalaryView{
				HeaderView:              headerTitleView(monthlyTransferAmount, getInfoPopup(transferSalaryPopupHeader, popupBody)),
				Amount:                  amount,
				AmountIconTextComponent: uiPb.NewITC().WithTexts(amount),
				CornerRadius:            20,
				BackgroundColour:        widget.GetBlockBackgroundColour(white),
				InfoView:                infoView,
				EditAmount:              editIcon,
				RightCta:                rightCta,
			},
		},
	}, nil
}

// nolint:ineffassign
func (c *ComponentBuilder) TransferSalaryViewV2(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	criteriaOption := data.GetTieringEssentials().GetTieringPitchResp().GetEntryCriteriaOptionType()
	if criteriaOption == enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED {
		criteriaOption = data.GetTieringEssentials().GetTieringPitchResp().GetCurrentCriteriaOptionType()

		// handling for old users since entry criteria option is nil for those users in etm table
		if lo.Contains(data.GetTieringEssentials().GetTieringPitchResp().GetCurrentCriteriaOptionTypes(), enums.CriteriaOptionType_AA_SALARY_AND_KYC) {
			criteriaOption = enums.CriteriaOptionType_AA_SALARY_AND_KYC
		}
	}

	switch criteriaOption {
	case enums.CriteriaOptionType_AA_SALARY_AND_KYC, enums.CriteriaOptionType_SALARY_AND_KYC:
		return c.getAASalaryTransferView(data)
	case enums.CriteriaOptionType_US_STOCKS_SIP_AND_KYC:
		return c.getUSStocksTransferView(data)
	case enums.CriteriaOptionType_DEPOSITS_AND_KYC:
		return c.getDepositsTransferSalaryView(data)
	default:
		return nil, nil
	}
}

func (c *ComponentBuilder) RewardWarningView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	// Check if current date is between 1-4th of the month
	currentDay := time.Now().In(datetime.IST).Day()
	if currentDay > 4 {
		return nil, nil
	}

	// Only show warning if user has earned benefits for previous month but not yet received
	if data.GetRewardsData().GetPrevMonthFiCoins() <= 0 {
		return nil, nil
	}

	if len(data.GetRewardsResponse().GetRewards()) > 0 {
		return nil, nil
	}

	// Low priority if the user is in grace period; high priority otherwise
	priority := uint32(110)
	if data.GetTieringEssentials().GetIsUserInGrace() {
		priority = 70
	}

	// Get previous month and current month names
	prevMonth := time.Now().In(datetime.IST).AddDate(0, -1, 0).Month().String()
	currentMonth := time.Now().In(datetime.IST).Month().String()

	benefitsOptions := &feTieringPb.BenefitsOptions{
		Priority: priority,
		Option: &feTieringPb.BenefitsOptions_WarningView{
			WarningView: &feTieringPb.WarningView{
				LeftView: &feTieringPb.WarningView_VisualElement{
					VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(goldenSandClockIcon, 40, 40),
				},
				Title: commontypes.GetTextFromStringFontColourFontStyle(
					fmt.Sprintf(rewardReadyText, prevMonth, currentMonth),
					darkYellow,
					commontypes.FontStyle_SUBTITLE_S,
				),
				BackgroundColour: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
					{
						Color:          paleYellow,
						StopPercentage: 50,
					},
					{
						Color:          golden,
						StopPercentage: 100,
					},
				}),
				CornerRadius: 20,
			},
		},
	}

	return benefitsOptions, nil
}

func (c *ComponentBuilder) MonthlyRewardWithMoneyPlant(data *EarnedBenefitData) ([]*feTieringPb.BenefitsOptions, error) {
	if !data.GetIsMoneyPlantEnabled() {
		return nil, nil
	}

	var (
		moneyPlants                           []*feTieringPb.BenefitsOptions
		rewardIdForPrevMonthRewardsMoneyPlant string
		isMoneyPlantExpiredVisible            bool
		noOfDaysToExpire                      uint32
		daysSinceMoneyPlantCreated            uint32
	)
	currTime := time.Now().In(datetime.IST)
	prevMonthString := currTime.AddDate(0, -1, 0).Month().String()

	for _, reward := range data.GetRewardsResponse().GetRewards() {
		if reward.GetStatus() == rewardPb.RewardStatus_EXPIRED {
			daysSinceMoneyPlantExpired := uint32(currTime.Sub(reward.GetExpiresAt().AsTime()).Hours() / 24)
			if daysSinceMoneyPlantExpired <= 3 {
				isMoneyPlantExpiredVisible = true
			}
		} else if reward.GetCreatedAt().AsTime().Month() == currTime.Month() {
			rewardIdForPrevMonthRewardsMoneyPlant = reward.GetId()
			noOfDaysToExpire = uint32(reward.GetExpiresAt().AsTime().Sub(currTime).Hours() / 24)
			daysSinceMoneyPlantCreated = uint32(currTime.Sub(reward.GetCreatedAt().AsTime()).Hours() / 24)
		}
	}

	if rewardIdForPrevMonthRewardsMoneyPlant != "" {
		screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&rewards.RewardClaimScreenOptions{
			RewardId: rewardIdForPrevMonthRewardsMoneyPlant,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to get screen option v2, %w", err)
		}
		text := fmt.Sprintf(moneyPlantClaimRewardsText, prevMonthString)
		if daysSinceMoneyPlantCreated > 3 {
			text = moneyPlantPost3DaysClaimRewardsText
		}
		title := commontypes.GetTextFromStringFontColourFontStyle(text, aliceBlue, commontypes.FontStyle_HEADLINE_M)
		priority := uint32(110)
		if data.GetTieringEssentials().GetIsUserInGrace() {
			priority = 80
		}
		benefitsOptions := &feTieringPb.BenefitsOptions{
			Priority: priority,
			Option: &feTieringPb.BenefitsOptions_RewardView{
				RewardView: &feTieringPb.RewardView{
					Status: feTieringPb.RewardView_ACTIVE,
					Header: &feTieringPb.RewardView_TitleComponent{
						Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(monthRewardText, prevMonthString), blackCat, commontypes.FontStyle_SUBTITLE_S)).WithLeftVisualElementUrlHeightAndWidth(starIcon, 16, 16).WithRightVisualElementUrlHeightAndWidth(starIcon, 16, 16).WithLeftImagePadding(8).WithRightImagePadding(8).WithContainerPadding(4, 24, 4, 24),
						BackgroundColour: &widget.BackgroundColour{
							Colour: &widgetPb.BackgroundColour_LinearGradient{
								LinearGradient: &widgetPb.LinearGradient{
									Degree: 90,
									LinearColorStops: []*widgetPb.ColorStop{
										{
											Color: paleYellow,
										},
										{
											Color:          feldspar,
											StopPercentage: 80,
										},
									},
								},
							},
						},
						BorderColour: &widget.BackgroundColour{
							Colour: &widgetPb.BackgroundColour_RadialGradient{
								RadialGradient: &widgetPb.RadialGradient{
									Center: &widgetPb.CenterCoordinates{
										CenterX: 100,
										CenterY: 70,
									},
									OuterRadius: 100,
									Colours:     []string{lightGrayishOrange, softOrange},
								},
							},
						},
					},
					Title: title,
					Cta: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Claim Now", aliceBlue, commontypes.FontStyle_SUBTITLE_XS)).WithRightImageUrlHeightAndWidth(whiteChevron, 16, 16).WithContainerBackgroundColor(blackCat).WithContainerPadding(4, 8, 4, 8).WithContainerCornerRadius(12).WithDeeplink(&deeplinkPb.Deeplink{
						Screen:          deeplinkPb.Screen_REWARD_CLAIM_SCREEN,
						ScreenOptionsV2: screenOptionsV2,
					}),
					Expiry: getExpiryContentForMoneyPlant(daysSinceMoneyPlantCreated, noOfDaysToExpire),
					PlantIcon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: moneyPlantIcon,
								},
								Properties: &commontypes.VisualElementProperties{
									Height: 117,
									Width:  83,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					BackgroundColour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: jetBlack,
						},
					},
					BorderColour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_LinearGradient{
							LinearGradient: &widget.LinearGradient{
								Degree: 90,
								LinearColorStops: []*widgetPb.ColorStop{
									{
										Color:          "#E7C173",
										StopPercentage: 70,
									},
									{
										Color: "#E6BE6F",
									},
								},
							},
						},
					},
					Shadow: &widget.Shadow{
						Height: 4,
						Colour: widget.GetBlockBackgroundColour("#7F000000"),
						Blur:   5,
					},
				},
			},
		}
		moneyPlants = append(moneyPlants, benefitsOptions)
	}

	if isMoneyPlantExpiredVisible {
		moneyPlants = append(moneyPlants, getExpiredMoneyPlant())
	}
	return moneyPlants, nil
}

func (c *ComponentBuilder) HealthInsuranceView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	if !data.GetTierToLoad().IsSalaryTier() {
		return nil, nil
	}

	if !data.GetHealthInsuranceDetails().GetEligibleForHealthInsurance() {
		return nil, nil
	}

	healthInsuranceBenefitOptions := &feTieringPb.BenefitsOptions{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(healthInsuranceTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		BackgroundColour: widget.GetBlockBackgroundColour(white),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_HEALTH_INSURANCE_VIEW.String(),
	}

	if data.GetHealthInsuranceDetails().GetHasActiveHealthInsurance() {
		healthInsuranceBenefitOptions.Option = getActiveHealthInsuranceComponent(data)
	} else {
		healthInsuranceBenefitOptions.Option = getInactiveHealthInsuranceComponent(data)
	}

	return healthInsuranceBenefitOptions, nil
}

// convertGenconfITCToProto converts a genconf IconTextComponent to protobuf IconTextComponent
func (c *ComponentBuilder) convertGenconfITCToProto(genconfITC *genconf.IconTextComponent) *uiPb.IconTextComponent {
	if genconfITC == nil {
		return nil
	}

	// Create the protobuf IconTextComponent
	protoITC := &uiPb.IconTextComponent{
		LeftImgTxtPadding:  genconfITC.LeftImgTxtPadding(),
		RightImgTxtPadding: genconfITC.RightImgTxtPadding(),
	}

	// Convert texts
	if len(genconfITC.Texts()) > 0 {
		for _, t := range genconfITC.Texts() {
			fontStyleEnum, ok := commontypes.FontStyle_value[t.FontStyle]
			if !ok {
				continue
			}
			protoITC.Texts = append(protoITC.Texts,
				commontypes.GetTextFromStringFontColourFontStyle(t.Content, t.FontColor, commontypes.FontStyle(fontStyleEnum)))
		}
	}

	// Convert left visual element
	if genconfITC.LeftVisualElementImage() != nil && len(genconfITC.LeftVisualElementImage().Url()) > 0 {
		leftImg := genconfITC.LeftVisualElementImage()
		protoITC.LeftVisualElement = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: leftImg.Url(),
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  leftImg.Properties().Width(),
						Height: leftImg.Properties().Height(),
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		}
	}

	// Convert right visual element
	if genconfITC.RightVisualElementImage() != nil && len(genconfITC.RightVisualElementImage().Url()) > 0 {
		rightImg := genconfITC.RightVisualElementImage()
		protoITC.RightVisualElement = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: rightImg.Url(),
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  rightImg.Properties().Width(),
						Height: rightImg.Properties().Height(),
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		}
	}

	// Convert container properties
	if genconfITC.ContainerProperties() != nil {
		containerProps := genconfITC.ContainerProperties()
		protoITC.ContainerProperties = &uiPb.IconTextComponent_ContainerProperties{
			BgColor:      containerProps.BgColor(),
			CornerRadius: containerProps.CornerRadius(),
			Height:       containerProps.Height(),
			Width:        containerProps.Width(),
			BorderColor:  containerProps.BorderColor(),
			BorderWidth:  containerProps.BorderWidth(),
		}

		// Add padding if it exists
		dummyCtx := context.Background()
		if containerProps.Padding() != nil {
			protoITC.ContainerProperties.LeftPadding = containerProps.Padding().Left(dummyCtx)
			protoITC.ContainerProperties.RightPadding = containerProps.Padding().Right(dummyCtx)
			protoITC.ContainerProperties.TopPadding = containerProps.Padding().Top(dummyCtx)
			protoITC.ContainerProperties.BottomPadding = containerProps.Padding().Bottom(dummyCtx)
		}
	}

	return protoITC
}

// getAMBEntryPoint generates the AMB entry point component using pre-fetched data
func (c *ComponentBuilder) getAMBEntryPoint(data *EarnedBenefitData) *uiPb.IconTextComponent {
	// Skip regular tier scenarios as instructed
	if data.GetTierToLoad() == external.Tier_TIER_FI_REGULAR {
		return nil
	}

	// Check if AMB is enabled for this actor using pre-fetched data
	if !data.GetIsAmbEnabledForActor() {
		return nil
	}

	// Check if AMB info was successfully fetched
	if data.GetAmbInfo() == nil {
		return nil
	}

	// Get the configured AMB entry point from config and convert it
	configComponent := lo.Ternary(data.ambShortfallExists, c.gconf.Tiering().AMBConfig().MonthlyBenefitsEntryPoint().Shortfall(), c.gconf.Tiering().AMBConfig().MonthlyBenefitsEntryPoint().Default())
	ambEntryPoint := c.convertGenconfITCToProto(configComponent)
	if ambEntryPoint == nil {
		return nil
	}

	// Add the deeplink since it's not handled in the config conversion
	ambEntryPoint.Deeplink = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_AMB_DETAILS_SCREEN}

	return ambEntryPoint
}
