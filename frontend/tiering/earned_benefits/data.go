package earned_benefits

import (
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/rewards"
	feTieringPb "github.com/epifi/gamma/api/frontend/tiering"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	dataCollector "github.com/epifi/gamma/frontend/tiering/data_collector"
	feTieringHelper "github.com/epifi/gamma/frontend/tiering/helper"
)

type EarnedBenefitData struct {
	actorId string
	// the screen will be loaded based on tierToLoad value
	// eg. if user downgraded from Infinite recently (within downgrade window), we load Infinite screen for the user
	tierToLoad                tieringExternalPb.Tier
	userBalance               *gmoney.Money
	tieringEssentials         *feTieringHelper.TieringFeEssentials
	tierTimeRanges            []*tieringPb.TimeRange
	debitCardOrderChargesData *dataCollector.DebitCardOrderChargesData
	forexRefundAtTier         *gmoney.Money
	chequeBookOrderData       *dataCollector.ChequeBookOrderData
	rewardsData               *dataCollector.RewardsDataForEarnedBenefits
	tierToPitch               tieringExternalPb.Tier
	totalBenefitsEarned       *gmoney.Money
	aaSalaryData              *dataCollector.AaSalaryData
	fetchFailedMap            map[feTieringPb.BenefitsType]bool
	segmentMembershipMap      map[string]*segmentPb.SegmentMembership
	evaluatedTier             tieringExternalPb.Tier
	catalogOffers             *rewards.GetCatalogOffersAndFiltersResponse
	RewardsResponse           *rewardsPb.RewardsResponse
	isMoneyPlantEnabled       bool
	employerData              *employment.EmployerInfo
	healthInsuranceDetails    *dataCollector.HealthInsuranceDetails
	// AMB related fields
	isAmbEnabledForActor bool
	ambInfo              *tieringPb.GetAMBInfoResponse
	ambShortfallExists   bool
}

type EarnedBenefitHistoriesData struct {
	earnedBenefitsData         *EarnedBenefitData
	monthlyRewardsData         []*dataCollector.MonthlyRewardsData
	monthlyForexData           []*dataCollector.MonthlyForexData
	monthlyBenefitsDisplayData []*monthlyBenefitsDisplayData
}

type monthlyBenefitsDisplayData struct {
	monthYear           *dataCollector.MonthYear
	monthYearStr        string
	cashbackEarned      *gmoney.Money
	feesSaved           *gmoney.Money // includes dc order fee and cheque book charges
	fiCoinsEarned       int32
	totalBenefitsEarned *gmoney.Money // includes cashback earned, fees saved and fi-coins converted money value
}

func (e *EarnedBenefitData) GetActorId() string {
	if e != nil {
		return e.actorId
	}
	return ""
}

func (e *EarnedBenefitData) GetTierToLoad() tieringExternalPb.Tier {
	if e != nil {
		return e.tierToLoad
	}
	return tieringExternalPb.Tier_TIER_UNSPECIFIED
}

func (e *EarnedBenefitData) GetUserBalance() *gmoney.Money {
	if e != nil {
		return e.userBalance
	}
	return nil
}

func (e *EarnedBenefitData) GetTieringEssentials() *feTieringHelper.TieringFeEssentials {
	if e != nil {
		return e.tieringEssentials
	}
	return nil
}

func (e *EarnedBenefitData) GetTierTimeRanges() []*tieringPb.TimeRange {
	if e != nil {
		return e.tierTimeRanges
	}
	return nil
}

func (e *EarnedBenefitData) GetDebitCardOrderChargesData() *dataCollector.DebitCardOrderChargesData {
	if e != nil {
		return e.debitCardOrderChargesData
	}
	return nil
}

func (e *EarnedBenefitData) GetForexRefundAtTier() *gmoney.Money {
	if e != nil {
		return e.forexRefundAtTier
	}
	return nil
}

func (e *EarnedBenefitData) GetChequeBookOrderData() *dataCollector.ChequeBookOrderData {
	if e != nil {
		return e.chequeBookOrderData
	}
	return nil
}

func (e *EarnedBenefitData) GetRewardsData() *dataCollector.RewardsDataForEarnedBenefits {
	if e != nil {
		return e.rewardsData
	}
	return nil
}

func (e *EarnedBenefitData) GetTierToPitch() tieringExternalPb.Tier {
	if e != nil {
		return e.tierToPitch
	}
	return tieringExternalPb.Tier_TIER_UNSPECIFIED
}

func (e *EarnedBenefitData) GetTotalBenefitsEarned() *gmoney.Money {
	if e != nil {
		return e.totalBenefitsEarned
	}
	return nil
}

func (e *EarnedBenefitData) GetFetchFailedMap() map[feTieringPb.BenefitsType]bool {
	if e != nil {
		return e.fetchFailedMap
	}
	return nil
}

func (e *EarnedBenefitData) GetSegmentMembershipMap() map[string]*segmentPb.SegmentMembership {
	if e != nil {
		return e.segmentMembershipMap
	}
	return nil
}

func (e *EarnedBenefitData) GetAaSalaryData() *dataCollector.AaSalaryData {
	if e != nil {
		return e.aaSalaryData
	}
	return nil
}

func (e *EarnedBenefitData) GetCatalogOffers() *rewards.GetCatalogOffersAndFiltersResponse {
	if e != nil {
		return e.catalogOffers
	}
	return nil
}

func (r *EarnedBenefitData) GetRewardsResponse() *rewardsPb.RewardsResponse {
	if r != nil {
		return r.RewardsResponse
	}
	return nil
}

func (e *EarnedBenefitData) GetEvaluatedTier() tieringExternalPb.Tier {
	if e != nil {
		return e.evaluatedTier
	}
	return tieringExternalPb.Tier_TIER_UNSPECIFIED
}

func (e *EarnedBenefitData) GetIsMoneyPlantEnabled() bool {
	if e != nil {
		return e.isMoneyPlantEnabled
	}
	return false
}

func (e *EarnedBenefitHistoriesData) GetEarnedBenefitsData() *EarnedBenefitData {
	if e != nil {
		return e.earnedBenefitsData
	}
	return nil
}

func (e *EarnedBenefitHistoriesData) GetMonthlyRewardsData() []*dataCollector.MonthlyRewardsData {
	if e != nil {
		return e.monthlyRewardsData
	}
	return nil
}

func (e *EarnedBenefitHistoriesData) GetMonthlyForexData() []*dataCollector.MonthlyForexData {
	if e != nil {
		return e.monthlyForexData
	}
	return nil
}

func (e *EarnedBenefitHistoriesData) GetMonthlyBenefitsDisplayData() []*monthlyBenefitsDisplayData {
	if e != nil {
		return e.monthlyBenefitsDisplayData
	}
	return nil
}

func (m *monthlyBenefitsDisplayData) GetMonthYear() *dataCollector.MonthYear {
	if m != nil {
		return m.monthYear
	}
	return nil
}

func (m *monthlyBenefitsDisplayData) GetMonthYearStr() string {
	if m != nil {
		return m.monthYearStr
	}
	return ""
}

func (m *monthlyBenefitsDisplayData) GetCashbackEarned() *gmoney.Money {
	if m != nil {
		return m.cashbackEarned
	}
	return nil
}

func (m *monthlyBenefitsDisplayData) GetFeesSaved() *gmoney.Money {
	if m != nil {
		return m.feesSaved
	}
	return nil
}

func (m *monthlyBenefitsDisplayData) GetFiCoinsEarned() int32 {
	if m != nil {
		return m.fiCoinsEarned
	}
	return 0
}

func (m *monthlyBenefitsDisplayData) GetTotalBenefitsEarned() *gmoney.Money {
	if m != nil {
		return m.totalBenefitsEarned
	}
	return nil
}

func (e *EarnedBenefitData) GetEmployerData() *employment.EmployerInfo {
	if e != nil {
		return e.employerData
	}
	return nil
}

func (e *EarnedBenefitData) GetHealthInsuranceDetails() *dataCollector.HealthInsuranceDetails {
	if e != nil {
		return e.healthInsuranceDetails
	}
	return nil
}

func (e *EarnedBenefitData) GetIsAmbEnabledForActor() bool {
	if e != nil {
		return e.isAmbEnabledForActor
	}
	return false
}

func (e *EarnedBenefitData) GetAmbInfo() *tieringPb.GetAMBInfoResponse {
	if e != nil {
		return e.ambInfo
	}
	return nil
}

func (e *EarnedBenefitData) GetAmbShortfallExists() bool {
	if e != nil {
		return e.ambShortfallExists
	}
	return false
}
