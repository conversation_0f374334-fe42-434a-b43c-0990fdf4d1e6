package baseprovider

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	paltypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

// nolint: funlen, dupl
// this for returning all components to client, will deprecate it when not needed
func (bdp *BaseDeeplinkProvider) GetAcqToLendLandingScreen(lh *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_ACQ_TO_LEND_LANDING_SCREEN, &paltypes.AcqToLendLandingScreenOptionsV2{
		LoanHeader: lh,
		UiComponents: []*paltypes.AcqToLendUiComponent{
			{
				Component: &paltypes.AcqToLendUiComponent_SectionApplicationBar{
					SectionApplicationBar: &paltypes.SectionApplicationBar{
						ApplicationBar: &paltypes.SectionApplicationBar_OverFlowMenu_{
							OverFlowMenu: &paltypes.SectionApplicationBar_OverFlowMenu{
								OverflowItems: []*ui.IconTextComponent{
									{
										Texts: []*commontypes.Text{
											provider.GetTextWithBgColor("Customise loan", "#383838", ",", commontypes.FontStyle_SUBTITLE_2),
										},
										Deeplink: getEditLoanBottomDeeplink(),
										LeftVisualElement: &commontypes.VisualElement{
											Asset: &commontypes.VisualElement_Image_{
												Image: &commontypes.VisualElement_Image{
													Source: &commontypes.VisualElement_Image_Url{
														Url: "https://epifi-icons.pointz.in/preapprovedloan/atl-edit-pencil-icon.png",
													},
													Properties: &commontypes.VisualElementProperties{
														Width:  24,
														Height: 24,
													},
												},
											},
										}},
									{
										Texts: []*commontypes.Text{
											provider.GetTextWithBgColor("FAQs", "#383838", ",", commontypes.FontStyle_SUBTITLE_2),
										},
										Deeplink: &deeplinkPb.Deeplink{
											Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_KNOW_MORE_SCREEN,
											ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanKnowMoreScreenOptions{
												PreApprovedLoanKnowMoreScreenOptions: &deeplinkPb.PreApprovedLoanKnowMoreScreenOptions{
													LoanHeader: lh,
												},
											},
										},
										LeftVisualElement: provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/no-offer-help-circle.png", 24, 24),
									},
								},
							},
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionHeader{
					SectionHeader: &paltypes.SectionHeader{
						VisualElementTitleSubtitleElement: &widget.VisualElementTitleSubtitleElement{
							VisualElement: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source: &commontypes.VisualElement_Image_Url{
											Url: "https://epifi-icons.pointz.in/preapprovedloan/atl-pen-pad.png",
										},
										Properties: &commontypes.VisualElementProperties{
											Width:  120,
											Height: 120,
										},
										ImageType: commontypes.ImageType_PNG,
									},
								},
							},
							TitleText:    provider.GetTextWithBgColor("Get the best loan offer", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
							SubtitleText: provider.GetTextWithBgColor("It only takes 2 mins", "#929599", "", commontypes.FontStyle_BODY_S),
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionTypeGrid{
					SectionTypeGrid: &paltypes.SectionTypeGrid{
						SectionTitle: &commontypes.Text{
							FontColor:    "#6294A6",
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Cash in minutes"},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E4F1F5"},
						},
						InformationBlocks: []*ui.VerticalIconTextComponent{
							{
								Texts: []*commontypes.Text{
									{
										FontColor:    "#6294A6",
										DisplayValue: &commontypes.Text_PlainString{PlainString: "Competitive\ninterest rates"},
										FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
									},
								},
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/atl-money-percent.png",
								},
							},
							{
								Texts: []*commontypes.Text{
									{
										FontColor:    "#6294A6",
										DisplayValue: &commontypes.Text_PlainString{PlainString: "Zero pre-\nclosure fees"},
										FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
									},
								},
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/atl-zero-fee.png",
								},
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/atl-purple-thunder.png",
								},
								Texts: []*commontypes.Text{
									{
										FontColor:    "#6294A6",
										DisplayValue: &commontypes.Text_PlainString{PlainString: "Quick\ndisbursal"},
										FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
									},
								},
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/atl-phone.png",
								},
								Texts: []*commontypes.Text{
									{
										FontColor:    "#6294A6",
										DisplayValue: &commontypes.Text_PlainString{PlainString: "100%\ndigital"},
										FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
									},
								},
							},
						},
						NumOfRows: 2,
						NumOfCols: 2,
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionTypeBanner{
					SectionTypeBanner: &paltypes.SectionTypeBanner{
						SectionTitle: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									FontColor:    "#E8AD62",
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Learn More\nabout loans"},
									FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
								},
							},
							RightVisualElement: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source: &commontypes.VisualElement_Image_Url{
											Url: "https://epifi-icons.pointz.in/preapprovedloan/atl-banner-right-chevron.png",
										},
										Properties: &commontypes.VisualElementProperties{
											Width:  108,
											Height: 88,
										},
										ImageType: commontypes.ImageType_PNG,
									},
								},
							},
						},
						BenefitIcon: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Image_{
								Image: &commontypes.VisualElement_Image{
									Source: &commontypes.VisualElement_Image_Url{
										Url: "https://epifi-icons.pointz.in/preapprovedloan/atl-money-bag.png",
									},
									Properties: &commontypes.VisualElementProperties{
										Width:  108,
										Height: 88,
									},
									ImageType: commontypes.ImageType_PNG,
								},
							},
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFCEB"},
						},
						Cta: &deeplinkPb.Cta{
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_KNOW_MORE_SCREEN,
								ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanKnowMoreScreenOptions{
									PreApprovedLoanKnowMoreScreenOptions: &deeplinkPb.PreApprovedLoanKnowMoreScreenOptions{
										LoanHeader: lh,
									},
								},
							},
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionTypeOfferTicket{
					SectionTypeOfferTicket: &paltypes.SectionTypeOfferTicket{
						OfferExpiryText: &commontypes.TextWithIcon{
							Text:    provider.GetTextWithBgColor("Offer expires in 25 days", "#2D5E6E", "#BCDCE7", commontypes.FontStyle_OVERLINE_2XS_CAPS),
							IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/atl-blue-clock.png",
						},
						TicketTitle:       provider.GetTextWithBgColor("Congratulations!", "#313234", "", commontypes.FontStyle_DISPLAY_XL),
						TicketDescription: provider.GetTextWithBgColor("You have got a loan offer.", "#929599", "", commontypes.FontStyle_SUBTITLE_S),
						TicketBox: &paltypes.SectionTypeOfferTicket_TicketBox{
							BgColor: "#FFFFFF",
							OfferAmount: &types.Money{
								CurrencyCode: "INR",
								Units:        200000,
							},
							InterestRate: provider.GetTextWithBgColor(fmt.Sprintf("@ %.2f%% interest", 9.28), "#2D5E6E", "#E4F1F5", commontypes.FontStyle_SUBTITLE_S),
						},
						BgColor: "#EFF2F6",
						BackgroundImage: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Image_{
								Image: &commontypes.VisualElement_Image{
									Source: &commontypes.VisualElement_Image_Url{
										Url: "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-success-bg-card.png",
									},
									Properties: &commontypes.VisualElementProperties{
										Width:  344,
										Height: 380,
									},
								},
							},
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionTypeLoanDetails{
					SectionTypeLoanDetails: &paltypes.SectionTypeLoanDetails{
						BgColor: "#F6F9FD",
						LoanDetails: &deeplinkPb.InfoBlockV2{
							Title: provider.GetTextWithBgColor("DETAILS", "#929599", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
							InfoItems: []*deeplinkPb.InfoItemV2{
								{
									Title: provider.GetTextWithBgColor("Loan amount", "#6A6D70", "", commontypes.FontStyle_HEADLINE_S),
									Desc:  provider.GetTextWithBgColor("1,20,000", "#313234", "", commontypes.FontStyle_NUMBER_S),
								},
								{
									Title: provider.GetTextWithBgColor("Duration", "#6A6D70", "", commontypes.FontStyle_HEADLINE_S),
									Desc:  provider.GetTextWithBgColor("11.5%", "#313234", "", commontypes.FontStyle_NUMBER_S),
								},
								{
									Title: provider.GetTextWithBgColor("Interest Rate", "#6A6D70", "", commontypes.FontStyle_HEADLINE_S),
									Desc:  provider.GetTextWithBgColor("1,20,000", "#313234", "", commontypes.FontStyle_NUMBER_S),
								},
							},
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionApplicationMessage{
					SectionApplicationMessage: &paltypes.SectionApplicationMessage{
						Message: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Video KYC reviews take 1–2 business days. We’ll \nnotify you once the verification is complete ⏳"},
								},
							},
							LeftVisualElement: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source: &commontypes.VisualElement_Image_Url{
											Url: "https://epifi-icons.pointz.in/preapprovedloan/loan-management-pending.png",
										},
										Properties: &commontypes.VisualElementProperties{
											Width:  20,
											Height: 20,
										},
									},
								},
							},
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								BgColor:       "#FBF3E6",
								CornerRadius:  16,
								LeftPadding:   16,
								RightPadding:  16,
								TopPadding:    16,
								BottomPadding: 16,
							},
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionTypeProgress{
					SectionTypeProgress: &paltypes.SectionTypeProgress{
						ProgressComponents: []*paltypes.SectionTypeProgress_SectionTypeProgressComponent{
							{
								Icon: &commontypes.VisualElement{
									Asset: &commontypes.VisualElement_Image_{
										Image: &commontypes.VisualElement_Image{
											Source: &commontypes.VisualElement_Image_Url{
												Url: "https://epifi-icons.pointz.in/preapprovedloan/progress-green-tick.png",
											},
											Properties: &commontypes.VisualElementProperties{
												Width:  20,
												Height: 20,
											},
										},
									},
								},
								Text:               provider.GetTextWithBgColor("See\noffer", "#313234", "", commontypes.FontStyle_OVERLINE_2XS_CAPS),
								IsCompleted:        true,
								FollowUpLineColour: "#D5E6CE",
							},
							{
								Icon: &commontypes.VisualElement{
									Asset: &commontypes.VisualElement_Image_{
										Image: &commontypes.VisualElement_Image{
											Source: &commontypes.VisualElement_Image_Url{
												Url: "https://epifi-icons.pointz.in/preapprovedloan/progress-green-tick.png",
											},
											Properties: &commontypes.VisualElementProperties{
												Width:  20,
												Height: 20,
											},
										},
									},
								},
								Text:               provider.GetTextWithBgColor("Customise\nloan", "#313234", "", commontypes.FontStyle_OVERLINE_2XS_CAPS),
								IsCompleted:        true,
								FollowUpLineColour: "#B2B5B9",
							},
							{
								Icon: &commontypes.VisualElement{
									Asset: &commontypes.VisualElement_Image_{
										Image: &commontypes.VisualElement_Image{
											Source: &commontypes.VisualElement_Image_Url{
												Url: "https://epifi-icons.pointz.in/preapprovedloan/progress-green-tick.png",
											},
											Properties: &commontypes.VisualElementProperties{
												Width:  20,
												Height: 20,
											},
										},
									},
								},
								Text:        provider.GetTextWithBgColor("Verify\ndetails", "#B2B5B9", "", commontypes.FontStyle_OVERLINE_2XS_CAPS),
								IsCompleted: false,
							},
						},
					},
				},
			},
			{
				Component: &paltypes.AcqToLendUiComponent_SectionFooter{
					SectionFooter: &paltypes.SectionFooter{
						Button: &deeplinkPb.Button{
							Text: &commontypes.Text{
								FontColor:    "#FFFFFF",
								BgColor:      "#00B899",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "Get a loan"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							},
							Padding: &deeplinkPb.Button_Padding{
								LeftPadding:   12,
								RightPadding:  24,
								TopPadding:    12,
								BottomPadding: 24,
							},
							Margin: &deeplinkPb.Button_Margin{
								LeftMargin:   32,
								RightMargin:  32,
								BottomMargin: 15,
								TopMargin:    28,
							},
							Cta: &deeplinkPb.Cta{
								Type:   deeplinkPb.Cta_CUSTOM,
								Status: deeplinkPb.Cta_CTA_STATUS_ENABLED,
							},
						},
						BottomItc: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									FontColor:    "#00B899",
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Explore all Fi products"},
									FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
								},
							},
							LeftVisualElement: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source: &commontypes.VisualElement_Image_Url{
											Url: "https://epifi-icons.pointz.in/preapprovedloan/atl-explore-product.png",
										},
										Properties: &commontypes.VisualElementProperties{
											Width:  41,
											Height: 20,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	})
	if dlErr != nil {
		logger.ErrorNoCtx("error to get atl landing screen deeplink", zap.Error(dlErr))
		dl = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN}
	}
	return dl
}

// nolint: funlen, dupl
func (bdp *BaseDeeplinkProvider) GetApplicationProgressScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, req *provider.GetApplicationProgressScreenRequest, applMsg *palTypesPb.SectionApplicationMessage) (*deeplinkPb.Deeplink, error) {
	logger.ErrorNoCtx("unimplemented GetApplicationProgressScreen")
	return nil, epifierrors.ErrMethodUnimplemented
}

func (bdp *BaseDeeplinkProvider) GetEligibilitySuccessScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, offerAmount *money.Money, interestRate float64, offerId string, showOfferV2 bool, lo *palBePb.LoanOffer) *deeplinkPb.Deeplink {
	logger.ErrorNoCtx("unimplemented GetEligibilitySuccessScreen")
	return nil
}
func getEditLoanBottomDeeplink() *deeplinkPb.Deeplink {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_EDIT_LOAN_BOTTOM_SHEET, &paltypes.EditLoanBottomSheetOptions{
		VisualElementTitleSubtitleElement: &widget.VisualElementTitleSubtitleElement{
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/preapprovedloan/atl-clear-history.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  120,
							Height: 120,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			TitleText:    provider.GetTextWithBgColor("Want to change your loan amount?", "#333333", "", commontypes.FontStyle_HEADLINE_L),
			SubtitleText: provider.GetTextWithBgColor("You'll need to verify your details again, per our safety guidelines.", "#8D8D8D", "", commontypes.FontStyle_BODY_S),
		},
		BackCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Go back",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		ProceedCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Change Now",
			DisplayTheme: deeplinkPb.Cta_SECONDARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
	})
	if dlErr != nil {
		logger.ErrorNoCtx("error to get atl landing screen deeplink", zap.Error(dlErr))
		dl = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PL_ACQ_TO_LEND_LANDING_SCREEN}
	}
	return dl
}

func (bdp *BaseDeeplinkProvider) GetNextDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, showOfferV2 bool, actorId string, postLoansV2Active bool) (*deeplinkPb.Deeplink, error) {
	logger.ErrorNoCtx("unimplemented GetNextDeeplink")
	return nil, nil
}

func (bdp *BaseDeeplinkProvider) GetDashboardMsgComponent(lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, loanSteps []*palBePb.LoanStepExecution) (*palTypesPb.SectionApplicationMessage, bool, bool, bool) {
	var latestLse *palBePb.LoanStepExecution
	if len(loanSteps) > 0 {
		latestLse = loanSteps[0]
	}
	applicationData := getApplicationStateData(lr, latestLse)

	canStartAnotherApp := canStartAnotherApplication(lr)
	return convertToApplMsgComponent(applicationData), applicationData.GetCanCancel(), canStartAnotherApp, applicationData.GetDl()
}

func canStartAnotherApplication(currentLr *palBePb.LoanRequest) bool {
	// allow user to start another application only if the current application is terminal because only one active application is allowed at a time
	// LR success means loan account is created, even in that case new application is not allowed
	return currentLr.IsFailedTerminal()
}

// this will convert application  status message SectionApplicationMessage component
func convertToApplMsgComponent(data *enrichData) *palTypesPb.SectionApplicationMessage {

	return &palTypesPb.SectionApplicationMessage{
		Message: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: data.GetMessage()},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       data.GetBgColor(),
				CornerRadius:  19,
				LeftPadding:   16,
				RightPadding:  16,
				TopPadding:    16,
				BottomPadding: 16,
			},
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: data.GetIcon(),
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  20,
							Height: 20,
						},
					},
				},
			},
		},
	}
}
