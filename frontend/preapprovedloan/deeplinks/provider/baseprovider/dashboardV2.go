package baseprovider

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"
)

const (
	getLoanCtaText1           string = "Apply again"
	getLoanCtaText2           string = "Get another loan"
	exploreAlternateOfferText string = "Explore another offer"
)

// nolint: funlen
func (bdp *BaseDeeplinkProvider) GetLoanDashboardScreenDeepLinkWithScreenOptionsV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palPb.GetDashboardResponse, showLoanOfferScreenV2 bool, entryPoint string) (*deeplinkpb.Deeplink, error) {
	totalEmi := money.ZeroINR().GetPb()
	loanPaidCount := 0
	var thisMonthPaidInstallmentDate *date.Date
	for _, li := range res.GetLoanInfoList() {
		var err error
		switch li.GetLoanAccount().GetStatus() {
		case palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE:
			if totalEmi, thisMonthPaidInstallmentDate, err = getTotalEmiForInstallmentInfoList(li.GetInstallmentInfoList(), totalEmi); err != nil {
				return nil, errors.Wrap(err, "failed to get Total Emi For InstallmentInfo List")
			}
		case palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED:
			loanPaidCount += 1
		default:
			continue
		}
	}
	var pastLoanDetails *deeplinkpb.InfoItem
	if loanPaidCount > 0 {
		loanString := "loan"
		if loanPaidCount > 1 {
			loanString = "loans"
		}
		pastLoanDetails = &deeplinkpb.InfoItem{
			Title: "Past Loans",
			Desc:  fmt.Sprintf("%v %v fully repaid", loanPaidCount, loanString),
		}
	}

	var lds []*deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails
	showAnotherLoanCta := false

	if res.GetRecentLoanRequest() != nil && res.GetRecentLoanRequest().GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
		ld, _ := enrichLoanApplicationDetailsV2(lh, res.GetRecentLoanRequest(), res.GetLoanSteps())
		if ld != nil {
			lds = append(lds, ld)
		}
		showAnotherLoanCta = canStartAnotherApplication(res.GetRecentLoanRequest())
	}

	exploreAlterateOffer := false
	// if Lr is in failed/cancelled state and loan offer exists in res, set the another loan cta to true
	if isTerminalState(res.GetRecentLoanRequest().GetStatus()) && res.GetActiveLoanOffer() != nil {
		exploreAlterateOffer = !(res.GetRecentLoanRequest().GetLoanProgram() == res.GetActiveLoanOffer().GetLoanProgram() &&
			res.GetRecentLoanRequest().GetVendor() == res.GetActiveLoanOffer().GetVendor())
	}

	loanNumber := 0
	for _, li := range res.GetLoanInfoList() {
		if li.GetLoanAccount().GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			// If any active loans already present, get a new loan CTA design to be secondary type CTA
			for _, ii := range li.GetInstallmentInfoList() {
				if ii.GetLoanInstallmentInfo().GetDeactivatedAt() != nil {
					continue
				}
				loanNumber += 1
				lds = append(lds, fillActiveLoanDetails(lh, li, ii, thisMonthPaidInstallmentDate))
			}
		}
	}

	ctaDeeplink := &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_OFFER_DETAILS_SCREEN,
		ScreenOptions: &deeplinkpb.Deeplink_PreApprovedLoanOfferDetailsScreenOptions{
			PreApprovedLoanOfferDetailsScreenOptions: &deeplinkpb.PreApprovedLoanOfferDetailsScreenOptions{
				// change LH, to get frm loan offer
				LoanHeader: &palPbFeEnums.LoanHeader{
					LoanProgram: helper.GetFeLoanProgramFromBe(res.GetActiveLoanOffer().GetLoanProgram()),
					Vendor:      helper.GetPalFeVendorFromBe(res.GetActiveLoanOffer().GetVendor()),
				},
				OfferId: res.GetActiveLoanOffer().GetId(),
			},
		},
	}
	if res.GetActiveLoanOffer().GetVendor() == palPb.Vendor_MONEYVIEW {
		ctaDeeplink = bdp.GetLoanLandingInfo(ctx, &palPbFeEnums.LoanHeader{
			Vendor:      helper.GetPalFeVendorFromBe(res.GetActiveLoanOffer().GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(res.GetActiveLoanOffer().GetLoanProgram()),
		}, nil)
	}

	getAnotherLoanCta := &deeplinkpb.Cta{
		Text:         getLoanCtaText1,
		Deeplink:     ctaDeeplink,
		DisplayTheme: deeplinkpb.Cta_PRIMARY,
		Status:       deeplinkpb.Cta_CTA_STATUS_DISABLED,
	}
	if loanNumber > 0 {
		getAnotherLoanCta.Text = getLoanCtaText2
		getAnotherLoanCta.DisplayTheme = deeplinkpb.Cta_SECONDARY
	}
	if exploreAlterateOffer {
		getAnotherLoanCta.Text = exploreAlternateOfferText
	}
	if res.GetCheckUserEligibility() && showAnotherLoanCta {
		getAnotherLoanCta.Status = deeplinkpb.Cta_CTA_STATUS_ENABLED
	}

	return &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN,
		ScreenOptions: &deeplinkpb.Deeplink_PreApprovedLoanDashboardScreenOptions{
			PreApprovedLoanDashboardScreenOptions: &deeplinkpb.PreApprovedLoanDashboardScreenOptions{
				TopBanner: &deeplinkpb.PreApprovedLoanDashboardScreenOptions_TopBanner{
					Title:                fmt.Sprintf("Total EMI for %v", time.Now().In(datetime.IST).Format("Jan")),
					SubTitle:             "for active loans",
					TotalEmiForCurrMonth: types.GetFromBeMoney(totalEmi),
				},
				LoanDetails:       lds,
				GetAnotherLoanCta: getAnotherLoanCta,
				PastLoanDetails:   pastLoanDetails,
				BottomInfoTiles: []*deeplinkpb.Tile{
					GetFaqTile(lh),
				},
				PartnershipUrl: "https://epifi-icons.pointz.in/preapprovedloan/dashboard-partnership.png",
				LoanHeader:     lh,
			},
		},
	}, nil
}

// nolint: dupl, funlen
func fillActiveLoanDetails(lh *palPbFeEnums.LoanHeader, li *palPb.LoanInfo, ii *palPb.InstallmentInfo, thisMonthPaidInstallmentDate *date.Date) *deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails {
	return &deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails{
		Title:      li.GetLoanAccount().GetDetails().GetLoanName(),
		LoanAmount: types.GetFromBeMoney(li.GetLoanAccount().GetLoanAmountInfo().GetLoanAmount()),
		Details: []*deeplinkpb.InfoItem{
			{
				Title: "TENURE",
				Desc:  fmt.Sprintf("%v months", li.GetLoanAccount().GetDetails().GetTenureInMonths()),
			},
			{
				Title: "INTEREST",
				Desc:  fmt.Sprintf("%v%%", li.GetLoanAccount().GetDetails().GetInterestRate()),
			},
			{
				Title: "EMI",
				Desc:  money.ToDisplayStringInIndianFormat(ii.GetLoanInstallmentInfo().GetDetails().GetNextEmiAmount(), 0, true),
			},
			GetAprInfoItem(li.GetLoanAccount().GetDetails().GetAprRate()),
		},
		EmiDetails: &deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails_EmiDetails{
			Title:       "EMI Payments",
			EmiEntities: getEmiEntities(ii.GetLoanInstallmentInfo().GetNextInstallmentDate(), thisMonthPaidInstallmentDate),
		},
		MoreDetails: &deeplinkpb.Cta{
			Type: deeplinkpb.Cta_CUSTOM,
			Text: "More details",
			Deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_PreApprovedLoanDetailsScreenOptions{
					PreApprovedLoanDetailsScreenOptions: &deeplinkpb.PreApprovedLoanDetailsScreenOptions{
						LoanId:     li.GetLoanAccount().GetId(),
						LoanHeader: lh,
					},
				},
			},
			DisplayTheme: deeplinkpb.Cta_TEXT,
			Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
		},
		LoanId:        li.GetLoanAccount().GetId(),
		OverflowItems: getAccountOverFlowItem(li.GetLoanAccount()),
	}
}

// nolint: funlen
func enrichLoanApplicationDetailsV2(lh *palPbFeEnums.LoanHeader, lr *palPb.LoanRequest, loanSteps []*palPb.LoanStepExecution) (*deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails, *enrichData) {
	applicationData := defaultEnrichData

	statusPollDl := &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkpb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
			PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkpb.PreApprovedLoanApplicationStatusPollScreenOptions{
				LoanRequestId: lr.GetId(),
				PollingText: &deeplinkpb.InfoItemV2{
					Title: &commontypes.Text{
						FontColor:    "#333333",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Please wait"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
					},
				},
				LoanHeader:       lh,
				BackgroundColour: &widgetPb.BackgroundColour{Colour: &widgetPb.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
			},
		},
	}

	var latestLse *palPb.LoanStepExecution
	if len(loanSteps) > 0 {
		latestLse = loanSteps[0]
	}
	applicationData = getApplicationStateData(lr, latestLse)

	ld := &deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails{
		Title:      "LOAN APPLICATION #1",
		LoanAmount: types.GetFromBeMoney(lr.GetDetails().GetLoanInfo().GetAmount()),
		Details: []*deeplinkpb.InfoItem{
			{
				Title: "TENURE",
				Desc:  fmt.Sprintf("%v months", lr.GetDetails().GetLoanInfo().GetTenureInMonths()),
			},
			{
				Title: "INTEREST",
				Desc:  fmt.Sprintf("%v%%", lr.GetDetails().GetLoanInfo().GetInterestRate()),
			},
			{
				Title: "EMI",
				Desc:  money.ToDisplayStringInIndianFormat(lr.GetDetails().GetLoanInfo().GetEmiAmount(), 0, true),
			},
		},
		OverflowItems:     getApplicationOverFlowItemV2(lh, lr, applicationData.GetCanCancel()),
		LoanState:         &deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails_LoanState{},
		InProgressMessage: &deeplinkpb.PreApprovedLoanDashboardScreenOptions_LoanDetails_InProgressMessage{},
	}

	ld.GetInProgressMessage().Message = applicationData.GetMessage()
	ld.GetInProgressMessage().BgColour = applicationData.GetBgColor()
	ld.GetInProgressMessage().IconUrl = applicationData.GetIcon()
	if applicationData.statusPollDl {
		ld.GetInProgressMessage().NextStep = &deeplinkpb.Cta{
			Deeplink: statusPollDl,
			Text:     applicationData.GetCtaText(),
		}
	}
	ld.GetLoanState().Title = applicationData.GetTitle()
	ld.GetLoanState().BgColour = applicationData.GetBgColor()
	if applicationData.GetShowReloadCta() {
		ld.ReloadCta = &deeplinkpb.InfoItemWithCta{
			Info: &deeplinkpb.InfoItem{
				Title: "Update status now",
			},
			// TODO(@prasoon): Can add the CTA if needed in future as of now behaviour is to recall the fetchAPI
		}
	}

	return ld, applicationData
}

func getApplicationOverFlowItemV2(lh *palPbFeEnums.LoanHeader, req *palPb.LoanRequest, canCancel bool) []*deeplinkpb.Cta {
	var flowItems []*deeplinkpb.Cta
	if canCancel {
		cancelCta := &deeplinkpb.Cta{
			Type: deeplinkpb.Cta_CUSTOM,
			Text: "Cancel Application",
			Deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_CANCEL_APPLICATION_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_PreApprovedLoanCancelApplicationScreenOptions{
					PreApprovedLoanCancelApplicationScreenOptions: &deeplinkpb.PreApprovedLoanCancelApplicationScreenOptions{
						LoanReqId:  req.GetId(),
						Title:      "Cancel Application",
						IconUrl:    "https://epifi-icons.pointz.in/preapprovedloan/cancel-application.png",
						Message:    "Are you sure you want to cancel this loan application?",
						LoanHeader: lh,
					},
				},
			},
			DisplayTheme: deeplinkpb.Cta_SECONDARY,
			Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
		}
		flowItems = append(flowItems, cancelCta)
	}
	// TODO(@prasoon): Add edit details overflow item
	return flowItems
}

func isTerminalState(status palPb.LoanRequestStatus) bool {
	return status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED || status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED
}
