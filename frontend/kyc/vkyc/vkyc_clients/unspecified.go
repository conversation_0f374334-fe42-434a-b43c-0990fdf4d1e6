//nolint:dupl
package vkyc_clients

import (
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
)

// Unspecified struct handles case of unspecified entry point
type Unspecified struct {
	EntryPoint deeplinkPb.EntryPoint
}

//nolint:dupl
func (s *Unspecified) GetApprovedOrFullKycUserDeeplink() *deeplinkPb.Deeplink {
	// fallback in case caller didnt provide deeplink
	ctaList := []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_DONE,
			Text:         documentInReviewCtaText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_VKYC_STATUS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_VkycStatusScreenOptions{
			VkycStatusScreenOptions: &deeplinkPb.VKYCStatusScreenOptions{
				EntryPoint: s.EntryPoint,
				IconUrl:    alreadyFullKYCIconUrl,
				Title:      alreadyFullKYCTitle,
				Subtitle:   alreadyFullKYCDetails,
				CtaList:    ctaList,
			},
		},
	}
}

//nolint:dupl
func (s *Unspecified) GetVKYCStatusInReviewDeeplink() *deeplinkPb.Deeplink {
	// deeplink for vkyc status in review
	ctaList := []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_DONE,
			Text:         documentInReviewCtaText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_VKYC_STATUS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_VkycStatusScreenOptions{
			VkycStatusScreenOptions: &deeplinkPb.VKYCStatusScreenOptions{
				EntryPoint: s.EntryPoint,
				IconUrl:    documentInReviewIconUrl,
				Title:      documentInReviewTitle,
				Subtitle:   documentInReviewSubtitle,
				CtaList:    ctaList,
			},
		},
	}
}

//nolint:dupl
func (s *Unspecified) GetVKYCStatusRejectedDeeplink() *deeplinkPb.Deeplink {
	// deeplink for vkyc status rejected
	ctaList := []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_DONE,
			Text:         auditorRejectedCtaText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_VKYC_STATUS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_VkycStatusScreenOptions{
			VkycStatusScreenOptions: &deeplinkPb.VKYCStatusScreenOptions{
				EntryPoint: s.EntryPoint,
				IconUrl:    auditorRejectedIconUrl,
				Title:      auditorRejectedTitle,
				Subtitle:   auditorRejectedSubtitle,
				CtaList:    ctaList,
			},
		},
	}
}

//nolint:dupl
func (s *Unspecified) GetVKYCStatusOtherDeeplink(instructionScreenDeeplink *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	// deeplink for vkyc status != approved, review, rejected
	ctaList := []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         salaryScreenCtaText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink:     instructionScreenDeeplink,
		},
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_VKYC_STATUS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_VkycStatusScreenOptions{
			VkycStatusScreenOptions: &deeplinkPb.VKYCStatusScreenOptions{
				EntryPoint: s.EntryPoint,
				IconUrl:    liveProfileIconUrl,
				Title:      defaultScreenTitle,
				Subtitle:   defaultScreenSubtitle,
				CtaList:    ctaList,
			},
		},
	}
}
