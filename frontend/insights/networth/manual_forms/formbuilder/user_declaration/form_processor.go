package user_declaration

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifigrpc"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	userDeclarationPb "github.com/epifi/gamma/api/insights/user_declaration"
	userDeclarationFromPb "github.com/epifi/gamma/api/insights/user_declaration/form"
	userDeclarationModelPb "github.com/epifi/gamma/api/insights/user_declaration/model"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
)

type UserDeclarationFromProcessor struct {
	userDeclarationClient             userDeclarationPb.ServiceClient
	userDeclarationFormBuilderFactory UserDeclarationFormBuilderFactory
}

func NewUserDeclarationFromProcessor(
	userDeclarationClient userDeclarationPb.ServiceClient,
	userDeclarationFormBuilderFactory UserDeclarationFormBuilderFactory) *UserDeclarationFromProcessor {
	return &UserDeclarationFromProcessor{
		userDeclarationClient:             userDeclarationClient,
		userDeclarationFormBuilderFactory: userDeclarationFormBuilderFactory,
	}
}

func (u *UserDeclarationFromProcessor) BuildForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	userDeclaration, userDeclarationType, err := u.getDeclarationAndDeclarationType(ctx, req.ActorId, req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get declaration and declaration type: %w", err)
	}

	userDeclarationFormBuilder, err := u.userDeclarationFormBuilderFactory.GetUserDeclarationFormBuilder(ctx, userDeclarationType)
	if err != nil {
		return nil, fmt.Errorf("failed to get form user declaration form builder : %w", err)
	}

	return userDeclarationFormBuilder.BuildForm(ctx, &BuildFormRequest{
		ActorId:             req.ActorId,
		UserDeclarationType: userDeclarationType,
		UserDeclaration:     userDeclaration,
	})
}

func (u *UserDeclarationFromProcessor) getDeclarationAndDeclarationType(ctx context.Context, actorId string, formIdentifier *typesv2.ManualAssetFormIdentifier) (*userDeclarationModelPb.Declaration, userDeclarationModelPb.UserDeclarationType, error) {
	userDeclarationFromIdentifier, err := u.extractUserDeclarationFormIdentifier(formIdentifier)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to extract user declaration form identifier: %w", err)
	}

	var (
		declaration         *userDeclarationModelPb.Declaration
		userDeclarationType userDeclarationModelPb.UserDeclarationType
	)
	switch userDeclarationFromIdentifier.GetId().(type) {
	case *userDeclarationFromPb.UserDeclarationFormIdentifier_ExternalId:
		userDeclaration, err := u.getUserDeclaration(ctx, actorId, userDeclarationFromIdentifier.GetExternalId())
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get user declaration: %w", err)
		}
		declaration = userDeclaration.GetUserDeclaration()
		userDeclarationType = userDeclaration.GetDeclarationType()
	case *userDeclarationFromPb.UserDeclarationFormIdentifier_UserDeclarationType:
		userDeclarationType = userDeclarationFromIdentifier.GetUserDeclarationType()
	default:
		return nil, 0, fmt.Errorf("unhandled user declaration form identifier %T", userDeclarationFromIdentifier.GetId())
	}

	return declaration, userDeclarationType, nil
}

func (u *UserDeclarationFromProcessor) extractUserDeclarationFormIdentifier(formIdentifier *typesv2.ManualAssetFormIdentifier) (*userDeclarationFromPb.UserDeclarationFormIdentifier, error) {
	_, ok := formIdentifier.GetIdentifier().(*typesv2.ManualAssetFormIdentifier_GenericIdentifier)
	if !ok {
		return nil, fmt.Errorf("invalid form identifier %T", formIdentifier.GetIdentifier())
	}

	id := &networthBeFePb.ManualFormIdentifier{}
	err := protojson.Unmarshal([]byte(formIdentifier.GetGenericIdentifier()), id)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal external id: %w", err)
	}

	_, ok = id.GetIdentifier().(*networthBeFePb.ManualFormIdentifier_InsightsUserDeclarationFormIdentifier)
	if !ok {
		return nil, fmt.Errorf("invalid form identifier of type %T", id.GetIdentifier())
	}

	return id.GetInsightsUserDeclarationFormIdentifier(), nil
}

func (u *UserDeclarationFromProcessor) getUserDeclaration(ctx context.Context, actorId string, externalId string) (*userDeclarationModelPb.UserDeclaration, error) {
	res, err := u.userDeclarationClient.GetUserDeclaration(ctx, &userDeclarationPb.GetUserDeclarationRequest{
		ActorId: actorId,
		UserDeclaration: &userDeclarationPb.GetUserDeclarationRequest_ExternalId{
			ExternalId: externalId,
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get user declaration with external id %s: %w", externalId, rpcErr)
	}
	return res.GetUserDeclaration(), nil
}

func (u *UserDeclarationFromProcessor) SubmitForm(ctx context.Context, req *formbuilder.SubmitFormRequest) error {
	_, userDeclarationType, err := u.getDeclarationAndDeclarationType(ctx, req.ActorId, req.FormIdentifier)
	if err != nil {
		return fmt.Errorf("failed to user declaration type: %w", err)
	}

	userDeclarationFormBuilder, err := u.userDeclarationFormBuilderFactory.GetUserDeclarationFormBuilder(ctx, userDeclarationType)
	if err != nil {
		return fmt.Errorf("failed to get form user declaration form builder : %w", err)
	}

	userDeclarationFromIdentifier, err := u.extractUserDeclarationFormIdentifier(req.FormIdentifier)
	if err != nil {
		return fmt.Errorf("failed to extract user declaration form identifier: %w", err)
	}

	declaration, err := userDeclarationFormBuilder.VerifyAndConvertFormInputToUserDeclaration(ctx, &VerifyAndConvertFormInputToUserDeclarationRequest{
		inputComponents:               req.InputComponents,
		UserDeclarationType:           userDeclarationType,
		UserDeclarationFromIdentifier: userDeclarationFromIdentifier,
		ActorId:                       req.ActorId,
	})
	if err != nil {
		return fmt.Errorf("failed to convert form input to user declaration: %w", err)
	}

	switch userDeclarationFromIdentifier.GetId().(type) {
	case *userDeclarationFromPb.UserDeclarationFormIdentifier_ExternalId:
		res, err := u.userDeclarationClient.UpdateUserDeclaration(ctx, &userDeclarationPb.UpdateUserDeclarationRequest{
			ActorId:         req.ActorId,
			ExternalId:      userDeclarationFromIdentifier.GetExternalId(),
			UserDeclaration: declaration,
		})
		if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
			return fmt.Errorf("failed to update user declaration: %w", rpcErr)
		}
	case *userDeclarationFromPb.UserDeclarationFormIdentifier_UserDeclarationType:
		res, err := u.userDeclarationClient.CreateUserDeclaration(ctx, &userDeclarationPb.CreateUserDeclarationRequest{
			ActorId:             req.ActorId,
			UserDeclarationType: userDeclarationType,
			UserDeclaration:     declaration,
		})
		if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
			return fmt.Errorf("failed to create user declaration, declaration type: %s: %w", userDeclarationType, rpcErr)
		}
	default:
		return fmt.Errorf("unhandled user declaration form identifier %T", userDeclarationFromIdentifier.GetId())
	}
	return nil
}

func (u *UserDeclarationFromProcessor) BuildEmptyForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	_, userDeclarationType, err := u.getDeclarationAndDeclarationType(ctx, req.ActorId, req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get declaration and declaration type: %w", err)
	}

	userDeclarationFormBuilder, err := u.userDeclarationFormBuilderFactory.GetUserDeclarationFormBuilder(ctx, userDeclarationType)
	if err != nil {
		return nil, fmt.Errorf("failed to get form user declaration form builder : %w", err)
	}

	return userDeclarationFormBuilder.BuildForm(ctx, &BuildFormRequest{
		ActorId:             req.ActorId,
		UserDeclarationType: userDeclarationType,
	})
}
