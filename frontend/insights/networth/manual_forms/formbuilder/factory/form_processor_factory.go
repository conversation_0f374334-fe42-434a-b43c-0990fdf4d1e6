package factory

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"google.golang.org/protobuf/encoding/protojson"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/networth"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/profile"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/user_declaration"
)

var FormProcessorWireSet = wire.NewSet(NewFormProcessorFactory, wire.Bind(new(formbuilder.FormProcessor), new(*FormProcessorFactory)),
	networth.NewNetworthFormBuilder, user_declaration.NewUserDeclarationFromProcessor, profile.NewProfileDataCollectionFormProcessor,
	profile.NewPanFormProcessor, profile.NewDobFormProcessor)

type FormProcessorFactory struct {
	networthFormProcessor         *networth.NetworthFormProcessor
	userDeclarationFromProcessor  *user_declaration.UserDeclarationFromProcessor
	profileDataCollectorProcessor *profile.ProfileDataCollectionFormProcessor
}

var _ formbuilder.FormProcessor = &FormProcessorFactory{}

func NewFormProcessorFactory(
	networthFormProcessor *networth.NetworthFormProcessor,
	userDeclarationFromProcessor *user_declaration.UserDeclarationFromProcessor,
	profileDataCollectorProcessor *profile.ProfileDataCollectionFormProcessor) *FormProcessorFactory {
	return &FormProcessorFactory{
		networthFormProcessor:         networthFormProcessor,
		userDeclarationFromProcessor:  userDeclarationFromProcessor,
		profileDataCollectorProcessor: profileDataCollectorProcessor,
	}
}

func (f *FormProcessorFactory) BuildForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	formProcessor, err := f.getFormProcessor(req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get form processor: %w", err)
	}
	return formProcessor.BuildForm(ctx, req)
}

func (f *FormProcessorFactory) SubmitForm(ctx context.Context, req *formbuilder.SubmitFormRequest) error {
	formProcessor, err := f.getFormProcessor(req.FormIdentifier)
	if err != nil {
		return fmt.Errorf("failed to get form processor: %w", err)
	}
	return formProcessor.SubmitForm(ctx, req)
}

func (f *FormProcessorFactory) BuildEmptyForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	formProcessor, err := f.getFormProcessor(req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get form processor: %w", err)
	}
	return formProcessor.BuildEmptyForm(ctx, req)
}

func (f *FormProcessorFactory) getFormProcessor(formIdentifier *typesv2.ManualAssetFormIdentifier) (formbuilder.FormProcessor, error) {
	switch formIdentifier.GetIdentifier().(type) {
	case *typesv2.ManualAssetFormIdentifier_AssetType:
		return f.networthFormProcessor, nil
	case *typesv2.ManualAssetFormIdentifier_ExternalId:
		return f.networthFormProcessor, nil
	case *typesv2.ManualAssetFormIdentifier_GenericIdentifier:
		return f.getFormProcessorForGenericIdentifier(formIdentifier.GetGenericIdentifier())
	default:
		return nil, fmt.Errorf("invalid form identifier %T", formIdentifier.GetIdentifier())
	}
}

func (f *FormProcessorFactory) getFormProcessorForGenericIdentifier(genericIdentifier string) (formbuilder.FormProcessor, error) {
	id := &networthBeFePb.ManualFormIdentifier{}
	err := protojson.Unmarshal([]byte(genericIdentifier), id)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal generic identifier: %w", err)
	}

	switch id.GetIdentifier().(type) {
	case *networthBeFePb.ManualFormIdentifier_InsightsUserDeclarationFormIdentifier:
		return f.userDeclarationFromProcessor, nil
	case *networthBeFePb.ManualFormIdentifier_ProfileDataFormIdentifier:
		return f.profileDataCollectorProcessor, nil
	default:
		return nil, fmt.Errorf("invalid generic identifier %T", id.GetIdentifier())
	}
}
