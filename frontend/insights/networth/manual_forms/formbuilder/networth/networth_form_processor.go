package networth

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifigrpc"
	goUtils "github.com/epifi/be-common/pkg/go_utils"

	"github.com/epifi/gamma/api/consent"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	"github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/utils"
)

const (
	consentHtmlText = "I agree to <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/T&C\">Fi Insights T&C</a> and processing of my personal data by Fi and its partners for providing services to me on Fi App."
	removeCtaColor  = "#AA301F"
	removeCta       = "Remove"
	deleteAssetIcon = "https://epifi-icons.pointz.in/networth/delete_icon_manual_asset.png"
)

type NetworthFormProcessor struct {
	formBuilderFactory FormBuilderFactory
	networthClient     networthBePb.NetWorthClient
	consentClient      consent.ConsentClient
	formInputValidator inputvalidator.FormInputValidator
}

func NewNetworthFormBuilder(formBuilderFactory FormBuilderFactory,
	networthClient networthBePb.NetWorthClient,
	consentClient consent.ConsentClient,
	formInputValidator inputvalidator.FormInputValidator) *NetworthFormProcessor {
	return &NetworthFormProcessor{
		formBuilderFactory: formBuilderFactory,
		networthClient:     networthClient,
		consentClient:      consentClient,
		formInputValidator: formInputValidator,
	}
}

func (b *NetworthFormProcessor) BuildForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	var (
		form *networthFePb.NetWorthManualForm
		err  error
	)
	switch req.FormIdentifier.GetIdentifier().(type) {
	case *typesv2.ManualAssetFormIdentifier_AssetType:
		form, err = b.getEmptyManualFormForAssetType(ctx, req.ActorId, req.FormIdentifier.GetAssetType())
		if err != nil {
			return nil, fmt.Errorf("failed to generate empty manual form for assetType %v: %w", req.FormIdentifier.GetAssetType(), err)
		}
	case *typesv2.ManualAssetFormIdentifier_ExternalId:
		externalId := req.FormIdentifier.GetExternalId()
		form, err = b.getUserFilledForm(ctx, req.ActorId, externalId)
		if err != nil {
			return nil, fmt.Errorf("failed to generate pre filled manual form for external_id %v: %w", externalId, err)
		}
	default:
		return nil, fmt.Errorf("unhandled identifier type : %T in request", req.FormIdentifier.GetIdentifier())
	}

	return form, nil
}

func (b *NetworthFormProcessor) getEmptyManualFormForAssetType(ctx context.Context, actorId, assetTypeStr string) (*networthFePb.NetWorthManualForm, error) {
	assetTypeEnum := goUtils.Enum(assetTypeStr, networthBePb.AssetType_value, networthBePb.AssetType_ASSET_TYPE_UNSPECIFIED)
	formBuilder, err := b.formBuilderFactory.GetFormBuilder(ctx, assetTypeEnum)
	if err != nil {
		return nil, fmt.Errorf("failed to get form builder : %w", err)
	}
	form, err := formBuilder.BuildForm(ctx, &networthBeFePb.BuildFormRequest{ActorId: actorId})
	if err != nil {
		return nil, fmt.Errorf("failed to build form for asset type : %w", err)
	}
	return form, nil
}

func (b *NetworthFormProcessor) getUserFilledForm(ctx context.Context, actorId, assetExternalId string) (*networthFePb.NetWorthManualForm, error) {
	investmentDeclResp, err := b.networthClient.GetInvestmentDeclaration(ctx, &networthBePb.GetInvestmentDeclarationRequest{
		ActorId:    actorId,
		ExternalId: assetExternalId,
	})
	if rpcErr := epifigrpc.RPCError(investmentDeclResp, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get investment decl for actor + external_id : %w", rpcErr)
	}
	instrumentType := investmentDeclResp.GetInvestmentDetails().GetInvestmentDeclaration().GetInstrumentType()
	assetType, found := utils.InstrumentTypeToAssetTypeMap[instrumentType]
	if !found {
		return nil, fmt.Errorf("failed to get asset type from instrument type : %s", instrumentType.String())
	}
	formBuilder, err := b.formBuilderFactory.GetFormBuilder(ctx, assetType)
	if err != nil {
		return nil, fmt.Errorf("failed to get form builder : %w", err)
	}
	form, err := formBuilder.BuildForm(ctx, &networthBeFePb.BuildFormRequest{
		ActorId:               actorId,
		InvestmentDeclaration: investmentDeclResp.GetInvestmentDetails().GetInvestmentDeclaration(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to build form for asset type : %w", err)
	}
	return form, nil
}

func (b *NetworthFormProcessor) SubmitForm(ctx context.Context, req *formbuilder.SubmitFormRequest) error {
	actorId := req.ActorId
	switch req.FormIdentifier.GetIdentifier().(type) {
	case *typesv2.ManualAssetFormIdentifier_AssetType:
		if err := b.submitNewForm(ctx, actorId, req.FormIdentifier.GetAssetType(), req.FormData); err != nil {
			return fmt.Errorf("failed to submit new form of %v: %w", req.FormIdentifier.GetAssetType(), err)
		}
	case *typesv2.ManualAssetFormIdentifier_ExternalId:
		externalId := req.FormIdentifier.GetExternalId()
		if externalId == "" {
			return fmt.Errorf("external id cannot be empty for external_id formIdentifier")
		}
		err := b.updateExistingForm(ctx, actorId, externalId, req.FormData)
		if err != nil {
			return fmt.Errorf("failed to update existing form with external id %s: %w", externalId, err)
		}
	default:
		return fmt.Errorf("unhandled form identifier type %T", req.FormIdentifier.GetIdentifier())
	}

	return nil
}

func (b *NetworthFormProcessor) submitNewForm(ctx context.Context, actorId string, assetTypeStr string,
	formData []*networthFePb.NetWorthManualInputData) error {
	assetType := goUtils.Enum(assetTypeStr, networthBePb.AssetType_value, networthBePb.AssetType_ASSET_TYPE_UNSPECIFIED)
	decl, err := b.getInvestmentDeclForFormData(ctx, actorId, assetType, formData)
	if err != nil {
		return fmt.Errorf("failed to gen investment decl from form_data : %w", err)
	}
	declareInvestmentResp, err := b.networthClient.DeclareInvestment(ctx, &networthBePb.DeclareInvestmentRequest{
		ActorId:            actorId,
		InstrumentType:     decl.GetInstrumentType(),
		InvestedAmount:     decl.GetInvestedAmount(),
		InvestedAt:         decl.GetInvestedAt(),
		MaturityTime:       decl.GetMaturityDate(),
		InterestRate:       decl.GetInterestRate(),
		DeclarationDetails: decl.GetDeclarationDetails(),
	})
	if rpcErr := epifigrpc.RPCError(declareInvestmentResp, err); rpcErr != nil {
		return fmt.Errorf("failed to declare investment : %w", rpcErr)
	}
	return nil
}

func (b *NetworthFormProcessor) updateExistingForm(ctx context.Context, actorId string, externalId string, formData []*networthFePb.NetWorthManualInputData) error {
	investmentDecl, err := b.getInvestmentDeclaration(ctx, actorId, externalId)
	if err != nil {
		return fmt.Errorf("failed to get investment decl for external_id %s : %w", externalId, err)
	}
	instrumentType := investmentDecl.GetInstrumentType()
	assetType, found := utils.InstrumentTypeToAssetTypeMap[instrumentType]
	if !found {
		return fmt.Errorf("instrumentType not found in instrumentToAssetType map : %s", instrumentType.String())
	}
	decl, err := b.getInvestmentDeclForFormData(ctx, actorId, assetType, formData)
	if err != nil {
		return fmt.Errorf("failed to gen investment decl from form_data : %w", err)
	}
	// set actor_id, external_id in declaration to update by (actor, external_id)
	decl.ActorId = actorId
	decl.ExternalId = externalId
	updateInvestmentDeclResp, err := b.networthClient.UpdateInvestmentDeclaration(ctx, &networthBePb.UpdateInvestmentDeclarationRequest{
		UpdatedDeclaration: decl,
		FieldMasks: []model.InvestmentDeclarationFieldMask{
			model.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT,
			model.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_MATURITY_TIME,
			model.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE,
			model.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT,
			model.InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_DECLARATION_DETAILS,
		},
	})
	if rpcErr := epifigrpc.RPCError(updateInvestmentDeclResp, err); rpcErr != nil {
		return fmt.Errorf("failed to update investment decl : %w", rpcErr)
	}
	return nil
}

func (b *NetworthFormProcessor) getInvestmentDeclaration(ctx context.Context, actorId, externalId string) (*model.InvestmentDeclaration, error) {
	investmentDeclResp, err := b.networthClient.GetInvestmentDeclaration(ctx, &networthBePb.GetInvestmentDeclarationRequest{
		ActorId:    actorId,
		ExternalId: externalId,
	})
	if rpcErr := epifigrpc.RPCError(investmentDeclResp, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get investment decl for external_id : %w", rpcErr)
	}
	return investmentDeclResp.GetInvestmentDetails().GetInvestmentDeclaration(), nil
}

func (b *NetworthFormProcessor) getInvestmentDeclForFormData(ctx context.Context, actorId string, assetType networthBePb.AssetType,
	formData []*networthFePb.NetWorthManualInputData) (*model.InvestmentDeclaration, error) {
	var formInputComponents []*networthFePb.NetWorthManualFormInputComponent
	formBuilder, err := b.formBuilderFactory.GetFormBuilder(ctx, assetType)
	if err != nil {
		return nil, fmt.Errorf("failed to get form builder for assetType %s : %w", assetType.String(), err)
	}
	form, err := formBuilder.BuildForm(ctx, &networthBeFePb.BuildFormRequest{ActorId: actorId})
	if err != nil {
		return nil, fmt.Errorf("failed to get empty form for assetType %s : %w", assetType.String(), err)
	}
	formInputComponents, err = b.validateAndSetInputDataInForm(ctx, formData, form)
	if err != nil {
		return nil, fmt.Errorf("failure in validate and set input data : %w", err)
	}
	decl, err := formBuilder.ConvertFormInputToInvestmentDeclaration(ctx, formInputComponents)
	if err != nil {
		return nil, fmt.Errorf("failed to convert formInputComponents to investment decl : %w", err)
	}
	return decl, nil
}

func (b *NetworthFormProcessor) validateAndSetInputDataInForm(ctx context.Context, formData []*networthFePb.NetWorthManualInputData, form *networthFePb.NetWorthManualForm) ([]*networthFePb.NetWorthManualFormInputComponent, error) {
	allInputComponents := form.GetAllInputComponents()
	// note that validate input data also sets the input data into formInputComponents
	validateErr := b.formInputValidator.ValidateInputData(ctx, formData, allInputComponents)
	if validateErr != nil {
		return nil, fmt.Errorf("failure in validating input data : %w", validateErr)
	}
	return allInputComponents, nil
}

func (b *NetworthFormProcessor) getAssetTypeForForm(ctx context.Context, actorId string, formIdentifier *typesv2.ManualAssetFormIdentifier) (networthBePb.AssetType, error) {
	switch formIdentifier.GetIdentifier().(type) {
	case *typesv2.ManualAssetFormIdentifier_AssetType:
		return goUtils.Enum(formIdentifier.GetAssetType(), networthBePb.AssetType_value, networthBePb.AssetType_ASSET_TYPE_UNSPECIFIED), nil
	case *typesv2.ManualAssetFormIdentifier_ExternalId:
		investmentDecl, err := b.getInvestmentDeclaration(ctx, actorId, formIdentifier.GetExternalId())
		if err != nil {
			return 0, fmt.Errorf("failed to get investment decl for external_id %s : %w", formIdentifier.GetExternalId(), err)
		}
		assetType, found := utils.InstrumentTypeToAssetTypeMap[investmentDecl.GetInstrumentType()]
		if !found {
			return 0, fmt.Errorf("instrumentType not found in instrumentToAssetType map %v", investmentDecl.GetInstrumentType())
		}
		return assetType, nil
	default:
		return 0, fmt.Errorf("unhandled form identifier type %T", formIdentifier.GetIdentifier())
	}
}

func (b *NetworthFormProcessor) BuildEmptyForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	assetType, err := b.getAssetTypeForForm(ctx, req.ActorId, req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get asset type for form : %w", err)
	}
	form, err := b.getEmptyManualFormForAssetType(ctx, req.ActorId, assetType.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get empty form for asset type %s : %w", assetType.String(), err)
	}
	return form, nil
}
