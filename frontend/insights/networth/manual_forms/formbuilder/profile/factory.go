package profile

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
)

type ProfileDataCollectionFormProcessor struct {
	panFormProcessor *PanFormProcessor
	dobFromProcessor *DobFormProcessor
}

func NewProfileDataCollectionFormProcessor(
	panFormProcessor *PanFormProcessor,
	dobFromProcessor *DobFormProcessor) *ProfileDataCollectionFormProcessor {
	return &ProfileDataCollectionFormProcessor{
		panFormProcessor: panFormProcessor,
		dobFromProcessor: dobFromProcessor,
	}
}

func (p *ProfileDataCollectionFormProcessor) BuildForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	formProcessor, err := p.getFromProcessor(req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get form processor: %w", err)
	}
	return formProcessor.BuildForm(ctx, req)
}

func (p *ProfileDataCollectionFormProcessor) SubmitForm(ctx context.Context, req *formbuilder.SubmitFormRequest) error {
	formProcessor, err := p.getFromProcessor(req.FormIdentifier)
	if err != nil {
		return fmt.Errorf("failed to get form processor: %w", err)
	}
	return formProcessor.SubmitForm(ctx, req)
}

func (p *ProfileDataCollectionFormProcessor) BuildEmptyForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	formProcessor, err := p.getFromProcessor(req.FormIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get form processor: %w", err)
	}
	return formProcessor.BuildEmptyForm(ctx, req)
}

func (p *ProfileDataCollectionFormProcessor) getFromProcessor(formIdentifier *typesv2.ManualAssetFormIdentifier) (formbuilder.FormProcessor, error) {
	_, ok := formIdentifier.GetIdentifier().(*typesv2.ManualAssetFormIdentifier_GenericIdentifier)
	if !ok {
		return nil, fmt.Errorf("invalid form identifier %T", formIdentifier.GetIdentifier())
	}

	id := &networthBeFePb.ManualFormIdentifier{}
	err := protojson.Unmarshal([]byte(formIdentifier.GetGenericIdentifier()), id)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal external id: %w", err)
	}

	_, ok = id.GetIdentifier().(*networthBeFePb.ManualFormIdentifier_ProfileDataFormIdentifier)
	if !ok {
		return nil, fmt.Errorf("invalid form identifier of type %T", id.GetIdentifier())
	}

	switch id.GetProfileDataFormIdentifier().GetType() {
	case networthBeFePb.ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN:
		return p.panFormProcessor, nil
	case networthBeFePb.ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_DOB:
		return p.dobFromProcessor, nil
	default:
		return nil, fmt.Errorf("failed to get form processor for profile data from identifier type %v", id.GetProfileDataFormIdentifier().GetType())
	}
}
