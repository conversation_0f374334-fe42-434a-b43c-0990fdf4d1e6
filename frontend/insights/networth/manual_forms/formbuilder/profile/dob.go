package profile

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputbuilder"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
)

const UnverifiedDobFormField = "UnverifiedDobFormField"

type DobFormProcessor struct {
	dobProcessor insightsPkg.IDobProcessor
}

func NewDobFormProcessor(dobProcessor insightsPkg.IDobProcessor) *DobFormProcessor {
	return &DobFormProcessor{dobProcessor: dobProcessor}
}

func (d *DobFormProcessor) BuildForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	dobFieldBuilder := d.getDobFieldWithValidation()

	dob, err := d.dobProcessor.GetVerifiedOrUnverifiedDoB(ctx, req.ActorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to get unverified dob: %w", err)
	}
	if dob != nil {
		if dob.IsVerified {
			return nil, fmt.Errorf("dob is already verified")
		}
		if dob.DoB != nil {
			dobFieldBuilder.WithValue(dob.DoB)
		}
	}

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("Date of Birth").
		WithInputComponent(dobFieldBuilder.Build())
	return networthFePb.NewNetWorthManualForm("Date of Birth", "Submit").
		WithComponentsSection(inputSection), nil
}

func (d *DobFormProcessor) getDobFieldWithValidation() *inputbuilder.DateBuilder {
	dobFieldBuilder := inputbuilder.NewDateBuilder("DATE OF BIRTH", "Date of Birth", UnverifiedDobFormField)
	// ToDo: Add date range validation once it is supported

	return dobFieldBuilder
}

func (d *DobFormProcessor) SubmitForm(ctx context.Context, req *formbuilder.SubmitFormRequest) error {
	if len(req.InputComponents) != 1 {
		return fmt.Errorf("expected 1 input component, got %d", len(req.InputComponents))
	}

	dob := req.InputComponents[0].GetInputData().GetInputValueFromSingleOption().GetDateData().GetData()

	if err := d.dobProcessor.StoreUnverifiedDoB(ctx, req.ActorId, dob); err != nil {
		return fmt.Errorf("failed to store unverified dob: %w", err)
	}

	return nil
}

func (d *DobFormProcessor) BuildEmptyForm(_ context.Context, _ *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	dobFieldBuilder := d.getDobFieldWithValidation()

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("Date of Birth").
		WithInputComponent(dobFieldBuilder.Build())
	return networthFePb.NewNetWorthManualForm("Date of Birth", "Submit").
		WithComponentsSection(inputSection), nil
}
