package profile

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputbuilder"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
)

const UnverifiedPanFormField = "UnverifiedPanFormField"

type PanFormProcessor struct {
	panProcessor insightsPkg.IPanProcessor
}

func NewPanFormProcessor(panProcessor insightsPkg.IPanProcessor) *PanFormProcessor {
	return &PanFormProcessor{panProcessor: panProcessor}
}

func (p *PanFormProcessor) BuildForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	panFieldBuilder := p.getPanFieldWithValidation()

	pan, err := p.panProcessor.GetVerifiedOrUnverifiedPan(ctx, req.ActorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to get unverified pan: %w", err)
	}
	if pan != nil {
		if pan.IsVerified {
			return nil, fmt.Errorf("pan is already verified")
		}
		if pan.Pan != "" {
			panFieldBuilder.WithValue(pan.Pan)
		}
	}

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("PAN").
		WithInputComponent(panFieldBuilder.Build())
	return networthFePb.NewNetWorthManualForm("PAN Details", "Submit").
		WithComponentsSection(inputSection), nil
}

func (p *PanFormProcessor) getPanFieldWithValidation() *inputbuilder.StringBuilder {
	panFieldBuilder := inputbuilder.NewStringBuilder("PAN", "Pan", UnverifiedPanFormField)
	panFieldBuilder.WithValidation(&networthFePb.StringType_StringValidation{
		MinLen:               10,
		MaxLen:               10,
		Regex:                "^([a-zA-Z]){3}[p|P][a-zA-Z]([0-9]){4}([a-zA-Z])$",
		ValidationFailureMsg: "Invalid PAN",
	})

	return panFieldBuilder
}

func (p *PanFormProcessor) SubmitForm(ctx context.Context, req *formbuilder.SubmitFormRequest) error {
	if len(req.InputComponents) != 1 {
		return fmt.Errorf("expected 1 input component, got %d", len(req.InputComponents))
	}

	pan := req.InputComponents[0].GetInputData().GetInputValueFromSingleOption().GetStringData().GetData().GetValue()

	if err := p.panProcessor.StoreUnverifiedPan(ctx, req.ActorId, pan, userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_WEALTH_BUILDER_DATA); err != nil {
		return fmt.Errorf("failed to store unverified pan: %w", err)
	}

	return nil
}

func (p *PanFormProcessor) BuildEmptyForm(ctx context.Context, req *formbuilder.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	panFieldBuilder := p.getPanFieldWithValidation()

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("PAN").
		WithInputComponent(panFieldBuilder.Build())
	return networthFePb.NewNetWorthManualForm("PAN Details", "Submit").
		WithComponentsSection(inputSection), nil
}
