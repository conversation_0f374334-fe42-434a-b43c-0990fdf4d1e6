package valuegenerator

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/epifi/be-common/pkg/datetime"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"

	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	investementUiPb "github.com/epifi/gamma/api/frontend/investment/ui"
	secretsConfigPb "github.com/epifi/gamma/api/insights/secrets/config"
	secretsModelPb "github.com/epifi/gamma/api/insights/secrets/model"
	"github.com/epifi/gamma/frontend/insights/secrets/colors"
	"github.com/epifi/gamma/frontend/insights/secrets/epf/store"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
	"github.com/epifi/gamma/frontend/insights/secrets/valuegenerator"
)

var (
	DonutSlicesColors = []string{colors.ColorMoss700, colors.ColorSupportingOcean700, colors.ColorSupportingAmber900, colors.ColorSlate, colors.ColorSupportingHoney700, colors.ColorDarkBase, colors.ColorSupportingAmber400, colors.ColorSupportingHoney50, colors.ColorSupportingAmber700, colors.ColorSupportingAmber200, colors.ColorSupportingAmber400Opacity60, colors.ColorSupportingMoss200, colors.ColorSupportingMoss400Opacity60, colors.ColorSupportingMoss900, colors.ColorSupportingMoss700, colors.ColorSnow, colors.ColorSupportingMoss50, colors.ColorOnDarkHighEmphasis, colors.ColorSupportingAmber50}
)

type EpfContributorsDataProvider struct {
	*BaseEpfDimensionDataProvider
	employerContributions []*Contributor
	employeeContribution  *Employee
}

func NewEpfContributorsDataProvider(secret *secretsModelPb.Secret, timeImpl datetime.Time) *EpfContributorsDataProvider {
	return &EpfContributorsDataProvider{
		BaseEpfDimensionDataProvider: NewBaseEpfDimensionDataProvider(secret, timeImpl),
		employerContributions:        make([]*Contributor, 0),
	}
}

func (e *EpfContributorsDataProvider) InitDataProvider(ctx context.Context, req *InitDataProviderRequest) error {
	employerContributions, employeeContribution, err := e.getAggregateContributions(req.EpfStore)
	if err != nil {
		return fmt.Errorf("failed to get passbooks: %w", err)
	}
	e.employerContributions = employerContributions
	e.employeeContribution = employeeContribution
	return nil
}

type Contributor struct {
	EntityName string
	Amount     *moneyPb.Money
	valuegenerator.UnimplementedDeeplinkForBar
	Percentage     float64
	Color          string
	LineItemConfig *secretsConfigPb.LineItemConfig
}

func (c *Contributor) GetRightHeading() string {
	return money.GetDisplayString(c.Amount, 2, false, true, money.IndianNumberSystem)
}

func (c *Contributor) GetComparisonValue() float64 {
	return float64(c.Amount.GetUnits())
}

func (c *Contributor) GetLeftHeading() string {
	return c.EntityName
}

func (c *Contributor) GetIconUrl() string {
	return "https://epifi-icons.pointz.in/insights/secrets/office_building_medium_moss.png"
}

func (c *Contributor) GetTopValue() string {
	x := money.ToDisplayStringWithSuffixAndPrecisionV2(c.Amount, true, true, 0, false, money.IndianNumberSystem)
	return x
}

func (c *Contributor) GetBarColor() string {
	return ""
}

func (c *Contributor) GetBottomIconUrl() string {
	return c.GetIconUrl()
}

func (c *Contributor) GetBarValue() float64 {
	return c.GetComparisonValue()
}

func (c *Contributor) GetSliceValue() float64 {
	return c.Percentage
}

func (c *Contributor) GetToolTipTexts() (string, string) {
	return c.EntityName, fmt.Sprintf("%s%%", decimal.NewFromFloat(c.Percentage).Round(2).String())
}

func (c *Contributor) GetDonutColor() string {
	return c.Color
}

//nolint:dupl
func (c *Contributor) GetCustomLineItem() *investementUiPb.LineItem {
	return secretsFePb.NewLineItemBuilder(c.LineItemConfig).
		SetSolidColorForLeftIcon(c.GetDonutColor()).
		SetLeftHeading(c.GetLeftHeading()).
		SetRightHeading(c.GetRightHeading()).Build()
}

type Employee struct {
	*Contributor
	IconUrl string
}

func (e *Employee) GetIconUrl() string {
	return e.IconUrl
}

func (e *Employee) GetBottomIconUrl() string {
	return e.IconUrl
}

func (c *EpfContributorsDataProvider) GetLineItemsData(ctx context.Context, req *GetLineItemsDataRequest) ([]LineItemData, error) {
	employerContributions := c.employerContributions
	for i, _ := range employerContributions {
		employerContributions[i].LineItemConfig = req.LineItemConfig
	}
	contributions := lo.Map(employerContributions, func(item *Contributor, _ int) LineItemData {
		return item
	})

	return contributions, nil
}

type EpfContributorsBarCharData struct {
	largestEmployerContributor  string
	largestEmployerContribution *moneyPb.Money
	barChartItems               []valuegenerator.BarChartItemData
}

func (e *EpfContributorsBarCharData) GetBenchmarkValue() float64 {
	return math.MaxFloat64
}

func (e *EpfContributorsBarCharData) GetPrimaryValue() string {
	return e.largestEmployerContributor
}

func (e *EpfContributorsBarCharData) GetSecondaryValue() string {
	return money.GetDisplayString(e.largestEmployerContribution, 0, false, true, money.IndianNumberSystem)
}

func (e *EpfContributorsBarCharData) GetBarChartItemsData() []valuegenerator.BarChartItemData {
	return e.barChartItems
}

func (c *EpfContributorsDataProvider) GetBarChartData(ctx context.Context, req *GetBarChartItemsDataRequest) (valuegenerator.BarChartData, error) {
	employerContributions, employeeContribution := c.employerContributions, c.employeeContribution
	if len(employerContributions) == 0 {
		return nil, fmt.Errorf("no employer contributions found")
	}

	largestEmployerContributor, largestEmployerContribution := employerContributions[0].EntityName, employerContributions[0].Amount

	barsData := lo.Map(employerContributions, func(item *Contributor, _ int) valuegenerator.BarChartItemData {
		return item
	})

	barsData = append(barsData, employeeContribution)

	return &EpfContributorsBarCharData{
		largestEmployerContributor:  largestEmployerContributor,
		largestEmployerContribution: largestEmployerContribution,
		barChartItems:               barsData,
	}, nil
}

func (c *EpfContributorsDataProvider) GetSummaryData(ctx context.Context, req *GetSummaryDataRequest) (string, error) {
	employerContributions := c.employerContributions
	if len(employerContributions) == 0 {
		return "", secretErrors.NoDataToBuildSecretPostFilters
	}

	return employerContributions[0].EntityName, nil
}

type EpfContributorsDonutCardData struct {
	primaryTitle    string
	secondaryData   string
	donutCardSlices []valuegenerator.DonutCardSlice
}

func (e *EpfContributorsDonutCardData) GetPrimaryValue() string {
	return e.primaryTitle
}

func (e *EpfContributorsDonutCardData) GetSecondaryValue() string {
	return e.secondaryData
}

func (e *EpfContributorsDonutCardData) GetDonutCardSlices() []valuegenerator.DonutCardSlice {
	return e.donutCardSlices
}

func (c *EpfContributorsDataProvider) GetDonutCardData(ctx context.Context, req *GetDonutCardDataRequest) (valuegenerator.DonutCardData, error) {
	employerContributions := c.employerContributions
	if len(employerContributions) == 0 {
		return nil, fmt.Errorf("no employer contributions found")
	}
	largestEmployerContributor := employerContributions[0].EntityName
	donutSlicesData := lo.Map(employerContributions, func(entity *Contributor, _ int) valuegenerator.DonutCardSlice {
		return entity
	})
	return &EpfContributorsDonutCardData{
		secondaryData:   largestEmployerContributor,
		donutCardSlices: donutSlicesData,
	}, nil
}

func (c *EpfContributorsDataProvider) getAggregateContributions(s *store.EpfStore) (employerContributions []*Contributor, employeeContribution *Employee, err error) {
	passbooks, err := s.GetPassbooks()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get passbooks: %w", err)
	}

	totalEmployerContribution := money.ZeroINR().GetPb()
	employeeCont := money.ZeroINR().GetPb()
	for _, passbook := range passbooks {
		contributions, err := passbook.GetTotalContributionsInPassbook()
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get total contributions in passbook: %w", err)
		}
		employerContributions = append(employerContributions, &Contributor{
			EntityName: passbook.GetCompanyDisplayName(),
			Amount:     contributions.EmployerContribution,
		})
		employeeCont, err = money.Sum(employeeCont, contributions.EmployeeContribution)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to sum employee contribution: %w", err)
		}
		totalEmployerContribution, err = money.Sum(totalEmployerContribution, contributions.EmployerContribution)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to sum employer contribution: %w", err)
		}
	}

	if money.IsZero(totalEmployerContribution) {
		return nil, nil, secretErrors.NoDataToBuildSecretPostFilters
	}

	for i, employerContribution := range employerContributions {
		employerContribution.Percentage = (float64(employerContribution.Amount.GetUnits()) / float64(totalEmployerContribution.GetUnits())) * 100
		i %= len(DonutSlicesColors)
		employerContribution.Color = DonutSlicesColors[i]
	}

	// sorting employer contributions to amount in descending order
	sort.Slice(employerContributions, func(i, j int) bool {
		return employerContributions[i].Amount.GetUnits() > employerContributions[j].Amount.GetUnits()
	})

	if len(s.UanAccounts) == 0 {
		return nil, nil, fmt.Errorf("no uan accounts found")
	}
	employeeName := "Self"
	if memberName := s.UanAccounts[0].GetRawDetails().GetEmployeeDetails().GetMemberName(); memberName != "" {
		employeeName = fmt.Sprintf("%s (Self)", cases.Title(language.English).String(memberName))
	}
	employeeContribution = &Employee{
		Contributor: &Contributor{
			EntityName: employeeName,
			Amount:     employeeCont,
		},
		IconUrl: "https://epifi-icons.pointz.in/epf/epf_default_firm.png",
	}
	return employerContributions, employeeContribution, nil
}
