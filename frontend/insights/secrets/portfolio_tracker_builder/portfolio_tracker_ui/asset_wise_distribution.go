package portfoliotrackerui

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/epifi/be-common/pkg/logger"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	beNetworthPb "github.com/epifi/gamma/api/insights/networth"
	assetWiseDistributionBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui/asset_wise_distribution_builder"
)

type AssetWiseDistribution struct {
	assetWiseDistributionFactory assetWiseDistributionBuilder.IAssetWiseDistributionFactory
}

func NewAssetWiseDistribution(assetWiseDistributionFactory assetWiseDistributionBuilder.IAssetWiseDistributionFactory) *AssetWiseDistribution {
	return &AssetWiseDistribution{
		assetWiseDistributionFactory: assetWiseDistributionFactory,
	}
}

type FilterTag struct {
	AssetType   beNetworthPb.AssetType
	DisplayName string
	IsSelected  bool
	IsDisabled  bool
}

var assetWiseDistributionFilterTags = []FilterTag{
	{
		AssetType:   beNetworthPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
		DisplayName: "Mutual Funds",
		IsSelected:  true, // only one filter can be selected
		IsDisabled:  false,
	},
	{
		AssetType:   beNetworthPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
		DisplayName: "Indian Stocks",
		IsSelected:  false, // priotising Mutual Funds, so Indian Stocks is not selected by default
		IsDisabled:  false,
	},
}

func (t *AssetWiseDistribution) BuildPortfolioTrackerComponent(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) (*strategy.BuildPortfolioTrackerComponentResponse, error) {
	res := &strategy.BuildPortfolioTrackerComponentResponse{}
	assetWiseDistribution := secretsFePb.NewAssetDetailsComponentBuilder().
		SetComponentType(req.ComponentType.String()).
		SetTitle("Asset-wise distribution", "")
	for _, assetWiseDistributionTag := range assetWiseDistributionFilterTags {
		filterTagBuilder := secretsFePb.NewAssetFilterTagBuilder().
			SetAssetType(assetWiseDistributionTag.AssetType.String()).
			SetSelectedAndUnSelectedTag(assetWiseDistributionTag.DisplayName).
			SetIsDisabled(assetWiseDistributionTag.IsDisabled)
		if assetWiseDistributionTag.IsSelected {
			filterTagBuilder = filterTagBuilder.MarkAsSelected()
		}

		assetDistributionBuilder, err := t.assetWiseDistributionFactory.GetAssetWiseDistributionBuilder(assetWiseDistributionTag.AssetType)
		if err != nil {
			logger.Error(ctx, "failed to get asset wise distribution builder", zap.Any(logger.ASSET_TYPE, assetWiseDistributionTag.AssetType), zap.Error(err))
			continue
		}
		assetWiseDistributionSummary, err := assetDistributionBuilder.BuildAssetWiseDistribution(ctx, &strategy.BuildAssetWiseDistributionRequest{
			ActorId:             req.ActorId,
			AssetType:           assetWiseDistributionTag.AssetType,
			AnalysisVariableMap: req.AnalysisVariableMap,
			LineItemCount:       5,
			TrackingStrategy:    req.TrackingStrategy,
		})
		if err != nil {
			logger.Error(ctx, "failed to build asset wise distribution for asset", zap.Any(logger.ASSET_TYPE, assetWiseDistributionTag.AssetType), zap.Error(err))
			continue
		}
		// can be nil if we cannot build due to insufficient data
		// ignoring it gracefully will make sure other assets load
		if assetWiseDistributionSummary == nil {
			logger.WarnWithCtx(ctx, "failed to build asset wise distribution for asset due to insufficient data", zap.Any(logger.ASSET_TYPE, assetWiseDistributionTag.AssetType))
			continue
		}

		assetWiseDistribution = assetWiseDistribution.AddFilterTag(filterTagBuilder.Build()).
			AddDetailCard(assetWiseDistributionSummary.AssetType.String(), assetWiseDistributionSummary.PortfolioTrackerAssetDetailsCard)
	}

	navigationToggle := secretsFePb.NewNavigationToggleBuilder().
		SetComponentType(req.ComponentType.String()).
		SetSelectedAndUnSelectedToggleIcon(&secretsFePb.ToggleIconParams{
			SelectedIcon:   "https://epifi-icons.pointz.in/secrets/pie-chart-selected.png",
			UnselectedIcon: "https://epifi-icons.pointz.in/secrets/pie-chart.png",
			Text:           "Asset Distribution",
		}).
		Build()

	res.PortfolioTrackerComponent = &secretsFePb.PortfolioTrackerComponent{
		Component: &secretsFePb.PortfolioTrackerComponent_AssetDetailsComponent{
			AssetDetailsComponent: assetWiseDistribution.Build(),
		},
	}
	res.ComponentType = req.ComponentType
	res.NavigationToggle = navigationToggle
	return res, nil
}
