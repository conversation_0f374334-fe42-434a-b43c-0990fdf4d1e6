package assetwisedistributionbuilder

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"golang.org/x/exp/slices"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/money"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2"
	secretsScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	secretsRequestParams "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets/portfoliotracker"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"

	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	feInvestmentPb "github.com/epifi/gamma/api/frontend/investment/ui"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	beEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
)

func defaultMetadata() *analyserVariablePb.SecurityMetadata {
	return &analyserVariablePb.SecurityMetadata{
		LogoUrl:      "https://epifi-icons.pointz.in/bank/logo/default_logo.png",
		SecurityName: "Unknown",
	}
}

type IndianStocksDistribution struct {
	releaseEvaluator release.IEvaluator
}

func NewIndianStocksDistribution(
	releaseEvaluator release.IEvaluator,
) *IndianStocksDistribution {
	return &IndianStocksDistribution{
		releaseEvaluator: releaseEvaluator,
	}
}

func (isd *IndianStocksDistribution) BuildAssetWiseDistribution(ctx context.Context, req *strategy.BuildAssetWiseDistributionRequest) (*strategy.BuildAssetWiseDistributionResponse, error) {
	isEnabled, evaluatorErr := isd.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesv2.Feature_FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION).WithActorId(req.ActorId))
	if evaluatorErr != nil {
		return nil, fmt.Errorf("evaluate failed: %w", evaluatorErr)
	}
	if !isEnabled {
		return nil, nil
	}
	assetDayChangeVariable, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION)
	}
	if assetDayChangeVariable.GetAnalysisVariableState() != analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE {
		return nil, nil
	}

	sortedStocks := assetDayChangeVariable.GetIndianStocksDistribution().GetDayChangeResponse().GetAssetsValueChange()
	slices.SortFunc(sortedStocks, func(a, b *networthBePb.AssetValueChange) int {
		if a.GetChange() < b.GetChange() {
			return 1
		}
		return 0
	})

	portfolioTrackerAssetDetailsCardBuilder := secretsFePb.NewPortfolioTrackerAssetDetailsCardBuilder().
		SetAssetType(networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES.String()).
		SetAssetCountText(fmt.Sprintf("%d Stocks", len(sortedStocks))).
		SetBgColour(widget.GetBlockBackgroundColour(colors.ColorGreyV2))

	if req.LineItemCount != 0 && len(sortedStocks) > req.LineItemCount {
		portfolioTrackerAssetDetailsCardBuilder = portfolioTrackerAssetDetailsCardBuilder.SetViewMoreCta("View More", &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&secretsScreenOptions.AssetDistributionPageScreenOptions{
				RequestParams: &secretsRequestParams.AssetDetailsPageRequestParams{
					AssetType: req.AssetType.String(),
				},
			}),
		})
	}

	securityMetadataMap := assetDayChangeVariable.GetIndianStocksDistribution().GetSecurityMetadataMap()
	indianStocksDayChangeResp := assetDayChangeVariable.GetIndianStocksDistribution().GetDayChangeResponse()
	aggregatedDailyChangeValue := indianStocksDayChangeResp.GetTotalChange()

	lineItemCount := req.LineItemCount
	if lineItemCount == 0 || lineItemCount > len(sortedStocks) {
		lineItemCount = len(sortedStocks)
	}

	for _, stockChange := range sortedStocks[:lineItemCount] {
		var metadata *analyserVariablePb.SecurityMetadata
		if metadata_, ok := securityMetadataMap[stockChange.AssetId]; ok {
			metadata = metadata_
		} else {
			metadata = defaultMetadata()
		}
		// For now, using AssetId as name and a default icon.
		defaultLineItem := feInvestmentPb.NewLineItem().
			WithLeftIconProperties(metadata.GetLogoUrl(), 32, 32, commontypes.ImageType_PNG).
			WithLeftHeading(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(metadata.GetSecurityName(), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S)))

		dailyChangeValue := stockChange.GetChange()
		// Assuming FinalDateValue represents current value and InitialDateValue represents invested value for stocks.
		// This might need adjustment based on actual data meaning.
		currentValue := stockChange.GetFinalDateValue()
		investedValue := stockChange.GetInitialDateValue()
		returnsValue := money.ZeroINR().GetPb()
		// since we are doing intersection of stocks on available on both dates this shouldn't happen ideally
		if investedValue == nil || money.ToFloat(investedValue) == 0 || currentValue == nil || money.ToFloat(currentValue) == 0 {
			return nil, fmt.Errorf("invested value cannot be 0, stock_id: %s", stockChange.GetAssetId())
		}
		sum, err := money.Subtract(currentValue, investedValue)
		if err == nil {
			returnsValue = sum
		}
		dailyChangePercentage := (dailyChangeValue / money.ToFloat(investedValue)) * 100
		returnsPercentage := (money.ToFloat(returnsValue) / money.ToFloat(investedValue)) * 100

		portfolioTrackerLineItem := secretsFePb.NewPortfolioTrackerLineItemBuilder().
			SetLineItem(defaultLineItem).
			AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_DAILY_CHANGE.String(),
				feInvestmentPb.NewLineItemHeadingTag().WithHeading(getRightHeadingTruncatedMoney(money.ParseFloat(dailyChangeValue, "INR"))).
					AddTag(feInvestmentPb.NewTags().WithTag(ui.NewITC().WithTexts(getRightTagPercentage(dailyChangePercentage)...)))).
			AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_RETURNS.String(),
				feInvestmentPb.NewLineItemHeadingTag().WithHeading(getRightHeadingTruncatedMoney(returnsValue)).
					AddTag(feInvestmentPb.NewTags().WithTag(ui.NewITC().WithTexts(getRightTagPercentage(returnsPercentage)...)))).
			AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_VALUE.String(),
				feInvestmentPb.NewLineItemHeadingTag().WithHeading(getRightHeadingTruncatedMoney(currentValue)).
					AddTag(feInvestmentPb.NewTags().WithTag(ui.NewITC().WithTexts(getRightTagMoney(investedValue)...)))).
			Build()

		portfolioTrackerAssetDetailsCardBuilder.AddLineItem(portfolioTrackerLineItem)
	}

	portfolioTrackerAssetDetailsCardBuilder = setStockToggleValues(portfolioTrackerAssetDetailsCardBuilder, indianStocksDayChangeResp, aggregatedDailyChangeValue)

	return &strategy.BuildAssetWiseDistributionResponse{
		AssetType:                        req.AssetType,
		PortfolioTrackerAssetDetailsCard: portfolioTrackerAssetDetailsCardBuilder.Build(),
	}, nil
}

func setStockToggleValues(
	builder *secretsFePb.PortfolioTrackerAssetDetailsCardBuilder,
	stockResp *networthBePb.AssetTypeDayChangeResponse,
	aggregatedDailyChangeValue float64,
) *secretsFePb.PortfolioTrackerAssetDetailsCardBuilder {
	currentPortfolioValue := stockResp.GetFinalDateTotalValue()
	investedPortfolioValue := stockResp.GetInitialDateTotalValue()

	dailyChangePercentage := 0.0
	if investedPortfolioValue != nil && money.ToFloat(investedPortfolioValue) != 0 {
		dailyChangePercentage = (aggregatedDailyChangeValue / money.ToFloat(investedPortfolioValue)) * 100
	}

	totalReturnsValue := money.ZeroINR().GetPb()
	if currentPortfolioValue != nil && investedPortfolioValue != nil {
		sum, err := money.Subtract(currentPortfolioValue, investedPortfolioValue)
		if err == nil {
			totalReturnsValue = sum
		}
	}

	returnsPercentage := 0.0
	if investedPortfolioValue != nil && money.ToFloat(investedPortfolioValue) != 0 && totalReturnsValue != nil {
		returnsPercentage = (money.ToFloat(totalReturnsValue) / money.ToFloat(investedPortfolioValue)) * 100
	}

	builder.
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_DAILY_CHANGE.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getToggleHeadlineForMoneyAndPercentage(money.ParseFloat(aggregatedDailyChangeValue, "INR"), dailyChangePercentage)...)).
			SetToggleText("Day Change (%)").Build()).
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_RETURNS.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getToggleHeadlineForMoneyAndPercentage(totalReturnsValue, returnsPercentage)...)).
			SetToggleText("Returns (%)").Build()).
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_VALUE.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getToggleHeadlineForCurrentAndInvestedAmounts(currentPortfolioValue, investedPortfolioValue)...)).
			SetToggleText("Current (Invested)").Build())
	return builder
}
