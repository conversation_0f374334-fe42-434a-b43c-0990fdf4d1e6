package portfoliotrackerui

import (
	"context"
	"fmt"
	"strings"

	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/mf"
	investmentPkg "github.com/epifi/gamma/pkg/investment"

	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/frontend/insights/networth/common"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/integer"
	moneyPb "github.com/epifi/be-common/pkg/money"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	typesV2Pb "github.com/epifi/gamma/api/typesv2"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
)

type PortfolioSummary struct {
	gconf         *genconf.Config
	dailyTracker  *strategyImpl.DailyTracker
	weeklyTracker *strategyImpl.WeeklyTracker
}

func NewPortfolioSummary(
	gconf *genconf.Config,
	dailyTracker *strategyImpl.DailyTracker,
	weeklyTracker *strategyImpl.WeeklyTracker,
) *PortfolioSummary {
	return &PortfolioSummary{
		gconf:         gconf,
		dailyTracker:  dailyTracker,
		weeklyTracker: weeklyTracker,
	}
}

const (
	redImageUrl                = "https://epifi-icons.pointz.in/portfolio_summary/red_info"
	greenImageUrl              = "https://epifi-icons.pointz.in/portfolio_summary/right_image"
	TopTextTagsBgColor         = "#1FD6D9DD"
	LightGreyLinearGradient    = "#4A4A4A"
	DarkGreyLinearGradient     = "#242527"
	percentPositiveChangeColor = "#AFD2A2"
	percentNegativeChangeColor = "#F1CE9B"
	valueNegativeChangeColor   = "#E8AD62"
	valuePositiveChangeColor   = "#86BA6F"
	BorderColour               = "#BF57595D"
	InfoText                   = "An overview of how your investments – Mutual Funds, Stocks, EPFs, Deposits etc – performed yesterday. Connect more assets to get the most accurate data. Bank balances and transactions are not included."
)

func (t *PortfolioSummary) BuildPortfolioTrackerComponent(ctx context.Context, req *strategyImpl.BuildPortfolioTrackerComponentRequest) (*strategyImpl.BuildPortfolioTrackerComponentResponse, error) {
	summaryStrategyConfig, err := req.TrackingStrategy.GetSummaryComponentConfig(ctx, req)
	if err != nil {
		return nil, err
	}

	summaryComponentBuilder := secretsFePb.NewPortfolioSummaryComponentBuilder()
	summaryComponentBuilder.SetComponentType(secretFeEnums.PortfolioTrackerComponentType_name[int32(req.ComponentType)])
	summaryComponentBuilder.SetTitle("Summary", InfoText)
	summaryComponentBuilder.AddSummaryCard(t.buildSummaryCard(summaryStrategyConfig))

	navigationToggle := secretsFePb.NewNavigationToggleBuilder().
		SetComponentType(req.ComponentType.String()).
		MarkToggleSelected().
		SetSelectedAndUnSelectedToggleIcon(&secretsFePb.ToggleIconParams{
			SelectedIcon:   "https://epifi-icons.pointz.in/secrets/clipboard-selected.png",
			UnselectedIcon: "https://epifi-icons.pointz.in/secrets/daily-networth-summary.png",
			Text:           "Summary",
		}).
		Build()

	summaryComponent := summaryComponentBuilder.Build()

	return &strategyImpl.BuildPortfolioTrackerComponentResponse{
		ComponentType:    req.ComponentType,
		NavigationToggle: navigationToggle,
		PortfolioTrackerComponent: &secretsFePb.PortfolioTrackerComponent{
			Component: &secretsFePb.PortfolioTrackerComponent_PortfolioSummaryComponent{
				PortfolioSummaryComponent: summaryComponent,
			},
		},
	}, nil
}

func (t *PortfolioSummary) buildSummaryCard(summaryStrategyConfig *strategyImpl.SummaryComponentConfig) *secretsFePb.PortfolioSummaryComponent_SummaryCard {
	var totalCurrentValue, totalPreviousValue, totalDailyChange float64
	totalCurrentValue = summaryStrategyConfig.TotalCurrentValue
	totalPreviousValue = summaryStrategyConfig.TotalPreviousValue
	totalDailyChange = summaryStrategyConfig.TotalChange

	percentageChange := float64(0)
	if totalPreviousValue != 0 {
		percentageChange = (totalDailyChange / totalPreviousValue) * 100
	}

	changeSign := ""
	percentColorType := percentNegativeChangeColor
	valueColorType := valueNegativeChangeColor
	trendImage := redImageUrl
	if percentageChange >= 0 {
		changeSign = "+"
		percentColorType = percentPositiveChangeColor
		valueColorType = valuePositiveChangeColor
		trendImage = greenImageUrl
	}
	percentageChangeText := fmt.Sprintf("%s%.2f%%", changeSign, percentageChange)
	computedCurrentValue := moneyPb.ToDisplayStringWithSuffixAndPrecisionV2(
		mf.FloatToMoney(totalCurrentValue), false, true, 2, false, moneyPb.IndianNumberSystem,
	)
	computedPortfolioChangeText := moneyPb.ToDisplayStringWithSuffixAndPrecisionV2(
		mf.FloatToMoney(totalDailyChange), false, true, 2, false, moneyPb.IndianNumberSystem,
	)
	portFolioChangeText := fmt.Sprintf("%s%s", changeSign, computedPortfolioChangeText)
	changeValueAnimation := &secretsFePb.PortfolioTrackerRollingAnimationDetails{
		UpdatedPortfolio: typesV2Pb.GetFromBeMoney(mf.FloatToMoney(totalDailyChange)),
		CurrencySymbol:   commontypes.GetTextFromStringFontColourFontStyle(common.RupeeSymbol, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_CURRENCY_XL),
		UpdatedPortfolioText: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.RupeeSymbol, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_CURRENCY_XL),
			commontypes.GetTextFromStringFontColourFontStyle(portFolioChangeText, valueColorType, commontypes.FontStyle_DISPLAY_3XL)),
	}

	summaryCardBuilder := secretsFePb.NewSummaryCardBuilder()
	summaryCardBuilder.AddTopTextTag(typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("INVESTMENTS",
		colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)).WithContainerBackgroundColor(TopTextTagsBgColor).
		WithContainerPadding(4, 8, 4, 8).WithContainerCornerRadius(8))
	summaryCardBuilder.AddTopTextTag(typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(summaryStrategyConfig.ReturnsText,
		colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)).WithContainerBackgroundColor(TopTextTagsBgColor).
		WithContainerPadding(4, 8, 4, 8).WithContainerCornerRadius(8))
	summaryCardBuilder.SetBottomAddendumBar(t.getNextReportDate())
	summaryCardBuilder.SetRightImage(commontypes.GetVisualElementFromUrlHeightAndWidth(trendImage, 124, 76))
	summaryCardBuilder.AddBottomTextTag(typesUiPb.NewITC().WithTexts(
		commontypes.GetTextFromStringFontColourFontStyle("CURRENT "+common.RupeeSymbol, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS),
		commontypes.GetTextFromStringFontColourFontStyle(computedCurrentValue, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)).
		WithBorder(colors.ColorOnDarkDisabled700, 1).WithContainerCornerRadius(32).WithContainerPadding(4, 8, 4, 8),
	)
	summaryCardBuilder.SetBackgroundColour(widgetPb.GetRadialGradientBackgroundColor(&widgetPb.CenterCoordinates{CenterX: 5, CenterY: 5}, 104, []string{LightGreyLinearGradient, DarkGreyLinearGradient}))
	if percentageChange != 0 {
		summaryCardBuilder.SetChangePercentTag(typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(percentageChangeText, percentColorType, commontypes.FontStyle_NUMBER_2XS)).
			WithBorder(BorderColour, 1).WithContainerCornerRadius(32).WithContainerPadding(4, 4, 4, 8))
	}
	summaryCardBuilder.SetChangeValueAnimation(changeValueAnimation)

	return summaryCardBuilder.Build()
}

// getNextReportDate generates a formatted string for the next portfolio report date and time.
// It returns a string in the format "Next report on {day}{suffix} {month}, 11:00 AM"
// where:
// - day is tomorrow's date with ordinal suffix (e.g., "1st", "2nd", "3rd", "4th")
// - month is the abbreviated month name (e.g., "Jan", "Feb")
// If the current time is after 11:00 AM, it uses the next day's date.
// If before 11:00 AM, it uses the current day's date.
func (t *PortfolioSummary) getNextReportDate() string {
	dateString := "Next report on 27th Feb, 11:00 AM"
	// Get tomorrow's date by adding 24 hours to current time
	nextTradingDay := investmentPkg.GetNextTradingDayTime()
	// Get the day and format it with suffix using the helper function
	day := nextTradingDay.Day()
	dayWithSuffix := fmt.Sprintf("%s", integer.GetOrdinalSuffix(day))
	// Format the month as short name (Jan, Feb, etc.)
	month := nextTradingDay.Format("Jan")
	// Create the new date string
	newDateStr := fmt.Sprintf("%s %s", dayWithSuffix, month)
	// Find and replace the old date pattern
	result := strings.Replace(dateString, "27th Feb", newDateStr, 1)

	return result
}
