package portfoliotrackerui

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	dynConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"
	mfUtils "github.com/epifi/gamma/pkg/networth"
)

type MarketInsightComponent struct {
	config *dynConf.Config
}

func NewMarketInsightComponent(config *dynConf.Config) *MarketInsightComponent {
	return &MarketInsightComponent{
		config: config,
	}
}

func (m *MarketInsightComponent) BuildPortfolioTrackerComponent(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) (*strategy.BuildPortfolioTrackerComponentResponse, error) {
	if m.config.DailyNetworthConfig().MarketInsight() == nil {
		return nil, fmt.Errorf("market insight config not found")
	}

	config := m.config.DailyNetworthConfig().MarketInsight()

	// Create MarketInsightComponent builder
	marketInsightComponent := secretsFePb.NewMarketInsightComponentBuilder().
		SetComponentType(req.ComponentType.String()).
		SetHeader(config.Header().IconURL(), config.Header().Title()).
		SetDescription(config.Description()).
		SetComponentImage(config.GraphImageURL()).
		SetDisclaimer(config.Disclaimer()).
		SetBackgroundColour(widget.GetBlockBackgroundColour(config.BackgroundColor())).
		SetCornerRadius(float32(config.CornerRadius())).
		Build()

	navigationToggle := secretsFePb.NewNavigationToggleBuilder().
		SetComponentType(req.ComponentType.String()).
		SetSelectedAndUnSelectedToggleIcon(&secretsFePb.ToggleIconParams{
			SelectedIcon:   "https://epifi-icons.pointz.in/secrets/market_comparison_toggle_selected.png",
			UnselectedIcon: "https://epifi-icons.pointz.in/secrets/market_comparison_toggle.png",
			Text:           "Insights",
		}).
		Build()

	// Decided to show insights only if the percentage change is negative
	mfSchemeAnalyticsResp, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable for market insights %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}

	// nolint: dogsled
	_, _, _, percentageChange := mfUtils.GetMfAggregatedValues(ctx, mfSchemeAnalyticsResp.GetMfSecretsSchemeAnalytics())
	if percentageChange >= 0 {
		return &strategy.BuildPortfolioTrackerComponentResponse{}, nil
	}

	return &strategy.BuildPortfolioTrackerComponentResponse{
		NavigationToggle: navigationToggle,
		PortfolioTrackerComponent: &secretsFePb.PortfolioTrackerComponent{
			Component: &secretsFePb.PortfolioTrackerComponent_MarketInsightComponent{
				MarketInsightComponent: marketInsightComponent,
			},
		},
	}, nil
}
