package strategy

import (
	"context"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	mfUtils "github.com/epifi/gamma/pkg/networth"
)

type TitleComponentConfig struct {
	Title            string
	TitleColor       string
	Subtitle         string
	SubtitleColor    string
	DisplayDate      string
	Disclaimer       *uiPb.IconTextComponent
	VisualElementUrl string
}

type SummaryComponentConfig struct {
	TotalCurrentValue  float64
	TotalPreviousValue float64
	TotalChange        float64
	ReturnsText        string
}

type MfDistributionComponentConfig struct {
	SortedMfDailyChangeList []*mfUtils.DailyChangeDetails
	SchemeAnalyticsVariable *analyserVariablePb.AnalysisVariable
	AggregatedChangeValue   float64
	PercentageChangeMap     map[string]float64
	ValueChangeMap          map[string]float64
	ChangeText              string
}

type TopMoversComponentConfig struct {
	AnalyserVariable []analyserVariablePb.AnalysisVariableName
}

type PortfolioTrackerStrategy interface {
	GetTitleComponentConfig() *TitleComponentConfig
	GetSummaryComponentConfig(ctx context.Context, req *BuildPortfolioTrackerComponentRequest) (*SummaryComponentConfig, error)
	GetMfDistributionComponentConfig(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*MfDistributionComponentConfig, error)
	GetTopMoversConfig() *TopMoversComponentConfig
	GetComponentToVariableMap() map[secretFeEnums.PortfolioTrackerComponentType][]analyserVariablePb.AnalysisVariableName
	GetComponentTypes() []secretFeEnums.PortfolioTrackerComponentType
}
