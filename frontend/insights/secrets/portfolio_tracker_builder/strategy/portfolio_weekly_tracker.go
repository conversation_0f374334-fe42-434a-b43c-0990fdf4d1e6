package strategy

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	secretColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
	mfUtils "github.com/epifi/gamma/pkg/networth"
)

type WeeklyTracker struct{}

func NewWeeklyTracker() *WeeklyTracker {
	return &WeeklyTracker{}
}

func (w *WeeklyTracker) GetTitleComponentConfig() *TitleComponentConfig {
	return &TitleComponentConfig{
		Title:            "Weekly Mutual Fund Tracker",
		TitleColor:       secretColors.ColorSupportingBerry200,
		Subtitle:         "Know what changed this week",
		SubtitleColor:    colors.ColorOnDarkMediumEmphasis,
		DisplayDate:      getDisplayDateForWeeklyPortfolioTracker(),
		Disclaimer:       getDisclaimer(),
		VisualElementUrl: "https://epifi-icons.pointz.in/weekly_report/header_image",
	}
}

func (w *WeeklyTracker) GetSummaryComponentConfig(ctx context.Context, req *BuildPortfolioTrackerComponentRequest) (*SummaryComponentConfig, error) {
	mfWeeklyResp, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION)
	}

	return &SummaryComponentConfig{
		TotalCurrentValue:  moneyPb.ToFloat(mfWeeklyResp.GetMfWeeklyDistribution().GetDayChangeResponse().GetFinalDateTotalValue()),
		TotalPreviousValue: moneyPb.ToFloat(mfWeeklyResp.GetMfWeeklyDistribution().GetDayChangeResponse().GetInitialDateTotalValue()),
		TotalChange:        mfWeeklyResp.GetMfWeeklyDistribution().GetDayChangeResponse().GetTotalChange(),
		ReturnsText:        "1 WEEK RETURNS",
	}, nil
}

func (w *WeeklyTracker) GetMfDistributionComponentConfig(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*MfDistributionComponentConfig, error) {
	variable, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		logger.Error(ctx, "error when fetch analyser variable mf scheme analytics")
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}

	sortedMfDailyChangeList := mfUtils.GetSchemeAnalyticsBySortedDayChange(ctx, variable.GetMfSecretsSchemeAnalytics())

	mfWeeklyVariable, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION]
	if !ok {
		logger.Error(ctx, "error when fetch analyser variable mf weekly distribution")
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION)
	}

	aggregatedWeeklyChangeValue := mfWeeklyVariable.GetMfWeeklyDistribution().GetDayChangeResponse().GetTotalChange()

	weeklyChangeMap := mfUtils.WeeklyChangeMapCreation(mfWeeklyVariable.GetMfWeeklyDistribution().GetDayChangeResponse().GetAssetsValueChange())

	var valueChangeMap = make(map[string]float64)
	var percentageChangeMap = make(map[string]float64)

	for _, mfDailyChange := range sortedMfDailyChangeList {
		weeklyChange, changeValueOk := weeklyChangeMap[mfDailyChange.SchemeAnalytics.GetSchemeDetail().GetId()]
		if !changeValueOk {
			return nil, fmt.Errorf("weekly change not found for scheme ID: %s", mfDailyChange.SchemeAnalytics.GetSchemeDetail().GetId())
		}
		percentageChange := mfUtils.CalculateWeeklyPercentageChange(weeklyChange)
		valueChangeMap[mfDailyChange.SchemeAnalytics.GetSchemeDetail().GetId()] = weeklyChange.GetChange()
		percentageChangeMap[mfDailyChange.SchemeAnalytics.GetSchemeDetail().GetId()] = percentageChange
	}

	return &MfDistributionComponentConfig{
		SortedMfDailyChangeList: sortedMfDailyChangeList,
		SchemeAnalyticsVariable: variable,
		AggregatedChangeValue:   aggregatedWeeklyChangeValue,
		ValueChangeMap:          valueChangeMap,
		PercentageChangeMap:     percentageChangeMap,
		ChangeText:              "Week Change (%)",
	}, nil
}

func (d *WeeklyTracker) GetTopMoversConfig() *TopMoversComponentConfig {
	return &TopMoversComponentConfig{
		AnalyserVariable: []analyserVariablePb.AnalysisVariableName{
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION,
		},
	}
}

// getDisplayDateForWeeklyPortfolioTracker returns a string representing the date range for the last week.
// The function calculates today's date as the end date, and then computes the start date as 7 days before the end date.
// Both dates are formatted as "DD Mon" (e.g., "30 Mar"). The returned string is in the format "DD Mon - DD Mon",
// for example: "29 Mar - 05 Apr".
func getDisplayDateForWeeklyPortfolioTracker() string {
	// Get today's date
	end := time.Now().In(datetime.IST)
	// Get the date one week back
	start := end.AddDate(0, 0, -7)
	// Format both dates as "DD Mon"
	startStr := fmt.Sprintf("%02d %s", start.Day(), start.Format("Jan"))
	endStr := fmt.Sprintf("%02d %s", end.Day(), end.Format("Jan"))
	return fmt.Sprintf("%s - %s", startStr, endStr)
}

func (d *WeeklyTracker) GetComponentToVariableMap() map[secretFeEnums.PortfolioTrackerComponentType][]analyserVariablePb.AnalysisVariableName {
	ComponentTypeToVariableNames := map[secretFeEnums.PortfolioTrackerComponentType][]analyserVariablePb.AnalysisVariableName{
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY:            {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS:         {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_YOU_VS_MARKET:      {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION: {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION:  {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS:           {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
	}
	return ComponentTypeToVariableNames
}

func (d *WeeklyTracker) GetComponentTypes() []secretFeEnums.PortfolioTrackerComponentType {
	return []secretFeEnums.PortfolioTrackerComponentType{
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION,
	}
}
