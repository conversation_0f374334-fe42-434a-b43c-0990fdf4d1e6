package strategy

import (
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
)

type BuildPortfolioTrackerComponentRequest struct {
	ActorId             string
	ComponentType       secretFeEnums.PortfolioTrackerComponentType
	AnalysisVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable
	TrackingStrategy    PortfolioTrackerStrategy
}

type BuildPortfolioTrackerComponentResponse struct {
	ComponentType             secretFeEnums.PortfolioTrackerComponentType
	NavigationToggle          *secretsFePb.NavigationToggle
	PortfolioTrackerComponent *secretsFePb.PortfolioTrackerComponent
}

type BuildAssetWiseDistributionRequest struct {
	ActorId             string
	AssetType           networthBePb.AssetType
	AnalysisVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable
	LineItemCount       int
	TrackingStrategy    PortfolioTrackerStrategy
}

type BuildAssetWiseDistributionResponse struct {
	AssetType                        networthBePb.AssetType
	PortfolioTrackerAssetDetailsCard *secretsFePb.PortfolioTrackerAssetDetailsCard
}
