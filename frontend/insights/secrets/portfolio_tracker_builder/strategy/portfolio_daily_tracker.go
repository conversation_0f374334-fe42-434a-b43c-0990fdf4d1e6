package strategy

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	typesV2Pb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
	mfUtils "github.com/epifi/gamma/pkg/networth"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/integer"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	investmentPkg "github.com/epifi/gamma/pkg/investment"
)

type DailyTracker struct {
	releaseEvaluator release.IEvaluator
}

func NewDailyTracker(releaseEvaluator release.IEvaluator) *DailyTracker {
	return &DailyTracker{
		releaseEvaluator: releaseEvaluator,
	}
}

func (d *DailyTracker) GetTitleComponentConfig() *TitleComponentConfig {
	return &TitleComponentConfig{
		Title:            "Daily Mutual Fund Tracker",
		TitleColor:       colors.ColorForest,
		Subtitle:         "Know what changed in a day, everyday",
		SubtitleColor:    colors.ColorOnDarkHighEmphasis,
		DisplayDate:      getDisplayDateForPortfolioTracker(),
		Disclaimer:       getDisclaimer(),
		VisualElementUrl: "https://epifi-icons.pointz.in/secets/daily-report-background",
	}
}

func (d *DailyTracker) GetSummaryComponentConfig(ctx context.Context, req *BuildPortfolioTrackerComponentRequest) (*SummaryComponentConfig, error) {
	var totalCurrentValue, totalPreviousValue, totalChange float64

	// Get MF scheme analytics
	mfSchemeAnalyticsResp, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}
	// Get Indian stocks assets
	indianStocksResp, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION)
	}

	mfCurrentValue, mfPreviousValue, mfDailyChange, _ := mfUtils.GetMfAggregatedValues(ctx, mfSchemeAnalyticsResp.GetMfSecretsSchemeAnalytics())
	totalCurrentValue += mfCurrentValue
	totalPreviousValue += mfPreviousValue
	totalChange += mfDailyChange

	indianStocksCurrentValue, indianStocksPreviousValue, indianStocksDailyChange := d.getIndianStocksValues(ctx, indianStocksResp.GetIndianStocksDistribution().GetDayChangeResponse())
	totalCurrentValue += indianStocksCurrentValue
	totalPreviousValue += indianStocksPreviousValue
	totalChange += indianStocksDailyChange

	return &SummaryComponentConfig{
		TotalCurrentValue:  totalCurrentValue,
		TotalPreviousValue: totalPreviousValue,
		TotalChange:        totalChange,
		ReturnsText:        "1D RETURNS",
	}, nil
}

func (d *DailyTracker) GetMfDistributionComponentConfig(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*MfDistributionComponentConfig, error) {
	variable, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		logger.Error(ctx, "error when fetch analyser variable mf scheme analytics")
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}

	sortedMfDailyChangeList := mfUtils.GetSchemeAnalyticsBySortedDayChange(ctx, variable.GetMfSecretsSchemeAnalytics())

	_, _, aggregatedDailyChangeValue := mfUtils.CalculateAggregatedMfValues(sortedMfDailyChangeList)

	var valueChangeMap = make(map[string]float64)
	var percentageChangeMap = make(map[string]float64)

	for _, mfDailyChange := range sortedMfDailyChangeList {
		dailyChangeValue := mfUtils.CalculateDailyChange(mfDailyChange)
		valueChangeMap[mfDailyChange.SchemeAnalytics.GetSchemeDetail().GetId()] = dailyChangeValue
		percentageChangeMap[mfDailyChange.SchemeAnalytics.GetSchemeDetail().GetId()] = mfDailyChange.DailyNavPercentageChange
	}

	return &MfDistributionComponentConfig{
		SortedMfDailyChangeList: sortedMfDailyChangeList,
		SchemeAnalyticsVariable: variable,
		AggregatedChangeValue:   aggregatedDailyChangeValue,
		ValueChangeMap:          valueChangeMap,
		PercentageChangeMap:     percentageChangeMap,
		ChangeText:              "Day Change (%)",
	}, nil
}

func (d *DailyTracker) GetTopMoversConfig() *TopMoversComponentConfig {
	return &TopMoversComponentConfig{
		AnalyserVariable: []analyserVariablePb.AnalysisVariableName{
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS,
		},
	}
}

// getDisplayDateForPortfolioTracker returns the current date in the format "1st Jan", "2nd Feb" etc.
// The function always returns yesterday's date by default.
// And if it's before 11 AM, it returns the day before yesterday's date.
// This is because before 11 AM, yesterday's data is not ready to be displayed.
func getDisplayDateForPortfolioTracker() string {
	// get the required date to display on the header
	reportTime := investmentPkg.GetLastTradingDayTime()
	// Get the day and format it with suffix
	day := reportTime.Day()
	dayWithSuffix := integer.GetOrdinalSuffix(day)
	// Format the month as short name (Jan, Feb, etc.)
	month := reportTime.Format("Jan")
	// Return formatted date string
	return fmt.Sprintf("%s %s", dayWithSuffix, month)
}

func getDisclaimer() *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithRightVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/insights/secrets/epifi-wealth-logo2", 11, 56).
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Powered by", "#929599", commontypes.FontStyle_SUBTITLE_XS))
}

func (d *DailyTracker) getIndianStocksValues(ctx context.Context, indianStocksChangeResp *networthBePb.AssetTypeDayChangeResponse) (float64, float64, float64) {
	isEnabled, evaluateErr := d.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesV2Pb.Feature_FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION))
	if evaluateErr != nil || !isEnabled {
		// default to 0 values as graceful handling so at least mf can be shown
		return 0, 0, 0
	}
	return moneyPb.ToFloat(indianStocksChangeResp.GetFinalDateTotalValue()), moneyPb.ToFloat(indianStocksChangeResp.GetInitialDateTotalValue()), indianStocksChangeResp.GetTotalChange()
}

func (d *DailyTracker) GetComponentToVariableMap() map[secretFeEnums.PortfolioTrackerComponentType][]analyserVariablePb.AnalysisVariableName {
	ComponentTypeToVariableNames := map[secretFeEnums.PortfolioTrackerComponentType][]analyserVariablePb.AnalysisVariableName{
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY:            {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS:         {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_YOU_VS_MARKET:      {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION: {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION:  {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS:           {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
	}
	return ComponentTypeToVariableNames
}

func (d *DailyTracker) GetComponentTypes() []secretFeEnums.PortfolioTrackerComponentType {
	return []secretFeEnums.PortfolioTrackerComponentType{
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_YOU_VS_MARKET,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION,
	}
}
