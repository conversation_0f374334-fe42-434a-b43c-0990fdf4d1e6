package strategy

import (
	"github.com/epifi/gamma/api/typesv2/ui"

	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
)

type BuildPortfolioTrackerRequest struct {
	ActorId        string
	ComponentTypes []secretFeEnums.PortfolioTrackerComponentType
}

type BuildPortfolioTrackerResponse struct {
	FixedComponents      []*secretsFePb.PortfolioTrackerComponent
	ScrollableComponents []*secretsFePb.PortfolioTrackerComponent
	FooterComponents     []*ui.IconTextComponent
}

func (b *BuildPortfolioTrackerResponse) GetFixedComponents() []*secretsFePb.PortfolioTrackerComponent {
	if b != nil {
		return b.FixedComponents
	}
	return nil
}

func (b *BuildPortfolioTrackerResponse) GetScrollableComponents() []*secretsFePb.PortfolioTrackerComponent {
	if b != nil {
		return b.ScrollableComponents
	}
	return nil
}

func (b *BuildPortfolioTrackerResponse) GetFooterComponents() []*ui.IconTextComponent {
	if b != nil {
		return b.FooterComponents
	}
	return nil
}
