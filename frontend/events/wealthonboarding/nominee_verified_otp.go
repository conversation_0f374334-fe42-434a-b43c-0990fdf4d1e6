package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	feEvents "github.com/epifi/gamma/frontend/events"
	"github.com/epifi/be-common/pkg/events"
)

type NomineeVerifyOTPEvent struct {
	EventId    string
	UserId     string
	ProspectId string
	EventName  string
	EventType  string
	SessionId  string
	AppVersion uint32
	AppOs      string
	Time       time.Time
}

func (e *NomineeVerifyOTPEvent) GetEventId() string {
	return e.EventId
}

func (e *NomineeVerifyOTPEvent) GetUserId() string {
	return e.UserId
}

func (e *NomineeVerifyOTPEvent) GetProspectId() string {
	return e.ProspectId
}

func (e *NomineeVerifyOTPEvent) GetEventName() string {
	return e.EventName
}

func (e *NomineeVerifyOTPEvent) GetEventType() string {
	return e.EventType
}

func (e *NomineeVerifyOTPEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *NomineeVerifyOTPEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewNomineeVerifyOTPEvent(actorId string, sessionId string, platform commontypes.Platform, appVersion uint32) *NomineeGenerateOTPEvent {
	return &NomineeGenerateOTPEvent{
		EventId:    uuid.New().String(),
		UserId:     actorId,
		ProspectId: uuid.New().String(),
		EventName:  feEvents.WealthNomineeOTPVerified,
		EventType:  events.EventTrack,
		SessionId:  sessionId,
		AppVersion: appVersion,
		AppOs:      platform.String(),
	}
}
