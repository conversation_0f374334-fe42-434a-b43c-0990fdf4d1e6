package connected_account

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
)

const (
	DDMonthYYYYDateFormat = "02 January 2006"
	DDMonthFormat         = "02 Jan"
	DefaultFontOpacity    = 100

	// Title (Title to show on the dashboard card)
	HomeDashboardFiLiteTitle          = "All Accounts"
	HomeDashboardTitle                = "All Bank Accounts"
	HomeDashboardTitleV2              = "All Banks"
	HomeDashboardTitleFiLiteFontColor = "#878A8D" // slate
	HomeDashboardTitleFontColor       = "#7FBECE" // dark green
	HomeDashboardTitleFontColorV2     = "#6A6D70" // Content / On Dark / Low Emphasis 600
	HomeDashboardSubTitle             = "Track all your balances & \nspends in one place"
	HomeDashboardSubTitleV2           = "View all your balances\nin one place"
	HomeDashboardSubTitleFontColor    = "#FFFFFF"
	CaHomeZeroStateImg                = "https://epifi-icons.pointz.in/onboarding/lite/pi_chart.png"
	CaHomeZeroStateImgV2              = "https://epifi-icons.pointz.in/home/<USER>"
	CaHomeZeroStateTickerText         = "Connect Bank Accounts"
	CaHomeZeroStateTickerTextColor    = "#00B899" // fi green
	CaHomeTickerRightIcon             = "https://epifi-icons.pointz.in/home/<USER>"
	// Body(Parameters that contribute to the body of the dashboard view)
	// // Privacy mode image
	PrivacyModeImageUrl    = "https://epifi-icons.pointz.in/home-v2/privacy_panda.png"
	PrivacyModeImageWidth  = 92
	PrivacyModeImageHeight = 26
	// // EyeCaHomeDashboardIcon
	// // // EyeCaHomeDashboardIconText
	EyeCaHomeDashboardIconTextColor       = "#878A8D" // Slate
	EyeCaHomeDashboardIconText            = "HIDE\nINFO"
	EyeCaHomeDashboardIconTextOnSelection = "SHOW\nINFO"
	// // // EyeCaHomeDashboardIconImage
	EyeCaHomeDashboardIconImageUrl    = "https://epifi-icons.pointz.in/home-v2/HideIconV2.png"
	EyeCaHomeDashboardIconImageUrlV2  = "https://epifi-icons.pointz.in/home/<USER>/hide-balance-on.png"
	EyeCaHomeDashboardIconImageWidth  = 44
	EyeCaHomeDashboardIconImageHeight = 36
	// // // EyeCaHomeDashboardIconImageOnSelection
	EyeCaHomeDashboardIconImageOnSelectionUrl    = "https://epifi-icons.pointz.in/home-v2/ShowIconV2.png"
	EyeCaHomeDashboardIconImageOnSelectionUrlV2  = "https://epifi-icons.pointz.in/home/<USER>/hide-balance-off.png"
	EyeCaHomeDashboardIconImageOnSelectionWidth  = 44
	EyeCaHomeDashboardIconImageOnSelectionHeight = 36
	// // LinkCaHomeDashboardIcon
	// // // LinkCaHomeDashboardIconImage
	LinkCaHomeDashboardIconTextColor = "#878A8D" // Slate
	LinkCaHomeDashboardIconText      = "LINK\nACCOUNTS"
	// // // LinkCaHomeDashboardIconImage
	LinkCaHomeDashboardIconImageUrl    = "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
	LinkCaHomeDashboardIconImageUrlV2  = "https://epifi-icons.pointz.in/home/<USER>/account-link.png"
	LinkCaHomeDashboardIconImageWidth  = 44
	LinkCaHomeDashboardIconImageHeight = 40
	// // MoneyCurrencyText
	MoneyCurrencyTextFontColor   = "#66C0DAE0" // Mid Ocean, with 40% opacity
	MoneyCurrencyTextFontColorV2 = "#66ECEEF0" // White-ish, same as savings dashboard, same
	MoneyCurrencyTextFontFamily  = "Inter"
	MoneyCurrencyTextFontStyle   = "normal"
	MoneyCurrencyTextFontSize    = "24px"
	// // MoneyUnitsText
	MoneyUnitsTextFontColor   = "#C0DAE0" // Mid Ocean
	MoneyUnitsTextFontColorV2 = "#ECEEF0" // White-ish, same as savings dashboard
	MoneyUnitsTextFontFamily  = "Gilroy"
	MoneyUnitsTextFontStyle   = "normal"
	MoneyUnitsTextFontSize    = "28px"
	// // MoneyNanosText
	MoneyNanosTextFontColorV2 = "#66ECEEF0" // White-ish, same as savings dashboard
	MoneyNanosTextFontColor   = "#66C0DAE0" // Mid Ocean, with 40% opacity
	MoneyNanosTextFontFamily  = "Gilroy"
	MoneyNanosTextFontStyle   = "normal"
	MoneyNanosTextFontSize    = "24px"
	// // ZeroStateIcon
	// // // ZeroStateIconImage
	ZeroStateIconImageUrl      = "https://epifi-icons.pointz.in/home-v2/LinkIconV3.png"
	ZeroStateIconImageUrlV2    = "https://epifi-icons.pointz.in/home/<USER>"
	ZeroStateIconImageWidth    = 24
	ZeroStateIconImageWidthV2  = 20
	ZeroStateIconImageHeight   = 24
	ZeroStateIconImageHeightV2 = 20
	// // // ZeroStateIconText:
	ZeroStateIconFontColor      = "#FFFFFF" // Monochrome/Snow
	ZeroStateIconFontColorV2    = "#00B899" // Fi Green
	ZeroStateIconBgColor        = "#00B899" // Forest/Colours/Fi Green
	ZeroStateIconBgColorV2      = "#28292B" // dark
	ZeroStateIconDisplayValue   = "Track bank accounts"
	ZeroStateIconDisplayValueV2 = "Connect accounts"
	// // AccountDetailsCaHomeDashboardIcon
	// // // AccountDetailsCaHomeDashboardIconImage
	AccountDetailsCaHomeDashboardIconImageUrl    = "https://epifi-icons.pointz.in/home-v2/account_details_bank.png"
	AccountDetailsCaHomeDashboardIconImageWidth  = 40
	AccountDetailsCaHomeDashboardIconImageHeight = 40
	// // // AccountDetailsCaHomeDashboardIconTitle
	AccountDetailsCaHomeDashboardIconTitleFontColor    = "#8D8D8D" // Gray/Slate
	AccountDetailsCaHomeDashboardIconTitleDisplayValue = "ACCOUNT DETAILS"
	AccountDetailsCaHomeDashboardIconTitleFontFamily   = "Gilroy"
	AccountDetailsCaHomeDashboardIconTitleFontStyle    = "normal"
	AccountDetailsCaHomeDashboardIconTitleFontSize     = "10px"
	// // AccountSettingsCaHomeDashboardIcon
	// // // AccountSettingsCaHomeDashboardIconImage
	AccountSettingsCaHomeDashboardIconImageUrl    = "https://epifi-icons.pointz.in/home-v2/account_settings_gear.png"
	AccountSettingsCaHomeDashboardIconImageWidth  = 40
	AccountSettingsCaHomeDashboardIconImageHeight = 40
	// // // AccountSettingsCaHomeDashboardIconTitle:
	AccountSettingsCaHomeDashboardIconTitleFontColor    = "#8D8D8D" // Gray/Slate
	AccountSettingsCaHomeDashboardIconTitleDisplayValue = "ACCOUNT SETTINGS"
	AccountSettingsCaHomeDashboardIconTitleFontFamily   = "Gilroy"
	AccountSettingsCaHomeDashboardIconTitleFontStyle    = "normal"
	AccountSettingsCaHomeDashboardIconTitleFontSize     = "10px"
	// Footer
	FooterLeftCaHomeDashboardIconWidth  = 28
	FooterLeftCaHomeDashboardIconHeight = 28
	FooterLeftImageTxtPadding           = 0
	FooterCornerRadius                  = 13
	FooterBgColor                       = "#383838" // Gray/Charcoal
	// // FooterBalanceText
	FooterBalanceTextFontColor = "#CED2D6" // Gray/Onyx
	FooterBalanceTextHeight    = 59
	FooterBalanceTextWidth     = 28
	FooterLeftPadding          = 0
	FooterRightPadding         = 10
	FooterTopPadding           = 0
	FooterBottomPadding        = 0
	// // FooterRightIcon
	FooterRightIconUrl    = "https://epifi-icons.pointz.in/home-v2/DashboardFooterSeparatorCharcoal.png"
	FooterRightIconWidth  = 1
	FooterRightIconHeight = 30
	// ZeroStateFooterProperties
	// // ZeroStateFooterPropertiesImage
	ZeroStateFooterPropertiesImageUrl    = "https://epifi-icons.pointz.in/home-v2/zero_state_bank_icon.png"
	ZeroStateFooterPropertiesImageWidth  = 20
	ZeroStateFooterPropertiesImageHeight = 20
	// // ZeroStateFooterPropertiesText
	ZeroStateFooterPropertiesTextFontColor    = "#606265" // Monochrome/Lead
	ZeroStateFooterPropertiesTextDisplayValue = "No bank accounts connected yet"
	ZeroStateTickerText                       = "You haven't connected any accounts"
	ZeroStateTickerTextFontColor              = "#929599"
	ZeroStateFooterPropertiesTextFontFamily   = "Gilroy"
	ZeroStateFooterPropertiesTextFontStyle    = "normal"
	ZeroStateFooterPropertiesTextFontSize     = "14px"

	CaHomeDashboardTickerLeftPadding          = 20
	CaHomeDashboardTickerRightPadding         = 32
	CaHomeDashboardTickerTopPadding           = 18
	CaHomeDashboardTickerBottomPadding        = 18
	CaDashWarningTickerIconUrl                = "https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"
	CaDashFipLogoIconHeight                   = 19
	CaDashFipLogoIconWidth                    = 19
	CaDashWarningTickerIconHeight             = 14
	CaDashWarningTickerIconWidth              = 14
	CaDashWarningTickerIconTextPadding        = 4
	CaDashWarningTickerStopedSyncTextSingle   = "%d bank stopped syncing"
	CaDashWarningTickerStopedSyncTextMultiple = "%d banks stopped syncing"
	CaDashWarningTickerStopedSyncTextColor    = "#929599"
	CaDashTickerBankNameTextColor             = "#6A6D70"
	CaDashTickerMoneyValueTextColor           = "#929599"
	StopSyncedWarningPopupIconUrl             = "https://epifi-icons.pointz.in/home/<USER>"
	StopSyncedWarningPopupTitleText           = "Your details may not be \naccurate right now"
	StopSyncedWarningPopupTitleTextColor      = "#000000"
	StopSyncedWarningPopupBodyText            = "Only %d of your %d bank accounts were synced yesterday. To view the accounts which haven’t been updated, tap ‘Learn more’."
	StopSyncedWarningPopupBodyTextColor       = "#646464"
	StopSyncedWarningPopupBgColor             = "#E7ECF0"
	StopSyncedWarningPopupLearnMoreCtaText    = "Learn more"
	StopSyncedWarningPopupOkCtaText           = "Ok, Got it"

	// background colour for dashboard icons
	DashboardIconsBgColour   = "#313234" // Monochrome/Night
	DashboardIconsBgColourV2 = "#28292B" // dark

	// Disconnect Account Info BottomSheet
	DisconnectInfoBottomSheetTitleText             = "Permanently Disconnect Account"
	DisconnectInfoBottomSheetTitleFontColor        = "#333333"
	DisconnectInfoBottomSheetTitleBgColor          = "#FFFFFF"
	DisconnectInfoBottomSheetTitleFontStyle        = commontypes.FontStyle_HEADLINE_3
	DisconnectInfoBottomSheetTitleFontColorOpacity = 100

	DisconnectInfoBottomSheetSubTitleText             = "Are you sure you want to disconnect this account from Fi? This action cannot be undone. Here’s what’ll happen:"
	DisconnectInfoBottomSheetSubTitleFontColor        = "#333333"
	DisconnectInfoBottomSheetSubTitleBgColor          = "#FFFFFF"
	DisconnectInfoBottomSheetSubTitleFontStyle        = commontypes.FontStyle_BODY_3_PARA
	DisconnectInfoBottomSheetSubTitleFontColorOpacity = 100

	DisconnectInfoBottomSheetInfoListOneIconURL = "https://epifi-icons.pointz.in/connectedaccounts/rocket.png"
	DisconnectInfoBottomSheetInfoListOneTitle   = "Your insights won't use data from this account after %s, and will become less accurate."

	DisconnectInfoBottomSheetInfoListTwoIconURL = "https://epifi-icons.pointz.in/connectedaccounts/transactions.png"
	DisconnectInfoBottomSheetInfoListTwoTitle   = "Transactions after %s from this account won't be visible on the app."

	DisconnectInfoBottomSheetProceedCtaText = "PROCEED"
	DisconnectInfoBottomSheetBackCtaText    = "GO BACK"

	// Delete Account Info BottomSheet
	DeleteInfoBottomSheetTitleText             = "Permanently Delete Account Data"
	DeleteInfoBottomSheetTitleFontColor        = "#333333"
	DeleteInfoBottomSheetTitleBgColor          = "#FFFFFF"
	DeleteInfoBottomSheetTitleFontStyle        = commontypes.FontStyle_HEADLINE_3
	DeleteInfoBottomSheetTitleFontColorOpacity = 100

	DeleteInfoBottomSheetSubTitleText             = "Are you sure you want to delete this account’s data from Fi? This action cannot be undone. Here’s what’ll happen:"
	DeleteInfoBottomSheetSubTitleFontColor        = "#333333"
	DeleteInfoBottomSheetSubTitleBgColor          = "#FFFFFF"
	DeleteInfoBottomSheetSubTitleFontStyle        = commontypes.FontStyle_BODY_3_PARA
	DeleteInfoBottomSheetSubTitleFontColorOpacity = 100

	DeleteInfoBottomSheetInfoListOneIconURL = "https://epifi-icons.pointz.in/connectedaccounts/rocket.png"
	DeleteInfoBottomSheetInfoListOneTitle   = "Your insights won't use past and future data from this account."

	DeleteInfoBottomSheetInfoListTwoIconURL = "https://epifi-icons.pointz.in/connectedaccounts/transactions.png"
	DeleteInfoBottomSheetInfoListTwoTitle   = "All your past transactions from this account will be deleted from Fi. And future ones won't be included."

	DeleteInfoBottomSheetProceedCtaText = "PROCEED"
	DeleteInfoBottomSheetBackCtaText    = "GO BACK"

	// Defaults for Disconnect Account Action
	DefaultDisconnectAccountAction            = feConnectedAccPb.AccountAction_ACCOUNT_ACTION_DISCONNECT
	DefaultDisconnectAccountActionDisplayText = "Disconnect Account"

	// Defaults for Delete Account Action
	DefaultDeleteAccountAction            = feConnectedAccPb.AccountAction_ACCOUNT_ACTION_DELETE
	DefaultDeleteAccountActionDisplayText = "Delete Data"

	// Consent Renewal SDK Login Screen
	ConsentRenewalSdkLoginScreenTitleText             = "Enter OTP to keep these accounts connected to Fi"
	ConsentRenewalSdkLoginScreenTitleFontColor        = "#333333"
	ConsentRenewalSdkLoginScreenTitleBgColor          = "#E7ECF0"
	ConsentRenewalSdkLoginScreenTitleFontStyle        = commontypes.FontStyle_HEADLINE_L
	ConsentRenewalSdkLoginScreenTitleFontColorOpacity = 100

	ConsentRenewalSdkLoginScreenTitleTextForLoggedInUser = "Confirm to keep these accounts connected to Fi"

	// Consent Renewal - Balance Dashboard entrypoint
	BalanceDashoboardRenewalCtaText     = "Reconnect your banks to Fi"
	BalanceDashboardRenewalCtaFontColor = "#EFC0C0"
	BalanceDashboardRenewalCtaBgColor   = "#313234"
	BalanceDashboardRenewalCtaFontStyle = commontypes.FontStyle_SUBTITLE_XS

	ConsentRenewalDangerIconUrl            = "https://epifi-icons.pointz.in/connectedaccount/renewal/3/Balances/Danger.png"
	BalanceDashboardRenewalCtaRightIconUrl = "https://epifi-icons.pointz.in/connectedaccount/renewal/3/Balances/arrow.png"
	BalanceDashboardRenewalPopupBgColor    = "#FAD0D0"
	ConsentRenewalDangerIconHeight         = 100
	ConsentRenewalDangerIconWidth          = 100

	BalanceDashboardRenewalPopupTitleText      = "Some bank accounts are not connected to Fi"
	AccountSettingsPageRenewalPopupTitleText   = "Your bank accounts are not connected to Fi"
	BalanceDashboardRenewalPopupTitleFontColor = "#000000"
	BalanceDashboardRenewalPopupTitleBgColor   = "#FAD0D0"
	BalanceDashboardRenewalPopupTitleFontStyle = commontypes.FontStyle_SUBTITLE_1

	BalanceDashboardRenewalPopupSubtitleExpiredText  = "Disconnected on %s"
	BalanceDashboardRenewalPopupSubtitleExpiringText = "Disconnecting on %s"
	BalanceDashboardRenewalPopupSubtitleFontColor    = "#333333"
	BalanceDashboardRenewalPopupSubtitleBgColor      = "#EFC0C0"
	BalanceDashboardRenewalPopupSubtitleFontStyle    = commontypes.FontStyle_SUBTITLE_3

	BalanceDashboardRenewalPopupBodyText      = "No worries! Tap confirm now to reconnect your bank accounts to Fi"
	BalanceDashboardRenewalPopupBodyFontColor = "#646464"
	BalanceDashboardRenewalPopupBodyBgColor   = "#FAD0D0"
	BalanceDashboardRenewalPopupBodyFontStyle = commontypes.FontStyle_SUBTITLE_3

	BalanceDashboardRenewalPopupBackCtaText    = "Remind me later"
	BalanceDashboardRenewalPopupProceedCtaText = "Confirm"

	BalanceDashboardRenewalTileTagLeftImgTxtPadding = 5
	BalanceDashboardRenewalTileTagCornerRadius      = 11
	BalanceDashboardRenewalTileTagHeight            = 16
	BalanceDashboardRenewalTileTagWidth             = 112
	BalanceDashboardRenewalTileTagLeftPadding       = 4
	BalanceDashboardRenewalTileTagRightPadding      = 8

	BalanceDashboardRenewalTileTagExpiredIconUrl         = "https://epifi-icons.pointz.in/connectedaccount/renewal/3/Balances/warning-triangle.png"
	BalanceDashboardRenewalTileTagExpiredIconImageHeight = 16
	BalanceDashboardRenewalTileTagExpiredIconImageWidth  = 16

	BalanceDashboardRenewalTileTagExpiredText       = "NOT CONNECTED"
	BalanceDashboardRenewalTileTagExpiringText      = "DISCONNECTING SOON"
	BalanceDashboardRenewalTileTagExpiredFontColor  = "#A73F4B"
	BalanceDashboardRenewalTileTagExpiringFontColor = "#AC7C44"
	BalanceDashboardRenewalTileTagExpiredBgColor    = "#FAD0D0"
	BalanceDashboardRenewalTileTagExpiringBgColor   = "#F4E7BF"

	// Reoobe Bottomsheet
	ReeobeBottomsheetTitleText      = "Delete existing account data"
	ReeobeBottomsheetTitleFontColor = "#333333"
	ReeobeBottomsheetTitleFontStyle = commontypes.FontStyle_HEADLINE_3

	ReeobeBottomsheetBodyText      = "By connecting bank accounts linked to your new mobile number, you will erase all transaction and balance data connected to your previous phone number on Fi"
	ReeobeBottomsheetBodyFontColor = "#333333"
	ReeobeBottomsheetBodyFontStyle = commontypes.FontStyle_BODY_3_PARA

	ReeobeBottomsheetFooterText      = "Read our Connected Accounts FAQs for more details"
	ReeobeBottomsheetFooterFontColor = "#333333"
	ReeobeBottomsheetFooterFontStyle = commontypes.FontStyle_BODY_3_PARA

	ReeobeBottomsheetBackCtaText    = "GO BACK"
	ReeobeBottomsheetProceedCtaText = "PROCEED"

	// Balance Dashboard
	CollectiveBalanceDefaultFontColor        = "#FFFFFF"
	CollectiveBalanceConsentRenewalFontColor = "#CF8888"

	AccountManagerConsentRenewalIconWidth  = 24
	AccountManagerConsentRenewalIconHeight = 24
	AccountManagerConsentRenewalIconUrl    = "https://epifi-icons.pointz.in/connectedaccount/renewal/warning-error-triangle-red.png"

	AccountManagerConsentRenewalNudgeIconUrl          = "https://epifi-icons.pointz.in/connectedaccount/renewal/3/Balances/Danger.png"
	AccountManagerConsentRenewalNudgeIconWidth        = 36
	AccountManagerConsentRenewalNudgeIconHeight       = 36
	AccountManagerConsentRenewalNudgeText             = "Your bank accounts aren’t connected to Fi. Tap to connect them again"
	AccountManagerConsentRenewalNudgeFontColor        = "#313234" // night
	AccountManagerConsentRenewalNudgeFontStyle        = commontypes.FontStyle_SUBTITLE_S
	AccountManagerConsentRenewalNudgeTextImagePadding = 20
	AccountManagerConsentRenewalNudgeBgColor          = "#E3E7EC" // smoke
	AccountManagerConsentRenewalNudgeLeftPadding      = 28
	AccountManagerConsentRenewalNudgeRightPadding     = 28
	AccountManagerConsentRenewalNudgeTopPadding       = 28
	AccountManagerConsentRenewalNudgeBottomPadding    = 28
	AccountManagerConsentRenewalNudgeCornerRadius     = 19
)

const (
	FiToFiBottomSheetDisplayTitle               = "Track your money better"
	FiToFiBottomSheetTitleFontColor             = "#313234"
	FiToFiBottomSheetDisplayDesc                = "Verify account details for improved insights on your spends"
	FiToFiBottomSheetDescFontColor              = "#606265"
	FiToFiBottomSheetWealthTncFontColor         = "#8D8D8D"
	FiToFiBottomSheetCtaDisplayText             = "Verify now"
	FiToFiBottomSheetIconWidth                  = 49
	FiToFiBottomSheetIconHeight                 = 64
	FiToFiBottomSheetIconURL                    = "https://epifi-icons.pointz.in/connectedaccount/secure_fi_to_fi_lock"
	FiToFiBottomSheetAaLoginDisplayTitle        = "Verify your mobile number"
	FiToFiBottomSheetAaLoginDisplayDesc         = "Enter the OTP received on \n%s to help us fetch your account"
	FiToFiBottomSheetAaAlreadyLoginDisplayTitle = "Confirm & verify your mobile number linked to this a/c"
	FiToFiBottomSheetAccDiscoveryDisplayTitle   = "Fetching your Savings Account"
	FiToFiBottomSheetAccDiscoveryDisplayDesc    = "This should take a few seconds"
	FiToFiBottomSheetFipLoginDisplayTitle       = "Account Verification"
	FiToFiBottomSheetFipLoginDisplayDesc        = "Enter the OTP to verify your account"
	FederalSavingAccount                        = "Federal Savings Account"
	FederalFipLogoURL                           = "https://epifi-icons.pointz.in/connectedaccounts/banks/Federal.png"
	FederalFipMaxDepositAccounts                = 50
)

const (
	FiToFiSuccessTerminalScreenBottomSheetTitle                = "Federal Bank Savings Account verification successful"
	FiToFiSuccessTerminalScreenBottomSheetTitleFontColor       = "#333333"
	FiToFiSuccessTerminalScreenBottomSheetTitleFontStyle       = commontypes.FontStyle_HEADLINE_L
	FiToFiSuccessTerminalScreenBottomSheetDescA                = "You can now go back to tracking your expenses"
	FiToFiSuccessTerminalScreenBottomSheetDescFontColor        = "#8D8D8D"
	FiToFiSuccessTerminalScreenBottomSheetDescFontStyle        = commontypes.FontStyle_BODY_S
	FiToFiSuccessTerminalScreenBottomSheetDescB                = "You can link your other bank accounts to Fi & track your spends in an improved way in one place"
	FiToFiSuccessTerminalScreenBottomSheetConnectAccCta        = "Link bank a/c"
	FiToFiSuccessTerminalScreenBottomSheetConnectAccLaterCta   = "Maybe later"
	FiToFiSuccessTerminalScreenBottomSheetIconWidth            = 40
	FiToFiSuccessTerminalScreenBottomSheetIconHeight           = 40
	FiToFiSuccessTerminalScreenBottomSheetAutoDismissDuration  = 2
	FiToFiSuccessTerminalScreenBottomSheetBgColor              = "#FFFFFF"
	FiToFiSuccessTerminalScreenBottomSheetSuccessScreenIconURL = "https://epifi-icons.pointz.in/connectedaccount/fi_to_fi_success_terminal_screen_icon"
)

const (
	consentRenewalWidgetDangerIconHeight       = 56
	consentRenewalWidgetDangerIconWidth        = 56
	ConsentRenewalWidgetTitleText              = "Your bank accounts aren’t connected to Fi"
	consentRenewalWidgetTitleFontStyle         = commontypes.FontStyle_HEADLINE_M
	consentRenewalWidgetTitleFontColor         = "#313234" // night
	ConsentRenewalWidgetSubTitleText           = "No worries! Confirm with OTP now to connect them again"
	consentRenewalWidgetSubTitleFontStyle      = commontypes.FontStyle_BODY_S
	consentRenewalWidgetSubTitleFontColor      = "#606265" // lead
	consentRenewalWidgetContainerBgColor       = "#E3E7EC" // smoke
	consentRenewalWidgetContainerBorderRadius  = 24
	consentRenewalWidgetContainerTopPadding    = 32
	consentRenewalWidgetContainerBottomPadding = 32
	consentRenewalWidgetContainerLeftPadding   = 24
	consentRenewalWidgetContainerRightPadding  = 24
	consentRenewalWidgetCtaBgColor             = "#FFFFFF" // snow
	consentRenewalWidgetCtaBorderRadius        = 19
	consentRenewalWidgetCtaTopPadding          = 12
	consentRenewalWidgetCtaBottomPadding       = 12
	consentRenewalWidgetCtaLeftPadding         = 54
	consentRenewalWidgetCtaRightPadding        = 54
	consentRenewalWidgetCtaText                = "Get OTP"
	consentRenewalWidgetCtaTextFontColor       = "#00B899" // Fi Green
	consentRenewalWidgetCtaTextFontStyle       = commontypes.FontStyle_BUTTON_M
)

const (
	consentRenewalActionBannerLeftIconHeight      = 24
	consentRenewalActionBannerLeftIconWidth       = 24
	consentRenewalActionBannerTextFontColor       = "#FFFFFF" // snow
	consentRenewalActionBannerTextBgColor         = "#A73F4B" // deep-peach
	consentRenewalActionBannerText                = "Reconnect your banks to Fi"
	consentRenewalActionBannerTextFontStyle       = commontypes.FontStyle_SUBTITLE_S
	consentRenewalActionBannerRightIconUrl        = "https://epifi-icons.pointz.in/connectedaccount/renewal/actionBanner/DeepPeachrightArrow"
	consentRenewalActionBannerRightIconHeight     = 24
	consentRenewalActionBannerRightIconWidth      = 24
	consentRenewalActionBannerLeftImgTextPadding  = 8
	consentRenewalActionBannerRightImgTextPadding = 12
	consentRenewalActionBannerBgColor             = "#A73F4B" // deep-peach
	consentRenewalActionBannerTopPadding          = 8
	consentRenewalActionBannerBottomPadding       = 8
)

const (
	FixedDeposits                            = "Fixed Deposits"
	SmartDeposits                            = "Smart Deposits"
	NetWorthDepositSectionInfoCtaUrl         = "https://epifi-icons.pointz.in/connectedaccount/info_icon_1.png"
	RecurringDeposits                        = "Recurring Deposits"
	MaturedAndClosedDeposits                 = "Matured & closed deposits"
	NetWorthDepositSectionInfoCtaImageWidth  = 24
	NetWorthDepositSectionInfoCtaImageHeight = 24
	TotalDepositedText                       = "Total Deposited"
	TotalDepositedTextFontColor              = "#8D8D8D"
	ConnectMoreDepositsAccIconUrl            = "https://epifi-icons.pointz.in/connectedaccount/link.png"
	ConnectMoreDepositsAccText               = "Add more deposits"
	AccTileBalanceTextFontColor              = "#333333"
	AccTileHeadingText                       = "#333333"
	AccTileSubHeadingText                    = "#646464"
	MaturingOnText                           = "Maturing on"
	MaturedOnText                            = "Matured on"
	RenewsOnText                             = "Renews on"
	FdDepAccTileLogoHeight                   = 48
	FDDepAccTileLogoWidth                    = 48
	SdDepAccTileLogoHeight                   = 40
	SdDepAccTileLogoWidth                    = 40
	FiFDAccTileDisplayTag                    = "https://epifi-icons.pointz.in/connectedaccount/fi_fd_icon_1.png"
	FiFDAccTileDisplayTagHeight              = 24
	FiFDAccTileDisplayTagWidth               = 24
	FiFDAccTileBgColor                       = "#CDC6E8"
	FiSDAccTileBgColor                       = "#D9F2CC"
	MaturingSoonText                         = "Maturing soon"
	AccountTileDefaultBgColor                = "#EFF2F6"
	Days                                     = "Days"
	Interest                                 = "% Interest"
	PlusRD                                   = "Plus RD"
	FDPlus                                   = "FD Plus"
	AccTileSubHeadingFontColor               = "#333333"
	AccTileHeadingFontColor                  = "#646464"
	UserDeclaredInvestmentIconUrl            = "https://epifi-icons.pointz.in/connectedaccount/manually_added_dep.png"
	ToastMessageOnTapAccTile                 = "Coming Soon: Edit & delete feature for deposits!"
	ClosedOnText                             = "Closed on"
	dateFormat                               = "2 Jan 2006"
)

const (
	NoAccDiscoveredTitleText           = "We're working to support more bank accounts soon!"
	NoAccDiscoveredSubTitleText        = "We couldn’t to find any accounts linked to your mobile number %s"
	NoAccDiscoveredFipIconText         = "https://epifi-icons.pointz.in/connectedaccount/no_acc_discover.png"
	FipListNotDiscoveryText            = "Axis, Kotak, Federal Bank & more are coming soon!"
	NoAccDiscoveredProceedDeeplinkText = "TELL US WHERE YOU BANK"
)

// discovery screen params display constants
const (
	accountDiscoveryLoadingTextDefault             = "Searching for your accounts"
	cantSeeAccountsTextDefault                     = "Searching for your accounts"
	accountDiscoveryLoadingTextIndStocks           = "Searching for your \ndemat accounts"
	cantSeeAccountsTextIndStocks                   = "Can’t see your demat accounts?"
	accountDiscoveryTitleTextIndStocks             = "Select the demat accounts you \nwant to link & track on Fi"
	accountDiscoverySubtitleTextIndStocks          = "We found these demat accounts linked to %s"
	accountDiscoverySubtitleSearchingTextIndStocks = "One moment! We're searching for your demat accounts linked to %s."
)

const (
	Height                                            = 32
	Width                                             = 32
	AaSalaryBenefitsScreenTopIconUrl                  = "https://epifi-icons.pointz.in/aasalary/benefitsscreen/top_icon.png"
	AaSalaryBenefitsScreenBenefitTitle                = "Steps to upgrade"
	AaSalaryBenefitsScreenBenefitTitleFontColor       = "#313234"
	AaSalaryBenefitsScreenBenefitTitleFontStyle       = commontypes.FontStyle_SUBTITLE_S
	AaSalaryBenefitsScreenBgColor                     = "#F6F9FD"
	AaSalaryBenefitsScreenBenefitText1                = "Connect the a/c where you get your salary"
	AaSalaryBenefitsScreenBenefitText2                = "Transfer a portion of your salary"
	AaSalaryBenefitsScreenBenefitText3                = "Instant upgrade & earn up 3% back"
	AaSalaryBenefitsScreenBenefitIconUrl              = "https://epifi-icons.pointz.in/aasalary/upi.png"
	AaSalaryBenefitsScreenBenefitTextFontColor        = "#6A6D70"
	AaSalaryBenefitsScreenBenefitTextFontStyle        = commontypes.FontStyle_BODY_S
	AaSalaryBenefitsScreenBenefitLeftImgPadding int32 = 8
	AaSalaryBenefitsScreenCsLabelText                 = "COMING SOON"
	AaSalaryBenefitsScreenLabelText                   = "Upload your ITR to calculate cashback"
)

const (
	BankFacingIssueIconUrl  = "https://epifi-icons.pointz.in/connectedaccounts/bank_issue.png"
	NoMatchingResult        = "No matching results.\nTry another search?"
	NoMatchingResultIcon    = "https://epifi-icons.pointz.in/aa-salary/parrot_with_wire.png"
	AaSalaryBankSearchTitle = "Select your salary bank account"
	Continue                = "Continue"
	OkGotIt                 = "Ok, got it"
	ServerDownTitle         = "Bank servers are down"
	ServerDownSubTitle      = "We’ll notify you once they’re back up"
	UnAvailableTitle        = "Couldn’t find account"
	UnAvailableSubTitle     = "Your salary account isn’t in our supported list of banks. We’ll let you know when this changes."
	DefaultErrorTitle       = "Something went wrong"
	DefaultErrorSubTitle    = "We are working on it"
	PopularBanksTitle       = "Popular Banks"
	AllBanksTitle           = "All Banks"
)

const (
	BankServerDownIssue   = "BANK_SERVER_DOWN"
	BankNotSupportedIssue = "BANK_NOT_SUPPORTED"
)
