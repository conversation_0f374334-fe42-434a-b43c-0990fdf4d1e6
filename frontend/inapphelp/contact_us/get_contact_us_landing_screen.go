package contact_us

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/cx/ticket"
	feTicketPb "github.com/epifi/gamma/api/frontend/cx/ticket"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/frontend/inapphelp/faq"
	"github.com/epifi/gamma/api/risk/enums"
	riskProfilePb "github.com/epifi/gamma/api/risk/profile"
	types "github.com/epifi/gamma/api/typesv2"
	cxDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/help"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/cx/chat"
	"github.com/epifi/gamma/cx/ticket/ticket_states"
	inapphelpevents "github.com/epifi/gamma/frontend/inapphelp/events"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/feature/release/config"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	contactUsPb "github.com/epifi/gamma/api/frontend/inapphelp/contact_us"
	irPb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/frontend/events/inapphelp"
	"github.com/epifi/gamma/frontend/inapphelp/contact_us/constants"
)

const (
	landingScreenTitle         = "What can we help you with?"
	contactUsScreenDescription = "Our team is here to help you!"
	contactUsBannerIconUrl     = "https://epifi-icons.pointz.in/contact_us/landing_banner.png"
	searchScreenTitle          = "Tell us about your issue"
)

type IncidentSectionWithMeta struct {
	Widget   *contactUsPb.IncidentWidgetSection
	Date     time.Time
	Priority int
}

// Constants for V2 landing screen
const (
	v2LandingScreenTitle     = "Help Center"
	v2TopSectionTitle        = "Get expert help at your fingertips!"
	v2TopSectionSubtitle     = "Good news - we're already on these"
	v2FaqSectionTitle        = "Get your queries resolved instantly"
	v2ChatButtonText         = "Chat with us"
	primaryCtaText           = "View Ticket"
	autoIDText               = "Auto ID"
	contactUsLandingScreenV2 = "Contact Us Landing Screen V2"
	ticketId                 = "TICKET_ID"
	l1Category               = "L1_CATEGORY"
	l2CATEGORY               = "L2_CATEGORY"
	l3CATEGORY               = "L3_CATEGORY"
	ctaText                  = "CTA_TEXT"
	ticketType               = "TICKET_TYPE"
	source                   = "SOURCE"
)

func (s *Service) GetContactUsLandingScreen(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenRequest) (*contactUsPb.GetContactUsLandingScreenResponse, error) {
	defer s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), inapphelp.NewIssueReportingLandedEvent(inapphelp.InAppIssueReportingLandingScreenLoad, req.GetReq()))

	screenTitle := commontypes.GetTextFromStringFontColourFontStyleFontAlignment(landingScreenTitle, colors.ColorLead,
		commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT)
	screenItems := s.getLandingScreenItems(ctx, req)

	return &contactUsPb.GetContactUsLandingScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Result: &contactUsPb.GetContactUsLandingScreenResponse_IssueDirectory{
			IssueDirectory: s.getIssueDirectory(ctx, req),
		},
		ScreenTitle:           screenTitle,
		MinQueryLength:        s.contactUsConf.MinQueryLength(),
		MaxQueryLength:        s.contactUsConf.MaxQueryLength(),
		QueryValidationConfig: s.getQueryValidationConfig(),
		LandingScreenItems:    screenItems,
		BannerImage:           commontypes.GetImageFromUrl(contactUsBannerIconUrl),
		ScreenDescription:     commontypes.GetTextFromStringFontColourFontStyle(contactUsScreenDescription, colors.ColorMonochromeAsh, commontypes.FontStyle_HEADLINE_S),
		LandingScreenTitle:    commontypes.GetTextFromStringFontColourFontStyle(searchScreenTitle, colors.ColorSnow, commontypes.FontStyle_HEADLINE_L),
		ChatbotDeeplink:       s.checkEligibilityAndGetChatbotDl(ctx, req),
	}, nil
}

// checkEligibilityAndGetChatbotDl checks if the user is eligible for the forced chatbot flow
// and returns the deeplink to the chatbot screen
func (s *Service) checkEligibilityAndGetChatbotDl(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenRequest) *deeplinkPb.Deeplink {
	if !s.contactUsConf.ForcedChatbotFlowExperimentConfig().Enable() {
		return nil
	}
	var (
		actorId     = req.GetReq().GetAuth().GetActorId()
		userLayer   = events.GetUserLayerBucket(actorId)
		freshchatdl = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CHAT_WITH_US_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_ChatWithUsScreenOptions{
				ChatWithUsScreenOptions: &deeplinkPb.ChatWithUsScreenOptions{
					ChatViewType:   types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK,
					ContextCode:    chat.ContextCodeConnectToAnAgent,
					InitialMessage: &deeplinkPb.ChatWithUsScreenOptions_InitialMessage{Text: "We're connecting you to our team..."},
				},
			},
		}
		cohort                = ""
		shouldSendFreshchatDl = false
	)

	userGroupConstraintData := release.NewUserGroupConstraintData(&config.UserGroupConstraintConfig{
		AllowedGroups: s.contactUsConf.ForcedChatbotFlowExperimentConfig().AllowedUserGroups(),
	})
	userGroupConstraint := release.NewUserGroupConstraint(s.actorClient, s.userClient, s.ugClient)
	isInAllowedUserGroup, err := userGroupConstraint.Evaluate(ctx, userGroupConstraintData,
		release.NewCommonConstraintData(types.Feature_FEATURE_UNSPECIFIED).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to call userGroupConstraint.Evaluate", zap.String(logger.ACTOR_ID_V2,
			actorId), zap.Error(err))

	}

	switch {
	case isInAllowedUserGroup:
		cohort = "ALLOWED_USER_GROUP"
		shouldSendFreshchatDl = true
	// If the user is not in the allowed user group, check if they are in the allowed cohorts and satisfies the user layer percentage
	case s.isUserInFreshchatExperimentLayer(userLayer):
		if lo.Contains(s.contactUsConf.ForcedChatbotFlowExperimentConfig().AllowedCohorts().ToStringArray(), AccountFreezeChatbotExperimentCohort) {
			cohort = "AccountFreezeChatbotExperimentCohort"
			shouldSendFreshchatDl = s.isAccountFrozen(ctx, actorId)
		}
	}

	s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), inapphelpevents.NewContactUsLandingScreenLoaded(actorId, cohort, userLayer, shouldSendFreshchatDl))
	if shouldSendFreshchatDl {
		return freshchatdl
	}
	return nil
}

func (s *Service) isAccountFrozen(ctx context.Context, actorId string) bool {
	riskProfileRes, err := s.riskProfileClient.GetDetailedUserProfile(ctx, &riskProfilePb.GetDetailedUserProfileRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(riskProfileRes, err); rpcErr != nil {
		logger.Error(ctx, "error getting user profile", zap.Error(rpcErr))
	}
	isAccountFrozen, _ := s.getUserAccountFreezeStatus(riskProfileRes)
	return isAccountFrozen
}

func (s *Service) isUserInFreshchatExperimentLayer(userLayer int16) bool {
	return int64(userLayer) < s.contactUsConf.ForcedChatbotFlowExperimentConfig().UserLayerPercentage()
}

// function to return if user is risk blocked and risk block type user is in
func (s *Service) getUserAccountFreezeStatus(riskUserProfileResp *riskProfilePb.GetDetailedUserProfileResponse) (bool, enums.AccountFreezeStatus) {
	// Identifying both is risk block and block type in same function & condition so that we don't need to parse rpc result again
	for _, accountDetails := range riskUserProfileResp.GetAccountsInfo() {
		if accountDetails.GetAccountType() == accounts.Type_SAVINGS {
			if accountDetails.GetAccountStatus().GetPresentStatus() != enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN &&
				accountDetails.GetAccountStatus().GetPresentStatus() != enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNSPECIFIED {
				return true, accountDetails.GetAccountStatus().GetPresentStatus()
			}
		}
	}
	return false, enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNSPECIFIED
}

func (s *Service) getLandingScreenItems(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenRequest) []*contactUsPb.LandingScreenItems {
	var screenItems []*contactUsPb.LandingScreenItems

	ticketResp, ticketErr := s.feTicketClient.GetTicketBanner(ctx, &feTicketPb.GetTicketBannerRequest{
		Req:          req.GetReq(),
		StatusFilter: feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
	})
	if rErr := epifigrpc.RPCError(ticketResp.GetRespHeader(), ticketErr); rErr != nil && !ticketResp.GetRespHeader().GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to fetch ticket banner", zap.Error(rErr))
	}
	if ticketResp.GetTicketDetails() != nil {
		screenItems = append(screenItems, s.createTicketDetailsItem(ticketResp))
	}

	categoryResp, categoryErr := s.faqClient.GetAllCategories(ctx, &faq.GetAllCategoriesRequest{
		Req: req.GetReq(),
	})
	if rErr := epifigrpc.RPCError(categoryResp.GetRespHeader(), categoryErr); rErr != nil {
		logger.Error(ctx, "failed to fetch all categories", zap.Error(rErr))
	}
	if len(categoryResp.GetCategories()) != 0 {
		screenItems = append(screenItems, s.createFaqCategoriesItem(categoryResp))
	}

	issueResp, issueErr := s.issueReportingClient.GetIssueBySource(ctx, &irPb.GetIssueBySourceRequest{
		ActorId:   req.GetReq().GetAuth().GetActorId(),
		Sources:   []irPb.IssueSource{irPb.IssueSource_ISSUE_SOURCE_TRENDING, irPb.IssueSource_ISSUE_SOURCE_SEARCH_HISTORY},
		SessionId: req.GetReq().GetSessionId(),
	})
	if rErr := epifigrpc.RPCError(issueResp, issueErr); rErr != nil && !issueResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to fetch issue by sources", zap.Error(rErr))
	}
	trendingIssues, recentIssues := s.getIssuesBySource(issueResp)
	screenItems = append(screenItems, &contactUsPb.LandingScreenItems{
		Item: &contactUsPb.LandingScreenItems_IssueDirectory{
			IssueDirectory: getIssueDirectory(recentIssues, trendingIssues),
		},
	})

	return screenItems
}

// nolint: dupl
func (s *Service) createTicketDetailsItem(ticketResp *feTicketPb.GetTicketBannerResponse) *contactUsPb.LandingScreenItems {
	return &contactUsPb.LandingScreenItems{
		Item: &contactUsPb.LandingScreenItems_TicketDetails{
			TicketDetails: &contactUsPb.TicketDetails{
				Title:   commontypes.GetTextFromStringFontColourFontStyle(recentTicketText, colors.ColorDarkLayer2, commontypes.FontStyle_SUBTITLE_M),
				Details: ticketResp.GetTicketDetails(),
				ViewAll: &typesUiPb.IconTextComponent{
					Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(viewAllText, colors.ColorMonochromeLead, commontypes.FontStyle_OVERLINE_XS_CAPS)},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_CX_TICKET_LIST_SCREEN,
					},
					RightVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: constants.GreyRightChevron,
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  constants.ViewAllIconWidth,
									Height: constants.ViewAllIconHeight,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
				},
			},
		},
	}
}

// nolint: dupl
func (s *Service) createFaqCategoriesItem(categoryResp *faq.GetAllCategoriesResponse) *contactUsPb.LandingScreenItems {
	return &contactUsPb.LandingScreenItems{
		Item: &contactUsPb.LandingScreenItems_FaqCategories{
			FaqCategories: &contactUsPb.FaqCategories{
				Title:      commontypes.GetTextFromStringFontColourFontStyle(helpCategoriesText, colors.ColorDarkLayer2, commontypes.FontStyle_SUBTITLE_M),
				Categories: categoryResp.GetCategories(),
				ViewMore: &typesUiPb.IconTextComponent{
					Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(viewAllText, colors.ColorMonochromeLead, commontypes.FontStyle_OVERLINE_XS_CAPS)},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_CX_CATEGORY_SEARCH_SCREEN,
					},
					RightVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: constants.GreyRightChevron,
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  constants.ViewAllIconWidth,
									Height: constants.ViewAllIconHeight,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
				},
			},
		},
	}
}

func (s *Service) getIssuesBySource(issueResp *irPb.GetIssueBySourceResponse) (trendingIssues, recentIssues []*irPb.UserIssue) {
	for _, issueBySource := range issueResp.GetIssueResponses() {
		switch issueBySource.GetSource() {
		case irPb.IssueSource_ISSUE_SOURCE_TRENDING:
			trendingIssues = issueBySource.GetIssues()
		case irPb.IssueSource_ISSUE_SOURCE_SEARCH_HISTORY:
			recentIssues = issueBySource.GetIssues()
		}
	}
	return
}

func (s *Service) getIssueDirectory(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenRequest) *contactUsPb.IssueDirectory {
	issueResp, issueErr := s.issueReportingClient.GetIssueBySource(ctx, &irPb.GetIssueBySourceRequest{
		ActorId:   req.GetReq().GetAuth().GetActorId(),
		Sources:   []irPb.IssueSource{irPb.IssueSource_ISSUE_SOURCE_TRENDING, irPb.IssueSource_ISSUE_SOURCE_SEARCH_HISTORY},
		SessionId: req.GetReq().GetSessionId(),
	})
	if rErr := epifigrpc.RPCError(issueResp, issueErr); rErr != nil && !issueResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to fetch issue by sources", zap.Error(rErr))
	}

	trendingIssues, recentIssues := s.getIssuesBySource(issueResp)
	return getIssueDirectory(recentIssues, trendingIssues)
}

func (s *Service) GetOpenIncidentsScreen(ctx context.Context, req *contactUsPb.GetOpenIncidentsScreenRequest) (*contactUsPb.GetOpenIncidentsScreenResponse, error) {
	return &contactUsPb.GetOpenIncidentsScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ScreenDetails: &contactUsPb.IncidentScreenDetails{
			ToolBar: &contactUsPb.ToolBar{
				Title:    commontypes.GetTextFromStringFontColourFontStyle("History", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M),
				LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(constants.IncidentBackChevronIconUrl, 28, 28),
				RightIcons: []*typesUiPb.IconTextComponent{
					typesUiPb.NewITC().WithRightImageUrlHeightAndWidth("", 24, 24),
				},
			},
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: colors.ColorDarkBase,
				},
			},
			Incidents: &contactUsPb.IncidentsListWidget{
				IncidentsTitle:         commontypes.GetTextFromStringFontColourFontStyle("Active issues", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_DISPLAY_M),
				IncidentWidgetSections: s.buildIncidentSection(ctx, req.GetReq(), "Incidents See More Screen"),
			},
		},
	}, nil
}

// GetContactUsLandingScreenV2 returns the V2 version of the contact us landing screen
func (s *Service) GetContactUsLandingScreenV2(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenV2Request) (*contactUsPb.GetContactUsLandingScreenV2Response, error) {
	if req == nil || req.GetReq() == nil {
		return nil, nil
	}

	resp := &contactUsPb.GetContactUsLandingScreenV2Response{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		PageTopSection: s.buildPageTopSection(ctx, req),
		HelpSections:   s.buildHelpSections(ctx, req),
		FloatingIcon:   s.buildFloatingIcon(),
	}
	return resp, nil
}

// buildPageTopSection builds the top section of the landing page
func (s *Service) buildPageTopSection(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenV2Request) *contactUsPb.PageTopSection {
	incidentSection := s.buildIncidentSection(ctx, req.GetReq(), contactUsLandingScreenV2)
	topSectionSubTitle := typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(v2TopSectionSubtitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S))
	seeMore := buildSeeMoreButton()
	incidentSectionLength := int64(len(incidentSection))

	if incidentSectionLength <= s.genConf.CxLandingPageV2Config().MaxNumberOfOpenIndividualTickets() {
		seeMore = nil
		if incidentSectionLength == 0 {
			topSectionSubTitle = nil
		}
	}

	incidentSection = incidentSection[:min(s.genConf.CxLandingPageV2Config().MaxNumberOfOpenIndividualTickets(), incidentSectionLength)]

	return &contactUsPb.PageTopSection{
		ToolBar: &contactUsPb.ToolBar{
			LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(constants.IncidentBackChevronIconUrl, 28, 28),
			RightIcons: []*typesUiPb.IconTextComponent{
				typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("HISTORY", colors.ColorForest, commontypes.FontStyle_OVERLINE_XS_CAPS)).WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-timer-icon.png", 20, 20).WithDeeplink(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_CX_TICKET_LIST_SCREEN,
				}),
			},
		},
		TopSectionDetails: &contactUsPb.TopSectionDetails{
			FiCareImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-fi-care-icon-updated.png", 112, 112),
			Title:       typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(v2TopSectionTitle, colors.ColorWater, commontypes.FontStyle_DISPLAY_L)),
			Subtitle:    topSectionSubTitle,
		},
		TopSectionBgImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-background-image.png", 412, 572),
		IncidentSection:   incidentSection,
		SeeMore:           seeMore,
	}
}

// buildIncidentSection builds the incident widget section dynamically from user's open tickets
func (s *Service) buildIncidentSection(ctx context.Context, req *header.RequestHeader, screenName string) []*contactUsPb.IncidentWidgetSection {
	var statusList []ticket.Status
	statusList = append(statusList, ticket_states.GetOpenTicketStates()...)
	statusList = append(statusList, ticket_states.GetWaitingOnCustomerTicketStates()...)
	statusList = append(statusList, ticket_states.GetClosedTicketStates()...)

	var productCategoryList []ticket.ProductCategory

	// whitelisted product categories to be shown in app
	// Ref : cx config - TicketConfig.ShowTicketsInAppConfig.WhitelistedProductCategories
	for productCategory := range ticket.ProductCategory_name {
		if productCategory != 0 {
			productCategoryList = append(productCategoryList, ticket.ProductCategory(productCategory))
		}
	}

	getTicketsReq := &ticket.GetSupportTicketsRequest{
		TicketFilters: &ticket.TicketFilters{
			StatusList:          statusList,
			ToTime:              timestampPb.Now(),
			ActorIdList:         []string{req.GetAuth().GetActorId()},
			ProductCategoryList: productCategoryList,
		},
		PageContextRequest: &rpc.PageContextRequest{
			PageSize: s.genConf.CxLandingPageV2Config().MaxNumberOfTicketsToFetch(),
		},
	}

	ticketsResp, err := s.ticketClient.GetSupportTickets(ctx, getTicketsReq)
	if te := epifigrpc.RPCError(ticketsResp, err); te != nil {
		logger.Error(ctx, "error calling backend FAQ service", zap.Error(te))
		return nil
	}

	allTickets := ticketsResp.GetTickets()
	incidentDetails := make(chan IncidentSectionWithMeta, len(allTickets))

	errGroup, errGroupCtx := errgroup.WithContext(ctx)
	for _, ticketDetail := range allTickets {
		td := ticketDetail
		errGroup.Go(func() error {
			ticketCreationDate := datetime.TimestampToDateInLoc(td.GetCreatedAt(), datetime.IST)
			ticketCreationDateString := strings.ToUpper(datetime.DateToString(ticketCreationDate, "02 Jan", datetime.IST))
			screenOptions, getScreenOptionsErr := deeplinkv3.GetScreenOptionV2(&cxDlOptions.CXTicketDetailScreenOptions{TicketId: td.GetId()})
			if getScreenOptionsErr != nil {
				logger.Error(ctx, "error getting screen options for ticket detail", zap.Error(getScreenOptionsErr), zap.Int64("ticketId", td.GetId()))
				return nil
			}
			ticketDetailsDl := &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_CX_TICKET_DETAIL_SCREEN,
				ScreenOptionsV2: screenOptions,
			}
			td.GetCustomFields().GetExpectedResolutionDate()
			ticketTitle := td.GetSubject()
			ticketDescription := td.GetDescriptionText()

			isTicketAutoId := isTicketAutoID(td)
			if isTicketAutoId {
				ticketTransformationResp, getTicketTransformationErr := s.ticketClient.GetCategoryTransformation(errGroupCtx, &ticket.GetCategoryTransformationsRequest{
					Ticket: td,
				})

				if te := epifigrpc.RPCError(ticketTransformationResp, getTicketTransformationErr); te != nil {
					logger.Error(ctx, "error getting ticket transformation", zap.Error(te), zap.Int64("ticketId", td.GetId()))
				}

				ticketDescription = ticketTransformationResp.GetDescription()
				ticketTitle = ticketTransformationResp.GetTitle()
			}

			incidentWidgetSection := &contactUsPb.IncidentWidgetSection{
				TopSection: &contactUsPb.IncidentWidgetSection_TopSection{
					LeftImage: commontypes.GetVisualElementFromUrlHeightAndWidth(constants.IncidentTicketIconUrl3x, 44, 44),
					TitleTag: &typesUiPb.VerticalKeyValuePair{
						Title:                        typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(ticketTitle, colors.ColorGreyV2, commontypes.FontStyle_SUBTITLE_S).WithMaxLines(1).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
						Value:                        typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%s%s", ticketCreationDateString, " • TICKET"), colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS)),
						VerticalPaddingBtwTitleValue: 8,
						HAlignment:                   typesUiPb.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
					},
					RightItc: typesUiPb.NewITC().WithRightImageUrlHeightAndWidth(constants.IncidentTopSectionRightChevronUrl, 20, 20).WithDeeplink(ticketDetailsDl),
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: colors.ColorSnow},
					},
				},
				MiddleSection: &contactUsPb.IncidentWidgetSection_MiddleSection{
					MiddleItc: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Assigned to expert", colors.ColorMoss700, commontypes.FontStyle_SUBTITLE_XS)).WithLeftImageUrlHeightAndWidth(constants.IncidentUserCheckIconUrl, 20, 20),
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: colors.ColorSnow},
					},
				},
				BottomSection: &contactUsPb.IncidentWidgetSection_BottomSection{
					PrimaryCta: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(primaryCtaText, colors.ColorSnow, commontypes.FontStyle_BUTTON_S)).WithRightImageUrlHeightAndWidth(constants.IncidentRightChevronIconUrl, 20, 20).WithContainerCornerRadius(40).WithContainerBackgroundColor(colors.ColorLightPrimaryAction).WithContainerPadding(8, 8, 8, 12).WithDeeplink(ticketDetailsDl),
					SecondaryCta: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("See FAQs", colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_S)).WithLeftImageUrlHeightAndWidth(constants.IncidentAnnotationDotsIconUrl, 20, 20).WithContainerCornerRadius(40).WithContainerBackgroundColor(colors.ColorSnow).WithContainerPadding(8, 12, 8, 12).WithDeeplink(&deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_CX_CATEGORY_SEARCH_SCREEN,
					}),
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#EDF5EB"},
					},
				},
				BorderColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: colors.ColorWater},
				},
				EventProperties: getEventPropertiesForIncidentWidget(td, screenName),
			}

			if !isTicketAutoId {
				ticketDescription = ""
			}

			if len(ticketDescription) > 0 {
				incidentWidgetSection.BottomSection.Description = commontypes.GetTextFromStringFontColourFontStyle(ticketDescription, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S).WithMaxLines(3)
			}

			// Determine the date to use for sorting and set priority
			sortDate := td.GetCreatedAt().AsTime()
			status := td.GetStatus()
			priority := getPriorityFromTicketStatus(status)

			if status == ticket.Status_STATUS_CLOSED || status == ticket.Status_STATUS_RESOLVED {
				isEligibleForDisplay := checkIfTicketIsEligibleForDisplay(td, isTicketAutoId)
				statusStr := getStatusStrFromTicketStatus(status)
				if isEligibleForDisplay {
					incidentWidgetSection.TopSection.TitleTag.Value = typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%s%s", ticketCreationDateString, statusStr), colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS))
					incidentWidgetSection.MiddleSection = nil
					incidentDetails <- IncidentSectionWithMeta{
						Widget:   incidentWidgetSection,
						Date:     td.GetUpdatedAt().AsTime(),
						Priority: priority,
					}
				}
				return nil
			}

			incidentDetails <- IncidentSectionWithMeta{
				Widget:   incidentWidgetSection,
				Date:     sortDate,
				Priority: priority,
			}
			return nil
		})
	}

	if err = errGroup.Wait(); err != nil {
		logger.Error(ctx, "error fetching tickets for CX Landing Page V2", zap.Error(err))
	}
	close(incidentDetails)

	// Collect all incident widgets with meta
	incidentWidgets := make([]IncidentSectionWithMeta, 0, len(allTickets))
	for incidentDetail := range incidentDetails {
		incidentWidgets = append(incidentWidgets, incidentDetail)
	}

	// Sort by priority, then by date descending
	sort.SliceStable(incidentWidgets, func(i, j int) bool {
		if incidentWidgets[i].Priority != incidentWidgets[j].Priority {
			return incidentWidgets[i].Priority < incidentWidgets[j].Priority // lower priority value comes first
		}
		return incidentWidgets[i].Date.After(incidentWidgets[j].Date) // latest date first
	})

	var sections []*contactUsPb.IncidentWidgetSection
	for _, meta := range incidentWidgets {
		sections = append(sections, meta.Widget)
	}

	return sections
}

func isTicketAutoID(ticket *ticket.Ticket) bool {
	return lo.Contains(ticket.GetTags(), autoIDText)
}

func getEventPropertiesForIncidentWidget(ticket *ticket.Ticket, screenName string) map[string]string {
	currentTicketType := "MANUAL"
	if isTicketAutoID(ticket) {
		currentTicketType = "Auto ID"
	}
	ticketIdStr := strconv.Itoa(int(ticket.GetId()))

	return map[string]string{
		ticketId:   ticketIdStr,
		l1Category: ticket.GetCustomFieldWithValue().GetProductCategory(),
		l2CATEGORY: ticket.GetCustomFieldWithValue().GetProductCategoryDetails(),
		l3CATEGORY: ticket.GetCustomFieldWithValue().GetSubCategory(),
		ctaText:    primaryCtaText,
		ticketType: currentTicketType,
		source:     screenName,
	}
}

// buildHelpSections builds the help sections including FAQ widget
func (s *Service) buildHelpSections(ctx context.Context, req *contactUsPb.GetContactUsLandingScreenV2Request) []*contactUsPb.HelpSections {
	type faqRespWithPriority struct {
		resp     *faq.GetFAQByIdResponse
		priority int
	}

	faqDetails := s.genConf.CxLandingPageV2Config().HelpSectionConfig().FAQDetails
	faqDetailsChan := make(chan faqRespWithPriority, len(faqDetails))
	errGroup, errGroupCtx := errgroup.WithContext(ctx)

	for _, faqDetail := range faqDetails {
		errGroup.Go(func() error {
			faqId := faqDetail.FAQId
			faqType, ok := faq.FAQType_value[faqDetail.FAQType]
			if !ok {
				return nil
			}
			priority := int(faqDetail.Priority)
			resp, err := s.faqClient.GetFAQById(errGroupCtx, &faq.GetFAQByIdRequest{
				Auth:    req.GetReq().GetAuth(),
				Req:     req.GetReq(),
				FaqId:   faqId,
				FaqType: faq.FAQType(faqType),
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				logger.Error(errGroupCtx, "error calling backend FAQ service", zap.Error(te))
				return te
			}
			faqDetailsChan <- faqRespWithPriority{resp: resp, priority: priority}
			return nil
		})
	}

	if err := errGroup.Wait(); err != nil {
		logger.Error(ctx, "error fetching FAQ details for CX Landing Page V2", zap.Error(err))
	}

	close(faqDetailsChan)
	var respWithPriority []faqRespWithPriority
	for faqDetail := range faqDetailsChan {
		respWithPriority = append(respWithPriority, faqDetail)
	}

	// Sort by priority (More the priority value, higher the priority)
	sort.SliceStable(respWithPriority, func(i, j int) bool {
		return respWithPriority[i].priority > respWithPriority[j].priority
	})

	var sortedResp []*faq.GetFAQByIdResponse
	for _, item := range respWithPriority {
		sortedResp = append(sortedResp, item.resp)
	}

	return []*contactUsPb.HelpSections{
		{
			Section: &contactUsPb.HelpSections_FaqWidgetSection{
				FaqWidgetSection: &contactUsPb.FAQWidgetSection{
					BorderColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: colors.ColorLightLayer1,
						},
					},
					Title:           commontypes.GetTextFromStringFontColourFontStyle(v2FaqSectionTitle, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_DISPLAY_M),
					FaqItems:        s.buildFAQItems(sortedResp),
					EventProperties: getEventPropertiesForFAQWidget(),
				},
			},
		},
	}
}

func getEventPropertiesForFAQWidget() map[string]string {
	return map[string]string{
		"SOURCE": contactUsLandingScreenV2,
	}
}

// buildFAQItems builds the FAQ items list
func (s *Service) buildFAQItems(faqResp []*faq.GetFAQByIdResponse) []*contactUsPb.FAQWidgetSection_FAQListItem {
	var (
		items      []*contactUsPb.FAQWidgetSection_FAQListItem
		categories []*faq.Category
	)
	for _, faqDetail := range faqResp {
		switch faqDetail.GetFaq().(type) {
		case *faq.GetFAQByIdResponse_Article:
			if article := faqDetail.GetArticle(); article != nil {
				items = append(items, &contactUsPb.FAQWidgetSection_FAQListItem{
					Item: &contactUsPb.FAQWidgetSection_FAQListItem_FaqItem{
						FaqItem: &contactUsPb.FAQWidgetSection_FAQListItem_FAQItem{
							LeftIcon:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-white-annotation-dots.png", 24, 24),
							FaqTitle:  commontypes.GetTextFromStringFontColourFontStyle(article.GetTitle(), colors.ColorDarkLayer2, commontypes.FontStyle_SUBTITLE_S),
							RightIcon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-right-grey-chevron.png", 24, 24),
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_FAQ_ARTICLE,
								ScreenOptions: &deeplinkPb.Deeplink_FaqArticleOptions{
									FaqArticleOptions: &deeplinkPb.FaqArticleOptions{
										ArticleId: strconv.FormatInt(article.GetId(), 10),
									},
								},
							},
						},
					},
				})
			}
		case *faq.GetFAQByIdResponse_Category:
			if category := faqDetail.GetCategory(); category != nil {
				category.BackgroundColor = colors.ColorOnDarkHighEmphasis
				categories = append(categories, category)
			}
		}
	}

	categories = append(categories, &faq.Category{
		BackgroundColor: colors.ColorOnDarkHighEmphasis,
		TitleV2:         commontypes.GetTextFromStringFontColourFontStyle("See More", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_S),
		VisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-right-chevron-category.png", 24, 24),
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CX_CATEGORY_SEARCH_SCREEN,
		},
	})

	items = append(items, &contactUsPb.FAQWidgetSection_FAQListItem{
		Item: &contactUsPb.FAQWidgetSection_FAQListItem_CategoryListItem_{
			CategoryListItem: &contactUsPb.FAQWidgetSection_FAQListItem_CategoryListItem{
				Categories: categories,
			},
		},
	})

	return items
}

// buildFloatingIcon builds the floating chat icon
func (s *Service) buildFloatingIcon() *contactUsPb.FloatingIcon {
	return &contactUsPb.FloatingIcon{
		Icon: &home.Icon{
			Title:      commontypes.GetTextFromStringFontColourFontStyle("Chat with us", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M),
			ActionType: home.Icon_ACTION_TYPE_DEEPLINK,
			Action: &home.Icon_Deeplink{
				Deeplink: s.getQueryScreenDeeplink(),
			},
			BgColourV2:    widget.GetLinearGradientBackgroundColour(45, []*widget.ColorStop{{Color: "#006D5B", StopPercentage: 0}, {Color: "#00B899", StopPercentage: 100}}),
			VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-message.png", 32, 32),
		},
	}
}

// buildSeeMoreButton builds the see more button
func buildSeeMoreButton() *typesUiPb.IconTextComponent {
	return typesUiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle("SEE MORE", colors.ColorLightPrimaryAction, commontypes.FontStyle_OVERLINE_XS_CAPS)).
		WithRightImageUrlHeightAndWidth("https://epifi-icons.pointz.in/cx/inapphelp/landing-page-v2-down-chevron.png", 20, 20).
		WithContainerCornerRadius(40).
		WithContainerBackgroundColor(colors.ColorDarkLayer1).
		WithContainerPadding(8, 8, 8, 16).
		WithDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_OPEN_INCIDENTS_SCREEN,
		})
}

func (s *Service) getQueryScreenDeeplink() *deeplinkPb.Deeplink {
	queryValidationFailureMessage := fmt.Sprintf(s.contactUsConf.QueryValidationFailureMessage(),
		strconv.FormatInt(s.contactUsConf.MinQueryLength(), 10), strconv.FormatInt(s.contactUsConf.MinWordCount(), 10))
	validationFailureTextComponent := commontypes.GetTextFromStringFontColourFontStyleFontAlignment(queryValidationFailureMessage,
		"#E795AE", commontypes.FontStyle_BODY_XS, commontypes.Text_ALIGNMENT_LEFT)
	screenTitle := commontypes.GetTextFromStringFontColourFontStyleFontAlignment(landingScreenTitle, colors.ColorLead,
		commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT)

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONTACT_US_QUERY_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&cxDlOptions.CXHelpQueryScreenOptions{
			ScreenTitle: screenTitle,
			QueryValidationConfig: &contactUsPb.QueryValidationConfig{
				MinCharacterCount:    s.contactUsConf.MinQueryLength(),
				MaxCharacterCount:    s.contactUsConf.MaxQueryLength(),
				MinWordCount:         s.contactUsConf.MinWordCount(),
				ValidationFailureMsg: validationFailureTextComponent},
		})}
}

func getPriorityFromTicketStatus(status ticket.Status) int {
	switch status {
	case ticket.Status_STATUS_RESOLVED:
		return 2
	case ticket.Status_STATUS_CLOSED:
		return 1
	}
	return 0
}

func getStatusStrFromTicketStatus(status ticket.Status) string {
	switch status {
	case ticket.Status_STATUS_RESOLVED:
		return " • RESOLVED"
	case ticket.Status_STATUS_CLOSED:
		return " • CLOSED"
	}
	return " • OPEN"
}

func checkIfTicketIsEligibleForDisplay(td *ticket.Ticket, isTicketAutoID bool) bool {
	isNotEligible := (isTicketAutoID && time.Since(td.GetUpdatedAt().AsTime()) > 24*time.Hour) || (!isTicketAutoID && time.Since(td.GetUpdatedAt().AsTime()) > 48*time.Hour)
	return !isNotEligible
}
