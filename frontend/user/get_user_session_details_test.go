package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/test"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	bcPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountEnums "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/connected_account/external"
	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	feAccounts "github.com/epifi/gamma/api/frontend/account"
	"github.com/epifi/gamma/api/frontend/account/enums"
	feUpiPb "github.com/epifi/gamma/api/frontend/account/upi"
	"github.com/epifi/gamma/api/frontend/analytics"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/smsscanner"
	pb "github.com/epifi/gamma/api/frontend/user"
	kycPb "github.com/epifi/gamma/api/kyc"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/upi"
	onboarding2 "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	bePb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendormapping"
	headerPkg "github.com/epifi/gamma/pkg/frontend/header"
)

func TestService_GetUserSessionDetails(t *testing.T) {
	var (
		req   *header.RequestHeader
		actId = "actor1"
	)
	type args struct {
		req *pb.GetUserSessionDetailsRequest
	}
	ctxB := context.WithValue(epificontext.CtxWithActorId(context.Background(), actId), epificontext.CtxUserEmailKey, "<EMAIL>")
	req = &header.RequestHeader{
		Auth: &header.AuthHeader{
			ActorId: actId,
		},
		AppVersionCode: 02,
	}
	bcClientReq := &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actId},
	}
	bcClientResp := &bcPb.GetBankCustomerResponse{
		Status: rpc.StatusOk(),
		BankCustomer: &bcPb.BankCustomer{
			ActorId:          actId,
			VendorCustomerId: "referenceId",
			Vendor:           commonvgpb.Vendor_FEDERAL_BANK,
			KycInfo: &bcPb.KYCInfo{
				KycLevel: kycPb.KYCLevel_FULL_KYC,
			},
		},
	}
	userRes := &bePb.GetUserResponse{
		User: &bePb.User{
			Id: "userId",
			Profile: &bePb.Profile{
				KycName: &commontypes.Name{
					FirstName: "User Kyc",
				},
				Email: "<EMAIL>",
			},
		},
		Status: rpc.StatusOk(),
	}
	accountIdRes := &ffAccPb.GetAccountResponse{
		Account: &ffAccPb.CreditAccount{
			Id: "accountId",
		},
		Status: rpc.StatusOk(),
	}
	cardIdRes := &firefly.GetCreditCardResponse{
		CreditCard: &firefly.CreditCard{
			BasicInfo: &firefly.BasicInfo{
				MaskedCardNumber: "1234XXXXXXXX5678",
			},
		},
		Status: rpc.StatusOk(),
	}
	onboardingRes := &onboarding.GetDetailsResponse{
		Status: rpc.StatusOk(),
		Details: &onboarding.OnboardingDetails{
			CompletedAt: timestamp.New(time.Now()),
			StageMetadata: &onboarding.StageMetadata{
				IntentSelectionMetadata: &onboarding.IntentSelectionMetadata{
					Selection: onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
				},
			},
		},
	}
	getFeatDetailsRes := &onboarding.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
	}
	vmResp := &vendormapping.GetBEMappingByIdResponse{
		FcmId:       "firehoseId",
		MsClarityId: "msClarityId",
		Status:      rpc.StatusOk(),
	}
	savingsRes := &savings.GetAccountResponse{
		Account: &savings.Account{
			Id:        "accountId",
			State:     savings.State_CREATED,
			AccountNo: "1234XXX5432",
		},
	}
	savingsAccountsEssentialsSingleAcc := []*savings.SavingsAccountEssentials{
		{
			Id:        "accountId",
			State:     savings.State_CREATED,
			AccountNo: "1234XXX5432",
			SkuInfo: &savings.SKUInfo{
				AccountProductOffering: accountTypesPb.AccountProductOffering_APO_REGULAR,
			},
		},
	}
	savingsAccountsEssentialsMultiAcc := []*savings.SavingsAccountEssentials{
		{
			Id:        "accountId",
			State:     savings.State_CREATED,
			AccountNo: "1234XXX5432",
		},
		{
			Id:        "accountId2",
			State:     savings.State_CREATED,
			AccountNo: "2234XXX5432",
			SkuInfo:   &savings.SKUInfo{AccountProductOffering: accountTypesPb.AccountProductOffering_APO_NRO},
		},
	}
	groupsMappedToEmailRes := &group.GetGroupsMappedToEmailResponse{
		Status: rpc.StatusOk(),
		Groups: []commontypes.UserGroup{
			commontypes.UserGroup_INTERNAL,
			commontypes.UserGroup_CREDIT_CARD_INTERNAL,
		},
	}
	pinStatusRes := &upi.PinStatusResponse{
		Status: rpc.StatusOk(),
		AccountPinStateMap: map[string]upi.PinSetState{
			savingsRes.GetAccount().GetId(): upi.PinSetState_PIN_SET,
		},
	}
	actorRes := &actor.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor: &types.Actor{
			Id: req.GetAuth().GetActorId(),
		},
	}
	tpapRes := &onboarding2.GetAccountsResponse{
		Status: rpc.StatusOk(),
		Accounts: []*onboarding2.UpiAccount{
			{
				Id:           "upiId1",
				ActorId:      actorRes.GetActor().GetId(),
				AccountRefId: savingsRes.GetAccount().GetId(),
				AccountType:  accounts.Type_FIXED_DEPOSIT,
				PinSetStatus: upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
			},
		},
	}
	getCardGroupsRes := &cardPb.GetCardGroupsResponse{
		Status: rpc.StatusOk(),
		Cards: []*card.Card{
			{
				Id: "cardId1",
			},
		},
	}
	connectedAccountsRes := &connectedAccountPb.GetAccountsResponse{
		Status: rpc.StatusOk(),
		AccountDetailsList: []*external.AccountDetails{
			{
				AccountId:           "accountId",
				MaskedAccountNumber: "XXXXX1234",
			},
		},
	}

	// fill firebase properties
	firebaseUserProperties := map[string]string{
		pb.FirebaseProperty_KYC_LEVEL.String(): bcClientResp.GetBankCustomer().GetKycInfo().GetKycLevel().String(),
	}
	firebaseUserProperties[pb.FirebaseProperty_IS_INTERNAL_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_FNF_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_CONNECTED_ACC_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_INVESTMENT_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_P2P_INVESTMENT_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_SALARY_PROGRAM_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_PREAPPROVED_LOAN_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_CX_INTERNAL_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_INVEST_LANDING_INT.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_CREDIT_CARD_ENABLED.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_USSTOCKS_ENABLED.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_IS_FI_LITE_USER.String()] = FalseString
	firebaseUserProperties[pb.FirebaseProperty_VERSION_CODE.String()] = strconv.FormatInt(int64(req.GetAppVersionCode()), 10)

	setFireBaseUserProps(groupsMappedToEmailRes.GetGroups(), firebaseUserProperties)
	// release evaluator always true in the test
	firebaseUserProperties[pb.FirebaseProperty_IS_INVESTMENT_USER.String()] = TrueString
	firebaseUserProperties[pb.FirebaseProperty_IS_PREAPPROVED_LOAN_USER.String()] = TrueString

	// ms clarity config
	msClarityConfig := &analytics.MsClarityConfig{
		EnableSdk:            true,
		AllowedActivityNames: []string{},
		AllowedScreenNames:   []string{},
	}

	// scienaptic config
	scienapticSmsScannerConfig := &smsscanner.ScienapticSmsScannerConfig{
		EnableSdk: true,
	}

	// feature map
	featureMap := make(map[string]*pb.FeatureInfo)
	for _, featureVal := range types.Feature_value {
		// Ignoring older features for backward compatibility
		if types.Feature(featureVal) == types.Feature_FEATURE_UNSPECIFIED ||
			types.Feature(featureVal) == types.Feature_INVESTMENT_MF_UI ||
			types.Feature(featureVal) == types.Feature_PAY_VIA_PHONE_NUMBER {
			continue
		}
		info := &pb.FeatureInfo{Enable: true}
		// disabling home design enhancements for local testing
		if types.Feature(featureVal) == types.Feature_FEATURE_HOME_DESIGN_ENHANCEMENTS ||
			types.Feature(featureVal) == types.Feature_FEATURE_REWARDS_CATALOG_MERGED_PAGE {
			info.Enable = false
		}
		featureMap[types.Feature(featureVal).String()] = info
	}

	// convert tpap account to fe account
	internalAccount := savingsRes.GetAccount()
	derivedAccountIdString, _ := idgen.EncodeProtoToStdBase64(&accounts.DerivedAccountId{
		TpapAccountId:     tpapRes.GetAccounts()[0].GetId(),
		InternalAccountId: internalAccount.GetId(),
	})

	tpapFeAccount := &feAccounts.Account{
		AccountId:           internalAccount.GetId(),
		DerivedAccountId:    derivedAccountIdString,
		MaskedAccountNumber: tpapRes.GetAccounts()[0].GetMaskedAccountNumber(),
		AccountType:         tpapRes.GetAccounts()[0].GetAccountType(),
		Capabilities: []feAccounts.AccountCapability{
			feAccounts.AccountCapability_CREDIT,
			feAccounts.AccountCapability_DEBIT,
		},
		AccountProvenances: []enums.AccountProvenance{enums.AccountProvenance_ACCOUNT_PROVENANCE_TPAP, enums.AccountProvenance_ACCOUNT_PROVENANCE_INTERNAL},
		IfscCode:           tpapRes.GetAccounts()[0].GetIfscCode(),
		PinSetInfo: &feAccounts.PinSetInfo{
			PinSetState: feUpiPb.PinSetState_PIN_SET,
			Action:      nil,
		},
		Identifier: &feAccounts.Account_SavingsAccount{
			SavingsAccount: &feAccounts.SavingsAccount{
				PinSetState: feUpiPb.PinSetState_PIN_SET,
				Action:      nil,
			},
		},
		ConnectedAccount: &feAccounts.ConnectedAccount{},
		BankLogoUrl:      conf.BankIcon.FederalBankUrl,
		PartnerBank:      (*conf.VendorToBankNameMap)[internalAccount.GetPartnerBank().String()],
		IsPrimaryAccount: false,
	}

	derivedAccountIdProto := &accounts.DerivedAccountId{
		CreditCardAccountId: accountIdRes.GetAccount().GetId(),
	}
	derivedAccountIdStringCC, _ := idgen.EncodeProtoToStdBase64(derivedAccountIdProto)

	// fill merged accounts
	mergedAccounts := []*feAccounts.Account{
		tpapFeAccount,
		// added from getCreditAccounts
		{
			AccountId: accountIdRes.GetAccount().GetId(),
			// we will show card number for credit card transactions
			MaskedAccountNumber: "••5678",
			BankLogoUrl:         conf.BankIcon.FederalFiIconUrl,
			AccountType:         accounts.Type_CREDIT_CARD_ACCOUNT,
			Capabilities: []feAccounts.AccountCapability{
				feAccounts.AccountCapability_CREDIT,
				feAccounts.AccountCapability_DEBIT,
			},
			DerivedAccountId: derivedAccountIdStringCC,
			AccountProvenances: []enums.AccountProvenance{
				enums.AccountProvenance_ACCOUNT_PROVENANCE_CREDIT_CARD_ACCOUNT,
			},
			PartnerBank: "Fi-Federal Cobranded Credit Card",
		},
	}

	internalNonTPAPAccount := savingsAccountsEssentialsMultiAcc[1]
	internalNonTPAPDerivedAccId, _ := idgen.EncodeProtoToStdBase64(&accounts.DerivedAccountId{
		InternalAccountId: internalNonTPAPAccount.GetId(),
	})
	mergedAccountsMultiInternal := []*feAccounts.Account{
		// internal non-tpap account
		{
			AccountId:              internalNonTPAPAccount.GetId(),
			MaskedAccountNumber:    mask.GetMaskedAccountNumber(internalNonTPAPAccount.GetAccountNo(), ""),
			PartnerBank:            (*conf.VendorToBankNameMap)[internalNonTPAPAccount.GetPartnerBank().String()],
			BankLogoUrl:            conf.BankIcon.FederalFiIconUrl,
			AccountType:            accounts.Type_SAVINGS,
			AccountProductOffering: accountTypesPb.AccountProductOffering_APO_NRO,
			Capabilities: []feAccounts.AccountCapability{
				feAccounts.AccountCapability_CREDIT,
				feAccounts.AccountCapability_DEBIT,
			},
			Identifier: &feAccounts.Account_SavingsAccount{
				SavingsAccount: &feAccounts.SavingsAccount{
					PinSetState: feUpiPb.PinSetState_PIN_SET,
				},
			},
			IsPrimaryAccount: true,
			DerivedAccountId: internalNonTPAPDerivedAccId,
			AccountProvenances: []enums.AccountProvenance{
				enums.AccountProvenance_ACCOUNT_PROVENANCE_INTERNAL,
			},
			PinSetInfo: &feAccounts.PinSetInfo{
				PinSetState: feUpiPb.PinSetState_PIN_SET,
			},
			IfscCode: internalNonTPAPAccount.GetIfscCode(),
		},
		// tpap accounts
		tpapFeAccount,
		// added from getCreditAccounts
		{
			AccountId: accountIdRes.GetAccount().GetId(),
			// we will show card number for credit card transactions
			MaskedAccountNumber: "••5678",
			BankLogoUrl:         conf.BankIcon.FederalFiIconUrl,
			AccountType:         accounts.Type_CREDIT_CARD_ACCOUNT,
			Capabilities: []feAccounts.AccountCapability{
				feAccounts.AccountCapability_CREDIT,
				feAccounts.AccountCapability_DEBIT,
			},
			DerivedAccountId: derivedAccountIdStringCC,
			AccountProvenances: []enums.AccountProvenance{
				enums.AccountProvenance_ACCOUNT_PROVENANCE_CREDIT_CARD_ACCOUNT,
			},
			PartnerBank: "Fi-Federal Cobranded Credit Card",
		},
	}

	mocksGoGroup1 := func(args args, md *mockedDependencies) {
		md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), &bcPb.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bcPb.GetBankCustomerRequest_ActorId{
				ActorId: args.req.GetReq().GetAuth().GetActorId(),
			},
		}).Return(bcClientResp, nil).MaxTimes(1)
		// get credit card accounts
		md.ffAccountingClient.EXPECT().GetAccount(gomock.Any(), &ffAccPb.GetAccountRequest{
			GetBy: &ffAccPb.GetAccountRequest_ByActorIdAndRefId{
				ByActorIdAndRefId: &ffAccPb.GetAccountRequest_ActorIdAndRefId{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					ReferenceId: bcClientResp.GetBankCustomer().GetVendorCustomerId(),
				},
			},
		}).MaxTimes(1).Return(accountIdRes, nil)
		md.ffClient.EXPECT().GetCreditCard(gomock.Any(), &firefly.GetCreditCardRequest{
			GetBy: &firefly.GetCreditCardRequest_ActorId{
				ActorId: args.req.GetReq().GetAuth().GetActorId(),
			},
		}).MaxTimes(1).Return(cardIdRes, nil)

		md.vendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormapping.GetBEMappingByIdRequest{
			Id: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(vmResp, nil)
		md.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
			CachedData: true,
			ActorId:    "actor1",
		}).MaxTimes(1).Return(onboardingRes, nil)
		md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).MaxTimes(1).Return(getFeatDetailsRes, nil)
	}
	mocksGoGroup2 := func(args args, md *mockedDependencies) {

		md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), bcClientReq).AnyTimes().Return(bcClientResp, nil)
		// get feature map config and get firebase properties
		md.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil)

		md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(5).Return(&onboarding2.IsTpapEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsMapperEnabledForActor(gomock.Any(), &onboarding2.IsMapperEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsMapperEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
			Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savings.ActorIdBankFilter{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
			Status: rpc.StatusOk(),
		}, nil)
	}
	vpaCreationGroup := func(args args, md *mockedDependencies) {
		// vpa
		md.accountPiClient.EXPECT().GetPiByAccountId(gomock.Any(), &accountPiPb.GetPiByAccountIdRequest{
			AccountId:   savingsRes.GetAccount().GetId(),
			AccountType: accounts.Type_SAVINGS,
			PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
		}).MaxTimes(1).Return(&accountPiPb.GetPiByAccountIdResponse{
			Status: rpc.StatusOk(),
		}, nil)

		md.upiOnboardingClient.EXPECT().LinkInternalAccount(gomock.Any(), newLinkInternalAccountRequestMatcher(&onboarding2.LinkInternalAccountRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		})).MaxTimes(1).Return(&onboarding2.LinkInternalAccountResponse{
			Status: rpc.StatusOk(),
		}, nil)
	}
	vpaCreationGroupMultiInternalAccounts := func(args args, md *mockedDependencies) {
		// vpa
		md.accountPiClient.EXPECT().GetPiByAccountId(gomock.Any(), test.NewProtoArgMatcher(&accountPiPb.GetPiByAccountIdRequest{
			AccountId:   savingsAccountsEssentialsMultiAcc[0].GetId(),
			AccountType: accounts.Type_SAVINGS,
			PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
		})).Return(&accountPiPb.GetPiByAccountIdResponse{
			Status: rpc.StatusOk(),
		}, nil)
		md.accountPiClient.EXPECT().GetPiByAccountId(gomock.Any(), test.NewProtoArgMatcher(&accountPiPb.GetPiByAccountIdRequest{
			AccountId:   savingsAccountsEssentialsMultiAcc[1].GetId(),
			AccountType: accounts.Type_SAVINGS,
			PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
		})).Return(&accountPiPb.GetPiByAccountIdResponse{
			Status: rpc.StatusOk(),
		}, nil)

		md.upiOnboardingClient.EXPECT().LinkInternalAccount(gomock.Any(), newLinkInternalAccountRequestMatcher(&onboarding2.LinkInternalAccountRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		})).MaxTimes(2).Return(&onboarding2.LinkInternalAccountResponse{
			Status: rpc.StatusOk(),
		}, nil)
	}
	mocksGoGroup3 := func(args args, md *mockedDependencies) {
		vpaCreationGroup(args, md)
		// get pin status
		md.upiClient.EXPECT().GetPinStatus(gomock.Any(), &upi.PinStatusRequest{
			CurrentActorId: args.req.GetReq().GetAuth().GetActorId(),
			AccountIds:     []string{savingsRes.GetAccount().GetId()},
		}).MaxTimes(1).Return(pinStatusRes, nil)

		// connected accounts
		md.connectAccountsClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
			ActorId:           args.req.GetReq().GetAuth().GetActorId(),
			AccountFilterList: defaultConnectedAccountFilters,
			AccInstrumentTypeList: []connectedAccountEnums.AccInstrumentType{
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT},
		}).MaxTimes(1).Return(connectedAccountsRes, nil)

		// tpap enabled check
		md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(3).Return(&onboarding2.IsTpapEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsMapperEnabledForActor(gomock.Any(), &onboarding2.IsMapperEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsMapperEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
			Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savings.ActorIdBankFilter{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
			Status: rpc.StatusOk(),
		}, nil)

		md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		// tpap accounts
		md.upiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), &onboarding2.GetAccountsRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
			AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE},
		}).MaxTimes(1).Return(tpapRes, nil)
	}

	mocksGoGroup1Done := func(args args, md *mockedDependencies) {
		md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), &bcPb.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bcPb.GetBankCustomerRequest_ActorId{
				ActorId: args.req.GetReq().GetAuth().GetActorId(),
			},
		}).Return(bcClientResp, nil)
		// get credit card accounts
		md.ffAccountingClient.EXPECT().GetAccount(gomock.Any(), &ffAccPb.GetAccountRequest{
			GetBy: &ffAccPb.GetAccountRequest_ByActorIdAndRefId{
				ByActorIdAndRefId: &ffAccPb.GetAccountRequest_ActorIdAndRefId{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					ReferenceId: bcClientResp.GetBankCustomer().GetVendorCustomerId(),
				},
			},
		}).Return(accountIdRes, nil)
		md.ffClient.EXPECT().GetCreditCard(gomock.Any(), &firefly.GetCreditCardRequest{
			GetBy: &firefly.GetCreditCardRequest_ActorId{
				ActorId: args.req.GetReq().GetAuth().GetActorId(),
			},
		}).Return(cardIdRes, nil)

		md.vendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormapping.GetBEMappingByIdRequest{
			Id: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(vmResp, nil)

		md.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
			CachedData: true,
			ActorId:    "actor1",
		}).Return(onboardingRes, nil)

		md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(getFeatDetailsRes, nil)
	}
	mocksGoGroup2Done := func(md *mockedDependencies) {

		md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), bcClientReq).Return(bcClientResp, nil)
		// get feature map config and get firebase properties
		md.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil)

	}
	mocksGoGroup3Done := func(args args, md *mockedDependencies) {
		vpaCreationGroup(args, md)

		// get pin status
		md.upiClient.EXPECT().GetPinStatus(gomock.Any(), &upi.PinStatusRequest{
			CurrentActorId: args.req.GetReq().GetAuth().GetActorId(),
			AccountIds:     []string{savingsRes.GetAccount().GetId()},
		}).Return(pinStatusRes, nil)

		// connected accounts
		md.connectAccountsClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
			ActorId:           args.req.GetReq().GetAuth().GetActorId(),
			AccountFilterList: defaultConnectedAccountFilters,
			AccInstrumentTypeList: []connectedAccountEnums.AccInstrumentType{
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT}},
		).Return(connectedAccountsRes, nil)

		// tpap enabled check
		md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId()}).MaxTimes(3).Return(&onboarding2.IsTpapEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsMapperEnabledForActor(gomock.Any(), &onboarding2.IsMapperEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsMapperEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
			Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savings.ActorIdBankFilter{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
			Status: rpc.StatusOk(),
		}, nil)

		md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		// tpap accounts
		md.upiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), &onboarding2.GetAccountsRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
			AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE},
		}).Return(tpapRes, nil)
	}
	mocksGoGroupMultiInternalAccountAccounts := func(args args, md *mockedDependencies) {
		vpaCreationGroupMultiInternalAccounts(args, md)

		// get pin status
		md.upiClient.EXPECT().GetPinStatus(gomock.Any(), test.NewProtoArgMatcher(&upi.PinStatusRequest{
			CurrentActorId: args.req.GetReq().GetAuth().GetActorId(),
			AccountIds:     []string{savingsAccountsEssentialsMultiAcc[0].GetId(), savingsAccountsEssentialsMultiAcc[1].GetId()},
		})).Return(&upi.PinStatusResponse{
			Status: rpc.StatusOk(),
			AccountPinStateMap: map[string]upi.PinSetState{
				savingsAccountsEssentialsMultiAcc[0].GetId(): upi.PinSetState_PIN_SET,
				savingsAccountsEssentialsMultiAcc[1].GetId(): upi.PinSetState_PIN_SET,
			},
		}, nil)

		// connected accounts
		md.connectAccountsClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
			ActorId:           args.req.GetReq().GetAuth().GetActorId(),
			AccountFilterList: defaultConnectedAccountFilters,
			AccInstrumentTypeList: []connectedAccountEnums.AccInstrumentType{
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
				connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT}},
		).Return(connectedAccountsRes, nil)

		// tpap enabled check
		md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId()}).MaxTimes(3).Return(&onboarding2.IsTpapEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsMapperEnabledForActor(gomock.Any(), &onboarding2.IsMapperEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsMapperEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)

		md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
			Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savings.ActorIdBankFilter{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
			Status: rpc.StatusOk(),
		}, nil)

		md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
		}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
			Status:    rpc.StatusOk(),
			IsEnabled: true,
		}, nil)
		// tpap accounts
		md.upiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), &onboarding2.GetAccountsRequest{
			ActorId: args.req.GetReq().GetAuth().GetActorId(),
			AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE},
		}).Return(tpapRes, nil)
	}
	mocksGoGroup4Done := func(md *mockedDependencies) {
		md.cardClient.EXPECT().GetCardGroups(gomock.Any(), &cardPb.GetCardGroupsRequest{
			Actor:               actorRes.GetActor(),
			AscOrderCreatedTime: false,
			NumGroups:           1,
			GetAll:              false,
		}).Return(getCardGroupsRes, nil)
	}

	tests := []struct {
		name      string
		args      args
		want      *pb.GetUserSessionDetailsResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies) *sync.WaitGroup
	}{
		{
			name: "failed: error in getting user",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(&bePb.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				return nil
			},
		},
		{
			name: "failed: error in getting groups mapped to email",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1(args, md)
				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).Return(&group.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				return nil
			},
		},
		{
			name: "failed: error in getting savings accounts",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
					ActorId:     req.GetAuth().GetActorId(),
					Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
				}).MaxTimes(1).Return(&consentPb.FetchConsentResponse{Status: rpc.StatusOk()}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1(args, md)
				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).MaxTimes(2).Return(&group.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mocksGoGroup2(args, md)
				md.dataCollector.EXPECT().GetSavingsAccounts(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
				// tpap accounts
				md.upiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), &onboarding2.GetAccountsRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE},
				}).MaxTimes(1).Return(tpapRes, nil)
				return nil
			},
		},
		{
			name: "failed: error in getting actor details",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
					ActorId:     req.GetAuth().GetActorId(),
					Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
				}).MaxTimes(1).Return(&consentPb.FetchConsentResponse{Status: rpc.StatusOk()}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1(args, md)
				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).MaxTimes(2).Return(groupsMappedToEmailRes, nil)

				mocksGoGroup2(args, md)
				md.dataCollector.EXPECT().GetSavingsAccounts(gomock.Any(), gomock.Any()).Return(savingsAccountsEssentialsSingleAcc, nil)
				mocksGoGroup3(args, md)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				return nil
			},
		},
		{
			name: "failed: error in getting merged accounts",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
					ActorId:     req.GetAuth().GetActorId(),
					Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consentPb.FetchConsentResponse{Status: rpc.StatusOk()}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1Done(args, md)
				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).Times(2).Return(groupsMappedToEmailRes, nil)
				mocksGoGroup2Done(md)

				md.dataCollector.EXPECT().GetSavingsAccounts(gomock.Any(), gomock.Any()).Return(savingsAccountsEssentialsSingleAcc, nil)
				mocksGoGroup3Done(args, md)

				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(actorRes, nil)
				mocksGoGroup4Done(md)

				// get merged accounts
				md.upiOnboardingClient.EXPECT().GetPinStatus(gomock.Any(), &onboarding2.GetPinStatusRequest{
					AccountIds: []string{tpapRes.GetAccounts()[0].GetId()},
				}).Return(&onboarding2.GetPinStatusResponse{
					Status: rpc.StatusOk(),
					UpiAccountPinStatusMap: map[string]upiOnboardingEnumsPb.UpiPinSetStatus{
						tpapRes.GetAccounts()[0].GetId(): upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
					},
				}, nil)

				md.connectAccountsClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), getRequestPayloadToFetchConnectedAccountDetails(connectedAccountsRes.GetAccountDetailsList())).
					Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
						Status: rpc.StatusInternal(),
					}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(3).Return(&onboarding2.IsTpapEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
					Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savings.ActorIdBankFilter{
							ActorId:     args.req.GetReq().GetAuth().GetActorId(),
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				return nil
			},
		},
		{
			name: "success",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status:                     rpc.StatusOk(),
				CardIds:                    []string{getCardGroupsRes.GetCards()[0].GetId()},
				FirebaseId:                 vmResp.GetFcmId(),
				MsClarityId:                vmResp.GetMsClarityId(),
				FirebaseUserProperties:     firebaseUserProperties,
				DisplayName:                userRes.GetUser().GetProfile().GetKycName(),
				RespHeader:                 headerPkg.SuccessRespHeader(),
				Accounts:                   mergedAccounts,
				FeatureMap:                 featureMap,
				IsSaOnboarded:              true,
				IsHomeAccessible:           onboardingRes.GetDetails().GetCompletedAt() != nil || getFeatDetailsRes.GetIsFiLiteUser(),
				CurrentFeature:             onboardingRes.GetDetails().GetStageMetadata().GetIntentSelectionMetadata().GetSelection().String(),
				MsClarityConfig:            msClarityConfig,
				ScienapticSmsScannerConfig: scienapticSmsScannerConfig,
				CardTabs:                   getCardTabs(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
					ActorId:     req.GetAuth().GetActorId(),
					Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consentPb.FetchConsentResponse{Status: rpc.StatusOk()}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1Done(args, md)

				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).Times(2).Return(groupsMappedToEmailRes, nil)
				mocksGoGroup2Done(md)
				md.dataCollector.EXPECT().GetSavingsAccounts(gomock.Any(), gomock.Any()).Return(savingsAccountsEssentialsSingleAcc, nil)
				mocksGoGroup3Done(args, md)

				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(actorRes, nil)
				mocksGoGroup4Done(md)

				// get merged accounts
				md.upiOnboardingClient.EXPECT().GetPinStatus(gomock.Any(), &onboarding2.GetPinStatusRequest{
					AccountIds: []string{tpapRes.GetAccounts()[0].GetId()},
				}).Return(&onboarding2.GetPinStatusResponse{
					Status: rpc.StatusOk(),
					UpiAccountPinStatusMap: map[string]upiOnboardingEnumsPb.UpiPinSetStatus{
						tpapRes.GetAccounts()[0].GetId(): upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
					},
				}, nil)

				md.connectAccountsClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), getRequestPayloadToFetchConnectedAccountDetails(connectedAccountsRes.GetAccountDetailsList())).
					Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
						Status: rpc.StatusOk(),
						AccountDetailsMap: map[string]*connectedAccountPb.AccountProfileSummaryDetails{
							savingsRes.GetAccount().GetId(): {
								AccountDetails: &external.AccountDetails{
									AccountId:         savingsRes.GetAccount().GetId(),
									AccInstrumentType: connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
								},
							},
						},
					}, nil)
				md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(4).Return(&onboarding2.IsTpapEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
					Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savings.ActorIdBankFilter{
							ActorId:     args.req.GetReq().GetAuth().GetActorId(),
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				return nil
			},
		},
		{
			name: "success with journey completed",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status:                     rpc.StatusOk(),
				CardIds:                    []string{getCardGroupsRes.GetCards()[0].GetId()},
				FirebaseId:                 vmResp.GetFcmId(),
				MsClarityId:                vmResp.GetMsClarityId(),
				FirebaseUserProperties:     firebaseUserProperties,
				DisplayName:                userRes.GetUser().GetProfile().GetKycName(),
				RespHeader:                 headerPkg.SuccessRespHeader(),
				Accounts:                   mergedAccounts,
				FeatureMap:                 featureMap,
				IsSaOnboarded:              true,
				IsHomeAccessible:           onboardingRes.GetDetails().GetCompletedAt() != nil || getFeatDetailsRes.GetIsFiLiteUser(),
				CurrentFeature:             onboardingRes.GetDetails().GetStageMetadata().GetIntentSelectionMetadata().GetSelection().String(),
				MsClarityConfig:            msClarityConfig,
				ScienapticSmsScannerConfig: scienapticSmsScannerConfig,
				CardTabs:                   getCardTabs(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
					ActorId:     req.GetAuth().GetActorId(),
					Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consentPb.FetchConsentResponse{Status: rpc.StatusOk()}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1Done(args, md)

				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).Times(2).Return(groupsMappedToEmailRes, nil)
				mocksGoGroup2Done(md)
				md.dataCollector.EXPECT().GetSavingsAccounts(gomock.Any(), gomock.Any()).Return(savingsAccountsEssentialsSingleAcc, nil)
				mocksGoGroup3Done(args, md)

				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(actorRes, nil)
				mocksGoGroup4Done(md)

				// get merged accounts
				md.upiOnboardingClient.EXPECT().GetPinStatus(gomock.Any(), &onboarding2.GetPinStatusRequest{
					AccountIds: []string{tpapRes.GetAccounts()[0].GetId()},
				}).Return(&onboarding2.GetPinStatusResponse{
					Status: rpc.StatusOk(),
					UpiAccountPinStatusMap: map[string]upiOnboardingEnumsPb.UpiPinSetStatus{
						tpapRes.GetAccounts()[0].GetId(): upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
					},
				}, nil)

				md.connectAccountsClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), getRequestPayloadToFetchConnectedAccountDetails(connectedAccountsRes.GetAccountDetailsList())).
					Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
						Status: rpc.StatusOk(),
						AccountDetailsMap: map[string]*connectedAccountPb.AccountProfileSummaryDetails{
							savingsRes.GetAccount().GetId(): {
								AccountDetails: &external.AccountDetails{
									AccountId:         savingsRes.GetAccount().GetId(),
									AccInstrumentType: connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
								},
							},
						},
					}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(4).Return(&onboarding2.IsTpapEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
					Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savings.ActorIdBankFilter{
							ActorId:     args.req.GetReq().GetAuth().GetActorId(),
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				return nil
			},
		},
		{
			name: "should successfully return the response with multi-internal accounts scenario",
			args: args{
				req: &pb.GetUserSessionDetailsRequest{
					Req: req,
				},
			},
			want: &pb.GetUserSessionDetailsResponse{
				Status:                     rpc.StatusOk(),
				CardIds:                    []string{getCardGroupsRes.GetCards()[0].GetId()},
				FirebaseId:                 vmResp.GetFcmId(),
				MsClarityId:                vmResp.GetMsClarityId(),
				FirebaseUserProperties:     firebaseUserProperties,
				DisplayName:                userRes.GetUser().GetProfile().GetKycName(),
				RespHeader:                 headerPkg.SuccessRespHeader(),
				Accounts:                   mergedAccountsMultiInternal,
				FeatureMap:                 featureMap,
				IsSaOnboarded:              true,
				IsHomeAccessible:           onboardingRes.GetDetails().GetCompletedAt() != nil || getFeatDetailsRes.GetIsFiLiteUser(),
				CurrentFeature:             onboardingRes.GetDetails().GetStageMetadata().GetIntentSelectionMetadata().GetSelection().String(),
				MsClarityConfig:            msClarityConfig,
				ScienapticSmsScannerConfig: scienapticSmsScannerConfig,
				CardTabs:                   getCardTabs(),
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) *sync.WaitGroup {
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
					ActorId:     req.GetAuth().GetActorId(),
					Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consentPb.FetchConsentResponse{Status: rpc.StatusOk()}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &bePb.GetUserRequest{
					Identifier: &bePb.GetUserRequest_ActorId{
						ActorId: req.GetAuth().GetActorId(),
					},
				}).Return(userRes, nil)
				mocksGoGroup1Done(args, md)

				md.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				}).Times(2).Return(groupsMappedToEmailRes, nil)
				mocksGoGroup2Done(md)
				md.dataCollector.EXPECT().GetSavingsAccounts(gomock.Any(), gomock.Any()).Return(savingsAccountsEssentialsMultiAcc, nil)
				mocksGoGroupMultiInternalAccountAccounts(args, md)

				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actor.GetActorByIdRequest{
					Id: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(actorRes, nil)
				mocksGoGroup4Done(md)

				// get merged accounts
				md.upiOnboardingClient.EXPECT().GetPinStatus(gomock.Any(), &onboarding2.GetPinStatusRequest{
					AccountIds: []string{tpapRes.GetAccounts()[0].GetId()},
				}).Return(&onboarding2.GetPinStatusResponse{
					Status: rpc.StatusOk(),
					UpiAccountPinStatusMap: map[string]upiOnboardingEnumsPb.UpiPinSetStatus{
						tpapRes.GetAccounts()[0].GetId(): upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
					},
				}, nil)

				md.connectAccountsClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), getRequestPayloadToFetchConnectedAccountDetails(connectedAccountsRes.GetAccountDetailsList())).
					Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
						Status: rpc.StatusOk(),
						AccountDetailsMap: map[string]*connectedAccountPb.AccountProfileSummaryDetails{
							savingsRes.GetAccount().GetId(): {
								AccountDetails: &external.AccountDetails{
									AccountId:         savingsRes.GetAccount().GetId(),
									AccInstrumentType: connectedAccountEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
								},
							},
						},
					}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiPinSetUsingAadhaarEnabledForActor(gomock.Any(), &onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiPinSetUsingAadhaarEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), &onboarding2.IsTpapEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(4).Return(&onboarding2.IsTpapEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)

				md.upiOnboardingClient.EXPECT().IsUpiInternationalPaymentEnabledForActor(gomock.Any(), &onboarding2.IsUpiInternationalPaymentEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsUpiInternationalPaymentEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				md.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savings.GetSavingsAccountEssentialsRequest{
					Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savings.ActorIdBankFilter{
							ActorId:     args.req.GetReq().GetAuth().GetActorId(),
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).MaxTimes(1).Return(&savings.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.upiOnboardingClient.EXPECT().IsCcLinkingEnabledForActor(gomock.Any(), &onboarding2.IsCcLinkingEnabledForActorRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).MaxTimes(1).Return(&onboarding2.IsCcLinkingEnabledForActorResponse{
					Status:    rpc.StatusOk(),
					IsEnabled: true,
				}, nil)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), &savings.GetAccountRequest{
					Identifier: &savings.GetAccountRequest_Id{Id: "accountId2"},
				}).MaxTimes(1).Return(&savings.GetAccountResponse{
					Status: rpc.StatusOk(),
					Account: &savings.Account{
						Id:        "accountId2",
						CreatedAt: timestamp.New(time.Now()),
					},
				}, nil)
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			wg := tt.wantMocks(tt.args, md)
			got, err := s.GetUserSessionDetails(ctxB, tt.args.req)
			if wg != nil {
				wg.Wait()
			}
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("Check %v", diff)
				}
				t.Errorf("GetUserSessionDetails() got = %v, \nwant = %v", got, tt.want)
			}
		})
	}
}
