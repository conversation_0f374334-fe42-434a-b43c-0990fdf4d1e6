package user

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"google.golang.org/genproto/googleapis/type/date"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	creditreportpb "github.com/epifi/gamma/api/creditreportv2"
	vgCreditReport "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/frontend/user/events"
	"github.com/epifi/gamma/frontend/user/metrics"
	"github.com/epifi/gamma/pkg/address"

	gammanames "github.com/epifi/gamma/pkg/names"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/frontend/account/screening/uistate"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/user"
	"github.com/epifi/gamma/api/kyc"
	panPb "github.com/epifi/gamma/api/pan"
	types "github.com/epifi/gamma/api/typesv2"
	dlForm "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/form"
	"github.com/epifi/gamma/api/typesv2/form"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	pkgEmployment "github.com/epifi/gamma/pkg/employment"
)

type PanDetails struct {
	Pan  string
	Name *commontypes.Name
	Dob  *date.Date
}

func (s *Service) UpdateFormDetails(ctx context.Context, req *user.UpdateFormDetailsRequest) (*user.UpdateFormDetailsResponse, error) {
	proc := s.getUpdateFormDetailsProcessor(ctx, req.GetSource())
	if proc == nil {
		return &user.UpdateFormDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("no processor found for source"),
			},
		}, nil
	}
	inlineError, errResp := proc.ValidateFields(ctx, req)
	if errResp != nil {
		return &user.UpdateFormDetailsResponse{
			RespHeader: errResp,
		}, nil
	}
	if inlineError != nil && len(inlineError) != 0 {
		return &user.UpdateFormDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusFailedPrecondition(),
			},
			FieldInlineError: inlineError,
		}, nil
	}
	if errResp = proc.UpdateDetails(ctx, req); errResp != nil {
		return &user.UpdateFormDetailsResponse{
			RespHeader: errResp,
		}, nil
	}
	return &user.UpdateFormDetailsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

func (s *Service) FetchFormDetails(ctx context.Context, req *user.FetchFormDetailsRequest) (*user.FetchFormDetailsResponse, error) {
	flow := goutils.Enum(req.GetSource(), dlForm.UpdateUserDetailsFlow_value, dlForm.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_UNSPECIFIED)
	fieldValues := map[string]*form.FieldValue{}

	/*
		Variable declarations for PAN Autofill from Credit Report :
			We are fetching the PAN, Name and DOB from the credit report and autofilling the PAN, Name and DOB fields in the form.
			Credit report is fetched only if the PAN, Name and DOB fields are present in the fieldIds.
			Boolean variables which stores which fields were filled, and which weren't are used for sending events to track the status of PAN autofill.
	*/
	var (
		crPanDetails                                                         = s.checkAndFetchCreditReportForPanAutofill(ctx, req)
		plPanDetails                                                         = s.getPanAutofillDetailsForLoans(ctx, req, flow)
		isPanAutofilledFromCr, isNameAutofilledFromCr, isDobAutofilledFromCr bool
	)

	for _, fieldId := range req.GetFieldIds() {
		switch fieldId {
		case form.FieldIdentifier_FIELD_IDENTIFIER_COMMUNICATION_ADDRESS.String():
			if err := s.appendCommunicationAddress(ctx, req.GetReq().GetAuth().GetActorId(), fieldValues, flow); err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}
		case form.FieldIdentifier_FIELD_IDENTIFIER_LOANS_COMMUNICATION_ADDRESS.String():
			if err := s.appendLoansCommunicationAddress(ctx, req.GetReq().GetAuth().GetActorId(), fieldValues); err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}
		case form.FieldIdentifier_FIELD_IDENTIFIER_OCCUPATION_TYPE.String(),
			form.FieldIdentifier_FIELD_IDENTIFIER_ANNUAL_INCOME.String(),
			form.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_TYPE.String():
			if err := s.appendEmploymentDetails(ctx, req.GetReq().GetAuth().GetActorId(), req.GetFieldIds(), fieldValues); err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}
		case form.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_INFO.String():
			if err := s.appendEmploymentInfo(ctx, req.GetReq().GetAuth().GetActorId(), fieldValues, flow); err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}
		case form.FieldIdentifier_FIELD_IDENTIFIER_LOANS_EMPLOYMENT_INFO.String():
			if err := s.appendLoansEmploymentInfo(ctx, req.GetReq().GetAuth().GetActorId(), fieldValues); err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}

		case form.FieldIdentifier_FIELD_IDENTIFIER_FATHERS_NAME.String(),
			form.FieldIdentifier_FIELD_IDENTIFIER_MOTHERS_NAME.String(),
			form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(),
			form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String(),
			form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String():
			profile, err := s.getUserProfile(ctx, req.GetReq().GetAuth().GetActorId())
			if err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}

			profilePanDetails := &PanDetails{
				Pan:  profile.GetPAN(),
				Name: profile.GetPanName(),
				Dob:  profile.GetDateOfBirth(),
			}

			switch fieldId {
			case form.FieldIdentifier_FIELD_IDENTIFIER_FATHERS_NAME.String():
				fieldValues[fieldId] = &form.FieldValue{
					Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
					Value: &form.FieldValue_StringValue{
						StringValue: profile.GetFatherName().ToString(),
					},
				}
			case form.FieldIdentifier_FIELD_IDENTIFIER_MOTHERS_NAME.String():
				fieldValues[fieldId] = &form.FieldValue{
					Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
					Value: &form.FieldValue_StringValue{
						StringValue: profile.GetMotherName().ToString(),
					},
				}
			case form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String():
				pan := ""
				switch {
				case isPlPanDetailsFlow(flow) && plPanDetails != nil:
					pan = plPanDetails.Pan
				case profilePanDetails != nil && profilePanDetails.Pan != "":
					pan = profilePanDetails.Pan
				case crPanDetails != nil && crPanDetails.Pan != "":
					pan = crPanDetails.Pan
					isPanAutofilledFromCr = true
				}
				fieldValues[fieldId] = &form.FieldValue{
					Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
					Value: &form.FieldValue_StringValue{
						StringValue: pan,
					},
				}
			case form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String():
				bestName := &commontypes.Name{}
				switch {
				case isPlPanDetailsFlow(flow) && plPanDetails != nil:
					bestName = plPanDetails.Name
				case profilePanDetails != nil && profilePanDetails.Name != nil:
					bestName = profilePanDetails.Name
				case crPanDetails != nil && crPanDetails.Name != nil:
					bestName = crPanDetails.Name
					isNameAutofilledFromCr = true
				default:
					bestName = gammanames.BestNameFromProfile(ctx, profile)
				}
				fieldValues[fieldId] = &form.FieldValue{
					Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
					Value: &form.FieldValue_StringValue{
						StringValue: bestName.ToString(),
					},
				}
			case form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String():
				dob := &date.Date{}
				switch {
				case isPlPanDetailsFlow(flow) && plPanDetails != nil:
					dob = plPanDetails.Dob
				case profilePanDetails != nil && profilePanDetails.Dob != nil &&
					(profilePanDetails.Dob.GetYear() != 0 && profilePanDetails.Dob.GetMonth() != 0 && profilePanDetails.Dob.GetDay() != 0):
					dob = profilePanDetails.Dob
				case crPanDetails != nil && crPanDetails.Dob != nil &&
					(crPanDetails.Dob.GetYear() != 0 && crPanDetails.Dob.GetMonth() != 0 && crPanDetails.Dob.GetDay() != 0):
					dob = crPanDetails.Dob
					isDobAutofilledFromCr = true
				}
				fieldValues[fieldId] = &form.FieldValue{
					Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
					Value: &form.FieldValue_DateValue{
						DateValue: &types.Date{
							Year:  dob.GetYear(),
							Month: dob.GetMonth(),
							Day:   dob.GetDay(),
						},
					},
				}
			}
		case form.FieldIdentifier_FIELD_IDENTIFIER_PAN_GUARDIAN_NAME.String():
			guardianName, err := s.fetchGuardianNameFromPan(ctx, req.GetReq().GetAuth().GetActorId())
			if err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}
			fieldValues[fieldId] = &form.FieldValue{
				Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
				Value: &form.FieldValue_StringValue{
					StringValue: guardianName.ToString(),
				},
			}
		case form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_DATA.String():
			if err := s.appendPassportData(ctx, req.GetReq().GetAuth().GetActorId(), fieldValues); err != nil {
				return &user.FetchFormDetailsResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					},
				}, nil
			}
		}
	}

	s.checkAndSendPanAutofillEvent(ctx, req, isPanAutofilledFromCr, isDobAutofilledFromCr, isNameAutofilledFromCr)
	return &user.FetchFormDetailsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Values: fieldValues,
	}, nil
}

func isPlPanDetailsFlow(flow dlForm.UpdateUserDetailsFlow) bool {
	return flow == dlForm.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_FEDERAL_LOANS_ONBOARDING_PAN
}

func (s *Service) checkAndFetchCreditReportForPanAutofill(ctx context.Context, req *user.FetchFormDetailsRequest) *PanDetails {
	if lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String()) ||
		lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String()) ||
		lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String()) {
		return s.getPanAutofillDetailsFromCreditReport(ctx, req.GetReq().GetAuth().GetActorId(), req.GetSource())
	}
	return nil
}

func (s *Service) checkAndSendPanAutofillEvent(ctx context.Context, req *user.FetchFormDetailsRequest, isPanAutofilled, isNameAutofilled, isDobAutofilled bool) {
	if lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String()) ||
		lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String()) ||
		lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String()) {
		s.sendPanAutofillEvent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetSource(), isPanAutofilled, isNameAutofilled, isDobAutofilled)
		metrics.RecordPanAutofillMetrics(commontypes.BoolToBooleanEnum(isPanAutofilled), commontypes.BoolToBooleanEnum(isDobAutofilled), commontypes.BoolToBooleanEnum(isNameAutofilled))
	}
}

func (s *Service) getPanAutofillDetailsFromCreditReport(ctx context.Context, actorId, source string) *PanDetails {
	crResp, err := s.creditReportClient.GetCreditReport(ctx, &creditreportpb.GetCreditReportRequest{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(crResp, err); rpcErr != nil {
		logger.Error(ctx, "getPanAutofillDetailsFromCreditReport: error in getting credit report data, returning nil", zap.Error(rpcErr))
		return nil
	}

	if crResp.GetCreditReportData() == nil {
		logger.Error(ctx, "getPanAutofillDetailsFromCreditReport: credit report data is nil, returning")
		return nil
	}

	logger.Info(ctx, "getPanAutofillDetailsFromCreditReport: got credit report")

	var (
		crPan              = crResp.GetCreditReportData().GetPan()
		crApplicantDetails = crResp.GetCreditReport().GetCreditReportDataRaw().GetCurrentApplication().GetCurrentApplicationDetails().GetCurrentApplicantDetails()
		crName             = &commontypes.Name{
			FirstName:  crApplicantDetails.GetFirstName(),
			MiddleName: s.getMiddleNameFromCr(crApplicantDetails),
			LastName:   crApplicantDetails.GetLastName(),
		}
		crDob = crResp.GetCreditReportData().GetDob()
	)

	s.sendPanAutofillCreditReportFetchedEvent(ctx, actorId, source, crPan, crName, crDob)
	resp, err := s.panClient.ValidateV2(ctx, &panPb.ValidateV2Request{
		ActorId: actorId,
		Pan:     crPan,
		Name:    crName.ToString(),
		Dob:     crDob,
		Vendor:  vgPb.Vendor_FEDERAL_BANK,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "getPanAutofillDetailsFromCreditReport: error in getting the vg response to ValidateV2, returning empty", zap.Error(rpcErr))
		return nil
	}

	logger.Info(ctx, "getPanAutofillDetailsFromCreditReport: validatev2 resp", zap.Any(logger.RESPONSE, resp))

	pan := lo.Ternary(resp.GetPanValid().ToBool() == true, crPan, "")
	name := lo.Ternary[*commontypes.Name](resp.GetNameMatch().ToBool() == true, crName, nil)
	dob := lo.Ternary(resp.GetPanValid().ToBool() == true, crDob, nil)

	s.sendPanAutofillDataValidationEvent(ctx, actorId, source, resp)
	metrics.RecordPanValidationMetrics(resp.GetPanValid(), resp.GetDobMatch(), resp.GetNameMatch())
	return &PanDetails{
		Pan:  pan,
		Name: name,
		Dob:  dob,
	}
}

func (s *Service) getPanAutofillDetailsForLoans(ctx context.Context, req *user.FetchFormDetailsRequest, flow dlForm.UpdateUserDetailsFlow) *PanDetails {
	if flow != dlForm.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_FEDERAL_LOANS_ONBOARDING_PAN {
		return nil
	}
	if !(lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String()) ||
		lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String()) ||
		lo.Contains(req.GetFieldIds(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String())) {
		return nil
	}

	aUser, userErr := s.client.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(aUser, userErr); rpcErr != nil {
		logger.Error(ctx, "getPanAutofillDetailsForLoans: error in getting user data", zap.Error(rpcErr))
		return nil
	}

	var (
		panNumber string
		panName   *commontypes.Name
		dob       *date.Date
	)

	// Fetch the PAN, Name and DOB from the user's data verification details
	// Since this list is always appended, the latest data will always be at the tail of the list
	for idx := len(aUser.GetUser().GetDataVerificationDetails().GetDataVerificationDetails()) - 1; idx >= 0; idx-- {
		dvd := aUser.GetUser().GetDataVerificationDetails().GetDataVerificationDetails()[idx]
		if dvd.GetVerificationMethod() != userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA {
			continue
		}

		switch {
		case dvd.GetDataType() == userPb.DataType_DATA_TYPE_PAN && panNumber == "":
			panNumber = dvd.GetPanNumber()
		case dvd.GetDataType() == userPb.DataType_DATA_TYPE_PAN_NAME && panName == nil:
			panName = dvd.GetPanName()
		case dvd.GetDataType() == userPb.DataType_DATA_TYPE_DOB && dob == nil:
			dob = dvd.GetDOB()
		}
	}

	return &PanDetails{
		Pan:  panNumber,
		Name: panName,
		Dob:  dob,
	}
}

func (s *Service) getMiddleNameFromCr(crApplicantDetails *vgCreditReport.CurrentApplicantDetails) string {
	if crApplicantDetails == nil {
		return ""
	}

	middleName := ""
	if crApplicantDetails.GetMiddleName1() != "" {
		middleName += crApplicantDetails.GetMiddleName1() + " "
	}
	if crApplicantDetails.GetMiddleName2() != "" {
		middleName += crApplicantDetails.GetMiddleName2() + " "
	}

	if crApplicantDetails.GetMiddleName3() != "" {
		middleName += crApplicantDetails.GetMiddleName3()
	}

	return strings.TrimSpace(middleName)
}

func (s *Service) sendPanAutofillDataValidationEvent(ctx context.Context, actorId string, source string,
	resp *panPb.ValidateV2Response) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		panValidationStatus := "Failure"
		if resp.GetPanValid().ToBool() && resp.GetNameMatch().ToBool() && resp.GetDobMatch().ToBool() {
			panValidationStatus = "Success"
		}

		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewPanAutofillDataValidationServer(actorId, panValidationStatus, source, resp.GetPanValid().ToBool(), resp.GetNameMatch().ToBool(), resp.GetDobMatch().ToBool()))
	})
}

func (s *Service) sendPanAutofillEvent(ctx context.Context, actorId string, source string,
	isPanAutofilled, isNameAutofilled, isDobAutofilled bool) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewPanAutofillServer(actorId, source, isPanAutofilled, isNameAutofilled, isDobAutofilled))
	})
}

func (s *Service) sendPanAutofillCreditReportFetchedEvent(ctx context.Context, actorId string, source string,
	crPan string, crName *commontypes.Name, crDob *date.Date) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewPanAutofillCreditReportFetchedServer(actorId, source, crPan != "", crName.ToString() != "", crDob.String() != ""))
	})
}

func (s *Service) getUserProfile(ctx context.Context, actorId string) (*userPb.Profile, error) {
	resp, errResp := s.client.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching user from actor id", zap.Error(err))
		return nil, err
	}
	return resp.GetUser().GetProfile(), nil
}

func (s *Service) fetchGuardianNameFromPan(ctx context.Context, actorId string) (*commontypes.Name, error) {
	resp, errResp := s.panClient.GetScannedPanDocument(ctx, &panPb.GetScannedPanDocumentRequest{
		Identifier: &panPb.GetScannedPanDocumentRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching scanned pan details", zap.Error(err))
		return nil, err
	}
	return resp.GetScannedPanAttempt().GetGuardianInfo().GetGuardianName(), nil
}

func (s *Service) appendCommunicationAddress(ctx context.Context, actorId string, fieldValues map[string]*form.FieldValue, source dlForm.UpdateUserDetailsFlow) error {
	if source == dlForm.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_CONFIRM_NR_VKYC_DETAILS {
		return s.appendNrCommunicationAddress(ctx, actorId, fieldValues)
	}
	kycResp, err := s.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(kycResp, err); err != nil {
		logger.Error(ctx, "failed to fetch kyc record from kyc service", zap.Error(err))
		return err
	}
	fieldValues[form.FieldIdentifier_FIELD_IDENTIFIER_COMMUNICATION_ADDRESS.String()] = &form.FieldValue{
		Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
		Value: &form.FieldValue_StringValue{
			StringValue: s.convertPostalAddressToReadableFormat(s.getPostalAddress(kycResp.GetKycRecord())),
		},
	}
	return nil
}

func (s *Service) appendLoansCommunicationAddress(ctx context.Context, actorId string, fieldValues map[string]*form.FieldValue) error {
	userResp, respErr := s.client.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting user data", zap.Error(rpcErr))
		return rpcErr
	}
	commsAddress, ok := userResp.GetUser().GetProfile().GetAddresses()[types.AddressType_LOAN_COMMUNICATION.String()]
	if !ok {
		logger.Error(ctx, "loans communication address not found")
		return fmt.Errorf("communication address not found")
	}
	fieldValues[form.FieldIdentifier_FIELD_IDENTIFIER_LOANS_COMMUNICATION_ADDRESS.String()] = &form.FieldValue{
		Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
		Value: &form.FieldValue_StringValue{
			StringValue: address.ConvertPostalAddressToString(commsAddress),
		},
	}
	return nil
}

func (s *Service) appendNrCommunicationAddress(ctx context.Context, actorId string, fieldValues map[string]*form.FieldValue) error {
	userResp, respErr := s.client.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting user data", zap.Error(rpcErr))
		return rpcErr
	}
	fieldValues[form.FieldIdentifier_FIELD_IDENTIFIER_COMMUNICATION_ADDRESS.String()] = &form.FieldValue{
		Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
		Value: &form.FieldValue_StringValue{
			StringValue: address.ConvertPostalAddressToString(userResp.GetUser().GetProfile().GetAddresses()[types.AddressType_MAILING.String()]),
		},
	}
	return nil
}

func (s *Service) getPostalAddress(kycRecord *kyc.KYCRecord) *postaladdress.PostalAddress {
	if kycRecord.GetPermanentAddress() != nil {
		return kycRecord.GetPermanentAddress()
	}
	return kycRecord.GetCommunicationAddress()
}

func (s *Service) appendEmploymentDetails(ctx context.Context, actorId string, fieldIds []string, fieldValues map[string]*form.FieldValue) error {
	empInfoRes, err := s.employmentClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(empInfoRes, err); err != nil {
		logger.Error(ctx, "error in getting employment info of user", zap.Error(err))
		return fmt.Errorf("error in getting employment info of user")
	}
	for _, fieldId := range fieldIds {
		switch fieldId {
		case form.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_TYPE.String():
			fieldValues[fieldId] = &form.FieldValue{
				Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
				Value: &form.FieldValue_StringValue{
					StringValue: getEmploymentTypeDisplayText(empInfoRes.GetEmploymentData().GetEmploymentType()),
				},
			}

		case form.FieldIdentifier_FIELD_IDENTIFIER_OCCUPATION_TYPE.String():
			fieldValues[fieldId] = &form.FieldValue{
				Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
				Value: &form.FieldValue_StringValue{
					StringValue: getOccupationType(empInfoRes.GetEmploymentData().GetOccupationType()),
				},
			}

		case form.FieldIdentifier_FIELD_IDENTIFIER_ANNUAL_INCOME.String():
			fieldValues[fieldId] = &form.FieldValue{
				Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
				Value: &form.FieldValue_StringValue{
					StringValue: getIncomeRange(empInfoRes.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary()),
				},
			}
		}
	}
	return nil
}

func (s *Service) appendEmploymentInfo(ctx context.Context, actorId string, fieldValues map[string]*form.FieldValue, source dlForm.UpdateUserDetailsFlow) error {
	var displayIncomeRange string
	empInfoResp, err := s.employmentClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(empInfoResp, err); err != nil {
		logger.Error(ctx, "failed to fetch employment info from employment service", zap.Error(err))
		return err
	}
	if source == dlForm.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_CONFIRM_NR_VKYC_DETAILS {
		displayIncomeRange = getNRIncomeRange(empInfoResp.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary())
	} else {
		displayIncomeRange = getIncomeRange(empInfoResp.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary())
	}

	fieldValues[form.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_INFO.String()] = &form.FieldValue{
		Type: form.FieldValueType_FIELD_VALUE_TYPE_MAP,
		Value: &form.FieldValue_MapValue{
			MapValue: &form.Map{
				Map: map[string]*commontypes.Text{
					form.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_TYPE.String(): commontypes.GetTextFromStringFontColourFontStyle(getEmploymentTypeDisplayText(empInfoResp.GetEmploymentData().GetEmploymentType()), "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_OCCUPATION_TYPE.String(): commontypes.GetTextFromStringFontColourFontStyle(getOccupationType(empInfoResp.GetEmploymentData().GetOccupationType()), "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_ANNUAL_INCOME.String():   commontypes.GetTextFromStringFontColourFontStyle(displayIncomeRange, "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		},
	}
	return nil
}

func (s *Service) appendLoansEmploymentInfo(ctx context.Context, actorId string, fieldValues map[string]*form.FieldValue) error {
	userResp, respErr := s.client.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting user data", zap.Error(rpcErr))
		return rpcErr
	}

	var employmentDetails *userPb.DataVerificationDetail_EmploymentDetail
	for _, verificationDetails := range userResp.GetUser().GetDataVerificationDetails().GetDataVerificationDetails() {
		if verificationDetails.GetVerificationMethod() == userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA {
			employmentDetails = verificationDetails.GetEmploymentDetail()
			// NOTE: We are not breaking here as we want the latest employment details from the list.
		}
	}

	fieldValues[form.FieldIdentifier_FIELD_IDENTIFIER_LOANS_EMPLOYMENT_INFO.String()] = &form.FieldValue{
		Type: form.FieldValueType_FIELD_VALUE_TYPE_MAP,
		Value: &form.FieldValue_MapValue{
			MapValue: &form.Map{
				Map: map[string]*commontypes.Text{
					form.FieldIdentifier_FIELD_IDENTIFIER_EMPLOYMENT_TYPE.String(): commontypes.GetTextFromStringFontColourFontStyle(getEmploymentTypeDisplayText(employment.GetEmploymentType(employmentDetails.GetEmploymentType())), "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_ANNUAL_INCOME.String():   commontypes.GetTextFromStringFontColourFontStyle(pkgMoney.ToDisplayString(employmentDetails.GetMonthlyIncome()), "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_OCCUPATION_TYPE.String(): commontypes.GetTextFromStringFontColourFontStyle(getOccupationType(employmentDetails.GetOccupationType()), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		},
	}
	return nil
}

func (s *Service) appendPassportData(ctx context.Context, actorId string, fieldValues map[string]*form.FieldValue) error {
	passResp, passErr := s.onboardingClient.FetchPassport(ctx, &onbPb.FetchPassportRequest{ActorId: actorId})
	if err := epifigrpc.RPCError(passResp, passErr); err != nil {
		logger.Error(ctx, "error in fetching passport data", zap.Error(err))
		return err
	}

	addr, err := getStringFromAddressLines(passResp.GetPassport().GetAddress().GetAddressLines())
	if err != nil {
		return err
	}

	surname := passResp.GetPassport().GetName().GetLastName()
	givenName := passResp.GetPassport().GetName().GetFirstName() +
		" " + passResp.GetPassport().GetName().GetMiddleName()
	dob := passResp.GetPassport().GetDateOfBirth()
	dobString := strconv.FormatInt(int64(dob.GetYear()), 10) + "/" + strconv.FormatInt(int64(dob.GetMonth()), 10) + "/" + strconv.FormatInt(int64(dob.GetDay()), 10)
	fileNumber := passResp.GetPassport().GetFileNumber()

	fieldValues[form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_DATA.String()] = &form.FieldValue{
		Type: form.FieldValueType_FIELD_VALUE_TYPE_MAP,
		Value: &form.FieldValue_MapValue{
			MapValue: &form.Map{
				Map: map[string]*commontypes.Text{
					form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_SURNAME.String():       commontypes.GetTextFromStringFontColourFontStyle(surname, "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_GIVEN_NAME.String():    commontypes.GetTextFromStringFontColourFontStyle(givenName, "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_DATE_OF_BIRTH.String(): commontypes.GetTextFromStringFontColourFontStyle(dobString, "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_ADDRESS.String():       commontypes.GetTextFromStringFontColourFontStyle(addr, "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_FILE_NUMBER.String():   commontypes.GetTextFromStringFontColourFontStyle(fileNumber, "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_MOTHERS_NAME.String():           commontypes.GetTextFromStringFontColourFontStyle(passResp.GetPassport().GetMothersName().ToString(), "#313234", commontypes.FontStyle_HEADLINE_M),
					form.FieldIdentifier_FIELD_IDENTIFIER_FATHERS_NAME.String():           commontypes.GetTextFromStringFontColourFontStyle(passResp.GetPassport().GetFathersName().ToString(), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		},
	}
	return nil
}

func getEmploymentTypeDisplayText(employmentType employment.EmploymentType) string {
	if employmentType == employment.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		return "NA"
	}
	feEmploymentType, ok := getFeEmploymentTypeMap[employmentType]
	if !ok {
		feEmploymentType = uistate.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED
	}
	return pkgEmployment.EmploymentTypeToDisplayText[feEmploymentType]
}

func getOccupationType(occupationType employment.OccupationType) string {
	if occupationType != employment.OccupationType_OCCUPATION_TYPE_UNSPECIFIED &&
		!lo.Contains(pkgEmployment.OccupationDropDownExclusionList, occupationType.String()) {
		occ, ok := pkgEmployment.OccupationTypeToDisplayText[occupationType]
		if !ok {
			occ = "NA"
		}
		return occ
	}
	return "NA"
}

func getIncomeRange(annualSalary *screening.AnnualSalary) string {
	if annualSalary.GetRange() == nil || annualSalary.GetRange().GetMaxValue() == 0 {
		return fmt.Sprintf("%v", int(annualSalary.GetAbsolute()/12))
	}
	return getMonthlySalRangeFromAnnualSalary(annualSalary)
}

func getNRIncomeRange(annualSalary *screening.AnnualSalary) string {
	minValue := &types.Money{
		CurrencyCode: annualSalary.GetRange().GetAlternateDisplayed().GetCurrencyCode().String(),
		Units:        int64(annualSalary.GetRange().GetAlternateDisplayed().GetMinVal()),
	}
	maxValue := &types.Money{
		CurrencyCode: annualSalary.GetRange().GetAlternateDisplayed().GetCurrencyCode().String(),
		Units:        int64(annualSalary.GetRange().GetAlternateDisplayed().GetMaxVal()),
	}
	return fmt.Sprintf("%s - %s %s",
		pkgMoney.ToDisplayStringWithSuffixAndPrecision(minValue.GetBeMoney(), false, true, 2, pkgMoney.IndianNumberSystem),
		pkgMoney.ToDisplayStringWithSuffixAndPrecision(maxValue.GetBeMoney(), false, true, 2, pkgMoney.IndianNumberSystem), maxValue.GetCurrencyCode())

}

func getMonthlySalRangeFromAnnualSalary(annualSalary *screening.AnnualSalary) string {
	maxSal := annualSalary.GetRange().GetMaxValue()
	switch {
	case maxSal <= 100000:
		return "0 - 8K"
	case maxSal <= 500000:
		return "8K - 41K"
	case maxSal <= 1000000:
		return "41K - 83K"
	case maxSal <= 2500000:
		return "83K - 2.1Lacs"
	case maxSal <= 5000000:
		return "2.1Lacs - 4.1Lacs"
	case maxSal <= 10000000:
		return "4.1Lacs - 8.3Lacs"
	default:
		return "8.3Lacs- 20Lacs"
	}
}

func getStringFromAddressLines(lines []string) (string, error) {
	if len(lines) == 0 {
		return "", fmt.Errorf("address lines are empty")
	}
	addr := strings.Join(lines, ", ")
	return addr, nil
}
