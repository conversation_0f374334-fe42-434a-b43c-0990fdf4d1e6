package ui

import commontypes "github.com/epifi/be-common/api/typesv2/common"

const (
	WalletActivationIcon     = "https://epifi-icons.pointz.in/usstocks_image/wallet_uss_landing_page_icon.png"
	HourGlass                = "https://epifi-icons.pointz.in/usstocks_images/hour_glass.png"
	ChevronRightArrow        = "https://epifi-icons.pointz.in/usstocks_images/chevron-right.png"
	ChevronRightGreyArrow    = "https://epifi-icons.pointz.in/usstocks_images/chevron-right-grey.png"
	FileIconGrey             = "https://epifi-icons.pointz.in/usstocks_images/file-icon-grey.png"
	PlusImage                = "https://epifi-icons.pointz.in/usstocks_images/plus.png"
	GreenDollarImage         = "https://epifi-icons.pointz.in/usstocks_images/green_dollar.png"
	TadaEmojiImage           = "https://epifi-icons.pointz.in/usstocks_images/tada.png"
	WalletWithdrawArrow      = "https://epifi-icons.pointz.in/usstocks_images/wallet_withdraw_arrow.png"
	WalletAddFundsArrow      = "https://epifi-icons.pointz.in/usstocks_images/wallet_buy_arrow.png"
	ActivityTxnUpArrow       = "https://epifi-icons.pointz.in/usstocks_images/arrow-up-right.png"
	ActivityTxnDownArrow     = "https://epifi-icons.pointz.in/usstocks_images/arrow-down-left.png"
	ActivityTxnCrossIcon     = "https://epifi-icons.pointz.in/usstocks_images/cross-icon.png"
	AlpacaLogo               = "https://epifi-icons.pointz.in/usstocks_images/alpaca_logo.png"
	StarIcon                 = "https://epifi-icons.pointz.in/usstocks_images/star.png"
	LinearFederalBank        = "https://epifi-icons.pointz.in/usstocks_images/linear_federal_bank.png"
	TimerIcon                = "https://epifi-icons.pointz.in/investments/landing/timer.png"
	CopyIcon                 = "https://epifi-icons.pointz.in/usstocks_images/copy_icon.png"
	WalletIconBlackAndWhite  = "https://epifi-icons.pointz.in/usstocks_images/wallet_icon_black_and_white.png"
	WalletIconGreen          = "https://epifi-icons.pointz.in/usstocks_images/wallet-icon-green.png"
	WhiteRightArrow          = "https://epifi-icons.pointz.in/usstocks_images/white-right-arrow.png"
	PlusIcon                 = "https://epifi-icons.pointz.in/usstocks_images/plus-icon.png"
	ProgressTracker40Percent = "https://epifi-icons.pointz.in/usstocks_images/progress-tracker-40-percent.png"
	HourGlassImage           = "https://epifi-icons.pointz.in/usstocks_images/hour-glass-img.png"
	GiftIcon                 = "https://epifi-icons.pointz.in/usstocks_images/gift.png"
	GreyGiftSmallIcon        = "https://epifi-icons.pointz.in/usstocks_images/grey_gift_icon_small.png"
	ChevronYellowRightArrow  = "https://epifi-icons.pointz.in/usstocks_images/chevron-right-yellow-arrow.png"
	RoadblockIcon            = "https://epifi-icons.pointz.in/usstocks_images/roadblock.png"
	CelebrationIcon          = "https://epifi-icons.pointz.in/usstocks_images/celebration.png"
	ChevronGreenRightArrow   = "https://epifi-icons.pointz.in/usstocks_images/chevron-right-light-green-arrow.png"
	TargetIcon               = "https://epifi-icons.pointz.in/usstocks_images/target.png"
	MarketIcon               = "https://epifi-icons.pointz.in/usstocks_images/market.png"
	WatchlistIcon            = "https://epifi-icons.pointz.in/usstocks_images/watchlist.png"
	USFundsIcon              = "https://epifi-icons.pointz.in/usstocks_images/us_funds_v2.png"
)

func GetImage(imgType commontypes.ImageType, url string, width, height int32) *commontypes.Image {
	return &commontypes.Image{
		ImageType: imgType,
		ImageUrl:  url,
		Width:     width,
		Height:    height,
	}
}
