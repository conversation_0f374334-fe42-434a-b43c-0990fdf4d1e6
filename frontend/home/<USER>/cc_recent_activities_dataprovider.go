package dataprovider

import (
	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sync"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	actorPb "github.com/epifi/gamma/api/actor"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	homePb "github.com/epifi/gamma/api/frontend/home"
	actorActivityEnumPb "github.com/epifi/gamma/api/order/actoractivity/enums"
	timelinePb "github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	featureCfg "github.com/epifi/gamma/pkg/feature"
)

type CCRecentActivitiesDataProvider struct {
	ffAccountingClient ffAccountsPb.AccountingClient
	actorClient        actorPb.ActorClient
	userClient         userPb.UsersClient
	timelineClient     timelinePb.TimelineServiceClient
	homeParams         *config.HomeParams
	userGroupClient    userGroupPb.GroupClient
	dynconf            *genconf.Config
	conf               *config.Config
}

func NewCCRecentActivitiesDataProvider(ffAccountingClient ffAccountsPb.AccountingClient,
	actorClient actorPb.ActorClient, userClient userPb.UsersClient, timelineClient timelinePb.TimelineServiceClient,
	homeParams *config.HomeParams, userGroupClient userGroupPb.GroupClient, dynconf *genconf.Config, conf *config.Config) *CCRecentActivitiesDataProvider {
	return &CCRecentActivitiesDataProvider{
		ffAccountingClient: ffAccountingClient,
		actorClient:        actorClient,
		userClient:         userClient,
		timelineClient:     timelineClient,
		homeParams:         homeParams,
		userGroupClient:    userGroupClient,
		dynconf:            dynconf,
		conf:               conf,
	}
}

func (c *CCRecentActivitiesDataProvider) GetRecentActivity(ctx context.Context, currentActorId string, pageSize int32,
	toTime *timestamppb.Timestamp, recentActivitiesChannelResponse chan<- *RecentActivitiesChannelResponse, wg *sync.WaitGroup) {
	defer wg.Done()

	if !featureCfg.IsFeatureEnabledForUser(ctx, currentActorId, &cfg.FeatureReleaseConfig{
		IsFeatureRestricted: c.dynconf.Flags().EnableCCRecentActivityFeatureFlag().IsFeatureRestricted(),
		AllowedUserGroups:   c.dynconf.Flags().EnableCCRecentActivityFeatureFlag().AllowedUserGroups(),
	}, c.userGroupClient, c.userClient, c.actorClient) {
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD,
			Err:                  epifierrors.ErrRecordNotFound,
		}
		return
	}

	getTxnRes, err := c.ffAccountingClient.GetPaginatedCreditCardTxnView(ctx, &ffAccountsPb.GetPaginatedCreditCardTxnViewRequest{
		Identifier:     &ffAccountsPb.GetPaginatedCreditCardTxnViewRequest_ActorId{ActorId: currentActorId},
		StartTimestamp: toTime,
		PageSize:       pageSize,
		Descending:     true,
		Statuses:       []ffAccEnumsPb.TransactionStatus{ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS},
	})
	switch {
	case err != nil:
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD,
			Err:                  fmt.Errorf("error from cc transaction source %w", err),
		}
		return
	case getTxnRes.GetStatus().IsRecordNotFound():
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD,
			Err:                  epifierrors.ErrRecordNotFound,
		}
		return
	case !getTxnRes.GetStatus().IsSuccess():
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD,
			Err:                  fmt.Errorf("non-success error code for cc transaction %s", getTxnRes.GetStatus().String()),
		}
		return
	default:
		logger.Debug(ctx, "successfully fetched cc transaction")
	}

	// Process the transactions and build responses
	userActivityResponses, err := c.buildActivities(ctx, currentActorId, getTxnRes.GetTxnsViewModelList())
	if err != nil {
		logger.Error(ctx, "error processing credit card transactions", zap.Error(err))
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD,
			Err:                  fmt.Errorf("error processing credit card transactions: %w", err),
		}
		return
	}

	recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
		UserActivityResponse: userActivityResponses,
		ActivitySource:       actorActivityEnumPb.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD,
		Err:                  nil,
	}
}

// buildActivities processes credit card transactions and builds user activity responses in bulk
func (c *CCRecentActivitiesDataProvider) buildActivities(ctx context.Context, currentActorId string,
	ccTxnViewModels []*ffAccountsPb.CreditCardTransactionViewModel) ([]*homePb.UserActivityResponse, error) {

	if len(ccTxnViewModels) == 0 {
		return nil, nil
	}

	// First pass: Collect all required actor IDs
	otherActorIds := make([]string, 0, len(ccTxnViewModels))

	for _, ccTxnView := range ccTxnViewModels {
		otherActorId := ccTxnView.GetOtherActorId()
		if lo.IndexOf(otherActorIds, otherActorId) < 0 {
			// If the actor ID is not already present in the list
			otherActorIds = append(otherActorIds, otherActorId)
		}
	}

	// Bulk fetch entity details
	entityDetailsRes, err := getActorDetails(ctx, c.actorClient, otherActorIds)
	if err != nil {
		return nil, err
	}

	// Create a map for quick lookup
	entityDetailsMap := make(map[string]*actorPb.GetEntityDetailsResponse_EntityDetail)
	for _, detail := range entityDetailsRes.GetEntityDetails() {
		entityDetailsMap[detail.GetActorId()] = detail
	}

	// Bulk fetch timeline data
	timelineRes, err := c.timelineClient.GetTimelinesForActor(ctx, &timelinePb.GetTimelinesForActorRequest{
		PrimaryActorId:    currentActorId,
		SecondaryActorIds: otherActorIds,
	})
	if te := epifigrpc.RPCError(timelineRes, err); te != nil {
		return nil, fmt.Errorf("error fetching timelines in bulk: %w", te)
	}

	// Create a map for timeline lookup
	timelineMap := make(map[string]*timelinePb.Timeline)
	for _, timeline := range timelineRes.GetTimelines() {
		// currentActorId can be primary or secondary in the timeline.
		if timeline.GetPrimaryActorId() == currentActorId {
			timelineMap[timeline.GetSecondaryActorId()] = timeline
		} else {
			timelineMap[timeline.GetPrimaryActorId()] = timeline
		}
	}

	// Second pass: Build responses using the bulk fetched data
	userActivityResponses := make([]*homePb.UserActivityResponse, 0, len(ccTxnViewModels))
	for _, ccTxnView := range ccTxnViewModels {
		activity, err := c.convertCcTxnViewToUserActivity(ctx, ccTxnView, entityDetailsMap, timelineMap)
		if err != nil {
			// error logged in the function, skip this activity to gracefully handle error and still process other activities.
			// TODO: Ideally this should be tracked and alert oncall if this happens. For now, the old behaviour is maintained.
			continue
		}
		userActivityResponses = append(userActivityResponses, activity)
	}

	return userActivityResponses, nil
}

// convertCcTxnViewToUserActivity creates a single user activity response from a credit card transaction
// using pre-fetched entity and timeline data maps
func (c *CCRecentActivitiesDataProvider) convertCcTxnViewToUserActivity(ctx context.Context,
	ccTxnViewModel *ffAccountsPb.CreditCardTransactionViewModel,
	entityDetailsMap map[string]*actorPb.GetEntityDetailsResponse_EntityDetail,
	timelineMap map[string]*timelinePb.Timeline) (*homePb.UserActivityResponse, error) {

	otherActorId := ccTxnViewModel.GetOtherActorId()

	// Get entity details from map
	entityDetails, ok := entityDetailsMap[otherActorId]
	if !ok {
		logger.Error(ctx, "entity details not found for actor", zap.String("actor_id", otherActorId))
		return nil, fmt.Errorf("entity details not found for actor %s", otherActorId)
	}

	// Get timeline from map
	timeline, ok := timelineMap[otherActorId]
	if !ok {
		logger.Error(ctx, "timeline not found for actor", zap.String("actor_id", otherActorId))
		return nil, fmt.Errorf("timeline not found for actor %s", otherActorId)
	}

	// Determine text color and image URL based on transaction type
	var textColour, imageUrl string
	if ccTxnViewModel.GetTransactionType() == ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
		textColour = c.homeParams.AmountColourMap.CreditColour
		imageUrl = c.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.CreditUrl
	} else {
		textColour = c.homeParams.AmountColourMap.DebitColour
		imageUrl = c.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.DebitUrl
	}

	// Build the activity response
	activity := &homePb.UserActivityResponse{
		Title:  entityDetails.GetName().ToSentenceCaseString(),
		Amount: types.GetFromBeMoney(ccTxnViewModel.GetAmount()),
		Icon: &commontypes.Image{
			ImageUrl: entityDetails.GetProfileImageUrl(),
		},
		Link: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_TIMELINE,
			ScreenOptions: &deeplinkPb.Deeplink_TimelineScreenOptions{
				TimelineScreenOptions: &deeplinkPb.TimelineScreenOptions{
					TimelineId: timeline.GetId(),
				},
			},
		},
		ImageColourCode: actorPb.GetColourCodeForActor(otherActorId),
		TitleColourCode: textColour,
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ActivityTime: ccTxnViewModel.GetTransactionTimestamp(),
		TxnType:      &commontypes.Image{ImageUrl: imageUrl},
	}

	return activity, nil
}
