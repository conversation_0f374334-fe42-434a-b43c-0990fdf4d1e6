package home

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"

	"context"
	"fmt"
	"math"
	"time"

	"github.com/epifi/be-common/pkg/epifigrpc"

	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/frontend/tiering/helper/aasalary"
	"github.com/epifi/gamma/frontend/user"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	earnedBenefits "github.com/epifi/gamma/frontend/tiering/earned_benefits"
	"github.com/epifi/gamma/frontend/tiering/helper"
)

var (
	upgradedToTierText    = "Upgraded to %s"
	youAreOnTierText      = "You’re on %s"
	minKycBadge           = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/MinKycBadge.png"}
	fullKycBadge          = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FullKycBadge.png"}
	promptChevronImage    = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"}
	promptChevronV2Image  = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home/<USER>"}
	warningGraceBadge     = "https://epifi-icons.pointz.in/home-v2/MinKycBadge.png"
	warningDowngradeBadge = "https://epifi-icons.pointz.in/tiering/warning_downgrade.png"

	tierBadgeRegularIconUrl  = "https://epifi-icons.pointz.in/tiering/badge/regular3d.png"
	tierBadgeBasicIconUrl    = "https://epifi-icons.pointz.in/tiering/badge/standard3d.png"
	tierBadgePlusIconUrl     = "https://epifi-icons.pointz.in/tiering/badge/plus3d.png"
	tierBadgeInfiniteIconUrl = "https://epifi-icons.pointz.in/tiering/badge/infinite3d.png"
	tierBadgeSalaryIconUrl   = "https://epifi-icons.pointz.in/tiering/badge/salary-updated.png"
	tierBadgeAaSalaryIconUrl = "https://epifi-icons.pointz.in/tiering/badge/prime3d.png"

	// redWarningTriangleUrl    = "https://epifi-icons.pointz.in/tiering/notch/red_triangle.png"
	yellowWarningTriangleUrl = "https://epifi-icons.pointz.in/tiering/notch/yellow_triangle.png"

	graceTextPlus       = "2x rewards will expire soon!"
	graceTextInfinite   = "4x rewards will expire soon!"
	graceTextSalary     = "Salary rewards will expire soon!"
	graceTextSalaryLite = "Salary lite rewards will expire soon!"
	graceTextAaSalary   = "Prime rewards will expire soon!"

	downgradeTextPlus       = "You lost access to Plus"
	downgradeTextInfinite   = "You lost access to Infinite"
	downgradeTextSalary     = "Salary Plan deactivated"
	downgradeTextSalaryLite = "Salary Lite Plan deactivated"
	downgradeTextAaSalary   = "Prime Plan deactivated"

	tierTextRegular                              = "View Regular Benefits"
	tierTextBasic                                = "Upgrade account"
	tierTextPlus                                 = "View your Plus benefits"
	tierTextInfinite                             = "View your Infinite benefits"
	tierTextSalary                               = "View your Salary benefits"
	tierTextSalaryLite                           = "View your Salary lite benefits"
	tierTextAaSalary                             = "View Prime benefits"
	tierTextPrimeUpgrade                         = "Earn up to 3% back"
	tierTextAaSalaryNudgeToCommitSalary          = "Afraid of commitment?"
	tierTextAaSalaryNudgeToTransferAmount        = "1 tap away from Prime"
	tierTextAaSalaryUpgradedToPrime              = "Upgraded to Prime 🎉"
	tierTextAaSalaryPrimeDowngradeSalaryBand3to2 = "Switched to 2% back"
	tierTextAaSalaryDue                          = "Next Prime transfer due"
	tierTextAaSalaryCashbackEnds                 = "%s back ends in %d days"
	tierTextAaSalaryLosingCashback               = "Losing %s back ⚠️"
	tierTextAaSalaryLastDay                      = "Last day of Prime ⚠️️"
	tierTextAaSalaryCashbackExpired              = "%s back Expired"

	bgColorWGrace    = "#EAD8A3" // Colour/Mid Lemon
	bgColorWoGrace   = "#DEEEF2" // Colour/Pastel Ocean
	bgColorDowngrade = "#EFC0C0" // Colour/Mid Peach

	promptChevron           = "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"
	promptChevronDarkLemon  = "https://epifi-icons.pointz.in/home-v2/PromptChevronDarkLemon.png"
	promptChevronRavenSteel = "https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"

	promptChevronDarkUrl  = "https://epifi-icons.pointz.in/tiering/notch/chevronDark.png"
	promptChevronLightUrl = "https://epifi-icons.pointz.in/tiering/notch/chevronLight.png"

	cashBack2percent     = "Claim 2% back now"
	stepsRemFrom30k      = "Just %s steps away from ₹30k"
	zeroBalSalaryAccount = "Get 0 balance salary a/c"
	instantSalary50k     = "Get ₹50K Instant salary"
	get30kMoreOnSalary   = "Salaried? Get ₹30k more"

	addFundsDeeplink = func() *deeplink.Deeplink {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_TRANSFER_IN,
		}
	}

	// Deprecated: use feTieringHelper.AllPlansDeeplink
	allPlansDeeplink = func() *deeplink.Deeplink {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_TIER_ALL_PLANS_REDIRECTION,
			ScreenOptions: &deeplink.Deeplink_TierAllPlansRedirectionOptions{
				TierAllPlansRedirectionOptions: &deeplink.TierAllPlansRedirectionOptions{
					UiContext: deeplink.Screen_PROFILE_SCREEN.String(),
				},
			},
		}
	}

	primeAllPlansDeeplink = func(isPlansV2Enabled bool) *deeplink.Deeplink {
		return tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, isPlansV2Enabled)
	}

	// Deprecated
	allPlansDeeplinkNextHighestTier = func() *deeplink.Deeplink {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_TIER_ALL_PLANS_REDIRECTION,
		}
	}

	salaryBenefitsDeeplink = func() *deeplink.Deeplink {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN,
		}
	}

	salaryBenefitsRedirectionDeeplink = func() *deeplink.Deeplink {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION,
		}
	}

	aaSalaryLandingScreenDeeplink = func() *deeplink.Deeplink {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_AA_SALARY_LANDING_SCREEN,
		}
	}

	graceInfoPopupPlusTitle        = "You will lose access to 2x rewards and your Plus plan"
	graceInfoPopupInfiniteTitle    = "You will lose access to 2% back and your Infinite plan"
	graceInfoPlusTickerTitle       = "Add funds to stay on Plus & get 2x rewards"
	graceInfoInfiniteTickerTitle   = "Add funds to stay on Infinite & get 2% back"
	graceInfoPopupPlusSubTitle     = "To stay on the Plus plan, you need to keep a minimum balance of %s in your account"
	graceInfoPopupInfiniteSubTitle = "To stay on the Infinite plan, you need to keep a minimum balance of %s in your account"
	graceInfoPopupIconUrl          = "https://epifi-icons.pointz.in/home/<USER>/Danger_4x.png"
	graceInfoPopupLaterCtaText     = "Later"
	graceInfoPopupAddMoneyCtaText  = "Add Money"
	graceInfoPopupBgColor          = "#EAD8A3"
	graceInfoPopupSubTitleBgColor  = "#FFFFFF"

	graceRedMidColor   = "#EFC0C0"
	graceRedColor      = "#CF8888"
	graceLemonMidColor = "#EAD8A3"
	graceLemonColor    = "#D3B250"
)

func getGraceStartTimeFromMovementDetail(movementDetail *external.MovementExternalDetails) time.Time {
	gracePeriod := movementDetail.GetGraceParams().GetPeriod()
	movementTimestamp := movementDetail.GetMovementTimestamp().AsTime()
	graceStartTime := movementTimestamp.Add(-time.Duration(gracePeriod) * time.Second)

	return graceStartTime
}

func (s *Service) getTieringInfoFromResp(tieringResp *beTieringPb.GetTieringPitchV2Response, tieringConfigResp *beTieringPb.GetConfigParamsResponse) (
	bool, beTieringExtPb.Tier, bool, beTieringExtPb.Tier, bool, error) {
	beExtTier := tieringResp.GetCurrentTier()
	var toShowGraceInfo bool
	for _, movementDetail := range tieringResp.GetMovementDetailsList() {
		if movementDetail.GetTierName() == beExtTier && !movementDetail.GetIsMovementAllowed() {
			graceStartTime := getGraceStartTimeFromMovementDetail(movementDetail)
			movementTimestamp := movementDetail.GetMovementTimestamp().AsTime()
			if time.Until(movementTimestamp) > tieringConfigResp.GetGraceWindowDuration().AsDuration() &&
				time.Since(graceStartTime) > tieringConfigResp.GetGraceInitialWindowDuration().AsDuration() {
				toShowGraceInfo = true
			}
		}
	}
	var isUserDowngraded bool
	var previousTier beTieringExtPb.Tier
	// Check if the last movement for the user is a downgrade movement
	// If YES, then check if downgrade_timestamp >= current_time - threshold
	if tieringResp.GetLastDowngradeDetails().GetMovementTimestamp().AsTime().After(tieringResp.GetLastUpgradeDetails().GetMovementTimestamp().AsTime()) &&
		time.Since(tieringResp.GetLastDowngradeDetails().GetMovementTimestamp().AsTime()) < tieringConfigResp.GetDowngradeWindowDuration().AsDuration() {
		isUserDowngraded = true
		beExtTier := tieringResp.GetLastDowngradeDetails().GetFromTier()
		previousTier = beExtTier
	}
	isUserInCoolOff, coolOffCheckErr := helper.IsUserInCoolOff(beExtTier, tieringResp.GetMovementDetailsList())
	if coolOffCheckErr != nil {
		return false, 0, false, 0, false, fmt.Errorf("error checking if user in cool off or not")
	}
	return isUserDowngraded, previousTier, toShowGraceInfo, beExtTier, isUserInCoolOff, nil
}

func getTieringPromptBadge(isUserDowngraded, isUserInGracePeriod bool, previousTier, currentTier beTieringExtPb.Tier) *commontypes.Image {
	if isUserDowngraded && !(previousTier.IsAaSalaryTier() && currentTier.IsAaSalaryTier()) {
		return &commontypes.Image{ImageUrl: warningDowngradeBadge}
	}
	if isUserInGracePeriod {
		return &commontypes.Image{ImageUrl: warningGraceBadge}
	}
	return &commontypes.Image{ImageUrl: getBadgeUrlByTier(currentTier)}
}

func getBadgeUrlByTier(currentTier beTieringExtPb.Tier) string {
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return tierBadgeRegularIconUrl
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return tierBadgeBasicIconUrl
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return tierBadgePlusIconUrl
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return tierBadgeInfiniteIconUrl
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return tierBadgeSalaryIconUrl
	default:
		if currentTier.IsAaSalaryTier() {
			return tierBadgeAaSalaryIconUrl
		}
		return ""
	}
}

func (s *Service) getTieringPromptTitleAndDeeplink(ctx context.Context, actorId string, toShowPrime, isUserDowngraded, isUserInGracePeriod bool, previousTier, currentTier beTieringExtPb.Tier, isTieringEarnedBenefitsEnabled bool) (*commontypes.Text, *deeplink.Deeplink) {
	if isUserDowngraded && !(previousTier.IsAaSalaryTier() && currentTier.IsAaSalaryTier()) {
		return getPromptTitleByTierForDowngrade(previousTier), tiering.AllPlansDeeplink(currentTier, false)
	}
	if isUserInGracePeriod {
		return getPromptTitleAndDlByTierForGrace(currentTier)
	}

	aaSalDetails, getAaSalDetailsErr := s.salaryProgramClient.GetAaSalaryDetails(ctx, &salaryPb.GetAaSalaryDetailsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(aaSalDetails, getAaSalDetailsErr); grpcErr != nil {
		logger.Error(ctx, "error fetching salary details for actor", zap.Error(grpcErr))
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextPrimeUpgrade, user.AaSalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS), primeAllPlansDeeplink(false)
	}

	switch {
	case currentTier.IsAaSalaryTier():
		return getPromptTitleAndDeeplinkForAaSalary(ctx, currentTier, previousTier, isTieringEarnedBenefitsEnabled, aaSalDetails.GetUpcomingTransferCriteria(), aaSalDetails.GetLatestActivation())
	case toShowPrime:
		return s.getEarnedBenefitsPromptTitleAndDeeplinkToShowPrime(ctx, actorId, aaSalDetails, currentTier, isTieringEarnedBenefitsEnabled)
	default:
		return getEarnedBenefitsPromptTitleByCurrentTier(ctx, currentTier, isTieringEarnedBenefitsEnabled)
	}
}

func (s *Service) getEarnedBenefitsPromptTitleAndDeeplinkToShowPrime(ctx context.Context, actorId string, aaSalDetails *salaryPb.GetAaSalaryDetailsResponse, currentTier beTieringExtPb.Tier, isTieringEarnedBenefitsEnabled bool) (*commontypes.Text, *deeplink.Deeplink) {
	primeUpgradeText, primeDeepLink := commontypes.GetTextFromStringFontColourFontStyle(tierTextPrimeUpgrade, user.AaSalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS), primeAllPlansDeeplink(false)

	if aaSalDetails.GetCurrentStage() == salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_INCOME_ESTIMATED {
		getTransferSetupParamsResp, getTransferSetupParamsErr := s.salaryProgramClient.GetAaSalaryTransferSetupParams(ctx, &salaryPb.GetAaSalaryTransferSetupParamsRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(getTransferSetupParamsResp, getTransferSetupParamsErr); rpcErr != nil {
			if getTransferSetupParamsResp.GetStatus().GetCode() == uint32(salaryPb.GetAaSalaryTransferSetupParamsResponse_NOT_ELIGIBLE) {
				return getEarnedBenefitsPromptTitleByCurrentTier(ctx, currentTier, isTieringEarnedBenefitsEnabled)
			}
			return primeUpgradeText, primeDeepLink
		}
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextAaSalaryNudgeToCommitSalary, user.AaSalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS), aaSalaryLandingScreenDeeplink()
	}
	if aaSalDetails.GetCurrentStage() == salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_SALARY_COMMITTED {
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextAaSalaryNudgeToTransferAmount, user.AaSalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS), aaSalaryLandingScreenDeeplink()
	}
	return primeUpgradeText, primeDeepLink
}

func getPromptTitleAndDeeplinkForAaSalary(ctx context.Context, currentTier, previousTier beTieringExtPb.Tier, isTieringEarnedBenefitsEnabled bool, upcomingTransferCriteria *salaryPb.TransferCriteria, latestActivation *salaryPb.SalaryProgramActivationHistory) (*commontypes.Text, *deeplink.Deeplink) {
	// Calculation for Days
	daysSinceLastActivated := int32(time.Now().Sub(latestActivation.GetActiveFrom().AsTime()).Hours())/24 + 1
	dueDaysLeftForRewardActivation, dueDaysLeftForRewardActivationErr := aasalary.GetAaSalDueDaysForRewardActivation(upcomingTransferCriteria, latestActivation)
	if dueDaysLeftForRewardActivationErr != nil {
		logger.Error(ctx, "error getting due days left for reward activation", zap.Error(dueDaysLeftForRewardActivationErr))
		dueDaysLeftForRewardActivation, daysSinceLastActivated = -1, -1
	}

	if isTieringEarnedBenefitsEnabled {
		promptTitle, aaSalaryDeeplink, getTitleAndDeepLinkErr := getPromptTitleAndDeeplinkAaSalary(currentTier, previousTier, daysSinceLastActivated, dueDaysLeftForRewardActivation)
		if getTitleAndDeepLinkErr != nil {
			logger.Error(ctx, "error getting earned benefits prompt title and deeplink", zap.Error(getTitleAndDeepLinkErr), zap.Any("current_tier", currentTier))
			return nil, nil
		}
		return promptTitle, aaSalaryDeeplink
	}
	return nil, nil
}

func (s *Service) shouldShowSalaryHighConfidenceNotch(currentTier beTieringExtPb.Tier, segmentMembershipMap map[string]*segmentPb.SegmentMembership) bool {
	return !currentTier.IsSalaryRelatedTier() &&
		currentTier != beTieringExtPb.Tier_TIER_FI_INFINITE &&
		segmentMembershipMap != nil &&
		segmentMembershipMap[s.genconf.Tiering().SegmentIds().SalaryHighConfidenceNotchSegment()].GetIsActorMember() &&
		time.Since(s.genconf.Tiering().SalaryHighConfNotchEnabledTimestamp()) >= 0
}

func (s *Service) getPromptTitleForSalaryHighConfNotch(ctx context.Context, salaryRegistrationStarted, salaryRegistrationCompleted bool) *commontypes.Text {
	notchDay := math.Ceil(time.Since(s.genconf.Tiering().SalaryHighConfNotchEnabledTimestamp()).Hours() / 24)

	var stepsRemaining string
	switch {
	case !salaryRegistrationStarted:
		stepsRemaining = "3"
	case salaryRegistrationStarted && !salaryRegistrationCompleted:
		stepsRemaining = "2"
	case salaryRegistrationCompleted:
		stepsRemaining = "1"
	default:
	}

	var notchText string
	switch {
	case notchDay >= 1 && notchDay <= 10:
		notchText = cashBack2percent
	case notchDay >= 11 && notchDay <= 20:
		notchText = fmt.Sprintf(stepsRemFrom30k, stepsRemaining)
	case notchDay >= 21 && notchDay <= 31:
		notchText = zeroBalSalaryAccount
	case notchDay >= 32 && notchDay <= 42:
		notchText = fmt.Sprintf(stepsRemFrom30k, stepsRemaining)
	case notchDay >= 43 && notchDay <= 53:
		notchText = instantSalary50k
	case notchDay >= 54 && notchDay <= 60:
		notchText = get30kMoreOnSalary
	}

	logger.Info(ctx, "showing salary high confidence notch", zap.String("notch_text", notchText))
	return commontypes.GetTextFromStringFontColourFontStyle(notchText, PitchUpgradeToSalaryColour, commontypes.FontStyle_SUBTITLE_XS)
}

func getPromptTitleByTierForDowngrade(previousTier beTieringExtPb.Tier) *commontypes.Text {
	switch previousTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return commontypes.GetTextFromStringFontColourFontStyle(downgradeTextPlus, UserDowngradedColour, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return commontypes.GetTextFromStringFontColourFontStyle(downgradeTextInfinite, UserDowngradedColour, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return commontypes.GetTextFromStringFontColourFontStyle(downgradeTextSalary, UserDowngradedColour, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return commontypes.GetTextFromStringFontColourFontStyle(downgradeTextSalaryLite, UserDowngradedColour, commontypes.FontStyle_SUBTITLE_XS)
	default:
		if previousTier.IsAaSalaryTier() {
			return commontypes.GetTextFromStringFontColourFontStyle(downgradeTextAaSalary, UserDowngradedColour, commontypes.FontStyle_SUBTITLE_XS)
		}
		return nil
	}
}

func getPromptTitleAndDlByTierForGrace(currentTier beTieringExtPb.Tier) (*commontypes.Text, *deeplink.Deeplink) {
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return commontypes.GetTextFromStringFontColourFontStyle(graceTextPlus, UserInGraceColour, commontypes.FontStyle_SUBTITLE_XS), addFundsDeeplink()
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return commontypes.GetTextFromStringFontColourFontStyle(graceTextInfinite, UserInGraceColour, commontypes.FontStyle_SUBTITLE_XS), addFundsDeeplink()
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return commontypes.GetTextFromStringFontColourFontStyle(graceTextSalary, UserInGraceColour, commontypes.FontStyle_SUBTITLE_XS), addFundsDeeplink()
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return commontypes.GetTextFromStringFontColourFontStyle(graceTextSalaryLite, UserInGraceColour, commontypes.FontStyle_SUBTITLE_XS), addFundsDeeplink()
	default:
		if currentTier.IsAaSalaryTier() {
			return commontypes.GetTextFromStringFontColourFontStyle(graceTextAaSalary, UserInGraceColour, commontypes.FontStyle_SUBTITLE_XS), salaryBenefitsDeeplink()
		}
		return nil, nil
	}
}

func getEarnedBenefitsPromptTitleByCurrentTier(ctx context.Context, currentTier beTieringExtPb.Tier, isTieringEarnedBenefitsEnabled bool) (*commontypes.Text, *deeplink.Deeplink) {
	if isTieringEarnedBenefitsEnabled && earnedBenefits.IsTierAllowedForEarnedBenefitsScreen(currentTier) {
		earnedBenefitsDeeplink, getEarnedBenefitsDlErr := earnedBenefits.GetEarnedBenefitsDeeplink(currentTier)
		promptTitle := getEarnedBenefitsPromptTitleByTier(currentTier)
		if getEarnedBenefitsDlErr != nil || promptTitle == nil {
			logger.Error(ctx, "error getting earned benefits prompt title and deeplink", zap.Error(getEarnedBenefitsDlErr), zap.Any("prompt_title", promptTitle), zap.Any("current_tier", currentTier))
			return nil, nil
		}
		return promptTitle, earnedBenefitsDeeplink
	}
	if currentTier == beTieringExtPb.Tier_TIER_FI_SALARY {
		return getEarnedBenefitsPromptTitleByTier(currentTier), salaryBenefitsRedirectionDeeplink()
	}
	return getEarnedBenefitsPromptTitleByTier(currentTier), tiering.AllPlansDeeplink(currentTier, false)
}

func getEarnedBenefitsPromptTitleByTier(tier beTieringExtPb.Tier) *commontypes.Text {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextRegular, user.RegularTierTitleColor, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextBasic, user.StandardTierTitleColour, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextPlus, user.PlusTierTitleColor, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextInfinite, user.InfiniteTierTitleColor, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextSalary, user.SalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS)
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return commontypes.GetTextFromStringFontColourFontStyle(tierTextSalaryLite, user.SalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS)
	default:
		return nil
	}
}

func getTieringPromptBgColor(isUserDowngraded, isUserInGracePeriod bool) string {
	if isUserDowngraded {
		return bgColorDowngrade
	}
	if isUserInGracePeriod {
		return bgColorWGrace
	}
	return bgColorWoGrace
}

func getTieringPromptChevron(isUserDowngraded, isUserInGracePeriod bool) string {
	if isUserInGracePeriod || isUserDowngraded {
		return promptChevronDarkLemon
	}
	return promptChevron
}

func (s *Service) getTieringPromptDeeplink(
	toShowPrime, isUserInGracePeriod, isUserDowngraded, isEarnedBenefitsEnabled bool,
	currentTier, previousTier beTieringExtPb.Tier,
) (*deeplink.Deeplink, error) {
	if toShowPrime && !currentTier.IsAaSalaryTier() {
		return primeAllPlansDeeplink(false), nil
	}
	if isEarnedBenefitsEnabled && earnedBenefits.ShouldShowTieringEarnedBenefitsScreen(currentTier, previousTier, isUserDowngraded) {
		tierToLoad := earnedBenefits.GetTierToLoad(currentTier, previousTier, isUserDowngraded)
		return earnedBenefits.GetEarnedBenefitsDeeplink(tierToLoad)
	}

	if isUserInGracePeriod {
		if currentTier.IsSalaryRelatedTier() {
			return salaryBenefitsDeeplink(), nil
		}
		return addFundsDeeplink(), nil
	}
	if currentTier.IsSalaryOrSalaryLiteTier() {
		return salaryBenefitsRedirectionDeeplink(), nil
	}
	return tiering.AllPlansDeeplink(currentTier, false), nil
}

// getTieringInfoFromRespForSavingsDash
// Return params:
// bool -> isUserInGrace
// beTieringExtPb.Tier -> currentTier
// gmoney.Money -> minBalanceForCurTier
func getTieringInfoFromRespForSavingsDash(tieringPitchResp *beTieringPb.GetTieringPitchV2Response) (bool, beTieringExtPb.Tier,
	*money.Money, error) {
	isUserInGracePeriod, _, graceCheckErr := helper.IsUserInGracePeriod(tieringPitchResp.GetCurrentTier(), tieringPitchResp.GetMovementDetailsList())
	if graceCheckErr != nil {
		return false, 0, nil, errors.Wrap(graceCheckErr, "error checking if user is in grace period")
	}
	minBalance, minBalanceErr := helper.GetMinBalanceFromTierOptions(tieringPitchResp.GetCurrentTier(), tieringPitchResp.GetMovementDetailsList())
	if minBalanceErr != nil {
		return false, 0, nil, errors.Wrap(minBalanceErr, "error fetching min balance for tier")
	}
	feTier := tieringPitchResp.GetCurrentTier()
	return isUserInGracePeriod, feTier, minBalance, nil
}

func getGraceDiscrepancyDeeplink(_ context.Context, currentTier beTieringExtPb.Tier, minBalance *money.Money) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplink.InformationPopupOptions{
				IconUrl: graceInfoPopupIconUrl,
				Ctas: []*deeplink.Cta{
					{
						Text:         graceInfoPopupLaterCtaText,
						DisplayTheme: deeplink.Cta_SECONDARY,
					},
					{
						Text:         graceInfoPopupAddMoneyCtaText,
						DisplayTheme: deeplink.Cta_PRIMARY,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_TRANSFER_IN,
						},
					},
				},
				TextTitle: commontypes.GetPlainStringText(getGraceInfoTitle(currentTier)),
				BodyTexts: []*commontypes.Text{
					{
						BgColor: graceInfoPopupSubTitleBgColor,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: getGraceInfoSubTitle(currentTier, pkgMoney.ToDisplayStringInIndianFormat(minBalance,
								0, true)),
						},
					},
				},
				BgColor: graceInfoPopupBgColor,
			},
		},
	}
}

// Method to get the grace info title for new dashboard based on the tier of user
func getGraceInfoTitleForNewDashboard(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return graceInfoPlusTickerTitle
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return graceInfoInfiniteTickerTitle
	default:
		return ""
	}
}

func getGraceInfoTitle(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return graceInfoPopupPlusTitle
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return graceInfoPopupInfiniteTitle
	default:
		return ""
	}
}

func getGraceInfoSubTitle(tier beTieringExtPb.Tier, minBalanceString string) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return fmt.Sprintf(graceInfoPopupPlusSubTitle, minBalanceString)
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return fmt.Sprintf(graceInfoPopupInfiniteSubTitle, minBalanceString)
	default:
		return ""
	}
}

func (s *Service) getColourForMoney(ctx context.Context, currColour, tieringRedColor, tieringLemonColor string, toShowTieringRelatedInfo bool, tieringPitchResp *beTieringPb.GetTieringPitchV2Response) string {
	if !toShowTieringRelatedInfo {
		return currColour
	}
	isUserInGracePeriod, mmtTimestamp, graceCheckErr := helper.IsUserInGracePeriod(tieringPitchResp.GetCurrentTier(), tieringPitchResp.GetMovementDetailsList())
	if graceCheckErr != nil {
		logger.Error(ctx, "error checking if user is in grace period", zap.Error(graceCheckErr))
		return currColour
	}
	if !isUserInGracePeriod {
		return currColour
	}
	curTimestamp := timestampPb.Now()
	if curTimestamp.AsTime().Add(s.genconf.HomeRevampParams().TieringDangerCutoffDuration()).After(mmtTimestamp.AsTime()) {
		return tieringRedColor
	}
	return tieringLemonColor
}

func getPromptTitleAndDeeplinkAaSalary(currentTier, previousTier beTieringExtPb.Tier, daysSinceLastActivated int32, dueDaysLeftForRewardActivation int32) (*commontypes.Text, *deeplink.Deeplink, error) {
	var promptTitle, currentTierToCashbackPercent string
	var aaSalaryDeeplink *deeplink.Deeplink
	currentTierToCashbackPercent = aasalary.TierToCashbackPercent[currentTier]
	earnedBenefitsDeeplink, getEarnedBenefitsDeeplinkErr := earnedBenefits.GetEarnedBenefitsDeeplink(currentTier)

	switch {
	// Case when user has not activated the tier / error in getting due days left for reward activation and days since last activated
	case daysSinceLastActivated == -1 && dueDaysLeftForRewardActivation == -1:
		promptTitle, aaSalaryDeeplink = tierTextAaSalary, primeAllPlansDeeplink(false)
	// Case when user has recently activated the tier
	case daysSinceLastActivated >= 0 && daysSinceLastActivated <= 3:
		// Handling this case explicitly
		if previousTier == beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3 && currentTier == beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2 {
			promptTitle = tierTextAaSalaryPrimeDowngradeSalaryBand3to2
		} else {
			promptTitle = tierTextAaSalaryUpgradedToPrime
		}
		aaSalaryDeeplink = earnedBenefitsDeeplink
	// Case when due days left for reward activation is between 5 (exclusive) and 10 (inclusive)
	case dueDaysLeftForRewardActivation > 5 && dueDaysLeftForRewardActivation <= 10:
		promptTitle, aaSalaryDeeplink = tierTextAaSalaryDue, aaSalaryLandingScreenDeeplink()
	// Case when due days left for reward activation is between 3 (inclusive) and 5 (inclusive)
	case dueDaysLeftForRewardActivation >= 3 && dueDaysLeftForRewardActivation <= 5:
		promptTitle, aaSalaryDeeplink = fmt.Sprintf(tierTextAaSalaryCashbackEnds, currentTierToCashbackPercent, dueDaysLeftForRewardActivation), aaSalaryLandingScreenDeeplink()
	case dueDaysLeftForRewardActivation == 2:
		promptTitle, aaSalaryDeeplink = fmt.Sprintf(tierTextAaSalaryLosingCashback, currentTierToCashbackPercent), aaSalaryLandingScreenDeeplink()
	case dueDaysLeftForRewardActivation == 1:
		promptTitle, aaSalaryDeeplink = tierTextAaSalaryLastDay, aaSalaryLandingScreenDeeplink()
	// Case when the due date has passed and the user has not reactivated the tier
	case dueDaysLeftForRewardActivation <= 0 && dueDaysLeftForRewardActivation >= -7:
		promptTitle, aaSalaryDeeplink = fmt.Sprintf(tierTextAaSalaryCashbackExpired, currentTierToCashbackPercent), aaSalaryLandingScreenDeeplink()
	// Case when user is activated and due days left for reward activation is more than 10
	case dueDaysLeftForRewardActivation > 10 && daysSinceLastActivated > 3:
		promptTitle, aaSalaryDeeplink = tierTextAaSalary, earnedBenefitsDeeplink
	default:
		promptTitle, aaSalaryDeeplink = tierTextAaSalary, primeAllPlansDeeplink(false)
	}

	return commontypes.GetTextFromStringFontColourFontStyle(promptTitle, user.AaSalaryTierTitleColor, commontypes.FontStyle_SUBTITLE_XS), aaSalaryDeeplink, getEarnedBenefitsDeeplinkErr
}
