package dataprovider

import (
	"context"
	"fmt"
	"sync"

	"github.com/samber/lo"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/api/accounts"
	accountsPb "github.com/epifi/gamma/api/accounts"
	beDepositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/frontend/deeplink"
	homePb "github.com/epifi/gamma/api/frontend/home"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	beTimelinePb "github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	groupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/pkg/feature/release"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	actorActivityEnumPb "github.com/epifi/gamma/api/order/actoractivity/enums"

	"google.golang.org/protobuf/types/known/timestamppb"

	actorPb "github.com/epifi/gamma/api/actor"
	orderPb "github.com/epifi/gamma/api/order"
)

type PayRecentActivitiesDataProvider struct {
	orderClient                             orderPb.OrderServiceClient
	actorClient                             actorPb.ActorClient
	whitelistedWorkflowsForRecentActivities []orderPb.OrderWorkflow
	timelineClient                          beTimelinePb.TimelineServiceClient
	piClient                                piPb.PiClient
	depositClient                           beDepositPb.DepositClient
	homeParams                              *config.HomeParams
	conf                                    *config.Config
	releaseEvaluator                        release.IEvaluator
	usersClient                             userPb.UsersClient
	userGroupClient                         groupPb.GroupClient
}

func NewPayRecentActivitiesDataProvider(orderClient orderPb.OrderServiceClient,
	actorClient actorPb.ActorClient,
	whitelistedWorkflowsForRecentActivities []orderPb.OrderWorkflow,
	timelineClient beTimelinePb.TimelineServiceClient,
	piClient piPb.PiClient,
	depositClient beDepositPb.DepositClient,
	conf *config.Config,
	releaseEvaluator release.IEvaluator,
	usersClient userPb.UsersClient,
	userGroupClient groupPb.GroupClient) RecentActivityDataProvider {
	var recentActivityDataProvider RecentActivityDataProvider = &PayRecentActivitiesDataProvider{
		orderClient:                             orderClient,
		actorClient:                             actorClient,
		whitelistedWorkflowsForRecentActivities: whitelistedWorkflowsForRecentActivities,
		timelineClient:                          timelineClient,
		piClient:                                piClient,
		depositClient:                           depositClient,
		homeParams:                              conf.HomeParams,
		conf:                                    conf,
		releaseEvaluator:                        releaseEvaluator,
		usersClient:                             usersClient,
		userGroupClient:                         userGroupClient,
	}
	return recentActivityDataProvider
}

type orderProcessingData struct {
	order            *orderPb.Order
	link             *deeplink.Deeplink
	otherActorId     string
	textColour       string
	txnImageUrl      string
	title            string
	imageURL         string
	depositAccountId string
	accountType      accountsPb.Type
	isDeposit        bool
	isP2PInvestment  bool
}

// orderHandling processes the orders and returns the user activity response
func (p *PayRecentActivitiesDataProvider) buildActivities(ctx context.Context, currentActorId string, orders []*orderPb.Order) ([]*homePb.UserActivityResponse, error) {
	// First pass: Collect data and identify which actors need details
	processingData := make([]*orderProcessingData, 0, len(orders))
	var nonDepositTxnActorIds []string

	for _, order := range orders {
		// Skip add funds orders in the paid state, as the payment is made to the pool account at that point, which does not indicate that the order itself is paid.
		if order.GetStatus() == orderPb.OrderStatus_PAID && order.IsAddFundsOrder() {
			continue
		}
		// For international fund transfer, if the order is in payment state, then it is considered as paid
		if order.GetWorkflow() == orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER && order.GetStatus() == orderPb.OrderStatus_IN_PAYMENT {
			order.Status = orderPb.OrderStatus_PAID
		}
		data, err := p.enrichOrderDetails(ctx, currentActorId, order)
		if err != nil {
			return nil, err
		}
		if !data.isDeposit {
			if !lo.Contains(nonDepositTxnActorIds, data.otherActorId) {
				nonDepositTxnActorIds = append(nonDepositTxnActorIds, data.otherActorId)
			}
		}
		processingData = append(processingData, data)
	}

	nonDepositTxnActorDetails, err := getActorDetails(ctx, p.actorClient, nonDepositTxnActorIds)
	if err != nil {
		return nil, err
	}

	// Bulk fetch timeline details
	timelineDetails, err := p.timelineClient.GetTimelinesForActor(ctx, &beTimelinePb.GetTimelinesForActorRequest{
		PrimaryActorId:    currentActorId,
		SecondaryActorIds: nonDepositTxnActorIds,
	})
	if te := epifigrpc.RPCError(timelineDetails, err); te != nil {
		return nil, fmt.Errorf("failed to fetch timeline details: %w", err)
	}

	// Construct responses
	responses := make([]*homePb.UserActivityResponse, 0, len(processingData))
	for _, data := range processingData {
		response, err := p.constructUserActivityResponse(data, nonDepositTxnActorDetails, timelineDetails, currentActorId)
		if err != nil {
			logger.Error(ctx, "failed to build response for order", zap.Error(err))
			return nil, fmt.Errorf("failed to build response for order: %w", err)
		}
		response.ActivityTime = data.order.GetUpdatedAt()
		responses = append(responses, response)
	}

	return responses, nil
}

func getActorDetails(ctx context.Context, actorClient actorPb.ActorClient, actorIds []string) (*actorPb.GetEntityDetailsResponse, error) {
	if len(actorIds) == 0 {
		// No actor IDs to fetch, return an empty response and avoid unnecessary RPC call
		// RPC also returns error if actorIds is empty
		return &actorPb.GetEntityDetailsResponse{
			Status: rpc.StatusOk(),
		}, nil
	}
	entityDetails, err := actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{
		ActorIds: actorIds,
	})
	if te := epifigrpc.RPCError(entityDetails, err); te != nil {
		logger.Error(ctx, "failed to fetch actor details", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch actor details: %w", err)
	}
	return entityDetails, err
}

// prepareOrderDetails determines otherActorId, colors, etc.
func (p *PayRecentActivitiesDataProvider) enrichOrderDetails(ctx context.Context, currentActorId string, order *orderPb.Order) (*orderProcessingData, error) {
	data := &orderProcessingData{
		order: order,
	}

	// Check if this is a P2P investment
	data.isP2PInvestment = order.GetWorkflow() == orderPb.OrderWorkflow_P2P_INVESTMENT ||
		order.GetWorkflow() == orderPb.OrderWorkflow_P2P_WITHDRAWAL ||
		orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_JUMP_P2P_INVESTMENT)

	// Check if this is a deposit
	data.isDeposit = orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_DEPOSIT)

	switch {
	case data.isDeposit:
		data.otherActorId = order.GetToActorId()
		var err error
		data.title, data.accountType, data.textColour, data.imageURL, data.depositAccountId, data.txnImageUrl, err = p.getDepositActivityDetails(ctx, order, currentActorId)
		if err != nil {
			return nil, fmt.Errorf("failed to get deposit activity details: %w", err)
		}

	case data.isP2PInvestment:
		var err error
		data.textColour, data.imageURL, data.otherActorId, data.txnImageUrl, err = p.getP2PActivityDetails(order, currentActorId)
		if err != nil {
			return nil, fmt.Errorf("failed to get P2P activity details: %w", err)
		}

	case order.GetFromActorId() == currentActorId && order.GetStatus() == orderPb.OrderStatus_IN_PAYMENT:
		// Pending transaction
		data.otherActorId = order.GetToActorId()
		data.textColour = p.homeParams.AmountColourMap.PendingColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.PendingUrl

	case order.GetFromActorId() == currentActorId && order.GetStatus() == orderPb.OrderStatus_PAYMENT_FAILED:
		// Failed transaction
		data.otherActorId = order.GetToActorId()
		data.textColour = p.homeParams.AmountColourMap.FailedColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.FailedUrl

	case order.GetFromActorId() == currentActorId && order.GetStatus() == orderPb.OrderStatus_PAYMENT_REVERSED:
		// Reversed transaction
		data.otherActorId = order.GetToActorId()
		data.textColour = p.homeParams.AmountColourMap.ReversedColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.ReversedUrl

	case order.GetToActorId() == currentActorId && order.GetStatus() == orderPb.OrderStatus_PAYMENT_FAILED:
		// Failed credit transaction (only for specific workflows like ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER)
		data.otherActorId = order.GetFromActorId()
		data.textColour = p.homeParams.AmountColourMap.FailedColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.FailedUrl

	case order.GetToActorId() == currentActorId && order.GetWorkflow() == orderPb.OrderWorkflow_ADD_FUNDS && order.GetStatus() == orderPb.OrderStatus_SETTLEMENT_FAILED:
		// Add Funds 2nd leg settlement failed txn
		data.otherActorId = order.GetFromActorId()
		data.textColour = p.homeParams.AmountColourMap.FailedColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.FailedUrl

	case order.GetToActorId() == currentActorId && order.GetWorkflow() == orderPb.OrderWorkflow_ADD_FUNDS && order.GetStatus() == orderPb.OrderStatus_IN_SETTLEMENT:
		// Add Funds 2nd leg settlement pending txn
		data.otherActorId = order.GetFromActorId()
		data.textColour = p.homeParams.AmountColourMap.PendingColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.PendingUrl

	case order.GetToActorId() == currentActorId:
		// Successful credit transaction
		data.otherActorId = order.GetFromActorId()
		data.textColour = p.homeParams.AmountColourMap.CreditColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.CreditUrl

	case order.GetFromActorId() == currentActorId:
		// Successful debit transaction
		data.otherActorId = order.GetToActorId()
		data.textColour = p.homeParams.AmountColourMap.DefaultColour
		data.txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.DebitUrl

	default:
		// Log an error with the order ID to help identify and debug issues related to specific orders
		logger.Error(ctx, "failed to get deposit activity details", zap.String(logger.ORDER_ID, order.GetId()))

	}
	return data, nil
}

// constructUserActivityResponse constructs the user activity response for a single order
func (p *PayRecentActivitiesDataProvider) constructUserActivityResponse(data *orderProcessingData,
	nonDepositTxnActorDetails *actorPb.GetEntityDetailsResponse, timelineDetails *beTimelinePb.GetTimelinesForActorResponse, currentActorId string) (*homePb.UserActivityResponse, error) {

	// For non-deposit cases, get actor details from bulk response
	if data.title == "" {
		actorDetail, exist := lo.Find(nonDepositTxnActorDetails.GetEntityDetails(), func(actor *actorPb.GetEntityDetailsResponse_EntityDetail) bool {
			return actor.GetActorId() == data.otherActorId
		})
		if !exist {
			return nil, fmt.Errorf("actor details not found for actor ID: %s", data.otherActorId)
		}
		data.title = actorDetail.GetName().ToString()
		data.imageURL = actorDetail.GetProfileImageUrl()
	}

	// For non-deposit cases, set up timeline link
	if !data.isDeposit && timelineDetails != nil {
		// Find matching timeline from bulk response
		for _, timeline := range timelineDetails.GetTimelines() {
			isMatch := false

			// Self transfer case
			if currentActorId == data.otherActorId {
				isMatch = timeline.GetPrimaryActorId() == currentActorId && timeline.GetSecondaryActorId() == currentActorId
			} else {
				// Non-self-transfer case
				isMatch = timeline.GetPrimaryActorId() == data.otherActorId || timeline.GetSecondaryActorId() == data.otherActorId
			}

			if isMatch {
				data.link = &deeplink.Deeplink{
					Screen: deeplink.Screen_TIMELINE,
					ScreenOptions: &deeplink.Deeplink_TimelineScreenOptions{
						TimelineScreenOptions: &deeplink.TimelineScreenOptions{
							TimelineId: timeline.GetId(),
						},
					},
				}
				break
			}
		}
	}

	// Handle deposit links
	if data.isDeposit {
		if data.depositAccountId != "" {
			data.link = &deeplink.Deeplink{
				Screen: deeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
				ScreenOptions: &deeplink.Deeplink_DepositDetailsScreenOptions{
					DepositDetailsScreenOptions: &deeplink.DepositAccountDetailsScreenOptions{
						AccountId:   data.depositAccountId,
						DepositType: data.accountType,
					},
				},
			}
		} else {
			data.link = &deeplink.Deeplink{
				Screen: deeplink.Screen_DEPOSIT_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_DepositAccountLandingScreenOption{
					DepositAccountLandingScreenOption: &deeplink.DepositAccountLandingScreenOptions{
						DepositType: data.accountType,
					},
				},
			}
		}
	}

	return &homePb.UserActivityResponse{
		Title:           data.title,
		Amount:          types.GetFromBeMoney(data.order.GetAmount()),
		Icon:            &commontypes.Image{ImageUrl: data.imageURL},
		Link:            data.link,
		ImageColourCode: actorPb.GetColourCodeForActor(data.otherActorId),
		TitleColourCode: data.textColour,
		TxnType:         &commontypes.Image{ImageUrl: data.txnImageUrl},
	}, nil
}

// GetRecentActivity fetches recent activities for the user
// Changes required to show/hide pay transactions:
// 1. If the order status is in paid (and should not be shown) or in settled, fulfilled, in_settlement (and has to be shown) then add the respective order workflow,status in the mandatoryStatusWorkflow in order/dao/order_for_actor_with_status_and_workflow.go. For more context, refer to the func getMandatoryStatusWorkflowFilter in the same file
// 2. If the order has any other {workflow,status} combination, and we want to show in the recent user activities, add the status to workflow mapping in the OrderStatusWorkflowTypeFilters in the below function
func (p *PayRecentActivitiesDataProvider) GetRecentActivity(ctx context.Context, currentActorId string, pageSize int32, toTime *timestamppb.Timestamp, recentActivitiesChannelResponse chan<- *RecentActivitiesChannelResponse, wg *sync.WaitGroup) {
	defer wg.Done()

	fieldMask := []orderPb.OrderFieldMask{
		orderPb.OrderFieldMask_ID,
		orderPb.OrderFieldMask_TO_ACTOR_ID,
		orderPb.OrderFieldMask_FROM_ACTOR_ID,
		orderPb.OrderFieldMask_AMOUNT,
		orderPb.OrderFieldMask_WORKFLOW,
		orderPb.OrderFieldMask_TAGS,
		orderPb.OrderFieldMask_STATUS,
		orderPb.OrderFieldMask_UPDATED_AT,
	}

	userRecentActivityRes, err := p.orderClient.GetOrdersForActorWithNoOffset(ctx, &orderPb.GetOrdersForActorWithNoOffsetRequest{
		ActorId:         currentActorId,
		PageSize:        pageSize,
		TransactionType: orderPb.GetOrdersForActorWithNoOffsetRequest_BOTH,
		StatusFilters:   []orderPb.GetOrdersForActorWithNoOffsetRequest_OrderStatusFilter{orderPb.GetOrdersForActorWithNoOffsetRequest_SUCCESS},
		ToTime:          toTime,
		FieldMask:       fieldMask,
		SortBy:          orderPb.OrderFieldMask_UPDATED_AT,
		Workflows:       p.whitelistedWorkflowsForRecentActivities,
		OrderStatusWorkflowTypeFiltersForDebitTransactions: []*orderPb.OrderStatusAndWorkflowTypeFilter{
			{
				OrderStatus: orderPb.OrderStatus_IN_PAYMENT,
				Workflows: []orderPb.OrderWorkflow{
					orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
					orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER,
					orderPb.OrderWorkflow_OFF_APP_UPI,
					orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
					orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
					orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
				},
			}, {
				OrderStatus: orderPb.OrderStatus_PAYMENT_FAILED,
				Workflows: []orderPb.OrderWorkflow{
					orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
					orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER,
					orderPb.OrderWorkflow_NO_OP,
					orderPb.OrderWorkflow_OFF_APP_UPI,
					orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
					orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
					orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
				},
			}, {
				OrderStatus: orderPb.OrderStatus_PAYMENT_REVERSED,
				Workflows: []orderPb.OrderWorkflow{
					orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
					orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER,
					orderPb.OrderWorkflow_OFF_APP_UPI,
					orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
					orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
					orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
				},
			},
		},
		OrderStatusWorkflowTypeFiltersForCreditTransactions: []*orderPb.OrderStatusAndWorkflowTypeFilter{
			{
				OrderStatus: orderPb.OrderStatus_PAYMENT_FAILED,
				Workflows: []orderPb.OrderWorkflow{
					orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
				},
			},
			{
				OrderStatus: orderPb.OrderStatus_IN_SETTLEMENT,
				Workflows: []orderPb.OrderWorkflow{
					orderPb.OrderWorkflow_ADD_FUNDS,
				},
			},
			{
				OrderStatus: orderPb.OrderStatus_SETTLEMENT_FAILED,
				Workflows: []orderPb.OrderWorkflow{
					orderPb.OrderWorkflow_ADD_FUNDS,
				},
			},
		},
		EntryPointType: orderPb.EntryPointType_ENTRY_POINT_TYPE_UI,
	})
	switch {
	case err != nil:
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_FI_TRANSACTION,
			Err:                  fmt.Errorf("error from fi transaction source %w", err),
		}
		return
	case userRecentActivityRes.GetStatus().IsRecordNotFound():
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_FI_TRANSACTION,
			Err:                  nil,
		}
		return
	case !userRecentActivityRes.GetStatus().IsSuccess():
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_FI_TRANSACTION,
			Err:                  fmt.Errorf("non-success error code for fi transaction %s", userRecentActivityRes.GetStatus().String()),
		}
		return
	default:
		logger.Debug(ctx, "successfully fetched fi transaction")
	}

	userActivityResponses, err := p.buildActivities(ctx, currentActorId, userRecentActivityRes.GetOrders())
	if err != nil {
		recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
			UserActivityResponse: nil,
			ActivitySource:       actorActivityEnumPb.ActivitySource_FI_TRANSACTION,
			Err:                  fmt.Errorf("error processing orders: %w", err),
		}
		return
	}

	recentActivitiesChannelResponse <- &RecentActivitiesChannelResponse{
		UserActivityResponse: userActivityResponses,
		ActivitySource:       actorActivityEnumPb.ActivitySource_FI_TRANSACTION,
		Err:                  nil,
	}

}

// getDepositActivityDetails returns nil, activity title, icon url for a savings Payment instrument
// TODO(sakthi) revisit for lint check
// nolint: funlen
func (p *PayRecentActivitiesDataProvider) getDepositActivityDetails(ctx context.Context, order *orderPb.Order, actorId string) (string, accounts.Type, string, string, string,
	string, error) {
	var (
		accountPiFrom  *piPb.Account
		accountPiTo    *piPb.Account
		depositTitle   string
		depositAccount *beDepositPb.DepositAccount
		accountType    accountsPb.Type
		textColour     string
		imageUrl       string
		err            error
		txnImageUrl    string
	)

	orderRes, err := p.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{OrderId: order.GetId()})
	if err = epifigrpc.RPCError(orderRes, err); err != nil {
		return "", 0, "", "", "", "", fmt.Errorf("failed to fetch order with txn: %w", err)
	}

	if len(orderRes.GetOrderWithTransactions().GetTransactions()) == 0 {
		return "", 0, "", "", "", "", fmt.Errorf("deposit order doesnt have txn")
	}

	piFrom := orderRes.GetOrderWithTransactions().GetTransactions()[0].GetPiFrom()
	piTo := orderRes.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()

	accountPiFrom, err = p.getAccountDetailsFromPi(ctx, piFrom)
	if err != nil {
		return "", 0, "", "", "", "", fmt.Errorf("failed to get account details for pi : %s: %w", piFrom, err)
	}

	accountPiTo, err = p.getAccountDetailsFromPi(ctx, piTo)
	if err != nil {
		return "", 0, "", "", "", "", fmt.Errorf("failed to get account details for pi : %s: %w", piTo, err)
	}
	textColour = p.homeParams.AmountColourMap.SavingsColour
	txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.InvestUrl

	// if PiTo belongs to a deposit account, the txn can either be an add funds to SD or deposit creation one
	// if PiFrom belongs to a deposit account, the txn can either be for deposit preclosure or deposit maturity
	if accountsPb.IsDepositAccount(accountPiTo.GetAccountType()) {
		depositAccount, err = p.getDepositAccount(ctx, accountPiTo.GetActualAccountNumber(), accountPiTo.GetIfscCode())
		if err != nil {
			return "", 0, "", "", "", "", fmt.Errorf("failed to get deposit account: %w", err)
		}
		// categorise deposit txns
		depositTitle = depositAccount.GetName()
	} else {
		// TODO(harish): handle FD interest credit txns in actor activity
		//  Monorail:https://monorail.pointz.in/p/fi-app/issues/detail?id=19555
		if !accountsPb.IsDepositAccount(accountPiFrom.GetAccountType()) {
			depositTitle = "Interest credited for FD"
			depositAccount = &beDepositPb.DepositAccount{Type: accountsPb.Type_FIXED_DEPOSIT}
			textColour = p.homeParams.AmountColourMap.CreditColour
			txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.CreditUrl
		} else {
			depositAccount, err = p.getDepositAccount(ctx, accountPiFrom.GetActualAccountNumber(), accountPiFrom.GetIfscCode())
			if err != nil {
				return "", 0, "", "", "", "", fmt.Errorf("failed to get deposit account: %w", err)
			}
			// categorise deposit txns
			depositTitle = depositAccount.GetName()
			textColour = p.homeParams.AmountColourMap.CreditColour
			txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.CreditUrl
		}
	}

	accountType = depositAccount.GetType()

	depositIcons := p.conf.HomeV2DepositIcons
	switch depositAccount.GetType() {
	case accountsPb.Type_FIXED_DEPOSIT:
		imageUrl = depositIcons.FDUrl
	case accountsPb.Type_SMART_DEPOSIT:
		imageUrl = depositIcons.SDUrl
	}
	return depositTitle, accountType, textColour, imageUrl, depositAccount.GetId(), txnImageUrl, nil
}

// nolint:unparam
func (p *PayRecentActivitiesDataProvider) getP2PActivityDetails(order *orderPb.Order, actorId string) (string, string, string, string, error) {
	var (
		textColour   string
		imageUrl     string
		otherActorId string
		txnImageUrl  string
	)
	textColour = p.homeParams.AmountColourMap.SavingsColour
	imageUrl = p.conf.P2PIcons.LiquiloansIcon

	switch {
	case order.GetWorkflow() == orderPb.OrderWorkflow_P2P_INVESTMENT || order.GetFromActorId() == actorId:
		otherActorId = order.GetToActorId()
		txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.InvestUrl
	case order.GetWorkflow() == orderPb.OrderWorkflow_P2P_WITHDRAWAL || order.GetToActorId() == actorId:
		otherActorId = order.GetFromActorId()
		txnImageUrl = p.conf.HomeRevampParams.RecentActivitiesParams.HomeTxnTypeImageUrls.CreditUrl
	}

	return textColour, imageUrl, otherActorId, txnImageUrl, nil
}

// getAccountDetailsFromPi fetches account number, ifsc code and type of account given a PI.
// returns error in case PI is not of account type of rpc call to PI service fails
func (p *PayRecentActivitiesDataProvider) getAccountDetailsFromPi(ctx context.Context, piId string) (*piPb.Account, error) {

	pi, err := p.getPIDetails(ctx, piId)
	if err != nil {
		return nil, err
	}

	if pi.Type != piPb.PaymentInstrumentType_BANK_ACCOUNT && pi.Type != piPb.PaymentInstrumentType_GENERIC {
		return nil, fmt.Errorf("payment instrument is of type: %s expected %s : ", pi.Type.String(),
			piPb.PaymentInstrumentType_BANK_ACCOUNT.String())
	}

	return pi.GetAccount(), nil
}

// getPIDetails calls pi service's `GetPiByID` RPC and returns payment-instrument
func (p *PayRecentActivitiesDataProvider) getPIDetails(ctx context.Context, piId string) (*piPb.PaymentInstrument, error) {
	piReq := &piPb.GetPiByIdRequest{
		Id: piId,
	}
	piRes, err := p.piClient.GetPiById(ctx, piReq)
	switch {
	case err != nil:
		return nil, fmt.Errorf("payment instrument details can't be fetched due to rpc failure: PI: %s : %w",
			piId, err)
	case piRes.Status.IsRecordNotFound():
		return nil, fmt.Errorf("PI record not found for PI: %s: ", piId)
	case !piRes.Status.IsSuccess():
		return nil, fmt.Errorf("got unexpected response from payment instrument service: %v", piRes.Status)
	}

	return piRes.PaymentInstrument, nil
}

// getDepositAccount fetches deposit account via account number and ifsc code
func (p *PayRecentActivitiesDataProvider) getDepositAccount(ctx context.Context, accountNumber, ifscCode string) (*beDepositPb.DepositAccount, error) {
	res, err := p.depositClient.GetByAccountNumberAndIfsc(ctx, &beDepositPb.GetByAccountNumberAndIfscRequest{
		AccountNumber: accountNumber,
		IfscCode:      ifscCode,
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("failed to get deposit account by account number and ifsc, err %w", err)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("got unsuccessful status response from depositClient.GetByAccountNumberAndIfsc() %v", res.GetStatus())
	default:
		return res.GetAccount(), nil
	}
}

func (p *PayRecentActivitiesDataProvider) IsFeatureEnabled(ctx context.Context, featureName types.Feature, actorId string) (bool, error) {
	actorRes, err := p.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err = epifigrpc.RPCError(actorRes, err); err != nil {
		return false, fmt.Errorf("failed to get actor details, %w", err)
	}
	var (
		userId = actorRes.GetActor().GetEntityId()
	)
	// get user info
	userResp, err := p.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: userId,
		},
	})

	if te := epifigrpc.RPCError(userResp, err); te != nil {
		return false, fmt.Errorf("failed to get user details, %w", err)
	}
	groupResp, err := p.userGroupClient.GetGroupsMappedToEmail(ctx, &groupPb.GetGroupsMappedToEmailRequest{
		Email: userResp.GetUser().GetProfile().GetEmail(),
	})
	if te := epifigrpc.RPCError(groupResp, err); te != nil {
		return false, fmt.Errorf("failed to get user group details, %w", err)
	}
	enableFeature, err := p.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(featureName).WithActorId(actorId).WithUserGroup(groupResp.GetGroups()))
	if err != nil {
		return false, fmt.Errorf("failed to evaluate feature, %w", err)
	}
	return enableFeature, nil
}
