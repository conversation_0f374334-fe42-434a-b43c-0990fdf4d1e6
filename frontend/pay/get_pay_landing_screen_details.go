package pay

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	pkgColors "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	types "github.com/epifi/gamma/api/typesv2"
	typesPayPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/pay_search_screen_v2"
	qrScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/qr"
	typesUpiPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"

	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pay/constants"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
)

var (
	// the ui info is independent of any user info
	// so, we can cache it (in memory).
	payLandingScreenPopUpUiContent *fePayPb.PayLandingPopUp
)

type quickAction struct {
	ctaText     string
	ctaDeeplink *deeplink.Deeplink
	iconUrl     string
	tag         *ui.IconTextComponent
	lottieUrl   string
}

// GetPayLandingScreenDetails : is used to fetch / generate all the details on pay
// landing screen for the users.
// NOTE - As of now this Pay landing screen is client handled. We need to make it completely
//
//	BE driven. But for now only Banner will be driven from BE. Rest handling will be on
//	client itself.
func (s *Service) GetPayLandingScreenDetails(ctx context.Context, req *fePayPb.GetPayLandingScreenDetailsRequest) (*fePayPb.GetPayLandingScreenDetailsResponse, error) {
	var (
		res = &fePayPb.GetPayLandingScreenDetailsResponse{
			RespHeader: &headerPb.ResponseHeader{},
		}
		err     error
		actorId = req.GetReq().GetAuth().GetActorId()
		errResp = func(status *rpcPb.Status, errorView *errors.ErrorView) (*fePayPb.GetPayLandingScreenDetailsResponse, error) {
			return &fePayPb.GetPayLandingScreenDetailsResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status:    status,
					ErrorView: errorView,
				},
			}, nil
		}
		// isPayDesignFixitEnabled flag is used to check if we need to show new design-fixit changes for pay landing screen.
		// we will be using Home_Design_Enhancements feature flag along with minimum app version restrictions
		// to check if we need to show new design-fixit changes for pay landing screen.
		isPayDesignFixitEnabled bool
	)

	// vpa is required to be shown on the vpa migration screen
	// This pkg method will fetch the vpa of the primary account.
	// If primary account is other than Fi, then anyways vpa migration
	// won't be required, so it is safe to use this method to fetch vpa
	// rather than fetching specifically for Fi account
	vpa, _, _, err := fePkgUpi.GetVpaAndDerivedAccIdForPrimaryAccount(ctx, actorId, s.upiOnboardingClient, s.accountPiClient, s.savingsClient)
	if err != nil {
		logger.Error(ctx, "error while fetching vpa for the actor",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err))

		return errResp(rpcPb.StatusInternal(), defaultErrorView)
	}

	// Check if UPI Mapper is enabled
	isUpiMapperEnabled := fePkgUpi.IsUpiMapperEnabledForActor(ctx, actorId, s.upiOnboardingClient)
	isPayDesignFixitEnabled = s.shouldShowNewPayLandingScreen(ctx, actorId, req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAppVersionCode())

	payLandingScreenBanner, payLandingScreenBannerErr := s.getPayLandingScreenBanner(ctx, actorId, vpa, isUpiMapperEnabled, isPayDesignFixitEnabled)
	if payLandingScreenBannerErr != nil {
		return errResp(rpcPb.StatusInternal(), defaultErrorView)
	}
	if payLandingScreenBanner != nil {
		populateUiInfoForPayLandingScreenBanner(payLandingScreenBanner, res)
	}

	isPaySearchV2Enabled, err := s.isPaySearchV2Enabled(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error evaluating if pay search v2 enabed", zap.Error(err))
	}

	if isPaySearchV2Enabled {
		s.populateUiInfoForPaySearchV2(ctx, actorId, res, isPayDesignFixitEnabled)
	}

	res.PayLandingPopUp, err = s.getOneClickTpapEnablementPopUp(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking / generating the one click tpap enablement pop up for actor",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err))

		return errResp(rpcPb.StatusInternal(), defaultErrorView)
	}

	// Populate widget Tiles like All Transactions, AutoPay , Manage Upi Numbers.
	s.populateUiInfoForPayWidgets(res, isPayDesignFixitEnabled, isUpiMapperEnabled)

	// populating VpaWidget, TopSectionBackground and FooterTrustMarker
	// These are new fields specifically added for the new design fixit changes.
	if isPayDesignFixitEnabled {
		res.PayLandingScreenLayout.Title = s.getPayLandingScreenTitle()
		res.PayLandingScreenLayout.Widgets = append(res.PayLandingScreenLayout.Widgets, s.getVpaWidget(vpa))
		res.PayLandingScreenLayout.TopSectionBackground = getTopSectionBackgroundImage()
		res.PayLandingScreenLayout.FooterSection = s.getFooterTrustMarker()
	}
	res.GetRespHeader().Status = rpcPb.StatusOk()
	return res, nil
}

// getOneClickTpapEnablementPopUp : generates the pop up for the one click tpap enablement flow
func (s *Service) getOneClickTpapEnablementPopUp(ctx context.Context, actorId string) (*fePayPb.PayLandingPopUp, error) {
	checkEligibilityForPopUpRes, err := s.upiOnboardingClient.CheckEligibilityForOneClickFlowPopUp(ctx, &upiOnboardingPb.CheckEligibilityForOneClickFlowPopUpRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(checkEligibilityForPopUpRes, err); err != nil {
		return nil, fmt.Errorf("error while checking if user is eligible for one click flow pop up : %w", err)
	}
	if !checkEligibilityForPopUpRes.GetIsEligible() {
		return nil, nil
	}

	// populate ui content for then pop up
	payLandingScreenPopUp := s.populateUiContentForPayLandingScreenPopUp()

	// generate deeplink to link connected accounts via tpap
	if payLandingScreenPopUp.GetCta().Deeplink, err = getDeeplinkToLinkConnectedAccountsViaTpap(); err != nil {
		return nil, err
	}
	return payLandingScreenPopUp, nil
}

func (s *Service) populateUiContentForPayLandingScreenPopUp() *fePayPb.PayLandingPopUp {
	if payLandingScreenPopUpUiContent != nil {
		return payLandingScreenPopUpUiContent
	}

	payLandingScreenPopUpUiContent = &fePayPb.PayLandingPopUp{
		PayLandingPopType: fePayPb.PayLandingPopType_PAY_LANDING_POP_TYPE_LINK_CONNECTED_ACCOUNTS_VIA_TPAP,
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			"Now pay with any\n bank account on Fi",
			pkgColors.ColorOnLightHighEmphasis,
			commontypes.FontStyle_SUBTITLE_1,
		),
		Icon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/upi/icons/one_click_popup_logo.png",
			91,
			91,
		),
		FeatureInfo: []*ui.IconTextComponent{
			{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(
						"Get insights into your spending",
						pkgColors.ColorOnDarkLowEmphasis,
						commontypes.FontStyle_SUBTITLE_S,
					),
				},
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
					"https://epifi-icons.pointz.in/upi/icons/magnify.png",
					28,
					28,
				),
				LeftImgTxtPadding: 12,
			},
			{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(
						"Track all your balances & spends",
						pkgColors.ColorOnDarkLowEmphasis,
						commontypes.FontStyle_SUBTITLE_S,
					),
				},
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
					"https://epifi-icons.pointz.in/upi/icons/money_bank.png",
					28,
					28,
				),
				LeftImgTxtPadding: 12,
			},
		},
		Cta: &deeplink.Cta{
			Type:         deeplink.Cta_CONTINUE,
			Text:         "Enable this",
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		CloseButton: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/upi/icons/close_button.png",
			27,
			27,
		),
		BgColor:         pkgColors.ColorSnow,
		TncConsent:      s.getTncContent(),
		TncCheckBoxList: getTncCheckBoxList(),
	}
	return payLandingScreenPopUpUiContent
}

func getDeeplinkToLinkConnectedAccountsViaTpap() (*deeplink.Deeplink, error) {
	linkConnectedAccountsViaTpapDeeplink, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_LINK_CONNECTED_ACCOUNTS_VIA_TPAP, &typesUpiPb.LinkConnectedAccountsViaTpapScreenOptions{
		ShouldTriggerLinkingAutomatically: true,
	})
	if err != nil {
		return nil, fmt.Errorf("error while generating deeplink to link connected accounts via tpap : %w", err)
	}
	return linkConnectedAccountsViaTpapDeeplink, nil
}

// getTncContent - creates and returns the TnC content to be shown on list account screen
func (s *Service) getTncContent() *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_Html{
			Html: fmt.Sprintf(s.conf.ListAccountScreenParams.Tnc, s.conf.ListAccountScreenParams.TncHyperlinkFontColor,
				s.conf.ListAccountScreenParams.TncUrl, s.conf.ListAccountScreenParams.TncHyperlinkFontColor, s.conf.ListAccountScreenParams.PrivacyNoticeUrl),
		},
		FontColor: s.conf.ListAccountScreenParams.TncContentFontColor,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_BODY_4_PARA,
		},
	}
}

// getTncCheckBoxList - creates and returns the TnC content to be shown
// to consent of the user to link AA accounts via TPAP / UPI
func getTncCheckBoxList() *fePayPb.TncCheckBoxList {
	return &fePayPb.TncCheckBoxList{
		CheckBoxes: []*widgetPb.CheckboxItem{
			{
				Id: consent.ConsentType_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP.String(),
				DisplayText: commontypes.GetTextFromStringFontColourFontStyle("I consent to Epifi Wealth sharing with Epifi Tech, the names of bank accounts connected by me for the purpose of linking these accounts with my UPI ID.",
					pkgColors.ColorSteel,
					commontypes.FontStyle_BODY_4_PARA),
				IsChecked: true,
			},
			{
				Id: consent.ConsentType_LINK_CONNECTED_ACCOUNTS_VIA_UPI.String(),
				DisplayText: commontypes.GetTextFromStringFontColourFontStyle("I consent to Epifi Tech linking the displayed account with my UPI ID.",
					pkgColors.ColorSteel,
					commontypes.FontStyle_BODY_4_PARA),
				IsChecked: true,
			},
		},
	}
}

func (s *Service) isPaySearchV2Enabled(ctx context.Context, actorId string) (bool, error) {
	return s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_PAY_SEARCH_V2).WithActorId(actorId))
}

func populateUiInfoForPayLandingScreenBanner(banner *fePayPb.Banner, res *fePayPb.GetPayLandingScreenDetailsResponse) {
	res.PayLandingScreenLayout = &fePayPb.PayLandingScreenLayout{
		Widgets: []*fePayPb.PayLandingScreenWidget{
			{
				WidgetType: fePayPb.PayLandingScreenWidget_WIDGET_TYPE_BANNER,
				Widget: &fePayPb.PayLandingScreenWidget_Banner{
					Banner: banner,
				},
			},
		},
	}
}

func (s *Service) populateUiInfoForPaySearchV2(ctx context.Context, actorId string, res *fePayPb.GetPayLandingScreenDetailsResponse, isPayDesignFixitEnabled bool) {
	if res.GetPayLandingScreenLayout() == nil {
		res.PayLandingScreenLayout = &fePayPb.PayLandingScreenLayout{}
	}

	paySearchV2Widgets := []*fePayPb.PayLandingScreenWidget{
		{
			WidgetType: fePayPb.PayLandingScreenWidget_WIDGET_TYPE_SEARCH_BAR,
			Widget: &fePayPb.PayLandingScreenWidget_SearchBar{
				SearchBar: lo.Ternary(isPayDesignFixitEnabled, getSearchBarV2(), getSearchBar()),
			},
			WidgetBg: &widgetPb.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_BlockColour{
					BlockColour: pkgColors.ColorNight,
				},
			},
		},
		{
			WidgetType: fePayPb.PayLandingScreenWidget_WIDGET_TYPE_QUICK_ACTIONS,
			Widget: &fePayPb.PayLandingScreenWidget_PaymentOptions{
				PaymentOptions: s.getQuickActions(ctx, actorId, isPayDesignFixitEnabled),
			},
			WidgetBg: &widgetPb.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_BlockColour{
					BlockColour: pkgColors.ColorNight,
				},
			},
		},
	}

	res.PayLandingScreenLayout.Widgets = append(res.PayLandingScreenLayout.Widgets, paySearchV2Widgets...)
}

func (s *Service) populateUiInfoForPayWidgets(res *fePayPb.GetPayLandingScreenDetailsResponse, isPayDesignFixitEnabled, isUpiMapperEnabled bool) {
	if res.GetPayLandingScreenLayout() == nil {
		res.PayLandingScreenLayout = &fePayPb.PayLandingScreenLayout{}
	}

	var paySettings *fePayPb.PaySettingsWidget

	if isPayDesignFixitEnabled {
		// Get base tiles (All Transactions, AutoPay)
		paySettings = s.getPayWidgetTiles(isUpiMapperEnabled)
	} else if isUpiMapperEnabled {
		// Only show the manage UPI settings tile
		paySettings = s.getPaySettings()
	}

	// Only add the pay settings widget if we have valid pay settings
	if paySettings != nil {
		payWidgets := []*fePayPb.PayLandingScreenWidget{
			{
				WidgetType: fePayPb.PayLandingScreenWidget_WIDGET_TYPE_PAY_SETTINGS,
				Widget: &fePayPb.PayLandingScreenWidget_PaySettings{
					PaySettings: paySettings,
				},
				WidgetBg: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_BlockColour{
						BlockColour: pkgColors.ColorNight,
					},
				},
			},
		}

		res.PayLandingScreenLayout.Widgets = append(res.PayLandingScreenLayout.Widgets, payWidgets...)
	}
}

// getTpapEntryPointBanner : creates a banner for the TPAP account linking.
// Based on the actor's current state, next action deeplink will be decided
//  1. If vpa migration is not done, user will see migrate vpa deeplink
//  2. If TPAP account linking for account is enabled then user will get
//     deeplink to connect more bank accounts.
func (s *Service) getTpapEntryPointBanner(ctx context.Context, actorId, vpa string, isPayDesignFixitEnabled bool) (*fePayPb.Banner, error) {
	if !fePkgUpi.IsFeatureEnabledForActor(ctx, actorId, types.Feature_NEW_VPA_HANDLE, s.upiOnboardingClient) {
		return nil, nil
	}

	tpapEntryDeeplink, err := fePkgUpi.GetTpapEntryPointDeeplink(ctx, actorId, vpa, s.dynamicConf, s.upiOnboardingClient)
	if err != nil {
		return nil, fmt.Errorf("error while creating tpap entry point deeplink for actor :%w", err)
	}

	if isPayDesignFixitEnabled {
		return s.getPayLandingScreenBannerV2(s.conf.PayLandingScreenParams.TpapEntryPointBannerV2, tpapEntryDeeplink), nil
	}

	payLandingScreenConf := s.conf.PayLandingScreenParams.TpapEntryPointBanner
	return &fePayPb.Banner{
		Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(
			payLandingScreenConf.Icon.Url,
			payLandingScreenConf.Icon.Height,
			payLandingScreenConf.Icon.Width,
		),
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			payLandingScreenConf.Title.DisplayValue,
			payLandingScreenConf.Title.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[payLandingScreenConf.Title.FontStyle])),

		Cta: &ui.IconTextComponent{
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
				payLandingScreenConf.Cta.RightIcon.Url,
				payLandingScreenConf.Cta.RightIcon.Height,
				payLandingScreenConf.Cta.RightIcon.Width,
			),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					payLandingScreenConf.Cta.Text,
					payLandingScreenConf.Cta.Color,
					(commontypes.FontStyle)(commontypes.FontStyle_value[payLandingScreenConf.Cta.Style])),
			},
			RightImgTxtPadding: payLandingScreenConf.Cta.RightImgTxtPadding,
		},
		BgColour: &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_LinearGradient{
				LinearGradient: &widgetPb.LinearGradient{
					Degree: 0,
					LinearColorStops: []*widgetPb.ColorStop{
						{
							Color:          "#879EDB",
							StopPercentage: 70,
						},
						{
							Color:          "#4B6CA3",
							StopPercentage: 100,
						},
					},
				},
			},
		},
		Deeplink: tpapEntryDeeplink,
	}, nil
}

func (s *Service) getPaySettings() *fePayPb.PaySettingsWidget {
	return &fePayPb.PaySettingsWidget{
		Tiles: []*fePayPb.PaySettingsWidget_Tile{
			{
				Title: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(s.dynamicConf.PaySettingsWidgetUiConfig().LeftIconUrl(), 32, 32),
					Texts: []*commontypes.Text{
						{
							FontColor: "#333333",
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: s.dynamicConf.PaySettingsWidgetUiConfig().TitleText(),
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_4},
						},
					},
					LeftImgTxtPadding: 8,
				},
				RightIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(s.dynamicConf.PaySettingsWidgetUiConfig().RightIconUrl(), 32, 32),

				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#FFFFFF",
					CornerRadius:  16,
					TopPadding:    20,
					LeftPadding:   16,
					BottomPadding: 20,
					RightPadding:  16,
				},
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_MANAGE_UPI_SETTINGS,
				},
			},
		},
	}
}

func (s *Service) getPayWidgetTiles(isUpiMapperEnabled bool) *fePayPb.PaySettingsWidget {
	tiles := []*fePayPb.PaySettingsWidget_Tile{
		// All Transactions Tile
		createTileFromConfig(s.dynamicConf.PayWidgetAllTxnConfig(), deeplink.Screen_TRANSACTION_TIMELINE),
		// AutoPay Tile
		createTileFromConfig(s.dynamicConf.PayWidgetAutoPayConfig(), deeplink.Screen_AUTOPAY_HUB),
	}
	if isUpiMapperEnabled {
		// Manage UPI number tile
		tiles = append(tiles, createTileFromConfig(s.dynamicConf.PayWidgetManageUpiNumberConfig(), deeplink.Screen_MANAGE_UPI_SETTINGS))
	}
	return &fePayPb.PaySettingsWidget{
		Tiles: tiles,
	}
}

// Helper function to create a tile from a configuration
func createTileFromConfig(tileConfig *genconf.PaySettingsWidgetUiConfig, screen deeplink.Screen) *fePayPb.PaySettingsWidget_Tile {
	return &fePayPb.PaySettingsWidget_Tile{
		Title: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(tileConfig.LeftIconUrl(), 32, 32),
			Texts: []*commontypes.Text{
				{
					FontColor: pkgColors.ColorOnLightHighEmphasis,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: tileConfig.TitleText(),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_4},
				},
			},
			LeftImgTxtPadding: 8,
		},
		RightIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(tileConfig.RightIconUrl(), 32, 32),
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       "#FFFFFF",
			CornerRadius:  16,
			TopPadding:    16,
			LeftPadding:   16,
			BottomPadding: 16,
			RightPadding:  16,
		},
		Deeplink: &deeplink.Deeplink{
			Screen: screen,
		},
	}
}

func getSearchBarV2() *fePayPb.SearchBarWidget {
	paySearchDL := deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_PAY_SEARCH_SCREEN_V2, &typesPayPb.PaySearchV2ScreenOptions{
		Header:              nil,
		PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_GLOBAL_SEARCH,
	})
	paySearchWithFocusedContactsDL := deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_PAY_SEARCH_SCREEN_V2, &typesPayPb.PaySearchV2ScreenOptions{
		Header:              nil,
		PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_GLOBAL_SEARCH,
		FocusedSection:      typesPayPb.SearchSection_SEARCH_SECTION_CONTACTS,
	})
	return &fePayPb.SearchBarWidget{
		SearchBar: &fePayPb.SearchBarWidget_SearchBar{
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgBorderColour: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_LinearGradient{
						LinearGradient: &widgetPb.LinearGradient{
							Degree: 60,
							LinearColorStops: []*widgetPb.ColorStop{
								{
									Color:          "#80D8D8D8",
									StopPercentage: 0,
								},
								{
									Color:          "#80191919",
									StopPercentage: 100,
								},
							},
						},
					},
				},
				BackgroundColour: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_LinearGradient{
						LinearGradient: &widgetPb.LinearGradient{
							Degree: 180,
							LinearColorStops: []*widgetPb.ColorStop{
								{
									Color:          "#4D3F4146",
									StopPercentage: 0,
								},
								{
									Color:          "#26313234",
									StopPercentage: 100,
								},
							},
						},
					},
				},
				CornerRadius: 24,
				BorderWidth:  1,
				Height:       48,
			},
			LeftIcon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/pay/search_icon_v2.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  28,
							Height: 28,
						},
					},
				},
			},
			PlaceholderContent: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Lottie_{
					Lottie: &commontypes.VisualElement_Lottie{
						Source:      &commontypes.VisualElement_Lottie_Url{Url: "https://epifi-icons.pointz.in/pay/search-bar-v2.json"},
						RepeatCount: -1,
						Properties: &commontypes.VisualElementProperties{
							Width:  190,
							Height: 26,
						},
					},
				},
			},
			Deeplink: paySearchDL,
		},
		TrailingIcon: &ui.IconTextComponent{
			Deeplink: paySearchWithFocusedContactsDL,
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/pay/contacts.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  24,
							Height: 24,
						},
					},
				},
			},
		},
	}
}

func getSearchBar() *fePayPb.SearchBarWidget {
	paySearchDL := deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_PAY_SEARCH_SCREEN_V2, &typesPayPb.PaySearchV2ScreenOptions{
		Header:              nil,
		PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_GLOBAL_SEARCH,
	})
	paySearchWithFocusedContactsDL := deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_PAY_SEARCH_SCREEN_V2, &typesPayPb.PaySearchV2ScreenOptions{
		Header:              nil,
		PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_GLOBAL_SEARCH,
		FocusedSection:      typesPayPb.SearchSection_SEARCH_SECTION_CONTACTS,
	})
	return &fePayPb.SearchBarWidget{
		SearchBar: &fePayPb.SearchBarWidget_SearchBar{
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#313234",
				BackgroundColour: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_BlockColour{
						BlockColour: "#313234",
					},
				},
				CornerRadius: 20,
				Height:       52,
				BorderWidth:  1,
			},
			LeftIcon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home/<USER>/search-icon.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  24,
							Height: 24,
						},
					},
				},
			},
			PlaceholderContent: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Lottie_{
					Lottie: &commontypes.VisualElement_Lottie{
						Source:      &commontypes.VisualElement_Lottie_Url{Url: "https://epifi-icons.pointz.in/pay/search-bar-v2.json"},
						RepeatCount: -1,
						Properties: &commontypes.VisualElementProperties{
							Width:  190,
							Height: 26,
						},
					},
				},
			},
			Deeplink: paySearchDL,
		},
		TrailingIcon: &ui.IconTextComponent{
			Deeplink: paySearchWithFocusedContactsDL,
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/pay/contacts.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  24,
							Height: 24,
						},
					},
				},
			},
		},
	}
}

// nolint:funlen
func (s *Service) getQuickActions(ctx context.Context, actorId string, isPayDesignFixitEnabled bool) *fePayPb.QuickActionsWidget {
	var (
		paySearchDL = deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_PAY_SEARCH_SCREEN_V2, &typesPayPb.PaySearchV2ScreenOptions{
			Header:              nil,
			PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_NUMBER_SEARCH,
		})
		ctas            []*fePayPb.QuickActionsWidget_QuickActionCta
		qrScreenOptions *qrScreenOptionsPb.QRScanScreenOptions
		qrScreen        = deeplink.Screen_PAY_QR_SCREEN // Default screen
	)

	// Use s.releaseEvaluator which was passed implicitly via receiver 's'
	isQrEnhancementsEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_QR_SCAN_ENHANCEMENTS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating qr enhancements for quick actions", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	if isQrEnhancementsEnabled {
		qrScreenOptions = &qrScreenOptionsPb.QRScanScreenOptions{
			PayBottomSheet: &qrScreenOptionsPb.QRScanScreenOptions_PayBottomSheet{
				IsSearchBarEnabled:          true,
				IsRecentTransactionsEnabled: true,
			},
		}
	}

	if fePkgUpi.IsMlKitQrEnabled(ctx, actorId, s.releaseEvaluator) {
		qrScreen = deeplink.Screen_PAY_ML_KIT_QR_SCREEN
	}

	qrDeeplink := &deeplink.Deeplink{
		Screen:          qrScreen,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(qrScreenOptions),
	}

	var quickActions []quickAction
	var quickActionConfig *config.QuickActions
	quickActionConfig = s.conf.PayLandingScreenParams.QuickActions
	if isPayDesignFixitEnabled {
		quickActionConfig = s.conf.PayLandingScreenParams.QuickActionsV2
	}
	quickActions = []quickAction{
		{
			ctaText:     quickActionConfig.ScanQrAction.CtaText,
			ctaDeeplink: qrDeeplink, // Use evaluated deeplink
			iconUrl:     quickActionConfig.ScanQrAction.IconUrl,
		},
		{
			ctaText:     quickActionConfig.BankTransfer.CtaText,
			ctaDeeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PAY_VIA_BANK_TRANSFER},
			iconUrl:     quickActionConfig.BankTransfer.IconUrl,
		},
		lo.Ternary(isPayDesignFixitEnabled, getPayViaNumberQuickActionV2(quickActionConfig, paySearchDL), getPayViaNumberQuickAction(quickActionConfig, paySearchDL)),
		{
			ctaText:     quickActionConfig.PayUpiId.CtaText,
			ctaDeeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PAY_VIA_UPI},
			iconUrl:     quickActionConfig.PayUpiId.IconUrl,
		},
	}

	for _, action := range quickActions {
		cta := &fePayPb.QuickActionsWidget_QuickActionCta{
			Cta: &ui.VerticalIconTextComponent{
				TopImgTxtPadding: 8,
				Texts: []*commontypes.Text{
					{
						FontColor:    quickActionConfig.FontColor,
						DisplayValue: &commontypes.Text_PlainString{PlainString: action.ctaText},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_CAPTION_1},
						Alignment:    commontypes.Text_ALIGNMENT_CENTER,
					},
				},
				Deeplink: action.ctaDeeplink,
			},
			Tag: action.tag,
		}
		if action.iconUrl != "" {
			cta.Cta.TopVisualElement = &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: action.iconUrl},
						Properties: &commontypes.VisualElementProperties{
							Width:  56,
							Height: 56,
						},
					},
				},
			}
		} else if action.lottieUrl != "" {
			cta.Cta.TopVisualElement = &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Lottie_{
					Lottie: &commontypes.VisualElement_Lottie{
						Source: &commontypes.VisualElement_Lottie_Url{Url: action.lottieUrl},
						Properties: &commontypes.VisualElementProperties{
							Width:  56,
							Height: 56,
						},
						RepeatCount: -1,
					},
				},
			}
		}
		ctas = append(ctas, cta)
	}
	return &fePayPb.QuickActionsWidget{Ctas: ctas}
}

func getPayViaNumberQuickAction(quickActionConfig *config.QuickActions, paySearchDL *deeplink.Deeplink) quickAction {
	return quickAction{
		ctaText:     quickActionConfig.PayNumber.CtaText,
		ctaDeeplink: paySearchDL,
		lottieUrl:   quickActionConfig.PayNumber.LottieUrl,
		tag: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "NEW"},
					FontColor:    "#AC7C44",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_3XS_CAPS},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#F4E7BF",
				CornerRadius:  9,
				LeftPadding:   4,
				RightPadding:  4,
				TopPadding:    2,
				BottomPadding: 2,
			},
		},
	}
}

func getPayViaNumberQuickActionV2(quickActionConfig *config.QuickActions, paySearchDL *deeplink.Deeplink) quickAction {
	return quickAction{
		ctaText:     quickActionConfig.PayNumber.CtaText,
		ctaDeeplink: paySearchDL,
		iconUrl:     quickActionConfig.PayNumber.IconUrl,
	}
}

func (s *Service) getPayLandingScreenBanner(ctx context.Context, actorId string, vpa string, isUpiMapperEnabled, isPayDesignFixitEnabled bool) (*fePayPb.Banner, error) {
	var (
		payLandingScreenBanner         *fePayPb.Banner
		err                            error
		isMapperQuickLinkBannerEnabled bool
	)

	isMapperQuickLinkBannerEnabled, err = s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating mapper quick link banner", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	if isUpiMapperEnabled && isMapperQuickLinkBannerEnabled && s.isEligibleForMapperQuickLinkBanner(ctx, actorId) {
		payLandingScreenBanner = lo.Ternary(isPayDesignFixitEnabled,
			s.getPayLandingScreenBannerV2(s.conf.PayLandingScreenParams.UpiMapperQuickLinkBannerV2, &deeplink.Deeplink{Screen: deeplink.Screen_UPI_MAPPER_QUICK_LINK}),
			s.getUpiMapperQuickLinkBanner())
	}

	// If mapper quick link banner wasn't created, fallback to TPAP banner
	if payLandingScreenBanner == nil {
		payLandingScreenBanner, err = s.getTpapEntryPointBanner(ctx, actorId, vpa, isPayDesignFixitEnabled)
		if err != nil {
			logger.Error(ctx, "error while generating tpap entry point",
				zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Error(err))
			return nil, err
		}
	}
	return payLandingScreenBanner, nil
}

// Helper method to check if UPI Mapper Quick Link banner should be shown or not.
func (s *Service) isEligibleForMapperQuickLinkBanner(ctx context.Context, actorId string) bool {

	phoneNumber, err := s.getPhoneNumberForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to fetch phone number for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	upiNumberPiMapping, err := s.upiOnboardingClient.GetUpiNumberPiMapping(ctx, &upiOnboardingPb.GetUpiNumberPiMappingRequest{
		UpiNumber: phoneNumber.ToStringNationalNumber(),
	})
	te := epifigrpc.RPCError(upiNumberPiMapping, err)
	if te != nil {
		if upiNumberPiMapping.GetStatus().IsRecordNotFound() {
			// Handle record not found case
			return true
		}
		logger.Error(ctx, "error while fetching upi number pi mapping", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		return false
	}
	// If the mapping exists, then the banner should not be shown
	return false
}

func (s *Service) getUpiMapperQuickLinkBanner() *fePayPb.Banner {
	payLandingScreenConf := s.conf.PayLandingScreenParams.UpiMapperQuickLinkBanner
	return &fePayPb.Banner{
		Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(
			payLandingScreenConf.Icon.Url,
			payLandingScreenConf.Icon.Height,
			payLandingScreenConf.Icon.Width),
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			payLandingScreenConf.Title.DisplayValue,
			payLandingScreenConf.Title.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[payLandingScreenConf.Title.FontStyle])),
		Cta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					payLandingScreenConf.Cta.Text,
					payLandingScreenConf.Cta.Color,
					(commontypes.FontStyle)(commontypes.FontStyle_value[payLandingScreenConf.Cta.Style])),
			},
		},
		BgColour: &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_BlockColour{
				BlockColour: "#648E4D",
			},
		},
		Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_UPI_MAPPER_QUICK_LINK},
	}
}

func (s *Service) getPayLandingScreenBannerV2(payLandingBannerConfig *config.Banner, bannerDeeplink *deeplink.Deeplink) *fePayPb.Banner {
	return &fePayPb.Banner{
		Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(
			payLandingBannerConfig.Icon.Url,
			payLandingBannerConfig.Icon.Height,
			payLandingBannerConfig.Icon.Width),
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			payLandingBannerConfig.Title.DisplayValue,
			payLandingBannerConfig.Title.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[payLandingBannerConfig.Title.FontStyle])),
		Cta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					payLandingBannerConfig.Cta.Text,
					payLandingBannerConfig.Cta.Color,
					(commontypes.FontStyle)(commontypes.FontStyle_value[payLandingBannerConfig.Cta.Style])),
			},
		},
		BgColour: &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_LinearGradient{
				LinearGradient: &widgetPb.LinearGradient{
					Degree: 180,
					LinearColorStops: []*widgetPb.ColorStop{
						{
							Color:          "#00DCF3EE",
							StopPercentage: -57,
						},
						{
							Color:          "#3FDCF3EE",
							StopPercentage: 100,
						},
					},
				},
			},
		},
		BorderColour: &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_BlockColour{
				BlockColour: "#B2DCF3EE",
			},
		},
		Deeplink: bannerDeeplink,
	}
}

// getPhoneNumberForActor returns actors phone number
func (s *Service) getPhoneNumberForActor(ctx context.Context, actorId string) (*commontypes.PhoneNumber, error) {
	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		return nil, fmt.Errorf("failed to get actor entity details %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	return entityRes.GetMobileNumber(), nil
}

func getTopSectionBackgroundImage() *fePayPb.PayLandingScreenLayout_BackgroundVisualElement {
	return &fePayPb.PayLandingScreenLayout_BackgroundVisualElement{
		BackgroundVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/pay/top_bg.png"),
	}
}

func (s *Service) getPayLandingScreenTitle() *commontypes.Text {
	payLandingScreenTitle := s.conf.PayLandingScreenParams.PayLandingScreenTitle
	return commontypes.GetTextFromStringFontColourFontStyle(payLandingScreenTitle.DisplayValue, payLandingScreenTitle.FontColour, (commontypes.FontStyle)(commontypes.FontStyle_value[payLandingScreenTitle.FontStyle]))
}

func (s *Service) getVpaWidget(vpa string) *fePayPb.PayLandingScreenWidget {
	return &fePayPb.PayLandingScreenWidget{
		WidgetType: fePayPb.PayLandingScreenWidget_WIDGET_TYPE_VPA,
		Widget: &fePayPb.PayLandingScreenWidget_VpaWidget{
			VpaWidget: &fePayPb.VpaWidget{
				Vpa: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(
							"UPI ID: ",
							pkgColors.ColorOnDarkLowEmphasis,
							commontypes.FontStyle_HEADLINE_XS,
						),
						commontypes.GetTextFromStringFontColourFontStyle(
							vpa,
							pkgColors.ColorOnDarkHighEmphasis,
							commontypes.FontStyle_HEADLINE_XS,
						),
					},

					RightVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/pay/copy_icon_v2.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  20,
									Height: 20,
								},
							},
						},
					},
					RightImgTxtPadding: 4,
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BgColor:       "#1A000000",
						BorderColor:   "#28292B",
						BorderWidth:   1,
						CornerRadius:  24,
						TopPadding:    14,
						LeftPadding:   12,
						BottomPadding: 14,
						RightPadding:  12,
					},
				},
				CopyContent: vpa,
				ToastText:   "UPI ID copied to clipboard",
			},
		},
		WidgetBg: &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_BlockColour{
				BlockColour: pkgColors.ColorNight,
			},
		},
	}
}

func (s *Service) getFooterTrustMarker() *fePayPb.FooterTrustMarker {
	return &fePayPb.FooterTrustMarker{
		Content: &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					IsScrollable: false,
					Components: []*components.Component{
						// 1. Header Section for Trust Marker
						s.getTrustMarkerHeaderComponent(),
						// 2. Partner Logos Section
						s.getTrustMarkerPartnerLogos(),
						// 3. Why Trust Fi Section
						s.getTrustMarkerStoriesComponent(),
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
									},
									BgColor: widgetPb.GetBlockBackgroundColour(pkgColors.ColorOnDarkHighEmphasis),
									Padding: &properties.PaddingProperty{
										Top:    50,
										Left:   16,
										Bottom: 90,
										Right:  20,
									},
								},
							},
						},
					},
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER,
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					LoadBehavior: &behaviors.LifecycleBehavior{
						Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "LoadedPayTrustMarker",
						},
					},
					VisibleBehavior: &behaviors.LifecycleBehavior{
						Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "ViewedPayTrustMarker",
						},
					},
				},
			},
		},
		BgColor: &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_BlockColour{
				BlockColour: pkgColors.ColorOnDarkHighEmphasis,
			},
		},
	}
}

// getFooterTrustMarkerTitleSection returns the title for SDUI Component (trust footer)
// Example: "1 Crore+ Successful payments"
func (s *Service) getTrustMarkerHeaderComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.VerticalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringWithCustomFontStyle(s.conf.TrustMarkerFooterPayConfig.FirstTitleHeader, "#b2b5b9", &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "bold",
							FontSize:   "32",
						})),
					},
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringWithCustomFontStyle(s.conf.TrustMarkerFooterPayConfig.SecondTitleHeader, "#b2b5b9", &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "bold",
							FontSize:   "32",
						})),
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Bottom: 8,
								},
							},
						},
					},
				},
			},
		),
	}
}

// getTrustMarkerPartnerLogos returns the section containing partner logos
// Example: "Powered by Federal Bank, Visa, BHIM UPI, Epifi Wealth"
func (s *Service) getTrustMarkerPartnerLogos() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.FederalBankLogo, 20, 80)),
					},
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.VisaLogo, 20, 38)),
					},
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.BhimUpiIconLogo, 20, 110)),
					},
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.EpifiWealthLogo, 20, 90)),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
								Padding: &properties.PaddingProperty{
									Top:    24,
									Bottom: 24,
								},
							},
						},
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			},
		),
	}
}

// getTrustMarkerStoriesComponent returns the section containing clickable links which redirects to any story.
// Example:  "Why trust Fi?" section
func (s *Service) getTrustMarkerStoriesComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&ui.IconTextComponent{
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(constants.PlayIcon, 16, 16),
				Texts:             []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(s.conf.TrustMarkerFooterPayConfig.StoriesButtonText, "#00B899", commontypes.FontStyle_HEADLINE_XS)},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#F6F9FD",
					Height:        28,
					TopPadding:    6,
					BottomPadding: 6,
					RightPadding:  12,
					LeftPadding:   12,
					CornerRadius:  13,
				},
			}),
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: getAnyWithoutError(
					&deeplink.Deeplink{
						Screen: deeplink.Screen_STORY_SCREEN,
						ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
							StoryScreenOptions: &deeplink.StoryScreenOptions{
								StoryUrl:   s.conf.TrustMarkerFooterPayConfig.StoriesDeeplinkURL,
								StoryTitle: "Why Trust Fi?",
							},
						},
					})}},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: "ClickedPayTrustMarker",
					Properties: map[string]string{
						"sub_component_name": "TrustMarkerStoriesButton",
					},
				},
			},
		},
	}
}

func getAnyWithoutError(msg proto.Message) *anypb.Any {
	res, _ := anypb.New(msg)
	return res
}
