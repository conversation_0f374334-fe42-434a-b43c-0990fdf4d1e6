package tiering

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	genconf2 "github.com/epifi/be-common/pkg/frontend/app/genconf"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	afV2Pb "github.com/epifi/gamma/api/frontend/pay/add_funds_v2"
	afV2TieringPb "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/tiering"
	tieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	ambProperties "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/properties/amb"
	tieringProperties "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/properties/tiering"
	"github.com/epifi/gamma/frontend/pkg/tiering/amb"
)

func GetAddFundsTopCardForTiering(ctx context.Context, conf *genconf.Config, isPitchEnabled bool, tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails,
	appPlatform commontypes.Platform, appVersion uint32) *afV2Pb.AddFundsCardComponent {
	addFundsTopCard := &afV2Pb.AddFundsCardComponent{}
	// card for tiering
	addFundsTopCard.CardType = afV2Pb.AddFundsCardType_ADD_FUNDS_CARD_TIERING
	// card options for tiering card
	addFundsTopCard.CardOptions = GetCardOptionsForTiering(tieringPitchDetails, appPlatform, appVersion)
	if !isPitchEnabled {
		if genconf2.IsFeatureEnabledOnPlatform(ctx, conf.AddFundsParams().AddFundsV3Params().ImageWithTextConstraints()) {
			addFundsTopCard.CardType = afV2Pb.AddFundsCardType_ADD_FUNDS_CARD_TYPE_IMAGE_WITH_TEXT
			addFundsTopCard.CardOptions = &afV2Pb.AddFundsCardComponent_ImageWithTextComponent_{
				ImageWithTextComponent: &afV2Pb.AddFundsCardComponent_ImageWithTextComponent{
					ImageWithText: &ui.VerticalIconTextComponent{
						TopImgTxtPadding: tieringProperties.TopCardImageTxtPadding,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle(tieringProperties.TopCardImageText, tieringProperties.TopCardImageTextFontColor, commontypes.FontStyle_HEADLINE_L),
						},
						TopVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(tieringProperties.TopCardImageTxtImgUrl,
							tieringProperties.TopCardImageTextHeight, tieringProperties.TopCardImageTextWidth),
						TopIcon: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  tieringProperties.TopCardImageTxtImgUrl,
							Width:     tieringProperties.TopCardImageTextWidth,
							Height:    tieringProperties.TopCardImageTextHeight,
						},
					},
				},
			}
		} else {
			addFundsTopCard.CardType = afV2Pb.AddFundsCardType_ADD_FUNDS_CARD_TYPE_IMAGE
			addFundsTopCard.CardOptions = &afV2Pb.AddFundsCardComponent_AddFundsImage{
				AddFundsImage: commontypes.GetVisualElementFromUrlHeightAndWidth(tieringProperties.TopCardImageUrl,
					tieringProperties.TopCardImageHeight, tieringProperties.TopCardImageWidth),
			}
		}
	}

	return addFundsTopCard
}

func GetCardOptionsForTiering(tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails, appPlatform commontypes.Platform, appVersion uint32) *afV2Pb.AddFundsCardComponent_TieringCardOptions {
	// bottomInfoTitle, bottomInfoSubTitle := tieringProperties.TieringTopCardBottomInfoTitle, tieringProperties.TieringTopCardBottomInfoSubTitle
	// // Change the title and subtitle if this is a retention pitch
	// if tieringPitchDetails.GetIsRetentionPitch() {
	//	bottomInfoTitle = tieringProperties.TieringTopCardBottomInfoRetentionTitle
	//	bottomInfoSubTitle = tieringProperties.TieringTopCardBottomInfoRetentionSubTitle
	// }
	// bottomInfo := &afV2TieringPb.TieringCardBottomInfo{
	//	Title: commontypes.GetTextFromStringFontColourFontStyle(
	//		fmt.Sprintf(bottomInfoTitle, tieringProperties.GetDisplayStringForTier(tieringPitchDetails.GetNextTier())),
	//		tieringProperties.TieringTopCardBottomInfoTitleFontColour,
	//		commontypes.FontStyle_SUBTITLE_S,
	//	),
	//	SubTitle: commontypes.GetTextFromStringFontColourFontStyle(
	//		fmt.Sprintf(
	//			bottomInfoSubTitle,
	//			moneyPkg.ToDisplayStringWithPrecision(tieringPitchDetails.GetNextTierMinAmount(), 0),
	//			tieringProperties.GetDisplayStringForTier(tieringPitchDetails.GetNextTier()),
	//		),
	//		tieringProperties.TieringTopCardBottomInfoSubTitleFontColour,
	//		commontypes.FontStyle_BODY_XS,
	//	),
	//	BgColour: widgetPb.GetBlockBackgroundColour(tieringProperties.TieringTopCardBottomInfoBgColour),
	// }
	topCardTitle := tieringProperties.TieringTopCardTitle
	if tieringPitchDetails.GetIsRetentionPitch() {
		topCardTitle = tieringProperties.TieringTopCardRetentionTitle
	}

	return &afV2Pb.AddFundsCardComponent_TieringCardOptions{
		TieringCardOptions: &afV2TieringPb.TieringCardOptions{
			Title: commontypes.GetTextFromStringFontColourFontStyle(
				fmt.Sprintf(
					topCardTitle,
					tieringProperties.GetDisplayStringForTier(tieringPitchDetails.GetNextTier()),
				),
				tieringProperties.TieringTopCardTitleFontColour,
				commontypes.FontStyle_HEADLINE_M,
			),
			BgColour:           widgetPb.GetBlockBackgroundColour(tieringProperties.TieringTopCardBgColour),
			ProgressBarDetails: getProgressBarDetails(tieringPitchDetails),
			BottomInfoV2: &afV2TieringPb.TieringCardBottomInfoV2{
				BottomInfo: &afV2TieringPb.TieringCardBottomInfoV2_BottomItc{
					BottomItc: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle(getTopCardBottomItcTitle(tieringPitchDetails.GetNextTier()),
								tieringProperties.TopCardBottomItcTitleFontColor, commontypes.FontStyle_SUBTITLE_S),
						},
						LeftImgTxtPadding: tieringProperties.TopCardBottomItcLeftImgpading,
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor:       tieringProperties.TopCardBottomItcContainerBgColor,
							CornerRadius:  tieringProperties.TopCardBottomItcContainerRadius,
							LeftPadding:   tieringProperties.TopCardBottomItcContainerLeftPadding,
							RightPadding:  tieringProperties.TopCardBottomItcContainerRightPadding,
							TopPadding:    tieringProperties.TopCardBottomItcContainerTopPadding,
							BottomPadding: tieringProperties.TopCardBottomItcContainerBottomPadding,
						},
						LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(getTopCardBottomItcLeftImgUrl(tieringPitchDetails.GetNextTier()),
							tieringProperties.TopCardBottomItcLeftImgHeight, tieringProperties.TopCardBottomItcLeftImgWidth),
					},
				},
			},
		},
	}
}

func getTopCardBottomItcTitle(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return tieringProperties.TopCardBottomItcPlusTitle
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return tieringProperties.TopCardBottomItcInfiniteTitle
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return tieringProperties.TopCardBottomItcPrimeTitle
	default:
		return ""
	}
}

func getTopCardBottomItcLeftImgUrl(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return tieringProperties.TopCardBottomItcPlusLeftImgUrl
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return tieringProperties.TopCardBottomItcInfiniteLeftImgUrl
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return tieringProperties.TopCardBottomItcPrimeLeftImgUrl
	default:
		return ""
	}
}

func getProgressBarDetails(tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails) *afV2TieringPb.ProgressBarDetails {
	progressNumerator := (float64)(tieringPitchDetails.GetCurrentBalanceAmount().GetUnits() - tieringPitchDetails.GetCurrentTierMinAmount().GetUnits())
	progressDenominator := (float64)(tieringPitchDetails.GetNextTierMinAmount().GetUnits() - tieringPitchDetails.GetCurrentTierMinAmount().GetUnits())

	progress := int32(0)
	if progressDenominator > 0.0 {
		actualProgress := (int32)((progressNumerator / progressDenominator) * 100)
		if actualProgress > 0 {
			progress = actualProgress
		}
	}

	return &afV2TieringPb.ProgressBarDetails{
		StartNode: &afV2TieringPb.ProgressBarNode{
			NodeImageUrl: &commontypes.Image{ImageUrl: tieringProperties.GetAddFundsBadgeUrlForTopCard(tieringPitchDetails.GetCurrentTier())},
			NodeTitle: commontypes.GetTextFromStringFontColourFontStyle(
				tieringProperties.GetDisplayStringForTier(tieringPitchDetails.GetCurrentTier()),
				tieringProperties.TieringTopCardStartNodeFontColour,
				commontypes.FontStyle_MICRO_1,
			),
			NodeAmount: typesPb.GetFromBeMoney(tieringPitchDetails.GetCurrentTierMinAmount()),
		},
		EndNode: &afV2TieringPb.ProgressBarNode{
			NodeImageUrl: &commontypes.Image{ImageUrl: tieringProperties.GetAddFundsBadgeUrlForTopCard(tieringPitchDetails.GetNextTier())},
			NodeTitle: commontypes.GetTextFromStringFontColourFontStyle(
				tieringProperties.GetDisplayStringForTier(tieringPitchDetails.GetNextTier()),
				tieringProperties.TieringTopCardEndNodeFontColour,
				commontypes.FontStyle_MICRO_1,
			),
			NodeAmount: typesPb.GetFromBeMoney(tieringPitchDetails.GetNextTierMinAmount()),
		},
		CurrentProgressNode: &afV2TieringPb.ProgressBarNode{
			NodeTitle: commontypes.GetTextFromStringFontColourFontStyle(
				tieringProperties.TieringTopCardCurrentNodeTitle,
				tieringProperties.TieringTopCardCurrentNodeFontColour,
				commontypes.FontStyle_MICRO_1,
			),
			NodeTitleImage: &commontypes.Image{ImageUrl: tieringProperties.TieringProgressBarCurrentNodeImageUrl},
			NodeAmount:     typesPb.GetFromBeMoney(pkgMoney.NewMoney(tieringPitchDetails.GetCurrentBalanceAmount()).Floor()),
		},
		FilledProgressBarColour:   widgetPb.GetBlockBackgroundColour(tieringProperties.GetTopCardProgressBarFilledColourForTier(tieringPitchDetails.GetCurrentTier())),
		UnfilledProgressBarColour: widgetPb.GetBlockBackgroundColour(tieringProperties.TieringTopCardProgressBarUnfilledColour),
		Progress:                  progress,
	}
}

// GetAddFundsTopCardForAMB creates an Add Funds card component with AMB information
func GetAddFundsTopCardForAMB(ctx context.Context, conf *genconf.Config, ambData *amb.AMBData,
	appPlatform commontypes.Platform, appVersion uint32) *afV2Pb.AddFundsCardComponent {
	addFundsTopCard := &afV2Pb.AddFundsCardComponent{}

	// Card for AMB
	addFundsTopCard.CardType = afV2Pb.AddFundsCardType_ADD_FUNDS_CARD_TIERING

	// Card options for AMB card
	addFundsTopCard.CardOptions = GetCardOptionsForAMB(ambData)

	// If feature flag for image with text is enabled, use that instead
	if genconf2.IsFeatureEnabledOnPlatform(ctx, conf.AddFundsParams().AddFundsV3Params().ImageWithTextConstraints()) {
		addFundsTopCard.CardType = afV2Pb.AddFundsCardType_ADD_FUNDS_CARD_TIERING
	} else {
		addFundsTopCard.CardType = afV2Pb.AddFundsCardType_ADD_FUNDS_CARD_TYPE_IMAGE
		addFundsTopCard.CardOptions = &afV2Pb.AddFundsCardComponent_AddFundsImage{
			AddFundsImage: commontypes.GetVisualElementFromUrlHeightAndWidth(ambProperties.TopCardImageUrl,
				ambProperties.TopCardImageHeight, ambProperties.TopCardImageWidth),
		}
	}

	return addFundsTopCard
}

// GetCardOptionsForAMB creates card options for AMB card
func GetCardOptionsForAMB(ambData *amb.AMBData) *afV2Pb.AddFundsCardComponent_TieringCardOptions {
	return &afV2Pb.AddFundsCardComponent_TieringCardOptions{
		TieringCardOptions: &afV2TieringPb.TieringCardOptions{
			Title: commontypes.GetTextFromStringFontColourFontStyle(
				ambProperties.TopCardTitle,
				ambProperties.TopCardTitleFontColour,
				commontypes.FontStyle_HEADLINE_M,
			),
			BgColour:           widgetPb.GetBlockBackgroundColour(ambProperties.TopCardBgColour),
			ProgressBarDetails: getAMBProgressBarDetails(ambData),
			BottomInfoV2: &afV2TieringPb.TieringCardBottomInfoV2{
				BottomInfo: &afV2TieringPb.TieringCardBottomInfoV2_BottomItc{
					BottomItc: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle(getAMBBottomItcText(ambData),
								ambProperties.BottomItcTitleFontColor, commontypes.FontStyle_SUBTITLE_S),
						},
						LeftImgTxtPadding: ambProperties.BottomItcLeftImgpading,
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor:       ambProperties.BottomItcContainerBgColor,
							CornerRadius:  ambProperties.BottomItcContainerRadius,
							LeftPadding:   ambProperties.BottomItcContainerLeftPadding,
							RightPadding:  ambProperties.BottomItcContainerRightPadding,
							TopPadding:    ambProperties.BottomItcContainerTopPadding,
							BottomPadding: ambProperties.BottomItcContainerBottomPadding,
						},
						LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(ambProperties.BottomItcLeftImgUrl,
							ambProperties.BottomItcLeftImgHeight, ambProperties.BottomItcLeftImgWidth),
					},
				},
			},
		},
	}
}

// getAMBBottomItcText returns the text for the bottom information component
func getAMBBottomItcText(ambData *amb.AMBData) string {
	// If there's a penalty amount, show that
	if ambData.PenaltyAmount != nil {
		return ambProperties.BottomItcPenaltyText
	}

	// If there's cashback at stake, show that
	if ambData.RewardsAtStake != "" {
		return fmt.Sprintf(ambProperties.BottomItcWarningText, ambData.RewardsAtStake)
	}

	// Default to showing amount needed to maintain AMB
	amountNeededStr := pkgMoney.ToDisplayStringWithPrecision(ambData.AmountNeeded, 0)
	return fmt.Sprintf(ambProperties.BottomItcText, amountNeededStr)
}

// getAMBProgressBarDetails creates progress bar details for AMB
func getAMBProgressBarDetails(ambData *amb.AMBData) *afV2TieringPb.ProgressBarDetails {
	return &afV2TieringPb.ProgressBarDetails{
		StartNode: &afV2TieringPb.ProgressBarNode{
			NodeImageUrl: &commontypes.Image{ImageUrl: ambProperties.StartNodeImageUrl},
			NodeAmount:   typesPb.GetFromBeMoney(pkgMoney.ZeroINR().GetPb()),
		},
		CurrentProgressNode: &afV2TieringPb.ProgressBarNode{
			NodeTitle: commontypes.GetTextFromStringFontColourFontStyle(
				ambProperties.TopCardCurrentAMBTitle,
				tieringProperties.TieringTopCardCurrentNodeFontColour,
				commontypes.FontStyle_MICRO_1,
			),
			NodeTitleImage: &commontypes.Image{ImageUrl: tieringProperties.TieringProgressBarCurrentNodeImageUrl},
			NodeAmount:     typesPb.GetFromBeMoney(pkgMoney.NewMoney(ambData.CurrentAMB).Floor()),
		},
		EndNode: &afV2TieringPb.ProgressBarNode{
			NodeTitle: commontypes.GetTextFromStringFontColourFontStyle(
				ambProperties.TopCardTargetAMBTitle,
				ambProperties.EndNodeFontColour,
				commontypes.FontStyle_MICRO_1,
			),
			NodeAmount:   typesPb.GetFromBeMoney(ambData.TargetAMB),
			NodeImageUrl: &commontypes.Image{ImageUrl: ambProperties.EndNodeImageUrl},
		},
		FilledProgressBarColour:   widgetPb.GetBlockBackgroundColour(ambProperties.ProgressBarFilledColour),
		UnfilledProgressBarColour: widgetPb.GetBlockBackgroundColour(ambProperties.ProgressBarUnfilledColour),
		Progress:                  ambData.ProgressPercentage,
	}
}
