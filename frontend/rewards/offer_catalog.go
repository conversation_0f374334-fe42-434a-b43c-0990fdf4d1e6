package rewards

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Knetic/govaluate"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	becComponents "github.com/epifi/be-common/api/typesv2/common/ui/components"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	accrualPb "github.com/epifi/gamma/api/accrual"
	beCasperPb "github.com/epifi/gamma/api/casper"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	beRedemptionPb "github.com/epifi/gamma/api/casper/redemption"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feErrors "github.com/epifi/gamma/api/frontend/errors"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	feRewardsPkgPb "github.com/epifi/gamma/api/frontend/rewards/pkg"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	rewardsScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/frontend/rewards/offerdisplay"
	"github.com/epifi/gamma/frontend/rewards/offerwidget"
	feRewardsPkg "github.com/epifi/gamma/frontend/rewards/pkg"
	"github.com/epifi/gamma/frontend/rewards/tags"
	onbPkg "github.com/epifi/gamma/pkg/onboarding"
	savingsPkg "github.com/epifi/gamma/pkg/savings"
)

type userAttributesForOfferCatalog struct {
	doesUserOwnACc bool
}

func (u *userAttributesForOfferCatalog) getDoesUserOwnACc() bool {
	if u != nil {
		return u.doesUserOwnACc
	}
	return false
}

func (r *RewardService) GetRewardsAndOffersWidget(ctx context.Context, req *fePb.GetRewardsAndOffersWidgetRequest) (*fePb.GetRewardsAndOffersWidgetResponse, error) {
	// fetch fi coin offers, exchanger offers and card offers
	var (
		actorId                        = req.GetReq().GetAuth().GetActorId()
		appPlatform                    = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersionCode                 = req.GetReq().GetAppVersionCode()
		beDebitCardOffers              []*beCasperPb.Offer
		beCreditCardOffers             []*beCasperPb.Offer
		feCatalogOffers                []*fePb.CatalogOfferV1
		catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails
		fiCoinsBalance                 uint32
		newRewardsCount                uint32
		creditCardTypeId               string
	)

	offerWidgetGenerator, err := r.offerWidgetGeneratorFactory.GetOfferWidgetGenerator(req.GetScreen(), req.GetWidgetType())
	if err != nil {
		logger.Error(ctx, "error in fetching offer widget generator", zap.Error(err))
		return &fePb.GetRewardsAndOffersWidgetResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}

	// fetch the offer types required to power the widget
	offerTypes := offerWidgetGenerator.GetOfferTypes(ctx)

	offersErrGrp, gctx := errgroup.WithContext(ctx)
	// fetch fi-debit-card offers
	if lo.Contains(offerTypes, offerwidget.DebitCardOffers) {
		offersErrGrp.Go(func() error {
			var err error
			beDebitCardOffers, _, err = r.getBeFiCardOffers(gctx, actorId, fePb.CardType_DEBIT_CARD, nil, nil)
			if err != nil {
				return fmt.Errorf("error while fetching be fi debit card offers: %w", err)
			}
			return nil
		})
	}

	// fetch fi-credit-card offers
	if lo.Contains(offerTypes, offerwidget.CreditCardOffers) {
		offersErrGrp.Go(func() error {
			creditCardTypeId = r.getAppropriateCreditCardTypeId(ctx, actorId)

			_, tabFilterTags, err := r.getCardTypeAndTagsForCardTypeId(creditCardTypeId)
			if err != nil {
				return fmt.Errorf("invalid card offers page card type id in request, err: %w", err)
			}

			beCreditCardOffers, _, err = r.getBeFiCardOffers(gctx, actorId, fePb.CardType_CREDIT_CARD, nil, tabFilterTags)
			if err != nil {
				return fmt.Errorf("error while fetching be fi credit card offers: %w", err)
			}
			return nil
		})
	}

	// fetch exchanger offers
	if lo.Contains(offerTypes, offerwidget.CatalogOffers) {
		offersErrGrp.Go(func() error {
			var err error
			feCatalogOffers, catalogOffersAdditionalDetails, err = r.getOrderedOffersAndExchangerOffers(
				gctx,
				req.GetReq(),
				[]string{},
				&tags.Config{
					// keeping this as home screen, since tag configs are same across screens for this widget
					TagRenderLocation: tags.RenderLocationHomeScreen,
					ExtractTags:       false,
				},
				nil,
				false,
				fePb.SortBy_UNSPECIFIED_SORT_BY,
				req.GetScreen(),
			)
			if err != nil {
				return fmt.Errorf("error fetching catalog offers: %w", err)
			}

			// construct tab data for catalog offers
			fiCoinsBalance, err = r.getFiCoinsBalanceForActor(ctx, actorId)
			if err != nil {
				return fmt.Errorf("error in fetching fi coins balance, err: %w", err)
			}

			// if last_my_rewards_visit_time is not nil, we'll fetch count of new rewards
			// generated since then and show that on the rewards' widget. otherwise we'll show
			// the count of unopened rewards for the last 7 days.
			newRewardsCount, err = feRewardsPkg.GetNewRewardsCount(ctx, actorId, req.GetLastMyRewardsVisitTimestamp(), r.rewardsGeneratorClient)
			if err != nil {
				// ignoring failure with a log since this is not critical for the widget
				logger.Error(ctx, "failed to fetch new rewards count", zap.Error(err))
			}

			return nil
		})
	}

	if err := offersErrGrp.Wait(); err != nil {
		logger.Error(ctx, "error in fetching offers", zap.Error(err))
		return &fePb.GetRewardsAndOffersWidgetResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in fetching offers")},
		}, nil
	}

	offers := &offerwidget.GetTabsDataOffersParam{
		BeDebitCardOffers:  beDebitCardOffers,
		BeCreditCardOffers: beCreditCardOffers,
		FeCatalogOffers:    feCatalogOffers,
		AdditionalDetails: &offerwidget.AdditionalDetails{
			CatalogOffersAdditionalDetails: catalogOffersAdditionalDetails,
		},
	}

	filter, tabDataMap, areTabsVisible := offerWidgetGenerator.GetTabsData(ctx, actorId, fiCoinsBalance, newRewardsCount, offers, creditCardTypeId, appPlatform, appVersionCode, req.GetReq().GetSessionId(), req.GetScreen())

	var tabs []*fePb.GetRewardsAndOffersWidgetResponse_Tab
	for _, tab := range filter.GetTabs() {
		tabs = append(tabs, &fePb.GetRewardsAndOffersWidgetResponse_Tab{Id: tab.GetId(), ActiveCta: tab.GetActiveCta(), InactiveCta: tab.GetInactiveCta()})
	}

	res := &fePb.GetRewardsAndOffersWidgetResponse{
		Title:          offerWidgetGenerator.GetTitle(ctx),
		Tabs:           tabs,
		Filter:         filter,
		TabDataMap:     tabDataMap,
		WaysToEarn:     offerWidgetGenerator.GetBottomCta(appPlatform, appVersionCode),
		BottomCta:      offerWidgetGenerator.GetBottomCta(appPlatform, appVersionCode),
		AreTabsVisible: areTabsVisible,
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}
	return res, nil
}

func (r *RewardService) GetOfferCatalogV1(ctx context.Context, req *fePb.GetOfferCatalogV1Request) (*fePb.GetOfferCatalogV1Response, error) {
	sortBy, err := r.getSortByEnum(req.GetSortBy())
	if err != nil {
		logger.WarnWithCtx(ctx, "unhandled sort option encountered, using default sorting.", zap.String("sortByString", req.GetSortBy()), zap.Error(err))
	}

	offerCatalog, _, err := r.getOrderedOffersAndExchangerOffers(
		ctx,
		req.GetReq(),
		req.GetDisplayFirstOfferIds(),
		&tags.Config{
			TagRenderLocation: tags.RenderLocationFiCoinOffersCatalogCard,
			ExtractTags:       false,
		},
		r.convertFeFiltersToBeFilters(req.GetFilters()),
		false,
		sortBy,
		deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
	)
	if err != nil {
		logger.Error(ctx, "error fetching fe and be offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		return &fePb.GetOfferCatalogV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching fe and be offers")}, nil
	}

	return &fePb.GetOfferCatalogV1Response{Status: rpc.StatusOk(), Offers: offerCatalog}, nil
}

// getOrderedOffersAndExchangerOffers fetches active catalog offers from BE,
// filtered by the specified filter, converts them to FE protos and orders them
// by the specified `sortBy` and `screen` parameter. `tagsConfig` contains config related to
// tags.
func (r *RewardService) getOrderedOffersAndExchangerOffers(ctx context.Context, reqHeader *feHeaderPb.RequestHeader, displayFirstOrderIds []string, tagsConfig *tags.Config, filters *beCasperPb.CatalogFilters, shouldFilterOnFrontend bool, sortBy fePb.SortBy, screen deeplinkPb.Screen) ([]*fePb.CatalogOfferV1, *feRewardsPkg.CatalogOffersAdditionalDetails, error) {
	var (
		actorId = reqHeader.GetAuth().GetActorId()

		beFiCoinOffers           []*beCasperPb.Offer
		beFiCoinOffersListingMap map[string]*beCasperPb.OfferListing

		beExchangerOffers                                 []*beExchangerPb.ExchangerOffer
		beExchangerOffersListingMap                       map[string]*beExchangerPb.ExchangerOfferListing
		beOfferIdToOfferLevelInventoryMap                 map[string]*beCasperPb.OfferInventory
		beExchangerOffersAttemptsCountMap                 map[string]int32
		beExchangerOffersMaxCapHitMap                     map[string]bool
		isUserFullSalaryProgramActive                     bool
		fiCoinsBalance                                    uint32
		offerIdToUserLevelAttemptsRemainingMap            map[string]int32
		exchangerOfferIdsToRemainingMonthlyRedemptionsMap map[string]uint32
		offerIdsToRemainingMonthlyRedemptionsMap          map[string]uint32

		// user specific metadata of CatalogOffersAdditionalDetails
		userSpecificMetadata *feRewardsPkg.UserSpecificMetadata
		err                  error
	)

	offersErrGrp, gctx := errgroup.WithContext(ctx)

	// fetch fi-coin-offers
	offersErrGrp.Go(func() error {
		var err error
		beFiCoinOffers, beFiCoinOffersListingMap, beOfferIdToOfferLevelInventoryMap, offerIdToUserLevelAttemptsRemainingMap, offerIdsToRemainingMonthlyRedemptionsMap, err = r.getBeFiCoinOffers(gctx, reqHeader, filters, shouldFilterOnFrontend)
		if err != nil {
			return fmt.Errorf("error fetching fi-coin-offers: %w", err)
		}

		return nil
	})

	// fetch exchanger-offers
	offersErrGrp.Go(func() error {
		var err error
		beExchangerOffers, beExchangerOffersListingMap, beExchangerOffersAttemptsCountMap, beExchangerOffersMaxCapHitMap, isUserFullSalaryProgramActive, fiCoinsBalance, exchangerOfferIdsToRemainingMonthlyRedemptionsMap, err = r.getBeExchangerOffers(gctx, reqHeader, filters, beExchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE, shouldFilterOnFrontend)
		if err != nil {
			return fmt.Errorf("error fetching exchanger-offers: %w", err)
		}

		return nil
	})

	// fetch CatalogOffersAdditionalDetails user specific metadata
	offersErrGrp.Go(func() error {
		userSpecificMetadata, err = r.getUserSpecificMetadata(gctx, actorId, screen)
		if err != nil {
			logger.Error(gctx, "error in updating catalog offer additional details", zap.Error(err))
		}
		return nil
	})

	if err := offersErrGrp.Wait(); err != nil {
		logger.Error(ctx, "error fetching fe and be offers", zap.Error(err))
		return nil, nil, fmt.Errorf("error fetching fe and be offers")
	}

	displayExpressionFunctionMap := r.offerDisplayEngine.GetExpressionFunctionMap(ctx, actorId, beFiCoinOffers, beExchangerOffers)

	beFiCoinOffers, beExchangerOffers, err = r.filterBEOffersAndExchangerOffersForActor(ctx, actorId, beFiCoinOffers, beExchangerOffers, displayExpressionFunctionMap)
	if err != nil {
		logger.Error(ctx, "error filtering be offers and exchanger offers", zap.Error(err))
		// not failing the flow due to error in filtering, since current filtering logic is not critical
	}

	feFiCoinOffers, err := r.getFeFiCoinOffers(ctx, reqHeader, beFiCoinOffers, beFiCoinOffersListingMap, displayExpressionFunctionMap, tagsConfig.GetTagRenderLocation(), beOfferIdToOfferLevelInventoryMap, fiCoinsBalance, offerIdToUserLevelAttemptsRemainingMap, offerIdsToRemainingMonthlyRedemptionsMap)
	if err != nil {
		logger.Error(ctx, "error converting be coin offers to fe coins offers", zap.Error(err))
		return nil, nil, fmt.Errorf("error converting be coin offers to fe coins offers")
	}

	feExchangerOffers, err := r.getFeExchangerOffers(ctx, reqHeader, beExchangerOffers, beExchangerOffersListingMap, beExchangerOffersAttemptsCountMap, beExchangerOffersMaxCapHitMap, isUserFullSalaryProgramActive, fiCoinsBalance, displayExpressionFunctionMap, tagsConfig.GetTagRenderLocation(), exchangerOfferIdsToRemainingMonthlyRedemptionsMap)
	if err != nil {
		logger.Error(ctx, "error converting be exchanger offers to fe exchanger offers", zap.Error(err))
		return nil, nil, fmt.Errorf("error converting be exchanger offers to fe exchanger offers")
	}

	// generating beFiCoinOffers list based on offers present in feFiCoinOffers list
	offerIdToConversionRatio := map[string]*fePb.FiCoinsConversionRatio{}
	feFiCoinOfferIdExistenceMap := make(map[string]struct{})
	for _, feOffer := range feFiCoinOffers {
		feFiCoinOfferIdExistenceMap[feOffer.GetId()] = struct{}{}
	}
	var feConvertedBeFiCoinOffers []*beCasperPb.Offer
	feConvertedBeFiCoinOffersMap := make(map[string]*beCasperPb.Offer)
	for _, beOffer := range beFiCoinOffers {
		if _, ok := feFiCoinOfferIdExistenceMap[beOffer.GetId()]; ok {
			feConvertedBeFiCoinOffers = append(feConvertedBeFiCoinOffers, beOffer)
			feConvertedBeFiCoinOffersMap[beOffer.GetId()] = beOffer
			if beOffer.GetAdditionalDetails().GetConversionRatio().GetCashEquivalent() != 0 {
				offerIdToConversionRatio[beOffer.GetId()] = getFeConversionRatio(uint64(beOffer.GetAdditionalDetails().GetConversionRatio().GetFiCoinsValue()), uint64(beOffer.GetAdditionalDetails().GetConversionRatio().GetCashEquivalent()))
			}
		}
	}

	// generating beExchangerOffers list based on offers present in feExchangerOffers list
	feExchangerOfferIdExistenceMap := make(map[string]struct{})
	for _, feOffer := range feExchangerOffers {
		feExchangerOfferIdExistenceMap[feOffer.GetId()] = struct{}{}
	}
	var feConvertedBeExchangerOffers []*beExchangerPb.ExchangerOffer
	feConvertedBeExchangerOffersMap := make(map[string]*beExchangerPb.ExchangerOffer)
	for _, beOffer := range beExchangerOffers {
		if _, ok := feExchangerOfferIdExistenceMap[beOffer.GetId()]; ok {
			feConvertedBeExchangerOffers = append(feConvertedBeExchangerOffers, beOffer)
			feConvertedBeExchangerOffersMap[beOffer.GetId()] = beOffer
			if beOffer.GetAdditionalDetails().GetConversionRatio().GetCashEquivalent() != 0 {
				offerIdToConversionRatio[beOffer.GetId()] = getFeConversionRatio(uint64(beOffer.GetAdditionalDetails().GetConversionRatio().GetFiCoinsValue()), uint64(beOffer.GetAdditionalDetails().GetConversionRatio().GetCashEquivalent()))
			}
		}
	}

	var (
		tagNames            []beCasperPb.TagName
		categoryTagNames    []beCasperPb.CategoryTag
		subcategoryTagNames []beCasperPb.SubCategoryTag
	)
	// extract unique tags from BE offers if flag extractTags is set
	if tagsConfig.GetExtractTags() {
		// we only need to extract tags from those offers that have been converted to FE
		// proto successfully, i.e., have not been filtered out because of some
		// condition or error in conversion.
		tagNames, categoryTagNames, subcategoryTagNames = r.getUniqueTagsFromOffers(feConvertedBeExchangerOffers, feConvertedBeFiCoinOffers)
	}

	// filter offers if FE filtering is required
	filteredOffersMap := map[string]struct{}{}
	filteredExchangerOffersMap := map[string]struct{}{}
	if shouldFilterOnFrontend {
		var (
			tagFilters         []beCasperPb.TagName
			categoryTagFilters []beCasperPb.CategoryTag
		)
		// we're ignoring error here as if FE filtering fails because of any reason we'll return the complete catalog
		tagFilters = filters.GetTags()
		categoryTagFilters = filters.GetCategoryTags()

		// filter fi coin offers
		lo.ForEach(feConvertedBeFiCoinOffers, func(offer *beCasperPb.Offer, _ int) {
			if lo.Some(offer.GetTagsInfo().GetManualTags(), tagFilters) || lo.Some(offer.GetTagsInfo().GetTags(), tagFilters) || lo.Contains(categoryTagFilters, offer.GetTagsInfo().GetCategoryTag()) {
				filteredOffersMap[offer.GetId()] = struct{}{}
			}
		})

		// filter exchanger offers
		lo.ForEach(feConvertedBeExchangerOffers, func(offer *beExchangerPb.ExchangerOffer, _ int) {
			if lo.Some(offer.GetTagsInfo().GetManualTags(), tagFilters) || lo.Some(offer.GetTagsInfo().GetTags(), tagFilters) || lo.Contains(categoryTagFilters, offer.GetTagsInfo().GetCategoryTag()) {
				filteredExchangerOffersMap[offer.GetId()] = struct{}{}
			}
		})
	}

	// generate the wrapper offer using fe offers
	offerCatalog := make([]*fePb.CatalogOfferV1, 0, len(feFiCoinOffers)+len(feExchangerOffers))
	// allFeCatalogOffers contains all the FE catalog offers, including the ones that are filtered out on frontend
	allFeCatalogOffers := make([]*fePb.CatalogOfferV1, 0, len(feFiCoinOffers)+len(feExchangerOffers))
	for _, fiCoinOffer := range feFiCoinOffers {
		// get top right tags and all tags
		topRightTags := getTopRightTagsForCatalogOffer(fiCoinOffer.GetTags(), offerIdToConversionRatio[fiCoinOffer.GetId()])
		allTags := getAllTags(feConvertedBeFiCoinOffersMap[fiCoinOffer.GetId()].GetTagsInfo())
		categoryTag := feConvertedBeFiCoinOffersMap[fiCoinOffer.GetId()].GetTagsInfo().GetCategoryTag().String()
		subcategoryTag := feConvertedBeFiCoinOffersMap[fiCoinOffer.GetId()].GetTagsInfo().GetSubCategoryTag().String()
		// if FE filtering is required, only append those offers
		if shouldFilterOnFrontend && (len(filters.GetTags()) > 0 || len(filters.GetCategoryTags()) > 0) {
			if _, ok := filteredOffersMap[fiCoinOffer.GetId()]; ok {
				offerCatalog = append(offerCatalog, &fePb.CatalogOfferV1{OfferData: &fePb.CatalogOfferV1_Offer{Offer: fiCoinOffer}, TopRightTags: topRightTags, AllTags: allTags, CategoryTag: categoryTag, SubCategoryTag: subcategoryTag})
			}
		} else {
			offerCatalog = append(offerCatalog, &fePb.CatalogOfferV1{OfferData: &fePb.CatalogOfferV1_Offer{Offer: fiCoinOffer}, TopRightTags: topRightTags, AllTags: allTags, CategoryTag: categoryTag, SubCategoryTag: subcategoryTag})
		}
		allFeCatalogOffers = append(allFeCatalogOffers, &fePb.CatalogOfferV1{OfferData: &fePb.CatalogOfferV1_Offer{Offer: fiCoinOffer}, TopRightTags: topRightTags, AllTags: allTags, CategoryTag: categoryTag, SubCategoryTag: subcategoryTag})
	}
	for _, exchangerOffer := range feExchangerOffers {
		// get top right tags and all tags
		topRightTags := getTopRightTagsForCatalogOffer(exchangerOffer.GetTags(), offerIdToConversionRatio[exchangerOffer.GetId()])
		allTags := getAllTags(feConvertedBeExchangerOffersMap[exchangerOffer.GetId()].GetTagsInfo())
		categoryTag := feConvertedBeExchangerOffersMap[exchangerOffer.GetId()].GetTagsInfo().GetCategoryTag().String()
		subcategoryTag := feConvertedBeExchangerOffersMap[exchangerOffer.GetId()].GetTagsInfo().GetSubCategoryTag().String()
		// if FE filtering is required, only append those offers
		if shouldFilterOnFrontend && (len(filters.GetTags()) > 0 || len(filters.GetCategoryTags()) > 0) {
			if _, ok := filteredExchangerOffersMap[exchangerOffer.GetId()]; ok {
				offerCatalog = append(offerCatalog, &fePb.CatalogOfferV1{OfferData: &fePb.CatalogOfferV1_ExchangerOffer{ExchangerOffer: exchangerOffer}, TopRightTags: topRightTags, AllTags: allTags, CategoryTag: categoryTag, SubCategoryTag: subcategoryTag})
			}
		} else {
			offerCatalog = append(offerCatalog, &fePb.CatalogOfferV1{OfferData: &fePb.CatalogOfferV1_ExchangerOffer{ExchangerOffer: exchangerOffer}, TopRightTags: topRightTags, AllTags: allTags, CategoryTag: categoryTag, SubCategoryTag: subcategoryTag})
		}
		allFeCatalogOffers = append(allFeCatalogOffers, &fePb.CatalogOfferV1{OfferData: &fePb.CatalogOfferV1_ExchangerOffer{ExchangerOffer: exchangerOffer}, TopRightTags: topRightTags, AllTags: allTags, CategoryTag: categoryTag, SubCategoryTag: subcategoryTag})
	}

	// sort offerCatalog for display
	if r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().OffersOrderingConfig().IsDynamicSortingEnabled(ctx) {
		r.dynamicOfferCatalogOrderingForDisplay(ctx, offerCatalog, beFiCoinOffers, beExchangerOffers, displayFirstOrderIds, sortBy, screen, fiCoinsBalance)
		r.dynamicOfferCatalogOrderingForDisplay(ctx, allFeCatalogOffers, beFiCoinOffers, beExchangerOffers, displayFirstOrderIds, sortBy, screen, fiCoinsBalance)
	} else {
		r.staticOfferCatalogOrderingForDisplay(offerCatalog, beFiCoinOffers, beExchangerOffers, displayFirstOrderIds, sortBy, screen)
		r.staticOfferCatalogOrderingForDisplay(allFeCatalogOffers, beFiCoinOffers, beExchangerOffers, displayFirstOrderIds, sortBy, screen)
	}

	return offerCatalog, &feRewardsPkg.CatalogOffersAdditionalDetails{
		TagNames:                      tagNames,
		CategoryTagNames:              categoryTagNames,
		SubCategoryTagNames:           subcategoryTagNames,
		OfferIdToBeOfferMap:           feConvertedBeFiCoinOffersMap,
		OfferIdToBeExchangerOffersMap: feConvertedBeExchangerOffersMap,
		AllFeCatalogOffers:            allFeCatalogOffers,
		UserSpecificMetadata:          userSpecificMetadata,
	}, nil
}

// filterBEOffersAndExchangerOffersForActor filters BE offers and exchanger offers.
// if the user has a credit card, all offers are returned. Otherwise, offers with specific category tags are excluded.
func (r *RewardService) filterBEOffersAndExchangerOffersForActor(ctx context.Context, actorId string, beOffersList []*beCasperPb.Offer, beExchangerOffersList []*beExchangerPb.ExchangerOffer, displayExpressionFunctionMap map[string]govaluate.ExpressionFunction) ([]*beCasperPb.Offer, []*beExchangerPb.ExchangerOffer, error) {
	// if there are no category tags to exclude, return all offers
	if r.dyconf.RewardsFrontendMeta().NonCCUsersCatalogOffersExcludedCategoryTags(ctx) == "" {
		return beOffersList, beExchangerOffersList, nil
	}

	var (
		beOffersListFiltered          []*beCasperPb.Offer
		beExchangerOffersListFiltered []*beExchangerPb.ExchangerOffer
	)

	var isUserCCActive bool
	// check if the user has a credit card using the display expression function if available (to avoid duplicate call to cc)
	if expressionFunction, ok := displayExpressionFunctionMap[offerdisplay.CriteriaToExpressionFunctionMap[offerdisplay.IsUserCreditCardActive]]; ok {
		val, evalErr := expressionFunction()
		if evalErr != nil {
			return beOffersList, beExchangerOffersList, fmt.Errorf("error evaluating expression function: %w", evalErr)
		}
		isUserCCActive, ok = val.(bool)
		if !ok {
			return beOffersList, beExchangerOffersList, fmt.Errorf("error casting value to bool")
		}
	} else {
		var err error
		isUserCCActive, err = r.checkIfUserHasACc(ctx, actorId)
		if err != nil {
			return beOffersList, beExchangerOffersList, fmt.Errorf("error checking if user has a credit card: %w", err)
		}
	}
	// if user has a credit card, return all offers, since the below filtering is only for users without a credit card
	if isUserCCActive {
		return beOffersList, beExchangerOffersList, nil
	}

	var categoriesToExclude []beCasperPb.CategoryTag
	for _, categoryStr := range strings.Split(r.dyconf.RewardsFrontendMeta().NonCCUsersCatalogOffersExcludedCategoryTags(ctx), ",") {
		if _, ok := beCasperPb.CategoryTag_value[categoryStr]; !ok {
			return nil, nil, fmt.Errorf("invalid category tag: %s", categoryStr)
		}
		categoriesToExclude = append(categoriesToExclude, beCasperPb.CategoryTag(beCasperPb.CategoryTag_value[categoryStr]))
	}

	beOffersListFiltered = lo.Filter(beOffersList, func(offer *beCasperPb.Offer, idx int) bool {
		return !lo.Contains(categoriesToExclude, offer.GetTagsInfo().GetCategoryTag())
	})

	beExchangerOffersListFiltered = lo.Filter(beExchangerOffersList, func(offer *beExchangerPb.ExchangerOffer, idx int) bool {
		return !lo.Contains(categoriesToExclude, offer.GetTagsInfo().GetCategoryTag())
	})

	return beOffersListFiltered, beExchangerOffersListFiltered, nil
}

// getBeFiCoinOffers calls backend to fetch fi coin offers and offer id to listing map
// nolint:funlen
// todo(sresth) refactor and reduce the number of values returned
func (r *RewardService) getBeFiCoinOffers(
	ctx context.Context,
	req *feHeaderPb.RequestHeader,
	filters *beCasperPb.CatalogFilters,
	shouldFilterOnFrontend bool,
) ([]*beCasperPb.Offer, map[string]*beCasperPb.OfferListing, map[string]*beCasperPb.OfferInventory, map[string]int32, map[string]uint32, error) {
	var (
		actorId           = req.GetAuth().GetActorId()
		appPlatform       = req.GetAuth().GetDevice().GetPlatform()
		appVersion        = req.GetAppVersionCode()
		beTagsList        []beCasperPb.TagName
		beCategoryTagList []beCasperPb.CategoryTag
		err               error
	)

	// if FE filtering is required we won't pass filters while calling BE RPC
	if !shouldFilterOnFrontend {
		beTagsList = filters.GetTags()
		beCategoryTagList = filters.GetCategoryTags()
	}

	getOffersRequest := &beCasperPb.GetOffersRequest{
		ActorId:        actorId,
		RedemptionMode: r.getBeOfferRedemptionMode(fePb.OfferRedemptionMode_FI_COINS),
		Filters:        &beCasperPb.CatalogFilters{Tags: beTagsList, CategoryTags: beCategoryTagList},
		// ignored the PageContext field as it's not used at the backend anyway
	}

	getOffersResponse, err := r.offerListingClient.GetOffers(ctx, getOffersRequest)
	if err != nil || !getOffersResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetOffers call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOffersResponse.GetStatus()))
		return nil, nil, nil, nil, nil, errors.New("GetOffers call failed in getFeAndBeFiCoinOffers")
	}
	beOffersList := make([]*beCasperPb.Offer, 0, len(getOffersResponse.GetOffers()))
	offerIdToListingMap := getOffersResponse.GetOfferIdToListingMap()

	// filter out offers which are unsupported
	var offerIdListUnfiltered []string
	for _, beOffer := range getOffersResponse.GetOffers() {
		offerIdListUnfiltered = append(offerIdListUnfiltered, beOffer.GetId())
	}

	getOfferInventoryByOfferIdsResponse, err := r.offerInventoryService.GetOfferInventoryByOfferIds(ctx,
		&beCasperPb.GetOfferInventoryByOfferIdsRequest{
			OfferIds: offerIdListUnfiltered,
		},
	)
	if err != nil || !getOfferInventoryByOfferIdsResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetOfferInventoryByOfferIds call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOfferInventoryByOfferIdsResponse.GetStatus()))
		return nil, nil, nil, nil, nil, errors.New("GetOfferInventoryByOfferIds call failed in getBeFiCoinOffers")
	}
	offerIdToOfferInventoryMap := getOfferInventoryByOfferIdsResponse.GetOfferIdToOfferInventoryMap()

	offerIdToUserLevelAttemptsRemainingMap := getOffersResponse.GetOfferIdToUserLevelAttemptsRemainingMap()

	// filter out offers which are unsupported
	for _, beOffer := range getOffersResponse.GetOffers() {
		userLevelAttemptsRemaining := offerIdToUserLevelAttemptsRemainingMap[beOffer.GetId()]
		globalLevelOfferInventory := offerIdToOfferInventoryMap[beOffer.GetId()].GetAvailableCount()
		shouldDisplayFiCoinOfferOnAppVersion, err1 := r.shouldDisplayFiCoinOfferOnAppVersion(ctx, beOffer, offerIdToListingMap[beOffer.GetId()], userLevelAttemptsRemaining, globalLevelOfferInventory, appPlatform, appVersion)
		// bypassing error as flow should not break due to corropt offer
		if err1 != nil {
			logger.Error(ctx, "shouldDisplayFiCoinOfferOnAppVersion call failed", zap.Error(err1), zap.String("offerId: ", beOffer.GetId()))
			continue
		}
		if !shouldDisplayFiCoinOfferOnAppVersion {
			continue
		}

		beOffersList = append(beOffersList, beOffer)
	}

	return beOffersList, offerIdToListingMap, offerIdToOfferInventoryMap, offerIdToUserLevelAttemptsRemainingMap, getOffersResponse.GetOfferIdToRemainingMonthlyRedemptionsMap(), nil
}

// getFeFiCoinOffers converts be fi coin offers to fe fi coin offers using render location and tagEligibility map
// nolint:funlen
func (r *RewardService) getFeFiCoinOffers(
	ctx context.Context,
	req *feHeaderPb.RequestHeader,
	beOffersList []*beCasperPb.Offer,
	offerIdToListingMap map[string]*beCasperPb.OfferListing,
	displayExpressionFunctionMap map[string]govaluate.ExpressionFunction,
	renderLocation tags.RenderLocation,
	beOfferIdToOfferLevelInventoryMap map[string]*beCasperPb.OfferInventory,
	fiCoinsBalance uint32,
	offerIdToUserLevelAttemptsRemainingMap map[string]int32,
	offerIdsToRemainingMonthlyRedemptionsMap map[string]uint32,
) ([]*fePb.Offer, error) {
	var (
		isSalaryAccountExclusiveOfferPresent = false
		isUserFullSalaryProgramActive        = false
		actorId                              = req.GetAuth().GetActorId()
		appPlatform                          = req.GetAuth().GetDevice().GetPlatform()
		appVersion                           = req.GetAppVersionCode()
	)

	for _, beOffer := range beOffersList {
		if beOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive() || beOffer.GetAdditionalDetails().GetSalaryAccountDisplayTag() != nil {
			isSalaryAccountExclusiveOfferPresent = true
			break
		}
	}
	if isSalaryAccountExclusiveOfferPresent {
		// fetch salary program details
		var err error
		isUserFullSalaryProgramActive, err = r.IsUserFullSalaryProgramActive(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error checking if salary program is active for user or not", zap.Error(err))
			return nil, errors.New("error checking if salary program is active for user or not")
		}
	}

	feOffersList := make([]*fePb.Offer, 0, len(beOffersList))
	for _, beOffer := range beOffersList {
		isMaxMonthlyCapHit := false
		if remainingMonthlyRedemptions, ok := offerIdsToRemainingMonthlyRedemptionsMap[beOffer.GetId()]; ok && remainingMonthlyRedemptions == 0 {
			isMaxMonthlyCapHit = true
		}
		feOffer, err := r.getFeOffer(ctx, beOffer, offerIdToListingMap[beOffer.GetId()], displayExpressionFunctionMap, isUserFullSalaryProgramActive, appVersion, appPlatform, renderLocation, beOfferIdToOfferLevelInventoryMap, fiCoinsBalance, offerIdToUserLevelAttemptsRemainingMap, isMaxMonthlyCapHit)
		if err != nil {
			logger.Error(ctx, "error converting beOffer to feOffer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Error(err))
			continue
		}
		if feOffer == nil {
			logger.Debug(ctx, "user is not eligible for the offer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Error(err))
			continue
		}
		// if app version/platform combo doesn't support UnredeemableOfferLabel, skip this offer
		if feOffer.GetDisplayDetails().GetUnredeemableOfferLabel() != nil {
			if (appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingUnredeemableOfferLabel()) || (appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingUnredeemableOfferLabel()) {
				logger.Info(ctx, "app version/platform combo doesn't support unredeemable offer label, skipping offer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Uint32("appVersion", appVersion), zap.String("appPlatform", appPlatform.String()))
				continue
			}
		}

		feOffersList = append(feOffersList, feOffer)
	}

	return feOffersList, nil
}

// getBeExchangerOffers fetches the beExchangerOffers, attempts for each offers by the actor and current fi-coins balance
// returns beExchangerOffersList, offerIdToListingMa, offerIdToAttemptsCountMap, offerIdToMaxCapHitMap, isUserFullSalaryProgramActive, fiCoinsBalance
// nolint:funlen
func (r *RewardService) getBeExchangerOffers(
	ctx context.Context,
	req *feHeaderPb.RequestHeader,
	catalogFilters *beCasperPb.CatalogFilters,
	offerStatus beExchangerPb.ExchangerOfferStatus,
	shouldFilterOnFrontend bool,
) ([]*beExchangerPb.ExchangerOffer, map[string]*beExchangerPb.ExchangerOfferListing, map[string]int32, map[string]bool, bool, uint32, map[string]uint32, error) {
	var (
		actorId           = req.GetAuth().GetActorId()
		beTagsList        []beCasperPb.TagName
		beCategoryTagList []beCasperPb.CategoryTag
		err               error
	)

	// if FE filtering is required we won't pass filters while calling BE RPC
	if !shouldFilterOnFrontend {
		beTagsList = catalogFilters.GetTags()
		beCategoryTagList = catalogFilters.GetCategoryTags()
	}

	getOffersRequest := &beExchangerPb.GetExchangerOffersRequest{
		ActorId: actorId,
		Filters: &beCasperPb.CatalogFilters{Tags: beTagsList, CategoryTags: beCategoryTagList},
		FiltersV2: &beExchangerPb.GetExchangerOffersRequest_FiltersV2{
			Status: offerStatus,
		},
	}

	getOffersResponse, err := r.exchangerOfferClient.GetExchangerOffers(ctx, getOffersRequest)
	if err != nil || !getOffersResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetExchangerOffers call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOffersResponse.GetStatus()))
		return nil, nil, nil, nil, false, 0, nil, errors.New("GetExchangerOffers call failed in getFeAndBeExchangerOffers")
	}

	beExchangerOffersList := make([]*beExchangerPb.ExchangerOffer, 0, len(getOffersResponse.GetExchangerOffers()))

	// filtering out exchanger offers that are not supported by client's app version, for e.g.
	// 1. Physical merchandise only for CBR-V2
	// 2. Salary Account Exclusive offers
	for _, beExchangerOffer := range getOffersResponse.GetExchangerOffers() {
		// CBR offers filtering based on app-version
		isExchangerOfferSupportedOnAppVersion, err1 := r.isExchangerOfferSupportedOnAppVersion(ctx, getOffersResponse.GetOfferIdToListingMap()[beExchangerOffer.GetId()], beExchangerOffer, req)
		// bypassing the error as one corrupted offer should not break the flow
		if err1 != nil {
			logger.Error(ctx, "isExchangerOfferSupportedOnAppVersion call failed in getBeExchangerOffers ", zap.String("offerId:", beExchangerOffer.GetId()), zap.Error(err1))
			continue
		}
		if !isExchangerOfferSupportedOnAppVersion {
			continue
		}

		beExchangerOffersList = append(beExchangerOffersList, beExchangerOffer)
	}

	// todo: move the below independent method calls into goroutines

	// check salary program status for the actor to decide on offers' status
	var (
		isSalaryAccountExclusiveOfferPresent = false
		isUserFullSalaryProgramActive        = false
	)

	for _, beExchangerOffer := range beExchangerOffersList {
		if beExchangerOffer.GetOfferDisplayDetails().GetSalaryAccountTag() != nil || beExchangerOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive() {
			isSalaryAccountExclusiveOfferPresent = true
			break
		}
	}
	if isSalaryAccountExclusiveOfferPresent {
		// fetch salary program details
		isUserFullSalaryProgramActive, err = r.IsUserFullSalaryProgramActive(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error checking if salary program is active for user or not", zap.Error(err))
			return nil, nil, nil, nil, false, 0, nil, errors.New("error checking if salary program is active for user or not")
		}
	}

	// retrieve all offer-ids to fetch attempts made by actor for each offer, and all offer Ids with monthly cap defined
	exchangerOfferIds := make([]string, len(beExchangerOffersList))
	var offerIdsWithMonthlyCap []string
	offerIdToMonthlyCapMap := map[string]uint32{}
	for i, beExchangerOffer := range beExchangerOffersList {
		exchangerOfferIds[i] = beExchangerOffer.GetId()
		if beExchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap() != 0 {
			offerIdsWithMonthlyCap = append(offerIdsWithMonthlyCap, beExchangerOffer.GetId())
			offerIdToMonthlyCapMap[beExchangerOffer.GetId()] = beExchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap()
		}
	}
	currentDayStartTime := datetime.GetTimeAtStartOfTheDay(time.Now().In(datetime.IST))
	offersActorAttemptsCountRes, err := r.exchangerOfferClient.GetExchangerOffersActorAttemptsCount(ctx, &beExchangerPb.GetExchangerOffersActorAttemptsCountRequest{
		ActorId:           actorId,
		ExchangerOfferIds: exchangerOfferIds,
		// start time of today's date in IST
		FromAttemptedTime: timestamppb.New(currentDayStartTime),
		// start time of tomorrow's date in IST
		ToAttemptedTime: timestamppb.New(currentDayStartTime.AddDate(0, 0, 1)),
	})
	if err != nil || !offersActorAttemptsCountRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching attempts count for exchanger-offers", zap.String(logger.ACTOR_ID, actorId),
			zap.Any(logger.RPC_STATUS, offersActorAttemptsCountRes.GetStatus()), zap.Error(err),
		)
		return nil, nil, nil, nil, false, 0, nil, errors.New("error fetching attempts count for exchanger-offers")
	}

	// retrieve the current fi-coins balance to decide the whether offers are operable or not
	fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		return nil, nil, nil, nil, false, 0, nil, errors.Wrap(err, "error fetching fi-coins balance for actor in getFeAndBeExchangerOffers")
	}

	offerIdToMaxCapHitMap, err := r.isEORewardUnitsMaxCapReached(ctx, actorId, beExchangerOffersList)
	if err != nil {
		return nil, nil, nil, nil, false, 0, nil, errors.Wrap(err, "error checking if max-cap for reward-units is hit by an actor for exchanger-offers")
	}

	// fetch the monthly redemptions done on offers that have a monthly limit specified
	offerIdToRemainingMonthlyRedemptionsMap := map[string]uint32{}
	if len(offerIdsWithMonthlyCap) > 0 {
		redemptionCountsForActorOfferIdsInMonthRes, err := r.exchangerOfferClient.GetRedemptionCountsForActorOfferIdsInMonth(ctx, &beExchangerPb.GetRedemptionCountsForActorOfferIdsInMonthRequest{
			ActorId:        actorId,
			OfferIds:       offerIdsWithMonthlyCap,
			MonthTimestamp: timestamppb.Now(),
		})
		// if RPC returns a NotFound status, we don't need to do anything
		if !redemptionCountsForActorOfferIdsInMonthRes.GetStatus().IsSuccess() || err != nil {
			return nil, nil, nil, nil, false, 0, nil, fmt.Errorf("error while fetching redemption count of offers for actor in month, err: %w", epifigrpc.RPCError(redemptionCountsForActorOfferIdsInMonthRes, err))
		}
		for offerId, monthlyCap := range offerIdToMonthlyCapMap {
			if redemptionsCount, ok := redemptionCountsForActorOfferIdsInMonthRes.GetOfferIdToRedemptionsCountInMonthMap()[offerId]; ok {
				offerIdToRemainingMonthlyRedemptionsMap[offerId] = monthlyCap - redemptionsCount
			}
		}

	}

	return beExchangerOffersList, getOffersResponse.GetOfferIdToListingMap(), offersActorAttemptsCountRes.GetOfferIdToAttemptsCountMap(), offerIdToMaxCapHitMap, isUserFullSalaryProgramActive, fiCoinsBalance, offerIdToRemainingMonthlyRedemptionsMap, nil
}

// getFeExchangerOffers converts beExchangerOffers to feExchangerOffers using offer id to listingMap, attemptsMap and maxCapHitMap, and fiCoinsBalance
// nolint:funlen
func (r *RewardService) getFeExchangerOffers(
	ctx context.Context,
	req *feHeaderPb.RequestHeader,
	beExchangerOffersList []*beExchangerPb.ExchangerOffer,
	offerIdToListingMap map[string]*beExchangerPb.ExchangerOfferListing,
	offerIdToAttemptsCountMap map[string]int32,
	offerIdToMaxCapHitMap map[string]bool,
	isUserFullSalaryProgramActive bool,
	fiCoinsBalance uint32,
	displayExpressionFunctionMap map[string]govaluate.ExpressionFunction,
	renderLocation tags.RenderLocation,
	offerIdsToRemainingMonthlyRedemptionsMap map[string]uint32,
) ([]*fePb.ExchangerOfferWidget, error) {
	// convert beExchangerOffer to feExchangerOffer
	feExchangerOffersList := make([]*fePb.ExchangerOfferWidget, 0, len(beExchangerOffersList))

	for _, beExchangerOffer := range beExchangerOffersList {
		isMonthlyCapHit := false
		if beExchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap() != 0 {
			if remainingMonthlyAttempts, ok := offerIdsToRemainingMonthlyRedemptionsMap[beExchangerOffer.GetId()]; ok && remainingMonthlyAttempts == 0 {
				isMonthlyCapHit = true
			}
		}

		feExchangerOffer, err := r.convertToFeExchangerOffer(ctx, beExchangerOffer, offerIdToListingMap[beExchangerOffer.GetId()],
			offerIdToAttemptsCountMap[beExchangerOffer.GetId()],
			fiCoinsBalance, offerIdToMaxCapHitMap[beExchangerOffer.GetId()], isUserFullSalaryProgramActive,
			displayExpressionFunctionMap, renderLocation, isMonthlyCapHit,
		)
		if err != nil {
			logger.Error(ctx, "error converting beExchangerOffer to feExchangerOffer", zap.String(logger.OFFER_ID, beExchangerOffer.GetId()), zap.Error(err))
			continue
		}
		if feExchangerOffer == nil {
			logger.Debug(ctx, "exchanger offer is not eligible for user", zap.String(logger.OFFER_ID, beExchangerOffer.GetId()))
			continue
		}

		feExchangerOffersList = append(feExchangerOffersList, feExchangerOffer)
	}
	return feExchangerOffersList, nil
}

// getBeFiCardOffers returns be fi card offers and offer-id to listings map
// nolint:funlen
func (r *RewardService) getBeFiCardOffers(ctx context.Context, actorId string, cardType fePb.CardType, orTags []string, andTags []string) ([]*beCasperPb.Offer, map[string]*beCasperPb.OfferListing, error) {

	beOfferOrTags := r.getBeCatalogTagsFromTagNamesList(orTags)
	beOfferAndTags := r.getBeCatalogTagsFromTagNamesList(andTags)

	var (
		getCardOffersResponse     *beCasperPb.GetCardOffersResponse
		beOffers                  []*beCasperPb.Offer
		isRecentlySaOnboardedUser = r.isRecentlySaOnboardedUser(ctx, actorId, time.Hour*24*28)
		err                       error
	)
	// fetch catalog for cache cases where no filters are needed while fetching the catalog
	if len(beOfferOrTags) == 0 && len(beOfferAndTags) == 0 {
		getCardOffersCacheKey := fmt.Sprintf(cardOfferCatalogCacheKeyTemplate, cardType.String())
		getCardOffersCachedResponse, err := r.rewardsCacheStorage.Get(ctx, getCardOffersCacheKey)
		if err == nil {
			getCardOffersUnmarshalledRes := &beCasperPb.GetCardOffersResponse{}
			if err = protojson.Unmarshal([]byte(getCardOffersCachedResponse), getCardOffersUnmarshalledRes); err != nil {
				// intentionally muting the error as unmarshalling error shouldn't fail the
				// catalog load
				logger.Error(ctx, "error while unmarshalling getCardOffersResponse", zap.Error(err))
			} else {
				getCardOffersResponse = getCardOffersUnmarshalledRes
			}
		} else if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			// intentionally muting the error as cached fetch error shouldn't fail the
			// catalog load, can fall back to fetching the offer catalog directly from source
			// for such cases
			logger.Error(ctx, "error while reading getCardOffersResponse from cache", zap.Error(err))
		}
	}

	if getCardOffersResponse == nil {
		getCardOffersResponse, err = r.offerListingClient.GetCardOffers(ctx, &beCasperPb.GetCardOffersRequest{
			RedemptionMode: r.getBeOfferRedemptionModeFromCardType(cardType),
			FiltersV2: &beCasperPb.CatalogFiltersV2{
				OrTags:  beOfferOrTags,
				AndTags: beOfferAndTags,
			},
		})
		if rpcErr := epifigrpc.RPCError(getCardOffersResponse, err); rpcErr != nil {
			return nil, nil, fmt.Errorf("error while fetching Card offers, err: %w", rpcErr)
		}

		// cache catalog response for cases where no filters are applied
		if len(beOfferOrTags) == 0 && len(beOfferAndTags) == 0 {
			marshalledRes, err := protojson.Marshal(getCardOffersResponse)
			if err != nil {
				// intentionally muting the error as marshalling error shouldn't fail the
				// catalog load
				logger.Error(ctx, "error while marshalling getCardOffersResponse", zap.Error(err))
			} else {
				// calculating ttl according to card offer active till time
				// eg. if an offer is only active till 5 mins more and defaultCardOfferCatalogCacheTtl is 10 mins then we change to ttl to 5 mins
				cardOfferCatalogCacheTtl := defaultCardOfferCatalogCacheTtl
				for _, listing := range getCardOffersResponse.GetOfferIdToListingMap() {
					listingActiveTillTime, parseErr := time.Parse(time.RFC3339, listing.GetActiveTill())
					// muting error as it should not make catalog load fail
					if parseErr != nil {
						logger.Error(ctx, "error in parsing active till time", zap.Error(parseErr))
					} else if timeDiff := listingActiveTillTime.Sub(time.Now()); timeDiff < cardOfferCatalogCacheTtl && timeDiff > 0 {
						cardOfferCatalogCacheTtl = timeDiff
					}
				}
				err = r.rewardsCacheStorage.Set(ctx, fmt.Sprintf(cardOfferCatalogCacheKeyTemplate, cardType.String()), string(marshalledRes), cardOfferCatalogCacheTtl)
				if err != nil {
					// intentionally muting error as error while caching shouldn't fail the entire flow
					logger.Error(ctx, "error while storing getCardOffersResponse to cache", zap.Error(err))
				}
			}
		}
	}

	beOffers = getCardOffersResponse.GetOffers()
	// offers should be ordered by display rank (in ascending order), then creation date (in descending order)
	sort.Slice(beOffers, func(i, j int) bool {
		return sortBeCardOffer(beOffers[i], beOffers[j], isRecentlySaOnboardedUser)
	})

	return beOffers, getCardOffersResponse.GetOfferIdToListingMap(), nil
}

// sortBeCardOffer sorts the offers based on the following criteria:
// 1. If the offer has a booster tag and the user is D0-D28, then display the booster offer first
// 2. If both offers are booster offers, then order by display rank in ascending order
func sortBeCardOffer(iOffer, jOffer *beCasperPb.Offer, isRecentlySaOnboardedUser bool) bool {
	var (
		iOfferDisplayRank, jOfferDisplayRank             = iOffer.GetAdditionalDetails().GetOfferDisplayRank(), jOffer.GetAdditionalDetails().GetOfferDisplayRank()
		iOfferTagsInfo, jOfferTagsInfo                   = iOffer.GetTagsInfo(), jOffer.GetTagsInfo()
		iOfferCreatedAt, jOfferCreatedAt                 = iOffer.GetCreatedAt().AsTime(), jOffer.GetCreatedAt().AsTime()
		iOfferBoosterTagPresent, jOfferBoosterTagPresent = lo.Contains(iOfferTagsInfo.GetManualTags(), beCasperPb.TagName_NEW_USER_PRIORITY), lo.Contains(jOfferTagsInfo.GetManualTags(), beCasperPb.TagName_NEW_USER_PRIORITY)
	)

	// if offer has a booster tag and user is D0-D28, then display the booster offer first
	// if both offers are booster offers, then order by display rank in ascending order
	if isRecentlySaOnboardedUser && (iOfferBoosterTagPresent != jOfferBoosterTagPresent) {
		return iOfferBoosterTagPresent
	}

	// order by display rank in ascending order
	if iOfferDisplayRank != jOfferDisplayRank {
		return iOfferDisplayRank < jOfferDisplayRank
	}
	// for equal display rank order by creation date in descending order
	return iOfferCreatedAt.After(jOfferCreatedAt)
}

func (r *RewardService) isRecentlySaOnboardedUser(ctx context.Context, actorId string, onboardedWithinDuration time.Duration) bool {
	var (
		validOnboardingCompletedStatus = []onboardingPb.FeatureStatus{
			onboardingPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS,
			onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE,
			onboardingPb.FeatureStatus_FEATURE_STATUS_INACTIVE,
		}
	)
	getDetailsResp, err := r.onboardingClient.GetDetails(ctx, &onboardingPb.GetDetailsRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(getDetailsResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching user details", zap.Error(rpcErr))
		return false
	}

	// check if the user is recently onboarded
	featureInfo := getDetailsResp.GetDetails().GetFeatureDetails().GetFeatureInfo()
	if featureInfo != nil {
		saFeatureInfo := featureInfo[onboardingPb.Feature_FEATURE_SA.String()]
		return lo.Contains(validOnboardingCompletedStatus, saFeatureInfo.GetFeatureStatus()) && time.Since(saFeatureInfo.GetCompletedAt().AsTime()) < onboardedWithinDuration
	}

	return false
}

// isEORewardUnitsMaxCapReached checks whether max-caps are hit for the reward-types by an actor or not.
// Currently, it checks at exchanger-offer-group level since that's where we have the max-cap config defined
// Note: Tomorrow it can have offer level checks
// nolint:funlen
func (r *RewardService) isEORewardUnitsMaxCapReached(ctx context.Context, actorId string, exchangerOffers []*beExchangerPb.ExchangerOffer) (map[string]bool, error) {
	var exchangerOfferGroupIds []string
	var groupsWithMaxCapConfig []string
	offerIdToMaxCapHitMap := make(map[string]bool)
	groupIdToOffersMap := make(map[string][]*beExchangerPb.ExchangerOffer)

	// set the max-cap hit to false for all offers by default
	for _, offer := range exchangerOffers {
		offerIdToMaxCapHitMap[offer.GetId()] = false
	}

	// extract offers added under groups
	// Note: Since we have config info at group level currently
	for _, offer := range exchangerOffers {
		if offer.GetGroupId() != "" {
			exchangerOfferGroupIds = append(exchangerOfferGroupIds, offer.GetGroupId())
			groupIdToOffersMap[offer.GetGroupId()] = append(groupIdToOffersMap[offer.GetGroupId()], offer)
		}
	}

	// return if the offers don't belong to any group
	// Note: This will change if tomorrow we add the config checks at offer level, or add inventory support
	if len(exchangerOfferGroupIds) == 0 {
		return offerIdToMaxCapHitMap, nil
	}

	exchangerOfferGroupsRes, err := r.exchangerOfferClient.GetExchangerOfferGroupsByIds(ctx, &beExchangerPb.GetExchangerOfferGroupsByIdsRequest{
		GroupIds: exchangerOfferGroupIds,
	})
	if err != nil || !exchangerOfferGroupsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching exchanger-offer groups by ids",
			zap.Any(logger.RPC_STATUS, exchangerOfferGroupsRes.GetStatus()), zap.Error(err),
		)
		return nil, errors.New("error fetching exchanger-offer groups by ids")
	}
	// extract those groups with max-cap config set
	for groupId, group := range exchangerOfferGroupsRes.GetGroupIdToGroupMap() {
		if group.GetEOGroupRewardUnitsCapUserAggregate() == nil ||
			len(group.GetEOGroupRewardUnitsCapUserAggregate().GetUnitsCaps()) == 0 {
			continue
		}
		groupsWithMaxCapConfig = append(groupsWithMaxCapConfig, groupId)
	}

	// return if there are no max-cap configs set for any groups
	if len(groupsWithMaxCapConfig) == 0 {
		return offerIdToMaxCapHitMap, nil
	}

	// fetch utilisation
	groupRewardUnitsActorUtilisationRes, err := r.exchangerOfferClient.GetEOGroupsRewardUnitsActorUtilisation(ctx, &beExchangerPb.GetEOGroupsRewardUnitsActorUtilisationRequest{
		ActorId:  actorId,
		GroupIds: groupsWithMaxCapConfig,
	})
	if err != nil {
		logger.Error(ctx, "error fetching exchanger-offer groups reward units utilisation", zap.Any(logger.RPC_STATUS, groupRewardUnitsActorUtilisationRes.GetStatus()), zap.Error(err))
		return nil, errors.New("error fetching exchanger-offer groups reward units utilisation")
	}

	// check if the utilisation of reward units by the actor has maxed out for an offer under a group
	for groupId, utilisation := range groupRewardUnitsActorUtilisationRes.GetGroupIdToUtilisationMap() {
		group := exchangerOfferGroupsRes.GetGroupIdToGroupMap()[groupId]
		offers := groupIdToOffersMap[groupId]

		for _, offer := range offers {
			// check if all options can generate only cash reward.
			// Why? Since if there are other possibilities for an option apart from cash, then the capping
			// logic during options generation will fall back to the other options.
			// Thus, if an option can only generate cash rewards, we'd want to check the cap utilisation
			// and deactivate the offer accordingly since there won't be any fallbacks.
			if !checkIfExchangerOptionsAreAllCash(offer.GetOfferOptionsConfig().GetOptionsConfig()) {
				continue
			}

			// check if the utilisation has maxed out
			// Note: Currently checking only for cash since we are planning to set max-cap only for cash
			if checkIfGroupLevelUtilisationHitMaxCap(group.GetEOGroupRewardUnitsCapUserAggregate().GetUnitsCaps(), utilisation, beExchangerPb.RewardType_REWARD_TYPE_CASH) {
				offerIdToMaxCapHitMap[offer.GetId()] = true
			}
		}
	}

	return offerIdToMaxCapHitMap, nil
}

// checkIfExchangerOptionsAreAllCash checks if the options of an exchanger-offer are all cash reward-type or not
func checkIfExchangerOptionsAreAllCash(optionsConfig []*beExchangerPb.ExchangerOfferOptionConfig) bool {
	if len(optionsConfig) == 0 {
		return false
	}

	for _, optionConfig := range optionsConfig {
		// should never be empty ideally. But covering this case explicitly.
		if len(optionConfig.GetRewardConfigUnits()) == 0 {
			return false
		}

		for _, configUnit := range optionConfig.GetRewardConfigUnits() {
			if configUnit.GetRewardType() != beExchangerPb.RewardType_REWARD_TYPE_CASH {
				return false
			}
		}
	}

	return true
}

// checkIfGroupLevelUtilisationHitMaxCap checks if the utilisation has hit the max-caps according to the config for the given reward-type or not
func checkIfGroupLevelUtilisationHitMaxCap(unitsCaps []*beExchangerPb.RewardUnitsCapAggregate_UnitsCap, utilisation *beExchangerPb.ExchangerOfferGroupRewardUnitsActorUtilisation, rewardType beExchangerPb.RewardType) bool {
	for _, unitsCap := range unitsCaps {
		if unitsCap.GetRewardType() == rewardType {
			if utilisation.GetCashUnits() >= unitsCap.GetUnits() {
				return true
			}
		}
	}

	return false
}

// struct to store the fields which will be used for ordering of the offer-catalog
type offerOrderingFields struct {
	DisplayRank              int32
	RedemptionPrice          uint32
	IsDisplayFirstOffer      bool
	DisplayFirstRank         int
	IsPromotedOffer          bool
	PromotedOfferRank        int32
	IsCcExclusiveOffer       bool
	IsSelectiveEligibleOffer bool
	IsAffordableOffer        bool
	IsCbrOffer               bool
	CreatedAt                *timestamppb.Timestamp
}

// constants used while ordering implementation for offer catalog
const (
	fiCoinOfferType           = "FI_COIN"
	exchangerOfferType        = "EXCHANGER"
	topDisplayRankOffersCount = 3
)

// staticOfferCatalogOrderingForDisplay orders the offers using attributes from fe and be entities
// @arg displayFirstOfferIds : list of offers that should be displayed first on the catalog (if present).
// this is a static ordering function and does the same sorting for everyone barring display first offers
// which may be context specific
// The sorting order is as follows:
// * Display First Offers
// * Promoted Offers
// * Display Rank (if equal, then the latest created offer is shown first)
// nolint:funlen
func (r *RewardService) staticOfferCatalogOrderingForDisplay(
	offerCatalog []*fePb.CatalogOfferV1,
	beFiCoinOffers []*beCasperPb.Offer,
	beExchangerOffers []*beExchangerPb.ExchangerOffer,
	displayFirstOfferIds []string,
	sortBy fePb.SortBy,
	screen deeplinkPb.Screen,
) {
	displayFirstOfferIdsToDisplayRank := make(map[string]int)
	for i, displayFirstOfferId := range displayFirstOfferIds {
		displayFirstOfferIdsToDisplayRank[displayFirstOfferId] = i
	}

	// offerId+offerType as "key" to prevent key collision in case of same offer-ids across casper
	offerIdToOrderingFieldsMap := make(map[string]*offerOrderingFields, len(beFiCoinOffers)+len(beExchangerOffers))

	for _, beFiCoinOffer := range beFiCoinOffers {
		redemptionPrice := uint32(beFiCoinOffer.GetPrice())
		if beFiCoinOffer.GetDiscountDetails().GetDiscountedPrice() != 0 {
			redemptionPrice = uint32(beFiCoinOffer.GetDiscountDetails().GetDiscountedPrice())
		}

		displayRank, found := beFiCoinOffer.GetAdditionalDetails().GetScreenDisplayRankMap()[screen.String()]
		if !found {
			displayRank = beFiCoinOffer.GetAdditionalDetails().GetOfferDisplayRank()
		}
		offerOrderFields := &offerOrderingFields{
			DisplayRank:     displayRank,
			CreatedAt:       beFiCoinOffer.GetCreatedAt(),
			RedemptionPrice: redemptionPrice,
		}
		if displayFirstRank, ok := displayFirstOfferIdsToDisplayRank[beFiCoinOffer.GetId()]; ok {
			offerOrderFields.IsDisplayFirstOffer = true
			offerOrderFields.DisplayFirstRank = displayFirstRank
		}
		offerIdToOrderingFieldsMap[beFiCoinOffer.GetId()+fiCoinOfferType] = offerOrderFields

		if lo.Contains(beFiCoinOffer.GetTagsInfo().GetManualTags(), beCasperPb.TagName_CREDIT_CARD_EXCLUSIVE) {
			offerOrderFields.IsCcExclusiveOffer = true
		}

		if beFiCoinOffer.GetAdditionalDetails().GetIsPromoOffer() {
			offerOrderFields.IsPromotedOffer = beFiCoinOffer.GetAdditionalDetails().GetIsPromoOffer()
			offerOrderFields.PromotedOfferRank = beFiCoinOffer.GetAdditionalDetails().GetPromoOfferDisplayRank()
		}
	}

	for _, beExchangerOffer := range beExchangerOffers {
		displayRank, found := beExchangerOffer.GetOfferDisplayDetails().GetScreenDisplayRankMap()[screen.String()]
		if !found {
			displayRank = beExchangerOffer.GetOfferDisplayDetails().GetDisplayRank()
		}
		offerOrderFields := &offerOrderingFields{
			DisplayRank:     displayRank,
			CreatedAt:       beExchangerOffer.GetCreatedAt(),
			RedemptionPrice: uint32(beExchangerOffer.GetRedemptionPrice()),
		}
		if displayFirstRank, ok := displayFirstOfferIdsToDisplayRank[beExchangerOffer.GetId()]; ok {
			offerOrderFields.IsDisplayFirstOffer = true
			offerOrderFields.DisplayFirstRank = displayFirstRank
		}
		offerIdToOrderingFieldsMap[beExchangerOffer.GetId()+exchangerOfferType] = offerOrderFields

		if lo.Contains(beExchangerOffer.GetTagsInfo().GetManualTags(), beCasperPb.TagName_CREDIT_CARD_EXCLUSIVE) {
			offerOrderFields.IsCcExclusiveOffer = true
		}

		if beExchangerOffer.GetAdditionalDetails().GetIsPromoOffer() {
			offerOrderFields.IsPromotedOffer = beExchangerOffer.GetAdditionalDetails().GetIsPromoOffer()
			offerOrderFields.PromotedOfferRank = beExchangerOffer.GetAdditionalDetails().GetPromoOfferDisplayRank()
		}
	}

	sort.Slice(offerCatalog, func(i, j int) bool {

		// fetching the ordering fields for an offer
		iOrderingFields := fetchOrderingFieldsFromCatalogOffer(offerCatalog[i], offerIdToOrderingFieldsMap)
		jOrderingFields := fetchOrderingFieldsFromCatalogOffer(offerCatalog[j], offerIdToOrderingFieldsMap)

		// sort based on different criteria depending on the value of `sortBy` parameter
		switch sortBy {
		case fePb.SortBy_REDEMPTION_PRICE_ASC:
			return iOrderingFields.RedemptionPrice < jOrderingFields.RedemptionPrice
		case fePb.SortBy_REDEMPTION_PRICE_DESC:
			return iOrderingFields.RedemptionPrice > jOrderingFields.RedemptionPrice
		default:
			// if any of the offer is the display first offer,
			// then it should be displayed first as compared to other offers.
			if iOrderingFields.IsDisplayFirstOffer || jOrderingFields.IsDisplayFirstOffer {
				// if both offers are display first offer, then use displayFirstRank for ordering amongst them.
				// lower displayFirstRank offer should be shown first
				if iOrderingFields.IsDisplayFirstOffer && jOrderingFields.IsDisplayFirstOffer {
					return iOrderingFields.DisplayFirstRank < jOrderingFields.DisplayFirstRank
				}
				return iOrderingFields.IsDisplayFirstOffer
			}

			// if any of the offer is the promoted offer,
			// then it should be displayed next
			if iOrderingFields.IsPromotedOffer || jOrderingFields.IsPromotedOffer {
				// if both offers promoted offer, then use promotedOfferRank for ordering amongst them.
				// lower promotedOfferRank offer should be shown first
				if iOrderingFields.IsPromotedOffer && jOrderingFields.IsPromotedOffer {
					return iOrderingFields.PromotedOfferRank < jOrderingFields.PromotedOfferRank
				}
				return iOrderingFields.IsPromotedOffer
			}

			// lower display rank gets higher precedence, i.e. ascending order
			if iOrderingFields.DisplayRank != jOrderingFields.DisplayRank {
				return iOrderingFields.DisplayRank < jOrderingFields.DisplayRank
			}

			// if display rank is same, order by creation date in descending order
			return iOrderingFields.CreatedAt.AsTime().After(jOrderingFields.CreatedAt.AsTime())
		}
	})

}

// dynamicOfferCatalogOrderingForDisplay orders the offers using attributes from fe and be entities
// This is a dynamic sorting function which is user sorted and ordered specific to users based on :
// eligibility and affordability
// Further sorting details can be found in : sortOfferCatalogOnTailoredCriteria
// nolint:funlen
func (r *RewardService) dynamicOfferCatalogOrderingForDisplay(ctx context.Context, offerCatalog []*fePb.CatalogOfferV1, beFiCoinOffers []*beCasperPb.Offer, beExchangerOffers []*beExchangerPb.ExchangerOffer, displayFirstOfferIds []string, sortBy fePb.SortBy, screen deeplinkPb.Screen,
	fiCoinsBalance uint32) {
	var (
		// Prioritized offers : promoted + display first offer count
		topPrioritizedOffersCount = 0
		// this is a potential affordability multiplier for any offer
		// The default value for this would be 1 (Default affordability),
		// Can be set to any multiplier to show a rather unaffordable offer
		// at priority. Ex : Salary eligible users to be shown salary eligible
		// offers at 1.2 * fiCoinsBalance as the base fi coins balance
		// This multiplier is calculated based on expected earnings for that
		// particular eligibility check
		offerPotentialAffordabilityMultiplier          = float32(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().OffersOrderingConfig().OfferPotentialAffordabilityMultiplier(ctx)) / float32(100)
		exchangerOfferPotentialAffordabilityMultiplier = float32(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().OffersOrderingConfig().ExchangerOfferPotentialAffordabilityMultiplier(ctx)) / float32(100)
	)

	// mapping display first offer ids to their ranks to prioritize them
	displayFirstOfferIdsToDisplayRank := make(map[string]int)
	for i, displayFirstOfferId := range displayFirstOfferIds {
		displayFirstOfferIdsToDisplayRank[displayFirstOfferId] = i
	}

	// offerId+offerType as "key" to prevent key collision in case of same offer-ids across casper
	offerIdToOrderingFieldsMap := make(map[string]*offerOrderingFields, len(beFiCoinOffers)+len(beExchangerOffers))

	// updating offerOrderFields for fi coin offers
	for _, beFiCoinOffer := range beFiCoinOffers {
		redemptionPrice := uint32(beFiCoinOffer.GetPrice())
		if beFiCoinOffer.GetDiscountDetails().GetDiscountedPrice() != 0 {
			redemptionPrice = uint32(beFiCoinOffer.GetDiscountDetails().GetDiscountedPrice())
		}

		displayRank, found := beFiCoinOffer.GetAdditionalDetails().GetScreenDisplayRankMap()[screen.String()]
		if !found {
			displayRank = beFiCoinOffer.GetAdditionalDetails().GetOfferDisplayRank()
		}
		offerOrderFields := &offerOrderingFields{
			DisplayRank:       displayRank,
			CreatedAt:         beFiCoinOffer.GetCreatedAt(),
			RedemptionPrice:   redemptionPrice,
			IsAffordableOffer: false,
		}

		if float32(fiCoinsBalance)*offerPotentialAffordabilityMultiplier >= float32(offerOrderFields.RedemptionPrice) {
			offerOrderFields.IsAffordableOffer = true
		}

		if displayFirstRank, ok := displayFirstOfferIdsToDisplayRank[beFiCoinOffer.GetId()]; ok {
			offerOrderFields.IsDisplayFirstOffer = true
			offerOrderFields.DisplayFirstRank = displayFirstRank
			topPrioritizedOffersCount++
		}
		offerIdToOrderingFieldsMap[beFiCoinOffer.GetId()+fiCoinOfferType] = offerOrderFields

		if beFiCoinOffer.GetAdditionalDetails().GetIsPromoOffer() {
			offerOrderFields.IsPromotedOffer = beFiCoinOffer.GetAdditionalDetails().GetIsPromoOffer()
			offerOrderFields.PromotedOfferRank = beFiCoinOffer.GetAdditionalDetails().GetPromoOfferDisplayRank()
			topPrioritizedOffersCount++
		}
	}

	// updating offerOrderFields for exchanger offers
	for _, beExchangerOffer := range beExchangerOffers {
		displayRank, found := beExchangerOffer.GetOfferDisplayDetails().GetScreenDisplayRankMap()[screen.String()]
		if !found {
			displayRank = beExchangerOffer.GetOfferDisplayDetails().GetDisplayRank()
		}
		offerOrderFields := &offerOrderingFields{
			IsCbrOffer:        true,
			DisplayRank:       displayRank,
			CreatedAt:         beExchangerOffer.GetCreatedAt(),
			RedemptionPrice:   uint32(beExchangerOffer.GetRedemptionPrice()),
			IsAffordableOffer: false,
		}

		if float32(fiCoinsBalance)*exchangerOfferPotentialAffordabilityMultiplier >= float32(offerOrderFields.RedemptionPrice) {
			offerOrderFields.IsAffordableOffer = true
		}

		if displayFirstRank, ok := displayFirstOfferIdsToDisplayRank[beExchangerOffer.GetId()]; ok {
			offerOrderFields.IsDisplayFirstOffer = true
			offerOrderFields.DisplayFirstRank = displayFirstRank
			topPrioritizedOffersCount++
		}
		offerIdToOrderingFieldsMap[beExchangerOffer.GetId()+exchangerOfferType] = offerOrderFields

		if beExchangerOffer.GetAdditionalDetails().GetIsPromoOffer() {
			offerOrderFields.IsPromotedOffer = beExchangerOffer.GetAdditionalDetails().GetIsPromoOffer()
			offerOrderFields.PromotedOfferRank = beExchangerOffer.GetAdditionalDetails().GetPromoOfferDisplayRank()
			topPrioritizedOffersCount++
		}
	}

	// updating some offerOrderFields from offerCatalog
	for _, catalogOffer := range offerCatalog {
		switch catalogOffer.GetOfferData().(type) {
		case *fePb.CatalogOfferV1_Offer:
			offer := catalogOffer.GetOffer()
			offerOrderFields := offerIdToOrderingFieldsMap[offer.GetId()+fiCoinOfferType]
			offerOrderFields.IsSelectiveEligibleOffer = offer.GetDisplayDetails().GetIsSelectiveEligible()
		case *fePb.CatalogOfferV1_ExchangerOffer:
			exchangerOffer := catalogOffer.GetExchangerOffer()
			offerOrderFields := offerIdToOrderingFieldsMap[exchangerOffer.GetId()+exchangerOfferType]
			offerOrderFields.IsSelectiveEligibleOffer = exchangerOffer.GetDisplayDetails().GetIsSelectiveEligible()
		default:
			// shouldn't happen
		}
	}

	sortOfferCatalogOnTailoredCriteria(offerCatalog, topPrioritizedOffersCount, offerIdToOrderingFieldsMap, sortBy)
}

// sortOfferCatalogOnTailoredCriteria : sorts the offer catalog dynamically using the following logic :
// * Display First Offers
// * Promoted Offers
// * topDisplayRankOffersCount Display Rank Offers (Sorted by rank,created_at_desc)
// * Eligible + Affordable
// * Eligible + Unaffordable
// * Ineligible + Affordable
// * Ineligible + Unaffordable
// The last 4 groups have random cbr/non-cbr alternation inside those groups
// Eligibility here does not include affordability as a factor
// Affordability uses a AffordabilityPotentialMultiplier for individual offer
// Effect : userFiCoinsBalance = userFiCoinsBalance * AffordabilityPotentialMultiplier
// Ref : https://monorail.pointz.in/p/fi-app/issues/detail?id=84640
// nolint:funlen
func sortOfferCatalogOnTailoredCriteria(
	offerCatalog []*fePb.CatalogOfferV1,
	topPrioritizedOffersCount int,
	offerIdToOrderingFieldsMap map[string]*offerOrderingFields,
	sortBy fePb.SortBy) {

	// if sorting is explicitly selected by user
	if sortBy != fePb.SortBy_UNSPECIFIED_SORT_BY {
		sort.Slice(offerCatalog, func(i, j int) bool {

			// fetching the ordering fields for an offer
			iOrderingFields := fetchOrderingFieldsFromCatalogOffer(offerCatalog[i], offerIdToOrderingFieldsMap)
			jOrderingFields := fetchOrderingFieldsFromCatalogOffer(offerCatalog[j], offerIdToOrderingFieldsMap)

			// sort based on different criteria depending on the value of `sortBy` parameter
			switch sortBy {
			case fePb.SortBy_REDEMPTION_PRICE_ASC:
				return iOrderingFields.RedemptionPrice < jOrderingFields.RedemptionPrice
			case fePb.SortBy_REDEMPTION_PRICE_DESC:
				return iOrderingFields.RedemptionPrice > jOrderingFields.RedemptionPrice
			default:
				// fallback to display rank logic
				// lower display rank gets higher precedence, i.e. ascending order
				if iOrderingFields.DisplayRank != jOrderingFields.DisplayRank {
					return iOrderingFields.DisplayRank < jOrderingFields.DisplayRank
				}

				// if display rank is same, order by creation date in descending order
				return iOrderingFields.CreatedAt.AsTime().After(jOrderingFields.CreatedAt.AsTime())
			}
		})
		// returning as no further sorting is required
		return
	}

	// fetching the finalTopOffersCount which will include :
	// * Display First Offers
	// * Promo Offers
	// * topDisplayRankOffersCount Display Rank Offers (Sorted by rank,created_at_desc)
	// NOTE : Do not change the initial handling so that user is always showed
	// 		  promoted, display first and topDisplayRankOffersCount offers at the top.
	finalTopOffersCount := topPrioritizedOffersCount + topDisplayRankOffersCount

	// sorting for the finalTopOffersCount
	sort.Slice(offerCatalog, func(i, j int) bool {

		// fetching the ordering fields for an offer
		iOrderingFields := fetchOrderingFieldsFromCatalogOffer(offerCatalog[i], offerIdToOrderingFieldsMap)
		jOrderingFields := fetchOrderingFieldsFromCatalogOffer(offerCatalog[j], offerIdToOrderingFieldsMap)

		// if any of the offer is the display first offer,
		// then it should be displayed first as compared to other offers.
		if iOrderingFields.IsDisplayFirstOffer || jOrderingFields.IsDisplayFirstOffer {
			// if both offers are display first offer, then use displayFirstRank for ordering amongst them.
			// lower displayFirstRank offer should be shown first
			if iOrderingFields.IsDisplayFirstOffer && jOrderingFields.IsDisplayFirstOffer {
				return iOrderingFields.DisplayFirstRank < jOrderingFields.DisplayFirstRank
			}
			return iOrderingFields.IsDisplayFirstOffer
		}

		// if any of the offer is the promoted offer,
		// then it should be displayed next
		if iOrderingFields.IsPromotedOffer || jOrderingFields.IsPromotedOffer {
			// if both offers promoted offer, then use promotedOfferRank for ordering amongst them.
			// lower promotedOfferRank offer should be shown first
			if iOrderingFields.IsPromotedOffer && jOrderingFields.IsPromotedOffer {
				return iOrderingFields.PromotedOfferRank < jOrderingFields.PromotedOfferRank
			}
			return iOrderingFields.IsPromotedOffer
		}

		// lower display rank gets higher precedence, i.e. ascending order
		if iOrderingFields.DisplayRank != jOrderingFields.DisplayRank {
			return iOrderingFields.DisplayRank < jOrderingFields.DisplayRank
		}

		// if display rank is same, order by creation date in descending order
		return iOrderingFields.CreatedAt.AsTime().After(jOrderingFields.CreatedAt.AsTime())
	})

	finalSortedTopOffers := lo.Slice(offerCatalog, 0, finalTopOffersCount)
	remainingOffers := lo.Slice(offerCatalog, finalTopOffersCount, len(offerCatalog))

	// fetching the updated remaining offers after apply the above custom sorting logic
	remainingOffers = groupAndSortRemainingOffers(remainingOffers, offerIdToOrderingFieldsMap)

	// doing this to avoid appendAssign which may cause memory errors
	finalSortedTopOffers = append(finalSortedTopOffers, remainingOffers...)
	offerCatalog = finalSortedTopOffers
}

// groupAndSortRemainingOffers sorts the remaining offers based on the following logic :
// First groups them into :
// * Eligible + Affordable
// * Eligible + Unaffordable
// * Ineligible + Affordable
// * Ineligible + Unaffordable
// Eligibility here does not include affordability as a factor
// Affordability uses a AffordabilityPotentialMultiplier for individual offer
// Effect : userFiCoinsBalance = userFiCoinsBalance * AffordabilityPotentialMultiplier
// inside each group we are sorting according to cbr/non-cbr alternate offers
func groupAndSortRemainingOffers(remainingOffers []*fePb.CatalogOfferV1, offerIdToOrderingFieldsMap map[string]*offerOrderingFields) []*fePb.CatalogOfferV1 {
	// randomly sorting every time to get a different order in cbr/non-cbr alternation
	sort.Slice(remainingOffers, func(i, j int) bool {
		return randomBool()
	})

	cbrEligibleAffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	nonCbrEligibleAffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	cbrEligibleUnaffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	nonCbrEligibleUnaffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	cbrIneligibleAffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	nonCbrIneligibleAffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	cbrIneligibleUnaffordableOffers := make([]*fePb.CatalogOfferV1, 0)
	nonCbrIneligibleUnaffordableOffers := make([]*fePb.CatalogOfferV1, 0)

	for _, offer := range remainingOffers {
		orderingFields := fetchOrderingFieldsFromCatalogOffer(offer, offerIdToOrderingFieldsMap)
		isAffordableOffer := orderingFields.IsAffordableOffer
		isEligibleOffer := orderingFields.IsSelectiveEligibleOffer
		isCbrOffer := orderingFields.IsCbrOffer

		switch {
		case isEligibleOffer && isAffordableOffer && isCbrOffer:
			cbrEligibleAffordableOffers = append(cbrEligibleAffordableOffers, offer)
		case isEligibleOffer && isAffordableOffer && !isCbrOffer:
			nonCbrEligibleAffordableOffers = append(nonCbrEligibleAffordableOffers, offer)
		case isEligibleOffer && !isAffordableOffer && isCbrOffer:
			cbrEligibleUnaffordableOffers = append(cbrEligibleUnaffordableOffers, offer)
		case isEligibleOffer && !isAffordableOffer && !isCbrOffer:
			nonCbrEligibleUnaffordableOffers = append(nonCbrEligibleUnaffordableOffers, offer)
		case !isEligibleOffer && isAffordableOffer && isCbrOffer:
			cbrIneligibleAffordableOffers = append(cbrIneligibleAffordableOffers, offer)
		case !isEligibleOffer && isAffordableOffer && !isCbrOffer:
			nonCbrIneligibleAffordableOffers = append(nonCbrIneligibleAffordableOffers, offer)
		case !isEligibleOffer && !isAffordableOffer && isCbrOffer:
			cbrIneligibleUnaffordableOffers = append(cbrIneligibleUnaffordableOffers, offer)
		case !isEligibleOffer && !isAffordableOffer && !isCbrOffer:
			nonCbrIneligibleUnaffordableOffers = append(nonCbrIneligibleUnaffordableOffers, offer)
		}
	}

	remainingOffers = make([]*fePb.CatalogOfferV1, 0, len(remainingOffers))
	remainingOffers = append(remainingOffers, alternateCBRNonCBROffers(cbrEligibleAffordableOffers, nonCbrEligibleAffordableOffers)...)
	remainingOffers = append(remainingOffers, alternateCBRNonCBROffers(cbrEligibleUnaffordableOffers, nonCbrEligibleUnaffordableOffers)...)
	remainingOffers = append(remainingOffers, alternateCBRNonCBROffers(cbrIneligibleAffordableOffers, nonCbrIneligibleAffordableOffers)...)
	remainingOffers = append(remainingOffers, alternateCBRNonCBROffers(cbrIneligibleUnaffordableOffers, nonCbrIneligibleUnaffordableOffers)...)

	return remainingOffers
}

// alternateCBRNonCBROffers alternates CBR and non-CBR offers inside each group
func alternateCBRNonCBROffers(cbrOffers, nonCbrOffers []*fePb.CatalogOfferV1) []*fePb.CatalogOfferV1 {
	mergedOffers := make([]*fePb.CatalogOfferV1, 0, len(cbrOffers)+len(nonCbrOffers))
	cbrOffersLen := len(cbrOffers)
	nonCbrOffersLen := len(nonCbrOffers)

	// Alternate between CBR and non-CBR offers
	for i := 0; i < min(cbrOffersLen, nonCbrOffersLen); i++ {
		mergedOffers = append(mergedOffers, cbrOffers[i], nonCbrOffers[i])
	}

	// Append remaining elements if any
	if cbrOffersLen < nonCbrOffersLen {
		mergedOffers = append(mergedOffers, nonCbrOffers[cbrOffersLen:]...)
	} else {
		mergedOffers = append(mergedOffers, cbrOffers[nonCbrOffersLen:]...)
	}

	return mergedOffers
}

func fetchOrderingFieldsFromCatalogOffer(catalogOffer *fePb.CatalogOfferV1, offerIdToOrderingFieldsMap map[string]*offerOrderingFields) *offerOrderingFields {
	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		return offerIdToOrderingFieldsMap[catalogOffer.GetOffer().GetId()+fiCoinOfferType]
	case *fePb.CatalogOfferV1_ExchangerOffer:
		return offerIdToOrderingFieldsMap[catalogOffer.GetExchangerOffer().GetId()+exchangerOfferType]
	default:
		// shouldn't happen
		return nil
	}
}

// shouldDisplayFiCoinOfferOnAppVersion checks whether the offer (non-CBR) is supported by the client or not
func (r *RewardService) shouldDisplayFiCoinOfferOnAppVersion(ctx context.Context, beOffer *beCasperPb.Offer, beOfferListing *beCasperPb.OfferListing, userLevelRemainingAttempts int32, globalLevelOfferInventory int32, appPlatform commontypes.Platform, appVersion uint32) (bool, error) {
	// salary exclusive offers filtering based on app-version
	if (beOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive() || beOffer.GetAdditionalDetails().GetSalaryAccountDisplayTag() != nil) && !r.checkIfAppSupportsSalaryAccountExclusiveOffer(appPlatform, appVersion) {
		return false, nil
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_POWER_UP {
		// skip power-up offers which aren't supported by a min version of android client
		if appPlatform == commontypes.Platform_ANDROID && appVersion < uint32(r.dyconf.RewardsFrontendMeta().MinAndroidVersionSupportingPowerUpRewards()) {
			return false, nil
		}

		// skip power-up offers for iOS where we provide dynamic data via offer-metadata till it's supported at the client
		if appPlatform == commontypes.Platform_IOS && len(beOffer.GetOfferMetadata().GetPowerUpOfferMetadata().GetExtraMetadata()) != 0 &&
			appVersion < r.dyconf.RewardsFrontendMeta().MinIosVersionSupportingDynamicDataForReedemedOffers() {
			return false, nil
		}
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_THRIWE_BENEFITS_PACKAGE {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingThriweBenefitsPackageOffers() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingThriweBenefitsPackageOffers() {
			return false, nil
		}
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_CMS_COUPON {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingDefaultOfferType() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingDefaultOfferType() {
			return false, nil
		}
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_EXTERNAL_VENDOR {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingExternalVendorOffer() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingExternalVendorOffer() {
			return false, nil
		}
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_LOUNGE_ACCESS {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingDefaultOfferType() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingDefaultOfferType() {
			return false, nil
		}
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS || beOffer.GetOfferType() == beCasperPb.OfferType_VISTARA_AIR_MILES {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingFiCoinsToPointsOfferType() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingFiCoinsToPointsOfferType() {
			return false, nil
		}
	}

	// if app version/platform combo doesn't support user/global level inventory exhausted offers
	if userLevelRemainingAttempts == 0 || globalLevelOfferInventory == 0 {
		if (appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingInventoryExhaustedOffer()) || (appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingInventoryExhaustedOffer()) {
			logger.Debug(ctx, "app version/platform combo doesn't support user/global level inventory exhausted offers, skipping offer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Uint32("appVersion", appVersion), zap.String("appPlatform", appPlatform.String()))
			return false, nil
		}
	}

	// parsing activeSince and displaySince to check coming soon case
	activeSince, err := time.Parse(time.RFC3339, beOfferListing.GetActiveSince())
	if err != nil {
		return false, errors.Wrap(err, "error parsing offer active since time string")
	}
	displaySince, err := time.Parse(time.RFC3339, beOfferListing.GetDisplaySince())
	if err != nil {
		return false, errors.Wrap(err, "error parsing offer display since time string")
	}

	// if app version/platform combo doesn't support coming soon offers
	// checking for min(MinAppVersionSupportingComingSoonOffer, MinAppVersionSupportingUnredeemableOfferLabel) as coming soon offers are supported after both app version
	if displaySince.Before(time.Now()) && time.Now().Before(activeSince) {
		if (appPlatform == commontypes.Platform_ANDROID && appVersion < min(r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingComingSoonOffer(), r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingUnredeemableOfferLabel())) ||
			(appPlatform == commontypes.Platform_IOS && appVersion < min(r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingComingSoonOffer(), r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingUnredeemableOfferLabel())) {
			logger.Info(ctx, "app version/platform combo doesn't support coming soon offers, skipping offer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Uint32("appVersion", appVersion), zap.String("appPlatform", appPlatform.String()))
			return false, nil
		}
	}

	return true, nil
}

func (r *RewardService) isExchangerOfferSupportedOnAppVersion(ctx context.Context, exchangerOfferListing *beExchangerPb.ExchangerOfferListing, exchangerOffer *beExchangerPb.ExchangerOffer, feRequestHeader *feHeaderPb.RequestHeader) (bool, error) {
	appPlatform, appVersion := feRequestHeader.GetAuth().GetDevice().GetPlatform(), int(feRequestHeader.GetAppVersionCode())

	for _, optionsConfig := range exchangerOffer.GetOfferOptionsConfig().GetOptionsConfig() {
		for _, rewardConfigUnit := range optionsConfig.GetRewardConfigUnits() {
			rewardType := rewardConfigUnit.GetRewardType()
			switch rewardType {
			case beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE, beExchangerPb.RewardType_REWARD_TYPE_EGV:
				if appPlatform == commontypes.Platform_ANDROID && appVersion < r.rewardsFrontendMeta.MinAndroidAppVersionSupportingCBRV2 ||
					appPlatform == commontypes.Platform_IOS && appVersion < r.rewardsFrontendMeta.MinIosAppVersionSupportingCBRV2 {
					return false, nil
				}
			default:
				continue
			}
		}
	}

	// salary exclusive offers filtering based on app-version
	if exchangerOffer.GetOfferDisplayDetails().GetSalaryAccountTag() != nil && !r.checkIfAppSupportsSalaryAccountExclusiveOffer(appPlatform, uint32(appVersion)) {
		return false, nil
	}

	// currently GetExchangerOfferById flow does not support exchangerOfferListing, so skipping this check
	if exchangerOfferListing != nil {
		// parsing activeSince and displaySince to check coming soon case
		activeSince, err := time.Parse(time.RFC3339, exchangerOfferListing.GetActiveSince())
		if err != nil {
			return false, errors.Wrap(err, "error parsing offer active since time string")
		}
		displaySince, err := time.Parse(time.RFC3339, exchangerOfferListing.GetDisplaySince())
		if err != nil {
			return false, errors.Wrap(err, "error parsing offer display since time string")
		}

		// if app version/platform combo doesn't support coming soon offers
		// checking for min(MinAppVersionSupportingComingSoonOffer, MinAppVersionSupportingUnredeemableOfferLabel)
		if displaySince.Before(time.Now()) && time.Now().Before(activeSince) {
			if (appPlatform == commontypes.Platform_ANDROID && uint32(appVersion) < min(r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingComingSoonOffer(), r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingUnredeemableOfferLabel())) ||
				(appPlatform == commontypes.Platform_IOS && uint32(appVersion) < min(r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingComingSoonOffer(), r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingUnredeemableOfferLabel())) {
				logger.Info(ctx, "app version/platform combo doesn't support coming soon offers, skipping offer", zap.String(logger.OFFER_ID, exchangerOffer.GetId()), zap.Uint32("appVersion", uint32(appVersion)), zap.String("appPlatform", appPlatform.String()))
				return false, nil
			}
		}
	}

	return true, nil
}

// checkIfAppSupportsSalaryAccountExclusiveOffer checks whether the salary-exclusive offer (CBR, non-CBR) is supported by client or not
func (r *RewardService) checkIfAppSupportsSalaryAccountExclusiveOffer(appPlatform commontypes.Platform, appVersion uint32) bool {
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		if appVersion < uint32(r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingSalaryExclusiveOffer()) {
			return false
		}
	case commontypes.Platform_IOS:
		if appVersion < uint32(r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingSalaryExclusiveOffer()) {
			return false
		}
	default:
		// do nothing
	}

	return true
}

func (r *RewardService) GetExchangerOfferById(ctx context.Context, req *fePb.GetExchangerOfferByIdRequest) (*fePb.GetExchangerOfferByIdResponse, error) {
	var actorId = req.GetReq().GetAuth().GetActorId()

	beExchangerOfferRes, err := r.exchangerOfferClient.GetExchangerOffersByIds(ctx, &beExchangerPb.GetExchangerOffersByIdsRequest{
		Ids: []string{req.GetOfferId()},
		Filters: &beExchangerPb.GetExchangerOffersByIdsRequest_Filters{
			// hard check on active status to add guard rails and prevent landing on the offer if it's not active
			Status: beExchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE,
		},
	})

	if err != nil || !beExchangerOfferRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching exchanger offer by id", zap.Any(logger.RPC_STATUS, beExchangerOfferRes.GetStatus()),
			zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(err),
		)
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching exchanger offer")}, nil
	}
	if len(beExchangerOfferRes.GetExchangerOffers()) == 0 {
		logger.Error(ctx, "exchanger offer not found", zap.String(logger.OFFER_ID, req.GetOfferId()))
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	beExchangerOffer := beExchangerOfferRes.GetExchangerOffers()[0]

	// todo(rohanchougule): move the check to validate the operability of the offer in a separate method
	currentDayStartTime := datetime.GetTimeAtStartOfTheDay(time.Now().In(datetime.IST))
	attemptsCountForOfferRes, err := r.exchangerOfferClient.GetExchangerOffersActorAttemptsCount(ctx, &beExchangerPb.GetExchangerOffersActorAttemptsCountRequest{
		ActorId:           actorId,
		ExchangerOfferIds: []string{req.GetOfferId()},
		// start time of today's date in IST
		FromAttemptedTime: timestamppb.New(currentDayStartTime),
		// start time of tomorrow's date in IST
		ToAttemptedTime: timestamppb.New(currentDayStartTime.AddDate(0, 0, 1)),
	})
	if err != nil || !attemptsCountForOfferRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching attempts count of offer for actor", zap.Error(err), zap.String(logger.OFFER_ID, req.GetOfferId()),
			zap.Any(logger.RPC_STATUS, attemptsCountForOfferRes.GetStatus()),
		)
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching exchanger offer attempts count")}, nil
	}

	// retrieve the current fi-coins balance to decide the whether offer is operable or not
	fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error fetching fi-coins balance in GetExchangerOfferById", zap.Error(err))
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching fi-coins balance for the actor")}, nil
	}

	offerIdToMaxCapHitMap, err := r.isEORewardUnitsMaxCapReached(ctx, actorId, beExchangerOfferRes.GetExchangerOffers())
	if err != nil {
		logger.Error(ctx, "error checking if max-cap for reward-units is hit by an actor for exchanger-offer",
			zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(err),
		)
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error checking if max-cap for reward-units is hit by an actor for exchanger-offer")}, nil
	}

	// check salary program status for the actor
	var (
		isSalaryAccountExclusiveOffer = false
		isUserFullSalaryProgramActive = false
	)

	if beExchangerOffer.GetOfferDisplayDetails().GetSalaryAccountTag() != nil ||
		beExchangerOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive() {
		isSalaryAccountExclusiveOffer = true
	}

	if isSalaryAccountExclusiveOffer {
		// fetch salary program details
		isUserFullSalaryProgramActive, err = r.IsUserFullSalaryProgramActive(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error checking if salary program is active for user or not", zap.Error(err))
			return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error checking if salary program is active for actor or not")}, nil
		}
	}

	// todo(sresth) create a rpc to get exchangerOfferListing and pass that below
	isExchangerOfferSupportedOnAppVersion, err := r.isExchangerOfferSupportedOnAppVersion(ctx, nil, beExchangerOffer, req.GetReq())
	// bypassing the error as one corrupted offer should not break the flow
	if err != nil {
		logger.Error(ctx, "isExchangerOfferSupportedOnAppVersion call failed in getBeExchangerOffers ", zap.String("offerId:", beExchangerOffer.GetId()), zap.Error(err))
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("isExchangerOfferSupportedOnAppVersion call failed in getBeExchangerOffers, offerId: %s", beExchangerOfferRes.GetExchangerOffers()[0].GetId()))}, nil
	}
	if !isExchangerOfferSupportedOnAppVersion {
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("offerId not supported on the current app version, offerId: %s", beExchangerOfferRes.GetExchangerOffers()[0].GetId()))}, nil
	}

	// get expression function map for display filtering
	displayExpressionFunctionMap := r.offerDisplayEngine.GetExpressionFunctionMap(ctx, actorId, nil, beExchangerOfferRes.GetExchangerOffers()[:1])

	isMonthlyCapHit := false
	if beExchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap() != 0 {
		redemptionCountsForActorOfferIdsInMonthRes, redemptionCountsErr := r.exchangerOfferClient.GetRedemptionCountsForActorOfferIdsInMonth(ctx, &beExchangerPb.GetRedemptionCountsForActorOfferIdsInMonthRequest{
			ActorId:        actorId,
			OfferIds:       []string{beExchangerOffer.GetId()},
			MonthTimestamp: timestamppb.Now(),
		})
		// if RPC returns a NotFound status, we don't need to do anything
		if (!redemptionCountsForActorOfferIdsInMonthRes.GetStatus().IsSuccess() && !redemptionCountsForActorOfferIdsInMonthRes.GetStatus().IsRecordNotFound()) || redemptionCountsErr != nil {
			logger.Error(ctx, "error while fetching monthly redemption count for actor/offer", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.OFFER_ID, beExchangerOffer.GetId()), zap.Time("monthTimestamp", time.Now()))
			return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error while fetching monthly redemption count for actor/offer")}, nil
		}
		if redemptionCountsForActorOfferIdsInMonthRes.GetStatus().IsSuccess() {
			if redemptionsCount, ok := redemptionCountsForActorOfferIdsInMonthRes.GetOfferIdToRedemptionsCountInMonthMap()[beExchangerOffer.GetId()]; ok {
				isMonthlyCapHit = beExchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap()-redemptionsCount == 0
			}
		}
	}

	feExchangerOffer, err := r.convertToFeExchangerOffer(ctx, beExchangerOfferRes.GetExchangerOffers()[0], nil,
		attemptsCountForOfferRes.GetOfferIdToAttemptsCountMap()[req.GetOfferId()], fiCoinsBalance,
		offerIdToMaxCapHitMap[req.GetOfferId()], isUserFullSalaryProgramActive, displayExpressionFunctionMap, tags.RenderLocationFiCoinOffersCatalogCard,
		isMonthlyCapHit,
	)
	if err != nil {
		logger.Error(ctx, "error converting be offer to fe offer", zap.Error(err), zap.String(logger.OFFER_ID, req.GetOfferId()))
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg("error converting be offer to fe offer")}, nil
	}
	if feExchangerOffer == nil {
		logger.Info(ctx, "exchanger offer is not eligible for user", zap.String(logger.OFFER_ID, req.GetOfferId()))
		return &fePb.GetExchangerOfferByIdResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	return &fePb.GetExchangerOfferByIdResponse{
		Status:         rpc.StatusOk(),
		ExchangerOffer: feExchangerOffer,
		FiCoinsBalance: fiCoinsBalance,
	}, nil
}

func (r *RewardService) GetCatalogOffersAndFilters(ctx context.Context, req *fePb.GetCatalogOffersAndFiltersRequest) (*fePb.GetCatalogOffersAndFiltersResponse, error) {
	var shouldFilterOnFe bool
	if len(req.GetFilters().GetTags()) > 0 {
		shouldFilterOnFe = true
	}

	offerCatalog, additionalDetails, err := r.getOrderedOffersAndExchangerOffers(
		ctx,
		req.GetReq(),
		req.GetDisplayFirstOfferIds(),
		&tags.Config{
			// the tag render location to fetch the RenderLocationToFontStyleMap and RenderLocationToTagDisplayPropertiesMap for where we want to render the tags
			TagRenderLocation: tags.RenderLocationFiCoinOffersCatalogCard,
			// the extract tags is set to true to extract all the unique tags from all the offers
			ExtractTags: true,
		},
		r.convertFeFiltersToBeFilters(req.GetFilters()),
		// **NOTE**: we're doing a frontend layer filtering after fetching all the
		// active offers from BE because we want to generate all possible filters that
		// can be applied to the unfiltered list of offers. that list is generated via
		// the tags applied to offers. if we will fetch filtered offers from BE we will
		// only receive offers having the tag based on which we're filtering, and won't
		// be able to generate all filters list.
		shouldFilterOnFe,
		fePb.SortBy_UNSPECIFIED_SORT_BY,
		deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
	)
	if err != nil {
		logger.Error(ctx, "error fetching fe and be offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		return &fePb.GetCatalogOffersAndFiltersResponse{RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching fe and be offers")}}, nil
	}

	// fetching all tags list from the offers based on tag names and category tag names in the offers
	allTagsList := fetchUniqueFiltersTagList(additionalDetails.GetTagNames(), additionalDetails.GetCategoryTagNames())

	// generating list of all filters
	allTagDetails, allTagListOrderedByPriority, err := r.tagsManager.GetGenericTagsDetailsOrderedByPriority(ctx, allTagsList, tags.RenderLocationFiCoinOffersAllFilters)
	if err != nil {
		logger.Error(ctx, "failed to get filters list.", zap.Any("tagsList", additionalDetails.GetTagNames()), zap.Error(err))
		return &fePb.GetCatalogOffersAndFiltersResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to get filters list.")},
		}, nil
	}

	var allTagFilters []*fePb.CatalogTagFilter
	for i, feTagDetails := range allTagDetails {
		allTagFilters = append(allTagFilters, &fePb.CatalogTagFilter{
			TagName:           allTagListOrderedByPriority[i],
			InactiveFilterCta: feTagDetails,
			ActiveFilterCta:   r.tagsManager.GetActiveCatalogTagFilterDetails(feTagDetails),
		})
	}

	maxPromotedFiltersForTopBar := r.dyconf.RewardsFrontendMeta().CatalogFiltersConfig().MaxPromotedFiltersForTopBar()
	if int32(len(allTagListOrderedByPriority)) < maxPromotedFiltersForTopBar {
		maxPromotedFiltersForTopBar = int32(len(allTagListOrderedByPriority))
	}

	// generating list of prioritised filters
	priorityTagsDetails, priorityTagListOrderedByPriority, err := r.tagsManager.GetGenericTagsDetailsOrderedByPriority(ctx, allTagListOrderedByPriority[:maxPromotedFiltersForTopBar], tags.RenderLocationFiCoinOffersFiltersList)
	if err != nil {
		logger.Error(ctx, "failed to get filters list.", zap.Any("tagsList", allTagListOrderedByPriority[:maxPromotedFiltersForTopBar]), zap.Error(err))
		return &fePb.GetCatalogOffersAndFiltersResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to get filters list.")},
		}, nil
	}

	// top filters bar
	var widgets []*fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget

	widgets = append(
		widgets,
		// add "All Filters" button to filters bar
		&fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget{
			Widget: &fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget_AllFiltersWidget{
				AllFiltersWidget: &fePb.GetCatalogOffersAndFiltersResponse_AllFiltersWidget{
					Cta: &ui.IconTextComponent{
						Texts:     []*commontypes.Text{{DisplayValue: &commontypes.Text_PlainString{PlainString: "All filters"}}},
						LeftIcon:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/catalog_filters_all_filters_icon.png"},
						RightIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png"},
					},
					TagFilters: allTagFilters,
				},
			},
		},
	)

	for i, priorityTagDetail := range priorityTagsDetails {
		widgets = append(widgets, &fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget{
			Widget: &fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget_TagFilterWidget{
				TagFilterWidget: &fePb.CatalogTagFilter{
					TagName:           priorityTagListOrderedByPriority[i],
					InactiveFilterCta: priorityTagDetail,
					ActiveFilterCta:   r.tagsManager.GetActiveCatalogTagFilterDetails(priorityTagDetail),
				},
			},
		})
	}

	widgets = append(
		widgets,
		// add "Sort By" button to filters bar
		&fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget{
			Widget: &fePb.GetCatalogOffersAndFiltersResponse_TopBarWidget_SortByWidget{
				SortByWidget: &fePb.GetCatalogOffersAndFiltersResponse_SortByWidget{
					Cta: &ui.IconTextComponent{
						Texts:     []*commontypes.Text{{DisplayValue: &commontypes.Text_PlainString{PlainString: "Sort By"}}},
						RightIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png"},
					},
					SortOptions: r.getCatalogSortOptions(),
				},
			},
		},
	)

	return &fePb.GetCatalogOffersAndFiltersResponse{
		TopBar: &fePb.GetCatalogOffersAndFiltersResponse_TopBar{
			ClearFiltersCta: &ui.IconTextComponent{
				LeftIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/clear_filters_icon.png"},
			},
			Widgets: widgets,
		},
		MaxPromotedFiltersForTopBar: uint32(maxPromotedFiltersForTopBar),
		Offers:                      offerCatalog,
		RespHeader:                  &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

// GetOffersCatalogPage returns the SDUI based offers catalog page
func (r *RewardService) GetOffersCatalogPage(ctx context.Context, req *fePb.GetOffersCatalogPageRequest) (*fePb.GetOffersCatalogPageResponse, error) {
	var (
		err           error
		finalResponse = &fePb.GetOffersCatalogPageResponse{
			PageDetails: &fePb.GetOffersCatalogPageResponse_PageDetails{},
		}
		responseWithStatus = func(rpcStatus *rpc.Status, errorView *feErrors.ErrorView) (*fePb.GetOffersCatalogPageResponse, error) {
			finalResponse.RespHeader = feHeaderPb.NewResponseHeader(rpcStatus, errorView, "")
			return finalResponse, nil
		}
		isFeatureRewardsCatalogMergedPageEnabled = featureflags.IsFeatureRewardsCatalogMergedPageEnabled(ctx, &featureflags.IsFeatureRewardsCatalogMergedPageEnabledRequest{
			ActorId: req.GetReq().GetAuth().GetActorId(),
			ExternalDeps: &common.ExternalDependencies{
				Evaluator: r.releaseEvaluator,
			},
		})
	)

	// set the top bar details
	finalResponse.GetPageDetails().TopBarDetails = r.fetchTopBarDetails(isFeatureRewardsCatalogMergedPageEnabled)

	// set the different page sections and additional details of the page
	finalResponse.GetPageDetails().PageSections, finalResponse.GetPageDetails().AdditionalDetails, err = r.fetchPageSectionsAndAdditionalDetails(ctx, req, isFeatureRewardsCatalogMergedPageEnabled)
	if err != nil {
		logger.Error(ctx, "error fetching page sections", zap.Error(err))
		return responseWithStatus(rpc.StatusInternal(), nil)
	}

	// set the page ui details
	finalResponse.GetPageDetails().PageUiDetails = r.fetchPageUiDetails(isFeatureRewardsCatalogMergedPageEnabled)

	return responseWithStatus(rpc.StatusOk(), nil)
}

// fetchTopBarDetails fetches the top bar details for the offers catalog page
func (r *RewardService) fetchTopBarDetails(isFeatureRewardsCatalogMergedPageEnabled bool) *fePb.GetOffersCatalogPageResponse_TopBarDetails {
	var (
		bgColour = r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().TopBarDetailsConfig().BackgroundColour()
		helpCta  = &fePb.CtaV1{
			LeftIcon: commontypes.GetVisualElementImageFromUrl(questionMarkIconUrl).WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  22,
				Height: 22,
			}),
			Action: &fePb.CtaV1_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HELP_MAIN,
				},
			},
		}
		rewardsTnCCta = &fePb.CtaV1{
			LeftIcon: commontypes.GetVisualElementImageFromUrl(tncIconUrl).WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  22,
				Height: 22,
			}),
			Action: &fePb.CtaV1_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_WEB_PAGE,
					ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
						WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
							WebpageTitle: rewardsTnCWebpageTitle,
							WebpageUrl:   rewardsTnCWebpageUrl,
						},
					},
				},
			},
		}
		ctas = []*fePb.CtaV1{helpCta}
	)
	if isFeatureRewardsCatalogMergedPageEnabled {
		bgColour = colorGunmetal
		ctas = append(ctas, rewardsTnCCta)
	}
	return &fePb.GetOffersCatalogPageResponse_TopBarDetails{
		Title: commontypes.GetTextFromStringFontColourFontStyle(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().TopBarDetailsConfig().Title(), colorFiSnow, commontypes.FontStyle_HEADLINE_M).
			WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
		BackgroundColor: widget.GetBlockBackgroundColour(bgColour),
		Ctas:            ctas,
	}
}

// sectionWithRank is a helper struct to keep track of the section and its rank
type sectionWithRank struct {
	section *fePb.GetOffersCatalogPageResponse_Section
	rank    int
}

// fetchPageSectionsAndAdditionalDetails fetches the page sections and additional details for the offers catalog page
func (r *RewardService) fetchPageSectionsAndAdditionalDetails(ctx context.Context, req *fePb.GetOffersCatalogPageRequest, isFeatureRewardsCatalogMergedPageEnabled bool) ([]*fePb.GetOffersCatalogPageResponse_Section, *fePb.GetOffersCatalogPageResponse_AdditionalDetails, error) {
	var (
		// we are fetching the full page if no filters are passed
		sectionTypesToFetch                                                                           = req.GetFilters().GetSectionTypes()
		shouldFetchFullPage                                                                           = lo.Contains(sectionTypesToFetch, fePb.GetOffersCatalogPageRequest_SECTION_TYPE_UNSPECIFIED)
		sectionFetchConfig                                                                            = make(map[fePb.GetOffersCatalogPageRequest_SectionType]bool)
		shouldFetchAdditionalDetails                                                                  = len(req.GetFilters().GetOffersSectionSpecificFilters().GetDisplayFirstOfferIds()) > 0
		myEarningsSection, bannersSection, offerFiltersSection, offersSection, unopenedRewardsSection *fePb.GetOffersCatalogPageResponse_Section
		finalPageSections                                                                             []*fePb.GetOffersCatalogPageResponse_Section
		pageAdditionalDetails                                                                         *fePb.GetOffersCatalogPageResponse_AdditionalDetails
		err                                                                                           error
	)

	// setting the section fetch config
	for sectionTypeEnumKey := range fePb.GetOffersCatalogPageRequest_SectionType_name {
		var (
			sectionType = fePb.GetOffersCatalogPageRequest_SectionType(sectionTypeEnumKey)
			// we set shouldFetchSection to true if the section type is in the sectionTypesToFetch or if we are fetching the full page
			shouldFetchSection = shouldFetchFullPage || lo.Contains(sectionTypesToFetch, sectionType)
		)

		switch sectionType {
		// unopened rewards section is only fetched if the feature merged catalog page is enabled
		case fePb.GetOffersCatalogPageRequest_SECTION_TYPE_UNOPENED_REWARDS:
			shouldFetchSection = shouldFetchSection && isFeatureRewardsCatalogMergedPageEnabled
		case fePb.GetOffersCatalogPageRequest_SECTION_TYPE_UNSPECIFIED:
			continue
		}

		sectionFetchConfig[sectionType] = shouldFetchSection
	}

	// fetch all sections concurrently
	sectionsErrorGroup, gctx := errgroup.WithContext(ctx)
	sectionsResultChannel := make(chan *sectionWithRank, 5)

	if sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_MY_EARNINGS] {
		sectionsErrorGroup.Go(func() error {
			myEarningsSection, err = r.fetchMyEarningsSection(gctx, req, isFeatureRewardsCatalogMergedPageEnabled)
			if err != nil {
				logger.Error(ctx, "error fetching my earnings section", zap.Error(err))
				return errors.Wrap(err, "error fetching my earnings section")
			}
			sectionsResultChannel <- &sectionWithRank{section: myEarningsSection, rank: 1}
			return nil
		})
	}

	if sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_UNOPENED_REWARDS] {
		sectionsErrorGroup.Go(func() error {
			unopenedRewardsSection, err = r.fetchUnopenedRewardsSection(gctx, req)
			if err != nil {
				logger.Error(ctx, "error fetching unopened rewards section", zap.Error(err))
				return errors.Wrap(err, "error fetching unopened rewards section")
			}
			sectionsResultChannel <- &sectionWithRank{section: unopenedRewardsSection, rank: 2}
			return nil
		})
	}

	// fetch offer filters and offers section
	// putting this in the same place due to the interdependence of the these sections
	if sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_BANNERS] || sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_OFFER_FILTERS] || sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_OFFERS] {
		var (
			catalogOffers []*fePb.CatalogOfferV1
			// these are the filters applied by the user
			beCatalogFilters = r.convertFeFiltersToBeFilters(&fePb.CatalogFilters{
				Tags: req.GetFilters().GetOffersSectionSpecificFilters().GetTagNames(),
			})
			// this is the unique combination of tags and category tags present in all the offers
			uniqueFiltersTagList           []string
			catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails
			firstOfferCategoryTag          string
		)

		sectionsErrorGroup.Go(func() error {
			catalogOffers, catalogOffersAdditionalDetails, err = r.getOrderedOffersAndExchangerOffers(
				ctx,
				req.GetReq(),
				req.GetFilters().GetOffersSectionSpecificFilters().GetDisplayFirstOfferIds(),
				&tags.Config{
					TagRenderLocation: tags.RenderLocationOffersCatalogPageCatalogCard,
					// setting this true as we want all the tags to be extracted from the offers everytime to show them as sections
					ExtractTags: true,
				},
				// offers will be filtered based on the tags and category tags applied by the user
				beCatalogFilters,
				shouldFetchFullPage,
				fePb.SortBy_UNSPECIFIED_SORT_BY,
				deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
			)
			if err != nil {
				logger.Error(ctx, "error fetching offers and exchanger offers", zap.Error(err))
				return errors.Wrap(err, "error fetching offers and exchanger offers")
			}

			if shouldFetchAdditionalDetails {
				// fetching display first offer's view offer details deeplink
				displayFirstOfferViewOfferDetailsDeeplink := r.getDisplayFirstOfferViewOfferDetailsDeeplink(ctx, catalogOffers[0], catalogOffersAdditionalDetails, req.GetFilters().GetOffersSectionSpecificFilters().GetDisplayFirstOfferIds())
				firstOfferCategoryTag = catalogOffers[0].GetCategoryTag()

				// setting the page additional details
				pageAdditionalDetails = &fePb.GetOffersCatalogPageResponse_AdditionalDetails{
					DisplayFirstOfferViewOfferDetailsDeeplink: displayFirstOfferViewOfferDetailsDeeplink,
				}
			}

			if sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_BANNERS] && apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().BannersSection().FeatureConfig()) {
				bannersSection = r.fetchBannersSection(ctx, catalogOffersAdditionalDetails, isFeatureRewardsCatalogMergedPageEnabled)
				sectionsResultChannel <- &sectionWithRank{section: bannersSection, rank: 3}
			}

			// fetch all the unique tags and category tags to be used in offer filters and offers sections
			uniqueFiltersTagList = fetchUniqueFiltersTagList(catalogOffersAdditionalDetails.GetTagNames(), catalogOffersAdditionalDetails.GetCategoryTagNames())
			// adding allOffersFilterText tag to the list of unique tags
			uniqueFiltersTagList = append(uniqueFiltersTagList, allOffersFilterText)

			if sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_OFFER_FILTERS] {
				offerFiltersSection, err = r.fetchOfferFiltersSection(ctx, uniqueFiltersTagList, firstOfferCategoryTag, isFeatureRewardsCatalogMergedPageEnabled)
				if err != nil {
					logger.Error(ctx, "error fetching offer filters section", zap.Error(err))
					return errors.Wrap(err, "error fetching offer filters section")
				}
				sectionsResultChannel <- &sectionWithRank{section: offerFiltersSection, rank: 4}
			}

			if sectionFetchConfig[fePb.GetOffersCatalogPageRequest_SECTION_TYPE_OFFERS] {
				offersSection = r.fetchOffersSection(ctx, req, catalogOffers, beCatalogFilters, uniqueFiltersTagList, catalogOffersAdditionalDetails, firstOfferCategoryTag, isFeatureRewardsCatalogMergedPageEnabled)
				sectionsResultChannel <- &sectionWithRank{section: offersSection, rank: 5}
			}

			return nil
		})
	}

	// wait for all sections to be fetched
	err = sectionsErrorGroup.Wait()
	if err != nil {
		logger.Error(ctx, "error fetching sections", zap.Error(err))
		return nil, nil, err
	}

	close(sectionsResultChannel)

	// sort the sections based on the rank
	finalPageSections = fetchSectionsSortedByRank(sectionsResultChannel)

	return finalPageSections, pageAdditionalDetails, nil
}

// getUserSpecificMetadata fetches the user specific metadata for the CatalogOffersAdditionalDetails
func (r *RewardService) getUserSpecificMetadata(ctx context.Context, actorId string, screen deeplinkPb.Screen) (*feRewardsPkg.UserSpecificMetadata, error) {
	var isLoanDefaultUser bool

	// IsLoanDefaultUser is a user specific metadata that is used to show the loan default reminder bottom sheet
	// currently we are only updating this :
	// * for new offers catalog screen
	// * if the loan default reminder bottom sheet is not disabled for the new offers catalog screen
	if screen == deeplinkPb.Screen_OFFERS_LANDING_SCREEN &&
		feRewardsPkg.IsOffersCatalogPageV2Enabled(ctx, r.dyconf) &&
		apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().OffersSection().LoanDefaultReminderBottomSheetFeatureConfig()) {
		// check is user is a loan default user
		loanUserStatusResp, err := r.palClient.GetLoansUserStatus(ctx, &palPb.GetLoansUserStatusRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(loanUserStatusResp, err); rpcErr != nil {
			logger.Error(ctx, "error calling GetLoansUserStatus rpc", zap.Error(err))
			return nil, rpcErr
		}
		isLoanDefaultUser = loanUserStatusResp.GetIsLoanOverdue()
	}

	// return the user specific metadata with the required fields.
	return &feRewardsPkg.UserSpecificMetadata{
		IsLoanDefaultUser: isLoanDefaultUser,
	}, nil
}

// fetchSectionsSortedByRank sorts the sections based on the rank and returns the final sections
func fetchSectionsSortedByRank(sectionWithRankChannel chan *sectionWithRank) []*fePb.GetOffersCatalogPageResponse_Section {
	var sectionsWithRank []*sectionWithRank
	for singleSectionWithRank := range sectionWithRankChannel {
		sectionsWithRank = append(sectionsWithRank, singleSectionWithRank)
	}

	sort.Slice(sectionsWithRank, func(i, j int) bool {
		return sectionsWithRank[i].rank < sectionsWithRank[j].rank
	})

	var finalSections []*fePb.GetOffersCatalogPageResponse_Section
	for _, singleSectionWithRank := range sectionsWithRank {
		if singleSectionWithRank.section == nil {
			continue
		}

		finalSections = append(finalSections, singleSectionWithRank.section)
	}
	return finalSections
}

// fetchMyEarningsSection : fetches the "my earnings" section of the page containing the collected offers and fi coins balance
func (r *RewardService) fetchMyEarningsSection(ctx context.Context, req *fePb.GetOffersCatalogPageRequest, isFeatureRewardsCatalogMergedPageEnabled bool) (*fePb.GetOffersCatalogPageResponse_Section, error) {
	if isFeatureRewardsCatalogMergedPageEnabled {
		return r.fetchMyEarningsSectionV2(ctx, req)
	}

	var (
		actorId                     = req.GetReq().GetAuth().GetActorId()
		collectedOffersPageDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
		}
		rewardsScreenDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
		}
	)

	// fetch user fi coin balance
	fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error fetching fi coins balance for my earnings section", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	// setting the collected offers icon text component
	collectedOffersComponent := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(collectedOffersCtaText, colorFiSnow, commontypes.FontStyle_BUTTON_3).WithAlignment(commontypes.Text_ALIGNMENT_LEFT),
		},
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(collectedOffersTagUrl, 16, 16).
			WithImageType(commontypes.ImageType_PNG).
			WithImagePadding(4, 4, 4, 4),
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronRightWithCircleBgGray, 16, 16).
			WithImageType(commontypes.ImageType_PNG).
			WithImagePadding(4, 4, 4, 4),
		LeftImgTxtPadding:  6,
		RightImgTxtPadding: 6,
	}

	// setting the fi coin balance icon text component
	fiCoinBalanceComponent := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(fiCoinsBalance), 0), colorFiSnow, commontypes.FontStyle_NUMBER_L),
		},
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsIconUrl, 28, 28).
			WithImageType(commontypes.ImageType_PNG),
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronRightWithCircleBgGray, 16, 16).
			WithImageType(commontypes.ImageType_PNG).
			WithImagePadding(4, 4, 4, 4),
		LeftImgTxtPadding:  6,
		RightImgTxtPadding: 6,
	}

	return &fePb.GetOffersCatalogPageResponse_Section{
		BackgroundColor: widget.GetBlockBackgroundColour(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().MyEarningsSection().BackgroundColour()),
		Section: &fePb.GetOffersCatalogPageResponse_Section_MyEarningsSection{
			MyEarningsSection: &fePb.GetOffersCatalogPageResponse_MyEarningsSection{
				Section: &sections.Section{
					Content: &sections.Section_HorizontalListSection{
						HorizontalListSection: &sections.HorizontalListSection{
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: properties.GetContainerProperty().
											WithMargin(16, 20, 16, 20).
											WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
									}},
							},
							Components: []*components.Component{
								{
									Content: GetAnyWithoutError(collectedOffersComponent),
									InteractionBehaviors: []*behaviors.InteractionBehavior{
										{
											Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
												OnClickBehavior: &behaviors.OnClickBehavior{
													Action: GetAnyWithoutError(collectedOffersPageDeeplink),
												},
											},
											AnalyticsEvent: &analytics.AnalyticsEvent{
												EventName: "ClickedCollectedOffersIcon",
											},
										},
									},
								},
								{
									Content: GetAnyWithoutError(fiCoinBalanceComponent),
									InteractionBehaviors: []*behaviors.InteractionBehavior{
										{
											Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
												OnClickBehavior: &behaviors.OnClickBehavior{
													Action: GetAnyWithoutError(rewardsScreenDeeplink),
												},
											},
										},
									},
								},
							},
							HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
						},
					},
				},
			},
		},
	}, nil
}

func (r *RewardService) fetchMyEarningsSectionV2(ctx context.Context, req *fePb.GetOffersCatalogPageRequest) (*fePb.GetOffersCatalogPageResponse_Section, error) {
	var (
		actorId                     = req.GetReq().GetAuth().GetActorId()
		collectedOffersPageDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
		}
		rewardsScreenDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REDEEMED_REWARDS_SCREEN,
		}
	)

	// fetch user fi coin balance
	fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error fetching fi coins balance for my earnings section", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	// setting the collected offers icon text component
	collectedOffersITC := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(collectedOffersCtaText, colorContentOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M).WithAlignment(commontypes.Text_ALIGNMENT_RIGHT),
		},
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronWhiteUrl, 28, 28).
			WithImageType(commontypes.ImageType_PNG).
			WithImagePadding(4, 6, 4, 6),
		RightImgTxtPadding: 6,
	}

	// might need to add a count of collected offeras in the future as an ITC
	collectedOffersSection := &components.Component{
		Content: GetAnyWithoutError(
			&sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: GetAnyWithoutError(collectedOffersITC),
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: GetAnyWithoutError(collectedOffersPageDeeplink),
									},
								},
								AnalyticsEvent: &analytics.AnalyticsEvent{
									EventName: "ClickedCollectedOffersIcon",
								},
							},
						},
					},
				},
			},
		),
	}

	// setting the fi coin balance icon text component
	fiCoinBalanceITC := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(fiCoinsBalance), 0), colorFiSnow, commontypes.FontStyle_HEADLINE_2XL),
		},
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsIconUrl, 28, 28).
			WithImageType(commontypes.ImageType_PNG),
		LeftImgTxtPadding: 8,
	}
	allRewardsITC := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle("All Rewards", colorContentOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_XS),
		},
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronWhiteUrl, 16, 16).
			WithImageType(commontypes.ImageType_PNG),
		RightImgTxtPadding: 6,
	}
	fiCoinsBalanceSection := &components.Component{
		Content: GetAnyWithoutError(
			&sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Your Fi Coins", colorContentOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_M)),
					},
					{
						Content: GetAnyWithoutError(fiCoinBalanceITC),
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: GetAnyWithoutError(rewardsScreenDeeplink),
									},
								},
							},
						},
					},
					{
						Content: GetAnyWithoutError(allRewardsITC),
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: GetAnyWithoutError(rewardsScreenDeeplink),
									},
								},
							},
						},
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
			},
		),
	}

	myRewardsEarningSections := make([]*components.Component, 0)

	myRewardsEarningSections = append(myRewardsEarningSections, fiCoinsBalanceSection, collectedOffersSection)

	return &fePb.GetOffersCatalogPageResponse_Section{
		BackgroundColor: widget.GetBlockBackgroundColour(colorGunmetal),
		Section: &fePb.GetOffersCatalogPageResponse_Section_MyEarningsSection{
			MyEarningsSection: &fePb.GetOffersCatalogPageResponse_MyEarningsSection{
				Section: &sections.Section{
					Content: &sections.Section_HorizontalListSection{
						HorizontalListSection: &sections.HorizontalListSection{
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: properties.GetContainerProperty().
											WithMargin(16, 20, 16, 20).
											WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
									}},
							},
							Components:            myRewardsEarningSections,
							HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
							VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
						},
					},
				},
			},
		},
	}, nil
}

func (r *RewardService) fetchUnopenedRewardsSection(ctx context.Context, req *fePb.GetOffersCatalogPageRequest) (*fePb.GetOffersCatalogPageResponse_Section, error) {
	var (
		actorId                = req.GetReq().GetAuth().GetActorId()
		unopenedRewardsSection *fePb.GetOffersCatalogPageResponse_Section_UnopenedRewardsSection
	)

	unopenedRewardsSection = &fePb.GetOffersCatalogPageResponse_Section_UnopenedRewardsSection{
		UnopenedRewardsSection: &fePb.GetOffersCatalogPageResponse_UnopenedRewardsSection{
			Banner: &fePb.GetOffersCatalogPageResponse_UnopenedRewardsSection_UnopenedRewardsSectionBanner{
				LeadingImage:  commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsGiftUrl, 44, 46),
				TrailingImage: commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronGreyUrl, 24, 24),
				Title:         commontypes.GetTextFromStringFontColourFontStyle("Ways to earn more Fi Coins", colorFiSnow, commontypes.FontStyle_HEADLINE_S),
				Deeplink:      &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN},
				BorderColor: widget.GetRadialGradientBackgroundColor(&widget.CenterCoordinates{
					CenterX: 50,
					CenterY: 50,
				}, 100, []string{"#4DDCF3EE", "#0DDCF3EE"}),
				BgColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
					{Color: "#3F414666", StopPercentage: 0},
					{Color: "#31323433", StopPercentage: 100},
				}),
			},
		},
	}

	userAndAppAttributes, err := r.getUserAndAppAttributes(ctx, req.GetReq().GetAuth(), req.GetReq().GetAppVersionCode())
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching user and app attributes")
	}

	// fetch unopened rewards
	unopenedRewards, title, err := r.getUnopenedRewardsAndTitle(ctx, actorId, userAndAppAttributes)
	if err != nil {
		logger.Error(ctx, "error fetching unopened rewards", zap.Error(err))
		return nil, err
	}

	// maximum length of unopened rewards
	maxUnopenedMoneyPlantsToDisplay := min(len(unopenedRewards), int(r.rewardsFrontendMeta.MaxUnopenedRewardsToShowOnMyRewardsScreen))

	if maxUnopenedMoneyPlantsToDisplay > 0 {
		unopenedRewardsSection.UnopenedRewardsSection.Header = commontypes.GetTextFromStringFontColourFontStyle(title, colorContentOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S)
		unopenedRewardsSection.UnopenedRewardsSection.ViewAllCta = ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("VIEW ALL", colorFiGreen, commontypes.FontStyle_OVERLINE_XS_CAPS)).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_UNREDEEMED_REWARDS_SCREEN}).
			WithRightVisualElementUrlHeightAndWidth(rightChevronGreenUrl, 16, 16)
		unopenedRewardsSection.UnopenedRewardsSection.Rewards = unopenedRewards[:maxUnopenedMoneyPlantsToDisplay]
	}

	return &fePb.GetOffersCatalogPageResponse_Section{
		BackgroundColor: widget.GetBlockBackgroundColour(colorGunmetal),
		Section:         unopenedRewardsSection,
	}, nil
}

// fetchBannersSection fetches the banners section containing the promoted offers
func (r *RewardService) fetchBannersSection(ctx context.Context, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails, _ bool) *fePb.GetOffersCatalogPageResponse_Section {
	var (
		offerBanners []*feRewardsPkgPb.Banner
		bannerRank   uint32
	)

	for _, catalogOffer := range catalogOffersAdditionalDetails.GetAllFeCatalogOffers() {
		if isPromoOffer(catalogOffer, catalogOffersAdditionalDetails) {
			bannerRank++
			offerBanners = append(offerBanners, r.makeBannerForPromotedCatalogOffer(ctx, catalogOffer, catalogOffersAdditionalDetails, bannerRank))
		}
	}

	if len(offerBanners) == 0 {
		return nil
	}

	return &fePb.GetOffersCatalogPageResponse_Section{
		BackgroundColor: widget.GetBlockBackgroundColour(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().BannersSection().BackgroundColour()),
		Section: &fePb.GetOffersCatalogPageResponse_Section_BannersSection{
			BannersSection: &fePb.GetOffersCatalogPageResponse_BannersSection{
				Banners: offerBanners,
				ScrollBehaviour: &feRewardsPkgPb.ScrollBehaviour{
					ScrollOrientation: feRewardsPkgPb.ScrollOrientation_SCROLL_ORIENTATION_HORIZONTAL,
					ScrollMode:        feRewardsPkgPb.ScrollMode_SCROLL_MODE_INFINITE,
					ScrollType:        feRewardsPkgPb.ScrollType_SCROLL_TYPE_AUTO_SCROLL,
					ScrollTypeData: &feRewardsPkgPb.ScrollBehaviour_ScrollTypeAutoScrollData_{
						ScrollTypeAutoScrollData: &feRewardsPkgPb.ScrollBehaviour_ScrollTypeAutoScrollData{
							FirstScrollDelay: 3500,
							ScrollInterval:   2500,
						},
					},
				},
			},
		},
	}
}

func isPromoOffer(catalogOffer *fePb.CatalogOfferV1, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails) bool {
	var (
		feToBeOffersMap          = catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()
		feToBeExchangerOffersMap = catalogOffersAdditionalDetails.GetOfferIdToBeExchangerOffersMap()
	)

	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		return feToBeOffersMap[catalogOffer.GetOffer().GetId()].GetAdditionalDetails().GetIsPromoOffer()
	case *fePb.CatalogOfferV1_ExchangerOffer:
		return feToBeExchangerOffersMap[catalogOffer.GetExchangerOffer().GetId()].GetAdditionalDetails().GetIsPromoOffer()
	default:
		logger.ErrorNoCtx("unimplemented offer data type", zap.Any("offerDataType", catalogOffer.GetOfferData()))
	}

	return false
}

// makeBannerForPromotedCatalogOffer creates a banner for the promoted catalog offer for the new offers catalog screen
func (r *RewardService) makeBannerForPromotedCatalogOffer(ctx context.Context, catalogOffer *fePb.CatalogOfferV1, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails, bannerRank uint32) *feRewardsPkgPb.Banner {
	var (
		offerId          string
		categoryTag      string
		bannerIconUrl    string
		bannerBgImageUrl string
		promoOfferTitle  string
		bannerBgColor    string

		feToBeOffersMap          = catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()
		feToBeExchangerOffersMap = catalogOffersAdditionalDetails.GetOfferIdToBeExchangerOffersMap()
	)

	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		offer := catalogOffer.GetOffer()
		offerId = offer.GetId()
		promoOfferTitle = feToBeOffersMap[offerId].GetAdditionalDetails().GetPromoTitle()
		bannerBgColor = offer.GetDisplayDetails().GetBgColor()
		bannerIconUrl, bannerBgImageUrl = fetchFiCoinOfferImageUrls(offer.GetDisplayDetails().GetImages())
	case *fePb.CatalogOfferV1_ExchangerOffer:
		exchangerOffer := catalogOffer.GetExchangerOffer()
		offerId = exchangerOffer.GetId()
		promoOfferTitle = feToBeExchangerOffersMap[offerId].GetAdditionalDetails().GetPromoTitle()
		bannerBgColor = exchangerOffer.GetDisplayDetails().GetBgColor()
		bannerIconUrl = exchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
		bannerBgImageUrl = exchangerOffer.GetDisplayDetails().GetImageUrl()
	default:
		logger.Error(ctx, "unimplemented offer data type", zap.Any("offerDataType", catalogOffer.GetOfferData()))
		return nil
	}

	return &feRewardsPkgPb.Banner{
		LeftVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth(bannerIconUrl, 52, 52).WithImageType(commontypes.ImageType_PNG),
		Title:              commontypes.GetTextFromStringFontColourFontStyleFontAlignment(promoOfferTitle, colorFiSnow, commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT),
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(bannerBgImageUrl, 112, 112).WithImageType(commontypes.ImageType_PNG),
		IndicatorDetails: &feRewardsPkgPb.Banner_PageControlDetails{
			DefaultIndicatorColor:  widget.GetBlockBackgroundColour("#CED2D6"),
			SelectedIndicatorColor: widget.GetBlockBackgroundColour(colorFiSnow),
		},
		BackgroundColor: widget.GetBlockBackgroundColour(bannerBgColor),
		Shadow:          widget.GetShadow(4, 0, 0, widget.GetBlockBackgroundColour(colors.GetShadowHex(bannerBgColor, 0.5))),
		Cta: &feRewardsPkgPb.Cta{
			Action: &feRewardsPkgPb.Cta_DeeplinkAction{
				DeeplinkAction: r.fetchViewOfferDetailsDeeplink(ctx, catalogOffer, r.fetchRedemptionButtonDeeplink(ctx, catalogOffer, catalogOffersAdditionalDetails), catalogOffersAdditionalDetails),
			},
		},
		AnalyticsDetails: &feRewardsPkgPb.Banner_AnalyticsDetails{
			EventProperties: map[string]string{
				"offer_id":        offerId,
				"category_tag":    categoryTag,
				"horizontal_rank": strconv.FormatUint(uint64(bannerRank), 10),
			},
		},
	}
}

// fetchOfferFiltersSection fetches the "offer filters" section of the page containing the filters for the offers catalog
// secondFilterTag is called second because "all" will always be the first filter
func (r *RewardService) fetchOfferFiltersSection(ctx context.Context, uniqueFiltersTagList []string, secondFilterTag string, isFeatureRewardsCatalogMergedPageEnabled bool) (*fePb.GetOffersCatalogPageResponse_Section, error) {
	var (
		finalOfferFiltersWidget []*fePb.GetOffersCatalogPageResponse_OfferFilterWidget
	)

	// fetching filter details
	filtersDetails, err := r.tagsManager.GetVerticalTagFiltersOrderedByPriority(ctx, uniqueFiltersTagList, tags.RenderLocationOffersCatalogPageFiltersList, secondFilterTag, 2)
	if err != nil {
		logger.Error(ctx, "error fetching vertical tag details", zap.Error(err))
		return nil, errors.Wrap(err, "error fetching vertical tag details")
	}

	for _, filterDetails := range filtersDetails {

		if isFeatureRewardsCatalogMergedPageEnabled {
			var (
				activeCta   = filterDetails.GetActiveFilterCta()
				inactiveCta = filterDetails.GetInactiveFilterCta()
			)

			activeCta.GetTitle().GetContainerProperties().BgColor = colors.ColorWater
			if len(activeCta.GetValue().GetTexts()) > 0 {
				activeCta.GetValue().GetTexts()[0].FontColor = colors.ColorOnLightHighEmphasis
			}

			inactiveCta.GetTitle().GetContainerProperties().BgColor = colorCloudGray
			if len(inactiveCta.GetValue().GetTexts()) > 0 {
				inactiveCta.GetValue().GetTexts()[0].FontColor = colors.ColorOnDarkLowEmphasis
			}
		}

		if filterDetails.GetTagName() == allOffersFilterText {
			finalOfferFiltersWidget = append(finalOfferFiltersWidget, &fePb.GetOffersCatalogPageResponse_OfferFilterWidget{
				Widget: &fePb.GetOffersCatalogPageResponse_OfferFilterWidget_AllOffersFilter{
					AllOffersFilter: &fePb.GetOffersCatalogPageResponse_AllOffersCatalogFilter{
						InactiveFilterCta: filterDetails.GetInactiveFilterCta(),
						ActiveFilterCta:   filterDetails.GetActiveFilterCta(),
					},
				},
			})
		} else {
			finalOfferFiltersWidget = append(finalOfferFiltersWidget, &fePb.GetOffersCatalogPageResponse_OfferFilterWidget{
				Widget: &fePb.GetOffersCatalogPageResponse_OfferFilterWidget_TagFilter{
					TagFilter: &fePb.VerticalCatalogTagFilter{
						TagName:           filterDetails.GetTagName(),
						InactiveFilterCta: filterDetails.GetInactiveFilterCta(),
						ActiveFilterCta:   filterDetails.GetActiveFilterCta(),
					},
				},
			})
		}
	}

	return &fePb.GetOffersCatalogPageResponse_Section{
		BackgroundColor: widget.GetBlockBackgroundColour(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().OfferFiltersSection().BackgroundColour()),
		Section: &fePb.GetOffersCatalogPageResponse_Section_OfferFiltersSection{
			OfferFiltersSection: &fePb.GetOffersCatalogPageResponse_OfferFiltersSection{
				Filters: finalOfferFiltersWidget,
			},
		},
	}, nil
}

func fetchUniqueFiltersTagList(tagsPbList []beCasperPb.TagName, categoryTagList []beCasperPb.CategoryTag) []string {
	var uniqueFiltersTagList []string
	for _, tag := range tagsPbList {
		uniqueFiltersTagList = append(uniqueFiltersTagList, tag.String())
	}
	for _, categoryTag := range categoryTagList {
		uniqueFiltersTagList = append(uniqueFiltersTagList, categoryTag.String())
	}
	return uniqueFiltersTagList
}

// fetchOffersSection : fetches the "offers" section of the page containing the offers catalog
func (r *RewardService) fetchOffersSection(ctx context.Context, req *fePb.GetOffersCatalogPageRequest, offersCatalog []*fePb.CatalogOfferV1, beCatalogFilters *beCasperPb.CatalogFilters, uniqueFiltersTagList []string, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails, firstSectionTag string, isFeatureRewardsCatalogMergedPageEnabled bool) *fePb.GetOffersCatalogPageResponse_Section {
	var (
		finalOffersSection = &fePb.GetOffersCatalogPageResponse_Section{}

		catalogOffersSectionsAsSections    = make([]*sections.Section, 0)
		catalogOffersSectionsTitleToCount  = make(map[string]int)
		catalogOffersSectionsTitleToOffers = make(map[string][]*fePb.CatalogOfferV1)

		fetchAllOffersPage          = len(req.GetFilters().GetOffersSectionSpecificFilters().GetTagNames()) == 0
		fetchTagFilteredOffers      = len(beCatalogFilters.GetTags()) != 0
		fetchCategoryFilteredOffers = len(beCatalogFilters.GetCategoryTags()) != 0

		filterTag         beCasperPb.TagName
		filterCategoryTag beCasperPb.CategoryTag

		offerVerticalRankOutsideGridSection = new(uint32)
	)

	// fetching the filter tag and category tag
	// currently only single filter is supported
	if fetchTagFilteredOffers {
		filterTag = beCatalogFilters.GetTags()[0]
	}
	if fetchCategoryFilteredOffers {
		filterCategoryTag = beCatalogFilters.GetCategoryTags()[0]
	}

	// mapping each section title to its count and offers
	for _, offer := range offersCatalog {
		switch {
		case fetchAllOffersPage:
			if isNonProdEnv() {
				for _, tag := range offer.GetAllTags() {
					catalogOffersSectionsTitleToCount[tag]++
					catalogOffersSectionsTitleToOffers[tag] = append(catalogOffersSectionsTitleToOffers[tag], offer)
				}
			}
			catalogOffersSectionsTitleToCount[offer.GetCategoryTag()]++
			catalogOffersSectionsTitleToOffers[offer.GetCategoryTag()] = append(catalogOffersSectionsTitleToOffers[offer.GetCategoryTag()], offer)
		case fetchTagFilteredOffers && lo.Contains(offer.GetAllTags(), filterTag.String()):
			catalogOffersSectionsTitleToCount[filterTag.String()]++
			catalogOffersSectionsTitleToOffers[filterTag.String()] = append(catalogOffersSectionsTitleToOffers[filterTag.String()], offer)
		case fetchCategoryFilteredOffers && offer.GetCategoryTag() == filterCategoryTag.String():
			catalogOffersSectionsTitleToCount[filterCategoryTag.String()]++
			catalogOffersSectionsTitleToOffers[filterCategoryTag.String()] = append(catalogOffersSectionsTitleToOffers[filterCategoryTag.String()], offer)
		default:
			if isNonProdEnv() {
				for _, tag := range offer.GetAllTags() {
					catalogOffersSectionsTitleToCount[tag]++
					catalogOffersSectionsTitleToOffers[tag] = append(catalogOffersSectionsTitleToOffers[tag], offer)
				}
			}
			catalogOffersSectionsTitleToCount[offer.GetCategoryTag()]++
			catalogOffersSectionsTitleToOffers[offer.GetCategoryTag()] = append(catalogOffersSectionsTitleToOffers[offer.GetCategoryTag()], offer)
		}
	}

	// fetching the uniqueFiltersTagList in its ordering
	orderedUniqueFiltersTagList, orderedUniqueFiltersTagDisplayNameList := r.tagsManager.GetTagsOrderedByPriority(ctx, uniqueFiltersTagList, tags.RenderLocationOffersCatalogPageFiltersList, firstSectionTag, 1)

	// creating the catalogOffersSectionsAsSections ordered by the same priority as the offer filters(RenderLocationOffersCatalogPageFiltersList)
	for i, uniqueFilterTag := range orderedUniqueFiltersTagList {
		count, ok := catalogOffersSectionsTitleToCount[uniqueFilterTag]
		if !ok {
			continue
		}
		catalogOffersSection := &sections.VerticalListSection{
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
			VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(fetchHeaderForCatalogOffersSection(orderedUniqueFiltersTagDisplayNameList[i], count, isFeatureRewardsCatalogMergedPageEnabled)),
				},
				{
					Content: GetAnyWithoutError(r.fetchOffersForCatalogOffersSection(ctx, catalogOffersSectionsTitleToOffers[uniqueFilterTag], r.fetchOffersOrientationInCatalogOffersSection(ctx, req), catalogOffersAdditionalDetails, offerVerticalRankOutsideGridSection)),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: properties.GetContainerProperty().
							WithMargin(0, 10, 0, 10),
					},
				},
			},
		}

		catalogOffersSectionsAsSections = append(catalogOffersSectionsAsSections, &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: catalogOffersSection,
			},
		})
	}

	// setting the final offers section
	finalOffersSection.BackgroundColor = widget.GetBlockBackgroundColour(r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().OffersSection().BackgroundColour())
	finalOffersSection.Section = &fePb.GetOffersCatalogPageResponse_Section_OffersSection{
		OffersSection: &fePb.GetOffersCatalogPageResponse_OffersSection{
			CatalogOffersSections: catalogOffersSectionsAsSections,
		},
	}

	return finalOffersSection
}

// fetchOffersForCatalogOffersSection fetches the offers part of one header+offers section
func (r *RewardService) fetchOffersForCatalogOffersSection(ctx context.Context, offers []*fePb.CatalogOfferV1, orientation sections.GridListSection_Orientation, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails, offerVerticalRankOutsideGridSection *uint32) *sections.GridListSection {
	var (
		catalogOfferCardsAsComponents = make([]*components.Component, 0, len(offers))
		totalOffers                   = len(offers)

		gridCellSize       = 1
		height             = finalCatalogOfferCardHeight * gridCellSize
		width              = 0
		widthDimensionType = properties.Size_Dimension_DIMENSION_TYPE_FILL
	)

	if orientation == sections.GridListSection_ORIENTATION_VERTICAL {
		gridCellSize = 2
		horizontalTiles := totalOffers/gridCellSize + totalOffers%gridCellSize
		// total height of the vertical section based on the card height and space between the number of horizontal tiles and extra padding for bottom
		height = horizontalTiles*finalCatalogOfferCardHeight + (horizontalTiles-1)*verticalPaddingBetweenCatalogCards + verticalPaddingBetweenCatalogCards
		// For vertical grids, we need to calculate the grid's horizontal width, according to the card's widths.
		// Otherwise, the cards tend to stretch and occupy the entire cell-width in Android.
		// Total width = card width * number of cells + total margin between the cells + left and right margin in the grid
		width = catalogOfferCardWidth*gridCellSize + horizontalPaddingBetweenCatalogCards*(gridCellSize-1) + horizontalPaddingBetweenCatalogCards*2 // Left and right margin
		widthDimensionType = properties.Size_Dimension_DIMENSION_TYPE_EXACT
	}

	for i, offer := range offers {
		// fetching the offer horizontal and vertical rank
		offerHorizontalRank, offerVerticalRank := fetchOfferRanks(i, gridCellSize, orientation, offerVerticalRankOutsideGridSection)

		catalogOfferSduiCard, err := r.makeCatalogOfferSduiCard(ctx, offer, catalogOffersAdditionalDetails, getCatalogCardLeftMargin(i, orientation), getCatalogCardRightMargin(i, totalOffers, orientation), offerHorizontalRank, offerVerticalRank)
		if err != nil {
			logger.Error(ctx, "error making catalog offer sdui card", zap.Error(err), zap.String(logger.OFFER_ID, offer.GetOffer().GetId()))
			continue
		}
		catalogOfferCardsAsComponents = append(catalogOfferCardsAsComponents, &components.Component{
			Content: GetAnyWithoutError(catalogOfferSduiCard),
		})
	}

	// updating the offerVerticalRankOutsideGridSection according to totalOffers,orientation and gridCellSize
	updateOfferVerticalRankOutsideGridSection(totalOffers, gridCellSize, orientation, offerVerticalRankOutsideGridSection)

	return &sections.GridListSection{
		Components:  catalogOfferCardsAsComponents,
		Orientation: orientation,
		GridCells: &sections.GridListSection_Fixed{
			Fixed: &sections.GridListSection_CellsFixedCount{
				Count: int32(gridCellSize),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, int32(height)).
						WithWidth(widthDimensionType, int32(width)),
				},
			},
		},
		// AnalyticsEvent: ScrolledOffersCard
	}
}

func updateOfferVerticalRankOutsideGridSection(totalOffers int, gridCellSize int, orientation sections.GridListSection_Orientation, offerVerticalRankOutsideGridSection *uint32) {
	if totalOffers >= gridCellSize {
		if orientation == sections.GridListSection_ORIENTATION_HORIZONTAL {
			*offerVerticalRankOutsideGridSection += uint32(gridCellSize)
		} else {
			*offerVerticalRankOutsideGridSection += uint32(math.Ceil(float64(totalOffers) / float64(gridCellSize)))
		}
	}
}

// fetchOfferRanks fetches the offer horizontal and vertical rank based on the index and orientation
func fetchOfferRanks(index int, gridCellSize int, orientation sections.GridListSection_Orientation, offerVerticalRankOutsideGridSection *uint32) (uint32, uint32) {
	var offerVerticalRank, offerHorizontalRank uint32
	// setting the offer horizontal and vertical rank based on orientation
	if orientation == sections.GridListSection_ORIENTATION_HORIZONTAL {
		offerHorizontalRank = uint32(index/gridCellSize) + 1
		offerVerticalRank = *offerVerticalRankOutsideGridSection + uint32(index%gridCellSize) + 1
	} else {
		offerHorizontalRank = uint32(index%gridCellSize) + 1
		offerVerticalRank = *offerVerticalRankOutsideGridSection + uint32(index/gridCellSize) + 1
	}
	return offerHorizontalRank, offerVerticalRank
}

func (r *RewardService) fetchOffersOrientationInCatalogOffersSection(ctx context.Context, req *fePb.GetOffersCatalogPageRequest) sections.GridListSection_Orientation {
	if len(req.GetFilters().GetOffersSectionSpecificFilters().GetTagNames()) == 0 || r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().OffersSection().IsHorizontalScroll(ctx) {
		return sections.GridListSection_ORIENTATION_HORIZONTAL
	}
	return sections.GridListSection_ORIENTATION_VERTICAL
}

// fetchHeaderForCatalogOffersSection fetches the header for the catalog offers section
func fetchHeaderForCatalogOffersSection(title string, count int, isFeatureRewardsCatalogMergedPageEnabled bool) *sections.HorizontalListSection {
	titleTextColor := colorFiSnow
	if isFeatureRewardsCatalogMergedPageEnabled {
		titleTextColor = colorGunmetal
	}

	return &sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(ui.NewITC().WithTexts(
					commontypes.GetTextFromStringFontColourFontStyle(title, titleTextColor, commontypes.FontStyle_SUBTITLE_3).WithMaxLines(1),
					commontypes.GetTextFromStringFontColourFontStyle(" • ", colorContentOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_3).WithMaxLines(1),
					commontypes.GetTextFromStringFontColourFontStyle(fetchNumberOfOffersAsString(count), colorContentOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_3).WithMaxLines(1),
				)),
			},
		},
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
		VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithMargin(16, 0, 16, 16),
				},
			},
		},
	}
}

// makeCatalogOfferSduiCard creates the SDUI card for the catalog offer
// nolint : funlen
func (r *RewardService) makeCatalogOfferSduiCard(ctx context.Context, catalogOffer *fePb.CatalogOfferV1, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails, cardLeftMargin int32, cardRightMargin int32, offerHorizontalRank uint32, offerVerticalRank uint32) (*sections.DepthWiseListSection, error) {
	var (
		redemptionButtonDeeplink *deeplinkPb.Deeplink
		viewOfferDetailsDeeplink *deeplinkPb.Deeplink

		offerId                string
		offerIconUrl           string
		offerBgImageUrl        string
		offerTitle             string
		cardBgColor            string
		isOfferNotRedeemable   bool
		showGreyedOutOfferCard bool
		shouldOfferBeClickable bool

		ctaLabel *fePb.Label
		topLabel *fePb.Label

		feToBeOffersMap          = catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()
		feToBeExchangerOffersMap = catalogOffersAdditionalDetails.GetOfferIdToBeExchangerOffersMap()
	)

	// fetch offer details based on the offer data type
	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		offer := catalogOffer.GetOffer()
		offerId = offer.GetId()
		beOffer := feToBeOffersMap[offerId]
		offerTitle = beOffer.GetAdditionalDetails().GetShortName()
		if offerTitle == "" {
			offerTitle = offer.GetDisplayDetails().GetName()
		}
		offerIconUrl, offerBgImageUrl = fetchFiCoinOfferImageUrls(offer.GetDisplayDetails().GetImages())
		cardBgColor = offer.GetDisplayDetails().GetBgColor()
		ctaLabel = offer.GetDisplayDetails().GetCtaLabel()
		isOfferNotRedeemable = offer.GetDisplayDetails().GetIsOfferNotRedeemable()
		showGreyedOutOfferCard = offer.GetDisplayDetails().GetShowGreyedOutOfferCard()
		shouldOfferBeClickable = !isOfferNotRedeemable || offer.GetDisplayDetails().GetInoperableInfo() != nil
	case *fePb.CatalogOfferV1_ExchangerOffer:
		exchangerOffer := catalogOffer.GetExchangerOffer()
		offerId = exchangerOffer.GetId()
		beExchangerOffer := feToBeExchangerOffersMap[offerId]
		offerTitle = beExchangerOffer.GetOfferDisplayDetails().GetShortTitle()
		if offerTitle == "" {
			offerTitle = exchangerOffer.GetDisplayDetails().GetTitle()
		}
		cardBgColor = exchangerOffer.GetDisplayDetails().GetBgColor()
		offerIconUrl = exchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
		offerBgImageUrl = exchangerOffer.GetDisplayDetails().GetImageUrl()
		ctaLabel = exchangerOffer.GetDisplayDetails().GetCtaLabel()
		topLabel = exchangerOffer.GetDisplayDetails().GetTopLabel()
		isOfferNotRedeemable = exchangerOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		showGreyedOutOfferCard = exchangerOffer.GetDisplayDetails().GetShowGreyedOutOfferCard()
		shouldOfferBeClickable = !isOfferNotRedeemable || exchangerOffer.GetDisplayDetails().GetInoperableInfo() != nil || exchangerOffer.GetDisplayDetails().GetInoperableInfoV2() != nil
	default:
		logger.Error(ctx, "unimplemented offer data type", zap.Any("offerDataType", catalogOffer.GetOfferData()))
	}

	// set the card background color to grey if the offer is greyed out
	if showGreyedOutOfferCard {
		cardBgColor = colorContentOnDarkMediumEmphasis
	}
	// fetch redemption button deeplink and view offer details deeplink only if the offer is clickable
	if shouldOfferBeClickable {
		// fetch redemption button deeplink
		redemptionButtonDeeplink = r.fetchRedemptionButtonDeeplink(ctx, catalogOffer, catalogOffersAdditionalDetails)

		// fetch view offer details deeplink
		viewOfferDetailsDeeplink = r.fetchViewOfferDetailsDeeplink(ctx, catalogOffer, redemptionButtonDeeplink, catalogOffersAdditionalDetails)
	}

	// set event details
	var (
		isCtaCountdownTimerPresent           = "no"
		ctaState                             = "active"
		offerRedemptionButtonCtaText         = r.getSduiCatalogOfferRedemptionText(ctx, offerRedemptionButton, catalogOffer, catalogOffersAdditionalDetails)
		offerRedemptionButtonCtaTextAsString = getTextsValue(offerRedemptionButtonCtaText)
	)
	if ctaLabel.GetCountdownTimer() != nil {
		isCtaCountdownTimerPresent = "yes"
	}
	if isOfferNotRedeemable {
		ctaState = "inactive"
	}

	catalogOfferSduiCardAsSection := &sections.DepthWiseListSection{
		// Shadow Colour depth wise section container
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithBlockBgColor(colors.GetShadowHex(cardBgColor, 0.5)).
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, finalCatalogOfferCardHeight). // offerWidgetCardHeight + offerWidgetCardShadowHeight
						WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardWidth).        // offerWidgetCardWidth
						WithAllCornerRadii(19, 19, 19, 19).
						WithMargin(cardLeftMargin, 0, cardRightMargin, 0),
				},
			},
		},
		Alignment: sections.DepthWiseListSection_BOTTOM_CENTER,
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: GetAnyWithoutError(viewOfferDetailsDeeplink),
					},
				},
				AnalyticsEvent: r.getOffersCatalogPageV2AnalyticsEvent("ClickedCatalogOffersCard",
					offerId, "card_body", "", ctaState, isCtaCountdownTimerPresent,
					offerHorizontalRank, offerVerticalRank),
			},
		},
		VisibleBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
			AnalyticsEvent: r.getOffersCatalogPageV2AnalyticsEvent("ViewedCatalogOffersCard",
				offerId, "card_body", offerRedemptionButtonCtaTextAsString, ctaState, isCtaCountdownTimerPresent,
				offerHorizontalRank, offerVerticalRank),
		},
		Components: []*components.Component{
			// Background colour - VerticalListSection
			{
				Content: GetAnyWithoutError(&sections.VerticalListSection{
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithBlockBgColor(cardBgColor).
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardHeight).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardWidth).
									WithAllCornerRadii(19, 19, 19, 19).
									WithMargin(0, 0, 0, 4),
							},
						},
					},
				}),
			},
			// Background image - VerticalListSection
			{
				Content: GetAnyWithoutError(&sections.VerticalListSection{
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardHeight).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardWidth).
									WithAllCornerRadii(0, 0, 19, 19).
									WithMargin(0, 0, 0, 4),
							},
						},
					},
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(commontypes.GetVisualElementImageFromUrl(offerBgImageUrl).
								WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
								Width: catalogOfferCardWidth,
							})),
						},
					},
				}),
			},
			// Offer content - VerticalListSection
			// Logo and top right tag: HorizontalListSection
			// Title 				 : HorizontalListSection
			{
				Content: GetAnyWithoutError(&sections.VerticalListSection{
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardHeight). // offerWidgetCardHeight
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardWidth).   // offerWidgetCardWidth
									WithAllCornerRadii(19, 19, 19, 19).
									WithMargin(0, 0, 0, 4),
							},
						},
					},
					Components: []*components.Component{
						// Logo and top right tag: HorizontalListSection
						{
							Content: GetAnyWithoutError(&sections.HorizontalListSection{
								HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
								VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: properties.GetContainerProperty().
												WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
												WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, catalogOfferCardWidth).
												WithMargin(16, 16, 0, 6),
										},
									},
								},
								Components: []*components.Component{
									// Logo
									{
										Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(offerIconUrl, 32, 32).WithImageType(commontypes.ImageType_PNG)),
									},
									// Top right tag : HorizontalListSection
									{
										Content: GetAnyWithoutError(&sections.HorizontalListSection{
											VerticalAlignment: sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Corner: &properties.CornerProperty{
																BottomLeftCorner:    8,
																TopLeftCornerRadius: 8,
															},
															BgColor: widget.GetBlockBackgroundColour("#CC333333"),
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
																},
															},
														},
													},
												},
											},
											Components: []*components.Component{
												// Top right tag
												{
													Content: GetAnyWithoutError(r.getSduiCatalogOfferCardTopRightTag(catalogOffer.GetTopRightTags())),
												},
											},
										}),
									},
								},
							}),
						},
						// Title : HorizontalListSection
						{
							Content: GetAnyWithoutError(&sections.HorizontalListSection{
								HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: properties.GetContainerProperty().
												WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, catalogOfferCardHeight).
												WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, catalogOfferCardWidth).
												WithMargin(16, 6, 16, 0),
										},
									},
								},
								Components: []*components.Component{
									{
										Content: GetAnyWithoutError(
											ui.NewITC().WithTexts(
												commontypes.GetTextFromStringFontColourFontStyleFontAlignment(offerTitle, colorFiSnow, commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT).
													WithMaxLines(3),
												// commontypes.GetTextFromStringFontColourFontStyle(" ›", colorFiSnow, commontypes.FontStyle_HEADLINE_L).
												//	WithMaxLines(1),
											),
										),
									},
								},
							}),
						},
					},
				}),
			},
			// Redemption Button - VerticalListSection
			// CTA Label 		 : HorizontalListSection
			// Redemption Button : HorizontalListSection
			{
				Content: GetAnyWithoutError(&sections.VerticalListSection{
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardHeight).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, catalogOfferCardWidth),
							},
						},
					},
					Components: []*components.Component{
						// CTA Label
						{
							Content: r.getSduiCatalogCardCtaLabel(ctaLabel, topLabel),
						},
						// Redemption Button
						{
							Content: GetAnyWithoutError(r.getSduiCatalogOfferCardRedemptionCta(offerRedemptionButtonCtaText, redemptionButtonDeeplink,
								r.getOffersCatalogPageV2AnalyticsEvent("ClickedCatalogOffersCard", offerId, "cta", offerRedemptionButtonCtaTextAsString, ctaState, isCtaCountdownTimerPresent, offerHorizontalRank, offerVerticalRank))),
						},
					},
				}),
			},
		},
	}

	return catalogOfferSduiCardAsSection, nil
}

func (r *RewardService) fetchPageUiDetails(isFeatureRewardsCatalogMergedPageEnabled bool) *fePb.GetOffersCatalogPageResponse_PageUiDetails {
	bgColour := r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().PageUiDetails().BackgroundColour()
	if isFeatureRewardsCatalogMergedPageEnabled {
		bgColour = colorFiSnow
	}
	return &fePb.GetOffersCatalogPageResponse_PageUiDetails{
		BackgroundColor: widget.GetBlockBackgroundColour(bgColour),
	}
}

// fetchRedemptionButtonDeeplink fetches the redemption button deeplink for the catalog offer
func (r *RewardService) fetchRedemptionButtonDeeplink(ctx context.Context, catalogOffer *fePb.CatalogOfferV1, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails) *deeplinkPb.Deeplink {
	var (
		feToBeOffersMap = catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()

		offerId, offerTypeForEvent string
		isOfferNotRedeemable       bool

		redemptionButtonDeeplink                       *deeplinkPb.Deeplink
		catalogOfferRedemptionBottomSheetScreenOptions *rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions
		nextActionCta                                  = &feRewardsPkgPb.Cta{
			Itc:    getNextActionCtaItc(),
			Shadow: greenShadow,
		}
	)

	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		offerTypeForEvent = offerTypeNonCbr
		feOffer := catalogOffer.GetOffer()
		offerId = feOffer.GetId()
		beOffer := feToBeOffersMap[offerId]
		offerType := beOffer.GetOfferType()
		isOfferNotRedeemable = feOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		finalRedemptionPrice := func() uint32 {
			if feOffer.GetDiscountDetails().GetDiscountedPrice() != 0 {
				return feOffer.GetDiscountDetails().GetDiscountedPrice()
			}
			return uint32(feOffer.GetPrice())
		}()
		// fetching the screen options for the deeplink
		switch {
		// setting screen options with inoperable info
		case isOfferNotRedeemable:
			catalogOfferRedemptionBottomSheetScreenOptions = r.convertInoperableInfoToCatalogOfferRedemptionBottomSheetScreenOptions(feOffer.GetDisplayDetails().GetInoperableInfo())
			catalogOfferRedemptionBottomSheetScreenOptions.AnalyticsDetails = getCatalogOfferRedemptionBottomSheetEventProperties(
				offerId,
				catalogOfferRedemptionInoperableInfoBottomSheetScreenName,
				offerTypeForEvent,
				getTextsValue(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetTitle().GetTexts()),
				getTextsValue(getTextsFromItcs(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetDescriptions())))
		// setting screen options with custom action based on some offer types which require a custom screen
		case lo.Contains(customBottomSheetOfferTypes, offerType):
			catalogOfferRedemptionBottomSheetScreenOptions = &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions{
				BottomSheetData: &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionCustomActionBottomSheetData{
					OfferRedemptionCustomActionBottomSheetData: &rewardsScreenOptionsPb.OfferRedemptionCustomActionBottomSheetData{
						CustomAction: &feRewardsPkgPb.CustomAction{
							ActionType: feRewardsPkgPb.CustomAction_MAKE_API_CALL,
							ActionApi:  feRewardsPkgPb.CustomAction_GET_REDEEM_OFFER_INPUT_SCREEN,
							ActionData: &feRewardsPkgPb.CustomAction_GetRedeemOfferInputScreenApiActionData_{
								GetRedeemOfferInputScreenApiActionData: &feRewardsPkgPb.CustomAction_GetRedeemOfferInputScreenApiActionData{
									OfferId:         offerId,
									RedemptionPrice: finalRedemptionPrice,
								},
							},
						},
					},
				},
			}

			catalogOfferRedemptionBottomSheetScreenOptions.AnalyticsDetails = getCatalogOfferRedemptionBottomSheetEventProperties(
				offerId,
				catalogOfferRedemptionCustomActionBottomSheetScreenName,
				offerTypeForEvent,
				"",
				"")

		// the default screen options contain the offer redemption
		// which contains the fi coin redemption screen
		default:
			catalogOfferRedemptionBottomSheetScreenOptions = r.getSduiCatalogOfferRedemptionDefaultBottomSheetScreenOptions(offerId, r.getSduiCatalogOfferRedemptionText(ctx, offerRedemptionBottomSheet, catalogOffer, catalogOffersAdditionalDetails))
			catalogOfferRedemptionBottomSheetScreenOptions.AnalyticsDetails = getCatalogOfferRedemptionBottomSheetEventProperties(
				offerId,
				catalogOfferRedemptionDefaultBottomSheetScreenName,
				offerTypeForEvent,
				getTextsValue(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetTitle().GetTexts()),
				getTextsValue(getTextsFromItcs(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetDescriptions())))
		}

		redemptionButtonDeeplink = &deeplinkPb.Deeplink{
			Screen:          deeplinkPb.Screen_CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET,
			ScreenOptionsV2: GetAnyWithoutError(catalogOfferRedemptionBottomSheetScreenOptions),
		}
	case *fePb.CatalogOfferV1_ExchangerOffer:
		offerTypeForEvent = offerTypeCbr
		feExchangerOffer := catalogOffer.GetExchangerOffer()
		offerId = feExchangerOffer.GetId()
		isOfferNotRedeemable = feExchangerOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		switch {
		// setting the deeplink if offer is not redeemable
		case isOfferNotRedeemable:
			inoperableInfo := feExchangerOffer.GetDisplayDetails().GetInoperableInfo()
			inoperableInfoV2 := feExchangerOffer.GetDisplayDetails().GetInoperableInfoV2()

			if inoperableInfoV2 == nil {
				nextActionCta.GetItc().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(inoperableInfo.GetDetailedInfo().GetCtaText(), colorFiSnow, commontypes.FontStyle_BUTTON_M, commontypes.Text_ALIGNMENT_CENTER))
				catalogOfferRedemptionBottomSheetScreenOptions = &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions{
					BottomSheetData: &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionBottomSheetData{
						OfferRedemptionBottomSheetData: &rewardsScreenOptionsPb.OfferRedemptionBottomSheetData{
							Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(inoperableInfo.GetDetailedInfo().GetTitle(), colorInk, commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_CENTER)),
							Descriptions: []*ui.IconTextComponent{
								ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(inoperableInfo.GetDetailedInfo().GetDescription(), colorContentOnDarkLowEmphasis, commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)),
							},
							NextActionCta: nextActionCta,
							CloseCta:      &feRewardsPkgPb.Cta{},
						},
					},
				}
			} else {
				catalogOfferRedemptionBottomSheetScreenOptions = r.convertInoperableInfoToCatalogOfferRedemptionBottomSheetScreenOptions(inoperableInfoV2)
			}

			catalogOfferRedemptionBottomSheetScreenOptions.AnalyticsDetails = getCatalogOfferRedemptionBottomSheetEventProperties(
				offerId,
				catalogOfferRedemptionInoperableInfoBottomSheetScreenName,
				offerTypeForEvent,
				getTextsValue(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetTitle().GetTexts()),
				getTextsValue(getTextsFromItcs(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetDescriptions())))

			redemptionButtonDeeplink = &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET,
				ScreenOptionsV2: GetAnyWithoutError(catalogOfferRedemptionBottomSheetScreenOptions),
			}
		// default handling is the redemption screen
		default:
			redemptionButtonDeeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REDEEM_EXCHANGER_OFFER,
				ScreenOptions: &deeplinkPb.Deeplink_RedeemExchangerOfferScreenOptions{
					RedeemExchangerOfferScreenOptions: &deeplinkPb.RedeemExchangerOfferScreenOptions{
						OfferId: offerId,
					},
				},
			}
		}
	default:
		logger.Error(ctx, "invalid offer data type", zap.Any("offerData", catalogOffer.GetOfferData()))
	}

	return r.fetchUpdatedRedemptionButtonDeeplink(redemptionButtonDeeplink, offerId, offerTypeForEvent, catalogOffersAdditionalDetails)
}

func (r *RewardService) fetchUpdatedRedemptionButtonDeeplink(redemptionButtonDeeplink *deeplinkPb.Deeplink, offerId, offerTypeForEvent string, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails) *deeplinkPb.Deeplink {
	// returning user specific redemption button deeplink
	switch {
	// If the user is a loan default user, we are returning the loan default user redemption bottom sheet
	// Note : This would be false if LoanDefaultReminderBottomSheetFeatureConfig is disabled to avoid unnecessary calls to pal client
	case catalogOffersAdditionalDetails.GetUserSpecificMetadata().GetIsLoanDefaultUser():
		catalogOfferRedemptionBottomSheetScreenOptions := r.getSduiCatalogOfferRedemptionLoanDefaultUserBottomSheetScreenOptions(redemptionButtonDeeplink)
		catalogOfferRedemptionBottomSheetScreenOptions.AnalyticsDetails = getCatalogOfferRedemptionBottomSheetEventProperties(
			offerId,
			catalogOfferRedemptionLoanEmiReminderBottomSheetScreenName,
			offerTypeForEvent,
			getTextsValue(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetTitle().GetTexts()),
			getTextsValue(getTextsFromItcs(catalogOfferRedemptionBottomSheetScreenOptions.GetOfferRedemptionBottomSheetData().GetDescriptions())))
		return &deeplinkPb.Deeplink{
			Screen:          deeplinkPb.Screen_CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET,
			ScreenOptionsV2: GetAnyWithoutError(catalogOfferRedemptionBottomSheetScreenOptions),
		}
	default:
		return redemptionButtonDeeplink
	}
}

// fetchViewOfferDetailsDeeplink fetches the view offer details deeplink for a single catalog offer
func (r *RewardService) fetchViewOfferDetailsDeeplink(ctx context.Context, catalogOffer *fePb.CatalogOfferV1, redemptionButtonDeeplink *deeplinkPb.Deeplink, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails) *deeplinkPb.Deeplink {
	var (
		offerId              string
		offerTitle           string
		topSectionBgColor    string
		offerLogoUrl         string
		offerProductImageUrl string
		isOfferNotRedeemable bool
		howToRedeem          []*commontypes.Text
		tAndCs               []*commontypes.Text
		countDownTimer       *fePb.CountdownTimer
		vendorName           beCasperPb.OfferVendor
	)

	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		feOffer := catalogOffer.GetOffer()
		offerId = feOffer.GetId()
		beOffer := catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()[offerId]
		vendorName = beOffer.GetVendorName()
		offerTitle = feOffer.GetDisplayDetails().GetOfferTitle()
		offerLogoUrl, offerProductImageUrl = fetchFiCoinOfferImageUrls(feOffer.GetDisplayDetails().GetImages())
		isOfferNotRedeemable = feOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		topSectionBgColor = feOffer.GetDisplayDetails().GetBgColor()
		howToRedeem = r.formatStringListToHtmlTextList(beOffer.GetAdditionalDetails().GetHowToRedeem())
		tAndCs = r.getFormattedCatalogOfferTncs(beOffer.GetTnc().GetTncList())
		countDownTimer = feOffer.GetDisplayDetails().GetCtaLabel().GetCountdownTimer()
	case *fePb.CatalogOfferV1_ExchangerOffer:
		feExchangerOffer := catalogOffer.GetExchangerOffer()
		offerId = feExchangerOffer.GetId()
		beExchangerOffer := catalogOffersAdditionalDetails.GetOfferIdToBeExchangerOffersMap()[offerId]
		offerTitle = feExchangerOffer.GetDisplayDetails().GetInfoBannerTitle()
		offerLogoUrl = feExchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
		offerProductImageUrl = feExchangerOffer.GetDisplayDetails().GetImageUrl()
		isOfferNotRedeemable = feExchangerOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		topSectionBgColor = feExchangerOffer.GetDisplayDetails().GetInfoBannerBgColor()
		howToRedeem = r.formatStringListToHtmlTextList(beExchangerOffer.GetOfferDisplayDetails().GetHowToRedeem())
		tAndCs = r.getFormattedCatalogOfferTncs(beExchangerOffer.GetOfferDisplayDetails().GetTnc())
		countDownTimer = feExchangerOffer.GetDisplayDetails().GetCtaLabel().GetCountdownTimer()
	default:
		logger.Error(ctx, "invalid Offer data type", zap.Any("offerData", catalogOffer.GetOfferData()))
	}

	// if the vendor is poshvine, we are returning the redemption deeplink to redirect the user directly to the poshvine page
	if vendorName == beCasperPb.OfferVendor_POSHVINE {
		return redemptionButtonDeeplink
	}

	topSection := &rewardsScreenOptionsPb.OfferDetailsBottomSheetScreenOptions_TopSection{
		OfferTitle:        commontypes.GetTextFromStringFontColourFontStyle(offerTitle, offerDetailsTopSectionTitleColor, commontypes.FontStyle_HEADLINE_L),
		OfferLogo:         commontypes.GetVisualElementFromUrlHeightAndWidth(offerLogoUrl, 48, 48),
		BgColor:           widget.GetBlockBackgroundColour(topSectionBgColor),
		OfferProductImage: commontypes.GetVisualElementFromUrlHeightAndWidth(offerProductImageUrl, 134, 134),
	}

	offerDetailsSection := []*becComponents.SectionDetails{
		{
			Header: &becComponents.SectionDetails_Header{
				Title:   commontypes.GetTextFromStringFontColourFontStyle("How to redeem", colorGrayNight, commontypes.FontStyle_SUBTITLE_S),
				BgColor: widget.GetBlockBackgroundColour(colorContentOnDarkHighEmphasis),
			},
			Infos:   howToRedeem,
			BgColor: widget.GetBlockBackgroundColour(colorFiSnow),
		},
		{
			Header: &becComponents.SectionDetails_Header{
				Title:   commontypes.GetTextFromStringFontColourFontStyle("Terms & Conditions", colorGrayNight, commontypes.FontStyle_SUBTITLE_S),
				BgColor: widget.GetBlockBackgroundColour(colorContentOnDarkHighEmphasis),
			},
			Infos:   tAndCs,
			BgColor: widget.GetBlockBackgroundColour(colorFiSnow),
		},
	}

	redemptionCtaItcBgColor := func() string {
		if isOfferNotRedeemable {
			return "#DDE1E7"
		}
		return colorFiGreen
	}()
	redemptionCtaItcShadow := func() *ui.Shadow {
		if isOfferNotRedeemable {
			return greyShadow
		}
		return greenShadow
	}()

	redemptionCta := &feRewardsPkgPb.Cta{
		Itc: ui.NewITC().WithTexts(r.getSduiCatalogOfferRedemptionText(ctx, viewOfferDetailsRedemptionButton, catalogOffer, catalogOffersAdditionalDetails)...).
			WithContainerBackgroundColor(redemptionCtaItcBgColor).
			WithContainerPaddingSymmetrical(0, 10),
		Shadow: redemptionCtaItcShadow,
		Action: &feRewardsPkgPb.Cta_DeeplinkAction{
			DeeplinkAction: redemptionButtonDeeplink,
		},
	}

	offerDetailsBottomSheetScreenOptions := &rewardsScreenOptionsPb.OfferDetailsBottomSheetScreenOptions{
		Title:               commontypes.GetTextFromStringFontColourFontStyle(offerDetailsBottomSheetTitle, offerDetailsBottomSheetTitleColor, commontypes.FontStyle_HEADLINE_M),
		TopSection:          topSection,
		OfferDetailsSection: offerDetailsSection,
		Cta:                 redemptionCta,
		CountdownTimer:      r.convertCountdownTimer(countDownTimer),
		OfferId:             offerId,
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_OFFER_DETAILS_BOTTOM_SHEET,
		ScreenOptionsV2: GetAnyWithoutError(offerDetailsBottomSheetScreenOptions),
	}
}

// getDisplayFirstOfferViewOfferDetailsDeeplink fetches the view offer details deeplink for the first offer in the display first offer section
func (r *RewardService) getDisplayFirstOfferViewOfferDetailsDeeplink(ctx context.Context, displayFirstCatalogOffer *fePb.CatalogOfferV1, catalogOffersAdditionalDetails *feRewardsPkg.CatalogOffersAdditionalDetails, displayFirstOfferIds []string) *deeplinkPb.Deeplink {
	var offerId string

	switch displayFirstCatalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		offerId = displayFirstCatalogOffer.GetOffer().GetId()
	case *fePb.CatalogOfferV1_ExchangerOffer:
		offerId = displayFirstCatalogOffer.GetExchangerOffer().GetId()
	default:
		logger.Error(ctx, "invalid offer data type")
	}

	if len(displayFirstOfferIds) > 0 && offerId != displayFirstOfferIds[0] {
		return nil
	}

	// fetch redemption button deeplink
	redemptionButtonDeeplink := r.fetchRedemptionButtonDeeplink(ctx, displayFirstCatalogOffer, catalogOffersAdditionalDetails)

	// fetch view offer details deeplink
	return r.fetchViewOfferDetailsDeeplink(ctx, displayFirstCatalogOffer, redemptionButtonDeeplink, catalogOffersAdditionalDetails)
}

func (r *RewardService) GetOffersCatalogPageVersionToRender(ctx context.Context, req *fePb.GetOffersCatalogPageVersionToRenderRequest) (*fePb.GetOffersCatalogPageVersionToRenderResponse, error) {
	if feRewardsPkg.IsOffersCatalogPageV2Enabled(ctx, r.dyconf) {
		return &fePb.GetOffersCatalogPageVersionToRenderResponse{
			RespHeader: feHeaderPb.NewResponseHeader(rpc.StatusOk(), nil, ""),
			Version:    fePb.GetOffersCatalogPageVersionToRenderResponse_CATALOG_OFFERS_PAGE_VERSION_2,
		}, nil
	}

	return &fePb.GetOffersCatalogPageVersionToRenderResponse{
		RespHeader: feHeaderPb.NewResponseHeader(rpc.StatusOk(), nil, ""),
		Version:    fePb.GetOffersCatalogPageVersionToRenderResponse_CATALOG_OFFERS_PAGE_VERSION_1,
	}, nil
}

func isNonProdEnv() bool {
	env, _ := cfg.GetEnvironment()
	return !cfg.IsProdEnv(env)
}

// getUniqueTagsFromOffers combines all tags present in given exchanger and
// catalog offers (automatically applied/manually applied) into a single list
// Currently there are three types of tags :
// 1. Tags (single offer can have multiple tags)
// 2. CategoryTags (single offer can have only one category tag)
// 3. SubCategoryTags (single offer can have only one sub category tag)
func (r *RewardService) getUniqueTagsFromOffers(beExchangerOffers []*beExchangerPb.ExchangerOffer, beOffers []*beCasperPb.Offer) ([]beCasperPb.TagName, []beCasperPb.CategoryTag, []beCasperPb.SubCategoryTag) {
	var (
		tagsExistenceMap           = map[beCasperPb.TagName]bool{}
		categoryTagExistenceMap    = map[beCasperPb.CategoryTag]bool{}
		subCategoryTagExistenceMap = map[beCasperPb.SubCategoryTag]bool{}

		uniqueTags            []beCasperPb.TagName
		uniqueCategoryTags    []beCasperPb.CategoryTag
		uniqueSubCategoryTags []beCasperPb.SubCategoryTag
	)
	for _, exchangerOffer := range beExchangerOffers {
		for _, tag := range exchangerOffer.GetTagsInfo().GetTags() {
			tagsExistenceMap[tag] = true
		}

		for _, tag := range exchangerOffer.GetTagsInfo().GetManualTags() {
			tagsExistenceMap[tag] = true
		}

		categoryTagExistenceMap[exchangerOffer.GetTagsInfo().GetCategoryTag()] = true
		subCategoryTagExistenceMap[exchangerOffer.GetTagsInfo().GetSubCategoryTag()] = true
	}

	for _, offer := range beOffers {
		for _, tag := range offer.GetTagsInfo().GetTags() {
			tagsExistenceMap[tag] = true
		}

		for _, tag := range offer.GetTagsInfo().GetManualTags() {
			tagsExistenceMap[tag] = true
		}

		categoryTagExistenceMap[offer.GetTagsInfo().GetCategoryTag()] = true
		subCategoryTagExistenceMap[offer.GetTagsInfo().GetSubCategoryTag()] = true
	}

	for beTag := range tagsExistenceMap {
		if beTag != beCasperPb.TagName_UNSPECIFIED_TAG_NAME {
			uniqueTags = append(uniqueTags, beTag)
		}
	}

	for beCategoryTag := range categoryTagExistenceMap {
		if beCategoryTag != beCasperPb.CategoryTag_CATEGORY_TAG_UNSPECIFIED {
			uniqueCategoryTags = append(uniqueCategoryTags, beCategoryTag)
		}
	}

	for beSubCategoryTag := range subCategoryTagExistenceMap {
		if beSubCategoryTag != beCasperPb.SubCategoryTag_SUB_CATEGORY_TAG_UNSPECIFIED {
			uniqueSubCategoryTags = append(uniqueSubCategoryTags, beSubCategoryTag)
		}
	}

	return uniqueTags, uniqueCategoryTags, uniqueSubCategoryTags
}

func (r *RewardService) getCatalogSortOptions() []*fePb.CatalogSortOption {
	return []*fePb.CatalogSortOption{
		{
			SortBy: fePb.SortBy_REDEMPTION_PRICE_ASC.String(),
			SortOptionCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Price: Low to high"},
						FontColor:    "#646464",
						BgColor:      "#F7F9FA",
					},
				},
			},
		},
		{
			SortBy: fePb.SortBy_REDEMPTION_PRICE_DESC.String(),
			SortOptionCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Price: High to Low"},
						FontColor:    "#646464",
						BgColor:      "#F7F9FA",
					},
				},
			},
		},
	}
}

// getSortByEnum will return mapping of sortByString in fePb.SortBy enum.
func (r *RewardService) getSortByEnum(sortByString string) (fePb.SortBy, error) {
	if sortOptionVal, ok := fePb.SortBy_value[sortByString]; ok {
		return fePb.SortBy(sortOptionVal), nil
	} else {
		return fePb.SortBy_UNSPECIFIED_SORT_BY, fmt.Errorf("unhandled sort option encountered, sortByString: %s", sortByString)
	}
}

// GetCardOffersTabs fetching credit/debit card offers page layout tabs.
func (r *RewardService) GetCardOffersTabs(ctx context.Context, req *fePb.GetCardOffersTabsRequest) (*fePb.GetCardOffersTabsResponse, error) {
	// list of tabs are debit card tab and amplifi credit card tab by default
	dcTab := r.getCardOffersPageTab(fePb.CardTypeId_DEBIT_CARD_ID.String(), debitCardOffersPageTabTitle, debitCardOffersPageTabLeftIconUrl)
	ccTab := r.getCardOffersPageTab(fePb.CardTypeId_AMPLIFI_CREDIT_CARD_ID.String(), amplifiCreditCardOffersPageTabTitle, amplifiCreditCardOffersPageTabLeftIconUrl)

	// replacing ccTab with magnifi or simplifi if user is eligible for magnifi or simplifi cc
	creditCardTypeId := r.getAppropriateCreditCardTypeId(ctx, req.GetReq().GetAuth().GetActorId())
	switch creditCardTypeId {
	case fePb.CardTypeId_MAGNIFI_CREDIT_CARD_ID.String():
		ccTab = r.getCardOffersPageTab(fePb.CardTypeId_MAGNIFI_CREDIT_CARD_ID.String(), magnifiCreditCardOffersPageTabTitle, magnifiCreditCardOffersPageTabLeftIconUrl)
	case fePb.CardTypeId_SIMPLIFI_CREDIT_CARD_ID.String():
		ccTab = r.getCardOffersPageTab(fePb.CardTypeId_SIMPLIFI_CREDIT_CARD_ID.String(), simplifiCreditCardOffersPageTabTitle, simplifiCreditCardOffersPageTabLeftIconUrl)
	}

	return &fePb.GetCardOffersTabsResponse{
		Tabs:              []*fePb.GetCardOffersTabsResponse_Tab{dcTab, ccTab},
		DefaultCardTypeId: fePb.CardTypeId_DEBIT_CARD_ID.String(),
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

// GetCardOffersCatalogLayout fetches credit/debit card offers catalog page layout.
func (r *RewardService) GetCardOffersCatalogLayout(ctx context.Context, req *fePb.GetCardOffersCatalogLayoutRequest) (*fePb.GetCardOffersCatalogLayoutResponse, error) {
	var (
		cardType      = req.GetCardType()
		andFilterTags []string
	)

	// if card type id is given in request, we deduce card type and mandatory filter tag from given card type id
	// for eg- AMPLIFI_CREDIT_CARD_ID will give CREDIT_CARD type and AMPLIFI_CREDIT_CARD_EXCLUSIVE tag
	// we update the cardType to CREDIT_CARD and mandatory filter tag to AMPLIFI_CREDIT_CARD_EXCLUSIVE
	if req.GetCardTypeId() != "" {
		tabCardType, tabFilterTags, err := r.getCardTypeAndTagsForCardTypeId(req.GetCardTypeId())
		if err != nil {
			logger.Error(ctx, "invalid card type id in request", zap.Error(err))
			return &fePb.GetCardOffersCatalogLayoutResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("invalid card type id in request"),
				},
			}, nil
		}
		cardType = tabCardType
		andFilterTags = tabFilterTags
	}

	beOffers, offerIdToOfferListingMap, err := r.getBeFiCardOffers(ctx, req.GetReq().GetAuth().GetActorId(), cardType, nil, andFilterTags)
	if err != nil {
		logger.Error(ctx, "error fetching BE card offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		return &fePb.GetCardOffersCatalogLayoutResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error fetching BE card offers"),
			},
		}, nil
	}

	// extract tags from be offers
	beOfferTagsList, _, _ := r.getUniqueTagsFromOffers(nil, beOffers)

	var feAllCardOffers []*fePb.CardOffer
	var promotedOffers []*beCasperPb.Offer
	for _, beOffer := range beOffers {
		// add to promoted offers if offer is marked as promoted offer
		if beOffer.GetAdditionalDetails().GetIsPromoOffer() {
			promotedOffers = append(promotedOffers, beOffer)
		}
		feCardOffer, offerErr := r.getFeCardOfferFromBeOffer(beOffer, offerIdToOfferListingMap[beOffer.GetId()], false)
		if offerErr != nil {
			logger.Error(ctx, "error getting FE card offers from BE offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Error(offerErr))
			// intentionally muting error, catalog page shouldn't fail due to failure in few offers.
		} else {
			feAllCardOffers = append(feAllCardOffers, feCardOffer)
		}
	}

	// sorting based on promo offer display rank in ASC order, if promo display is present for none offers then default rank is used.
	sort.Slice(promotedOffers, func(i, j int) bool {
		return promotedOffers[i].GetAdditionalDetails().GetPromoOfferDisplayRank() <= promotedOffers[j].GetAdditionalDetails().GetPromoOfferDisplayRank()
	})

	var fePromotedCardOffers []*fePb.CardOffer
	for _, promotedOffer := range promotedOffers {
		fePromotedCardOffer, offerErr := r.getFeCardOfferFromBeOffer(promotedOffer, offerIdToOfferListingMap[promotedOffer.GetId()], true)
		if offerErr != nil {
			logger.Error(ctx, "error getting FE promoted card offers from BE offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.OFFER_ID, promotedOffer.GetId()), zap.Error(offerErr))
			// intentionally muting error, catalog page shouldn't fail due to failure in few offers.
		} else {
			fePromotedCardOffers = append(fePromotedCardOffers, fePromotedCardOffer)
		}
	}

	// generating list of all filters
	allTagDetails, orderedAllTagNames, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, beOfferTagsList, tags.RenderLocationCardCatalogAllFilters)
	if err != nil {
		logger.Error(ctx, "failed to get tag details", zap.Any("tagsList", beOfferTagsList), zap.Error(err))
		return &fePb.GetCardOffersCatalogLayoutResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to get tag details")},
		}, nil
	}

	var allFilterTags []*fePb.CatalogTagFilter
	for i, feTagDetails := range allTagDetails {
		allFilterTags = append(allFilterTags, &fePb.CatalogTagFilter{
			TagName:           orderedAllTagNames[i].String(),
			InactiveFilterCta: feTagDetails,
			ActiveFilterCta:   r.tagsManager.GetActiveCatalogTagFilterDetails(feTagDetails),
		})
	}

	maxPromotedFiltersForTopBar := r.dyconf.RewardsFrontendMeta().CardCatalogConfig().MaxPromotedFiltersForTopBar()
	if int32(len(orderedAllTagNames)) < maxPromotedFiltersForTopBar {
		maxPromotedFiltersForTopBar = int32(len(orderedAllTagNames))
	}

	// generating list of prioritised filters
	priorityTagsDetails, orderedPriorityTagNames, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, orderedAllTagNames[:maxPromotedFiltersForTopBar], tags.RenderLocationCardCatalogFiltersList)
	if err != nil {
		logger.Error(ctx, "failed to get tag details", zap.Any("tagsList", orderedAllTagNames[:maxPromotedFiltersForTopBar]), zap.Error(err))
		return &fePb.GetCardOffersCatalogLayoutResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to get tag details")},
		}, nil
	}

	// top filters bar
	var widgets []*fePb.GetCardOffersCatalogLayoutResponse_TopBarWidget
	for i, priorityTagDetail := range priorityTagsDetails {
		widgets = append(widgets, &fePb.GetCardOffersCatalogLayoutResponse_TopBarWidget{
			Widget: &fePb.GetCardOffersCatalogLayoutResponse_TopBarWidget_FilterTagWidget{
				FilterTagWidget: &fePb.CatalogTagFilter{
					TagName:           orderedPriorityTagNames[i].String(),
					InactiveFilterCta: priorityTagDetail,
					ActiveFilterCta:   r.tagsManager.GetActiveCatalogTagFilterDetails(priorityTagDetail),
				},
			},
		})
	}

	widgets = append(
		widgets,
		// add "All Filters" button to filters bar
		&fePb.GetCardOffersCatalogLayoutResponse_TopBarWidget{
			Widget: &fePb.GetCardOffersCatalogLayoutResponse_TopBarWidget_AllFiltersWidget{
				AllFiltersWidget: &fePb.GetCardOffersCatalogLayoutResponse_AllFiltersWidget{
					Cta: &ui.IconTextComponent{
						Texts: []*commontypes.Text{{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "All filters"},
							FontColor:    "#646464",
						}},
						LeftIcon:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/catalog_filters_all_filters_icon.png"},
						RightIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png"},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor: "#FFFFFF",
						},
					},
					FilterTags: allFilterTags,
				},
			},
		},
	)

	var cta *fePb.CTA
	if req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_IOS ||
		(req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_ANDROID && req.GetReq().GetAppVersionCode() >= r.dyconf.RewardsFrontendMeta().MinAndroidVersionSupportingUpdatedOffersScreenCta()) {
		switch req.GetCardType() {
		case fePb.CardType_CREDIT_CARD:
			// check if user has CC or not
			doesUserHaveCc, checkErr := r.checkIfUserHasACc(ctx, req.GetReq().GetAuth().GetActorId())
			switch {
			case checkErr != nil:
				// just logging in case of error as we don't want to fail the RPC if we aren't
				// able to determine if we want to show the CTA or not
				logger.Error(ctx, "error while checking if user has a CC", zap.Error(err))
			case !doesUserHaveCc:
				cta = &fePb.CTA{
					Text:      &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Get the Fi-Federal Credit Card"}},
					IsVisible: true,
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
					},
				}
			default:
				// do nothing
			}
		case fePb.CardType_DEBIT_CARD:
			// check if user has a savings account or not
			acctResp, getErr := r.savingsClient.GetAccountsList(ctx, &savingsPb.GetAccountsListRequest{
				Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
					BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
						ActorIds:                []string{req.GetReq().GetAuth().GetActorId()},
						AccountProductOfferings: savingsPkg.SupportedSavingsAccountProductOfferings,
						PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
					},
				},
			})
			if rpcErr := epifigrpc.RPCError(acctResp, getErr); rpcErr != nil {
				// update cta if no savings account present for the user
				if rpc.StatusFromError(rpcErr).IsRecordNotFound() || len(acctResp.GetAccounts()) == 0 {
					deeplink, dlErr := onbPkg.GetSABenefitsScreen(ctx)
					if dlErr != nil {
						// log the error and return nil cta
						logger.ErrorNoCtx("error while getting savings account benefits screen deeplink", zap.Error(err))
						break
					}
					cta = &fePb.CTA{
						Text:           &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Get the Fi-Federal Debit Card"}},
						IsVisible:      true,
						DeeplinkAction: deeplink,
					}
				} else {
					// just logging in case of error as we don't want to fail the RPC if we aren't
					// able to determine if we want to show the CTA or not
					logger.Error(ctx, "savingsClient.GetAccountsList call failed", zap.Error(rpcErr))
				}
			}
		}
	}

	return &fePb.GetCardOffersCatalogLayoutResponse{
		PromotedOffersSection: &fePb.GetCardOffersCatalogLayoutResponse_PromotedOffersSection{
			Offers: fePromotedCardOffers,
		},
		OfferCatalogSection: &fePb.GetCardOffersCatalogLayoutResponse_OffersCatalogSection{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Explore offers",
				},
				BgColor: "#333333",
			},
			Offers: feAllCardOffers,
			CatalogTopBar: &fePb.GetCardOffersCatalogLayoutResponse_CatalogTopBar{
				ClearFiltersCta: &ui.IconTextComponent{
					LeftIcon: &commontypes.Image{
						ImageUrl: "https://epifi-icons.pointz.in/rewards/clear_filters_icon.png",
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BgColor: "#DDDFE1",
					},
				},
				Widgets: widgets,
			},
		},
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Cta: cta,
	}, nil
}

// GetCardOffers fetches fi credit/debit card offers.
func (r *RewardService) GetCardOffers(ctx context.Context, req *fePb.GetCardOffersRequest) (*fePb.GetCardOffersResponse, error) {
	var (
		cardType      = req.GetCardType()
		orFilterTags  = req.GetFilters().GetTags()
		actorId       = req.GetReq().GetAuth().GetActorId()
		andFilterTags []string
	)

	// if card type id is given in request, we deduce card type and mandatory filter tag from given card type id
	// for eg- AMPLIFI_CREDIT_CARD_ID will give CREDIT_CARD type and AMPLIFI_CREDIT_CARD_EXCLUSIVE tag
	// we update the cardType to CREDIT_CARD and mandatory filter tag to AMPLIFI_CREDIT_CARD_EXCLUSIVE
	if req.GetCardTypeId() != "" {
		tabCardType, tabFilterTags, err := r.getCardTypeAndTagsForCardTypeId(req.GetCardTypeId())
		if err != nil {
			logger.Error(ctx, "invalid card offers page card type id in request", zap.Error(err))
			return &fePb.GetCardOffersResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("invalid card offers page card type id in request"),
				},
			}, nil
		}
		cardType = tabCardType
		andFilterTags = tabFilterTags
	}

	beOffers, offerIdToListingMap, err := r.getBeFiCardOffers(ctx, actorId, cardType, orFilterTags, andFilterTags)
	if err != nil {
		logger.Error(ctx, "error fetching BE card offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		return &fePb.GetCardOffersResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error fetching BE card offers"),
			},
		}, nil
	}

	// handling for promoted offers
	var populatePromotionalDetails bool
	if req.GetFilters().GetOfferCategory() == fePb.OfferCategory_PROMOTED_OFFERS {
		var bePromoOffers []*beCasperPb.Offer
		for _, beOffer := range beOffers {
			if beOffer.GetAdditionalDetails().GetIsPromoOffer() {
				bePromoOffers = append(bePromoOffers, beOffer)
			}
		}
		// sorting based on promo offer display rank if offer category is promoted offers, if promo display is present for none offers then default rank is used.
		sort.Slice(bePromoOffers, func(i, j int) bool {
			return bePromoOffers[i].GetAdditionalDetails().GetPromoOfferDisplayRank() <= beOffers[j].GetAdditionalDetails().GetPromoOfferDisplayRank()
		})
		beOffers = bePromoOffers
		populatePromotionalDetails = true
	}

	var feCardOffers []*fePb.CardOffer
	for _, beOffer := range beOffers {
		feCardOffer, offerErr := r.getFeCardOfferFromBeOffer(beOffer, offerIdToListingMap[beOffer.GetId()], populatePromotionalDetails)
		if offerErr != nil {
			logger.Error(ctx, "error getting FE card offers from BE offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Error(offerErr))
			// intentionally muting error, catalog page shouldn't fail due to failure in few offers.
		} else {
			feCardOffers = append(feCardOffers, feCardOffer)
		}
	}

	return &fePb.GetCardOffersResponse{
		Offers: feCardOffers,
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

func (r *RewardService) getUserAttributesForOfferCatalog(ctx context.Context, actorId string) (*userAttributesForOfferCatalog, error) {
	doesUserOwnACc, err := r.doesUserOwnACc(ctx, actorId)
	if err != nil {
		return nil, err
	}

	return &userAttributesForOfferCatalog{
		doesUserOwnACc: doesUserOwnACc,
	}, nil
}

func (r *RewardService) getCardOffersPageTab(id string, title string, leftIconUrl string) *fePb.GetCardOffersTabsResponse_Tab {
	return &fePb.GetCardOffersTabsResponse_Tab{
		Id: id,
		ActiveCta: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementImageFromUrl(leftIconUrl).WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24}),
			Texts:             []*commontypes.Text{commontypes.GetPlainStringText(title).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor("#313234")},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				LeftPadding:  8,
				RightPadding: 12,
				BgColor:      "#FFFFFF",
			},
		},
		InactiveCta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetPlainStringText(title).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor("#606265")},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				LeftPadding:  12,
				RightPadding: 12,
				BgColor:      "#DCE1E5",
			},
		},
	}
}

func (r *RewardService) getCardTypeAndTagsForCardTypeId(cardTypeId string) (fePb.CardType, []string, error) {
	switch cardTypeId {
	case fePb.CardTypeId_DEBIT_CARD_ID.String():
		return fePb.CardType_DEBIT_CARD, nil, nil
	case fePb.CardTypeId_AMPLIFI_CREDIT_CARD_ID.String():
		return fePb.CardType_CREDIT_CARD, []string{beCasperPb.TagName_AMPLIFI_CREDIT_CARD_EXCLUSIVE.String()}, nil
	case fePb.CardTypeId_SIMPLIFI_CREDIT_CARD_ID.String():
		return fePb.CardType_CREDIT_CARD, []string{beCasperPb.TagName_SIMPLIFI_CREDIT_CARD_EXCLUSIVE.String()}, nil
	case fePb.CardTypeId_MAGNIFI_CREDIT_CARD_ID.String():
		return fePb.CardType_CREDIT_CARD, []string{beCasperPb.TagName_MAGNIFI_CREDIT_CARD_EXCLUSIVE.String()}, nil
	default:
		return fePb.CardType_CARD_TYPE_UNSPECIFIED, nil, fmt.Errorf("invalid card offers page card type id: %s", cardTypeId)
	}
}

func (r *RewardService) GetConvertFiCoinsOfferRedemptionScreen(ctx context.Context, req *fePb.GetConvertFiCoinsOfferRedemptionScreenRequest) (*fePb.GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	// fetch offer details
	catalogOfferRes, err := r.offerCatalogClient.GetOfferDetailsById(ctx, &beCasperPb.GetOfferDetailsByIdRequest{OfferId: req.GetOfferId()})
	if rpcErr := epifigrpc.RPCError(catalogOfferRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching offer from catalog", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(rpcErr))
		return &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching offer from catalog")},
		}, nil
	}
	catalogOffer := catalogOfferRes.GetOffer()

	// fetch user's available Fi-Coins balance
	accountDetailsRes, err := r.accrualClient.GetAccountDetails(ctx, &accrualPb.GetAccountDetailsRequest{
		ActorId:     actorId,
		AccountType: accrualPb.AccountType_ACCOUNT_TYPE_FICOINS,
	})
	if rpcErr := epifigrpc.RPCError(accountDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching account details for actor", zap.Error(rpcErr))
		return &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching account details for actor")},
		}, nil
	}

	switch catalogOffer.GetOfferType() {
	case beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS:
		return r.getFiCoinsOfferRedemptionScreenForClubItcGreenPointsOffer(accountDetailsRes.GetAvailableBalance(), catalogOffer.GetOfferMetadata().GetFiCoinsToPointsOfferMetadata())
	case beCasperPb.OfferType_VISTARA_AIR_MILES:
		return r.getFiCoinsOfferRedemptionScreenForVistaraAirMilesOffer(accountDetailsRes.GetAvailableBalance(), catalogOffer.GetOfferMetadata().GetFiCoinsToPointsOfferMetadata())
	default:
		logger.Error(ctx, "offer type not supported for dynamic fi coins conversion", zap.String("offerType", catalogOffer.GetOfferType().String()))
		return &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("offer type not supported for dynamic fi coins conversion")},
		}, nil
	}
}

func (r *RewardService) getFiCoinsOfferRedemptionScreenForClubItcGreenPointsOffer(availableFiCoinBalance int32, fiCoinsToPointsOfferMetadata *beCasperPb.FiCoinsToPointsOfferMetadata) (*fePb.GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	return &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse{
		RespHeader:                          &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		Title:                               commontypes.GetPlainStringText("How many Fi-Coins do you want to convert?").WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor("#282828"),
		AvailableFiCoinBalance:              availableFiCoinBalance,
		FiCoinsToPointsConversionMultiplier: fiCoinsToPointsOfferMetadata.GetFiCoinsToPointsConversionMultiplier(),
		FiCoinsStepSizeMultiplier:           fiCoinsToPointsOfferMetadata.GetFiCoinsStepSizeMultiplier(),
		MinConvertableFiCoins:               fiCoinsToPointsOfferMetadata.GetMinConvertableFiCoins(),
		MaxConvertableFiCoins:               fiCoinsToPointsOfferMetadata.GetMaxConvertableFiCoins(),
		PointsBanner: &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse_Banner{
			Description: fiCoinsToPointsOfferMetadata.GetPointsBanner().GetDescription(),
			LeftImgUrl:  fiCoinsToPointsOfferMetadata.GetPointsBanner().GetLeftImgUrl(),
			RightImgUrl: fiCoinsToPointsOfferMetadata.GetPointsBanner().GetRightImgUrl(),
			BgColor:     fiCoinsToPointsOfferMetadata.GetPointsBanner().GetBgColor(),
		},
		Disclaimer: &typesUi.TextWithHyperlinks{
			Text: commontypes.GetPlainStringText("You'll need a Club ITC account to claim this offer. Create one here").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#646464"),
			HyperlinkMap: map[string]*typesUi.HyperLink{
				"Create one here": {Link: &typesUi.HyperLink_Url{Url: "https://www.itchotels.com/in/en/enrol"}},
			},
		},
		Cta: &fePb.CtaV1{
			CtaTitle: commontypes.GetPlainStringText("Next").WithFontStyle(commontypes.FontStyle_BUTTON_M).WithFontColor("#FFFFFF"),
			Action:   &fePb.CtaV1_CustomAction{CustomAction: &fePb.CustomAction{ActionType: fePb.CustomAction_MAKE_API_CALL, ActionApi: fePb.CustomActionApi_USER_DETAILS_INPUT_SCREEN_RPC}},
			BgColor:  "#00B899",
		},
	}, nil
}

func (r *RewardService) getFiCoinsOfferRedemptionScreenForVistaraAirMilesOffer(availableFiCoinBalance int32, fiCoinsToPointsOfferMetadata *beCasperPb.FiCoinsToPointsOfferMetadata) (*fePb.GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	return &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse{
		RespHeader:                          &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		Title:                               commontypes.GetPlainStringText("How many Fi-Coins do you want to convert?").WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor("#282828"),
		AvailableFiCoinBalance:              availableFiCoinBalance,
		FiCoinsToPointsConversionMultiplier: fiCoinsToPointsOfferMetadata.GetFiCoinsToPointsConversionMultiplier(),
		FiCoinsStepSizeMultiplier:           fiCoinsToPointsOfferMetadata.GetFiCoinsStepSizeMultiplier(),
		MinConvertableFiCoins:               fiCoinsToPointsOfferMetadata.GetMinConvertableFiCoins(),
		MaxConvertableFiCoins:               fiCoinsToPointsOfferMetadata.GetMaxConvertableFiCoins(),
		PointsBanner: &fePb.GetConvertFiCoinsOfferRedemptionScreenResponse_Banner{
			Description: fiCoinsToPointsOfferMetadata.GetPointsBanner().GetDescription(),
			LeftImgUrl:  fiCoinsToPointsOfferMetadata.GetPointsBanner().GetLeftImgUrl(),
			RightImgUrl: fiCoinsToPointsOfferMetadata.GetPointsBanner().GetRightImgUrl(),
			BgColor:     fiCoinsToPointsOfferMetadata.GetPointsBanner().GetBgColor(),
		},
		Disclaimer: &typesUi.TextWithHyperlinks{
			Text: commontypes.GetPlainStringText("A Club Vistara membership is required to claim this offer").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#646464"),
		},
		Cta: &fePb.CtaV1{
			CtaTitle: commontypes.GetPlainStringText("Next").WithFontStyle(commontypes.FontStyle_BUTTON_M).WithFontColor("#FFFFFF"),
			Action:   &fePb.CtaV1_CustomAction{CustomAction: &fePb.CustomAction{ActionType: fePb.CustomAction_MAKE_API_CALL, ActionApi: fePb.CustomActionApi_USER_DETAILS_INPUT_SCREEN_RPC}},
			BgColor:  "#00B899",
		},
	}, nil
}

func (r *RewardService) GetUserDetailsInputOfferRedemptionScreen(ctx context.Context, req *fePb.GetUserDetailsInputOfferRedemptionScreenRequest) (*fePb.GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	// fetch offer details
	catalogOfferRes, err := r.offerCatalogClient.GetOfferDetailsById(ctx, &beCasperPb.GetOfferDetailsByIdRequest{OfferId: req.GetOfferId()})
	if rpcErr := epifigrpc.RPCError(catalogOfferRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching offer from catalog", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(rpcErr))
		return &fePb.GetUserDetailsInputOfferRedemptionScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching offer from catalog")},
		}, nil
	}
	catalogOffer := catalogOfferRes.GetOffer()

	switch catalogOffer.GetOfferType() {
	case beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS:
		return r.getUserDetailsInputScreenForClubItcGreenPointsOffer()
	case beCasperPb.OfferType_VISTARA_AIR_MILES:
		return r.getUserDetailsInputScreenForClubVistaraOffer()
	default:
		logger.Error(ctx, "offer type not supported for additional user input", zap.String("offerType", catalogOffer.GetOfferType().String()))
		return &fePb.GetUserDetailsInputOfferRedemptionScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("offer type not supported for additional user input")},
		}, nil
	}
}

func (r *RewardService) getUserDetailsInputScreenForClubItcGreenPointsOffer() (*fePb.GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	return &fePb.GetUserDetailsInputOfferRedemptionScreenResponse{
		RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		Title:      commontypes.GetPlainStringText("To claim this offer, enter your Club ITC membership details").WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor("#282828"),
		TextInputFields: []*types.TextInputField{
			{
				Id:          beRedemptionPb.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_USER_NAME.String(),
				Title:       commontypes.GetPlainStringText("NAME").WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS).WithFontColor("#B2B5B9"),
				Placeholder: commontypes.GetPlainStringText("Name").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#8D8D8D"),
				Description: commontypes.GetPlainStringText("Your name as registered with Club ITC").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#9DA1A4"),
				DataType:    types.TextInputField_DATA_TYPE_STRING,
			},
			{
				Id:          beRedemptionPb.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_CLUB_ITC_ID.String(),
				Title:       commontypes.GetPlainStringText("CLUB ITC ID").WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS).WithFontColor("#B2B5B9"),
				Placeholder: commontypes.GetPlainStringText("Club ITC ID").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#8D8D8D"),
				Description: commontypes.GetPlainStringText("Your 8-digit membership ID e.g C75C0001").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#9DA1A4"),
				DataType:    types.TextInputField_DATA_TYPE_STRING,
				ValidationRules: []*types.TextInputField_ValidationRule{
					{
						Regex:          `[A-Z]\d{2}[A-Z][A-Z,\d]\d{3}`,
						FailureMessage: commontypes.GetPlainStringText("Membership ID should be exactly 8 digits").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#FA3B11"),
					},
				},
			},
		},
		Disclaimer: &typesUi.TextWithHyperlinks{
			Text: commontypes.GetPlainStringText("Not a Club ITC member? Create an account").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#646464"),
			HyperlinkMap: map[string]*typesUi.HyperLink{
				"Create an account": {Link: &typesUi.HyperLink_Url{Url: "https://www.itchotels.com/in/en/enrol"}},
			},
		},
		Cta: &fePb.CtaV1{
			CtaTitle: commontypes.GetPlainStringText("Confirm").WithFontStyle(commontypes.FontStyle_BUTTON_M).WithFontColor("#FFFFFF"),
			Action:   &fePb.CtaV1_CustomAction{CustomAction: &fePb.CustomAction{ActionType: fePb.CustomAction_MAKE_API_CALL, ActionApi: fePb.CustomActionApi_INITIATE_REDEMPTION_RPC}},
			BgColor:  "#00B899",
		},
	}, nil
}

// nolint: dupl
func (r *RewardService) getUserDetailsInputScreenForClubVistaraOffer() (*fePb.GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	return &fePb.GetUserDetailsInputOfferRedemptionScreenResponse{
		RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		Title:      commontypes.GetPlainStringText("To claim this offer, enter your Club Vistara membership details").WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor("#282828"),
		TextInputFields: []*types.TextInputField{
			{
				Id:          beRedemptionPb.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_EMAIL_ID.String(),
				Title:       commontypes.GetPlainStringText("EMAIL ID").WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS).WithFontColor("#B2B5B9"),
				Placeholder: commontypes.GetPlainStringText("EMAIL ID").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#8D8D8D"),
				Description: commontypes.GetPlainStringText("Your email id registered with Club Vistara").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#9DA1A4"),
				DataType:    types.TextInputField_DATA_TYPE_STRING,
			},
			{
				Id:          beRedemptionPb.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_CLUB_VISTARA_ID.String(),
				Title:       commontypes.GetPlainStringText("CLUB VISTARA ID").WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS).WithFontColor("#B2B5B9"),
				Placeholder: commontypes.GetPlainStringText("CLUB VISTARA ID").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#8D8D8D"),
				Description: commontypes.GetPlainStringText("Your 9-digit membership ID").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#9DA1A4"),
				DataType:    types.TextInputField_DATA_TYPE_STRING,
				ValidationRules: []*types.TextInputField_ValidationRule{
					{
						Regex:          "^[a-zA-Z0-9]{9}$",
						FailureMessage: commontypes.GetPlainStringText("Membership ID should be exactly 9 digits").WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor("#FA3B11"),
					},
				},
			},
		},
		Disclaimer: &typesUi.TextWithHyperlinks{
			Text: commontypes.GetPlainStringText("Not a Club Vistara member? Create an account").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#646464"),
			HyperlinkMap: map[string]*typesUi.HyperLink{
				"Create an account": {Link: &typesUi.HyperLink_Url{Url: "https://go.fi.money/Vistara"}},
			},
		},
		Cta: &fePb.CtaV1{
			CtaTitle: commontypes.GetPlainStringText("Confirm").WithFontStyle(commontypes.FontStyle_BUTTON_M).WithFontColor("#FFFFFF"),
			Action:   &fePb.CtaV1_CustomAction{CustomAction: &fePb.CustomAction{ActionType: fePb.CustomAction_MAKE_API_CALL, ActionApi: fePb.CustomActionApi_INITIATE_REDEMPTION_RPC}},
			BgColor:  "#00B899",
		},
	}, nil
}

// GetDynamicUrlForWebPageScreen requires to pass RequestMetadata parameter.
// Format supported for RequestMetadata => "vendor:POSHVINE,target_url:fimoney.poshvine.com/giftcards"
func (r *RewardService) GetDynamicUrlForWebPageScreen(ctx context.Context, req *fePb.GetDynamicUrlForWebPageScreenRequest) (*fePb.GetDynamicUrlForWebPageScreenResponse, error) {
	var (
		actorId    = req.GetReq().GetAuth().GetActorId()
		webpageUrl string
		err        error
	)

	if req.GetRequestMetadata() == "" {
		logger.Error(ctx, "request metadata should not be empty", zap.String(logger.ACTOR_ID_V2, actorId))
		return &fePb.GetDynamicUrlForWebPageScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("request metadata should not be empty")},
		}, nil
	}

	reqMetadataMap := getRequestMetadataMapFromString(ctx, req.GetRequestMetadata())
	vendor, containsVendor := reqMetadataMap[vendorForDynamicUrlForWebpageScreen]
	if !containsVendor {
		logger.Error(ctx, "no vendor passed in request meta data", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("requestMetadata", req.GetRequestMetadata()))
		return &fePb.GetDynamicUrlForWebPageScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("no vendor passed in request meta data"),
			},
		}, nil
	}

	switch vendor {
	case beCasperPb.OfferVendor_POSHVINE.String():
		webpageUrl, err = r.getExternalVendorDynamicWebpageUrl(ctx, actorId, reqMetadataMap, beCasperPb.OfferVendor_POSHVINE)
		if err != nil {
			logger.Error(ctx, "error while fetching poshVine webpage url", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return &fePb.GetDynamicUrlForWebPageScreenResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching poshVine webpage url"),
				},
			}, nil
		}
	case beCasperPb.OfferVendor_DPANDA.String():
		webpageUrl, err = r.getExternalVendorDynamicWebpageUrl(ctx, actorId, reqMetadataMap, beCasperPb.OfferVendor_DPANDA)
		if err != nil {
			logger.Error(ctx, "error while fetching dpanda webpage url", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return &fePb.GetDynamicUrlForWebPageScreenResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching dPanda webpage url"),
				},
			}, nil
		}
	}
	if webpageUrl == "" {
		return &fePb.GetDynamicUrlForWebPageScreenResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			},
		}, nil
	}
	return &fePb.GetDynamicUrlForWebPageScreenResponse{
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		WebpageUrl: webpageUrl,
	}, nil
}

func (r *RewardService) getExternalVendorDynamicWebpageUrl(ctx context.Context, actorId string, metadata map[string]string, vendor beCasperPb.OfferVendor) (string, error) {
	targetUrl, containsTargetUrl := metadata[targetUrlForDynamicUrlForWebpageScreen]
	if containsTargetUrl {
		beResp, err := r.offerCatalogClient.GetExternalVendorDynamicWebpageUrl(ctx, &beCasperPb.GetExternalVendorDynamicWebpageUrlRequest{
			ActorId:    actorId,
			VendorName: vendor,
			TargetUrl:  targetUrl,
		})
		if rpcErr := epifigrpc.RPCError(beResp, err); rpcErr != nil {
			return "", rpcErr
		}
		return beResp.GetWebpageUrl(), nil
	}
	return "", fmt.Errorf("insufficient target url in metadata for fetching web url")
}
