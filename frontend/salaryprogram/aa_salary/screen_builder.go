package aa_salary

import (
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/logger"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext"

	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/document_upload/client_states"
	"github.com/epifi/gamma/api/frontend/document_upload/polling"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/connected_account"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/tiering/helper"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/spacing"

	caPb "github.com/epifi/gamma/api/connected_account"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	caPbEnums "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	feSalaryPb "github.com/epifi/gamma/api/frontend/salaryprogram"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/tiering/external"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay"
	salaryScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnums "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/frontend/config/genconf"
	caFactoryProcessor "github.com/epifi/gamma/frontend/connected_account/factory/processor"
	earnedBenefits "github.com/epifi/gamma/frontend/tiering/earned_benefits"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type IScreenBuilder interface {
	BuildDeeplinkScreen(ctx context.Context, screen feSalaryPb.AaSalaryScreen, data *ScreenData) (*deeplinkPb.Deeplink, error)
	GetAddFundsAction(ctx context.Context, data *ScreenData) (*deeplinkPb.Deeplink, error)
}

type ScreenBuilder struct {
	gconf                   *genconf.Config
	salaryProgramClient     beSalaryPb.SalaryProgramClient
	savingsClient           savingsPb.SavingsClient
	accountPIRelationClient accountPiPb.AccountPIRelationClient
	upiOnbClient            upiOnbPb.UpiOnboardingClient
	caClient                caPb.ConnectedAccountClient
	conf                    *config.Config
	releaseEvaluator        release.IEvaluator
}

func NewScreenBuilder(
	gconf *genconf.Config,
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	savingsClient savingsPb.SavingsClient,
	accountPIRelationClient accountPiPb.AccountPIRelationClient,
	upiOnbClient upiOnbPb.UpiOnboardingClient,
	caClient caPb.ConnectedAccountClient,
	conf *config.Config,
	releaseEvaluator release.IEvaluator,
) *ScreenBuilder {
	return &ScreenBuilder{
		gconf:                   gconf,
		salaryProgramClient:     salaryProgramClient,
		savingsClient:           savingsClient,
		accountPIRelationClient: accountPIRelationClient,
		upiOnbClient:            upiOnbClient,
		caClient:                caClient,
		conf:                    conf,
		releaseEvaluator:        releaseEvaluator,
	}
}

func (s *ScreenBuilder) BuildDeeplinkScreen(ctx context.Context, screen feSalaryPb.AaSalaryScreen, data *ScreenData) (*deeplinkPb.Deeplink, error) {
	switch screen {
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_ORCHESTRATION_API:
		return getAaSalaryLandingScreenDeeplink(screen)
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_CONNECT_ACCOUNT:
		return getConnectAccountDeeplinkForAaSalary()
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_AMOUNT_TRANSFER_SETUP:
		return s.getTransferFundsSetupDeeplink(ctx, data.GetActorId(), data.GetCurrentTier())
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_TAKING_LONGER_THAN_EXPECTED:
		return getUsualAaTerminalScreen(screen, longerThanExpectedTitle, longerThanExpectedDesc, recentTimeIcon, okGotIt, deeplinkPb.Cta_DONE, &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HOME,
		}, false)
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_NOT_ELIGIBLE_FOR_CASHBACK:
		return getNotEligibleForCashbackScreen(screen)
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_THANKS_FOR_SHARING_INTEREST:
		return getThanksForSharingInterestScreen(screen)
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_ORCHESTRATION_API_FOR_CALCULATING_CASHBACK:
		return getAaSalaryLandingScreenDeeplink(screen)
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_CASHBACK_ACTIVATED:
		return s.getCashbackActivatedScreen(ctx, data.GetActorId(), screen)
	case feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_SOURCE_OF_SALARY:
		return s.getSourceOfSalaryScreen(ctx, data.GetActorId())
	default:
		return nil, fmt.Errorf("deeplink screen for %s is not available", screen.String())
	}
}

// GetAddFundsAction checks if user is eligible for in-app add funds
// returns off app add funds screen if user is not eligible for in-app add funds
func (s *ScreenBuilder) GetAddFundsAction(ctx context.Context, data *ScreenData) (*deeplinkPb.Deeplink, error) {
	getUpiAccResp, getUpiAccErr := s.upiOnbClient.GetAccounts(ctx, &upiOnbPb.GetAccountsRequest{
		ActorId:       data.GetActorId(),
		AccountStatus: []upiOnbEnums.UpiAccountStatus{upiOnbEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
	})
	if rpcErr := epifigrpc.RPCError(getUpiAccResp, getUpiAccErr); rpcErr != nil {
		return nil, fmt.Errorf("GetAccounts rpc failed, %w", rpcErr)
	}

	if s.shouldAllowTpapTransfer(getUpiAccResp.GetAccounts()) {
		return s.getAddFundsDeeplink(data)
	}

	return s.getOffAppAddFundsScreen(ctx, data)
}

func getAaSalaryLandingScreenDeeplink(screen feSalaryPb.AaSalaryScreen) (*deeplinkPb.Deeplink, error) {
	dl := &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_AA_SALARY_LANDING_SCREEN}

	if screen == feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_ORCHESTRATION_API_FOR_CALCULATING_CASHBACK {
		landingScreenScreenOption := &salaryScreenOptionsPb.AaSalaryLandingScreenOptions{
			LoaderText: calculatingBenefitsLoaderText,
		}

		var screenOptionsErr error
		dl.ScreenOptionsV2, screenOptionsErr = deeplinkv3.GetScreenOptionV2(landingScreenScreenOption)
		if screenOptionsErr != nil {
			return nil, fmt.Errorf("failed to get screen options v2, %w", screenOptionsErr)
		}
	}

	return dl, nil
}

func getConnectAccountDeeplinkForAaSalary() (*deeplinkPb.Deeplink, error) {
	screenOptions, err := deeplinkv3.GetScreenOptionV2(&connectedaccount.ConnectAccountsFlowScreenOptions{
		CaFlowName: caPbEnums.CAFlowName_CA_FLOW_NAME_AA_SALARY.String(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get screen options for ConnectAccountsFlowScreenOptions, %w", err)
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
		ScreenOptionsV2: screenOptions,
	}, nil
}

func (s *ScreenBuilder) isTransferSetupV2Enabled(ctx context.Context, appPlatform commontypes.Platform, appVersion int) bool {
	if appPlatform == commontypes.Platform_IOS && appVersion < s.gconf.SalaryProgram().AaSalaryConfig().AaSalaryAmountSetupVersionV2().MinIOSVersion() ||
		appPlatform == commontypes.Platform_ANDROID && appVersion < s.gconf.SalaryProgram().AaSalaryConfig().AaSalaryAmountSetupVersionV2().MinAndroidVersion() {
		return false
	}

	if !s.gconf.SalaryProgram().AaSalaryConfig().EnableAaSalaryAmountSetupVersionV2(ctx) {
		return false
	}

	return true
}

// nolint:funlen
func (s *ScreenBuilder) getTransferFundsSetupDeeplink(ctx context.Context, actorId string, currentTier external.Tier) (*deeplinkPb.Deeplink, error) {
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	appVersion := epificontext.AppVersionFromContext(ctx)
	version := types.Version_VERSION_V1
	if s.isTransferSetupV2Enabled(ctx, appPlatform, appVersion) {
		version = types.Version_VERSION_V2
	}

	getTransferSetupParamsResp, getTransferSetupParamsErr := s.salaryProgramClient.GetAaSalaryTransferSetupParams(ctx, &beSalaryPb.GetAaSalaryTransferSetupParamsRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(getTransferSetupParamsResp, getTransferSetupParamsErr); rpcErr != nil {
		if getTransferSetupParamsResp.GetStatus().GetCode() == uint32(beSalaryPb.GetAaSalaryTransferSetupParamsResponse_NOT_ELIGIBLE) {
			return getNotEligibleForCashbackScreen(feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_NOT_ELIGIBLE_FOR_CASHBACK)
		}

		return nil, fmt.Errorf("GetAaSalaryTransferSetupParams rpc failed, %w", rpcErr)
	}

	amountRanges := getTransferSetupParamsResp.GetAmountRangeForSalaryBands()
	if len(amountRanges) == 0 {
		return nil, fmt.Errorf("GetAmountRangeForSalaryBands cannot be empty")
	}

	amountRangeForBands := getTransferSetupParamsResp.GetAmountRangeForSalaryBands()

	// assuming that amount ranges are in sorted order
	minTransferAmount := amountRanges[0].GetMinAmount()
	maxTransferAmount := amountRanges[len(amountRanges)-1].GetMaxAmount()
	maxBandEligible := amountRangeForBands[len(amountRangeForBands)-1].GetSalaryBand()
	initialAmount := getTransferSetupParamsResp.GetDefaultTransferAmountForUser()

	transferAmountCta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         transferSetupSwipeCtaText,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
	splitPointers, Ranges := getSliderOptionFromAmountRangeForBands(amountRanges)

	sliderOptions := &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption{
		SliderRanges:  make([]*salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges, 0, len(Ranges)),
		SplitPointers: splitPointers,
	}
	amountRangeList := make([]*salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange, 0, len(Ranges))
	for _, amountRange := range Ranges {
		cashbackPercentage := amountRange.Cashback
		var tagText *commontypes.Text
		var tagItc *uiPb.IconTextComponent
		if version == types.Version_VERSION_V2 {
			tagText = commontypes.GetHtmlText(fmt.Sprintf(youEarnBack, cashbackPercentage)).
				WithFontColor(colors.ColorOnDarkHighEmphasis).
				WithFontStyle(commontypes.FontStyle_SUBTITLE_S)
			tagItc = uiPb.NewITC().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(youEarn, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S)).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(cashbackPercentage, colors.SupportingMoss200, commontypes.FontStyle_SUBTITLE_S)).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(backText, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S))
		} else {
			tagText = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(sliderTagText, cashbackPercentage), colors.ColorMoss700, commontypes.FontStyle_OVERLINE_XS_CAPS)
		}
		sliderOptions.SliderRanges = append(sliderOptions.GetSliderRanges(), &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges{
			MinValue: amountRange.MinAmount,
			MaxValue: amountRange.MaxAmount,
			Tag:      commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(sliderTagText, cashbackPercentage), colors.ColorMoss700, commontypes.FontStyle_OVERLINE_XS_CAPS),
		})
		amountRangeList = append(amountRangeList, &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange{
			MinValue: amountRange.MinAmount,
			MaxValue: amountRange.MaxAmount,
			Tag:      tagText,
			TagItc:   tagItc,
		})
	}

	var suggestions []*commontypes.Text

	suggestions = append(suggestions,
		commontypes.GetTextFromStringFontColourFontStyle(
			money.ToDisplayStringInIndianFormat(minTransferAmount, 0, false), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
		commontypes.GetTextFromStringFontColourFontStyle(
			money.ToDisplayStringInIndianFormat(initialAmount, 0, false), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
		commontypes.GetTextFromStringFontColourFontStyle(
			money.ToDisplayStringInIndianFormat(maxTransferAmount, 0, false), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
	)

	amountSelectionCard := &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(amountSelectionTitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S),
		RupeesSymbolText: commontypes.GetTextFromStringFontColourFontStyle(rupeeSymbolText, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_CURRENCY_XL),
		EditIcon:         commontypes.GetVisualElementFromUrlHeightAndWidth(editIconGreen, 24, 24),
		BgColor: widgetPb.GetLinearGradientBackgroundColour(45, []*widgetPb.ColorStop{
			{Color: "#57595D", StopPercentage: 0},
			{Color: "#28292B", StopPercentage: 0},
		}),
		InitialAmount: typesPb.GetFromBeMoney(initialAmount),
		SliderOption:  sliderOptions,
		BenefitsSubCard: &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard{
			BgColour: widgetPb.GetBlockBackgroundColour(colors.ColorDarkLayer1),
			Benefit1: uiPb.NewITC().
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(benefitsCash, 40, 40)).
				WithLeftImagePadding(8).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(benefit1Text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_XS)),
			Benefit2: uiPb.NewITC().
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(noAutoDebit, 40, 40)).
				WithLeftImagePadding(8).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(benefit2Text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_XS)),
		},
		AmountInfo: &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo{
			MinAmountDisplayVal: commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithPrecision(minTransferAmount, 0), colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),
			MaxAmountDisplayVal: commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithPrecision(maxTransferAmount, 0), colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),
			MinAmount:           typesPb.GetFromBeMoney(minTransferAmount),
			MaxAmount:           typesPb.GetFromBeMoney(maxTransferAmount),
			MinAmountError:      commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(minTransferAmountErr, money.ToDisplayStringWithPrecision(minTransferAmount, 0)), colors.SupportingCherry400, commontypes.FontStyle_SUBTITLE_S),
			MaxAmountError:      commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(maxTransferAmountErr, money.ToDisplayStringWithPrecision(maxTransferAmount, 0)), colors.SupportingCherry400, commontypes.FontStyle_SUBTITLE_S),
		},
		AmountRanges:            amountRangeList,
		AmountSuggestions:       suggestions,
		SelectedSuggestionIndex: 1, // Kept it static for now since we want to display the initial amount as the selected suggestion
	}

	var animation string

	switch {
	case currentTier.IsAaSalaryTier():
		animation = ""
	default:
		animation = threePercentsCashbackLottie
	}

	var backConfirmPopup *deeplinkPb.InformationPopupOptions
	popUpConfig := s.gconf.SalaryProgram().AaSalaryConfig().AaSalaryAmountSetupBackConfirmationPopup()
	if popUpConfig.Enable(ctx) && (appPlatform == commontypes.Platform_IOS && appVersion >= popUpConfig.AppVersion().MinIOSVersion() ||
		appPlatform == commontypes.Platform_ANDROID && appVersion >= popUpConfig.AppVersion().MinAndroidVersion()) {
		backConfirmPopup = buildBackConfirmationPopUp(ctx, popUpConfig)
	}

	transferSetupScreenOption := &salaryScreenOptionsPb.AaSalaryProgramFlowsAmountTransferSetupScreenOptions{
		TopCashbackIcon:       commontypes.GetVisualElementFromUrlHeightAndWidth(primeIcon, 88, 128),
		Title:                 commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(transferSetupScreenTitle, AaSalaryBandToCashbackPercent[maxBandEligible]), colors.ColorSnow, commontypes.FontStyle_HEADLINE_L),
		TransferAmountCta:     transferAmountCta,
		AmountSelectionCard:   amountSelectionCard,
		BackNavigationElement: commontypes.GetVisualElementFromUrlHeightAndWidth(leftChevron, 24, 24),
		InfoNavigationElement: uiPb.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 24, 24)).WithDeeplink(getInfoPopup(transferSetupInfoPopupTitle, transferSetupInfoPopupBody)),
		Animation:             animation,
		BgColor:               widgetPb.GetBlockBackgroundColour(colors.ColorDarkBase),
		Version:               version,
		PoweredBy: uiPb.NewITC().
			WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(federalBankBlue6416, 16, 64)).
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(poweredBy, colors.ColorMonochromeSlate, commontypes.FontStyle_OVERLINE_2XS_CAPS)).
			WithRightImagePadding(spacing.Spacing2Xs),
		InfoPopup: backConfirmPopup,
	}

	screenOptionV2, err := deeplinkv3.GetScreenOptionV2(transferSetupScreenOption)
	if err != nil {
		return nil, fmt.Errorf("faield to get screen option v2, %w", err)
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN,
		ScreenOptionsV2: screenOptionV2,
	}, nil
}

func getNotEligibleForCashbackScreen(screen feSalaryPb.AaSalaryScreen) (*deeplinkPb.Deeplink, error) {
	notedYourInterestDeeplink, err := getThanksForSharingInterestScreen(feSalaryPb.AaSalaryScreen_AA_SALARY_SCREEN_THANKS_FOR_SHARING_INTEREST)
	if err != nil {
		return nil, fmt.Errorf("failed to get noted your interest deeplink, %w", err)
	}

	return getUsualAaTerminalScreen(screen, notEligibleForCashbackTitle, notEligibleForCashbackDesc, lockIcon, notEligibleForCashbackCtaText, deeplinkPb.Cta_CUSTOM, notedYourInterestDeeplink, false)
}

func getThanksForSharingInterestScreen(screen feSalaryPb.AaSalaryScreen) (*deeplinkPb.Deeplink, error) {
	return getUsualAaTerminalScreen(screen, notedYourInterestTitle, notedYourInterestDesc, tickIcon, notedYourInterestCtaText, deeplinkPb.Cta_DONE, &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_HOME,
	}, false)
}

func (s *ScreenBuilder) getCashbackActivatedScreen(ctx context.Context, actorId string, screen feSalaryPb.AaSalaryScreen) (*deeplinkPb.Deeplink, error) {
	aaSalaryDetailsResp, aaSalaryDetailsErr := s.salaryProgramClient.GetAaSalaryDetails(ctx, &beSalaryPb.GetAaSalaryDetailsRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(aaSalaryDetailsResp, aaSalaryDetailsErr); rpcErr != nil {
		return nil, fmt.Errorf("GetAaSalaryDetails rpc failed, %w", rpcErr)
	}

	earnedBenefitsDeeplink, earnedBenefitsDeeplinkErr := earnedBenefits.GetEarnedBenefitsDeeplink(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1)
	if earnedBenefitsDeeplinkErr != nil {
		return nil, fmt.Errorf("failed to get earned benefits deeplink, %w", earnedBenefitsDeeplinkErr)
	}

	title := fmt.Sprintf(cashbackActivatedTitle, AaSalaryBandToCashbackPercent[aaSalaryDetailsResp.GetSalaryBand()])
	desc := fmt.Sprintf(cashbackActivatedDesc, AaSalaryBandToCashbackPercent[aaSalaryDetailsResp.GetSalaryBand()])
	return getUsualAaTerminalScreen(screen, title, desc, tickIcon, cashbackActivatedCtaText, deeplinkPb.Cta_CUSTOM, earnedBenefitsDeeplink, true)
}

// nolint:funlen
func (s *ScreenBuilder) getSourceOfSalaryScreen(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	appVersion := epificontext.AppVersionFromContext(ctx)

	releaseConstraint := release.NewCommonConstraintData(types.Feature_PRIME_SMS_PARSER).WithActorId(actorId)
	isSmsParserEnabledForActor, evaluationErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if evaluationErr != nil {
		logger.Error(ctx, "error in evaluating sms parser release constraint", zap.Error(evaluationErr))
	}
	if isSmsParserEnabledForActor {
		if estimateErr := s.estimateSalaryFromSmsParser(ctx, actorId); estimateErr != nil {
			logger.Error(ctx, "error in estimating salary from sms parser", zap.Error(estimateErr))
		} else {
			return helper.AaSalaryLandingScreenDeeplinkForSMSParser(), nil
		}
	}

	connectAccountTitle := commontypes.GetTextFromStringFontColourFontStyle(connectAccountTitleText, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S)

	if appPlatform == commontypes.Platform_IOS && appVersion >= s.conf.SalaryProgram.AaSalaryConfig.AaSalarySourceScreenHideConnectTitle.MinIOSVersion ||
		appPlatform == commontypes.Platform_ANDROID && appVersion >= s.conf.SalaryProgram.AaSalaryConfig.AaSalarySourceScreenHideConnectTitle.MinAndroidVersion {
		connectAccountTitle = nil
	}

	getAllAccountsResp, getAllAccountsErr := s.caClient.GetAllAccounts(ctx, &caPb.GetAllAccountsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: 100,
		},
		ActorId:           actorId,
		AccountFilterList: []beCaExtPb.AccountFilter{beCaExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE},
		AccountSubTypeList: []*caPb.AccountSubType{
			{
				SubType: &caPb.AccountSubType_DepositAccountType{
					DepositAccountType: caPbEnums.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(getAllAccountsResp, getAllAccountsErr); rpcErr != nil {
		if getAllAccountsResp.GetStatus().IsRecordNotFound() {
			return getConnectAccountDeeplinkForAaSalary()
		}
		return nil, fmt.Errorf("failed to get source of salary screen. GetAllAccounts rpc failed, %w", rpcErr)
	}

	accountList := getAllAccountsResp.GetAccountDetailsList()
	var connectedAccountInfoList []*salaryScreenOptionsPb.AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent
	if accountList == nil || len(accountList) == 0 {
		return getConnectAccountDeeplinkForAaSalary()
	}
	successDeeplink, err := buildSalaryAccountVerifiedScreen()
	if err != nil {
		return nil, fmt.Errorf("faield to build salary account verified screen, %w", err)
	}
	aaSalaryDataPullScreenOptions := &salaryScreenOptionsPb.AASalaryDataPullScreenOptions{
		ScreenBgColor: widgetPb.GetBlockBackgroundColour(colors.ColorDarkBase),
		ProgressState: &salaryScreenOptionsPb.AASalaryDataPullScreenOptions_UiData{
			Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth(statementProgressIcon, 124, 124),
			Title:    commontypes.GetTextFromStringFontColourFontStyle(statementProgressTitle, colors.ColorSnow, commontypes.FontStyle_HEADLINE_XL),
			Subtitle: commontypes.GetTextFromStringFontColourFontStyle(statementProgressSubTitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S),
		},
		ErrorState: &salaryScreenOptionsPb.AASalaryDataPullScreenOptions_UiData{
			Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth(statementErrorIcon, 124, 124),
			Title:    commontypes.GetTextFromStringFontColourFontStyle(statementErrorTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_XL),
			Subtitle: commontypes.GetTextFromStringFontColourFontStyle(statementErrorSubTitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S),
			RetryCta: &uiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(retryText, colors.ColorSnow, commontypes.FontStyle_BUTTON_M),
				},
				ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
					BgColor:       colors.ColorLightPrimaryAction,
					CornerRadius:  19,
					LeftPadding:   24,
					RightPadding:  24,
					TopPadding:    10,
					BottomPadding: 10,
				},
			},
			CloseCta: &uiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor: colors.ColorLightPrimaryAction,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: closeText,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
					},
				},
				ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
					BgColor:       colors.ColorCharcoal,
					CornerRadius:  19,
					LeftPadding:   24,
					RightPadding:  24,
					TopPadding:    10,
					BottomPadding: 10,
				},
			},
		},
		PollingOptions: &polling.DocumentPollingOption{
			DocumentType:     client_states.DocumentType_DOCUMENT_TYPE_CONNECTED_ACCOUNT_STATEMENT,
			Client:           client_states.Client_CLIENT_SALARY_PROGRAM,
			PollingDuration:  60,
			PollingFrequency: 10,
		},
		PullBankData:    false,
		SuccessDeeplink: successDeeplink,
	}
	for _, aaAccount := range accountList {
		accountNumber := aaAccount.GetMaskedAccountNumber()
		aaSalaryDataPullScreenOptions.GetPollingOptions().DocumentParams = &polling.DocumentParams{
			Params: &polling.DocumentParams_ConnectedAccountStatementParams{
				ConnectedAccountStatementParams: &polling.ConnectedAccountStatementParams{
					AccountId: aaAccount.GetAccountId(),
				},
			},
		}
		aaSalaryDataPullScreenOptions.ProgressState.Title = commontypes.GetTextFromStringFontColourFontStyle(statementProgressTitle+aaAccount.GetFipMeta().GetDisplayName()+" •••• "+accountNumber[len(accountNumber)-4:], colors.ColorSnow, commontypes.FontStyle_HEADLINE_XL)
		aaSalaryDataPullScreenOptionsV2, getScreenOptionV2Err := deeplinkv3.GetScreenOptionV2(aaSalaryDataPullScreenOptions)
		if getScreenOptionV2Err != nil {
			return nil, fmt.Errorf("faield to get screen option v2, %w", getScreenOptionV2Err)
		}
		connectedAccountRowComponent := &salaryScreenOptionsPb.AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent{
			BankIcon:         commontypes.GetVisualElementFromUrlHeightAndWidth(aaAccount.GetFipMeta().GetLogoUrl(), 40, 40),
			BankAccountLabel: commontypes.GetTextFromStringFontColourFontStyle(aaAccount.GetFipMeta().GetDisplayName()+" •••• "+accountNumber[len(accountNumber)-4:], colors.ColorNight, commontypes.FontStyle_SUBTITLE_M),
			IsSelected:       len(accountList) == 1,
			Deeplink: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_AA_SALARY_DATA_PULL,
				ScreenOptionsV2: aaSalaryDataPullScreenOptionsV2,
			},
		}
		connectedAccountInfoList = append(connectedAccountInfoList, connectedAccountRowComponent)
	}
	connectAccountScreenOptions, err := deeplinkv3.GetScreenOptionV2(&connectedaccount.ConnectAccountsFlowScreenOptions{
		CaFlowName: caPbEnums.CAFlowName_CA_FLOW_NAME_AA_SALARY.String(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get screen options for ConnectAccountsFlowScreenOptions, %w", err)
	}
	sourceOfSalaryScreenOptions := &salaryScreenOptionsPb.AaSalarySourceOfFundScreenOptions{
		TopIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/aasalary/salary_verification.png").
			WithProperties(&commontypes.VisualElementProperties{
				Width:  100,
				Height: 100,
			}),
		Title:    commontypes.GetTextFromStringFontColourFontStyle(sourceOfSalaryScreenTitle, colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle(sourceOfSalaryScreenSubTitle, colors.ColorLead, commontypes.FontStyle_BODY_S),
		ConnectedAccountsComponent: &salaryScreenOptionsPb.AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent{
			Title:             connectAccountTitle,
			ConnectedAccounts: connectedAccountInfoList,
			ConnectNewAccount: &uiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(connectSalaryAccountText, colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_S),
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen:          deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
					ScreenOptionsV2: connectAccountScreenOptions,
				},
				LeftVisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: plusIconPng,
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  20,
								Height: 20,
							},
						},
					},
				},
				ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
					BgColor:       colors.ColorOnDarkHighEmphasis,
					CornerRadius:  19,
					TopPadding:    14,
					BottomPadding: 14,
				},
			},
			PartnerLogo: &uiPb.IconTextComponent{
				LeftVisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: poweredByEpifiWealthPng,
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  108,
								Height: 16,
							},
						},
					},
				},
			},
		},
		TermsInfo: []*widgetPb.CheckboxItem{
			{
				Id:          consentPb.ConsentType_FI_WEALTH_TNC.String(),
				DisplayText: commontypes.GetTextFromHtmlStringFontColourFontStyle(checkboxWealthTermsText, colors.ColorSlate, commontypes.FontStyle_BODY_XS),
				IsChecked:   false,
			},
		},
		ProceedCta: &deeplink.Cta{
			Type:   deeplink.Cta_CONTINUE,
			Status: deeplink.Cta_CTA_STATUS_ENABLED,
			Text:   proceedText,
		},
		BottomView: &connected_account.ComingSoonComponent{
			BackgroundColour: widgetPb.GetBlockBackgroundColour(colors.ColorOnDarkHighEmphasis),
			Label:            commontypes.GetTextFromStringFontColourFontStyle(uploadItr, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_XS),
			ComingSoonLabel: uiPb.NewITC().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(comingSoonText, colors.ColorSupportingMoss900, commontypes.FontStyle_OVERLINE_2XS_CAPS)).
				WithContainerBackgroundColor(colors.ColorSupportingMoss100).
				WithContainerCornerRadius(12).
				WithContainerPadding(2, 8, 2, 8),
		},
	}

	screenOptionV2, err := deeplinkv3.GetScreenOptionV2(sourceOfSalaryScreenOptions)
	if err != nil {
		return nil, fmt.Errorf("faield to get screen option v2, %w", err)
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_AA_SALARY_SOURCE_OF_FUND_SCREEN,
		ScreenOptionsV2: screenOptionV2,
	}, nil
}

func (s *ScreenBuilder) estimateSalaryFromSmsParser(ctx context.Context, actorId string) error {
	estimateResp, estimateErr := s.salaryProgramClient.EstimateAndStoreSalaryFromSmsParser(ctx, &beSalaryPb.EstimateAndStoreSalaryFromSmsParserRequest{
		ActorId:                    actorId,
		SalaryEstimationProvenance: beSalaryPb.SalaryEstimationProvenance_SALARY_ESTIMATION_PROVENANCE_IN_APP_USER_REQUEST,
	})
	if rpcErr := epifigrpc.RPCError(estimateResp, estimateErr); rpcErr != nil {
		return fmt.Errorf("failed to estimate salary from sms parser, %w", rpcErr)
	}
	return nil
}

func buildSalaryAccountVerifiedScreen() (*deeplinkPb.Deeplink, error) {
	aaLandingScreenOptions, getScreenOptionsErr := deeplinkv3.GetScreenOptionV2(&salaryScreenOptionsPb.AaSalaryLandingScreenOptions{
		LoaderText: calculatingBenefitsLoaderText,
	})
	if getScreenOptionsErr != nil {
		return nil, fmt.Errorf("failed to get deeplink screen options, %w", getScreenOptionsErr)
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
		ScreenOptions: caFactoryProcessor.GetCAConsentTerminalScreenOptions(aaSalaryConnectAccountSuccessScreenHeading, aaSalaryConnectAccountSuccessScreenSubHeading, connectedAccount3dSuccessScreenIcon, &deeplinkPb.Cta{
			Type: deeplinkPb.Cta_CUSTOM,
			Text: aaSalaryImportDataSuccessScreenCta,
			Deeplink: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_AA_SALARY_LANDING_SCREEN,
				ScreenOptionsV2: aaLandingScreenOptions,
			},
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		}, true),
	}, nil
}

// shouldAllowTpapTransfer checks if
// user has any tpap account connected before 24hrs
func (s *ScreenBuilder) shouldAllowTpapTransfer(tpapAccounts []*upiOnbPb.UpiAccount) bool {
	allowTpapTransfer := false
	for _, account := range tpapAccounts {
		if account.IsInternal() {
			continue
		}

		if time.Since(account.GetCreatedAt().AsTime()) > s.gconf.SalaryProgram().AaSalaryTpapCooloffDuration() {
			allowTpapTransfer = true
			return allowTpapTransfer
		}
	}

	return allowTpapTransfer
}

func (s *ScreenBuilder) getAddFundsDeeplink(data *ScreenData) (*deeplinkPb.Deeplink, error) {
	paymentOptionsScreenOptions, getScreenOptionsErr := deeplinkv3.GetScreenOptionV2(&pay.PaymentOptionsScreenOptions{
		Amount:                  typesPb.GetFromBeMoney(data.GetCommittedSalary()),
		TransactionUiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_AA_SALARY,
	})
	if getScreenOptionsErr != nil {
		return nil, getScreenOptionsErr
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_PAYMENT_OPTIONS_FULL_SCREEN_BOTTOM_SHEET,
		ScreenOptionsV2: paymentOptionsScreenOptions,
	}, nil
}

// nolint:funlen
func (s *ScreenBuilder) getOffAppAddFundsScreen(ctx context.Context, data *ScreenData) (*deeplinkPb.Deeplink, error) {
	committedSalaryStr := money.ToDisplayStringWithPrecision(data.GetCommittedSalary(), 0)

	userFiVpa, getVpaErr := s.getVpaForActor(ctx, data.GetActorId())
	if getVpaErr != nil {
		return nil, fmt.Errorf("failed to get vpa for actor, %w", getVpaErr)
	}

	offAppAddFundsScreenOption := &salaryScreenOptionsPb.AddFundsViaOffAppTransferScreenOptions{
		TopIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(rupeeSymbolGreen2, 88, 88),
		Title:   commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(offAppTransferTitle, committedSalaryStr), colors.ColorSnow, commontypes.FontStyle_HEADLINE_XL),
		InfoComponents: []*salaryScreenOptionsPb.OffAppAddFundsInfoComponent{
			{
				SerialNumber: commontypes.GetVisualElementFromUrlHeightAndWidth(serialNo1, 28, 28),
				Title:        commontypes.GetTextFromStringFontColourFontStyle(infoComponent1Title, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
				Content: &salaryScreenOptionsPb.OffAppAddFundsInfoComponent_IconsList_{IconsList: &salaryScreenOptionsPb.OffAppAddFundsInfoComponent_IconsList{
					Header: commontypes.GetTextFromStringFontColourFontStyle(recommendedApps, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS),
					AppIcons: []*commontypes.VisualElement{
						commontypes.GetVisualElementFromUrlHeightAndWidth(googlePayIcon, 32, 32),
						commontypes.GetVisualElementFromUrlHeightAndWidth(phonePeIcon, 32, 32),
						commontypes.GetVisualElementFromUrlHeightAndWidth(paytmIcon, 32, 32),
						commontypes.GetVisualElementFromUrlHeightAndWidth(whatsappIcon, 32, 32),
					},
				}},
				BgColour: widgetPb.GetBlockBackgroundColour(colors.ColorDarkLayer1),
			},
			{
				SerialNumber: commontypes.GetVisualElementFromUrlHeightAndWidth(serialNo2, 28, 28),
				Title:        commontypes.GetTextFromStringFontColourFontStyle(infoComponent2Title, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
				Content: &salaryScreenOptionsPb.OffAppAddFundsInfoComponent_CopyText_{
					CopyText: &salaryScreenOptionsPb.OffAppAddFundsInfoComponent_CopyText{
						Text:     commontypes.GetTextFromStringFontColourFontStyle(userFiVpa, colors.ColorSnow, commontypes.FontStyle_HEADLINE_S),
						BgColour: widgetPb.GetBlockBackgroundColour(colors.ColorOnLightHighEmphasis),
						CopyIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(copyIconGreen, 24, 24),
						Type:     "UPI_ID",
					},
				},
				BgColour: widgetPb.GetBlockBackgroundColour(colors.ColorDarkLayer1),
			},
			{
				SerialNumber: commontypes.GetVisualElementFromUrlHeightAndWidth(serialNo3, 28, 28),
				Title:        commontypes.GetTextFromStringFontColourFontStyle(infoComponent3Title, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
				BgColour:     widgetPb.GetBlockBackgroundColour(colors.ColorDarkLayer1),
			},
		},
		BackNavigationIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(leftChevron, 24, 24),
		InfoIcon:           uiPb.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 24, 24)).WithDeeplink(getInfoPopup(transferSetupInfoPopupTitle, transferSetupInfoPopupBody)),
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_DONE,
			Text:         offAppTransferCtaText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
		BgColor: widgetPb.GetBlockBackgroundColour(colors.ColorDarkBase),
	}

	screenOption, err := deeplinkv3.GetScreenOptionV2(offAppAddFundsScreenOption)
	if err != nil {
		return nil, fmt.Errorf("failed to get screen option, %w", err)
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_AA_SALARY_ADD_FUNDS_VIA_OFF_APP_TRANSFER,
		ScreenOptionsV2: screenOption,
	}, nil
}

func (s *ScreenBuilder) getVpaForActor(ctx context.Context, actorId string) (string, error) {
	getSavingsAccEssentialsResp, getSavingsAccEssentialsErr := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
		ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
			ActorId:     actorId,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		},
	}})
	if rpcErr := epifigrpc.RPCError(getSavingsAccEssentialsResp, getSavingsAccEssentialsErr); rpcErr != nil {
		return "", fmt.Errorf("GetSavingsAccountEssentials rpc failed, %w", rpcErr)
	}

	accountPiReq := &accountPiPb.GetPiByAccountIdRequest{
		AccountId:   getSavingsAccEssentialsResp.GetAccount().GetId(),
		AccountType: accounts.Type_SAVINGS,
		PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
	}
	piByAccountId, piByAccountIdErr := s.accountPIRelationClient.GetPiByAccountId(ctx, accountPiReq)
	if rpcErr := epifigrpc.RPCError(piByAccountId, piByAccountIdErr); rpcErr != nil {
		return "", fmt.Errorf("GetPiByAccountId rpc failed %w", rpcErr)
	}
	for _, pi := range piByAccountId.GetPaymentInstruments() {
		if pi.GetType() == piPb.PaymentInstrumentType_UPI && pi.GetState() != piPb.PaymentInstrumentState_CLOSED {
			return pi.GetUpi().GetVpa(), nil
		}
	}

	return "", fmt.Errorf("vpa not found for account : %v", getSavingsAccEssentialsResp.GetAccount().GetId())
}

func buildBackConfirmationPopUp(ctx context.Context, config *genconf.AaSalaryAmountSetupBackConfirmationPopup) *deeplinkPb.InformationPopupOptions {
	var icon *commontypes.VisualElement
	if len(config.Icon(ctx)) > 0 {
		icon = commontypes.GetVisualElementFromUrlHeightAndWidth(config.Icon(ctx), 120, 120)
	}
	ctas := make([]*deeplinkPb.Cta, 0)
	if len(config.Cta1Text(ctx)) > 0 {
		ctas = append(ctas, &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_DONE,
			Text:         config.Cta1Text(ctx),
			DisplayTheme: deeplinkPb.Cta_SECONDARY,
		})
	}
	if len(config.Cta2Text(ctx)) > 0 {
		ctas = append(ctas, &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			Text:         config.Cta2Text(ctx),
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		})
	}
	return &deeplinkPb.InformationPopupOptions{
		Icon: icon,
		InfoList: []*deeplinkPb.InformationPopupOptions_Info{
			{
				TextTitle:    commontypes.GetTextFromStringFontColourFontStyle(config.Title(ctx), colors.ColorSnow, commontypes.FontStyle_HEADLINE_L),
				TextSubTitle: commontypes.GetTextFromStringFontColourFontStyle(config.SubTitle(ctx), colors.ColorSnow, commontypes.FontStyle_BODY_S),
			},
		},
		Ctas: ctas,
		GradientBgColor: widgetPb.GetLinearGradientBackgroundColour(135, []*widgetPb.ColorStop{
			{Color: "#57595D", StopPercentage: 0},
			{Color: "#28292B", StopPercentage: 90},
		}),
	}
}
