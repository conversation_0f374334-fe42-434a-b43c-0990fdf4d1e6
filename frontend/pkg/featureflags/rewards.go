package featureflags

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/pkg/feature/release"
)

// IsFeatureRewardsCatalogMergedPageEnabledRequest is the request for IsFeatureRewardsCatalogMergedPageEnabled.
type IsFeatureRewardsCatalogMergedPageEnabledRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId returns the ActorId from the request.
func (r *IsFeatureRewardsCatalogMergedPageEnabledRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps returns the ExternalDependencies from the request.
func (r *IsFeatureRewardsCatalogMergedPageEnabledRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *IsFeatureRewardsCatalogMergedPageEnabledRequest) Validate() error {
	env, _ := cfg.GetEnvironment()
	if cfg.IsTestEnv(env) {
		return fmt.Errorf("feature flags are not enabled in test environment")
	}

	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps()
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetEvaluator() == nil {
		return fmt.Errorf("evaluator is nil")
	}
	return nil
}

// IsFeatureRewardsCatalogMergedPageEnabled checks if the rewards catalog merged page is enabled for the user
func IsFeatureRewardsCatalogMergedPageEnabled(ctx context.Context, req *IsFeatureRewardsCatalogMergedPageEnabledRequest) bool {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsFeatureRewardsCatalogMergedPageEnabledRequest", zap.Error(err))
		return false
	}

	var (
		actorId   = req.GetActorId()
		evaluator = req.GetExternalDeps().GetEvaluator()
	)

	var (
		feature           = types.Feature_FEATURE_REWARDS_CATALOG_MERGED_PAGE
		featureConstraint = release.NewCommonConstraintData(feature).WithActorId(actorId)
	)

	isFeatureEnabled, errEval := evaluator.Evaluate(ctx, featureConstraint)
	if errEval != nil {
		logger.Error(ctx, "error evaluating feature flag", zap.String(logger.FEATURE, feature.String()), zap.Error(errEval))
		isFeatureEnabled = false
	}
	return isFeatureEnabled
}
